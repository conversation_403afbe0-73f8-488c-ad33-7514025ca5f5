package com.cfpamf.ms.insur.weixin.web;

import com.alibaba.fastjson.JSON;
import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.ms.bms.facade.vo.JwtUserInfo;
import com.cfpamf.ms.insur.admin.annotation.MaskMethod;
import com.cfpamf.ms.insur.admin.pojo.vo.SmBaseOrderVO;
import com.cfpamf.ms.insur.admin.web.AbstractController;
import com.cfpamf.ms.insur.base.bean.SmyPageInfo;
import com.cfpamf.ms.insur.base.constant.BaseConstants;
import com.cfpamf.ms.insur.base.exception.ExcptEnum;
import com.cfpamf.ms.insur.base.util.HttpRequestUtil;
import com.cfpamf.ms.insur.weixin.pojo.dto.WxOrderUpdDTO;
import com.cfpamf.ms.insur.weixin.pojo.dto.order.InsuredListDTO;
import com.cfpamf.ms.insur.weixin.pojo.dto.policy.EndorListDTO;
import com.cfpamf.ms.insur.weixin.pojo.dto.policy.PolicyListDTO;
import com.cfpamf.ms.insur.weixin.pojo.query.WxCmsQuery;
import com.cfpamf.ms.insur.weixin.pojo.query.WxOrderQuery;
import com.cfpamf.ms.insur.weixin.pojo.query.WxRenewQuery;
import com.cfpamf.ms.insur.weixin.pojo.query.group.InvoiceInfoQuery;
import com.cfpamf.ms.insur.weixin.pojo.query.policy.GroupPolicyQuery;
import com.cfpamf.ms.insur.weixin.pojo.query.settlement.WxCostQuery;
import com.cfpamf.ms.insur.weixin.pojo.vo.*;
import com.cfpamf.ms.insur.weixin.pojo.vo.apply.common.BankVo;
import com.cfpamf.ms.insur.weixin.pojo.vo.group.EmpInvoiceVo;
import com.cfpamf.ms.insur.weixin.pojo.vo.group.InvoicePolicyListVo;
import com.cfpamf.ms.insur.weixin.pojo.vo.product.EndorPolicyInfo;
import com.cfpamf.ms.insur.weixin.pojo.vo.product.PolicyVo;
import com.cfpamf.ms.insur.weixin.pojo.vo.settlement.WxSettlementCostTotalVO;
import com.cfpamf.ms.insur.weixin.pojo.vo.settlement.WxUserAddCostListVo;
import com.cfpamf.ms.insur.weixin.pojo.vo.settlement.WxUserCostListVo;
import com.cfpamf.ms.insur.weixin.service.WxCcOrderService;
import com.cfpamf.ms.insur.weixin.service.policy.PolicyService;
import com.cfpamf.ms.insur.weixin.service.settlement.WxSettlementCostService;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.Objects;

/**
 * wx微信用户中心/我的订单/保单接口
 *
 * <AUTHOR>
 **/
@Slf4j
@Api(value = "用户中心/我的订单,保单接口", tags = {"用户中心/我的订单,保单接口"})
@RequestMapping(BaseConstants.WX_VERSION + "/center")
@RestController
public class WxCcOrderController extends AbstractController {

    /**
     * 微信用户中心service
     */
    @Autowired
    private WxCcOrderService centerService;

    @Autowired
    private PolicyService policyService;
    @Autowired
    private WxSettlementCostService wxSettlementCostService;

    /**
     * 获取微信用户中心统计信息
     *
     * @param openId
     * @param authorization
     * @return
     */
    @ApiOperation(value = "获取微信用户中心统计信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "openId", value = "微信openId", required = false, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "authorization", value = "authorization", required = true, dataType = "string", paramType = "query")
    })
    @GetMapping("/user/home")
    public WxUserHomeVo getWxUserHomeSmy(@RequestParam(required = false) String openId, @RequestParam String authorization) {
        return centerService.getWxCcCmsSmyV3(openId, authorization);
    }

    @ApiOperation(value = "获取微信用户中心统计信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "authorization", value = "authorization", required = true, dataType = "string", paramType = "query")
    })
    @GetMapping("/user/homeWithoutOpenId")
    public WxUserHomeVo getWxUserHomeSmy(@RequestParam String authorization) {
        return centerService.getWxCcCmsSmyWithoutOpenId(authorization);
    }

    /**
     * 获取微信提成列表
     *
     * @param query
     * @return
     */
    @ApiOperation(value = "获取微信提成列表", tags = "CORE")
    @PostMapping("/user/commission")
    public SmyPageInfo<WxUserCmsListVo, WxCmsSmyVo> getWxUserCmsList(@RequestBody WxCmsQuery query) {
        return centerService.getWxUserCmsListV4(query);
    }

    /**
     * 在保险业务助手中获取微信提成列表
     *
     * @param query
     * @return
     */
    @ApiOperation(value = "获取微信提成列表", tags = "CORE")
    @PostMapping("/biz/helper/user/commission")
    public SmyPageInfo<WxUserCmsListVo, WxCmsSmyVo> getWxUserCmsList(@RequestBody WxCmsQuery query, @RequestHeader String authorization) {
        JwtUserInfo userInfo = HttpRequestUtil.getUserOrThrowExp();
        return centerService.getWxUserCmsListV4(query, userInfo);
    }

    /**
     * 获取微信新提成列表
     *
     * @param query
     * @return
     */
    @ApiOperation(value = "获取微信新提成列表")
    @PostMapping("/user/v3/commission")
    public SmyPageInfo<WxUserCmsListVo, WxCmsSmyVo> getWxUserCmsListV3(@RequestBody WxCmsQuery query) {
        //return centerService.getWxUserCmsListV3(query);
        return centerService.getWxUserCmsListV4(query);
    }

    /**
     * 微信24版我的推广费(列表+汇总)
     *
     * @param query
     * @return
     */
    @ApiOperation(value = "微信24版我的推广费(分页列表+汇总)")
    @PostMapping("/user/costListAndSummary")
    public SmyPageInfo<WxUserCostListVo, WxCmsSmyVo> getWxUserCostListAndSummary(@RequestBody WxCostQuery query) {
        log.info("微信24版我的推广费(分页列表+汇总), 入参:{}", query);
        JwtUserInfo userInfo = HttpRequestUtil.getUserOrThrowExp();
        log.info("微信24版我的推广费(分页列表+汇总), 当前登录人员工号:{}", userInfo.getJobNumber());
        //设置登录客户经理工号
        query.setUserId(userInfo.getJobNumber());

        SmyPageInfo<WxUserCostListVo,WxCmsSmyVo> list = wxSettlementCostService.pageWxUserCostListAndSummary(query);
        return list;
    }

    @ApiOperation(value = "微信24版加佣金(分页列表+汇总)")
    @PostMapping("/user/addCostListAndSummary")
    public SmyPageInfo<WxUserAddCostListVo, WxCmsSmyVo> getWxUserAddCostListAndSummary(@RequestBody WxCostQuery query) {
        log.info("微信24版加佣金(分页列表+汇总), 入参:{}", query);
        JwtUserInfo userInfo = HttpRequestUtil.getUserOrThrowExp();
        log.info("微信24版加佣金(分页列表+汇总), 当前登录人员工号:{}", userInfo.getJobNumber());
        //设置登录客户经理工号
        query.setUserId(userInfo.getJobNumber());
        return new SmyPageInfo<WxUserAddCostListVo, WxCmsSmyVo>();

    }


    /**
     * 微信24版我的推广费(列表)
     *
     * @param query
     * @return
     */
    @ApiOperation(value = "微信24版我的推广费(分页列表)")
    @PostMapping("/user/costList")
    public PageInfo<WxUserCostListVo> getWxUserCostList(@RequestBody WxCostQuery query) {
        log.info("微信24版我的推广费(分页列表), 入参:{}", query);
        JwtUserInfo userInfo = HttpRequestUtil.getUserOrThrowExp();
        log.info("微信24版我的推广费(分页列表), 当前登录人员工号:{}", userInfo.getJobNumber());
        //设置登录客户经理工号
        query.setUserId(userInfo.getJobNumber());
        PageInfo<WxUserCostListVo> list = wxSettlementCostService.pageWxUserCostList(query);
        return list;
    }

    /**
     * 微信24版我的推广费(汇总)
     *
     * @param query
     * @return
     */
    @ApiOperation(value = "微信24版我的推广费(汇总)")
    @PostMapping("/user/costSummary")
    public WxCmsSmyVo getWxUserCostSummary(@RequestBody WxCostQuery query) {
        log.info("微信24版我的推广费(汇总), 入参:{}", query);
        JwtUserInfo userInfo = HttpRequestUtil.getUserOrThrowExp();
        log.info("微信24版我的推广费(汇总), 当前登录人员工号:{}", userInfo.getJobNumber());
        //设置登录客户经理工号
        query.setUserId(userInfo.getJobNumber());
        WxCmsSmyVo smyVo = wxSettlementCostService.getWxUserCostSummary(query);
        return smyVo;
    }

    /**
     * 微信24版我的推广费(汇总)
     *
     * @param query
     * @return
     */
    @ApiOperation(value = "微信24版首页推广费")
    @PostMapping("/user/homePageCostSummary")
    public WxSettlementCostTotalVO getWxUserHomePageCostSummary(@RequestBody WxCostQuery query) {

        WxSettlementCostTotalVO smyVo = wxSettlementCostService.get24WxUserHomePageCostSummary(query);
        return smyVo;
    }


    /**
     * 查询用户微信保单列表
     *
     * @param query
     * @return
     */
    @ApiOperation(value = "查询用户微信保单列表")
    @PostMapping("/user/policy")
    public PageInfo<WxPolicyListVo> getWxPolicyListByPage(@RequestBody WxOrderQuery query) {
        log.info("开始查询微信端保单列表:{}", query);
        return centerService.getWxPolicyListByPage(query);
    }

    /**
     * 保单下载
     *
     * @param policyId
     * @param openId
     * @param authorization
     */
    @ApiOperation(value = "保单下载")
    @ApiImplicitParam(name = "policyId", value = "保单Id", required = true, dataType = "string", paramType = "path")
    @GetMapping("/policy/{policyId}/download")
    public void downloadPolicy(@PathVariable int policyId,
                               @RequestParam(required = false) String openId,
                               @RequestParam String authorization,
                               HttpServletResponse response) {
        try {
            centerService.downloadPolicy(policyId, openId, authorization, response);
        } catch (Throwable e) {
            log.warn("", e);
        }
    }

    /**
     * 保单下载
     *
     * @param policyId
     * @param openId
     * @param authorization
     */
    @ApiOperation(value = "保单下载")
    @ApiImplicitParam(name = "policyId", value = "保单Id", required = true, dataType = "string", paramType = "path")
    @GetMapping("/policy/{policyId}/downloadURL")
    public PageInfo<String> downloadPolicyURL(@PathVariable int policyId,
                               @RequestParam(required = false) String openId,
                               @RequestParam String authorization) {
        try {
            String url = centerService.downloadPolicyURL(policyId, openId, authorization);
            return new PageInfo<>(Arrays.asList(url));
        } catch (Throwable e) {
            log.warn("", e);
        }
        return new PageInfo();
    }

    /**
     * 团险保单下载
     *
     * @param endorsementNo
     * @param openId
     * @param authorization
     */
    @ApiOperation(value = "团险保单下载")
    @ApiImplicitParam(name = "endorsementNo", value = "批单号", required = true, dataType = "string", paramType = "path")
    @GetMapping("/policy/{endorsementNo}/downloadEndorPolicy")
    public void downloadEndorPolicy(@PathVariable String endorsementNo,
                                    @RequestParam(required = false) String openId,
                                    @RequestParam String authorization,
                                    HttpServletResponse response) {
        centerService.downloadEndorPolicy(endorsementNo, openId, authorization, response);
    }

    /**
     * 查询用户微信订单列表
     *
     * @param query
     * @return
     */
    @ApiOperation(value = "查询用户微信订单列表", tags = "CORE")
    @PostMapping("/user/order")
    public PageInfo<WxOrderListVo> getWxOrderListByPage(@RequestBody WxOrderQuery query) {
        log.info("开始查询用户微信订单列表:{}", query);
        return centerService.getWxOrderListByPage(query);
    }

    /**
     * 查看用户微信订单详情
     * 2021-11-5:安全测评整改
     *
     * @param orderId
     * @param openId
     * @param authorization
     * @return
     */
    @ApiOperation(value = "查询用户微信订单详情（保险公司）")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "orderId", value = "保单Id", required = true, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "openId", value = "微信openId", required = false, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "authorization", value = "授权token", required = true, dataType = "string", paramType = "query")
    })
    @GetMapping("/user/order/{orderId}")
    @MaskMethod
    public WxOrderQueryDetailVO getWxOrderById(@PathVariable int orderId, @RequestParam(required = false) String openId, @RequestParam String authorization) {
        return centerService.getWxOrderById(orderId);
    }

    /**
     * 在保险销售助手中查看用户微信订单详情
     *
     * @param orderId
     * @param authorization
     * @return
     */
    @ApiOperation(value = "在保险销售助手中查看用户微信订单详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "orderId", value = "保单Id", required = true, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "authorization", value = "授权token", required = true, dataType = "string", paramType = "query")
    })
    @GetMapping("/biz/helper/user/order/{orderId}")
    @MaskMethod
    public WxOrderQueryDetailVO getWxOrderById(@PathVariable int orderId, @RequestHeader String authorization) {
        JwtUserInfo userInfo = HttpRequestUtil.getUserOrThrowExp();
        return centerService.getWxOrderById(orderId, userInfo);
    }

    /**
     * 该接口主要用于前端回显订单数据，方便用户续保，
     * 不做脱敏
     *
     * @return
     */
    @ApiOperation(value = "查询用户微信订单详情（保险公司）", tags = "CORE")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "orderId", value = "订单id", required = true, dataType = "string", paramType = "string"),
    })
    @GetMapping("/user/order/detail")
    public WxOrderQueryDetailVO queryOrderDetail(@RequestParam("orderId") int orderId) {
        return centerService.getShareOrderById(orderId);
    }

    /**
     * 该接口主要用于前端回显订单数据，方便用户续保，
     * 不做脱敏
     *
     * @return
     */
    @ApiOperation(value = "查询用户微信订单详情（订单关闭重新下单）")
    @GetMapping("/user/order/reorderDetail")
    public WxOrderQueryDetailVO queryOrderReorderDetail(@RequestParam("orderId") String orderId) {
        return centerService.getWxReorderOrderById(orderId);
    }

    /**
     * 查看用户微信订单详情
     *
     * @param orderId
     * @param openId
     * @param authorization
     * @return
     */
    @ApiOperation(value = "查询用户微信订单详情 根据订单号")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "orderId", value = "订单号", required = true, dataType = "string", paramType = "path"),
            @ApiImplicitParam(name = "openId", value = "微信openId", required = false, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "authorization", value = "授权token", required = true, dataType = "string", paramType = "query")
    })
    @GetMapping("/user/order/detail/by_order_no")
    public WxOrderQueryDetailVO getWxOrderByFhOrderId(@RequestParam String orderId, @RequestParam(required = false) String openId, @RequestParam String authorization) {
        return centerService.getWxOrderByFhOrderId(orderId, openId, authorization);
    }

    @ApiOperation(value = "查询订单的基础数据")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "fhOrderId", value = "订单编号", required = true, dataType = "string", paramType = "path")
    })
    @GetMapping("/user/order/local/{fhOrderId}")
    public SmBaseOrderVO getSimpleOrderById(@PathVariable String fhOrderId) {
        return centerService.getOrderLocalBaseInfo(fhOrderId);
    }


    /**
     * 删除用户微信订单
     *
     * @param fhOrderId
     * @return
     */
    @ApiOperation(value = "删除用户微信订单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "fhOrderId", value = "泛华orderId", required = true, dataType = "string", paramType = "path"),
            @ApiImplicitParam(name = "openId", value = "微信openId", required = false, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "authorization", value = "授权token", required = true, dataType = "string", paramType = "query")
    })
    @DeleteMapping("/user/order/{fhOrderId}")
    public void deleteWxOrder(@PathVariable String fhOrderId, @RequestParam(required = false) String openId, @RequestParam String authorization) {
        centerService.deleteWxOrder(fhOrderId, openId, authorization);
    }

    /**
     * 更新用户微信订单与泛华同步
     *
     * @param dto
     * @return
     */
    @ApiOperation(value = "更新用户微信订单与泛华同步")
    @PutMapping("/user/order")
    public void updateWxOrder(@RequestBody WxOrderUpdDTO dto) {
        centerService.updateWxOrder(dto.getFhOrderId(), dto.getOpenId(), dto.getAuthorization());
    }

    /**
     * 查询用户续保订单列表
     *
     * @param query
     * @return
     */
    @ApiOperation(value = "查询用户续保订单列表")
    @PostMapping("/user/renew")
    public PageInfo<WxRenewListVO> getWxRenewListByPage(@RequestBody WxRenewQuery query) {
        return centerService.getWxRenewListByPage(query);
    }

    /**
     * 查询订单渠道信息
     *
     * @param orderId
     * @return
     */
    @ApiOperation(value = "查询订单渠道信息")
    @GetMapping("/order/channel")
    public WxOrderChannelVO getWxOrderChannel(@RequestParam String orderId) {
        return centerService.getWxOrderChannel(orderId);
    }

    /**
     * 查询订单渠道信息
     *
     * @param orderId
     * @return
     */
    @ApiOperation(value = "查询订单渠道对接信息")
    @GetMapping("/order/product/api")
    public WxProductApiVO getWxOrderProductAPi(@RequestParam String orderId) {
        return centerService.getWxOrderProductApi(orderId);
    }

    @ApiOperation(value = "查询团单列表")
    @PostMapping("/group/order/list")
    public PageInfo<PolicyListDTO> groupOrder(@RequestBody GroupPolicyQuery query) {
        if (query.getIssueFlag() == null) {
            query.setIssueFlag(1);
        }
        String userId = HttpRequestUtil.getUserId();
        query.setUserId(userId);
        List<PolicyListDTO> rtn = centerService.queryGroupOrder(query);
        return new PageInfo<>(rtn);
    }

    @ApiOperation(value = "发票-查询团单列表")
    @PostMapping("/group/invoice/list")
    public PageInfo<InvoicePolicyListVo> groupInvoiceList(@RequestBody GroupPolicyQuery query) {
        if (query.getIssueFlag() == null) {
            query.setIssueFlag(1);
        }
        String userId = HttpRequestUtil.getUserId();
        query.setUserId(userId);
        return centerService.queryGroupInvoiceList(query);
    }

    @ApiOperation(value = "获取待批改的保单信息", tags = "CORE")
    @ApiImplicitParams({
    })
    @GetMapping("/group/policy")
    public EndorPolicyInfo endorInfo(@RequestParam("policyNo") String policyNo, @RequestParam("orderId") String orderId) {
        return policyService.queryPolicyInfo(policyNo, orderId);
    }

    @ApiOperation(value = "批改：获取团险职业列表", tags = "CORE")
    @GetMapping("/policy/job/list")
    public List<WxTreeVO> getJobList4Correct(@RequestParam("orderId") String orderId, @RequestParam(value = "policyNo", required = false) String policyNo) {
        log.info("批改流程查询职业列表:{},{}", orderId, policyNo);
        if (StringUtils.isBlank(orderId)) {
            throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(), "订单Id参数不能为空");
        }
        return policyService.getJobListV2(orderId);
    }

    @ApiOperation(value = "获取保单在保的被保人列表", tags = "CORE")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "orderId", value = "原单的订单Id", required = true, dataType = "String")
    })
    @GetMapping("/policy/insured/list")
    public Collection<InsuredListDTO> getInsureds(@RequestParam("orderId") String orderId,
                                                  @RequestParam(value = "policyNo", required = false) String policyNo,
                                                  @RequestParam(value = "keyword", required = false) String keyword) {
        return policyService.getInsureds(orderId, policyNo, keyword);
    }

    @ApiOperation(value = "查看批单列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "invoiceStatus", value = "开票状态：0=未申请,1=已申请", required = true, dataType = "String"),
            @ApiImplicitParam(name = "orderId", value = "原单的订单Id号", required = true, dataType = "String")
    })
    @GetMapping("/endor/list")
    public List<EndorListDTO> getEndorList(@RequestParam(value = "orderId") String orderId, @RequestParam(value = "policyNo") String policyNo, @RequestParam(value = "invoiceStatus", required = false) String invoiceStatus) {
        return policyService.getEndorList(orderId, policyNo);
    }

    @ApiOperation(value = "获取银行列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "channel", value = "保司渠道编码,例如:za", required = true, dataType = "String")
    })
    @GetMapping("/bank/list")
    public List<BankVo> banklist(@RequestParam(value = "channel", required = false) String channel, @RequestParam(value = "keyword", required = false) String keyword) {
        return policyService.banklist(channel, keyword);
    }

    @ApiOperation(value = "获取保单/批单信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "channel", value = "保司渠道编码,例如:za", required = true, dataType = "String"),
            @ApiImplicitParam(name = "orderId", value = "原单Id", required = true, dataType = "String")
    })
    @GetMapping("/policy/detail")
    @MaskMethod
    public PolicyVo policyInfo(@RequestParam("orderId") String orderId, @RequestParam("policyNo") String policyNo, @RequestParam(value = "endorNo", required = false) String endorNo) {
        log.info("查询保单/批单详情:{},{},{}", orderId, policyNo, endorNo);
        return policyService.policyInfo(orderId, policyNo, endorNo);
    }

    @PostMapping("/specialTicket/invoiceInfoQuery")
    @ApiOperation(value = "获取雇主责任险开票信息")
    public EmpInvoiceVo invoiceInfoQuery(@Validated @RequestBody InvoiceInfoQuery query, HttpServletRequest request) {
        Boolean valid = super.sliderValid(query.getKey(), query.getX());
        if (Objects.equals(valid, Boolean.FALSE)) {
            return EmpInvoiceVo.builder().resultCode(EmpInvoiceVo.ERROR_VALID_CODE).errorMessage("验证码错误").build();
        }
        return policyService.queryInvoiceByPolicyNo(query.getPolicyNo());
    }


    /**
     * 该接口主要用于前端回显团险订单数据，方便用户续保，
     * 不做脱敏
     *
     * @return
     */
    @ApiOperation(value = "查询用户微信团险订单详情（订单关闭重新下单）")
    @GetMapping("/user/group/order/reorderDetail")
    public WxOrderQueryDetailVO queryGroupOrderReorderDetail(@RequestParam("orderId") String orderId) {
        return centerService.getGroupShareOrderById(orderId);
    }
}
