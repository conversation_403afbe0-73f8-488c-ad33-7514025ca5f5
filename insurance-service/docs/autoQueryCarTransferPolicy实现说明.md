# autoQueryCarTransferPolicy 方法实现说明

## 概述
本文档说明了 `autoQueryCarTransferPolicy` 方法的实现，该方法用于查询车险转投保单，支持脱敏数据匹配。

## 实现的文件

### 1. MaskMatchUtil.java
**路径**: `insurance-service/src/main/java/com/cfpamf/ms/insur/base/util/MaskMatchUtil.java`

**功能**: 脱敏数据匹配工具类，提供以下方法：
- `matchMaskedName()`: 匹配脱敏后的投保人姓名
- `matchMaskedPlateNumber()`: 匹配脱敏后的车牌号
- `matchMaskedPersonInfo()`: 综合匹配脱敏后的投保人姓名和车牌号

**脱敏匹配算法**:
1. **正则表达式匹配**: 将脱敏字符'*'替换为正则表达式的'.'进行匹配
2. **自定义相似度计算**: 逐字符比较，'*'作为通配符，计算匹配度
3. **编辑距离算法**: 适用于长度不同的字符串，考虑'*'作为通配符
4. **综合相似度**: 使用加权平均计算姓名和证件号的综合匹配度

**相似度阈值设置**:
- 投保人姓名匹配阈值: 0.6
- 车牌号匹配阈值: 0.7
- 综合匹配阈值: 0.7
- 投保人姓名权重: 0.6，车牌号权重: 0.4

### 2. InsuranceRenewMapper.java
**路径**: `insurance-service/src/main/java/com/cfpamf/ms/insur/admin/renewal/dao/InsuranceRenewMapper.java`

**修改内容**: 添加了 `autoQueryCarTransferPolicy` 方法声明

### 3. InsuranceRenewMapper.xml
**路径**: `insurance-service/src/main/resources/mapper/admin/InsuranceRenewMapper.xml`

**修改内容**: 添加了 `autoQueryCarTransferPolicy` 的SQL查询
- 查询车险保单信息
- 包含投保人姓名和证件号字段
- 按照时间范围和险种分类进行过滤

### 4. TransferPolicyVo.java
**路径**: `insurance-service/src/main/java/com/cfpamf/ms/insur/admin/renewal/vo/TransferPolicyVo.java`

**修改内容**: 添加了投保人信息字段：
- `applicantPersonName`: 投保人姓名
- `applicantIdNumber`: 投保人证件号

### 5. InsuranceRenewServiceImpl.java
**路径**: `insurance-service/src/main/java/com/cfpamf/ms/insur/admin/renewal/service/impl/InsuranceRenewServiceImpl.java`

**修改内容**: 
- 添加了 `autoQueryCarTransferPolicy()` 方法实现
- 添加了 `filterByMaskMatch()` 私有方法用于脱敏匹配过滤
- 添加了 `MaskMatchUtil` 的import

## 方法实现逻辑

### autoQueryCarTransferPolicy 方法流程：

1. **构建查询参数**
   - 设置失效时间、证件号、险种分类等查询条件
   - 设置时间范围（失效时间前60天到后30天）

2. **执行数据库查询**
   - 调用 `insuranceRenewMapper.autoQueryCarTransferPolicy()` 查询所有满足条件的车险保单

3. **脱敏匹配过滤**
   - 如果查询结果为空，直接返回
   - 否则调用 `filterByMaskMatch()` 方法进行脱敏匹配过滤

4. **返回匹配结果**
   - 返回经过脱敏匹配过滤后的保单列表

### filterByMaskMatch 方法流程：

1. **遍历保单列表**
2. **对每个保单进行脱敏匹配**
   - 使用 `MaskMatchUtil.matchMaskedPersonInfo()` 方法
   - 比较输入的投保人姓名、证件号与保单中的投保人姓名、证件号
3. **过滤匹配的保单**
   - 只保留匹配成功的保单

## 脱敏匹配算法详解

### 方法1：正则表达式匹配
- 将脱敏字符串中的每个'*'替换为正则表达式的'.'
- 使用Pattern.compile()进行完全匹配
- 如果匹配成功，相似度为1.0

### 方法2：自定义相似度计算
- 要求两个字符串长度相同
- 逐个字符比较：
  - 如果脱敏字符串中的字符是'*'，则跳过（通配符）
  - 如果字符相同，计入匹配
  - 如果字符不同且不是'*'，计入不匹配
- 相似度 = 匹配字符数 / (总字符数 - '*'的数量)

### 方法3：编辑距离算法
- 适用于长度不同的字符串
- 使用动态规划计算Levenshtein距离
- 考虑'*'作为通配符，匹配任意字符
- 相似度 = 1 - (编辑距离 / 最大长度)

### 综合匹配策略
- 分别计算投保人姓名和车牌号的相似度
- 使用加权平均：综合相似度 = 投保人姓名相似度 × 0.6 + 车牌号相似度 × 0.4
- 如果综合相似度 ≥ 0.7，则认为匹配成功

## 测试

创建了测试类 `MaskMatchUtilTest.java` 来验证脱敏匹配逻辑的正确性，包括：
- 基本姓名匹配测试
- 证件号匹配测试
- 综合匹配测试
- 真实场景模拟测试

## 使用方式

该方法已经集成到现有的 `listAutoQueryTransferPolicyList()` 方法中，当产品类型为"车险"时会自动调用：

```java
if(Objects.equals(insuranceRenewDto.getPolicyProductType(),"车险")){
    return autoQueryCarTransferPolicy(insuranceRenewDto);
}
```

## 注意事项

1. **性能考虑**: 脱敏匹配是在内存中进行的，如果查询结果集很大，可能会影响性能
2. **匹配准确性**: 脱敏匹配基于启发式规则，可能存在误匹配的情况
3. **数据依赖**: 需要确保数据库中的投保人信息字段正确填充
