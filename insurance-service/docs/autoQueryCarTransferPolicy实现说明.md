# autoQueryCarTransferPolicy 方法实现说明

## 概述
本文档说明了 `autoQueryCarTransferPolicy` 方法的实现，该方法用于查询车险转投保单，支持脱敏数据匹配。

## 实现的文件

### 1. MaskMatchUtil.java
**路径**: `insurance-service/src/main/java/com/cfpamf/ms/insur/base/util/MaskMatchUtil.java`

**功能**: 脱敏数据匹配工具类，提供以下方法：
- `matchMaskedName()`: 匹配脱敏后的姓名
- `matchMaskedIdNumber()`: 匹配脱敏后的证件号
- `matchMaskedPersonInfo()`: 综合匹配脱敏后的姓名和证件号

**脱敏匹配规则**:
- 采用投保人姓名+年牌号模糊匹配
- 去除姓名、年牌号中脱敏的部分（*字符）
- 主要依靠姓名匹配，证件号作为辅助
- 由于证件号可能是虚拟的，不能用身份证进行精确匹配

### 2. InsuranceRenewMapper.java
**路径**: `insurance-service/src/main/java/com/cfpamf/ms/insur/admin/renewal/dao/InsuranceRenewMapper.java`

**修改内容**: 添加了 `autoQueryCarTransferPolicy` 方法声明

### 3. InsuranceRenewMapper.xml
**路径**: `insurance-service/src/main/resources/mapper/admin/InsuranceRenewMapper.xml`

**修改内容**: 添加了 `autoQueryCarTransferPolicy` 的SQL查询
- 查询车险保单信息
- 包含投保人姓名和证件号字段
- 按照时间范围和险种分类进行过滤

### 4. TransferPolicyVo.java
**路径**: `insurance-service/src/main/java/com/cfpamf/ms/insur/admin/renewal/vo/TransferPolicyVo.java`

**修改内容**: 添加了投保人信息字段：
- `applicantPersonName`: 投保人姓名
- `applicantIdNumber`: 投保人证件号

### 5. InsuranceRenewServiceImpl.java
**路径**: `insurance-service/src/main/java/com/cfpamf/ms/insur/admin/renewal/service/impl/InsuranceRenewServiceImpl.java`

**修改内容**: 
- 添加了 `autoQueryCarTransferPolicy()` 方法实现
- 添加了 `filterByMaskMatch()` 私有方法用于脱敏匹配过滤
- 添加了 `MaskMatchUtil` 的import

## 方法实现逻辑

### autoQueryCarTransferPolicy 方法流程：

1. **构建查询参数**
   - 设置失效时间、证件号、险种分类等查询条件
   - 设置时间范围（失效时间前60天到后30天）

2. **执行数据库查询**
   - 调用 `insuranceRenewMapper.autoQueryCarTransferPolicy()` 查询所有满足条件的车险保单

3. **脱敏匹配过滤**
   - 如果查询结果为空，直接返回
   - 否则调用 `filterByMaskMatch()` 方法进行脱敏匹配过滤

4. **返回匹配结果**
   - 返回经过脱敏匹配过滤后的保单列表

### filterByMaskMatch 方法流程：

1. **遍历保单列表**
2. **对每个保单进行脱敏匹配**
   - 使用 `MaskMatchUtil.matchMaskedPersonInfo()` 方法
   - 比较输入的投保人姓名、证件号与保单中的投保人姓名、证件号
3. **过滤匹配的保单**
   - 只保留匹配成功的保单

## 脱敏匹配规则详解

根据需求文档中的脱敏数据匹配规则：

### 姓名匹配规则：
- 去除姓名中的脱敏字符（*）
- 检查非脱敏部分是否匹配
- 支持部分匹配（一个姓名包含另一个姓名的字符）

### 证件号匹配规则：
- 由于证件号可能是虚拟的，不进行精确匹配
- 主要依靠姓名匹配

### 综合匹配规则：
- 如果姓名匹配，则认为匹配成功
- 如果姓名不匹配但证件号完全相同，也认为匹配成功

## 测试

创建了测试类 `MaskMatchUtilTest.java` 来验证脱敏匹配逻辑的正确性，包括：
- 基本姓名匹配测试
- 证件号匹配测试
- 综合匹配测试
- 真实场景模拟测试

## 使用方式

该方法已经集成到现有的 `listAutoQueryTransferPolicyList()` 方法中，当产品类型为"车险"时会自动调用：

```java
if(Objects.equals(insuranceRenewDto.getPolicyProductType(),"车险")){
    return autoQueryCarTransferPolicy(insuranceRenewDto);
}
```

## 注意事项

1. **性能考虑**: 脱敏匹配是在内存中进行的，如果查询结果集很大，可能会影响性能
2. **匹配准确性**: 脱敏匹配基于启发式规则，可能存在误匹配的情况
3. **数据依赖**: 需要确保数据库中的投保人信息字段正确填充
