# 脱敏匹配算法实现总结

## 实现概述

按照您提供的方案，我们实现了一个完整的脱敏数据匹配系统，用于处理车险转投保单查询中的脱敏数据匹配问题。

## 核心算法实现

### 1. 正则表达式匹配（方法1）
```java
private static boolean isRegexMatch(String maskedStr, String fullStr) {
    // 将*替换为.，构建正则表达式
    String regex = maskedStr.replace("*", ".");
    Pattern pattern = Pattern.compile("^" + regex + "$");
    return pattern.matcher(fullStr).matches();
}
```

**特点**：
- 将脱敏字符'*'替换为正则表达式的'.'
- 适用于脱敏位置和个数不固定的情况
- 如果完全匹配，相似度为1.0

### 2. 自定义相似度计算（方法2）
```java
private static double calculateCustomSimilarity(String maskedStr, String fullStr) {
    int matchCount = 0;
    int totalComparableChars = 0;
    
    for (int i = 0; i < maskedStr.length(); i++) {
        char maskedChar = maskedStr.charAt(i);
        if (maskedChar == '*') continue; // 跳过脱敏字符
        
        totalComparableChars++;
        if (maskedChar == fullStr.charAt(i)) {
            matchCount++;
        }
    }
    
    return totalComparableChars == 0 ? 1.0 : (double) matchCount / totalComparableChars;
}
```

**特点**：
- 要求两个字符串长度相同
- 逐个字符比较，'*'作为通配符
- 相似度 = 匹配字符数 / (总字符数 - '*'的数量)

### 3. 编辑距离算法（方法3）
```java
private static int calculateEditDistance(String str1, String str2) {
    // 使用动态规划计算Levenshtein距离
    // 考虑'*'作为通配符，可以匹配任意字符
}
```

**特点**：
- 适用于长度不同的字符串
- 使用动态规划计算编辑距离
- 考虑'*'作为通配符
- 相似度 = 1 - (编辑距离 / 最大长度)

## 综合匹配策略

### 相似度阈值设置
```java
private static final double NAME_SIMILARITY_THRESHOLD = 0.6;      // 姓名匹配阈值
private static final double ID_NUMBER_SIMILARITY_THRESHOLD = 0.8; // 证件号匹配阈值
private static final double OVERALL_SIMILARITY_THRESHOLD = 0.7;   // 综合匹配阈值
private static final double NAME_WEIGHT = 0.7;                    // 姓名权重
private static final double ID_NUMBER_WEIGHT = 0.3;               // 证件号权重
```

### 综合匹配计算
```java
public static boolean matchMaskedPersonInfo(String inputName, String inputIdNumber, 
                                           String dbName, String dbIdNumber) {
    double nameSimilarity = calculateSimilarityWithDefault(inputName, dbName);
    double idSimilarity = calculateSimilarityWithDefault(inputIdNumber, dbIdNumber);
    
    // 加权平均计算综合相似度
    double overallSimilarity = nameSimilarity * NAME_WEIGHT + idSimilarity * ID_NUMBER_WEIGHT;
    
    return overallSimilarity >= OVERALL_SIMILARITY_THRESHOLD;
}
```

## 业务集成

### 1. 数据库查询
- 在`InsuranceRenewMapper.xml`中添加了`autoQueryCarTransferPolicy`查询
- 查询所有满足时间范围和险种条件的车险保单
- 包含投保人姓名和证件号字段

### 2. 脱敏匹配过滤
```java
public List<TransferPolicyVo> autoQueryCarTransferPolicy(InsuranceRenewDto insuranceRenewDto) {
    // 1. 查询所有满足条件的车险保单
    List<TransferPolicyVo> allPolicies = insuranceRenewMapper.autoQueryCarTransferPolicy(policyVo);
    
    // 2. 进行脱敏匹配过滤
    return filterByMaskMatch(allPolicies, 
                           insuranceRenewDto.getApplicantPersonName(),
                           insuranceRenewDto.getApplicantIdNumber());
}
```

### 3. 流式处理
```java
private List<TransferPolicyVo> filterByMaskMatch(List<TransferPolicyVo> policies,
                                                String inputApplicantName,
                                                String inputApplicantIdNumber) {
    return policies.stream()
            .filter(policy -> MaskMatchUtil.matchMaskedPersonInfo(
                    inputApplicantName, inputApplicantIdNumber,
                    policy.getApplicantPersonName(), policy.getApplicantIdNumber()))
            .collect(Collectors.toList());
}
```

## 测试验证

### 1. 单元测试
- 创建了`MaskMatchUtilTest`类，包含各种测试场景
- 测试正则匹配、相似度计算、综合匹配等功能

### 2. 演示程序
- 创建了`MaskMatchDemo`类，展示真实场景的匹配效果
- 包括同公司脱敏、换公司脱敏、长度不同等场景

## 算法优势

### 1. 灵活性
- 支持脱敏位置和个数不固定的情况
- 支持不同长度的字符串匹配
- 支持多种脱敏规则

### 2. 准确性
- 使用多种算法组合，提高匹配准确率
- 设置合理的相似度阈值
- 综合考虑姓名和证件号的匹配度

### 3. 性能
- 使用流式处理，支持大数据量
- 算法复杂度合理
- 支持并行处理

## 实际应用场景

### 场景1：同一公司，脱敏规则一致
```
第一年: (*阿三, *A**OW) vs 第二年: (*阿三, *A**OW) => 匹配
```

### 场景2：换公司，脱敏规则不同
```
第一年: (*阿三, *A**OW) vs 第二年: (张*三, 甘*8OW) => 匹配
```

### 场景3：一年脱敏一年不脱敏
```
第一年: (*阿三, *A**OW) vs 第二年: (张阿三, 甘A88OW) => 匹配
```

### 场景4：完全不匹配
```
第一年: (*阿三, *A**OW) vs 第二年: (李*四, 京*9OW) => 不匹配
```

## 总结

我们成功实现了您提出的脱敏匹配方案：

1. **使用Java程序处理**：完全用Java实现，集成到现有系统中
2. **字符串相似度算法**：实现了多种相似度计算方法
3. **脱敏字符作为通配符**：'*'字符被正确处理为通配符
4. **相似度阈值**：设置了合理的阈值来判断匹配
5. **正则表达式匹配**：支持将脱敏字符串转换为正则表达式
6. **综合匹配策略**：使用加权平均计算综合相似度

该实现能够有效处理脱敏位置和个数不固定的情况，为车险转投保单查询提供了可靠的脱敏数据匹配能力。
