# 车险脱敏匹配实现说明

## 修改概述

根据业务需求，将脱敏数据匹配工具类从原来的"投保人姓名+身份证号"匹配模式改为"投保人姓名+车牌号"匹配模式，更适合车险业务场景。

## 主要修改内容

### 1. MaskMatchUtil工具类修改

#### 方法名称变更
- `matchMaskedIdNumber()` → `matchMaskedPlateNumber()`
- 参数含义从证件号改为车牌号

#### 阈值和权重调整
```java
// 修改前
private static final double ID_NUMBER_SIMILARITY_THRESHOLD = 0.8;
private static final double NAME_WEIGHT = 0.7;
private static final double ID_NUMBER_WEIGHT = 0.3;

// 修改后
private static final double PLATE_NUMBER_SIMILARITY_THRESHOLD = 0.7;
private static final double NAME_WEIGHT = 0.6;
private static final double PLATE_NUMBER_WEIGHT = 0.4;
```

#### 新增兼容方法
```java
public static boolean matchMaskedCarInsuranceInfo(String inputName, String inputIdNumber, 
                                                 String dbName, String dbIdNumber) {
    return matchMaskedPersonInfo(inputName, inputIdNumber, dbName, dbIdNumber);
}
```

### 2. 数据库查询修改

#### TransferPolicyVo增加车牌号字段
```java
@ApiModelProperty("车牌号")
private String carPlateNo;
```

#### SQL查询增加车牌号关联
```sql
select distinct t.policyNo,t.idNumber,t1.endTime,t4.productName,t1.fhOrderId,t1.productId,t1.planId,t1.paymentTime,t1.startTime,
t1.totalAmount,t1.submitTime,t5.personName as applicantPersonName,t5.idNumber as applicantIdNumber,
COALESCE(t6.car_plate_no, t7.plate_num) as carPlateNo
from sm_order_insured t
left join sm_order t1 on t1.fhOrderId = t.fhOrderId
left join sm_product t4 on t1.productId = t4.id
left join dwd_commodity_main_product_name_map t2 on t1.productId = t2.zhnx_product_id and t1.planId = t2.zhnx_plan_id
left join sm_order_applicant t5 on t5.fhOrderId = t1.fhOrderId
left join sm_order_car t6 on t6.fh_order_id = t1.fhOrderId
left join auto_order_car t7 on t7.order_no = t1.fhOrderId
```

**说明**：
- `t6.car_plate_no`：来自`sm_order_car`表
- `t7.plate_num`：来自`auto_order_car`表
- 使用`COALESCE`函数优先取`sm_order_car`表的车牌号，如果为空则取`auto_order_car`表的车牌号

### 3. 业务逻辑修改

#### InsuranceRenewDto增加车牌号字段
```java
@ApiModelProperty("车牌号")
String carPlateNo;
```

#### 新增车险专用过滤方法
```java
private List<TransferPolicyVo> filterByMaskMatchForCar(List<TransferPolicyVo> policies,
                                                      String inputApplicantName,
                                                      String inputCarPlateNo) {
    return policies.stream()
            .filter(policy -> MaskMatchUtil.matchMaskedPersonInfo(
                    inputApplicantName, inputCarPlateNo,
                    policy.getApplicantPersonName(), policy.getCarPlateNo()))
            .collect(Collectors.toList());
}
```

#### 车牌号获取方法
```java
private String getCarPlateNoFromRenewDto(InsuranceRenewDto insuranceRenewDto) {
    return insuranceRenewDto.getCarPlateNo();
}
```

### 4. 测试用例更新

#### 车牌号匹配测试
```java
@Test
public void testMatchMaskedPlateNumber() {
    // 测试完全相同的情况
    assertTrue(MaskMatchUtil.matchMaskedPlateNumber("甘A88OW", "甘A88OW"));
    
    // 测试脱敏匹配的情况
    assertTrue(MaskMatchUtil.matchMaskedPlateNumber("*A**OW", "甘A88OW"));
    assertTrue(MaskMatchUtil.matchMaskedPlateNumber("甘*8OW", "甘A88OW"));
    
    // 测试不匹配的情况
    assertFalse(MaskMatchUtil.matchMaskedPlateNumber("*B**OW", "甘A88OW"));
}
```

#### 车险特有场景测试
```java
@Test
public void testCarInsuranceSpecificScenarios() {
    // 测试新能源车牌
    assertTrue(MaskMatchUtil.matchMaskedPlateNumber("甘A*88OW", "甘AD88OW"));
    
    // 测试不同地区车牌
    assertFalse(MaskMatchUtil.matchMaskedPlateNumber("甘*88OW", "京A88OW"));
    
    // 测试投保人姓名权重更高的情况
    assertTrue(MaskMatchUtil.matchMaskedPersonInfo("张三", "*B88OW", "张三", "甘A88OW"));
}
```

## 车牌号脱敏匹配特点

### 1. 车牌号格式特点
- **普通车牌**：如"甘A88OW"（6位）
- **新能源车牌**：如"甘AD88OW"（7位）
- **地区标识**：首字符为省份简称
- **字母数字混合**：包含字母和数字

### 2. 脱敏规则适应
- **位置脱敏**：支持任意位置的'*'脱敏
- **多位脱敏**：支持连续多位脱敏
- **长度保持**：脱敏后长度与原车牌号相同

### 3. 匹配策略
- **精确匹配**：优先使用正则表达式精确匹配
- **相似度匹配**：当精确匹配失败时使用相似度算法
- **权重平衡**：投保人姓名权重0.6，车牌号权重0.4

## 业务优势

### 1. 更符合车险业务
- 车牌号是车险的核心标识
- 避免了身份证号可能为虚拟号码的问题
- 提高了匹配的准确性

### 2. 支持多种车牌类型
- 普通燃油车车牌
- 新能源车车牌
- 不同地区车牌

### 3. 灵活的脱敏处理
- 支持不同保险公司的脱敏规则
- 支持脱敏位置和数量的变化
- 保持较高的匹配准确率

## 使用示例

### 输入参数
```java
InsuranceRenewDto renewDto = new InsuranceRenewDto();
renewDto.setApplicantPersonName("*三");  // 脱敏的投保人姓名
renewDto.setCarPlateNo("*A**OW");        // 脱敏的车牌号
```

### 匹配结果
```java
// 数据库中的保单信息
TransferPolicyVo policy = new TransferPolicyVo();
policy.setApplicantPersonName("张三");    // 完整的投保人姓名
policy.setCarPlateNo("甘A88OW");          // 完整的车牌号

// 匹配结果：true（综合相似度 = 1.0 * 0.6 + 1.0 * 0.4 = 1.0 > 0.7）
boolean matched = MaskMatchUtil.matchMaskedPersonInfo("*三", "*A**OW", "张三", "甘A88OW");
```

## 总结

通过将脱敏匹配从"投保人姓名+身份证号"改为"投保人姓名+车牌号"，系统更好地适应了车险业务场景，提高了匹配的准确性和实用性。新的实现支持各种车牌号格式和脱敏规则，为车险转投保单查询提供了可靠的技术支持。
