<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.cfpamf.ms</groupId>
        <artifactId>insurance</artifactId>
        <version>1.3.5.v20220509-SNAPSHOT</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>insurance-service</artifactId>
    <packaging>jar</packaging>

    <name>insurance-service</name>
    <description>小额保险业务微服务</description>
    <distributionManagement>
        <repository>
            <id>CFPAMF</id>
            <name>CFPAMF Repository</name>
            <url>http://nexus.pub.cfpamf.com/repository/releases/</url>
        </repository>
        <snapshotRepository>
            <id>CFPAMF</id>
            <name>CFPAMF Repository</name>
            <url>http://nexus.pub.cfpamf.com/repository/snapshots/</url>
        </snapshotRepository>
    </distributionManagement>
    <repositories>
        <repository>
            <id>public</id>
            <name>Public Repositories</name>
            <url>http://nexus.pub.cfpamf.com/repository/public/</url>
            <releases>
            </releases>
            <snapshots>
            </snapshots>
        </repository>
    </repositories>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>1.8</java.version>
        <spring-boot-mybatis>1.3.2</spring-boot-mybatis>
        <tk-mybatis>1.1.5</tk-mybatis>
        <spring-session-redis>1.3.1.RELEASE</spring-session-redis>
        <spring-config-version>1.3.3.RELEASE</spring-config-version>
        <mybatis-pagehelper>1.2.10</mybatis-pagehelper>
        <apache-poi-version>3.17</apache-poi-version>
        <aliyun-oss-version>3.1.0</aliyun-oss-version>
        <weixin-sdk-version>2.9.9.BETA</weixin-sdk-version>
        <zxing-version>3.3.0</zxing-version>
        <jxls-version>1.0.5</jxls-version>
        <jackson-version>2.8.10</jackson-version>
        <cfpamf-bms-version>1.0.1.v20200713-SNAPSHOT</cfpamf-bms-version>
        <cfpamf-custservice-version>2.3.2-SNAPSHOT</cfpamf-custservice-version>
        <cfpamf-rabbitmq-version>1.0.1</cfpamf-rabbitmq-version>
        <kaptcha-version>2.3.2</kaptcha-version>
        <fastjson-version>1.2.83</fastjson-version>
        <mysql-version>8.0.26</mysql-version>
        <postgresql-version>42.2.5</postgresql-version>
        <jsoup-version>1.11.2</jsoup-version>
        <apache-cxf-version>3.2.4</apache-cxf-version>
        <activiti.version>6.0.0</activiti.version>
        <powermock.version>2.0.9</powermock.version>
        <groovy.version>2.4.12</groovy.version>
        <druid.version>1.1.22</druid.version>
        <insurance-pay.version>1.0.3-SNAPSHOT</insurance-pay.version>
        <opencsv.version>4.3.2</opencsv.version>
        <jcraft-jsch.version>0.1.54</jcraft-jsch.version>
        <zip4j.version>2.9.1</zip4j.version>
    </properties>

    <dependencies>
        <dependency>
            <artifactId>insurance-auth-facade</artifactId>
            <groupId>com.cfpamf.ms.insur.auth</groupId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
        <!--    common包 定义读写数据源切换规则    -->
        <dependency>
            <groupId>com.cfpamf.ms</groupId>
            <artifactId>insurance-common</artifactId>
            <version>1.3.5.v20220509-SNAPSHOT</version>
        </dependency>

        <!--    XStream是一个简单的将javabean转换为XML形式的框架    -->
        <dependency>
            <artifactId>xstream</artifactId>
            <groupId>com.thoughtworks.xstream</groupId>
<!--            <version>1.4.18</version>-->
            <version>1.4.20</version>
        </dependency>

        <!--    分销中心客户端    -->
        <dependency>
            <groupId>com.cfpamf.ms</groupId>
            <artifactId>feign-client-dist</artifactId>
            <version>20230715-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.alibaba</groupId>
                    <artifactId>fastjson</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!--    调用第三方网关服务client    -->
        <dependency>
            <groupId>com.cfpamf.ms</groupId>
            <artifactId>feign-client-thirdGateway</artifactId>
            <version>20220830</version>
            <exclusions>
                <exclusion>
                    <groupId>com.alibaba</groupId>
                    <artifactId>fastjson</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.cfpamf.ms</groupId>
            <artifactId>feign-client</artifactId>
            <version>20250202-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.alibaba</groupId>
                    <artifactId>fastjson</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!--    车车科技签名类    -->
        <dependency>
            <groupId>com.cheche365.cheche.signature</groupId>
            <artifactId>cheche-signature</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
        <!-- excel util start -->
        <!-- https://mvnrepository.com/artifact/com.alibaba/easyexcel -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>2.2.6</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml-schemas</artifactId>
            <version>${apache-poi-version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
            <version>${apache-poi-version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
            <version>${apache-poi-version}</version>
        </dependency>
        <!-- excel util end -->


        <!-- 众安 -->
        <dependency>
            <groupId>com.zhongan.open</groupId>
            <artifactId>za-open-api-sdk</artifactId>
            <version>2.0.1</version>
        </dependency>
        <!-- https://mvnrepository.com/artifact/com.monitorjbl/xlsx-streamer -->
        <!--    读取超大excel时使用    -->
        <dependency>
            <groupId>com.monitorjbl</groupId>
            <artifactId>xlsx-streamer</artifactId>
            <version>2.1.0</version>
        </dependency>



        <!--  公司统一省市区 -->
        <dependency>

            <groupId>com.cfpamf.ms</groupId>
            <artifactId>bizconfigservice-facade</artifactId>
            <version>1.0.0.202000827-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.cloud</groupId>
                    <artifactId>spring-cloud-starter-openfeign</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.cfpamf</groupId>
                    <artifactId>ms-common</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.cfpamf.common</groupId>
                    <artifactId>ms-common</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>io.springfox</groupId>
                    <artifactId>springfox-swagger2</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>io.springfox</groupId>
                    <artifactId>springfox-swagger-ui</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- https://mvnrepository.com/artifact/com.aliyun/aliyun-java-sdk-sts -->
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>aliyun-java-sdk-sts</artifactId>
            <version>3.0.2</version>
        </dependency>

        <!--    pdf处理工具    -->
        <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>itextpdf</artifactId>
            <version>5.5.13</version>
        </dependency>
        <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>itext-asian</artifactId>
            <version>5.2.0</version>
        </dependency>

        <!--    html转pdf    -->
        <dependency>
            <groupId>org.xhtmlrenderer</groupId>
            <artifactId>flying-saucer-pdf</artifactId>
            <version>9.0.3</version>
        </dependency>
        <dependency>
            <groupId>com.itextpdf.tool</groupId>
            <artifactId>xmlworker</artifactId>
            <version>5.4.1</version>
        </dependency>

        <!--    Google开源的优秀图片处理的第三方Java类库    -->
        <dependency>
            <groupId>net.coobird</groupId>
            <artifactId>thumbnailator</artifactId>
            <version>0.4.8</version>
        </dependency>
        <dependency>
            <groupId>org.freemarker</groupId>
            <artifactId>freemarker</artifactId>
            <version>2.3.26-incubating</version>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>com.cfpamf.ms</groupId>-->
<!--            <artifactId>ms-alipayservice-facade</artifactId>-->
<!--            <version>1.0.4-20200602-SNAPSHOT</version>-->
<!--            <exclusions>-->
<!--                <exclusion>-->
<!--                    <groupId>com.cfpamf.common</groupId>-->
<!--                    <artifactId>ms-common</artifactId>-->
<!--                </exclusion>-->
<!--                <exclusion>-->
<!--                    <groupId>org.springframework.cloud</groupId>-->
<!--                    <artifactId>spring-cloud-starter-feign</artifactId>-->
<!--                </exclusion>-->
<!--            </exclusions>-->
<!--        </dependency>-->

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid</artifactId>
            <version>${druid.version}</version>
        </dependency>
        <dependency>
            <groupId>com.cdfinance.cipherer</groupId>
            <artifactId>cipherer-spring-boot-starter</artifactId>
            <version>1.1.0</version>
        </dependency>

        <!--    对象映射转换    -->
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
            <version>1.2.0.Final</version>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-processor</artifactId>
            <version>1.2.0.Final</version>
            <scope>provided</scope>
        </dependency>

        <!--        <dependency>-->
        <!--            <groupId>org.codehaus.groovy</groupId>-->
        <!--            <artifactId>groovy</artifactId>-->
        <!--            <version>${groovy.version}</version>-->
        <!--        </dependency>-->
        <!-- https://mvnrepository.com/artifact/org.codehaus.groovy/groovy-bsf -->
        <!--    groovy脚本调用工具    -->
        <dependency>
            <groupId>org.codehaus.groovy</groupId>
            <artifactId>groovy-bsf</artifactId>
            <version>${groovy.version}</version>
        </dependency>

        <dependency>
            <groupId>org.codehaus.groovy</groupId>
            <artifactId>groovy-all</artifactId>
            <version>${groovy.version}</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
        </dependency>

        <!--   feign     -->
        <dependency>
            <groupId>io.github.openfeign.form</groupId>
            <artifactId>feign-form</artifactId>
            <version>3.8.0</version>
        </dependency>

        <dependency>
            <groupId>io.github.openfeign</groupId>
            <artifactId>feign-httpclient</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>

<!--        <dependency>-->
<!--            <groupId>com.alibaba.csp</groupId>-->
<!--            <artifactId>spring-boot-starter-ahas-sentinel-client</artifactId>-->
<!--&lt;!&ndash;            <version>1.10.13</version>&ndash;&gt;-->
<!--            <version>1.5.2</version>-->
<!--        </dependency>-->
        <dependency>
            <groupId>com.h2database</groupId>
            <artifactId>h2</artifactId>
            <scope>test</scope>
        </dependency>

        <!--    mock工具    -->
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-module-junit4-legacy</artifactId>
            <version>${powermock.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-api-mockito2</artifactId>
            <version>${powermock.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-module-junit4</artifactId>
            <version>${powermock.version}</version>
            <scope>test</scope>
        </dependency>
        <!--    JAVA对象随机初始化工具，单元测试利器    -->
        <dependency>
            <groupId>com.github.jsonzou</groupId>
            <artifactId>jmockdata</artifactId>
            <version>4.3.0</version>
            <scope>test</scope>
        </dependency>

        <!--    guava工具包    -->
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <version>20.0</version>
        </dependency>

        <!--    hutool工具包    -->
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-core</artifactId>
            <version>5.8.27</version>
        </dependency>

        <!--    swagger    -->
        <dependency>
            <groupId>io.swagger</groupId>
            <artifactId>swagger-models</artifactId>
            <version>1.5.20</version>
        </dependency>
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger-ui</artifactId>
            <version>2.9.2</version>
        </dependency>
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger2</artifactId>
            <version>2.9.2</version>
        </dependency>

        <!--    mail    -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-mail</artifactId>
        </dependency>

        <!--    springboot 默认JSON库    -->
        <dependency>
            <groupId>com.fasterxml.jackson.datatype</groupId>
            <artifactId>jackson-datatype-jsr310</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.dataformat</groupId>
            <artifactId>jackson-dataformat-xml</artifactId>
            <version>${jackson-version}</version>
        </dependency>

        <!--    工作流引擎    -->
        <dependency>
            <groupId>org.activiti</groupId>
            <artifactId>activiti-spring-boot-starter-basic</artifactId>
            <version>${activiti.version}</version>
        </dependency>
        <dependency>
            <groupId>org.activiti</groupId>
            <artifactId>activiti-common-rest</artifactId>
            <version>${activiti.version}</version>
        </dependency>

        <!--    UUID工具    -->
        <dependency>
            <groupId>com.fasterxml.uuid</groupId>
            <artifactId>java-uuid-generator</artifactId>
            <version>3.1.3</version>
            <exclusions>
                <exclusion>
                    <groupId>log4j</groupId>
                    <artifactId>log4j</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!--    云流程 支持RESTful风格访问和操控流程引擎    -->
        <dependency>
            <groupId>org.activiti</groupId>
            <artifactId>activiti-rest</artifactId>
            <version>${activiti.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-log4j12</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>io.swagger</groupId>
                    <artifactId>swagger-annotations</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>io.lettuce</groupId>
                    <artifactId>lettuce-core</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>redis.clients</groupId>
            <artifactId>jedis</artifactId>
        </dependency>

        <!--    mybatis全家桶 整合spring    -->
        <dependency>
            <groupId>org.mybatis.spring.boot</groupId>
            <artifactId>mybatis-spring-boot-starter</artifactId>
            <version>${spring-boot-mybatis}</version>
        </dependency>

        <!--    tk mapper全家桶 整合spring    -->
        <dependency>
            <groupId>tk.mybatis</groupId>
            <artifactId>mapper-spring-boot-starter</artifactId>
            <version>${tk-mybatis}</version>
        </dependency>
        <dependency>
            <groupId>org.mybatis</groupId>
            <artifactId>mybatis-typehandlers-jsr310</artifactId>
            <version>1.0.2</version>
        </dependency>

        <!--    springcloud配置中心    -->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-config</artifactId>
            <version>${spring-config-version}</version>
        </dependency>

        <!--    springboot+Rabbit 高级消息队列服务整合    -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-amqp</artifactId>
        </dependency>

        <!--    分页帮手    -->
        <dependency>
            <groupId>com.github.pagehelper</groupId>
            <artifactId>pagehelper-spring-boot-starter</artifactId>
            <version>${mybatis-pagehelper}</version>
            <exclusions>
                <exclusion>
                    <artifactId>mybatis-spring-boot-starter</artifactId>
                    <groupId>org.mybatis.spring.boot</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <!--    阿里云OSS    -->
        <dependency>
            <groupId>com.aliyun.oss</groupId>
            <artifactId>aliyun-sdk-oss</artifactId>
            <version>${aliyun-oss-version}</version>
            <!-- 与加解密依赖的包冲突,所以排除 -->
            <exclusions>
                <exclusion>
                    <groupId>com.aliyun</groupId>
                    <artifactId>aliyun-java-sdk-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.aliyun</groupId>
                    <artifactId>aliyun-java-sdk-ecs</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!--    微信授权工具    -->
        <dependency>
            <groupId>com.github.binarywang</groupId>
            <artifactId>weixin-java-mp</artifactId>
            <version>${weixin-sdk-version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>xstream</artifactId>
                    <groupId>com.thoughtworks.xstream</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <!--    二维码生成与解析    -->
        <dependency>
            <groupId>com.google.zxing</groupId>
            <artifactId>core</artifactId>
            <version>${zxing-version}</version>
        </dependency>
        <dependency>
            <groupId>com.google.zxing</groupId>
            <artifactId>javase</artifactId>
            <version>${zxing-version}</version>
        </dependency>

        <!--    验证码生成    -->
        <dependency>
            <groupId>com.github.penggle</groupId>
            <artifactId>kaptcha</artifactId>
            <version>${kaptcha-version}</version>
        </dependency>

        <!--    excel导出利器    -->
        <dependency>
            <groupId>net.sf.jxls</groupId>
            <artifactId>jxls-core</artifactId>
            <version>${jxls-version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.poi</groupId>
                    <artifactId>poi</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.poi</groupId>
                    <artifactId>poi-ooxml</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.cfpamf.common</groupId>
            <artifactId>ms-common-rabbitmq</artifactId>
            <version>${cfpamf-rabbitmq-version}</version>
        </dependency>

        <!--    MySQL驱动    -->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <version>${mysql-version}</version>
        </dependency>

        <!--    PG库驱动    -->
        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
            <version>${postgresql-version}</version>
        </dependency>

        <!--    ali fastjson    -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>${fastjson-version}</version>
        </dependency>

        <!--    网页爬虫    -->
        <dependency>
            <groupId>org.jsoup</groupId>
            <artifactId>jsoup</artifactId>
            <version>${jsoup-version}</version>
        </dependency>

        <!--    SpringBoot整合Apache CXF    -->
        <dependency>
            <groupId>org.apache.cxf</groupId>
            <artifactId>cxf-spring-boot-starter-jaxws</artifactId>
            <version>${apache-cxf-version}</version>
        </dependency>
        <dependency>
            <groupId>com.cfpamf</groupId>
            <artifactId>ms-common</artifactId>
            <version>0.1.2-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.alibaba</groupId>
                    <artifactId>druid</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>xstream</artifactId>
                    <groupId>com.thoughtworks.xstream</groupId>
                </exclusion>
                <exclusion>
                    <groupId>com.alibaba</groupId>
                    <artifactId>fastjson</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-email</artifactId>
            <version>1.3.3</version>
        </dependency>
        <dependency>
            <groupId>com.cfpamf.ms</groupId>
            <artifactId>insurance-facade</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.cfpamf.ms</groupId>
                    <artifactId>bms-service-facade</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.cfpamf</groupId>
                    <artifactId>ms-common</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>io.springfox</groupId>
                    <artifactId>springfox-swagger2</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>io.springfox</groupId>
                    <artifactId>springfox-swagger-ui</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>jettison</artifactId>
                    <groupId>org.codehaus.jettison</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-lang3</artifactId>
                    <groupId>org.apache.commons</groupId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework.cloud</groupId>
                    <artifactId>spring-cloud-starter-eureka-server</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework.cloud</groupId>
                    <artifactId>spring-cloud-starter-eureka</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework.cloud</groupId>
                    <artifactId>spring-cloud-starter-bus-amqp</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework.cloud</groupId>
                    <artifactId>spring-cloud-netflix-hystrix-stream</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-aop</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>de.codecentric</groupId>
                    <artifactId>spring-boot-admin-server</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>de.codecentric</groupId>
                    <artifactId>spring-boot-admin-starter-client</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.oracle</groupId>
                    <artifactId>ojdbc6</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>mysql</groupId>
                    <artifactId>mysql-connector-java</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.alibaba</groupId>
                    <artifactId>fastjson</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!--    埋点系统    -->
        <dependency>
            <groupId>com.cfpamf.ms</groupId>
            <artifactId>log-facade</artifactId>
            <version>1.3.5.v20220509-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>netty-transport-native-epoll</artifactId>
                    <groupId>io.netty</groupId>
                </exclusion>
                <exclusion>
                    <groupId>io.netty</groupId>
                    <artifactId>netty-codec-http</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!--    apache通用http客户端    -->
        <dependency>
            <groupId>commons-httpclient</groupId>
            <artifactId>commons-httpclient</artifactId>
            <version>3.1</version>
        </dependency>

        <!--        log4j  日志-->
        <!-- https://mvnrepository.com/artifact/log4j/log4j -->
        <dependency>
            <groupId>log4j</groupId>
            <artifactId>log4j</artifactId>
            <version>1.2.17</version>
        </dependency>
        <!--Redis工具类-->
        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson</artifactId>
            <version>3.16.3</version>
        </dependency>

        <!-- xxl 升级-->
        <dependency>
            <groupId>com.xuxueli</groupId>
            <artifactId>xxl-job-core</artifactId>
            <version>2.3.0</version>
        </dependency>
        <!-- 排除springboot中依赖的netty相关的包，使用xxl-job-core中依赖的netty-all -->
        <dependency>
            <groupId>com.cfpamf.common</groupId>
            <artifactId>ms-common</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>ojdbc6</artifactId>
                    <groupId>com.oracle</groupId>
                </exclusion>
                <exclusion>
                    <groupId>io.netty</groupId>
                    <artifactId>netty-codec-http</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>io.netty</groupId>
                    <artifactId>netty-transport-native-epoll</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>xstream</artifactId>
                    <groupId>com.thoughtworks.xstream</groupId>
                </exclusion>
                <exclusion>
                    <groupId>com.alibaba</groupId>
                    <artifactId>fastjson</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- FTPClient需要用到 -->
        <dependency>
            <groupId>commons-net</groupId>
            <artifactId>commons-net</artifactId>
            <version>3.3</version>
        </dependency>

        <!--    SSH2的纯Java实现    -->
        <dependency>
            <groupId>com.jcraft</groupId>
            <artifactId>jsch</artifactId>
            <version>${jcraft-jsch.version}</version>
        </dependency>

        <!-- csv文件解析依赖 -->
        <dependency>
            <groupId>com.opencsv</groupId>
            <artifactId>opencsv</artifactId>
            <version>${opencsv.version}</version>
        </dependency>

        <!--    JAVA处理压缩文件    -->
        <dependency>
            <groupId>net.lingala.zip4j</groupId>
            <artifactId>zip4j</artifactId>
            <version>${zip4j.version}</version>
        </dependency>

        <!--    钉钉SDK    -->
        <dependency>
            <groupId>com.dingtalk.open</groupId>
            <artifactId>taobao-sdk-java-auto_1479188381469</artifactId>
            <version>20190306</version>
        </dependency>

        <!--    阿里云 arms    -->
        <dependency>
            <groupId>com.alibaba.arms.apm</groupId>
            <artifactId>arms-sdk</artifactId>
            <version>1.7.3</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>0.8.7</version>
                <configuration>
                    <excludes>
                        <exclude>**/po/**</exclude>
                        <exclude>**/vo/**</exclude>
                        <exclude>**/dto/**</exclude>
                        <exclude>**/query/**</exclude>
                        <exclude>**/dao/**</exclude>
                        <exclude>**/web/**</exclude>
                        <exclude>**/form/**</exclude>
                        <exclude>**/mapper/**</exclude>
                        <exclude>**/pojo/**</exclude>
                        <exclude>**/enums/**</exclude>
                        <exclude>**/controller/**</exclude>
                        <exclude>**/constant/**</exclude>
                        <exclude>**/exception/**</exclude>
                        <exclude>**/event/**</exclude>
                        <exclude>**/fegin/**</exclude>
                        <exclude>**/annotation/**</exclude>
                        <exclude>**/advice/**</exclude>
                        <exclude>**/base/**</exclude>
                        <exclude>**/message/**</exclude>
                        <exclude>**/job/**</exclude>
                    </excludes>
                </configuration>
                <executions>
                    <execution>
                        <id>prepare-agent</id>
                        <goals>
                            <goal>prepare-agent</goal>
                        </goals>
                        <configuration>
                            <propertyName>jcocoArgLine</propertyName>
                        </configuration>
                    </execution>
                    <execution>
                        <id>report-aggregate</id>
                        <phase>test</phase>
                        <goals>
                            <goal>report</goal>
                        </goals>
                    </execution>
<!--                    <execution>-->
<!--                        <id>default-check</id>-->
<!--                        <goals>-->
<!--                            <goal>check</goal>-->
<!--                        </goals>-->
<!--                    </execution>-->
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>2.9</version>
                <configuration>
                    <argLine>-Xmx1024m -XX:MaxMetaspaceSize=128m ${jcocoArgLine}</argLine>
                    <forkMode>always</forkMode>
                    <parallel>methods</parallel>
                    <threadCount>8</threadCount>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
