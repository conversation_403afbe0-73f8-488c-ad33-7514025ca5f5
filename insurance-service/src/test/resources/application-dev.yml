# 微服务名称
application:
  name: ms-insurance

# 启动端口
server:
  port: 9334

# redis配置
spring:
  # 环境参数
  profiles:
    active: dev
  # 上传文件配置
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB
  http:
    #    multipart:
    #      maxRequestSize: 10Mb
    #      maxFileSize: 10Mb
    encoding:
      charset: UTF-8
      enabled: true
      force: true
    messages:
      encoding: UTF-8

  # 共同数据库配置
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driverClassName: com.mysql.jdbc.Driver
    url: jdbc:h2:mem:demo;MODE=MYSQL;
    minEvictableIdleTimeMillis: 300000
    timeBetweenEvictionRunsMillis: 60000
    poolPreparedStatements: true
    testOnReturn: false
    initialSize: 5
    minIdle: 5
    maxActive: 25
    filters: log4j
    maxWait: 60000
    initialize: true
    maxPoolPreparedStatementPerConnectionSize: 20
    testOnBorrow: false
    testWhileIdle: true
    validationQuery: SELECT 1 FROM DUAL
    username:
    password:
    hikari:
      connection-test-query: SELECT 1 FROM DAUL
      minimum-idle: 3
      maximum-pool-size: 24
      pool-name: ${spring.application.name}-CP
      idle-timeout: 10000
  redis:
    host: r-2zef4d5b5cdab4e4.redis.rds.aliyuncs.com
    port: 6379
    password: Db123456
    database: 120
    timeout: 30000
    #    pool:
    #      max-active: 20
    #      max-idle: 5
    multipart:
      file-size-threshold: 0
      enabled: true
      resolve-lazily: false
      max-request-size: 20MB
      max-file-size: 10MB
    jedis:
      pool:
        max-idle: 5
        max-active: 20
  session:
    store-type: redis
  rabbitmq:
    #    host: rabbitmq.dsg.cfpamf.com
    #    port: 5672
    #    username: admin
    #    password: admin
    host: rabbitmq.tsg.cfpamf.com
    port: 5672
    username: admin
    password: donttelldev
    #### 工作流
  activiti:
    check-process-definitions: false
    jpa-enabled: false
    async-executor-activate: true
  # 发送邮件配置
  mail:
    host: smtp.263.net
    port: 465
    password: 63a7911D82156C04
    username: <EMAIL>
    properties:
      mail:
        smtp:
          ssl:
            enable: true
          socketFactory:
            port: 465
            class: javax.net.ssl.SSLSocketFactory
          auth: true
          starttls:
            enable: true
            required: true

# 不同环境启动权限配置
author:
  # 登录配置
  login:
    expireTime: 43200
    url: http://**************:8086/api/faker/api/authenticate
  # token有效期
  token:
    expireTime: 3600
    lockTime: 60
  # swagger启动配置
  swagger:
    enable: true
  # 登录授权配置
  filter:
    enable: true
  # 实名认证，异常录入有效天数，如果配置小于等于0，每次都需要实名认证，异常录入将失效
  realNameAuth:
    expireDays: 30

# 不同环境数据库配置
ins:
  datasource:
    #    url: ********************************************************************************************************************************************************************************************************************************
    #    username: safes
    #    password: SAFES
    url: *************************************************************************************************************************************************************************************************************************************
    username: safes
    password: Zhnx#safes@T
#    url: ***************************************************************************************************************************************************************************************************
#    username: root
#    password: 123456

dc:
  datasource:
    url: *****************************************************************************************************************
    driverClassName: org.postgresql.Driver
    username: devops
    password: devops123

# 泛华接口参数配置
fanhua:
  # 车险参数
  amInsur:
    # 渠道
    channel:
      id: qd_zhonghenongxin
      secret: zCjBITsovx3FO6NXt6j59fgjfGQXPCaH
    encrypt:
      privateKey: MIICdwIBADANBgkqhkiG9w0BAQEFAASCAmEwggJdAgEAAoGBAO6EbBtBromYnqUPvSq8a4+R+tH40eWC0446GrZs700TfGoF0xcQv+6TKuXMbUu0UmcH5ri/DYlite3toJ4JrrDd20FCSFTOWsABit5hCJRNmynGU7TpRHWjrLSXOaZd0R5nxiXTvjnR4BJz5Q8dJ3b1h+pjLbnAoItPZG+GaPOpAgMBAAECgYEAuVLI0LDLFXwxH91HxPHbvRTWxtjG9cYd7G93G/EuSjvuuk5GQrCwAIX2mdCpx12XfRhli3xe3zWEWBb/amvpf2EK4prSqzWzv3SXFIKPAY4BQyQ0SiA6kUb//D/t+t12smRTlLt5TvOpAH8FIhe87Yosxz9lXEnugy2yI3p2oGUCQQD6UgyseWJcgQTT5Y9c8LzaiSc4U+g8WMmE3j/V+gCsIMoTqGHh+aIuGGwUK/S/BYb1nfuCGlkNyqNqUCjLeFTTAkEA8+3Qv2N7NUfZtodqTndAPY9G3+iNGfirr2SeOzeUTVbSh/qg7u/zlLfRXjtWTMhtqK1FO+CgtLY3tW7YDJG4EwJAK01QRfHFkyz6cdFvSGuYr9E0CKlzLiVJzwNHVbOmtCAD9PyW2il95a1x3Ndxwi2pmAmZPXtjVmBsfnKZbAFH4wJBAL3HDF2a3ES7vdqQyFh71vMOAao6l2zZV1mCAsk3mJ4DKpC4oXTEItJVoQKbT601UnulMvQ+80kla3ow3s4IoRkCQGjQseOaJrrAPVdNUmPrtMUOfdnJeHH69/hxRTLv9FsVVAAz80Xy3Y1JTcTBefJtQFz/VmFS/fWkMSE6JxHTlpk=
    url:
      accessToken: http://icm.52zzb.com/cm/channelService/getToken
      queryPlatInfo: http://icm.52zzb.com/cm/channelService/queryPlatInfo
      innerAuthCode: http://icm.52zzb.com/cm/channelService/getInnerAuthCode
      homePage: https://m.52zzb.com/#/channelIndex?authCode=
      loginPage: http://safes.test.cfpamf.com/wx/#/carLogin?openId=

  # 小额保险参数
  smInsur:
    # 渠道
    channel:
      cid: '05167'
      name: 中和农信
      secret: zhnx
    # 加密
    encrypt:
      aesIv: ceshiJieMa123456
      aesKey: ceshiMiMa1234567
    # 接口url
    url:
      #      submitOrder: https://www.org.baoxian.com/console/shop/third_party_interface!saveOrdersInfo.action?infoParam=
      #      queryOrder: https://www.org.baoxian.com/console/shop/third_party_interface!getOrderInfo.action?infoParam=
      #      payOrder: https://www.org.baoxian.com/orderNew/gotoInfoConfirm.do?isThirdParty=1&channelCode=05167&orderSn=
      submitOrder: https://www.baoxian.com/console/shop/third_party_interface!saveOrdersInfo.action?infoParam=
      queryOrder: https://www.baoxian.com/console/shop/third_party_interface!getOrderInfo.action?infoParam=
      payOrder: https://m.baoxian.com/orderNew/gotoInfoConfirm.do?isThirdParty=1&channelCode=05167&orderSn=


# 微信公众号参数配置
wechat:
  api-domain: https://bmstest.cdfinance.com.cn/api/insurance/micro
  front-domain: https://h5-test.xiaowhale.com
  frontIndexUrl: https://safes.test.cfpamf.com/wx/#/index?openId=%s&from=share&authorization=%s
  frontAccountQRUrl: https://safes.test.cfpamf.com/wx/#/accountQR?openId=%s&authorization=%s
  frontLoginUrl: https://safes.test.cfpamf.com/wx/#/microLogin?openId=%s&authorization=%s&jumpUrl=%s
  frontRenewUrl: https://safes.test.cfpamf.com/wx/#/renew?openId=%s&authorization=%s
  frontClaimProcessUrl: https://safes.test.cfpamf.com/wx/#/claimsSchedule?openId=%s&authorization=%s
  frontClaimDetailUrl: https://safes.test.cfpamf.com/wx/#/claimsScheduleDetail?openId=%s&authorization=%s&id=%s&type=%s&role=%s
  frontClaimTodoUrl: https://safes.test.cfpamf.com/wx/#/claimsTodo?openId=%s&authorization=%s
  frontClaimApprovalUrl: https://safes.test.cfpamf.com/wx/#/claimsApproval?openId=%s&authorization=%s
  frontPolicySearchUrl: https://safes.test.cfpamf.com/wx/#/sp
  front-cancel-detail: https://safes.test.cfpamf.com/wx/#/cancelDetail/%s/%s?cancelId=%s&openId=%s&authorization=%s
  backIndexUrl: https://bms.test.cfpamf.com/api/insurance/micro/wx/index/login?rdl=%s&sid=%s
  backRenewUrl: https://bms.test.cfpamf.com/api/insurance/micro/wx/index/renew
  backClaimProcessUrl: https://bms.test.cfpamf.com/api/insurance/micro/wx/index/claimProgress
  backClaimDetailUrl: https://bms.test.cfpamf.com/api/insurance/micro/wx/index/claimDetail
  backClaimTodoUrl: https://bms.test.cfpamf.com/api/insurance/micro/wx/index/claimTodo
  backClaimApprovalUrl: https://bms.test.cfpamf.com/api/insurance/micro/wx/index/claimApproval
  back-cancel-detail: https://bms.test.cfpamf.com/api/insurance/micro/wx/index/cancel/{cancelId}/{role}/{insuredId}
  backJumpUrl: https://open.weixin.qq.com/connect/oauth2/authorize?appid=wxbd10d23b6f05cf8c&redirect_uri=%s&response_type=code&scope=snsapi_userinfo&state=%s#wechat_redirect
  appId: wxbd10d23b6f05cf8c
  appSecret: cd421d7f830dd0d168dbd1c34cb04b36
  loginErrorUrl: https://safes.test.cfpamf.com/wx/#/otherError?errType=%s&onlyDisplay=true
  message:
    weekRenewTid: yF6CfZeOD5k822ZsWdWlgoO8SWYVLXm9dl79MRlEJw8
    monthRenewTid: yF6CfZeOD5k822ZsWdWlgoO8SWYVLXm9dl79MRlEJw8
    claimResultId: cRCxWuB3BJfCWMqxF-En9jSy6oHGAPdFoabEgiMTWlo
    #    cancelResultId: cRCxWuB3BJfCWMqxF-En9jSy6oHGAPdFoabEgiMTWlo
    cancelResultId: UQyQ7wTTvorsEBNdGOvs_eYaHd6qZRXrd5ekcvYhv2I
    cancelSuccessId: KNhdc44sKk4BsRcbm7YAgO71ZABw-M9ri3iSHDrPOi0
    custNotifyId: ZQs5wUaVRee_Cx9RcU7hLuLpTd-xlUFJFCNj_mqYDqk
    renewalTermWaitPayNotifyId: SBqFhzQ6Sy_WovciyaRrTntPUvXfK85N-3ERDy-0iGk
    renewalTermPaySucceesNotifyId: QwGHHX0RD_FWmonq_Zs0m33rVL63VfhdFXnsiMtoEco


oms:
  oauthUrl: http://oms.cfpamf.org.cn/api/auth/oauth/token
  queryEmploeeUrl: http://oms.cfpamf.org.cn/api/biz/user/list/jobNumber
  account: ***********
  password: ZHNX_111111
  open-url: https://bms-test.cdfinance.com.cn/api/oms/biz/publics
# xxl任务调度
xxl:
  #换成新版本的key值
  job:
    open: false
    admin:
      #新版本的url地址
      addresses: http://pub-xxl-job.tsg.cfpamf.com/xxl-job-admin
    executor:
      appname: insurance-biz
      ip: ************
      port: 9991
      logpath: /Users/<USER>/temp/logs/
      logretentiondays: -1
    accessToken:
  # 需要添加的内容
  newjob:
    admin:
      # dev地址
      #addresses: http://xxl-job2.dsg.cfpamf.com/xxl-job-admin
      # test地址
      addresses: http://xxl-job2.tsg.cfpamf.com/xxl-job-admin
    executor:
      appname: ${spring.application.name}
      ip:
      port: 9991
      logpath: /${spring.application.name}/xxl
      logretentiondays: 5
    accessToken:
bms:
  system:
    id: 3
  api:
    url: http://bms-service.tsg.cfpamf.com/
  role:
    admin: R0001
    bizManager: R0002
    bizSupport: R0003
    claimClerk: R0004
    branchBuManager: R0023
    regionManager: R0005
    zoneManager: R0024
    regionSupervise: R0006
    branchManager: R0007
    branchSupervise: R0008
    branchBiz: R0009
    insBiz: R0010
    riskBiz: R0011
    operBiz: R0012
    dataBiz: R0013
    bizCheck: R0014
    financeBiz: R0021
    subAdmin: R0022
    auditBiz: R0025
    branchBuContact: R0028
    customerManager: R0029
    yongTongManager: R0030
    yongTongBiz: R0031
    insurRegionSuper: R0032
    auditBiz2: R0033
    auditBiz3: R0034
    productManager: R0035
    performanceManager: R0036
    #s47 添加财务结算角色
    financeSettlement: R0044
    #s47 添加保险助理
    insuranceAssistant: R0045
    #s48 添加数据中心
    dataCenter: R0039
  permission:
    customer_sensitive_common: INS_CUSTOMER_SENSITIVE
    customer_sensitive_order_list: CUSTOMER_SENSITIVE_ORDER_LIST
    customer_sensitive_order_cms: CUSTOMER_SENSITIVE_ORDER_CMS
    customer_sensitive_claim_list: CUSTOMER_SENSITIVE_CLAIM_LIST
    customer_sensitive_claim_detail: CUSTOMER_SENSITIVE_CLAIM_DETAIL
    customer_sensitive_customer_list: CUSTOMER_SENSITIVE_CUSTOMER_LIST
    customer_sensitive_customer_detail: CUSTOMER_SENSITIVE_CUSTOMER_DETAIL
  open-url: https://bms-test.cdfinance.com.cn/api/bms/biz/publics

bms-wx:
  system:
    id: 3
  role:
    employeeCode: R0004
    agentCode: R0002
    normalCode: R0003


# 阿里云ocr文字识别配置
aliyun:
  ocr:
    host: http://dm-51.data.aliyun.com
    path: /rest/160601/ocr/ocr_idcard.json
    appcode: 68aba32145ca4bc1aa6d60272d62fc85
    dayLimit: 100
  #key：LTAI4FeQAMY7LFxtNFEPZQax
  #secret：******************************
  #bucket：safesfiles
  # 非生产的新key：
  #key：LTAI4FkaWm37HxWhJWVPU4wv
  #secret：******************************
  #bucket：safesfiles-test
  oss:
    aliyunOssBucket: safesfiles-test
    subBucketImage: image
    aliyunOssEndpoint: https://oss-cn-beijing.aliyuncs.com
    aliyunOssAccessKeyId: LTAI4FkaWm37HxWhJWVPU4wv
    aliyunOssAccessKeySecret: ******************************
    aliyunOssExpiretime: ************
    accessEndpoint: https://${aliyun.oss.aliyunOssBucket}.oss-cn-beijing.aliyuncs.com

# UDC推送配置
# UDC推送配置
udc:
  sms-config:
    mqExchange: LJ.Test.UDC
    mqRoutingKey: cs.udc.receivemessage
    mqQueueName: cs.udc.receivemessage
    renewTemplateCode: 9006
    cacTemplateCode: 9007
    uwsTemplateHcCode: 9008
    uwsTemplateNcCode: 9009
    #s50 续保新增自定义模版，短信消息完全有业务系统自己生成
    selfTemplateCode: DX10005
    uwsGroupTemplateHcCode: DX10001
    uwsGroupTemplateNcCode: DX10002
    uwsPersonTemplateHcCode: DX10003
    uwsPersonTemplateNcCode: DX10004
    insCustNotifyApplCode: insCustNotifyAppl
    applicantTemplateCode: applicantCode
# 中华联合保险对接参数配置
cic:
  partnerCode: ZHNX
  partnerSubCode: 15
  gwChUser: ZHNX
  gwChPwd: ZHNX@PTN
  submitOrderTx: CIC001
  queryOrderTx: CIC004
  prePayOrderTx: 1000
  queryPaymentTx: 1001
  orderUrl: http://*************:6001/NLifeServiceST
  payUrl:  http://*************:6001/PayServiceSt
  paySuccessJumpUrl: https://safes.test.cfpamf.com/api/v1/order/cic/payRedirect?fhOrderId=%s
  paySuccessRedirectUrl: https://safes.test.cfpamf.com/wx/#/userOrder


##
insurance:
  url: http://safes-biz.dsg.cfpamf.com
# 客户中心接口地址
ms-service-customer:
  url: http://ms-customer.dsg.cfpamf.com
ms-alipay-service:
  url: ''
log-gateway:
  url: ''
claim:
  headquarters:
    approver:
      userId: CNBJ0621
      userName: 彭丽敏
    settlements:
      - userId: CNBJ0621
        userName: 彭丽敏
      - userId:  ZHNX11373
        userName: 申佳
      - userId:  ZHNX11408
        userName: 李烨坤
  email:
    channel:
      - name: cic
        code: cic1504
        #        email: <EMAIL>
        email: <EMAIL>
        theme-template: '{insuredPersonName}+{insuredIdNumber}+保险理赔资料'
        content-template: '{insuredPersonName}+{insuredIdNumber}+保险理赔资料+{date}'
        enclosure-template: '{insuredPersonName}+{insuredIdNumber}'
        company: 中华联合
      - name: cic
        code: cic1502
        #        email: <EMAIL>
        email: <EMAIL>
        company: 中华联合
        theme-template: '{insuredPersonName}+{insuredIdNumber}+保险理赔资料'
        content-template: '{insuredPersonName}+{insuredIdNumber}+保险理赔资料+{date}'
        enclosure-template: '{insuredPersonName}+{insuredIdNumber}'
      - name: gsc
        code: gsc
        #        email: <EMAIL>
        email: <EMAIL>
        company: 中国人寿财产保险公司
        theme-template: '{insuredPersonName}+{insuredIdNumber}+保险理赔资料'
        content-template: '{insuredPersonName}+{insuredIdNumber}+保险理赔资料+{date}'
        enclosure-template: '{insuredPersonName}+{insuredIdNumber}'
      - name: dj
        code: dj
        #        email: <EMAIL>
        email: <EMAIL>
        company: 大家财产保险公司
        theme-template: '{insuredPersonName}+{insuredIdNumber}+保险理赔资料'
        content-template: '{insuredPersonName}+{insuredIdNumber}+保险理赔资料+{date}'
        enclosure-template: '{insuredPersonName}+{insuredIdNumber}'
      - name: za
        code: za
        #        email: <EMAIL>
        email: <EMAIL>
        company: 众安在线保险公司
        theme-template: '{insuredPersonName}+{insuredIdNumber}+保险理赔资料'
        content-template: '{insuredPersonName}+{insuredIdNumber}+保险理赔资料+{date}+{applicantCellPhone}'
        enclosure-template: '{insuredPersonName}+{insuredIdNumber}'
      - name: default
        code: default
        #        email: <EMAIL>
        email: <EMAIL>
        company: default
        theme-template: '{insuredPersonName}+{insuredIdNumber}+保险理赔资料'
        content-template: '{insuredPersonName}+{insuredIdNumber}+保险理赔资料+{date}'
        enclosure-template: '{insuredPersonName}+{insuredIdNumber}'
logging:
  level:
    ROOT: info
    tk.mybatis: TRACE
    com.ibatis: debug
    com.cfpamf.ms.insur.admin.dao: debug
    com.cfpamf.ms.insur.weixin.dao: debug
    com.cfpamf.ms.insur.admin.convertedpremium.dao: debug
    com.cfpamf.ms.insur.admin.renewal.dao: debug
    io.swagger: error
  #    org.activiti.engine.impl.persistence.entity: trace
  file: ./logs/access.log


capp:
  pilotLimit: false
  pilotOrgNo: HNPJ,HNLL,HNDK
#management:
#  security:
#    enabled: false

#management:
#  endpoints:
#    enabled-by-default: false


cancel:
  email:
    cic-1504: # 中华联合 保单号第七位起为1504，落地机构为赤峰中支
      email: <EMAIL> #
      title-template: '{companyName}+退保+{insuredPersonName}+{customerAdminName}+{date}'
      content-template: '{bankOfDeposit}'
    cic-1502:  #保单号第七位起为1502，落地机构为包头中支
      email: <EMAIL>
      title-template: '{companyName}+退保+{insuredPersonName}+{customerAdminName}+{date}'
      content-template: '{bankOfDeposit}'
    c4002:   # 泛华渠道——泰康
      email: <EMAIL>
      title-template: '{companyName}+退保+{insuredPersonName}+{customerAdminName}+{date}'
      content-template: '{bankOfDeposit}'


# 保险公司配置 大家
company:
  dj: ## 大家
    gateway: https://z.ab95569.com/ucp/gw
    version: 1.0.0
    channelOneCode: ZHONGHENONGXIN
    channelTwoCode: ZHAPP
    channelThreeCode:
    secret: d41d8cd98f00b204e9800998ecf8427e
    serviceName: #接口名称
      endorse: channel.endorse #撤单
      check: channel.check #个单出单
      sign: channel.sign # 签单
    pay-gateway: https://pgwtest.djbx.com
    pay-trans-source: '059'
    pay-ckvalue: 44193C5533DD1D2D
    pay-apply-entity: '430000'
    ### return /order/dj/payCallback/{orderId}
    pay-notify-url: http://zhengji.dev.cfpamf.com/back/public/order/dj/payCallback/%s
    pay-return-url: http://zhengji.dev.cfpamf.com/back/public/order/dj/payRedirect/%s
    pay-redirect-url: https://safes.test.cfpamf.com/wx/#/userOrder
    pay-pre-url: http://zhengji.dev.cfpamf.com/wx/public/proxy/dj/%s
  error-redirect-url: https://safes.test.cfpamf.com/wx/#/emptyData?errType=%s
  gsc:
    requestType: "01"
    businessNature: "0"
    projectCode: PROG1489
    productCode: P2701156
    programCode: PROG1489-DUIJIE-001
    channelCode: "05"
    gateway: http://47.98.83.232:5083/ESB/P101T/
    pay-gateway: http://106.37.195.142:5120/clppay/_cdMAction.do
    pay-callback-url: http://safes.test.cfpamf.com/api/v1/order/gsc/payCallback
    pay-redirect-url: https://safes.test.cfpamf.com/wx/#/userOrder
    pay-corp-code: zhnx
    pay-key: 123456
    pay-pre-url: http://safes-biz.tsg.cfpamf.com/wx/public/proxy/gsc/%s
  tk:
    token: 1izSlPOezflX0061MQsrrO6MFK21vyNX
    gateway: http://test.cloud.tk.cn/hopen/
    channelCode: '105000000600'
    channel-code-alias: 'yongtong'
    pay-pre-url: ${insurance-pay.frontPayUrl}
    notify-token: c201b3a5356e4904b536076573ca0537
    product-group-map:
      'S20200607': '裸百万+质重+体检+特药'
    online-service-channel-code: cdfin
    online-service-channel-entrance: zhnxrk
    online-service-channel-key: 05fec77b22ff41a4ba9589c7afc537ea
    online-service-channel-type: H5
    online-service-channel-gateway: https://ecuat.tk.cn/IMS
    ai-check-questionnaire-url: http://weixin.yongtongbx.com
    #车险配置
    auto-h5-key: A9CAED282E471031
    auto-h5-iv: none
    auto-h5-channel-id: 71031
    auto-h5-channel-code: '105000000600'
    auto-h5-channel-secret: zhonghe_44ee530a8dce
    auto-gateway: http://wechat.wei.tk.cn/carTKInsure/#/index/home
    auto-validate-id: Q10
    #s46添加雇主责任险回传验签key
    accept-employer-sign-key: Xjxh#123456
    #s47添加雇主责任险支付成功后调整页面
    employer-pay-redirect-url: ${wechat.front-domain}/wx/#/index
  xm:
    share-code: PKKKKwl3
    gateway: https://uat-www.trustlife.com/etrust/webapi/micro
    product-url: https://m.trustlife.com/#/productDetail/%s
    channel-code: 410
    ftp:
      host: ************
      port: 15022
      userName: xiaojing
      passWord: xiaojing
      timeout: 60000
  fx:
    md5Secret: thirdApplyKey
    aesSecret: FOSUN2018
    gateway: https://uat-isales.fosun-uhi.com/cps/third
    th_order_url: ${company.fx.gateway}/getUrl
    #生产：XJXH01
    sourceCode: WEBCPS01
    businessCode: WEBCPS01
    pay-callback-url: https://bms-test.cdfinance.com.cn/api/insurance/micro/back/public/order/fx/payCallback
    pay-return-url: https://bms-test.cdfinance.com.cn/api/insurance/micro/back/public/order/fx/payRedirect
    pay-redirect-url: ${wechat.front-domain}/wx/#/index
    #s46 添加部分产品还是原有对接方式
    #生产地址：http://weixin.fosun-uhi.com/wx/w/pay/ThirdApply
    old-gateway: http://weixintest.fosun-uhi.com/wx/w/pay/ThirdApply
    oldSourceCode: XJXH01
    oldBusinessCode: XJXH01
    old-pay-callback-url: https://bms-test.cdfinance.com.cn/api/insurance/micro/back/public/order/fx/oldPayCallback
    #s47 添加回访退保相关配置
    #回访接口地址：生产地址:http://impthird.fuis.fosun-uhi.com:9292/third/ebiz-entry/ebiz/queryVisitInfo.do
    query-visit-url: http://sit-impthird.fuis.fosun-uhi.com:9292/third/ebiz-entry/ebiz/queryVisitInfo.do
    visit-md5-secret: ewealth
    visit-aes-secret: FOSUN2016
    renewal-grace-days: 70
    ftp:
      host: ***************
      port: 5023
      userName: XJXH01_test
      passWord: dCmJ8mTP
      timeout: 60000


  zhongan:
    #    地址：http://opengw.daily.zhongan.com/Gateway.do
    gateway: http://opengw.daily.zhongan.com/Gateway.do
    app-key: 10001
    #    公钥：
    public-key: MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDIgHnOn7LLILlKETd6BFRJ0GqgS2Y3mn1wMQmyh9zEyWlz5p1zrahRahbXAfCfSqshSNfqOmAQzSHRVjCqjsAw1jyqrXaPdKBmr90DIpIxmIyKXv4GGAkPyJ/6FTFY99uhpiq0qadD/uSzQsefWo0aTvP/65zi3eof7TcZ32oWpwIDAQAB
    #    私钥：
    private-key: MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAO8h8JCJAMb1nd0uBkzZuWyNnL+atBzJKvIG7escD45ODf0AWKr8vSqLZ01HD86+a496CGjsae6GybK8C1MqiMSwaAsIv31nKD6U8xF607MPrD3r2lyjwUnmqBZY++R6yFNYz9ZDXcdiwCudESRsXunPJq7zfnnglCtEH+qqW8/VAgMBAAECgYEAnVc2gtMyKLbZTPuId65WG7+9oDB5S+ttD1xR1P1cmuRuvcYpkS/Eg6a/rJASLZULDpdbyzWqqaAUPD8QMINvAr3ZtkbwH5R0F/4aqOlx/5B0Okjsp3eSK2bQ8J2m/MmFKZxr6Aily7YUDdxcGcjLizsGi1KDkWS22JRufEeUNA0CQQD+g1XJ7ELqmUtrS4m4XnadB25f0g5QC0tMjoa3d9soMzK3q+Drkv8EZVpGSmSHEo1VlE7HUcnKNvK1BO5Nm4iXAkEA8IeZxaWmEcsRqiuFz8xmYGtKcYTmHgheGF4D+fnnFozSNP+3sS1lfgFQrjUkqUyZOoG1hPc6SDhGS4nbXwiscwJASO+gPR58yrgledkK3ZAMk9GWWtVajqu953GMv7UUU//gD+yspzXX6Q2WgkA9cMvrPtQig1I37sAya5e/JvRkfwJARzzCDEmdP9PW7YFqZjrxb0kXiTuFNAviYnEl2FltWb5nW48JBo6dao5VKONQclvfXfagnjriphUUrLatpB3bhQJAKRfJS6jDAIVKt7So5HOdzk4ipxgrMjG/QtZ1grO+VQectk4+tCwdJhOrr5blvdPQvFVqXBQfXuE7cibZrGs4sQ==
    #    接口版本一般都是1.0.0，如有变化开发人员会告知。
    version: 1.0.0
    env: dev
    file-gw-env: tst
    channel-code: B02AYKH
    pay-pre-url: ${insurance-pay.frontPayUrl}
    pay-return-url: https://bms-test.cdfinance.com.cn/api/insurance/micro/back/public/order/fx/payRedirect
    ai-check-channel-code: c20200190755002
    ai-check-token: f49208jxBg
    ai-check-accept-url: ${wechat.api-domain}/back/public/order/ai_check/za/accept/%s
    ai-check-fail-url: ${wechat.api-domain}/back/public/order/ai_check/za/fail/%s
    ai-check-timeout-url: ${wechat.front-domain}/#/otherError?errType=请求超时
    ai-check-front-url: ${wechat.front-domain}/#/aiCheckResult
    ai-check-front-timeout-url: ${wechat.front-domain}/#/aiCheckResult
    ai-check-front-fail-url: ${wechat.front-domain}/#/proDetail/%d
    notify-key: 6ec5fe246e4180b3d53016a8cb7aaa4d
    cancel-notify-key: NoXWI0gnu76DP6Uz
    group-channel-type: YONGTONG
    group-sign-key: 123456
    #s48 绑卡/自动续保
    #绑卡渠道
    protocol-channel: PRODUCT-5301
    #绑卡渠道
    bind-channel-code: c20219741225001
    #绑卡回传加密签名
    bind-sign-key: GH7VM9xMSj
    #保司h5方式的绑卡地址
    #生产地址：https://ihealth.zhongan.com/api/marigold/mobile/bindcardInBetween/gateWay?
    bind-h5-url: https://ihealth-test.zhongan.com/api/marigold/mobile/bindcardInBetween/gateWay?
    #绑卡成功的重定向地址
    bind-callback-url: ${wechat.api-domain}/back/public/order/za/handChannelBindRedirect
    bind-redirect-url: ${wechat.front-domain}/wx/#/index
    #s50 续保保司h5地址
    #生产地址：https://ihealth.zhongan.com/api/marigold/mobile/renewal/gateWay?
    renewal-h5-url: https://ihealth-test.zhongan.com/api/marigold/mobile/renewal/gateWay?
    group-channel-code: XIAOJING
    pay-channel: wxpay,alipay
    srcType: H5
    orderType: INS
    showUrl: https://www.zhongan.com
    notifyUrl: https://bms-test.cdfinance.com.cn/api/insurance/micro/back/public/group/zapay/notify
    merchantCode: 10001
    commodity-subject: 众安团险投保支付
    payGateway: http://cashier.itest.zhongan.com/zanc/gateway.do
    payAppKey: test
    seefee-pay-pre-url:
    group-order-type: seeFee
    file-upload-url: http://filegw-daily.zhongan.com/file/upload
    company-id: 207
  # 车车科技
  cc:
    app-id: 3e6606bee7
    app-secret: 554e17101e
    invite-code: '500150000001'
    name: '申佳'
    product-url: https://i.m.bedrock.chetimes.com/cby/#/channel/zhnx
  jdal:
    aesKey: syscPolicy
system:
  api-domain: ''
ms-bizconfig-service:
  url: http://ms-bizconfig.tsg.cfpamf.com/

insurance-pay:
  url: http://insurance-pay.dsg.cfpamf.com/
  key: caa08fe60b014a14b5503fb2ea60aae1
  source-sys: insur
  frontPayUrl: https://safes.test.cfpamf.com/wx/#/orderPay?orderId=%s
  notify-url: https://safes.test.cfpamf.com/api/v1/order/insurPay/callback
  expired-time: 300

hrms-biz:
  url: http://hrms-biz.tsg.cfpamf.com/

dbc-service:
  url: http://dbc-service.tsg.cfpamf.com/
third-gateway:
  url: http://third-gateway.tsg.cfpamf.com/

#todo 后期配置成集合 统一配置扫描
operation-mq:
  exchange: insurance.operation
  routing-key: activity
  queue: ${operation-mq.exchange}.${operation-mq.routing-key}
special-origin-product:
  productMap:
    same-type-with-time-range:
      - 157,158
    product-sames:
      - 136,233,466,468
product:
  sames:
    - 136,233,466,468
reconciliation:
  allow-error-amount: 0.5


