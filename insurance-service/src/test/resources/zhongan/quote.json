{"channelOrderNo": "ZA21081702640164P", "productCateCode": "XIAOJING", "channelCode": "XIAOJING", "effectiveDate": "20210823000000", "expireDate": "20220822235959", "coveragePeriodType": 3, "coveragePeriod": 12, "orgHolder": {"orgName": "小鲸向海", "contactEmail": "<EMAIL>", "contactMobile": "18127065162", "contactPerson": "曾华光", "orgAddress": "长沙市岳麓区芯城科技园", "orgCertNo": "92233006MA19GL7R7A", "orgCertType": "U", "orgCityCode": "430100", "orgCountryCode": "430104", "orgProvinceCode": "430000"}, "productList": [{"productCode": "P_9001", "productName": "团体意外险", "planType": "01", "hasSocialInsurance": "Y", "professionType": 1, "clauseList": [{"clauseCode": "7H0", "commissionRate": "0.45", "clauseAmount": 0.0, "liabilityList": [{"liabCode": "ZXF017", "liabAmount": 50000, "liabFactorList": [{"factorCode": "", "factorValue": ""}]}], "clauseFactorList": [{"factorCode": "", "factorValue": ""}]}]}], "insuredList": [{"channelUserNo": "1", "certNo": "371326198707100496", "certType": "I", "hasSocialInsurance": "Y", "name": "张三", "productCode": "P_9001", "profession": "0101009", "relationToMaster": "1"}, {"channelUserNo": "2", "certNo": "430381199002047423", "certType": "I", "hasSocialInsurance": "Y", "name": "李四", "productCode": "P_9001", "profession": "0101009", "relationToMaster": "1"}, {"channelUserNo": "3", "certNo": "232301198603014190", "certType": "I", "hasSocialInsurance": "Y", "name": "王五", "productCode": "P_9001", "profession": "0101009", "relationToMaster": "1"}]}