{"channelCode": "105000000600", "productCode": "11N00569", "comboCode": "11N00574", "orgId": "87f59b2466024d7ba5fd81e2b5429980", "channelContractNo": "640435762976698368", "requestFlag": "1", "proposalTime": "20220629105959", "effectiveTime": "20220630000000", "expiredTime": "20220729235959", "successUrl": "https://www.baidu.com", "failUrl": "https://www.sina.com.cn", "platformId": "PUB_ONLINE", "commissionRate": "0.24", "businessSource": "DBAA", "reqIp": "*************", "isCashier": "0", "payerInfo": {"payerName": "fkr", "payerCredentialType": "01", "payerCredentialNo": "342600199212166760", "payerPhoneNumber": "***********"}, "planList": [{"planCode": "IYTY01", "planName": "IYTY01", "dynamicFactorList": {"deductible": "0", "compensationCroportionNotRelatedToSocialSecurity": "100", "businessLevel": "3"}, "dutyList": [{"amount": 30000000, "dutyFreeCode": "PYW00034Z033", "premium": 9200, "taxableCode": "PYW00034Z033"}, {"amount": 1800000, "dutyFreeCode": "AYL00027Z832", "premium": 2300, "taxableCode": "AYL00103Z832"}, {"amount": 3000000, "dutyFreeCode": "AYL00027Z831", "premium": 8600, "taxableCode": "AYL00103Z831"}]}], "personalPolicyList": [{"channelPolicyNo": "640435812649840640", "planNo": "IYTY01", "paymentType": "5", "personEffectiveTime": "20220630000000", "name": "小米", "credentialType": "01", "credentialNo": "120101199003071294", "isPrimary": "Y", "socialSecurityType": "2", "primaryRelation": "1", "phoneNumber": "***********", "holderRelation": "9", "businessCode": "00103004", "businessLevel": "3", "email": "<EMAIL>"}, {"channelPolicyNo": "640435813996212224", "planNo": "IYTY01", "paymentType": "5", "personEffectiveTime": "20220630000000", "name": "大米", "credentialType": "01", "credentialNo": "120101199003074778", "isPrimary": "Y", "socialSecurityType": "2", "primaryRelation": "1", "phoneNumber": "***********", "holderRelation": "9", "businessCode": "00103004", "businessLevel": "3", "email": "<EMAIL>"}, {"channelPolicyNo": "640435813996212224", "planNo": "IYTY01", "paymentType": "5", "personEffectiveTime": "20220630000000", "name": "百米", "credentialType": "01", "credentialNo": "120101199003071016", "isPrimary": "Y", "socialSecurityType": "2", "primaryRelation": "1", "phoneNumber": "***********", "holderRelation": "9", "businessCode": "00103004", "businessLevel": "3", "email": "<EMAIL>"}]}