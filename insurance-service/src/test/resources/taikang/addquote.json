{"orgId": "A21000100571", "businessSource": "DBAA", "channelCode": "105000000600", "productCode": "11N00569", "comboCode": "11N00574", "channelContractNo": "TK22070802645504P", "proposalTime": "20220707194021", "requestFlag": "2", "failUrl": "https://www.baidu.com", "groupPolicyNo": "8H2205DA1A2C8D8", "isCashier": "0", "reqIp": "0:0:0:0:0:0:0:1", "successUrl": "https://www.baidu.com", "effectiveTime": "20220630000000", "expiredTime": "20220729235959", "correctEffectiveDate": "20220708000000", "platformId": "WAP", "payerInfo": {"payerCredentialNo": "13253219600510085x", "payerCredentialType": "01", "payerName": "今晚打老虎", "payerPhoneNumber": "***********"}, "personalPolicyList": [{"businessCode": "02001004", "businessLevel": "3", "channelPolicyNo": "sdfsdfsdf223232", "credentialNo": "340102199003076199", "credentialType": "01", "personEffectiveTime": "20220708000000", "gender": "1", "holderRelation": "9", "isPrimary": "Y", "name": "wangba", "paymentType": "5", "planNo": "IYTY01", "primaryRelation": "1", "socialSecurityType": "1"}], "planList": [{"dutyList": [{"amount": 10000000, "dutyFreeCode": "PYW00034Z033", "taxableCode": "PYW00034Z033", "premium": 242}], "dynamicFactorList": {"businessLevel": "3", "compensationCroportionNotRelatedToSocialSecurity": 100, "deductible": 0}, "personCount": 1, "planCode": "IYTY01"}]}