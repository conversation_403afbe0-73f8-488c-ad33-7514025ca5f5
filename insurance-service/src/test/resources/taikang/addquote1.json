{"channelCode": "105000000600", "comboCode": "11N00574", "commissionRate": "0.15", "correctEffectiveDate": "20220729000000", "effectiveTime": "20220723000000", "expiredTime": "20230722235959", "groupPeriod": 12, "groupPeriodUnit": "M", "insureType": "2", "groupPolicyNo": "8H2205DA1A32T28", "personalPolicyList": [{"businessCode": "00305001", "businessLevel": "3", "channelPolicyNo": "sdfsdfsdf223232", "credentialNo": "440103199003076850", "credentialType": "01", "gender": "1", "holderRelation": "9", "isPrimary": "Y", "name": "刘一", "paymentType": "5", "personEffectiveTime": "20220723000000", "planNo": "Tai_Kang_3", "primaryRelation": "1", "socialSecurityType": "1"}], "planList": [{"dutyList": [{"amount": 10000000, "dutyFreeCode": "PYW00034Z033", "taxableCode": "PYW00034Z033"}, {"amount": 1000000, "dutyFreeCode": "AYL00027Z831", "taxableCode": "AYL00103Z831"}, {"amount": 1800000, "dutyFreeCode": "AYL00027Z832", "taxableCode": "AYL00103Z832"}], "dynamicFactorList": {"businessLevel": "3", "compensationCroportionNotRelatedToSocialSecurity": 100, "deductible": 0}, "personCount": 1, "planCode": "Tai_Kang_3"}], "productCode": "11N00569", "requestFlag": "2"}