{"businessSource": "DBAA", "channelCode": "105000000600", "channelContractNo": "TKPAY22072702645885P", "comboCode": "11N00574", "correctEffectiveDate": "20220729000000", "effectiveTime": "20220728000000", "expiredTime": "20230727235959", "groupPeriod": 12, "groupPeriodUnit": "M", "groupPolicyNo": "8H2205DA1A33K68", "isCashier": "0", "orgId": "87f59b2466024d7ba5fd81e2b5429980", "platformId": "WAP", "productCode": "11N00569", "proposalTime": "20220727141121", "reqIp": "************", "requestFlag": "2", "successUrl": "https://www.baidu.com", "failUrl": "https://www.baidu.com", "payerInfo": {"payerCredentialNo": "91110117690003056C", "payerCredentialType": "3", "payerName": "北京小鲸向海保险代理有限公司", "payerPhoneNumber": "***********"}, "personalPolicyList": [{"businessCode": "00202003", "businessLevel": "3", "channelPolicyNo": "27071effe4f24292a72e251dd7221212", "credentialNo": "370102199003078579", "credentialType": "01", "gender": "1", "holderRelation": "9", "isPrimary": "Y", "name": "王一", "paymentType": "5", "personEffectiveTime": "20220729000000", "planNo": "Tai_Kang_3", "primaryRelation": "1", "socialSecurityType": "1"}], "planList": [{"dutyList": [{"amount": 10000000, "dutyFreeCode": "PYW00034Z033", "premium": 3291, "taxableCode": "PYW00034Z033"}, {"amount": 1800000, "dutyFreeCode": "AYL00027Z832", "premium": 3291, "taxableCode": "AYL00103Z832"}, {"amount": 1000000, "dutyFreeCode": "AYL00027Z831", "premium": 4089, "taxableCode": "AYL00103Z831"}], "dynamicFactorList": {"businessLevel": "3", "compensationCroportionNotRelatedToSocialSecurity": 100, "deductible": 0}, "personCount": 1, "planCode": "Tai_Kang_3"}]}