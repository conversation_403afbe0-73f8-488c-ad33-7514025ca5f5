{"businessSource": "DBAA", "channelCode": "105000000600", "channelContractNo": "ZA22080302646060P", "comboCode": "11N00574", "commissionRate": "0.15", "effectiveTime": "20220804000000", "expireTime": "120", "expiredTime": "20230803235959", "failUrl": "https://bmstest.cdfinance.com.cn/api/insurance/micro/back/public/order/common/tkPay/payRedirect/ZA22080302646060P", "groupPeriod": 12, "groupPeriodUnit": "M", "isCashier": "1", "orgId": "87f59b2466024d7ba5fd81e2b5429980", "payerInfo": {"payerCredentialNo": "91110117690003056C", "payerCredentialType": "3", "payerName": "北京小鲸向海保险代理有限公司", "payerPhoneNumber": "***********"}, "personalPolicyList": [{"businessCode": "00103002", "businessLevel": "1", "channelPolicyNo": "ae83b88ad90d4c00b026ec33ec9e7dae", "credentialNo": "140105199003071373", "credentialType": "01", "gender": "1", "holderRelation": "9", "isPrimary": "Y", "name": "王一", "paymentType": "5", "personEffectiveTime": "20220804000000", "planNo": "Tai_Kang_1", "primaryRelation": "1", "socialSecurityType": "1"}, {"businessCode": "00103002", "businessLevel": "1", "channelPolicyNo": "e1dbc9765c5c4601b6080b36f57b2280", "credentialNo": "140105199003077038", "credentialType": "01", "gender": "1", "holderRelation": "9", "isPrimary": "Y", "name": "王二", "paymentType": "5", "personEffectiveTime": "20220804000000", "planNo": "Tai_Kang_1", "primaryRelation": "1", "socialSecurityType": "1"}, {"businessCode": "00103002", "businessLevel": "1", "channelPolicyNo": "77846a3b90294bd1ab8e04ee8343845b", "credentialNo": "140105199003071672", "credentialType": "01", "gender": "1", "holderRelation": "9", "isPrimary": "Y", "name": "王三", "paymentType": "5", "personEffectiveTime": "20220804000000", "planNo": "Tai_Kang_1", "primaryRelation": "1", "socialSecurityType": "1"}, {"businessCode": "00103004", "businessLevel": "3", "channelPolicyNo": "b29bac6e519e44c99b6dc5a92e439fca", "credentialNo": "140105199003076238", "credentialType": "01", "gender": "1", "holderRelation": "9", "isPrimary": "Y", "name": "王四", "paymentType": "5", "personEffectiveTime": "20220804000000", "planNo": "Tai_Kang_3", "primaryRelation": "1", "socialSecurityType": "1"}, {"businessCode": "00103004", "businessLevel": "3", "channelPolicyNo": "b226b1818a3640b4887f0b0eb0e34e63", "credentialNo": "140105199003074734", "credentialType": "01", "gender": "1", "holderRelation": "9", "isPrimary": "Y", "name": "王五", "paymentType": "5", "personEffectiveTime": "20220804000000", "planNo": "Tai_Kang_3", "primaryRelation": "1", "socialSecurityType": "1"}], "planList": [{"dutyList": [{"amount": 30000000, "dutyFreeCode": "PYW00034Z033", "premium": 9200, "taxableCode": "PYW00034Z033"}, {"amount": 1800000, "dutyFreeCode": "AYL00027Z832", "premium": 2300, "taxableCode": "AYL00103Z832"}, {"amount": 3000000, "dutyFreeCode": "AYL00027Z831", "premium": 8600, "taxableCode": "AYL00103Z831"}], "dynamicFactorList": {"businessLevel": "1", "compensationCroportionNotRelatedToSocialSecurity": 100, "deductible": 0}, "personCount": 3, "planCode": "Tai_Kang_1", "planName": "Tai_Kang_1"}, {"dutyList": [{"amount": 30000000, "dutyFreeCode": "PYW00034Z033", "kindCode": "PYW00034Z033", "premium": 13200, "taxableCode": "PYW00034Z033"}, {"amount": 1800000, "dutyFreeCode": "AYL00027Z832", "kindCode": "AYL00027Z832", "premium": 3300, "taxableCode": "AYL00103Z832"}, {"amount": 3000000, "dutyFreeCode": "AYL00027Z831", "kindCode": "AYL00027Z831", "premium": 12700, "taxableCode": "AYL00103Z831"}], "dynamicFactorList": {"businessLevel": "3", "compensationCroportionNotRelatedToSocialSecurity": 100, "deductible": 0}, "personCount": 2, "planCode": "Tai_Kang_3", "planName": "Tai_Kang_3"}], "platformId": "PUB_ONLINE", "productCode": "11N00569", "proposalTime": "20220803182902", "reqIp": "*************", "requestFlag": "1", "successUrl": "https://bmstest.cdfinance.com.cn/api/insurance/micro/back/public/order/common/tkPay/payRedirect/ZA22080302646060P"}