package org.mockito.configuration;

import com.alibaba.fastjson.util.ParameterizedTypeImpl;
import com.cfpamf.ms.insur.admin.service.BaseTest;
import com.cfpamf.ms.insur.base.dao.MyMappler;
import com.github.jsonzou.jmockdata.JMockData;
import com.github.jsonzou.jmockdata.TypeReference;
import org.mockito.stubbing.Answer;

import java.lang.reflect.Method;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR> 2020/3/10 14:54
 */
public class MockitoConfiguration extends DefaultMockitoConfiguration {

    @Override
    public Answer<Object> getDefaultAnswer() {
        return invocation -> {

            try {
                Object mock1 = invocation.getMock();
                ParameterizedType type = null;

                if (mock1 instanceof MyMappler) {
                    Class<?> aClass = mock1.getClass();
                    String name = aClass.getName();
                    String realClaz = name.substring(0, name.indexOf("$MockitoMock"));
                    Class<?> aClass1 = Class.forName(realClaz);
                    type = (ParameterizedType) aClass1.getGenericInterfaces()[0];
                }
                Method method = invocation.getMethod();
                Class<?> returnType = method.getReturnType();
                if (returnType != void.class && returnType != Void.class) {

                    Type genericReturnType = method.getGenericReturnType();

                    Map<String, Type> val = new HashMap<>(1);
                    val.put("val", genericReturnType);
                    Object mock = null;
                    if (Objects.equals(returnType.toString(), genericReturnType.toString())) {
                        mock = JMockData.mock(returnType, BaseTest.defaultMockConfig());
                    } else {
                        String s = genericReturnType.toString();
                        if (s.contains("<T>")) {
                            if (genericReturnType instanceof ParameterizedType) {
                                ParameterizedType tmp = (ParameterizedType) genericReturnType;
                                genericReturnType = new ParameterizedTypeImpl(
                                        type.getActualTypeArguments(), tmp.getOwnerType(), tmp.getRawType());
                                val.put("val", genericReturnType);
                            }

                        } else if (Objects.equals(s, "T")) {
                            val.put("val", type.getActualTypeArguments()[0]);
                        }
                        mock = JMockData.mock(new TypeReference<Object>() {
                            @Override
                            public Type getType() {
                                return val.get("val");
                            }

                        }, BaseTest.defaultMockConfig());
                    }
                    return mock;
                }
            } catch (Exception e) {

                return null;
            }
            return null;
        };
    }

}
