package com.cfpamf.ms.insur.admin.importor;

import com.cfpamf.ms.insur.admin.dao.safes.AuthUserMapper;
import com.cfpamf.ms.insur.admin.dao.safes.SmProductMapper;
import com.cfpamf.ms.insur.admin.dao.safes.TmpOrderValidPolicyMapper;
import com.cfpamf.ms.insur.admin.dao.safes.product.risk.SysRiskMapper;
import com.cfpamf.ms.insur.admin.enums.EnumChannel;
import com.cfpamf.ms.insur.admin.pojo.dto.SmOrderExcelDTO;
import com.cfpamf.ms.insur.admin.pojo.dto.SmOrderImportDTO;
import com.cfpamf.ms.insur.admin.service.BaseTest;
import com.cfpamf.ms.insur.admin.service.OrderNoGenerator;
import com.cfpamf.ms.insur.admin.service.SmCommonSettingService;
import com.cfpamf.ms.insur.admin.service.SmPolicyRegisterService;
import com.cfpamf.ms.insur.admin.validation.SmOfflineOrderValidation;
import com.cfpamf.ms.insur.admin.validation.ValidContext;
import com.cfpamf.ms.insur.admin.validation.ValidationResult;
import com.cfpamf.ms.insur.base.constant.ChannelConstant;
import com.cfpamf.ms.insur.base.util.HttpRequestUtil;
import com.github.jsonzou.jmockdata.JMockData;
import com.github.jsonzou.jmockdata.TypeReference;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import javax.validation.Validator;
import java.util.*;
import java.util.stream.Collectors;

//@RunWith(PowerMockRunner.class)
//@PrepareForTest({HttpRequestUtil.class})
//public class OrderValidationTest extends BaseTest {
//
//    @Mock
//    Validator validator;
//
//    @Mock
//    SmProductMapper productMapper;
//
//    @Mock
//    SmCommonSettingService commonSettingService;
//
//    @Mock
//    AuthUserMapper authUserMapper;
//
//    @Mock
//    TmpOrderValidPolicyMapper tmpOrderValidPolicyMapper;
//
//    @Mock
//    OrderNoGenerator orderNoGenerator;
//
//    @Mock
//    SmPolicyRegisterService registerService;
//
//    @Mock
//    SysRiskMapper riskMapper;
//
//    @InjectMocks
//    SmOfflineOrderValidation validation;
//
//    @Before
//    public void before() throws Exception {
//        super.setUp();
//    }

//    public ValidContext<SmOrderExcelDTO> mockContext(){
//        ValidContext<SmOrderExcelDTO> context = new ValidContext();
//        context.setChannel(EnumChannel.ZA.getCode());
//
//        TypeReference<List<SmOrderExcelDTO>> tr = new TypeReference<List<SmOrderExcelDTO>>() {
//        };
//        List<SmOrderExcelDTO> data = JMockData.mock(tr);
//        context.setResult(data);
//
//        List<ValidationResult<SmOrderExcelDTO>> vas = data.stream()
//                .map(ValidationResult::success)
//                .collect(Collectors.toList());
//        Map<Boolean, List<ValidationResult<SmOrderExcelDTO>>> tmap = new HashMap<>();
//        vas.forEach(a->{
//            Boolean part = a.isSuccess();
//            List<ValidationResult<SmOrderExcelDTO>> validPart = tmap.get(part);
//            if(validPart==null){
//                validPart = new ArrayList<>();
//            }
//            validPart.add(a);
//            tmap.put(part,validPart);
//        });
//        context.setResult(data);
//        context.setValidationMap(tmap);
//        context.setValidationData(vas);
//        return context;
//    }
//
//    @Test
//    public void validPlan() {
//        ValidContext<> context = mockContext();
//        validation.validPlan(context);
//        /**
//         * No Error
//         */
//        Assert.assertTrue(Boolean.TRUE);
//    }
//
//    @Test
//    public void validRecommend() {
//        ValidContext context = mockContext();
//        validation.validRecommend(context);
//        /**
//         * No Error
//         */
//        Assert.assertTrue(Boolean.TRUE);
//    }
//
//
//    @Test
//    public void convertDict() {
//        ValidContext context = mockContext();
//        validation.convertDict(context,false);
//        validation.convertDict(context,true);
//        /**
//         * No Error
//         */
//        Assert.assertTrue(Boolean.TRUE);
//    }
//
//    @Test
//    public void validRisk() {
//        ValidContext context = mockContext();
//        validation.validRisk(context);
//        /**
//         * No Error
//         */
//        Assert.assertTrue(Boolean.TRUE);
//    }
//
//    @Test
//    public void validDuplicate4LongTerm() {
//        ValidContext context = mockContext();
//        SmOrderImportDTO dto = JMockData.mock(SmOrderImportDTO.class);
//        validation.validDuplicate4LongTerm(dto,context);
//        /**
//         * No Error
//         */
//        Assert.assertTrue(Boolean.TRUE);
//    }
//
//    @Test
//    public void validPremium4LongTerm() {
//        ValidContext context = mockContext();
//        validation.validPremium4LongTerm(context);
//        /**
//         * No Error
//         */
//        Assert.assertTrue(Boolean.TRUE);
//    }
//}
