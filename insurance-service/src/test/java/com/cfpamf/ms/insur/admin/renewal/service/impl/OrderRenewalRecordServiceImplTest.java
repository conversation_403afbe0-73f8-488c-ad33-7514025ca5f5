package com.cfpamf.ms.insur.admin.renewal.service.impl;

import com.cfpamf.ms.insur.admin.dao.safes.order.SmOrderInsuredMapper;
import com.cfpamf.ms.insur.admin.pojo.dto.order.OrderRenewalDTO;
import com.cfpamf.ms.insur.admin.pojo.po.SmOrderInsured;
import com.cfpamf.ms.insur.admin.renewal.dao.OrderRenewalMapper;
import com.cfpamf.ms.insur.admin.renewal.entity.OrderRenewal;
import com.cfpamf.ms.insur.admin.renewal.enums.OrderRenewalStatus;
import com.cfpamf.ms.insur.admin.renewal.enums.OrderRenewalType;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class OrderRenewalRecordServiceImplTest {

    @Mock
    private OrderRenewalMapper mockOrderRenewalMapper;
    @Mock
    private SmOrderInsuredMapper mockSmOrderInsuredMapper;
    @Mock
    private OrderRenewalRecordServiceImpl orderRenewalRecordServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        orderRenewalRecordServiceImplUnderTest = new OrderRenewalRecordServiceImpl(mockOrderRenewalMapper,
                mockSmOrderInsuredMapper);
    }

    @Test
    public void testCreate() {
        // Setup
        // Configure OrderRenewalMapper.getByOldOrderId(...).
        final OrderRenewal orderRenewal = new OrderRenewal();
        orderRenewal.setCreateBy("createBy");
        orderRenewal.setUpdateBy("createBy");
        orderRenewal.setOldOrderId("oldOrderId");
        orderRenewal.setOldPolicyNo("oldPolicyNo");
        orderRenewal.setNewOrderId("newOrderId");
        orderRenewal.setType(OrderRenewalType.CHANGE);
        orderRenewal.setStatus(OrderRenewalStatus.WAITED);
        orderRenewal.setIdNumber("idNumber");
        final List<OrderRenewal> orderRenewalList = Arrays.asList(orderRenewal);
        mockOrderRenewalMapper.getByOldOrderId("oldOrderId");

        // Configure SmOrderInsuredMapper.selectByOrderId(...).
        final SmOrderInsured smOrderInsured = new SmOrderInsured();
        smOrderInsured.setId(0);
        smOrderInsured.setFhOrderId("fhOrderId");
        smOrderInsured.setIdNumber("idNumber");
        smOrderInsured.setPolicyNo("oldPolicyNo");
        smOrderInsured.setEnabledFlag(0);
        final List<SmOrderInsured> insureds = Arrays.asList(smOrderInsured);
        mockSmOrderInsuredMapper.selectByOrderId("oldOrderId");

        // Run the test
        orderRenewalRecordServiceImplUnderTest.create("oldOrderId", "newOrderId");

        // Verify the results
        // Confirm OrderRenewalMapper.insertList(...).
        final OrderRenewal orderRenewal1 = new OrderRenewal();
        orderRenewal1.setCreateBy("createBy");
        orderRenewal1.setUpdateBy("createBy");
        orderRenewal1.setOldOrderId("oldOrderId");
        orderRenewal1.setOldPolicyNo("oldPolicyNo");
        orderRenewal1.setNewOrderId("newOrderId");
        orderRenewal1.setType(OrderRenewalType.CHANGE);
        orderRenewal1.setStatus(OrderRenewalStatus.WAITED);
        orderRenewal1.setIdNumber("idNumber");
        final List<OrderRenewal> list = Arrays.asList(orderRenewal1);
        mockOrderRenewalMapper.insertList(list);
    }

    @Test
    public void testCreate_OrderRenewalMapperGetByOldOrderIdReturnsNoItems() {
        // Setup
        when(mockOrderRenewalMapper.getByOldOrderId("oldOrderId")).thenReturn(Collections.emptyList());

        // Configure SmOrderInsuredMapper.selectByOrderId(...).
        final SmOrderInsured smOrderInsured = new SmOrderInsured();
        smOrderInsured.setId(0);
        smOrderInsured.setFhOrderId("fhOrderId");
        smOrderInsured.setIdNumber("idNumber");
        smOrderInsured.setPolicyNo("oldPolicyNo");
        smOrderInsured.setEnabledFlag(0);
        final List<SmOrderInsured> insureds = Arrays.asList(smOrderInsured);
        mockSmOrderInsuredMapper.selectByOrderId("oldOrderId");

        // Run the test
        orderRenewalRecordServiceImplUnderTest.create("oldOrderId", "newOrderId");

        // Verify the results
        // Confirm OrderRenewalMapper.insertList(...).
        final OrderRenewal orderRenewal = new OrderRenewal();
        orderRenewal.setCreateBy("createBy");
        orderRenewal.setUpdateBy("createBy");
        orderRenewal.setOldOrderId("oldOrderId");
        orderRenewal.setOldPolicyNo("oldPolicyNo");
        orderRenewal.setNewOrderId("newOrderId");
        orderRenewal.setType(OrderRenewalType.CHANGE);
        orderRenewal.setStatus(OrderRenewalStatus.WAITED);
        orderRenewal.setIdNumber("idNumber");
        final List<OrderRenewal> list = Arrays.asList(orderRenewal);
        mockOrderRenewalMapper.insertList(list);
    }

    @Test
    public void testCreate_SmOrderInsuredMapperReturnsNoItems() {
        // Setup
        // Configure OrderRenewalMapper.getByOldOrderId(...).
        final OrderRenewal orderRenewal = new OrderRenewal();
        orderRenewal.setCreateBy("createBy");
        orderRenewal.setUpdateBy("createBy");
        orderRenewal.setOldOrderId("oldOrderId");
        orderRenewal.setOldPolicyNo("oldPolicyNo");
        orderRenewal.setNewOrderId("newOrderId");
        orderRenewal.setType(OrderRenewalType.CHANGE);
        orderRenewal.setStatus(OrderRenewalStatus.WAITED);
        orderRenewal.setIdNumber("idNumber");
        final List<OrderRenewal> orderRenewalList = Arrays.asList(orderRenewal);
        mockOrderRenewalMapper.getByOldOrderId("oldOrderId");

        mockSmOrderInsuredMapper.selectByOrderId("oldOrderId");

        // Run the test
        orderRenewalRecordServiceImplUnderTest.create("oldOrderId", "newOrderId");

        // Verify the results
    }

    @Test
    public void testGetInsurancePolicyLatestRecord() {
        // Setup
        // Configure OrderRenewalMapper.getInsurancePolicyLatestRecord(...).
        final OrderRenewal orderRenewal = new OrderRenewal();
        orderRenewal.setCreateBy("createBy");
        orderRenewal.setUpdateBy("createBy");
        orderRenewal.setOldOrderId("oldOrderId");
        orderRenewal.setOldPolicyNo("oldPolicyNo");
        orderRenewal.setNewOrderId("newOrderId");
        orderRenewal.setType(OrderRenewalType.CHANGE);
        orderRenewal.setStatus(OrderRenewalStatus.WAITED);
        orderRenewal.setIdNumber("idNumber");
        mockOrderRenewalMapper.getInsurancePolicyLatestRecord("policyNo");

        // Run the test
        final OrderRenewal result = orderRenewalRecordServiceImplUnderTest.getInsurancePolicyLatestRecord("policyNo");

        // Verify the results
    }

    @Test
    public void testSave() {
        // Setup
        final OrderRenewal orderRenewalRecord = new OrderRenewal();
        orderRenewalRecord.setCreateBy("createBy");
        orderRenewalRecord.setUpdateBy("createBy");
        orderRenewalRecord.setOldOrderId("oldOrderId");
        orderRenewalRecord.setOldPolicyNo("oldPolicyNo");
        orderRenewalRecord.setNewOrderId("newOrderId");
        orderRenewalRecord.setType(OrderRenewalType.CHANGE);
        orderRenewalRecord.setStatus(OrderRenewalStatus.WAITED);
        orderRenewalRecord.setIdNumber("idNumber");

        // Run the test
        orderRenewalRecordServiceImplUnderTest.save(orderRenewalRecord);

        // Verify the results
        mockOrderRenewalMapper.insertSelective(any(OrderRenewal.class));
    }
}
