package com.cfpamf.ms.insur.admin.job;

import com.cfpamf.ms.insur.admin.event.handler.CustomerCenterPushJobHandler;
import org.junit.FixMethodOrder;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.MethodSorters;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

/*@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("dev")
@FixMethodOrder(MethodSorters.NAME_ASCENDING)*/
public class CustomerCenterPushJobHandlerTest {

   /* @Autowired
    private CustomerCenterPushJobHandler pushJobHandler;

    @Test
    public void execute() {
//        pushJobHandler.execute(null);
    }

    @Test
    public void pushCustomerCenterNewestCustomerInfo() {
        System.out.println();
//        pushJobHandler.pushCustomerCenterNewestCustomerInfo(33);
        System.out.println();
    }*/
}