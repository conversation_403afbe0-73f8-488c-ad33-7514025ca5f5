package com.cfpamf.ms.insur.admin.service;

import com.cfpamf.ms.insur.admin.claim.entity.SmClaimImportResult;
import com.cfpamf.ms.insur.admin.claim.service.impl.SmClaimMailConfigServiceImpl;
import com.cfpamf.ms.insur.admin.config.ClaimEmailProperties;
import com.cfpamf.ms.insur.admin.constant.ClaimFileType;
import com.cfpamf.ms.insur.admin.dao.safes.*;
import com.cfpamf.ms.insur.admin.dao.safes.claim.*;
import com.cfpamf.ms.insur.admin.dao.safes.order.SmOrderInsuredMapper;
import com.cfpamf.ms.insur.admin.pojo.dto.*;
import com.cfpamf.ms.insur.admin.pojo.form.claim.CaseClosedSuccessImport;
import com.cfpamf.ms.insur.admin.pojo.form.claim.ChannelEmail;
import com.cfpamf.ms.insur.admin.pojo.form.claim.ClaimEmailForm;
import com.cfpamf.ms.insur.admin.pojo.po.claim.*;
import com.cfpamf.ms.insur.admin.pojo.query.SmClaimQuery;
import com.cfpamf.ms.insur.admin.pojo.query.SmClaimSmyQuery;
import com.cfpamf.ms.insur.admin.pojo.query.claim.SmClaimEvaluationQuery;
import com.cfpamf.ms.insur.admin.pojo.vo.*;
import com.cfpamf.ms.insur.admin.pojo.vo.claim.ClaimFileSimpleVo;
import com.cfpamf.ms.insur.admin.pojo.vo.claim.SmClaimCancelReportVo;
import com.cfpamf.ms.insur.admin.pojo.vo.claim.evaluation.SmClaimEvaluationPageVo;
import com.cfpamf.ms.insur.admin.service.claim.ClaimRiskReasonService;
import com.cfpamf.ms.insur.admin.service.claim.SmClaimRuleLabelDetailService;
import com.cfpamf.ms.insur.admin.service.claim.SmClaimRuleLabelRecordService;
import com.cfpamf.ms.insur.admin.service.claim.evaluation.SmClaimEvaluationService;
import com.cfpamf.ms.insur.admin.service.claim.impl.ClaimProcessDistinctServiceImpl;
import com.cfpamf.ms.insur.admin.service.claim.impl.SmClaimTemplateInfoServiceImpl;
import com.cfpamf.ms.insur.admin.service.claim.impl.hasten.ClaimHastenProcessor;
import com.cfpamf.ms.insur.admin.validation.SmOfflineOrderValidation;
import com.cfpamf.ms.insur.base.bean.SmyPageInfo;
import com.cfpamf.ms.insur.base.config.BmsConfig;
import com.cfpamf.ms.insur.base.event.EventBusEngine;
import com.cfpamf.ms.insur.base.exception.BizException;
import com.cfpamf.ms.insur.base.service.BaseWorkflow;
import com.cfpamf.ms.insur.base.service.BmsService;
import com.cfpamf.ms.insur.base.service.DLockTemplate;
import com.cfpamf.ms.insur.base.service.EnumDictionaryService;
import com.cfpamf.ms.insur.base.util.email.JavaMailHelper;
import com.cfpamf.ms.insur.weixin.pojo.dto.WxClaimDTO;
import com.cfpamf.ms.insur.weixin.pojo.dto.claim.za.SmSettlementClaimQuery;
import com.cfpamf.ms.insur.weixin.pojo.query.WxClaimQuery;
import com.cfpamf.ms.insur.weixin.pojo.vo.CaseClosedValidateImportVo;
import com.cfpamf.ms.insur.weixin.pojo.vo.WxClaimAccidentVO;
import com.cfpamf.ms.insur.weixin.pojo.vo.WxClaimDetailVO;
import com.cfpamf.ms.insur.weixin.pojo.vo.WxClaimListVo;
import com.cfpamf.ms.insur.weixin.pojo.vo.claim.WxClaimCancelReportListVo;
import com.cfpamf.ms.insur.weixin.pojo.vo.claim.evaluation.ClaimEvaluationVo;
import com.cfpamf.ms.insur.weixin.pojo.vo.claim.evaluation.ClaimQuestionnaireVo;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.pagehelper.PageInfo;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.modules.junit4.PowerMockRunner;
import org.slf4j.Logger;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.core.task.TaskRejectedException;
import org.springframework.mock.web.MockHttpServletResponse;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@RunWith(PowerMockRunner.class)
public class SmClaimServiceImplTest extends BaseTest {

    @Mock private SmOrderMapper mockOrderMapper;
    @Mock private ClaimRiskReasonService mockClaimRiskReasonService;
    @Mock private SmCommonSettingMapper mockSmCommonSettingMapper;
    @Mock private ClaimProcessDistinctServiceImpl mockProcessDistinctService;
    @Mock private DLockTemplate mockDLockTemplate;
    @Mock private DictionaryService mockDictionaryService;
    @Mock private SmClaimImportResultMapper mockClaimImportResultMapper;
    @Mock private SmClaimHastenMapper mockHastenMapper;
    @Mock private AuthUserMapper mockAuthUserMapper;
    @Mock private SystemFileService mockFileService;
    @Mock private SmClaimRuleLabelDetailService mockSmClaimRuleLabelDetailService;
    @Mock private SmClaimFinishNotifyMapper mockClaimFinishNotifyMapper;
    @Mock private SmClaimCancelReportMapper mockClaimCancelReportMapper;
    @Mock private EnumDictionaryService mockEnumDictionaryService;
    @Mock private AsyncTaskExecutor mockTaskExecutor;
    private String mainFrom;
    @Mock private ClaimWorkflow workflow;
    @Mock private BmsConfig bmsConfig;
    @Mock private BmsService bmsService;
    @Mock private SmClaimEmailMapper smClaimEmailMapper;
    private ObjectMapper objectMapper;
    @Mock private SmClaimRuleLabelDetailService smClaimRuleLabelDetailService;
    @Mock private SmClaimRuleLabelRecordService smClaimRuleLabelRecordService;
    @Mock private SmCmpySettingMapper cmpySettingMapper;
    @Mock private JavaMailHelper javaMailHelper;
    @Mock private ClaimEmailProperties claimEmailProperties;
    @Mock private SmClaimReimbursementZaMapper claimReimbursementZaMapper;
    @Mock private SmClaimMailConfigServiceImpl claimMailConfigService;
    @Mock private SmOrderInsuredMapper orderInsuredMapper;
    @Mock private SmClaimReimbursementGtccMapper claimReimbursementGtccMapper;
    @Mock private SmClaimReimbursementKpMapper claimReimbursementKpMapper;
    @Mock private ClaimHastenProcessor claimHastenProcessor;
    @Mock private AuthUserMapper authUserMapper;
    @Mock private SmClaimLabelMapper claimLabelMapper;
    @Mock private Logger log;
    @Mock private SmClaimMapper mapper;
    @Mock private SmClaimEvaluationService evaluationService;
    @Mock private UserService userService;
    @Mock private SmClaimProgressExpectTimeMapper claimProgressExpectTimeMapper;
    @Mock private ClaimProcessDistinctServiceImpl distinctService;
    @Mock private EventBusEngine busEngine;
    @Mock private SmClaimAiFileMapper aiFileMapper;
    @Mock private SmClaimTemplateInfoServiceImpl claimTemplateInfoService;
    @Mock private DictionaryService dictionaryService;
    private List<String> list;

    @InjectMocks private SmClaimServiceImpl smClaimServiceImplUnderTest;

    @Test
    public void testGetSmClaimByPage() {
        // Setup
        final SmClaimQuery query = new SmClaimQuery();
        query.setPage(0);
        query.setSize(0);
        query.setAll(false);
        query.setQueryPage(false);
        query.setReportDateStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setReportDateEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setRiskDateStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setRiskDateEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setClaimState("claimState");
        query.setFinishDateStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setFinishDateEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setClaimId(0);
        query.setApplicant("applicant");
        query.setApplicantType(0);

        // Configure SmClaimFinishNotifyMapper.selectByClaimIdList(...).
        final SmClaimFinishNotify smClaimFinishNotify = new SmClaimFinishNotify();
        smClaimFinishNotify.setId(0);
        smClaimFinishNotify.setEnabledFlag(0);
        smClaimFinishNotify.setClaimId(0);
        smClaimFinishNotify.setRiskAddress("riskAddress");
        smClaimFinishNotify.setRiskTypeName("riskTypeName");
        final List<SmClaimFinishNotify> smClaimFinishNotifies = Arrays.asList(smClaimFinishNotify);
        when(mockClaimFinishNotifyMapper.selectByClaimIdList(Arrays.asList(0))).thenReturn(smClaimFinishNotifies);

        when(mockDictionaryService.groupByTypes(Arrays.asList("value"))).thenReturn(new HashMap<>());

        // Configure SmClaimRuleLabelDetailService.listClaimLabelInfoByClaimIds(...).
        final SmClaimRuleLabelDetailPO smClaimRuleLabelDetailPO = new SmClaimRuleLabelDetailPO();
        smClaimRuleLabelDetailPO.setId(0);
        smClaimRuleLabelDetailPO.setEnabledFlag(0);
        smClaimRuleLabelDetailPO.setClaimId(0);
        smClaimRuleLabelDetailPO.setValueCode("label");
        smClaimRuleLabelDetailPO.setValueDesc("labelName");
        final List<SmClaimRuleLabelDetailPO> smClaimRuleLabelDetailPOS = Arrays.asList(smClaimRuleLabelDetailPO);
        when(mockSmClaimRuleLabelDetailService.listClaimLabelInfoByClaimIds(Arrays.asList(0))).thenReturn(smClaimRuleLabelDetailPOS);

        // Run the test
        final PageInfo<SmClaimVO> result = smClaimServiceImplUnderTest.getSmClaimByPage(query);

        // Verify the results
    }

    @Test
    public void testGetSmClaimByPage_SmClaimFinishNotifyMapperReturnsNoItems() {
        // Setup
        final SmClaimQuery query = new SmClaimQuery();
        query.setPage(0);
        query.setSize(0);
        query.setAll(false);
        query.setQueryPage(false);
        query.setReportDateStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setReportDateEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setRiskDateStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setRiskDateEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setClaimState("claimState");
        query.setFinishDateStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setFinishDateEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setClaimId(0);
        query.setApplicant("applicant");
        query.setApplicantType(0);

        when(mockClaimFinishNotifyMapper.selectByClaimIdList(Arrays.asList(0))).thenReturn(Collections.emptyList());
        when(mockDictionaryService.groupByTypes(Arrays.asList("value"))).thenReturn(new HashMap<>());

        // Configure SmClaimRuleLabelDetailService.listClaimLabelInfoByClaimIds(...).
        final SmClaimRuleLabelDetailPO smClaimRuleLabelDetailPO = new SmClaimRuleLabelDetailPO();
        smClaimRuleLabelDetailPO.setId(0);
        smClaimRuleLabelDetailPO.setEnabledFlag(0);
        smClaimRuleLabelDetailPO.setClaimId(0);
        smClaimRuleLabelDetailPO.setValueCode("label");
        smClaimRuleLabelDetailPO.setValueDesc("labelName");
        final List<SmClaimRuleLabelDetailPO> smClaimRuleLabelDetailPOS = Arrays.asList(smClaimRuleLabelDetailPO);
        when(mockSmClaimRuleLabelDetailService.listClaimLabelInfoByClaimIds(Arrays.asList(0))).thenReturn(smClaimRuleLabelDetailPOS);

        // Run the test
        final PageInfo<SmClaimVO> result = smClaimServiceImplUnderTest.getSmClaimByPage(query);

        // Verify the results
    }

    @Test
    public void testGetSmClaimByPage_SmClaimRuleLabelDetailServiceReturnsNull() {
        // Setup
        final SmClaimQuery query = new SmClaimQuery();
        query.setPage(0);
        query.setSize(0);
        query.setAll(false);
        query.setQueryPage(false);
        query.setReportDateStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setReportDateEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setRiskDateStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setRiskDateEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setClaimState("claimState");
        query.setFinishDateStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setFinishDateEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setClaimId(0);
        query.setApplicant("applicant");
        query.setApplicantType(0);

        // Configure SmClaimFinishNotifyMapper.selectByClaimIdList(...).
        final SmClaimFinishNotify smClaimFinishNotify = new SmClaimFinishNotify();
        smClaimFinishNotify.setId(0);
        smClaimFinishNotify.setEnabledFlag(0);
        smClaimFinishNotify.setClaimId(0);
        smClaimFinishNotify.setRiskAddress("riskAddress");
        smClaimFinishNotify.setRiskTypeName("riskTypeName");
        final List<SmClaimFinishNotify> smClaimFinishNotifies = Arrays.asList(smClaimFinishNotify);
        when(mockClaimFinishNotifyMapper.selectByClaimIdList(Arrays.asList(0))).thenReturn(smClaimFinishNotifies);

        when(mockDictionaryService.groupByTypes(Arrays.asList("value"))).thenReturn(new HashMap<>());
        when(mockSmClaimRuleLabelDetailService.listClaimLabelInfoByClaimIds(Arrays.asList(0))).thenReturn(null);

        // Run the test
        final PageInfo<SmClaimVO> result = smClaimServiceImplUnderTest.getSmClaimByPage(query);

        // Verify the results
    }

    @Test
    public void testGetSmClaimByPage_SmClaimRuleLabelDetailServiceReturnsNoItems() {
        // Setup
        final SmClaimQuery query = new SmClaimQuery();
        query.setPage(0);
        query.setSize(0);
        query.setAll(false);
        query.setQueryPage(false);
        query.setReportDateStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setReportDateEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setRiskDateStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setRiskDateEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setClaimState("claimState");
        query.setFinishDateStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setFinishDateEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setClaimId(0);
        query.setApplicant("applicant");
        query.setApplicantType(0);

        // Configure SmClaimFinishNotifyMapper.selectByClaimIdList(...).
        final SmClaimFinishNotify smClaimFinishNotify = new SmClaimFinishNotify();
        smClaimFinishNotify.setId(0);
        smClaimFinishNotify.setEnabledFlag(0);
        smClaimFinishNotify.setClaimId(0);
        smClaimFinishNotify.setRiskAddress("riskAddress");
        smClaimFinishNotify.setRiskTypeName("riskTypeName");
        final List<SmClaimFinishNotify> smClaimFinishNotifies = Arrays.asList(smClaimFinishNotify);
        when(mockClaimFinishNotifyMapper.selectByClaimIdList(Arrays.asList(0))).thenReturn(smClaimFinishNotifies);

        when(mockDictionaryService.groupByTypes(Arrays.asList("value"))).thenReturn(new HashMap<>());
        when(mockSmClaimRuleLabelDetailService.listClaimLabelInfoByClaimIds(Arrays.asList(0))).thenReturn(Collections.emptyList());

        // Run the test
        final PageInfo<SmClaimVO> result = smClaimServiceImplUnderTest.getSmClaimByPage(query);

        // Verify the results
    }

    @Test
    public void testInitSmClaimQuery() {
        // Setup
        final SmClaimQuery query = new SmClaimQuery();
        query.setPage(0);
        query.setSize(0);
        query.setAll(false);
        query.setQueryPage(false);
        query.setReportDateStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setReportDateEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setRiskDateStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setRiskDateEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setClaimState("claimState");
        query.setFinishDateStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setFinishDateEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setClaimId(0);
        query.setApplicant("applicant");
        query.setApplicantType(0);

        // Run the test
        smClaimServiceImplUnderTest.initSmClaimQuery(query);

        // Verify the results
    }

    @Test
    public void testGetSmClaimById() {
        // Setup
        final WxClaimDetailVO expectedResult = new WxClaimDetailVO();
        expectedResult.setId(0);
        expectedResult.setInsuredId(0);
        expectedResult.setClaimNo("claimNo");
        expectedResult.setRiskType("riskType");
        expectedResult.setReportTime("reportTime");
        expectedResult.setClaimState("sCode");
        expectedResult.setClaimResult("保司核赔");
        expectedResult.setFinishState("finishState");
        expectedResult.setSettlement("settlement");
        expectedResult.setAccidentTypes(new String[]{"accidentTypes"});
        expectedResult.setMedicalInsur("medicalInsur");
        expectedResult.setOtherInsur("otherInsur");
        expectedResult.setSafesCenterApprovalTimes(0);
        expectedResult.setProcessType("processType");
        expectedResult.setVisitingHospital("visitingHospital");
        expectedResult.setVisitingHospitalName("visitingHospitalName");
        expectedResult.setVisitingDate("visitingDate");
        expectedResult.setVisitingOccupation("visitingOccupation");

        // Run the test
        final WxClaimDetailVO result = smClaimServiceImplUnderTest.getSmClaimById(0);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetSmClaimByInsuredId() {
        // Setup
        final WxClaimDetailVO wxClaimDetailVO = new WxClaimDetailVO();
        wxClaimDetailVO.setId(0);
        wxClaimDetailVO.setInsuredId(0);
        wxClaimDetailVO.setClaimNo("claimNo");
        wxClaimDetailVO.setRiskType("riskType");
        wxClaimDetailVO.setReportTime("reportTime");
        wxClaimDetailVO.setClaimState("sCode");
        wxClaimDetailVO.setClaimResult("保司核赔");
        wxClaimDetailVO.setFinishState("finishState");
        wxClaimDetailVO.setSettlement("settlement");
        wxClaimDetailVO.setAccidentTypes(new String[]{"accidentTypes"});
        wxClaimDetailVO.setMedicalInsur("medicalInsur");
        wxClaimDetailVO.setOtherInsur("otherInsur");
        wxClaimDetailVO.setSafesCenterApprovalTimes(0);
        wxClaimDetailVO.setProcessType("processType");
        wxClaimDetailVO.setVisitingHospital("visitingHospital");
        wxClaimDetailVO.setVisitingHospitalName("visitingHospitalName");
        wxClaimDetailVO.setVisitingDate("visitingDate");
        wxClaimDetailVO.setVisitingOccupation("visitingOccupation");
        final List<WxClaimDetailVO> expectedResult = Arrays.asList(wxClaimDetailVO);

        // Run the test
        final List<WxClaimDetailVO> result = smClaimServiceImplUnderTest.getSmClaimByInsuredId(0);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetSmClaimAccidentTypes() {
        // Setup
        final WxClaimAccidentVO expectedResult = new WxClaimAccidentVO();
        expectedResult.setAccidentTypeCodes(new String[]{"accidentTypeCodes"});
        expectedResult.setMedicalInsur("medicalInsur");
        expectedResult.setOtherInsur("otherInsur");
        expectedResult.setVisitingHospital("visitingHospital");
        expectedResult.setVisitingHospitalName("visitingHospitalName");
        expectedResult.setVisitingDate("visitingDate");
        expectedResult.setVisitingOccupation("visitingOccupation");

        // Run the test
        final WxClaimAccidentVO result = smClaimServiceImplUnderTest.getSmClaimAccidentTypes(0);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetSmClaimFileCombByClaimId() {
        // Setup
        final SmClaimFileCombVO fileCombVO = new SmClaimFileCombVO();
        fileCombVO.setOrder(0);
        fileCombVO.setClaimId(0);
        fileCombVO.setFileTypeCode("fileTypeCode");
        fileCombVO.setFileTypeName("name");
        fileCombVO.setFileRequire(false);
        fileCombVO.setFileUrls(Arrays.asList("value"));
        final ClaimFileSimpleVo claimFileSimpleVo = new ClaimFileSimpleVo();
        claimFileSimpleVo.setCfId(0);
        claimFileSimpleVo.setFileUrl("fileUrl");
        claimFileSimpleVo.setNewFlag(0);
        claimFileSimpleVo.setFileTypeCode("fileTypeCode");
        claimFileSimpleVo.setFileTypeName("fileTypeName");
        claimFileSimpleVo.setZaKey("zaKey");
        claimFileSimpleVo.setFileApproveNode("fileApproveNode");
        claimFileSimpleVo.setAiResponse("aiResponse");
        claimFileSimpleVo.setFileUniqueCode("fileUniqueCode");
        fileCombVO.setFileSimpleVoList(Arrays.asList(claimFileSimpleVo));
        fileCombVO.setExampleFileUrlList(Arrays.asList("value"));
        fileCombVO.setTemplateFileUrlList(Arrays.asList("value"));
        fileCombVO.setExampleFileUrlDescription("exampleFileUrlDescription");
        final List<SmClaimFileCombVO> expectedResult = Arrays.asList(fileCombVO);

        // Configure SmOrderMapper.getOrderInsuredByInsId(...).
        final SmOrderInsuredVO smOrderInsuredVO = new SmOrderInsuredVO();
        smOrderInsuredVO.setIdNumber("idNumber");
        smOrderInsuredVO.setBirthday("birthday");
        smOrderInsuredVO.setInsuredId(0);
        smOrderInsuredVO.setBranchId("branchId");
        smOrderInsuredVO.setCompanyName("companyName");
        when(mockOrderMapper.getOrderInsuredByInsId(0)).thenReturn(smOrderInsuredVO);

        // Run the test
        final List<SmClaimFileCombVO> result = smClaimServiceImplUnderTest.getSmClaimFileCombByClaimId(0);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetSmClaimFileCombByClaimFIleIdList() {
        // Setup
        final SmClaimFileCombVO fileCombVO = new SmClaimFileCombVO();
        fileCombVO.setOrder(0);
        fileCombVO.setClaimId(0);
        fileCombVO.setFileTypeCode("fileTypeCode");
        fileCombVO.setFileTypeName("name");
        fileCombVO.setFileRequire(false);
        fileCombVO.setFileUrls(Arrays.asList("value"));
        final ClaimFileSimpleVo claimFileSimpleVo = new ClaimFileSimpleVo();
        claimFileSimpleVo.setCfId(0);
        claimFileSimpleVo.setFileUrl("fileUrl");
        claimFileSimpleVo.setNewFlag(0);
        claimFileSimpleVo.setFileTypeCode("fileTypeCode");
        claimFileSimpleVo.setFileTypeName("fileTypeName");
        claimFileSimpleVo.setZaKey("zaKey");
        claimFileSimpleVo.setFileApproveNode("fileApproveNode");
        claimFileSimpleVo.setAiResponse("aiResponse");
        claimFileSimpleVo.setFileUniqueCode("fileUniqueCode");
        fileCombVO.setFileSimpleVoList(Arrays.asList(claimFileSimpleVo));
        fileCombVO.setExampleFileUrlList(Arrays.asList("value"));
        fileCombVO.setTemplateFileUrlList(Arrays.asList("value"));
        fileCombVO.setExampleFileUrlDescription("exampleFileUrlDescription");
        final List<SmClaimFileCombVO> expectedResult = Arrays.asList(fileCombVO);

        // Configure SmOrderMapper.getOrderInsuredByInsId(...).
        final SmOrderInsuredVO smOrderInsuredVO = new SmOrderInsuredVO();
        smOrderInsuredVO.setIdNumber("idNumber");
        smOrderInsuredVO.setBirthday("birthday");
        smOrderInsuredVO.setInsuredId(0);
        smOrderInsuredVO.setBranchId("branchId");
        smOrderInsuredVO.setCompanyName("companyName");
        when(mockOrderMapper.getOrderInsuredByInsId(0)).thenReturn(smOrderInsuredVO);

        // Run the test
        final List<SmClaimFileCombVO> result = smClaimServiceImplUnderTest.getSmClaimFileCombByClaimFIleIdList(0, Arrays.asList(0));

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetClaimFileType() {
        // Setup
        // Configure SmOrderMapper.getOrderInsuredByInsId(...).
        final SmOrderInsuredVO smOrderInsuredVO = new SmOrderInsuredVO();
        smOrderInsuredVO.setIdNumber("idNumber");
        smOrderInsuredVO.setBirthday("birthday");
        smOrderInsuredVO.setInsuredId(0);
        smOrderInsuredVO.setBranchId("branchId");
        smOrderInsuredVO.setCompanyName("companyName");
        when(mockOrderMapper.getOrderInsuredByInsId(0)).thenReturn(smOrderInsuredVO);

        // Run the test
        final List<ClaimFileType> result = smClaimServiceImplUnderTest.getClaimFileType(0);

        // Verify the results
        assertEquals(Arrays.asList(ClaimFileType.CLAIM_APPLY), result);
    }

    @Test
    public void testGetClaimExpressByClaimId() {
        // Setup
        final SmClaimExpressVO smClaimExpressVO = new SmClaimExpressVO();
        smClaimExpressVO.setCeId(0);
        smClaimExpressVO.setClaimId(0);
        smClaimExpressVO.setExpressDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smClaimExpressVO.setExpressCompanyName("expressCompanyName");
        smClaimExpressVO.setExpressNo("expressNo");
        final List<SmClaimExpressVO> expectedResult = Arrays.asList(smClaimExpressVO);

        // Run the test
        final List<SmClaimExpressVO> result = smClaimServiceImplUnderTest.getClaimExpressByClaimId(0);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testUpdateClaimNote() {
        // Setup
        final WxClaimDTO dto = new WxClaimDTO();
        dto.setId(0);
        dto.setInsuredId(0);
        dto.setRiskType("riskType");
        dto.setRiskTime("riskTime");
        dto.setProcessType("processType");

        // Run the test
        smClaimServiceImplUnderTest.updateClaimNote(dto);

        // Verify the results
    }

    @Test
    public void testUpdateClaimSettlement() {
        // Setup
        final WxClaimDTO dto = new WxClaimDTO();
        dto.setId(0);
        dto.setInsuredId(0);
        dto.setRiskType("riskType");
        dto.setRiskTime("riskTime");
        dto.setProcessType("processType");

        // Run the test
        smClaimServiceImplUnderTest.updateClaimSettlement(dto);

        // Verify the results
    }

    @Test
    public void testUpdateClaimRiskType() {
        // Setup
        final WxClaimDTO dto = new WxClaimDTO();
        dto.setId(0);
        dto.setInsuredId(0);
        dto.setRiskType("riskType");
        dto.setRiskTime("riskTime");
        dto.setProcessType("processType");

        // Run the test
        smClaimServiceImplUnderTest.updateClaimRiskType(dto);

        // Verify the results
    }

    @Test
    public void testUpdateClaimInfo() {
        // Setup
        final WxClaimDTO dto = new WxClaimDTO();
        dto.setId(0);
        dto.setInsuredId(0);
        dto.setRiskType("riskType");
        dto.setRiskTime("riskTime");
        dto.setProcessType("processType");

        // Run the test
        smClaimServiceImplUnderTest.updateClaimInfo(dto);

        // Verify the results
    }

    @Test
    public void testUpdateClaimAccidentTypes() {
        // Setup
        // Run the test
        smClaimServiceImplUnderTest.updateClaimAccidentTypes(0,
                                                             "accidentTypeJoin",
                                                             "medicalInsur",
                                                             "otherInsur",
                                                             "visitingHospital",
                                                             "visitingHospitalName",
                                                             "visitingDate",
                                                             "visitingOccupation");

        // Verify the results
    }

    @Test
    public void testGetWxClaimList() {
        // Setup
        final WxClaimQuery query = new WxClaimQuery();
        query.setPage(0);
        query.setSize(0);
        query.setAll(false);
        query.setQueryPage(false);
        query.setUserId("userId");

        // Run the test
        final PageInfo<WxClaimCancelReportListVo> result = smClaimServiceImplUnderTest.getWxClaimList(query);

        // Verify the results
    }

    @Test
    public void testGetWxFollowUpClaimList() {
        // Setup
        final WxClaimQuery query = new WxClaimQuery();
        query.setPage(0);
        query.setSize(0);
        query.setAll(false);
        query.setQueryPage(false);
        query.setUserId("userId");

        // Run the test
        final PageInfo<WxClaimListVo> result = smClaimServiceImplUnderTest.getWxFollowUpClaimList(query);

        // Verify the results
    }

    @Test
    public void testIsCustomerAdminFollow() {
        assertFalse(smClaimServiceImplUnderTest.isCustomerAdminFollow("claimState"));
    }

    @Test
    public void testIsPCOFollow() {
        assertFalse(smClaimServiceImplUnderTest.isPCOFollow("claimState"));
    }

    @Test
    public void testIsSettlementFollow() {
        assertFalse(smClaimServiceImplUnderTest.isSettlementFollow("claimState"));
    }

    @Test
    public void testCountWxFollowUpClaimList() {
        // Setup
        final WxClaimQuery query = new WxClaimQuery();
        query.setPage(0);
        query.setSize(0);
        query.setAll(false);
        query.setQueryPage(false);
        query.setUserId("userId");

        // Run the test
        final long result = smClaimServiceImplUnderTest.countWxFollowUpClaimList(query);

        // Verify the results
        assertEquals(0L, result);
    }

    @Test
    public void testSaveClaimFollowUp() {
        // Setup
        final SmClaimFollowUpDTO dto = new SmClaimFollowUpDTO();
        dto.setClaimId(0);
        dto.setType("code");
        dto.setConsultType("consultType");
        dto.setCurrentNode("sCode");
        dto.setParentFollowUpId(0);

        // Run the test
        smClaimServiceImplUnderTest.saveClaimFollowUp(dto);

        // Verify the results
    }

    @Test
    public void testCheckCurrentDayHastenFollowUp() {
        // Setup
        // Run the test
        smClaimServiceImplUnderTest.checkCurrentDayHastenFollowUp(0);

        // Verify the results
    }

    @Test
    public void testCountWxClaimList() {
        // Setup
        final WxClaimQuery query = new WxClaimQuery();
        query.setPage(0);
        query.setSize(0);
        query.setAll(false);
        query.setQueryPage(false);
        query.setUserId("userId");

        // Run the test
        final long result = smClaimServiceImplUnderTest.countWxClaimList(query);

        // Verify the results
        assertEquals(0L, result);
    }

    @Test
    public void testGetWxClaimTodoListByPage() {
        // Setup
        final WxClaimQuery query = new WxClaimQuery();
        query.setPage(0);
        query.setSize(0);
        query.setAll(false);
        query.setQueryPage(false);
        query.setUserId("userId");

        // Run the test
        final PageInfo<WxClaimListVo> result = smClaimServiceImplUnderTest.getWxClaimTodoListByPage(query);

        // Verify the results
    }

    @Test
    public void testCountWxClaimTodoList() {
        // Setup
        final WxClaimQuery query = new WxClaimQuery();
        query.setPage(0);
        query.setSize(0);
        query.setAll(false);
        query.setQueryPage(false);
        query.setUserId("userId");

        // Run the test
        final long result = smClaimServiceImplUnderTest.countWxClaimTodoList(query);

        // Verify the results
        assertEquals(0L, result);
    }

    @Test
    public void testGetClaimProgressForBackend() {
        // Setup
        final SmClaimProgressVO expectedResult = new SmClaimProgressVO();
        final ProgressVO progressVO = new ProgressVO();
        progressVO.setProgressId(0);
        progressVO.setSCode("sCode");
        progressVO.setDataJson("dataJson");
        progressVO.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        expectedResult.setProgressList(Arrays.asList(progressVO));
        final ClaimWorkflow.Step nextProgress = new BaseWorkflow.Step();
        nextProgress.setSCode("sCode");
        nextProgress.setSName("保司核赔");
        final BaseWorkflow.Option option = new BaseWorkflow.Option();
        option.setOCode("optionCode");
        option.setOName("AI自动过审");
        option.setOType("-1");
        option.setOValue("");
        option.setNextStep("stepDataCheckBySafesCenter");
        nextProgress.setOptions(Arrays.asList(option));
        expectedResult.setNextProgress(nextProgress);
        final ProgressStepVO progressStepVO = new ProgressStepVO();
        progressStepVO.setSCode("scode");
        progressStepVO.setSName("sname");
        expectedResult.setAllSteps(Arrays.asList(progressStepVO));
        final ClaimExpectWaitTimeProgressStepVO claimExpectWaitTimeProgressStepVO = new ClaimExpectWaitTimeProgressStepVO();
        claimExpectWaitTimeProgressStepVO.setSCode("scode");
        claimExpectWaitTimeProgressStepVO.setSName("sname");
        claimExpectWaitTimeProgressStepVO.setExpectWaitTime(0);
        expectedResult.setExpectWaitTimeProgressStepList(Arrays.asList(claimExpectWaitTimeProgressStepVO));

        // Run the test
        final SmClaimProgressVO result = smClaimServiceImplUnderTest.getClaimProgressForBackend(0);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testSaveFinishProgress() {
        // Setup
        final ProgressDTO dto = new ProgressDTO();
        dto.setId(0L);
        dto.setClaimId(0);
        dto.setSCode("sCode");
        dto.setSName("保司核赔");
        dto.setOCode("optionCode");
        dto.setOName("AI自动过审");
        dto.setOType("-1");
        dto.setOValue("");
        dto.setDataJson("dataJson");
        dto.setOTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        dto.setExpressTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        dto.setSettlement("settlement");
        dto.setCreateBy("createBy");
        final ClaimEmailForm claimEmailForm = new ClaimEmailForm();
        claimEmailForm.setTheme("theme");
        claimEmailForm.setContent("content");
        claimEmailForm.setAllEnclosure(0);
        claimEmailForm.setAutoSend(0);
        dto.setClaimEmailForm(claimEmailForm);
        dto.setEvaluationStatus(0);

        // Configure SmOrderMapper.getOrderInsuredByInsId(...).
        final SmOrderInsuredVO smOrderInsuredVO = new SmOrderInsuredVO();
        smOrderInsuredVO.setIdNumber("idNumber");
        smOrderInsuredVO.setBirthday("birthday");
        smOrderInsuredVO.setInsuredId(0);
        smOrderInsuredVO.setBranchId("branchId");
        smOrderInsuredVO.setCompanyName("companyName");
        when(mockOrderMapper.getOrderInsuredByInsId(0)).thenReturn(smOrderInsuredVO);

        // Run the test
        smClaimServiceImplUnderTest.saveFinishProgress(dto);

        // Verify the results
    }

    @Test
    public void testChannel() {
        assertEquals(Arrays.asList("value"), smClaimServiceImplUnderTest.channel());
    }

    @Test
    public void testUpdateClaimToCancelReport() {
        // Setup
        final AuthUserVO authUserVO = new AuthUserVO();
        authUserVO.setRegionName("regionName");
        authUserVO.setUserId("customerAdminId");
        authUserVO.setUserName("createBy");
        authUserVO.setUserMobile("customerAdminMobile");
        authUserVO.setHrOrgId(0);

        final SmClaimCancelReportProgress smClaimCancelReportProgress = new SmClaimCancelReportProgress();
        smClaimCancelReportProgress.setId(0);
        smClaimCancelReportProgress.setEnabledFlag(0);
        smClaimCancelReportProgress.setCreateBy("createBy");
        smClaimCancelReportProgress.setUpdateBy("updateBy");
        smClaimCancelReportProgress.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        // Run the test
        smClaimServiceImplUnderTest.updateClaimToCancelReport(0, authUserVO, smClaimCancelReportProgress);

        // Verify the results
    }

    @Test
    public void testRetry() {
        // Setup
        // Configure SmOrderMapper.getOrderInsuredByInsId(...).
        final SmOrderInsuredVO smOrderInsuredVO = new SmOrderInsuredVO();
        smOrderInsuredVO.setIdNumber("idNumber");
        smOrderInsuredVO.setBirthday("birthday");
        smOrderInsuredVO.setInsuredId(0);
        smOrderInsuredVO.setBranchId("branchId");
        smOrderInsuredVO.setCompanyName("companyName");
        when(mockOrderMapper.getOrderInsuredByInsId(0)).thenReturn(smOrderInsuredVO);

        // Run the test
        smClaimServiceImplUnderTest.retry(Arrays.asList(0));

        // Verify the results
    }

    @Test
    public void testRetryNew() {
        // Setup
        // Configure SmOrderMapper.getOrderInsuredByInsId(...).
        final SmOrderInsuredVO smOrderInsuredVO = new SmOrderInsuredVO();
        smOrderInsuredVO.setIdNumber("idNumber");
        smOrderInsuredVO.setBirthday("birthday");
        smOrderInsuredVO.setInsuredId(0);
        smOrderInsuredVO.setBranchId("branchId");
        smOrderInsuredVO.setCompanyName("companyName");
        when(mockOrderMapper.getOrderInsuredByInsId(0)).thenReturn(smOrderInsuredVO);

        // Run the test
        smClaimServiceImplUnderTest.retryNew(Arrays.asList(0));

        // Verify the results
    }

    @Test
    public void testRetryMail() {
        // Setup
        final ClaimEmailForm claimEmailForm = new ClaimEmailForm();
        claimEmailForm.setTheme("theme");
        claimEmailForm.setContent("content");
        claimEmailForm.setClaimFileIdList(Arrays.asList(0));
        claimEmailForm.setAllEnclosure(0);
        claimEmailForm.setAutoSend(0);
        final ChannelEmail channelEmail = new ChannelEmail();
        channelEmail.setCode("code");
        channelEmail.setName("name");
        channelEmail.setEmail("email");
        channelEmail.setThemeTemplate("themeTemplate");
        channelEmail.setContentTemplate("contentTemplate");
        final SmClaimEmail claimEmail = new SmClaimEmail(0, claimEmailForm, channelEmail, "formEmail");

        // Configure SmOrderMapper.getOrderInsuredByInsId(...).
        final SmOrderInsuredVO smOrderInsuredVO = new SmOrderInsuredVO();
        smOrderInsuredVO.setIdNumber("idNumber");
        smOrderInsuredVO.setBirthday("birthday");
        smOrderInsuredVO.setInsuredId(0);
        smOrderInsuredVO.setBranchId("branchId");
        smOrderInsuredVO.setCompanyName("companyName");
        when(mockOrderMapper.getOrderInsuredByInsId(0)).thenReturn(smOrderInsuredVO);

        // Run the test
        smClaimServiceImplUnderTest.retryMail(claimEmail);

        // Verify the results
    }

    @Test
    public void testUpdateFinishResult() {
        // Setup
        final ProgressDTO dto = new ProgressDTO();
        dto.setId(0L);
        dto.setClaimId(0);
        dto.setSCode("sCode");
        dto.setSName("保司核赔");
        dto.setOCode("optionCode");
        dto.setOName("AI自动过审");
        dto.setOType("-1");
        dto.setOValue("");
        dto.setDataJson("dataJson");
        dto.setOTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        dto.setExpressTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        dto.setSettlement("settlement");
        dto.setCreateBy("createBy");
        final ClaimEmailForm claimEmailForm = new ClaimEmailForm();
        claimEmailForm.setTheme("theme");
        claimEmailForm.setContent("content");
        claimEmailForm.setAllEnclosure(0);
        claimEmailForm.setAutoSend(0);
        dto.setClaimEmailForm(claimEmailForm);
        dto.setEvaluationStatus(0);

        // Run the test
        smClaimServiceImplUnderTest.updateFinishResult(0, dto);

        // Verify the results
    }

    @Test
    public void testDownloadFilesByFIleCombs() {
        // Setup
        final MockHttpServletResponse resp = new MockHttpServletResponse();
        final SmClaimFileCombVO fileCombVO = new SmClaimFileCombVO();
        fileCombVO.setOrder(0);
        fileCombVO.setClaimId(0);
        fileCombVO.setFileTypeCode("fileTypeCode");
        fileCombVO.setFileTypeName("name");
        fileCombVO.setFileRequire(false);
        fileCombVO.setFileUrls(Arrays.asList("value"));
        final ClaimFileSimpleVo claimFileSimpleVo = new ClaimFileSimpleVo();
        claimFileSimpleVo.setCfId(0);
        claimFileSimpleVo.setFileUrl("fileUrl");
        claimFileSimpleVo.setNewFlag(0);
        claimFileSimpleVo.setFileTypeCode("fileTypeCode");
        claimFileSimpleVo.setFileTypeName("fileTypeName");
        claimFileSimpleVo.setZaKey("zaKey");
        claimFileSimpleVo.setFileApproveNode("fileApproveNode");
        claimFileSimpleVo.setAiResponse("aiResponse");
        claimFileSimpleVo.setFileUniqueCode("fileUniqueCode");
        fileCombVO.setFileSimpleVoList(Arrays.asList(claimFileSimpleVo));
        fileCombVO.setExampleFileUrlList(Arrays.asList("value"));
        fileCombVO.setTemplateFileUrlList(Arrays.asList("value"));
        fileCombVO.setExampleFileUrlDescription("exampleFileUrlDescription");
        final List<SmClaimFileCombVO> fileCombs = Arrays.asList(fileCombVO);

        // Run the test
        smClaimServiceImplUnderTest.downloadFilesByFIleCombs(resp, fileCombs, "fileName");

        // Verify the results
    }

    @Test
    public void testDownloadCancelClaimList() {
        // Setup
        final SmClaimQuery query = new SmClaimQuery();
        query.setPage(0);
        query.setSize(0);
        query.setAll(false);
        query.setQueryPage(false);
        query.setReportDateStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setReportDateEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setRiskDateStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setRiskDateEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setClaimState("claimState");
        query.setFinishDateStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setFinishDateEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setClaimId(0);
        query.setApplicant("applicant");
        query.setApplicantType(0);

        final MockHttpServletResponse resp = new MockHttpServletResponse();

        // Configure SmClaimCancelReportMapper.search(...).
        final SmClaimCancelReportVo smClaimCancelReportVo = new SmClaimCancelReportVo();
        smClaimCancelReportVo.setClaimCancelReportId(0);
        smClaimCancelReportVo.setId("id");
        smClaimCancelReportVo.setApplicantIdNumber("applicantIdNumber");
        smClaimCancelReportVo.setInsuredIdNumber("insuredIdNumber");
        smClaimCancelReportVo.setLabelValueDescList(Arrays.asList("value"));
        final List<SmClaimCancelReportVo> smClaimCancelReportVos = Arrays.asList(smClaimCancelReportVo);
        final SmClaimQuery query1 = new SmClaimQuery();
        query1.setPage(0);
        query1.setSize(0);
        query1.setAll(false);
        query1.setQueryPage(false);
        query1.setReportDateStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query1.setReportDateEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query1.setRiskDateStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query1.setRiskDateEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query1.setClaimState("claimState");
        query1.setFinishDateStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query1.setFinishDateEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query1.setClaimId(0);
        query1.setApplicant("applicant");
        query1.setApplicantType(0);
        when(mockClaimCancelReportMapper.search(query1)).thenReturn(smClaimCancelReportVos);

        // Configure AuthUserMapper.listUserByIdCards(...).
        final UserDTO userDTO = new UserDTO();
        userDTO.setId(0);
        userDTO.setRegionCode("regionCode");
        userDTO.setRegionName("regionName");
        userDTO.setUserIdCard("userIdCard");
        userDTO.setStatus("status");
        final List<UserDTO> userDTOS = Arrays.asList(userDTO);
        when(mockAuthUserMapper.listUserByIdCards(Arrays.asList("value"))).thenReturn(userDTOS);

        // Configure SmClaimRuleLabelDetailService.listClaimLabelInfoByClaimIds(...).
        final SmClaimRuleLabelDetailPO smClaimRuleLabelDetailPO = new SmClaimRuleLabelDetailPO();
        smClaimRuleLabelDetailPO.setId(0);
        smClaimRuleLabelDetailPO.setEnabledFlag(0);
        smClaimRuleLabelDetailPO.setClaimId(0);
        smClaimRuleLabelDetailPO.setValueCode("label");
        smClaimRuleLabelDetailPO.setValueDesc("labelName");
        final List<SmClaimRuleLabelDetailPO> smClaimRuleLabelDetailPOS = Arrays.asList(smClaimRuleLabelDetailPO);
        when(mockSmClaimRuleLabelDetailService.listClaimLabelInfoByClaimIds(Arrays.asList(0))).thenReturn(smClaimRuleLabelDetailPOS);

        // Configure SmCommonSettingMapper.queryListOption(...).
        final SmCommonSettingVO smCommonSettingVO = new SmCommonSettingVO();
        smCommonSettingVO.setFieldCode("fieldCode");
        smCommonSettingVO.setOptionName("optionName");
        smCommonSettingVO.setCompanyId(0);
        smCommonSettingVO.setOptionCode("optionCode");
        smCommonSettingVO.setCommonCode("commonCode");
        final List<SmCommonSettingVO> smCommonSettingVOS = Arrays.asList(smCommonSettingVO);
        final SmOfflineOrderValidation.CodeMapperHelper code = new SmOfflineOrderValidation.CodeMapperHelper();
        code.setCompanyId("companyId");
        code.setFieldCode(new HashSet<>(Arrays.asList("value")));
        code.setOptionName(new HashSet<>(Arrays.asList("value")));
        when(mockSmCommonSettingMapper.queryListOption(code)).thenReturn(smCommonSettingVOS);

        // Run the test
        smClaimServiceImplUnderTest.downloadCancelClaimList(query, resp);

        // Verify the results
    }

    @Test
    public void testDownloadCancelClaimList_SmClaimCancelReportMapperReturnsNoItems() {
        // Setup
        final SmClaimQuery query = new SmClaimQuery();
        query.setPage(0);
        query.setSize(0);
        query.setAll(false);
        query.setQueryPage(false);
        query.setReportDateStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setReportDateEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setRiskDateStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setRiskDateEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setClaimState("claimState");
        query.setFinishDateStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setFinishDateEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setClaimId(0);
        query.setApplicant("applicant");
        query.setApplicantType(0);

        final MockHttpServletResponse resp = new MockHttpServletResponse();

        // Configure SmClaimCancelReportMapper.search(...).
        final SmClaimQuery query1 = new SmClaimQuery();
        query1.setPage(0);
        query1.setSize(0);
        query1.setAll(false);
        query1.setQueryPage(false);
        query1.setReportDateStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query1.setReportDateEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query1.setRiskDateStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query1.setRiskDateEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query1.setClaimState("claimState");
        query1.setFinishDateStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query1.setFinishDateEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query1.setClaimId(0);
        query1.setApplicant("applicant");
        query1.setApplicantType(0);
        when(mockClaimCancelReportMapper.search(query1)).thenReturn(Collections.emptyList());

        // Configure AuthUserMapper.listUserByIdCards(...).
        final UserDTO userDTO = new UserDTO();
        userDTO.setId(0);
        userDTO.setRegionCode("regionCode");
        userDTO.setRegionName("regionName");
        userDTO.setUserIdCard("userIdCard");
        userDTO.setStatus("status");
        final List<UserDTO> userDTOS = Arrays.asList(userDTO);
        when(mockAuthUserMapper.listUserByIdCards(Arrays.asList("value"))).thenReturn(userDTOS);

        // Configure SmClaimRuleLabelDetailService.listClaimLabelInfoByClaimIds(...).
        final SmClaimRuleLabelDetailPO smClaimRuleLabelDetailPO = new SmClaimRuleLabelDetailPO();
        smClaimRuleLabelDetailPO.setId(0);
        smClaimRuleLabelDetailPO.setEnabledFlag(0);
        smClaimRuleLabelDetailPO.setClaimId(0);
        smClaimRuleLabelDetailPO.setValueCode("label");
        smClaimRuleLabelDetailPO.setValueDesc("labelName");
        final List<SmClaimRuleLabelDetailPO> smClaimRuleLabelDetailPOS = Arrays.asList(smClaimRuleLabelDetailPO);
        when(mockSmClaimRuleLabelDetailService.listClaimLabelInfoByClaimIds(Arrays.asList(0))).thenReturn(smClaimRuleLabelDetailPOS);

        // Configure SmCommonSettingMapper.queryListOption(...).
        final SmCommonSettingVO smCommonSettingVO = new SmCommonSettingVO();
        smCommonSettingVO.setFieldCode("fieldCode");
        smCommonSettingVO.setOptionName("optionName");
        smCommonSettingVO.setCompanyId(0);
        smCommonSettingVO.setOptionCode("optionCode");
        smCommonSettingVO.setCommonCode("commonCode");
        final List<SmCommonSettingVO> smCommonSettingVOS = Arrays.asList(smCommonSettingVO);
        final SmOfflineOrderValidation.CodeMapperHelper code = new SmOfflineOrderValidation.CodeMapperHelper();
        code.setCompanyId("companyId");
        code.setFieldCode(new HashSet<>(Arrays.asList("value")));
        code.setOptionName(new HashSet<>(Arrays.asList("value")));
        when(mockSmCommonSettingMapper.queryListOption(code)).thenReturn(smCommonSettingVOS);

        // Run the test
        smClaimServiceImplUnderTest.downloadCancelClaimList(query, resp);

        // Verify the results
    }

    @Test
    public void testDownloadCancelClaimList_AuthUserMapperReturnsNoItems() {
        // Setup
        final SmClaimQuery query = new SmClaimQuery();
        query.setPage(0);
        query.setSize(0);
        query.setAll(false);
        query.setQueryPage(false);
        query.setReportDateStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setReportDateEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setRiskDateStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setRiskDateEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setClaimState("claimState");
        query.setFinishDateStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setFinishDateEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setClaimId(0);
        query.setApplicant("applicant");
        query.setApplicantType(0);

        final MockHttpServletResponse resp = new MockHttpServletResponse();

        // Configure SmClaimCancelReportMapper.search(...).
        final SmClaimCancelReportVo smClaimCancelReportVo = new SmClaimCancelReportVo();
        smClaimCancelReportVo.setClaimCancelReportId(0);
        smClaimCancelReportVo.setId("id");
        smClaimCancelReportVo.setApplicantIdNumber("applicantIdNumber");
        smClaimCancelReportVo.setInsuredIdNumber("insuredIdNumber");
        smClaimCancelReportVo.setLabelValueDescList(Arrays.asList("value"));
        final List<SmClaimCancelReportVo> smClaimCancelReportVos = Arrays.asList(smClaimCancelReportVo);
        final SmClaimQuery query1 = new SmClaimQuery();
        query1.setPage(0);
        query1.setSize(0);
        query1.setAll(false);
        query1.setQueryPage(false);
        query1.setReportDateStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query1.setReportDateEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query1.setRiskDateStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query1.setRiskDateEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query1.setClaimState("claimState");
        query1.setFinishDateStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query1.setFinishDateEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query1.setClaimId(0);
        query1.setApplicant("applicant");
        query1.setApplicantType(0);
        when(mockClaimCancelReportMapper.search(query1)).thenReturn(smClaimCancelReportVos);

        when(mockAuthUserMapper.listUserByIdCards(Arrays.asList("value"))).thenReturn(Collections.emptyList());

        // Configure SmClaimRuleLabelDetailService.listClaimLabelInfoByClaimIds(...).
        final SmClaimRuleLabelDetailPO smClaimRuleLabelDetailPO = new SmClaimRuleLabelDetailPO();
        smClaimRuleLabelDetailPO.setId(0);
        smClaimRuleLabelDetailPO.setEnabledFlag(0);
        smClaimRuleLabelDetailPO.setClaimId(0);
        smClaimRuleLabelDetailPO.setValueCode("label");
        smClaimRuleLabelDetailPO.setValueDesc("labelName");
        final List<SmClaimRuleLabelDetailPO> smClaimRuleLabelDetailPOS = Arrays.asList(smClaimRuleLabelDetailPO);
        when(mockSmClaimRuleLabelDetailService.listClaimLabelInfoByClaimIds(Arrays.asList(0))).thenReturn(smClaimRuleLabelDetailPOS);

        // Configure SmCommonSettingMapper.queryListOption(...).
        final SmCommonSettingVO smCommonSettingVO = new SmCommonSettingVO();
        smCommonSettingVO.setFieldCode("fieldCode");
        smCommonSettingVO.setOptionName("optionName");
        smCommonSettingVO.setCompanyId(0);
        smCommonSettingVO.setOptionCode("optionCode");
        smCommonSettingVO.setCommonCode("commonCode");
        final List<SmCommonSettingVO> smCommonSettingVOS = Arrays.asList(smCommonSettingVO);
        final SmOfflineOrderValidation.CodeMapperHelper code = new SmOfflineOrderValidation.CodeMapperHelper();
        code.setCompanyId("companyId");
        code.setFieldCode(new HashSet<>(Arrays.asList("value")));
        code.setOptionName(new HashSet<>(Arrays.asList("value")));
        when(mockSmCommonSettingMapper.queryListOption(code)).thenReturn(smCommonSettingVOS);

        // Run the test
        smClaimServiceImplUnderTest.downloadCancelClaimList(query, resp);

        // Verify the results
    }

    @Test
    public void testDownloadCancelClaimList_SmClaimRuleLabelDetailServiceReturnsNull() {
        // Setup
        final SmClaimQuery query = new SmClaimQuery();
        query.setPage(0);
        query.setSize(0);
        query.setAll(false);
        query.setQueryPage(false);
        query.setReportDateStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setReportDateEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setRiskDateStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setRiskDateEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setClaimState("claimState");
        query.setFinishDateStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setFinishDateEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setClaimId(0);
        query.setApplicant("applicant");
        query.setApplicantType(0);

        final MockHttpServletResponse resp = new MockHttpServletResponse();

        // Configure SmClaimCancelReportMapper.search(...).
        final SmClaimCancelReportVo smClaimCancelReportVo = new SmClaimCancelReportVo();
        smClaimCancelReportVo.setClaimCancelReportId(0);
        smClaimCancelReportVo.setId("id");
        smClaimCancelReportVo.setApplicantIdNumber("applicantIdNumber");
        smClaimCancelReportVo.setInsuredIdNumber("insuredIdNumber");
        smClaimCancelReportVo.setLabelValueDescList(Arrays.asList("value"));
        final List<SmClaimCancelReportVo> smClaimCancelReportVos = Arrays.asList(smClaimCancelReportVo);
        final SmClaimQuery query1 = new SmClaimQuery();
        query1.setPage(0);
        query1.setSize(0);
        query1.setAll(false);
        query1.setQueryPage(false);
        query1.setReportDateStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query1.setReportDateEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query1.setRiskDateStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query1.setRiskDateEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query1.setClaimState("claimState");
        query1.setFinishDateStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query1.setFinishDateEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query1.setClaimId(0);
        query1.setApplicant("applicant");
        query1.setApplicantType(0);
        when(mockClaimCancelReportMapper.search(query1)).thenReturn(smClaimCancelReportVos);

        // Configure AuthUserMapper.listUserByIdCards(...).
        final UserDTO userDTO = new UserDTO();
        userDTO.setId(0);
        userDTO.setRegionCode("regionCode");
        userDTO.setRegionName("regionName");
        userDTO.setUserIdCard("userIdCard");
        userDTO.setStatus("status");
        final List<UserDTO> userDTOS = Arrays.asList(userDTO);
        when(mockAuthUserMapper.listUserByIdCards(Arrays.asList("value"))).thenReturn(userDTOS);

        when(mockSmClaimRuleLabelDetailService.listClaimLabelInfoByClaimIds(Arrays.asList(0))).thenReturn(null);

        // Configure SmCommonSettingMapper.queryListOption(...).
        final SmCommonSettingVO smCommonSettingVO = new SmCommonSettingVO();
        smCommonSettingVO.setFieldCode("fieldCode");
        smCommonSettingVO.setOptionName("optionName");
        smCommonSettingVO.setCompanyId(0);
        smCommonSettingVO.setOptionCode("optionCode");
        smCommonSettingVO.setCommonCode("commonCode");
        final List<SmCommonSettingVO> smCommonSettingVOS = Arrays.asList(smCommonSettingVO);
        final SmOfflineOrderValidation.CodeMapperHelper code = new SmOfflineOrderValidation.CodeMapperHelper();
        code.setCompanyId("companyId");
        code.setFieldCode(new HashSet<>(Arrays.asList("value")));
        code.setOptionName(new HashSet<>(Arrays.asList("value")));
        when(mockSmCommonSettingMapper.queryListOption(code)).thenReturn(smCommonSettingVOS);

        // Run the test
        smClaimServiceImplUnderTest.downloadCancelClaimList(query, resp);

        // Verify the results
    }

    @Test
    public void testDownloadCancelClaimList_SmClaimRuleLabelDetailServiceReturnsNoItems() {
        // Setup
        final SmClaimQuery query = new SmClaimQuery();
        query.setPage(0);
        query.setSize(0);
        query.setAll(false);
        query.setQueryPage(false);
        query.setReportDateStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setReportDateEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setRiskDateStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setRiskDateEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setClaimState("claimState");
        query.setFinishDateStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setFinishDateEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setClaimId(0);
        query.setApplicant("applicant");
        query.setApplicantType(0);

        final MockHttpServletResponse resp = new MockHttpServletResponse();

        // Configure SmClaimCancelReportMapper.search(...).
        final SmClaimCancelReportVo smClaimCancelReportVo = new SmClaimCancelReportVo();
        smClaimCancelReportVo.setClaimCancelReportId(0);
        smClaimCancelReportVo.setId("id");
        smClaimCancelReportVo.setApplicantIdNumber("applicantIdNumber");
        smClaimCancelReportVo.setInsuredIdNumber("insuredIdNumber");
        smClaimCancelReportVo.setLabelValueDescList(Arrays.asList("value"));
        final List<SmClaimCancelReportVo> smClaimCancelReportVos = Arrays.asList(smClaimCancelReportVo);
        final SmClaimQuery query1 = new SmClaimQuery();
        query1.setPage(0);
        query1.setSize(0);
        query1.setAll(false);
        query1.setQueryPage(false);
        query1.setReportDateStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query1.setReportDateEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query1.setRiskDateStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query1.setRiskDateEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query1.setClaimState("claimState");
        query1.setFinishDateStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query1.setFinishDateEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query1.setClaimId(0);
        query1.setApplicant("applicant");
        query1.setApplicantType(0);
        when(mockClaimCancelReportMapper.search(query1)).thenReturn(smClaimCancelReportVos);

        // Configure AuthUserMapper.listUserByIdCards(...).
        final UserDTO userDTO = new UserDTO();
        userDTO.setId(0);
        userDTO.setRegionCode("regionCode");
        userDTO.setRegionName("regionName");
        userDTO.setUserIdCard("userIdCard");
        userDTO.setStatus("status");
        final List<UserDTO> userDTOS = Arrays.asList(userDTO);
        when(mockAuthUserMapper.listUserByIdCards(Arrays.asList("value"))).thenReturn(userDTOS);

        when(mockSmClaimRuleLabelDetailService.listClaimLabelInfoByClaimIds(Arrays.asList(0))).thenReturn(Collections.emptyList());

        // Configure SmCommonSettingMapper.queryListOption(...).
        final SmCommonSettingVO smCommonSettingVO = new SmCommonSettingVO();
        smCommonSettingVO.setFieldCode("fieldCode");
        smCommonSettingVO.setOptionName("optionName");
        smCommonSettingVO.setCompanyId(0);
        smCommonSettingVO.setOptionCode("optionCode");
        smCommonSettingVO.setCommonCode("commonCode");
        final List<SmCommonSettingVO> smCommonSettingVOS = Arrays.asList(smCommonSettingVO);
        final SmOfflineOrderValidation.CodeMapperHelper code = new SmOfflineOrderValidation.CodeMapperHelper();
        code.setCompanyId("companyId");
        code.setFieldCode(new HashSet<>(Arrays.asList("value")));
        code.setOptionName(new HashSet<>(Arrays.asList("value")));
        when(mockSmCommonSettingMapper.queryListOption(code)).thenReturn(smCommonSettingVOS);

        // Run the test
        smClaimServiceImplUnderTest.downloadCancelClaimList(query, resp);

        // Verify the results
    }

    @Test
    public void testDownloadCancelClaimList_SmCommonSettingMapperReturnsNoItems() {
        // Setup
        final SmClaimQuery query = new SmClaimQuery();
        query.setPage(0);
        query.setSize(0);
        query.setAll(false);
        query.setQueryPage(false);
        query.setReportDateStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setReportDateEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setRiskDateStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setRiskDateEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setClaimState("claimState");
        query.setFinishDateStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setFinishDateEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setClaimId(0);
        query.setApplicant("applicant");
        query.setApplicantType(0);

        final MockHttpServletResponse resp = new MockHttpServletResponse();

        // Configure SmClaimCancelReportMapper.search(...).
        final SmClaimCancelReportVo smClaimCancelReportVo = new SmClaimCancelReportVo();
        smClaimCancelReportVo.setClaimCancelReportId(0);
        smClaimCancelReportVo.setId("id");
        smClaimCancelReportVo.setApplicantIdNumber("applicantIdNumber");
        smClaimCancelReportVo.setInsuredIdNumber("insuredIdNumber");
        smClaimCancelReportVo.setLabelValueDescList(Arrays.asList("value"));
        final List<SmClaimCancelReportVo> smClaimCancelReportVos = Arrays.asList(smClaimCancelReportVo);
        final SmClaimQuery query1 = new SmClaimQuery();
        query1.setPage(0);
        query1.setSize(0);
        query1.setAll(false);
        query1.setQueryPage(false);
        query1.setReportDateStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query1.setReportDateEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query1.setRiskDateStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query1.setRiskDateEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query1.setClaimState("claimState");
        query1.setFinishDateStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query1.setFinishDateEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query1.setClaimId(0);
        query1.setApplicant("applicant");
        query1.setApplicantType(0);
        when(mockClaimCancelReportMapper.search(query1)).thenReturn(smClaimCancelReportVos);

        // Configure AuthUserMapper.listUserByIdCards(...).
        final UserDTO userDTO = new UserDTO();
        userDTO.setId(0);
        userDTO.setRegionCode("regionCode");
        userDTO.setRegionName("regionName");
        userDTO.setUserIdCard("userIdCard");
        userDTO.setStatus("status");
        final List<UserDTO> userDTOS = Arrays.asList(userDTO);
        when(mockAuthUserMapper.listUserByIdCards(Arrays.asList("value"))).thenReturn(userDTOS);

        // Configure SmClaimRuleLabelDetailService.listClaimLabelInfoByClaimIds(...).
        final SmClaimRuleLabelDetailPO smClaimRuleLabelDetailPO = new SmClaimRuleLabelDetailPO();
        smClaimRuleLabelDetailPO.setId(0);
        smClaimRuleLabelDetailPO.setEnabledFlag(0);
        smClaimRuleLabelDetailPO.setClaimId(0);
        smClaimRuleLabelDetailPO.setValueCode("label");
        smClaimRuleLabelDetailPO.setValueDesc("labelName");
        final List<SmClaimRuleLabelDetailPO> smClaimRuleLabelDetailPOS = Arrays.asList(smClaimRuleLabelDetailPO);
        when(mockSmClaimRuleLabelDetailService.listClaimLabelInfoByClaimIds(Arrays.asList(0))).thenReturn(smClaimRuleLabelDetailPOS);

        // Configure SmCommonSettingMapper.queryListOption(...).
        final SmOfflineOrderValidation.CodeMapperHelper code = new SmOfflineOrderValidation.CodeMapperHelper();
        code.setCompanyId("companyId");
        code.setFieldCode(new HashSet<>(Arrays.asList("value")));
        code.setOptionName(new HashSet<>(Arrays.asList("value")));
        when(mockSmCommonSettingMapper.queryListOption(code)).thenReturn(Collections.emptyList());

        // Run the test
        smClaimServiceImplUnderTest.downloadCancelClaimList(query, resp);

        // Verify the results
    }

    @Test
    public void testDownloadClaims() {
        // Setup
        final SmClaimQuery query = new SmClaimQuery();
        query.setPage(0);
        query.setSize(0);
        query.setAll(false);
        query.setQueryPage(false);
        query.setReportDateStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setReportDateEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setRiskDateStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setRiskDateEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setClaimState("claimState");
        query.setFinishDateStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setFinishDateEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setClaimId(0);
        query.setApplicant("applicant");
        query.setApplicantType(0);

        final MockHttpServletResponse resp = new MockHttpServletResponse();

        // Configure SmClaimFinishNotifyMapper.selectByClaimIdList(...).
        final SmClaimFinishNotify smClaimFinishNotify = new SmClaimFinishNotify();
        smClaimFinishNotify.setId(0);
        smClaimFinishNotify.setEnabledFlag(0);
        smClaimFinishNotify.setClaimId(0);
        smClaimFinishNotify.setRiskAddress("riskAddress");
        smClaimFinishNotify.setRiskTypeName("riskTypeName");
        final List<SmClaimFinishNotify> smClaimFinishNotifies = Arrays.asList(smClaimFinishNotify);
        when(mockClaimFinishNotifyMapper.selectByClaimIdList(Arrays.asList(0))).thenReturn(smClaimFinishNotifies);

        when(mockDictionaryService.groupByTypes(Arrays.asList("value"))).thenReturn(new HashMap<>());

        // Configure SmClaimRuleLabelDetailService.listClaimLabelInfoByClaimIds(...).
        final SmClaimRuleLabelDetailPO smClaimRuleLabelDetailPO = new SmClaimRuleLabelDetailPO();
        smClaimRuleLabelDetailPO.setId(0);
        smClaimRuleLabelDetailPO.setEnabledFlag(0);
        smClaimRuleLabelDetailPO.setClaimId(0);
        smClaimRuleLabelDetailPO.setValueCode("label");
        smClaimRuleLabelDetailPO.setValueDesc("labelName");
        final List<SmClaimRuleLabelDetailPO> smClaimRuleLabelDetailPOS = Arrays.asList(smClaimRuleLabelDetailPO);
        when(mockSmClaimRuleLabelDetailService.listClaimLabelInfoByClaimIds(Arrays.asList(0))).thenReturn(smClaimRuleLabelDetailPOS);

        // Configure AuthUserMapper.listUserByIdCards(...).
        final UserDTO userDTO = new UserDTO();
        userDTO.setId(0);
        userDTO.setRegionCode("regionCode");
        userDTO.setRegionName("regionName");
        userDTO.setUserIdCard("userIdCard");
        userDTO.setStatus("status");
        final List<UserDTO> userDTOS = Arrays.asList(userDTO);
        when(mockAuthUserMapper.listUserByIdCards(Arrays.asList("value"))).thenReturn(userDTOS);

        when(mockClaimRiskReasonService.getRiskReasonByCode("claimRiskReason")).thenReturn("claimRiskReason");

        // Configure SmCommonSettingMapper.queryListOption(...).
        final SmCommonSettingVO smCommonSettingVO = new SmCommonSettingVO();
        smCommonSettingVO.setFieldCode("fieldCode");
        smCommonSettingVO.setOptionName("optionName");
        smCommonSettingVO.setCompanyId(0);
        smCommonSettingVO.setOptionCode("optionCode");
        smCommonSettingVO.setCommonCode("commonCode");
        final List<SmCommonSettingVO> smCommonSettingVOS = Arrays.asList(smCommonSettingVO);
        final SmOfflineOrderValidation.CodeMapperHelper code = new SmOfflineOrderValidation.CodeMapperHelper();
        code.setCompanyId("companyId");
        code.setFieldCode(new HashSet<>(Arrays.asList("value")));
        code.setOptionName(new HashSet<>(Arrays.asList("value")));
        when(mockSmCommonSettingMapper.queryListOption(code)).thenReturn(smCommonSettingVOS);

        // Run the test
        smClaimServiceImplUnderTest.downloadClaims(query, resp);

        // Verify the results
    }

    @Test
    public void testDownloadClaims_SmClaimFinishNotifyMapperReturnsNoItems() {
        // Setup
        final SmClaimQuery query = new SmClaimQuery();
        query.setPage(0);
        query.setSize(0);
        query.setAll(false);
        query.setQueryPage(false);
        query.setReportDateStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setReportDateEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setRiskDateStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setRiskDateEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setClaimState("claimState");
        query.setFinishDateStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setFinishDateEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setClaimId(0);
        query.setApplicant("applicant");
        query.setApplicantType(0);

        final MockHttpServletResponse resp = new MockHttpServletResponse();
        when(mockClaimFinishNotifyMapper.selectByClaimIdList(Arrays.asList(0))).thenReturn(Collections.emptyList());
        when(mockDictionaryService.groupByTypes(Arrays.asList("value"))).thenReturn(new HashMap<>());

        // Configure SmClaimRuleLabelDetailService.listClaimLabelInfoByClaimIds(...).
        final SmClaimRuleLabelDetailPO smClaimRuleLabelDetailPO = new SmClaimRuleLabelDetailPO();
        smClaimRuleLabelDetailPO.setId(0);
        smClaimRuleLabelDetailPO.setEnabledFlag(0);
        smClaimRuleLabelDetailPO.setClaimId(0);
        smClaimRuleLabelDetailPO.setValueCode("label");
        smClaimRuleLabelDetailPO.setValueDesc("labelName");
        final List<SmClaimRuleLabelDetailPO> smClaimRuleLabelDetailPOS = Arrays.asList(smClaimRuleLabelDetailPO);
        when(mockSmClaimRuleLabelDetailService.listClaimLabelInfoByClaimIds(Arrays.asList(0))).thenReturn(smClaimRuleLabelDetailPOS);

        // Configure AuthUserMapper.listUserByIdCards(...).
        final UserDTO userDTO = new UserDTO();
        userDTO.setId(0);
        userDTO.setRegionCode("regionCode");
        userDTO.setRegionName("regionName");
        userDTO.setUserIdCard("userIdCard");
        userDTO.setStatus("status");
        final List<UserDTO> userDTOS = Arrays.asList(userDTO);
        when(mockAuthUserMapper.listUserByIdCards(Arrays.asList("value"))).thenReturn(userDTOS);

        when(mockClaimRiskReasonService.getRiskReasonByCode("claimRiskReason")).thenReturn("claimRiskReason");

        // Configure SmCommonSettingMapper.queryListOption(...).
        final SmCommonSettingVO smCommonSettingVO = new SmCommonSettingVO();
        smCommonSettingVO.setFieldCode("fieldCode");
        smCommonSettingVO.setOptionName("optionName");
        smCommonSettingVO.setCompanyId(0);
        smCommonSettingVO.setOptionCode("optionCode");
        smCommonSettingVO.setCommonCode("commonCode");
        final List<SmCommonSettingVO> smCommonSettingVOS = Arrays.asList(smCommonSettingVO);
        final SmOfflineOrderValidation.CodeMapperHelper code = new SmOfflineOrderValidation.CodeMapperHelper();
        code.setCompanyId("companyId");
        code.setFieldCode(new HashSet<>(Arrays.asList("value")));
        code.setOptionName(new HashSet<>(Arrays.asList("value")));
        when(mockSmCommonSettingMapper.queryListOption(code)).thenReturn(smCommonSettingVOS);

        // Run the test
        smClaimServiceImplUnderTest.downloadClaims(query, resp);

        // Verify the results
    }

    @Test
    public void testDownloadClaims_SmClaimRuleLabelDetailServiceReturnsNull() {
        // Setup
        final SmClaimQuery query = new SmClaimQuery();
        query.setPage(0);
        query.setSize(0);
        query.setAll(false);
        query.setQueryPage(false);
        query.setReportDateStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setReportDateEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setRiskDateStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setRiskDateEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setClaimState("claimState");
        query.setFinishDateStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setFinishDateEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setClaimId(0);
        query.setApplicant("applicant");
        query.setApplicantType(0);

        final MockHttpServletResponse resp = new MockHttpServletResponse();

        // Configure SmClaimFinishNotifyMapper.selectByClaimIdList(...).
        final SmClaimFinishNotify smClaimFinishNotify = new SmClaimFinishNotify();
        smClaimFinishNotify.setId(0);
        smClaimFinishNotify.setEnabledFlag(0);
        smClaimFinishNotify.setClaimId(0);
        smClaimFinishNotify.setRiskAddress("riskAddress");
        smClaimFinishNotify.setRiskTypeName("riskTypeName");
        final List<SmClaimFinishNotify> smClaimFinishNotifies = Arrays.asList(smClaimFinishNotify);
        when(mockClaimFinishNotifyMapper.selectByClaimIdList(Arrays.asList(0))).thenReturn(smClaimFinishNotifies);

        when(mockDictionaryService.groupByTypes(Arrays.asList("value"))).thenReturn(new HashMap<>());
        when(mockSmClaimRuleLabelDetailService.listClaimLabelInfoByClaimIds(Arrays.asList(0))).thenReturn(null);

        // Configure AuthUserMapper.listUserByIdCards(...).
        final UserDTO userDTO = new UserDTO();
        userDTO.setId(0);
        userDTO.setRegionCode("regionCode");
        userDTO.setRegionName("regionName");
        userDTO.setUserIdCard("userIdCard");
        userDTO.setStatus("status");
        final List<UserDTO> userDTOS = Arrays.asList(userDTO);
        when(mockAuthUserMapper.listUserByIdCards(Arrays.asList("value"))).thenReturn(userDTOS);

        when(mockClaimRiskReasonService.getRiskReasonByCode("claimRiskReason")).thenReturn("claimRiskReason");

        // Configure SmCommonSettingMapper.queryListOption(...).
        final SmCommonSettingVO smCommonSettingVO = new SmCommonSettingVO();
        smCommonSettingVO.setFieldCode("fieldCode");
        smCommonSettingVO.setOptionName("optionName");
        smCommonSettingVO.setCompanyId(0);
        smCommonSettingVO.setOptionCode("optionCode");
        smCommonSettingVO.setCommonCode("commonCode");
        final List<SmCommonSettingVO> smCommonSettingVOS = Arrays.asList(smCommonSettingVO);
        final SmOfflineOrderValidation.CodeMapperHelper code = new SmOfflineOrderValidation.CodeMapperHelper();
        code.setCompanyId("companyId");
        code.setFieldCode(new HashSet<>(Arrays.asList("value")));
        code.setOptionName(new HashSet<>(Arrays.asList("value")));
        when(mockSmCommonSettingMapper.queryListOption(code)).thenReturn(smCommonSettingVOS);

        // Run the test
        smClaimServiceImplUnderTest.downloadClaims(query, resp);

        // Verify the results
    }

    @Test
    public void testDownloadClaims_SmClaimRuleLabelDetailServiceReturnsNoItems() {
        // Setup
        final SmClaimQuery query = new SmClaimQuery();
        query.setPage(0);
        query.setSize(0);
        query.setAll(false);
        query.setQueryPage(false);
        query.setReportDateStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setReportDateEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setRiskDateStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setRiskDateEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setClaimState("claimState");
        query.setFinishDateStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setFinishDateEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setClaimId(0);
        query.setApplicant("applicant");
        query.setApplicantType(0);

        final MockHttpServletResponse resp = new MockHttpServletResponse();

        // Configure SmClaimFinishNotifyMapper.selectByClaimIdList(...).
        final SmClaimFinishNotify smClaimFinishNotify = new SmClaimFinishNotify();
        smClaimFinishNotify.setId(0);
        smClaimFinishNotify.setEnabledFlag(0);
        smClaimFinishNotify.setClaimId(0);
        smClaimFinishNotify.setRiskAddress("riskAddress");
        smClaimFinishNotify.setRiskTypeName("riskTypeName");
        final List<SmClaimFinishNotify> smClaimFinishNotifies = Arrays.asList(smClaimFinishNotify);
        when(mockClaimFinishNotifyMapper.selectByClaimIdList(Arrays.asList(0))).thenReturn(smClaimFinishNotifies);

        when(mockDictionaryService.groupByTypes(Arrays.asList("value"))).thenReturn(new HashMap<>());
        when(mockSmClaimRuleLabelDetailService.listClaimLabelInfoByClaimIds(Arrays.asList(0))).thenReturn(Collections.emptyList());

        // Configure AuthUserMapper.listUserByIdCards(...).
        final UserDTO userDTO = new UserDTO();
        userDTO.setId(0);
        userDTO.setRegionCode("regionCode");
        userDTO.setRegionName("regionName");
        userDTO.setUserIdCard("userIdCard");
        userDTO.setStatus("status");
        final List<UserDTO> userDTOS = Arrays.asList(userDTO);
        when(mockAuthUserMapper.listUserByIdCards(Arrays.asList("value"))).thenReturn(userDTOS);

        when(mockClaimRiskReasonService.getRiskReasonByCode("claimRiskReason")).thenReturn("claimRiskReason");

        // Configure SmCommonSettingMapper.queryListOption(...).
        final SmCommonSettingVO smCommonSettingVO = new SmCommonSettingVO();
        smCommonSettingVO.setFieldCode("fieldCode");
        smCommonSettingVO.setOptionName("optionName");
        smCommonSettingVO.setCompanyId(0);
        smCommonSettingVO.setOptionCode("optionCode");
        smCommonSettingVO.setCommonCode("commonCode");
        final List<SmCommonSettingVO> smCommonSettingVOS = Arrays.asList(smCommonSettingVO);
        final SmOfflineOrderValidation.CodeMapperHelper code = new SmOfflineOrderValidation.CodeMapperHelper();
        code.setCompanyId("companyId");
        code.setFieldCode(new HashSet<>(Arrays.asList("value")));
        code.setOptionName(new HashSet<>(Arrays.asList("value")));
        when(mockSmCommonSettingMapper.queryListOption(code)).thenReturn(smCommonSettingVOS);

        // Run the test
        smClaimServiceImplUnderTest.downloadClaims(query, resp);

        // Verify the results
    }

    @Test
    public void testDownloadClaims_AuthUserMapperReturnsNoItems() {
        // Setup
        final SmClaimQuery query = new SmClaimQuery();
        query.setPage(0);
        query.setSize(0);
        query.setAll(false);
        query.setQueryPage(false);
        query.setReportDateStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setReportDateEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setRiskDateStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setRiskDateEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setClaimState("claimState");
        query.setFinishDateStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setFinishDateEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setClaimId(0);
        query.setApplicant("applicant");
        query.setApplicantType(0);

        final MockHttpServletResponse resp = new MockHttpServletResponse();

        // Configure SmClaimFinishNotifyMapper.selectByClaimIdList(...).
        final SmClaimFinishNotify smClaimFinishNotify = new SmClaimFinishNotify();
        smClaimFinishNotify.setId(0);
        smClaimFinishNotify.setEnabledFlag(0);
        smClaimFinishNotify.setClaimId(0);
        smClaimFinishNotify.setRiskAddress("riskAddress");
        smClaimFinishNotify.setRiskTypeName("riskTypeName");
        final List<SmClaimFinishNotify> smClaimFinishNotifies = Arrays.asList(smClaimFinishNotify);
        when(mockClaimFinishNotifyMapper.selectByClaimIdList(Arrays.asList(0))).thenReturn(smClaimFinishNotifies);

        when(mockDictionaryService.groupByTypes(Arrays.asList("value"))).thenReturn(new HashMap<>());

        // Configure SmClaimRuleLabelDetailService.listClaimLabelInfoByClaimIds(...).
        final SmClaimRuleLabelDetailPO smClaimRuleLabelDetailPO = new SmClaimRuleLabelDetailPO();
        smClaimRuleLabelDetailPO.setId(0);
        smClaimRuleLabelDetailPO.setEnabledFlag(0);
        smClaimRuleLabelDetailPO.setClaimId(0);
        smClaimRuleLabelDetailPO.setValueCode("label");
        smClaimRuleLabelDetailPO.setValueDesc("labelName");
        final List<SmClaimRuleLabelDetailPO> smClaimRuleLabelDetailPOS = Arrays.asList(smClaimRuleLabelDetailPO);
        when(mockSmClaimRuleLabelDetailService.listClaimLabelInfoByClaimIds(Arrays.asList(0))).thenReturn(smClaimRuleLabelDetailPOS);

        when(mockAuthUserMapper.listUserByIdCards(Arrays.asList("value"))).thenReturn(Collections.emptyList());
        when(mockClaimRiskReasonService.getRiskReasonByCode("claimRiskReason")).thenReturn("claimRiskReason");

        // Configure SmCommonSettingMapper.queryListOption(...).
        final SmCommonSettingVO smCommonSettingVO = new SmCommonSettingVO();
        smCommonSettingVO.setFieldCode("fieldCode");
        smCommonSettingVO.setOptionName("optionName");
        smCommonSettingVO.setCompanyId(0);
        smCommonSettingVO.setOptionCode("optionCode");
        smCommonSettingVO.setCommonCode("commonCode");
        final List<SmCommonSettingVO> smCommonSettingVOS = Arrays.asList(smCommonSettingVO);
        final SmOfflineOrderValidation.CodeMapperHelper code = new SmOfflineOrderValidation.CodeMapperHelper();
        code.setCompanyId("companyId");
        code.setFieldCode(new HashSet<>(Arrays.asList("value")));
        code.setOptionName(new HashSet<>(Arrays.asList("value")));
        when(mockSmCommonSettingMapper.queryListOption(code)).thenReturn(smCommonSettingVOS);

        // Run the test
        smClaimServiceImplUnderTest.downloadClaims(query, resp);

        // Verify the results
    }

    @Test
    public void testDownloadClaims_SmCommonSettingMapperReturnsNoItems() {
        // Setup
        final SmClaimQuery query = new SmClaimQuery();
        query.setPage(0);
        query.setSize(0);
        query.setAll(false);
        query.setQueryPage(false);
        query.setReportDateStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setReportDateEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setRiskDateStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setRiskDateEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setClaimState("claimState");
        query.setFinishDateStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setFinishDateEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setClaimId(0);
        query.setApplicant("applicant");
        query.setApplicantType(0);

        final MockHttpServletResponse resp = new MockHttpServletResponse();

        // Configure SmClaimFinishNotifyMapper.selectByClaimIdList(...).
        final SmClaimFinishNotify smClaimFinishNotify = new SmClaimFinishNotify();
        smClaimFinishNotify.setId(0);
        smClaimFinishNotify.setEnabledFlag(0);
        smClaimFinishNotify.setClaimId(0);
        smClaimFinishNotify.setRiskAddress("riskAddress");
        smClaimFinishNotify.setRiskTypeName("riskTypeName");
        final List<SmClaimFinishNotify> smClaimFinishNotifies = Arrays.asList(smClaimFinishNotify);
        when(mockClaimFinishNotifyMapper.selectByClaimIdList(Arrays.asList(0))).thenReturn(smClaimFinishNotifies);

        when(mockDictionaryService.groupByTypes(Arrays.asList("value"))).thenReturn(new HashMap<>());

        // Configure SmClaimRuleLabelDetailService.listClaimLabelInfoByClaimIds(...).
        final SmClaimRuleLabelDetailPO smClaimRuleLabelDetailPO = new SmClaimRuleLabelDetailPO();
        smClaimRuleLabelDetailPO.setId(0);
        smClaimRuleLabelDetailPO.setEnabledFlag(0);
        smClaimRuleLabelDetailPO.setClaimId(0);
        smClaimRuleLabelDetailPO.setValueCode("label");
        smClaimRuleLabelDetailPO.setValueDesc("labelName");
        final List<SmClaimRuleLabelDetailPO> smClaimRuleLabelDetailPOS = Arrays.asList(smClaimRuleLabelDetailPO);
        when(mockSmClaimRuleLabelDetailService.listClaimLabelInfoByClaimIds(Arrays.asList(0))).thenReturn(smClaimRuleLabelDetailPOS);

        // Configure AuthUserMapper.listUserByIdCards(...).
        final UserDTO userDTO = new UserDTO();
        userDTO.setId(0);
        userDTO.setRegionCode("regionCode");
        userDTO.setRegionName("regionName");
        userDTO.setUserIdCard("userIdCard");
        userDTO.setStatus("status");
        final List<UserDTO> userDTOS = Arrays.asList(userDTO);
        when(mockAuthUserMapper.listUserByIdCards(Arrays.asList("value"))).thenReturn(userDTOS);

        when(mockClaimRiskReasonService.getRiskReasonByCode("claimRiskReason")).thenReturn("claimRiskReason");

        // Configure SmCommonSettingMapper.queryListOption(...).
        final SmOfflineOrderValidation.CodeMapperHelper code = new SmOfflineOrderValidation.CodeMapperHelper();
        code.setCompanyId("companyId");
        code.setFieldCode(new HashSet<>(Arrays.asList("value")));
        code.setOptionName(new HashSet<>(Arrays.asList("value")));
        when(mockSmCommonSettingMapper.queryListOption(code)).thenReturn(Collections.emptyList());

        // Run the test
        smClaimServiceImplUnderTest.downloadClaims(query, resp);

        // Verify the results
    }

    @Test
    public void testGetClaimSummaryByProduct() {
        // Setup
        final SmClaimSmyQuery query = new SmClaimSmyQuery();
        query.setPage(0);
        query.setSize(0);
        query.setAll(false);
        query.setQueryPage(false);
        query.setStartDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setEndDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setStartDateR(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setEndDateR(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setStartDateK(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setEndDateK(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setQueryType(0);

        final SmClaimProductSummaryVO smClaimProductSummaryVO = new SmClaimProductSummaryVO();
        smClaimProductSummaryVO.setCaseCount(0);
        smClaimProductSummaryVO.setRiskCycleTime(new BigDecimal("0.00"));
        smClaimProductSummaryVO.setTotalPay(new BigDecimal("0.00"));
        smClaimProductSummaryVO.setUnPayCount(0);
        smClaimProductSummaryVO.setUnPayCount1(0);
        smClaimProductSummaryVO.setUnPayCount2(0);
        smClaimProductSummaryVO.setUnPayCount3(0);
        smClaimProductSummaryVO.setTotalUnPay(new BigDecimal("0.00"));
        smClaimProductSummaryVO.setTotalUnPay1(new BigDecimal("0.00"));
        smClaimProductSummaryVO.setTotalUnPay2(new BigDecimal("0.00"));
        smClaimProductSummaryVO.setTotalUnPay3(new BigDecimal("0.00"));
        smClaimProductSummaryVO.setRiskRation(new BigDecimal("0.00"));
        smClaimProductSummaryVO.setLossPayRation(new BigDecimal("0.00"));
        smClaimProductSummaryVO.setActualPayRation(new BigDecimal("0.00"));
        smClaimProductSummaryVO.setRiskCount(0);
        smClaimProductSummaryVO.setPlanId(0);
        smClaimProductSummaryVO.setRegionName("regionName");
        smClaimProductSummaryVO.setOrganizationName("organizationName");
        final SmClaimSummaryVO smClaimSummaryVO = new SmClaimSummaryVO();
        smClaimSummaryVO.setProductName("合计");
        smClaimSummaryVO.setRegionName("合计");
        smClaimSummaryVO.setOrgName("合计");
        smClaimSummaryVO.setCaseCount(0);
        smClaimSummaryVO.setRiskCycleTime(new BigDecimal("0.00"));
        smClaimSummaryVO.setTotalPay(new BigDecimal("0.00"));
        smClaimSummaryVO.setUnPayCount(0);
        smClaimSummaryVO.setUnPayCount1(0);
        smClaimSummaryVO.setUnPayCount2(0);
        smClaimSummaryVO.setUnPayCount3(0);
        smClaimSummaryVO.setTotalUnPay(new BigDecimal("0.00"));
        smClaimSummaryVO.setTotalUnPay1(new BigDecimal("0.00"));
        smClaimSummaryVO.setTotalUnPay2(new BigDecimal("0.00"));
        smClaimSummaryVO.setTotalUnPay3(new BigDecimal("0.00"));
        smClaimSummaryVO.setRiskRation(new BigDecimal("0.00"));
        smClaimSummaryVO.setLossPayRation(new BigDecimal("0.00"));
        smClaimSummaryVO.setActualPayRation(new BigDecimal("0.00"));
        smClaimSummaryVO.setRiskCount(0);
        final SmyPageInfo<SmClaimProductSummaryVO, SmClaimSummaryVO> expectedResult = new SmyPageInfo<>(Arrays.asList(smClaimProductSummaryVO), smClaimSummaryVO);

        // Run the test
        final SmyPageInfo<SmClaimProductSummaryVO, SmClaimSummaryVO> result = smClaimServiceImplUnderTest.getClaimSummaryByProduct(query);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetClaimSummaryByRegion() {
        // Setup
        final SmClaimSmyQuery query = new SmClaimSmyQuery();
        query.setPage(0);
        query.setSize(0);
        query.setAll(false);
        query.setQueryPage(false);
        query.setStartDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setEndDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setStartDateR(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setEndDateR(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setStartDateK(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setEndDateK(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setQueryType(0);

        final SmClaimRegionSummaryVO smClaimRegionSummaryVO = new SmClaimRegionSummaryVO();
        smClaimRegionSummaryVO.setRegionName("regionName");
        smClaimRegionSummaryVO.setCaseCount(0);
        smClaimRegionSummaryVO.setRiskCycleTime(new BigDecimal("0.00"));
        smClaimRegionSummaryVO.setTotalPay(new BigDecimal("0.00"));
        smClaimRegionSummaryVO.setUnPayCount(0);
        smClaimRegionSummaryVO.setUnPayCount1(0);
        smClaimRegionSummaryVO.setUnPayCount2(0);
        smClaimRegionSummaryVO.setUnPayCount3(0);
        smClaimRegionSummaryVO.setTotalUnPay(new BigDecimal("0.00"));
        smClaimRegionSummaryVO.setTotalUnPay1(new BigDecimal("0.00"));
        smClaimRegionSummaryVO.setTotalUnPay2(new BigDecimal("0.00"));
        smClaimRegionSummaryVO.setTotalUnPay3(new BigDecimal("0.00"));
        smClaimRegionSummaryVO.setRiskRation(new BigDecimal("0.00"));
        smClaimRegionSummaryVO.setLossPayRation(new BigDecimal("0.00"));
        smClaimRegionSummaryVO.setActualPayRation(new BigDecimal("0.00"));
        smClaimRegionSummaryVO.setRiskCount(0);
        final SmClaimSummaryVO smClaimSummaryVO = new SmClaimSummaryVO();
        smClaimSummaryVO.setProductName("合计");
        smClaimSummaryVO.setRegionName("合计");
        smClaimSummaryVO.setOrgName("合计");
        smClaimSummaryVO.setCaseCount(0);
        smClaimSummaryVO.setRiskCycleTime(new BigDecimal("0.00"));
        smClaimSummaryVO.setTotalPay(new BigDecimal("0.00"));
        smClaimSummaryVO.setUnPayCount(0);
        smClaimSummaryVO.setUnPayCount1(0);
        smClaimSummaryVO.setUnPayCount2(0);
        smClaimSummaryVO.setUnPayCount3(0);
        smClaimSummaryVO.setTotalUnPay(new BigDecimal("0.00"));
        smClaimSummaryVO.setTotalUnPay1(new BigDecimal("0.00"));
        smClaimSummaryVO.setTotalUnPay2(new BigDecimal("0.00"));
        smClaimSummaryVO.setTotalUnPay3(new BigDecimal("0.00"));
        smClaimSummaryVO.setRiskRation(new BigDecimal("0.00"));
        smClaimSummaryVO.setLossPayRation(new BigDecimal("0.00"));
        smClaimSummaryVO.setActualPayRation(new BigDecimal("0.00"));
        smClaimSummaryVO.setRiskCount(0);
        final SmyPageInfo<SmClaimRegionSummaryVO, SmClaimSummaryVO> expectedResult = new SmyPageInfo<>(Arrays.asList(smClaimRegionSummaryVO), smClaimSummaryVO);

        // Run the test
        final SmyPageInfo<SmClaimRegionSummaryVO, SmClaimSummaryVO> result = smClaimServiceImplUnderTest.getClaimSummaryByRegion(query);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetClaimSummaryByOrg() {
        // Setup
        final SmClaimSmyQuery query = new SmClaimSmyQuery();
        query.setPage(0);
        query.setSize(0);
        query.setAll(false);
        query.setQueryPage(false);
        query.setStartDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setEndDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setStartDateR(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setEndDateR(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setStartDateK(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setEndDateK(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setQueryType(0);

        final SmClaimOrgSummaryVO smClaimOrgSummaryVO = new SmClaimOrgSummaryVO();
        smClaimOrgSummaryVO.setOrgName("orgName");
        smClaimOrgSummaryVO.setRegionName("regionName");
        smClaimOrgSummaryVO.setCaseCount(0);
        smClaimOrgSummaryVO.setRiskCycleTime(new BigDecimal("0.00"));
        smClaimOrgSummaryVO.setTotalPay(new BigDecimal("0.00"));
        smClaimOrgSummaryVO.setUnPayCount(0);
        smClaimOrgSummaryVO.setUnPayCount1(0);
        smClaimOrgSummaryVO.setUnPayCount2(0);
        smClaimOrgSummaryVO.setUnPayCount3(0);
        smClaimOrgSummaryVO.setTotalUnPay(new BigDecimal("0.00"));
        smClaimOrgSummaryVO.setTotalUnPay1(new BigDecimal("0.00"));
        smClaimOrgSummaryVO.setTotalUnPay2(new BigDecimal("0.00"));
        smClaimOrgSummaryVO.setTotalUnPay3(new BigDecimal("0.00"));
        smClaimOrgSummaryVO.setRiskRation(new BigDecimal("0.00"));
        smClaimOrgSummaryVO.setLossPayRation(new BigDecimal("0.00"));
        smClaimOrgSummaryVO.setActualPayRation(new BigDecimal("0.00"));
        smClaimOrgSummaryVO.setRiskCount(0);
        final SmClaimSummaryVO smClaimSummaryVO = new SmClaimSummaryVO();
        smClaimSummaryVO.setProductName("合计");
        smClaimSummaryVO.setRegionName("合计");
        smClaimSummaryVO.setOrgName("合计");
        smClaimSummaryVO.setCaseCount(0);
        smClaimSummaryVO.setRiskCycleTime(new BigDecimal("0.00"));
        smClaimSummaryVO.setTotalPay(new BigDecimal("0.00"));
        smClaimSummaryVO.setUnPayCount(0);
        smClaimSummaryVO.setUnPayCount1(0);
        smClaimSummaryVO.setUnPayCount2(0);
        smClaimSummaryVO.setUnPayCount3(0);
        smClaimSummaryVO.setTotalUnPay(new BigDecimal("0.00"));
        smClaimSummaryVO.setTotalUnPay1(new BigDecimal("0.00"));
        smClaimSummaryVO.setTotalUnPay2(new BigDecimal("0.00"));
        smClaimSummaryVO.setTotalUnPay3(new BigDecimal("0.00"));
        smClaimSummaryVO.setRiskRation(new BigDecimal("0.00"));
        smClaimSummaryVO.setLossPayRation(new BigDecimal("0.00"));
        smClaimSummaryVO.setActualPayRation(new BigDecimal("0.00"));
        smClaimSummaryVO.setRiskCount(0);
        final SmyPageInfo<SmClaimOrgSummaryVO, SmClaimSummaryVO> expectedResult = new SmyPageInfo<>(Arrays.asList(smClaimOrgSummaryVO), smClaimSummaryVO);

        // Run the test
        final SmyPageInfo<SmClaimOrgSummaryVO, SmClaimSummaryVO> result = smClaimServiceImplUnderTest.getClaimSummaryByOrg(query);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testDownloadSmClaimRegionSummary() {
        // Setup
        final SmClaimSmyQuery query = new SmClaimSmyQuery();
        query.setPage(0);
        query.setSize(0);
        query.setAll(false);
        query.setQueryPage(false);
        query.setStartDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setEndDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setStartDateR(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setEndDateR(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setStartDateK(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setEndDateK(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setQueryType(0);

        final MockHttpServletResponse resp = new MockHttpServletResponse();

        // Run the test
        smClaimServiceImplUnderTest.downloadSmClaimRegionSummary(query, resp);

        // Verify the results
    }

    @Test
    public void testDownloadSmClaimOrgSummary() {
        // Setup
        final SmClaimSmyQuery query = new SmClaimSmyQuery();
        query.setPage(0);
        query.setSize(0);
        query.setAll(false);
        query.setQueryPage(false);
        query.setStartDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setEndDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setStartDateR(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setEndDateR(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setStartDateK(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setEndDateK(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setQueryType(0);

        final MockHttpServletResponse resp = new MockHttpServletResponse();

        // Run the test
        smClaimServiceImplUnderTest.downloadSmClaimOrgSummary(query, resp);

        // Verify the results
    }

    @Test
    public void testDownloadSmClaimProductSummary() {
        // Setup
        final SmClaimSmyQuery query = new SmClaimSmyQuery();
        query.setPage(0);
        query.setSize(0);
        query.setAll(false);
        query.setQueryPage(false);
        query.setStartDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setEndDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setStartDateR(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setEndDateR(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setStartDateK(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setEndDateK(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setQueryType(0);

        final MockHttpServletResponse resp = new MockHttpServletResponse();

        // Run the test
        smClaimServiceImplUnderTest.downloadSmClaimProductSummary(query, resp);

        // Verify the results
    }

    @Test
    public void testUpdateClaim() {
        // Setup
        final WxClaimDTO wxClaimDTO = new WxClaimDTO();
        wxClaimDTO.setId(0);
        wxClaimDTO.setInsuredId(0);
        wxClaimDTO.setRiskType("riskType");
        wxClaimDTO.setRiskTime("riskTime");
        wxClaimDTO.setProcessType("processType");

        // Run the test
        smClaimServiceImplUnderTest.updateClaim(wxClaimDTO);

        // Verify the results
    }

    @Test
    public void testGetClaimById() {
        // Setup
        final SmClaim expectedResult = new SmClaim();
        expectedResult.setId(0);
        expectedResult.setClaimState("sCode");
        expectedResult.setClaimResult("保司核赔");
        expectedResult.setFinishState("finishState");
        expectedResult.setChannel("channel");

        // Run the test
        final SmClaim result = smClaimServiceImplUnderTest.getClaimById(0);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetCompanyIdByClaimId() {
        // Setup
        // Run the test
        final Integer result = smClaimServiceImplUnderTest.getCompanyIdByClaimId(0);

        // Verify the results
        assertEquals(Integer.valueOf(0), result);
    }

    @Test
    public void testUpdateClaimRiskReason() {
        // Setup
        final WxClaimDTO dto = new WxClaimDTO();
        dto.setId(0);
        dto.setInsuredId(0);
        dto.setRiskType("riskType");
        dto.setRiskTime("riskTime");
        dto.setProcessType("processType");

        // Run the test
        smClaimServiceImplUnderTest.updateClaimRiskReason(dto);

        // Verify the results
    }

    @Test
    public void testUpdateFinishNotify() {
        // Setup
        // Run the test
        smClaimServiceImplUnderTest.updateFinishNotify(0, "fileUrl");

        // Verify the results
    }

    @Test
    public void testPageWxSettlement() {
        // Setup
        final SmSettlementClaimQuery query = new SmSettlementClaimQuery();
        query.setPage(0);
        query.setSize(0);
        query.setAll(false);
        query.setQueryPage(false);
        query.setFuzzyKey("fuzzyKey");

        // Run the test
        final PageInfo<SmClaimVO> result = smClaimServiceImplUnderTest.pageWxSettlement(query);

        // Verify the results
    }

    @Test
    public void testPageWxSettlementHandled() {
        // Setup
        final SmSettlementClaimQuery query = new SmSettlementClaimQuery();
        query.setPage(0);
        query.setSize(0);
        query.setAll(false);
        query.setQueryPage(false);
        query.setFuzzyKey("fuzzyKey");

        // Run the test
        final PageInfo<SmClaimGuideHandledVO> result = smClaimServiceImplUnderTest.pageWxSettlementHandled(query);

        // Verify the results
    }

    @Test
    public void testParseCaseClosedFile() {
        // Setup
        when(mockDLockTemplate.tryLock("lockKey", 3600L)).thenReturn(true);
        doAnswer(invocation -> {
            ((Runnable) invocation.getArguments()[0]).run();
            return null;
        }).when(mockTaskExecutor).execute(any(Runnable.class));

        // Configure SmOrderMapper.getOrderInsuredByInsId(...).
        final SmOrderInsuredVO smOrderInsuredVO = new SmOrderInsuredVO();
        smOrderInsuredVO.setIdNumber("idNumber");
        smOrderInsuredVO.setBirthday("birthday");
        smOrderInsuredVO.setInsuredId(0);
        smOrderInsuredVO.setBranchId("branchId");
        smOrderInsuredVO.setCompanyName("companyName");
        when(mockOrderMapper.getOrderInsuredByInsId(0)).thenReturn(smOrderInsuredVO);

        // Run the test
        final String result = smClaimServiceImplUnderTest.parseCaseClosedFile("url");

        // Verify the results
        assertEquals("result", result);
        verify(mockTaskExecutor).execute(any(Runnable.class));

        // Confirm SmClaimImportResultMapper.insert(...).
        final SmClaimImportResult record = new SmClaimImportResult();
        record.setErrorUrl("errorUrl");
        record.setSuccessNum(0);
        record.setErrorNum(0);
        record.setBatchId("batchId");
        record.setBusinessType("CLAIM_CASE_CLOSED_IMPORT");
        record.setCreateBy("createBy");
        record.setImportUrl("url");
        record.setFileName("fileName");
        verify(mockClaimImportResultMapper).insert(record);
        verify(mockDLockTemplate).unLock("lockKey");
    }

    @Test(expected = BizException.class)
    public void testParseCaseClosedFile_DLockTemplateTryLockReturnsFalse() {
        // Setup
        when(mockDLockTemplate.tryLock("lockKey", 3600L)).thenReturn(false);

        // Run the test
        smClaimServiceImplUnderTest.parseCaseClosedFile("url");
    }

    @Test(expected = TaskRejectedException.class)
    public void testParseCaseClosedFile_AsyncTaskExecutorThrowsTaskRejectedException() {
        // Setup
        when(mockDLockTemplate.tryLock("lockKey", 3600L)).thenReturn(true);
        doThrow(TaskRejectedException.class).when(mockTaskExecutor).execute(any(Runnable.class));

        // Run the test
        smClaimServiceImplUnderTest.parseCaseClosedFile("url");
    }

    @Test
    public void testImportCaseClosedInfo() {
        // Setup
        final CaseClosedSuccessImport caseClosedSuccessImport = new CaseClosedSuccessImport();
        caseClosedSuccessImport.setPolicyNo("policyNo");
        caseClosedSuccessImport.setInsuredName("insuredName");
        caseClosedSuccessImport.setCaseCloseDate("caseCloseDate");
        caseClosedSuccessImport.setPayedMoney("");
        caseClosedSuccessImport.setOtherMark("otherMark");
        caseClosedSuccessImport.setError("error");
        final List<CaseClosedSuccessImport> list = Arrays.asList(caseClosedSuccessImport);

        // Configure SmOrderMapper.getOrderInsuredByInsId(...).
        final SmOrderInsuredVO smOrderInsuredVO = new SmOrderInsuredVO();
        smOrderInsuredVO.setIdNumber("idNumber");
        smOrderInsuredVO.setBirthday("birthday");
        smOrderInsuredVO.setInsuredId(0);
        smOrderInsuredVO.setBranchId("branchId");
        smOrderInsuredVO.setCompanyName("companyName");
        when(mockOrderMapper.getOrderInsuredByInsId(0)).thenReturn(smOrderInsuredVO);

        // Run the test
        smClaimServiceImplUnderTest.importCaseClosedInfo(list, "batchId", "url", "createBy");

        // Verify the results
        // Confirm SmClaimImportResultMapper.insert(...).
        final SmClaimImportResult record = new SmClaimImportResult();
        record.setErrorUrl("errorUrl");
        record.setSuccessNum(0);
        record.setErrorNum(0);
        record.setBatchId("batchId");
        record.setBusinessType("CLAIM_CASE_CLOSED_IMPORT");
        record.setCreateBy("createBy");
        record.setImportUrl("url");
        record.setFileName("fileName");
        verify(mockClaimImportResultMapper).insert(record);
    }

    @Test
    public void testDetailByBatchId() {
        // Setup
        final SmClaimImportResult expectedResult = new SmClaimImportResult();
        expectedResult.setErrorUrl("errorUrl");
        expectedResult.setSuccessNum(0);
        expectedResult.setErrorNum(0);
        expectedResult.setBatchId("batchId");
        expectedResult.setBusinessType("CLAIM_CASE_CLOSED_IMPORT");
        expectedResult.setCreateBy("createBy");
        expectedResult.setImportUrl("url");
        expectedResult.setFileName("fileName");

        // Configure SmClaimImportResultMapper.selectByBatchCode(...).
        final SmClaimImportResult smClaimImportResult = new SmClaimImportResult();
        smClaimImportResult.setErrorUrl("errorUrl");
        smClaimImportResult.setSuccessNum(0);
        smClaimImportResult.setErrorNum(0);
        smClaimImportResult.setBatchId("batchId");
        smClaimImportResult.setBusinessType("CLAIM_CASE_CLOSED_IMPORT");
        smClaimImportResult.setCreateBy("createBy");
        smClaimImportResult.setImportUrl("url");
        smClaimImportResult.setFileName("fileName");
        when(mockClaimImportResultMapper.selectByBatchCode("batchId")).thenReturn(smClaimImportResult);

        // Run the test
        final SmClaimImportResult result = smClaimServiceImplUnderTest.detailByBatchId("batchId");

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testDoImport() {
        // Setup
        final CaseClosedSuccessImport caseClosedSuccessImport = new CaseClosedSuccessImport();
        caseClosedSuccessImport.setPolicyNo("policyNo");
        caseClosedSuccessImport.setInsuredName("insuredName");
        caseClosedSuccessImport.setCaseCloseDate("caseCloseDate");
        caseClosedSuccessImport.setPayedMoney("");
        caseClosedSuccessImport.setOtherMark("otherMark");
        caseClosedSuccessImport.setError("error");
        final List<CaseClosedSuccessImport> list = Arrays.asList(caseClosedSuccessImport);
        final CaseClosedValidateImportVo caseClosedValidateImportVo = new CaseClosedValidateImportVo();
        caseClosedValidateImportVo.setPolicyNo("policyNo");
        caseClosedValidateImportVo.setInsuredName("insuredName");
        caseClosedValidateImportVo.setProductAttrCode("productAttrCode");
        caseClosedValidateImportVo.setClaimId(0);
        caseClosedValidateImportVo.setClaimState("claimState");
        caseClosedValidateImportVo.setProcessType("processType");
        final List<CaseClosedValidateImportVo> contextParams = Arrays.asList(caseClosedValidateImportVo);

        // Configure SmOrderMapper.getOrderInsuredByInsId(...).
        final SmOrderInsuredVO smOrderInsuredVO = new SmOrderInsuredVO();
        smOrderInsuredVO.setIdNumber("idNumber");
        smOrderInsuredVO.setBirthday("birthday");
        smOrderInsuredVO.setInsuredId(0);
        smOrderInsuredVO.setBranchId("branchId");
        smOrderInsuredVO.setCompanyName("companyName");
        when(mockOrderMapper.getOrderInsuredByInsId(0)).thenReturn(smOrderInsuredVO);

        // Run the test
        smClaimServiceImplUnderTest.doImport(list, contextParams);

        // Verify the results
    }

    @Test
    public void testGetIntegerClaimProgressDTOMap() {
        // Setup
        final Map<Integer, ClaimProgressDTO> expectedResult = new HashMap<>();

        // Configure SmClaimHastenMapper.lastResult(...).
        final ClaimProgressDTO claimProgressDTO = new ClaimProgressDTO();
        claimProgressDTO.setId(0L);
        claimProgressDTO.setClaimId(0);
        claimProgressDTO.setSCode("sCode");
        claimProgressDTO.setSName("保司核赔");
        claimProgressDTO.setOCode("optionCode");
        claimProgressDTO.setOName("AI自动过审");
        claimProgressDTO.setOType("-1");
        claimProgressDTO.setOValue("");
        claimProgressDTO.setDataJson("dataJson");
        claimProgressDTO.setOTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        claimProgressDTO.setExpressTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        claimProgressDTO.setSettlement("settlement");
        claimProgressDTO.setCreateBy("createBy");
        final ClaimEmailForm claimEmailForm = new ClaimEmailForm();
        claimEmailForm.setTheme("theme");
        claimEmailForm.setContent("content");
        claimEmailForm.setAllEnclosure(0);
        claimEmailForm.setAutoSend(0);
        claimProgressDTO.setClaimEmailForm(claimEmailForm);
        claimProgressDTO.setEvaluationStatus(0);
        final List<ClaimProgressDTO> claimProgressDTOS = Arrays.asList(claimProgressDTO);
        when(mockHastenMapper.lastResult(Arrays.asList(0))).thenReturn(claimProgressDTOS);

        // Run the test
        final Map<Integer, ClaimProgressDTO> result = smClaimServiceImplUnderTest.getIntegerClaimProgressDTOMap(Arrays.asList(0));

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetIntegerClaimProgressDTOMap_SmClaimHastenMapperReturnsNoItems() {
        // Setup
        final Map<Integer, ClaimProgressDTO> expectedResult = new HashMap<>();
        when(mockHastenMapper.lastResult(Arrays.asList(0))).thenReturn(Collections.emptyList());

        // Run the test
        final Map<Integer, ClaimProgressDTO> result = smClaimServiceImplUnderTest.getIntegerClaimProgressDTOMap(Arrays.asList(0));

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testQueryUnClosedCaseByInsuredIdAndRiskType() {
        // Setup
        final SmClaim expectedResult = new SmClaim();
        expectedResult.setId(0);
        expectedResult.setClaimState("sCode");
        expectedResult.setClaimResult("保司核赔");
        expectedResult.setFinishState("finishState");
        expectedResult.setChannel("channel");

        // Run the test
        final SmClaim result = smClaimServiceImplUnderTest.queryUnClosedCaseByInsuredIdAndRiskType(0, "riskType");

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testAddLabel() {
        // Setup
        final SmClaimLabel label = new SmClaimLabel();
        label.setId(0);
        label.setEnabledFlag(0);
        label.setCreateBy("createBy");
        label.setUpdateBy("updateBy");
        label.setClaimId(0);
        label.setLabel("label");

        // Run the test
        final Integer result = smClaimServiceImplUnderTest.addLabel(label);

        // Verify the results
        assertEquals(Integer.valueOf(0), result);
    }

    @Test
    public void testFinishEvaluation() {
        // Setup
        // Run the test
        final Integer result = smClaimServiceImplUnderTest.finishEvaluation(0, "score");

        // Verify the results
        assertEquals(Integer.valueOf(0), result);
    }

    @Test
    public void testQueryEvaluationPage() {
        // Setup
        final SmClaimEvaluationQuery query = new SmClaimEvaluationQuery();
        query.setPage(0);
        query.setSize(0);
        query.setAll(false);
        query.setQueryPage(false);
        query.setEvaluationDateEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        when(mockDictionaryService.getMapByType("channel")).thenReturn(new HashMap<>());

        // Run the test
        final PageInfo<SmClaimEvaluationPageVo> result = smClaimServiceImplUnderTest.queryEvaluationPage(query);

        // Verify the results
    }

    @Test
    public void testQueryEvaluation() {
        // Setup
        final ClaimEvaluationVo expectedResult = new ClaimEvaluationVo();
        expectedResult.setClaimId(0);
        expectedResult.setEvaluationScore("evaluationScore");
        expectedResult.setEvaluationContent("evaluationContent");
        expectedResult.setEvaluationLabel(Arrays.asList("value"));
        expectedResult.setEvaluationSharePic(Arrays.asList("value"));
        final ClaimQuestionnaireVo claimQuestionnaireVo = new ClaimQuestionnaireVo();
        expectedResult.setQuestionnaireList(Arrays.asList(claimQuestionnaireVo));

        when(mockFileService.reGenerateUrl4Expired(Arrays.asList("value"))).thenReturn(Arrays.asList("value"));

        // Run the test
        final ClaimEvaluationVo result = smClaimServiceImplUnderTest.queryEvaluation(0);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testQueryEvaluation_SystemFileServiceReturnsNoItems() {
        // Setup
        final ClaimEvaluationVo expectedResult = new ClaimEvaluationVo();
        expectedResult.setClaimId(0);
        expectedResult.setEvaluationScore("evaluationScore");
        expectedResult.setEvaluationContent("evaluationContent");
        expectedResult.setEvaluationLabel(Arrays.asList("value"));
        expectedResult.setEvaluationSharePic(Arrays.asList("value"));
        final ClaimQuestionnaireVo claimQuestionnaireVo = new ClaimQuestionnaireVo();
        expectedResult.setQuestionnaireList(Arrays.asList(claimQuestionnaireVo));

        when(mockFileService.reGenerateUrl4Expired(Arrays.asList("value"))).thenReturn(Collections.emptyList());

        // Run the test
        final ClaimEvaluationVo result = smClaimServiceImplUnderTest.queryEvaluation(0);

        // Verify the results
        assertEquals(expectedResult, result);
    }
}
