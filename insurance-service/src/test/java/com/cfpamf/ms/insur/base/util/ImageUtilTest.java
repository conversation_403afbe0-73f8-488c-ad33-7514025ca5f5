package com.cfpamf.ms.insur.base.util;

import com.cfpamf.ms.insur.admin.pojo.dto.QrcodeDTO;
import org.junit.FixMethodOrder;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.MethodSorters;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

/*@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("dev")
@FixMethodOrder(MethodSorters.NAME_ASCENDING)*/
public class ImageUtilTest {

   /* @Test
    public void getQrcodeBase64() {
        QrcodeDTO dto = new QrcodeDTO();
        dto.setUrl("wwww.baidu.com");
        dto.setWidth(250);
        dto.setHeight(250);
        dto.setMargin(2);
        QrcodeUtil.getQrcodeBase64(dto, true);
        QrcodeUtil.getQrcodeBase64(dto, false);
    }*/
}