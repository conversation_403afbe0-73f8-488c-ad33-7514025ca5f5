package com.cfpamf.ms.insur.admin.external.cic.api;

import com.cfpamf.ms.insur.admin.external.cic.dto.InsureRet;
import com.cfpamf.ms.insur.admin.external.cic.util.XmlMapperUtil;

/**
 * <AUTHOR>
 **/
public class TestXml {

    public static void main(String[] args) {
        String xml = "<?xml version=\"1.0\" encoding=\"GBK\" ?>\n" +
                "<INSUREQRET>\n" +
                "\t<HEAD>\n" +
                "\t\t<TRANSRNO>CIC001</TRANSRNO>\n" +
                "\t\t<RESULTCODE>4002</RESULTCODE>\n" +
                "\t\t<ERRINFO>连接异常</ERRINFO>\n" +
                "\t</HEAD>\n" +
                "\t<MAIN>\n" +
                "\t</MAIN>\n" +
                "\n" +
                "</INSUREQRET>\n";

        InsureRet insureRet = XmlMapperUtil.xmlToBean(xml, InsureRet.class);
        System.out.println();
    }
}
