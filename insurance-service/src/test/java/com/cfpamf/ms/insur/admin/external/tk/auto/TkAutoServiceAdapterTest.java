package com.cfpamf.ms.insur.admin.external.tk.auto;

import org.junit.Before;
import org.junit.Test;

import static org.junit.Assert.*;

/**
 * <AUTHOR> 2021/5/6 11:49
 */
public class TkAutoServiceAdapterTest {

    @Before
    public void setUp() throws Exception {
    }

    @Test
    public void getAutoQueryString() {
    }

    @Test
    public void queryTkList() {
    }

    @Test
    public void queryTkDetail() {
    }

    @Test
    public void submitChannelOrder() {
    }

    @Test
    public void prePayChannelOrder() {
    }

    @Test
    public void queryChannelOrderInfo() {
    }

    @Test
    public void getOrderPayedCallbackResp() {
    }
}
