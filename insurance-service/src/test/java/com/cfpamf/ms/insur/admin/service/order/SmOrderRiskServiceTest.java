package com.cfpamf.ms.insur.admin.service.order;

import com.cfpamf.ms.insur.admin.service.BaseTest;
import com.cfpamf.ms.insur.base.util.HttpRequestUtil;
import com.github.jsonzou.jmockdata.JMockData;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

@RunWith(PowerMockRunner.class)
@PrepareForTest({HttpRequestUtil.class})
public class SmOrderRiskServiceTest extends BaseTest {
    @InjectMocks
    SmOrderRiskService smOrderRiskService;
    @org.mockito.Mock
    com.cfpamf.ms.insur.admin.dao.safes.order.SmOrderRiskDutyMapper smOrderRiskDutyMapper;
    @org.mockito.Mock
    com.cfpamf.ms.insur.admin.dao.safes.product.risk.SysRiskMapper sysRiskMapper;

    @Test
    public void listRiskPremiumByFhOrderId() {
        try {
            smOrderRiskService.listRiskPremiumByFhOrderId(",KYFFg,");
        } catch (Exception e) {

        }
    }

    @Test
    public void listByFhOrderId() {
        try {
            smOrderRiskService.listByFhOrderId(",bGKxG,");
        } catch (Exception e) {

        }
    }

    @Test
    public void listRiskByRiskIds() {
        try {
            smOrderRiskService.listRiskByRiskIds(JMockData.mock(java.util.List.class));
        } catch (Exception e) {

        }
    }

    @Test
    public void listTestFormByOrderId() {
        try {
            smOrderRiskService.listTestFormByOrderId(",BePmx,");
        } catch (Exception e) {

        }
    }
}
