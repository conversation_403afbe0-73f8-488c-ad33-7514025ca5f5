package com.cfpamf.ms.insur.admin.service;

import com.cfpamf.ms.insur.admin.claim.form.ZaClaimQuery;
import com.cfpamf.ms.insur.admin.claim.vo.ZaBankInfoVO;
import com.cfpamf.ms.insur.admin.dao.safes.AuthUserMapper;
import com.cfpamf.ms.insur.admin.dao.safes.SmClaimMapper;
import com.cfpamf.ms.insur.admin.dao.safes.SmOrderMapper;
import com.cfpamf.ms.insur.admin.dao.safes.claim.SmClaimBankZaMapper;
import com.cfpamf.ms.insur.admin.dao.safes.claim.SmClaimCompanyStatusZaMapper;
import com.cfpamf.ms.insur.admin.dao.safes.claim.SmClaimRegionZaMapper;
import com.cfpamf.ms.insur.admin.dao.safes.claim.SmClaimReimbursementZaMapper;
import com.cfpamf.ms.insur.admin.dao.safes.order.SmOrderInsuredMapper;
import com.cfpamf.ms.insur.admin.external.zhongan.ZaApiProperties;
import com.cfpamf.ms.insur.admin.external.zhongan.api.ZaApiService;
import com.cfpamf.ms.insur.admin.external.zhongan.model.claim.*;
import com.cfpamf.ms.insur.admin.pojo.dto.ProgressDTO;
import com.cfpamf.ms.insur.admin.pojo.dto.SmClaimFileCombDTO;
import com.cfpamf.ms.insur.admin.pojo.dto.claim.NewFlagFileForm;
import com.cfpamf.ms.insur.admin.pojo.form.claim.ClaimEmailForm;
import com.cfpamf.ms.insur.admin.pojo.po.SmOrderInsured;
import com.cfpamf.ms.insur.admin.pojo.po.claim.SmClaim;
import com.cfpamf.ms.insur.admin.pojo.po.claim.SmClaimCompanyStatusZa;
import com.cfpamf.ms.insur.admin.pojo.po.claim.SmClaimReimbursementZa;
import com.cfpamf.ms.insur.admin.pojo.vo.*;
import com.cfpamf.ms.insur.admin.pojo.vo.claim.ClaimFileSimpleVo;
import com.cfpamf.ms.insur.admin.service.claim.ZaClaimStatusUpdateService;
import com.cfpamf.ms.insur.base.service.BaseWorkflow;
import com.cfpamf.ms.insur.base.service.DLockTemplate;
import com.cfpamf.ms.insur.base.util.HttpRequestUtil;
import com.cfpamf.ms.insur.base.util.RedisUtil;
import com.cfpamf.ms.insur.weixin.pojo.dto.WxClaimDTO;
import com.cfpamf.ms.insur.weixin.pojo.dto.WxClaimFileCombDTO;
import com.cfpamf.ms.insur.weixin.pojo.dto.claim.za.WxZaClaimDTO;
import com.cfpamf.ms.insur.weixin.pojo.vo.WxClaimDetailVO;
import com.cfpamf.ms.insur.weixin.pojo.vo.WxSessionVO;
import com.cfpamf.ms.insur.weixin.pojo.vo.WxUserVO;
import com.cfpamf.ms.insur.weixin.pojo.vo.claim.za.WxZaClaimDetailVO;
import com.fasterxml.jackson.core.JsonParseException;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.zhongan.filegateway.common.FileUploadResponse;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.transaction.TransactionException;
import org.springframework.transaction.support.TransactionCallback;
import org.springframework.transaction.support.TransactionTemplate;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(PowerMockRunner.class)
@PrepareForTest({HttpRequestUtil.class})
public class ZaClaimServiceTest extends BaseTest {

    @Mock
    private ClaimWorkflow mockWorkflow;
    @Mock
    private SmClaimMapper mockClaimMapper;
    @Mock
    private SmOrderMapper mockOrderMapper;
    @Mock
    private SmClaimReimbursementZaMapper mockClaimReimbursementZaMapper;
    @Mock
    private SmOrderInsuredMapper mockInsuredMapper;
    @Mock
    private UserService mockUserService;
    @Mock
    private ZaApiService mockZaApiService;
    @Mock
    private SmClaimBankZaMapper mockClaimBankZaMapper;
    @Mock
    private SmClaimRegionZaMapper mockRegionZaMapper;
    @Mock
    private AuthUserMapper mockAuthUserMapper;
    @Mock
    private ZaApiProperties mockZaApiProperties;
    @Mock
    private SmClaimCompanyStatusZaMapper mockClaimCompanyStatusZaMapper;
    @Mock
    private DLockTemplate mockLockTemplate;
    @Mock
    private ObjectMapper mockJsonMapper;
    @Mock
    private TransactionTemplate mockTransactionTemplate;
    @Mock
    private BaseCommonClaimService commonClaimService;
    @Mock
    private SmClaimServiceImpl mockClaimService;
    @Mock
    private RedisUtil<String, Integer> redisUtil;

    @Mock
    private ZaApiService zaApiService;

    @InjectMocks
    private ZaClaimServiceImpl zaClaimServiceUnderTest;

    @Test
    public void testConstructReportStatusProgressMap() {
        // Setup
        final List<ZaClaimStatusUpdateService> zaClaimStatusUpdateServices = Arrays.asList();

        // Run the test
        zaClaimServiceUnderTest.constructReportStatusProgressMap(zaClaimStatusUpdateServices);

        // Verify the results
    }

    @Test
    public void testDetail() {
        // Setup
        final WxZaClaimDetailVO expectedResult = new WxZaClaimDetailVO();
        final WxClaimDetailVO claimDetailVO = new WxClaimDetailVO();
        claimDetailVO.setId(0);
        claimDetailVO.setInsuredId(0);
        claimDetailVO.setClaimNo("claimNo");
        claimDetailVO.setRiskTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        claimDetailVO.setRiskDesc("accidentDesc");
        claimDetailVO.setReportTime("reportDate");
        claimDetailVO.setClaimState("claimState");
        claimDetailVO.setClaimResult("claimResult");
        claimDetailVO.setSettlement("settlement");
        final SmClaimProgressVO progress = new SmClaimProgressVO();
        final ProgressVO progressVO = new ProgressVO();
        progressVO.setProgressId(0);
        progressVO.setSCode("sCode");
        progressVO.setDataJson("dataJson");
        progressVO.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        progress.setProgressList(Arrays.asList(progressVO));
        final ClaimWorkflow.Step nextProgress = new BaseWorkflow.Step();
        nextProgress.setSCode("claimState");
        nextProgress.setSName("claimResult");
        final BaseWorkflow.Option option = new BaseWorkflow.Option();
        option.setOCode("oCode");
        option.setOName("oName");
        option.setOType("oType");
        option.setOValue("oValue");
        nextProgress.setOptions(Arrays.asList(option));
        progress.setNextProgress(nextProgress);
        final ProgressStepVO progressStepVO = new ProgressStepVO();
        progress.setAllSteps(Arrays.asList(progressStepVO));
        claimDetailVO.setProgress(progress);
        claimDetailVO.setEstimatedAmount(new BigDecimal("0.00"));
        claimDetailVO.setSafesCenterApprovalTimes(0);
        expectedResult.setClaimDetailVO(claimDetailVO);
        final SmClaimFileCombVO smClaimFileCombVO = new SmClaimFileCombVO();
        smClaimFileCombVO.setFileTypeCode("fileTypeCode");
        smClaimFileCombVO.setFileTypeName("name");
        smClaimFileCombVO.setFileRequire(false);
        smClaimFileCombVO.setFileUrls(Arrays.asList("value"));
        final ClaimFileSimpleVo claimFileSimpleVo = new ClaimFileSimpleVo();
        claimFileSimpleVo.setCfId(0);
        claimFileSimpleVo.setFileUrl("fileUrl");
        claimFileSimpleVo.setNewFlag(0);
        claimFileSimpleVo.setFileTypeCode("attachmentType");
        smClaimFileCombVO.setFileSimpleVoList(Arrays.asList(claimFileSimpleVo));
        smClaimFileCombVO.setExampleFileUrlList(Arrays.asList("value"));
        smClaimFileCombVO.setExampleFileUrlDescription("exampleFileUrlDescription");
        expectedResult.setZaClaimFiles(Arrays.asList(smClaimFileCombVO));

        // Configure SmClaimMapper.getSmClaimById(...).
        final WxClaimDetailVO wxClaimDetailVO = new WxClaimDetailVO();
        wxClaimDetailVO.setId(0);
        wxClaimDetailVO.setInsuredId(0);
        wxClaimDetailVO.setClaimNo("claimNo");
        wxClaimDetailVO.setRiskTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        wxClaimDetailVO.setRiskDesc("accidentDesc");
        wxClaimDetailVO.setReportTime("reportDate");
        wxClaimDetailVO.setClaimState("claimState");
        wxClaimDetailVO.setClaimResult("claimResult");
        wxClaimDetailVO.setSettlement("settlement");
        final SmClaimProgressVO progress1 = new SmClaimProgressVO();
        final ProgressVO progressVO1 = new ProgressVO();
        progressVO1.setProgressId(0);
        progressVO1.setSCode("sCode");
        progressVO1.setDataJson("dataJson");
        progressVO1.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        progress1.setProgressList(Arrays.asList(progressVO1));
        final ClaimWorkflow.Step nextProgress1 = new BaseWorkflow.Step();
        nextProgress1.setSCode("claimState");
        nextProgress1.setSName("claimResult");
        final BaseWorkflow.Option option1 = new BaseWorkflow.Option();
        option1.setOCode("oCode");
        option1.setOName("oName");
        option1.setOType("oType");
        option1.setOValue("oValue");
        nextProgress1.setOptions(Arrays.asList(option1));
        progress1.setNextProgress(nextProgress1);
        final ProgressStepVO progressStepVO1 = new ProgressStepVO();
        progress1.setAllSteps(Arrays.asList(progressStepVO1));
        wxClaimDetailVO.setProgress(progress1);
        wxClaimDetailVO.setEstimatedAmount(new BigDecimal("0.00"));
        wxClaimDetailVO.setSafesCenterApprovalTimes(0);
        when(mockClaimMapper.getSmClaimById(0)).thenReturn(wxClaimDetailVO);

        // Configure SmClaimReimbursementZaMapper.selectByClaimId(...).
        final SmClaimReimbursementZa reimbursementZa = new SmClaimReimbursementZa();
        reimbursementZa.setId(0);
        reimbursementZa.setClaimId(0);
        reimbursementZa.setAccidentType("accidentType");
        reimbursementZa.setReimbursementType("accidentType");
        reimbursementZa.setVisitingDate(LocalDate.of(2020, 1, 1));
        reimbursementZa.setVistingHospital("vistingHospital");
        reimbursementZa.setVistingHospitalName("vistingHospitalName");
        reimbursementZa.setRelationWithCasualty("insurantType");
        reimbursementZa.setApplierName("applierName");
        reimbursementZa.setApplierPhone("applierPhone");
        reimbursementZa.setApplierCertificateType("applierCertificateType");
        reimbursementZa.setApplierCertificateCode("applierCertificateCode");
        reimbursementZa.setApplierEmail("applierEmail");
        reimbursementZa.setPayeeWithCasualty("payeeRelation");
        reimbursementZa.setPayeeWay("payeeWay");
        reimbursementZa.setPayeeName("bankOpenName");
        reimbursementZa.setBankCard("bankAccountNo");
        reimbursementZa.setDepositBankCode("bankCode");
        reimbursementZa.setZfbAccount("zfbAccount");
        reimbursementZa.setDepositBankName("depositBankName");
        reimbursementZa.setPayeeBankDistrictCode("bankAreaCode");
        reimbursementZa.setPayeeBankDistrictName("bankAreaName");
        reimbursementZa.setPayeeBankBranchCode("bankBranchNameCode");
        reimbursementZa.setPayeeBankBranchName("bankBranchName");
        reimbursementZa.setCreateBy("createBy");
        reimbursementZa.setZaReportNo("reportNo");
        when(mockClaimReimbursementZaMapper.selectByClaimId(0)).thenReturn(reimbursementZa);

        // Configure SmClaimMapper.listSmClaimFileUnitByClaimId(...).
        final SmClaimFileUnitVO smClaimFileUnitVO = new SmClaimFileUnitVO();
        smClaimFileUnitVO.setCfId(0);
        smClaimFileUnitVO.setClaimId(0);
        smClaimFileUnitVO.setFileTypeCode("attachmentType");
        smClaimFileUnitVO.setFileTypeName("attachmentName");
        smClaimFileUnitVO.setFileUrl("fileUrl");
        smClaimFileUnitVO.setNewFlag(0);
        smClaimFileUnitVO.setZaKey("attachmentKey");
        final List<SmClaimFileUnitVO> smClaimFileUnitVOS = Arrays.asList(smClaimFileUnitVO);
        when(mockClaimMapper.listSmClaimFileUnitByClaimId(0)).thenReturn(smClaimFileUnitVOS);

        // Configure SmClaimMapper.getByClaimId(...).
        final SmClaim claim = new SmClaim();
        claim.setId(0);
        claim.setClaimNo("claimNo");
        claim.setInsuredId(0);
        claim.setRiskType("riskType");
        claim.setPayMoney(new BigDecimal("0.00"));
        claim.setRiskTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        claim.setRiskDesc("riskDesc");
        claim.setDatatTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        claim.setClaimState("claimState");
        claim.setClaimResult("claimResult");
        claim.setFinishState("finishState");
        claim.setFinishResult("finishResult");
        claim.setFinishtime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        claim.setNote("note");
        claim.setEstimatedAmount(new BigDecimal("0.00"));
        when(mockClaimMapper.getByClaimId(0)).thenReturn(claim);

        // Configure SmOrderInsuredMapper.selectByPrimaryKeyMustExists(...).
        final SmOrderInsured smOrderInsured = new SmOrderInsured();
        smOrderInsured.setId(0);
        smOrderInsured.setFhOrderId("fhOrderId");
        smOrderInsured.setRelationship("relationship");
        smOrderInsured.setPersonName("insuredName");
        smOrderInsured.setPersonGender("personGender");
        smOrderInsured.setIdType("certType");
        smOrderInsured.setIdNumber("certNo");
        smOrderInsured.setIdPeriodStart("idPeriodStart");
        smOrderInsured.setIdPeriodEnd("idPeriodEnd");
        smOrderInsured.setBirthday("birthday");
        smOrderInsured.setCellPhone("cellPhone");
        smOrderInsured.setEmail("email");
        smOrderInsured.setAnnualIncome("annualIncome");
        smOrderInsured.setFlightNo("flightNo");
        smOrderInsured.setPolicyNo("individualPolicyNo");
        when(mockInsuredMapper.selectByPrimaryKeyMustExists(0)).thenReturn(smOrderInsured);

        // Run the test
        final WxZaClaimDetailVO result = zaClaimServiceUnderTest.detail(0);

    }

    @Test
    public void testSaveZaClaimData() {
        // Setup
        final WxZaClaimDTO zaClaimDTO = new WxZaClaimDTO();
        final WxClaimDTO wxClaimDTO = new WxClaimDTO();
        wxClaimDTO.setId(0);
        wxClaimDTO.setInsuredId(0);
        wxClaimDTO.setRiskType("riskType");
        wxClaimDTO.setRiskTime("riskTime");
        wxClaimDTO.setCreateBy("createBy");
        wxClaimDTO.setClaimState("claimState");
        wxClaimDTO.setClaimResult("claimResult");
        wxClaimDTO.setClaimNo("claimNo");
        zaClaimDTO.setWxClaimDTO(wxClaimDTO);
        zaClaimDTO.setAccidentType("accidentType");
        zaClaimDTO.setReimbursementType("accidentType");
        zaClaimDTO.setVisitingDate(LocalDate.of(2020, 1, 1));
        zaClaimDTO.setVistingHospital("vistingHospital");
        zaClaimDTO.setVistingHospitalName("vistingHospitalName");
        zaClaimDTO.setRelationWithCasualty("insurantType");
        zaClaimDTO.setApplierName("applierName");
        zaClaimDTO.setApplierPhone("applierPhone");
        zaClaimDTO.setApplierCertificateType("applierCertificateType");
        zaClaimDTO.setApplierCertificateCode("applierCertificateCode");
        zaClaimDTO.setApplierEmail("applierEmail");
        zaClaimDTO.setPayeeWithCasualty("payeeRelation");
        zaClaimDTO.setPayeeWay("payeeWay");
        zaClaimDTO.setPayeeName("bankOpenName");
        zaClaimDTO.setBankCard("bankAccountNo");
        zaClaimDTO.setDepositBankCode("bankCode");
        zaClaimDTO.setZfbAccount("zfbAccount");
        zaClaimDTO.setDepositBankName("depositBankName");
        zaClaimDTO.setPayeeBankDistrictCode("bankAreaCode");
        zaClaimDTO.setPayeeBankDistrictName("bankAreaName");
        zaClaimDTO.setPayeeBankBranchCode("bankBranchNameCode");
        zaClaimDTO.setPayeeBankBranchName("bankBranchName");

        // Configure SmClaimReimbursementZaMapper.selectByClaimId(...).
        final SmClaimReimbursementZa reimbursementZa = new SmClaimReimbursementZa();
        reimbursementZa.setId(0);
        reimbursementZa.setClaimId(0);
        reimbursementZa.setAccidentType("accidentType");
        reimbursementZa.setReimbursementType("accidentType");
        reimbursementZa.setVisitingDate(LocalDate.of(2020, 1, 1));
        reimbursementZa.setVistingHospital("vistingHospital");
        reimbursementZa.setVistingHospitalName("vistingHospitalName");
        reimbursementZa.setRelationWithCasualty("insurantType");
        reimbursementZa.setApplierName("applierName");
        reimbursementZa.setApplierPhone("applierPhone");
        reimbursementZa.setApplierCertificateType("applierCertificateType");
        reimbursementZa.setApplierCertificateCode("applierCertificateCode");
        reimbursementZa.setApplierEmail("applierEmail");
        reimbursementZa.setPayeeWithCasualty("payeeRelation");
        reimbursementZa.setPayeeWay("payeeWay");
        reimbursementZa.setPayeeName("bankOpenName");
        reimbursementZa.setBankCard("bankAccountNo");
        reimbursementZa.setDepositBankCode("bankCode");
        reimbursementZa.setZfbAccount("zfbAccount");
        reimbursementZa.setDepositBankName("depositBankName");
        reimbursementZa.setPayeeBankDistrictCode("bankAreaCode");
        reimbursementZa.setPayeeBankDistrictName("bankAreaName");
        reimbursementZa.setPayeeBankBranchCode("bankBranchNameCode");
        reimbursementZa.setPayeeBankBranchName("bankBranchName");
        reimbursementZa.setCreateBy("createBy");
        reimbursementZa.setZaReportNo("reportNo");
        when(mockClaimReimbursementZaMapper.selectByClaimId(0)).thenReturn(reimbursementZa);

        when(mockClaimReimbursementZaMapper.insert(new SmClaimReimbursementZa())).thenReturn(0);
        when(mockClaimReimbursementZaMapper.updateByPrimaryKeySelective(new SmClaimReimbursementZa())).thenReturn(0);

        // Run the test
        try {
            zaClaimServiceUnderTest.saveZaClaimData(zaClaimDTO);
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    @Test
    public void testGetCombineFile() {
        // Setup
        final SmClaimFileCombVO smClaimFileCombVO = new SmClaimFileCombVO();
        smClaimFileCombVO.setClaimId(0);
        smClaimFileCombVO.setFileTypeCode("fileTypeCode");
        smClaimFileCombVO.setFileTypeName("name");
        smClaimFileCombVO.setFileRequire(false);
        smClaimFileCombVO.setFileUrls(Arrays.asList("value"));
        final ClaimFileSimpleVo claimFileSimpleVo = new ClaimFileSimpleVo();
        claimFileSimpleVo.setCfId(0);
        claimFileSimpleVo.setFileUrl("fileUrl");
        claimFileSimpleVo.setNewFlag(0);
        claimFileSimpleVo.setFileTypeCode("attachmentType");
        smClaimFileCombVO.setFileSimpleVoList(Arrays.asList(claimFileSimpleVo));
        smClaimFileCombVO.setExampleFileUrlList(Arrays.asList("value"));
        smClaimFileCombVO.setExampleFileUrlDescription("exampleFileUrlDescription");
        final List<SmClaimFileCombVO> expectedResult = Arrays.asList(smClaimFileCombVO);

        // Configure SmClaimMapper.listSmClaimFileUnitByClaimId(...).
        final SmClaimFileUnitVO smClaimFileUnitVO = new SmClaimFileUnitVO();
        smClaimFileUnitVO.setCfId(0);
        smClaimFileUnitVO.setClaimId(0);
        smClaimFileUnitVO.setFileTypeCode("attachmentType");
        smClaimFileUnitVO.setFileTypeName("attachmentName");
        smClaimFileUnitVO.setFileUrl("fileUrl");
        smClaimFileUnitVO.setNewFlag(0);
        smClaimFileUnitVO.setZaKey("attachmentKey");
        final List<SmClaimFileUnitVO> smClaimFileUnitVOS = Arrays.asList(smClaimFileUnitVO);
        when(mockClaimMapper.listSmClaimFileUnitByClaimId(0)).thenReturn(smClaimFileUnitVOS);

        // Configure SmClaimMapper.getByClaimId(...).
        final SmClaim claim = new SmClaim();
        claim.setId(0);
        claim.setClaimNo("claimNo");
        claim.setInsuredId(0);
        claim.setRiskType("riskType");
        claim.setPayMoney(new BigDecimal("0.00"));
        claim.setRiskTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        claim.setRiskDesc("riskDesc");
        claim.setDatatTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        claim.setClaimState("claimState");
        claim.setClaimResult("claimResult");
        claim.setFinishState("finishState");
        claim.setFinishResult("finishResult");
        claim.setFinishtime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        claim.setNote("note");
        claim.setEstimatedAmount(new BigDecimal("0.00"));
        when(mockClaimMapper.getByClaimId(0)).thenReturn(claim);

        // Configure SmOrderInsuredMapper.selectByPrimaryKeyMustExists(...).
        final SmOrderInsured smOrderInsured = new SmOrderInsured();
        smOrderInsured.setId(0);
        smOrderInsured.setFhOrderId("fhOrderId");
        smOrderInsured.setRelationship("relationship");
        smOrderInsured.setPersonName("insuredName");
        smOrderInsured.setPersonGender("personGender");
        smOrderInsured.setIdType("certType");
        smOrderInsured.setIdNumber("certNo");
        smOrderInsured.setIdPeriodStart("idPeriodStart");
        smOrderInsured.setIdPeriodEnd("idPeriodEnd");
        smOrderInsured.setBirthday("birthday");
        smOrderInsured.setCellPhone("cellPhone");
        smOrderInsured.setEmail("email");
        smOrderInsured.setAnnualIncome("annualIncome");
        smOrderInsured.setFlightNo("flightNo");
        smOrderInsured.setPolicyNo("individualPolicyNo");
        when(mockInsuredMapper.selectByPrimaryKeyMustExists(0)).thenReturn(smOrderInsured);

        // Configure SmClaimReimbursementZaMapper.selectByClaimId(...).
        final SmClaimReimbursementZa reimbursementZa = new SmClaimReimbursementZa();
        reimbursementZa.setId(0);
        reimbursementZa.setClaimId(0);
        reimbursementZa.setAccidentType("accidentType");
        reimbursementZa.setReimbursementType("accidentType");
        reimbursementZa.setVisitingDate(LocalDate.of(2020, 1, 1));
        reimbursementZa.setVistingHospital("vistingHospital");
        reimbursementZa.setVistingHospitalName("vistingHospitalName");
        reimbursementZa.setRelationWithCasualty("insurantType");
        reimbursementZa.setApplierName("applierName");
        reimbursementZa.setApplierPhone("applierPhone");
        reimbursementZa.setApplierCertificateType("applierCertificateType");
        reimbursementZa.setApplierCertificateCode("applierCertificateCode");
        reimbursementZa.setApplierEmail("applierEmail");
        reimbursementZa.setPayeeWithCasualty("payeeRelation");
        reimbursementZa.setPayeeWay("payeeWay");
        reimbursementZa.setPayeeName("bankOpenName");
        reimbursementZa.setBankCard("bankAccountNo");
        reimbursementZa.setDepositBankCode("bankCode");
        reimbursementZa.setZfbAccount("zfbAccount");
        reimbursementZa.setDepositBankName("depositBankName");
        reimbursementZa.setPayeeBankDistrictCode("bankAreaCode");
        reimbursementZa.setPayeeBankDistrictName("bankAreaName");
        reimbursementZa.setPayeeBankBranchCode("bankBranchNameCode");
        reimbursementZa.setPayeeBankBranchName("bankBranchName");
        reimbursementZa.setCreateBy("createBy");
        reimbursementZa.setZaReportNo("reportNo");
        when(mockClaimReimbursementZaMapper.selectByClaimId(0)).thenReturn(reimbursementZa);

        // Run the test
        final List<SmClaimFileCombVO> result = zaClaimServiceUnderTest.getCombineFile(0);

    }

    @Test
    public void testGetCombineFile_SmClaimMapperListSmClaimFileUnitByClaimIdReturnsNoItems() {
        // Setup
        when(mockClaimMapper.listSmClaimFileUnitByClaimId(0)).thenReturn(Collections.emptyList());

        // Configure SmClaimMapper.getByClaimId(...).
        final SmClaim claim = new SmClaim();
        claim.setId(0);
        claim.setClaimNo("claimNo");
        claim.setInsuredId(0);
        claim.setRiskType("riskType");
        claim.setPayMoney(new BigDecimal("0.00"));
        claim.setRiskTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        claim.setRiskDesc("riskDesc");
        claim.setDatatTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        claim.setClaimState("claimState");
        claim.setClaimResult("claimResult");
        claim.setFinishState("finishState");
        claim.setFinishResult("finishResult");
        claim.setFinishtime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        claim.setNote("note");
        claim.setEstimatedAmount(new BigDecimal("0.00"));
        when(mockClaimMapper.getByClaimId(0)).thenReturn(claim);

        // Configure SmOrderInsuredMapper.selectByPrimaryKeyMustExists(...).
        final SmOrderInsured smOrderInsured = new SmOrderInsured();
        smOrderInsured.setId(0);
        smOrderInsured.setFhOrderId("fhOrderId");
        smOrderInsured.setRelationship("relationship");
        smOrderInsured.setPersonName("insuredName");
        smOrderInsured.setPersonGender("personGender");
        smOrderInsured.setIdType("certType");
        smOrderInsured.setIdNumber("certNo");
        smOrderInsured.setIdPeriodStart("idPeriodStart");
        smOrderInsured.setIdPeriodEnd("idPeriodEnd");
        smOrderInsured.setBirthday("birthday");
        smOrderInsured.setCellPhone("cellPhone");
        smOrderInsured.setEmail("email");
        smOrderInsured.setAnnualIncome("annualIncome");
        smOrderInsured.setFlightNo("flightNo");
        smOrderInsured.setPolicyNo("individualPolicyNo");
        when(mockInsuredMapper.selectByPrimaryKeyMustExists(0)).thenReturn(smOrderInsured);

        // Configure SmClaimReimbursementZaMapper.selectByClaimId(...).
        final SmClaimReimbursementZa reimbursementZa = new SmClaimReimbursementZa();
        reimbursementZa.setId(0);
        reimbursementZa.setClaimId(0);
        reimbursementZa.setAccidentType("accidentType");
        reimbursementZa.setReimbursementType("accidentType");
        reimbursementZa.setVisitingDate(LocalDate.of(2020, 1, 1));
        reimbursementZa.setVistingHospital("vistingHospital");
        reimbursementZa.setVistingHospitalName("vistingHospitalName");
        reimbursementZa.setRelationWithCasualty("insurantType");
        reimbursementZa.setApplierName("applierName");
        reimbursementZa.setApplierPhone("applierPhone");
        reimbursementZa.setApplierCertificateType("applierCertificateType");
        reimbursementZa.setApplierCertificateCode("applierCertificateCode");
        reimbursementZa.setApplierEmail("applierEmail");
        reimbursementZa.setPayeeWithCasualty("payeeRelation");
        reimbursementZa.setPayeeWay("payeeWay");
        reimbursementZa.setPayeeName("bankOpenName");
        reimbursementZa.setBankCard("bankAccountNo");
        reimbursementZa.setDepositBankCode("bankCode");
        reimbursementZa.setZfbAccount("zfbAccount");
        reimbursementZa.setDepositBankName("depositBankName");
        reimbursementZa.setPayeeBankDistrictCode("bankAreaCode");
        reimbursementZa.setPayeeBankDistrictName("bankAreaName");
        reimbursementZa.setPayeeBankBranchCode("bankBranchNameCode");
        reimbursementZa.setPayeeBankBranchName("bankBranchName");
        reimbursementZa.setCreateBy("createBy");
        reimbursementZa.setZaReportNo("reportNo");
        when(mockClaimReimbursementZaMapper.selectByClaimId(0)).thenReturn(reimbursementZa);

        // Run the test
        final List<SmClaimFileCombVO> result = zaClaimServiceUnderTest.getCombineFile(0);

        // Verify the results
    }

    @Test
    public void testListDefaultFile1() {
        // Setup
        final SmClaimFileCombVO smClaimFileCombVO = new SmClaimFileCombVO();
        smClaimFileCombVO.setClaimId(0);
        smClaimFileCombVO.setFileTypeCode("fileTypeCode");
        smClaimFileCombVO.setFileTypeName("name");
        smClaimFileCombVO.setFileRequire(false);
        smClaimFileCombVO.setFileUrls(Arrays.asList("value"));
        final ClaimFileSimpleVo claimFileSimpleVo = new ClaimFileSimpleVo();
        claimFileSimpleVo.setCfId(0);
        claimFileSimpleVo.setFileUrl("fileUrl");
        claimFileSimpleVo.setNewFlag(0);
        claimFileSimpleVo.setFileTypeCode("attachmentType");
        smClaimFileCombVO.setFileSimpleVoList(Arrays.asList(claimFileSimpleVo));
        smClaimFileCombVO.setExampleFileUrlList(Arrays.asList("value"));
        smClaimFileCombVO.setExampleFileUrlDescription("exampleFileUrlDescription");
        final List<SmClaimFileCombVO> expectedResult = Arrays.asList(smClaimFileCombVO);

        // Configure SmClaimMapper.getByClaimId(...).
        final SmClaim claim = new SmClaim();
        claim.setId(0);
        claim.setClaimNo("claimNo");
        claim.setInsuredId(0);
        claim.setRiskType("riskType");
        claim.setPayMoney(new BigDecimal("0.00"));
        claim.setRiskTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        claim.setRiskDesc("riskDesc");
        claim.setDatatTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        claim.setClaimState("claimState");
        claim.setClaimResult("claimResult");
        claim.setFinishState("finishState");
        claim.setFinishResult("finishResult");
        claim.setFinishtime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        claim.setNote("note");
        claim.setEstimatedAmount(new BigDecimal("0.00"));
        when(mockClaimMapper.getByClaimId(0)).thenReturn(claim);

        // Configure SmOrderInsuredMapper.selectByPrimaryKeyMustExists(...).
        final SmOrderInsured smOrderInsured = new SmOrderInsured();
        smOrderInsured.setId(0);
        smOrderInsured.setFhOrderId("fhOrderId");
        smOrderInsured.setRelationship("relationship");
        smOrderInsured.setPersonName("insuredName");
        smOrderInsured.setPersonGender("personGender");
        smOrderInsured.setIdType("certType");
        smOrderInsured.setIdNumber("certNo");
        smOrderInsured.setIdPeriodStart("idPeriodStart");
        smOrderInsured.setIdPeriodEnd("idPeriodEnd");
        smOrderInsured.setBirthday("birthday");
        smOrderInsured.setCellPhone("cellPhone");
        smOrderInsured.setEmail("email");
        smOrderInsured.setAnnualIncome("annualIncome");
        smOrderInsured.setFlightNo("flightNo");
        smOrderInsured.setPolicyNo("individualPolicyNo");
        when(mockInsuredMapper.selectByPrimaryKeyMustExists(0)).thenReturn(smOrderInsured);

        // Configure SmClaimReimbursementZaMapper.selectByClaimId(...).
        final SmClaimReimbursementZa reimbursementZa = new SmClaimReimbursementZa();
        reimbursementZa.setId(0);
        reimbursementZa.setClaimId(0);
        reimbursementZa.setAccidentType("accidentType");
        reimbursementZa.setReimbursementType("accidentType");
        reimbursementZa.setVisitingDate(LocalDate.of(2020, 1, 1));
        reimbursementZa.setVistingHospital("vistingHospital");
        reimbursementZa.setVistingHospitalName("vistingHospitalName");
        reimbursementZa.setRelationWithCasualty("insurantType");
        reimbursementZa.setApplierName("applierName");
        reimbursementZa.setApplierPhone("applierPhone");
        reimbursementZa.setApplierCertificateType("applierCertificateType");
        reimbursementZa.setApplierCertificateCode("applierCertificateCode");
        reimbursementZa.setApplierEmail("applierEmail");
        reimbursementZa.setPayeeWithCasualty("payeeRelation");
        reimbursementZa.setPayeeWay("payeeWay");
        reimbursementZa.setPayeeName("bankOpenName");
        reimbursementZa.setBankCard("bankAccountNo");
        reimbursementZa.setDepositBankCode("bankCode");
        reimbursementZa.setZfbAccount("zfbAccount");
        reimbursementZa.setDepositBankName("depositBankName");
        reimbursementZa.setPayeeBankDistrictCode("bankAreaCode");
        reimbursementZa.setPayeeBankDistrictName("bankAreaName");
        reimbursementZa.setPayeeBankBranchCode("bankBranchNameCode");
        reimbursementZa.setPayeeBankBranchName("bankBranchName");
        reimbursementZa.setCreateBy("createBy");
        reimbursementZa.setZaReportNo("reportNo");
        when(mockClaimReimbursementZaMapper.selectByClaimId(0)).thenReturn(reimbursementZa);

        // Run the test
        final List<SmClaimFileCombVO> result = zaClaimServiceUnderTest.listDefaultFile(0);

        // Verify the results
    }

    @Test
    public void testSaveWxClaimFileByClaimId() {
        // Setup
        final WxClaimFileCombDTO dto = new WxClaimFileCombDTO();
        final SmClaimFileCombDTO smClaimFileCombDTO = new SmClaimFileCombDTO();
        smClaimFileCombDTO.setClaimId(0);
        smClaimFileCombDTO.setFileTypeCode("fileTypeCode");
        smClaimFileCombDTO.setFileTypeName("fileTypeName");
        smClaimFileCombDTO.setFileRequire(false);
        final NewFlagFileForm newFlagFileForm = new NewFlagFileForm();
        newFlagFileForm.setFileUrl("fileUrl");
        newFlagFileForm.setNewFlag(0);
        smClaimFileCombDTO.setNewFlagFileList(Arrays.asList(newFlagFileForm));
        dto.setFileCombs(Arrays.asList(smClaimFileCombDTO));
        dto.setUserName("createBy");

        final WxSessionVO session = new WxSessionVO();
        session.setUserId("userId");
        session.setSystem("system");
        session.setChannel("channel");
        session.setAuthorization("authorization");
        session.setBmsToken("bmsToken");
        session.setShowCmsRatio(false);
        session.setShowCicEntry(false);
        session.setShowProcessApproval(false);
        session.setAgentType(0);
        final WxUserVO wxUserVO = new WxUserVO();
        wxUserVO.setId(0);
        wxUserVO.setOrgPath("orgPath");
        wxUserVO.setRegionName("regionName");
        wxUserVO.setBranchRegionName("branchRegionName");
        wxUserVO.setOrgCode("orgCode");
        session.setWxUsers(Arrays.asList(wxUserVO));

        // Configure SmClaimMapper.getSmClaimById(...).
        final WxClaimDetailVO wxClaimDetailVO = new WxClaimDetailVO();
        wxClaimDetailVO.setId(0);
        wxClaimDetailVO.setInsuredId(0);
        wxClaimDetailVO.setClaimNo("claimNo");
        wxClaimDetailVO.setRiskTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        wxClaimDetailVO.setRiskDesc("accidentDesc");
        wxClaimDetailVO.setReportTime("reportDate");
        wxClaimDetailVO.setClaimState("claimState");
        wxClaimDetailVO.setClaimResult("claimResult");
        wxClaimDetailVO.setSettlement("settlement");
        final SmClaimProgressVO progress = new SmClaimProgressVO();
        final ProgressVO progressVO = new ProgressVO();
        progressVO.setProgressId(0);
        progressVO.setSCode("sCode");
        progressVO.setDataJson("dataJson");
        progressVO.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        progress.setProgressList(Arrays.asList(progressVO));
        final ClaimWorkflow.Step nextProgress = new BaseWorkflow.Step();
        nextProgress.setSCode("claimState");
        nextProgress.setSName("claimResult");
        final BaseWorkflow.Option option = new BaseWorkflow.Option();
        option.setOCode("oCode");
        option.setOName("oName");
        option.setOType("oType");
        option.setOValue("oValue");
        nextProgress.setOptions(Arrays.asList(option));
        progress.setNextProgress(nextProgress);
        final ProgressStepVO progressStepVO = new ProgressStepVO();
        progress.setAllSteps(Arrays.asList(progressStepVO));
        wxClaimDetailVO.setProgress(progress);
        wxClaimDetailVO.setEstimatedAmount(new BigDecimal("0.00"));
        wxClaimDetailVO.setSafesCenterApprovalTimes(0);
        when(mockClaimMapper.getSmClaimById(0)).thenReturn(wxClaimDetailVO);

        // Configure UserService.getAuthUserByUserId(...).
        final AuthUserVO authUserVO = new AuthUserVO();
        authUserVO.setId(0);
        authUserVO.setRegionName("regionName");
        authUserVO.setRegionCode("regionCode");
        authUserVO.setOrgCode("orgCode");
        authUserVO.setOrganizationName("organizationName");
        authUserVO.setOrganizationFullName("organizationFullName");
        authUserVO.setUserId("userId");
        authUserVO.setAgentId(0);
        authUserVO.setAgentTopUserId("agentTopUserId");
        authUserVO.setUserName("userName");
        authUserVO.setUserMobile("userMobile");
        authUserVO.setWxNickName("wxNickName");
        authUserVO.setPostName("postName");
        authUserVO.setWxImgUrl("wxImgUrl");
        authUserVO.setHrOrgId(0);
        when(mockUserService.getAuthUserByUserId("userId")).thenReturn(authUserVO);

        // Configure UserService.getChannelPCOByHrOrgId(...).
        final AuthUserVO authUserVO1 = new AuthUserVO();
        authUserVO1.setId(0);
        authUserVO1.setRegionName("regionName");
        authUserVO1.setRegionCode("regionCode");
        authUserVO1.setOrgCode("orgCode");
        authUserVO1.setOrganizationName("organizationName");
        authUserVO1.setOrganizationFullName("organizationFullName");
        authUserVO1.setUserId("userId");
        authUserVO1.setAgentId(0);
        authUserVO1.setAgentTopUserId("agentTopUserId");
        authUserVO1.setUserName("userName");
        authUserVO1.setUserMobile("userMobile");
        authUserVO1.setWxNickName("wxNickName");
        authUserVO1.setPostName("postName");
        authUserVO1.setWxImgUrl("wxImgUrl");
        authUserVO1.setHrOrgId(0);
        when(mockUserService.getChannelPCOByHrOrgId(0)).thenReturn(authUserVO1);

        // Configure SmClaimReimbursementZaMapper.selectByClaimId(...).
        final SmClaimReimbursementZa reimbursementZa = new SmClaimReimbursementZa();
        reimbursementZa.setId(0);
        reimbursementZa.setClaimId(0);
        reimbursementZa.setAccidentType("accidentType");
        reimbursementZa.setReimbursementType("accidentType");
        reimbursementZa.setVisitingDate(LocalDate.of(2020, 1, 1));
        reimbursementZa.setVistingHospital("vistingHospital");
        reimbursementZa.setVistingHospitalName("vistingHospitalName");
        reimbursementZa.setRelationWithCasualty("insurantType");
        reimbursementZa.setApplierName("applierName");
        reimbursementZa.setApplierPhone("applierPhone");
        reimbursementZa.setApplierCertificateType("applierCertificateType");
        reimbursementZa.setApplierCertificateCode("applierCertificateCode");
        reimbursementZa.setApplierEmail("applierEmail");
        reimbursementZa.setPayeeWithCasualty("payeeRelation");
        reimbursementZa.setPayeeWay("payeeWay");
        reimbursementZa.setPayeeName("bankOpenName");
        reimbursementZa.setBankCard("bankAccountNo");
        reimbursementZa.setDepositBankCode("bankCode");
        reimbursementZa.setZfbAccount("zfbAccount");
        reimbursementZa.setDepositBankName("depositBankName");
        reimbursementZa.setPayeeBankDistrictCode("bankAreaCode");
        reimbursementZa.setPayeeBankDistrictName("bankAreaName");
        reimbursementZa.setPayeeBankBranchCode("bankBranchNameCode");
        reimbursementZa.setPayeeBankBranchName("bankBranchName");
        reimbursementZa.setCreateBy("createBy");
        reimbursementZa.setZaReportNo("reportNo");
        when(mockClaimReimbursementZaMapper.selectByClaimId(0)).thenReturn(reimbursementZa);

        // Configure ZaApiService.claimSearch(...).
        final ZaClaimQueryResponse zaClaimQueryResponse = new ZaClaimQueryResponse();
        zaClaimQueryResponse.setErrCode("errCode");
        zaClaimQueryResponse.setErrMsg("errMsg");
        zaClaimQueryResponse.setReportBizNo("reportBizNo");
        zaClaimQueryResponse.setReportNo("reportNo");
        zaClaimQueryResponse.setCurrentStatus("currentStatus");
        zaClaimQueryResponse.setPaidAmount("paidAmount");
        final ZaClaimQueryResponse.SubExtraInfo subExtraInfo = new ZaClaimQueryResponse.SubExtraInfo();
        subExtraInfo.setStatus("status");
        subExtraInfo.setReason("reason");
        subExtraInfo.setSubReason("subReason");
        subExtraInfo.setOperator("operator");
        subExtraInfo.setOperatorDate("operatorDate");
        subExtraInfo.setOperatorType("operatorType");
        zaClaimQueryResponse.setSubExtraList(Arrays.asList(subExtraInfo));
        zaClaimQueryResponse.setBackType("backType");
        zaClaimQueryResponse.setFallbackReason("fallbackReason");
        when(mockZaApiService.claimSearch(new ZaSearchRequestWrapper(new ZaSearchRequest()))).thenReturn(zaClaimQueryResponse);

        // Configure ClaimWorkflow.goToNextStep(...).
        final BaseWorkflow.Step step = new BaseWorkflow.Step();
        step.setSType(0);
        step.setSCode("claimState");
        step.setSName("claimResult");
        final BaseWorkflow.Option option1 = new BaseWorkflow.Option();
        option1.setOCode("oCode");
        option1.setOName("oName");
        option1.setOType("oType");
        option1.setOValue("oValue");
        option1.setOTimeName("oTimeName");
        option1.setNextStep("nextStep");
        step.setOptions(Arrays.asList(option1));
        when(mockWorkflow.goToNextStep("claimState", "oCode")).thenReturn(step);

        // Configure SmClaimMapper.getLastClaimExpressByClaimId(...).
        final SmClaimExpressVO expressVO = new SmClaimExpressVO();
        expressVO.setCeId(0);
        expressVO.setClaimId(0);
        expressVO.setExpressDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        expressVO.setExpressCompanyName("expressCompanyName");
        expressVO.setExpressNo("expressNo");
        expressVO.setExpressUrlJoin("expressUrlJoin");
        expressVO.setExpressUrls(Arrays.asList("value"));
        when(mockClaimMapper.getLastClaimExpressByClaimId(0)).thenReturn(expressVO);

        // Configure ZaApiService.addExpressTask(...).
        final ZaExpressResponse zaExpressResponse = new ZaExpressResponse();
        zaExpressResponse.setErrCode("errCode");
        zaExpressResponse.setErrMsg("errMsg");
        when(mockZaApiService.addExpressTask(new ZaClaimExpressTaskWrapper(new ZaExpressTask()))).thenReturn(zaExpressResponse);

        when(mockAuthUserMapper.getUserBizCodeByIdNumber("userName")).thenReturn("createBy");

        // Configure SmOrderInsuredMapper.selectByPrimaryKeyMustExists(...).
        final SmOrderInsured smOrderInsured = new SmOrderInsured();
        smOrderInsured.setId(0);
        smOrderInsured.setFhOrderId("fhOrderId");
        smOrderInsured.setRelationship("relationship");
        smOrderInsured.setPersonName("insuredName");
        smOrderInsured.setPersonGender("personGender");
        smOrderInsured.setIdType("certType");
        smOrderInsured.setIdNumber("certNo");
        smOrderInsured.setIdPeriodStart("idPeriodStart");
        smOrderInsured.setIdPeriodEnd("idPeriodEnd");
        smOrderInsured.setBirthday("birthday");
        smOrderInsured.setCellPhone("cellPhone");
        smOrderInsured.setEmail("email");
        smOrderInsured.setAnnualIncome("annualIncome");
        smOrderInsured.setFlightNo("flightNo");
        smOrderInsured.setPolicyNo("individualPolicyNo");
        when(mockInsuredMapper.selectByPrimaryKeyMustExists(0)).thenReturn(smOrderInsured);

        // Configure SmClaimMapper.listSmClaimFileUnitByClaimId(...).
        final SmClaimFileUnitVO smClaimFileUnitVO = new SmClaimFileUnitVO();
        smClaimFileUnitVO.setCfId(0);
        smClaimFileUnitVO.setClaimId(0);
        smClaimFileUnitVO.setFileTypeCode("attachmentType");
        smClaimFileUnitVO.setFileTypeName("attachmentName");
        smClaimFileUnitVO.setFileUrl("fileUrl");
        smClaimFileUnitVO.setNewFlag(0);
        smClaimFileUnitVO.setZaKey("attachmentKey");
        final List<SmClaimFileUnitVO> smClaimFileUnitVOS = Arrays.asList(smClaimFileUnitVO);
        when(mockClaimMapper.listSmClaimFileUnitByClaimId(0)).thenReturn(smClaimFileUnitVOS);

        // Configure ZaApiService.uploadFile(...).
        final FileUploadResponse fileUploadResponse = new FileUploadResponse();
        fileUploadResponse.setCode("code");
        fileUploadResponse.setData("data");
        fileUploadResponse.setMsg("msg");
        fileUploadResponse.setTimestamp("timestamp");
        fileUploadResponse.setSign("sign");
        when(mockZaApiService.uploadFile("pathname")).thenReturn(fileUploadResponse);

        when(mockClaimMapper.updateClaimFileZaKey(0, "data")).thenReturn(0);

        // Configure ZaApiService.claimReport(...).
        final ReportResponse reportResponse = new ReportResponse();
        reportResponse.setErrCode("errCode");
        reportResponse.setErrMsg("errMsg");
        reportResponse.setReportBizNo("reportBizNo");
        reportResponse.setChannelBatchNo("channelBatchNo");
        reportResponse.setChannelReportNo("channelReportNo");
        reportResponse.setSource("source");
        reportResponse.setReportNo("reportNo");
        reportResponse.setIndividualPolicyNo("individualPolicyNo");
        when(mockZaApiService.claimReport(new ZaRequestWrapper<>())).thenReturn(reportResponse);

        when(mockTransactionTemplate.execute(any(TransactionCallback.class))).thenReturn("result");
        when(mockClaimReimbursementZaMapper.updateZaReportNoByClaimId(0, "reportNo", LocalDateTime.now())).thenReturn(0);

        // Run the test
        zaClaimServiceUnderTest.saveWxClaimFileByClaimId(0, dto, session);

    }

    @Test
    public void testSaveWxClaimFileByClaimId_SmClaimMapperListSmClaimFileUnitByClaimIdReturnsNoItems() {
        // Setup
        final WxClaimFileCombDTO dto = new WxClaimFileCombDTO();
        final SmClaimFileCombDTO smClaimFileCombDTO = new SmClaimFileCombDTO();
        smClaimFileCombDTO.setClaimId(0);
        smClaimFileCombDTO.setFileTypeCode("fileTypeCode");
        smClaimFileCombDTO.setFileTypeName("fileTypeName");
        smClaimFileCombDTO.setFileRequire(false);
        final NewFlagFileForm newFlagFileForm = new NewFlagFileForm();
        newFlagFileForm.setFileUrl("fileUrl");
        newFlagFileForm.setNewFlag(0);
        smClaimFileCombDTO.setNewFlagFileList(Arrays.asList(newFlagFileForm));
        dto.setFileCombs(Arrays.asList(smClaimFileCombDTO));
        dto.setUserName("createBy");

        final WxSessionVO session = new WxSessionVO();
        session.setUserId("userId");
        session.setSystem("system");
        session.setChannel("channel");
        session.setAuthorization("authorization");
        session.setBmsToken("bmsToken");
        session.setShowCmsRatio(false);
        session.setShowCicEntry(false);
        session.setShowProcessApproval(false);
        session.setAgentType(0);
        final WxUserVO wxUserVO = new WxUserVO();
        wxUserVO.setId(0);
        wxUserVO.setOrgPath("orgPath");
        wxUserVO.setRegionName("regionName");
        wxUserVO.setBranchRegionName("branchRegionName");
        wxUserVO.setOrgCode("orgCode");
        session.setWxUsers(Arrays.asList(wxUserVO));

        // Configure SmClaimMapper.getSmClaimById(...).
        final WxClaimDetailVO wxClaimDetailVO = new WxClaimDetailVO();
        wxClaimDetailVO.setId(0);
        wxClaimDetailVO.setInsuredId(0);
        wxClaimDetailVO.setClaimNo("claimNo");
        wxClaimDetailVO.setRiskTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        wxClaimDetailVO.setRiskDesc("accidentDesc");
        wxClaimDetailVO.setReportTime("reportDate");
        wxClaimDetailVO.setClaimState("claimState");
        wxClaimDetailVO.setClaimResult("claimResult");
        wxClaimDetailVO.setSettlement("settlement");
        final SmClaimProgressVO progress = new SmClaimProgressVO();
        final ProgressVO progressVO = new ProgressVO();
        progressVO.setProgressId(0);
        progressVO.setSCode("sCode");
        progressVO.setDataJson("dataJson");
        progressVO.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        progress.setProgressList(Arrays.asList(progressVO));
        final ClaimWorkflow.Step nextProgress = new BaseWorkflow.Step();
        nextProgress.setSCode("claimState");
        nextProgress.setSName("claimResult");
        final BaseWorkflow.Option option = new BaseWorkflow.Option();
        option.setOCode("oCode");
        option.setOName("oName");
        option.setOType("oType");
        option.setOValue("oValue");
        nextProgress.setOptions(Arrays.asList(option));
        progress.setNextProgress(nextProgress);
        final ProgressStepVO progressStepVO = new ProgressStepVO();
        progress.setAllSteps(Arrays.asList(progressStepVO));
        wxClaimDetailVO.setProgress(progress);
        wxClaimDetailVO.setEstimatedAmount(new BigDecimal("0.00"));
        wxClaimDetailVO.setSafesCenterApprovalTimes(0);
        when(mockClaimMapper.getSmClaimById(0)).thenReturn(wxClaimDetailVO);

        // Configure UserService.getAuthUserByUserId(...).
        final AuthUserVO authUserVO = new AuthUserVO();
        authUserVO.setId(0);
        authUserVO.setRegionName("regionName");
        authUserVO.setRegionCode("regionCode");
        authUserVO.setOrgCode("orgCode");
        authUserVO.setOrganizationName("organizationName");
        authUserVO.setOrganizationFullName("organizationFullName");
        authUserVO.setUserId("userId");
        authUserVO.setAgentId(0);
        authUserVO.setAgentTopUserId("agentTopUserId");
        authUserVO.setUserName("userName");
        authUserVO.setUserMobile("userMobile");
        authUserVO.setWxNickName("wxNickName");
        authUserVO.setPostName("postName");
        authUserVO.setWxImgUrl("wxImgUrl");
        authUserVO.setHrOrgId(0);
        when(mockUserService.getAuthUserByUserId("userId")).thenReturn(authUserVO);

        // Configure UserService.getChannelPCOByHrOrgId(...).
        final AuthUserVO authUserVO1 = new AuthUserVO();
        authUserVO1.setId(0);
        authUserVO1.setRegionName("regionName");
        authUserVO1.setRegionCode("regionCode");
        authUserVO1.setOrgCode("orgCode");
        authUserVO1.setOrganizationName("organizationName");
        authUserVO1.setOrganizationFullName("organizationFullName");
        authUserVO1.setUserId("userId");
        authUserVO1.setAgentId(0);
        authUserVO1.setAgentTopUserId("agentTopUserId");
        authUserVO1.setUserName("userName");
        authUserVO1.setUserMobile("userMobile");
        authUserVO1.setWxNickName("wxNickName");
        authUserVO1.setPostName("postName");
        authUserVO1.setWxImgUrl("wxImgUrl");
        authUserVO1.setHrOrgId(0);
        when(mockUserService.getChannelPCOByHrOrgId(0)).thenReturn(authUserVO1);

        // Configure SmClaimReimbursementZaMapper.selectByClaimId(...).
        final SmClaimReimbursementZa reimbursementZa = new SmClaimReimbursementZa();
        reimbursementZa.setId(0);
        reimbursementZa.setClaimId(0);
        reimbursementZa.setAccidentType("accidentType");
        reimbursementZa.setReimbursementType("accidentType");
        reimbursementZa.setVisitingDate(LocalDate.of(2020, 1, 1));
        reimbursementZa.setVistingHospital("vistingHospital");
        reimbursementZa.setVistingHospitalName("vistingHospitalName");
        reimbursementZa.setRelationWithCasualty("insurantType");
        reimbursementZa.setApplierName("applierName");
        reimbursementZa.setApplierPhone("applierPhone");
        reimbursementZa.setApplierCertificateType("applierCertificateType");
        reimbursementZa.setApplierCertificateCode("applierCertificateCode");
        reimbursementZa.setApplierEmail("applierEmail");
        reimbursementZa.setPayeeWithCasualty("payeeRelation");
        reimbursementZa.setPayeeWay("payeeWay");
        reimbursementZa.setPayeeName("bankOpenName");
        reimbursementZa.setBankCard("bankAccountNo");
        reimbursementZa.setDepositBankCode("bankCode");
        reimbursementZa.setZfbAccount("zfbAccount");
        reimbursementZa.setDepositBankName("depositBankName");
        reimbursementZa.setPayeeBankDistrictCode("bankAreaCode");
        reimbursementZa.setPayeeBankDistrictName("bankAreaName");
        reimbursementZa.setPayeeBankBranchCode("bankBranchNameCode");
        reimbursementZa.setPayeeBankBranchName("bankBranchName");
        reimbursementZa.setCreateBy("createBy");
        reimbursementZa.setZaReportNo("reportNo");
        when(mockClaimReimbursementZaMapper.selectByClaimId(0)).thenReturn(reimbursementZa);

        // Configure ZaApiService.claimSearch(...).
        final ZaClaimQueryResponse zaClaimQueryResponse = new ZaClaimQueryResponse();
        zaClaimQueryResponse.setErrCode("errCode");
        zaClaimQueryResponse.setErrMsg("errMsg");
        zaClaimQueryResponse.setReportBizNo("reportBizNo");
        zaClaimQueryResponse.setReportNo("reportNo");
        zaClaimQueryResponse.setCurrentStatus("currentStatus");
        zaClaimQueryResponse.setPaidAmount("paidAmount");
        final ZaClaimQueryResponse.SubExtraInfo subExtraInfo = new ZaClaimQueryResponse.SubExtraInfo();
        subExtraInfo.setStatus("status");
        subExtraInfo.setReason("reason");
        subExtraInfo.setSubReason("subReason");
        subExtraInfo.setOperator("operator");
        subExtraInfo.setOperatorDate("operatorDate");
        subExtraInfo.setOperatorType("operatorType");
        zaClaimQueryResponse.setSubExtraList(Arrays.asList(subExtraInfo));
        zaClaimQueryResponse.setBackType("backType");
        zaClaimQueryResponse.setFallbackReason("fallbackReason");
        when(mockZaApiService.claimSearch(new ZaSearchRequestWrapper(new ZaSearchRequest()))).thenReturn(zaClaimQueryResponse);

        // Configure ClaimWorkflow.goToNextStep(...).
        final BaseWorkflow.Step step = new BaseWorkflow.Step();
        step.setSType(0);
        step.setSCode("claimState");
        step.setSName("claimResult");
        final BaseWorkflow.Option option1 = new BaseWorkflow.Option();
        option1.setOCode("oCode");
        option1.setOName("oName");
        option1.setOType("oType");
        option1.setOValue("oValue");
        option1.setOTimeName("oTimeName");
        option1.setNextStep("nextStep");
        step.setOptions(Arrays.asList(option1));
        when(mockWorkflow.goToNextStep("claimState", "oCode")).thenReturn(step);

        // Configure SmClaimMapper.getLastClaimExpressByClaimId(...).
        final SmClaimExpressVO expressVO = new SmClaimExpressVO();
        expressVO.setCeId(0);
        expressVO.setClaimId(0);
        expressVO.setExpressDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        expressVO.setExpressCompanyName("expressCompanyName");
        expressVO.setExpressNo("expressNo");
        expressVO.setExpressUrlJoin("expressUrlJoin");
        expressVO.setExpressUrls(Arrays.asList("value"));
        when(mockClaimMapper.getLastClaimExpressByClaimId(0)).thenReturn(expressVO);

        // Configure ZaApiService.addExpressTask(...).
        final ZaExpressResponse zaExpressResponse = new ZaExpressResponse();
        zaExpressResponse.setErrCode("errCode");
        zaExpressResponse.setErrMsg("errMsg");
        when(mockZaApiService.addExpressTask(new ZaClaimExpressTaskWrapper(new ZaExpressTask()))).thenReturn(zaExpressResponse);

        when(mockAuthUserMapper.getUserBizCodeByIdNumber("userName")).thenReturn("createBy");

        // Configure SmOrderInsuredMapper.selectByPrimaryKeyMustExists(...).
        final SmOrderInsured smOrderInsured = new SmOrderInsured();
        smOrderInsured.setId(0);
        smOrderInsured.setFhOrderId("fhOrderId");
        smOrderInsured.setRelationship("relationship");
        smOrderInsured.setPersonName("insuredName");
        smOrderInsured.setPersonGender("personGender");
        smOrderInsured.setIdType("certType");
        smOrderInsured.setIdNumber("certNo");
        smOrderInsured.setIdPeriodStart("idPeriodStart");
        smOrderInsured.setIdPeriodEnd("idPeriodEnd");
        smOrderInsured.setBirthday("birthday");
        smOrderInsured.setCellPhone("cellPhone");
        smOrderInsured.setEmail("email");
        smOrderInsured.setAnnualIncome("annualIncome");
        smOrderInsured.setFlightNo("flightNo");
        smOrderInsured.setPolicyNo("individualPolicyNo");
        when(mockInsuredMapper.selectByPrimaryKeyMustExists(0)).thenReturn(smOrderInsured);

        when(mockClaimMapper.listSmClaimFileUnitByClaimId(0)).thenReturn(Collections.emptyList());

        // Configure ZaApiService.uploadFile(...).
        final FileUploadResponse fileUploadResponse = new FileUploadResponse();
        fileUploadResponse.setCode("code");
        fileUploadResponse.setData("data");
        fileUploadResponse.setMsg("msg");
        fileUploadResponse.setTimestamp("timestamp");
        fileUploadResponse.setSign("sign");
        when(mockZaApiService.uploadFile("pathname")).thenReturn(fileUploadResponse);

        when(mockClaimMapper.updateClaimFileZaKey(0, "data")).thenReturn(0);

        // Configure ZaApiService.claimReport(...).
        final ReportResponse reportResponse = new ReportResponse();
        reportResponse.setErrCode("errCode");
        reportResponse.setErrMsg("errMsg");
        reportResponse.setReportBizNo("reportBizNo");
        reportResponse.setChannelBatchNo("channelBatchNo");
        reportResponse.setChannelReportNo("channelReportNo");
        reportResponse.setSource("source");
        reportResponse.setReportNo("reportNo");
        reportResponse.setIndividualPolicyNo("individualPolicyNo");
        when(mockZaApiService.claimReport(new ZaRequestWrapper<>())).thenReturn(reportResponse);

        when(mockTransactionTemplate.execute(any(TransactionCallback.class))).thenReturn("result");
        when(mockClaimReimbursementZaMapper.updateZaReportNoByClaimId(0, "reportNo", LocalDateTime.now())).thenReturn(0);

        // Run the test
        zaClaimServiceUnderTest.saveWxClaimFileByClaimId(0, dto, session);

    }

    @Test
    public void testSaveWxClaimFileByClaimId_TransactionTemplateThrowsTransactionException() {
        // Setup
        final WxClaimFileCombDTO dto = new WxClaimFileCombDTO();
        final SmClaimFileCombDTO smClaimFileCombDTO = new SmClaimFileCombDTO();
        smClaimFileCombDTO.setClaimId(0);
        smClaimFileCombDTO.setFileTypeCode("fileTypeCode");
        smClaimFileCombDTO.setFileTypeName("fileTypeName");
        smClaimFileCombDTO.setFileRequire(false);
        final NewFlagFileForm newFlagFileForm = new NewFlagFileForm();
        newFlagFileForm.setFileUrl("fileUrl");
        newFlagFileForm.setNewFlag(0);
        smClaimFileCombDTO.setNewFlagFileList(Arrays.asList(newFlagFileForm));
        dto.setFileCombs(Arrays.asList(smClaimFileCombDTO));
        dto.setUserName("createBy");

        final WxSessionVO session = new WxSessionVO();
        session.setUserId("userId");
        session.setSystem("system");
        session.setChannel("channel");
        session.setAuthorization("authorization");
        session.setBmsToken("bmsToken");
        session.setShowCmsRatio(false);
        session.setShowCicEntry(false);
        session.setShowProcessApproval(false);
        session.setAgentType(0);
        final WxUserVO wxUserVO = new WxUserVO();
        wxUserVO.setId(0);
        wxUserVO.setOrgPath("orgPath");
        wxUserVO.setRegionName("regionName");
        wxUserVO.setBranchRegionName("branchRegionName");
        wxUserVO.setOrgCode("orgCode");
        session.setWxUsers(Arrays.asList(wxUserVO));

        // Configure SmClaimMapper.getSmClaimById(...).
        final WxClaimDetailVO wxClaimDetailVO = new WxClaimDetailVO();
        wxClaimDetailVO.setId(0);
        wxClaimDetailVO.setInsuredId(0);
        wxClaimDetailVO.setClaimNo("claimNo");
        wxClaimDetailVO.setRiskTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        wxClaimDetailVO.setRiskDesc("accidentDesc");
        wxClaimDetailVO.setReportTime("reportDate");
        wxClaimDetailVO.setClaimState("claimState");
        wxClaimDetailVO.setClaimResult("claimResult");
        wxClaimDetailVO.setSettlement("settlement");
        final SmClaimProgressVO progress = new SmClaimProgressVO();
        final ProgressVO progressVO = new ProgressVO();
        progressVO.setProgressId(0);
        progressVO.setSCode("sCode");
        progressVO.setDataJson("dataJson");
        progressVO.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        progress.setProgressList(Arrays.asList(progressVO));
        final ClaimWorkflow.Step nextProgress = new BaseWorkflow.Step();
        nextProgress.setSCode("claimState");
        nextProgress.setSName("claimResult");
        final BaseWorkflow.Option option = new BaseWorkflow.Option();
        option.setOCode("oCode");
        option.setOName("oName");
        option.setOType("oType");
        option.setOValue("oValue");
        nextProgress.setOptions(Arrays.asList(option));
        progress.setNextProgress(nextProgress);
        final ProgressStepVO progressStepVO = new ProgressStepVO();
        progress.setAllSteps(Arrays.asList(progressStepVO));
        wxClaimDetailVO.setProgress(progress);
        wxClaimDetailVO.setEstimatedAmount(new BigDecimal("0.00"));
        wxClaimDetailVO.setSafesCenterApprovalTimes(0);
        when(mockClaimMapper.getSmClaimById(0)).thenReturn(wxClaimDetailVO);

        // Configure UserService.getAuthUserByUserId(...).
        final AuthUserVO authUserVO = new AuthUserVO();
        authUserVO.setId(0);
        authUserVO.setRegionName("regionName");
        authUserVO.setRegionCode("regionCode");
        authUserVO.setOrgCode("orgCode");
        authUserVO.setOrganizationName("organizationName");
        authUserVO.setOrganizationFullName("organizationFullName");
        authUserVO.setUserId("userId");
        authUserVO.setAgentId(0);
        authUserVO.setAgentTopUserId("agentTopUserId");
        authUserVO.setUserName("userName");
        authUserVO.setUserMobile("userMobile");
        authUserVO.setWxNickName("wxNickName");
        authUserVO.setPostName("postName");
        authUserVO.setWxImgUrl("wxImgUrl");
        authUserVO.setHrOrgId(0);
        when(mockUserService.getAuthUserByUserId("userId")).thenReturn(authUserVO);

        // Configure UserService.getChannelPCOByHrOrgId(...).
        final AuthUserVO authUserVO1 = new AuthUserVO();
        authUserVO1.setId(0);
        authUserVO1.setRegionName("regionName");
        authUserVO1.setRegionCode("regionCode");
        authUserVO1.setOrgCode("orgCode");
        authUserVO1.setOrganizationName("organizationName");
        authUserVO1.setOrganizationFullName("organizationFullName");
        authUserVO1.setUserId("userId");
        authUserVO1.setAgentId(0);
        authUserVO1.setAgentTopUserId("agentTopUserId");
        authUserVO1.setUserName("userName");
        authUserVO1.setUserMobile("userMobile");
        authUserVO1.setWxNickName("wxNickName");
        authUserVO1.setPostName("postName");
        authUserVO1.setWxImgUrl("wxImgUrl");
        authUserVO1.setHrOrgId(0);
        when(mockUserService.getChannelPCOByHrOrgId(0)).thenReturn(authUserVO1);

        // Configure SmClaimReimbursementZaMapper.selectByClaimId(...).
        final SmClaimReimbursementZa reimbursementZa = new SmClaimReimbursementZa();
        reimbursementZa.setId(0);
        reimbursementZa.setClaimId(0);
        reimbursementZa.setAccidentType("accidentType");
        reimbursementZa.setReimbursementType("accidentType");
        reimbursementZa.setVisitingDate(LocalDate.of(2020, 1, 1));
        reimbursementZa.setVistingHospital("vistingHospital");
        reimbursementZa.setVistingHospitalName("vistingHospitalName");
        reimbursementZa.setRelationWithCasualty("insurantType");
        reimbursementZa.setApplierName("applierName");
        reimbursementZa.setApplierPhone("applierPhone");
        reimbursementZa.setApplierCertificateType("applierCertificateType");
        reimbursementZa.setApplierCertificateCode("applierCertificateCode");
        reimbursementZa.setApplierEmail("applierEmail");
        reimbursementZa.setPayeeWithCasualty("payeeRelation");
        reimbursementZa.setPayeeWay("payeeWay");
        reimbursementZa.setPayeeName("bankOpenName");
        reimbursementZa.setBankCard("bankAccountNo");
        reimbursementZa.setDepositBankCode("bankCode");
        reimbursementZa.setZfbAccount("zfbAccount");
        reimbursementZa.setDepositBankName("depositBankName");
        reimbursementZa.setPayeeBankDistrictCode("bankAreaCode");
        reimbursementZa.setPayeeBankDistrictName("bankAreaName");
        reimbursementZa.setPayeeBankBranchCode("bankBranchNameCode");
        reimbursementZa.setPayeeBankBranchName("bankBranchName");
        reimbursementZa.setCreateBy("createBy");
        reimbursementZa.setZaReportNo("reportNo");
        when(mockClaimReimbursementZaMapper.selectByClaimId(0)).thenReturn(reimbursementZa);

        // Configure ZaApiService.claimSearch(...).
        final ZaClaimQueryResponse zaClaimQueryResponse = new ZaClaimQueryResponse();
        zaClaimQueryResponse.setErrCode("errCode");
        zaClaimQueryResponse.setErrMsg("errMsg");
        zaClaimQueryResponse.setReportBizNo("reportBizNo");
        zaClaimQueryResponse.setReportNo("reportNo");
        zaClaimQueryResponse.setCurrentStatus("currentStatus");
        zaClaimQueryResponse.setPaidAmount("paidAmount");
        final ZaClaimQueryResponse.SubExtraInfo subExtraInfo = new ZaClaimQueryResponse.SubExtraInfo();
        subExtraInfo.setStatus("status");
        subExtraInfo.setReason("reason");
        subExtraInfo.setSubReason("subReason");
        subExtraInfo.setOperator("operator");
        subExtraInfo.setOperatorDate("operatorDate");
        subExtraInfo.setOperatorType("operatorType");
        zaClaimQueryResponse.setSubExtraList(Arrays.asList(subExtraInfo));
        zaClaimQueryResponse.setBackType("backType");
        zaClaimQueryResponse.setFallbackReason("fallbackReason");
        when(mockZaApiService.claimSearch(new ZaSearchRequestWrapper(new ZaSearchRequest()))).thenReturn(zaClaimQueryResponse);

        // Configure ClaimWorkflow.goToNextStep(...).
        final BaseWorkflow.Step step = new BaseWorkflow.Step();
        step.setSType(0);
        step.setSCode("claimState");
        step.setSName("claimResult");
        final BaseWorkflow.Option option1 = new BaseWorkflow.Option();
        option1.setOCode("oCode");
        option1.setOName("oName");
        option1.setOType("oType");
        option1.setOValue("oValue");
        option1.setOTimeName("oTimeName");
        option1.setNextStep("nextStep");
        step.setOptions(Arrays.asList(option1));
        when(mockWorkflow.goToNextStep("claimState", "oCode")).thenReturn(step);

        // Configure SmClaimMapper.getLastClaimExpressByClaimId(...).
        final SmClaimExpressVO expressVO = new SmClaimExpressVO();
        expressVO.setCeId(0);
        expressVO.setClaimId(0);
        expressVO.setExpressDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        expressVO.setExpressCompanyName("expressCompanyName");
        expressVO.setExpressNo("expressNo");
        expressVO.setExpressUrlJoin("expressUrlJoin");
        expressVO.setExpressUrls(Arrays.asList("value"));
        when(mockClaimMapper.getLastClaimExpressByClaimId(0)).thenReturn(expressVO);

        // Configure ZaApiService.addExpressTask(...).
        final ZaExpressResponse zaExpressResponse = new ZaExpressResponse();
        zaExpressResponse.setErrCode("errCode");
        zaExpressResponse.setErrMsg("errMsg");
        when(mockZaApiService.addExpressTask(new ZaClaimExpressTaskWrapper(new ZaExpressTask()))).thenReturn(zaExpressResponse);

        when(mockAuthUserMapper.getUserBizCodeByIdNumber("userName")).thenReturn("createBy");

        // Configure SmOrderInsuredMapper.selectByPrimaryKeyMustExists(...).
        final SmOrderInsured smOrderInsured = new SmOrderInsured();
        smOrderInsured.setId(0);
        smOrderInsured.setFhOrderId("fhOrderId");
        smOrderInsured.setRelationship("relationship");
        smOrderInsured.setPersonName("insuredName");
        smOrderInsured.setPersonGender("personGender");
        smOrderInsured.setIdType("certType");
        smOrderInsured.setIdNumber("certNo");
        smOrderInsured.setIdPeriodStart("idPeriodStart");
        smOrderInsured.setIdPeriodEnd("idPeriodEnd");
        smOrderInsured.setBirthday("birthday");
        smOrderInsured.setCellPhone("cellPhone");
        smOrderInsured.setEmail("email");
        smOrderInsured.setAnnualIncome("annualIncome");
        smOrderInsured.setFlightNo("flightNo");
        smOrderInsured.setPolicyNo("individualPolicyNo");
        when(mockInsuredMapper.selectByPrimaryKeyMustExists(0)).thenReturn(smOrderInsured);

        // Configure SmClaimMapper.listSmClaimFileUnitByClaimId(...).
        final SmClaimFileUnitVO smClaimFileUnitVO = new SmClaimFileUnitVO();
        smClaimFileUnitVO.setCfId(0);
        smClaimFileUnitVO.setClaimId(0);
        smClaimFileUnitVO.setFileTypeCode("attachmentType");
        smClaimFileUnitVO.setFileTypeName("attachmentName");
        smClaimFileUnitVO.setFileUrl("fileUrl");
        smClaimFileUnitVO.setNewFlag(0);
        smClaimFileUnitVO.setZaKey("attachmentKey");
        final List<SmClaimFileUnitVO> smClaimFileUnitVOS = Arrays.asList(smClaimFileUnitVO);
        when(mockClaimMapper.listSmClaimFileUnitByClaimId(0)).thenReturn(smClaimFileUnitVOS);

        // Configure ZaApiService.uploadFile(...).
        final FileUploadResponse fileUploadResponse = new FileUploadResponse();
        fileUploadResponse.setCode("code");
        fileUploadResponse.setData("data");
        fileUploadResponse.setMsg("msg");
        fileUploadResponse.setTimestamp("timestamp");
        fileUploadResponse.setSign("sign");
        when(mockZaApiService.uploadFile("pathname")).thenReturn(fileUploadResponse);

        when(mockClaimMapper.updateClaimFileZaKey(0, "data")).thenReturn(0);

        // Configure ZaApiService.claimReport(...).
        final ReportResponse reportResponse = new ReportResponse();
        reportResponse.setErrCode("errCode");
        reportResponse.setErrMsg("errMsg");
        reportResponse.setReportBizNo("reportBizNo");
        reportResponse.setChannelBatchNo("channelBatchNo");
        reportResponse.setChannelReportNo("channelReportNo");
        reportResponse.setSource("source");
        reportResponse.setReportNo("reportNo");
        reportResponse.setIndividualPolicyNo("individualPolicyNo");
        when(mockZaApiService.claimReport(new ZaRequestWrapper<>())).thenReturn(reportResponse);

        try {
            when(mockClaimReimbursementZaMapper.updateZaReportNoByClaimId(0, "reportNo", LocalDateTime.now())).thenReturn(0);

            // Run the test
            zaClaimServiceUnderTest.saveWxClaimFileByClaimId(0, dto, session);
        } catch (TransactionException e) {
        }

    }

    @Test
    public void testSaveProgress() {
        // Setup
        final ProgressDTO dto = new ProgressDTO();
        dto.setId(0L);
        dto.setClaimId(0);
        dto.setSCode("claimState");
        dto.setSName("claimResult");
        dto.setOCode("oCode");
        dto.setOName("oName");
        dto.setOType("oType");
        dto.setOValue("oValue");
        dto.setDataJson("dataJson");
        dto.setOTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        dto.setExpressTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        dto.setSettlement("settlement");
        dto.setCreateBy("createBy");
        final ClaimEmailForm claimEmailForm = new ClaimEmailForm();
        claimEmailForm.setTheme("theme");
        dto.setClaimEmailForm(claimEmailForm);

        // Configure SmClaimReimbursementZaMapper.selectByClaimId(...).
        final SmClaimReimbursementZa reimbursementZa = new SmClaimReimbursementZa();
        reimbursementZa.setId(0);
        reimbursementZa.setClaimId(0);
        reimbursementZa.setAccidentType("accidentType");
        reimbursementZa.setReimbursementType("accidentType");
        reimbursementZa.setVisitingDate(LocalDate.of(2020, 1, 1));
        reimbursementZa.setVistingHospital("vistingHospital");
        reimbursementZa.setVistingHospitalName("vistingHospitalName");
        reimbursementZa.setRelationWithCasualty("insurantType");
        reimbursementZa.setApplierName("applierName");
        reimbursementZa.setApplierPhone("applierPhone");
        reimbursementZa.setApplierCertificateType("applierCertificateType");
        reimbursementZa.setApplierCertificateCode("applierCertificateCode");
        reimbursementZa.setApplierEmail("applierEmail");
        reimbursementZa.setPayeeWithCasualty("payeeRelation");
        reimbursementZa.setPayeeWay("payeeWay");
        reimbursementZa.setPayeeName("bankOpenName");
        reimbursementZa.setBankCard("bankAccountNo");
        reimbursementZa.setDepositBankCode("bankCode");
        reimbursementZa.setZfbAccount("zfbAccount");
        reimbursementZa.setDepositBankName("depositBankName");
        reimbursementZa.setPayeeBankDistrictCode("bankAreaCode");
        reimbursementZa.setPayeeBankDistrictName("bankAreaName");
        reimbursementZa.setPayeeBankBranchCode("bankBranchNameCode");
        reimbursementZa.setPayeeBankBranchName("bankBranchName");
        reimbursementZa.setCreateBy("createBy");
        reimbursementZa.setZaReportNo("reportNo");
        when(mockClaimReimbursementZaMapper.selectByClaimId(0)).thenReturn(reimbursementZa);

        // Configure ZaApiService.claimSearch(...).
        final ZaClaimQueryResponse zaClaimQueryResponse = new ZaClaimQueryResponse();
        zaClaimQueryResponse.setErrCode("errCode");
        zaClaimQueryResponse.setErrMsg("errMsg");
        zaClaimQueryResponse.setReportBizNo("reportBizNo");
        zaClaimQueryResponse.setReportNo("reportNo");
        zaClaimQueryResponse.setCurrentStatus("currentStatus");
        zaClaimQueryResponse.setPaidAmount("paidAmount");
        final ZaClaimQueryResponse.SubExtraInfo subExtraInfo = new ZaClaimQueryResponse.SubExtraInfo();
        subExtraInfo.setStatus("status");
        subExtraInfo.setReason("reason");
        subExtraInfo.setSubReason("subReason");
        subExtraInfo.setOperator("operator");
        subExtraInfo.setOperatorDate("operatorDate");
        subExtraInfo.setOperatorType("operatorType");
        zaClaimQueryResponse.setSubExtraList(Arrays.asList(subExtraInfo));
        zaClaimQueryResponse.setBackType("backType");
        zaClaimQueryResponse.setFallbackReason("fallbackReason");
        when(mockZaApiService.claimSearch(new ZaSearchRequestWrapper(new ZaSearchRequest()))).thenReturn(zaClaimQueryResponse);

        // Configure ClaimWorkflow.goToNextStep(...).
        final BaseWorkflow.Step step = new BaseWorkflow.Step();
        step.setSType(0);
        step.setSCode("claimState");
        step.setSName("claimResult");
        final BaseWorkflow.Option option = new BaseWorkflow.Option();
        option.setOCode("oCode");
        option.setOName("oName");
        option.setOType("oType");
        option.setOValue("oValue");
        option.setOTimeName("oTimeName");
        option.setNextStep("nextStep");
        step.setOptions(Arrays.asList(option));
        when(mockWorkflow.goToNextStep("claimState", "oCode")).thenReturn(step);

        // Configure SmClaimMapper.getLastClaimExpressByClaimId(...).
        final SmClaimExpressVO expressVO = new SmClaimExpressVO();
        expressVO.setCeId(0);
        expressVO.setClaimId(0);
        expressVO.setExpressDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        expressVO.setExpressCompanyName("expressCompanyName");
        expressVO.setExpressNo("expressNo");
        expressVO.setExpressUrlJoin("expressUrlJoin");
        expressVO.setExpressUrls(Arrays.asList("value"));
        when(mockClaimMapper.getLastClaimExpressByClaimId(0)).thenReturn(expressVO);

        // Configure ZaApiService.addExpressTask(...).
        final ZaExpressResponse zaExpressResponse = new ZaExpressResponse();
        zaExpressResponse.setErrCode("errCode");
        zaExpressResponse.setErrMsg("errMsg");
        when(mockZaApiService.addExpressTask(new ZaClaimExpressTaskWrapper(new ZaExpressTask()))).thenReturn(zaExpressResponse);

        // Configure UserService.getAuthUserByUserId(...).
        final AuthUserVO authUserVO = new AuthUserVO();
        authUserVO.setId(0);
        authUserVO.setRegionName("regionName");
        authUserVO.setRegionCode("regionCode");
        authUserVO.setOrgCode("orgCode");
        authUserVO.setOrganizationName("organizationName");
        authUserVO.setOrganizationFullName("organizationFullName");
        authUserVO.setUserId("userId");
        authUserVO.setAgentId(0);
        authUserVO.setAgentTopUserId("agentTopUserId");
        authUserVO.setUserName("userName");
        authUserVO.setUserMobile("userMobile");
        authUserVO.setWxNickName("wxNickName");
        authUserVO.setPostName("postName");
        authUserVO.setWxImgUrl("wxImgUrl");
        authUserVO.setHrOrgId(0);
        when(mockUserService.getAuthUserByUserId("jobNumber")).thenReturn(authUserVO);

        when(mockAuthUserMapper.getUserBizCodeByIdNumber("userName")).thenReturn("createBy");

        // Configure SmOrderInsuredMapper.selectByPrimaryKeyMustExists(...).
        final SmOrderInsured smOrderInsured = new SmOrderInsured();
        smOrderInsured.setId(0);
        smOrderInsured.setFhOrderId("fhOrderId");
        smOrderInsured.setRelationship("relationship");
        smOrderInsured.setPersonName("insuredName");
        smOrderInsured.setPersonGender("personGender");
        smOrderInsured.setIdType("certType");
        smOrderInsured.setIdNumber("certNo");
        smOrderInsured.setIdPeriodStart("idPeriodStart");
        smOrderInsured.setIdPeriodEnd("idPeriodEnd");
        smOrderInsured.setBirthday("birthday");
        smOrderInsured.setCellPhone("cellPhone");
        smOrderInsured.setEmail("email");
        smOrderInsured.setAnnualIncome("annualIncome");
        smOrderInsured.setFlightNo("flightNo");
        smOrderInsured.setPolicyNo("individualPolicyNo");
        when(mockInsuredMapper.selectByPrimaryKeyMustExists(0)).thenReturn(smOrderInsured);

        // Configure SmClaimMapper.listSmClaimFileUnitByClaimId(...).
        final SmClaimFileUnitVO smClaimFileUnitVO = new SmClaimFileUnitVO();
        smClaimFileUnitVO.setCfId(0);
        smClaimFileUnitVO.setClaimId(0);
        smClaimFileUnitVO.setFileTypeCode("attachmentType");
        smClaimFileUnitVO.setFileTypeName("attachmentName");
        smClaimFileUnitVO.setFileUrl("fileUrl");
        smClaimFileUnitVO.setNewFlag(0);
        smClaimFileUnitVO.setZaKey("attachmentKey");
        final List<SmClaimFileUnitVO> smClaimFileUnitVOS = Arrays.asList(smClaimFileUnitVO);
        when(mockClaimMapper.listSmClaimFileUnitByClaimId(0)).thenReturn(smClaimFileUnitVOS);

        // Configure ZaApiService.uploadFile(...).
        final FileUploadResponse fileUploadResponse = new FileUploadResponse();
        fileUploadResponse.setCode("code");
        fileUploadResponse.setData("data");
        fileUploadResponse.setMsg("msg");
        fileUploadResponse.setTimestamp("timestamp");
        fileUploadResponse.setSign("sign");
        when(mockZaApiService.uploadFile("pathname")).thenReturn(fileUploadResponse);

        when(mockClaimMapper.updateClaimFileZaKey(0, "data")).thenReturn(0);

        // Configure ZaApiService.claimReport(...).
        final ReportResponse reportResponse = new ReportResponse();
        reportResponse.setErrCode("errCode");
        reportResponse.setErrMsg("errMsg");
        reportResponse.setReportBizNo("reportBizNo");
        reportResponse.setChannelBatchNo("channelBatchNo");
        reportResponse.setChannelReportNo("channelReportNo");
        reportResponse.setSource("source");
        reportResponse.setReportNo("reportNo");
        reportResponse.setIndividualPolicyNo("individualPolicyNo");
        when(mockZaApiService.claimReport(new ZaRequestWrapper<>())).thenReturn(reportResponse);

        when(mockTransactionTemplate.execute(any(TransactionCallback.class))).thenReturn("result");
        when(mockClaimReimbursementZaMapper.updateZaReportNoByClaimId(0, "reportNo", LocalDateTime.now())).thenReturn(0);

        // Run the test
        zaClaimServiceUnderTest.saveProgress(dto);

    }

    @Test
    public void testSaveProgress_SmClaimMapperListSmClaimFileUnitByClaimIdReturnsNoItems() {
        // Setup
        final ProgressDTO dto = new ProgressDTO();
        dto.setId(0L);
        dto.setClaimId(0);
        dto.setSCode("claimState");
        dto.setSName("claimResult");
        dto.setOCode("oCode");
        dto.setOName("oName");
        dto.setOType("oType");
        dto.setOValue("oValue");
        dto.setDataJson("dataJson");
        dto.setOTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        dto.setExpressTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        dto.setSettlement("settlement");
        dto.setCreateBy("createBy");
        final ClaimEmailForm claimEmailForm = new ClaimEmailForm();
        claimEmailForm.setTheme("theme");
        dto.setClaimEmailForm(claimEmailForm);

        // Configure SmClaimReimbursementZaMapper.selectByClaimId(...).
        final SmClaimReimbursementZa reimbursementZa = new SmClaimReimbursementZa();
        reimbursementZa.setId(0);
        reimbursementZa.setClaimId(0);
        reimbursementZa.setAccidentType("accidentType");
        reimbursementZa.setReimbursementType("accidentType");
        reimbursementZa.setVisitingDate(LocalDate.of(2020, 1, 1));
        reimbursementZa.setVistingHospital("vistingHospital");
        reimbursementZa.setVistingHospitalName("vistingHospitalName");
        reimbursementZa.setRelationWithCasualty("insurantType");
        reimbursementZa.setApplierName("applierName");
        reimbursementZa.setApplierPhone("applierPhone");
        reimbursementZa.setApplierCertificateType("applierCertificateType");
        reimbursementZa.setApplierCertificateCode("applierCertificateCode");
        reimbursementZa.setApplierEmail("applierEmail");
        reimbursementZa.setPayeeWithCasualty("payeeRelation");
        reimbursementZa.setPayeeWay("payeeWay");
        reimbursementZa.setPayeeName("bankOpenName");
        reimbursementZa.setBankCard("bankAccountNo");
        reimbursementZa.setDepositBankCode("bankCode");
        reimbursementZa.setZfbAccount("zfbAccount");
        reimbursementZa.setDepositBankName("depositBankName");
        reimbursementZa.setPayeeBankDistrictCode("bankAreaCode");
        reimbursementZa.setPayeeBankDistrictName("bankAreaName");
        reimbursementZa.setPayeeBankBranchCode("bankBranchNameCode");
        reimbursementZa.setPayeeBankBranchName("bankBranchName");
        reimbursementZa.setCreateBy("createBy");
        reimbursementZa.setZaReportNo("reportNo");
        when(mockClaimReimbursementZaMapper.selectByClaimId(0)).thenReturn(reimbursementZa);

        // Configure ZaApiService.claimSearch(...).
        final ZaClaimQueryResponse zaClaimQueryResponse = new ZaClaimQueryResponse();
        zaClaimQueryResponse.setErrCode("errCode");
        zaClaimQueryResponse.setErrMsg("errMsg");
        zaClaimQueryResponse.setReportBizNo("reportBizNo");
        zaClaimQueryResponse.setReportNo("reportNo");
        zaClaimQueryResponse.setCurrentStatus("currentStatus");
        zaClaimQueryResponse.setPaidAmount("paidAmount");
        final ZaClaimQueryResponse.SubExtraInfo subExtraInfo = new ZaClaimQueryResponse.SubExtraInfo();
        subExtraInfo.setStatus("status");
        subExtraInfo.setReason("reason");
        subExtraInfo.setSubReason("subReason");
        subExtraInfo.setOperator("operator");
        subExtraInfo.setOperatorDate("operatorDate");
        subExtraInfo.setOperatorType("operatorType");
        zaClaimQueryResponse.setSubExtraList(Arrays.asList(subExtraInfo));
        zaClaimQueryResponse.setBackType("backType");
        zaClaimQueryResponse.setFallbackReason("fallbackReason");
        when(mockZaApiService.claimSearch(new ZaSearchRequestWrapper(new ZaSearchRequest()))).thenReturn(zaClaimQueryResponse);

        // Configure ClaimWorkflow.goToNextStep(...).
        final BaseWorkflow.Step step = new BaseWorkflow.Step();
        step.setSType(0);
        step.setSCode("claimState");
        step.setSName("claimResult");
        final BaseWorkflow.Option option = new BaseWorkflow.Option();
        option.setOCode("oCode");
        option.setOName("oName");
        option.setOType("oType");
        option.setOValue("oValue");
        option.setOTimeName("oTimeName");
        option.setNextStep("nextStep");
        step.setOptions(Arrays.asList(option));
        when(mockWorkflow.goToNextStep("claimState", "oCode")).thenReturn(step);

        // Configure SmClaimMapper.getLastClaimExpressByClaimId(...).
        final SmClaimExpressVO expressVO = new SmClaimExpressVO();
        expressVO.setCeId(0);
        expressVO.setClaimId(0);
        expressVO.setExpressDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        expressVO.setExpressCompanyName("expressCompanyName");
        expressVO.setExpressNo("expressNo");
        expressVO.setExpressUrlJoin("expressUrlJoin");
        expressVO.setExpressUrls(Arrays.asList("value"));
        when(mockClaimMapper.getLastClaimExpressByClaimId(0)).thenReturn(expressVO);

        // Configure ZaApiService.addExpressTask(...).
        final ZaExpressResponse zaExpressResponse = new ZaExpressResponse();
        zaExpressResponse.setErrCode("errCode");
        zaExpressResponse.setErrMsg("errMsg");
        when(mockZaApiService.addExpressTask(new ZaClaimExpressTaskWrapper(new ZaExpressTask()))).thenReturn(zaExpressResponse);

        // Configure UserService.getAuthUserByUserId(...).
        final AuthUserVO authUserVO = new AuthUserVO();
        authUserVO.setId(0);
        authUserVO.setRegionName("regionName");
        authUserVO.setRegionCode("regionCode");
        authUserVO.setOrgCode("orgCode");
        authUserVO.setOrganizationName("organizationName");
        authUserVO.setOrganizationFullName("organizationFullName");
        authUserVO.setUserId("userId");
        authUserVO.setAgentId(0);
        authUserVO.setAgentTopUserId("agentTopUserId");
        authUserVO.setUserName("userName");
        authUserVO.setUserMobile("userMobile");
        authUserVO.setWxNickName("wxNickName");
        authUserVO.setPostName("postName");
        authUserVO.setWxImgUrl("wxImgUrl");
        authUserVO.setHrOrgId(0);
        when(mockUserService.getAuthUserByUserId("jobNumber")).thenReturn(authUserVO);

        when(mockAuthUserMapper.getUserBizCodeByIdNumber("userName")).thenReturn("createBy");

        // Configure SmOrderInsuredMapper.selectByPrimaryKeyMustExists(...).
        final SmOrderInsured smOrderInsured = new SmOrderInsured();
        smOrderInsured.setId(0);
        smOrderInsured.setFhOrderId("fhOrderId");
        smOrderInsured.setRelationship("relationship");
        smOrderInsured.setPersonName("insuredName");
        smOrderInsured.setPersonGender("personGender");
        smOrderInsured.setIdType("certType");
        smOrderInsured.setIdNumber("certNo");
        smOrderInsured.setIdPeriodStart("idPeriodStart");
        smOrderInsured.setIdPeriodEnd("idPeriodEnd");
        smOrderInsured.setBirthday("birthday");
        smOrderInsured.setCellPhone("cellPhone");
        smOrderInsured.setEmail("email");
        smOrderInsured.setAnnualIncome("annualIncome");
        smOrderInsured.setFlightNo("flightNo");
        smOrderInsured.setPolicyNo("individualPolicyNo");
        when(mockInsuredMapper.selectByPrimaryKeyMustExists(0)).thenReturn(smOrderInsured);

        when(mockClaimMapper.listSmClaimFileUnitByClaimId(0)).thenReturn(Collections.emptyList());

        // Configure ZaApiService.uploadFile(...).
        final FileUploadResponse fileUploadResponse = new FileUploadResponse();
        fileUploadResponse.setCode("code");
        fileUploadResponse.setData("data");
        fileUploadResponse.setMsg("msg");
        fileUploadResponse.setTimestamp("timestamp");
        fileUploadResponse.setSign("sign");
        when(mockZaApiService.uploadFile("pathname")).thenReturn(fileUploadResponse);

        when(mockClaimMapper.updateClaimFileZaKey(0, "data")).thenReturn(0);

        // Configure ZaApiService.claimReport(...).
        final ReportResponse reportResponse = new ReportResponse();
        reportResponse.setErrCode("errCode");
        reportResponse.setErrMsg("errMsg");
        reportResponse.setReportBizNo("reportBizNo");
        reportResponse.setChannelBatchNo("channelBatchNo");
        reportResponse.setChannelReportNo("channelReportNo");
        reportResponse.setSource("source");
        reportResponse.setReportNo("reportNo");
        reportResponse.setIndividualPolicyNo("individualPolicyNo");
        when(mockZaApiService.claimReport(new ZaRequestWrapper<>())).thenReturn(reportResponse);

        when(mockTransactionTemplate.execute(any(TransactionCallback.class))).thenReturn("result");
        when(mockClaimReimbursementZaMapper.updateZaReportNoByClaimId(0, "reportNo", LocalDateTime.now())).thenReturn(0);

        // Run the test
        zaClaimServiceUnderTest.saveProgress(dto);

    }

    @Test
    public void testSaveProgress_TransactionTemplateThrowsTransactionException() {
        // Setup
        final ProgressDTO dto = new ProgressDTO();
        dto.setId(0L);
        dto.setClaimId(0);
        dto.setSCode("claimState");
        dto.setSName("claimResult");
        dto.setOCode("oCode");
        dto.setOName("oName");
        dto.setOType("oType");
        dto.setOValue("oValue");
        dto.setDataJson("dataJson");
        dto.setOTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        dto.setExpressTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        dto.setSettlement("settlement");
        dto.setCreateBy("createBy");
        final ClaimEmailForm claimEmailForm = new ClaimEmailForm();
        claimEmailForm.setTheme("theme");
        dto.setClaimEmailForm(claimEmailForm);

        // Configure SmClaimReimbursementZaMapper.selectByClaimId(...).
        final SmClaimReimbursementZa reimbursementZa = new SmClaimReimbursementZa();
        reimbursementZa.setId(0);
        reimbursementZa.setClaimId(0);
        reimbursementZa.setAccidentType("accidentType");
        reimbursementZa.setReimbursementType("accidentType");
        reimbursementZa.setVisitingDate(LocalDate.of(2020, 1, 1));
        reimbursementZa.setVistingHospital("vistingHospital");
        reimbursementZa.setVistingHospitalName("vistingHospitalName");
        reimbursementZa.setRelationWithCasualty("insurantType");
        reimbursementZa.setApplierName("applierName");
        reimbursementZa.setApplierPhone("applierPhone");
        reimbursementZa.setApplierCertificateType("applierCertificateType");
        reimbursementZa.setApplierCertificateCode("applierCertificateCode");
        reimbursementZa.setApplierEmail("applierEmail");
        reimbursementZa.setPayeeWithCasualty("payeeRelation");
        reimbursementZa.setPayeeWay("payeeWay");
        reimbursementZa.setPayeeName("bankOpenName");
        reimbursementZa.setBankCard("bankAccountNo");
        reimbursementZa.setDepositBankCode("bankCode");
        reimbursementZa.setZfbAccount("zfbAccount");
        reimbursementZa.setDepositBankName("depositBankName");
        reimbursementZa.setPayeeBankDistrictCode("bankAreaCode");
        reimbursementZa.setPayeeBankDistrictName("bankAreaName");
        reimbursementZa.setPayeeBankBranchCode("bankBranchNameCode");
        reimbursementZa.setPayeeBankBranchName("bankBranchName");
        reimbursementZa.setCreateBy("createBy");
        reimbursementZa.setZaReportNo("reportNo");
        when(mockClaimReimbursementZaMapper.selectByClaimId(0)).thenReturn(reimbursementZa);

        // Configure ZaApiService.claimSearch(...).
        final ZaClaimQueryResponse zaClaimQueryResponse = new ZaClaimQueryResponse();
        zaClaimQueryResponse.setErrCode("errCode");
        zaClaimQueryResponse.setErrMsg("errMsg");
        zaClaimQueryResponse.setReportBizNo("reportBizNo");
        zaClaimQueryResponse.setReportNo("reportNo");
        zaClaimQueryResponse.setCurrentStatus("currentStatus");
        zaClaimQueryResponse.setPaidAmount("paidAmount");
        final ZaClaimQueryResponse.SubExtraInfo subExtraInfo = new ZaClaimQueryResponse.SubExtraInfo();
        subExtraInfo.setStatus("status");
        subExtraInfo.setReason("reason");
        subExtraInfo.setSubReason("subReason");
        subExtraInfo.setOperator("operator");
        subExtraInfo.setOperatorDate("operatorDate");
        subExtraInfo.setOperatorType("operatorType");
        zaClaimQueryResponse.setSubExtraList(Arrays.asList(subExtraInfo));
        zaClaimQueryResponse.setBackType("backType");
        zaClaimQueryResponse.setFallbackReason("fallbackReason");
        when(mockZaApiService.claimSearch(new ZaSearchRequestWrapper(new ZaSearchRequest()))).thenReturn(zaClaimQueryResponse);

        // Configure ClaimWorkflow.goToNextStep(...).
        final BaseWorkflow.Step step = new BaseWorkflow.Step();
        step.setSType(0);
        step.setSCode("claimState");
        step.setSName("claimResult");
        final BaseWorkflow.Option option = new BaseWorkflow.Option();
        option.setOCode("oCode");
        option.setOName("oName");
        option.setOType("oType");
        option.setOValue("oValue");
        option.setOTimeName("oTimeName");
        option.setNextStep("nextStep");
        step.setOptions(Arrays.asList(option));
        when(mockWorkflow.goToNextStep("claimState", "oCode")).thenReturn(step);

        // Configure SmClaimMapper.getLastClaimExpressByClaimId(...).
        final SmClaimExpressVO expressVO = new SmClaimExpressVO();
        expressVO.setCeId(0);
        expressVO.setClaimId(0);
        expressVO.setExpressDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        expressVO.setExpressCompanyName("expressCompanyName");
        expressVO.setExpressNo("expressNo");
        expressVO.setExpressUrlJoin("expressUrlJoin");
        expressVO.setExpressUrls(Arrays.asList("value"));
        when(mockClaimMapper.getLastClaimExpressByClaimId(0)).thenReturn(expressVO);

        // Configure ZaApiService.addExpressTask(...).
        final ZaExpressResponse zaExpressResponse = new ZaExpressResponse();
        zaExpressResponse.setErrCode("errCode");
        zaExpressResponse.setErrMsg("errMsg");
        when(mockZaApiService.addExpressTask(new ZaClaimExpressTaskWrapper(new ZaExpressTask()))).thenReturn(zaExpressResponse);

        // Configure UserService.getAuthUserByUserId(...).
        final AuthUserVO authUserVO = new AuthUserVO();
        authUserVO.setId(0);
        authUserVO.setRegionName("regionName");
        authUserVO.setRegionCode("regionCode");
        authUserVO.setOrgCode("orgCode");
        authUserVO.setOrganizationName("organizationName");
        authUserVO.setOrganizationFullName("organizationFullName");
        authUserVO.setUserId("userId");
        authUserVO.setAgentId(0);
        authUserVO.setAgentTopUserId("agentTopUserId");
        authUserVO.setUserName("userName");
        authUserVO.setUserMobile("userMobile");
        authUserVO.setWxNickName("wxNickName");
        authUserVO.setPostName("postName");
        authUserVO.setWxImgUrl("wxImgUrl");
        authUserVO.setHrOrgId(0);
        when(mockUserService.getAuthUserByUserId("jobNumber")).thenReturn(authUserVO);

        when(mockAuthUserMapper.getUserBizCodeByIdNumber("userName")).thenReturn("createBy");

        // Configure SmOrderInsuredMapper.selectByPrimaryKeyMustExists(...).
        final SmOrderInsured smOrderInsured = new SmOrderInsured();
        smOrderInsured.setId(0);
        smOrderInsured.setFhOrderId("fhOrderId");
        smOrderInsured.setRelationship("relationship");
        smOrderInsured.setPersonName("insuredName");
        smOrderInsured.setPersonGender("personGender");
        smOrderInsured.setIdType("certType");
        smOrderInsured.setIdNumber("certNo");
        smOrderInsured.setIdPeriodStart("idPeriodStart");
        smOrderInsured.setIdPeriodEnd("idPeriodEnd");
        smOrderInsured.setBirthday("birthday");
        smOrderInsured.setCellPhone("cellPhone");
        smOrderInsured.setEmail("email");
        smOrderInsured.setAnnualIncome("annualIncome");
        smOrderInsured.setFlightNo("flightNo");
        smOrderInsured.setPolicyNo("individualPolicyNo");
        when(mockInsuredMapper.selectByPrimaryKeyMustExists(0)).thenReturn(smOrderInsured);

        // Configure SmClaimMapper.listSmClaimFileUnitByClaimId(...).
        final SmClaimFileUnitVO smClaimFileUnitVO = new SmClaimFileUnitVO();
        smClaimFileUnitVO.setCfId(0);
        smClaimFileUnitVO.setClaimId(0);
        smClaimFileUnitVO.setFileTypeCode("attachmentType");
        smClaimFileUnitVO.setFileTypeName("attachmentName");
        smClaimFileUnitVO.setFileUrl("fileUrl");
        smClaimFileUnitVO.setNewFlag(0);
        smClaimFileUnitVO.setZaKey("attachmentKey");
        final List<SmClaimFileUnitVO> smClaimFileUnitVOS = Arrays.asList(smClaimFileUnitVO);
        when(mockClaimMapper.listSmClaimFileUnitByClaimId(0)).thenReturn(smClaimFileUnitVOS);

        // Configure ZaApiService.uploadFile(...).
        final FileUploadResponse fileUploadResponse = new FileUploadResponse();
        fileUploadResponse.setCode("code");
        fileUploadResponse.setData("data");
        fileUploadResponse.setMsg("msg");
        fileUploadResponse.setTimestamp("timestamp");
        fileUploadResponse.setSign("sign");
        when(mockZaApiService.uploadFile("pathname")).thenReturn(fileUploadResponse);

        when(mockClaimMapper.updateClaimFileZaKey(0, "data")).thenReturn(0);

        // Configure ZaApiService.claimReport(...).
        final ReportResponse reportResponse = new ReportResponse();
        reportResponse.setErrCode("errCode");
        reportResponse.setErrMsg("errMsg");
        reportResponse.setReportBizNo("reportBizNo");
        reportResponse.setChannelBatchNo("channelBatchNo");
        reportResponse.setChannelReportNo("channelReportNo");
        reportResponse.setSource("source");
        reportResponse.setReportNo("reportNo");
        reportResponse.setIndividualPolicyNo("individualPolicyNo");
        when(mockZaApiService.claimReport(new ZaRequestWrapper<>())).thenReturn(reportResponse);


        try {
            when(mockClaimReimbursementZaMapper.updateZaReportNoByClaimId(0, "reportNo", LocalDateTime.now())).thenReturn(0);
            // Run the test
            zaClaimServiceUnderTest.saveProgress(dto);
        } catch (TransactionException e) {
        }
    }

    @Test
    public void testGetSmClaimFileCombByClaimId() {
        // Setup
        final SmClaimFileCombVO smClaimFileCombVO = new SmClaimFileCombVO();
        smClaimFileCombVO.setClaimId(0);
        smClaimFileCombVO.setFileTypeCode("fileTypeCode");
        smClaimFileCombVO.setFileTypeName("name");
        smClaimFileCombVO.setFileRequire(false);
        smClaimFileCombVO.setFileUrls(Arrays.asList("value"));
        final ClaimFileSimpleVo claimFileSimpleVo = new ClaimFileSimpleVo();
        claimFileSimpleVo.setCfId(0);
        claimFileSimpleVo.setFileUrl("fileUrl");
        claimFileSimpleVo.setNewFlag(0);
        claimFileSimpleVo.setFileTypeCode("attachmentType");
        smClaimFileCombVO.setFileSimpleVoList(Arrays.asList(claimFileSimpleVo));
        smClaimFileCombVO.setExampleFileUrlList(Arrays.asList("value"));
        smClaimFileCombVO.setExampleFileUrlDescription("exampleFileUrlDescription");
        final List<SmClaimFileCombVO> expectedResult = Arrays.asList(smClaimFileCombVO);

        // Configure SmClaimMapper.listSmClaimFileUnitByClaimId(...).
        final SmClaimFileUnitVO smClaimFileUnitVO = new SmClaimFileUnitVO();
        smClaimFileUnitVO.setCfId(0);
        smClaimFileUnitVO.setClaimId(0);
        smClaimFileUnitVO.setFileTypeCode("attachmentType");
        smClaimFileUnitVO.setFileTypeName("attachmentName");
        smClaimFileUnitVO.setFileUrl("fileUrl");
        smClaimFileUnitVO.setNewFlag(0);
        smClaimFileUnitVO.setZaKey("attachmentKey");
        final List<SmClaimFileUnitVO> smClaimFileUnitVOS = Arrays.asList(smClaimFileUnitVO);
        when(mockClaimMapper.listSmClaimFileUnitByClaimId(0)).thenReturn(smClaimFileUnitVOS);

        // Configure SmClaimMapper.getByClaimId(...).
        final SmClaim claim = new SmClaim();
        claim.setId(0);
        claim.setClaimNo("claimNo");
        claim.setInsuredId(0);
        claim.setRiskType("riskType");
        claim.setPayMoney(new BigDecimal("0.00"));
        claim.setRiskTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        claim.setRiskDesc("riskDesc");
        claim.setDatatTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        claim.setClaimState("claimState");
        claim.setClaimResult("claimResult");
        claim.setFinishState("finishState");
        claim.setFinishResult("finishResult");
        claim.setFinishtime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        claim.setNote("note");
        claim.setEstimatedAmount(new BigDecimal("0.00"));
        when(mockClaimMapper.getByClaimId(0)).thenReturn(claim);

        // Configure SmOrderInsuredMapper.selectByPrimaryKeyMustExists(...).
        final SmOrderInsured smOrderInsured = new SmOrderInsured();
        smOrderInsured.setId(0);
        smOrderInsured.setFhOrderId("fhOrderId");
        smOrderInsured.setRelationship("relationship");
        smOrderInsured.setPersonName("insuredName");
        smOrderInsured.setPersonGender("personGender");
        smOrderInsured.setIdType("certType");
        smOrderInsured.setIdNumber("certNo");
        smOrderInsured.setIdPeriodStart("idPeriodStart");
        smOrderInsured.setIdPeriodEnd("idPeriodEnd");
        smOrderInsured.setBirthday("birthday");
        smOrderInsured.setCellPhone("cellPhone");
        smOrderInsured.setEmail("email");
        smOrderInsured.setAnnualIncome("annualIncome");
        smOrderInsured.setFlightNo("flightNo");
        smOrderInsured.setPolicyNo("individualPolicyNo");
        when(mockInsuredMapper.selectByPrimaryKeyMustExists(0)).thenReturn(smOrderInsured);

        // Configure SmClaimReimbursementZaMapper.selectByClaimId(...).
        final SmClaimReimbursementZa reimbursementZa = new SmClaimReimbursementZa();
        reimbursementZa.setId(0);
        reimbursementZa.setClaimId(0);
        reimbursementZa.setAccidentType("accidentType");
        reimbursementZa.setReimbursementType("accidentType");
        reimbursementZa.setVisitingDate(LocalDate.of(2020, 1, 1));
        reimbursementZa.setVistingHospital("vistingHospital");
        reimbursementZa.setVistingHospitalName("vistingHospitalName");
        reimbursementZa.setRelationWithCasualty("insurantType");
        reimbursementZa.setApplierName("applierName");
        reimbursementZa.setApplierPhone("applierPhone");
        reimbursementZa.setApplierCertificateType("applierCertificateType");
        reimbursementZa.setApplierCertificateCode("applierCertificateCode");
        reimbursementZa.setApplierEmail("applierEmail");
        reimbursementZa.setPayeeWithCasualty("payeeRelation");
        reimbursementZa.setPayeeWay("payeeWay");
        reimbursementZa.setPayeeName("bankOpenName");
        reimbursementZa.setBankCard("bankAccountNo");
        reimbursementZa.setDepositBankCode("bankCode");
        reimbursementZa.setZfbAccount("zfbAccount");
        reimbursementZa.setDepositBankName("depositBankName");
        reimbursementZa.setPayeeBankDistrictCode("bankAreaCode");
        reimbursementZa.setPayeeBankDistrictName("bankAreaName");
        reimbursementZa.setPayeeBankBranchCode("bankBranchNameCode");
        reimbursementZa.setPayeeBankBranchName("bankBranchName");
        reimbursementZa.setCreateBy("createBy");
        reimbursementZa.setZaReportNo("reportNo");
        when(mockClaimReimbursementZaMapper.selectByClaimId(0)).thenReturn(reimbursementZa);

        // Run the test
        final List<SmClaimFileCombVO> result = zaClaimServiceUnderTest.getSmClaimFileCombByClaimId(0);

        // Verify the results
    }

    @Test
    public void testGetSmClaimFileCombByClaimId_SmClaimMapperListSmClaimFileUnitByClaimIdReturnsNoItems() {
        // Setup
        when(mockClaimMapper.listSmClaimFileUnitByClaimId(0)).thenReturn(Collections.emptyList());

        // Configure SmClaimMapper.getByClaimId(...).
        final SmClaim claim = new SmClaim();
        claim.setId(0);
        claim.setClaimNo("claimNo");
        claim.setInsuredId(0);
        claim.setRiskType("riskType");
        claim.setPayMoney(new BigDecimal("0.00"));
        claim.setRiskTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        claim.setRiskDesc("riskDesc");
        claim.setDatatTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        claim.setClaimState("claimState");
        claim.setClaimResult("claimResult");
        claim.setFinishState("finishState");
        claim.setFinishResult("finishResult");
        claim.setFinishtime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        claim.setNote("note");
        claim.setEstimatedAmount(new BigDecimal("0.00"));
        when(mockClaimMapper.getByClaimId(0)).thenReturn(claim);

        // Configure SmOrderInsuredMapper.selectByPrimaryKeyMustExists(...).
        final SmOrderInsured smOrderInsured = new SmOrderInsured();
        smOrderInsured.setId(0);
        smOrderInsured.setFhOrderId("fhOrderId");
        smOrderInsured.setRelationship("relationship");
        smOrderInsured.setPersonName("insuredName");
        smOrderInsured.setPersonGender("personGender");
        smOrderInsured.setIdType("certType");
        smOrderInsured.setIdNumber("certNo");
        smOrderInsured.setIdPeriodStart("idPeriodStart");
        smOrderInsured.setIdPeriodEnd("idPeriodEnd");
        smOrderInsured.setBirthday("birthday");
        smOrderInsured.setCellPhone("cellPhone");
        smOrderInsured.setEmail("email");
        smOrderInsured.setAnnualIncome("annualIncome");
        smOrderInsured.setFlightNo("flightNo");
        smOrderInsured.setPolicyNo("individualPolicyNo");
        when(mockInsuredMapper.selectByPrimaryKeyMustExists(0)).thenReturn(smOrderInsured);

        // Configure SmClaimReimbursementZaMapper.selectByClaimId(...).
        final SmClaimReimbursementZa reimbursementZa = new SmClaimReimbursementZa();
        reimbursementZa.setId(0);
        reimbursementZa.setClaimId(0);
        reimbursementZa.setAccidentType("accidentType");
        reimbursementZa.setReimbursementType("accidentType");
        reimbursementZa.setVisitingDate(LocalDate.of(2020, 1, 1));
        reimbursementZa.setVistingHospital("vistingHospital");
        reimbursementZa.setVistingHospitalName("vistingHospitalName");
        reimbursementZa.setRelationWithCasualty("insurantType");
        reimbursementZa.setApplierName("applierName");
        reimbursementZa.setApplierPhone("applierPhone");
        reimbursementZa.setApplierCertificateType("applierCertificateType");
        reimbursementZa.setApplierCertificateCode("applierCertificateCode");
        reimbursementZa.setApplierEmail("applierEmail");
        reimbursementZa.setPayeeWithCasualty("payeeRelation");
        reimbursementZa.setPayeeWay("payeeWay");
        reimbursementZa.setPayeeName("bankOpenName");
        reimbursementZa.setBankCard("bankAccountNo");
        reimbursementZa.setDepositBankCode("bankCode");
        reimbursementZa.setZfbAccount("zfbAccount");
        reimbursementZa.setDepositBankName("depositBankName");
        reimbursementZa.setPayeeBankDistrictCode("bankAreaCode");
        reimbursementZa.setPayeeBankDistrictName("bankAreaName");
        reimbursementZa.setPayeeBankBranchCode("bankBranchNameCode");
        reimbursementZa.setPayeeBankBranchName("bankBranchName");
        reimbursementZa.setCreateBy("createBy");
        reimbursementZa.setZaReportNo("reportNo");
        when(mockClaimReimbursementZaMapper.selectByClaimId(0)).thenReturn(reimbursementZa);

        // Run the test
        final List<SmClaimFileCombVO> result = zaClaimServiceUnderTest.getSmClaimFileCombByClaimId(0);

        // Verify the results
    }

    @Test
    public void testQueryHeadBank() {
        // Setup
        final ZaBankInfoVO zaBankInfoVO = new ZaBankInfoVO();
        zaBankInfoVO.setBankCode("bankCode");
        zaBankInfoVO.setBankCodeName("bankCodeName");
        final List<ZaBankInfoVO> expectedResult = Arrays.asList(zaBankInfoVO);

        // Configure SmClaimBankZaMapper.selectHeadBank(...).
        final ZaBankInfoVO zaBankInfoVO1 = new ZaBankInfoVO();
        zaBankInfoVO1.setBankCode("bankCode");
        zaBankInfoVO1.setBankCodeName("bankCodeName");
        final List<ZaBankInfoVO> zaBankInfoVOS = Arrays.asList(zaBankInfoVO1);
        when(mockClaimBankZaMapper.selectHeadBank("")).thenReturn(zaBankInfoVOS);

        // Run the test
        final List<ZaBankInfoVO> result = zaClaimServiceUnderTest.queryHeadBank("");

        // Verify the results
    }

    @Test
    public void testQueryHeadBank_SmClaimBankZaMapperReturnsNoItems() {
        // Setup
        when(mockClaimBankZaMapper.selectHeadBank("")).thenReturn(Collections.emptyList());

        // Run the test
        final List<ZaBankInfoVO> result = zaClaimServiceUnderTest.queryHeadBank("");

        // Verify the results
    }

    @Test
    public void testQueryBranchBank() {
        // Setup
        final ZaClaimQuery claimQuery = new ZaClaimQuery();
        claimQuery.setDistrictCode("districtCode");
        claimQuery.setBankCode("bankCode");

        final ZaBankInfoVO zaBankInfoVO = new ZaBankInfoVO();
        zaBankInfoVO.setBankCode("bankCode");
        zaBankInfoVO.setBankCodeName("bankCodeName");
        final List<ZaBankInfoVO> expectedResult = Arrays.asList(zaBankInfoVO);

        // Configure SmClaimBankZaMapper.selectBranchBank(...).
        final ZaBankInfoVO zaBankInfoVO1 = new ZaBankInfoVO();
        zaBankInfoVO1.setBankCode("bankCode");
        zaBankInfoVO1.setBankCodeName("bankCodeName");
        final List<ZaBankInfoVO> zaBankInfoVOS = Arrays.asList(zaBankInfoVO1);
        when(mockClaimBankZaMapper.selectBranchBank(new ZaClaimQuery())).thenReturn(zaBankInfoVOS);

        // Run the test
        final List<ZaBankInfoVO> result = zaClaimServiceUnderTest.queryBranchBank(claimQuery);

        // Verify the results
    }

    @Test
    public void testQueryBranchBank_SmClaimBankZaMapperReturnsNoItems() {
        // Setup
        final ZaClaimQuery claimQuery = new ZaClaimQuery();
        claimQuery.setDistrictCode("districtCode");
        claimQuery.setBankCode("bankCode");

        when(mockClaimBankZaMapper.selectBranchBank(new ZaClaimQuery())).thenReturn(Collections.emptyList());

        // Run the test
        final List<ZaBankInfoVO> result = zaClaimServiceUnderTest.queryBranchBank(claimQuery);

        // Verify the results
    }


    @Test
    public void testHandlerZaReportStatusCallBack() throws Exception {
        // Setup
        final HttpServletRequest request = new MockHttpServletRequest();
        final HttpServletResponse response = new MockHttpServletResponse();

        // Configure ObjectMapper.readValue(...).
        final ZaReportStatusReceive reportStatusReceive = new ZaReportStatusReceive();
        reportStatusReceive.setIndividualPolicyNo("individualPolicyNo");
        reportStatusReceive.setGroupPolicyNo("groupPolicyNo");
        reportStatusReceive.setReportBizNo("reportBizNo");
        reportStatusReceive.setReportNo("reportNo");
        reportStatusReceive.setSource("source");
        reportStatusReceive.setOriginalStatus("originalStatus");
        reportStatusReceive.setTargetStatus("currentStatus");
        reportStatusReceive.setMessageId("messageId");
        reportStatusReceive.setChangeDate("changeDate");
        reportStatusReceive.setFallbackType("backType");
        reportStatusReceive.setFallbackReason("fallbackReason");
        when(mockJsonMapper.readValue("content", ZaReportStatusReceive.class)).thenReturn(reportStatusReceive);

        when(mockClaimCompanyStatusZaMapper.existedByMessageId("messageId")).thenReturn(false);
        when(mockClaimCompanyStatusZaMapper.insertSelective(new SmClaimCompanyStatusZa())).thenReturn(0);
        when(mockLockTemplate.lock("messageId", 0L)).thenReturn(false);

        // Configure SmClaimMapper.getByClaimNo(...).
        final SmClaim claim = new SmClaim();
        claim.setId(0);
        claim.setClaimNo("claimNo");
        claim.setInsuredId(0);
        claim.setRiskType("riskType");
        claim.setPayMoney(new BigDecimal("0.00"));
        claim.setRiskTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        claim.setRiskDesc("riskDesc");
        claim.setDatatTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        claim.setClaimState("claimState");
        claim.setClaimResult("claimResult");
        claim.setFinishState("finishState");
        claim.setFinishResult("finishResult");
        claim.setFinishtime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        claim.setNote("note");
        claim.setEstimatedAmount(new BigDecimal("0.00"));
        when(mockClaimMapper.getByClaimNo("reportBizNo")).thenReturn(claim);

        // Run the test
        zaClaimServiceUnderTest.handlerZaReportStatusCallBack(request, response);

    }

    @Test
    public void testHandlerZaReportStatusCallBack_ObjectMapperThrowsIOException() throws Exception {
        // Setup
        final HttpServletRequest request = new MockHttpServletRequest();
        final HttpServletResponse response = new MockHttpServletResponse();
        when(mockJsonMapper.readValue("content", ZaReportStatusReceive.class)).thenThrow(IOException.class);
        when(mockClaimCompanyStatusZaMapper.existedByMessageId("messageId")).thenReturn(false);
        when(mockClaimCompanyStatusZaMapper.insertSelective(new SmClaimCompanyStatusZa())).thenReturn(0);
        when(mockLockTemplate.lock("messageId", 0L)).thenReturn(false);

        // Configure SmClaimMapper.getByClaimNo(...).
        final SmClaim claim = new SmClaim();
        claim.setId(0);
        claim.setClaimNo("claimNo");
        claim.setInsuredId(0);
        claim.setRiskType("riskType");
        claim.setPayMoney(new BigDecimal("0.00"));
        claim.setRiskTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        claim.setRiskDesc("riskDesc");
        claim.setDatatTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        claim.setClaimState("claimState");
        claim.setClaimResult("claimResult");
        claim.setFinishState("finishState");
        claim.setFinishResult("finishResult");
        claim.setFinishtime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        claim.setNote("note");
        claim.setEstimatedAmount(new BigDecimal("0.00"));
        when(mockClaimMapper.getByClaimNo("reportBizNo")).thenReturn(claim);

    }

    @Test
    public void testHandlerZaReportStatusCallBack_ObjectMapperThrowsJsonParseException() throws Exception {
        // Setup
        final HttpServletRequest request = new MockHttpServletRequest();
        final HttpServletResponse response = new MockHttpServletResponse();
        when(mockJsonMapper.readValue("content", ZaReportStatusReceive.class)).thenThrow(JsonParseException.class);
        when(mockClaimCompanyStatusZaMapper.existedByMessageId("messageId")).thenReturn(false);
        when(mockClaimCompanyStatusZaMapper.insertSelective(new SmClaimCompanyStatusZa())).thenReturn(0);
        when(mockLockTemplate.lock("messageId", 0L)).thenReturn(false);

        // Configure SmClaimMapper.getByClaimNo(...).
        final SmClaim claim = new SmClaim();
        claim.setId(0);
        claim.setClaimNo("claimNo");
        claim.setInsuredId(0);
        claim.setRiskType("riskType");
        claim.setPayMoney(new BigDecimal("0.00"));
        claim.setRiskTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        claim.setRiskDesc("riskDesc");
        claim.setDatatTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        claim.setClaimState("claimState");
        claim.setClaimResult("claimResult");
        claim.setFinishState("finishState");
        claim.setFinishResult("finishResult");
        claim.setFinishtime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        claim.setNote("note");
        claim.setEstimatedAmount(new BigDecimal("0.00"));
        when(mockClaimMapper.getByClaimNo("reportBizNo")).thenReturn(claim);

    }

    @Test
    public void testHandlerZaReportStatusCallBack_ObjectMapperThrowsJsonMappingException() throws Exception {
        // Setup
        final HttpServletRequest request = new MockHttpServletRequest();
        final HttpServletResponse response = new MockHttpServletResponse();
        when(mockJsonMapper.readValue("content", ZaReportStatusReceive.class)).thenThrow(JsonMappingException.class);
        when(mockClaimCompanyStatusZaMapper.existedByMessageId("messageId")).thenReturn(false);
        when(mockClaimCompanyStatusZaMapper.insertSelective(new SmClaimCompanyStatusZa())).thenReturn(0);
        when(mockLockTemplate.lock("messageId", 0L)).thenReturn(false);

        // Configure SmClaimMapper.getByClaimNo(...).
        final SmClaim claim = new SmClaim();
        claim.setId(0);
        claim.setClaimNo("claimNo");
        claim.setInsuredId(0);
        claim.setRiskType("riskType");
        claim.setPayMoney(new BigDecimal("0.00"));
        claim.setRiskTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        claim.setRiskDesc("riskDesc");
        claim.setDatatTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        claim.setClaimState("claimState");
        claim.setClaimResult("claimResult");
        claim.setFinishState("finishState");
        claim.setFinishResult("finishResult");
        claim.setFinishtime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        claim.setNote("note");
        claim.setEstimatedAmount(new BigDecimal("0.00"));
        when(mockClaimMapper.getByClaimNo("reportBizNo")).thenReturn(claim);

    }


}
