package com.cfpamf.ms.insur.admin.service.order;

import com.cfpamf.ms.insur.admin.dao.safes.order.SmOrderShareMapper;
import com.cfpamf.ms.insur.admin.pojo.po.order.SmOrderShare;
import com.cfpamf.ms.insur.admin.service.BaseTest;
import org.junit.Before;
import org.junit.Test;

import static org.mockito.Mockito.*;

public class SmOrderShareServiceTest extends BaseTest {

    private SmOrderShareService smOrderShareServiceUnderTest;

    @Before
    public void setUp() throws Exception {
        smOrderShareServiceUnderTest = new SmOrderShareService();
        smOrderShareServiceUnderTest.shareMapper = mock(SmOrderShareMapper.class);
    }

    @Test
    public void testGetShareByOrderId() {
        // Setup
        final SmOrderShare expectedResult = new SmOrderShare();
        expectedResult.setFhOrderId("fhOrderId");
        expectedResult.setFirstJobNumber("firstJobNumber");
        expectedResult.setFirstIdNumber("firstIdNumber");
        expectedResult.setShareIdNumber("shareIdNumber");
        expectedResult.setShareName("shareName");

        // Configure SmOrderShareMapper.selectByFhOrderId(...).
        final SmOrderShare smOrderShare = new SmOrderShare();
        smOrderShare.setFhOrderId("fhOrderId");
        smOrderShare.setFirstJobNumber("firstJobNumber");
        smOrderShare.setFirstIdNumber("firstIdNumber");
        smOrderShare.setShareIdNumber("shareIdNumber");
        smOrderShare.setShareName("shareName");
        when(smOrderShareServiceUnderTest.shareMapper.selectByFhOrderId("orderId")).thenReturn(smOrderShare);

        // Run the test
        final SmOrderShare result = smOrderShareServiceUnderTest.getShareByOrderId("orderId");

        // Verify the results
    }

    @Test
    public void testSaveShare() {
        // Setup
        final SmOrderShare share = new SmOrderShare();
        share.setFhOrderId("fhOrderId");
        share.setFirstJobNumber("firstJobNumber");
        share.setFirstIdNumber("firstIdNumber");
        share.setShareIdNumber("shareIdNumber");
        share.setShareName("shareName");

        // Run the test
        smOrderShareServiceUnderTest.saveShare(share);

        // Verify the results
        // Confirm SmOrderShareMapper.insertUseGeneratedKeys(...).
        final SmOrderShare record = new SmOrderShare();
        record.setFhOrderId("fhOrderId");
        record.setFirstJobNumber("firstJobNumber");
        record.setFirstIdNumber("firstIdNumber");
        record.setShareIdNumber("shareIdNumber");
        record.setShareName("shareName");
    }
}
