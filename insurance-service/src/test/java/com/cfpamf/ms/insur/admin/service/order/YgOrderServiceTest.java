package com.cfpamf.ms.insur.admin.service.order;

import com.cfpamf.ms.insur.admin.dao.safes.SmOrderMapper;
import com.cfpamf.ms.insur.admin.dao.safes.order.SmOrderRiskDutyMapper;
import com.cfpamf.ms.insur.admin.external.ygibao.YgOrderServiceAdapterImpl;
import com.cfpamf.ms.insur.admin.external.ygibao.model.YgPolicyContractInfoVo;
import com.cfpamf.ms.insur.admin.service.BaseTest;
import com.cfpamf.ms.insur.admin.service.SmProductService;
import com.cfpamf.ms.insur.base.util.HttpRequestUtil;
import com.github.jsonzou.jmockdata.JMockData;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.aop.framework.AopContext;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Collections;

/**
 * <AUTHOR> 2021/11/2 15:32
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({HttpRequestUtil.class, AopContext.class})
public class YgOrderServiceTest extends BaseTest {
    @InjectMocks
    YgOrderService ygOrderService;
    @org.mockito.Mock
    YgOrderServiceAdapterImpl adapter;
    @org.mockito.Mock
    com.fasterxml.jackson.databind.ObjectMapper mapper;
    @org.mockito.Mock
    com.cfpamf.ms.insur.admin.dao.safes.order.SmOrderExtendYgMapper extendMapper;
    @org.mockito.Mock
    com.cfpamf.ms.insur.admin.dao.safes.order.SmOrderDDDMapper orderDDDMapper;
    @org.mockito.Mock
    com.cfpamf.ms.insur.admin.service.SmXjxhService xjxhService;

    @Mock
    SmProductService productService;

    @Mock
    SmOrderRiskDutyMapper riskDutyMapper;

    @Mock
    SmOrderMapper orderMapper;

    @Override
    @Before
    public void setUp() throws Exception {
        super.setUp();
        PowerMockito.mockStatic(AopContext.class);
        PowerMockito.when(AopContext.currentProxy()).thenReturn(ygOrderService);
    }

    @Test
    public void support() {
        try {
            ygOrderService.support(",BsdQO,");
        } catch (Exception e) {

        }
    }

    @Test
    public void updateOrderPolicyInfo() {
        try {
            ygOrderService.updateOrderPolicyInfo(",AJYGE,");
        } catch (Exception e) {

        }
    }

    @Test
    public void submitOrder() {
        try {
            ygOrderService.submitOrder(",picXB,", JMockData.mock(com.cfpamf.ms.insur.admin.external.OrderSubmitRequest.class));
        } catch (Exception e) {

        }
    }

    @Test
    public void handAcceptSuccess() throws IOException {
        try {
            ygOrderService.handAcceptSuccess(new MockHttpServletRequest(), new MockHttpServletResponse());
        } catch (Exception e) {

        }
    }

    @Test
    public void handAcceptSuccessNotify() throws IOException {
        MockHttpServletRequest mockHttpServletRequest = new MockHttpServletRequest();
        String content = "{\"data\":\"{\\\"policySource\\\":\\\"POLICY_SOURCE:4\\\",\\\"policyNo\\\":\\\"8026900003516608\\\",\\\"serialNumber\\\":\\\"*******************\\\",\\\"companyCode\\\":\\\"YGRS000000\\\",\\\"companyName\\\":\\\"阳光人寿保险股份有限公司\\\",\\\"approvedTime\\\":\\\"2021-11-01 16:46:38\\\",\\\"enforceTime\\\":\\\"2021-11-02 00:00:00\\\",\\\"portfolioName\\\":\\\"i保·阳光橙两全保险\\\",\\\"customField\\\":\\\"\\\",\\\"policyApplicantInfo\\\":{\\\"applicantAddress\\\":\\\"北京市东城区和平里中街10号\\\",\\\"applicantIdCard\\\":\\\"220101197706010054\\\",\\\"applicantIdType\\\":\\\"POLICY_ID_TYPE:0\\\",\\\"applicantMobile\\\":\\\"***********\\\",\\\"applicantName\\\":\\\"老爸\\\",\\\"bankName\\\":\\\"工商银行\\\",\\\"cardNo\\\":\\\"6212260211234428000\\\",\\\"applicantBirthday\\\":\\\"1977-06-01\\\",\\\"applicantGender\\\":1},\\\"policyInsuredInfoList\\\":[{\\\"insuredAddress\\\":\\\"北京市东城区和平里中街10号\\\",\\\"insuredIdCard\\\":\\\"220101197706010054\\\",\\\"insuredIdType\\\":\\\"POLICY_ID_TYPE:0\\\",\\\"insuredMobile\\\":\\\"***********\\\",\\\"insuredName\\\":\\\"老爸\\\",\\\"insuredRelation\\\":\\\"POLICY_RELATION:ME\\\",\\\"insuredBirthday\\\":\\\"1977-06-01\\\",\\\"insuredGender\\\":1,\\\"policyProductInfoList\\\":[{\\\"premium\\\":49290,\\\"coverage\\\":1500000,\\\"mainInsurance\\\":1,\\\"periodType\\\":\\\"PERIOD_TYPE:1\\\",\\\"paymentPeriod\\\":10,\\\"paymentPeriodType\\\":\\\"PAYMENT_PERIOD_TYPE:1\\\",\\\"insuredPeriod\\\":70,\\\"insuredPeriodType\\\":\\\"INSURED_PERIOD_TYPE:3\\\",\\\"productCode\\\":\\\"AREE07~AREE07\\\",\\\"riskCode\\\":\\\"AREE07\\\",\\\"riskName\\\":\\\"i保·阳光橙两全保险\\\",\\\"policyBeneficiaryList\\\":[{\\\"beneficiarySubject\\\":\\\"BENEFICIARY_SUBJECT:0\\\",\\\"beneficiaryIdCardNumber\\\":\\\"220101199806010069\\\",\\\"beneficiaryIdCardType\\\":\\\"POLICY_ID_TYPE:0\\\",\\\"beneficiaryName\\\":\\\"老婆\\\",\\\"beneficiaryRelation\\\":\\\"POLICY_RELATION:SPOUSE\\\",\\\"beneficiaryBenefitOrder\\\":1,\\\"benefitRate\\\":100,\\\"beneficiaryBirthday\\\":\\\"1998-06-01\\\",\\\"beneficiaryGander\\\":1}]}]}]}\",\"apiSign\":\"30C7265C16BF08FD268B9DAC9ED76017\"}\n";
        mockHttpServletRequest.setContent(content.getBytes(StandardCharsets.UTF_8));
        mockHttpServletRequest.setContentType("application/json");
        YgPolicyContractInfoVo mock = JMockData.mock(YgPolicyContractInfoVo.class, con());
        Mockito.when(xjxhService.validAndParse(Mockito.any(), Mockito.any()))
                .thenReturn(mock);
        mock.getPolicyInsuredInfoList().get(0).getPolicyProductInfoList()
                .get(0).setMainInsurance(1);

        Mockito.when(extendMapper.select(Mockito.any()))
                .thenReturn(Collections.emptyList());
        Mockito.when(xjxhService.decodeXjxhRemark(Mockito.any()))
                .thenReturn(Collections.singletonMap("xjuid", "zhnx123-xiangzhu"));

        ygOrderService.handAcceptSuccess(mockHttpServletRequest, new MockHttpServletResponse());
    }

}

