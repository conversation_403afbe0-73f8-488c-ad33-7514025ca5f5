//package com.cfpamf.ms.insur.test;
//
//import com.cfpamf.ms.insur.admin.pojo.dto.order.SmOrderCarInfoDTO;
//import io.swagger.annotations.ApiModelProperty;
//import org.apache.commons.lang3.StringUtils;
//import org.junit.Test;
//
//import java.lang.reflect.Field;
//import java.util.HashMap;
//import java.util.Map;
//import java.util.Objects;
//import java.util.regex.Matcher;
//import java.util.regex.Pattern;
//import java.util.stream.Collectors;
//import java.util.stream.Stream;
//
///**
// * <AUTHOR> 2021/4/13 09:55
// */
//public class MapstructTest {
//
//    @Test
//    public void genMapper() {
//
//        Field[] declaredFields =
//                SmOrderCarInfoDTO.class.getDeclaredFields();
//
//        String ft = "  //${remark}\n" +
//                "                    @Mapping(source = \"${fieldName}\", target = \"${fieldName}\"),";
//
//        String str = Stream.of(declaredFields)
//                .map(field -> {
//                    Map<String, String> maps = new HashMap<>();
//                    maps.put("fieldName", field.getName());
//
//                    ApiModelProperty annotation = field.getAnnotation(ApiModelProperty.class);
//                    if (Objects.nonNull(annotation)) {
//                        maps.put("remark", annotation.value());
//                    }
//                    return strFormatUsingDict(ft, maps);
//                }).collect(Collectors.joining("\n"));
//        System.err.println(str);
//    }
//
//    public static String strFormatUsingDict(String template, Map<String, String> dict) {
//        String patternString = "\\$\\{(" + StringUtils.join(dict.keySet(), "|") + ")\\}";
//
//        Pattern pattern = Pattern.compile(patternString);
//        Matcher matcher = pattern.matcher(template);
//
//        StringBuffer sb = new StringBuffer();
//        while (matcher.find()) {
//            matcher.appendReplacement(sb, dict.get(matcher.group(1)));
//        }
//        matcher.appendTail(sb);
//        return sb.toString();
//    }
//}
