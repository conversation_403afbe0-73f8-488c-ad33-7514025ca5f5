package com.cfpamf.ms.insur.test;

import com.alibaba.fastjson.JSONObject;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;
import org.apache.commons.lang3.StringUtils;
import sun.misc.BASE64Decoder;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR> 2021/5/17 11:23
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class TestAutoGsc {

    public static void main(String[] args) throws Exception {
        String aesKey = "DZTB201908301724";
        String str = "https://hub.chinalife-p.com.cn/mescifp/sit/index.html?userCode=BoeBB20mJc54ukpcrwbhhNSpU06p14rExr1kTgsmyeQ%3D&bid=32000000&systemSource=JS0&userinfo={userInfo}&extparams={ext}";

        JSONObject json = new JSONObject()
                .fluentPut("userid", "ZHNX09760")
                .fluentPut("shareuserid", "");
        String user = URLEncoder.encode(encrypt(json.toJSONString(), aesKey),
                StandardCharsets.UTF_8.name());
        String extparams = "capp";

        HashMap<String, String> objectObjectHashMap = new HashMap<>();
        objectObjectHashMap.put("userInfo", user);
        objectObjectHashMap.put("ext", extparams);

        System.err.println(strFormatUsingDict(str, objectObjectHashMap));
    }

    public static String strFormatUsingDict(String template, Map<String, String> dict) {
        String patternString = "\\{(" + StringUtils.join(dict.keySet(), "|") + ")\\}";

        Pattern pattern = Pattern.compile(patternString);
        Matcher matcher = pattern.matcher(template);

        StringBuffer sb = new StringBuffer();
        while (matcher.find()) {
            matcher.appendReplacement(sb, dict.get(matcher.group(1)));
        }
        matcher.appendTail(sb);
        return sb.toString();
    }

    // 加密
    public static String encrypt(String sSrc, String sKey) throws Exception {
        if (sKey == null) {
            throw new IllegalArgumentException("Key为空null");
        }
        // 判断Key是否为16位
        if (sKey.length() != 16) {
            throw new IllegalArgumentException("Key长度不是16位");
        }
        byte[] raw = sKey.getBytes(StandardCharsets.UTF_8.name());
        SecretKeySpec skeySpec = new SecretKeySpec(raw, "AES");
        Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");//"算法/模式/补码方式"
        cipher.init(Cipher.ENCRYPT_MODE, skeySpec);
        byte[] encrypted = cipher.doFinal(sSrc.getBytes(StandardCharsets.UTF_8.name()));
        System.err.println(new String(encrypted));
        return Base64.getEncoder().encodeToString(encrypted);//此处使用BASE64做转码功能，同时能起到2次加密的作用。
    }

    // 解密
    public static String Decrypt(String sSrc, String sKey) throws Exception {
        try {
            // 判断Key是否正确
            if (sKey == null) {
                System.out.print("Key为空null");
                return null;
            }
            // 判断Key是否为16位
            if (sKey.length() != 16) {
                System.out.print("Key长度不是16位");
                return null;
            }
            byte[] raw = sKey.getBytes("utf-8");
            SecretKeySpec skeySpec = new SecretKeySpec(raw, "AES");
            Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
            cipher.init(Cipher.DECRYPT_MODE, skeySpec);
            byte[] encrypted1 = new BASE64Decoder().decodeBuffer(sSrc);//先用base64解密
            try {
                byte[] original = cipher.doFinal(encrypted1);
                String originalString = new String(original, "utf-8");
                return originalString;
            } catch (Exception e) {
                System.out.println(e.toString());
                return null;
            }
        } catch (Exception ex) {
            System.out.println(ex.toString());
            return null;
        }
    }


}
