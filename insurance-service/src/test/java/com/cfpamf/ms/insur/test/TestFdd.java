package com.cfpamf.ms.insur.test;

import com.alibaba.fastjson.JSON;
import com.cfpamf.ms.alipay.facade.enums.SignTypeEnum;
import com.cfpamf.ms.alipay.facade.request.AutoSignCreateRequest;
import com.cfpamf.ms.alipay.facade.request.ContractCustomerSign;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;
import org.junit.Test;

import java.util.Collections;

/**
 * <AUTHOR> 2020/6/8 15:22
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class TestFdd {

    @Test
    public void testFdd() {

        String tem = "http://safesfiles-test.oss-cn-beijing.aliyuncs.com/template%2Ftest.pdf?OSSAccessKeyId=LTAI4FkaWm37HxWhJWVPU4wv&Expires=1591604465&Signature=e65aOTNj1TFUC4cCpQUAclxSIfA%3D";
        AutoSignCreateRequest auto = new AutoSignCreateRequest();

        auto.setBizId("test001");
        auto.setDocUrl(tem);
        auto.setSignTypeEnum(SignTypeEnum.Customer);
        auto.setTitle("测试签章");
        ContractCustomerSign sign = new ContractCustomerSign();
        sign.setIdCardNo("320721199604170015");
        sign.setSignKeyword("");
        sign.setCustName("韦安");
        auto.setList(Collections.singletonList(sign));

        System.err.println(JSON.toJSONString(auto));
    }

    @Test
    public void test() {
    }
}
