package com.cfpamf.ms.insur.admin.service.renewal;

import com.cfpamf.ms.insur.admin.pojo.dto.SmOrderCorrectDTO;
import com.cfpamf.ms.insur.admin.renewal.dao.RenewalOrderMapper;
import com.cfpamf.ms.insur.admin.renewal.enums.OrderRenewalStatus;
import com.cfpamf.ms.insur.admin.renewal.form.RenewalOrderSearchForm;
import com.cfpamf.ms.insur.admin.renewal.service.OrderRenewalRecordService;
import com.cfpamf.ms.insur.admin.renewal.service.impl.RenewalOrderServiceImpl;
import com.cfpamf.ms.insur.admin.renewal.vo.OrderRenewalVo;
import com.cfpamf.ms.insur.admin.service.BaseTest;
import com.cfpamf.ms.insur.admin.service.SmOrderManageService;
import com.cfpamf.ms.insur.base.util.HttpRequestUtil;
import com.cfpamf.ms.insur.weixin.service.WxCcOrderService;
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2021/10/8 11:46
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({HttpRequestUtil.class})
public class RenewalOrderServiceImplTest extends BaseTest {

    @Mock
    RenewalOrderMapper renewalOrderMapper;
    @Mock
    SmOrderManageService smOrderManageService;
    @Mock
    WxCcOrderService wxCcOrderService;
    @Mock
    OrderRenewalRecordService orderRenewalRecordService;

    @InjectMocks
    RenewalOrderServiceImpl renewalOrderService;

    @Test
    public void testGetReportRenewalOrderList(){
        RenewalOrderSearchForm searchForm = Mockito.mock(RenewalOrderSearchForm.class);
        renewalOrderService.getReportRenewalOrderList(searchForm,"waited");
    }

    @Test
    public void testCorrectOrderRenewalStatus(){
        SmOrderCorrectDTO mock =new SmOrderCorrectDTO();
        mock.setNewValue(OrderRenewalStatus.RENEWED.name());
        mock.setOldValue(OrderRenewalStatus.OVER.name());
        mock.setPolicyNo("123123123");
        mock.setRelationPolicyNo("123123123123");
        OrderRenewalVo orderRenewalVo = new OrderRenewalVo();
        orderRenewalVo.setRenewalStatus(OrderRenewalStatus.OVER.name());
        orderRenewalVo.setOverRenewalPeriod(true);
        Mockito.when(renewalOrderMapper.getOrderRenewalVo("123123123")).thenReturn(orderRenewalVo);
        OrderRenewalVo orderRenewalVo1 = new OrderRenewalVo();
        orderRenewalVo1.setStartTime(LocalDateTime.now());
        Mockito.when(renewalOrderMapper.getOrderRenewalVo("123123123123")).thenReturn(orderRenewalVo1);
        renewalOrderService.correctOrderRenewalStatus(mock);
    }
    @Test
    public void testGetRenewalRemindDTOList(){
        renewalOrderService.getRenewalRemindDTOList(Lists.newArrayList(1),Lists.newArrayList(2));
    }
}
