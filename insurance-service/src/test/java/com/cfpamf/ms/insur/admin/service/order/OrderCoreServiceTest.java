package com.cfpamf.ms.insur.admin.service.order;

import com.cfpamf.ms.insur.admin.constant.SmConstants;
import com.cfpamf.ms.insur.admin.dao.safes.SmCommissionMapper;
import com.cfpamf.ms.insur.admin.dao.safes.SmOrderMapper;
import com.cfpamf.ms.insur.admin.dao.safes.order.SmOrderItemMapper;
import com.cfpamf.ms.insur.admin.event.OrderCommissionChangeEvent;
import com.cfpamf.ms.insur.admin.event.OrderCustomerChangeEvent;
import com.cfpamf.ms.insur.admin.external.OrderSubmitRequest;
import com.cfpamf.ms.insur.admin.external.OrderSubmitResponse;
import com.cfpamf.ms.insur.admin.external.fh.dto.FhInsuredPerson;
import com.cfpamf.ms.insur.admin.external.fh.dto.FhProperty;
import com.cfpamf.ms.insur.admin.pojo.convertor.OrderConvertor;
import com.cfpamf.ms.insur.admin.pojo.dto.BankCardBindingReqDTO;
import com.cfpamf.ms.insur.admin.pojo.dto.SmCreateOrderSubmitRequest;
import com.cfpamf.ms.insur.admin.pojo.dto.order.OrderRenewalDTO;
import com.cfpamf.ms.insur.admin.pojo.po.order.SmOrderRenewBindInfo;
import com.cfpamf.ms.insur.admin.pojo.vo.SmBaseOrderVO;
import com.cfpamf.ms.insur.admin.pojo.vo.SmCommissionSettingVO;
import com.cfpamf.ms.insur.admin.pojo.vo.SmPlanVO;
import com.cfpamf.ms.insur.admin.pojo.vo.SmPolicyInsured;
import com.cfpamf.ms.insur.admin.service.RenewalManagerService;
import com.cfpamf.ms.insur.admin.service.SmOrderRenewBindService;
import com.cfpamf.ms.insur.base.event.BaseEvent;
import com.cfpamf.ms.insur.base.event.EventBusEngine;
import com.cfpamf.ms.insur.base.util.HttpRequestUtil;
import org.apache.commons.io.IOUtils;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

import static org.junit.Assert.*;
@RunWith(PowerMockRunner.class)
@PrepareForTest({HttpRequestUtil.class, OrderConvertor.class})
public class OrderCoreServiceTest {
    @InjectMocks
    OrderCoreService orderCoreService;
    @Mock
    SmOrderMapper orderMapper;
    @Mock
    private SmOrderRenewBindService smOrderRenewBindService;
    @Mock
    private RenewalManagerService renewalManagerService;
    /**
     * 事件工作流
     */
    @Mock
    EventBusEngine eventBusEngine;
    @Mock
    private SmCommissionMapper cmsMapper;
    @Mock
    SmOrderItemMapper smOrderItemMapper;


    @Test
    public void saveRenewalOrderInfo() {
        SmCreateOrderSubmitRequest createDto = new SmCreateOrderSubmitRequest();
        createDto.setPropertyInfo(new FhProperty());
        createDto.setRenewBindInfo(new BankCardBindingReqDTO());
        List<FhInsuredPerson> insuredPersonList = new ArrayList<>();
        createDto.setInsuredPerson(insuredPersonList);


        FhInsuredPerson insuredPerson = new FhInsuredPerson();
        insuredPerson.setOldPolicyNo("old_1001");
        insuredPersonList.add(insuredPerson);

        SmPlanVO planVo = new SmPlanVO();
        planVo.setProductId(158);
        planVo.setProductId(311);

        PowerMockito.mockStatic(OrderConvertor.class);
        PowerMockito.when(OrderConvertor.mapperSmCreateOrderSubmitRequest(Mockito.any(OrderSubmitRequest.class),Mockito.any(OrderSubmitResponse.class),Mockito.any(SmPlanVO.class))).thenReturn(createDto);
        Mockito.doNothing().when(orderMapper).insertOrder(Mockito.any(SmCreateOrderSubmitRequest.class));
        Mockito.doNothing().when(orderMapper).insertOrderApplicant(Mockito.any(SmCreateOrderSubmitRequest.class));
        Mockito.doNothing().when(orderMapper).insertOrderInsured(Mockito.any(SmCreateOrderSubmitRequest.class));
        Mockito.doNothing().when(orderMapper).insertOrderPropertyInfo(Mockito.any(SmCreateOrderSubmitRequest.class));
        Mockito.when(smOrderRenewBindService.addOrderRenewBindInfo(Mockito.anyObject(),Mockito.anyObject())).thenReturn(new SmOrderRenewBindInfo());

        List<SmPolicyInsured> oldPolicyList = new ArrayList<>();
        SmPolicyInsured smPolicyInsured = new SmPolicyInsured();
        smPolicyInsured.setPolicyNo("old_1001");
        smPolicyInsured.setFhOrderId("old_order_id_1001");
        oldPolicyList.add(smPolicyInsured);
        Mockito.when(orderMapper.listOrderInsuredByPolicyNo(Mockito.anyString())).thenReturn(oldPolicyList);
        Mockito.doNothing().when(renewalManagerService).renewalOrderCallback(Mockito.any(OrderRenewalDTO.class));

        SmBaseOrderVO smBaseOrderVO = new SmBaseOrderVO();
        smBaseOrderVO.setPayStatus(SmConstants.ORDER_STATUS_PAYED);
        smBaseOrderVO.setPlanId(311);
        smBaseOrderVO.setCreateTime(new Date());
        Mockito.when(orderMapper.getBaseOrderInfoByOrderId(Mockito.anyString())).thenReturn(smBaseOrderVO);

        SmCommissionSettingVO settingVO = new SmCommissionSettingVO();
        settingVO.setId(1705);

        Mockito.when(cmsMapper.getCommissionSettingByPlanId(Mockito.anyInt(),Mockito.any())).thenReturn(settingVO);
        Mockito.when(orderMapper.updateOrderCommission(Mockito.anyString(),Mockito.anyString())).thenReturn(1);
        Mockito.when(orderMapper.updateOrderPaymentTime(Mockito.anyString())).thenReturn(1);

        Mockito.doNothing().when(eventBusEngine).publish(Mockito.any(BaseEvent.class));

        orderCoreService.saveRenewalOrderInfo(createDto,null,planVo);
    }

    @Test
    public void batchSaveOrderInfoGroup() {
        PowerMockito.doNothing().when(orderMapper).insertOrderBatch(Mockito.anyList());
        PowerMockito.doNothing().when(orderMapper).insertOrderApplicantBatch(Mockito.anyList());
        PowerMockito.doNothing().when(orderMapper).insertOrderInsuredBatch(Mockito.anyList());
        PowerMockito.when(smOrderItemMapper.insertOrderItemBatch(Mockito.anyList())).thenReturn(1);
        PowerMockito.when(orderMapper.updateOrderUnit(Mockito.anyList())).thenReturn(1);

        Mockito.doNothing().when(eventBusEngine).publish(Mockito.any(BaseEvent.class));
        orderCoreService.batchSaveOrderInfoGroup(Mockito.anyList());

    }
}