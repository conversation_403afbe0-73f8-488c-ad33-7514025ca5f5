package com.cfpamf.ms.insur.base.util;

import com.cfpamf.ms.insur.base.util.reflect.FieldRemoveDefault;
import com.cfpamf.ms.insur.base.util.reflect.FieldRemoveUtil;
import com.cfpamf.ms.insur.base.util.reflect.JsonRemove;
import com.github.jsonzou.jmockdata.JMockData;
import lombok.AccessLevel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldDefaults;
import org.junit.Assert;
import org.junit.Test;

import java.lang.reflect.Field;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * <AUTHOR>
 * @Date 2021/10/15 10:02
 * @Version 1.0
 */
public class FieldRemoveUtilTest {

    @Test
    public void testTranField2Null() {
        List<TestFiledRemoveUtilBean> list = IntStream.range(1, 10)
                .mapToObj(item -> JMockData.mock(TestFiledRemoveUtilBean.class))
                .collect(Collectors.toList());
        FieldRemoveUtil.tranListField2Null(list, TestFiledRemoveUtilBean.class, FieldRemoveDefault.class);
        Assert.assertTrue(list.stream().allMatch(item -> item.getA() == null));
    }

    @Test
    public void testExtractRemoveFieldName() {
        Set<String> xSet = FieldRemoveUtil.extractRemoveFieldName(TestFiledRemoveUtilBean.class, r.class);
        Assert.assertTrue(xSet.contains("e") && xSet.contains("f"));
        Set<String> ySet = FieldRemoveUtil.extractRemoveFieldName(TestFiledRemoveUtilBean.class, y.class);
        Assert.assertTrue(ySet.contains("c") && ySet.contains("d") && ySet.contains("f"));
    }

    @Test
    public void testExtractRemoveField() {
        Set<String> fieldNameSet = FieldRemoveUtil.extractRemoveField(TestFiledRemoveUtilBean.class, y.class).stream()
                .map(Field::getName).collect(Collectors.toSet());
        Assert.assertTrue(fieldNameSet.contains("c") && fieldNameSet.contains("d") && fieldNameSet.contains("f"));
    }

    @Data
    @EqualsAndHashCode
    @FieldDefaults(level = AccessLevel.PRIVATE)
    static public class TestFiledRemoveUtilBean {
        @JsonRemove
        String a;
        String b;
        @JsonRemove(classify = x.class)
        String c;
        @JsonRemove(classify = y.class)
        String d;
        @JsonRemove(classify = r.class)
        String e;
        @JsonRemove(classify = {r.class, y.class})
        String f;
    }

    interface r {
    }

    interface x {
    }

    interface y extends x {
    }

}
