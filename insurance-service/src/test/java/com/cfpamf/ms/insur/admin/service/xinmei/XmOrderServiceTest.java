package com.cfpamf.ms.insur.admin.service.xinmei;

import com.cfpamf.ms.insur.admin.dao.safes.order.SmOrderPolicyMapper;
import com.cfpamf.ms.insur.admin.enums.xinmei.EnumXmBackVisit;
import com.cfpamf.ms.insur.admin.enums.xinmei.EnumXmBackVisitWay;
import com.cfpamf.ms.insur.admin.external.xm.XmApiProperties;
import com.cfpamf.ms.insur.admin.external.xm.client.XmPolicyClient;
import com.cfpamf.ms.insur.admin.pojo.query.xinmei.backvisit.BackVisitResBody;
import com.cfpamf.ms.insur.admin.service.BaseTest;
import com.cfpamf.ms.insur.admin.service.order.XmOrderService;
import com.cfpamf.ms.insur.base.util.HttpRequestUtil;
import com.github.jsonzou.jmockdata.JMockData;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.beans.factory.annotation.Autowired;

@RunWith(PowerMockRunner.class)
@PrepareForTest({HttpRequestUtil.class})
public class XmOrderServiceTest extends BaseTest {

    @Mock
    private SmOrderPolicyMapper smOrderPolicyMapper;

    @Mock
    private XmApiProperties xmApiProperties;

    @Mock
    private XmPolicyClient xmPolicyClient;

    @InjectMocks
    private XmOrderService xmOrderService;

    @Before
    public void setUp() throws Exception {
        super.setUp();
    }

    @Test
    public void notifyBackVisit() {
        String orderId = JMockData.mock(String.class);
        xmOrderService.notifyBackVisit(orderId);
    }

    @Test
    public void handleBackVisit() {
        BackVisitResBody body = JMockData.mock(BackVisitResBody.class);
        xmOrderService.handleBackVisit(body);
    }
}
