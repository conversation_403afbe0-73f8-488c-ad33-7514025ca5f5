package com.cfpamf.ms.insur.weixin.service;

import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.ms.insur.admin.constant.EnumProductAttr;
import com.cfpamf.ms.insur.admin.dao.safes.product.ProductMapper;
import com.cfpamf.ms.insur.admin.pojo.dto.SmProductCoverageDiscountDTO;
import com.cfpamf.ms.insur.admin.pojo.vo.SmProductQuoteLimitItemVO;
import com.cfpamf.ms.insur.admin.service.BaseTest;
import com.cfpamf.ms.insur.base.controller.ApplicationTest;
import com.cfpamf.ms.insur.base.exception.BizException;
import com.cfpamf.ms.insur.base.util.HttpRequestUtil;
import com.cfpamf.ms.insur.weixin.dao.safes.PolicyMapper;
import com.cfpamf.ms.insur.weixin.pojo.dto.product.PremiumDTO;
import com.cfpamf.ms.insur.weixin.pojo.dto.product.QuoteLimitDTO;
import com.cfpamf.ms.insur.weixin.pojo.dto.product.TimeFactorItem;
import com.cfpamf.ms.insur.weixin.pojo.query.policy.CoverageQuery;
import com.cfpamf.ms.insur.weixin.pojo.query.policy.DutyFactorFlowQuery;
import com.cfpamf.ms.insur.weixin.pojo.query.policy.PlanQuery;
import com.cfpamf.ms.insur.weixin.pojo.vo.apply.common.GroupCheckReq;
import com.cfpamf.ms.insur.weixin.pojo.vo.apply.common.GroupInquiry;
import com.cfpamf.ms.insur.weixin.pojo.vo.apply.common.GroupInquiryResponse;
import com.cfpamf.ms.insur.weixin.pojo.vo.product.CoverageAmountVo;
import com.cfpamf.ms.insur.weixin.pojo.vo.product.CoverageVo;
import com.cfpamf.ms.insur.weixin.service.context.RFQHolder;
import com.cfpamf.ms.insur.weixin.service.context.RateFacor;
import com.cfpamf.ms.insur.weixin.service.policy.InquiryService;
import com.cfpamf.ms.insur.weixin.service.underwriting.GroupApplyHandler;
import com.cfpamf.ms.insur.weixin.util.StringTools;
import com.github.jsonzou.jmockdata.JMockData;
import com.github.jsonzou.jmockdata.TypeReference;
import groovy.util.Eval;
import org.apache.commons.lang.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

@RunWith(PowerMockRunner.class)
@PrepareForTest({HttpRequestUtil.class})
public class InquiryServiceTest extends BaseTest {

    @Mock
    private PolicyMapper policyMapper;

    @Mock
    private ProductMapper productMapper;

    @Mock
    private GroupApplyHandler applyHandler;

    @InjectMocks
    private InquiryService inquiryService;

    @Test
    public void tryPermiumByJob(){
        GroupInquiry req = JMockData.mock(GroupInquiry.class);

        TypeReference<HashMap<Integer,BigDecimal>> tr = new TypeReference<HashMap<Integer, BigDecimal>>() {
        };
        Map<Integer,BigDecimal> premiumMap = JMockData.mock(tr);

        List<QuoteLimitDTO> limits = new ArrayList<>();
        QuoteLimitDTO dto = JMockData.mock(QuoteLimitDTO.class);
        limits.add(dto);
        String jobClass = "1,2,3,4,5";
        Integer perQtry = JMockData.mock(Integer.class);

        BigDecimal data = inquiryService.tryPermiumByOneV2(req,premiumMap,null);
        System.out.println(data);
        Assert.assertTrue(data!=null);
    }

    /**
     * 计算规则：
     * 1、保障项目的保费=（选择的计划的保额&职业类别对应的保费）*责任调整因子;
     * 2、计划保费=∑保障项目的保费
     * 3、整单保费=计划保费*人数折扣
     * @return
     */
    @Test
    public void tryPermium(){
        GroupInquiry req = JMockData.mock(GroupInquiry.class);
        Integer productId = req.getProductId();
        String ocpGroup= JMockData.mock(String.class);
        List<PremiumDTO> premiumTable = policyMapper.queryPremium(productId,req.getPlan().getId(),ocpGroup);
        
        PlanQuery plan = req.getPlan();
        List<CoverageQuery> coverages = plan.getCoverages();
        BigDecimal res = new BigDecimal(0);
        Map<Integer,BigDecimal> premiumMap = convertMap(premiumTable);
        List<SmProductCoverageDiscountDTO> discounts = policyMapper.queryDiscount(productId);
        BigDecimal hundred = new BigDecimal(100);
        for(CoverageQuery cq:coverages){
            CoverageAmountVo amounts = cq.getAmount();
            Integer spcaId = amounts.getSpcaId();
            BigDecimal premium = premiumMap.get(spcaId);
            if(premium!=null){
                List<DutyFactorFlowQuery> flows = cq.getDutyFactor();
                if(flows!=null){
                    for(DutyFactorFlowQuery q:flows){
                        if(q.getFlow()!=null) {
                            premium = premium.multiply(q.getFlow()).divide(hundred);
                            res = res.add(premium);
                        }
                    }
                }
            }
        }
        /**
         * 计算人数折扣
         */
        GroupInquiryResponse resp = new GroupInquiryResponse();
        int qty = 3;
        SmProductCoverageDiscountDTO discount = discounts.stream().filter(i->qty>=i.getMinPerQty()&&qty<=i.getPerQty()).findAny().orElse(null);
        BigDecimal totalPremium = res.multiply(new BigDecimal(qty));
        if(discount!=null) {
            totalPremium = totalPremium.multiply(discount.getDiscount()).divide(hundred);

        }
        String acc = policyMapper.getProductAttr(productId, EnumProductAttr.PREMIUM_PEOPLE_FACTOR_ACC.getCode());
        int j = 2;
        if(StringUtils.isNotBlank(acc)&&acc.matches("^\\d+(\\.\\d*)?$")){
            j = Integer.parseInt(acc);
        }
        totalPremium = totalPremium.setScale(j, RoundingMode.HALF_UP);
        res = res.setScale(j, RoundingMode.HALF_UP);
        resp.setTotalPremium(totalPremium);
        Assert.assertNotNull(resp);
    }

    @Test
    public void tryPermium2(){
        GroupInquiry req = JMockData.mock(GroupInquiry.class);
        GroupInquiryResponse response = inquiryService.tryPermium(req);
        System.out.println(response);
        Assert.assertNotNull(response);
    }

    @Test
    public void tryPermiumByShortTerm(){
        BigDecimal premium = JMockData.mock(BigDecimal.class);
        RateFacor rf= JMockData.mock(RateFacor.class);

        BigDecimal rtn = inquiryService.tryPermiumByShortTerm(premium,rf);
        System.out.println(rtn);
        Assert.assertNotNull(rtn);
    }

    @Test
    public void tryPermiumByEnterpriserRisk(){

        Integer productId = JMockData.mock(Integer.class);

        Integer planId = JMockData.mock(Integer.class);

        BigDecimal res = JMockData.mock(BigDecimal.class);
        RateFacor rf = JMockData.mock(RateFacor.class);
        BigDecimal rtn = inquiryService.tryPermiumByEnterpriserRisk(res,rf);
        System.out.println(rtn);
        Assert.assertNotNull(rtn);
    }

    @Test
    public void tryPremiumByDutyFactor(){

        BigDecimal rawPremium = JMockData.mock(BigDecimal.class);
        TypeReference<List<DutyFactorFlowQuery>> tr = new TypeReference<List<DutyFactorFlowQuery>>() {
        };

        List<DutyFactorFlowQuery> flows = JMockData.mock(tr);

        BigDecimal rtn = inquiryService.tryPremiumByDutyFactor(rawPremium,flows);
        System.out.println(rtn);
        Assert.assertNotNull(rtn);
    }

    @Test
    public void tryPermiumByDiscount(){
        BigDecimal res = JMockData.mock(BigDecimal.class);
        BigDecimal discount = JMockData.mock(BigDecimal.class);

        BigDecimal rtn = inquiryService.tryPermiumByDiscount(res,discount);
        System.out.println(rtn);
        Assert.assertNotNull(rtn);
    }

    private Map<Integer,BigDecimal> convertMap(List<PremiumDTO> premiumTable) {
        Map<Integer,BigDecimal> tempMap = new HashMap<>();
        premiumTable.forEach(p->{
            tempMap.put(p.getSpcaId(),p.getPremium());
        });
        return tempMap;
    }

    @Mock
    GroupApplyHandler groupApplyHandler;

    @Test
    public void applyCheck() {
        GroupCheckReq req=JMockData.mock(GroupCheckReq.class);
        List<CoverageQuery> coverageList = req.getPlan().getCoverages();
        Map<String,Integer> ocpMap = new HashMap<>();
        req.getOccupationItemList().forEach(o->{ocpMap.put(o.getOccupationGroup(),o.getQty());});
        try {
            check(ocpMap, coverageList);
        }catch (BizException e){
            e.printStackTrace();
        }
        Assert.assertTrue(true);
    }

    public void check(  Map<String,Integer> ocpMap,List<CoverageQuery> coverageList){
        List<QuoteLimitDTO> limits = new ArrayList<>();
        QuoteLimitDTO dto = JMockData.mock(QuoteLimitDTO.class);
        dto.setLimitType(SmProductQuoteLimitItemVO.LIMIT_TYPE_PER);
        limits.add(dto);

        dto = JMockData.mock(QuoteLimitDTO.class);
        dto.setLimitType(SmProductQuoteLimitItemVO.LIMIT_TYPE_OCPN_PER);
        limits.add(dto);

        dto = JMockData.mock(QuoteLimitDTO.class);
        dto.setLimitType(SmProductQuoteLimitItemVO.LIMIT_TYPE_CVG_RELY);
        limits.add(dto);

        dto = JMockData.mock(QuoteLimitDTO.class);
        dto.setLimitType(SmProductQuoteLimitItemVO.LIMIT_TYPE_OCVG_AMOUNT);
        limits.add(dto);

        dto = JMockData.mock(QuoteLimitDTO.class);
        dto.setLimitType(SmProductQuoteLimitItemVO.LIMIT_TYPE_TIME);
        limits.add(dto);
        Map<String,CoverageQuery> coverageMap = new HashMap<>();
        coverageList.forEach(c->{coverageMap.put(String.valueOf(c.getSpcId()),c);});
        Integer qty = ocpMap.values().stream().reduce(Integer::sum).get();
        for(QuoteLimitDTO entry:limits){
//            groupApplyHandler.quoteLimitCheck(qty,ocpMap,coverageMap,entry);
        }
    }

    public  void checkCvgAmountLimit(Map<String,Integer> ocpMap,Map<String, CoverageQuery> coverageMap, QuoteLimitDTO entry) {
        String ocp = entry.getOccupationGroup();
        List<String> ocpLimits = Arrays.asList(ocp.split(","));
        Set<String> ocpList = ocpMap.keySet();
        if(ocpList.containsAll(ocpLimits)){
            String source = entry.getSourceSpcId();
            String rely= entry.getTargetSpcId();
            CoverageQuery scq = coverageMap.get(source);
            CoverageQuery rcq = coverageMap.get(rely);
            if(scq!=null&&rcq!=null) {
                BigDecimal amount1 = scq.getAmount().getCvgAmount();
                BigDecimal amount2 = rcq.getAmount().getCvgAmount();
                amount2 = amount2.multiply(entry.getAmountRatio()).divide(new BigDecimal(100));
                String script = amount1+entry.getOperation()+amount2;
                if(!ObjectUtils.equals(Eval.me(script),Boolean.TRUE)){
                    throw new BizException("-1","责任保额不满足条件");
                }
            }
        }
    }

    public void quoteLimitCheck(int qty, Map<String,Integer> ocpMap,Map<String, CoverageQuery> coverageMap,QuoteLimitDTO entry) {
        switch (entry.getLimitType()) {
            case SmProductQuoteLimitItemVO.LIMIT_TYPE_PER:
                checkPersonNumber(qty,entry);
                return;
            case SmProductQuoteLimitItemVO.LIMIT_TYPE_OCPN_PER:
                checkOcpnNumber(qty,ocpMap,entry);
                return;
            case SmProductQuoteLimitItemVO.LIMIT_TYPE_CVG_RELY:
                checkCvgRelation(coverageMap,entry);
                break;
            case SmProductQuoteLimitItemVO.LIMIT_TYPE_OCVG_AMOUNT:
                checkCvgAmountLimit(ocpMap,coverageMap,entry);
                break;
            case SmProductQuoteLimitItemVO.LIMIT_TYPE_TIME:
                break;
            default:
                return;
        }
    }
    public  void checkCvgRelation(Map<String,CoverageQuery> cvgMap,QuoteLimitDTO entry){
        String sourceSpc = entry.getSourceSpcId();
        String relySpc = entry.getRelyOnSpcId();
        List<String>  relySpcList = Arrays.asList(relySpc.split(","));
        Set<String> cvgList = cvgMap.keySet();
        String lossCvgId = null;
        if(cvgList.contains(sourceSpc)){
            switch (entry.getOperation()){
                case "one":
                    for(String o:relySpcList){
                        if (cvgList.contains(o)) {
                            return;
                        }
                    }
                    lossCvgId= relySpcList.get(0);
                    break;
                case "all":
                    if(cvgList.containsAll(relySpcList)){
                        return;
                    }
                    lossCvgId= relySpcList.get(0);
                    break;
                default:
                    break;
            }
        }
        if(lossCvgId!=null){
            CoverageVo cvgVo = policyMapper.queryCoverageById(lossCvgId);
            if(cvgVo!=null){
                throw new BizException("-1",String.format("请选择[%s]",cvgVo.getCvgItemName()));
            }
        }
    }


    public static void checkPersonNumber(int qty,QuoteLimitDTO entry){
        if(entry.getMinPerQty()>qty){
            throw new MSBizNormalException("-1","最少投保人数"+entry.getMinPerQty());
        }
    }

    public  static void checkOcpnNumber(Integer qty,Map<String,Integer> ocpMap, QuoteLimitDTO entry){
        String ocp=entry.getOccupationGroup();
        String[] ocps = ocp.split(",");
        Set<String> ocpSet = new HashSet<>(Arrays.asList(ocps));
        int ocpPers = 0;
        boolean retFlag = false;
        for(String o:ocpSet){
            if(!ocpMap.containsKey(o)){
                retFlag=true;
                break;
            }
            ocpPers+=ocpMap.get(o);
        }
        if(retFlag){
            return;
        }
        if(entry.getMinPerQty()!=null&&entry.getMinPerQty()>ocpPers){
            throw new MSBizNormalException("-1","投保人数不满足要求");
        }
        if(entry.getMinPerRatio()!=null){
            BigDecimal ocpDecimal = new BigDecimal(ocpPers);
            BigDecimal insDecimal = new BigDecimal(qty);
            ocpDecimal = ocpDecimal.divide(insDecimal);
            if(ocpDecimal.compareTo(entry.getMinPerRatio())<0){
                throw new MSBizNormalException("-1","投保人数不满足要求");
            }
        }
    }
}
