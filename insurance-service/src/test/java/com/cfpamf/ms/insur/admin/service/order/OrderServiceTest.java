package com.cfpamf.ms.insur.admin.service.order;

import com.cfpamf.ms.insur.admin.config.CappProperties;
import com.cfpamf.ms.insur.admin.dao.safes.*;
import com.cfpamf.ms.insur.admin.dao.safes.order.*;
import com.cfpamf.ms.insur.admin.external.cic.CicOrderServiceAdapterImpl;
import com.cfpamf.ms.insur.admin.external.fh.dto.FhProposer;
import com.cfpamf.ms.insur.admin.renewal.service.OrderRenewalRecordService;
import com.cfpamf.ms.insur.admin.service.*;
import com.cfpamf.ms.insur.base.event.EventBusEngine;
import com.cfpamf.ms.insur.base.exception.BizException;
import com.cfpamf.ms.insur.base.infeface.CustomerCenterService;
import com.cfpamf.ms.insur.base.service.BusinessTokenService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;

/**
 * <AUTHOR>
 * @date 2021/9/27 11:09
 */
@Slf4j
@RunWith(PowerMockRunner.class)
public class OrderServiceTest {
    @InjectMocks
    CicOrderService cicOrderService;

    @Mock
    CicOrderServiceAdapterImpl adapter;


    @Mock
    CappProperties properties;

    @Mock
    private CicPayErrorMapper cicPayErrorMapper;

    /**
     * 产品service
     */
    @Mock
    protected SmProductService productService;

    /**
     * 业务token service
     */
    @Mock
    protected BusinessTokenService tokenService;

    /**
     * 客户中心 service
     */
    @Mock
    protected CustomerCenterService ccService;

    /**
     * 产品提成 service
     */
    @Mock
    protected SmCommissionMapper cmsMapper;

    /**
     * 订单mapper
     */
    @Mock
    protected SmOrderMapper orderMapper;

    /**
     * 订单mapper
     */
    @Mock
    protected SmProductFormBuyLimitMapper formBuyLimitMapper;

    /**
     * 用户mapper
     */
    @Mock
    protected AuthUserMapper userMapper;

    @Mock
    protected OrderRenewalRecordService orderRenewalRecordService;

    /**
     * 事件工作流
     */
    @Lazy
    @Mock
    protected EventBusEngine busEngine;

    @Mock
    protected SmOrderCarMapper carMapper;

    @Mock
    protected SmOrderHouseMapper houseMapper;

    @Mock
    protected SmOrderGroupNotifyService smOrderGroupNotifyService;

    @Mock
    protected SmCommonSettingService commonSettingService;

    @Mock
    protected SmOrderItemMapper smOrderItemMapper;

    @Mock
    protected SmOrderRenewBindService smOrderRenewBindService;

    @Mock
    SmXjxhService smXjxhService;
    @Mock
    SmOrderPolicyMapper smOrderPolicyMapper;
    @Mock
    protected ChOrderPersonalNotifyService chOrderPersonalNotifyService;

    @Mock
    protected SmOrderRiskDutyMapper riskDutyMapper;
    @Mock
    protected SmProductMapper productMapper;

    @Value("${author.token.lockTime}")
    protected long tokenLockTime;

    /**
     * 投保人手机号码限制
     */
    @Value("${order.phoneLimit:10}")
    protected int phoneLimit = 10;

    @Before
    public void before() {

    }

    @Test
    public void testCheckProposerPhone() {
        FhProposer mock = Mockito.mock(FhProposer.class);
        Mockito.when(orderMapper.countApplicantByPhone(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(11);
        try {
            cicOrderService.checkProposerPhone(mock);
        } catch (BizException e) {
            Assert.assertEquals(e.getCode(), "801033");
        }
    }
}
