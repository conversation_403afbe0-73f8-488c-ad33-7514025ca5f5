package com.cfpamf.ms.insur.admin.service.order;

import com.cfpamf.ms.insur.admin.config.ProductProperties;
import com.cfpamf.ms.insur.admin.config.ProductSpecialRuleProperties;
import com.cfpamf.ms.insur.admin.dao.safes.*;
import com.cfpamf.ms.insur.admin.dao.safes.auto.*;
import com.cfpamf.ms.insur.admin.dao.safes.order.*;
import com.cfpamf.ms.insur.admin.enums.EnumChannel;
import com.cfpamf.ms.insur.admin.external.ChannelOrderService;
import com.cfpamf.ms.insur.admin.external.OrderSubmitRequest;
import com.cfpamf.ms.insur.admin.external.OrderSubmitResponse;
import com.cfpamf.ms.insur.admin.external.auto.AutoCvtHelper;
import com.cfpamf.ms.insur.admin.external.auto.gsc.GscAutoOrderServiceAdapter;
import com.cfpamf.ms.insur.admin.pojo.vo.SmPlanVO;
import com.cfpamf.ms.insur.admin.renewal.service.OrderRenewalRecordService;
import com.cfpamf.ms.insur.admin.service.*;
import com.cfpamf.ms.insur.base.event.EventBusEngine;
import com.cfpamf.ms.insur.base.infeface.CustomerCenterService;
import com.cfpamf.ms.insur.base.service.BusinessTokenService;
import com.cfpamf.ms.insur.base.util.HttpRequestUtil;
import com.cfpamf.ms.insur.common.service.monitor.BusinessMonitorService;
import com.cfpamf.ms.insur.weixin.dao.safes.EndorMapper;
import com.cfpamf.ms.pay.facade.config.PayFacadeConfigProperties;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.slf4j.Logger;

import java.time.format.DateTimeFormatter;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.mockito.Mockito.when;

@RunWith(PowerMockRunner.class)
@PrepareForTest(HttpRequestUtil.class)
public class AutoGscOrderServiceTest extends BaseTest {

    @Mock private GscAutoOrderServiceAdapter mockAdapter;
    @Mock private AutoOrderExtendMapper autoOrderExtendMapper;
    @Mock private AutoCvtHelper autoCvtHelper;
    @Mock private AutoOrderMapper autoOrderMapper;
    @Mock private AutoOrderPolicyMapper autoOrderPolicyMapper;
    @Mock private AutoOrderPersonMapper autoOrderPersonMapper;
    @Mock private AutoOrderCarMapper autoOrderCarMapper;
    @Mock private AutoOrderReceiverMapper autoOrderReceiverMapper;
    @Mock private AutoOrderRiskMapper autoOrderRiskMapper;
    @Mock private Logger log;
    private int YEAR_NODE;
    private DateTimeFormatter FMT_PARSE;
    private DateTimeFormatter FMT_SUB;
    @Mock private BusinessMonitorService monitorService;
    @Mock private SmProductService productService;
    @Mock private BusinessTokenService tokenService;
    @Mock private CustomerCenterService ccService;
    @Mock private SmCommissionMapper cmsMapper;
    @Mock private SmOrderMapper orderMapper;
    @Mock private SmOrderItemMapper orderItemMapper;
    @Mock private SmProductFormBuyLimitMapper formBuyLimitMapper;
    @Mock private AuthUserMapper userMapper;
    @Mock private OrderRenewalRecordService orderRenewalRecordService;
    @Mock private OrderCoreService orderCoreService;
    @Mock private SmChannelCallbackMapper channelCallbackMapper;
    @Mock private EventBusEngine busEngine;
    @Mock private SmOrderCarMapper carMapper;
    @Mock private SmOrderHouseMapper houseMapper;
    @Mock private SmOrderGroupNotifyService smOrderGroupNotifyService;
    @Mock private SmCommonSettingService commonSettingService;
    @Mock private SmOrderItemMapper smOrderItemMapper;
    @Mock private SmOrderRenewBindService smOrderRenewBindService;
    @Mock private SmXjxhService smXjxhService;
    @Mock private SmOrderPolicyMapper smOrderPolicyMapper;
    @Mock private ChOrderPersonalNotifyService chOrderPersonalNotifyService;
    @Mock private SmOrderRiskDutyMapper riskDutyMapper;
    @Mock private SmProductMapper productMapper;
    @Mock private SmOrderPolicyService smOrderPolicyService;
    @Mock private RenewalManagerService renewalManagerService;
    @Mock private SmOrderInsuredMapper insuredMapper;
    private long tokenLockTime;
    private int phoneLimit;
    @Mock private ProductProperties properties;
    @Mock private ProductSpecialRuleProperties productSpecialRuleProperties;
    @Mock private EndorMapper paymentMapper;
    @Mock private PayFacadeConfigProperties payFacadeConfigProperties;
    @Mock private TempOrderNewProductMapper tempOrderNewProductMapper;

    @InjectMocks private AutoGscOrderService autoGscOrderServiceUnderTest;

    @Test
    public void testGetJumpUrl() {
        // Setup
        when(mockAdapter.getJumpUrl("jobNumber", "ext", "h5Url")).thenReturn("result");

        // Run the test
        final String result = autoGscOrderServiceUnderTest.getJumpUrl("jobNumber", "ext", 0);

        // Verify the results
    }

    @Test
    public void testChannel() {
        assertEquals(EnumChannel.AUTO_GSC.getCode(), autoGscOrderServiceUnderTest.channel());
    }

    @Test
    public void testOrderService() {
        // Setup
        // Run the test
        final ChannelOrderService result = autoGscOrderServiceUnderTest.orderService();

        // Verify the results
    }

    @Test
    public void testSupport() {
        assertFalse(autoGscOrderServiceUnderTest.support("channel"));
    }

    @Test(expected = UnsupportedOperationException.class)
    public void testSubmitSuccessAfter() {
        autoGscOrderServiceUnderTest.submitSuccessAfter("userUniqueId", new SmPlanVO(), new OrderSubmitRequest(), new OrderSubmitRequest(), new OrderSubmitResponse());
    }

    @Test(expected = UnsupportedOperationException.class)
    public void testSubmitOrder() {
        autoGscOrderServiceUnderTest.submitOrder("userUniqueId", new OrderSubmitRequest());
    }
}
