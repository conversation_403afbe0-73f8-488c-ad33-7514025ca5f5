package com.cfpamf.ms.insur.base.util;

import com.cfpamf.ms.insur.base.annotation.Mask;
import org.junit.FixMethodOrder;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.MethodSorters;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Collections;
import java.util.List;

/*@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("dev")
@FixMethodOrder(MethodSorters.NAME_ASCENDING)*/
public class MaskUtilsTest {

    /*@Test
    public void testMaskStr() {
//        System.out.println(DataMaskUtil.maskMobile("15574307649"));
//        System.out.println(DataMaskUtil.maskIdCardNo("36220119900919181X"));
//        System.out.println(DataMaskUtil.maskEmail("<EMAIL>"));
    }


    @Test
    public void testMaskList() {
        Data data = new Data();
        List<Data> dataList = DataMaskUtil.maskList(Collections.singletonList(data));
//        System.out.println(data.toString());
    }

    @Test
    public void maskFullName() {
//        System.out.println(DataMaskUtil.maskFullName("张娜义"));
//        System.out.println(DataMaskUtil.maskFullName("欧阳娜娜"));
    }


    @lombok.Data
    public static class Data {
        @Mask(dataType = Mask.DataType.MOBILE)
        private String mobile = "15574307649";
        @Mask(dataType = Mask.DataType.EMAIL)
        private String email = "<EMAIL>";
        @Mask(dataType = Mask.DataType.ID_CARD)
        private String idCard = "362201199009191811";
    }*/
}