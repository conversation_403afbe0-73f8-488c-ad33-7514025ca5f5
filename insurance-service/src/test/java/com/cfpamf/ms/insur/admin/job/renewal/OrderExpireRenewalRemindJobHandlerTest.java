package com.cfpamf.ms.insur.admin.job.renewal;

import com.cfpamf.ms.insur.admin.renewal.service.RenewalOrderService;
import com.cfpamf.ms.insur.base.service.BmsService;
import com.cfpamf.ms.insur.base.util.HttpRequestUtil;
import com.cfpamf.ms.insur.base.util.SmsSenderUtil;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @date 2021/12/22 13:56
 */
@RunWith(PowerMockRunner.class)
public class OrderExpireRenewalRemindJobHandlerTest {

    @Mock
    BmsService bmsService;
    @Mock
    RenewalOrderService renewalOrderService;
    @Mock
    private SmsSenderUtil smsSenderUtil;
    @InjectMocks
    private OrderExpireRenewalRemindJobHandler orderExpireRenewalRemindJobHandler;

    @Test
    public void test(){
        orderExpireRenewalRemindJobHandler.execute();
    }
}
