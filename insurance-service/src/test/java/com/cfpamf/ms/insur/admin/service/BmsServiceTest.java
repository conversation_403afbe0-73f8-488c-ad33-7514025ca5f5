package com.cfpamf.ms.insur.admin.service;

import com.cfpamf.ms.bms.facade.vo.UserListVO;
import com.cfpamf.ms.insur.base.service.BmsService;
import org.junit.FixMethodOrder;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.MethodSorters;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

/*@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("dev")
@FixMethodOrder(MethodSorters.NAME_ASCENDING)*/
public class BmsServiceTest {

    /*@Autowired
    private BmsService bmsService;

    @Test
    public void findUserListByPage() {
        List<UserListVO> users = bmsService.findUserListByPage(null, new String[]{"ZHNX07902"});
        System.out.println();
    }*/
}