package com.cfpamf.ms.insur.admin.service.order;

import com.cfpamf.ms.insur.admin.dao.safes.app.SmOrderRelateCappUserMapper;
import com.cfpamf.ms.insur.admin.pojo.po.app.SmOrderRelateCappShareUser;
import com.cfpamf.ms.insur.admin.service.BaseTest;
import com.cfpamf.ms.insur.base.util.HttpRequestUtil;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(PowerMockRunner.class)
@PrepareForTest(HttpRequestUtil.class)
public class CappH5ServiceTest extends BaseTest {

    @Mock private SmOrderRelateCappUserMapper mockRelateCappShareInfoMapper;

    @InjectMocks private CappH5Service cappH5ServiceUnderTest;

    @Test
    public void testSaveRelation1() {
        // Setup
        final SmOrderRelateCappShareUser relateCappShareUser = new SmOrderRelateCappShareUser(
                "orderId",
                "shareCappUserId",
                LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0)
        );

        // Configure SmOrderRelateCappUserMapper.queryByFhOrderId(...).
        final SmOrderRelateCappShareUser smOrderRelateCappShareUser = new SmOrderRelateCappShareUser(
                "orderId",
                "shareCappUserId",
                LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0)
        );
        when(mockRelateCappShareInfoMapper.queryByFhOrderId("orderId")).thenReturn(smOrderRelateCappShareUser);

        // Run the test
        cappH5ServiceUnderTest.saveRelation(relateCappShareUser);

    }

    @Test
    public void testSaveRelation2() {
        // Setup
        final Map<String, String> callBackParams = new HashMap<>();

        // Configure SmOrderRelateCappUserMapper.queryByFhOrderId(...).
        final SmOrderRelateCappShareUser smOrderRelateCappShareUser = new SmOrderRelateCappShareUser(
                "orderId",
                "shareCappUserId",
                LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0)
        );
        when(mockRelateCappShareInfoMapper.queryByFhOrderId("orderId")).thenReturn(smOrderRelateCappShareUser);

        // Run the test
        cappH5ServiceUnderTest.saveRelation(callBackParams, "orderId");


    }
}
