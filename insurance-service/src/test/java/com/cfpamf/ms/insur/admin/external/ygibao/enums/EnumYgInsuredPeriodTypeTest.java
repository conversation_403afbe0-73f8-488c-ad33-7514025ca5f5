package com.cfpamf.ms.insur.admin.external.ygibao.enums;

import com.cfpamf.ms.insur.admin.external.ygibao.model.YgPolicyContractInfoVo;
import com.cfpamf.ms.insur.admin.external.ygibao.model.YgPolicyInsuredInfoList;
import com.cfpamf.ms.insur.admin.external.ygibao.model.YgPolicyProductInfoList;
import com.github.jsonzou.jmockdata.JMockData;
import junit.framework.TestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;

/**
 * <AUTHOR> 2021/11/8 14:50
 */
public class EnumYgInsuredPeriodTypeTest {

    @Before
    public void setUp() throws Exception {

    }


    @Test
    public void testYearGetEndDate() {
        YgPolicyContractInfoVo testModel = JMockData.mock(YgPolicyContractInfoVo.class);
        testModel.setEnforceTime("2020-08-11 00:00:00");
        YgPolicyInsuredInfoList insuredInfoList = testModel.getPolicyInsuredInfoList().get(0);
        insuredInfoList.setInsuredBirthday("1995-08-08");
        YgPolicyProductInfoList productInfo = insuredInfoList.getPolicyProductInfoList().get(0);

        productInfo.setMainInsurance(1);
        productInfo.setInsuredPeriod(30);

        Assert.assertEquals("2050-08-10 23:59:59", EnumYgInsuredPeriodType.ONLY_YEAR.getEndDate(productInfo, testModel));

    }

    @Test
    public void testMonthGetEndDate() {
        YgPolicyContractInfoVo testModel = JMockData.mock(YgPolicyContractInfoVo.class);
        testModel.setEnforceTime("2020-08-11 00:00:00");
        YgPolicyInsuredInfoList insuredInfoList = testModel.getPolicyInsuredInfoList().get(0);
        insuredInfoList.setInsuredBirthday("1995-08-08");
        YgPolicyProductInfoList productInfo = insuredInfoList.getPolicyProductInfoList().get(0);

        productInfo.setMainInsurance(1);
        productInfo.setInsuredPeriod(30);

        Assert.assertEquals("2023-02-10 23:59:59", EnumYgInsuredPeriodType.ONLY_MONTH.getEndDate(productInfo, testModel));

    }

    @Test
    public void testDayGetEndDate() {
        YgPolicyContractInfoVo testModel = JMockData.mock(YgPolicyContractInfoVo.class);
        testModel.setEnforceTime("2020-08-11 00:00:00");
        YgPolicyInsuredInfoList insuredInfoList = testModel.getPolicyInsuredInfoList().get(0);
        insuredInfoList.setInsuredBirthday("1995-08-08");
        YgPolicyProductInfoList productInfo = insuredInfoList.getPolicyProductInfoList().get(0);

        productInfo.setMainInsurance(1);
        productInfo.setInsuredPeriod(30);

        Assert.assertEquals("2020-09-09 23:59:59", EnumYgInsuredPeriodType.ONLY_DAY.getEndDate(productInfo, testModel));

    }

    @Test
    public void testAgeEndDate() {
        YgPolicyContractInfoVo testModel = JMockData.mock(YgPolicyContractInfoVo.class);
        testModel.setEnforceTime("2020-08-11 00:00:00");
        YgPolicyInsuredInfoList insuredInfoList = testModel.getPolicyInsuredInfoList().get(0);
        insuredInfoList.setInsuredBirthday("1995-08-08");
        YgPolicyProductInfoList productInfo = insuredInfoList.getPolicyProductInfoList().get(0);

        productInfo.setMainInsurance(1);
        productInfo.setInsuredPeriod(30);

        Assert.assertEquals("2025-08-07 23:59:59", EnumYgInsuredPeriodType.ONLY_AGE.getEndDate(productInfo, testModel));

    }


    @Test
    public void testFullEndDate() {
        YgPolicyContractInfoVo testModel = JMockData.mock(YgPolicyContractInfoVo.class);
        testModel.setEnforceTime("2020-08-11 00:00:00");
        YgPolicyInsuredInfoList insuredInfoList = testModel.getPolicyInsuredInfoList().get(0);
        insuredInfoList.setInsuredBirthday("1995-08-08");
        YgPolicyProductInfoList productInfo = insuredInfoList.getPolicyProductInfoList().get(0);

        productInfo.setMainInsurance(1);
        productInfo.setInsuredPeriod(30);

        Assert.assertEquals("2100-08-07 23:59:59", EnumYgInsuredPeriodType.FULL.getEndDate(productInfo, testModel));

    }
}
