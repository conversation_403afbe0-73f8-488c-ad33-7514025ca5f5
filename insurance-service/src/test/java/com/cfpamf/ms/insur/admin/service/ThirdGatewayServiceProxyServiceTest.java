package com.cfpamf.ms.insur.admin.service;

import com.cfpamf.ms.insur.admin.external.AICheckQueryRequest;
import com.cfpamf.ms.insur.base.util.HttpRequestUtil;
import com.cfpamf.ms.third.gateway.api.ShortUrlFacade;
import com.cfpamf.ms.third.gateway.vo.ShortUrlGenerateReq;
import com.cfpamf.ms.third.gateway.vo.ShortUrlGenerateResp;
import com.github.jsonzou.jmockdata.JMockData;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;

import static org.junit.Assert.*;
@RunWith(PowerMockRunner.class)
@PrepareForTest({HttpRequestUtil.class})
public class ThirdGatewayServiceProxyServiceTest extends BaseTest {
    @InjectMocks
    ThirdGatewayServiceProxyService thirdGatewayServiceProxyService;
    @Mock
    ShortUrlFacade shortUrlFacade;
    @Before
    public void setUp() throws Exception {
    }

    @After
    public void tearDown() throws Exception {
    }

    @Test
    public void genShortUrl() {
        String expect = "http://t.zhnx/xdfdf";
        ShortUrlGenerateResp resp = new ShortUrlGenerateResp();
        resp.setUrl(expect);
        Mockito.when(shortUrlFacade.generateShortUrl(Mockito.any(ShortUrlGenerateReq.class))).thenReturn(resp);
        String actual = thirdGatewayServiceProxyService.genShortUrl("https://ihealth-test.zhongan.com/api/marigold/mobile/renewal/gateWay?channelCode=c20219741225001&notifyContent=CNBJ0409&renewPolicyNo=IH1100014160223912&sign=6c196344b65d1204bad660dd6f54be5d");
        Assert.assertEquals(expect,actual);
    }
}