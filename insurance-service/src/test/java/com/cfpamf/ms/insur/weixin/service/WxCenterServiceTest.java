//package com.cfpamf.ms.insur.weixin.service;
//
//import com.cfpamf.ms.insur.base.util.HttpRequestUtil;
//import org.junit.FixMethodOrder;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.junit.runners.MethodSorters;
//import org.powermock.core.classloader.annotations.PrepareForTest;
//import org.powermock.modules.junit4.PowerMockRunner;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.test.context.ActiveProfiles;
//import org.springframework.test.context.junit4.SpringRunner;
//
//@RunWith(PowerMockRunner.class)
//@PrepareForTest({HttpRequestUtil.class})
//public class WxCenterServiceTest {
//
//    @Autowired
//    private WxCcUserService centerService;
//
//    @Test
//    public void reloadWxSession() {
//        centerService.reloadWxSession("ovBrA0kbLs-Qe3c-GAPkfywtGT7I", null, null, "59325");
//    }
//}