package com.cfpamf.ms.insur.admin.service.sys;

import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.common.ms.util.DateUtil;
import com.cfpamf.ms.insur.admin.dao.safes.product.ProductMapper;
import com.cfpamf.ms.insur.admin.dao.safes.sys.SystemShareKnowledgeConfigMapper;
import com.cfpamf.ms.insur.admin.dao.safes.sys.SystemShareRefMapper;
import com.cfpamf.ms.insur.admin.pojo.dto.sys.ShareKnowledgeDTO;
import com.cfpamf.ms.insur.admin.pojo.po.sys.SystemShareFitObject;
import com.cfpamf.ms.insur.admin.pojo.po.sys.SystemShareKnowledgeConfig;
import com.cfpamf.ms.insur.admin.pojo.po.sys.SystemShareRef;
import com.cfpamf.ms.insur.admin.pojo.query.ActivityProductQuery;
import com.cfpamf.ms.insur.admin.pojo.query.sys.ShareKnowledgeProductQuery;
import com.cfpamf.ms.insur.admin.pojo.query.sys.ShareKnowledgeQuery;
import com.cfpamf.ms.insur.admin.pojo.vo.SmActivityProductVO;
import com.cfpamf.ms.insur.admin.pojo.vo.product.RecommendedProductVO;
import com.cfpamf.ms.insur.admin.pojo.vo.product.ShareKnowledgeProductVO;
import com.cfpamf.ms.insur.admin.pojo.vo.sys.ProductShareKnowledgeDetailVO;
import com.cfpamf.ms.insur.admin.pojo.vo.sys.ShareKnowledgeListVO;
import com.cfpamf.ms.insur.admin.service.SmProductService;
import com.cfpamf.ms.insur.base.util.HttpRequestUtil;
import com.github.pagehelper.PageInfo;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.boot.test.mock.mockito.MockBean;

import java.util.ArrayList;
import java.util.List;

@RunWith(PowerMockRunner.class)
@PrepareForTest({HttpRequestUtil.class})
public class SystemShareKnowledgeServiceTest {
    @InjectMocks
    SystemShareKnowledgeService shareKnowledgeService;
    @Mock
    private SystemShareKnowledgeConfigMapper systemShareKnowledgeConfigMapper;
    @Mock
    private SystemShareRefMapper systemShareRefMapper;
    @Mock
    private ProductMapper productMapper;
    @Mock
    private SmProductService smProductService;

    @Test
    public void addSystemShareKnowledge() {
        ShareKnowledgeDTO addShareKnowledgeDTO = new ShareKnowledgeDTO();
        addShareKnowledgeDTO.setJobNumber("CNBJ0409");
        addShareKnowledgeDTO.setShareTitle("1111");
        addShareKnowledgeDTO.setShareDesc("2222");
        addShareKnowledgeDTO.setShareContent("sdfadfasdavadfdadfdfdfd");
        List<SystemShareFitObject> fitObjectList = new ArrayList<>();
        SystemShareFitObject s = new SystemShareFitObject();
        s.setObjDesc("179_desc");
        s.setObjId(179);
        fitObjectList.add(s);
        s = new SystemShareFitObject();
        s.setObjDesc("141_desc");
        s.setObjId(141);
        fitObjectList.add(s);
        s = new SystemShareFitObject();
        s.setObjDesc("157_desc");
        s.setObjId(157);
        fitObjectList.add(s);
        s = new SystemShareFitObject();
        s.setObjDesc("151_desc");
        s.setObjId(151);
        fitObjectList.add(s);

        addShareKnowledgeDTO.setObjList(fitObjectList);

        Mockito.when(systemShareKnowledgeConfigMapper.insertSelective(Mockito.any(SystemShareKnowledgeConfig.class))).thenReturn(1);
        Mockito.when(systemShareRefMapper.insertList(Mockito.anyList())).thenReturn(4);
        shareKnowledgeService.addSystemShareKnowledge(addShareKnowledgeDTO);

    }

    @Test
    public void getProductShareKnowledgeConfig() {
        Integer productId = 100;
        SystemShareKnowledgeConfig ret = new SystemShareKnowledgeConfig();
        ret.setType("product");
        ret.setShareTitle("title_1");
        ret.setShareDesc("share_desc");
        ret.setShareContent("1223fadfadfaeferq3");
        Mockito.when(systemShareKnowledgeConfigMapper.getShareKnowledgeConfigByObjId(Mockito.anyInt(), Mockito.anyString())).thenReturn(ret);
        SystemShareKnowledgeConfig act = shareKnowledgeService.getProductShareKnowledgeConfig(productId);
        Assert.assertEquals(ret, act);
    }

    @MockBean
    ShareKnowledgeQuery query;

    @Test
    public void listShareKnowledge() {
        query = new ShareKnowledgeQuery();
        query.setObjId(158);
        Mockito.when(systemShareKnowledgeConfigMapper.listProductShareKnowledge(query)).thenReturn(createConfigList());
        Mockito.when(systemShareKnowledgeConfigMapper.listProductShareRefByType(Mockito.anyString(), Mockito.anyList())).thenReturn(this.createRefList());
        PageInfo<ShareKnowledgeListVO> page = shareKnowledgeService.listShareKnowledge(query);
        Assert.assertEquals(1, page.getList().size());
        Assert.assertEquals(1, page.getTotal());
    }

    private List<SystemShareKnowledgeConfig> createConfigList() {
        List<SystemShareKnowledgeConfig> lst = new ArrayList<>();
        SystemShareKnowledgeConfig config = new SystemShareKnowledgeConfig();
        config.setId(1);
        config.setType("product");
        config.setShareTitle("title_1");
        config.setShareDesc("desc_1");
        config.setShareContent("content_1");
        config.setUpdateTime(DateUtil.getNow());
        config.setUpdateBy("CNBJ0409");
        lst.add(config);
        return lst;
    }

    private List<SystemShareRef> createRefList() {
        List<SystemShareRef> lst = new ArrayList<>();
        SystemShareRef config = new SystemShareRef();
        config.setId(1);
        config.setShareId(1);
        config.setObjId(158);
        config.setObjDesc("objDesc_1");
        lst.add(config);
        config = new SystemShareRef();
        config.setId(2);
        config.setShareId(1);
        config.setObjId(179);
        config.setObjDesc("objDesc_2");
        lst.add(config);
        return lst;
    }

    @Test
    public void listRefBySharedIds() {
        ShareKnowledgeQuery query = new ShareKnowledgeQuery();
        Mockito.when(systemShareKnowledgeConfigMapper.listProductShareRefByType(Mockito.anyString(), Mockito.anyList())).thenReturn(this.createRefList());

        List<SystemShareRef> refList = shareKnowledgeService.listRefBySharedIds("a", null, false);
        Assert.assertEquals(0, refList.size());


    }

    @Test
    public void listRefBySharedId() {
        Mockito.when(systemShareKnowledgeConfigMapper.listProductShareRefByType(Mockito.anyString(), Mockito.anyList())).thenReturn(this.createRefList());
        List<SystemShareRef> refList = shareKnowledgeService.listRefBySharedIds("abc", null, true);

//        Assert.assertEquals(2,refList.size());

    }

    @Test
    public void detailProductShareKnowledge() {
        Mockito.when(systemShareKnowledgeConfigMapper.selectByPrimaryKey(Mockito.anyInt())).thenReturn(null);
        try {
            shareKnowledgeService.detailProductShareKnowledge(Mockito.anyInt());
        } catch (MSBizNormalException e) {
            Assert.assertEquals("分享知识点不存在", e.getMessage());
        }
        Mockito.when(systemShareKnowledgeConfigMapper.selectByPrimaryKey(Mockito.anyInt())).thenReturn(this.createConfig());
        Mockito.when(systemShareKnowledgeConfigMapper.listProductShareRefByType(Mockito.anyString(), Mockito.anyList())).thenReturn(this.createRefList());
        Mockito.when(productMapper.getRecommendedProductByIdList(Mockito.anyList())).thenReturn(createProductVOList());
        ProductShareKnowledgeDetailVO detailVO = shareKnowledgeService.detailProductShareKnowledge(Mockito.anyInt());
        Assert.assertNotNull(detailVO);
    }

    private SystemShareKnowledgeConfig createConfig() {

        SystemShareKnowledgeConfig config = new SystemShareKnowledgeConfig();
        config.setId(1);
        config.setType("product");
        config.setShareTitle("title_1");
        config.setShareDesc("desc_1");
        config.setShareContent("content_1");
        config.setUpdateTime(DateUtil.getNow());
        config.setUpdateBy("CNBJ0409");

        return config;
    }

    private List<RecommendedProductVO> createProductVOList() {
        List<RecommendedProductVO> lst = new ArrayList<>();
        RecommendedProductVO vo = new RecommendedProductVO();
        vo.setId(158L);
        lst.add(vo);
        return lst;
    }

    @Test
    public void listProduct() {
        ShareKnowledgeProductQuery query = new ShareKnowledgeProductQuery();
        query.setShareId(1);
        Mockito.when(smProductService.getActivityProducts(Mockito.any(ActivityProductQuery.class))).thenReturn(createActivityProductVOList());
        Mockito.when(systemShareKnowledgeConfigMapper.listProductShareRefByType(Mockito.anyString(), Mockito.anyList())).thenReturn(this.createRefList());
        PageInfo<ShareKnowledgeProductVO> page = shareKnowledgeService.listProduct(query);
        Assert.assertNotNull(page);
        Assert.assertEquals(3, page.getList().size());
    }

    @Test
    public void rand() {
        SystemShareKnowledgeConfig randomDetail = shareKnowledgeService.getRandomDetail();
        Assert.assertNotNull(randomDetail);
    }

    private PageInfo<SmActivityProductVO> createActivityProductVOList() {

        List<SmActivityProductVO> lst = new ArrayList<>();
        SmActivityProductVO vo = new SmActivityProductVO();
        vo.setProductId(158);
        lst.add(vo);
        vo = new SmActivityProductVO();
        vo.setProductId(179);
        lst.add(vo);
        vo = new SmActivityProductVO();
        vo.setProductId(178);
        lst.add(vo);

        return new PageInfo<>(lst);
    }
}
