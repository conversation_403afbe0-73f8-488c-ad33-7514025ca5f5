package com.cfpamf.ms.insur.admin.service.pco;

import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.ms.insur.admin.enums.InsErrorEnum;
import com.cfpamf.ms.insur.admin.pojo.dto.pco.SmActTalkDTO;
import com.cfpamf.ms.insur.admin.pojo.po.pco.SmActTalk;
import com.cfpamf.ms.insur.admin.pojo.query.pco.SmActTalkQuery;
import com.cfpamf.ms.insur.admin.service.BaseTest;
import com.cfpamf.ms.insur.base.util.DownloadUtil;
import com.cfpamf.ms.insur.base.util.HttpRequestUtil;
import com.github.jsonzou.jmockdata.JMockData;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.aop.framework.AopContext;
import org.springframework.mock.web.MockHttpServletResponse;
import tk.mybatis.mapper.entity.Config;
import tk.mybatis.mapper.mapperhelper.EntityHelper;

import java.io.IOException;
import java.util.Collections;

/**
 * <AUTHOR> 2021/10/15 10:33
 */
@PrepareForTest({HttpRequestUtil.class, AopContext.class, DownloadUtil.class})
@RunWith(PowerMockRunner.class)
public class SmActTalkServiceTest extends BaseTest {
    @InjectMocks
    SmActTalkService smActTalkService;
    @org.mockito.Mock
    com.cfpamf.ms.insur.admin.dao.safes.pco.SmActTalkMapper actTalkMapper;
    @org.mockito.Mock
    com.cfpamf.ms.insur.admin.dao.safes.pco.SmActTalkPersonMapper personMapper;
    @org.mockito.Mock
    com.cfpamf.ms.insur.admin.dao.safes.pco.SmActTalkImportPersonMapper importPersonMapper;
    @org.mockito.Mock
    com.cfpamf.ms.insur.admin.service.UserService userService;
    @org.mockito.Mock
    com.cfpamf.ms.insur.admin.service.DistributionProxyService distributionProxyService;
    @org.mockito.Mock
    javax.validation.Validator validator;

    @Override
    @Before
    public void setUp() throws Exception {
        super.setUp();
        PowerMockito.mockStatic(AopContext.class);
        PowerMockito.when(AopContext.currentProxy()).thenReturn(smActTalkService);
    }

    @Test
    public void countOrgTodayAdd(){
        EntityHelper.initEntityNameMap(SmActTalk.class,new Config());
        smActTalkService.countOrgTodayAdd("24");
    }

    @Test
    public void list() {
        try {
            smActTalkService.list(JMockData.mock(com.cfpamf.ms.insur.admin.pojo.query.pco.SmActTalkQuery.class, con()));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void valid() {
        try {
            smActTalkService.valid(JMockData.mock(com.cfpamf.ms.insur.admin.pojo.dto.pco.SmActTalkDTO.class, con()));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void validN() {
        try {
            SmActTalkDTO mock = JMockData.mock(SmActTalkDTO.class, con());
            mock.getPersons().stream().forEach(p -> p.setInviteType(2));
            smActTalkService.valid(mock);
        } catch (MSBizNormalException e) {
            Assert.assertEquals(e.getErrorCode(), InsErrorEnum.PARAMS_ERROR.getCode());
        }

    }

    @Test
    public void saveTalk() {
        SmActTalkDTO mock = JMockData.mock(SmActTalkDTO.class, con());
        mock.setPersons(Collections.emptyList());
        smActTalkService.saveTalk(mock, ",SXfgx,");
    }


    @Test
    public void ex() throws IOException {
        SmActTalkDTO mock = JMockData.mock(SmActTalkDTO.class, con());
        mock.setPersons(Collections.emptyList());
        smActTalkService.export(JMockData.mock(SmActTalkQuery.class,con()), new MockHttpServletResponse());
    }

//    @Test
//    public void importTalkKey() throws Exception {
//
//        SmActTalkPersonImportDTO mock = JMockData.mock(SmActTalkPersonImportDTO.class);
//        mock.setFileUrl("http://safesfiles.oss-cn-beijing.aliyuncs.com/public/temp/test-02.xlsx");
//        smActTalkService.importTalkKey(JMockData.mock(com.cfpamf.ms.insur.admin.pojo.dto.pco.SmActTalkPersonImportDTO.class));
//    }

    @Test
    public void listExcelDataByTalkKey() {
        try {
            smActTalkService.listExcelDataByTalkKey(",cNoed,");
        } catch (Exception e) {

        }
    }

    @Test
    public void genKey() {
        try {
            smActTalkService.genKey();
        } catch (Exception e) {

        }
    }

    @Test
    public void validPersons() {
        try {
            smActTalkService.validPersons(JMockData.mock(java.util.List.class));
        } catch (Exception e) {

        }
    }

    @Test
    public void detail() {
        try {
            smActTalkService.detail(3987,Boolean.TRUE);
            smActTalkService.detail(3987,Boolean.FALSE);
        } catch (Exception e) {

        }
    }

    @Test
    public void plainMobile() {
        try {
            smActTalkService.plainMobile(13102);
        } catch (Exception e) {

        }
    }

    @Test
    public void listPersons() {
        try {
            smActTalkService.listPersons(4490);
        } catch (Exception e) {

        }
    }
}
