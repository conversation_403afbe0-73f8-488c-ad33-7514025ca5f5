package com.cfpamf.ms.insur.admin.external.cic.api;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.HashMap;
import java.util.Map;


public class CicGYDemo {

    //中华私钥
    static final String privateKey = "MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBALCZPAyZweRKf4i00zIHa5TuS/LAeK1JOTmG69puyObNNH0c6m7wbD7MiecDrHySEbqjt5GUzjw6zkgDGV0AKYHBp/XtAEUIv+j55UQZZnFdqKYRPDlYYBS1nkwzKGFRMPNGpagzOQ2wBBICetTY3CTkIYQyx2CYJ8IQcoHBx2LZAgMBAAECgYA8wCoRJ5+0uhAPZi01C6ptrGBCHQYmjob6DDj8hpnkE/mXhKf9DAYgwFo8N15R6CnjK8jNcwRTYeNukGpcPN8J6RcDIqcyQa1FWpDh2vCClKHQ0xkvrb7PqBQTSa3+0oaMKkXkDcACQaDVDpGFV9N1EAUPtuR6Rh2Oy3fhXkgk0QJBAPhYq/1YcIoPD4ogu4SZm+uVkE2VDLu4EH0yTHmfvhCo06556eqYXib395cE96HWu3DRp1TtGjkU9MKxLnC75j0CQQC2CoI0TfeSdyGWquCtBxc5UUKbPX7EQdmyaZN9s19araH5sAPH4hom9hEMGvfCVrcouwA3KHbIXAsNYxGjFFTNAkBTM0HLiomJdyo5mGW6MswtzNmfZEPzz+KWTveX95BxfF4OogFf+tPwMjrceWmM6qe5UEPDC/wxC3hEkZ+BXTHxAkB4bS1DosyD5TGgNbzaWlV/woUYo67n8FQu1e8Pegk7uJ/tyQZHRglc+u2+H10GAE28sTDsTGWAGHvrSD2Tr9CFAkEA1v84BNK8xPxq4CPiLj2oggonhGDvOp03iJQWNoZ8eFsSx6Y4+t+VmvaDp5B1xwXU9AK/d1HLBdIpziN8tReV2Q==";
    //中华公钥
    static final String publicKey = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCwmTwMmcHkSn+ItNMyB2uU7kvywHitSTk5huvabsjmzTR9HOpu8Gw+zInnA6x8khG6o7eRlM48Os5IAxldACmBwaf17QBFCL/o+eVEGWZxXaimETw5WGAUtZ5MMyhhUTDzRqWoMzkNsAQSAnrU2Nwk5CGEMsdgmCfCEHKBwcdi2QIDAQAB";
    //合作方私钥
    static final String ex_privateKey = "MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAIgLfCAl37ta57rdCN2Hf+6xxBIp5zab2Bp4KrTTuSMSJIURLlN/HvlU9LFGPqqTpc3i6Yc6wdj5b2OS/4KPj7Pdek+jOxF2iPbWjE/Hx88nOlV5pQesSRm2P1i+j8qEHIErmxGMqaUQ1dUXfBdV8CDjwllHZdTJRwF1mFH1oeGhAgMBAAECgYAkBJ5lSIG2cbfeUjzRodkuEeRuRqfPXm1VfLEZ1OA0N85xaH4SXxqNOn/aG1XmBVclbkkTo2vFDSz/eOCq6WVK/4oMJTliS8fJr5r0Ty5Xw0WZFc6CDR0g/0Zp+m4k1yGtLkVlma98RgHI1E4tQeD0At+doy3EWZoQVZu9fmIi3QJBAMSDTbKXEHEMatHnfsyKc9KIP5avByTqHdcb4oUfrP/zx1bWQjpWyUPLgj5c6iuednyMbot6EU38pmgh77FWoE8CQQCxOjt6M2/DlAsibE8c7bqtmV3wqKmlnU3Pem8dehmSo9wbG7bE9mUJhFQWDndkkMGVpcaTFxL4xM05scSaBXMPAkEAi17UbCTXdFXqLJSSSV6oZlhbQPNBjdy87SLJtMCSYbTzW5L5xYZnl71t0ezz55urTQoMHL+mJjlZi+EFrjBR4QJAfvuXVBZ4tOxlEfQCt1qKUAo8acufSf+smcVCyh8LJatscKvpx7Q3bps22FrjJtYlLkEKR6fXuxDfbsA1epn+9wJAX7/1TLtV/eyDC8P6iu2CH36dqCxlh4LCXu4ukv96Rs+7yu8UadnNjkjXg7Nh0gqqW37hzKVAT7kO/sZr+IjIgQ==";
    //合作方公钥
    static final String ex_publicKey = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCIC3wgJd+7Wue63Qjdh3/uscQSKec2m9gaeCq007kjEiSFES5Tfx75VPSxRj6qk6XN4umHOsHY+W9jkv+Cj4+z3XpPozsRdoj21oxPx8fPJzpVeaUHrEkZtj9Yvo/KhByBK5sRjKmlENXVF3wXVfAg48JZR2XUyUcBdZhR9aHhoQIDAQAB";

    //测试方法主入口
    public static void main(String[] args) throws Exception {
        //投保报文
        String paramJsonStr = "<?xml version='1.0' encoding='GBK'?>"
                + "<INSUREQ>"
                + "<HEAD>"
                + "<TRANSRNO>CIC001</TRANSRNO>"
                + "<PARTNERCODE>ZHNX</PARTNERCODE>"
                + "<PARTNERSUBCODE>15</PARTNERSUBCODE>"
                + "</HEAD>"
                + "<MAIN>"
                + "<PRODUCTCODE>C0509</PRODUCTCODE><!-- 方案代码 -->"
                + "<SERIALNUMBER>CIC001201712251103</SERIALNUMBER><!-- 交易流水号 -->"
                + "<TRANSRDATE>2019-01-15 10:00:33</TRANSRDATE><!-- 交易时间 -->"
                + "<PRODUCTUNIT>1</PRODUCTUNIT><!-- 投保份数 -->"
                + "<EFFDATE>2019-01-16 00:00:00</EFFDATE>"
                + "<TERMDATE>2020-01-16 00:00:00</TERMDATE>"
                + "<CATALOGPREMIUM>100.00</CATALOGPREMIUM><!-- 总保费 -->"
                + "<BBRUNIT>1</BBRUNIT><!-- 被保险人人数 --> "
                + "<APPCONTENT>投保内容</APPCONTENT><!-- 投保内容 -->"
                + "<ARBITRATIONDEPT/><!-- 仲裁机构 -->"
                + "<TAXPAYERNO/>"
                + "</MAIN>"
                + "<HEALPOLICYINFO>"
                + "<TBR><!-- 投保人 -->"
                + "<TBRNAME>张娜义</TBRNAME><!-- 投保人姓名 -->"
                + "<TBRSEX>1</TBRSEX>"
                + "<TBRIDTYPE>01</TBRIDTYPE>"
                + "<TBRIDNO>362201199009191811</TBRIDNO>"
                + "<TBRBIRTH>1990-09-19</TBRBIRTH>"
                + "<TBRADDR/>"
                + "<TBRPOSTCODE/>"
                + "<TBRTEL>13245678765</TBRTEL>"
                + "<TBRMOBILE>13245678765</TBRMOBILE>"
                + "<TBREMAIL><EMAIL></TBREMAIL>"
                + "<TBRBBRRELATION>01</TBRBBRRELATION>"
                + "</TBR>"
                + "<BBRS><!-- 被保人 -->"
                + "<BBR>"
                + "<BBRNAME>张娜义</BBRNAME>"
                + "<BBRSEX>1</BBRSEX>"
                + "<BBRBIRTH>1990-09-19</BBRBIRTH>"
                + "<BBRIDTYPE>01</BBRIDTYPE>"
                + "<BBRIDNO>362201199009191811</BBRIDNO>"
                + "<BBRADDR/>"
                + "<BBRPOSTCODE/>"
                + "<BBRTEL/>"
                + "<BBRMOBILE/>"
                + "<BBREMAIL/>"
                + "<BBRWORKTYPE>010107</BBRWORKTYPE>"
                + "<BBRCATEGORY/>"
                + "<BENIFITMARK>Y</BENIFITMARK>"
                + "<SYRS><!-- 受益人 -->"
                + "<SYR>"
                + "<SYRNAME>陈俊铮</SYRNAME>"
                + "<SYRSEX>1</SYRSEX>"
                + "<SYRBIRTH>1988-03-12</SYRBIRTH>"
                + "<SYRIDTYPE>01</SYRIDTYPE>"
                + "<SYRIDNO>430124198803120034</SYRIDNO>"
                + "<RELATIONTOINSURED/>"
                + "<BENIFITPROPORTION>100</BENIFITPROPORTION>"
                + "</SYR>"
                + "</SYRS>"
                + "</BBR>"
                + "</BBRS>"
                + "<STUINFO>"
                + "<SCHOOLNATURE>01</SCHOOLNATURE>"
                + "<STUDENTTYPE>07</STUDENTTYPE>"
                + "<SCHOOLTYPE>01</SCHOOLTYPE>"
                + "</STUINFO>	"
                + "<TRAVEL>"
                + "<GROUPNO>1234</GROUPNO>"
                + "<COUNTRYCODE>2314</COUNTRYCODE>"
                + "<COUNTRYNAME>234</COUNTRYNAME>"
                + "<LINE>2341</LINE>"
                + "</TRAVEL>"
                + "</HEALPOLICYINFO>"
                + "</INSUREQ>";

        String responses = interaction(null, "http://61.138.246.87:6001/NLifeServiceST", paramJsonStr, "ZLBX", "CIC001");
//        System.out.println("报文返回：" + responses);
    }

    //请求中华方交易系统
    public static String interaction(String sing, String requestUrl, String requestXml, String GW_CH_CODE, String GW_CH_TX) {
        Map<String, String> responseMap = new HashMap<String, String>();
        StringBuffer responseXml = new StringBuffer();
        HttpURLConnection httpConn = null;
        OutputStream out = null;
        BufferedReader in = null;
        URL url = null;
        try {
            url = new URL(requestUrl);
            httpConn = (HttpURLConnection) url.openConnection();
            byte[] buf = requestXml.getBytes("GBK");
            httpConn.setRequestProperty("Content-Length", String.valueOf(buf.length));
            httpConn.setRequestProperty("Content-Type", "text/xml; charset=gbk");
            httpConn.setRequestProperty("GW_FACADE_FLAG", "1");
            httpConn.setRequestProperty("GW_CH_TX", GW_CH_TX);
            httpConn.setRequestProperty("GW_CH_CODE", "ZHNX");
            httpConn.setRequestProperty("GW_CH_USER", "ZHNX");
            httpConn.setRequestProperty("GW_CH_PWD", "ZHNX");
//            httpConn.setRequestProperty("GW_CH_SING", sing);
            httpConn.setRequestMethod("POST");
            httpConn.setDoOutput(true);
            httpConn.setDoInput(true);
            httpConn.setConnectTimeout(60000);
            httpConn.setReadTimeout(60000);
            out = httpConn.getOutputStream();
            out.write(buf);
            out.flush();
            in = new BufferedReader(new InputStreamReader(httpConn.getInputStream(), "gbk"));
            String inputLine = "";
            //获取响应的Header信息
            //获取响应报文
            while ((inputLine = in.readLine()) != null) {
                responseXml.append(inputLine);
            }

        } catch (IOException e) {
//            System.out.println("交易系统请求失败");
        } finally {
            close(out);
            close(in);
            close(httpConn);
        }
        return responseXml.toString();
    }

    private static void close(HttpURLConnection httpConn) {
        if (httpConn != null) {
            httpConn.disconnect();
        }
    }

    public static void close(OutputStream out) {
        if (out != null) {
            try {
                out.close();
            } catch (IOException e) {
                // TODO Auto-generated catch block
//                System.out.println("释放OutputStream失败");
            }
        }
    }

    public static void close(BufferedReader in) {
        if (in != null) {
            try {
                in.close();
            } catch (IOException e) {
                // TODO Auto-generated catch block
//                System.out.println("释放BufferedReader失败");
            }
        }
    }

}
