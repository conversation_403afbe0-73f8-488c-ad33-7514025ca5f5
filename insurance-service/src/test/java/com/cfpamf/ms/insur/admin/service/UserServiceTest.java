package com.cfpamf.ms.insur.admin.service;

import com.cfpamf.ms.insur.admin.dao.safes.*;
import com.cfpamf.ms.insur.admin.job.SyncEmployeeJobHandler;
import com.cfpamf.ms.insur.admin.pojo.query.ChangeUserQuery;
import com.cfpamf.ms.insur.admin.pojo.vo.UserVO;
import com.cfpamf.ms.insur.base.bean.Pageable;
import com.cfpamf.ms.insur.base.infeface.CustomerCenterService;
import com.cfpamf.ms.insur.base.service.BmsService;
import com.cfpamf.ms.insur.base.util.HttpRequestUtil;
import com.cfpamf.ms.insur.weixin.service.WxCcUserService;
import com.github.jsonzou.jmockdata.JMockData;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/9/27 17:31
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({HttpRequestUtil.class})
public class UserServiceTest extends BaseTest {

    @InjectMocks
    UserService userService;

    /**
     * 车险授权service
     */
    @Mock
    private AmAuthService authService;

    /**
     * 客户中心service
     */
    @Mock
    private CustomerCenterService ccService;

    /**
     * 用户mapper
     */
    @Mock
    private AuthUserMapper userMapper;

    /**
     * 用户权限mapper
     */
    @Mock
    private UserAuthorityMapper uaMapper;

    /**
     * 微信用户中心service
     */
    @Mock
    private WxCcUserService ccUserService;

    /**
     * 同步员工定时任务
     */
    @Mock
    private SyncEmployeeJobHandler syncTask;

    /**
     * OmsService
     */
    @Mock
    private BmsService bmsService;

    /**
     * 代理人service
     */
    @Mock
    private SmAgentService agentService;

    /**
     * 组织mapper
     */
    @Mock
    private OrgMapper orgMapper;

    /**
     * 机构负责人mapper
     */
    @Mock
    private OrgPicExtraMapper picMapper;

    @Mock
    private UserPostMapper userPostMapper;
    @Mock
    private UserSyncService userSyncService;
    @Mock
    private UserPostService userPostService;

    @Test
    public void testGetChangeUsersByPage(){
        try {
            userService.getChangeUsersByPage(Mockito.mock(ChangeUserQuery.class));
        } catch (Exception e) {
        }
    }

    @Test
    public void testGetUsersByPage() throws Exception {
        setUp();
        userService.getUsersByPage(Mockito.anyString(),Mockito.anyBoolean(),new Pageable());
    }

    @Override
    public void setUp() throws Exception {
        super.setUp();
        UserVO userVO = JMockData.mock(UserVO.class);
        userVO.setUserMobile("15700718734");
        List<UserVO> userVOS = new ArrayList<>();
        userVOS.add(userVO);

        Mockito.when(userMapper.listUsers(Mockito.anyString(), Mockito.anyBoolean()))
                .thenReturn(userVOS);
    }
}
