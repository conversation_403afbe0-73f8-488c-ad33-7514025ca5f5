package com.cfpamf.ms.insur.test;

import com.cfpamf.ms.insur.base.util.ExcelReadUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;

/**
 * <AUTHOR> 2020/3/17 15:00
 */
public class OccReWriteUtil {

    public static void main(String[] args) throws IOException {

        String o1 = null;

        String o2 = null;

        String o3;
        String o3Code;

        String type;

//        File file = new File("/Users/<USER>/doc/zhnx/保险/thrid/诚泰职业编码/诚泰职业分类2020版.xlsx ");
        File file = new File("/Users/<USER>/doc/zhnx/保险/thrid/诚泰职业编码/诚泰职业分类2020版.xlsx");
        Workbook workbook = new XSSFWorkbook(new FileInputStream(file));
        HSSFWorkbook sheets = new HSSFWorkbook();
        HSSFSheet sheet = sheets.createSheet();
        Sheet sheetAt = workbook.getSheetAt(0);
        int lastRowNum = sheetAt.getLastRowNum();
        int addIndex = 0;
        for (int i = 1; i < lastRowNum; i++) {
            Row row = sheetAt.getRow(i);
            if (row == null) {
                continue;
            }
            
            String tmpo1 = ExcelReadUtil.getCellValue(row.getCell(0));
            String tmpo2 = ExcelReadUtil.getCellValue(row.getCell(1));
            o3Code = ExcelReadUtil.getCellValue(row.getCell(2));
            o3 = ExcelReadUtil.getCellValue(row.getCell(3));
            type = ExcelReadUtil.getCellValue(row.getCell(4));

            if (StringUtils.isBlank(o3Code)) {
                continue;
            }
            if (StringUtils.isNotBlank(tmpo1)) {
                o1 = tmpo1;
            }
            if (StringUtils.isNotBlank(tmpo2)) {
                o2 = tmpo2;
            }
            if (StringUtils.endsWith(type, ".0")) {
                type = type.substring(0, type.length() - 2);
            }
            if (StringUtils.endsWith(o3Code, ".0")) {
                o3Code = o3Code.substring(0, o3Code.length() - 2);
            }
            String o1Code = o3Code.substring(0, 2);
            String o2Code = o3Code.substring(0, 4);
            HSSFRow row1 = sheet.createRow(addIndex++);
            row1.createCell(0).setCellValue(o1Code);
            row1.createCell(1).setCellValue(o1);
            row1.createCell(2).setCellValue(o2Code);
            row1.createCell(3).setCellValue(o2);
            row1.createCell(4).setCellValue(o3Code);
            row1.createCell(5).setCellValue(o3);
            row1.createCell(6).setCellValue(type);
            System.err.println(String.format("%s\t%s\t%s:%s %s", o1, o2, o3, o3Code, type));
        }
        sheets.write(new File("ct-occ.xls"));
    }
}
