package com.cfpamf.ms.insur.base.util;

import com.cfpamf.ms.insur.admin.pojo.po.order.SmOrderRiskDuty;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR> 2021/4/22 11:07
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class MapperUtilTest {

    public static void main(String[] args) {

        //
//        @Mappings({
//                @Mapping(source = "name", target = "personName"),
//                @Mapping(source = "gender", target = "personGender"),
//                @Mapping(source = "certType", target = "idType"),
//                @Mapping(source = "certNo", target = "idNumber"),
//                @Mapping(source = "birthday", target = "birthday"),
//                @Mapping(source = "phone", target = "cellPhone"),
////            @Mapping(source = "email", target = "email"),
//                @Mapping(source = "hasSocialInsurance", target = "isSecurity"),
//                @Mapping(source = "relationToHolder", target = "relationship"),
//        })
//        FhInsuredPerson cvtPerson (ZaCustomerDTO insuredInfo);
        Class<?> clazz = SmOrderRiskDuty.class;
        String collect = Arrays.stream(clazz.getDeclaredFields())
                .map(field -> {
                    ApiModelProperty annotation = field.getAnnotation(ApiModelProperty.class);
                    String m = Objects.nonNull(annotation) ? annotation.value() : "";
                    return "\t//no mapping "+ m + "\n@Mapping(source = \"" + field.getName() + "\", target = \"" + field.getName() + "\")";
                }).collect(Collectors.joining(",\n"));

        String str = "    @Mappings({\n${fs}" +
                "    })\n" +
                "    ${simpleName} cvt${simpleName}(${simpleName} model);";
        HashMap<String, String> mp = new HashMap<>();
        mp.put("fs", collect);
        mp.put("simpleName", clazz.getSimpleName());

        System.err.println(strFormatUsingDict(str, mp));

    }

    public static String strFormatUsingDict(String template, Map<String, String> dict) {
        String patternString = "\\$\\{(" + StringUtils.join(dict.keySet(), "|") + ")\\}";

        Pattern pattern = Pattern.compile(patternString);
        Matcher matcher = pattern.matcher(template);

        StringBuffer sb = new StringBuffer();
        while (matcher.find()) {
            matcher.appendReplacement(sb, dict.get(matcher.group(1)));
        }
        matcher.appendTail(sb);
        return sb.toString();
    }
}
