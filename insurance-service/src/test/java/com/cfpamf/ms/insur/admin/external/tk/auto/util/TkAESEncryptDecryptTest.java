package com.cfpamf.ms.insur.admin.external.tk.auto.util;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;

/**
 * <AUTHOR> 2021/4/6 15:03
 */
public class TkAESEncryptDecryptTest {


    String userId = null;

    @Before
    public void setUp() throws Exception {
        userId = "ZHNX09760-osUxv1opgsu4AWXfOaYs_VISY33Q";
    }

    @Test
    public void encrypt() {

        // {工号}-{wxOpenId}
        TkAutoAESEncryptDecrypt.encrypt(userId, "A9CAED282E464191");

    }

    @Test
    public void decrypt() {
        String decrypt = TkAutoAESEncryptDecrypt.decrypt("08cd5d74ba5598a09f4b03bd28ca33a7a5beac4be6fb6c3af9f24fca8341cb1ab7794569b327cd3b78641802cfae0f52",
                "A9CAED282E464191");
        Assert.assertEquals(userId, decrypt);
    }

    @Test
    public void md5() {
        TkMD5Util.md5("64191Qguoxu");
    }

    @Test
    public void queryString() {

        TkMD5Util.md5("64191Qguoxu");
    }
}
