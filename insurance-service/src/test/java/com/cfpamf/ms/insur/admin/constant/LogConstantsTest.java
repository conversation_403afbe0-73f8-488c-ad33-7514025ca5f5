package com.cfpamf.ms.insur.admin.constant;

import com.cfpamf.ms.insur.base.constant.LogConstants;
import org.junit.Test;

import java.lang.reflect.Field;
import java.util.stream.Stream;

public class LogConstantsTest {

    @Test
    public void testGetAllField() {
        Class clazz = LogConstants.class;
        Field[] fields = LogConstants.class.getDeclaredFields();
        Stream.of(fields).forEach(f -> {
//            System.out.println(f.getName());
            try {
                System.out.println(f.get(clazz));
            } catch (IllegalAccessException e) {
                e.printStackTrace();
            }
        });
    }

    @Test
    public void testHex(){
        String s = Long.toHexString(1680136);

        System.err.println(s);
    }

}
