package com.cfpamf.ms.insur.admin.event.handler;

import com.cfpamf.ms.insur.admin.constant.SmConstants;
import com.cfpamf.ms.insur.admin.dao.safes.SmOrderSmsMapper;
import com.cfpamf.ms.insur.admin.enums.EnumOrderSubChannel;
import com.cfpamf.ms.insur.admin.pojo.po.SmOrderSms;
import com.cfpamf.ms.insur.admin.pojo.vo.SmOrderSmsNotifyVO;
import com.cfpamf.ms.insur.admin.service.BaseTest;
import com.cfpamf.ms.insur.admin.service.SmOrderManageService;
import com.cfpamf.ms.insur.base.config.UDCConfig;
import com.cfpamf.ms.insur.base.config.WechatConfig;
import com.cfpamf.ms.insur.base.util.DataMaskUtil;
import com.cfpamf.ms.insur.base.util.HttpRequestUtil;
import com.cfpamf.ms.insur.base.util.SmsSenderUtil;
import com.cfpamf.ms.insur.base.util.SpringFactoryUtil;
import com.github.jsonzou.jmockdata.JMockData;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.Collections;
import java.util.Objects;

@Slf4j
@RunWith(PowerMockRunner.class)
@PrepareForTest({HttpRequestUtil.class})
public class OrderAppSuccessSmsEventHandlerTest extends BaseTest {

    @Mock
    SmOrderSmsMapper smOrderSmsMapper;

    @Mock
    private SmsSenderUtil smsSenderUtil;

    @Mock
    private WechatConfig wechatConfig;

    @Test
    public void processGroupOrder() {
        String orderId = JMockData.mock(String.class);
        SmOrderManageService orderManageService = SpringFactoryUtil.getBean(SmOrderManageService.class);
        orderManageService.getGroupOrderNotifyMessage(orderId)
                .stream()
                .filter(order -> !Objects.equals(order.getSubChannel(), EnumOrderSubChannel.IMPORT.getCode()))
                .forEach(odr -> {
                    String messageCode = chooseMessageCode(SmConstants.PRODUCT_ATTR_GROUP, StringUtils.isNotBlank(odr.getOrderAdminName()));
                    String planName =odr.getPlanName()==null?"":odr.getPlanName();
                    smsSenderUtil.sendUWSMessage(Collections.singletonList(odr.getApplicantCellPhone()),
                            SmsSenderUtil.SmsGroupUwsDTO
                                    .builder()
                                    .productName(odr.getProductName())
                                    .planName(planName)
                                    .policyNo(odr.getPolicyNo())
                                    .personName(odr.getApplicantPersonName())
                                    .orderAmount(odr.getTotalAmount())
                                    .downloadUrl(wechatConfig.getFrontPolicySearchUrl())
                                    .contactName(odr.getOrderAdminName())
                                    .contactMobile(odr.getOrderAminMobile())
                                    .build(),messageCode);
                });
        Assert.assertTrue(true);
    }



    private String chooseMessageCode(String productType,boolean haveContract){
        UDCConfig udcConfig = smsSenderUtil.getUdcConfig();
        if(SmConstants.PRODUCT_ATTR_GROUP.equalsIgnoreCase(productType)){
            return haveContract?udcConfig.getUwsGroupTemplateHcCode():udcConfig.getUwsGroupTemplateNcCode();
        }
        if(SmConstants.PRODUCT_ATTR_PERSON.equalsIgnoreCase(productType)){
            return haveContract?udcConfig.getUwsPersonTemplateHcCode():udcConfig.getUwsPersonTemplateNcCode();
        }
        /**
         * 获取默认的模板
         */
        return haveContract?udcConfig.getUwsTemplateHcCode():udcConfig.getUwsTemplateNcCode();
    }

    @Test
    public void processPersonOrder() {
        String orderId = JMockData.mock(String.class);
        SmOrderManageService orderManageService = SpringFactoryUtil.getBean(SmOrderManageService.class);
        // 当前被保人保单更新成承保成功
        orderManageService.getOrderSmsNotifyList(orderId)
                .stream()
                //排除线下单
                .filter(order -> !Objects.equals(order.getSubChannel(), EnumOrderSubChannel.IMPORT.getCode()))
                //排除团险
                .filter(order -> !Objects.equals(order.getProductAttrCode(), SmConstants.PRODUCT_ATTR_GROUP))
                .forEach(odr -> {
                    // 发送待续保短信(无客户经理联系方式)
                    if (StringUtils.isEmpty(odr.getOrderAdminName())) {
                        sendUwOrderNcMessage(odr);
                    }
                    // 发送待续保短信（有客户经理联系方式）
                    else {
                        sendUwsOrderHcMessage(odr);
                    }
                    SmOrderSms smOrderSms = new SmOrderSms();
                    smOrderSms.setFhOrderId(odr.getFhOrderId());
                    smOrderSms.setInsuredId(odr.getInsuredId());
                    smOrderSms.setPolicyNo(odr.getPolicyNo());
                    smOrderSms.setState(1);
                    smOrderSmsMapper.insertUseGeneratedKeys(smOrderSms);
                });
        Assert.assertTrue(true);
    }

    private void sendUwOrderNcMessage(SmOrderSmsNotifyVO odr) {
        smsSenderUtil.sendUwsNcMessage(Collections.singletonList(odr.getApplicantCellPhone()),
                SmsSenderUtil.SmsUwsNcDTO
                        .builder()
                        .productName(odr.getProductName() + odr.getPlanName())
                        .policyNo(odr.getPolicyNo())
                        .personName(DataMaskUtil.maskFullName(odr.getInsuredPersonName()))
                        .orderAmount(odr.getTotalAmount())
                        .downloadUrl(wechatConfig.getFrontPolicySearchUrl())
                        .build());
    }

    private void sendUwsOrderHcMessage(SmOrderSmsNotifyVO odr) {
        smsSenderUtil.sendUwsHcMessage(Collections.singletonList(odr.getApplicantCellPhone()), SmsSenderUtil.SmsUwsHcDTO
                .builder()
                .productName(odr.getProductName() + odr.getPlanName())
                .policyNo(odr.getPolicyNo())
                .personName(DataMaskUtil.maskFullName(odr.getInsuredPersonName()))
                .orderAmount(odr.getTotalAmount())
                .downloadUrl(wechatConfig.getFrontPolicySearchUrl())
                .contactName(odr.getOrderAdminName())
                .contactMobile(odr.getOrderAminMobile())
                .build());
    }
}
