package com.cfpamf.ms.insur.admin.service.product;

import com.alibaba.fastjson.JSONArray;
import com.cfpamf.ms.insur.admin.dao.safes.product.risk.*;
import com.cfpamf.ms.insur.admin.pojo.dto.product.SysRiskFactorDTO;
import com.cfpamf.ms.insur.admin.pojo.po.product.risk.SysRiskFactorConfig;
import com.cfpamf.ms.insur.admin.pojo.vo.DictionaryVO;
import com.cfpamf.ms.insur.admin.service.DictionaryService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.List;

/**
 * <AUTHOR> 2021/7/2 10:12
 */
@RunWith(SpringJUnit4ClassRunner.class)
public class SysRiskFactorPriceServiceTest {
    @Mock
    DictionaryService dictionaryService;

    /**
     * 页面配置信息
     */
    @Mock
    SysRiskFactorFileMapper factorFileMapper;

    @Mock
    SysRiskFactorConfigMapper factorConfigMapper;
    /**
     *
     */
    @Mock
    SysRiskFactorMapper factorMapper;
    @Mock
    SysRiskFactorPriceMapper factorPriceMapper;

    @Mock
    SysRiskDutyMapper dutyMapper;
    @Mock
    SysRiskMapper riskMapper;

    @InjectMocks
    SysRiskFactorPriceService priceService;

    @Before
    public void setUp() throws Exception {
        String strs = "[\n" +
                "  {\n" +
                "    \"code\": \"underWritingAge\",\n" +
                "    \"name\": \"承保年龄\"\n" +
                "  },\n" +
                "  {\n" +
                "    \"code\": \"validPeriod\",\n" +
                "    \"name\": \"保险期限\"\n" +
                "  },\n" +
                "  {\n" +
                "    \"code\": \"socialSecurity\",\n" +
                "    \"name\": \"社保\"\n" +
                "  },\n" +
                "  {\n" +
                "    \"code\": \"vehicleSeatNumber\",\n" +
                "    \"name\": \"车辆座位数\"\n" +
                "  },\n" +
                "  {\n" +
                "    \"code\": \"sex\",\n" +
                "    \"name\": \"性别\"\n" +
                "  },\n" +
                "  {\n" +
                "    \"code\": \"occuplCategory\",\n" +
                "    \"name\": \"职业\"\n" +
                "  },\n" +
                "  {\n" +
                "    \"code\": \"protonHeavyIonMedicine\",\n" +
                "    \"name\": \"质重医疗\"\n" +
                "  },\n" +
                "  {\n" +
                "    \"code\": \"specifiedDiseaseSpecificCare\",\n" +
                "    \"name\": \"特需医疗\"\n" +
                "  },\n" +
                "  {\n" +
                "    \"code\": \"japanMedicalTreatment\",\n" +
                "    \"name\": \"赴日医疗\"\n" +
                "  },\n" +
                "  {\n" +
                "    \"code\": \"smoke\",\n" +
                "    \"name\": \"有无吸烟史\"\n" +
                "  },\n" +
                "  {\n" +
                "    \"code\": \"amount\",\n" +
                "    \"name\": \"基本保额\"\n" +
                "  },\n" +
                "  {\n" +
                "    \"code\": \"payType\",\n" +
                "    \"name\": \"缴费方式\"\n" +
                "  }\n" +
                "]";

        List<DictionaryVO> dictionaries = JSONArray.parseArray(strs, DictionaryVO.class);
        Mockito.when(dictionaryService.listByType(Mockito.anyString()))
                .thenReturn(dictionaries);
    }

    @Test
    public void importAll() {

        SysRiskFactorDTO test = new SysRiskFactorDTO();

        final SysRiskFactorConfig config = new SysRiskFactorConfig();

    }

    @Test
    public void importParse() {
    }

    @Test
    public void cvtTimeRange() {
    }
}
