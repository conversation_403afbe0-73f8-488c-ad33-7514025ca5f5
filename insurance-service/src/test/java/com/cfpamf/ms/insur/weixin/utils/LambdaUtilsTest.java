package com.cfpamf.ms.insur.weixin.utils;

import com.cfpamf.ms.insur.base.util.LambdaUtils;
import lombok.Data;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class LambdaUtilsTest {

    @Test
    public void safeToMap(){
        List<Cat> cats = new ArrayList<>();

        cats.add(new Cat(1,"<PERSON>"));
        cats.add(new Cat(2,"<PERSON>"));
        cats.add(new Cat(3,"<PERSON><PERSON>"));
        cats.add(new Cat(null,null));
        cats.add(null);
        Map<Integer,String> rtnMap = LambdaUtils.safeToMap(cats,Cat::getId,Cat::getName);
        System.out.println(rtnMap);
        Assert.assertTrue(!CollectionUtils.isEmpty(rtnMap));
    }

    @Test
    public void toMap(){
        List<Cat> cats = new ArrayList<>();

        cats.add(new Cat(1,"<PERSON>"));
        cats.add(new Cat(2,"<PERSON>"));
        cats.add(new Cat(3,"<PERSON><PERSON>"));
        cats.add(null);
        Map<Integer,Cat> rtnMap = LambdaUtils.toMap(cats,Cat::getId);
        System.out.println(rtnMap);
        Assert.assertTrue(!CollectionUtils.isEmpty(rtnMap));
    }

    @Data
    public class Cat{

        private Integer id;

        private String name;

        public Cat(Integer id,String name){
            this.id=id;
            this.name=name;
        }
    }
}
