package com.cfpamf.ms.insur.admin.external.tk;

import com.cfpamf.common.ms.util.DateUtil;
import com.cfpamf.ms.insur.admin.dao.safes.SmOrderMapper;
import com.cfpamf.ms.insur.admin.dao.safes.order.SmOrderItemMapper;
import com.cfpamf.ms.insur.admin.external.tk.model.employer.TkEmpEndorOrderReq;
import com.cfpamf.ms.insur.admin.external.tk.model.employer.TkEmpEndorStakeHolder;
import com.cfpamf.ms.insur.admin.pojo.po.SmOrder;
import com.cfpamf.ms.insur.admin.pojo.po.SmOrderApplicant;
import com.cfpamf.ms.insur.admin.pojo.po.order.SmOrderItem;
import com.cfpamf.ms.insur.admin.pojo.vo.SmBaseOrderVO;
import com.cfpamf.ms.insur.admin.pojo.vo.SmPlanVO;
import com.cfpamf.ms.insur.admin.service.BaseTest;
import com.cfpamf.ms.insur.base.util.HttpRequestUtil;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@RunWith(PowerMockRunner.class)
@PrepareForTest({HttpRequestUtil.class})
public class TkOrderServiceAdapterTest  extends BaseTest {
    @InjectMocks
    TkOrderServiceAdapterImpl tkOrderServiceAdapter;
    @Mock
    SmOrderMapper orderMapper;
    @Mock
    SmOrderItemMapper smOrderItemMapper;
    @Test
    public void testGenAgentEmployerUrl() {
        String url_1 = "http://test.cloud.tk.cn/tkpage/12N00068/#/?referralCode=";
        String url_2 = "http://test.cloud.tk.cn/tkpage/12N00068/#/?referralCode=CNBJ0409";
        String url_3 = tkOrderServiceAdapter.genAgentEmployerUrl(url_1,"CNBJ0409");
        Assert.assertEquals("验证通过",url_2,url_3);
    }

    @Test
    public void cvtByEmployerEndorsementNotify(){
        //批增
        tkOrderServiceAdapter.cvtByEmployerEndorsementNotify("",this.createIncreateTkEmpEndorOrderReq(),this.createSmBaseOrderVO(),this.createSmOrderApplicant(),this.createSmPlanVO());

        //批减
        Mockito.when(smOrderItemMapper.listOriginalPolicyByOrderId(Mockito.anyString(),Mockito.anyList())).thenReturn(this.createOrderItemList());

        Mockito.when(orderMapper.listSmOrderByOrderIds(Mockito.anyList())).thenReturn(this.createOrderList());
        tkOrderServiceAdapter.cvtByEmployerEndorsementNotify("",this.createDecreateTkEmpEndorOrderReq(),this.createSmBaseOrderVO(),this.createSmOrderApplicant(),this.createSmPlanVO());

    }
    private TkEmpEndorOrderReq createIncreateTkEmpEndorOrderReq(){
        TkEmpEndorOrderReq req = new TkEmpEndorOrderReq();
        req.setEndorType(TkConsts.ENDOR_TYPE_INCREASE);
        req.setEndorDate(LocalDateTime.parse("2021-11-12T17:27:46"));
        req.setEndorNo("E211112002038910105920");
        req.setPolicyNo("H211112001303860105920");
        req.setUnderWriteEndDate(LocalDateTime.parse("2021-11-12T17:34:50"));
        List<TkEmpEndorStakeHolder> stakeholderList = new ArrayList<>();
        req.setStakeholderList(stakeholderList);
        TkEmpEndorStakeHolder holder = new TkEmpEndorStakeHolder();
        holder.setChangeGrossPremium("186.000000");
        holder.setCredentialNo("11010019950202788X");
        holder.setCredentialType("01");
        holder.setName("啊啊啊");
        holder.setOccupationLevel("1");
        stakeholderList.add(holder);

        holder = new TkEmpEndorStakeHolder();
        holder.setChangeGrossPremium("584.000000");
        holder.setCredentialNo("11010019950202789X");
        holder.setCredentialType("01");
        holder.setName("啊啊啊");
        holder.setOccupationLevel("1");
        stakeholderList.add(holder);


        return req;
    }
    private TkEmpEndorOrderReq createDecreateTkEmpEndorOrderReq(){
        TkEmpEndorOrderReq req = new TkEmpEndorOrderReq();
        req.setEndorType(TkConsts.ENDOR_TYPE_DECREASE);
        req.setEndorDate(LocalDateTime.parse("2021-11-12T17:27:46"));
        req.setEndorNo("E211112002038910105920");
        req.setPolicyNo("H211112001303860105920");
        req.setUnderWriteEndDate(LocalDateTime.parse("2021-11-12T17:34:50"));
        List<TkEmpEndorStakeHolder> stakeholderList = new ArrayList<>();
        req.setStakeholderList(stakeholderList);
        TkEmpEndorStakeHolder holder = new TkEmpEndorStakeHolder();
        holder.setChangeGrossPremium("-86.000000");
        holder.setCredentialNo("11010019950202788X");
        holder.setCredentialType("01");
        holder.setName("啊啊啊");
        holder.setOccupationLevel("1");
        stakeholderList.add(holder);

        holder = new TkEmpEndorStakeHolder();
        holder.setChangeGrossPremium("-26.000000");
        holder.setCredentialNo("11010019950202789X");
        holder.setCredentialType("01");
        holder.setName("啊啊啊");
        holder.setOccupationLevel("1");
        stakeholderList.add(holder);
        return req;
    }
    private SmBaseOrderVO createSmBaseOrderVO(){
        SmBaseOrderVO vo = new SmBaseOrderVO();
        vo.setFhOrderId("TEST001");
        vo.setEndTime(DateUtil.getNow());
        vo.setStartTime(DateUtil.getNow());
        return vo;
    }
    private SmOrderApplicant createSmOrderApplicant(){
        SmOrderApplicant applicant = new SmOrderApplicant();
        return applicant;
    }
    private SmPlanVO createSmPlanVO(){
        SmPlanVO vo = new SmPlanVO();
        return vo;
    }

    private List<SmOrderItem> createOrderItemList(){
        List<SmOrderItem> orderItemList = new ArrayList<>();
        SmOrderItem item = new SmOrderItem();
        item.setFhOrderId("TEST001");
        item.setIdNumber("11010019950202788X");
        item.setTotalAmount(new BigDecimal("200"));
        orderItemList.add(item);

        item = new SmOrderItem();
        item.setFhOrderId("TEST001");
        item.setIdNumber("11010019950202789X");
        item.setTotalAmount(new BigDecimal("100"));
        orderItemList.add(item);
        return orderItemList;
    }

    private List<SmOrder> createOrderList(){
        List<SmOrder> orderList = new ArrayList<>();
        SmOrder order = new SmOrder();
        order.setEndTime(LocalDateTime.now());
        order.setStartTime(LocalDateTime.now());
        order.setFhOrderId("TEST001");
        orderList.add(order);
        return orderList;
    }


}