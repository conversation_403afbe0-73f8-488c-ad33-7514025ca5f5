package com.cfpamf.ms.insur.admin.external.dj.api;

import com.cfpamf.ms.insur.admin.enums.EnumOrderSubChannel;
import com.cfpamf.ms.insur.admin.service.BaseTest;
import com.cfpamf.ms.insur.base.util.HttpRequestUtil;
import com.github.jsonzou.jmockdata.JMockData;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.mock.web.MockHttpServletResponse;

import java.io.IOException;

/**
 * <AUTHOR> 2020/3/24 17:20
 */
@Slf4j
@RunWith(PowerMockRunner.class)
@PrepareForTest({HttpRequestUtil.class})
public class DjApiServiceTest extends BaseTest {
    @InjectMocks
    DjApiService djApiService;
    @Mock
    com.cfpamf.ms.insur.admin.external.dj.model.DjApiProperties properties;
    @Mock
    com.cfpamf.ms.insur.admin.service.SmOutApiErrorService errorService;
    @Mock
    com.cfpamf.ms.insur.admin.external.dj.client.DjOrderClient orderClient;
    @Mock
    com.cfpamf.ms.insur.admin.external.dj.client.DjPayClient payClient;
    @Mock
    com.cfpamf.ms.insur.admin.external.dj.client.DjPayQueryClient payQueryClient;
    @Mock
    com.cfpamf.ms.insur.admin.dao.safes.SmOrderMapper orderMapper;

    @Test
    public void check() {
        djApiService.check(JMockData.mock(com.cfpamf.ms.insur.admin.external.dj.model.req.DjCheckReq.class));
    }

    @Test
    public void getPaySubmitReq() {
        djApiService.getPaySubmitReq(",alxsx,", EnumOrderSubChannel.XIANGZHU.getCode());
    }

    @Test
    public void payHtml() throws IOException {
        djApiService.payHtml(",LWPSq,", new MockHttpServletResponse());
    }
}
