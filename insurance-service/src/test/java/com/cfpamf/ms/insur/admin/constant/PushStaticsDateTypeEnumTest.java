//package com.cfpamf.ms.insur.admin.constant;
//
//import org.junit.Test;
//
//import java.util.Date;
//import java.util.stream.Stream;
//
//public class PushStaticsDateTypeEnumTest {
//
//    @Test
//    public void testGetTime() {
//        Date today = new Date();
//        Stream.of(PushStaticsDateTypeEnum.values())
//                .forEach(enm -> {
////                    System.out.println(enm.getName() + "startTime=" + DateUtil.format(enm.getStartTime(today), DateUtil.CN_LONG_FORMAT));
////                    System.out.println(enm.getName() + "endTime=" + DateUtil.format(enm.getEndTime(today), DateUtil.CN_LONG_FORMAT));
//                });
//    }
//}