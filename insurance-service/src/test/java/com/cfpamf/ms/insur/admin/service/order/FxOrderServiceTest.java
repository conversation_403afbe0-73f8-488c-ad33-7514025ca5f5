package com.cfpamf.ms.insur.admin.service.order;

import com.cfpamf.ms.insur.admin.config.ProductProperties;
import com.cfpamf.ms.insur.admin.config.ProductSpecialRuleProperties;
import com.cfpamf.ms.insur.admin.dao.safes.*;
import com.cfpamf.ms.insur.admin.dao.safes.order.*;
import com.cfpamf.ms.insur.admin.enums.EnumChannel;
import com.cfpamf.ms.insur.admin.external.ChannelOrderService;
import com.cfpamf.ms.insur.admin.external.OrderSubmitRequest;
import com.cfpamf.ms.insur.admin.external.OrderSubmitResponse;
import com.cfpamf.ms.insur.admin.external.fh.dto.*;
import com.cfpamf.ms.insur.admin.external.fx.FxApiProperties;
import com.cfpamf.ms.insur.admin.external.fx.model.*;
import com.cfpamf.ms.insur.admin.external.fx.model.cancel.FxCancelReqHead;
import com.cfpamf.ms.insur.admin.external.fx.model.cancel.FxCancelTransInfoReq;
import com.cfpamf.ms.insur.admin.external.fx.model.visit.FxVisitResInfo;
import com.cfpamf.ms.insur.admin.external.fx.model.visit.FxVisitResultInfo;
import com.cfpamf.ms.insur.admin.pojo.dto.SmCreateOrderSubmitRequest;
import com.cfpamf.ms.insur.admin.pojo.dto.order.SmOrderCarInfoDTO;
import com.cfpamf.ms.insur.admin.pojo.dto.order.SmOrderHouseDTO;
import com.cfpamf.ms.insur.admin.pojo.form.product.TestPremiumDutyForm;
import com.cfpamf.ms.insur.admin.pojo.form.product.TestPremiumProductForm;
import com.cfpamf.ms.insur.admin.pojo.po.order.SmOrderPolicy;
import com.cfpamf.ms.insur.admin.pojo.po.order.SmOrderRiskDuty;
import com.cfpamf.ms.insur.admin.pojo.po.order.extend.SmOrderExtendFx;
import com.cfpamf.ms.insur.admin.pojo.vo.SmPlanVO;
import com.cfpamf.ms.insur.admin.renewal.service.OrderRenewalRecordService;
import com.cfpamf.ms.insur.admin.service.*;
import com.cfpamf.ms.insur.base.event.EventBusEngine;
import com.cfpamf.ms.insur.base.exception.BizException;
import com.cfpamf.ms.insur.base.infeface.CustomerCenterService;
import com.cfpamf.ms.insur.base.service.BusinessTokenService;
import com.cfpamf.ms.insur.admin.dao.safes.SmProductMapper;
import com.cfpamf.ms.insur.admin.dao.safes.order.SmOrderExtendFxMapper;
import com.cfpamf.ms.insur.admin.dao.safes.order.SmOrderRiskDutyMapper;
import com.cfpamf.ms.insur.admin.external.fx.FxOrderServiceAdapterImpl;
import com.cfpamf.ms.insur.admin.external.fx.model.FxPayNotifyReq;
import com.cfpamf.ms.insur.admin.service.BaseTest;
import com.cfpamf.ms.insur.admin.service.OrderNoGenerator;
import com.cfpamf.ms.insur.admin.service.SmCancelRefundService;
import com.cfpamf.ms.insur.base.util.HttpRequestUtil;
import com.cfpamf.ms.insur.common.service.monitor.BusinessMonitorService;
import com.cfpamf.ms.insur.weixin.dao.safes.EndorMapper;
import com.cfpamf.ms.pay.facade.config.PayFacadeConfigProperties;
import com.github.jsonzou.jmockdata.JMockData;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.slf4j.Logger;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;

import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(PowerMockRunner.class)
@PrepareForTest({HttpRequestUtil.class})
public class FxOrderServiceTest extends BaseTest {

    @Mock
    FxOrderServiceAdapterImpl adapter;
    @Mock
    SmOrderExtendFxMapper extendFxMapper;
    @Mock private FxOrderServiceAdapterImpl mockAdapter;
    @Mock private SmOrderExtendFxMapper mockExtendFxMapper;
    @Mock private OrderNoGenerator mockOrderNoGenerator;
    @Mock private SmProductMapper mockProductMapper;
    @Mock private SmCancelRefundService mockCancelRefundService;
    @Mock private SmOrderRiskDutyMapper mockOrderRiskDutyMapper;
    @Mock private FxApiProperties mockFxApiProperties;
    private int YEAR_NODE;
    private DateTimeFormatter FMT_PARSE;
    private DateTimeFormatter FMT_SUB;
    @Mock private BusinessMonitorService monitorService;
    @Mock private SmProductService productService;
    @Mock private BusinessTokenService tokenService;
    @Mock private CustomerCenterService ccService;
    @Mock private SmCommissionMapper cmsMapper;
    @Mock private SmOrderMapper orderMapper;
    @Mock private SmProductFormBuyLimitMapper formBuyLimitMapper;
    @Mock private AuthUserMapper userMapper;
    @Mock private OrderRenewalRecordService orderRenewalRecordService;
    @Mock private OrderCoreService orderCoreService;
    @Mock private SmChannelCallbackMapper channelCallbackMapper;
    @Mock private EventBusEngine busEngine;
    @Mock private SmOrderCarMapper carMapper;
    @Mock private SmOrderHouseMapper houseMapper;
    @Mock private SmOrderGroupNotifyService smOrderGroupNotifyService;
    @Mock private SmCommonSettingService commonSettingService;
    @Mock private SmOrderItemMapper smOrderItemMapper;
    @Mock private SmOrderRenewBindService smOrderRenewBindService;
    @Mock private SmXjxhService smXjxhService;
    @Mock private SmOrderPolicyMapper smOrderPolicyMapper;
    @Mock private ChOrderPersonalNotifyService chOrderPersonalNotifyService;
    @Mock private SmOrderPolicyService smOrderPolicyService;
    @Mock private RenewalManagerService renewalManagerService;
    @Mock private SmOrderInsuredMapper insuredMapper;
    private long tokenLockTime;
    private int phoneLimit;
    @Mock private ProductProperties properties;
    @Mock private ProductSpecialRuleProperties productSpecialRuleProperties;
    @Mock private EndorMapper paymentMapper;
    @Mock private PayFacadeConfigProperties payFacadeConfigProperties;
    @Mock private TempOrderNewProductMapper tempOrderNewProductMapper;
    @Mock private Logger log;

    @InjectMocks private FxOrderService fxOrderServiceUnderTest;

    @Test
    public void testChannel() {
        assertEquals(EnumChannel.FX.getCode(), fxOrderServiceUnderTest.channel());
    }

    @Test
    public void testOrderService() {
        // Setup
        // Run the test
        final ChannelOrderService result = fxOrderServiceUnderTest.orderService();

        // Verify the results
    }

    @Test
    public void testUpdateOrderPolicyInfo() {
        // Setup
        final Map<String, String> expectedResult = new HashMap<>();

        // Run the test
        final Map<String, String> result = fxOrderServiceUnderTest.updateOrderPolicyInfo("fhOrderId");

        // Verify the results
    }

    @Test
    public void testSupport() {
        assertFalse(fxOrderServiceUnderTest.support("channel"));
    }

    @Test
    public void testGetFxProductUrl() throws Exception {
        // Setup
        // Configure SmProductMapper.listProductPlansById(...).
        final SmPlanVO smPlanVO = new SmPlanVO();
        smPlanVO.setId(0);
        smPlanVO.setPlanId(0);
        smPlanVO.setChannel("channel");
        smPlanVO.setProductId(0);
        smPlanVO.setFhProductId("fhProductId");
        smPlanVO.setBuyLimit(0);
        smPlanVO.setPlanCode("fhProductId");
        final List<SmPlanVO> smPlanVOS = Arrays.asList(smPlanVO);
        when(mockProductMapper.listProductPlansById("productId", true)).thenReturn(smPlanVOS);

        when(mockAdapter.genAgentUrl("customField", "", "productId", "code")).thenReturn("result");

        // Run the test
        final String result = fxOrderServiceUnderTest.getFxProductUrl("customField", 0);

        // Verify the results
    }

    @Test
    public void testGetFxProductUrl_SmProductMapperReturnsNoItems() throws Exception {
        // Setup
        when(mockProductMapper.listProductPlansById("productId", true)).thenReturn(IntStream.range(0,1).mapToObj(x -> JMockData.mock(SmPlanVO.class)).collect(Collectors.toList()));
        when(mockAdapter.genAgentUrl("customField", "", "productId", "code")).thenReturn("result");

        // Run the test
        final String result = fxOrderServiceUnderTest.getFxProductUrl("customField", 0);

    }


    @Test
    public void testJump2Fx() throws Exception {
        // Setup
        final MockHttpServletResponse response = new MockHttpServletResponse();

        // Configure SmProductMapper.listProductPlansById(...).
        final SmPlanVO smPlanVO = new SmPlanVO();
        smPlanVO.setId(0);
        smPlanVO.setPlanId(0);
        smPlanVO.setChannel("channel");
        smPlanVO.setProductId(0);
        smPlanVO.setFhProductId("fhProductId");
        smPlanVO.setBuyLimit(0);
        smPlanVO.setPlanCode("fhProductId");
        final List<SmPlanVO> smPlanVOS = Arrays.asList(smPlanVO);
        when(mockProductMapper.listProductPlansById("productId", true)).thenReturn(smPlanVOS);

        when(mockAdapter.genAgentOldUrl("userId", "", "productId", "code")).thenReturn("result");
        when(mockAdapter.genAgentUrl("userId", "", "productId", "code")).thenReturn("result");

        // Run the test
        fxOrderServiceUnderTest.jump2Fx("userId", 0, false, response);

        // Verify the results
    }

    @Test
    public void testJump2Fx_SmProductMapperReturnsNoItems() throws Exception {
        // Setup
        final MockHttpServletResponse response = new MockHttpServletResponse();
        when(mockProductMapper.listProductPlansById("productId", true)).thenReturn(Collections.emptyList());
        when(mockAdapter.genAgentOldUrl("userId", "", "productId", "code")).thenReturn("result");
        when(mockAdapter.genAgentUrl("userId", "", "productId", "code")).thenReturn("result");

        // Run the test
        fxOrderServiceUnderTest.jump2Fx("userId", 0, false, response);

        // Verify the results
    }

    @Test
    public void testJump2Fx_FxOrderServiceAdapterGenAgentOldUrlThrowsException() throws Exception {
        // Setup
        final MockHttpServletResponse response = new MockHttpServletResponse();

        // Configure SmProductMapper.listProductPlansById(...).
        final SmPlanVO smPlanVO = new SmPlanVO();
        smPlanVO.setId(0);
        smPlanVO.setPlanId(0);
        smPlanVO.setChannel("channel");
        smPlanVO.setProductId(0);
        smPlanVO.setFhProductId("fhProductId");
        smPlanVO.setBuyLimit(0);
        smPlanVO.setPlanCode("fhProductId");
        final List<SmPlanVO> smPlanVOS = Arrays.asList(smPlanVO);
        when(mockProductMapper.listProductPlansById("productId", true)).thenReturn(smPlanVOS);

        when(mockAdapter.genAgentOldUrl("userId", "", "productId", "code")).thenThrow(Exception.class);

        // Run the test
        fxOrderServiceUnderTest.jump2Fx("userId", 0, false, response);

        // Verify the results
    }

    @Test
    public void testJump2Fx_FxOrderServiceAdapterGenAgentUrlThrowsException() throws Exception {
        // Setup
        final MockHttpServletResponse response = new MockHttpServletResponse();

        // Configure SmProductMapper.listProductPlansById(...).
        final SmPlanVO smPlanVO = new SmPlanVO();
        smPlanVO.setId(0);
        smPlanVO.setPlanId(0);
        smPlanVO.setChannel("channel");
        smPlanVO.setProductId(0);
        smPlanVO.setFhProductId("fhProductId");
        smPlanVO.setBuyLimit(0);
        smPlanVO.setPlanCode("fhProductId");
        final List<SmPlanVO> smPlanVOS = Arrays.asList(smPlanVO);
        when(mockProductMapper.listProductPlansById("productId", true)).thenReturn(smPlanVOS);

        when(mockAdapter.genAgentUrl("userId", "", "productId", "code")).thenThrow(Exception.class);

        // Run the test
        fxOrderServiceUnderTest.jump2Fx("userId", 0, false, response);

        // Verify the results
    }

    @Test
    public void testHandAsyncPayCallback() throws Exception {
        // Setup
        final MockHttpServletResponse response = new MockHttpServletResponse();
        final MockHttpServletRequest request = new MockHttpServletRequest();
        when(mockAdapter.aesDecrypt("object")).thenReturn("xmlStr");
        when(mockAdapter.checkSign("xmlStr", "ebiz_sign")).thenReturn(true);

        // Configure SmOrderExtendFxMapper.selectByOnlyCode(...).
        final SmOrderExtendFx smOrderExtendFx = new SmOrderExtendFx();
        smOrderExtendFx.setId(0);
        smOrderExtendFx.setFhOrderId("fhOrderId");
        smOrderExtendFx.setFxOnlyCode("fxOnlyCode");
        smOrderExtendFx.setFxSourceCode("FX_SOURCE_CODE_WEBCPS01");
        smOrderExtendFx.setProductId("productCode");
        smOrderExtendFx.setNotifyContent("object");
        smOrderExtendFx.setNotifyDecryptContent("xmlStr");
        smOrderExtendFx.setJobNumber("channelAgentCode");
        smOrderExtendFx.setOrgCode("orgCode");
        smOrderExtendFx.setPayIntv(0);
        smOrderExtendFx.setPayYears(0);
        smOrderExtendFx.setPayYearsIntv("payUnit");
        smOrderExtendFx.setInsureType("insureType");
        smOrderExtendFx.setInsureYears(0);
        smOrderExtendFx.setInsureYearsIntv("insureYearsIntv");
        smOrderExtendFx.setPolicyNo("policyNo");
        smOrderExtendFx.setOrderSource("orderSource");
        when(mockExtendFxMapper.selectByOnlyCode("onlyCode")).thenReturn(smOrderExtendFx);

        // Configure FxOrderServiceAdapter.failNotifyRes(...).
        final FxReqHead fxReqHead = new FxReqHead();
        fxReqHead.setTransType("transType");
        fxReqHead.setSourceCode("sourceCode");
        fxReqHead.setSerialNo("serialNo");
        when(mockAdapter.failNotifyRes(fxReqHead, "02", "resultMsg")).thenReturn("result");

        // Configure SmOrderExtendFxMapper.selectByPolicyNo(...).
        final SmOrderExtendFx smOrderExtendFx1 = new SmOrderExtendFx();
        smOrderExtendFx1.setId(0);
        smOrderExtendFx1.setFhOrderId("fhOrderId");
        smOrderExtendFx1.setFxOnlyCode("fxOnlyCode");
        smOrderExtendFx1.setFxSourceCode("FX_SOURCE_CODE_WEBCPS01");
        smOrderExtendFx1.setProductId("productCode");
        smOrderExtendFx1.setNotifyContent("object");
        smOrderExtendFx1.setNotifyDecryptContent("xmlStr");
        smOrderExtendFx1.setJobNumber("channelAgentCode");
        smOrderExtendFx1.setOrgCode("orgCode");
        smOrderExtendFx1.setPayIntv(0);
        smOrderExtendFx1.setPayYears(0);
        smOrderExtendFx1.setPayYearsIntv("payUnit");
        smOrderExtendFx1.setInsureType("insureType");
        smOrderExtendFx1.setInsureYears(0);
        smOrderExtendFx1.setInsureYearsIntv("insureYearsIntv");
        smOrderExtendFx1.setPolicyNo("policyNo");
        smOrderExtendFx1.setOrderSource("orderSource");
        when(mockExtendFxMapper.selectByPolicyNo("policyNo")).thenReturn(smOrderExtendFx1);

        // Configure FxOrderServiceAdapter.successNotifyRes(...).
        final FxReqHead fxReqHead1 = new FxReqHead();
        fxReqHead1.setTransType("transType");
        fxReqHead1.setSourceCode("sourceCode");
        fxReqHead1.setSerialNo("serialNo");
        when(mockAdapter.successNotifyRes(fxReqHead1)).thenReturn("result");

        when(mockOrderNoGenerator.getNextNo("code")).thenReturn("fhOrderId");

        // Configure FxOrderServiceAdapter.getOldMainRiskInfo(...).
        final FxRiskInfo fxRiskInfo = new FxRiskInfo();
        fxRiskInfo.setRiskCode("riskCode");
        fxRiskInfo.setRiskName("riskName");
        fxRiskInfo.setPrem(new BigDecimal("0.00"));
        fxRiskInfo.setAmnt(new BigDecimal("0.00"));
        fxRiskInfo.setPayIntv(0);
        fxRiskInfo.setPayYears(0);
        fxRiskInfo.setPayYearsIntv("payUnit");
        fxRiskInfo.setInsureType("insureType");
        fxRiskInfo.setInsureYears(0);
        fxRiskInfo.setInsureYearsIntv("insureYearsIntv");
        final FxDutyInfo fxDutyInfo = new FxDutyInfo();
        fxDutyInfo.setDutyCode("dutyCode");
        fxDutyInfo.setDutyName("dutyName");
        fxDutyInfo.setPrem(new BigDecimal("0.00"));
        fxDutyInfo.setAmnt(new BigDecimal("0.00"));
        fxRiskInfo.setDutyList(Arrays.asList(fxDutyInfo));
        final OldFxPolicyInfo policyInfo = new OldFxPolicyInfo();
        policyInfo.setOnlyCode("onlyCode");
        policyInfo.setPolicyNo("policyNo");
        policyInfo.setChannelAgentCode("channelAgentCode");
        policyInfo.setTotalPrem(new BigDecimal("0.00"));
        policyInfo.setProductCode("productCode");
        final FxRiskInfo fxRiskInfo1 = new FxRiskInfo();
        fxRiskInfo1.setRiskCode("riskCode");
        fxRiskInfo1.setRiskName("riskName");
        fxRiskInfo1.setPrem(new BigDecimal("0.00"));
        fxRiskInfo1.setAmnt(new BigDecimal("0.00"));
        fxRiskInfo1.setPayIntv(0);
        fxRiskInfo1.setPayYears(0);
        fxRiskInfo1.setPayYearsIntv("payUnit");
        fxRiskInfo1.setInsureType("insureType");
        fxRiskInfo1.setInsureYears(0);
        fxRiskInfo1.setInsureYearsIntv("insureYearsIntv");
        final FxDutyInfo fxDutyInfo1 = new FxDutyInfo();
        fxDutyInfo1.setDutyCode("dutyCode");
        fxDutyInfo1.setDutyName("dutyName");
        fxDutyInfo1.setPrem(new BigDecimal("0.00"));
        fxDutyInfo1.setAmnt(new BigDecimal("0.00"));
        fxRiskInfo1.setDutyList(Arrays.asList(fxDutyInfo1));
        policyInfo.setRiskList(Arrays.asList(fxRiskInfo1));
        when(mockAdapter.getOldMainRiskInfo(policyInfo)).thenReturn(fxRiskInfo);

        // Configure SmOrderExtendFxMapper.updateNotifyContent(...).
        final SmOrderExtendFx extendFx = new SmOrderExtendFx();
        extendFx.setId(0);
        extendFx.setFhOrderId("fhOrderId");
        extendFx.setFxOnlyCode("fxOnlyCode");
        extendFx.setFxSourceCode("FX_SOURCE_CODE_WEBCPS01");
        extendFx.setProductId("productCode");
        extendFx.setNotifyContent("object");
        extendFx.setNotifyDecryptContent("xmlStr");
        extendFx.setJobNumber("channelAgentCode");
        extendFx.setOrgCode("orgCode");
        extendFx.setPayIntv(0);
        extendFx.setPayYears(0);
        extendFx.setPayYearsIntv("payUnit");
        extendFx.setInsureType("insureType");
        extendFx.setInsureYears(0);
        extendFx.setInsureYearsIntv("insureYearsIntv");
        extendFx.setPolicyNo("policyNo");
        extendFx.setOrderSource("orderSource");
        when(mockExtendFxMapper.updateNotifyContent(extendFx)).thenReturn(0);

        // Configure SmOrderExtendFxMapper.insertSelective(...).
        final SmOrderExtendFx record = new SmOrderExtendFx();
        record.setId(0);
        record.setFhOrderId("fhOrderId");
        record.setFxOnlyCode("fxOnlyCode");
        record.setFxSourceCode("FX_SOURCE_CODE_WEBCPS01");
        record.setProductId("productCode");
        record.setNotifyContent("object");
        record.setNotifyDecryptContent("xmlStr");
        record.setJobNumber("channelAgentCode");
        record.setOrgCode("orgCode");
        record.setPayIntv(0);
        record.setPayYears(0);
        record.setPayYearsIntv("payUnit");
        record.setInsureType("insureType");
        record.setInsureYears(0);
        record.setInsureYearsIntv("insureYearsIntv");
        record.setPolicyNo("policyNo");
        record.setOrderSource("orderSource");
        when(mockExtendFxMapper.insertSelective(record)).thenReturn(0);

        // Configure FxOrderServiceAdapter.cvtByOldNotify(...).
        final OrderSubmitRequest orderSubmitRequest = new OrderSubmitRequest();
        final FhProduct productInfo = new FhProduct();
        productInfo.setProductId("fhProductId");
        productInfo.setRecommendId("recommendId");
        productInfo.setRecommendMainJobNumber("recommendMainJobNumber");
        productInfo.setRecommendOrgCode("recommendOrgCode");
        orderSubmitRequest.setProductInfo(productInfo);
        final FhOrderInfo orderInfo = new FhOrderInfo();
        orderInfo.setSubmitTime("submitTime");
        orderInfo.setStartTime("startTime");
        orderInfo.setEndTime("endTime");
        orderSubmitRequest.setOrderInfo(orderInfo);
        final FhProposer proposerInfo = new FhProposer();
        proposerInfo.setPersonName("insuredName");
        proposerInfo.setPersonGender("insuredPersonGender");
        proposerInfo.setIdType("idType");
        proposerInfo.setIdNumber("cardNo");
        proposerInfo.setBirthday("birthday");
        proposerInfo.setCellPhone("cellPhone");
        proposerInfo.setEmail("email");
        proposerInfo.setAddress("address");
        proposerInfo.setAppStatus("-2");
        proposerInfo.setPolicyNo("insIdNumber");
        proposerInfo.setDownloadURL("downloadURL");
        orderSubmitRequest.setProposerInfo(proposerInfo);
        final FhInsuredPerson fhInsuredPerson = new FhInsuredPerson();
        fhInsuredPerson.setPersonName("insuredName");
        fhInsuredPerson.setPersonGender("insuredPersonGender");
        fhInsuredPerson.setIdType("idType");
        fhInsuredPerson.setIdNumber("cardNo");
        fhInsuredPerson.setBirthday("birthday");
        fhInsuredPerson.setCellPhone("cellPhone");
        fhInsuredPerson.setAppStatus("-2");
        fhInsuredPerson.setRelationship("insuredAppntShipCode");
        fhInsuredPerson.setFlightNo("flightNo");
        fhInsuredPerson.setFlightTime("flightTime");
        fhInsuredPerson.setOccupationCode("");
        fhInsuredPerson.setOccupationGroup("occupationGroup");
        fhInsuredPerson.setDestinationCountryText("destinationCountryText");
        fhInsuredPerson.setAnnualIncome("annualIncome");
        fhInsuredPerson.setStudentType("studentType");
        fhInsuredPerson.setSchoolType("schoolType");
        fhInsuredPerson.setSchoolNature("schoolNature");
        fhInsuredPerson.setSchoolName("schoolName");
        fhInsuredPerson.setSchoolClass("schoolClass");
        fhInsuredPerson.setStudentId("studentId");
        fhInsuredPerson.setIdPeriodStart("idPeriodStart");
        fhInsuredPerson.setIdPeriodEnd("idPeriodEnd");
        fhInsuredPerson.setSmoke("smoke");
        fhInsuredPerson.setOldPolicyNo("oldPolicyNo");
        final TestPremiumDutyForm testPremiumDutyForm = new TestPremiumDutyForm();
        fhInsuredPerson.setDuties(Arrays.asList(testPremiumDutyForm));
        final TestPremiumProductForm product = new TestPremiumProductForm();
        product.setProductId(0);
        product.setPlanId(0);
        product.setPlanCode("planCode");
        product.setPremium(new BigDecimal("0.00"));
        product.setQty(0);
        final TestPremiumDutyForm testPremiumDutyForm1 = new TestPremiumDutyForm();
        product.setPremiumDutyForms(Arrays.asList(testPremiumDutyForm1));
        fhInsuredPerson.setProduct(product);
        orderSubmitRequest.setInsuredPerson(Arrays.asList(fhInsuredPerson));
        final FhProperty propertyInfo = new FhProperty();
        propertyInfo.setPropertyInfoIsExist("0");
        orderSubmitRequest.setPropertyInfo(propertyInfo);
        final SmOrderHouseDTO house = new SmOrderHouseDTO();
        orderSubmitRequest.setHouse(house);
        final SmOrderCarInfoDTO carInfo = new SmOrderCarInfoDTO();
        orderSubmitRequest.setCarInfo(carInfo);
        orderSubmitRequest.setProductType("code");
        orderSubmitRequest.setBizCode("bizCode");
        orderSubmitRequest.setOrderOutType("orderOutType");
        orderSubmitRequest.setToken("token");
        orderSubmitRequest.setPlanId(0);
        orderSubmitRequest.setQty(0);
        orderSubmitRequest.setPreOrderId("preOrderId");
        orderSubmitRequest.setRenew(false);
        orderSubmitRequest.setRealRenewFlag(false);
        orderSubmitRequest.setAppNo("appNo");
        orderSubmitRequest.setAgentId(0);
        orderSubmitRequest.setJobCode("jobCode");
        final SmCreateOrderSubmitRequest smCreateOrderSubmitRequest = new SmCreateOrderSubmitRequest(orderSubmitRequest);
        final OldFxPayNotifyTransInfoReq transInfoReq = new OldFxPayNotifyTransInfoReq();
        final OldFxPolicyInfo policyInfo1 = new OldFxPolicyInfo();
        policyInfo1.setOnlyCode("onlyCode");
        policyInfo1.setPolicyNo("policyNo");
        policyInfo1.setChannelAgentCode("channelAgentCode");
        policyInfo1.setTotalPrem(new BigDecimal("0.00"));
        policyInfo1.setProductCode("productCode");
        final FxRiskInfo fxRiskInfo2 = new FxRiskInfo();
        fxRiskInfo2.setRiskCode("riskCode");
        fxRiskInfo2.setRiskName("riskName");
        fxRiskInfo2.setPrem(new BigDecimal("0.00"));
        fxRiskInfo2.setAmnt(new BigDecimal("0.00"));
        fxRiskInfo2.setPayIntv(0);
        fxRiskInfo2.setPayYears(0);
        fxRiskInfo2.setPayYearsIntv("payUnit");
        fxRiskInfo2.setInsureType("insureType");
        fxRiskInfo2.setInsureYears(0);
        fxRiskInfo2.setInsureYearsIntv("insureYearsIntv");
        final FxDutyInfo fxDutyInfo2 = new FxDutyInfo();
        fxDutyInfo2.setDutyCode("dutyCode");
        fxDutyInfo2.setDutyName("dutyName");
        fxDutyInfo2.setPrem(new BigDecimal("0.00"));
        fxDutyInfo2.setAmnt(new BigDecimal("0.00"));
        fxRiskInfo2.setDutyList(Arrays.asList(fxDutyInfo2));
        policyInfo1.setRiskList(Arrays.asList(fxRiskInfo2));
        transInfoReq.setPolicyInfo(policyInfo1);
        when(mockAdapter.cvtByOldNotify(transInfoReq, "fhOrderId", "orderSource", "xjxhXjcid", "xjxhXjuid")).thenReturn(smCreateOrderSubmitRequest);

        // Configure FxOrderServiceAdapter.getMainRiskInfo(...).
        final FxRiskInfo fxRiskInfo3 = new FxRiskInfo();
        fxRiskInfo3.setRiskCode("riskCode");
        fxRiskInfo3.setRiskName("riskName");
        fxRiskInfo3.setPrem(new BigDecimal("0.00"));
        fxRiskInfo3.setAmnt(new BigDecimal("0.00"));
        fxRiskInfo3.setPayIntv(0);
        fxRiskInfo3.setPayYears(0);
        fxRiskInfo3.setPayYearsIntv("payUnit");
        fxRiskInfo3.setInsureType("insureType");
        fxRiskInfo3.setInsureYears(0);
        fxRiskInfo3.setInsureYearsIntv("insureYearsIntv");
        final FxDutyInfo fxDutyInfo3 = new FxDutyInfo();
        fxDutyInfo3.setDutyCode("dutyCode");
        fxDutyInfo3.setDutyName("dutyName");
        fxDutyInfo3.setPrem(new BigDecimal("0.00"));
        fxDutyInfo3.setAmnt(new BigDecimal("0.00"));
        fxRiskInfo3.setDutyList(Arrays.asList(fxDutyInfo3));
        final FxPolicyInfo policyInfo2 = new FxPolicyInfo();
        policyInfo2.setOnlyCode("onlyCode");
        policyInfo2.setPolicyNo("policyNo");
        policyInfo2.setChannelAgentCode("channelAgentCode");
        policyInfo2.setTotalPrem(new BigDecimal("0.00"));
        policyInfo2.setProductCode("productCode");
        final FxRiskInfo fxRiskInfo4 = new FxRiskInfo();
        fxRiskInfo4.setRiskCode("riskCode");
        fxRiskInfo4.setRiskName("riskName");
        fxRiskInfo4.setPrem(new BigDecimal("0.00"));
        fxRiskInfo4.setAmnt(new BigDecimal("0.00"));
        fxRiskInfo4.setPayIntv(0);
        fxRiskInfo4.setPayYears(0);
        fxRiskInfo4.setPayYearsIntv("payUnit");
        fxRiskInfo4.setInsureType("insureType");
        fxRiskInfo4.setInsureYears(0);
        fxRiskInfo4.setInsureYearsIntv("insureYearsIntv");
        final FxDutyInfo fxDutyInfo4 = new FxDutyInfo();
        fxDutyInfo4.setDutyCode("dutyCode");
        fxDutyInfo4.setDutyName("dutyName");
        fxDutyInfo4.setPrem(new BigDecimal("0.00"));
        fxDutyInfo4.setAmnt(new BigDecimal("0.00"));
        fxRiskInfo4.setDutyList(Arrays.asList(fxDutyInfo4));
        policyInfo2.setRiskList(Arrays.asList(fxRiskInfo4));
        final FxInsuredInfo fxInsuredInfo = new FxInsuredInfo();
        fxInsuredInfo.setCardNo("cardNo");
        policyInfo2.setInsuredInfoList(Arrays.asList(fxInsuredInfo));
        when(mockAdapter.getMainRiskInfo(policyInfo2)).thenReturn(fxRiskInfo3);

        // Configure FxOrderServiceAdapter.cvtByNotify(...).
        final OrderSubmitRequest orderSubmitRequest1 = new OrderSubmitRequest();
        final FhProduct productInfo1 = new FhProduct();
        productInfo1.setProductId("fhProductId");
        productInfo1.setRecommendId("recommendId");
        productInfo1.setRecommendMainJobNumber("recommendMainJobNumber");
        productInfo1.setRecommendOrgCode("recommendOrgCode");
        orderSubmitRequest1.setProductInfo(productInfo1);
        final FhOrderInfo orderInfo1 = new FhOrderInfo();
        orderInfo1.setSubmitTime("submitTime");
        orderInfo1.setStartTime("startTime");
        orderInfo1.setEndTime("endTime");
        orderSubmitRequest1.setOrderInfo(orderInfo1);
        final FhProposer proposerInfo1 = new FhProposer();
        proposerInfo1.setPersonName("insuredName");
        proposerInfo1.setPersonGender("insuredPersonGender");
        proposerInfo1.setIdType("idType");
        proposerInfo1.setIdNumber("cardNo");
        proposerInfo1.setBirthday("birthday");
        proposerInfo1.setCellPhone("cellPhone");
        proposerInfo1.setEmail("email");
        proposerInfo1.setAddress("address");
        proposerInfo1.setAppStatus("-2");
        proposerInfo1.setPolicyNo("insIdNumber");
        proposerInfo1.setDownloadURL("downloadURL");
        orderSubmitRequest1.setProposerInfo(proposerInfo1);
        final FhInsuredPerson fhInsuredPerson1 = new FhInsuredPerson();
        fhInsuredPerson1.setPersonName("insuredName");
        fhInsuredPerson1.setPersonGender("insuredPersonGender");
        fhInsuredPerson1.setIdType("idType");
        fhInsuredPerson1.setIdNumber("cardNo");
        fhInsuredPerson1.setBirthday("birthday");
        fhInsuredPerson1.setCellPhone("cellPhone");
        fhInsuredPerson1.setAppStatus("-2");
        fhInsuredPerson1.setRelationship("insuredAppntShipCode");
        fhInsuredPerson1.setFlightNo("flightNo");
        fhInsuredPerson1.setFlightTime("flightTime");
        fhInsuredPerson1.setOccupationCode("");
        fhInsuredPerson1.setOccupationGroup("occupationGroup");
        fhInsuredPerson1.setDestinationCountryText("destinationCountryText");
        fhInsuredPerson1.setAnnualIncome("annualIncome");
        fhInsuredPerson1.setStudentType("studentType");
        fhInsuredPerson1.setSchoolType("schoolType");
        fhInsuredPerson1.setSchoolNature("schoolNature");
        fhInsuredPerson1.setSchoolName("schoolName");
        fhInsuredPerson1.setSchoolClass("schoolClass");
        fhInsuredPerson1.setStudentId("studentId");
        fhInsuredPerson1.setIdPeriodStart("idPeriodStart");
        fhInsuredPerson1.setIdPeriodEnd("idPeriodEnd");
        fhInsuredPerson1.setSmoke("smoke");
        fhInsuredPerson1.setOldPolicyNo("oldPolicyNo");
        final TestPremiumDutyForm testPremiumDutyForm2 = new TestPremiumDutyForm();
        fhInsuredPerson1.setDuties(Arrays.asList(testPremiumDutyForm2));
        final TestPremiumProductForm product1 = new TestPremiumProductForm();
        product1.setProductId(0);
        product1.setPlanId(0);
        product1.setPlanCode("planCode");
        product1.setPremium(new BigDecimal("0.00"));
        product1.setQty(0);
        final TestPremiumDutyForm testPremiumDutyForm3 = new TestPremiumDutyForm();
        product1.setPremiumDutyForms(Arrays.asList(testPremiumDutyForm3));
        fhInsuredPerson1.setProduct(product1);
        orderSubmitRequest1.setInsuredPerson(Arrays.asList(fhInsuredPerson1));
        final FhProperty propertyInfo1 = new FhProperty();
        propertyInfo1.setPropertyInfoIsExist("0");
        orderSubmitRequest1.setPropertyInfo(propertyInfo1);
        final SmOrderHouseDTO house1 = new SmOrderHouseDTO();
        orderSubmitRequest1.setHouse(house1);
        final SmOrderCarInfoDTO carInfo1 = new SmOrderCarInfoDTO();
        orderSubmitRequest1.setCarInfo(carInfo1);
        orderSubmitRequest1.setProductType("code");
        orderSubmitRequest1.setBizCode("bizCode");
        orderSubmitRequest1.setOrderOutType("orderOutType");
        orderSubmitRequest1.setToken("token");
        orderSubmitRequest1.setPlanId(0);
        orderSubmitRequest1.setQty(0);
        orderSubmitRequest1.setPreOrderId("preOrderId");
        orderSubmitRequest1.setRenew(false);
        orderSubmitRequest1.setRealRenewFlag(false);
        orderSubmitRequest1.setAppNo("appNo");
        orderSubmitRequest1.setAgentId(0);
        orderSubmitRequest1.setJobCode("jobCode");
        final SmCreateOrderSubmitRequest smCreateOrderSubmitRequest1 = new SmCreateOrderSubmitRequest(orderSubmitRequest1);
        final FxPayNotifyTransInfoReq transInfoReq1 = new FxPayNotifyTransInfoReq();
        final FxPolicyInfo policyInfo3 = new FxPolicyInfo();
        policyInfo3.setOnlyCode("onlyCode");
        policyInfo3.setPolicyNo("policyNo");
        policyInfo3.setChannelAgentCode("channelAgentCode");
        policyInfo3.setTotalPrem(new BigDecimal("0.00"));
        policyInfo3.setProductCode("productCode");
        final FxRiskInfo fxRiskInfo5 = new FxRiskInfo();
        fxRiskInfo5.setRiskCode("riskCode");
        fxRiskInfo5.setRiskName("riskName");
        fxRiskInfo5.setPrem(new BigDecimal("0.00"));
        fxRiskInfo5.setAmnt(new BigDecimal("0.00"));
        fxRiskInfo5.setPayIntv(0);
        fxRiskInfo5.setPayYears(0);
        fxRiskInfo5.setPayYearsIntv("payUnit");
        fxRiskInfo5.setInsureType("insureType");
        fxRiskInfo5.setInsureYears(0);
        fxRiskInfo5.setInsureYearsIntv("insureYearsIntv");
        final FxDutyInfo fxDutyInfo5 = new FxDutyInfo();
        fxDutyInfo5.setDutyCode("dutyCode");
        fxDutyInfo5.setDutyName("dutyName");
        fxDutyInfo5.setPrem(new BigDecimal("0.00"));
        fxDutyInfo5.setAmnt(new BigDecimal("0.00"));
        fxRiskInfo5.setDutyList(Arrays.asList(fxDutyInfo5));
        policyInfo3.setRiskList(Arrays.asList(fxRiskInfo5));
        final FxInsuredInfo fxInsuredInfo1 = new FxInsuredInfo();
        fxInsuredInfo1.setCardNo("cardNo");
        policyInfo3.setInsuredInfoList(Arrays.asList(fxInsuredInfo1));
        transInfoReq1.setPolicyInfo(policyInfo3);
        when(mockAdapter.cvtByNotify(transInfoReq1, "fhOrderId", "orderSource", "xjxhXjcid", "xjxhXjuid")).thenReturn(smCreateOrderSubmitRequest1);

        // Run the test
        try{
            fxOrderServiceUnderTest.handAsyncPayCallback("orderIdParams", JMockData.mock(FxRequestBodyDTO.class), response, request);
        } catch(Exception e) {
        }

    }

    @Test(expected = BizException.class)
    public void testHandAsyncPayCallback_FxOrderServiceAdapterCheckSignReturnsFalse() throws Exception {
        // Setup
        final MockHttpServletResponse response = new MockHttpServletResponse();
        final MockHttpServletRequest request = new MockHttpServletRequest();
        when(mockAdapter.aesDecrypt("object")).thenReturn("xmlStr");
        when(mockAdapter.checkSign("xmlStr", "ebiz_sign")).thenReturn(false);

        // Run the test
        fxOrderServiceUnderTest.handAsyncPayCallback("orderIdParams", JMockData.mock(FxRequestBodyDTO.class), response, request);
    }

    @Test
    public void testHandAsyncPayCallback_SmOrderExtendFxMapperSelectByOnlyCodeReturnsNull() throws Exception {
        // Setup
        final MockHttpServletResponse response = new MockHttpServletResponse();
        final MockHttpServletRequest request = new MockHttpServletRequest();
        when(mockAdapter.aesDecrypt("object")).thenReturn("xmlStr");
        when(mockAdapter.checkSign("xmlStr", "ebiz_sign")).thenReturn(true);
        when(mockExtendFxMapper.selectByOnlyCode("onlyCode")).thenReturn(null);

        // Configure FxOrderServiceAdapter.failNotifyRes(...).
        final FxReqHead fxReqHead = new FxReqHead();
        fxReqHead.setTransType("transType");
        fxReqHead.setSourceCode("sourceCode");
        fxReqHead.setSerialNo("serialNo");
        when(mockAdapter.failNotifyRes(fxReqHead, "02", "resultMsg")).thenReturn("result");

        // Run the test
        try{
            fxOrderServiceUnderTest.handAsyncPayCallback("orderIdParams", JMockData.mock(FxRequestBodyDTO.class), response, request);
        } catch(Exception e) {

        }


        // Verify the results
    }

    @Test
    public void testHandAsyncPayCallback_SmOrderExtendFxMapperSelectByPolicyNoReturnsNull() throws Exception {
        // Setup
        final MockHttpServletResponse response = new MockHttpServletResponse();
        final MockHttpServletRequest request = new MockHttpServletRequest();
        when(mockAdapter.aesDecrypt("object")).thenReturn("xmlStr");
        when(mockAdapter.checkSign("xmlStr", "ebiz_sign")).thenReturn(true);

        // Configure SmOrderExtendFxMapper.selectByOnlyCode(...).
        final SmOrderExtendFx smOrderExtendFx = new SmOrderExtendFx();
        smOrderExtendFx.setId(0);
        smOrderExtendFx.setFhOrderId("fhOrderId");
        smOrderExtendFx.setFxOnlyCode("fxOnlyCode");
        smOrderExtendFx.setFxSourceCode("FX_SOURCE_CODE_WEBCPS01");
        smOrderExtendFx.setProductId("productCode");
        smOrderExtendFx.setNotifyContent("object");
        smOrderExtendFx.setNotifyDecryptContent("xmlStr");
        smOrderExtendFx.setJobNumber("channelAgentCode");
        smOrderExtendFx.setOrgCode("orgCode");
        smOrderExtendFx.setPayIntv(0);
        smOrderExtendFx.setPayYears(0);
        smOrderExtendFx.setPayYearsIntv("payUnit");
        smOrderExtendFx.setInsureType("insureType");
        smOrderExtendFx.setInsureYears(0);
        smOrderExtendFx.setInsureYearsIntv("insureYearsIntv");
        smOrderExtendFx.setPolicyNo("policyNo");
        smOrderExtendFx.setOrderSource("orderSource");
        when(mockExtendFxMapper.selectByOnlyCode("onlyCode")).thenReturn(smOrderExtendFx);

        when(mockExtendFxMapper.selectByPolicyNo("policyNo")).thenReturn(null);

        // Configure FxOrderServiceAdapter.failNotifyRes(...).
        final FxReqHead fxReqHead = new FxReqHead();
        fxReqHead.setTransType("transType");
        fxReqHead.setSourceCode("sourceCode");
        fxReqHead.setSerialNo("serialNo");
        when(mockAdapter.failNotifyRes(fxReqHead, "02", "resultMsg")).thenReturn("result");

        // Configure FxOrderServiceAdapter.successNotifyRes(...).
        final FxReqHead fxReqHead1 = new FxReqHead();
        fxReqHead1.setTransType("transType");
        fxReqHead1.setSourceCode("sourceCode");
        fxReqHead1.setSerialNo("serialNo");
        when(mockAdapter.successNotifyRes(fxReqHead1)).thenReturn("result");

        when(mockOrderNoGenerator.getNextNo("code")).thenReturn("fhOrderId");

        // Configure FxOrderServiceAdapter.getOldMainRiskInfo(...).
        final FxRiskInfo fxRiskInfo = new FxRiskInfo();
        fxRiskInfo.setRiskCode("riskCode");
        fxRiskInfo.setRiskName("riskName");
        fxRiskInfo.setPrem(new BigDecimal("0.00"));
        fxRiskInfo.setAmnt(new BigDecimal("0.00"));
        fxRiskInfo.setPayIntv(0);
        fxRiskInfo.setPayYears(0);
        fxRiskInfo.setPayYearsIntv("payUnit");
        fxRiskInfo.setInsureType("insureType");
        fxRiskInfo.setInsureYears(0);
        fxRiskInfo.setInsureYearsIntv("insureYearsIntv");
        final FxDutyInfo fxDutyInfo = new FxDutyInfo();
        fxDutyInfo.setDutyCode("dutyCode");
        fxDutyInfo.setDutyName("dutyName");
        fxDutyInfo.setPrem(new BigDecimal("0.00"));
        fxDutyInfo.setAmnt(new BigDecimal("0.00"));
        fxRiskInfo.setDutyList(Arrays.asList(fxDutyInfo));
        final OldFxPolicyInfo policyInfo = new OldFxPolicyInfo();
        policyInfo.setOnlyCode("onlyCode");
        policyInfo.setPolicyNo("policyNo");
        policyInfo.setChannelAgentCode("channelAgentCode");
        policyInfo.setTotalPrem(new BigDecimal("0.00"));
        policyInfo.setProductCode("productCode");
        final FxRiskInfo fxRiskInfo1 = new FxRiskInfo();
        fxRiskInfo1.setRiskCode("riskCode");
        fxRiskInfo1.setRiskName("riskName");
        fxRiskInfo1.setPrem(new BigDecimal("0.00"));
        fxRiskInfo1.setAmnt(new BigDecimal("0.00"));
        fxRiskInfo1.setPayIntv(0);
        fxRiskInfo1.setPayYears(0);
        fxRiskInfo1.setPayYearsIntv("payUnit");
        fxRiskInfo1.setInsureType("insureType");
        fxRiskInfo1.setInsureYears(0);
        fxRiskInfo1.setInsureYearsIntv("insureYearsIntv");
        final FxDutyInfo fxDutyInfo1 = new FxDutyInfo();
        fxDutyInfo1.setDutyCode("dutyCode");
        fxDutyInfo1.setDutyName("dutyName");
        fxDutyInfo1.setPrem(new BigDecimal("0.00"));
        fxDutyInfo1.setAmnt(new BigDecimal("0.00"));
        fxRiskInfo1.setDutyList(Arrays.asList(fxDutyInfo1));
        policyInfo.setRiskList(Arrays.asList(fxRiskInfo1));
        when(mockAdapter.getOldMainRiskInfo(policyInfo)).thenReturn(fxRiskInfo);

        // Configure SmOrderExtendFxMapper.updateNotifyContent(...).
        final SmOrderExtendFx extendFx = new SmOrderExtendFx();
        extendFx.setId(0);
        extendFx.setFhOrderId("fhOrderId");
        extendFx.setFxOnlyCode("fxOnlyCode");
        extendFx.setFxSourceCode("FX_SOURCE_CODE_WEBCPS01");
        extendFx.setProductId("productCode");
        extendFx.setNotifyContent("object");
        extendFx.setNotifyDecryptContent("xmlStr");
        extendFx.setJobNumber("channelAgentCode");
        extendFx.setOrgCode("orgCode");
        extendFx.setPayIntv(0);
        extendFx.setPayYears(0);
        extendFx.setPayYearsIntv("payUnit");
        extendFx.setInsureType("insureType");
        extendFx.setInsureYears(0);
        extendFx.setInsureYearsIntv("insureYearsIntv");
        extendFx.setPolicyNo("policyNo");
        extendFx.setOrderSource("orderSource");
        when(mockExtendFxMapper.updateNotifyContent(extendFx)).thenReturn(0);

        // Configure SmOrderExtendFxMapper.insertSelective(...).
        final SmOrderExtendFx record = new SmOrderExtendFx();
        record.setId(0);
        record.setFhOrderId("fhOrderId");
        record.setFxOnlyCode("fxOnlyCode");
        record.setFxSourceCode("FX_SOURCE_CODE_WEBCPS01");
        record.setProductId("productCode");
        record.setNotifyContent("object");
        record.setNotifyDecryptContent("xmlStr");
        record.setJobNumber("channelAgentCode");
        record.setOrgCode("orgCode");
        record.setPayIntv(0);
        record.setPayYears(0);
        record.setPayYearsIntv("payUnit");
        record.setInsureType("insureType");
        record.setInsureYears(0);
        record.setInsureYearsIntv("insureYearsIntv");
        record.setPolicyNo("policyNo");
        record.setOrderSource("orderSource");
        when(mockExtendFxMapper.insertSelective(record)).thenReturn(0);

        // Configure FxOrderServiceAdapter.cvtByOldNotify(...).
        final OrderSubmitRequest orderSubmitRequest = new OrderSubmitRequest();
        final FhProduct productInfo = new FhProduct();
        productInfo.setProductId("fhProductId");
        productInfo.setRecommendId("recommendId");
        productInfo.setRecommendMainJobNumber("recommendMainJobNumber");
        productInfo.setRecommendOrgCode("recommendOrgCode");
        orderSubmitRequest.setProductInfo(productInfo);
        final FhOrderInfo orderInfo = new FhOrderInfo();
        orderInfo.setSubmitTime("submitTime");
        orderInfo.setStartTime("startTime");
        orderInfo.setEndTime("endTime");
        orderSubmitRequest.setOrderInfo(orderInfo);
        final FhProposer proposerInfo = new FhProposer();
        proposerInfo.setPersonName("insuredName");
        proposerInfo.setPersonGender("insuredPersonGender");
        proposerInfo.setIdType("idType");
        proposerInfo.setIdNumber("cardNo");
        proposerInfo.setBirthday("birthday");
        proposerInfo.setCellPhone("cellPhone");
        proposerInfo.setEmail("email");
        proposerInfo.setAddress("address");
        proposerInfo.setAppStatus("-2");
        proposerInfo.setPolicyNo("insIdNumber");
        proposerInfo.setDownloadURL("downloadURL");
        orderSubmitRequest.setProposerInfo(proposerInfo);
        final FhInsuredPerson fhInsuredPerson = new FhInsuredPerson();
        fhInsuredPerson.setPersonName("insuredName");
        fhInsuredPerson.setPersonGender("insuredPersonGender");
        fhInsuredPerson.setIdType("idType");
        fhInsuredPerson.setIdNumber("cardNo");
        fhInsuredPerson.setBirthday("birthday");
        fhInsuredPerson.setCellPhone("cellPhone");
        fhInsuredPerson.setAppStatus("-2");
        fhInsuredPerson.setRelationship("insuredAppntShipCode");
        fhInsuredPerson.setFlightNo("flightNo");
        fhInsuredPerson.setFlightTime("flightTime");
        fhInsuredPerson.setOccupationCode("");
        fhInsuredPerson.setOccupationGroup("occupationGroup");
        fhInsuredPerson.setDestinationCountryText("destinationCountryText");
        fhInsuredPerson.setAnnualIncome("annualIncome");
        fhInsuredPerson.setStudentType("studentType");
        fhInsuredPerson.setSchoolType("schoolType");
        fhInsuredPerson.setSchoolNature("schoolNature");
        fhInsuredPerson.setSchoolName("schoolName");
        fhInsuredPerson.setSchoolClass("schoolClass");
        fhInsuredPerson.setStudentId("studentId");
        fhInsuredPerson.setIdPeriodStart("idPeriodStart");
        fhInsuredPerson.setIdPeriodEnd("idPeriodEnd");
        fhInsuredPerson.setSmoke("smoke");
        fhInsuredPerson.setOldPolicyNo("oldPolicyNo");
        final TestPremiumDutyForm testPremiumDutyForm = new TestPremiumDutyForm();
        fhInsuredPerson.setDuties(Arrays.asList(testPremiumDutyForm));
        final TestPremiumProductForm product = new TestPremiumProductForm();
        product.setProductId(0);
        product.setPlanId(0);
        product.setPlanCode("planCode");
        product.setPremium(new BigDecimal("0.00"));
        product.setQty(0);
        final TestPremiumDutyForm testPremiumDutyForm1 = new TestPremiumDutyForm();
        product.setPremiumDutyForms(Arrays.asList(testPremiumDutyForm1));
        fhInsuredPerson.setProduct(product);
        orderSubmitRequest.setInsuredPerson(Arrays.asList(fhInsuredPerson));
        final FhProperty propertyInfo = new FhProperty();
        propertyInfo.setPropertyInfoIsExist("0");
        orderSubmitRequest.setPropertyInfo(propertyInfo);
        final SmOrderHouseDTO house = new SmOrderHouseDTO();
        orderSubmitRequest.setHouse(house);
        final SmOrderCarInfoDTO carInfo = new SmOrderCarInfoDTO();
        orderSubmitRequest.setCarInfo(carInfo);
        orderSubmitRequest.setProductType("code");
        orderSubmitRequest.setBizCode("bizCode");
        orderSubmitRequest.setOrderOutType("orderOutType");
        orderSubmitRequest.setToken("token");
        orderSubmitRequest.setPlanId(0);
        orderSubmitRequest.setQty(0);
        orderSubmitRequest.setPreOrderId("preOrderId");
        orderSubmitRequest.setRenew(false);
        orderSubmitRequest.setRealRenewFlag(false);
        orderSubmitRequest.setAppNo("appNo");
        orderSubmitRequest.setAgentId(0);
        orderSubmitRequest.setJobCode("jobCode");
        final SmCreateOrderSubmitRequest smCreateOrderSubmitRequest = new SmCreateOrderSubmitRequest(orderSubmitRequest);
        final OldFxPayNotifyTransInfoReq transInfoReq = new OldFxPayNotifyTransInfoReq();
        final OldFxPolicyInfo policyInfo1 = new OldFxPolicyInfo();
        policyInfo1.setOnlyCode("onlyCode");
        policyInfo1.setPolicyNo("policyNo");
        policyInfo1.setChannelAgentCode("channelAgentCode");
        policyInfo1.setTotalPrem(new BigDecimal("0.00"));
        policyInfo1.setProductCode("productCode");
        final FxRiskInfo fxRiskInfo2 = new FxRiskInfo();
        fxRiskInfo2.setRiskCode("riskCode");
        fxRiskInfo2.setRiskName("riskName");
        fxRiskInfo2.setPrem(new BigDecimal("0.00"));
        fxRiskInfo2.setAmnt(new BigDecimal("0.00"));
        fxRiskInfo2.setPayIntv(0);
        fxRiskInfo2.setPayYears(0);
        fxRiskInfo2.setPayYearsIntv("payUnit");
        fxRiskInfo2.setInsureType("insureType");
        fxRiskInfo2.setInsureYears(0);
        fxRiskInfo2.setInsureYearsIntv("insureYearsIntv");
        final FxDutyInfo fxDutyInfo2 = new FxDutyInfo();
        fxDutyInfo2.setDutyCode("dutyCode");
        fxDutyInfo2.setDutyName("dutyName");
        fxDutyInfo2.setPrem(new BigDecimal("0.00"));
        fxDutyInfo2.setAmnt(new BigDecimal("0.00"));
        fxRiskInfo2.setDutyList(Arrays.asList(fxDutyInfo2));
        policyInfo1.setRiskList(Arrays.asList(fxRiskInfo2));
        transInfoReq.setPolicyInfo(policyInfo1);
        when(mockAdapter.cvtByOldNotify(transInfoReq, "fhOrderId", "orderSource", "xjxhXjcid", "xjxhXjuid")).thenReturn(smCreateOrderSubmitRequest);

        // Run the test
        try {
            fxOrderServiceUnderTest.handAsyncPayCallback("orderIdParams", JMockData.mock(FxRequestBodyDTO.class), response, request);

        } catch(Exception e) {

        }

        // Verify the results
    }

    @Test
    public void testAddGuaranteeInfo() {
        // Setup
        final FxPayNotifyReq notifyReq = new FxPayNotifyReq();
        final FxReqHead ebizHead = new FxReqHead();
        notifyReq.setEbizHead(ebizHead);
        final FxPayNotifyTransInfoReq transInfo = new FxPayNotifyTransInfoReq();
        final FxPolicyInfo policyInfo = new FxPolicyInfo();
        policyInfo.setOnlyCode("onlyCode");
        policyInfo.setPolicyNo("policyNo");
        policyInfo.setChannelAgentCode("channelAgentCode");
        policyInfo.setTotalPrem(new BigDecimal("0.00"));
        policyInfo.setProductCode("productCode");
        final FxRiskInfo fxRiskInfo = new FxRiskInfo();
        fxRiskInfo.setRiskCode("riskCode");
        fxRiskInfo.setRiskName("riskName");
        fxRiskInfo.setPrem(new BigDecimal("0.00"));
        fxRiskInfo.setAmnt(new BigDecimal("0.00"));
        fxRiskInfo.setPayIntv(0);
        fxRiskInfo.setPayYears(0);
        fxRiskInfo.setPayYearsIntv("payUnit");
        fxRiskInfo.setInsureType("insureType");
        fxRiskInfo.setInsureYears(0);
        fxRiskInfo.setInsureYearsIntv("insureYearsIntv");
        final FxDutyInfo fxDutyInfo = new FxDutyInfo();
        fxDutyInfo.setDutyCode("dutyCode");
        fxDutyInfo.setDutyName("dutyName");
        fxDutyInfo.setPrem(new BigDecimal("0.00"));
        fxDutyInfo.setAmnt(new BigDecimal("0.00"));
        fxRiskInfo.setDutyList(Arrays.asList(fxDutyInfo));
        policyInfo.setRiskList(Arrays.asList(fxRiskInfo));
        final FxInsuredInfo fxInsuredInfo = new FxInsuredInfo();
        fxInsuredInfo.setCardNo("cardNo");
        policyInfo.setInsuredInfoList(Arrays.asList(fxInsuredInfo));
        transInfo.setPolicyInfo(policyInfo);
        notifyReq.setTransInfo(transInfo);

        // Run the test
        fxOrderServiceUnderTest.addGuaranteeInfo(notifyReq, "fhOrderId");

    }

    @Test
    public void testSaveOrderInfo() {
        // Setup
        final OrderSubmitRequest dto = new OrderSubmitRequest();
        final FhProduct productInfo = new FhProduct();
        productInfo.setProductId("fhProductId");
        productInfo.setRecommendId("recommendId");
        productInfo.setRecommendMainJobNumber("recommendMainJobNumber");
        productInfo.setRecommendOrgCode("recommendOrgCode");
        dto.setProductInfo(productInfo);
        final FhOrderInfo orderInfo = new FhOrderInfo();
        orderInfo.setSubmitTime("submitTime");
        orderInfo.setStartTime("startTime");
        orderInfo.setEndTime("endTime");
        dto.setOrderInfo(orderInfo);
        final FhProposer proposerInfo = new FhProposer();
        proposerInfo.setPersonName("insuredName");
        proposerInfo.setPersonGender("insuredPersonGender");
        proposerInfo.setIdType("idType");
        proposerInfo.setIdNumber("cardNo");
        proposerInfo.setBirthday("birthday");
        proposerInfo.setCellPhone("cellPhone");
        proposerInfo.setEmail("email");
        proposerInfo.setAddress("address");
        proposerInfo.setAppStatus("-2");
        proposerInfo.setPolicyNo("insIdNumber");
        proposerInfo.setDownloadURL("downloadURL");
        dto.setProposerInfo(proposerInfo);
        final FhInsuredPerson fhInsuredPerson = new FhInsuredPerson();
        fhInsuredPerson.setPersonName("insuredName");
        fhInsuredPerson.setPersonGender("insuredPersonGender");
        fhInsuredPerson.setIdType("idType");
        fhInsuredPerson.setIdNumber("cardNo");
        fhInsuredPerson.setBirthday("birthday");
        fhInsuredPerson.setCellPhone("cellPhone");
        fhInsuredPerson.setAppStatus("-2");
        fhInsuredPerson.setRelationship("insuredAppntShipCode");
        fhInsuredPerson.setFlightNo("flightNo");
        fhInsuredPerson.setFlightTime("flightTime");
        fhInsuredPerson.setOccupationCode("");
        fhInsuredPerson.setOccupationGroup("occupationGroup");
        fhInsuredPerson.setDestinationCountryText("destinationCountryText");
        fhInsuredPerson.setAnnualIncome("annualIncome");
        fhInsuredPerson.setStudentType("studentType");
        fhInsuredPerson.setSchoolType("schoolType");
        fhInsuredPerson.setSchoolNature("schoolNature");
        fhInsuredPerson.setSchoolName("schoolName");
        fhInsuredPerson.setSchoolClass("schoolClass");
        fhInsuredPerson.setStudentId("studentId");
        fhInsuredPerson.setIdPeriodStart("idPeriodStart");
        fhInsuredPerson.setIdPeriodEnd("idPeriodEnd");
        fhInsuredPerson.setSmoke("smoke");
        fhInsuredPerson.setOldPolicyNo("oldPolicyNo");
        final TestPremiumDutyForm testPremiumDutyForm = new TestPremiumDutyForm();
        fhInsuredPerson.setDuties(Arrays.asList(testPremiumDutyForm));
        final TestPremiumProductForm product = new TestPremiumProductForm();
        product.setProductId(0);
        product.setPlanId(0);
        product.setPlanCode("planCode");
        product.setPremium(new BigDecimal("0.00"));
        product.setQty(0);
        final TestPremiumDutyForm testPremiumDutyForm1 = new TestPremiumDutyForm();
        product.setPremiumDutyForms(Arrays.asList(testPremiumDutyForm1));
        fhInsuredPerson.setProduct(product);
        dto.setInsuredPerson(Arrays.asList(fhInsuredPerson));
        final FhProperty propertyInfo = new FhProperty();
        propertyInfo.setPropertyInfoIsExist("0");
        dto.setPropertyInfo(propertyInfo);
        final SmOrderHouseDTO house = new SmOrderHouseDTO();
        dto.setHouse(house);
        final SmOrderCarInfoDTO carInfo = new SmOrderCarInfoDTO();
        dto.setCarInfo(carInfo);
        dto.setProductType("code");
        dto.setBizCode("bizCode");
        dto.setOrderOutType("orderOutType");
        dto.setToken("token");
        dto.setPlanId(0);
        dto.setQty(0);
        dto.setPreOrderId("preOrderId");
        dto.setRenew(false);
        dto.setRealRenewFlag(false);
        dto.setAppNo("appNo");
        dto.setAgentId(0);
        dto.setJobCode("jobCode");

        final OrderSubmitResponse smOrderResp = new OrderSubmitResponse();
        smOrderResp.setNoticeCode("-1");
        smOrderResp.setNoticeMsg("noticeMsg");
        smOrderResp.setOrderId("fhOrderId");
        smOrderResp.setAppNo("appNo");
        final OrderSubmitResponse.ReturnMap returnMap = new OrderSubmitResponse.ReturnMap();
        returnMap.setMsg("msg");
        returnMap.setRenewOrderSn("fhOrderId");
        smOrderResp.setReturnMap(returnMap);

        final SmPlanVO planVo = new SmPlanVO();
        planVo.setId(0);
        planVo.setPlanId(0);
        planVo.setChannel("channel");
        planVo.setProductId(0);
        planVo.setFhProductId("fhProductId");
        planVo.setBuyLimit(0);
        planVo.setPlanCode("fhProductId");

        // Run the test
        fxOrderServiceUnderTest.saveOrderInfo(dto, smOrderResp, planVo);

        // Verify the results
    }

    @Test
    public void testCvtOrderPolicy() {
        // Setup
        final FxPayNotifyReq notifyReq = new FxPayNotifyReq();
        final FxReqHead ebizHead = new FxReqHead();
        notifyReq.setEbizHead(ebizHead);
        final FxPayNotifyTransInfoReq transInfo = new FxPayNotifyTransInfoReq();
        final FxPolicyInfo policyInfo = new FxPolicyInfo();
        policyInfo.setOnlyCode("onlyCode");
        policyInfo.setPolicyNo("policyNo");
        policyInfo.setChannelAgentCode("channelAgentCode");
        policyInfo.setTotalPrem(new BigDecimal("0.00"));
        policyInfo.setProductCode("productCode");
        final FxRiskInfo fxRiskInfo = new FxRiskInfo();
        fxRiskInfo.setRiskCode("riskCode");
        fxRiskInfo.setRiskName("riskName");
        fxRiskInfo.setPrem(new BigDecimal("0.00"));
        fxRiskInfo.setAmnt(new BigDecimal("0.00"));
        fxRiskInfo.setPayIntv(0);
        fxRiskInfo.setPayYears(0);
        fxRiskInfo.setPayYearsIntv("payUnit");
        fxRiskInfo.setInsureType("insureType");
        fxRiskInfo.setInsureYears(0);
        fxRiskInfo.setInsureYearsIntv("insureYearsIntv");
        final FxDutyInfo fxDutyInfo = new FxDutyInfo();
        fxDutyInfo.setDutyCode("dutyCode");
        fxDutyInfo.setDutyName("dutyName");
        fxDutyInfo.setPrem(new BigDecimal("0.00"));
        fxDutyInfo.setAmnt(new BigDecimal("0.00"));
        fxRiskInfo.setDutyList(Arrays.asList(fxDutyInfo));
        policyInfo.setRiskList(Arrays.asList(fxRiskInfo));
        final FxInsuredInfo fxInsuredInfo = new FxInsuredInfo();
        fxInsuredInfo.setCardNo("cardNo");
        policyInfo.setInsuredInfoList(Arrays.asList(fxInsuredInfo));
        transInfo.setPolicyInfo(policyInfo);
        notifyReq.setTransInfo(transInfo);

        final SmOrderExtendFx extendFx = new SmOrderExtendFx();
        extendFx.setId(0);
        extendFx.setFhOrderId("fhOrderId");
        extendFx.setFxOnlyCode("fxOnlyCode");
        extendFx.setFxSourceCode("FX_SOURCE_CODE_WEBCPS01");
        extendFx.setProductId("productCode");
        extendFx.setNotifyContent("object");
        extendFx.setNotifyDecryptContent("xmlStr");
        extendFx.setJobNumber("channelAgentCode");
        extendFx.setOrgCode("orgCode");
        extendFx.setPayIntv(0);
        extendFx.setPayYears(0);
        extendFx.setPayYearsIntv("payUnit");
        extendFx.setInsureType("insureType");
        extendFx.setInsureYears(0);
        extendFx.setInsureYearsIntv("insureYearsIntv");
        extendFx.setPolicyNo("policyNo");
        extendFx.setOrderSource("orderSource");

        final SmOrderPolicy expectedResult = new SmOrderPolicy();
        expectedResult.setId(0);
        expectedResult.setFhOrderId("fhOrderId");
        expectedResult.setChannel("code");
        expectedResult.setPolicyNo("policyNo");
        expectedResult.setPremium(new BigDecimal("0.00"));
        expectedResult.setAmount(new BigDecimal("0.00"));
        expectedResult.setPayType("insureType");
        expectedResult.setPayPeriod("payingYears");
        expectedResult.setPayUnit("payUnit");
        expectedResult.setValidPeriod("coveredType");
        expectedResult.setValidUnit("insureYearsIntv");
        expectedResult.setVisitTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setVisitWay("visitWay");
        expectedResult.setVisitStatus("code");
        expectedResult.setSurrenderTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setSurrenderValidTime(LocalDate.of(2020, 1, 1));
        expectedResult.setCancelAmount(new BigDecimal("0.00"));
        expectedResult.setSurrenderType("surrenderType");
        expectedResult.setExtend("extend");

        // Run the test
        final SmOrderPolicy result = fxOrderServiceUnderTest.cvtOrderPolicy(notifyReq, extendFx);

        // Verify the results
    }

    @Test
    public void testHandSyncPayCallback() throws Exception {
        // Setup
        final MockHttpServletResponse response = new MockHttpServletResponse();
        final MockHttpServletRequest request = new MockHttpServletRequest();
        when(mockAdapter.getPayRedirectUrl("orderId")).thenReturn("result");

        // Run the test
        fxOrderServiceUnderTest.handSyncPayCallback("orderId", "object", response, request);

        // Verify the results
    }

    @Test
    public void testDecryptNotifyContentByOnlyCode() {
        // Setup
        // Configure SmOrderExtendFxMapper.selectByOnlyCode(...).
        final SmOrderExtendFx smOrderExtendFx = new SmOrderExtendFx();
        smOrderExtendFx.setId(0);
        smOrderExtendFx.setFhOrderId("fhOrderId");
        smOrderExtendFx.setFxOnlyCode("fxOnlyCode");
        smOrderExtendFx.setFxSourceCode("FX_SOURCE_CODE_WEBCPS01");
        smOrderExtendFx.setProductId("productCode");
        smOrderExtendFx.setNotifyContent("object");
        smOrderExtendFx.setNotifyDecryptContent("xmlStr");
        smOrderExtendFx.setJobNumber("channelAgentCode");
        smOrderExtendFx.setOrgCode("orgCode");
        smOrderExtendFx.setPayIntv(0);
        smOrderExtendFx.setPayYears(0);
        smOrderExtendFx.setPayYearsIntv("payUnit");
        smOrderExtendFx.setInsureType("insureType");
        smOrderExtendFx.setInsureYears(0);
        smOrderExtendFx.setInsureYearsIntv("insureYearsIntv");
        smOrderExtendFx.setPolicyNo("policyNo");
        smOrderExtendFx.setOrderSource("orderSource");
        when(mockExtendFxMapper.selectByOnlyCode("onlyCode")).thenReturn(smOrderExtendFx);

        when(mockAdapter.aesDecrypt("object")).thenReturn("扩展信息不存在");

        // Run the test
        final String result = fxOrderServiceUnderTest.decryptNotifyContentByOnlyCode("onlyCode");

        // Verify the results
        assertEquals("扩展信息不存在", result);
    }

    @Test
    public void testDecryptNotifyContentByOnlyCode_SmOrderExtendFxMapperReturnsNull() {
        // Setup
        when(mockExtendFxMapper.selectByOnlyCode("onlyCode")).thenReturn(null);

        // Run the test
        final String result = fxOrderServiceUnderTest.decryptNotifyContentByOnlyCode("onlyCode");

        // Verify the results
        assertEquals("扩展信息不存在", result);
    }

    @Test
    public void testManualHandCallBack() throws Exception {
        // Setup
        final MockHttpServletRequest request = new MockHttpServletRequest();
        final MockHttpServletResponse response = new MockHttpServletResponse();

        // Configure SmOrderExtendFxMapper.selectByOnlyCode(...).
        final SmOrderExtendFx smOrderExtendFx = new SmOrderExtendFx();
        smOrderExtendFx.setId(0);
        smOrderExtendFx.setFhOrderId("fhOrderId");
        smOrderExtendFx.setFxOnlyCode("fxOnlyCode");
        smOrderExtendFx.setFxSourceCode("FX_SOURCE_CODE_WEBCPS01");
        smOrderExtendFx.setProductId("productCode");
        smOrderExtendFx.setNotifyContent("object");
        smOrderExtendFx.setNotifyDecryptContent("xmlStr");
        smOrderExtendFx.setJobNumber("channelAgentCode");
        smOrderExtendFx.setOrgCode("orgCode");
        smOrderExtendFx.setPayIntv(0);
        smOrderExtendFx.setPayYears(0);
        smOrderExtendFx.setPayYearsIntv("payUnit");
        smOrderExtendFx.setInsureType("insureType");
        smOrderExtendFx.setInsureYears(0);
        smOrderExtendFx.setInsureYearsIntv("insureYearsIntv");
        smOrderExtendFx.setPolicyNo("policyNo");
        smOrderExtendFx.setOrderSource("orderSource");
        when(mockExtendFxMapper.selectByOnlyCode("onlyCode")).thenReturn(smOrderExtendFx);

        // Configure FxOrderServiceAdapter.failNotifyRes(...).
        final FxReqHead fxReqHead = new FxReqHead();
        fxReqHead.setTransType("transType");
        fxReqHead.setSourceCode("sourceCode");
        fxReqHead.setSerialNo("serialNo");
        when(mockAdapter.failNotifyRes(fxReqHead, "02", "resultMsg")).thenReturn("result");

        // Configure SmOrderExtendFxMapper.selectByPolicyNo(...).
        final SmOrderExtendFx smOrderExtendFx1 = new SmOrderExtendFx();
        smOrderExtendFx1.setId(0);
        smOrderExtendFx1.setFhOrderId("fhOrderId");
        smOrderExtendFx1.setFxOnlyCode("fxOnlyCode");
        smOrderExtendFx1.setFxSourceCode("FX_SOURCE_CODE_WEBCPS01");
        smOrderExtendFx1.setProductId("productCode");
        smOrderExtendFx1.setNotifyContent("object");
        smOrderExtendFx1.setNotifyDecryptContent("xmlStr");
        smOrderExtendFx1.setJobNumber("channelAgentCode");
        smOrderExtendFx1.setOrgCode("orgCode");
        smOrderExtendFx1.setPayIntv(0);
        smOrderExtendFx1.setPayYears(0);
        smOrderExtendFx1.setPayYearsIntv("payUnit");
        smOrderExtendFx1.setInsureType("insureType");
        smOrderExtendFx1.setInsureYears(0);
        smOrderExtendFx1.setInsureYearsIntv("insureYearsIntv");
        smOrderExtendFx1.setPolicyNo("policyNo");
        smOrderExtendFx1.setOrderSource("orderSource");
        when(mockExtendFxMapper.selectByPolicyNo("policyNo")).thenReturn(smOrderExtendFx1);

        // Configure FxOrderServiceAdapter.successNotifyRes(...).
        final FxReqHead fxReqHead1 = new FxReqHead();
        fxReqHead1.setTransType("transType");
        fxReqHead1.setSourceCode("sourceCode");
        fxReqHead1.setSerialNo("serialNo");
        when(mockAdapter.successNotifyRes(fxReqHead1)).thenReturn("result");

        when(mockOrderNoGenerator.getNextNo("code")).thenReturn("fhOrderId");

        // Configure FxOrderServiceAdapter.getOldMainRiskInfo(...).
        final FxRiskInfo fxRiskInfo = new FxRiskInfo();
        fxRiskInfo.setRiskCode("riskCode");
        fxRiskInfo.setRiskName("riskName");
        fxRiskInfo.setPrem(new BigDecimal("0.00"));
        fxRiskInfo.setAmnt(new BigDecimal("0.00"));
        fxRiskInfo.setPayIntv(0);
        fxRiskInfo.setPayYears(0);
        fxRiskInfo.setPayYearsIntv("payUnit");
        fxRiskInfo.setInsureType("insureType");
        fxRiskInfo.setInsureYears(0);
        fxRiskInfo.setInsureYearsIntv("insureYearsIntv");
        final FxDutyInfo fxDutyInfo = new FxDutyInfo();
        fxDutyInfo.setDutyCode("dutyCode");
        fxDutyInfo.setDutyName("dutyName");
        fxDutyInfo.setPrem(new BigDecimal("0.00"));
        fxDutyInfo.setAmnt(new BigDecimal("0.00"));
        fxRiskInfo.setDutyList(Arrays.asList(fxDutyInfo));
        final OldFxPolicyInfo policyInfo = new OldFxPolicyInfo();
        policyInfo.setOnlyCode("onlyCode");
        policyInfo.setPolicyNo("policyNo");
        policyInfo.setChannelAgentCode("channelAgentCode");
        policyInfo.setTotalPrem(new BigDecimal("0.00"));
        policyInfo.setProductCode("productCode");
        final FxRiskInfo fxRiskInfo1 = new FxRiskInfo();
        fxRiskInfo1.setRiskCode("riskCode");
        fxRiskInfo1.setRiskName("riskName");
        fxRiskInfo1.setPrem(new BigDecimal("0.00"));
        fxRiskInfo1.setAmnt(new BigDecimal("0.00"));
        fxRiskInfo1.setPayIntv(0);
        fxRiskInfo1.setPayYears(0);
        fxRiskInfo1.setPayYearsIntv("payUnit");
        fxRiskInfo1.setInsureType("insureType");
        fxRiskInfo1.setInsureYears(0);
        fxRiskInfo1.setInsureYearsIntv("insureYearsIntv");
        final FxDutyInfo fxDutyInfo1 = new FxDutyInfo();
        fxDutyInfo1.setDutyCode("dutyCode");
        fxDutyInfo1.setDutyName("dutyName");
        fxDutyInfo1.setPrem(new BigDecimal("0.00"));
        fxDutyInfo1.setAmnt(new BigDecimal("0.00"));
        fxRiskInfo1.setDutyList(Arrays.asList(fxDutyInfo1));
        policyInfo.setRiskList(Arrays.asList(fxRiskInfo1));
        when(mockAdapter.getOldMainRiskInfo(policyInfo)).thenReturn(fxRiskInfo);

        // Configure SmOrderExtendFxMapper.updateNotifyContent(...).
        final SmOrderExtendFx extendFx = new SmOrderExtendFx();
        extendFx.setId(0);
        extendFx.setFhOrderId("fhOrderId");
        extendFx.setFxOnlyCode("fxOnlyCode");
        extendFx.setFxSourceCode("FX_SOURCE_CODE_WEBCPS01");
        extendFx.setProductId("productCode");
        extendFx.setNotifyContent("object");
        extendFx.setNotifyDecryptContent("xmlStr");
        extendFx.setJobNumber("channelAgentCode");
        extendFx.setOrgCode("orgCode");
        extendFx.setPayIntv(0);
        extendFx.setPayYears(0);
        extendFx.setPayYearsIntv("payUnit");
        extendFx.setInsureType("insureType");
        extendFx.setInsureYears(0);
        extendFx.setInsureYearsIntv("insureYearsIntv");
        extendFx.setPolicyNo("policyNo");
        extendFx.setOrderSource("orderSource");
        when(mockExtendFxMapper.updateNotifyContent(extendFx)).thenReturn(0);

        // Configure SmOrderExtendFxMapper.insertSelective(...).
        final SmOrderExtendFx record = new SmOrderExtendFx();
        record.setId(0);
        record.setFhOrderId("fhOrderId");
        record.setFxOnlyCode("fxOnlyCode");
        record.setFxSourceCode("FX_SOURCE_CODE_WEBCPS01");
        record.setProductId("productCode");
        record.setNotifyContent("object");
        record.setNotifyDecryptContent("xmlStr");
        record.setJobNumber("channelAgentCode");
        record.setOrgCode("orgCode");
        record.setPayIntv(0);
        record.setPayYears(0);
        record.setPayYearsIntv("payUnit");
        record.setInsureType("insureType");
        record.setInsureYears(0);
        record.setInsureYearsIntv("insureYearsIntv");
        record.setPolicyNo("policyNo");
        record.setOrderSource("orderSource");
        when(mockExtendFxMapper.insertSelective(record)).thenReturn(0);

        // Configure FxOrderServiceAdapter.cvtByOldNotify(...).
        final OrderSubmitRequest orderSubmitRequest = new OrderSubmitRequest();
        final FhProduct productInfo = new FhProduct();
        productInfo.setProductId("fhProductId");
        productInfo.setRecommendId("recommendId");
        productInfo.setRecommendMainJobNumber("recommendMainJobNumber");
        productInfo.setRecommendOrgCode("recommendOrgCode");
        orderSubmitRequest.setProductInfo(productInfo);
        final FhOrderInfo orderInfo = new FhOrderInfo();
        orderInfo.setSubmitTime("submitTime");
        orderInfo.setStartTime("startTime");
        orderInfo.setEndTime("endTime");
        orderSubmitRequest.setOrderInfo(orderInfo);
        final FhProposer proposerInfo = new FhProposer();
        proposerInfo.setPersonName("insuredName");
        proposerInfo.setPersonGender("insuredPersonGender");
        proposerInfo.setIdType("idType");
        proposerInfo.setIdNumber("cardNo");
        proposerInfo.setBirthday("birthday");
        proposerInfo.setCellPhone("cellPhone");
        proposerInfo.setEmail("email");
        proposerInfo.setAddress("address");
        proposerInfo.setAppStatus("-2");
        proposerInfo.setPolicyNo("insIdNumber");
        proposerInfo.setDownloadURL("downloadURL");
        orderSubmitRequest.setProposerInfo(proposerInfo);
        final FhInsuredPerson fhInsuredPerson = new FhInsuredPerson();
        fhInsuredPerson.setPersonName("insuredName");
        fhInsuredPerson.setPersonGender("insuredPersonGender");
        fhInsuredPerson.setIdType("idType");
        fhInsuredPerson.setIdNumber("cardNo");
        fhInsuredPerson.setBirthday("birthday");
        fhInsuredPerson.setCellPhone("cellPhone");
        fhInsuredPerson.setAppStatus("-2");
        fhInsuredPerson.setRelationship("insuredAppntShipCode");
        fhInsuredPerson.setFlightNo("flightNo");
        fhInsuredPerson.setFlightTime("flightTime");
        fhInsuredPerson.setOccupationCode("");
        fhInsuredPerson.setOccupationGroup("occupationGroup");
        fhInsuredPerson.setDestinationCountryText("destinationCountryText");
        fhInsuredPerson.setAnnualIncome("annualIncome");
        fhInsuredPerson.setStudentType("studentType");
        fhInsuredPerson.setSchoolType("schoolType");
        fhInsuredPerson.setSchoolNature("schoolNature");
        fhInsuredPerson.setSchoolName("schoolName");
        fhInsuredPerson.setSchoolClass("schoolClass");
        fhInsuredPerson.setStudentId("studentId");
        fhInsuredPerson.setIdPeriodStart("idPeriodStart");
        fhInsuredPerson.setIdPeriodEnd("idPeriodEnd");
        fhInsuredPerson.setSmoke("smoke");
        fhInsuredPerson.setOldPolicyNo("oldPolicyNo");
        final TestPremiumDutyForm testPremiumDutyForm = new TestPremiumDutyForm();
        fhInsuredPerson.setDuties(Arrays.asList(testPremiumDutyForm));
        final TestPremiumProductForm product = new TestPremiumProductForm();
        product.setProductId(0);
        product.setPlanId(0);
        product.setPlanCode("planCode");
        product.setPremium(new BigDecimal("0.00"));
        product.setQty(0);
        final TestPremiumDutyForm testPremiumDutyForm1 = new TestPremiumDutyForm();
        product.setPremiumDutyForms(Arrays.asList(testPremiumDutyForm1));
        fhInsuredPerson.setProduct(product);
        orderSubmitRequest.setInsuredPerson(Arrays.asList(fhInsuredPerson));
        final FhProperty propertyInfo = new FhProperty();
        propertyInfo.setPropertyInfoIsExist("0");
        orderSubmitRequest.setPropertyInfo(propertyInfo);
        final SmOrderHouseDTO house = new SmOrderHouseDTO();
        orderSubmitRequest.setHouse(house);
        final SmOrderCarInfoDTO carInfo = new SmOrderCarInfoDTO();
        orderSubmitRequest.setCarInfo(carInfo);
        orderSubmitRequest.setProductType("code");
        orderSubmitRequest.setBizCode("bizCode");
        orderSubmitRequest.setOrderOutType("orderOutType");
        orderSubmitRequest.setToken("token");
        orderSubmitRequest.setPlanId(0);
        orderSubmitRequest.setQty(0);
        orderSubmitRequest.setPreOrderId("preOrderId");
        orderSubmitRequest.setRenew(false);
        orderSubmitRequest.setRealRenewFlag(false);
        orderSubmitRequest.setAppNo("appNo");
        orderSubmitRequest.setAgentId(0);
        orderSubmitRequest.setJobCode("jobCode");
        final SmCreateOrderSubmitRequest smCreateOrderSubmitRequest = new SmCreateOrderSubmitRequest(orderSubmitRequest);
        final OldFxPayNotifyTransInfoReq transInfoReq = new OldFxPayNotifyTransInfoReq();
        final OldFxPolicyInfo policyInfo1 = new OldFxPolicyInfo();
        policyInfo1.setOnlyCode("onlyCode");
        policyInfo1.setPolicyNo("policyNo");
        policyInfo1.setChannelAgentCode("channelAgentCode");
        policyInfo1.setTotalPrem(new BigDecimal("0.00"));
        policyInfo1.setProductCode("productCode");
        final FxRiskInfo fxRiskInfo2 = new FxRiskInfo();
        fxRiskInfo2.setRiskCode("riskCode");
        fxRiskInfo2.setRiskName("riskName");
        fxRiskInfo2.setPrem(new BigDecimal("0.00"));
        fxRiskInfo2.setAmnt(new BigDecimal("0.00"));
        fxRiskInfo2.setPayIntv(0);
        fxRiskInfo2.setPayYears(0);
        fxRiskInfo2.setPayYearsIntv("payUnit");
        fxRiskInfo2.setInsureType("insureType");
        fxRiskInfo2.setInsureYears(0);
        fxRiskInfo2.setInsureYearsIntv("insureYearsIntv");
        final FxDutyInfo fxDutyInfo2 = new FxDutyInfo();
        fxDutyInfo2.setDutyCode("dutyCode");
        fxDutyInfo2.setDutyName("dutyName");
        fxDutyInfo2.setPrem(new BigDecimal("0.00"));
        fxDutyInfo2.setAmnt(new BigDecimal("0.00"));
        fxRiskInfo2.setDutyList(Arrays.asList(fxDutyInfo2));
        policyInfo1.setRiskList(Arrays.asList(fxRiskInfo2));
        transInfoReq.setPolicyInfo(policyInfo1);
        when(mockAdapter.cvtByOldNotify(transInfoReq, "fhOrderId", "orderSource", "xjxhXjcid", "xjxhXjuid")).thenReturn(smCreateOrderSubmitRequest);

        // Configure FxOrderServiceAdapter.getMainRiskInfo(...).
        final FxRiskInfo fxRiskInfo3 = new FxRiskInfo();
        fxRiskInfo3.setRiskCode("riskCode");
        fxRiskInfo3.setRiskName("riskName");
        fxRiskInfo3.setPrem(new BigDecimal("0.00"));
        fxRiskInfo3.setAmnt(new BigDecimal("0.00"));
        fxRiskInfo3.setPayIntv(0);
        fxRiskInfo3.setPayYears(0);
        fxRiskInfo3.setPayYearsIntv("payUnit");
        fxRiskInfo3.setInsureType("insureType");
        fxRiskInfo3.setInsureYears(0);
        fxRiskInfo3.setInsureYearsIntv("insureYearsIntv");
        final FxDutyInfo fxDutyInfo3 = new FxDutyInfo();
        fxDutyInfo3.setDutyCode("dutyCode");
        fxDutyInfo3.setDutyName("dutyName");
        fxDutyInfo3.setPrem(new BigDecimal("0.00"));
        fxDutyInfo3.setAmnt(new BigDecimal("0.00"));
        fxRiskInfo3.setDutyList(Arrays.asList(fxDutyInfo3));
        final FxPolicyInfo policyInfo2 = new FxPolicyInfo();
        policyInfo2.setOnlyCode("onlyCode");
        policyInfo2.setPolicyNo("policyNo");
        policyInfo2.setChannelAgentCode("channelAgentCode");
        policyInfo2.setTotalPrem(new BigDecimal("0.00"));
        policyInfo2.setProductCode("productCode");
        final FxRiskInfo fxRiskInfo4 = new FxRiskInfo();
        fxRiskInfo4.setRiskCode("riskCode");
        fxRiskInfo4.setRiskName("riskName");
        fxRiskInfo4.setPrem(new BigDecimal("0.00"));
        fxRiskInfo4.setAmnt(new BigDecimal("0.00"));
        fxRiskInfo4.setPayIntv(0);
        fxRiskInfo4.setPayYears(0);
        fxRiskInfo4.setPayYearsIntv("payUnit");
        fxRiskInfo4.setInsureType("insureType");
        fxRiskInfo4.setInsureYears(0);
        fxRiskInfo4.setInsureYearsIntv("insureYearsIntv");
        final FxDutyInfo fxDutyInfo4 = new FxDutyInfo();
        fxDutyInfo4.setDutyCode("dutyCode");
        fxDutyInfo4.setDutyName("dutyName");
        fxDutyInfo4.setPrem(new BigDecimal("0.00"));
        fxDutyInfo4.setAmnt(new BigDecimal("0.00"));
        fxRiskInfo4.setDutyList(Arrays.asList(fxDutyInfo4));
        policyInfo2.setRiskList(Arrays.asList(fxRiskInfo4));
        final FxInsuredInfo fxInsuredInfo = new FxInsuredInfo();
        fxInsuredInfo.setCardNo("cardNo");
        policyInfo2.setInsuredInfoList(Arrays.asList(fxInsuredInfo));
        when(mockAdapter.getMainRiskInfo(policyInfo2)).thenReturn(fxRiskInfo3);

        // Configure FxOrderServiceAdapter.cvtByNotify(...).
        final OrderSubmitRequest orderSubmitRequest1 = new OrderSubmitRequest();
        final FhProduct productInfo1 = new FhProduct();
        productInfo1.setProductId("fhProductId");
        productInfo1.setRecommendId("recommendId");
        productInfo1.setRecommendMainJobNumber("recommendMainJobNumber");
        productInfo1.setRecommendOrgCode("recommendOrgCode");
        orderSubmitRequest1.setProductInfo(productInfo1);
        final FhOrderInfo orderInfo1 = new FhOrderInfo();
        orderInfo1.setSubmitTime("submitTime");
        orderInfo1.setStartTime("startTime");
        orderInfo1.setEndTime("endTime");
        orderSubmitRequest1.setOrderInfo(orderInfo1);
        final FhProposer proposerInfo1 = new FhProposer();
        proposerInfo1.setPersonName("insuredName");
        proposerInfo1.setPersonGender("insuredPersonGender");
        proposerInfo1.setIdType("idType");
        proposerInfo1.setIdNumber("cardNo");
        proposerInfo1.setBirthday("birthday");
        proposerInfo1.setCellPhone("cellPhone");
        proposerInfo1.setEmail("email");
        proposerInfo1.setAddress("address");
        proposerInfo1.setAppStatus("-2");
        proposerInfo1.setPolicyNo("insIdNumber");
        proposerInfo1.setDownloadURL("downloadURL");
        orderSubmitRequest1.setProposerInfo(proposerInfo1);
        final FhInsuredPerson fhInsuredPerson1 = new FhInsuredPerson();
        fhInsuredPerson1.setPersonName("insuredName");
        fhInsuredPerson1.setPersonGender("insuredPersonGender");
        fhInsuredPerson1.setIdType("idType");
        fhInsuredPerson1.setIdNumber("cardNo");
        fhInsuredPerson1.setBirthday("birthday");
        fhInsuredPerson1.setCellPhone("cellPhone");
        fhInsuredPerson1.setAppStatus("-2");
        fhInsuredPerson1.setRelationship("insuredAppntShipCode");
        fhInsuredPerson1.setFlightNo("flightNo");
        fhInsuredPerson1.setFlightTime("flightTime");
        fhInsuredPerson1.setOccupationCode("");
        fhInsuredPerson1.setOccupationGroup("occupationGroup");
        fhInsuredPerson1.setDestinationCountryText("destinationCountryText");
        fhInsuredPerson1.setAnnualIncome("annualIncome");
        fhInsuredPerson1.setStudentType("studentType");
        fhInsuredPerson1.setSchoolType("schoolType");
        fhInsuredPerson1.setSchoolNature("schoolNature");
        fhInsuredPerson1.setSchoolName("schoolName");
        fhInsuredPerson1.setSchoolClass("schoolClass");
        fhInsuredPerson1.setStudentId("studentId");
        fhInsuredPerson1.setIdPeriodStart("idPeriodStart");
        fhInsuredPerson1.setIdPeriodEnd("idPeriodEnd");
        fhInsuredPerson1.setSmoke("smoke");
        fhInsuredPerson1.setOldPolicyNo("oldPolicyNo");
        final TestPremiumDutyForm testPremiumDutyForm2 = new TestPremiumDutyForm();
        fhInsuredPerson1.setDuties(Arrays.asList(testPremiumDutyForm2));
        final TestPremiumProductForm product1 = new TestPremiumProductForm();
        product1.setProductId(0);
        product1.setPlanId(0);
        product1.setPlanCode("planCode");
        product1.setPremium(new BigDecimal("0.00"));
        product1.setQty(0);
        final TestPremiumDutyForm testPremiumDutyForm3 = new TestPremiumDutyForm();
        product1.setPremiumDutyForms(Arrays.asList(testPremiumDutyForm3));
        fhInsuredPerson1.setProduct(product1);
        orderSubmitRequest1.setInsuredPerson(Arrays.asList(fhInsuredPerson1));
        final FhProperty propertyInfo1 = new FhProperty();
        propertyInfo1.setPropertyInfoIsExist("0");
        orderSubmitRequest1.setPropertyInfo(propertyInfo1);
        final SmOrderHouseDTO house1 = new SmOrderHouseDTO();
        orderSubmitRequest1.setHouse(house1);
        final SmOrderCarInfoDTO carInfo1 = new SmOrderCarInfoDTO();
        orderSubmitRequest1.setCarInfo(carInfo1);
        orderSubmitRequest1.setProductType("code");
        orderSubmitRequest1.setBizCode("bizCode");
        orderSubmitRequest1.setOrderOutType("orderOutType");
        orderSubmitRequest1.setToken("token");
        orderSubmitRequest1.setPlanId(0);
        orderSubmitRequest1.setQty(0);
        orderSubmitRequest1.setPreOrderId("preOrderId");
        orderSubmitRequest1.setRenew(false);
        orderSubmitRequest1.setRealRenewFlag(false);
        orderSubmitRequest1.setAppNo("appNo");
        orderSubmitRequest1.setAgentId(0);
        orderSubmitRequest1.setJobCode("jobCode");
        final SmCreateOrderSubmitRequest smCreateOrderSubmitRequest1 = new SmCreateOrderSubmitRequest(orderSubmitRequest1);
        final FxPayNotifyTransInfoReq transInfoReq1 = new FxPayNotifyTransInfoReq();
        final FxPolicyInfo policyInfo3 = new FxPolicyInfo();
        policyInfo3.setOnlyCode("onlyCode");
        policyInfo3.setPolicyNo("policyNo");
        policyInfo3.setChannelAgentCode("channelAgentCode");
        policyInfo3.setTotalPrem(new BigDecimal("0.00"));
        policyInfo3.setProductCode("productCode");
        final FxRiskInfo fxRiskInfo5 = new FxRiskInfo();
        fxRiskInfo5.setRiskCode("riskCode");
        fxRiskInfo5.setRiskName("riskName");
        fxRiskInfo5.setPrem(new BigDecimal("0.00"));
        fxRiskInfo5.setAmnt(new BigDecimal("0.00"));
        fxRiskInfo5.setPayIntv(0);
        fxRiskInfo5.setPayYears(0);
        fxRiskInfo5.setPayYearsIntv("payUnit");
        fxRiskInfo5.setInsureType("insureType");
        fxRiskInfo5.setInsureYears(0);
        fxRiskInfo5.setInsureYearsIntv("insureYearsIntv");
        final FxDutyInfo fxDutyInfo5 = new FxDutyInfo();
        fxDutyInfo5.setDutyCode("dutyCode");
        fxDutyInfo5.setDutyName("dutyName");
        fxDutyInfo5.setPrem(new BigDecimal("0.00"));
        fxDutyInfo5.setAmnt(new BigDecimal("0.00"));
        fxRiskInfo5.setDutyList(Arrays.asList(fxDutyInfo5));
        policyInfo3.setRiskList(Arrays.asList(fxRiskInfo5));
        final FxInsuredInfo fxInsuredInfo1 = new FxInsuredInfo();
        fxInsuredInfo1.setCardNo("cardNo");
        policyInfo3.setInsuredInfoList(Arrays.asList(fxInsuredInfo1));
        transInfoReq1.setPolicyInfo(policyInfo3);
        when(mockAdapter.cvtByNotify(transInfoReq1, "fhOrderId", "orderSource", "xjxhXjcid", "xjxhXjuid")).thenReturn(smCreateOrderSubmitRequest1);

        // Run the test
        try{
            fxOrderServiceUnderTest.manualHandCallBack(request, response);
        } catch(Exception e) {
        }


    }

    @Test
    public void testManualHandCallBack_SmOrderExtendFxMapperSelectByOnlyCodeReturnsNull() throws Exception {
        // Setup
        final MockHttpServletRequest request = new MockHttpServletRequest();
        final MockHttpServletResponse response = new MockHttpServletResponse();
        when(mockExtendFxMapper.selectByOnlyCode("onlyCode")).thenReturn(null);

        // Configure FxOrderServiceAdapter.failNotifyRes(...).
        final FxReqHead fxReqHead = new FxReqHead();
        fxReqHead.setTransType("transType");
        fxReqHead.setSourceCode("sourceCode");
        fxReqHead.setSerialNo("serialNo");
        when(mockAdapter.failNotifyRes(fxReqHead, "02", "resultMsg")).thenReturn("result");

        // Run the test
        try{
            fxOrderServiceUnderTest.manualHandCallBack(request, response);
        } catch(Exception e) {
        }

        // Verify the results
    }

    @Test
    public void testManualHandCallBack_SmOrderExtendFxMapperSelectByPolicyNoReturnsNull() throws Exception {
        // Setup
        final MockHttpServletRequest request = new MockHttpServletRequest();
        final MockHttpServletResponse response = new MockHttpServletResponse();

        // Configure SmOrderExtendFxMapper.selectByOnlyCode(...).
        final SmOrderExtendFx smOrderExtendFx = new SmOrderExtendFx();
        smOrderExtendFx.setId(0);
        smOrderExtendFx.setFhOrderId("fhOrderId");
        smOrderExtendFx.setFxOnlyCode("fxOnlyCode");
        smOrderExtendFx.setFxSourceCode("FX_SOURCE_CODE_WEBCPS01");
        smOrderExtendFx.setProductId("productCode");
        smOrderExtendFx.setNotifyContent("object");
        smOrderExtendFx.setNotifyDecryptContent("xmlStr");
        smOrderExtendFx.setJobNumber("channelAgentCode");
        smOrderExtendFx.setOrgCode("orgCode");
        smOrderExtendFx.setPayIntv(0);
        smOrderExtendFx.setPayYears(0);
        smOrderExtendFx.setPayYearsIntv("payUnit");
        smOrderExtendFx.setInsureType("insureType");
        smOrderExtendFx.setInsureYears(0);
        smOrderExtendFx.setInsureYearsIntv("insureYearsIntv");
        smOrderExtendFx.setPolicyNo("policyNo");
        smOrderExtendFx.setOrderSource("orderSource");
        when(mockExtendFxMapper.selectByOnlyCode("onlyCode")).thenReturn(smOrderExtendFx);

        when(mockExtendFxMapper.selectByPolicyNo("policyNo")).thenReturn(null);

        // Configure FxOrderServiceAdapter.failNotifyRes(...).
        final FxReqHead fxReqHead = new FxReqHead();
        fxReqHead.setTransType("transType");
        fxReqHead.setSourceCode("sourceCode");
        fxReqHead.setSerialNo("serialNo");
        when(mockAdapter.failNotifyRes(fxReqHead, "02", "resultMsg")).thenReturn("result");

        // Configure FxOrderServiceAdapter.successNotifyRes(...).
        final FxReqHead fxReqHead1 = new FxReqHead();
        fxReqHead1.setTransType("transType");
        fxReqHead1.setSourceCode("sourceCode");
        fxReqHead1.setSerialNo("serialNo");
        when(mockAdapter.successNotifyRes(fxReqHead1)).thenReturn("result");

        when(mockOrderNoGenerator.getNextNo("code")).thenReturn("fhOrderId");

        // Configure FxOrderServiceAdapter.getOldMainRiskInfo(...).
        final FxRiskInfo fxRiskInfo = new FxRiskInfo();
        fxRiskInfo.setRiskCode("riskCode");
        fxRiskInfo.setRiskName("riskName");
        fxRiskInfo.setPrem(new BigDecimal("0.00"));
        fxRiskInfo.setAmnt(new BigDecimal("0.00"));
        fxRiskInfo.setPayIntv(0);
        fxRiskInfo.setPayYears(0);
        fxRiskInfo.setPayYearsIntv("payUnit");
        fxRiskInfo.setInsureType("insureType");
        fxRiskInfo.setInsureYears(0);
        fxRiskInfo.setInsureYearsIntv("insureYearsIntv");
        final FxDutyInfo fxDutyInfo = new FxDutyInfo();
        fxDutyInfo.setDutyCode("dutyCode");
        fxDutyInfo.setDutyName("dutyName");
        fxDutyInfo.setPrem(new BigDecimal("0.00"));
        fxDutyInfo.setAmnt(new BigDecimal("0.00"));
        fxRiskInfo.setDutyList(Arrays.asList(fxDutyInfo));
        final OldFxPolicyInfo policyInfo = new OldFxPolicyInfo();
        policyInfo.setOnlyCode("onlyCode");
        policyInfo.setPolicyNo("policyNo");
        policyInfo.setChannelAgentCode("channelAgentCode");
        policyInfo.setTotalPrem(new BigDecimal("0.00"));
        policyInfo.setProductCode("productCode");
        final FxRiskInfo fxRiskInfo1 = new FxRiskInfo();
        fxRiskInfo1.setRiskCode("riskCode");
        fxRiskInfo1.setRiskName("riskName");
        fxRiskInfo1.setPrem(new BigDecimal("0.00"));
        fxRiskInfo1.setAmnt(new BigDecimal("0.00"));
        fxRiskInfo1.setPayIntv(0);
        fxRiskInfo1.setPayYears(0);
        fxRiskInfo1.setPayYearsIntv("payUnit");
        fxRiskInfo1.setInsureType("insureType");
        fxRiskInfo1.setInsureYears(0);
        fxRiskInfo1.setInsureYearsIntv("insureYearsIntv");
        final FxDutyInfo fxDutyInfo1 = new FxDutyInfo();
        fxDutyInfo1.setDutyCode("dutyCode");
        fxDutyInfo1.setDutyName("dutyName");
        fxDutyInfo1.setPrem(new BigDecimal("0.00"));
        fxDutyInfo1.setAmnt(new BigDecimal("0.00"));
        fxRiskInfo1.setDutyList(Arrays.asList(fxDutyInfo1));
        policyInfo.setRiskList(Arrays.asList(fxRiskInfo1));
        when(mockAdapter.getOldMainRiskInfo(policyInfo)).thenReturn(fxRiskInfo);

        // Configure SmOrderExtendFxMapper.updateNotifyContent(...).
        final SmOrderExtendFx extendFx = new SmOrderExtendFx();
        extendFx.setId(0);
        extendFx.setFhOrderId("fhOrderId");
        extendFx.setFxOnlyCode("fxOnlyCode");
        extendFx.setFxSourceCode("FX_SOURCE_CODE_WEBCPS01");
        extendFx.setProductId("productCode");
        extendFx.setNotifyContent("object");
        extendFx.setNotifyDecryptContent("xmlStr");
        extendFx.setJobNumber("channelAgentCode");
        extendFx.setOrgCode("orgCode");
        extendFx.setPayIntv(0);
        extendFx.setPayYears(0);
        extendFx.setPayYearsIntv("payUnit");
        extendFx.setInsureType("insureType");
        extendFx.setInsureYears(0);
        extendFx.setInsureYearsIntv("insureYearsIntv");
        extendFx.setPolicyNo("policyNo");
        extendFx.setOrderSource("orderSource");
        when(mockExtendFxMapper.updateNotifyContent(extendFx)).thenReturn(0);

        // Configure SmOrderExtendFxMapper.insertSelective(...).
        final SmOrderExtendFx record = new SmOrderExtendFx();
        record.setId(0);
        record.setFhOrderId("fhOrderId");
        record.setFxOnlyCode("fxOnlyCode");
        record.setFxSourceCode("FX_SOURCE_CODE_WEBCPS01");
        record.setProductId("productCode");
        record.setNotifyContent("object");
        record.setNotifyDecryptContent("xmlStr");
        record.setJobNumber("channelAgentCode");
        record.setOrgCode("orgCode");
        record.setPayIntv(0);
        record.setPayYears(0);
        record.setPayYearsIntv("payUnit");
        record.setInsureType("insureType");
        record.setInsureYears(0);
        record.setInsureYearsIntv("insureYearsIntv");
        record.setPolicyNo("policyNo");
        record.setOrderSource("orderSource");
        when(mockExtendFxMapper.insertSelective(record)).thenReturn(0);

        // Configure FxOrderServiceAdapter.cvtByOldNotify(...).
        final OrderSubmitRequest orderSubmitRequest = new OrderSubmitRequest();
        final FhProduct productInfo = new FhProduct();
        productInfo.setProductId("fhProductId");
        productInfo.setRecommendId("recommendId");
        productInfo.setRecommendMainJobNumber("recommendMainJobNumber");
        productInfo.setRecommendOrgCode("recommendOrgCode");
        orderSubmitRequest.setProductInfo(productInfo);
        final FhOrderInfo orderInfo = new FhOrderInfo();
        orderInfo.setSubmitTime("submitTime");
        orderInfo.setStartTime("startTime");
        orderInfo.setEndTime("endTime");
        orderSubmitRequest.setOrderInfo(orderInfo);
        final FhProposer proposerInfo = new FhProposer();
        proposerInfo.setPersonName("insuredName");
        proposerInfo.setPersonGender("insuredPersonGender");
        proposerInfo.setIdType("idType");
        proposerInfo.setIdNumber("cardNo");
        proposerInfo.setBirthday("birthday");
        proposerInfo.setCellPhone("cellPhone");
        proposerInfo.setEmail("email");
        proposerInfo.setAddress("address");
        proposerInfo.setAppStatus("-2");
        proposerInfo.setPolicyNo("insIdNumber");
        proposerInfo.setDownloadURL("downloadURL");
        orderSubmitRequest.setProposerInfo(proposerInfo);
        final FhInsuredPerson fhInsuredPerson = new FhInsuredPerson();
        fhInsuredPerson.setPersonName("insuredName");
        fhInsuredPerson.setPersonGender("insuredPersonGender");
        fhInsuredPerson.setIdType("idType");
        fhInsuredPerson.setIdNumber("cardNo");
        fhInsuredPerson.setBirthday("birthday");
        fhInsuredPerson.setCellPhone("cellPhone");
        fhInsuredPerson.setAppStatus("-2");
        fhInsuredPerson.setRelationship("insuredAppntShipCode");
        fhInsuredPerson.setFlightNo("flightNo");
        fhInsuredPerson.setFlightTime("flightTime");
        fhInsuredPerson.setOccupationCode("");
        fhInsuredPerson.setOccupationGroup("occupationGroup");
        fhInsuredPerson.setDestinationCountryText("destinationCountryText");
        fhInsuredPerson.setAnnualIncome("annualIncome");
        fhInsuredPerson.setStudentType("studentType");
        fhInsuredPerson.setSchoolType("schoolType");
        fhInsuredPerson.setSchoolNature("schoolNature");
        fhInsuredPerson.setSchoolName("schoolName");
        fhInsuredPerson.setSchoolClass("schoolClass");
        fhInsuredPerson.setStudentId("studentId");
        fhInsuredPerson.setIdPeriodStart("idPeriodStart");
        fhInsuredPerson.setIdPeriodEnd("idPeriodEnd");
        fhInsuredPerson.setSmoke("smoke");
        fhInsuredPerson.setOldPolicyNo("oldPolicyNo");
        final TestPremiumDutyForm testPremiumDutyForm = new TestPremiumDutyForm();
        fhInsuredPerson.setDuties(Arrays.asList(testPremiumDutyForm));
        final TestPremiumProductForm product = new TestPremiumProductForm();
        product.setProductId(0);
        product.setPlanId(0);
        product.setPlanCode("planCode");
        product.setPremium(new BigDecimal("0.00"));
        product.setQty(0);
        final TestPremiumDutyForm testPremiumDutyForm1 = new TestPremiumDutyForm();
        product.setPremiumDutyForms(Arrays.asList(testPremiumDutyForm1));
        fhInsuredPerson.setProduct(product);
        orderSubmitRequest.setInsuredPerson(Arrays.asList(fhInsuredPerson));
        final FhProperty propertyInfo = new FhProperty();
        propertyInfo.setPropertyInfoIsExist("0");
        orderSubmitRequest.setPropertyInfo(propertyInfo);
        final SmOrderHouseDTO house = new SmOrderHouseDTO();
        orderSubmitRequest.setHouse(house);
        final SmOrderCarInfoDTO carInfo = new SmOrderCarInfoDTO();
        orderSubmitRequest.setCarInfo(carInfo);
        orderSubmitRequest.setProductType("code");
        orderSubmitRequest.setBizCode("bizCode");
        orderSubmitRequest.setOrderOutType("orderOutType");
        orderSubmitRequest.setToken("token");
        orderSubmitRequest.setPlanId(0);
        orderSubmitRequest.setQty(0);
        orderSubmitRequest.setPreOrderId("preOrderId");
        orderSubmitRequest.setRenew(false);
        orderSubmitRequest.setRealRenewFlag(false);
        orderSubmitRequest.setAppNo("appNo");
        orderSubmitRequest.setAgentId(0);
        orderSubmitRequest.setJobCode("jobCode");
        final SmCreateOrderSubmitRequest smCreateOrderSubmitRequest = new SmCreateOrderSubmitRequest(orderSubmitRequest);
        final OldFxPayNotifyTransInfoReq transInfoReq = new OldFxPayNotifyTransInfoReq();
        final OldFxPolicyInfo policyInfo1 = new OldFxPolicyInfo();
        policyInfo1.setOnlyCode("onlyCode");
        policyInfo1.setPolicyNo("policyNo");
        policyInfo1.setChannelAgentCode("channelAgentCode");
        policyInfo1.setTotalPrem(new BigDecimal("0.00"));
        policyInfo1.setProductCode("productCode");
        final FxRiskInfo fxRiskInfo2 = new FxRiskInfo();
        fxRiskInfo2.setRiskCode("riskCode");
        fxRiskInfo2.setRiskName("riskName");
        fxRiskInfo2.setPrem(new BigDecimal("0.00"));
        fxRiskInfo2.setAmnt(new BigDecimal("0.00"));
        fxRiskInfo2.setPayIntv(0);
        fxRiskInfo2.setPayYears(0);
        fxRiskInfo2.setPayYearsIntv("payUnit");
        fxRiskInfo2.setInsureType("insureType");
        fxRiskInfo2.setInsureYears(0);
        fxRiskInfo2.setInsureYearsIntv("insureYearsIntv");
        final FxDutyInfo fxDutyInfo2 = new FxDutyInfo();
        fxDutyInfo2.setDutyCode("dutyCode");
        fxDutyInfo2.setDutyName("dutyName");
        fxDutyInfo2.setPrem(new BigDecimal("0.00"));
        fxDutyInfo2.setAmnt(new BigDecimal("0.00"));
        fxRiskInfo2.setDutyList(Arrays.asList(fxDutyInfo2));
        policyInfo1.setRiskList(Arrays.asList(fxRiskInfo2));
        transInfoReq.setPolicyInfo(policyInfo1);
        when(mockAdapter.cvtByOldNotify(transInfoReq, "fhOrderId", "orderSource", "xjxhXjcid", "xjxhXjuid")).thenReturn(smCreateOrderSubmitRequest);

        // Run the test
        try{
            fxOrderServiceUnderTest.manualHandCallBack(request, response);
        } catch(Exception e) {
        }

        // Verify the results
    }

    @Test
    public void testQueryOrderVisitResult() throws Exception {
        // Setup
        // Configure SmOrderExtendFxMapper.listByPolicyNos(...).
        final SmOrderExtendFx smOrderExtendFx = new SmOrderExtendFx();
        smOrderExtendFx.setId(0);
        smOrderExtendFx.setFhOrderId("fhOrderId");
        smOrderExtendFx.setFxOnlyCode("fxOnlyCode");
        smOrderExtendFx.setFxSourceCode("FX_SOURCE_CODE_WEBCPS01");
        smOrderExtendFx.setProductId("productCode");
        smOrderExtendFx.setNotifyContent("object");
        smOrderExtendFx.setNotifyDecryptContent("xmlStr");
        smOrderExtendFx.setJobNumber("channelAgentCode");
        smOrderExtendFx.setOrgCode("orgCode");
        smOrderExtendFx.setPayIntv(0);
        smOrderExtendFx.setPayYears(0);
        smOrderExtendFx.setPayYearsIntv("payUnit");
        smOrderExtendFx.setInsureType("insureType");
        smOrderExtendFx.setInsureYears(0);
        smOrderExtendFx.setInsureYearsIntv("insureYearsIntv");
        smOrderExtendFx.setPolicyNo("policyNo");
        smOrderExtendFx.setOrderSource("orderSource");
        final List<SmOrderExtendFx> smOrderExtendFxes = Arrays.asList(smOrderExtendFx);
        when(mockExtendFxMapper.listByPolicyNos(Arrays.asList("value"))).thenReturn(smOrderExtendFxes);

        // Configure FxOrderServiceAdapter.queryVisitRetByOldSourceCode(...).
        final FxVisitResultInfo fxVisitResultInfo = new FxVisitResultInfo();
        final FxVisitResInfo fxVisitResInfo = new FxVisitResInfo();
        fxVisitResInfo.setContNo("policyNo");
        fxVisitResInfo.setVisitWay("visitWay");
        fxVisitResInfo.setVisitResult("visitResult");
        fxVisitResInfo.setVisitDate("visitDate");
        fxVisitResultInfo.setVisitResInfoList(Arrays.asList(fxVisitResInfo));
        when(mockAdapter.queryVisitRetByOldSourceCode(Arrays.asList("value"), "sourceCode", "businessCode")).thenReturn(fxVisitResultInfo);

        // Run the test
        fxOrderServiceUnderTest.queryOrderVisitResult(Arrays.asList("value"));

        // Verify the results
    }

    @Test
    public void testQueryOrderVisitResult_SmOrderExtendFxMapperReturnsNoItems() {
        // Setup
        when(mockExtendFxMapper.listByPolicyNos(Arrays.asList("value"))).thenReturn(Collections.emptyList());

        // Run the test
        fxOrderServiceUnderTest.queryOrderVisitResult(Arrays.asList("value"));

        // Verify the results
    }

    @Test
    public void testQueryOrderVisitResult_FxOrderServiceAdapterThrowsException() throws Exception {
        // Setup
        // Configure SmOrderExtendFxMapper.listByPolicyNos(...).
        final SmOrderExtendFx smOrderExtendFx = new SmOrderExtendFx();
        smOrderExtendFx.setId(0);
        smOrderExtendFx.setFhOrderId("fhOrderId");
        smOrderExtendFx.setFxOnlyCode("fxOnlyCode");
        smOrderExtendFx.setFxSourceCode("FX_SOURCE_CODE_WEBCPS01");
        smOrderExtendFx.setProductId("productCode");
        smOrderExtendFx.setNotifyContent("object");
        smOrderExtendFx.setNotifyDecryptContent("xmlStr");
        smOrderExtendFx.setJobNumber("channelAgentCode");
        smOrderExtendFx.setOrgCode("orgCode");
        smOrderExtendFx.setPayIntv(0);
        smOrderExtendFx.setPayYears(0);
        smOrderExtendFx.setPayYearsIntv("payUnit");
        smOrderExtendFx.setInsureType("insureType");
        smOrderExtendFx.setInsureYears(0);
        smOrderExtendFx.setInsureYearsIntv("insureYearsIntv");
        smOrderExtendFx.setPolicyNo("policyNo");
        smOrderExtendFx.setOrderSource("orderSource");
        final List<SmOrderExtendFx> smOrderExtendFxes = Arrays.asList(smOrderExtendFx);
        when(mockExtendFxMapper.listByPolicyNos(Arrays.asList("value"))).thenReturn(smOrderExtendFxes);

        when(mockAdapter.queryVisitRetByOldSourceCode(Arrays.asList("value"), "sourceCode", "businessCode")).thenThrow(Exception.class);

        // Run the test
        fxOrderServiceUnderTest.queryOrderVisitResult(Arrays.asList("value"));

        // Verify the results
    }

    @Test
    public void testHandCancelCallBack() throws Exception {
        // Setup
        final MockHttpServletRequest request = new MockHttpServletRequest();
        final MockHttpServletResponse response = new MockHttpServletResponse();
        when(mockAdapter.aesDecryptCancelMsg("xml")).thenReturn("jsonStr");
        when(mockAdapter.checkCancelSign("jsonStr", "ebiz_sign")).thenReturn(false);

        // Configure FxOrderServiceAdapter.successCancelRes(...).
        final FxCancelReqHead fxReqHead = new FxCancelReqHead();
        fxReqHead.setAsyn("asyn");
        fxReqHead.setCallBackUrl("callBackUrl");
        fxReqHead.setUserName("userName");
        fxReqHead.setPasswd("passwd");
        fxReqHead.setBusinessNo("businessNo");
        when(mockAdapter.successCancelRes(fxReqHead)).thenReturn("result");

        // Configure FxOrderServiceAdapter.cvtCancelOrderPolicy(...).
        final SmOrderPolicy smOrderPolicy = new SmOrderPolicy();
        smOrderPolicy.setId(0);
        smOrderPolicy.setFhOrderId("fhOrderId");
        smOrderPolicy.setChannel("code");
        smOrderPolicy.setPolicyNo("policyNo");
        smOrderPolicy.setPremium(new BigDecimal("0.00"));
        smOrderPolicy.setAmount(new BigDecimal("0.00"));
        smOrderPolicy.setPayType("insureType");
        smOrderPolicy.setPayPeriod("payingYears");
        smOrderPolicy.setPayUnit("payUnit");
        smOrderPolicy.setValidPeriod("coveredType");
        smOrderPolicy.setValidUnit("insureYearsIntv");
        smOrderPolicy.setVisitTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        smOrderPolicy.setVisitWay("visitWay");
        smOrderPolicy.setVisitStatus("code");
        smOrderPolicy.setSurrenderTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        smOrderPolicy.setSurrenderValidTime(LocalDate.of(2020, 1, 1));
        smOrderPolicy.setCancelAmount(new BigDecimal("0.00"));
        smOrderPolicy.setSurrenderType("surrenderType");
        smOrderPolicy.setExtend("extend");
        final FxCancelTransInfoReq transInfo = new FxCancelTransInfoReq();
        transInfo.setContNo("contNo");
        transInfo.setEdorAcceptNo("edorAcceptNo");
        transInfo.setTotalPrem(new BigDecimal("0.00"));
        transInfo.setWTAllFlag("wTAllFlag");
        transInfo.setPOSState("pOSState");
        final SmOrderPolicy queryObj = new SmOrderPolicy();
        queryObj.setId(0);
        queryObj.setFhOrderId("fhOrderId");
        queryObj.setChannel("code");
        queryObj.setPolicyNo("policyNo");
        queryObj.setPremium(new BigDecimal("0.00"));
        queryObj.setAmount(new BigDecimal("0.00"));
        queryObj.setPayType("insureType");
        queryObj.setPayPeriod("payingYears");
        queryObj.setPayUnit("payUnit");
        queryObj.setValidPeriod("coveredType");
        queryObj.setValidUnit("insureYearsIntv");
        queryObj.setVisitTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        queryObj.setVisitWay("visitWay");
        queryObj.setVisitStatus("code");
        queryObj.setSurrenderTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        queryObj.setSurrenderValidTime(LocalDate.of(2020, 1, 1));
        queryObj.setCancelAmount(new BigDecimal("0.00"));
        queryObj.setSurrenderType("surrenderType");
        queryObj.setExtend("extend");
        when(mockAdapter.cvtCancelOrderPolicy(transInfo, queryObj)).thenReturn(smOrderPolicy);

        // Run the test
        try{
            fxOrderServiceUnderTest.handCancelCallBack("object", request, response);
        } catch(Exception e) {
        }
    }

    @Test
    public void testHandCancelCallBack_FxOrderServiceAdapterCheckCancelSignReturnsTrue() throws Exception {
        // Setup
        final MockHttpServletRequest request = new MockHttpServletRequest();
        final MockHttpServletResponse response = new MockHttpServletResponse();
        when(mockAdapter.aesDecryptCancelMsg("xml")).thenReturn("jsonStr");
        when(mockAdapter.checkCancelSign("jsonStr", "ebiz_sign")).thenReturn(true);

        // Configure FxOrderServiceAdapter.failCancelRes(...).
        final FxCancelReqHead fxReqHead = new FxCancelReqHead();
        fxReqHead.setAsyn("asyn");
        fxReqHead.setCallBackUrl("callBackUrl");
        fxReqHead.setUserName("userName");
        fxReqHead.setPasswd("passwd");
        fxReqHead.setBusinessNo("businessNo");
        when(mockAdapter.failCancelRes(fxReqHead, "02", "验签失败")).thenReturn("result");

        // Run the test
        try{
            fxOrderServiceUnderTest.handCancelCallBack("object", request, response);
        } catch(Exception e) {
        }

        // Verify the results
    }

    @Test
    public void testHandOrderPolicy() {
        // Setup
        // Configure SmOrderExtendFxMapper.listUnDoRecord(...).
        final SmOrderExtendFx smOrderExtendFx = new SmOrderExtendFx();
        smOrderExtendFx.setId(0);
        smOrderExtendFx.setFhOrderId("fhOrderId");
        smOrderExtendFx.setFxOnlyCode("fxOnlyCode");
        smOrderExtendFx.setFxSourceCode("FX_SOURCE_CODE_WEBCPS01");
        smOrderExtendFx.setProductId("productCode");
        smOrderExtendFx.setNotifyContent("object");
        smOrderExtendFx.setNotifyDecryptContent("xmlStr");
        smOrderExtendFx.setJobNumber("channelAgentCode");
        smOrderExtendFx.setOrgCode("orgCode");
        smOrderExtendFx.setPayIntv(0);
        smOrderExtendFx.setPayYears(0);
        smOrderExtendFx.setPayYearsIntv("payUnit");
        smOrderExtendFx.setInsureType("insureType");
        smOrderExtendFx.setInsureYears(0);
        smOrderExtendFx.setInsureYearsIntv("insureYearsIntv");
        smOrderExtendFx.setPolicyNo("policyNo");
        smOrderExtendFx.setOrderSource("orderSource");
        final List<SmOrderExtendFx> smOrderExtendFxes = Arrays.asList(smOrderExtendFx);
        when(mockExtendFxMapper.listUnDoRecord()).thenReturn(smOrderExtendFxes);

        // Run the test
        fxOrderServiceUnderTest.handOrderPolicy();

        // Verify the results
    }

    @Test
    public void testHandOrderPolicy_SmOrderExtendFxMapperListUnDoRecordReturnsNoItems() {
        // Setup
        when(mockExtendFxMapper.listUnDoRecord()).thenReturn(Collections.emptyList());

        // Run the test
        fxOrderServiceUnderTest.handOrderPolicy();

        // Verify the results
    }

    @Test
    public void testPushRenewalTerm() {
        // Setup
        when(mockFxApiProperties.getRenewalGraceDays()).thenReturn(0);

        // Run the test
        fxOrderServiceUnderTest.pushRenewalTerm(1, 0);

        // Verify the results
    }

    @Test
    public void testManualFxPayWay() {
        // Setup
        // Configure SmOrderExtendFxMapper.listByPolicyNos(...).
        final SmOrderExtendFx smOrderExtendFx = new SmOrderExtendFx();
        smOrderExtendFx.setId(0);
        smOrderExtendFx.setFhOrderId("fhOrderId");
        smOrderExtendFx.setFxOnlyCode("fxOnlyCode");
        smOrderExtendFx.setFxSourceCode("FX_SOURCE_CODE_WEBCPS01");
        smOrderExtendFx.setProductId("productCode");
        smOrderExtendFx.setNotifyContent("object");
        smOrderExtendFx.setNotifyDecryptContent("xmlStr");
        smOrderExtendFx.setJobNumber("channelAgentCode");
        smOrderExtendFx.setOrgCode("orgCode");
        smOrderExtendFx.setPayIntv(0);
        smOrderExtendFx.setPayYears(0);
        smOrderExtendFx.setPayYearsIntv("payUnit");
        smOrderExtendFx.setInsureType("insureType");
        smOrderExtendFx.setInsureYears(0);
        smOrderExtendFx.setInsureYearsIntv("insureYearsIntv");
        smOrderExtendFx.setPolicyNo("policyNo");
        smOrderExtendFx.setOrderSource("orderSource");
        final List<SmOrderExtendFx> smOrderExtendFxes = Arrays.asList(smOrderExtendFx);
        when(mockExtendFxMapper.listByPolicyNos(Arrays.asList("value"))).thenReturn(smOrderExtendFxes);

        // Run the test
        fxOrderServiceUnderTest.manualFxPayWay(Arrays.asList("value"), "channel");

        // Verify the results
    }

    @Test
    public void testManualFxPayWay_SmOrderExtendFxMapperReturnsNoItems() {
        // Setup
        when(mockExtendFxMapper.listByPolicyNos(Arrays.asList("value"))).thenReturn(Collections.emptyList());

        // Run the test
        fxOrderServiceUnderTest.manualFxPayWay(Arrays.asList("value"), "channel");

        // Verify the results
    }
}
