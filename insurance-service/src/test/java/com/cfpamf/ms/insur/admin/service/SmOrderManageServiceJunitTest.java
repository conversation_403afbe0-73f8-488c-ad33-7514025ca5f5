package com.cfpamf.ms.insur.admin.service;

import com.cfpamf.common.ms.util.DateUtil;
import com.cfpamf.ms.bms.facade.vo.ModuleVO;
import com.cfpamf.ms.bms.facade.vo.UserDetailVO;
import com.cfpamf.ms.insur.admin.constant.SmConstants;
import com.cfpamf.ms.insur.admin.dao.safes.auto.AutoOrderPolicyMapper;
import com.cfpamf.ms.insur.admin.pojo.po.auto.order.AutoOrderPolicy;
import com.cfpamf.ms.insur.admin.pojo.query.AutoOrderV3Query;
import com.cfpamf.ms.insur.admin.pojo.query.SmOrderQuery;
import com.cfpamf.ms.insur.admin.pojo.query.SmOrderV3Query;
import com.cfpamf.ms.insur.admin.pojo.vo.DictionaryVO;
import com.cfpamf.ms.insur.admin.pojo.vo.SmOrderListVO;
import com.cfpamf.ms.insur.admin.pojo.vo.SmOrderV3ListVO;
import com.cfpamf.ms.insur.base.bean.Pageable;
import com.cfpamf.ms.insur.base.util.HttpRequestUtil;
import com.cfpamf.ms.insur.base.util.SpringFactoryUtil;
import com.cfpamf.ms.insur.base.util.ThreadUserUtil;
import com.github.jsonzou.jmockdata.JMockData;
import com.github.pagehelper.PageInfo;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.core.task.SimpleAsyncTaskExecutor;
import org.springframework.mock.web.MockHttpServletResponse;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@RunWith(PowerMockRunner.class)
@PrepareForTest({HttpRequestUtil.class})
public class SmOrderManageServiceJunitTest extends BaseTest {

    @InjectMocks
    SmOrderManageService smOrderManageService;
    @org.mockito.Mock
    com.cfpamf.ms.insur.base.service.InsurPayService payService;
    @org.mockito.Mock
    com.cfpamf.ms.insur.admin.dao.safes.order.SmOrderDDDMapper orderDDDMapper;
    @org.mockito.Mock
    com.cfpamf.ms.insur.admin.dao.safes.order.SmOrderApplicantMapper applicantMapper;
    @org.mockito.Mock
    com.cfpamf.ms.insur.admin.dao.safes.order.SmOrderInsuredMapper insuredMapper;
    @org.mockito.Mock
    com.cfpamf.ms.insur.admin.service.SmCancelService cancelService;
    @org.mockito.Mock
    com.cfpamf.ms.insur.admin.dao.safes.SmProductFormBuyLimitMapper formBuyLimitMapper;
    @org.mockito.Mock
    com.cfpamf.ms.insur.admin.dao.safes.SmCmpySettingMapper cmpySettingMapper;
    @org.mockito.Mock
    com.cfpamf.ms.insur.admin.service.SmProductService productService;
    @org.mockito.Mock
    com.cfpamf.ms.insur.admin.service.SmProductHistoryService productHistoryService;
    @org.mockito.Mock
    com.cfpamf.ms.insur.admin.service.SmReportService reportService;
    @org.mockito.Mock
    com.cfpamf.ms.insur.base.util.PermissionUtil permissionUtil;
    @org.mockito.Mock
    com.cfpamf.ms.insur.admin.dao.safes.SmOrderMapper orderMapper;
    @org.mockito.Mock
    com.cfpamf.ms.insur.admin.dao.safes.SmOrderSupmtMapper supmtMapper;
    @org.mockito.Mock
    com.cfpamf.ms.insur.base.service.BmsService bmsService;
    @org.mockito.Mock
    com.cfpamf.ms.insur.admin.dao.safes.AuthUserMapper userMapper;
    @org.mockito.Mock
    com.cfpamf.ms.insur.base.config.BmsConfig bmsConfig;
    @org.mockito.Mock
    com.cfpamf.ms.insur.admin.validation.SmOfflineOrderValidation validation;
    @org.mockito.Mock
    com.cfpamf.ms.insur.admin.dao.safes.SmOrderImportMapper orderImportMapper;
    @org.mockito.Mock
    com.cfpamf.ms.insur.admin.dao.safes.SmOrderImportErrorMapper orderImportErrorMapper;
    @org.mockito.Mock
    com.cfpamf.ms.insur.admin.service.OrderNoGenerator orderNoGenerator;
    @org.mockito.Mock
    com.cfpamf.ms.insur.base.event.EventBusEngine eventBusEngine;
    @org.mockito.Mock
    com.fasterxml.jackson.databind.ObjectMapper objectMapper;
    @org.mockito.Mock
    com.cfpamf.ms.insur.admin.dao.safes.UserPostMapper userPostMapper;
    @org.mockito.Mock
    com.cfpamf.ms.insur.admin.service.SmPolicyRegisterService registerService;
    @org.mockito.Mock
    com.cfpamf.ms.insur.admin.dao.safes.SmOrderImportExtendMapper smOrderImportExtendMapper;
    @org.mockito.Mock
    com.cfpamf.ms.insur.admin.service.SmOrderImportService smOrderImportService;
    @org.mockito.Mock
    com.cfpamf.ms.insur.admin.dao.safes.order.SmOrderItemMapper smOrderItemMapper;
    @org.mockito.Mock
    com.cfpamf.ms.insur.admin.dao.safes.SmCommissionMapper cmsMapper;
    @Mock
    AutoOrderPolicyMapper autoOrderPolicyMapper;
    @Mock
    DictionaryService dictionaryService;

    @Test
    public void getOrderByPage() {
        try {
            setUp();
            SmOrderQuery mock = JMockData.mock(SmOrderQuery.class);
            mock.setApplicantdName(null);
            smOrderManageService.getOrderByPage(mock);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void getOrderByPageV2() {
        try {
            smOrderManageService.getOrderByPageV2(JMockData.mock(com.cfpamf.ms.insur.admin.pojo.query.SmOrderQuery.class));
        } catch (Exception e) {

        }
    }

    @Test
    public void getJobChangeOrderIds() {
        try {
            smOrderManageService.getJobChangeOrderIds(",ymMUr,", ",AwJWL,", JMockData.mock(java.util.Date.class), JMockData.mock(java.util.Date.class), ",RCaFl,");
        } catch (Exception e) {

        }
    }

    @Test
    public void getChangOrderList() {
        try {
            smOrderManageService.getChangOrderList(",IQDoQ,", ",MreDP,", JMockData.mock(java.util.Date.class), JMockData.mock(java.util.Date.class), ",RXBEv,");
        } catch (Exception e) {

        }
    }

    @Test
    public void getOrderSmsNotifyList() {
        try {
            smOrderManageService.getOrderSmsNotifyList(",MXXlh,");
        } catch (Exception e) {

        }
    }

    @Test
    public void getSupplementOrder() {
        try {
            smOrderManageService.getSupplementOrder(JMockData.mock(com.cfpamf.ms.insur.admin.pojo.query.OrderSupmtQuery.class));
        } catch (Exception e) {

        }
    }

    @Test
    public void getOrderCorrectRecords() {
        try {
            smOrderManageService.getOrderCorrectRecords(JMockData.mock(com.cfpamf.ms.insur.admin.pojo.query.SmOrderCorrectQuery.class));
        } catch (Exception e) {

        }
    }

    @Test
    public void getOrderSummary() {
        try {
            smOrderManageService.getOrderSummary(JMockData.mock(com.cfpamf.ms.insur.admin.pojo.query.SmOrderQuery.class));
        } catch (Exception e) {

        }
    }

    @Test
    public void checkUnpayOrderInsuredProductBuyLimit() {
        try {
            smOrderManageService.checkUnpayOrderInsuredProductBuyLimit(",lpYQy,");
        } catch (Exception e) {

        }
    }

    @Test
    public void downloadOrders() {
        try {
            smOrderManageService.downloadOrders(JMockData.mock(com.cfpamf.ms.insur.admin.pojo.query.SmOrderQuery.class), new MockHttpServletResponse());
        } catch (Exception e) {

        }
    }

    @Test
    public void insertSupplementOrder() {
        try {
            smOrderManageService.insertSupplementOrder(JMockData.mock(com.cfpamf.ms.insur.admin.pojo.dto.SmOrderSupmtDTO.class));
        } catch (Exception e) {

        }
    }

    @Test
    public void insertSupplementOrder1() {
        try {
            smOrderManageService.insertSupplementOrder(JMockData.mock(com.cfpamf.ms.insur.admin.pojo.dto.SmOrderSupmtDTO.class), ",GhLEi,");
        } catch (Exception e) {

        }
    }

    @Test
    public void correctOrder() {
        try {
            smOrderManageService.correctOrder(JMockData.mock(com.cfpamf.ms.insur.admin.pojo.dto.SmOrderCorrectDTO.class));
        } catch (Exception e) {

        }
    }

    @Test
    public void correctMonthField() {
        try {
            smOrderManageService.correctMonthField(JMockData.mock(com.cfpamf.ms.insur.admin.pojo.dto.SmOrderCorrectDTO.class), JMockData.mock(com.cfpamf.ms.insur.admin.pojo.vo.SmOrderToCorrectVO.class));
        } catch (Exception e) {

        }
    }

    @Test
    public void correctOrderAppStatus() {
        try {
            smOrderManageService.correctOrderAppStatus(JMockData.mock(com.cfpamf.ms.insur.admin.pojo.dto.SmOrderCorrectCancelDTO.class));
        } catch (Exception e) {

        }
    }

    @Test
    public void importOrder() throws Exception {
        try {
            smOrderManageService.importGroupOrder(JMockData.mock(com.cfpamf.ms.insur.admin.pojo.dto.SmOrderImportDTO.class), ",rhIYu,");
        } catch (Exception e) {

        }
    }

    @Test
    public void importConfirm() throws IOException {
        try {
            smOrderManageService.importConfirm(3333, ",hJgAh,");
        } catch (Exception e) {

        }
    }

    @Test
    public void importList() {
        try {
            smOrderManageService.importList(JMockData.mock(com.cfpamf.ms.insur.admin.pojo.query.SmOrderImportQuery.class));
        } catch (Exception e) {

        }
    }

    @Test
    public void listInsuredByPolicyNo() {
        try {
            smOrderManageService.listInsuredByPolicyNo(",dOUbz,");
        } catch (Exception e) {

        }
    }

    @Test
    public void pushSupplementSuccess() {
        try {
            smOrderManageService.pushSupplementSuccess(",nKaXY,");
        } catch (Exception e) {

        }
    }

    @Test
    public void getOrderDetail() {
        try {
            smOrderManageService.getOrderDetail(",aFaJX,");
        } catch (Exception e) {

        }
    }

    @Test
    public void getPaymentInfo() {
        try {
            smOrderManageService.getPaymentInfo(",BLbaj,");
        } catch (Exception e) {

        }
    }

    @Test
    public void getOrderMaster() {
        try {
            smOrderManageService.getOrderMaster(",DbCjh,");
        } catch (Exception e) {

        }
    }

    @Test
    public void getCancel() {
        try {
            smOrderManageService.getCancel(",THhNk,");
        } catch (Exception e) {

        }
    }

    @Test
    public void getProductCoverage() {
        try {
            smOrderManageService.getProductCoverage(",woDdZ,");
        } catch (Exception e) {

        }
    }

    @Test
    public void checkSubmitOrderInsuredProductBuyLimit() {
        try {
            smOrderManageService.checkSubmitOrderInsuredProductBuyLimit(JMockData.mock(java.util.List.class), JMockData.mock(int.class));
        } catch (Exception e) {

        }
    }

    @Test
    public void getLocalBaseOrderInfo() {
        try {
            smOrderManageService.getLocalBaseOrderInfo(",UzVNP,");
        } catch (Exception e) {

        }
    }


    /*@Test
    public void testDownloadOrdersV3() {
        init();
        MockHttpServletResponse response = new MockHttpServletResponse();
        SmOrderV3Query query = new SmOrderV3Query();
        query.setCreateDateStart(DateUtil.parseDate("2021-10-01"));
        query.setCreateDateEnd(DateUtil.parseDate("2021-10-19"));
        query.setApplicantdType(1);
        query.setInsuredType(1);
        query.setOwnerType(1);
        smOrderManageService.downloadOrdersV3(query , response);
    }*/

    /*@Test
    public void testDownloadCarOrdersV3() {
        init();
        MockHttpServletResponse response = new MockHttpServletResponse();
        AutoOrderV3Query query = new AutoOrderV3Query();
        query.setCreateDateStart(DateUtil.parseDate("2021-10-01"));
        query.setCreateDateEnd(DateUtil.parseDate("2021-10-19"));
        query.setApplicantdType(1);
        query.setInsuredType(1);
        query.setOwnerType(1);
        query.setTabType("CX");
        smOrderManageService.downloadCarOrdersV3(query , response);
    }*/

    /*@Test
    public void testGetOrderByPageV3() {
        init();
        SmOrderV3Query query = new SmOrderV3Query();
        query.setCreateDateStart(DateUtil.parseDate("2021-10-01"));
        query.setCreateDateEnd(DateUtil.parseDate("2021-10-19"));
        query.setApplicantdType(1);
        query.setInsuredType(1);
        query.setOwnerType(1);
        smOrderManageService.getOrderByPageV3(query);
    }*/

    /*@Test
    public void testDownloadOrders() {
        init();
        MockHttpServletResponse response = new MockHttpServletResponse();
        SmOrderQuery query = new SmOrderQuery();
        query.setCreateDateStart(DateUtil.parseDate("2021-10-01"));
        query.setCreateDateEnd(DateUtil.parseDate("2021-10-19"));
        query.setApplicantdType(1);
        query.setInsuredType(1);
        smOrderManageService.downloadOrders(query, response);
    }*/

    public void init() {
        UserDetailVO.UserRoleVO userRoleVO = JMockData.mock(UserDetailVO.UserRoleVO.class);
        userRoleVO.setRoleCode("R0001");
        userRoleVO.setRoleName("保险|管理员");
        userRoleVO.setUserRoleType(1);
        userRoleVO.setOrgId(10);
        List<UserDetailVO.UserRoleVO> roleVOList = new ArrayList<>();
        roleVOList.add(userRoleVO);

        UserDetailVO userDetailVO = JMockData.mock(UserDetailVO.class);
        userDetailVO.setRoleList(roleVOList);
        userDetailVO.setIsHeadOrg(true);
        userDetailVO.setOrgId(10);
        ThreadUserUtil.USER_DETAIL_TL.set(userDetailVO);
        ModuleVO moduleVO = Mockito.mock(ModuleVO.class);
        moduleVO.setModuleCode("ORDER_PAYMENT_AMOUNT");
        List<ModuleVO> moduleVOS = new ArrayList<>();
        moduleVOS.add(moduleVO);
        ThreadUserUtil.USER_MODULES_TL.set(moduleVOS);
    }

    @Override
    public void setUp() throws Exception {
        super.setUp();
        init();
        SmOrderListVO smOrderListVO = JMockData.mock(SmOrderListVO.class, con());
        SmOrderListVO cancelOrder = JMockData.mock(SmOrderListVO.class, con());
        cancelOrder.setProductAttrCode(SmConstants.PRODUCT_ATTR_PERSON);
        cancelOrder.setAppStatus(SmConstants.POLICY_STATUS_CANCEL_SUCCESS);
        List<SmOrderListVO> smOrderListVOList = new ArrayList<>();
        smOrderListVOList.add(smOrderListVO);
        smOrderListVOList.add(cancelOrder);
        Mockito.when(orderMapper.listOrders(Mockito.any())).thenReturn(smOrderListVOList);

        List<SmOrderListVO> orderListVOS = IntStream.range(1,3)
                .mapToObj(item -> JMockData.mock(SmOrderListVO.class, con()))
                .collect(Collectors.toList());
        Mockito.when(orderMapper.findOrderCommissionByOderIdList(Mockito.anyList())).thenReturn(orderListVOS);

        List<AutoOrderPolicy> autoOrderPolicies = IntStream.range(1, 3)
                .mapToObj(item -> JMockData.mock(AutoOrderPolicy.class, con()))
                .collect(Collectors.toList());
        Mockito.when(autoOrderPolicyMapper.listAutoOrderPolicyByOrderNo(Mockito.anyList())).thenReturn(autoOrderPolicies);



        Mockito.when(orderMapper.listOrdersV3(Mockito.any())).thenReturn(
                IntStream.range(1,3).mapToObj(item -> JMockData.mock(SmOrderV3ListVO.class)).collect(Collectors.toList())
        );
        Mockito.when(orderMapper.countOrdersV3(Mockito.any())).thenReturn(10);

        PageInfo<DictionaryVO> dictionaryVOPageInfo = new PageInfo<>();
        dictionaryVOPageInfo.setList(IntStream.range(1,3).mapToObj(item -> JMockData.mock(DictionaryVO.class)).collect(Collectors.toList()));
        Mockito.when(dictionaryService.getDictionarysByPage(Mockito.anyString(), Mockito.anyString(), Mockito.any(), Mockito.anyBoolean()))
                .thenReturn(dictionaryVOPageInfo);

        Mockito.when(SpringFactoryUtil.getBean("regex-Executor", AsyncTaskExecutor.class)).thenReturn(new SimpleAsyncTaskExecutor());



    }
}
