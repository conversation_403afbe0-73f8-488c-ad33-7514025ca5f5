package com.cfpamf.ms.insur.weixin.service;

import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.ms.insur.admin.dao.safes.SmCancelMapper;
import com.cfpamf.ms.insur.admin.pojo.dto.cancel.CancelDTO;
import com.cfpamf.ms.insur.admin.service.BaseTest;
import com.cfpamf.ms.insur.admin.service.SmCancelService;
import com.cfpamf.ms.insur.base.service.BusinessTokenService;
import com.cfpamf.ms.insur.base.util.HttpRequestUtil;
import com.cfpamf.ms.insur.base.util.PermissionUtil;
import com.cfpamf.ms.insur.base.util.RedisUtil;
import com.cfpamf.ms.insur.weixin.pojo.dto.WxCancelDTO;
import com.cfpamf.ms.insur.weixin.pojo.query.WxCancelQuery;
import com.github.jsonzou.jmockdata.JMockData;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

/**
 * <AUTHOR> 2020/3/10 16:50
 */
@Slf4j
@RunWith(PowerMockRunner.class)
@PrepareForTest({HttpRequestUtil.class, PermissionUtil.class})
public class WxCancelServiceTest extends BaseTest {
    @InjectMocks
    WxCancelService wxCancelService;

    @Mock
    SmCancelMapper cancelMapper;

    @Mock
    private BusinessTokenService tokenService;
    /**
     * 退保业务处理
     */
    @Mock
    SmCancelService cancelService;

    @Mock
    RedisUtil<String, String> redisUtil;

    @Mock
    PermissionUtil permissionUtil;

    @Override
    @Before
    public void setUp() throws Exception {
        super.setUp();
        Mockito.when(redisUtil.get(Mockito.any()))
                .thenReturn("{\"authorization\":\"eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiI2OTQiLCJlbXBsb3llZUlkIjoiNTE4MyIsImhyVXNlcklkIjoiMTE4ODA5OTY5IiwiYWNjb3VudCI6IjE1MTk3MDE4OTc2IiwidXNlck5hbWUiOiLkvZXnu6rmhI8iLCJqb2JOdW1iZXIiOiJaSE5YMDgwMTgiLCJvcmdJZCI6IjI0MyIsImhyT3JnSWQiOiIzMDkzMTciLCJock9yZ0NvZGUiOiJITlBKIiwiaHJPcmdOYW1lIjoi5bmz5rGfIiwiaHJPcmdUcmVlUGF0aCI6IjkwMDEwNTE1My8yODI3MzIvMTQ3MTYxMS8yODQ1MTcvMTQ0NDU3NS8zMDkzMTciLCJ1c2VyVHlwZSI6IjIiLCJyYW5kb21Db2RlIjoiMjhCMkMzODYtNUVGQi00MzY2LUEzRjEtQzJGODY5NEVGMUVDIn0.UNINiduyZvZZReqoOK073RwwSfYsGDIssZ-NWhU9bV9my4mFa8wCccXlwGKkP_VRe1Lz51MfE5UsfFNIdTkeDA\",\"bindAgent\":false,\"bindEmployee\":true,\"bindWeixin\":false,\"bmsRoleCode\":\"R0004\",\"bmsSystemId\":12,\"bmsToken\":\"eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiI2OTQiLCJlbXBsb3llZUlkIjoiNTE4MyIsImhyVXNlcklkIjoiMTE4ODA5OTY5IiwiYWNjb3VudCI6IjE1MTk3MDE4OTc2IiwidXNlck5hbWUiOiLkvZXnu6rmhI8iLCJqb2JOdW1iZXIiOiJaSE5YMDgwMTgiLCJvcmdJZCI6IjI0MyIsImhyT3JnSWQiOiIzMDkzMTciLCJock9yZ0NvZGUiOiJITlBKIiwiaHJPcmdOYW1lIjoi5bmz5rGfIiwiaHJPcmdUcmVlUGF0aCI6IjkwMDEwNTE1My8yODI3MzIvMTQ3MTYxMS8yODQ1MTcvMTQ0NDU3NS8zMDkzMTciLCJ1c2VyVHlwZSI6IjIiLCJyYW5kb21Db2RlIjoiMjhCMkMzODYtNUVGQi00MzY2LUEzRjEtQzJGODY5NEVGMUVDIn0.UNINiduyZvZZReqoOK073RwwSfYsGDIssZ-NWhU9bV9my4mFa8wCccXlwGKkP_VRe1Lz51MfE5UsfFNIdTkeDA\",\"contextUser\":\"ZHNX08018\",\"id\":9947,\"orgPath\":\"900105153/282732/1471611/284517/1444575/309317\",\"organizationFullName\":\"平江\",\"organizationName\":\"平江\",\"postCode\":\"1081\",\"realVerify\":true,\"regionName\":\"湖南区域\",\"roleUniqueId\":\"ZHNX08018\",\"showCmsRatio\":false,\"showProcessApproval\":false,\"status\":\"3\",\"userId\":\"ZHNX08018\",\"userMobile\":\"***********\",\"userName\":\"何绪意\",\"userType\":\"employee\",\"wxImgUrl\":\"http://thirdwx.qlogo.cn/mmopen/vi_32/DYAIOgq83erpuAFvlYzJibK4q7U4AFy7J0YQHHxNXUWHMZBnKTuTS7mnONtxP8GqsFdVehsYciagJBbmdtcD5zicg/132\",\"wxNickName\":\"a中和农信何绪意***********\",\"wxOpenId\":\"oK6-1wF85NO0cFxRkFisD1avtDEI\",\"wxUsers\":[{\"id\":9947,\"orgPath\":\"900105153/282732/1471611/284517/1444575/309317\",\"organizationFullName\":\"平江\",\"organizationName\":\"平江\",\"postCode\":\"1081\",\"regionName\":\"湖南区域\",\"status\":\"3\",\"userId\":\"ZHNX08018\",\"userMobile\":\"***********\",\"userName\":\"何绪意\",\"userType\":\"employee\",\"wxImgUrl\":\"http://thirdwx.qlogo.cn/mmopen/vi_32/DYAIOgq83erpuAFvlYzJibK4q7U4AFy7J0YQHHxNXUWHMZBnKTuTS7mnONtxP8GqsFdVehsYciagJBbmdtcD5zicg/132\",\"wxNickName\":\"a中和农信何绪意***********\",\"wxOpenId\":\"oK6-1wF85NO0cFxRkFisD1avtDEI\"}]}")
        ;
    }

    @Test
    public void apply() {
        Mockito.when(tokenService.validateBusinessToken(Mockito.any(), Mockito.anyString(), Mockito.anyString()))
                .thenReturn(true);
        wxCancelService.apply(JMockData.mock(WxCancelDTO.class));
    }

    @Test
    public void canCancels() {
        wxCancelService.canCancels(JMockData.mock(WxCancelQuery.class));
    }

    @Test
    public void withMe() {
        wxCancelService.withMe(JMockData.mock(WxCancelQuery.class));
    }

    @Test
    public void getTodoCount() {
        wxCancelService.getTodoCount(JMockData.mock(WxCancelQuery.class));
    }

    @Test
    public void cancelCancel() {
        wxCancelService.cancelCancel(JMockData.mock(CancelDTO.class));
    }

    @Test
    public void withMeAuditing() {
        try {
            wxCancelService.withMeAuditing(JMockData.mock(WxCancelQuery.class));
        }catch (MSBizNormalException e){
            e.printStackTrace();
        }
    }

    @Test
    public void withMeAudited() {
        wxCancelService.withMeAudited(JMockData.mock(WxCancelQuery.class));
    }
}
