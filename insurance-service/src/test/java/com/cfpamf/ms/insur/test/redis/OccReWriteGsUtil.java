package com.cfpamf.ms.insur.test.redis;

import com.cfpamf.ms.insur.base.util.ExcelReadUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;

/**
 * 国寿职业编码
 * <AUTHOR> 2020/3/17 15:00
 */
public class OccReWriteGsUtil {

    public static void main(String[] args) throws IOException {

        String o1 = null;

        String o2 = null;

        String o3;
        String o3Code;

        String type;

        String ss = "第六大类\n" +
                "6生产、运输设备操作人员及有关人员\"\n";
        System.err.println(ss.replaceAll("(.*\\d+)(.*)", "$2"));
        System.err.println("6239905测量员".replaceAll("(\\d+)(.*)", "$2"));

        File file = new File("/Users/<USER>/Downloads/test.xlsx");
        Workbook workbook = new XSSFWorkbook(new FileInputStream(file));
        HSSFWorkbook sheets = new HSSFWorkbook();
        HSSFSheet sheet = sheets.createSheet();
        Sheet sheetAt = workbook.getSheetAt(0);
        int lastRowNum = sheetAt.getLastRowNum();
        for (int i = 0; i < lastRowNum; i++) {
            Row row = sheetAt.getRow(i);
            String tmpo1 = ExcelReadUtil.getCellValue(row.getCell(0));
            String tmpo2 = ExcelReadUtil.getCellValue(row.getCell(1));
            String tmpo3 = ExcelReadUtil.getCellValue(row.getCell(2));
            type = ExcelReadUtil.getCellValue(row.getCell(3));

            if (StringUtils.isNotBlank(tmpo1)) {
                o1 = tmpo1;
                o1 = o1.replaceAll("(.*\\d+)(.*)", "$2");
            }
            if (StringUtils.isNotBlank(tmpo2)) {
                o2 = tmpo2;
                o2 = o2.replaceAll("(\\d+)(.*)", "$2");
            }
            if (StringUtils.endsWith(type, ".0")) {
                type = type.substring(0, type.length() - 2);
            }
            o3Code = tmpo3.replaceAll("(\\d+)(.*)", "$1");
            ;
            o3 = tmpo3.replaceAll("(\\d+)(.*)", "$2");
            HSSFRow row1 = sheet.createRow(i);
            row1.createCell(0).setCellValue(o3Code.substring(0, 1));
            row1.createCell(1).setCellValue(o1);
            row1.createCell(2).setCellValue(o3Code.substring(0, 3));
            row1.createCell(3).setCellValue(o2);
            row1.createCell(4).setCellValue(o3Code);
            row1.createCell(5).setCellValue(o3);
            row1.createCell(6).setCellValue(type);
            System.err.println(String.format("%s\t%s\t%s:%s %s", o1, o2, o3, o3Code, type));
        }
        sheets.write(new File("gs-occ.xls"));
    }
}
