package com.cfpamf.ms.insur.admin.event;

import com.cfpamf.ms.insur.base.event.EventBusEngine;
import org.junit.FixMethodOrder;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.MethodSorters;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 **/
/*@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("dev")
@FixMethodOrder(MethodSorters.NAME_ASCENDING)*/
public class EventBusEngineTest {

    /*@Autowired
    private EventBusEngine busEngine;

    @Test
    public void publish() throws InterruptedException {
        OrderCommissionChangeEvent event = new OrderCommissionChangeEvent("18269325A5cwPG01");
        busEngine.publish(event);
        Thread.sleep(10000);
    }

    @Test
    public void testAgentRegisterEvent() {
        AgentRegisterEvent event = new AgentRegisterEvent(100000);
        busEngine.post(event);
    }

    @Test
    public void testOrderAppSuccessSmsEvent() {
        OrderAppSuccessSmsEvent event = new OrderAppSuccessSmsEvent("18685860cApNkL01", "232301198603014190");
        busEngine.post(event);
    }

    @Test
    public void testOrderCommissionChangeEvent() {
        OrderCommissionChangeEvent event = new OrderCommissionChangeEvent("18685860cApNkL01");
        busEngine.post(event);
    }


    @Test
    public void testOrderCustomerChangeEvent() {
        OrderCustomerChangeEvent event = new OrderCustomerChangeEvent("18685860cApNkL01");
        busEngine.post(event);
    }

    @Test
    public void testWxClaimNotifyEvent() {
        WxClaimNotifyEvent event = new WxClaimNotifyEvent(2120, "stepDataCheckBySafesCenter", "", "");
        busEngine.post(event);
    }*/


}
