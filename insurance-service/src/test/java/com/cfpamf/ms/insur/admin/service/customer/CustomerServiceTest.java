package com.cfpamf.ms.insur.admin.service.customer;

import com.cfpamf.ms.insur.admin.dao.safes.*;
import com.cfpamf.ms.insur.admin.pojo.query.ChangeUserQuery;
import com.cfpamf.ms.insur.admin.pojo.query.CustomerQuery;
import com.cfpamf.ms.insur.admin.service.BaseTest;
import com.cfpamf.ms.insur.admin.service.CustomerService;
import com.cfpamf.ms.insur.base.config.BmsConfig;
import com.cfpamf.ms.insur.base.exception.BizException;
import com.cfpamf.ms.insur.base.util.HttpRequestUtil;
import com.cfpamf.ms.insur.weixin.dao.safes.WxOrderMapper;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @date 2021/9/27 17:13
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({HttpRequestUtil.class})
public class CustomerServiceTest extends BaseTest { 
    
    @InjectMocks
    CustomerService customerService;

    /**
     * bms配置
     */
    @Mock
    private BmsConfig bmsConfig;

    /**
     * 客户mapper
     */
    @Mock
    private CustomerMapper mapper;

    /**
     * 订单mapper
     */
    @Mock
    private WxOrderMapper wxOrderMapper;

    /**
     * 订单mapper
     */
    @Mock
    private SmOrderMapper smOrderMapper;

    /**
     * 用户mapper
     */
    @Mock
    private AuthUserMapper userMapper;

    @Mock
    private UserPostMapper userPostMapper;

    @Mock
    ObjectMapper objectMapper;
    @Mock
    private TmpCustomerAdminChangeImportRecordMapper tmpCustomerAdminChangeImportRecordMapper;

    @Test
    public void testGetCustomerListByPage(){
        CustomerQuery query = Mockito.mock(CustomerQuery.class);
        try {
            customerService.getCustomerListByPage(query);
        } catch (BizException e) {

        }
    }

    @Test
    public void testGetCustomerAdminLogListByPage(){
        CustomerQuery query = Mockito.mock(CustomerQuery.class);
        try {
            customerService.getCustomerAdminLogListByPage(query);
        } catch (BizException e) {

        }
    }
}
