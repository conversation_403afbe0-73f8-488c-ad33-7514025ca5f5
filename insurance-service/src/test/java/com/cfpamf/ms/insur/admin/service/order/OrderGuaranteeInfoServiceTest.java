package com.cfpamf.ms.insur.admin.service.order;

import com.cfpamf.ms.insur.admin.dao.safes.SmOrderMapper;
import com.cfpamf.ms.insur.admin.dao.safes.SmProductHistoryMapper;
import com.cfpamf.ms.insur.admin.dao.safes.order.SmOrderRiskDutyMapper;
import com.cfpamf.ms.insur.admin.dao.safes.product.SmPlanHistoryMapper;
import com.cfpamf.ms.insur.admin.dao.safes.product.SmPlanRiskDutyHistoryMapper;
import com.cfpamf.ms.insur.admin.dao.safes.product.risk.SysRiskDutyMapper;
import com.cfpamf.ms.insur.admin.pojo.po.order.SmOrderRiskDuty;
import com.cfpamf.ms.insur.admin.pojo.po.product.SmPlanRiskDutyHistory;
import com.cfpamf.ms.insur.admin.pojo.po.product.risk.SysRiskDuty;
import com.cfpamf.ms.insur.admin.pojo.vo.SmOrderListVO;
import com.cfpamf.ms.insur.admin.pojo.vo.SmProductDetailVO;
import com.cfpamf.ms.insur.admin.pojo.vo.product.OrderGuaranteeDutyInfoVO;
import com.cfpamf.ms.insur.admin.service.BaseTest;
import com.cfpamf.ms.insur.base.util.HttpRequestUtil;
import com.cfpamf.ms.insur.weixin.dao.safes.PolicyMapper;
import com.cfpamf.ms.insur.weixin.pojo.dto.product.OrderProductDTO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.math.BigDecimal;
import java.util.*;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.mockito.Mockito.when;

@RunWith(PowerMockRunner.class)
@PrepareForTest({HttpRequestUtil.class})
public class OrderGuaranteeInfoServiceTest extends BaseTest {

    @Mock private SmOrderMapper mockOrderMapper;
    @Mock private SmProductHistoryMapper mockProductHistoryMapper;
    @Mock private SmOrderRiskDutyMapper mockOrderRiskDutyMapper;
    @Mock private SmPlanHistoryMapper mockPlanHistoryMapper;
    @Mock private SmPlanRiskDutyHistoryMapper mockRiskDutyHistoryMapper;
    @Mock private PolicyMapper mockPolicyMapper;
    @Mock private SysRiskDutyMapper mockSysRiskDutyMapper;

    @InjectMocks private OrderGuaranteeInfoService orderGuaranteeInfoServiceUnderTest;

    @Test
    public void testInitNonCarOrderInfo1() {
        // Setup
        final SmOrderListVO orderVo = new SmOrderListVO();
        orderVo.setSubmitTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderVo.setFhOrderId("fhOrderId");
        orderVo.setProductId(0);
        orderVo.setPlanId(0);
        orderVo.setProductCreateType("productCreateType");

        final OrderGuaranteeDutyInfoVO.OrderGuaranteeRiskInfo orderGuaranteeRiskInfo = new OrderGuaranteeDutyInfoVO.OrderGuaranteeRiskInfo();
        final OrderGuaranteeDutyInfoVO orderGuaranteeDutyInfoVO = new OrderGuaranteeDutyInfoVO();
        orderGuaranteeDutyInfoVO.setRiskName("cvgItemName");
        orderGuaranteeDutyInfoVO.setRiskCode("riskCode");
        orderGuaranteeDutyInfoVO.setRiskId(0L);
        orderGuaranteeDutyInfoVO.setRiskAmount(new BigDecimal("0.00"));
        orderGuaranteeDutyInfoVO.setDutyName("cvgItemName");
        orderGuaranteeDutyInfoVO.setDutyId("dutyId");
        orderGuaranteeDutyInfoVO.setDutyCode("dutyCode");
        orderGuaranteeDutyInfoVO.setDutyAmount(new BigDecimal("0.00"));
        orderGuaranteeDutyInfoVO.setIncludedTotalInsuredAmount(0);
        orderGuaranteeDutyInfoVO.setDutyPremium(new BigDecimal("0.00"));
        orderGuaranteeDutyInfoVO.setProductCreateType("name");
        orderGuaranteeDutyInfoVO.setApiType(0);
        orderGuaranteeDutyInfoVO.setRiskPremium(new BigDecimal("0.00"));
        orderGuaranteeRiskInfo.setGuaranteeInfoVOList(Arrays.asList(orderGuaranteeDutyInfoVO));
        final List<OrderGuaranteeDutyInfoVO.OrderGuaranteeRiskInfo> expectedResult = Arrays.asList(orderGuaranteeRiskInfo);
        when(mockOrderMapper.queryOrderProductVersion(0, new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).thenReturn(0);

        // Configure SmProductHistoryMapper.getProductById(...).
        final SmProductDetailVO smProductDetailVO = new SmProductDetailVO();
        smProductDetailVO.setApiType(0);
        smProductDetailVO.setProductCode("productCode");
        smProductDetailVO.setProductAttrCode("productAttrCode");
        smProductDetailVO.setIntroduceImageUrl("introduceImageUrl");
        smProductDetailVO.setHeadImageUrl("headImageUrl");
        when(mockProductHistoryMapper.getProductById(0, 0)).thenReturn(smProductDetailVO);

        // Configure SmOrderRiskDutyMapper.select(...).
        final SmOrderRiskDuty smOrderRiskDuty = new SmOrderRiskDuty();
        smOrderRiskDuty.setId(0);
        smOrderRiskDuty.setFhOrderId("fhOrderId");
        smOrderRiskDuty.setInsuredIdNumber("idNumber");
        smOrderRiskDuty.setRiskName("cvgItemName");
        smOrderRiskDuty.setRiskId(0L);
        smOrderRiskDuty.setRiskCode("riskCode");
        smOrderRiskDuty.setDutyName("cvgItemName");
        smOrderRiskDuty.setDutyId("dutyId");
        smOrderRiskDuty.setDutyCode("dutyCode");
        smOrderRiskDuty.setPremium(new BigDecimal("0.00"));
        smOrderRiskDuty.setAmount(new BigDecimal("0.00"));
        smOrderRiskDuty.setRiskAmount(new BigDecimal("0.00"));
        smOrderRiskDuty.setRiskPremium(new BigDecimal("0.00"));
        final List<SmOrderRiskDuty> smOrderRiskDuties = Arrays.asList(smOrderRiskDuty);
        final SmOrderRiskDuty record = new SmOrderRiskDuty();
        record.setId(0);
        record.setFhOrderId("fhOrderId");
        record.setInsuredIdNumber("idNumber");
        record.setRiskName("cvgItemName");
        record.setRiskId(0L);
        record.setRiskCode("riskCode");
        record.setDutyName("cvgItemName");
        record.setDutyId("dutyId");
        record.setDutyCode("dutyCode");
        record.setPremium(new BigDecimal("0.00"));
        record.setAmount(new BigDecimal("0.00"));
        record.setRiskAmount(new BigDecimal("0.00"));
        record.setRiskPremium(new BigDecimal("0.00"));
        when(mockOrderRiskDutyMapper.select(record)).thenReturn(smOrderRiskDuties);

        // Configure SmPlanRiskDutyHistoryMapper.selectEnabled(...).
        final SmPlanRiskDutyHistory smPlanRiskDutyHistory = new SmPlanRiskDutyHistory();
        smPlanRiskDutyHistory.setId(0);
        smPlanRiskDutyHistory.setPlanId(0);
        smPlanRiskDutyHistory.setProductId(0);
        smPlanRiskDutyHistory.setDutyKey("dutyKey");
        smPlanRiskDutyHistory.setDutyVersion(0);
        smPlanRiskDutyHistory.setIncludedRiskInsuredAmount(0);
        smPlanRiskDutyHistory.setVersion(0);
        final List<SmPlanRiskDutyHistory> smPlanRiskDutyHistories = Arrays.asList(smPlanRiskDutyHistory);
        final SmPlanRiskDutyHistory query = new SmPlanRiskDutyHistory();
        query.setId(0);
        query.setPlanId(0);
        query.setProductId(0);
        query.setDutyKey("dutyKey");
        query.setDutyVersion(0);
        query.setIncludedRiskInsuredAmount(0);
        query.setVersion(0);
        when(mockRiskDutyHistoryMapper.selectEnabled(query)).thenReturn(smPlanRiskDutyHistories);

        // Configure SysRiskDutyMapper.selectByDutyKeyList(...).
        final SysRiskDuty sysRiskDuty = new SysRiskDuty();
        sysRiskDuty.setId(0);
        sysRiskDuty.setDutyKey("dutyKey");
        sysRiskDuty.setVersion(0);
        sysRiskDuty.setRiskKey("riskKey");
        sysRiskDuty.setDutyName("dutyName");
        final List<SysRiskDuty> sysRiskDuties = Arrays.asList(sysRiskDuty);
        when(mockSysRiskDutyMapper.selectByDutyKeyList(Arrays.asList("value"))).thenReturn(sysRiskDuties);

        // Configure PolicyMapper.queryOrderProduct(...).
        final OrderProductDTO orderProductDTO = new OrderProductDTO();
        orderProductDTO.setId(0);
        orderProductDTO.setOrderId("orderId");
        orderProductDTO.setProductId(0);
        orderProductDTO.setProductName("productName");
        orderProductDTO.setPlanId(0);
        when(mockPolicyMapper.queryOrderProduct("fhOrderId")).thenReturn(orderProductDTO);

        // Configure SmPlanHistoryMapper.queryOrderGuaranteeInfo(...).
        final OrderGuaranteeDutyInfoVO orderGuaranteeDutyInfoVO1 = new OrderGuaranteeDutyInfoVO();
        orderGuaranteeDutyInfoVO1.setRiskName("cvgItemName");
        orderGuaranteeDutyInfoVO1.setRiskCode("riskCode");
        orderGuaranteeDutyInfoVO1.setRiskId(0L);
        orderGuaranteeDutyInfoVO1.setRiskAmount(new BigDecimal("0.00"));
        orderGuaranteeDutyInfoVO1.setDutyName("cvgItemName");
        orderGuaranteeDutyInfoVO1.setDutyId("dutyId");
        orderGuaranteeDutyInfoVO1.setDutyCode("dutyCode");
        orderGuaranteeDutyInfoVO1.setDutyAmount(new BigDecimal("0.00"));
        orderGuaranteeDutyInfoVO1.setIncludedTotalInsuredAmount(0);
        orderGuaranteeDutyInfoVO1.setDutyPremium(new BigDecimal("0.00"));
        orderGuaranteeDutyInfoVO1.setProductCreateType("name");
        orderGuaranteeDutyInfoVO1.setApiType(0);
        orderGuaranteeDutyInfoVO1.setRiskPremium(new BigDecimal("0.00"));
        final List<OrderGuaranteeDutyInfoVO> orderGuaranteeDutyInfoVOS = Arrays.asList(orderGuaranteeDutyInfoVO1);
        when(mockPlanHistoryMapper.queryOrderGuaranteeInfo(0, 0)).thenReturn(orderGuaranteeDutyInfoVOS);

        // Run the test
        final List<OrderGuaranteeDutyInfoVO.OrderGuaranteeRiskInfo> result = orderGuaranteeInfoServiceUnderTest.initNonCarOrderInfo(orderVo);

        // Verify the results
    }

    @Test
    public void testInitNonCarOrderInfo1_SmOrderRiskDutyMapperReturnsNoItems() {
        // Setup
        final SmOrderListVO orderVo = new SmOrderListVO();
        orderVo.setSubmitTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderVo.setFhOrderId("fhOrderId");
        orderVo.setProductId(0);
        orderVo.setPlanId(0);
        orderVo.setProductCreateType("productCreateType");

        when(mockOrderMapper.queryOrderProductVersion(0, new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).thenReturn(0);

        // Configure SmProductHistoryMapper.getProductById(...).
        final SmProductDetailVO smProductDetailVO = new SmProductDetailVO();
        smProductDetailVO.setApiType(0);
        smProductDetailVO.setProductCode("productCode");
        smProductDetailVO.setProductAttrCode("productAttrCode");
        smProductDetailVO.setIntroduceImageUrl("introduceImageUrl");
        smProductDetailVO.setHeadImageUrl("headImageUrl");
        when(mockProductHistoryMapper.getProductById(0, 0)).thenReturn(smProductDetailVO);

        // Configure SmOrderRiskDutyMapper.select(...).
        final SmOrderRiskDuty record = new SmOrderRiskDuty();
        record.setId(0);
        record.setFhOrderId("fhOrderId");
        record.setInsuredIdNumber("idNumber");
        record.setRiskName("cvgItemName");
        record.setRiskId(0L);
        record.setRiskCode("riskCode");
        record.setDutyName("cvgItemName");
        record.setDutyId("dutyId");
        record.setDutyCode("dutyCode");
        record.setPremium(new BigDecimal("0.00"));
        record.setAmount(new BigDecimal("0.00"));
        record.setRiskAmount(new BigDecimal("0.00"));
        record.setRiskPremium(new BigDecimal("0.00"));
        when(mockOrderRiskDutyMapper.select(record)).thenReturn(Collections.emptyList());

        // Run the test
        final List<OrderGuaranteeDutyInfoVO.OrderGuaranteeRiskInfo> result = orderGuaranteeInfoServiceUnderTest.initNonCarOrderInfo(orderVo);

        // Verify the results
    }

    @Test
    public void testInitNonCarOrderInfo1_SmPlanRiskDutyHistoryMapperReturnsNoItems() {
        // Setup
        final SmOrderListVO orderVo = new SmOrderListVO();
        orderVo.setSubmitTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderVo.setFhOrderId("fhOrderId");
        orderVo.setProductId(0);
        orderVo.setPlanId(0);
        orderVo.setProductCreateType("productCreateType");

        when(mockOrderMapper.queryOrderProductVersion(0, new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).thenReturn(0);

        // Configure SmProductHistoryMapper.getProductById(...).
        final SmProductDetailVO smProductDetailVO = new SmProductDetailVO();
        smProductDetailVO.setApiType(0);
        smProductDetailVO.setProductCode("productCode");
        smProductDetailVO.setProductAttrCode("productAttrCode");
        smProductDetailVO.setIntroduceImageUrl("introduceImageUrl");
        smProductDetailVO.setHeadImageUrl("headImageUrl");
        when(mockProductHistoryMapper.getProductById(0, 0)).thenReturn(smProductDetailVO);

        // Configure SmOrderRiskDutyMapper.select(...).
        final SmOrderRiskDuty smOrderRiskDuty = new SmOrderRiskDuty();
        smOrderRiskDuty.setId(0);
        smOrderRiskDuty.setFhOrderId("fhOrderId");
        smOrderRiskDuty.setInsuredIdNumber("idNumber");
        smOrderRiskDuty.setRiskName("cvgItemName");
        smOrderRiskDuty.setRiskId(0L);
        smOrderRiskDuty.setRiskCode("riskCode");
        smOrderRiskDuty.setDutyName("cvgItemName");
        smOrderRiskDuty.setDutyId("dutyId");
        smOrderRiskDuty.setDutyCode("dutyCode");
        smOrderRiskDuty.setPremium(new BigDecimal("0.00"));
        smOrderRiskDuty.setAmount(new BigDecimal("0.00"));
        smOrderRiskDuty.setRiskAmount(new BigDecimal("0.00"));
        smOrderRiskDuty.setRiskPremium(new BigDecimal("0.00"));
        final List<SmOrderRiskDuty> smOrderRiskDuties = Arrays.asList(smOrderRiskDuty);
        final SmOrderRiskDuty record = new SmOrderRiskDuty();
        record.setId(0);
        record.setFhOrderId("fhOrderId");
        record.setInsuredIdNumber("idNumber");
        record.setRiskName("cvgItemName");
        record.setRiskId(0L);
        record.setRiskCode("riskCode");
        record.setDutyName("cvgItemName");
        record.setDutyId("dutyId");
        record.setDutyCode("dutyCode");
        record.setPremium(new BigDecimal("0.00"));
        record.setAmount(new BigDecimal("0.00"));
        record.setRiskAmount(new BigDecimal("0.00"));
        record.setRiskPremium(new BigDecimal("0.00"));
        when(mockOrderRiskDutyMapper.select(record)).thenReturn(smOrderRiskDuties);

        // Configure SmPlanRiskDutyHistoryMapper.selectEnabled(...).
        final SmPlanRiskDutyHistory query = new SmPlanRiskDutyHistory();
        query.setId(0);
        query.setPlanId(0);
        query.setProductId(0);
        query.setDutyKey("dutyKey");
        query.setDutyVersion(0);
        query.setIncludedRiskInsuredAmount(0);
        query.setVersion(0);
        when(mockRiskDutyHistoryMapper.selectEnabled(query)).thenReturn(Collections.emptyList());

        // Run the test
        final List<OrderGuaranteeDutyInfoVO.OrderGuaranteeRiskInfo> result = orderGuaranteeInfoServiceUnderTest.initNonCarOrderInfo(orderVo);

        // Verify the results
    }

    @Test
    public void testInitNonCarOrderInfo1_SysRiskDutyMapperReturnsNoItems() {
        // Setup
        final SmOrderListVO orderVo = new SmOrderListVO();
        orderVo.setSubmitTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderVo.setFhOrderId("fhOrderId");
        orderVo.setProductId(0);
        orderVo.setPlanId(0);
        orderVo.setProductCreateType("productCreateType");

        when(mockOrderMapper.queryOrderProductVersion(0, new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).thenReturn(0);

        // Configure SmProductHistoryMapper.getProductById(...).
        final SmProductDetailVO smProductDetailVO = new SmProductDetailVO();
        smProductDetailVO.setApiType(0);
        smProductDetailVO.setProductCode("productCode");
        smProductDetailVO.setProductAttrCode("productAttrCode");
        smProductDetailVO.setIntroduceImageUrl("introduceImageUrl");
        smProductDetailVO.setHeadImageUrl("headImageUrl");
        when(mockProductHistoryMapper.getProductById(0, 0)).thenReturn(smProductDetailVO);

        // Configure SmOrderRiskDutyMapper.select(...).
        final SmOrderRiskDuty smOrderRiskDuty = new SmOrderRiskDuty();
        smOrderRiskDuty.setId(0);
        smOrderRiskDuty.setFhOrderId("fhOrderId");
        smOrderRiskDuty.setInsuredIdNumber("idNumber");
        smOrderRiskDuty.setRiskName("cvgItemName");
        smOrderRiskDuty.setRiskId(0L);
        smOrderRiskDuty.setRiskCode("riskCode");
        smOrderRiskDuty.setDutyName("cvgItemName");
        smOrderRiskDuty.setDutyId("dutyId");
        smOrderRiskDuty.setDutyCode("dutyCode");
        smOrderRiskDuty.setPremium(new BigDecimal("0.00"));
        smOrderRiskDuty.setAmount(new BigDecimal("0.00"));
        smOrderRiskDuty.setRiskAmount(new BigDecimal("0.00"));
        smOrderRiskDuty.setRiskPremium(new BigDecimal("0.00"));
        final List<SmOrderRiskDuty> smOrderRiskDuties = Arrays.asList(smOrderRiskDuty);
        final SmOrderRiskDuty record = new SmOrderRiskDuty();
        record.setId(0);
        record.setFhOrderId("fhOrderId");
        record.setInsuredIdNumber("idNumber");
        record.setRiskName("cvgItemName");
        record.setRiskId(0L);
        record.setRiskCode("riskCode");
        record.setDutyName("cvgItemName");
        record.setDutyId("dutyId");
        record.setDutyCode("dutyCode");
        record.setPremium(new BigDecimal("0.00"));
        record.setAmount(new BigDecimal("0.00"));
        record.setRiskAmount(new BigDecimal("0.00"));
        record.setRiskPremium(new BigDecimal("0.00"));
        when(mockOrderRiskDutyMapper.select(record)).thenReturn(smOrderRiskDuties);

        // Configure SmPlanRiskDutyHistoryMapper.selectEnabled(...).
        final SmPlanRiskDutyHistory smPlanRiskDutyHistory = new SmPlanRiskDutyHistory();
        smPlanRiskDutyHistory.setId(0);
        smPlanRiskDutyHistory.setPlanId(0);
        smPlanRiskDutyHistory.setProductId(0);
        smPlanRiskDutyHistory.setDutyKey("dutyKey");
        smPlanRiskDutyHistory.setDutyVersion(0);
        smPlanRiskDutyHistory.setIncludedRiskInsuredAmount(0);
        smPlanRiskDutyHistory.setVersion(0);
        final List<SmPlanRiskDutyHistory> smPlanRiskDutyHistories = Arrays.asList(smPlanRiskDutyHistory);
        final SmPlanRiskDutyHistory query = new SmPlanRiskDutyHistory();
        query.setId(0);
        query.setPlanId(0);
        query.setProductId(0);
        query.setDutyKey("dutyKey");
        query.setDutyVersion(0);
        query.setIncludedRiskInsuredAmount(0);
        query.setVersion(0);
        when(mockRiskDutyHistoryMapper.selectEnabled(query)).thenReturn(smPlanRiskDutyHistories);

        when(mockSysRiskDutyMapper.selectByDutyKeyList(Arrays.asList("value"))).thenReturn(Collections.emptyList());

        // Run the test
        final List<OrderGuaranteeDutyInfoVO.OrderGuaranteeRiskInfo> result = orderGuaranteeInfoServiceUnderTest.initNonCarOrderInfo(orderVo);

        // Verify the results
    }

    @Test
    public void testInitNonCarOrderInfo1_PolicyMapperReturnsNull() {
        // Setup
        final SmOrderListVO orderVo = new SmOrderListVO();
        orderVo.setSubmitTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderVo.setFhOrderId("fhOrderId");
        orderVo.setProductId(0);
        orderVo.setPlanId(0);
        orderVo.setProductCreateType("productCreateType");

        final OrderGuaranteeDutyInfoVO.OrderGuaranteeRiskInfo orderGuaranteeRiskInfo = new OrderGuaranteeDutyInfoVO.OrderGuaranteeRiskInfo();
        final OrderGuaranteeDutyInfoVO orderGuaranteeDutyInfoVO = new OrderGuaranteeDutyInfoVO();
        orderGuaranteeDutyInfoVO.setRiskName("cvgItemName");
        orderGuaranteeDutyInfoVO.setRiskCode("riskCode");
        orderGuaranteeDutyInfoVO.setRiskId(0L);
        orderGuaranteeDutyInfoVO.setRiskAmount(new BigDecimal("0.00"));
        orderGuaranteeDutyInfoVO.setDutyName("cvgItemName");
        orderGuaranteeDutyInfoVO.setDutyId("dutyId");
        orderGuaranteeDutyInfoVO.setDutyCode("dutyCode");
        orderGuaranteeDutyInfoVO.setDutyAmount(new BigDecimal("0.00"));
        orderGuaranteeDutyInfoVO.setIncludedTotalInsuredAmount(0);
        orderGuaranteeDutyInfoVO.setDutyPremium(new BigDecimal("0.00"));
        orderGuaranteeDutyInfoVO.setProductCreateType("name");
        orderGuaranteeDutyInfoVO.setApiType(0);
        orderGuaranteeDutyInfoVO.setRiskPremium(new BigDecimal("0.00"));
        orderGuaranteeRiskInfo.setGuaranteeInfoVOList(Arrays.asList(orderGuaranteeDutyInfoVO));
        final List<OrderGuaranteeDutyInfoVO.OrderGuaranteeRiskInfo> expectedResult = Arrays.asList(orderGuaranteeRiskInfo);
        when(mockOrderMapper.queryOrderProductVersion(0, new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).thenReturn(0);

        // Configure SmProductHistoryMapper.getProductById(...).
        final SmProductDetailVO smProductDetailVO = new SmProductDetailVO();
        smProductDetailVO.setApiType(0);
        smProductDetailVO.setProductCode("productCode");
        smProductDetailVO.setProductAttrCode("productAttrCode");
        smProductDetailVO.setIntroduceImageUrl("introduceImageUrl");
        smProductDetailVO.setHeadImageUrl("headImageUrl");
        when(mockProductHistoryMapper.getProductById(0, 0)).thenReturn(smProductDetailVO);

        when(mockPolicyMapper.queryOrderProduct("fhOrderId")).thenReturn(null);

        // Configure SmOrderRiskDutyMapper.select(...).
        final SmOrderRiskDuty smOrderRiskDuty = new SmOrderRiskDuty();
        smOrderRiskDuty.setId(0);
        smOrderRiskDuty.setFhOrderId("fhOrderId");
        smOrderRiskDuty.setInsuredIdNumber("idNumber");
        smOrderRiskDuty.setRiskName("cvgItemName");
        smOrderRiskDuty.setRiskId(0L);
        smOrderRiskDuty.setRiskCode("riskCode");
        smOrderRiskDuty.setDutyName("cvgItemName");
        smOrderRiskDuty.setDutyId("dutyId");
        smOrderRiskDuty.setDutyCode("dutyCode");
        smOrderRiskDuty.setPremium(new BigDecimal("0.00"));
        smOrderRiskDuty.setAmount(new BigDecimal("0.00"));
        smOrderRiskDuty.setRiskAmount(new BigDecimal("0.00"));
        smOrderRiskDuty.setRiskPremium(new BigDecimal("0.00"));
        final List<SmOrderRiskDuty> smOrderRiskDuties = Arrays.asList(smOrderRiskDuty);
        final SmOrderRiskDuty record = new SmOrderRiskDuty();
        record.setId(0);
        record.setFhOrderId("fhOrderId");
        record.setInsuredIdNumber("idNumber");
        record.setRiskName("cvgItemName");
        record.setRiskId(0L);
        record.setRiskCode("riskCode");
        record.setDutyName("cvgItemName");
        record.setDutyId("dutyId");
        record.setDutyCode("dutyCode");
        record.setPremium(new BigDecimal("0.00"));
        record.setAmount(new BigDecimal("0.00"));
        record.setRiskAmount(new BigDecimal("0.00"));
        record.setRiskPremium(new BigDecimal("0.00"));
        when(mockOrderRiskDutyMapper.select(record)).thenReturn(smOrderRiskDuties);

        // Run the test
        final List<OrderGuaranteeDutyInfoVO.OrderGuaranteeRiskInfo> result = orderGuaranteeInfoServiceUnderTest.initNonCarOrderInfo(orderVo);

        // Verify the results
    }

    @Test
    public void testInitNonCarOrderInfo1_SmPlanHistoryMapperReturnsNoItems() {
        // Setup
        final SmOrderListVO orderVo = new SmOrderListVO();
        orderVo.setSubmitTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderVo.setFhOrderId("fhOrderId");
        orderVo.setProductId(0);
        orderVo.setPlanId(0);
        orderVo.setProductCreateType("productCreateType");

        when(mockOrderMapper.queryOrderProductVersion(0, new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).thenReturn(0);

        // Configure SmProductHistoryMapper.getProductById(...).
        final SmProductDetailVO smProductDetailVO = new SmProductDetailVO();
        smProductDetailVO.setApiType(0);
        smProductDetailVO.setProductCode("productCode");
        smProductDetailVO.setProductAttrCode("productAttrCode");
        smProductDetailVO.setIntroduceImageUrl("introduceImageUrl");
        smProductDetailVO.setHeadImageUrl("headImageUrl");
        when(mockProductHistoryMapper.getProductById(0, 0)).thenReturn(smProductDetailVO);

        when(mockPlanHistoryMapper.queryOrderGuaranteeInfo(0, 0)).thenReturn(Collections.emptyList());

        // Configure SmOrderRiskDutyMapper.select(...).
        final SmOrderRiskDuty smOrderRiskDuty = new SmOrderRiskDuty();
        smOrderRiskDuty.setId(0);
        smOrderRiskDuty.setFhOrderId("fhOrderId");
        smOrderRiskDuty.setInsuredIdNumber("idNumber");
        smOrderRiskDuty.setRiskName("cvgItemName");
        smOrderRiskDuty.setRiskId(0L);
        smOrderRiskDuty.setRiskCode("riskCode");
        smOrderRiskDuty.setDutyName("cvgItemName");
        smOrderRiskDuty.setDutyId("dutyId");
        smOrderRiskDuty.setDutyCode("dutyCode");
        smOrderRiskDuty.setPremium(new BigDecimal("0.00"));
        smOrderRiskDuty.setAmount(new BigDecimal("0.00"));
        smOrderRiskDuty.setRiskAmount(new BigDecimal("0.00"));
        smOrderRiskDuty.setRiskPremium(new BigDecimal("0.00"));
        final List<SmOrderRiskDuty> smOrderRiskDuties = Arrays.asList(smOrderRiskDuty);
        final SmOrderRiskDuty record = new SmOrderRiskDuty();
        record.setId(0);
        record.setFhOrderId("fhOrderId");
        record.setInsuredIdNumber("idNumber");
        record.setRiskName("cvgItemName");
        record.setRiskId(0L);
        record.setRiskCode("riskCode");
        record.setDutyName("cvgItemName");
        record.setDutyId("dutyId");
        record.setDutyCode("dutyCode");
        record.setPremium(new BigDecimal("0.00"));
        record.setAmount(new BigDecimal("0.00"));
        record.setRiskAmount(new BigDecimal("0.00"));
        record.setRiskPremium(new BigDecimal("0.00"));
        when(mockOrderRiskDutyMapper.select(record)).thenReturn(smOrderRiskDuties);

        // Run the test
        final List<OrderGuaranteeDutyInfoVO.OrderGuaranteeRiskInfo> result = orderGuaranteeInfoServiceUnderTest.initNonCarOrderInfo(orderVo);

        // Verify the results
    }

    @Test
    public void testInitNonCarOrderInfo2() {
        // Setup
        final SmOrderListVO orderVo = new SmOrderListVO();
        orderVo.setSubmitTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderVo.setFhOrderId("fhOrderId");
        orderVo.setProductId(0);
        orderVo.setPlanId(0);
        orderVo.setProductCreateType("productCreateType");

        final OrderGuaranteeDutyInfoVO.OrderGuaranteeRiskInfo orderGuaranteeRiskInfo = new OrderGuaranteeDutyInfoVO.OrderGuaranteeRiskInfo();
        final OrderGuaranteeDutyInfoVO orderGuaranteeDutyInfoVO = new OrderGuaranteeDutyInfoVO();
        orderGuaranteeDutyInfoVO.setRiskName("cvgItemName");
        orderGuaranteeDutyInfoVO.setRiskCode("riskCode");
        orderGuaranteeDutyInfoVO.setRiskId(0L);
        orderGuaranteeDutyInfoVO.setRiskAmount(new BigDecimal("0.00"));
        orderGuaranteeDutyInfoVO.setDutyName("cvgItemName");
        orderGuaranteeDutyInfoVO.setDutyId("dutyId");
        orderGuaranteeDutyInfoVO.setDutyCode("dutyCode");
        orderGuaranteeDutyInfoVO.setDutyAmount(new BigDecimal("0.00"));
        orderGuaranteeDutyInfoVO.setIncludedTotalInsuredAmount(0);
        orderGuaranteeDutyInfoVO.setDutyPremium(new BigDecimal("0.00"));
        orderGuaranteeDutyInfoVO.setProductCreateType("name");
        orderGuaranteeDutyInfoVO.setApiType(0);
        orderGuaranteeDutyInfoVO.setRiskPremium(new BigDecimal("0.00"));
        orderGuaranteeRiskInfo.setGuaranteeInfoVOList(Arrays.asList(orderGuaranteeDutyInfoVO));
        final List<OrderGuaranteeDutyInfoVO.OrderGuaranteeRiskInfo> expectedResult = Arrays.asList(orderGuaranteeRiskInfo);
        when(mockOrderMapper.queryOrderProductVersion(0, new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).thenReturn(0);

        // Configure SmProductHistoryMapper.getProductById(...).
        final SmProductDetailVO smProductDetailVO = new SmProductDetailVO();
        smProductDetailVO.setApiType(0);
        smProductDetailVO.setProductCode("productCode");
        smProductDetailVO.setProductAttrCode("productAttrCode");
        smProductDetailVO.setIntroduceImageUrl("introduceImageUrl");
        smProductDetailVO.setHeadImageUrl("headImageUrl");
        when(mockProductHistoryMapper.getProductById(0, 0)).thenReturn(smProductDetailVO);

        // Configure SmOrderRiskDutyMapper.select(...).
        final SmOrderRiskDuty smOrderRiskDuty = new SmOrderRiskDuty();
        smOrderRiskDuty.setId(0);
        smOrderRiskDuty.setFhOrderId("fhOrderId");
        smOrderRiskDuty.setInsuredIdNumber("idNumber");
        smOrderRiskDuty.setRiskName("cvgItemName");
        smOrderRiskDuty.setRiskId(0L);
        smOrderRiskDuty.setRiskCode("riskCode");
        smOrderRiskDuty.setDutyName("cvgItemName");
        smOrderRiskDuty.setDutyId("dutyId");
        smOrderRiskDuty.setDutyCode("dutyCode");
        smOrderRiskDuty.setPremium(new BigDecimal("0.00"));
        smOrderRiskDuty.setAmount(new BigDecimal("0.00"));
        smOrderRiskDuty.setRiskAmount(new BigDecimal("0.00"));
        smOrderRiskDuty.setRiskPremium(new BigDecimal("0.00"));
        final List<SmOrderRiskDuty> smOrderRiskDuties = Arrays.asList(smOrderRiskDuty);
        final SmOrderRiskDuty record = new SmOrderRiskDuty();
        record.setId(0);
        record.setFhOrderId("fhOrderId");
        record.setInsuredIdNumber("idNumber");
        record.setRiskName("cvgItemName");
        record.setRiskId(0L);
        record.setRiskCode("riskCode");
        record.setDutyName("cvgItemName");
        record.setDutyId("dutyId");
        record.setDutyCode("dutyCode");
        record.setPremium(new BigDecimal("0.00"));
        record.setAmount(new BigDecimal("0.00"));
        record.setRiskAmount(new BigDecimal("0.00"));
        record.setRiskPremium(new BigDecimal("0.00"));
        when(mockOrderRiskDutyMapper.select(record)).thenReturn(smOrderRiskDuties);

        // Configure SmPlanRiskDutyHistoryMapper.selectEnabled(...).
        final SmPlanRiskDutyHistory smPlanRiskDutyHistory = new SmPlanRiskDutyHistory();
        smPlanRiskDutyHistory.setId(0);
        smPlanRiskDutyHistory.setPlanId(0);
        smPlanRiskDutyHistory.setProductId(0);
        smPlanRiskDutyHistory.setDutyKey("dutyKey");
        smPlanRiskDutyHistory.setDutyVersion(0);
        smPlanRiskDutyHistory.setIncludedRiskInsuredAmount(0);
        smPlanRiskDutyHistory.setVersion(0);
        final List<SmPlanRiskDutyHistory> smPlanRiskDutyHistories = Arrays.asList(smPlanRiskDutyHistory);
        final SmPlanRiskDutyHistory query = new SmPlanRiskDutyHistory();
        query.setId(0);
        query.setPlanId(0);
        query.setProductId(0);
        query.setDutyKey("dutyKey");
        query.setDutyVersion(0);
        query.setIncludedRiskInsuredAmount(0);
        query.setVersion(0);
        when(mockRiskDutyHistoryMapper.selectEnabled(query)).thenReturn(smPlanRiskDutyHistories);

        // Configure SysRiskDutyMapper.selectByDutyKeyList(...).
        final SysRiskDuty sysRiskDuty = new SysRiskDuty();
        sysRiskDuty.setId(0);
        sysRiskDuty.setDutyKey("dutyKey");
        sysRiskDuty.setVersion(0);
        sysRiskDuty.setRiskKey("riskKey");
        sysRiskDuty.setDutyName("dutyName");
        final List<SysRiskDuty> sysRiskDuties = Arrays.asList(sysRiskDuty);
        when(mockSysRiskDutyMapper.selectByDutyKeyList(Arrays.asList("value"))).thenReturn(sysRiskDuties);

        // Configure PolicyMapper.queryOrderProduct(...).
        final OrderProductDTO orderProductDTO = new OrderProductDTO();
        orderProductDTO.setId(0);
        orderProductDTO.setOrderId("orderId");
        orderProductDTO.setProductId(0);
        orderProductDTO.setProductName("productName");
        orderProductDTO.setPlanId(0);
        when(mockPolicyMapper.queryOrderProduct("fhOrderId")).thenReturn(orderProductDTO);

        // Configure SmPlanHistoryMapper.queryOrderGuaranteeInfo(...).
        final OrderGuaranteeDutyInfoVO orderGuaranteeDutyInfoVO1 = new OrderGuaranteeDutyInfoVO();
        orderGuaranteeDutyInfoVO1.setRiskName("cvgItemName");
        orderGuaranteeDutyInfoVO1.setRiskCode("riskCode");
        orderGuaranteeDutyInfoVO1.setRiskId(0L);
        orderGuaranteeDutyInfoVO1.setRiskAmount(new BigDecimal("0.00"));
        orderGuaranteeDutyInfoVO1.setDutyName("cvgItemName");
        orderGuaranteeDutyInfoVO1.setDutyId("dutyId");
        orderGuaranteeDutyInfoVO1.setDutyCode("dutyCode");
        orderGuaranteeDutyInfoVO1.setDutyAmount(new BigDecimal("0.00"));
        orderGuaranteeDutyInfoVO1.setIncludedTotalInsuredAmount(0);
        orderGuaranteeDutyInfoVO1.setDutyPremium(new BigDecimal("0.00"));
        orderGuaranteeDutyInfoVO1.setProductCreateType("name");
        orderGuaranteeDutyInfoVO1.setApiType(0);
        orderGuaranteeDutyInfoVO1.setRiskPremium(new BigDecimal("0.00"));
        final List<OrderGuaranteeDutyInfoVO> orderGuaranteeDutyInfoVOS = Arrays.asList(orderGuaranteeDutyInfoVO1);
        when(mockPlanHistoryMapper.queryOrderGuaranteeInfo(0, 0)).thenReturn(orderGuaranteeDutyInfoVOS);

        // Run the test
        final List<OrderGuaranteeDutyInfoVO.OrderGuaranteeRiskInfo> result = orderGuaranteeInfoServiceUnderTest.initNonCarOrderInfo(orderVo, "idNumber");

        // Verify the results
    }

    @Test
    public void testInitNonCarOrderInfo2_SmOrderRiskDutyMapperReturnsNoItems() {
        // Setup
        final SmOrderListVO orderVo = new SmOrderListVO();
        orderVo.setSubmitTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderVo.setFhOrderId("fhOrderId");
        orderVo.setProductId(0);
        orderVo.setPlanId(0);
        orderVo.setProductCreateType("productCreateType");

        when(mockOrderMapper.queryOrderProductVersion(0, new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).thenReturn(0);

        // Configure SmProductHistoryMapper.getProductById(...).
        final SmProductDetailVO smProductDetailVO = new SmProductDetailVO();
        smProductDetailVO.setApiType(0);
        smProductDetailVO.setProductCode("productCode");
        smProductDetailVO.setProductAttrCode("productAttrCode");
        smProductDetailVO.setIntroduceImageUrl("introduceImageUrl");
        smProductDetailVO.setHeadImageUrl("headImageUrl");
        when(mockProductHistoryMapper.getProductById(0, 0)).thenReturn(smProductDetailVO);

        // Configure SmOrderRiskDutyMapper.select(...).
        final SmOrderRiskDuty record = new SmOrderRiskDuty();
        record.setId(0);
        record.setFhOrderId("fhOrderId");
        record.setInsuredIdNumber("idNumber");
        record.setRiskName("cvgItemName");
        record.setRiskId(0L);
        record.setRiskCode("riskCode");
        record.setDutyName("cvgItemName");
        record.setDutyId("dutyId");
        record.setDutyCode("dutyCode");
        record.setPremium(new BigDecimal("0.00"));
        record.setAmount(new BigDecimal("0.00"));
        record.setRiskAmount(new BigDecimal("0.00"));
        record.setRiskPremium(new BigDecimal("0.00"));
        when(mockOrderRiskDutyMapper.select(record)).thenReturn(Collections.emptyList());

        // Run the test
        final List<OrderGuaranteeDutyInfoVO.OrderGuaranteeRiskInfo> result = orderGuaranteeInfoServiceUnderTest.initNonCarOrderInfo(orderVo, "idNumber");

        // Verify the results
    }

    @Test
    public void testInitNonCarOrderInfo2_SmPlanRiskDutyHistoryMapperReturnsNoItems() {
        // Setup
        final SmOrderListVO orderVo = new SmOrderListVO();
        orderVo.setSubmitTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderVo.setFhOrderId("fhOrderId");
        orderVo.setProductId(0);
        orderVo.setPlanId(0);
        orderVo.setProductCreateType("productCreateType");

        when(mockOrderMapper.queryOrderProductVersion(0, new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).thenReturn(0);

        // Configure SmProductHistoryMapper.getProductById(...).
        final SmProductDetailVO smProductDetailVO = new SmProductDetailVO();
        smProductDetailVO.setApiType(0);
        smProductDetailVO.setProductCode("productCode");
        smProductDetailVO.setProductAttrCode("productAttrCode");
        smProductDetailVO.setIntroduceImageUrl("introduceImageUrl");
        smProductDetailVO.setHeadImageUrl("headImageUrl");
        when(mockProductHistoryMapper.getProductById(0, 0)).thenReturn(smProductDetailVO);

        // Configure SmOrderRiskDutyMapper.select(...).
        final SmOrderRiskDuty smOrderRiskDuty = new SmOrderRiskDuty();
        smOrderRiskDuty.setId(0);
        smOrderRiskDuty.setFhOrderId("fhOrderId");
        smOrderRiskDuty.setInsuredIdNumber("idNumber");
        smOrderRiskDuty.setRiskName("cvgItemName");
        smOrderRiskDuty.setRiskId(0L);
        smOrderRiskDuty.setRiskCode("riskCode");
        smOrderRiskDuty.setDutyName("cvgItemName");
        smOrderRiskDuty.setDutyId("dutyId");
        smOrderRiskDuty.setDutyCode("dutyCode");
        smOrderRiskDuty.setPremium(new BigDecimal("0.00"));
        smOrderRiskDuty.setAmount(new BigDecimal("0.00"));
        smOrderRiskDuty.setRiskAmount(new BigDecimal("0.00"));
        smOrderRiskDuty.setRiskPremium(new BigDecimal("0.00"));
        final List<SmOrderRiskDuty> smOrderRiskDuties = Arrays.asList(smOrderRiskDuty);
        final SmOrderRiskDuty record = new SmOrderRiskDuty();
        record.setId(0);
        record.setFhOrderId("fhOrderId");
        record.setInsuredIdNumber("idNumber");
        record.setRiskName("cvgItemName");
        record.setRiskId(0L);
        record.setRiskCode("riskCode");
        record.setDutyName("cvgItemName");
        record.setDutyId("dutyId");
        record.setDutyCode("dutyCode");
        record.setPremium(new BigDecimal("0.00"));
        record.setAmount(new BigDecimal("0.00"));
        record.setRiskAmount(new BigDecimal("0.00"));
        record.setRiskPremium(new BigDecimal("0.00"));
        when(mockOrderRiskDutyMapper.select(record)).thenReturn(smOrderRiskDuties);

        // Configure SmPlanRiskDutyHistoryMapper.selectEnabled(...).
        final SmPlanRiskDutyHistory query = new SmPlanRiskDutyHistory();
        query.setId(0);
        query.setPlanId(0);
        query.setProductId(0);
        query.setDutyKey("dutyKey");
        query.setDutyVersion(0);
        query.setIncludedRiskInsuredAmount(0);
        query.setVersion(0);
        when(mockRiskDutyHistoryMapper.selectEnabled(query)).thenReturn(Collections.emptyList());

        // Run the test
        final List<OrderGuaranteeDutyInfoVO.OrderGuaranteeRiskInfo> result = orderGuaranteeInfoServiceUnderTest.initNonCarOrderInfo(orderVo, "idNumber");

        // Verify the results
    }

    @Test
    public void testInitNonCarOrderInfo2_SysRiskDutyMapperReturnsNoItems() {
        // Setup
        final SmOrderListVO orderVo = new SmOrderListVO();
        orderVo.setSubmitTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderVo.setFhOrderId("fhOrderId");
        orderVo.setProductId(0);
        orderVo.setPlanId(0);
        orderVo.setProductCreateType("productCreateType");

        when(mockOrderMapper.queryOrderProductVersion(0, new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).thenReturn(0);

        // Configure SmProductHistoryMapper.getProductById(...).
        final SmProductDetailVO smProductDetailVO = new SmProductDetailVO();
        smProductDetailVO.setApiType(0);
        smProductDetailVO.setProductCode("productCode");
        smProductDetailVO.setProductAttrCode("productAttrCode");
        smProductDetailVO.setIntroduceImageUrl("introduceImageUrl");
        smProductDetailVO.setHeadImageUrl("headImageUrl");
        when(mockProductHistoryMapper.getProductById(0, 0)).thenReturn(smProductDetailVO);

        // Configure SmOrderRiskDutyMapper.select(...).
        final SmOrderRiskDuty smOrderRiskDuty = new SmOrderRiskDuty();
        smOrderRiskDuty.setId(0);
        smOrderRiskDuty.setFhOrderId("fhOrderId");
        smOrderRiskDuty.setInsuredIdNumber("idNumber");
        smOrderRiskDuty.setRiskName("cvgItemName");
        smOrderRiskDuty.setRiskId(0L);
        smOrderRiskDuty.setRiskCode("riskCode");
        smOrderRiskDuty.setDutyName("cvgItemName");
        smOrderRiskDuty.setDutyId("dutyId");
        smOrderRiskDuty.setDutyCode("dutyCode");
        smOrderRiskDuty.setPremium(new BigDecimal("0.00"));
        smOrderRiskDuty.setAmount(new BigDecimal("0.00"));
        smOrderRiskDuty.setRiskAmount(new BigDecimal("0.00"));
        smOrderRiskDuty.setRiskPremium(new BigDecimal("0.00"));
        final List<SmOrderRiskDuty> smOrderRiskDuties = Arrays.asList(smOrderRiskDuty);
        final SmOrderRiskDuty record = new SmOrderRiskDuty();
        record.setId(0);
        record.setFhOrderId("fhOrderId");
        record.setInsuredIdNumber("idNumber");
        record.setRiskName("cvgItemName");
        record.setRiskId(0L);
        record.setRiskCode("riskCode");
        record.setDutyName("cvgItemName");
        record.setDutyId("dutyId");
        record.setDutyCode("dutyCode");
        record.setPremium(new BigDecimal("0.00"));
        record.setAmount(new BigDecimal("0.00"));
        record.setRiskAmount(new BigDecimal("0.00"));
        record.setRiskPremium(new BigDecimal("0.00"));
        when(mockOrderRiskDutyMapper.select(record)).thenReturn(smOrderRiskDuties);

        // Configure SmPlanRiskDutyHistoryMapper.selectEnabled(...).
        final SmPlanRiskDutyHistory smPlanRiskDutyHistory = new SmPlanRiskDutyHistory();
        smPlanRiskDutyHistory.setId(0);
        smPlanRiskDutyHistory.setPlanId(0);
        smPlanRiskDutyHistory.setProductId(0);
        smPlanRiskDutyHistory.setDutyKey("dutyKey");
        smPlanRiskDutyHistory.setDutyVersion(0);
        smPlanRiskDutyHistory.setIncludedRiskInsuredAmount(0);
        smPlanRiskDutyHistory.setVersion(0);
        final List<SmPlanRiskDutyHistory> smPlanRiskDutyHistories = Arrays.asList(smPlanRiskDutyHistory);
        final SmPlanRiskDutyHistory query = new SmPlanRiskDutyHistory();
        query.setId(0);
        query.setPlanId(0);
        query.setProductId(0);
        query.setDutyKey("dutyKey");
        query.setDutyVersion(0);
        query.setIncludedRiskInsuredAmount(0);
        query.setVersion(0);
        when(mockRiskDutyHistoryMapper.selectEnabled(query)).thenReturn(smPlanRiskDutyHistories);

        when(mockSysRiskDutyMapper.selectByDutyKeyList(Arrays.asList("value"))).thenReturn(Collections.emptyList());

        // Run the test
        final List<OrderGuaranteeDutyInfoVO.OrderGuaranteeRiskInfo> result = orderGuaranteeInfoServiceUnderTest.initNonCarOrderInfo(orderVo, "idNumber");

        // Verify the results
    }

    @Test
    public void testInitNonCarOrderInfo2_PolicyMapperReturnsNull() {
        // Setup
        final SmOrderListVO orderVo = new SmOrderListVO();
        orderVo.setSubmitTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderVo.setFhOrderId("fhOrderId");
        orderVo.setProductId(0);
        orderVo.setPlanId(0);
        orderVo.setProductCreateType("productCreateType");

        final OrderGuaranteeDutyInfoVO.OrderGuaranteeRiskInfo orderGuaranteeRiskInfo = new OrderGuaranteeDutyInfoVO.OrderGuaranteeRiskInfo();
        final OrderGuaranteeDutyInfoVO orderGuaranteeDutyInfoVO = new OrderGuaranteeDutyInfoVO();
        orderGuaranteeDutyInfoVO.setRiskName("cvgItemName");
        orderGuaranteeDutyInfoVO.setRiskCode("riskCode");
        orderGuaranteeDutyInfoVO.setRiskId(0L);
        orderGuaranteeDutyInfoVO.setRiskAmount(new BigDecimal("0.00"));
        orderGuaranteeDutyInfoVO.setDutyName("cvgItemName");
        orderGuaranteeDutyInfoVO.setDutyId("dutyId");
        orderGuaranteeDutyInfoVO.setDutyCode("dutyCode");
        orderGuaranteeDutyInfoVO.setDutyAmount(new BigDecimal("0.00"));
        orderGuaranteeDutyInfoVO.setIncludedTotalInsuredAmount(0);
        orderGuaranteeDutyInfoVO.setDutyPremium(new BigDecimal("0.00"));
        orderGuaranteeDutyInfoVO.setProductCreateType("name");
        orderGuaranteeDutyInfoVO.setApiType(0);
        orderGuaranteeDutyInfoVO.setRiskPremium(new BigDecimal("0.00"));
        orderGuaranteeRiskInfo.setGuaranteeInfoVOList(Arrays.asList(orderGuaranteeDutyInfoVO));
        final List<OrderGuaranteeDutyInfoVO.OrderGuaranteeRiskInfo> expectedResult = Arrays.asList(orderGuaranteeRiskInfo);
        when(mockOrderMapper.queryOrderProductVersion(0, new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).thenReturn(0);

        // Configure SmProductHistoryMapper.getProductById(...).
        final SmProductDetailVO smProductDetailVO = new SmProductDetailVO();
        smProductDetailVO.setApiType(0);
        smProductDetailVO.setProductCode("productCode");
        smProductDetailVO.setProductAttrCode("productAttrCode");
        smProductDetailVO.setIntroduceImageUrl("introduceImageUrl");
        smProductDetailVO.setHeadImageUrl("headImageUrl");
        when(mockProductHistoryMapper.getProductById(0, 0)).thenReturn(smProductDetailVO);

        when(mockPolicyMapper.queryOrderProduct("fhOrderId")).thenReturn(null);

        // Configure SmOrderRiskDutyMapper.select(...).
        final SmOrderRiskDuty smOrderRiskDuty = new SmOrderRiskDuty();
        smOrderRiskDuty.setId(0);
        smOrderRiskDuty.setFhOrderId("fhOrderId");
        smOrderRiskDuty.setInsuredIdNumber("idNumber");
        smOrderRiskDuty.setRiskName("cvgItemName");
        smOrderRiskDuty.setRiskId(0L);
        smOrderRiskDuty.setRiskCode("riskCode");
        smOrderRiskDuty.setDutyName("cvgItemName");
        smOrderRiskDuty.setDutyId("dutyId");
        smOrderRiskDuty.setDutyCode("dutyCode");
        smOrderRiskDuty.setPremium(new BigDecimal("0.00"));
        smOrderRiskDuty.setAmount(new BigDecimal("0.00"));
        smOrderRiskDuty.setRiskAmount(new BigDecimal("0.00"));
        smOrderRiskDuty.setRiskPremium(new BigDecimal("0.00"));
        final List<SmOrderRiskDuty> smOrderRiskDuties = Arrays.asList(smOrderRiskDuty);
        final SmOrderRiskDuty record = new SmOrderRiskDuty();
        record.setId(0);
        record.setFhOrderId("fhOrderId");
        record.setInsuredIdNumber("idNumber");
        record.setRiskName("cvgItemName");
        record.setRiskId(0L);
        record.setRiskCode("riskCode");
        record.setDutyName("cvgItemName");
        record.setDutyId("dutyId");
        record.setDutyCode("dutyCode");
        record.setPremium(new BigDecimal("0.00"));
        record.setAmount(new BigDecimal("0.00"));
        record.setRiskAmount(new BigDecimal("0.00"));
        record.setRiskPremium(new BigDecimal("0.00"));
        when(mockOrderRiskDutyMapper.select(record)).thenReturn(smOrderRiskDuties);

        // Run the test
        final List<OrderGuaranteeDutyInfoVO.OrderGuaranteeRiskInfo> result = orderGuaranteeInfoServiceUnderTest.initNonCarOrderInfo(orderVo, "idNumber");

        // Verify the results
    }

    @Test
    public void testInitNonCarOrderInfo2_SmPlanHistoryMapperReturnsNoItems() {
        // Setup
        final SmOrderListVO orderVo = new SmOrderListVO();
        orderVo.setSubmitTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderVo.setFhOrderId("fhOrderId");
        orderVo.setProductId(0);
        orderVo.setPlanId(0);
        orderVo.setProductCreateType("productCreateType");

        when(mockOrderMapper.queryOrderProductVersion(0, new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).thenReturn(0);

        // Configure SmProductHistoryMapper.getProductById(...).
        final SmProductDetailVO smProductDetailVO = new SmProductDetailVO();
        smProductDetailVO.setApiType(0);
        smProductDetailVO.setProductCode("productCode");
        smProductDetailVO.setProductAttrCode("productAttrCode");
        smProductDetailVO.setIntroduceImageUrl("introduceImageUrl");
        smProductDetailVO.setHeadImageUrl("headImageUrl");
        when(mockProductHistoryMapper.getProductById(0, 0)).thenReturn(smProductDetailVO);

        when(mockPlanHistoryMapper.queryOrderGuaranteeInfo(0, 0)).thenReturn(Collections.emptyList());

        // Configure SmOrderRiskDutyMapper.select(...).
        final SmOrderRiskDuty smOrderRiskDuty = new SmOrderRiskDuty();
        smOrderRiskDuty.setId(0);
        smOrderRiskDuty.setFhOrderId("fhOrderId");
        smOrderRiskDuty.setInsuredIdNumber("idNumber");
        smOrderRiskDuty.setRiskName("cvgItemName");
        smOrderRiskDuty.setRiskId(0L);
        smOrderRiskDuty.setRiskCode("riskCode");
        smOrderRiskDuty.setDutyName("cvgItemName");
        smOrderRiskDuty.setDutyId("dutyId");
        smOrderRiskDuty.setDutyCode("dutyCode");
        smOrderRiskDuty.setPremium(new BigDecimal("0.00"));
        smOrderRiskDuty.setAmount(new BigDecimal("0.00"));
        smOrderRiskDuty.setRiskAmount(new BigDecimal("0.00"));
        smOrderRiskDuty.setRiskPremium(new BigDecimal("0.00"));
        final List<SmOrderRiskDuty> smOrderRiskDuties = Arrays.asList(smOrderRiskDuty);
        final SmOrderRiskDuty record = new SmOrderRiskDuty();
        record.setId(0);
        record.setFhOrderId("fhOrderId");
        record.setInsuredIdNumber("idNumber");
        record.setRiskName("cvgItemName");
        record.setRiskId(0L);
        record.setRiskCode("riskCode");
        record.setDutyName("cvgItemName");
        record.setDutyId("dutyId");
        record.setDutyCode("dutyCode");
        record.setPremium(new BigDecimal("0.00"));
        record.setAmount(new BigDecimal("0.00"));
        record.setRiskAmount(new BigDecimal("0.00"));
        record.setRiskPremium(new BigDecimal("0.00"));
        when(mockOrderRiskDutyMapper.select(record)).thenReturn(smOrderRiskDuties);

        // Run the test
        final List<OrderGuaranteeDutyInfoVO.OrderGuaranteeRiskInfo> result = orderGuaranteeInfoServiceUnderTest.initNonCarOrderInfo(orderVo, "idNumber");

        // Verify the results
    }

    @Test
    public void testConvertToOrderGuaranteeDutyInfoVO() {
        // Setup
        final SmOrderRiskDuty item = new SmOrderRiskDuty();
        item.setId(0);
        item.setFhOrderId("fhOrderId");
        item.setInsuredIdNumber("idNumber");
        item.setRiskName("cvgItemName");
        item.setRiskId(0L);
        item.setRiskCode("riskCode");
        item.setDutyName("cvgItemName");
        item.setDutyId("dutyId");
        item.setDutyCode("dutyCode");
        item.setPremium(new BigDecimal("0.00"));
        item.setAmount(new BigDecimal("0.00"));
        item.setRiskAmount(new BigDecimal("0.00"));
        item.setRiskPremium(new BigDecimal("0.00"));

        final OrderGuaranteeDutyInfoVO expectedResult = new OrderGuaranteeDutyInfoVO();
        expectedResult.setRiskName("cvgItemName");
        expectedResult.setRiskCode("riskCode");
        expectedResult.setRiskId(0L);
        expectedResult.setRiskAmount(new BigDecimal("0.00"));
        expectedResult.setDutyName("cvgItemName");
        expectedResult.setDutyId("dutyId");
        expectedResult.setDutyCode("dutyCode");
        expectedResult.setDutyAmount(new BigDecimal("0.00"));
        expectedResult.setIncludedTotalInsuredAmount(0);
        expectedResult.setDutyPremium(new BigDecimal("0.00"));
        expectedResult.setProductCreateType("name");
        expectedResult.setApiType(0);
        expectedResult.setRiskPremium(new BigDecimal("0.00"));

        // Run the test
        final OrderGuaranteeDutyInfoVO result = OrderGuaranteeInfoService.convertToOrderGuaranteeDutyInfoVO(item);

        // Verify the results
    }

    @Test
    public void testFromSmOrderRiskDuty() {
        // Setup
        final SmOrderRiskDuty riskDuty = new SmOrderRiskDuty();
        riskDuty.setId(0);
        riskDuty.setFhOrderId("fhOrderId");
        riskDuty.setInsuredIdNumber("idNumber");
        riskDuty.setRiskName("cvgItemName");
        riskDuty.setRiskId(0L);
        riskDuty.setRiskCode("riskCode");
        riskDuty.setDutyName("cvgItemName");
        riskDuty.setDutyId("dutyId");
        riskDuty.setDutyCode("dutyCode");
        riskDuty.setPremium(new BigDecimal("0.00"));
        riskDuty.setAmount(new BigDecimal("0.00"));
        riskDuty.setRiskAmount(new BigDecimal("0.00"));
        riskDuty.setRiskPremium(new BigDecimal("0.00"));

        final Map<String, SmPlanRiskDutyHistory> dutyHistoryMap = new HashMap<>();
        final OrderGuaranteeDutyInfoVO expectedResult = new OrderGuaranteeDutyInfoVO();
        expectedResult.setRiskName("cvgItemName");
        expectedResult.setRiskCode("riskCode");
        expectedResult.setRiskId(0L);
        expectedResult.setRiskAmount(new BigDecimal("0.00"));
        expectedResult.setDutyName("cvgItemName");
        expectedResult.setDutyId("dutyId");
        expectedResult.setDutyCode("dutyCode");
        expectedResult.setDutyAmount(new BigDecimal("0.00"));
        expectedResult.setIncludedTotalInsuredAmount(0);
        expectedResult.setDutyPremium(new BigDecimal("0.00"));
        expectedResult.setProductCreateType("name");
        expectedResult.setApiType(0);
        expectedResult.setRiskPremium(new BigDecimal("0.00"));

        // Run the test
        final OrderGuaranteeDutyInfoVO result = OrderGuaranteeInfoService.fromSmOrderRiskDuty(riskDuty, dutyHistoryMap);

        // Verify the results
    }

    @Test
    public void testIsNemTemplate() {
        assertFalse(OrderGuaranteeInfoService.isNemTemplate("productCreateType"));
    }

    @Test
    public void testIsGroupRiskTemplate() {
        assertFalse(OrderGuaranteeInfoService.isGroupRiskTemplate("productCreateType"));
    }

    @Test
    public void testIsOldRiskTemplate() {
        assertFalse(OrderGuaranteeInfoService.isOldRiskTemplate("productCreateType"));
    }
}
