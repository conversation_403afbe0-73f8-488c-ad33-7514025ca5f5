package com.cfpamf.ms.insur.admin.test;

import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.model.CannedAccessControlList;
import com.aliyun.oss.model.ObjectMetadata;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;
import org.apache.commons.io.IOUtils;

import java.io.*;

/**
 * 生成一批可访问的地址
 * <AUTHOR> 2020/10/29 11:10
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class UnzipPolicyTest {
    public static void main(String[] args) throws Exception {

//        File file = new File("/Users/<USER>/Downloads/巴林右旗蓝精灵幼儿园分园");
//
//        OSS oss = new OSSClientBuilder().build("https://oss-cn-beijing.aliyuncs.com",
//                "LTAI4FeQAMY7LFxtNFEPZQax",
//                "******************************");
//
//        String bucket = "safesfiles";
//        //
//        File[] files = file.listFiles();
//        for (File file1 : files) {
//
//            String name = file1.getName();
//            ByteArrayOutputStream bys = new ByteArrayOutputStream(8096);
//            ZipInputStream zis = new ZipInputStream(new FileInputStream(file1));
//            ZipEntry ze = null;
//            if (((ze = zis.getNextEntry()) != null) && !ze.isDirectory()) {
//                ByteStreams.copy(zis, bys);
//            }
//            String key = "offline/group/20201029/T3030/" + name + ".pdf";
//            //创建上传Object的Metadata
//            ObjectMetadata metadata = new ObjectMetadata();
//            metadata.setCacheControl("public");
//            metadata.setHeader("Pragma", "cache");
//            metadata.setContentEncoding("utf-8");
//            metadata.setObjectAcl(CannedAccessControlList.PublicRead);
//            oss.putObject(bucket, key, new ByteArrayInputStream(bys.toByteArray()), metadata);
//            System.err.println("https://safesfiles.oss-cn-beijing.aliyuncs.com/" + key);
//        }
        // /Users/<USER>/Downloads
        System.err.println(uploadLocalFile("/Users/<USER>/Downloads/尊享e生2020版（年缴版）费率.pdf"));
        System.err.println(uploadLocalFile("/Users/<USER>/Downloads/重要提示.pdf"));
        System.err.println(uploadLocalFile("/Users/<USER>/Downloads/个人信息保护政策.pdf"));
    }

    public static String uploadLocalFile(String fileUrl) throws IOException {
        File file = new File(fileUrl);
        OSS oss = new OSSClientBuilder().build("https://oss-cn-beijing.aliyuncs.com",
                "LTAI4FeQAMY7LFxtNFEPZQax",
                "******************************");

        String bucket = "safesfiles";
        //
        String name = file.getName();
        byte[] bytes = IOUtils.toByteArray(new FileInputStream(file));
        String key = "offline/static/20201111/" + name;
        //创建上传Object的Metadata
        ObjectMetadata metadata = new ObjectMetadata();
        metadata.setCacheControl("public");
        metadata.setHeader("Pragma", "cache");
        metadata.setContentEncoding("utf-8");
        metadata.setObjectAcl(CannedAccessControlList.PublicRead);
        oss.putObject(bucket, key, new ByteArrayInputStream(bytes), metadata);
        return "https://safesfiles.oss-cn-beijing.aliyuncs.com/" + key;
    }
}

