package com.cfpamf.ms.insur.admin.channel;

import com.alibaba.fastjson.JSON;
import com.cfpamf.cmis.common.utils.DateUtils;
import com.cfpamf.common.ms.util.DateUtil;
import com.cfpamf.ms.insur.admin.external.fx.util.HttpClientUtil;
import com.cfpamf.ms.insur.admin.pojo.query.xinmei.Signature;
import com.cfpamf.ms.insur.admin.pojo.query.xinmei.XmQuery;
import com.cfpamf.ms.insur.admin.pojo.query.xinmei.backvisit.BackVisitHeader;
import com.cfpamf.ms.insur.admin.pojo.query.xinmei.backvisit.BackVisitQueryBody;
import com.cfpamf.ms.insur.admin.pojo.query.xinmei.surrender.QueryBody;
import com.cfpamf.ms.insur.admin.pojo.query.xinmei.surrender.QueryHead;
import com.cfpamf.ms.insur.weixin.common.IdGenerator;
import com.cfpamf.ms.insur.weixin.util.xinmei.RSAUtils;
import org.junit.Test;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class XinmeiMain {

    private String privateKey = "MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCLO+rr63woev83D1VW8c5zoA+juzsKmUR6k9p6eH6FTaI6RPRMezMX4+g0sJNM1LqB5xTRVsZ3jm2XXhEqFpRRboymhNCb1IPzd+Cvok1jovKCJf7qw28raA1/VdjcR+w7i357iboei2IIwknVm3GzgfsmlbtN5YpzNR43d1B0Fx9MP1Q/aPuL0SAA2HFlgv0J3q9He3FmKhAjTbNJbtYqTxF9rvSjzwCy6LqmiDJkD/v8IirXbGmfwTSId74J+15cpmKE5Wk3NoZ7nSvE45jDDSENFpcuuTZhOBAOb+NZzZK2PLI3dO/B03N0ZuV69y/VQZbzda8GqUmSrQFtQUiZAgMBAAECggEAYdVuUuuayE70udmaW4d5S6Gym5fZ+q6LgZ4WrGuyazSLw2nkR0FiO203Ip7YQepQSyr2Y8oXlraLpwrAANTwzu9hATjd5EFFnnQjewYOXu6GfJD6ODevC88HfOvzf4VCJuzzqRazovbJoeHzQ5cAI+Q3d3tKq4eY6y7t1oeODqOPSVUM6eDUD/71HP9+nfkGiETJt8gSPG8v0MnD4Ni2LnViPGP2MlPHoobXL03WDdC93jyq8rwZXUl7cUfLfkePzS71O53N+D4lpqjJDjgBaCgA7ZdRZYOWAUZfgvA5FgQC0vjfHZQZKuUVJxjQmt8fATL6/1ZG/kTd2emjBD0hyQKBgQDAfXGilYEsnB3NCcbNJEWLA1YrJ9PEjnLQXhg+I9Ag9NpRhSfOJyjFIK6Tzm/ouP11pagvu09WbUKpCtqkdCZHX9XdWouwgh7AF9MXorZc28jAueZgd7xpQNfuxzrbmrvM7kRVwqO7Z8vG4ZGrPgr1kesrkYbF7EUBWTWWOcSXWwKBgQC5LD1/rnoA/I8J6vUolqGE1KB0OKObEpzoiTG9iP4TSSkCAQKYF7S7MnCfRCMnaiArMfure8f5frpm6w5Ko5dN/zvupzQqgOFlTMWfLItRzj4zFi46qxXdXZdOVWJv/Z17KRGCqA4P7CVcHsTGJFSbeug8S2lYD6nN85+03GKWGwKBgQCbL4dBsI6nzGUqPMuN78QxQsthl4ZfXQirfHDHCFBvgXfRaSyiQ2fXJXYZq8EFOr787kUd1kEsSIIJjrqfAxUpTdY50xnzf7QS4iJJV+D2FCtRVY6qD15S7PgphW2qRqFJon5tNbKTECd1e73Y9PgY6z9RrM2fG9C2OZm+N2yLKQKBgB3Aj/kN7ahu7ASp7c4htb4D8+v4lcD60p9CO+26LyimHqKlyubpGC7jwdi1N/jDQnvuQgOhldXumMCkj91KP6tWkSidUPJrVsvR9AbJP3Fbc/79V1wx1QeGXCNH8NMRqtX5GUkm+2tvg+kzQGadu8Jnkxzs+PYfPidLN6P1rRl1AoGAGCzpmgPQazBEXldrT9RYdkEAe269Ua4m7sO8IldApEy+g7qd7421lad4LlyTcvkxvTI5Cf+K4CmqkSB8mSczDuiArCOoKAlTf32r61LFr2kFh3G3d4YAGu/1pcXEpxBClEluhXX1fNDm1x64114ebFdqwP0vXqPdWEYfu4zA5Qs=";

    @Test
    public void queryBackVisit() throws Exception {
        XmQuery query = mockBackVisitQuery();
        String url = "https://uat-www.trustlife.com/gw/webapi/xiaowhale/policyvisit";
        String response = HttpClientUtil.postJsonData(url, JSON.toJSONString(query));
        System.out.println(response);
    }

    @Test
    public void querySurrender() throws Exception {
        XmQuery query = mockSurrenderQuery();
        String url = "https://uat-www.trustlife.com/gw/webapi/xiaowhale/querysurrender";
        String response = HttpClientUtil.postJsonData(url, JSON.toJSONString(query));
        System.out.println(response);
    }


    XmQuery<BackVisitHeader, BackVisitQueryBody> mockBackVisitQuery() throws Exception {
        XmQuery<BackVisitHeader, BackVisitQueryBody> query = new XmQuery<>();
        BackVisitHeader header = new BackVisitHeader();
        header.setClientRequestId(IdGenerator.getUuid());
        header.setClientRequestTime(DateUtils.getNowTime());
        header.setBusinessChannel("410");
        query.setReqHead(header);

        BackVisitQueryBody body = new BackVisitQueryBody();
        List<String> list = new ArrayList<>();
        list.add("1211352829034388");
        body.setCertifyCode(list);
        query.setReqBody(body);

        Signature signature = new Signature();
        String data = JSON.toJSONString(body);
        String sign = RSAUtils.sign(data, privateKey);
        signature.setSign(sign);
        query.setSignature(signature);

        return query;
    }

    XmQuery<QueryHead, QueryBody> mockSurrenderQuery() throws Exception {
        XmQuery<QueryHead, QueryBody> query = new XmQuery<>();
        QueryHead header = new QueryHead();
        query.setReqHead(header);

        QueryBody body = new QueryBody();
        body.setChannelOrgId("410");
        body.setFinishTimeEnd("2022-04-28");
        body.setFinishTimeStart("2022-04-27");
        query.setReqBody(body);

        Signature signature = new Signature();
        String data = JSON.toJSONString(body);
        String sign = RSAUtils.sign(data, privateKey);
        signature.setSign(sign);
        query.setSignature(signature);

        return query;
    }


}
