package com.cfpamf.ms.insur.admin.event.handler;

import com.cfpamf.ms.insur.admin.event.OrderPolicyUrlChangeEvent;
import com.cfpamf.ms.insur.admin.pojo.po.SmPolicyCache;
import com.cfpamf.ms.insur.admin.service.BaseTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.Arrays;
import java.util.concurrent.CopyOnWriteArraySet;

/**
 * <AUTHOR> 2020/3/25 13:50
 */
@Slf4j
@RunWith(SpringJUnit4ClassRunner.class)
public class OrderPolicyUrlChangeHandlerTest extends BaseTest {
    @InjectMocks
    OrderPolicyUrlChangeHandler orderPolicyUrlChangeHandler;
    @Mock
    com.cfpamf.ms.insur.admin.dao.safes.SmPolicyCacheMapper policyCacheMapper;
    @Mock
    com.cfpamf.ms.insur.admin.dao.safes.SmOrderMapper orderMapper;
    @Mock
    com.aliyun.oss.OSS ossClient;

    @Before
    public void setUp() throws Exception {
    }

    @Test
    public void testSort() {
        CopyOnWriteArraySet<String> set = new CopyOnWriteArraySet<>();
        set.addAll(Arrays.asList("3", "2", "1"));
        Assert.assertEquals("3", set.iterator().next());
    }
    @Test
    public void handlerEmpty() {
        OrderPolicyUrlChangeEvent mock = new OrderPolicyUrlChangeEvent("test", "test", null);
        orderPolicyUrlChangeHandler.handler(mock);
    }

    @Test
    public void handlerExists() {

        String surl = "http://www.baidu.com";
        SmPolicyCache smPolicyCache = new SmPolicyCache();
        smPolicyCache.setSourceUrl(surl);
        Mockito.when(policyCacheMapper.selectOne(Mockito.any()))
                .thenReturn(smPolicyCache);
        OrderPolicyUrlChangeEvent mock = new OrderPolicyUrlChangeEvent("test", "test", surl);
        orderPolicyUrlChangeHandler.handler(mock);
    }

    @Test
    public void handlerNormal() {
        OrderPolicyUrlChangeEvent mock = new OrderPolicyUrlChangeEvent("test", "test", "http://61.138.246.87:6001/facade/print/epolicy?polno=01202015020800204100000025&sign=FEBE1AC73C29CAC205408EAC261CA659]]></EURL>\" target=\"_blank\">http://61.138.246.87:6001/facade/print/epolicy?polno=01202015020800204100000025&amp;sign=FEBE1AC73C29CAC205408EAC261CA659");

        orderPolicyUrlChangeHandler.handler(mock);
    }

    @Test
    public void setEnv() {
        orderPolicyUrlChangeHandler.setEnv(",CAECb,");
    }
}
