package com.cfpamf.ms.insur.admin.job;

import com.cfpamf.common.ms.util.DateUtil;
import com.cfpamf.ms.insur.admin.pojo.dto.order.OrderWaitRenewalDTO;
import com.cfpamf.ms.insur.admin.service.BaseTest;
import com.cfpamf.ms.insur.admin.service.RenewalManagerService;
import com.cfpamf.ms.insur.base.util.HttpRequestUtil;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.List;

@RunWith(PowerMockRunner.class)
@PrepareForTest({HttpRequestUtil.class})
public class SmOrderRenewalJobHandlerTest extends BaseTest {

    @InjectMocks
    SmOrderRenewalJobHandler smOrderRenewalJobHandler;
    @Mock
    RenewalManagerService renewalManagerService;
    @Mock
    OrderWaitRenewalDTO orderWaitRenewalDTO;
    @Before
    public void setUp() throws Exception {
        orderWaitRenewalDTO = new OrderWaitRenewalDTO();
        orderWaitRenewalDTO.setApplicationName("张三");
        orderWaitRenewalDTO.setApplicationMobile("13888888888");
        orderWaitRenewalDTO.setCurrCustomerAdminId("CNBJ0409");
        orderWaitRenewalDTO.setCurrCustomerAdminName("张健");
        orderWaitRenewalDTO.setCurrCustomerAdminMobile("13873106340");
        orderWaitRenewalDTO.setShortUrl("http://n.zhnx/dads");
        orderWaitRenewalDTO.setProductName("众安百万医疗");
        orderWaitRenewalDTO.setPlanName("家庭计划");
        orderWaitRenewalDTO.setGrace(12);
        orderWaitRenewalDTO.setEndTime(DateUtil.parseDate("2021-10-11 09:00:00"));
        orderWaitRenewalDTO.setRenewalEndDate(DateUtil.parseDate("2021-10-23 09:00:00"));
    }

    @After
    public void tearDown() throws Exception {
    }

    @Test
    public void execute() {
        List<OrderWaitRenewalDTO> list = new ArrayList<>();
        list.add(orderWaitRenewalDTO);
        Mockito.when(renewalManagerService.listSmsNotifyWaitRenewalOrderByPage(Mockito.anyByte())).thenReturn(list);
        Mockito.doNothing().when(renewalManagerService).sendWaitRenewalSms(Mockito.any());
        smOrderRenewalJobHandler.execute();
    }
}