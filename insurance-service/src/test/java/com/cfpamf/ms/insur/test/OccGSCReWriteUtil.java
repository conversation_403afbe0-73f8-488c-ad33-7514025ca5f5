package com.cfpamf.ms.insur.test;

import com.cfpamf.ms.insur.base.util.ExcelReadUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR> 2020/3/17 15:00
 */
public class OccGSCReWriteUtil {

    public static void main(String[] args) throws IOException {

        String o1 = null;

        String o2 = null;

        String o3 = null;
        String o3Code = null;

        String type;
        File file = new File("/Users/<USER>/doc/zhnx/保险/thrid/国寿财/职业编码.xlsx");
        Workbook workbook = new XSSFWorkbook(new FileInputStream(file));
        HSSFWorkbook sheets = new HSSFWorkbook();
        HSSFSheet sheet = sheets.createSheet();
        Sheet sheetAt = workbook.getSheetAt(0);
        int lastRowNum = sheetAt.getLastRowNum();
        int addIndex = 0;
        for (int i = 1; i < lastRowNum; i++) {
            Row row = sheetAt.getRow(i);
            if (row == null) {
                continue;
            }
            String tmpo1 = ExcelReadUtil.getCellValue(row.getCell(0));
            String tmpo2 = ExcelReadUtil.getCellValue(row.getCell(1));
            String tmpo3 = ExcelReadUtil.getCellValue(row.getCell(2));
            type = ExcelReadUtil.getCellValue(row.getCell(3));

            if (StringUtils.isBlank(tmpo3)) {
                continue;
            }
            if (StringUtils.isNotBlank(tmpo1)) {
                o1 = tmpo1;
            }
            if (StringUtils.isNotBlank(tmpo2)) {
                o2 = tmpo2;
            }
            if (StringUtils.endsWith(type, ".0")) {
                type = type.substring(0, type.length() - 2);
            }
            if (StringUtils.isNotBlank(tmpo3)) {
                String[] s = tmpo3.trim().split(" ");
                List<String> collect = Stream.of(s).filter(StringUtils::isNotBlank).collect(Collectors.toList());
                o3Code = collect.get(0);
                o3 = collect.get(1);
            }
            List<String> list = split2List(o1);
            List<String> list1 = split2List(o2);
            HSSFRow row1 = sheet.createRow(addIndex++);
            row1.createCell(0).setCellValue(list.get(0).trim());
            row1.createCell(1).setCellValue(list.get(1).trim());
            row1.createCell(2).setCellValue(list1.get(0).trim());
            row1.createCell(3).setCellValue(list1.get(1).trim());
            row1.createCell(4).setCellValue(o3Code);
            row1.createCell(5).setCellValue(o3);
            row1.createCell(6).setCellValue(type);
            System.err.println(String.format("%s\t%s\t%s:%s %s", o1, o2, o3, o3Code, type));
        }
        sheets.write(new File("gsc-occ.xls"));
    }

    public static List<String> split2List(String str) {
        String[] s = str.trim().split(" ");
        return Stream.of(s).filter(StringUtils::isNotBlank).collect(Collectors.toList());
    }
}
