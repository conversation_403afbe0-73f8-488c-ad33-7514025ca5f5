package com.cfpamf.ms.insur;

import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @Date 2022/5/6 17:08
 * @Version 1.0
 */
public class TestZaCliamCodeGenerator {
    public static void main(String[] args) {

        String s = "1,0,,0,0,,0-,,,,,,-1,1,1,1,1,1,1-,,,,,,1-,,,,,,1-,1,,,,,-,,1,,,,-,,1,,,,-,,1,,,,-,,,1,1,1,1-,,,1,,,1-1,1,0,,1,1,1-1,1,0,,0,,0-,,,0,,,-,,,,1,1,1";
        StringBuilder builder = new StringBuilder();
        builder.append("new Integer[][]{");
        int width = 7;
        Arrays.stream(s.split("-")).forEach(
                item -> {
                    builder.append("{");
                    String[] split = item.split(",", -1);
                    if (split.length == 0) {
                        split = new String[width];
                        for (int m = 0; m < width; m++) {
                            split[m] = "";
                        }
                    }
                    for (int i = 0; i < split.length; i++) {
                        String item1 = split[i];
                        if (StringUtils.isEmpty(item1)) {
                            builder.append("null");
                        } else {
                            builder.append(item1);
                        }
                        if (!(i == split.length - 1)) {
                            builder.append(",");
                        }
                    }
                    builder.append("},");
                }
        );
        builder.append("}");
        System.out.printf(builder.toString());
    }
}
