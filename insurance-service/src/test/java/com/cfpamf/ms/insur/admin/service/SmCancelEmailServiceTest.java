package com.cfpamf.ms.insur.admin.service;

import com.cfpamf.ms.insur.admin.config.CancelEmailProperties;
import com.cfpamf.ms.insur.admin.pojo.vo.cancel.CancelOrderDetail;
import com.cfpamf.ms.insur.admin.pojo.vo.cancel.SmCancelFileGroupVO;
import com.cfpamf.ms.insur.base.util.AliYunOssUtil;
import com.cfpamf.ms.insur.base.util.HttpRequestUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.github.jsonzou.jmockdata.JMockData;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
//import org.mockito.cglib.beans.BeanMap;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.cglib.beans.BeanMap;

import javax.mail.internet.MimeMessage;
import java.lang.reflect.Field;
import java.util.Collections;

/**
 * <AUTHOR> 2020/3/11 09:51
 */
@Slf4j
@RunWith(PowerMockRunner.class)
@PrepareForTest({HttpRequestUtil.class, AliYunOssUtil.class})
public class SmCancelEmailServiceTest extends BaseTest {
    @InjectMocks
    SmCancelEmailService smCancelEmailService;
    @Mock
    org.springframework.mail.javamail.JavaMailSender javaMailSender;
    @Mock
    com.cfpamf.ms.insur.admin.config.CancelEmailProperties properties;
    @Mock
    com.cfpamf.ms.insur.admin.service.SmCancelService cancelService;
    @Mock
    com.fasterxml.jackson.databind.ObjectMapper objectMapper;
    @Mock
    com.aliyun.oss.OSS OSS;

    @Before
    public void setUp() throws Exception {
        super.setUp();
        PowerMockito.mockStatic(AliYunOssUtil.class);

        Mockito.when(javaMailSender.createMimeMessage())
                .thenReturn(Mockito.mock(MimeMessage.class));

        BeanMap beanMap = BeanMap.create(smCancelEmailService);
        beanMap.put("mainFrom", "<EMAIL>");
        beanMap.put("cc", "<EMAIL>");

    }

    @Test
    public void getEmailTemplate() throws Exception {
        Field f = SmCancelEmailService.class
                .getDeclaredField("objectMapper");
        f.setAccessible(true);
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.registerModule(new JavaTimeModule());
        f.set(smCancelEmailService, objectMapper);

        CancelEmailProperties.EmailProperties mock1 = JMockData.mock(CancelEmailProperties.EmailProperties.class);
        Mockito.when(properties.getEmail())
                .thenReturn(Collections.singletonMap("c4002", mock1));

        CancelOrderDetail mock = JMockData.mock(CancelOrderDetail.class, con());
        mock.setBankAreaName("12,32");
        Mockito.when(cancelService.getCancelOrderDetail(Mockito.anyInt()))
                .thenReturn(mock);
        mock.setCompanyName("泰康在线财产保险股份有限公司");
        mock.setChannel("fh");
        smCancelEmailService.getEmailTemplate(16541, 1);
    }

    @Test
    public void sendEmail() {


        try {
            Field mainFrom = SmCancelEmailService.class
                    .getDeclaredField("mainFrom");
            mainFrom.setAccessible(true);
            mainFrom.set(smCancelEmailService, "<EMAIL>");

            Field cc = SmCancelEmailService.class
                    .getDeclaredField("cc");
            cc.setAccessible(true);
            cc.set(smCancelEmailService, "<EMAIL>");
            SmCancelFileGroupVO mock = JMockData.mock(SmCancelFileGroupVO.class);
            smCancelEmailService.sendEmail(JMockData.mock(com.cfpamf.ms.insur.admin.pojo.vo.cancel.CancelEmailTemplateVO.class),
                    Collections.singletonList(mock));
        } catch (Exception e) {
            e.printStackTrace();
        }

    }
}
