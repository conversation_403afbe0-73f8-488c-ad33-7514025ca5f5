package com.cfpamf.ms.insur.base.util.email;

import com.google.common.collect.Maps;
import org.apache.commons.lang3.reflect.FieldUtils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.mail.javamail.JavaMailSender;

import javax.mail.internet.MimeMessage;
import java.util.HashMap;

/**
 * <AUTHOR>
 * @date 2021/9/8 20:43
 */
@RunWith(PowerMockRunner.class)
public class JavaMailHelperTest {
    @InjectMocks
    JavaMailHelper javaMailHelper;

    /**
     * 发送邮件的账户
     */
    @Mock
    String mainFrom;


    @Mock
    JavaMailSender javaMailSender;

    @Before
    public void setMainFrom() throws IllegalAccessException {
        FieldUtils.writeField(FieldUtils.getDeclaredField(JavaMailHelper.class, "mainFrom", true), javaMailHelper, "<EMAIL>");
    }

    @Test
    public void testSendMail() throws Exception {
        ByteArrayResource byteArrayResource = new ByteArrayResource(new byte[]{1, 0});
        HashMap<String, ByteArrayResource> testMap = Maps.newHashMap();
        testMap.put("testFileName", byteArrayResource);
        Mockito.when(javaMailSender.createMimeMessage()).thenReturn(Mockito.mock(MimeMessage.class));
        javaMailHelper.sendMail("123", new String[]{"<EMAIL>"}, "testTXT", null, testMap);
    }
}
