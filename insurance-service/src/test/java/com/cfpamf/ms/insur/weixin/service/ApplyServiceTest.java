package com.cfpamf.ms.insur.weixin.service;

import com.cfpamf.ms.insur.admin.constant.SmConstants;
import com.cfpamf.ms.insur.admin.dao.safes.order.SmOrderItemMapper;
import com.cfpamf.ms.insur.admin.pojo.po.order.SmOrderItem;
import com.cfpamf.ms.insur.base.util.HttpRequestUtil;
import com.cfpamf.ms.insur.weixin.pojo.vo.apply.common.GroupInsured;
import com.cfpamf.ms.insur.weixin.pojo.vo.apply.zhongan.order.ProductReq;
import com.cfpamf.ms.insur.weixin.pojo.vo.apply.zhongan.request.ZaQuoteResp;
import lombok.extern.slf4j.Slf4j;
import org.junit.FixMethodOrder;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.MethodSorters;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.context.web.WebAppConfiguration;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

//@RunWith(SpringRunner.class)
//@SpringBootTest
//@ActiveProfiles("dev")
//@WebAppConfiguration
public class ApplyServiceTest {
//    @Autowired
    SmOrderItemMapper orderItemMapper;

//    @Test
    public void saveOrderItem(){
            List<SmOrderItem> orderItems = new ArrayList<>();
            SmOrderItem item = new SmOrderItem();
            item.setAppStatus(SmConstants.ORDER_STATUS_TO_PAY);
            item.setIdNumber("123");
            item.setIdType("23");
            item.setType(0);
            item.setProductId(451);
            item.setFhOrderId("AbcTest");
            item.setQty(1);
            item.setPlanCode("afsd");

                BigDecimal premium = new BigDecimal("200");
                item.setTotalAmount(premium);
                item.setUnitPrice(premium);
            item.setPlanId(861);
            orderItems.add(item);
        orderItemMapper.insertList(orderItems);
    }
}
