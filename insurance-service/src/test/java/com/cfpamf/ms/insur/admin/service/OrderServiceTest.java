package com.cfpamf.ms.insur.admin.service;

import org.junit.FixMethodOrder;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.MethodSorters;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

/*@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("dev")
@FixMethodOrder(MethodSorters.NAME_ASCENDING)*/
public class OrderServiceTest {

   /* @Autowired
    private SmOrderCoreService service;

    @Test
    public void updateOrderExpiredInfo() {
        service.updateExpireOrderInfo("18173992lwuANr01");
    }*/
}