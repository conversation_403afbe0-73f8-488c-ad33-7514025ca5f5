package com.cfpamf.ms.insur.admin.service;

import com.cfpamf.ms.insur.admin.dao.safes.SmUpChannelScalePrizeMapper;
import com.cfpamf.ms.insur.admin.dao.safes.SmUpChannelScalePrizePlanMapper;
import com.cfpamf.ms.insur.admin.dao.safes.SmUpChannelScalePrizeProductMapper;
import com.cfpamf.ms.insur.admin.enums.EnumPlanState;
import com.cfpamf.ms.insur.admin.pojo.dto.UpChannelScalePrizeDTO;
import com.cfpamf.ms.insur.admin.pojo.po.SmUpChannelScalePrize;
import com.cfpamf.ms.insur.admin.pojo.po.SmUpChannelScalePrizePlan;
import com.cfpamf.ms.insur.admin.pojo.query.UpChannelScalePrizeQuery;
import com.cfpamf.ms.insur.base.service.DLockTemplate;
import com.cfpamf.ms.insur.base.util.HttpRequestUtil;
import com.cfpamf.ms.insur.base.util.RedisUtil;
import com.github.jsonzou.jmockdata.JMockData;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * <AUTHOR>
 * @Date 2021/12/3 10:09
 * @Version 1.0
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({HttpRequestUtil.class})
public class SmUpChannelScalePrizeServiceTest extends BaseTest {

    @Mock
    private SmUpChannelScalePrizeMapper upChannelScalePrizeMapper;
    @Mock
    private SmUpChannelScalePrizePlanMapper upChannelScalePrizePlanMapper;
    @Mock
    private SmUpChannelScalePrizeProductMapper upChannelScalePrizeProductMapper;
    @Mock
    private DLockTemplate lockTemplate;
    @Mock
    private RedisUtil<String, Long> redisUtil;
    @Mock
    private DictionaryService dictionaryService;

    @InjectMocks
    private SmUpChannelScalePrizeService upChannelScalePrizeService;

    @Test
    public void testAddChannelScalePrize() {
        UpChannelScalePrizeDTO mockParams = JMockData.mock(UpChannelScalePrizeDTO.class, con());
        mockParams.setStartTime(LocalDateTime.now());
        mockParams.setEndTime(LocalDateTime.now().plusDays(1));
        upChannelScalePrizeService.addChannelScalePrize(mockParams);
    }

    @Test
    public void testUpdateChannelScalePrize() {
        UpChannelScalePrizeDTO mockParams = JMockData.mock(UpChannelScalePrizeDTO.class, con());
        mockParams.setStartTime(LocalDateTime.now());
        mockParams.setEndTime(LocalDateTime.now().plusDays(1));

        SmUpChannelScalePrize mockObj = JMockData.mock(SmUpChannelScalePrize.class, con());
        mockObj.setPlanState(EnumPlanState.UNPUBLISHED.getCode());
        Mockito.when(upChannelScalePrizeMapper.selectByPrimaryKey(Mockito.any())).thenReturn(mockObj);
        upChannelScalePrizeService.updateChannelScalePrize(mockParams);
    }

    @Test
    public void testPage() {
        upChannelScalePrizeService.page(JMockData.mock(UpChannelScalePrizeQuery.class, con()));
    }

    @Test
    public void testEditStateFlow() {
        List<EnumPlanState> params = new ArrayList<>();
        params.add(EnumPlanState.PAUSED);
        params.add(EnumPlanState.UNPUBLISHED);
        for (EnumPlanState param : params) {
            for (EnumPlanState enumPlanState : EnumPlanState.values()) {
                if (Arrays.asList(enumPlanState.getNextPlanState()).contains(param)) {
                    SmUpChannelScalePrize mockObj = JMockData.mock(SmUpChannelScalePrize.class, con());
                    mockObj.setPlanState(enumPlanState.getCode());
                    Mockito.when(upChannelScalePrizeMapper.selectByPrimaryKey(Mockito.any())).thenReturn(mockObj);
                    upChannelScalePrizeService.editStateFlow(1, param.getCode());
                }
            }
        }
    }

    @Test
    public void testUpdateDeleteState() {
        SmUpChannelScalePrize mockObj = JMockData.mock(SmUpChannelScalePrize.class, con());
        mockObj.setPlanState(EnumPlanState.UNPUBLISHED.getCode());
        Mockito.when(upChannelScalePrizeMapper.selectByPrimaryKey(Mockito.any())).thenReturn(mockObj);
        upChannelScalePrizeService.updateDeleteState(1);
    }

    @Test
    public void testQueryDetailById() {
        upChannelScalePrizeService.queryDetailById(1);
    }
}