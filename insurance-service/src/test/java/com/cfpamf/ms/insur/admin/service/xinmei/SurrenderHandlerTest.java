package com.cfpamf.ms.insur.admin.service.xinmei;

import com.cfpamf.ms.insur.admin.constant.SmConstants;
import com.cfpamf.ms.insur.admin.dao.safes.SmOrderMapper;
import com.cfpamf.ms.insur.admin.dao.safes.order.*;
import com.cfpamf.ms.insur.admin.external.xm.XmApiProperties;
import com.cfpamf.ms.insur.admin.external.xm.client.XmPolicyClient;
import com.cfpamf.ms.insur.admin.job.xinmei.SurrenderHandler;
import com.cfpamf.ms.insur.admin.pojo.dto.SmOrderCorrectCancelDTO;
import com.cfpamf.ms.insur.admin.pojo.po.order.SmOrderPolicy;
import com.cfpamf.ms.insur.admin.pojo.query.xinmei.backvisit.BackVisitResBody;
import com.cfpamf.ms.insur.admin.pojo.query.xinmei.surrender.SurrenderRiskInfo;
import com.cfpamf.ms.insur.admin.pojo.vo.SmBaseOrderVO;
import com.cfpamf.ms.insur.admin.service.BaseTest;
import com.cfpamf.ms.insur.admin.service.SmOrderManageService;
import com.cfpamf.ms.insur.admin.service.order.XmOrderService;
import com.cfpamf.ms.insur.base.event.EventBusEngine;
import com.cfpamf.ms.insur.base.util.HttpRequestUtil;
import com.github.jsonzou.jmockdata.JMockData;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.LocalDateTime;
import java.util.Date;

@RunWith(PowerMockRunner.class)
@PrepareForTest({HttpRequestUtil.class})
public class SurrenderHandlerTest extends BaseTest {

    @Mock
    private SmOrderMapper orderMapper;

    @Mock
    SmOrderDDDMapper orderDDDMapper;

    @Mock
    SmOrderApplicantMapper applicantMapper;

    @Mock
    SmOrderInsuredMapper insuredMapper;

    @Mock
    SmOrderItemMapper smOrderItemMapper;

    @Mock
    EventBusEngine eventBusEngine;

    @InjectMocks
    private SmOrderManageService orderManageService;

    @Mock
    private XmPolicyClient apiClient;

    @Mock
    private XmApiProperties apiProperties;

    @Mock
    private SmOrderPolicyMapper smOrderPolicyMapper;

    @Mock
    private XmOrderService xmOrderService;

    @InjectMocks
    private SurrenderHandler surrenderHandler;

    @Test
    public void mockSurrendere() {
        String policyNo = JMockData.mock(String.class);
        SmOrderCorrectCancelDTO vo = new SmOrderCorrectCancelDTO();
        vo.setIsPart(false);
        vo.setPolicyNo(policyNo);
        vo.setFieldCode("appStatus");
        vo.setOldValue(SmConstants.POLICY_STATUS_SUCCESS);
        vo.setNewValue(SmConstants.POLICY_STATUS_CANCEL_SUCCESS);
        orderManageService.correctOrderAppStatus(vo);
    }

    @Test
    public void handlerOne() {
        SurrenderRiskInfo policy = new SurrenderRiskInfo();
        policy.setPolicyCode("1211352018037688");
        policy.setStatus(3);
        policy.setEndTime("2024-04-01 18:00:00");
        policy.setFinishTime("2024-04-01 18:00:00");
        BackVisitResBody backVisitResBody = JMockData.mock(BackVisitResBody.class);
        surrenderHandler.handlerXmPolicy(policy, backVisitResBody);
    }

    @Test
    public void buildOrderPolicy() {
        SmBaseOrderVO order = JMockData.mock(SmBaseOrderVO.class);


        SurrenderRiskInfo risk = JMockData.mock(SurrenderRiskInfo.class);
        surrenderHandler.buildOrderPolicy(LocalDateTime.now(),order, risk);
    }

    @Test
    public void surrender() {
        SmBaseOrderVO order = JMockData.mock(SmBaseOrderVO.class);
        Date surrenderTime = new Date();
        SurrenderRiskInfo risk = JMockData.mock(SurrenderRiskInfo.class);
        BackVisitResBody backVisitResBody = JMockData.mock(BackVisitResBody.class);
        String orderId = order.getFhOrderId();
        orderMapper.updateOrderSurrender(orderId, surrenderTime, null);
        orderMapper.updateOrderItemStatus(orderId,
                SmConstants.POLICY_STATUS_CANCEL_SUCCESS,
                null);
        surrenderHandler.replaceOrderPolicy(LocalDateTime.now(),order, risk, backVisitResBody);
    }

    @Test
    public void fillBackVisitField() {
        BackVisitResBody body = JMockData.mock(BackVisitResBody.class);
        SmOrderPolicy policy = JMockData.mock(SmOrderPolicy.class);
        surrenderHandler.fillBackVisitField(body, policy);
    }

    @Test
    public void fix2LocalDateTime() {
        String time = JMockData.mock(String.class);
        LocalDateTime rtn = surrenderHandler.fix2LocalDateTime(time);
        Assert.assertTrue(rtn == null);

        time = "2022-05-07 11:02:00";
        rtn = surrenderHandler.fix2LocalDateTime(time);
        Assert.assertTrue(rtn != null);
    }

    @Test
    public void replaceOrderPolicy() {
        SmBaseOrderVO order = JMockData.mock(SmBaseOrderVO.class);
        SurrenderRiskInfo risk = JMockData.mock(SurrenderRiskInfo.class);
        BackVisitResBody backVisitResBody = JMockData.mock(BackVisitResBody.class);
        surrenderHandler.replaceOrderPolicy(LocalDateTime.now(),order, risk, backVisitResBody);
    }
}
