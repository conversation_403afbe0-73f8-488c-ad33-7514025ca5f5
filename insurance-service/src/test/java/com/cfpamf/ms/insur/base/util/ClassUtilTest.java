package com.cfpamf.ms.insur.base.util;

import com.cfpamf.ms.insur.base.util.reflect.ClassUtil;
import lombok.AccessLevel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldDefaults;
import org.junit.Assert;
import org.junit.Test;

import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;

/**
 * <AUTHOR>
 * @Date 2021/10/14 20:28
 * @Version 1.0
 */
public class ClassUtilTest {

    @Test
    public void testSetFieldValue() throws InvocationTargetException, IllegalAccessException, NoSuchMethodException {
        TestClassUtilBean tested = new TestClassUtilBean();
        for (Field field : TestClassUtilBean.class.getDeclaredFields()) {
            if (field.getType().isAssignableFrom(int.class)){
                ClassUtil.setFieldValue(tested, field, 2334);
                ClassUtil.setFieldValue(tested, field, null);
            }
        }
    }

    @Test
    public void testGetDefaultValue() {
        Assert.assertTrue(  (int) ClassUtil.getDefaultValue(int.class) == 0);
        Assert.assertTrue(  (short) ClassUtil.getDefaultValue(short.class) == (short)0);
        Assert.assertTrue(  (byte) ClassUtil.getDefaultValue(byte.class) == (byte)0);
        Assert.assertTrue(  (long) ClassUtil.getDefaultValue(long.class) == 0L);
        Assert.assertTrue(  (float) ClassUtil.getDefaultValue(float.class) == 0F);
        Assert.assertTrue(  (double) ClassUtil.getDefaultValue(double.class) == 0D);
        Assert.assertTrue(  (char) ClassUtil.getDefaultValue(char.class) == (char)0);
        Assert.assertTrue(  !(boolean) ClassUtil.getDefaultValue(boolean.class));
        Assert.assertTrue(  ClassUtil.getDefaultValue(null) == null);
        Assert.assertTrue(  ClassUtil.getDefaultValue(Object.class) == null);
    }


    @Data
    @EqualsAndHashCode
    @FieldDefaults(level = AccessLevel.PRIVATE)
    static public class TestClassUtilBean {
        String strType;
        int priIntType;
        long priLongType;
        short priShortType;
        char priCharType;
        byte priByteType;
        double priDoubleType;
        double priFloatType;
        boolean priBooleanType;
        Object objectType;
    }

}
