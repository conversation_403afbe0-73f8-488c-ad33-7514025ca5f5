package com.cfpamf.ms.insur.admin.test;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cfpamf.common.ms.util.DateUtil;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;
import org.springframework.http.*;
import org.springframework.web.client.RestTemplate;

import java.util.Date;

/**
 * <AUTHOR> 2020/9/14 11:27
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class LookBackMockTest {

    public static void main(String[] args) {
        RestTemplate restTemplate = new RestTemplate();

        String[] str = ("{\"eventName\":\"保险回溯\",\"productVersion\":\"1.0\",\"traceCode\":\"viewProductDetail\",\"ossUrl\":\"https://safesfiles-test.oss-cn-beijing.aliyuncs.com/lookback/ZHNX08073/viewProductDetail20200914090506.jpg\",\"beginTime\":\"2020-09-14 09:05:06\",\"traceName\":\"查看产品详情\",\"userId\":\"ZHNX08073\",\"userMobile\":\"17607676022\",\"wxOpenId\":\"osUxv1j9pCqL1JwUpwoS9pXtKO1g\",\"isShare\":false,\"appId\":6,\"platform\":\"3\",\"os\":\"android\"}\n" +
                "{\"eventName\":\"保险回溯\",\"productVersion\":\"1.0\",\"traceCode\":\"chooseProductPlan\",\"ossUrl\":\"https://safesfiles-test.oss-cn-beijing.aliyuncs.com/lookback/ZHNX08073/chooseProductPlan20200914090518.jpg\",\"beginTime\":\"2020-09-14 09:05:18\",\"traceName\":\"选择产品计划\",\"userId\":\"ZHNX08073\",\"userMobile\":\"17607676022\",\"wxOpenId\":\"osUxv1j9pCqL1JwUpwoS9pXtKO1g\",\"isShare\":false,\"appId\":6,\"platform\":\"3\",\"os\":\"android\"}\n" +
                "{\"eventName\":\"保险回溯\",\"productVersion\":\"1.0\",\"traceCode\":\"viewInsuranceNotice\",\"ossUrl\":\"https://safesfiles-test.oss-cn-beijing.aliyuncs.com/lookback/ZHNX08073/viewInsuranceNotice20200914090532.jpg\",\"beginTime\":\"2020-09-14 09:05:32\",\"traceName\":\"查看投保须知\",\"userId\":\"ZHNX08073\",\"userMobile\":\"17607676022\",\"wxOpenId\":\"osUxv1j9pCqL1JwUpwoS9pXtKO1g\",\"isShare\":false,\"appId\":6,\"platform\":\"3\",\"os\":\"android\"}\n" +
                "{\"eventName\":\"保险回溯\",\"productVersion\":\"1.0\",\"traceCode\":\"viewInsuranceClause\",\"ossUrl\":\"https://safesfiles-test.oss-cn-beijing.aliyuncs.com/lookback/ZHNX08073/viewInsuranceClause20200914090543.jpg\",\"beginTime\":\"2020-09-14 09:05:43\",\"traceName\":\"查看保险条款\",\"userId\":\"ZHNX08073\",\"userMobile\":\"17607676022\",\"wxOpenId\":\"osUxv1j9pCqL1JwUpwoS9pXtKO1g\",\"isShare\":false,\"appId\":6,\"platform\":\"3\",\"os\":\"android\"}\n" +
                "{\"eventName\":\"保险回溯\",\"productVersion\":\"1.0\",\"traceCode\":\"viewCustomerNotice\",\"ossUrl\":\"https://safesfiles-test.oss-cn-beijing.aliyuncs.com/lookback/ZHNX08073/viewCustomerNotice20200914090552.jpg\",\"beginTime\":\"2020-09-14 09:05:52\",\"traceName\":\"查看客户告知书\",\"userId\":\"ZHNX08073\",\"userMobile\":\"17607676022\",\"wxOpenId\":\"osUxv1j9pCqL1JwUpwoS9pXtKO1g\",\"isShare\":false,\"appId\":6,\"platform\":\"3\",\"os\":\"android\"}\n" +
                "{\"eventName\":\"保险回溯\",\"productVersion\":\"1.0\",\"traceCode\":\"viewHealthNotice\",\"ossUrl\":\"https://safesfiles-test.oss-cn-beijing.aliyuncs.com/lookback/ZHNX08073/viewHealthNotice20200914090100.jpg\",\"beginTime\":\"2020-09-14 09:05:59\",\"traceName\":\"查看健康告知\",\"userId\":\"ZHNX08073\",\"userMobile\":\"17607676022\",\"wxOpenId\":\"osUxv1j9pCqL1JwUpwoS9pXtKO1g\",\"isShare\":false,\"appId\":6,\"platform\":\"3\",\"os\":\"android\"}\n" +
                "{\"eventName\":\"保险回溯\",\"productVersion\":\"1.0\",\"traceCode\":\"applyOrderInput\",\"ossUrl\":\"https://safesfiles-test.oss-cn-beijing.aliyuncs.com/lookback/ZHNX08073/applyOrderInput20200914090612.jpg\",\"beginTime\":\"2020-09-14 09:06:12\",\"traceName\":\"投保信息录入\",\"userId\":\"ZHNX08073\",\"userMobile\":\"17607676022\",\"wxOpenId\":\"osUxv1j9pCqL1JwUpwoS9pXtKO1g\",\"isShare\":false,\"appId\":6,\"platform\":\"3\",\"os\":\"android\"}\n" +
                "{\"eventName\":\"保险回溯\",\"productVersion\":\"1.0\",\"traceCode\":\"modifyOrderInput\",\"ossUrl\":\"https://safesfiles-test.oss-cn-beijing.aliyuncs.com/lookback/ZHNX08073/modifyOrderInput20200914090630.jpg\",\"beginTime\":\"2020-09-14 09:06:30\",\"traceName\":\"修改投保信息\",\"orderId\":\"DJ20091400738236\",\"userId\":\"ZHNX08073\",\"userMobile\":\"17607676022\",\"wxOpenId\":\"osUxv1j9pCqL1JwUpwoS9pXtKO1g\",\"isShare\":false,\"appId\":6,\"platform\":\"3\",\"os\":\"android\"}\n" +
                "{\"eventName\":\"保险回溯\",\"productVersion\":\"1.0\",\"traceCode\":\"confirmOrderInput\",\"ossUrl\":\"https://safesfiles-test.oss-cn-beijing.aliyuncs.com/lookback/ZHNX08073/confirmOrderInput20200914090803.jpg\",\"beginTime\":\"2020-09-14 09:08:03\",\"traceName\":\"确认投保信息\",\"orderId\":\"DJ20091400738236\",\"userId\":\"ZHNX08073\",\"userMobile\":\"17607676022\",\"wxOpenId\":\"osUxv1j9pCqL1JwUpwoS9pXtKO1g\",\"isShare\":false,\"appId\":6,\"platform\":\"3\",\"os\":\"android\"}\n" +
                "{\"eventName\":\"保险回溯\",\"productVersion\":\"1.0\",\"traceCode\":\"orderApplySuccess\",\"ossUrl\":\"https://safesfiles-test.oss-cn-beijing.aliyuncs.com/lookback/ZHNX08073/orderApplySuccess20200914090856.jpg\",\"beginTime\":\"2020-09-14 09:08:56\",\"traceName\":\"投保成功\",\"orderId\":\"DJ20091400738236\",\"userId\":\"ZHNX08073\",\"userMobile\":\"17607676022\",\"wxOpenId\":\"osUxv1j9pCqL1JwUpwoS9pXtKO1g\",\"isShare\":false,\"appId\":6,\"platform\":\"3\",\"os\":\"android\"}"
        ).split("\n");


        String[] orders = ("GSC20072100737838\n" +
                "GSC20072100737835\n" +
                "DJ20072100737834\n" +
                "DJ20072100737833\n" +
                "DJ20072100737832\n" +
                "DJ20072100737825\n" +
                "DJ20072100737824\n" +
                "DJ20072100737823\n" +
                "CIC20072100737822\n" +
                "GSC20072000737833\n" +
                "GSC20072000737832\n" +
                "CIC20071600737819\n" +
                "CIC20071600737817\n" +
                "CIC20071600737816\n" +
                "CIC20071600737813\n" +
                "CIC20071600737811\n" +
                "CIC20071600737809\n" +
                "CIC20071600737807\n" +
                "CIC20071600737805\n" +
                "CIC20071600737802\n" +
                "DJ20071500737801\n" +
                "DJ20071400737800\n" +
                "DJ20071400737799\n" +
                "IMCIC20071000737800\n" +
                "IMCIC20071000737801\n" +
                "IMCIC20071000737802\n" +
                "IMCIC20070900737795\n" +
                "IMCIC20070900737794\n" +
                "IMCIC20070900737793\n" +
                "IMCIC20070800737801\n" +
                "IMCIC20070800737799\n" +
                "IMCIC20070800737800\n" +
                "DJ20070800737797\n" +
                "DJ20070800737796\n" +
                "DJ20070800737785\n" +
                "IMCIC20070700737793\n" +
                "IMCIC20070700737792\n" +
                "IMCIC20070700737791\n" +
                "IMCIC20070700737782\n" +
                "IMCIC20070700737781\n" +
                "IMCIC20070700737780\n" +
                "IMCIC20070700737779\n" +
                "IMCIC20070700737778\n" +
                "IMCIC20070700737777\n" +
                "IMCIC20070700737775\n" +
                "IMCIC20070700737774\n" +
                "IMCIC20070700737776\n" +
                "IMCIC20070700737771\n" +
                "IMCIC20070700737773\n" +
                "IMCIC20070700737772\n" +
                "IMCIC20070700737770\n" +
                "IMCIC20070700737768\n" +
                "IMCIC20070700737767\n" +
                "IMCIC20070700737769\n" +
                "IMCIC20070700737764\n" +
                "IMCIC20070700737766\n" +
                "IMCIC20070700737765\n" +
                "IMCIC20070700737763\n" +
                "IMCIC20070700737762\n" +
                "IMCIC20070700737761\n" +
                "IMCIC20070700737760\n" +
                "IMCIC20070700737759\n" +
                "IMCIC20070700737758\n" +
                "IMCIC20070700737757\n" +
                "IMCIC20070700737756\n" +
                "IMCIC20070700737755\n" +
                "IMCIC20070700737753\n" +
                "IMCIC20070700737752\n" +
                "IMCIC20070700737754\n" +
                "IMCIC20070700737751\n" +
                "IMCIC20070700737750\n" +
                "IMCIC20070700737749\n" +
                "IMCIC20070600737754\n" +
                "IMCIC20070600737753\n" +
                "IMCIC20070600737748\n" +
                "IMCIC20070600737747\n" +
                "IMCIC20070600737746\n" +
                "IMCIC20070600737745\n" +
                "IMCIC20070600737743\n" +
                "IMCIC20070600737741\n" +
                "IMCIC20070600737740\n" +
                "IMCIC20070600737739\n" +
                "IMCIC20070600737738\n" +
                "IMCIC20070600737737\n" +
                "IMCIC20070600737736\n" +
                "IMCIC20070600737735\n" +
                "IMCIC20070600737734\n" +
                "IMCIC20070600737732\n" +
                "IMCIC20070600737731\n" +
                "IMCIC20070600737733\n" +
                "IMFH20070600737730\n" +
                "IMFH20070600737729\n" +
                "CIC20070300737754\n" +
                "IMFH20070300737746\n" +
                "IMFH20070300737747\n" +
                "IMFH20070300737748\n" +
                "IMFH20070300737749\n" +
                "IMFH20070300737745\n" +
                "CIC20070300737743\n" +
                "IMFH20070300737740\n" +
                "IMFH20070300737741\n" +
                "IMFH20070300737739\n" +
                "IMFH20070300737742\n" +
                "IMFH20070300737743\n" +
                "IMFH20070300737735\n" +
                "IMFH20070300737736\n" +
                "IMFH20070300737737\n" +
                "IMFH20070300737738\n" +
                "IMFH20070300737734\n" +
                "IMFH20070300737728\n" +
                "IMCIC20070300737726\n" +
                "IMCIC20070300737725\n" +
                "IMCIC20070300737727\n" +
                "IMCIC20070300737718\n" +
                "IMCIC20070300737717\n" +
                "IMCIC20070300737716\n" +
                "IMFH20070300737714\n" +
                "IMFH20070300737710\n" +
                "IMCIC20070300737708\n" +
                "IMCIC20070300737707\n" +
                "IMCIC20070300737709\n" +
                "IMCIC20070300737700\n" +
                "IMCIC20070300737702\n" +
                "IMCIC20070300737701\n" +
                "IMCIC20070300737696\n" +
                "IMCIC20070300737695\n" +
                "IMCIC20070300737694\n" +
                "IMCIC20070300737693\n" +
                "IMCIC20070300737689\n" +
                "IMCIC20070300737690\n" +
                "IMFH20070300737688\n" +
                "IMCIC20070300737685\n" +
                "IMCIC20070300737686\n" +
                "IMCIC20070300737683\n" +
                "IMCIC20070300737682\n" +
                "CIC20070300737680\n" +
                "CIC20070300737679\n" +
                "CIC20070300737678\n" +
                "CIC20070300737677\n" +
                "CIC20070300737676\n" +
                "CIC20070300737675\n" +
                "CIC20070300737674\n" +
                "CIC20070300737673\n" +
                "CIC20070200737720\n" +
                "CIC20070200737719\n" +
                "CIC20070200737718\n" +
                "CIC20070200737717\n" +
                "CIC20070200737716\n" +
                "CIC20070200737715\n" +
                "CIC20070200737714\n" +
                "CIC20070200737713\n" +
                "CIC20070200737712\n" +
                "CIC20070200737711\n" +
                "CIC20070200737710\n" +
                "CIC20070200737709\n" +
                "CIC20070200737707\n" +
                "CIC20070200737706\n" +
                "CIC20070200737705\n" +
                "IMCIC20070200737699\n" +
                "IMCIC20070200737698\n" +
                "IMCIC20070200737701\n" +
                "IMCIC20070200737700\n" +
                "IMCIC20070200737686\n" +
                "IMCIC20070200737688\n" +
                "IMCIC20070200737679\n" +
                "IMCIC20070200737678\n" +
                "IMCIC20070200737669\n" +
                "IMCIC20070200737666\n" +
                "IMCIC20070200737665\n" +
                "IMFH20070200737659\n" +
                "IMCIC20070200737655\n" +
                "IMCIC20070200737649\n" +
                "IMCIC20070200737646\n" +
                "IMCIC20070200737645\n" +
                "IMCIC20070200737648\n" +
                "IMCIC20070200737647\n" +
                "IMCIC20070200737642").split("\n");

        for (int i = 0; i < orders.length; i++) {
            for (int i1 = 0; i1 < str.length; i1++) {
                JSONObject json = JSON.parseObject(str[i1]);
                if (json.containsKey("orderId")) {
                    json.put("orderId", orders[i]);
                }

                HttpHeaders requestHeaders = new HttpHeaders();
                requestHeaders.setContentType(MediaType.APPLICATION_JSON_UTF8);
                HttpEntity<String> requestEntity = new HttpEntity<>(json.toJSONString(), requestHeaders);
                json.fluentPut("beginTime", DateUtil.format(new Date(), DateUtil.CN_LONG_FORMAT))
                        .fluentPut("beginMsec", System.currentTimeMillis());
                ResponseEntity<String> exchange = restTemplate.exchange("https://log-gateway.cdfinance.com.cn/log-trace/user/event",
                        HttpMethod.POST, requestEntity,
                        String.class);
                int statusCodeValue = exchange.getStatusCodeValue();
                if (statusCodeValue >= 200 && statusCodeValue < 300) {

                } else {
                    System.err.println(statusCodeValue);
                }


            }
        }
    }
}
