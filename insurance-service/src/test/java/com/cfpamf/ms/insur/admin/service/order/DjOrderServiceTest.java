package com.cfpamf.ms.insur.admin.service.order;

import com.cfpamf.ms.insur.admin.constant.AmConstants;
import com.cfpamf.ms.insur.admin.dao.safes.AuthUserMapper;
import com.cfpamf.ms.insur.admin.dao.safes.SmCommissionMapper;
import com.cfpamf.ms.insur.admin.dao.safes.SmOrderMapper;
import com.cfpamf.ms.insur.admin.external.ChannelOrderService;
import com.cfpamf.ms.insur.admin.external.OrderQueryResponse;
import com.cfpamf.ms.insur.admin.external.dj.DjOrderServiceAdapterImpl;
import com.cfpamf.ms.insur.admin.external.dj.model.req.DJPayNotifyReq;
import com.cfpamf.ms.insur.admin.service.BaseTest;
import com.cfpamf.ms.insur.admin.service.SmProductService;
import com.cfpamf.ms.insur.base.event.EventBusEngine;
import com.cfpamf.ms.insur.base.infeface.CustomerCenterService;
import com.cfpamf.ms.insur.base.service.BusinessTokenService;
import com.cfpamf.ms.insur.base.util.HttpRequestUtil;
import com.github.jsonzou.jmockdata.JMockData;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * <AUTHOR> 2020/3/24 17:18
 */
@Slf4j
@RunWith(PowerMockRunner.class)
@PrepareForTest({HttpRequestUtil.class})
public class DjOrderServiceTest extends BaseTest {
    @InjectMocks
    DjOrderService djOrderService;
    @Mock
    com.cfpamf.ms.insur.admin.dao.safes.SmDjNotifyOrderMapper notifyOrderMapper;
    @Mock
    com.cfpamf.ms.insur.admin.external.dj.model.DjApiProperties properties;
    @Mock
    DjOrderServiceAdapterImpl adapter;

    /**
     * 产品service
     */
    @Mock
    protected SmProductService productService;

    /**
     * 业务token service
     */
    @Mock
    protected BusinessTokenService tokenService;

    /**
     * 客户中心 service
     */
    @Mock
    protected CustomerCenterService ccService;

    /**
     * 产品提成 service
     */
    @Mock
    protected SmCommissionMapper cmsMapper;

    /**
     * 订单mapper
     */
    @Mock
    protected SmOrderMapper orderMapper;

    /**
     * 用户mapper
     */
    @Mock
    protected AuthUserMapper userMapper;

    /**
     * 事件工作流
     */
    @Mock
    protected EventBusEngine busEngine;

    /**
     * token锁时间
     */
    @Mock
    protected long tokenLockTime = 123;

    @Before
    public void setUp() throws Exception {
        super.setUp();

        ChannelOrderService orderService = Mockito.mock(ChannelOrderService.class);

        OrderQueryResponse mockData = JMockData.mock(OrderQueryResponse.class);
        mockData.setNoticeCode(AmConstants.API_FANHUA_SUCCESS_0);
        Mockito.when(orderService.queryChannelOrderInfo(Mockito.any())).
                thenReturn(mockData);
        Mockito.when(mock.getBean(Mockito.anyString(), (Class<Object>) Mockito.any()))
                .thenAnswer(a -> {
                    Class<?> beanClass = a.getArgument(1);
                    if (beanClass == ChannelOrderService.class) {
                        return orderService;
                    }
                    return Mockito.mock(beanClass);
                });
    }

    @Test
    public void support() {
        djOrderService.support(",UMfil,");
    }

    @Test
    public void updateOrderPolicyInfo() {
        djOrderService.updateOrderPolicyInfo(",SgCCo,");
    }

    @Test
    public void getOrderInfo() {
        djOrderService.getOrderInfo(",svcCu,");
    }

    @Test
    public void handAsyncPayCallback() throws IOException {
        DJPayNotifyReq mock = JMockData.mock(DJPayNotifyReq.class, con());
        mock.setAmount("12.00");
        mock.setPayMadeDate(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        djOrderService.handAsyncPayCallback(",kfRIq,", mock, new MockHttpServletResponse(), new MockHttpServletRequest());
    }

    @Test
    public void handSyncPayCallback() throws IOException {
        djOrderService.handSyncPayCallback(",AqSrE,", null, new MockHttpServletResponse(), new MockHttpServletRequest());
    }
}
