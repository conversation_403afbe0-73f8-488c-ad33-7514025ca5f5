package com.cfpamf.ms.insur.admin.test;

import org.springframework.cglib.reflect.FastClass;
import org.springframework.cglib.reflect.FastMethod;

import java.lang.reflect.InvocationTargetException;

/**
 * <AUTHOR>
 */
public class TestCglib {

    public static void main(String[] args) throws InvocationTargetException {
        TestCglib test = new TestCglib();
        Class<?> serviceClass = test.getClass();
        String methodName = "test";
        Class<?>[] parameterTypes = new Class[]{String.class};
        Object[] parameters = new Object[]{"aaaa"};
        FastClass serviceFastClass = FastClass.create(serviceClass);
        FastMethod serviceFastMethod = serviceFastClass.getMethod(methodName, parameterTypes);
        Object result = serviceFastMethod.invoke(test, parameters);
        System.out.println(result.toString());
    }

    public String test(String s) {
        return "test=" + s;
    }
}
