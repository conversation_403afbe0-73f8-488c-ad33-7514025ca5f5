package com.cfpamf.ms.insur.admin.service.commission;

import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.ms.insur.admin.pojo.dto.commission.*;
import com.cfpamf.ms.insur.admin.pojo.dto.order.OrderCommissionInsuredInfoDTO;
import com.cfpamf.ms.insur.admin.pojo.dto.order.OrderRiskCorrectDTO;
import com.cfpamf.ms.insur.admin.pojo.po.commission.SmCommissionDetailItemPO;
import com.cfpamf.ms.insur.admin.pojo.po.commission.SystemCommissionConfig;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class CommissionCalcServiceTest {

    @Mock
    private CommissionConfigQueryService mockCommissionConfigQueryService;
    @Mock
    private CommissionExternalService mockCommissionExternalService;

    @Mock
    private CommissionCalcService commissionCalcServiceUnderTest;

    @Test
    public void testCalc() throws Exception {
        // Setup
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO.setFhOrderId("businessId");
        commissionCalcOrderInfoDTO.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO.setPolicyNo("policyNo");
        commissionCalcOrderInfoDTO.setAppStatus("1");
        commissionCalcOrderInfoDTO.setInsuredIdNumber("insuredIdNumber");
        commissionCalcOrderInfoDTO.setPlanId(0);
        commissionCalcOrderInfoDTO.setTotalAmount(new BigDecimal("0.00"));
        commissionCalcOrderInfoDTO.setOriginalAmount(new BigDecimal("0.00"));
        commissionCalcOrderInfoDTO.setSurrenderTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        commissionCalcOrderInfoDTO.setRefundAmount(new BigDecimal("0.00"));
        commissionCalcOrderInfoDTO.setPaymentTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        commissionCalcOrderInfoDTO.setVisitStatus("visitStatus");
        commissionCalcOrderInfoDTO.setRenewalAmount(new BigDecimal("0.00"));
        commissionCalcOrderInfoDTO.setRenewalTermTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        commissionCalcOrderInfoDTO.setPayWay("payWay");
        commissionCalcOrderInfoDTO.setCoveredYears("coveredYears");
        commissionCalcOrderInfoDTO.setValidPeriod("validPeriod");
        final CommissionCalcRiskInfoDTO commissionCalcRiskInfoDTO = new CommissionCalcRiskInfoDTO();
        commissionCalcRiskInfoDTO.setRiskId(0);
        commissionCalcRiskInfoDTO.setRiskName("riskName");
        commissionCalcRiskInfoDTO.setPayWay("payWay");
        commissionCalcRiskInfoDTO.setCoveredYears("coveredYears");
        commissionCalcRiskInfoDTO.setValidPeriod("validPeriod");
        commissionCalcRiskInfoDTO.setPremium(new BigDecimal("0.00"));
        commissionCalcRiskInfoDTO.setRefundAmount(new BigDecimal("0.00"));
        commissionCalcRiskInfoDTO.setRenewalAmount(new BigDecimal("0.00"));
        commissionCalcRiskInfoDTO.setRiskStatus("riskStatus");
        commissionCalcRiskInfoDTO.setRiskSurrenderTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        commissionCalcRiskInfoDTO.setRiskSurrenderType("riskSurrenderType");
        commissionCalcOrderInfoDTO.setRiskParamList(Arrays.asList(commissionCalcRiskInfoDTO));
        final List<CommissionCalcOrderInfoDTO> orderInfoDTOList = Arrays.asList(commissionCalcOrderInfoDTO);
        final CommissionCalcConfigInfoDTO configInfoDTO = new CommissionCalcConfigInfoDTO();
        configInfoDTO.setType(0);
        configInfoDTO.setInsureTypeSame(0);
        final CommissionCalcConfigDetailDTO commissionCalcConfigDetailDTO = new CommissionCalcConfigDetailDTO();
        commissionCalcConfigDetailDTO.setId(0);
        commissionCalcConfigDetailDTO.setPeriodNum(0);
        commissionCalcConfigDetailDTO.setPlanId(0);
        commissionCalcConfigDetailDTO.setRiskId(0);
        commissionCalcConfigDetailDTO.setPayWayList(Arrays.asList(0));
        commissionCalcConfigDetailDTO.setCoveredYearsList(Arrays.asList("value"));
        commissionCalcConfigDetailDTO.setValidPeriodList(Arrays.asList("value"));
        commissionCalcConfigDetailDTO.setCommissionRate(new BigDecimal("0.00"));
        configInfoDTO.setConfigDetailDTOList(Arrays.asList(commissionCalcConfigDetailDTO));

        final CalcErrorDTO processDTO = new CalcErrorDTO("errorMsg","errorMsgNoAlarm");
        final CalcCommissionItemResultDTO calcCommissionItemResultDTO = new CalcCommissionItemResultDTO();
        calcCommissionItemResultDTO.setPlanId(0);
        calcCommissionItemResultDTO.setRiskId(0);
        calcCommissionItemResultDTO.setCommissionType(0);
        calcCommissionItemResultDTO.setCommissionId(0);
        calcCommissionItemResultDTO.setCommissionRate(new BigDecimal("0.00"));
        calcCommissionItemResultDTO.setOrderId("businessId");
        calcCommissionItemResultDTO.setPolicyNo("policyNo");
        calcCommissionItemResultDTO.setInsuredIdNumber("insuredIdNumber");
        calcCommissionItemResultDTO.setPolicyStatus("1");
        calcCommissionItemResultDTO.setRiskStatus("1");
        calcCommissionItemResultDTO.setRiskName("riskName");
        calcCommissionItemResultDTO.setCalcWay(0);
        calcCommissionItemResultDTO.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        calcCommissionItemResultDTO.setPlanAmount(new BigDecimal("0.00"));
        calcCommissionItemResultDTO.setAmount(new BigDecimal("0.00"));
        calcCommissionItemResultDTO.setOriginalAmount(new BigDecimal("0.00"));
        calcCommissionItemResultDTO.setTermNum(0);
        calcCommissionItemResultDTO.setCommissionAmount(new BigDecimal("0.00"));
        calcCommissionItemResultDTO.setCommissionUserId("commissionUserId");
        calcCommissionItemResultDTO.setBusinessType("businessType");
        calcCommissionItemResultDTO.setBusinessId("businessId");
        final List<CalcCommissionItemResultDTO> expectedResult = Arrays.asList(calcCommissionItemResultDTO);

        // Run the test
        commissionCalcServiceUnderTest.calc(0, 0, orderInfoDTOList,
                configInfoDTO, processDTO, "commissionUserId", "businessType");
    }

    @Test
    public void testMatchingConfigDetail() {
        // Setup
        final CommissionCalcOrderInfoDTO insuredInfoDTO = new CommissionCalcOrderInfoDTO();
        insuredInfoDTO.setFhOrderId("businessId");
        insuredInfoDTO.setProductAttrCode("productAttrCode");
        insuredInfoDTO.setOriginalFhOrderId("originalFhOrderId");
        insuredInfoDTO.setPolicyNo("policyNo");
        insuredInfoDTO.setAppStatus("1");
        insuredInfoDTO.setInsuredIdNumber("insuredIdNumber");
        insuredInfoDTO.setPlanId(0);
        insuredInfoDTO.setTotalAmount(new BigDecimal("0.00"));
        insuredInfoDTO.setOriginalAmount(new BigDecimal("0.00"));
        insuredInfoDTO.setSurrenderTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        insuredInfoDTO.setRefundAmount(new BigDecimal("0.00"));
        insuredInfoDTO.setPaymentTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        insuredInfoDTO.setVisitStatus("visitStatus");
        insuredInfoDTO.setRenewalAmount(new BigDecimal("0.00"));
        insuredInfoDTO.setRenewalTermTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        insuredInfoDTO.setPayWay("payWay");
        insuredInfoDTO.setCoveredYears("coveredYears");
        insuredInfoDTO.setValidPeriod("validPeriod");
        final CommissionCalcRiskInfoDTO commissionCalcRiskInfoDTO = new CommissionCalcRiskInfoDTO();
        commissionCalcRiskInfoDTO.setRiskId(0);
        commissionCalcRiskInfoDTO.setRiskName("riskName");
        commissionCalcRiskInfoDTO.setPayWay("payWay");
        commissionCalcRiskInfoDTO.setCoveredYears("coveredYears");
        commissionCalcRiskInfoDTO.setValidPeriod("validPeriod");
        commissionCalcRiskInfoDTO.setPremium(new BigDecimal("0.00"));
        commissionCalcRiskInfoDTO.setRefundAmount(new BigDecimal("0.00"));
        commissionCalcRiskInfoDTO.setRenewalAmount(new BigDecimal("0.00"));
        commissionCalcRiskInfoDTO.setRiskStatus("riskStatus");
        commissionCalcRiskInfoDTO.setRiskSurrenderTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        commissionCalcRiskInfoDTO.setRiskSurrenderType("riskSurrenderType");
        insuredInfoDTO.setRiskParamList(Arrays.asList(commissionCalcRiskInfoDTO));

        final CommissionCalcRiskInfoDTO paramDTO = new CommissionCalcRiskInfoDTO();
        paramDTO.setRiskId(0);
        paramDTO.setRiskName("riskName");
        paramDTO.setPayWay("payWay");
        paramDTO.setCoveredYears("coveredYears");
        paramDTO.setValidPeriod("validPeriod");
        paramDTO.setPremium(new BigDecimal("0.00"));
        paramDTO.setRefundAmount(new BigDecimal("0.00"));
        paramDTO.setRenewalAmount(new BigDecimal("0.00"));
        paramDTO.setRiskStatus("riskStatus");
        paramDTO.setRiskSurrenderTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        paramDTO.setRiskSurrenderType("riskSurrenderType");

        final CommissionCalcConfigDetailDTO commissionCalcConfigDetailDTO = new CommissionCalcConfigDetailDTO();
        commissionCalcConfigDetailDTO.setId(0);
        commissionCalcConfigDetailDTO.setPeriodNum(0);
        commissionCalcConfigDetailDTO.setPlanId(0);
        commissionCalcConfigDetailDTO.setRiskId(0);
        commissionCalcConfigDetailDTO.setPayWayList(Arrays.asList(0));
        commissionCalcConfigDetailDTO.setCoveredYearsList(Arrays.asList("value"));
        commissionCalcConfigDetailDTO.setValidPeriodList(Arrays.asList("value"));
        commissionCalcConfigDetailDTO.setCommissionRate(new BigDecimal("0.00"));
        final List<CommissionCalcConfigDetailDTO> details = Arrays.asList(commissionCalcConfigDetailDTO);
        final CalcErrorDTO processDTO = new CalcErrorDTO("errorMsg","errorMsgNoAlarm");
        final CommissionCalcConfigDetailDTO expectedResult = new CommissionCalcConfigDetailDTO();
        expectedResult.setId(0);
        expectedResult.setPeriodNum(0);
        expectedResult.setPlanId(0);
        expectedResult.setRiskId(0);
        expectedResult.setPayWayList(Arrays.asList(0));
        expectedResult.setCoveredYearsList(Arrays.asList("value"));
        expectedResult.setValidPeriodList(Arrays.asList("value"));
        expectedResult.setCommissionRate(new BigDecimal("0.00"));

        final CommissionCalcConfigInfoDTO configInfoDTO = new CommissionCalcConfigInfoDTO();
        configInfoDTO.setType(0);
        configInfoDTO.setInsureTypeSame(0);
        configInfoDTO.setConfigDetailDTOList(Arrays.asList(commissionCalcConfigDetailDTO));

        // Run the test
        commissionCalcServiceUnderTest.matchingConfigDetail(configInfoDTO,1,insuredInfoDTO,
                0, 0, 0, paramDTO, details, processDTO);
    }

    @Test
    public void testCalcAmt() {
        commissionCalcServiceUnderTest.calcAmt(new BigDecimal("0.00"), new BigDecimal("0.00"));
    }

    @Test
    public void testCalcNewRefundDetailItem() {
        // Setup
        final CommissionCalcOrderInfoDTO orderInfo = new CommissionCalcOrderInfoDTO();
        orderInfo.setFhOrderId("businessId");
        orderInfo.setProductAttrCode("productAttrCode");
        orderInfo.setOriginalFhOrderId("originalFhOrderId");
        orderInfo.setPolicyNo("policyNo");
        orderInfo.setAppStatus("1");
        orderInfo.setInsuredIdNumber("insuredIdNumber");
        orderInfo.setPlanId(0);
        orderInfo.setTotalAmount(new BigDecimal("0.00"));
        orderInfo.setOriginalAmount(new BigDecimal("0.00"));
        orderInfo.setSurrenderTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderInfo.setRefundAmount(new BigDecimal("0.00"));
        orderInfo.setPaymentTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderInfo.setVisitStatus("visitStatus");
        orderInfo.setRenewalAmount(new BigDecimal("0.00"));
        orderInfo.setRenewalTermTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderInfo.setPayWay("payWay");
        orderInfo.setCoveredYears("coveredYears");
        orderInfo.setValidPeriod("validPeriod");
        final CommissionCalcRiskInfoDTO commissionCalcRiskInfoDTO = new CommissionCalcRiskInfoDTO();
        commissionCalcRiskInfoDTO.setRiskId(0);
        commissionCalcRiskInfoDTO.setRiskName("riskName");
        commissionCalcRiskInfoDTO.setPayWay("payWay");
        commissionCalcRiskInfoDTO.setCoveredYears("coveredYears");
        commissionCalcRiskInfoDTO.setValidPeriod("validPeriod");
        commissionCalcRiskInfoDTO.setPremium(new BigDecimal("0.00"));
        commissionCalcRiskInfoDTO.setRefundAmount(new BigDecimal("0.00"));
        commissionCalcRiskInfoDTO.setRenewalAmount(new BigDecimal("0.00"));
        commissionCalcRiskInfoDTO.setRiskStatus("riskStatus");
        commissionCalcRiskInfoDTO.setRiskSurrenderTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        commissionCalcRiskInfoDTO.setRiskSurrenderType("riskSurrenderType");
        orderInfo.setRiskParamList(Arrays.asList(commissionCalcRiskInfoDTO));

        final Map<Integer, List<SmCommissionDetailItemPO>> itemMap = new HashMap<>();
        final CalcCommissionItemResultDTO calcCommissionItemResultDTO = new CalcCommissionItemResultDTO();
        calcCommissionItemResultDTO.setPlanId(0);
        calcCommissionItemResultDTO.setRiskId(0);
        calcCommissionItemResultDTO.setCommissionType(0);
        calcCommissionItemResultDTO.setCommissionId(0);
        calcCommissionItemResultDTO.setCommissionRate(new BigDecimal("0.00"));
        calcCommissionItemResultDTO.setOrderId("businessId");
        calcCommissionItemResultDTO.setPolicyNo("policyNo");
        calcCommissionItemResultDTO.setInsuredIdNumber("insuredIdNumber");
        calcCommissionItemResultDTO.setPolicyStatus("1");
        calcCommissionItemResultDTO.setRiskStatus("1");
        calcCommissionItemResultDTO.setRiskName("riskName");
        calcCommissionItemResultDTO.setCalcWay(0);
        calcCommissionItemResultDTO.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        calcCommissionItemResultDTO.setPlanAmount(new BigDecimal("0.00"));
        calcCommissionItemResultDTO.setAmount(new BigDecimal("0.00"));
        calcCommissionItemResultDTO.setOriginalAmount(new BigDecimal("0.00"));
        calcCommissionItemResultDTO.setTermNum(0);
        calcCommissionItemResultDTO.setCommissionAmount(new BigDecimal("0.00"));
        calcCommissionItemResultDTO.setCommissionUserId("commissionUserId");
        calcCommissionItemResultDTO.setBusinessType("businessType");
        calcCommissionItemResultDTO.setBusinessId("businessId");
        final List<CalcCommissionItemResultDTO> expectedResult = Arrays.asList(calcCommissionItemResultDTO);
        mockCommissionExternalService.isDecreaseGroupOrder("productAttrCode", "1");
        mockCommissionExternalService.isNotHesitationPeriod("visitStatus", "riskSurrenderType");

        // Run the test
        commissionCalcServiceUnderTest.calcNewRefundDetailItem(
                orderInfo, itemMap);
    }

    @Test
    public void testCalcOldRefundDetailItem() {
        // Setup
        final CommissionCalcOrderInfoDTO orderInfo = new CommissionCalcOrderInfoDTO();
        orderInfo.setFhOrderId("businessId");
        orderInfo.setProductAttrCode("productAttrCode");
        orderInfo.setOriginalFhOrderId("originalFhOrderId");
        orderInfo.setPolicyNo("policyNo");
        orderInfo.setAppStatus("1");
        orderInfo.setInsuredIdNumber("insuredIdNumber");
        orderInfo.setPlanId(0);
        orderInfo.setTotalAmount(new BigDecimal("0.00"));
        orderInfo.setOriginalAmount(new BigDecimal("0.00"));
        orderInfo.setSurrenderTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderInfo.setRefundAmount(new BigDecimal("0.00"));
        orderInfo.setPaymentTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderInfo.setVisitStatus("visitStatus");
        orderInfo.setRenewalAmount(new BigDecimal("0.00"));
        orderInfo.setRenewalTermTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderInfo.setPayWay("payWay");
        orderInfo.setCoveredYears("coveredYears");
        orderInfo.setValidPeriod("validPeriod");
        final CommissionCalcRiskInfoDTO commissionCalcRiskInfoDTO = new CommissionCalcRiskInfoDTO();
        commissionCalcRiskInfoDTO.setRiskId(0);
        commissionCalcRiskInfoDTO.setRiskName("riskName");
        commissionCalcRiskInfoDTO.setPayWay("payWay");
        commissionCalcRiskInfoDTO.setCoveredYears("coveredYears");
        commissionCalcRiskInfoDTO.setValidPeriod("validPeriod");
        commissionCalcRiskInfoDTO.setPremium(new BigDecimal("0.00"));
        commissionCalcRiskInfoDTO.setRefundAmount(new BigDecimal("0.00"));
        commissionCalcRiskInfoDTO.setRenewalAmount(new BigDecimal("0.00"));
        commissionCalcRiskInfoDTO.setRiskStatus("riskStatus");
        commissionCalcRiskInfoDTO.setRiskSurrenderTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        commissionCalcRiskInfoDTO.setRiskSurrenderType("riskSurrenderType");
        orderInfo.setRiskParamList(Arrays.asList(commissionCalcRiskInfoDTO));

        final CommissionRateDTO commissionRateDTO = new CommissionRateDTO();
        commissionRateDTO.setPlanId(0);
        commissionRateDTO.setRiskId(0);
        commissionRateDTO.setCommissionType(0);
        commissionRateDTO.setCommissionId(0);
        commissionRateDTO.setCommissionRate(new BigDecimal("0.00"));
        final List<CommissionRateDTO> oldCommissionRates = Arrays.asList(commissionRateDTO);
        final CalcCommissionItemResultDTO calcCommissionItemResultDTO = new CalcCommissionItemResultDTO();
        calcCommissionItemResultDTO.setPlanId(0);
        calcCommissionItemResultDTO.setRiskId(0);
        calcCommissionItemResultDTO.setCommissionType(0);
        calcCommissionItemResultDTO.setCommissionId(0);
        calcCommissionItemResultDTO.setCommissionRate(new BigDecimal("0.00"));
        calcCommissionItemResultDTO.setOrderId("businessId");
        calcCommissionItemResultDTO.setPolicyNo("policyNo");
        calcCommissionItemResultDTO.setInsuredIdNumber("insuredIdNumber");
        calcCommissionItemResultDTO.setPolicyStatus("1");
        calcCommissionItemResultDTO.setRiskStatus("1");
        calcCommissionItemResultDTO.setRiskName("riskName");
        calcCommissionItemResultDTO.setCalcWay(0);
        calcCommissionItemResultDTO.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        calcCommissionItemResultDTO.setPlanAmount(new BigDecimal("0.00"));
        calcCommissionItemResultDTO.setAmount(new BigDecimal("0.00"));
        calcCommissionItemResultDTO.setOriginalAmount(new BigDecimal("0.00"));
        calcCommissionItemResultDTO.setTermNum(0);
        calcCommissionItemResultDTO.setCommissionAmount(new BigDecimal("0.00"));
        calcCommissionItemResultDTO.setCommissionUserId("commissionUserId");
        calcCommissionItemResultDTO.setBusinessType("businessType");
        calcCommissionItemResultDTO.setBusinessId("businessId");
        final List<CalcCommissionItemResultDTO> expectedResult = Arrays.asList(calcCommissionItemResultDTO);

        // Run the test
        commissionCalcServiceUnderTest.calcOldRefundDetailItem(
                orderInfo, oldCommissionRates, "commissionUserId");
    }

    @Test
    public void testCalcCorrectCommission() throws Exception {
        // Setup
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO.setFhOrderId("businessId");
        commissionCalcOrderInfoDTO.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO.setPolicyNo("policyNo");
        commissionCalcOrderInfoDTO.setAppStatus("1");
        commissionCalcOrderInfoDTO.setInsuredIdNumber("insuredIdNumber");
        commissionCalcOrderInfoDTO.setPlanId(0);
        commissionCalcOrderInfoDTO.setTotalAmount(new BigDecimal("0.00"));
        commissionCalcOrderInfoDTO.setOriginalAmount(new BigDecimal("0.00"));
        commissionCalcOrderInfoDTO.setSurrenderTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        commissionCalcOrderInfoDTO.setRefundAmount(new BigDecimal("0.00"));
        commissionCalcOrderInfoDTO.setPaymentTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        commissionCalcOrderInfoDTO.setVisitStatus("visitStatus");
        commissionCalcOrderInfoDTO.setRenewalAmount(new BigDecimal("0.00"));
        commissionCalcOrderInfoDTO.setRenewalTermTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        commissionCalcOrderInfoDTO.setPayWay("payWay");
        commissionCalcOrderInfoDTO.setCoveredYears("coveredYears");
        commissionCalcOrderInfoDTO.setValidPeriod("validPeriod");
        final CommissionCalcRiskInfoDTO commissionCalcRiskInfoDTO = new CommissionCalcRiskInfoDTO();
        commissionCalcRiskInfoDTO.setRiskId(0);
        commissionCalcRiskInfoDTO.setRiskName("riskName");
        commissionCalcRiskInfoDTO.setPayWay("payWay");
        commissionCalcRiskInfoDTO.setCoveredYears("coveredYears");
        commissionCalcRiskInfoDTO.setValidPeriod("validPeriod");
        commissionCalcRiskInfoDTO.setPremium(new BigDecimal("0.00"));
        commissionCalcRiskInfoDTO.setRefundAmount(new BigDecimal("0.00"));
        commissionCalcRiskInfoDTO.setRenewalAmount(new BigDecimal("0.00"));
        commissionCalcRiskInfoDTO.setRiskStatus("riskStatus");
        commissionCalcRiskInfoDTO.setRiskSurrenderTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        commissionCalcRiskInfoDTO.setRiskSurrenderType("riskSurrenderType");
        commissionCalcOrderInfoDTO.setRiskParamList(Arrays.asList(commissionCalcRiskInfoDTO));
        final List<CommissionCalcOrderInfoDTO> orderInfoDTOList = Arrays.asList(commissionCalcOrderInfoDTO);
        final OrderRiskCorrectDTO orderRiskCorrectDTO = new OrderRiskCorrectDTO();
        orderRiskCorrectDTO.setOrderId("orderId");
        orderRiskCorrectDTO.setPolicyNo("policyNo");
        orderRiskCorrectDTO.setCorrectId("businessId");
        orderRiskCorrectDTO.setCorrectType("correctType");
        orderRiskCorrectDTO.setRiskId(0);
        final List<OrderRiskCorrectDTO> correctDTOList = Arrays.asList(orderRiskCorrectDTO);
        final SmCommissionDetailItemPO smCommissionDetailItemPO = new SmCommissionDetailItemPO();
        smCommissionDetailItemPO.setId(0);
        smCommissionDetailItemPO.setOrderId("orderId");
        smCommissionDetailItemPO.setPolicyNo("policyNo");
        smCommissionDetailItemPO.setInsuredIdNumber("insuredIdNumber");
        smCommissionDetailItemPO.setRiskStatus("riskStatus");
        smCommissionDetailItemPO.setPlanId(0);
        smCommissionDetailItemPO.setRiskId(0);
        smCommissionDetailItemPO.setRiskName("riskName");
        smCommissionDetailItemPO.setAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setCommissionType(0);
        smCommissionDetailItemPO.setCommissionId(0);
        smCommissionDetailItemPO.setCommissionRate(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setCommissionUserId("commissionUserId");
        smCommissionDetailItemPO.setBusinessType("businessType");
        smCommissionDetailItemPO.setBusinessId("businessId");
        final List<SmCommissionDetailItemPO> itemPOs = Arrays.asList(smCommissionDetailItemPO);
        final CalcErrorDTO processDTO = new CalcErrorDTO("errorMsg","errorMsgNoAlarm");
        final CalcCommissionItemResultDTO calcCommissionItemResultDTO = new CalcCommissionItemResultDTO();
        calcCommissionItemResultDTO.setPlanId(0);
        calcCommissionItemResultDTO.setRiskId(0);
        calcCommissionItemResultDTO.setCommissionType(0);
        calcCommissionItemResultDTO.setCommissionId(0);
        calcCommissionItemResultDTO.setCommissionRate(new BigDecimal("0.00"));
        calcCommissionItemResultDTO.setOrderId("businessId");
        calcCommissionItemResultDTO.setPolicyNo("policyNo");
        calcCommissionItemResultDTO.setInsuredIdNumber("insuredIdNumber");
        calcCommissionItemResultDTO.setPolicyStatus("1");
        calcCommissionItemResultDTO.setRiskStatus("1");
        calcCommissionItemResultDTO.setRiskName("riskName");
        calcCommissionItemResultDTO.setCalcWay(0);
        calcCommissionItemResultDTO.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        calcCommissionItemResultDTO.setPlanAmount(new BigDecimal("0.00"));
        calcCommissionItemResultDTO.setAmount(new BigDecimal("0.00"));
        calcCommissionItemResultDTO.setOriginalAmount(new BigDecimal("0.00"));
        calcCommissionItemResultDTO.setTermNum(0);
        calcCommissionItemResultDTO.setCommissionAmount(new BigDecimal("0.00"));
        calcCommissionItemResultDTO.setCommissionUserId("commissionUserId");
        calcCommissionItemResultDTO.setBusinessType("businessType");
        calcCommissionItemResultDTO.setBusinessId("businessId");
        final List<CalcCommissionItemResultDTO> expectedResult = Arrays.asList(calcCommissionItemResultDTO);
        mockCommissionExternalService.isNotHesitationPeriod("visitStatus", "riskSurrenderType");

        // Run the test
        commissionCalcServiceUnderTest.calcCorrectCommission(0,
                orderInfoDTOList, correctDTOList, itemPOs, processDTO);
    }

    @Test
    public void testCalcRiskCorrectCommission() {
        // Setup
        final CommissionCalcOrderInfoDTO orderInfo = new CommissionCalcOrderInfoDTO();
        orderInfo.setFhOrderId("businessId");
        orderInfo.setProductAttrCode("productAttrCode");
        orderInfo.setOriginalFhOrderId("originalFhOrderId");
        orderInfo.setPolicyNo("policyNo");
        orderInfo.setAppStatus("1");
        orderInfo.setInsuredIdNumber("insuredIdNumber");
        orderInfo.setPlanId(0);
        orderInfo.setTotalAmount(new BigDecimal("0.00"));
        orderInfo.setOriginalAmount(new BigDecimal("0.00"));
        orderInfo.setSurrenderTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderInfo.setRefundAmount(new BigDecimal("0.00"));
        orderInfo.setPaymentTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderInfo.setVisitStatus("visitStatus");
        orderInfo.setRenewalAmount(new BigDecimal("0.00"));
        orderInfo.setRenewalTermTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderInfo.setPayWay("payWay");
        orderInfo.setCoveredYears("coveredYears");
        orderInfo.setValidPeriod("validPeriod");
        final CommissionCalcRiskInfoDTO commissionCalcRiskInfoDTO = new CommissionCalcRiskInfoDTO();
        commissionCalcRiskInfoDTO.setRiskId(0);
        commissionCalcRiskInfoDTO.setRiskName("riskName");
        commissionCalcRiskInfoDTO.setPayWay("payWay");
        commissionCalcRiskInfoDTO.setCoveredYears("coveredYears");
        commissionCalcRiskInfoDTO.setValidPeriod("validPeriod");
        commissionCalcRiskInfoDTO.setPremium(new BigDecimal("0.00"));
        commissionCalcRiskInfoDTO.setRefundAmount(new BigDecimal("0.00"));
        commissionCalcRiskInfoDTO.setRenewalAmount(new BigDecimal("0.00"));
        commissionCalcRiskInfoDTO.setRiskStatus("riskStatus");
        commissionCalcRiskInfoDTO.setRiskSurrenderTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        commissionCalcRiskInfoDTO.setRiskSurrenderType("riskSurrenderType");
        orderInfo.setRiskParamList(Arrays.asList(commissionCalcRiskInfoDTO));

        final OrderRiskCorrectDTO riskCorrectDTO = new OrderRiskCorrectDTO();
        riskCorrectDTO.setOrderId("orderId");
        riskCorrectDTO.setPolicyNo("policyNo");
        riskCorrectDTO.setCorrectId("businessId");
        riskCorrectDTO.setCorrectType("correctType");
        riskCorrectDTO.setRiskId(0);

        final CommissionCalcRiskInfoDTO riskInfoDTO = new CommissionCalcRiskInfoDTO();
        riskInfoDTO.setRiskId(0);
        riskInfoDTO.setRiskName("riskName");
        riskInfoDTO.setPayWay("payWay");
        riskInfoDTO.setCoveredYears("coveredYears");
        riskInfoDTO.setValidPeriod("validPeriod");
        riskInfoDTO.setPremium(new BigDecimal("0.00"));
        riskInfoDTO.setRefundAmount(new BigDecimal("0.00"));
        riskInfoDTO.setRenewalAmount(new BigDecimal("0.00"));
        riskInfoDTO.setRiskStatus("riskStatus");
        riskInfoDTO.setRiskSurrenderTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        riskInfoDTO.setRiskSurrenderType("riskSurrenderType");

        final SmCommissionDetailItemPO smCommissionDetailItemPO = new SmCommissionDetailItemPO();
        smCommissionDetailItemPO.setId(0);
        smCommissionDetailItemPO.setOrderId("orderId");
        smCommissionDetailItemPO.setPolicyNo("policyNo");
        smCommissionDetailItemPO.setInsuredIdNumber("insuredIdNumber");
        smCommissionDetailItemPO.setRiskStatus("riskStatus");
        smCommissionDetailItemPO.setPlanId(0);
        smCommissionDetailItemPO.setRiskId(0);
        smCommissionDetailItemPO.setRiskName("riskName");
        smCommissionDetailItemPO.setAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setCommissionType(0);
        smCommissionDetailItemPO.setCommissionId(0);
        smCommissionDetailItemPO.setCommissionRate(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setCommissionUserId("commissionUserId");
        smCommissionDetailItemPO.setBusinessType("businessType");
        smCommissionDetailItemPO.setBusinessId("businessId");
        final List<SmCommissionDetailItemPO> itemPOs = Arrays.asList(smCommissionDetailItemPO);
        final CalcCommissionItemResultDTO expectedResult = new CalcCommissionItemResultDTO();
        expectedResult.setPlanId(0);
        expectedResult.setRiskId(0);
        expectedResult.setCommissionType(0);
        expectedResult.setCommissionId(0);
        expectedResult.setCommissionRate(new BigDecimal("0.00"));
        expectedResult.setOrderId("businessId");
        expectedResult.setPolicyNo("policyNo");
        expectedResult.setInsuredIdNumber("insuredIdNumber");
        expectedResult.setPolicyStatus("1");
        expectedResult.setRiskStatus("1");
        expectedResult.setRiskName("riskName");
        expectedResult.setCalcWay(0);
        expectedResult.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        expectedResult.setPlanAmount(new BigDecimal("0.00"));
        expectedResult.setAmount(new BigDecimal("0.00"));
        expectedResult.setOriginalAmount(new BigDecimal("0.00"));
        expectedResult.setTermNum(0);
        expectedResult.setCommissionAmount(new BigDecimal("0.00"));
        expectedResult.setCommissionUserId("commissionUserId");
        expectedResult.setBusinessType("businessType");
        expectedResult.setBusinessId("businessId");

        mockCommissionExternalService.isNotHesitationPeriod("visitStatus", "riskSurrenderType");

        // Run the test
        commissionCalcServiceUnderTest.calcRiskCorrectCommission(0,
                orderInfo, riskCorrectDTO, riskInfoDTO, itemPOs);
    }

    @Test
    public void testCalcOrderCommissionV1() {
        // Setup
        final OrderCommissionInsuredInfoDTO orderCommissionInsuredInfoDTO = new OrderCommissionInsuredInfoDTO();
        orderCommissionInsuredInfoDTO.setFhOrderId("fhOrderId");
        orderCommissionInsuredInfoDTO.setPlanId(0);
        orderCommissionInsuredInfoDTO.setTotalAmount(new BigDecimal("0.00"));
        orderCommissionInsuredInfoDTO.setPolicyNo("policyNo");
        orderCommissionInsuredInfoDTO.setAppStatus("appStatus");
        orderCommissionInsuredInfoDTO.setInsuredIdNumber("insuredIdNumber");
        orderCommissionInsuredInfoDTO.setSurrenderTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderCommissionInsuredInfoDTO.setPaymentTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<OrderCommissionInsuredInfoDTO> insuredInfoDTOList = Arrays.asList(orderCommissionInsuredInfoDTO);
        final CommissionPlanParamDTO commissionPlanParamDTO = new CommissionPlanParamDTO();
        commissionPlanParamDTO.setPlanId(0);
        commissionPlanParamDTO.setRiskId(0);
        commissionPlanParamDTO.setNeedCalc(0);
        commissionPlanParamDTO.setRiskType(0);
        commissionPlanParamDTO.setPayWay("payWay");
        commissionPlanParamDTO.setCoveredYears("coveredYears");
        commissionPlanParamDTO.setValidPeriod("validPeriod");
        final CommissionPlanParamDTO.ResultDTO resultDTO = new CommissionPlanParamDTO.ResultDTO();
        resultDTO.setType(0);
        resultDTO.setCommissionId(0);
        resultDTO.setCommissionRate(new BigDecimal("0.00"));
        resultDTO.setUnMatchingMsg("unMatchingMsg");
        commissionPlanParamDTO.setMatchingRetList(Arrays.asList(resultDTO));
        final List<CommissionPlanParamDTO> commissionConfigParamList = Arrays.asList(commissionPlanParamDTO);
        final CalcCommissionResultDTO calcCommissionResultDTO = new CalcCommissionResultDTO();
        calcCommissionResultDTO.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        calcCommissionResultDTO.setOrderId("fhOrderId");
        calcCommissionResultDTO.setPolicyNo("policyNo");
        calcCommissionResultDTO.setPolicyStatus("appStatus");
        calcCommissionResultDTO.setInsuredIdNumber("insuredIdNumber");
        calcCommissionResultDTO.setPlanId(0);
        calcCommissionResultDTO.setAmount(new BigDecimal("0.00"));
        calcCommissionResultDTO.setRiskId(0);
        final CommissionDTO commissionDTO = new CommissionDTO();
        commissionDTO.setType(0);
        commissionDTO.setCommissionId(0);
        commissionDTO.setCommissionRate(new BigDecimal("0.00"));
        commissionDTO.setCommissionAmt(new BigDecimal("0.00"));
        commissionDTO.setAmount(new BigDecimal("0.00"));
        calcCommissionResultDTO.setCommissionList(Arrays.asList(commissionDTO));
        final List<CalcCommissionResultDTO> expectedResult = Arrays.asList(calcCommissionResultDTO);

        // Run the test
        commissionCalcServiceUnderTest.calcOrderCommissionV1(
                insuredInfoDTOList, commissionConfigParamList);
    }

    @Test
    public void testQueryCommissionRate() {
        // Setup
        final CommissionPlanParamDTO commissionPlanParamDTO = new CommissionPlanParamDTO();
        commissionPlanParamDTO.setPlanId(0);
        commissionPlanParamDTO.setRiskId(0);
        commissionPlanParamDTO.setNeedCalc(0);
        commissionPlanParamDTO.setRiskType(0);
        commissionPlanParamDTO.setPayWay("payWay");
        commissionPlanParamDTO.setCoveredYears("coveredYears");
        commissionPlanParamDTO.setValidPeriod("validPeriod");
        final CommissionPlanParamDTO.ResultDTO resultDTO = new CommissionPlanParamDTO.ResultDTO();
        resultDTO.setType(0);
        resultDTO.setCommissionId(0);
        resultDTO.setCommissionRate(new BigDecimal("0.00"));
        resultDTO.setUnMatchingMsg("unMatchingMsg");
        commissionPlanParamDTO.setMatchingRetList(Arrays.asList(resultDTO));
        final List<CommissionPlanParamDTO> commissionConfigParamList = Arrays.asList(commissionPlanParamDTO);

        // Configure CommissionConfigQueryService.listConfigByProductId(...).
        final SystemCommissionConfig systemCommissionConfig = new SystemCommissionConfig();
        systemCommissionConfig.setId(0);
        systemCommissionConfig.setType(0);
        systemCommissionConfig.setProductId(0);
        systemCommissionConfig.setStartTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        systemCommissionConfig.setEndTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        systemCommissionConfig.setInsureTypeSame(0);
        final List<SystemCommissionConfig> systemCommissionConfigs = Arrays.asList(systemCommissionConfig);
        mockCommissionConfigQueryService.listConfigByProductId(0);

        // Configure CommissionConfigQueryService.listDetailByConfigId(...).
        final CommissionConfigDetailDTO commissionConfigDetailDTO = new CommissionConfigDetailDTO();
        commissionConfigDetailDTO.setId(0);
        commissionConfigDetailDTO.setConfigId(0);
        commissionConfigDetailDTO.setPeriodNum(0);
        commissionConfigDetailDTO.setPlanId(0);
        commissionConfigDetailDTO.setRiskId(0);
        commissionConfigDetailDTO.setCommissionRate(new BigDecimal("0.00"));
        commissionConfigDetailDTO.setPayWayList(Arrays.asList(0));
        commissionConfigDetailDTO.setCoveredYearsList(Arrays.asList("value"));
        commissionConfigDetailDTO.setValidPeriodList(Arrays.asList("value"));
        final List<CommissionConfigDetailDTO> commissionConfigDetailDTOS = Arrays.asList(commissionConfigDetailDTO);
        mockCommissionConfigQueryService.listDetailByConfigId(0);

        // Run the test
        commissionCalcServiceUnderTest.queryCommissionRate(0,
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0, commissionConfigParamList);

    }

    @Test
    public void testQueryCommissionRate_CommissionConfigQueryServiceListConfigByProductIdReturnsNoItems() {
        // Setup
        final CommissionPlanParamDTO commissionPlanParamDTO = new CommissionPlanParamDTO();
        commissionPlanParamDTO.setPlanId(0);
        commissionPlanParamDTO.setRiskId(0);
        commissionPlanParamDTO.setNeedCalc(0);
        commissionPlanParamDTO.setRiskType(0);
        commissionPlanParamDTO.setPayWay("payWay");
        commissionPlanParamDTO.setCoveredYears("coveredYears");
        commissionPlanParamDTO.setValidPeriod("validPeriod");
        final CommissionPlanParamDTO.ResultDTO resultDTO = new CommissionPlanParamDTO.ResultDTO();
        resultDTO.setType(0);
        resultDTO.setCommissionId(0);
        resultDTO.setCommissionRate(new BigDecimal("0.00"));
        resultDTO.setUnMatchingMsg("unMatchingMsg");
        commissionPlanParamDTO.setMatchingRetList(Arrays.asList(resultDTO));
        final List<CommissionPlanParamDTO> commissionConfigParamList = Arrays.asList(commissionPlanParamDTO);
        mockCommissionConfigQueryService.listConfigByProductId(0);

        // Configure CommissionConfigQueryService.listDetailByConfigId(...).
        final CommissionConfigDetailDTO commissionConfigDetailDTO = new CommissionConfigDetailDTO();
        commissionConfigDetailDTO.setId(0);
        commissionConfigDetailDTO.setConfigId(0);
        commissionConfigDetailDTO.setPeriodNum(0);
        commissionConfigDetailDTO.setPlanId(0);
        commissionConfigDetailDTO.setRiskId(0);
        commissionConfigDetailDTO.setCommissionRate(new BigDecimal("0.00"));
        commissionConfigDetailDTO.setPayWayList(Arrays.asList(0));
        commissionConfigDetailDTO.setCoveredYearsList(Arrays.asList("value"));
        commissionConfigDetailDTO.setValidPeriodList(Arrays.asList("value"));
        final List<CommissionConfigDetailDTO> commissionConfigDetailDTOS = Arrays.asList(commissionConfigDetailDTO);
        mockCommissionConfigQueryService.listDetailByConfigId(0);

        // Run the test
        commissionCalcServiceUnderTest.queryCommissionRate(0,
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0, commissionConfigParamList);
    }

    @Test
    public void testQueryCommissionRate_CommissionConfigQueryServiceListDetailByConfigIdReturnsNoItems() {
        // Setup
        final CommissionPlanParamDTO commissionPlanParamDTO = new CommissionPlanParamDTO();
        commissionPlanParamDTO.setPlanId(0);
        commissionPlanParamDTO.setRiskId(0);
        commissionPlanParamDTO.setNeedCalc(0);
        commissionPlanParamDTO.setRiskType(0);
        commissionPlanParamDTO.setPayWay("payWay");
        commissionPlanParamDTO.setCoveredYears("coveredYears");
        commissionPlanParamDTO.setValidPeriod("validPeriod");
        final CommissionPlanParamDTO.ResultDTO resultDTO = new CommissionPlanParamDTO.ResultDTO();
        resultDTO.setType(0);
        resultDTO.setCommissionId(0);
        resultDTO.setCommissionRate(new BigDecimal("0.00"));
        resultDTO.setUnMatchingMsg("unMatchingMsg");
        commissionPlanParamDTO.setMatchingRetList(Arrays.asList(resultDTO));
        final List<CommissionPlanParamDTO> commissionConfigParamList = Arrays.asList(commissionPlanParamDTO);

        // Configure CommissionConfigQueryService.listConfigByProductId(...).
        final SystemCommissionConfig systemCommissionConfig = new SystemCommissionConfig();
        systemCommissionConfig.setId(0);
        systemCommissionConfig.setType(0);
        systemCommissionConfig.setProductId(0);
        systemCommissionConfig.setStartTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        systemCommissionConfig.setEndTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        systemCommissionConfig.setInsureTypeSame(0);
        final List<SystemCommissionConfig> systemCommissionConfigs = Arrays.asList(systemCommissionConfig);
        mockCommissionConfigQueryService.listConfigByProductId(0);

        mockCommissionConfigQueryService.listDetailByConfigId(0);

        // Run the test
        commissionCalcServiceUnderTest.queryCommissionRate(0,
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0, commissionConfigParamList);
    }

    @Test
    public void testMatchingCommissionRate() {
        // Setup
        final SystemCommissionConfig config = new SystemCommissionConfig();
        config.setId(0);
        config.setType(0);
        config.setProductId(0);
        config.setStartTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        config.setEndTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        config.setInsureTypeSame(0);

        final CommissionConfigDetailDTO commissionConfigDetailDTO = new CommissionConfigDetailDTO();
        commissionConfigDetailDTO.setId(0);
        commissionConfigDetailDTO.setConfigId(0);
        commissionConfigDetailDTO.setPeriodNum(0);
        commissionConfigDetailDTO.setPlanId(0);
        commissionConfigDetailDTO.setRiskId(0);
        commissionConfigDetailDTO.setCommissionRate(new BigDecimal("0.00"));
        commissionConfigDetailDTO.setPayWayList(Arrays.asList(0));
        commissionConfigDetailDTO.setCoveredYearsList(Arrays.asList("value"));
        commissionConfigDetailDTO.setValidPeriodList(Arrays.asList("value"));
        final List<CommissionConfigDetailDTO> details = Arrays.asList(commissionConfigDetailDTO);
        final CommissionPlanParamDTO commissionPlanParamDTO = new CommissionPlanParamDTO();
        commissionPlanParamDTO.setPlanId(0);
        commissionPlanParamDTO.setRiskId(0);
        commissionPlanParamDTO.setNeedCalc(0);
        commissionPlanParamDTO.setRiskType(0);
        commissionPlanParamDTO.setPayWay("payWay");
        commissionPlanParamDTO.setCoveredYears("coveredYears");
        commissionPlanParamDTO.setValidPeriod("validPeriod");
        final CommissionPlanParamDTO.ResultDTO resultDTO = new CommissionPlanParamDTO.ResultDTO();
        resultDTO.setType(0);
        resultDTO.setCommissionId(0);
        resultDTO.setCommissionRate(new BigDecimal("0.00"));
        resultDTO.setUnMatchingMsg("unMatchingMsg");
        commissionPlanParamDTO.setMatchingRetList(Arrays.asList(resultDTO));
        final List<CommissionPlanParamDTO> commissionConfigParamList = Arrays.asList(commissionPlanParamDTO);

        // Run the test
        commissionCalcServiceUnderTest.matchingCommissionRate(config, details, commissionConfigParamList);
    }

    @Test
    public void testGetMatchingDetailV2() {
        // Setup
        final CommissionPlanParamDTO paramDTO = new CommissionPlanParamDTO();
        paramDTO.setPlanId(0);
        paramDTO.setRiskId(0);
        paramDTO.setNeedCalc(0);
        paramDTO.setRiskType(0);
        paramDTO.setPayWay("payWay");
        paramDTO.setCoveredYears("coveredYears");
        paramDTO.setValidPeriod("validPeriod");
        final CommissionPlanParamDTO.ResultDTO resultDTO = new CommissionPlanParamDTO.ResultDTO();
        resultDTO.setType(0);
        resultDTO.setCommissionId(0);
        resultDTO.setCommissionRate(new BigDecimal("0.00"));
        resultDTO.setUnMatchingMsg("unMatchingMsg");
        paramDTO.setMatchingRetList(Arrays.asList(resultDTO));

        final CommissionConfigDetailDTO commissionConfigDetailDTO = new CommissionConfigDetailDTO();
        commissionConfigDetailDTO.setId(0);
        commissionConfigDetailDTO.setConfigId(0);
        commissionConfigDetailDTO.setPeriodNum(0);
        commissionConfigDetailDTO.setPlanId(0);
        commissionConfigDetailDTO.setRiskId(0);
        commissionConfigDetailDTO.setCommissionRate(new BigDecimal("0.00"));
        commissionConfigDetailDTO.setPayWayList(Arrays.asList(0));
        commissionConfigDetailDTO.setCoveredYearsList(Arrays.asList("value"));
        commissionConfigDetailDTO.setValidPeriodList(Arrays.asList("value"));
        final List<CommissionConfigDetailDTO> details = Arrays.asList(commissionConfigDetailDTO);
        final CommissionConfigDetailDTO expectedResult = new CommissionConfigDetailDTO();
        expectedResult.setId(0);
        expectedResult.setConfigId(0);
        expectedResult.setPeriodNum(0);
        expectedResult.setPlanId(0);
        expectedResult.setRiskId(0);
        expectedResult.setCommissionRate(new BigDecimal("0.00"));
        expectedResult.setPayWayList(Arrays.asList(0));
        expectedResult.setCoveredYearsList(Arrays.asList("value"));
        expectedResult.setValidPeriodList(Arrays.asList("value"));

        // Run the test
        commissionCalcServiceUnderTest.getMatchingDetailV2(0, paramDTO,
                details);
    }

    @Test
    public void testCalcOrderCommission() {
        // Setup
        final CalcCommissionParamDTO param = new CalcCommissionParamDTO();
        param.setOrderId("fhOrderId");
        param.setPolicyNo("policyNo");
        param.setPolicyStatus("policyStatus");
        param.setMatchingTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        param.setPeriodNum(0);
        param.setAmount(new BigDecimal("0.00"));
        param.setProductId(0);
        final CalcCommissionParamDTO.CalcItemDTO calcItemDTO = new CalcCommissionParamDTO.CalcItemDTO();
        calcItemDTO.setInsuredIdNumber("insuredIdNumber");
        calcItemDTO.setPlanId(0);
        calcItemDTO.setRiskAmount(new BigDecimal("0.00"));
        calcItemDTO.setRiskId(0);
        calcItemDTO.setRiskType(0);
        calcItemDTO.setPayWay("payWay");
        calcItemDTO.setCoveredYears("coveredYears");
        calcItemDTO.setValidPeriod("validPeriod");
        param.setCalcItemDTOList(Arrays.asList(calcItemDTO));

        // Configure CommissionConfigQueryService.listConfigByProductId(...).
        final SystemCommissionConfig systemCommissionConfig = new SystemCommissionConfig();
        systemCommissionConfig.setId(0);
        systemCommissionConfig.setType(0);
        systemCommissionConfig.setProductId(0);
        systemCommissionConfig.setStartTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        systemCommissionConfig.setEndTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        systemCommissionConfig.setInsureTypeSame(0);
        final List<SystemCommissionConfig> systemCommissionConfigs = Arrays.asList(systemCommissionConfig);
        mockCommissionConfigQueryService.listConfigByProductId(0);

        // Configure CommissionConfigQueryService.listDetailByConfigId(...).
        final CommissionConfigDetailDTO commissionConfigDetailDTO = new CommissionConfigDetailDTO();
        commissionConfigDetailDTO.setId(0);
        commissionConfigDetailDTO.setConfigId(0);
        commissionConfigDetailDTO.setPeriodNum(0);
        commissionConfigDetailDTO.setPlanId(0);
        commissionConfigDetailDTO.setRiskId(0);
        commissionConfigDetailDTO.setCommissionRate(new BigDecimal("0.00"));
        commissionConfigDetailDTO.setPayWayList(Arrays.asList(0));
        commissionConfigDetailDTO.setCoveredYearsList(Arrays.asList("value"));
        commissionConfigDetailDTO.setValidPeriodList(Arrays.asList("value"));
        final List<CommissionConfigDetailDTO> commissionConfigDetailDTOS = Arrays.asList(commissionConfigDetailDTO);
        mockCommissionConfigQueryService.listDetailByConfigId(0);

        // Run the test
        commissionCalcServiceUnderTest.calcOrderCommission(param);
    }

    @Test
    public void testCalcOrderCommission_CommissionConfigQueryServiceListConfigByProductIdReturnsNoItems() {
        // Setup
        final CalcCommissionParamDTO param = new CalcCommissionParamDTO();
        param.setOrderId("fhOrderId");
        param.setPolicyNo("policyNo");
        param.setPolicyStatus("policyStatus");
        param.setMatchingTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        param.setPeriodNum(0);
        param.setAmount(new BigDecimal("0.00"));
        param.setProductId(0);
        final CalcCommissionParamDTO.CalcItemDTO calcItemDTO = new CalcCommissionParamDTO.CalcItemDTO();
        calcItemDTO.setInsuredIdNumber("insuredIdNumber");
        calcItemDTO.setPlanId(0);
        calcItemDTO.setRiskAmount(new BigDecimal("0.00"));
        calcItemDTO.setRiskId(0);
        calcItemDTO.setRiskType(0);
        calcItemDTO.setPayWay("payWay");
        calcItemDTO.setCoveredYears("coveredYears");
        calcItemDTO.setValidPeriod("validPeriod");
        param.setCalcItemDTOList(Arrays.asList(calcItemDTO));

        mockCommissionConfigQueryService.listConfigByProductId(0);

        // Run the test
        commissionCalcServiceUnderTest.calcOrderCommission(param);
    }

    @Test
    public void testCalcOrderCommission_CommissionConfigQueryServiceListDetailByConfigIdReturnsNoItems() {
        // Setup
        final CalcCommissionParamDTO param = new CalcCommissionParamDTO();
        param.setOrderId("fhOrderId");
        param.setPolicyNo("policyNo");
        param.setPolicyStatus("policyStatus");
        param.setMatchingTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        param.setPeriodNum(0);
        param.setAmount(new BigDecimal("0.00"));
        param.setProductId(0);
        final CalcCommissionParamDTO.CalcItemDTO calcItemDTO = new CalcCommissionParamDTO.CalcItemDTO();
        calcItemDTO.setInsuredIdNumber("insuredIdNumber");
        calcItemDTO.setPlanId(0);
        calcItemDTO.setRiskAmount(new BigDecimal("0.00"));
        calcItemDTO.setRiskId(0);
        calcItemDTO.setRiskType(0);
        calcItemDTO.setPayWay("payWay");
        calcItemDTO.setCoveredYears("coveredYears");
        calcItemDTO.setValidPeriod("validPeriod");
        param.setCalcItemDTOList(Arrays.asList(calcItemDTO));

        final CalcCommissionResultDTO calcCommissionResultDTO = new CalcCommissionResultDTO();
        calcCommissionResultDTO.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        calcCommissionResultDTO.setOrderId("fhOrderId");
        calcCommissionResultDTO.setPolicyNo("policyNo");
        calcCommissionResultDTO.setPolicyStatus("appStatus");
        calcCommissionResultDTO.setInsuredIdNumber("insuredIdNumber");
        calcCommissionResultDTO.setPlanId(0);
        calcCommissionResultDTO.setAmount(new BigDecimal("0.00"));
        calcCommissionResultDTO.setRiskId(0);
        final CommissionDTO commissionDTO = new CommissionDTO();
        commissionDTO.setType(0);
        commissionDTO.setCommissionId(0);
        commissionDTO.setCommissionRate(new BigDecimal("0.00"));
        commissionDTO.setCommissionAmt(new BigDecimal("0.00"));
        commissionDTO.setAmount(new BigDecimal("0.00"));
        calcCommissionResultDTO.setCommissionList(Arrays.asList(commissionDTO));
        final List<CalcCommissionResultDTO> expectedResult = Arrays.asList(calcCommissionResultDTO);

        // Configure CommissionConfigQueryService.listConfigByProductId(...).
        final SystemCommissionConfig systemCommissionConfig = new SystemCommissionConfig();
        systemCommissionConfig.setId(0);
        systemCommissionConfig.setType(0);
        systemCommissionConfig.setProductId(0);
        systemCommissionConfig.setStartTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        systemCommissionConfig.setEndTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        systemCommissionConfig.setInsureTypeSame(0);
        final List<SystemCommissionConfig> systemCommissionConfigs = Arrays.asList(systemCommissionConfig);
        mockCommissionConfigQueryService.listConfigByProductId(0);

        mockCommissionConfigQueryService.listDetailByConfigId(0);

        // Run the test
        final List<CalcCommissionResultDTO> result = commissionCalcServiceUnderTest.calcOrderCommission(param);
    }
}
