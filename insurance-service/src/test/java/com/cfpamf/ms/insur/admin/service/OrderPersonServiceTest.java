package com.cfpamf.ms.insur.admin.service;

import com.cfpamf.ms.insur.admin.dao.safes.AuthUserMapper;
import com.cfpamf.ms.insur.admin.dao.safes.app.CuserShareMapper;
import com.cfpamf.ms.insur.admin.dao.safes.app.SmOrderRelateCappUserMapper;
import com.cfpamf.ms.insur.admin.dao.safes.order.SmOrderApplicantMapper;
import com.cfpamf.ms.insur.admin.dao.safes.order.SmOrderDDDMapper;
import com.cfpamf.ms.insur.admin.dao.safes.order.SmOrderInsuredMapper;
import com.cfpamf.ms.insur.admin.dao.safes.order.SmOrderShareMapper;
import com.cfpamf.ms.insur.admin.pojo.dto.order.OrderPerson;
import com.cfpamf.ms.insur.admin.pojo.dto.order.SimpleOrderDTO;
import com.cfpamf.ms.insur.admin.pojo.po.app.SmOrderRelateCappShareUser;
import com.cfpamf.ms.insur.base.util.HttpRequestUtil;
import com.github.jsonzou.jmockdata.JMockData;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.List;

/**
 * <AUTHOR> 2022/5/6 16:32
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({HttpRequestUtil.class})
public class OrderPersonServiceTest {

    @InjectMocks
    OrderPersonService orderService;
    @Mock
    SmOrderApplicantMapper applicantMapper;
    @Mock
    SmOrderInsuredMapper insuredMapper;
    @Mock
    SmOrderShareMapper orderShareMapper;
    @Mock
    SmOrderRelateCappUserMapper cappUserMapper;
    @Mock
    CuserShareMapper cuserShareMapper;
    @Mock
    SmOrderDDDMapper orderDDDMapper;
    @Mock
    AuthUserMapper userMapper;

    @Test
    public void persons() {
        SmOrderRelateCappShareUser mock = JMockData.mock(SmOrderRelateCappShareUser.class);
        mock.setShareCappUserId("1234");
        Mockito.when(cappUserMapper.queryByFhOrderId(Mockito.any()))
                .thenReturn(mock);
        OrderPerson person = orderService.persons("tset");
        Assert.assertNotNull(person);
    }

    @Test
    public void select() {

        List<SimpleOrderDTO> tset = orderService.selectByIdNumber("tset");
        Assert.assertEquals(true, tset.size() > 0);
    }
}
