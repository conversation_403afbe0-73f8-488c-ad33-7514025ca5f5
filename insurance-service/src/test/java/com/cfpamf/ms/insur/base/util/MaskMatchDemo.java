package com.cfpamf.ms.insur.base.util;

/**
 * 脱敏匹配算法演示程序
 * 展示不同脱敏情况下的匹配效果
 * 
 * <AUTHOR>
 */
public class MaskMatchDemo {
    
    public static void main(String[] args) {
        System.out.println("=== 脱敏匹配算法演示 ===\n");
        
        // 演示姓名匹配
        demonstrateNameMatching();
        
        // 演示证件号匹配
        demonstrateIdNumberMatching();
        
        // 演示综合匹配
        demonstratePersonInfoMatching();
        
        // 演示真实场景
        demonstrateRealWorldScenarios();
    }
    
    private static void demonstrateNameMatching() {
        System.out.println("1. 姓名匹配演示：");
        System.out.println("----------------------------------------");
        
        String[][] nameTests = {
            {"张三", "张三", "完全相同"},
            {"*三", "张三", "首字脱敏"},
            {"张*", "张三", "末字脱敏"},
            {"*阿三", "张阿三", "首字脱敏（三字姓名）"},
            {"张*三", "张阿三", "中间脱敏"},
            {"**三", "张阿三", "多字脱敏"},
            {"*阿*", "张阿三", "首末脱敏"},
            {"***", "张阿三", "全部脱敏"},
            {"张三", "李四", "完全不匹配"},
            {"*四", "张三", "脱敏不匹配"}
        };
        
        for (String[] test : nameTests) {
            boolean result = MaskMatchUtil.matchMaskedName(test[0], test[1]);
            System.out.printf("%-8s vs %-8s => %-6s (%s)\n", 
                test[0], test[1], result ? "匹配" : "不匹配", test[2]);
        }
        System.out.println();
    }
    
    private static void demonstrateIdNumberMatching() {
        System.out.println("2. 车牌号匹配演示：");
        System.out.println("----------------------------------------");

        String[][] plateTests = {
            {"甘A88OW", "甘A88OW", "完全相同"},
            {"*A**OW", "甘A88OW", "首末脱敏"},
            {"甘*8OW", "甘A88OW", "中间脱敏"},
            {"甘A*8*W", "甘A88OW", "多位脱敏"},
            {"*A88OW", "甘A88OW", "首位脱敏"},
            {"甘A88*", "甘A88OW", "末位脱敏"},
            {"*B**OW", "甘A88OW", "脱敏不匹配"},
            {"京A88OW", "甘A88OW", "地区不匹配"},
            {"甘AD88OW", "甘AD88OW", "新能源车牌"},
            {"甘A*88OW", "甘AD88OW", "新能源车牌脱敏"}
        };

        for (String[] test : plateTests) {
            boolean result = MaskMatchUtil.matchMaskedPlateNumber(test[0], test[1]);
            System.out.printf("%-12s vs %-12s => %-6s (%s)\n",
                test[0], test[1], result ? "匹配" : "不匹配", test[2]);
        }
        System.out.println();
    }
    
    private static void demonstratePersonInfoMatching() {
        System.out.println("3. 投保人姓名+车牌号综合匹配演示：");
        System.out.println("----------------------------------------");

        String[][] personTests = {
            {"张三", "甘A88OW", "张三", "甘A88OW", "完全匹配"},
            {"*三", "*A**OW", "张三", "甘A88OW", "姓名车牌号都脱敏"},
            {"*三", "*A**OW", "张三", "甘A99OW", "姓名匹配车牌号部分匹配"},
            {"李四", "甘A88OW", "张三", "甘A88OW", "姓名不匹配车牌号匹配"},
            {"李四", "*B**OW", "张三", "甘A88OW", "都不匹配"},
            {"张三", "******", "张三", "甘A88OW", "车牌号完全脱敏"},
            {"***", "甘A88OW", "张阿三", "甘A88OW", "姓名完全脱敏"}
        };

        for (String[] test : personTests) {
            boolean result = MaskMatchUtil.matchMaskedPersonInfo(test[0], test[1], test[2], test[3]);
            System.out.printf("(%s,%s) vs (%s,%s) => %-6s (%s)\n",
                test[0], test[1], test[2], test[3], result ? "匹配" : "不匹配", test[4]);
        }
        System.out.println();
    }
    
    private static void demonstrateRealWorldScenarios() {
        System.out.println("4. 真实场景演示：");
        System.out.println("----------------------------------------");
        
        // 场景1：同一公司，脱敏规则一致
        System.out.println("场景1：同一公司，脱敏规则一致");
        boolean result1 = MaskMatchUtil.matchMaskedPersonInfo("*阿三", "*A**OW", "*阿三", "*A**OW");
        System.out.printf("第一年: (*阿三, *A**OW) vs 第二年: (*阿三, *A**OW) => %s\n", result1 ? "匹配" : "不匹配");
        
        // 场景2：换公司，脱敏规则不同
        System.out.println("\n场景2：换公司，脱敏规则不同");
        boolean result2 = MaskMatchUtil.matchMaskedPersonInfo("*阿三", "*A**OW", "张*三", "甘*8OW");
        System.out.printf("第一年: (*阿三, *A**OW) vs 第二年: (张*三, 甘*8OW) => %s\n", result2 ? "匹配" : "不匹配");
        
        // 场景3：一年脱敏一年不脱敏
        System.out.println("\n场景3：一年脱敏一年不脱敏");
        boolean result3 = MaskMatchUtil.matchMaskedPersonInfo("*阿三", "*A**OW", "张阿三", "甘A88OW");
        System.out.printf("第一年: (*阿三, *A**OW) vs 第二年: (张阿三, 甘A88OW) => %s\n", result3 ? "匹配" : "不匹配");
        
        // 场景4：完全不匹配
        System.out.println("\n场景4：完全不匹配");
        boolean result4 = MaskMatchUtil.matchMaskedPersonInfo("*阿三", "*A**OW", "李*四", "京*9OW");
        System.out.printf("第一年: (*阿三, *A**OW) vs 第二年: (李*四, 京*9OW) => %s\n", result4 ? "匹配" : "不匹配");
        
        // 场景5：长度不同的脱敏
        System.out.println("\n场景5：长度不同的脱敏");
        boolean result5 = MaskMatchUtil.matchMaskedPersonInfo("张*", "*A88OW", "张阿三", "甘A88OW");
        System.out.printf("第一年: (张*, *A88OW) vs 第二年: (张阿三, 甘A88OW) => %s\n", result5 ? "匹配" : "不匹配");
        
        System.out.println("\n=== 演示结束 ===");
    }
}
