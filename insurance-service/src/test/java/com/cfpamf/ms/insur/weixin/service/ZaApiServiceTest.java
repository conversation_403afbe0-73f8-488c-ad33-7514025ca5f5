package com.cfpamf.ms.insur.weixin.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.cfpamf.ms.insur.admin.external.common.CompanyErrorModel;
import com.cfpamf.ms.insur.base.exception.BizException;
import com.cfpamf.ms.insur.base.exception.ExcptEnum;
import com.cfpamf.ms.insur.weixin.pojo.vo.apply.zhongan.request.ZaEPolicyReq;
import com.cfpamf.ms.insur.weixin.pojo.vo.apply.zhongan.request.ZaEPolicyResp;
import com.cfpamf.ms.insur.weixin.pojo.vo.apply.zhongan.request.ZaEndorRevokeResp;
import com.cfpamf.ms.insur.weixin.pojo.vo.apply.zhongan.request.ZaGroupEndorRevoke;
import com.zhongan.scorpoin.common.ZhongAnApiClient;
import com.zhongan.scorpoin.common.dto.CommonRequest;
import com.zhongan.scorpoin.common.dto.CommonResponse;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;

public class ZaApiServiceTest {
    private static String env="dev";
    private static String appKey = "10001";
    private static String secret = "MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAO8h8JCJAMb1nd0uBkzZuWyNnL+atBzJKvIG7escD45ODf0AWKr8vSqLZ01HD86+a496CGjsae6GybK8C1MqiMSwaAsIv31nKD6U8xF607MPrD3r2lyjwUnmqBZY++R6yFNYz9ZDXcdiwCudESRsXunPJq7zfnnglCtEH+qqW8/VAgMBAAECgYEAnVc2gtMyKLbZTPuId65WG7+9oDB5S+ttD1xR1P1cmuRuvcYpkS/Eg6a/rJASLZULDpdbyzWqqaAUPD8QMINvAr3ZtkbwH5R0F/4aqOlx/5B0Okjsp3eSK2bQ8J2m/MmFKZxr6Aily7YUDdxcGcjLizsGi1KDkWS22JRufEeUNA0CQQD+g1XJ7ELqmUtrS4m4XnadB25f0g5QC0tMjoa3d9soMzK3q+Drkv8EZVpGSmSHEo1VlE7HUcnKNvK1BO5Nm4iXAkEA8IeZxaWmEcsRqiuFz8xmYGtKcYTmHgheGF4D+fnnFozSNP+3sS1lfgFQrjUkqUyZOoG1hPc6SDhGS4nbXwiscwJASO+gPR58yrgledkK3ZAMk9GWWtVajqu953GMv7UUU//gD+yspzXX6Q2WgkA9cMvrPtQig1I37sAya5e/JvRkfwJARzzCDEmdP9PW7YFqZjrxb0kXiTuFNAviYnEl2FltWb5nW48JBo6dao5VKONQclvfXfagnjriphUUrLatpB3bhQJAKRfJS6jDAIVKt7So5HOdzk4ipxgrMjG/QtZ1grO+VQectk4+tCwdJhOrr5blvdPQvFVqXBQfXuE7cibZrGs4sQ==";
    static String version="1.0.0";

    public static void main(String[]a){
        ZaEPolicyReq req = new ZaEPolicyReq();
        req.setPolicyNo("HA1100001457023681");
        req.setChannelCode("XIAOJING");
        ZaEPolicyResp resp = queryEPolicyUrl(req);
        System.err.println(JSON.toJSONString(resp));

        revokeEndor();
    }

    public static Boolean revokeEndor() {
        ZaGroupEndorRevoke revoke = new ZaGroupEndorRevoke();
        revoke.setGroupPolicyNo("HA1100001457023681");
        revoke.setEndorsementNo("CS0200002514044365");
        revoke.setChannelCode("XIAOJING");
        ZhongAnApiClient client = new ZhongAnApiClient(env,appKey,secret,version);
        String proxyurl = "zhongan.health.group.endorsement.revoke";

        TypeReference<ZaEndorRevokeResp> tr=new TypeReference<ZaEndorRevokeResp>(){};
        ZaEndorRevokeResp resp = call(client,proxyurl,revoke,tr,false);
        if(resp==null||!resp.isSuccess()){
            throw new BizException(ExcptEnum.ZA_BIZ_ERROR.getCode(),resp.getMsgInfo());
        }
        return Boolean.TRUE;
    }


    public static ZaEPolicyResp queryEPolicyUrl(ZaEPolicyReq req) {
        ZhongAnApiClient client = new ZhongAnApiClient(env,appKey,secret,version);
        String proxyurl = "zhongan.health.policy.queryEPolicyURL";

        TypeReference<ZaEPolicyResp> tr=new TypeReference<ZaEPolicyResp>(){};
        Map<String,Object> paramMap = new HashMap();
        paramMap.put("infoJson",req);
        ZaEPolicyResp resp = call(client,proxyurl,paramMap,tr,false);
        if(resp==null||!resp.isSuccess()){
            throw new BizException(ExcptEnum.ZA_BIZ_ERROR.getCode(),resp.getMsgInfo());
        }
        return resp;
    }

    private static <T extends CompanyErrorModel> T call(ZhongAnApiClient client,
                                                        String serviceName,
                                                        Object body,
                                                        TypeReference<T> resModel, boolean autoThrow) {
        try {
            CommonRequest request = new CommonRequest(serviceName);

            JSONObject jsonObject = (JSONObject) JSONObject.toJSON(body);
            request.setParams(jsonObject);
            CommonResponse call = client.call(request);
            if (StringUtils.isNotBlank(call.getErrorMsg())) {
                throw new BizException(ExcptEnum.FH_ERROR_000099.getCode(), call.getErrorMsg());
            }
            T t = JSON.parseObject(call.getBizContent(), resModel);
            if (!t.isSuccess() && autoThrow) {
                throw new BizException(ExcptEnum.FH_ERROR_000099.getCode(), "[众安]" + t.errorMsg());
            }
            return t;
        } catch (BizException be) {
            throw be;
        } catch (Exception e) {
            throw new BizException(ExcptEnum.FH_ERROR_000099.getCode(), "[众安]" + e.getMessage());
        }
    }
}
