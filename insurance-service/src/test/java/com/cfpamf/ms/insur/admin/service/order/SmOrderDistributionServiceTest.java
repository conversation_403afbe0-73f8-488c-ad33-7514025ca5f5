package com.cfpamf.ms.insur.admin.service.order;

import com.cfpamf.ms.distribution.dto.OrderCommissionDTO;
import com.cfpamf.ms.insur.admin.pojo.po.order.SmOrderDistribution;
import com.cfpamf.ms.insur.admin.pojo.po.order.SmOrderItem;
import com.cfpamf.ms.insur.admin.service.BaseTest;
import com.cfpamf.ms.insur.base.util.HttpRequestUtil;
import com.github.jsonzou.jmockdata.JMockData;
import com.github.jsonzou.jmockdata.TypeReference;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import tk.mybatis.mapper.entity.Config;
import tk.mybatis.mapper.mapperhelper.EntityHelper;

import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR> 2021/12/10 13:55
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({HttpRequestUtil.class})
public class SmOrderDistributionServiceTest extends BaseTest {
    @InjectMocks
    SmOrderDistributionService smOrderDistributionService;
    @org.mockito.Mock
    com.cfpamf.ms.insur.admin.dao.safes.SmOrderMapper mapper;
    @org.mockito.Mock
    com.cfpamf.ms.insur.admin.service.DistributionProxyService distributionProxyService;
    @org.mockito.Mock
    com.cfpamf.ms.insur.admin.dao.safes.order.SmOrderDistributionMapper distributionMapper;
    @org.mockito.Mock
    com.cfpamf.ms.insur.admin.dao.safes.order.SmOrderDistributionRefundMapper distributionRefundMapper;
    @org.mockito.Mock
    com.cfpamf.ms.insur.admin.dao.safes.order.SmOrderShareMapper shareMapper;
    @org.mockito.Mock
    com.cfpamf.ms.insur.admin.service.pco.SmActTalkOrderService actTalkOrderService;
    @org.mockito.Mock
    com.cfpamf.ms.insur.admin.dao.safes.order.SmOrderItemMapper orderItemMapper;

    @Override
    @Before
    public void setUp() throws Exception {
        super.setUp();
        EntityHelper.initEntityNameMap(SmOrderDistribution.class, new Config());
        EntityHelper.initEntityNameMap(SmOrderItem.class, new Config());
    }
    @Test
    public void testAu(){

        AtomicInteger atomicInteger = new AtomicInteger(0);
        atomicInteger.incrementAndGet();
        atomicInteger.incrementAndGet();

        Assert.assertEquals(2, atomicInteger.get());

        atomicInteger.set(0);
        atomicInteger.incrementAndGet();
        atomicInteger.incrementAndGet();
        Assert.assertEquals(2, atomicInteger.get());

    }

    @Test
    public void changeState() {
        List<OrderCommissionDTO> mock = JMockData.mock(new TypeReference<List<OrderCommissionDTO>>() {
        });
        smOrderDistributionService.changeState(mock);
    }

    @Test
    public void pushCancel() {
        try {
            smOrderDistributionService.pushCancel(",Zptlh,");
        } catch (Exception e) {

        }
    }

    @Test
    public void distribution() {
        try {
            smOrderDistributionService.distribution(",OjvDz,");
        } catch (Exception e) {

        }
    }

    @Test
    public void getInfoByOrderId() {
        try {
            smOrderDistributionService.getInfoByOrderId(",rDDLh,");
        } catch (Exception e) {

        }
    }

    @Test
    public void distributionCore() {
        try {
            smOrderDistributionService.distributionCore(",RqHZl,", JMockData.mock(com.cfpamf.ms.insur.admin.pojo.vo.SmOrderListVO.class));
        } catch (Exception e) {

        }
    }

    @Test
    public void cvtOrder() {
        try {
            smOrderDistributionService.cvtOrder(JMockData.mock(com.cfpamf.ms.insur.admin.pojo.vo.SmOrderListVO.class), JMockData.mock(com.cfpamf.ms.insur.admin.pojo.po.order.SmOrderShare.class), JMockData.mock(java.lang.Boolean.class), JMockData.mock(com.cfpamf.ms.insur.admin.pojo.po.order.SmOrderItem.class), 22365);
        } catch (Exception e) {

        }
    }
}
