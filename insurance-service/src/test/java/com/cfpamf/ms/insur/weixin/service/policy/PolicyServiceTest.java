package com.cfpamf.ms.insur.weixin.service.policy;

import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.ms.insur.admin.config.ProductSpecialRuleProperties;
import com.cfpamf.ms.insur.admin.dao.safes.*;
import com.cfpamf.ms.insur.admin.dao.safes.channel.BankMapper;
import com.cfpamf.ms.insur.admin.dao.safes.product.ProductMapper;
import com.cfpamf.ms.insur.admin.external.whale.client.WhaleGroupApi;
import com.cfpamf.ms.insur.admin.external.whale.model.WhaleResp;
import com.cfpamf.ms.insur.admin.pojo.dto.product.PremiumFlow;
import com.cfpamf.ms.insur.admin.pojo.dto.product.SysDutyConfig;
import com.cfpamf.ms.insur.admin.pojo.po.channel.Bank;
import com.cfpamf.ms.insur.admin.pojo.vo.*;
import com.cfpamf.ms.insur.admin.service.OccupationService;
import com.cfpamf.ms.insur.admin.service.SmOrderManageService;
import com.cfpamf.ms.insur.admin.service.SmProductService;
import com.cfpamf.ms.insur.admin.service.sys.SysNotifyService;
import com.cfpamf.ms.insur.base.exception.BizException;
import com.cfpamf.ms.insur.base.util.email.JavaMailHelper;
import com.cfpamf.ms.insur.weixin.dao.safes.*;
import com.cfpamf.ms.insur.weixin.dao.safes.correct.CorrectLogMapper;
import com.cfpamf.ms.insur.weixin.pojo.dto.WxGlProductQuoteDTO;
import com.cfpamf.ms.insur.weixin.pojo.dto.order.*;
import com.cfpamf.ms.insur.weixin.pojo.dto.policy.*;
import com.cfpamf.ms.insur.weixin.pojo.dto.product.*;
import com.cfpamf.ms.insur.weixin.pojo.enums.whale.OperatorSourceTypeEnum;
import com.cfpamf.ms.insur.weixin.pojo.enums.whale.SourceSystemEnum;
import com.cfpamf.ms.insur.weixin.pojo.po.order.PayNotice;
import com.cfpamf.ms.insur.weixin.pojo.query.order.OrderDetailQuery;
import com.cfpamf.ms.insur.weixin.pojo.query.policy.PlanQuery;
import com.cfpamf.ms.insur.weixin.pojo.vo.WxTreeVO;
import com.cfpamf.ms.insur.weixin.pojo.vo.apply.common.*;
import com.cfpamf.ms.insur.weixin.pojo.vo.apply.whale.base.WhalePlatformData;
import com.cfpamf.ms.insur.weixin.pojo.vo.apply.whale.group.form.OuterBank;
import com.cfpamf.ms.insur.weixin.pojo.vo.apply.whale.group.request.GroupOuterBankListInput;
import com.cfpamf.ms.insur.weixin.pojo.vo.apply.whale.group.response.GroupOuterBankListOutput;
import com.cfpamf.ms.insur.weixin.pojo.vo.apply.zhongan.order.ProductReq;
import com.cfpamf.ms.insur.weixin.pojo.vo.group.*;
import com.cfpamf.ms.insur.weixin.pojo.vo.product.*;
import com.cfpamf.ms.insur.weixin.service.underwriting.GroupApplyHandler;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class PolicyServiceTest {

    @Mock
    private PolicyMapper mockPolicyMapper;
    @Mock
    private SmProductVersionMapper mockProductVersionMapper;
    @Mock
    private AuthUserMapper mockUserMapper;
    @Mock
    private SmCommissionMapper mockCommissionMapper;
    @Mock
    private EndorMapper mockEndorMapper;
    @Mock
    private SmOrderMapper mockOrderMapper;
    @Mock
    private WxOrderMapper mockWxOrderMapper;
    @Mock
    private TimeFactorMapper mockTimeFactorMapper;
    @Mock
    private ProductMapper mockProductMapper;
    @Mock
    private GroupApplyHandler mockApplyHandler;
    @Mock
    private SmProductService mockProductService;
    @Mock
    private SysNotifyService mockNotifyService;
    @Mock
    private WhaleGroupApi mockWhaleGroupApi;
    @Mock
    private SmOrderManageService mockOrderManageService;
    @Mock
    private OccupationService mockOccupationService;
    @Mock
    private ProductSpecialRuleProperties mockProductSpecialRuleProperties;
    @Mock
    private InquiryService mockInquiryService;
    @Mock
    private GroupApplyHandler mockGroupApplyHandler;
    @Mock
    private BankMapper mockBankMapper;
    @Mock
    private SmOccupationMapper mockMapper;
    @Mock
    private CorrectLogMapper mockCorrectLogMapper;
    @Mock
    private InvoiceMapper mockInvoiceMapper;
    @Mock
    private InvoiceRelationMapper mockInvoiceRelationMapper;
    @Mock
    private JavaMailHelper mockMailHelper;

    @InjectMocks
    private PolicyService policyServiceUnderTest;

    @Test
    public void testQueryProductInfo() {
        // Setup
        final ProductDetailVo expectedResult = new ProductDetailVo();
        expectedResult.setValidPeriod("validPeriod");
        expectedResult.setPolicyHolderCondition("policyHolderCondition");
        expectedResult.setProductSpecialsJoin(new String[]{"productSpecialsJoin"});
        expectedResult.setProductTags(new String[]{"productTags"});
        final PlanVo planVo = new PlanVo();
        planVo.setId(0);
        planVo.setPlanName("planName");
        planVo.setPlanCode("planCode");
        planVo.setPlanType("planType");
        planVo.setMinPremium(new BigDecimal("0.00"));
        planVo.setCommission(new BigDecimal("0.00"));
        final CoverageVo coverageVo = new CoverageVo();
        coverageVo.setSpcId(0);
        final CoverageAmountVo coverageAmountVo = new CoverageAmountVo();
        coverageAmountVo.setSpcId(0);
        coverageVo.setAmounts(Arrays.asList(coverageAmountVo));
        final DutyFactorFlowVo dutyFactorFlowVo = new DutyFactorFlowVo();
        dutyFactorFlowVo.setSpcId(0);
        dutyFactorFlowVo.setDutyCode("dutyCode");
        dutyFactorFlowVo.setDutyFactorName("dutyFactorName");
        dutyFactorFlowVo.setDutyFactorCode("dutyFactorCode");
        coverageVo.setDutyFlowFactor(Arrays.asList(dutyFactorFlowVo));
        planVo.setCoverages(Arrays.asList(coverageVo));
        expectedResult.setPlans(Arrays.asList(planVo));
        expectedResult.setSalesMode(new String[]{"salesMode"});
        final ShowTips tips = new ShowTips();
        tips.setMessage("此产品暂不支持对公转账，如有此需求请登录i云保进行询价及投保，保费以i云保展示为准。");
        expectedResult.setTips(tips);
        final TimeFactorItem timeFactorItem = new TimeFactorItem();
        timeFactorItem.setFactorValue("enterpriseRiskFactor");
        timeFactorItem.setTimeUnit("unit");
        timeFactorItem.setFlow("flow");
        expectedResult.setTimeFactorItems(Arrays.asList(timeFactorItem));
        expectedResult.setProductVersion("productVersion");

        // Configure PolicyMapper.getProductDetailById(...).
        final ProductDTO productDTO = new ProductDTO();
        productDTO.setProductTags("productTags");
        productDTO.setAttentions("attentions");
        productDTO.setHealthNotification("content");
        productDTO.setProductSpecialsJoin("productSpecialsJoin");
        productDTO.setGlProductNotice("glProductNotice");
        productDTO.setSalesMode("salesMode");
        when(mockPolicyMapper.getProductDetailById(0)).thenReturn(productDTO);

        when(mockProductVersionMapper.getMaxVersion(0)).thenReturn(0);

        // Configure PolicyMapper.getPlans(...).
        final PlanDTO planDTO = new PlanDTO();
        planDTO.setId(0);
        planDTO.setPlanName("planName");
        planDTO.setPlanCode("planCode");
        planDTO.setPlanType("planType");
        planDTO.setMinPremium(new BigDecimal("0.00"));
        final List<PlanDTO> planDTOS = Arrays.asList(planDTO);
        when(mockPolicyMapper.getPlans(0, "region")).thenReturn(planDTOS);

        // Configure SmCommissionMapper.getCommissionSettingByPlanIds(...).
        final SmCommissionSettingVO smCommissionSettingVO = new SmCommissionSettingVO();
        smCommissionSettingVO.setId(0);
        smCommissionSettingVO.setPlanId(0);
        smCommissionSettingVO.setPaymentProportion(new BigDecimal("0.00"));
        smCommissionSettingVO.setSettlementProportion(new BigDecimal("0.00"));
        smCommissionSettingVO.setSaleLevel("saleLevel");
        final List<SmCommissionSettingVO> smCommissionSettingVOS = Arrays.asList(smCommissionSettingVO);
        when(mockCommissionMapper.getCommissionSettingByPlanIds(Arrays.asList(0),
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).thenReturn(smCommissionSettingVOS);

        // Configure PolicyMapper.getCoverage(...).
        final CoverageVo coverageVo1 = new CoverageVo();
        coverageVo1.setSpcId(0);
        final CoverageAmountVo coverageAmountVo1 = new CoverageAmountVo();
        coverageAmountVo1.setSpcId(0);
        coverageVo1.setAmounts(Arrays.asList(coverageAmountVo1));
        final DutyFactorFlowVo dutyFactorFlowVo1 = new DutyFactorFlowVo();
        dutyFactorFlowVo1.setSpcId(0);
        dutyFactorFlowVo1.setDutyCode("dutyCode");
        dutyFactorFlowVo1.setDutyFactorName("dutyFactorName");
        dutyFactorFlowVo1.setDutyFactorCode("dutyFactorCode");
        coverageVo1.setDutyFlowFactor(Arrays.asList(dutyFactorFlowVo1));
        final List<CoverageVo> coverageVos = Arrays.asList(coverageVo1);
        when(mockPolicyMapper.getCoverage(0)).thenReturn(coverageVos);

        // Configure PolicyMapper.getCoverageAmounts(...).
        final CoverageAmountVo coverageAmountVo2 = new CoverageAmountVo();
        coverageAmountVo2.setSpcaId(0);
        coverageAmountVo2.setSpcId(0);
        coverageAmountVo2.setCvgAmount(new BigDecimal("0.00"));
        coverageAmountVo2.setCvgNotice("cvgNotice");
        final List<CoverageAmountVo> coverageAmountVos = Arrays.asList(coverageAmountVo2);
        when(mockPolicyMapper.getCoverageAmounts(0)).thenReturn(coverageAmountVos);

        // Configure PolicyMapper.getDutyFactorFlows(...).
        final DutyFactorFlowDTO dutyFactorFlowDTO = new DutyFactorFlowDTO();
        dutyFactorFlowDTO.setId(0);
        dutyFactorFlowDTO.setSpcId(0);
        dutyFactorFlowDTO.setDutyCode("dutyCode");
        dutyFactorFlowDTO.setDutyFactorCode("dutyFactorCode");
        dutyFactorFlowDTO.setDutyFactorName("dutyFactorName");
        dutyFactorFlowDTO.setFactorValue("factorValue");
        dutyFactorFlowDTO.setFactorName("factorName");
        dutyFactorFlowDTO.setOptionName("optionName");
        dutyFactorFlowDTO.setFlow("flow");
        dutyFactorFlowDTO.setIsDefault(0);
        final List<DutyFactorFlowDTO> dutyFactorFlowDTOS = Arrays.asList(dutyFactorFlowDTO);
        when(mockPolicyMapper.getDutyFactorFlows(0)).thenReturn(dutyFactorFlowDTOS);

        // Configure PolicyMapper.queryQuoteLimit(...).
        final QuoteLimitDTO quoteLimitDTO = new QuoteLimitDTO();
        quoteLimitDTO.setSpqlId(0);
        quoteLimitDTO.setProductId(0);
        quoteLimitDTO.setLimitType("limitType");
        quoteLimitDTO.setOccupationGroup("occupationGroup");
        quoteLimitDTO.setMinPerQty(0);
        final List<QuoteLimitDTO> quoteLimitDTOS = Arrays.asList(quoteLimitDTO);
        when(mockPolicyMapper.queryQuoteLimit(0)).thenReturn(quoteLimitDTOS);

        // Configure ProductMapper.queryPremiumFactor(...).
        final PremiumFlow premiumFlow = new PremiumFlow();
        premiumFlow.setId(0);
        premiumFlow.setProductId(0);
        premiumFlow.setPlanId(0);
        premiumFlow.setFactorValue("enterpriseRiskFactor");
        premiumFlow.setFlow("flow");
        final List<PremiumFlow> premiumFlows = Arrays.asList(premiumFlow);
        when(mockProductMapper.queryPremiumFactor(0, 0, "channelCode")).thenReturn(premiumFlows);

        policyServiceUnderTest.queryProductInfo(0, "region");
        //该方法没有复杂的逻辑：验证流程没有空指针即可
        Assert.assertTrue(Boolean.TRUE);
    }

    @Test
    public void testQueryProductInfo_PolicyMapperGetProductDetailByIdReturnsNull() {
        // Setup
        when(mockPolicyMapper.getProductDetailById(0)).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> policyServiceUnderTest.queryProductInfo(0, "region")).isInstanceOf(BizException.class);
    }

    @Test
    public void testQueryProductInfo_SmProductVersionMapperReturnsNull() {
        // Setup
        final ProductDetailVo expectedResult = new ProductDetailVo();
        expectedResult.setValidPeriod("validPeriod");
        expectedResult.setPolicyHolderCondition("policyHolderCondition");
        expectedResult.setProductSpecialsJoin(new String[]{"productSpecialsJoin"});
        expectedResult.setProductTags(new String[]{"productTags"});
        final PlanVo planVo = new PlanVo();
        planVo.setId(0);
        planVo.setPlanName("planName");
        planVo.setPlanCode("planCode");
        planVo.setPlanType("planType");
        planVo.setMinPremium(new BigDecimal("0.00"));
        planVo.setCommission(new BigDecimal("0.00"));
        final CoverageVo coverageVo = new CoverageVo();
        coverageVo.setSpcId(0);
        final CoverageAmountVo coverageAmountVo = new CoverageAmountVo();
        coverageAmountVo.setSpcId(0);
        coverageVo.setAmounts(Arrays.asList(coverageAmountVo));
        final DutyFactorFlowVo dutyFactorFlowVo = new DutyFactorFlowVo();
        dutyFactorFlowVo.setSpcId(0);
        dutyFactorFlowVo.setDutyCode("dutyCode");
        dutyFactorFlowVo.setDutyFactorName("dutyFactorName");
        dutyFactorFlowVo.setDutyFactorCode("dutyFactorCode");
        coverageVo.setDutyFlowFactor(Arrays.asList(dutyFactorFlowVo));
        planVo.setCoverages(Arrays.asList(coverageVo));
        expectedResult.setPlans(Arrays.asList(planVo));
        expectedResult.setSalesMode(new String[]{"salesMode"});
        final ShowTips tips = new ShowTips();
        tips.setMessage("此产品暂不支持对公转账，如有此需求请登录i云保进行询价及投保，保费以i云保展示为准。");
        expectedResult.setTips(tips);
        final TimeFactorItem timeFactorItem = new TimeFactorItem();
        timeFactorItem.setFactorValue("enterpriseRiskFactor");
        timeFactorItem.setTimeUnit("unit");
        timeFactorItem.setFlow("flow");
        expectedResult.setTimeFactorItems(Arrays.asList(timeFactorItem));
        expectedResult.setProductVersion("productVersion");

        // Configure PolicyMapper.getProductDetailById(...).
        final ProductDTO productDTO = new ProductDTO();
        productDTO.setProductTags("productTags");
        productDTO.setAttentions("attentions");
        productDTO.setHealthNotification("content");
        productDTO.setProductSpecialsJoin("productSpecialsJoin");
        productDTO.setGlProductNotice("glProductNotice");
        productDTO.setSalesMode("salesMode");
        when(mockPolicyMapper.getProductDetailById(0)).thenReturn(productDTO);

        when(mockProductVersionMapper.getMaxVersion(0)).thenReturn(null);

        // Configure PolicyMapper.getPlans(...).
        final PlanDTO planDTO = new PlanDTO();
        planDTO.setId(0);
        planDTO.setPlanName("planName");
        planDTO.setPlanCode("planCode");
        planDTO.setPlanType("planType");
        planDTO.setMinPremium(new BigDecimal("0.00"));
        final List<PlanDTO> planDTOS = Arrays.asList(planDTO);
        when(mockPolicyMapper.getPlans(0, "region")).thenReturn(planDTOS);

        // Configure SmCommissionMapper.getCommissionSettingByPlanIds(...).
        final SmCommissionSettingVO smCommissionSettingVO = new SmCommissionSettingVO();
        smCommissionSettingVO.setId(0);
        smCommissionSettingVO.setPlanId(0);
        smCommissionSettingVO.setPaymentProportion(new BigDecimal("0.00"));
        smCommissionSettingVO.setSettlementProportion(new BigDecimal("0.00"));
        smCommissionSettingVO.setSaleLevel("saleLevel");
        final List<SmCommissionSettingVO> smCommissionSettingVOS = Arrays.asList(smCommissionSettingVO);
        when(mockCommissionMapper.getCommissionSettingByPlanIds(Arrays.asList(0),
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).thenReturn(smCommissionSettingVOS);

        // Configure PolicyMapper.getCoverage(...).
        final CoverageVo coverageVo1 = new CoverageVo();
        coverageVo1.setSpcId(0);
        final CoverageAmountVo coverageAmountVo1 = new CoverageAmountVo();
        coverageAmountVo1.setSpcId(0);
        coverageVo1.setAmounts(Arrays.asList(coverageAmountVo1));
        final DutyFactorFlowVo dutyFactorFlowVo1 = new DutyFactorFlowVo();
        dutyFactorFlowVo1.setSpcId(0);
        dutyFactorFlowVo1.setDutyCode("dutyCode");
        dutyFactorFlowVo1.setDutyFactorName("dutyFactorName");
        dutyFactorFlowVo1.setDutyFactorCode("dutyFactorCode");
        coverageVo1.setDutyFlowFactor(Arrays.asList(dutyFactorFlowVo1));
        final List<CoverageVo> coverageVos = Arrays.asList(coverageVo1);
        when(mockPolicyMapper.getCoverage(0)).thenReturn(coverageVos);

        // Configure PolicyMapper.getCoverageAmounts(...).
        final CoverageAmountVo coverageAmountVo2 = new CoverageAmountVo();
        coverageAmountVo2.setSpcaId(0);
        coverageAmountVo2.setSpcId(0);
        coverageAmountVo2.setCvgAmount(new BigDecimal("0.00"));
        coverageAmountVo2.setCvgNotice("cvgNotice");
        final List<CoverageAmountVo> coverageAmountVos = Arrays.asList(coverageAmountVo2);
        when(mockPolicyMapper.getCoverageAmounts(0)).thenReturn(coverageAmountVos);

        // Configure PolicyMapper.getDutyFactorFlows(...).
        final DutyFactorFlowDTO dutyFactorFlowDTO = new DutyFactorFlowDTO();
        dutyFactorFlowDTO.setId(0);
        dutyFactorFlowDTO.setSpcId(0);
        dutyFactorFlowDTO.setDutyCode("dutyCode");
        dutyFactorFlowDTO.setDutyFactorCode("dutyFactorCode");
        dutyFactorFlowDTO.setDutyFactorName("dutyFactorName");
        dutyFactorFlowDTO.setFactorValue("factorValue");
        dutyFactorFlowDTO.setFactorName("factorName");
        dutyFactorFlowDTO.setOptionName("optionName");
        dutyFactorFlowDTO.setFlow("flow");
        dutyFactorFlowDTO.setIsDefault(0);
        final List<DutyFactorFlowDTO> dutyFactorFlowDTOS = Arrays.asList(dutyFactorFlowDTO);
        when(mockPolicyMapper.getDutyFactorFlows(0)).thenReturn(dutyFactorFlowDTOS);

        // Configure PolicyMapper.queryQuoteLimit(...).
        final QuoteLimitDTO quoteLimitDTO = new QuoteLimitDTO();
        quoteLimitDTO.setSpqlId(0);
        quoteLimitDTO.setProductId(0);
        quoteLimitDTO.setLimitType("limitType");
        quoteLimitDTO.setOccupationGroup("occupationGroup");
        quoteLimitDTO.setMinPerQty(0);
        final List<QuoteLimitDTO> quoteLimitDTOS = Arrays.asList(quoteLimitDTO);
        when(mockPolicyMapper.queryQuoteLimit(0)).thenReturn(quoteLimitDTOS);

        // Configure ProductMapper.queryPremiumFactor(...).
        final PremiumFlow premiumFlow = new PremiumFlow();
        premiumFlow.setId(0);
        premiumFlow.setProductId(0);
        premiumFlow.setPlanId(0);
        premiumFlow.setFactorValue("enterpriseRiskFactor");
        premiumFlow.setFlow("flow");
        final List<PremiumFlow> premiumFlows = Arrays.asList(premiumFlow);
        when(mockProductMapper.queryPremiumFactor(0, 0, "channelCode")).thenReturn(premiumFlows);

        // Run the test
        policyServiceUnderTest.queryProductInfo(0, "region");
    }

    @Test
    public void testQueryProductInfo_PolicyMapperGetPlansReturnsNoItems() {
        // Setup
        final ProductDetailVo expectedResult = new ProductDetailVo();
        expectedResult.setValidPeriod("validPeriod");
        expectedResult.setPolicyHolderCondition("policyHolderCondition");
        expectedResult.setProductSpecialsJoin(new String[]{"productSpecialsJoin"});
        expectedResult.setProductTags(new String[]{"productTags"});
        final PlanVo planVo = new PlanVo();
        planVo.setId(0);
        planVo.setPlanName("planName");
        planVo.setPlanCode("planCode");
        planVo.setPlanType("planType");
        planVo.setMinPremium(new BigDecimal("0.00"));
        planVo.setCommission(new BigDecimal("0.00"));
        final CoverageVo coverageVo = new CoverageVo();
        coverageVo.setSpcId(0);
        final CoverageAmountVo coverageAmountVo = new CoverageAmountVo();
        coverageAmountVo.setSpcId(0);
        coverageVo.setAmounts(Arrays.asList(coverageAmountVo));
        final DutyFactorFlowVo dutyFactorFlowVo = new DutyFactorFlowVo();
        dutyFactorFlowVo.setSpcId(0);
        dutyFactorFlowVo.setDutyCode("dutyCode");
        dutyFactorFlowVo.setDutyFactorName("dutyFactorName");
        dutyFactorFlowVo.setDutyFactorCode("dutyFactorCode");
        coverageVo.setDutyFlowFactor(Arrays.asList(dutyFactorFlowVo));
        planVo.setCoverages(Arrays.asList(coverageVo));
        expectedResult.setPlans(Arrays.asList(planVo));
        expectedResult.setSalesMode(new String[]{"salesMode"});
        final ShowTips tips = new ShowTips();
        tips.setMessage("此产品暂不支持对公转账，如有此需求请登录i云保进行询价及投保，保费以i云保展示为准。");
        expectedResult.setTips(tips);
        final TimeFactorItem timeFactorItem = new TimeFactorItem();
        timeFactorItem.setFactorValue("enterpriseRiskFactor");
        timeFactorItem.setTimeUnit("unit");
        timeFactorItem.setFlow("flow");
        expectedResult.setTimeFactorItems(Arrays.asList(timeFactorItem));
        expectedResult.setProductVersion("productVersion");

        // Configure PolicyMapper.getProductDetailById(...).
        final ProductDTO productDTO = new ProductDTO();
        productDTO.setProductTags("productTags");
        productDTO.setAttentions("attentions");
        productDTO.setHealthNotification("content");
        productDTO.setProductSpecialsJoin("productSpecialsJoin");
        productDTO.setGlProductNotice("glProductNotice");
        productDTO.setSalesMode("salesMode");
        when(mockPolicyMapper.getProductDetailById(0)).thenReturn(productDTO);

        when(mockProductVersionMapper.getMaxVersion(0)).thenReturn(0);
        when(mockPolicyMapper.getPlans(0, "region")).thenReturn(Collections.emptyList());

        // Configure SmCommissionMapper.getCommissionSettingByPlanIds(...).
        final SmCommissionSettingVO smCommissionSettingVO = new SmCommissionSettingVO();
        smCommissionSettingVO.setId(0);
        smCommissionSettingVO.setPlanId(0);
        smCommissionSettingVO.setPaymentProportion(new BigDecimal("0.00"));
        smCommissionSettingVO.setSettlementProportion(new BigDecimal("0.00"));
        smCommissionSettingVO.setSaleLevel("saleLevel");
        final List<SmCommissionSettingVO> smCommissionSettingVOS = Arrays.asList(smCommissionSettingVO);
        when(mockCommissionMapper.getCommissionSettingByPlanIds(Arrays.asList(0),
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).thenReturn(smCommissionSettingVOS);

        // Configure PolicyMapper.getCoverage(...).
        final CoverageVo coverageVo1 = new CoverageVo();
        coverageVo1.setSpcId(0);
        final CoverageAmountVo coverageAmountVo1 = new CoverageAmountVo();
        coverageAmountVo1.setSpcId(0);
        coverageVo1.setAmounts(Arrays.asList(coverageAmountVo1));
        final DutyFactorFlowVo dutyFactorFlowVo1 = new DutyFactorFlowVo();
        dutyFactorFlowVo1.setSpcId(0);
        dutyFactorFlowVo1.setDutyCode("dutyCode");
        dutyFactorFlowVo1.setDutyFactorName("dutyFactorName");
        dutyFactorFlowVo1.setDutyFactorCode("dutyFactorCode");
        coverageVo1.setDutyFlowFactor(Arrays.asList(dutyFactorFlowVo1));
        final List<CoverageVo> coverageVos = Arrays.asList(coverageVo1);
        when(mockPolicyMapper.getCoverage(0)).thenReturn(coverageVos);

        // Configure PolicyMapper.getCoverageAmounts(...).
        final CoverageAmountVo coverageAmountVo2 = new CoverageAmountVo();
        coverageAmountVo2.setSpcaId(0);
        coverageAmountVo2.setSpcId(0);
        coverageAmountVo2.setCvgAmount(new BigDecimal("0.00"));
        coverageAmountVo2.setCvgNotice("cvgNotice");
        final List<CoverageAmountVo> coverageAmountVos = Arrays.asList(coverageAmountVo2);
        when(mockPolicyMapper.getCoverageAmounts(0)).thenReturn(coverageAmountVos);

        // Configure PolicyMapper.getDutyFactorFlows(...).
        final DutyFactorFlowDTO dutyFactorFlowDTO = new DutyFactorFlowDTO();
        dutyFactorFlowDTO.setId(0);
        dutyFactorFlowDTO.setSpcId(0);
        dutyFactorFlowDTO.setDutyCode("dutyCode");
        dutyFactorFlowDTO.setDutyFactorCode("dutyFactorCode");
        dutyFactorFlowDTO.setDutyFactorName("dutyFactorName");
        dutyFactorFlowDTO.setFactorValue("factorValue");
        dutyFactorFlowDTO.setFactorName("factorName");
        dutyFactorFlowDTO.setOptionName("optionName");
        dutyFactorFlowDTO.setFlow("flow");
        dutyFactorFlowDTO.setIsDefault(0);
        final List<DutyFactorFlowDTO> dutyFactorFlowDTOS = Arrays.asList(dutyFactorFlowDTO);
        when(mockPolicyMapper.getDutyFactorFlows(0)).thenReturn(dutyFactorFlowDTOS);

        // Configure PolicyMapper.queryQuoteLimit(...).
        final QuoteLimitDTO quoteLimitDTO = new QuoteLimitDTO();
        quoteLimitDTO.setSpqlId(0);
        quoteLimitDTO.setProductId(0);
        quoteLimitDTO.setLimitType("limitType");
        quoteLimitDTO.setOccupationGroup("occupationGroup");
        quoteLimitDTO.setMinPerQty(0);
        final List<QuoteLimitDTO> quoteLimitDTOS = Arrays.asList(quoteLimitDTO);
        when(mockPolicyMapper.queryQuoteLimit(0)).thenReturn(quoteLimitDTOS);

        // Configure ProductMapper.queryPremiumFactor(...).
        final PremiumFlow premiumFlow = new PremiumFlow();
        premiumFlow.setId(0);
        premiumFlow.setProductId(0);
        premiumFlow.setPlanId(0);
        premiumFlow.setFactorValue("enterpriseRiskFactor");
        premiumFlow.setFlow("flow");
        final List<PremiumFlow> premiumFlows = Arrays.asList(premiumFlow);
        when(mockProductMapper.queryPremiumFactor(0, 0, "channelCode")).thenReturn(premiumFlows);

        // Run the test
        final ProductDetailVo result = policyServiceUnderTest.queryProductInfo(0, "region");

    }

    @Test
    public void testQueryProductInfo_SmCommissionMapperReturnsNull() {
        // Setup
        final ProductDetailVo expectedResult = new ProductDetailVo();
        expectedResult.setValidPeriod("validPeriod");
        expectedResult.setPolicyHolderCondition("policyHolderCondition");
        expectedResult.setProductSpecialsJoin(new String[]{"productSpecialsJoin"});
        expectedResult.setProductTags(new String[]{"productTags"});
        final PlanVo planVo = new PlanVo();
        planVo.setId(0);
        planVo.setPlanName("planName");
        planVo.setPlanCode("planCode");
        planVo.setPlanType("planType");
        planVo.setMinPremium(new BigDecimal("0.00"));
        planVo.setCommission(new BigDecimal("0.00"));
        final CoverageVo coverageVo = new CoverageVo();
        coverageVo.setSpcId(0);
        final CoverageAmountVo coverageAmountVo = new CoverageAmountVo();
        coverageAmountVo.setSpcId(0);
        coverageVo.setAmounts(Arrays.asList(coverageAmountVo));
        final DutyFactorFlowVo dutyFactorFlowVo = new DutyFactorFlowVo();
        dutyFactorFlowVo.setSpcId(0);
        dutyFactorFlowVo.setDutyCode("dutyCode");
        dutyFactorFlowVo.setDutyFactorName("dutyFactorName");
        dutyFactorFlowVo.setDutyFactorCode("dutyFactorCode");
        coverageVo.setDutyFlowFactor(Arrays.asList(dutyFactorFlowVo));
        planVo.setCoverages(Arrays.asList(coverageVo));
        expectedResult.setPlans(Arrays.asList(planVo));
        expectedResult.setSalesMode(new String[]{"salesMode"});
        final ShowTips tips = new ShowTips();
        tips.setMessage("此产品暂不支持对公转账，如有此需求请登录i云保进行询价及投保，保费以i云保展示为准。");
        expectedResult.setTips(tips);
        final TimeFactorItem timeFactorItem = new TimeFactorItem();
        timeFactorItem.setFactorValue("enterpriseRiskFactor");
        timeFactorItem.setTimeUnit("unit");
        timeFactorItem.setFlow("flow");
        expectedResult.setTimeFactorItems(Arrays.asList(timeFactorItem));
        expectedResult.setProductVersion("productVersion");

        // Configure PolicyMapper.getProductDetailById(...).
        final ProductDTO productDTO = new ProductDTO();
        productDTO.setProductTags("productTags");
        productDTO.setAttentions("attentions");
        productDTO.setHealthNotification("content");
        productDTO.setProductSpecialsJoin("productSpecialsJoin");
        productDTO.setGlProductNotice("glProductNotice");
        productDTO.setSalesMode("salesMode");
        when(mockPolicyMapper.getProductDetailById(0)).thenReturn(productDTO);

        when(mockProductVersionMapper.getMaxVersion(0)).thenReturn(0);

        // Configure PolicyMapper.getPlans(...).
        final PlanDTO planDTO = new PlanDTO();
        planDTO.setId(0);
        planDTO.setPlanName("planName");
        planDTO.setPlanCode("planCode");
        planDTO.setPlanType("planType");
        planDTO.setMinPremium(new BigDecimal("0.00"));
        final List<PlanDTO> planDTOS = Arrays.asList(planDTO);
        when(mockPolicyMapper.getPlans(0, "region")).thenReturn(planDTOS);

        when(mockCommissionMapper.getCommissionSettingByPlanIds(Arrays.asList(0),
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).thenReturn(null);

        // Configure PolicyMapper.getCoverage(...).
        final CoverageVo coverageVo1 = new CoverageVo();
        coverageVo1.setSpcId(0);
        final CoverageAmountVo coverageAmountVo1 = new CoverageAmountVo();
        coverageAmountVo1.setSpcId(0);
        coverageVo1.setAmounts(Arrays.asList(coverageAmountVo1));
        final DutyFactorFlowVo dutyFactorFlowVo1 = new DutyFactorFlowVo();
        dutyFactorFlowVo1.setSpcId(0);
        dutyFactorFlowVo1.setDutyCode("dutyCode");
        dutyFactorFlowVo1.setDutyFactorName("dutyFactorName");
        dutyFactorFlowVo1.setDutyFactorCode("dutyFactorCode");
        coverageVo1.setDutyFlowFactor(Arrays.asList(dutyFactorFlowVo1));
        final List<CoverageVo> coverageVos = Arrays.asList(coverageVo1);
        when(mockPolicyMapper.getCoverage(0)).thenReturn(coverageVos);

        // Configure PolicyMapper.getCoverageAmounts(...).
        final CoverageAmountVo coverageAmountVo2 = new CoverageAmountVo();
        coverageAmountVo2.setSpcaId(0);
        coverageAmountVo2.setSpcId(0);
        coverageAmountVo2.setCvgAmount(new BigDecimal("0.00"));
        coverageAmountVo2.setCvgNotice("cvgNotice");
        final List<CoverageAmountVo> coverageAmountVos = Arrays.asList(coverageAmountVo2);
        when(mockPolicyMapper.getCoverageAmounts(0)).thenReturn(coverageAmountVos);

        // Configure PolicyMapper.getDutyFactorFlows(...).
        final DutyFactorFlowDTO dutyFactorFlowDTO = new DutyFactorFlowDTO();
        dutyFactorFlowDTO.setId(0);
        dutyFactorFlowDTO.setSpcId(0);
        dutyFactorFlowDTO.setDutyCode("dutyCode");
        dutyFactorFlowDTO.setDutyFactorCode("dutyFactorCode");
        dutyFactorFlowDTO.setDutyFactorName("dutyFactorName");
        dutyFactorFlowDTO.setFactorValue("factorValue");
        dutyFactorFlowDTO.setFactorName("factorName");
        dutyFactorFlowDTO.setOptionName("optionName");
        dutyFactorFlowDTO.setFlow("flow");
        dutyFactorFlowDTO.setIsDefault(0);
        final List<DutyFactorFlowDTO> dutyFactorFlowDTOS = Arrays.asList(dutyFactorFlowDTO);
        when(mockPolicyMapper.getDutyFactorFlows(0)).thenReturn(dutyFactorFlowDTOS);

        // Configure PolicyMapper.queryQuoteLimit(...).
        final QuoteLimitDTO quoteLimitDTO = new QuoteLimitDTO();
        quoteLimitDTO.setSpqlId(0);
        quoteLimitDTO.setProductId(0);
        quoteLimitDTO.setLimitType("limitType");
        quoteLimitDTO.setOccupationGroup("occupationGroup");
        quoteLimitDTO.setMinPerQty(0);
        final List<QuoteLimitDTO> quoteLimitDTOS = Arrays.asList(quoteLimitDTO);
        when(mockPolicyMapper.queryQuoteLimit(0)).thenReturn(quoteLimitDTOS);

        // Configure ProductMapper.queryPremiumFactor(...).
        final PremiumFlow premiumFlow = new PremiumFlow();
        premiumFlow.setId(0);
        premiumFlow.setProductId(0);
        premiumFlow.setPlanId(0);
        premiumFlow.setFactorValue("enterpriseRiskFactor");
        premiumFlow.setFlow("flow");
        final List<PremiumFlow> premiumFlows = Arrays.asList(premiumFlow);
        when(mockProductMapper.queryPremiumFactor(0, 0, "channelCode")).thenReturn(premiumFlows);

        // Run the test
        policyServiceUnderTest.queryProductInfo(0, "region");

    }

    @Test
    public void testQueryProductInfo_SmCommissionMapperReturnsNoItems() {
        // Setup
        final ProductDetailVo expectedResult = new ProductDetailVo();
        expectedResult.setValidPeriod("validPeriod");
        expectedResult.setPolicyHolderCondition("policyHolderCondition");
        expectedResult.setProductSpecialsJoin(new String[]{"productSpecialsJoin"});
        expectedResult.setProductTags(new String[]{"productTags"});
        final PlanVo planVo = new PlanVo();
        planVo.setId(0);
        planVo.setPlanName("planName");
        planVo.setPlanCode("planCode");
        planVo.setPlanType("planType");
        planVo.setMinPremium(new BigDecimal("0.00"));
        planVo.setCommission(new BigDecimal("0.00"));
        final CoverageVo coverageVo = new CoverageVo();
        coverageVo.setSpcId(0);
        final CoverageAmountVo coverageAmountVo = new CoverageAmountVo();
        coverageAmountVo.setSpcId(0);
        coverageVo.setAmounts(Arrays.asList(coverageAmountVo));
        final DutyFactorFlowVo dutyFactorFlowVo = new DutyFactorFlowVo();
        dutyFactorFlowVo.setSpcId(0);
        dutyFactorFlowVo.setDutyCode("dutyCode");
        dutyFactorFlowVo.setDutyFactorName("dutyFactorName");
        dutyFactorFlowVo.setDutyFactorCode("dutyFactorCode");
        coverageVo.setDutyFlowFactor(Arrays.asList(dutyFactorFlowVo));
        planVo.setCoverages(Arrays.asList(coverageVo));
        expectedResult.setPlans(Arrays.asList(planVo));
        expectedResult.setSalesMode(new String[]{"salesMode"});
        final ShowTips tips = new ShowTips();
        tips.setMessage("此产品暂不支持对公转账，如有此需求请登录i云保进行询价及投保，保费以i云保展示为准。");
        expectedResult.setTips(tips);
        final TimeFactorItem timeFactorItem = new TimeFactorItem();
        timeFactorItem.setFactorValue("enterpriseRiskFactor");
        timeFactorItem.setTimeUnit("unit");
        timeFactorItem.setFlow("flow");
        expectedResult.setTimeFactorItems(Arrays.asList(timeFactorItem));
        expectedResult.setProductVersion("productVersion");

        // Configure PolicyMapper.getProductDetailById(...).
        final ProductDTO productDTO = new ProductDTO();
        productDTO.setProductTags("productTags");
        productDTO.setAttentions("attentions");
        productDTO.setHealthNotification("content");
        productDTO.setProductSpecialsJoin("productSpecialsJoin");
        productDTO.setGlProductNotice("glProductNotice");
        productDTO.setSalesMode("salesMode");
        when(mockPolicyMapper.getProductDetailById(0)).thenReturn(productDTO);

        when(mockProductVersionMapper.getMaxVersion(0)).thenReturn(0);

        // Configure PolicyMapper.getPlans(...).
        final PlanDTO planDTO = new PlanDTO();
        planDTO.setId(0);
        planDTO.setPlanName("planName");
        planDTO.setPlanCode("planCode");
        planDTO.setPlanType("planType");
        planDTO.setMinPremium(new BigDecimal("0.00"));
        final List<PlanDTO> planDTOS = Arrays.asList(planDTO);
        when(mockPolicyMapper.getPlans(0, "region")).thenReturn(planDTOS);

        when(mockCommissionMapper.getCommissionSettingByPlanIds(Arrays.asList(0),
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).thenReturn(Collections.emptyList());

        // Configure PolicyMapper.getCoverage(...).
        final CoverageVo coverageVo1 = new CoverageVo();
        coverageVo1.setSpcId(0);
        final CoverageAmountVo coverageAmountVo1 = new CoverageAmountVo();
        coverageAmountVo1.setSpcId(0);
        coverageVo1.setAmounts(Arrays.asList(coverageAmountVo1));
        final DutyFactorFlowVo dutyFactorFlowVo1 = new DutyFactorFlowVo();
        dutyFactorFlowVo1.setSpcId(0);
        dutyFactorFlowVo1.setDutyCode("dutyCode");
        dutyFactorFlowVo1.setDutyFactorName("dutyFactorName");
        dutyFactorFlowVo1.setDutyFactorCode("dutyFactorCode");
        coverageVo1.setDutyFlowFactor(Arrays.asList(dutyFactorFlowVo1));
        final List<CoverageVo> coverageVos = Arrays.asList(coverageVo1);
        when(mockPolicyMapper.getCoverage(0)).thenReturn(coverageVos);

        // Configure PolicyMapper.getCoverageAmounts(...).
        final CoverageAmountVo coverageAmountVo2 = new CoverageAmountVo();
        coverageAmountVo2.setSpcaId(0);
        coverageAmountVo2.setSpcId(0);
        coverageAmountVo2.setCvgAmount(new BigDecimal("0.00"));
        coverageAmountVo2.setCvgNotice("cvgNotice");
        final List<CoverageAmountVo> coverageAmountVos = Arrays.asList(coverageAmountVo2);
        when(mockPolicyMapper.getCoverageAmounts(0)).thenReturn(coverageAmountVos);

        // Configure PolicyMapper.getDutyFactorFlows(...).
        final DutyFactorFlowDTO dutyFactorFlowDTO = new DutyFactorFlowDTO();
        dutyFactorFlowDTO.setId(0);
        dutyFactorFlowDTO.setSpcId(0);
        dutyFactorFlowDTO.setDutyCode("dutyCode");
        dutyFactorFlowDTO.setDutyFactorCode("dutyFactorCode");
        dutyFactorFlowDTO.setDutyFactorName("dutyFactorName");
        dutyFactorFlowDTO.setFactorValue("factorValue");
        dutyFactorFlowDTO.setFactorName("factorName");
        dutyFactorFlowDTO.setOptionName("optionName");
        dutyFactorFlowDTO.setFlow("flow");
        dutyFactorFlowDTO.setIsDefault(0);
        final List<DutyFactorFlowDTO> dutyFactorFlowDTOS = Arrays.asList(dutyFactorFlowDTO);
        when(mockPolicyMapper.getDutyFactorFlows(0)).thenReturn(dutyFactorFlowDTOS);

        // Configure PolicyMapper.queryQuoteLimit(...).
        final QuoteLimitDTO quoteLimitDTO = new QuoteLimitDTO();
        quoteLimitDTO.setSpqlId(0);
        quoteLimitDTO.setProductId(0);
        quoteLimitDTO.setLimitType("limitType");
        quoteLimitDTO.setOccupationGroup("occupationGroup");
        quoteLimitDTO.setMinPerQty(0);
        final List<QuoteLimitDTO> quoteLimitDTOS = Arrays.asList(quoteLimitDTO);
        when(mockPolicyMapper.queryQuoteLimit(0)).thenReturn(quoteLimitDTOS);

        // Configure ProductMapper.queryPremiumFactor(...).
        final PremiumFlow premiumFlow = new PremiumFlow();
        premiumFlow.setId(0);
        premiumFlow.setProductId(0);
        premiumFlow.setPlanId(0);
        premiumFlow.setFactorValue("enterpriseRiskFactor");
        premiumFlow.setFlow("flow");
        final List<PremiumFlow> premiumFlows = Arrays.asList(premiumFlow);
        when(mockProductMapper.queryPremiumFactor(0, 0, "channelCode")).thenReturn(premiumFlows);

        // Run the test
         policyServiceUnderTest.queryProductInfo(0, "region");

    }

    @Test
    public void testQueryProductInfo_PolicyMapperGetCoverageReturnsNull() {
        // Setup
        final ProductDetailVo expectedResult = new ProductDetailVo();
        expectedResult.setValidPeriod("validPeriod");
        expectedResult.setPolicyHolderCondition("policyHolderCondition");
        expectedResult.setProductSpecialsJoin(new String[]{"productSpecialsJoin"});
        expectedResult.setProductTags(new String[]{"productTags"});
        final PlanVo planVo = new PlanVo();
        planVo.setId(0);
        planVo.setPlanName("planName");
        planVo.setPlanCode("planCode");
        planVo.setPlanType("planType");
        planVo.setMinPremium(new BigDecimal("0.00"));
        planVo.setCommission(new BigDecimal("0.00"));
        final CoverageVo coverageVo = new CoverageVo();
        coverageVo.setSpcId(0);
        final CoverageAmountVo coverageAmountVo = new CoverageAmountVo();
        coverageAmountVo.setSpcId(0);
        coverageVo.setAmounts(Arrays.asList(coverageAmountVo));
        final DutyFactorFlowVo dutyFactorFlowVo = new DutyFactorFlowVo();
        dutyFactorFlowVo.setSpcId(0);
        dutyFactorFlowVo.setDutyCode("dutyCode");
        dutyFactorFlowVo.setDutyFactorName("dutyFactorName");
        dutyFactorFlowVo.setDutyFactorCode("dutyFactorCode");
        coverageVo.setDutyFlowFactor(Arrays.asList(dutyFactorFlowVo));
        planVo.setCoverages(Arrays.asList(coverageVo));
        expectedResult.setPlans(Arrays.asList(planVo));
        expectedResult.setSalesMode(new String[]{"salesMode"});
        final ShowTips tips = new ShowTips();
        tips.setMessage("此产品暂不支持对公转账，如有此需求请登录i云保进行询价及投保，保费以i云保展示为准。");
        expectedResult.setTips(tips);
        final TimeFactorItem timeFactorItem = new TimeFactorItem();
        timeFactorItem.setFactorValue("enterpriseRiskFactor");
        timeFactorItem.setTimeUnit("unit");
        timeFactorItem.setFlow("flow");
        expectedResult.setTimeFactorItems(Arrays.asList(timeFactorItem));
        expectedResult.setProductVersion("productVersion");

        // Configure PolicyMapper.getProductDetailById(...).
        final ProductDTO productDTO = new ProductDTO();
        productDTO.setProductTags("productTags");
        productDTO.setAttentions("attentions");
        productDTO.setHealthNotification("content");
        productDTO.setProductSpecialsJoin("productSpecialsJoin");
        productDTO.setGlProductNotice("glProductNotice");
        productDTO.setSalesMode("salesMode");
        when(mockPolicyMapper.getProductDetailById(0)).thenReturn(productDTO);

        when(mockProductVersionMapper.getMaxVersion(0)).thenReturn(0);

        // Configure PolicyMapper.getPlans(...).
        final PlanDTO planDTO = new PlanDTO();
        planDTO.setId(0);
        planDTO.setPlanName("planName");
        planDTO.setPlanCode("planCode");
        planDTO.setPlanType("planType");
        planDTO.setMinPremium(new BigDecimal("0.00"));
        final List<PlanDTO> planDTOS = Arrays.asList(planDTO);
        when(mockPolicyMapper.getPlans(0, "region")).thenReturn(planDTOS);

        // Configure SmCommissionMapper.getCommissionSettingByPlanIds(...).
        final SmCommissionSettingVO smCommissionSettingVO = new SmCommissionSettingVO();
        smCommissionSettingVO.setId(0);
        smCommissionSettingVO.setPlanId(0);
        smCommissionSettingVO.setPaymentProportion(new BigDecimal("0.00"));
        smCommissionSettingVO.setSettlementProportion(new BigDecimal("0.00"));
        smCommissionSettingVO.setSaleLevel("saleLevel");
        final List<SmCommissionSettingVO> smCommissionSettingVOS = Arrays.asList(smCommissionSettingVO);
        when(mockCommissionMapper.getCommissionSettingByPlanIds(Arrays.asList(0),
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).thenReturn(smCommissionSettingVOS);

        when(mockPolicyMapper.getCoverage(0)).thenReturn(null);

        // Configure PolicyMapper.queryQuoteLimit(...).
        final QuoteLimitDTO quoteLimitDTO = new QuoteLimitDTO();
        quoteLimitDTO.setSpqlId(0);
        quoteLimitDTO.setProductId(0);
        quoteLimitDTO.setLimitType("limitType");
        quoteLimitDTO.setOccupationGroup("occupationGroup");
        quoteLimitDTO.setMinPerQty(0);
        final List<QuoteLimitDTO> quoteLimitDTOS = Arrays.asList(quoteLimitDTO);
        when(mockPolicyMapper.queryQuoteLimit(0)).thenReturn(quoteLimitDTOS);

        // Configure ProductMapper.queryPremiumFactor(...).
        final PremiumFlow premiumFlow = new PremiumFlow();
        premiumFlow.setId(0);
        premiumFlow.setProductId(0);
        premiumFlow.setPlanId(0);
        premiumFlow.setFactorValue("enterpriseRiskFactor");
        premiumFlow.setFlow("flow");
        final List<PremiumFlow> premiumFlows = Arrays.asList(premiumFlow);
        when(mockProductMapper.queryPremiumFactor(0, 0, "channelCode")).thenReturn(premiumFlows);

        // Run the test
        policyServiceUnderTest.queryProductInfo(0, "region");

    }

    @Test
    public void testQueryProductInfo_PolicyMapperGetCoverageReturnsNoItems() {
        // Setup
        final ProductDetailVo expectedResult = new ProductDetailVo();
        expectedResult.setValidPeriod("validPeriod");
        expectedResult.setPolicyHolderCondition("policyHolderCondition");
        expectedResult.setProductSpecialsJoin(new String[]{"productSpecialsJoin"});
        expectedResult.setProductTags(new String[]{"productTags"});
        final PlanVo planVo = new PlanVo();
        planVo.setId(0);
        planVo.setPlanName("planName");
        planVo.setPlanCode("planCode");
        planVo.setPlanType("planType");
        planVo.setMinPremium(new BigDecimal("0.00"));
        planVo.setCommission(new BigDecimal("0.00"));
        final CoverageVo coverageVo = new CoverageVo();
        coverageVo.setSpcId(0);
        final CoverageAmountVo coverageAmountVo = new CoverageAmountVo();
        coverageAmountVo.setSpcId(0);
        coverageVo.setAmounts(Arrays.asList(coverageAmountVo));
        final DutyFactorFlowVo dutyFactorFlowVo = new DutyFactorFlowVo();
        dutyFactorFlowVo.setSpcId(0);
        dutyFactorFlowVo.setDutyCode("dutyCode");
        dutyFactorFlowVo.setDutyFactorName("dutyFactorName");
        dutyFactorFlowVo.setDutyFactorCode("dutyFactorCode");
        coverageVo.setDutyFlowFactor(Arrays.asList(dutyFactorFlowVo));
        planVo.setCoverages(Arrays.asList(coverageVo));
        expectedResult.setPlans(Arrays.asList(planVo));
        expectedResult.setSalesMode(new String[]{"salesMode"});
        final ShowTips tips = new ShowTips();
        tips.setMessage("此产品暂不支持对公转账，如有此需求请登录i云保进行询价及投保，保费以i云保展示为准。");
        expectedResult.setTips(tips);
        final TimeFactorItem timeFactorItem = new TimeFactorItem();
        timeFactorItem.setFactorValue("enterpriseRiskFactor");
        timeFactorItem.setTimeUnit("unit");
        timeFactorItem.setFlow("flow");
        expectedResult.setTimeFactorItems(Arrays.asList(timeFactorItem));
        expectedResult.setProductVersion("productVersion");

        // Configure PolicyMapper.getProductDetailById(...).
        final ProductDTO productDTO = new ProductDTO();
        productDTO.setProductTags("productTags");
        productDTO.setAttentions("attentions");
        productDTO.setHealthNotification("content");
        productDTO.setProductSpecialsJoin("productSpecialsJoin");
        productDTO.setGlProductNotice("glProductNotice");
        productDTO.setSalesMode("salesMode");
        when(mockPolicyMapper.getProductDetailById(0)).thenReturn(productDTO);

        when(mockProductVersionMapper.getMaxVersion(0)).thenReturn(0);

        // Configure PolicyMapper.getPlans(...).
        final PlanDTO planDTO = new PlanDTO();
        planDTO.setId(0);
        planDTO.setPlanName("planName");
        planDTO.setPlanCode("planCode");
        planDTO.setPlanType("planType");
        planDTO.setMinPremium(new BigDecimal("0.00"));
        final List<PlanDTO> planDTOS = Arrays.asList(planDTO);
        when(mockPolicyMapper.getPlans(0, "region")).thenReturn(planDTOS);

        // Configure SmCommissionMapper.getCommissionSettingByPlanIds(...).
        final SmCommissionSettingVO smCommissionSettingVO = new SmCommissionSettingVO();
        smCommissionSettingVO.setId(0);
        smCommissionSettingVO.setPlanId(0);
        smCommissionSettingVO.setPaymentProportion(new BigDecimal("0.00"));
        smCommissionSettingVO.setSettlementProportion(new BigDecimal("0.00"));
        smCommissionSettingVO.setSaleLevel("saleLevel");
        final List<SmCommissionSettingVO> smCommissionSettingVOS = Arrays.asList(smCommissionSettingVO);
        when(mockCommissionMapper.getCommissionSettingByPlanIds(Arrays.asList(0),
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).thenReturn(smCommissionSettingVOS);

        when(mockPolicyMapper.getCoverage(0)).thenReturn(Collections.emptyList());

        // Configure PolicyMapper.getCoverageAmounts(...).
        final CoverageAmountVo coverageAmountVo1 = new CoverageAmountVo();
        coverageAmountVo1.setSpcaId(0);
        coverageAmountVo1.setSpcId(0);
        coverageAmountVo1.setCvgAmount(new BigDecimal("0.00"));
        coverageAmountVo1.setCvgNotice("cvgNotice");
        final List<CoverageAmountVo> coverageAmountVos = Arrays.asList(coverageAmountVo1);
        when(mockPolicyMapper.getCoverageAmounts(0)).thenReturn(coverageAmountVos);

        // Configure PolicyMapper.getDutyFactorFlows(...).
        final DutyFactorFlowDTO dutyFactorFlowDTO = new DutyFactorFlowDTO();
        dutyFactorFlowDTO.setId(0);
        dutyFactorFlowDTO.setSpcId(0);
        dutyFactorFlowDTO.setDutyCode("dutyCode");
        dutyFactorFlowDTO.setDutyFactorCode("dutyFactorCode");
        dutyFactorFlowDTO.setDutyFactorName("dutyFactorName");
        dutyFactorFlowDTO.setFactorValue("factorValue");
        dutyFactorFlowDTO.setFactorName("factorName");
        dutyFactorFlowDTO.setOptionName("optionName");
        dutyFactorFlowDTO.setFlow("flow");
        dutyFactorFlowDTO.setIsDefault(0);
        final List<DutyFactorFlowDTO> dutyFactorFlowDTOS = Arrays.asList(dutyFactorFlowDTO);
        when(mockPolicyMapper.getDutyFactorFlows(0)).thenReturn(dutyFactorFlowDTOS);

        // Configure PolicyMapper.queryQuoteLimit(...).
        final QuoteLimitDTO quoteLimitDTO = new QuoteLimitDTO();
        quoteLimitDTO.setSpqlId(0);
        quoteLimitDTO.setProductId(0);
        quoteLimitDTO.setLimitType("limitType");
        quoteLimitDTO.setOccupationGroup("occupationGroup");
        quoteLimitDTO.setMinPerQty(0);
        final List<QuoteLimitDTO> quoteLimitDTOS = Arrays.asList(quoteLimitDTO);
        when(mockPolicyMapper.queryQuoteLimit(0)).thenReturn(quoteLimitDTOS);

        // Configure ProductMapper.queryPremiumFactor(...).
        final PremiumFlow premiumFlow = new PremiumFlow();
        premiumFlow.setId(0);
        premiumFlow.setProductId(0);
        premiumFlow.setPlanId(0);
        premiumFlow.setFactorValue("enterpriseRiskFactor");
        premiumFlow.setFlow("flow");
        final List<PremiumFlow> premiumFlows = Arrays.asList(premiumFlow);
        when(mockProductMapper.queryPremiumFactor(0, 0, "channelCode")).thenReturn(premiumFlows);

        // Run the test
        policyServiceUnderTest.queryProductInfo(0, "region");

    }

    @Test
    public void testQueryProductInfo_PolicyMapperGetCoverageAmountsReturnsNoItems() {
        // Setup
        final ProductDetailVo expectedResult = new ProductDetailVo();
        expectedResult.setValidPeriod("validPeriod");
        expectedResult.setPolicyHolderCondition("policyHolderCondition");
        expectedResult.setProductSpecialsJoin(new String[]{"productSpecialsJoin"});
        expectedResult.setProductTags(new String[]{"productTags"});
        final PlanVo planVo = new PlanVo();
        planVo.setId(0);
        planVo.setPlanName("planName");
        planVo.setPlanCode("planCode");
        planVo.setPlanType("planType");
        planVo.setMinPremium(new BigDecimal("0.00"));
        planVo.setCommission(new BigDecimal("0.00"));
        final CoverageVo coverageVo = new CoverageVo();
        coverageVo.setSpcId(0);
        final CoverageAmountVo coverageAmountVo = new CoverageAmountVo();
        coverageAmountVo.setSpcId(0);
        coverageVo.setAmounts(Arrays.asList(coverageAmountVo));
        final DutyFactorFlowVo dutyFactorFlowVo = new DutyFactorFlowVo();
        dutyFactorFlowVo.setSpcId(0);
        dutyFactorFlowVo.setDutyCode("dutyCode");
        dutyFactorFlowVo.setDutyFactorName("dutyFactorName");
        dutyFactorFlowVo.setDutyFactorCode("dutyFactorCode");
        coverageVo.setDutyFlowFactor(Arrays.asList(dutyFactorFlowVo));
        planVo.setCoverages(Arrays.asList(coverageVo));
        expectedResult.setPlans(Arrays.asList(planVo));
        expectedResult.setSalesMode(new String[]{"salesMode"});
        final ShowTips tips = new ShowTips();
        tips.setMessage("此产品暂不支持对公转账，如有此需求请登录i云保进行询价及投保，保费以i云保展示为准。");
        expectedResult.setTips(tips);
        final TimeFactorItem timeFactorItem = new TimeFactorItem();
        timeFactorItem.setFactorValue("enterpriseRiskFactor");
        timeFactorItem.setTimeUnit("unit");
        timeFactorItem.setFlow("flow");
        expectedResult.setTimeFactorItems(Arrays.asList(timeFactorItem));
        expectedResult.setProductVersion("productVersion");

        // Configure PolicyMapper.getProductDetailById(...).
        final ProductDTO productDTO = new ProductDTO();
        productDTO.setProductTags("productTags");
        productDTO.setAttentions("attentions");
        productDTO.setHealthNotification("content");
        productDTO.setProductSpecialsJoin("productSpecialsJoin");
        productDTO.setGlProductNotice("glProductNotice");
        productDTO.setSalesMode("salesMode");
        when(mockPolicyMapper.getProductDetailById(0)).thenReturn(productDTO);

        when(mockProductVersionMapper.getMaxVersion(0)).thenReturn(0);

        // Configure PolicyMapper.getPlans(...).
        final PlanDTO planDTO = new PlanDTO();
        planDTO.setId(0);
        planDTO.setPlanName("planName");
        planDTO.setPlanCode("planCode");
        planDTO.setPlanType("planType");
        planDTO.setMinPremium(new BigDecimal("0.00"));
        final List<PlanDTO> planDTOS = Arrays.asList(planDTO);
        when(mockPolicyMapper.getPlans(0, "region")).thenReturn(planDTOS);

        // Configure SmCommissionMapper.getCommissionSettingByPlanIds(...).
        final SmCommissionSettingVO smCommissionSettingVO = new SmCommissionSettingVO();
        smCommissionSettingVO.setId(0);
        smCommissionSettingVO.setPlanId(0);
        smCommissionSettingVO.setPaymentProportion(new BigDecimal("0.00"));
        smCommissionSettingVO.setSettlementProportion(new BigDecimal("0.00"));
        smCommissionSettingVO.setSaleLevel("saleLevel");
        final List<SmCommissionSettingVO> smCommissionSettingVOS = Arrays.asList(smCommissionSettingVO);
        when(mockCommissionMapper.getCommissionSettingByPlanIds(Arrays.asList(0),
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).thenReturn(smCommissionSettingVOS);

        // Configure PolicyMapper.getCoverage(...).
        final CoverageVo coverageVo1 = new CoverageVo();
        coverageVo1.setSpcId(0);
        final CoverageAmountVo coverageAmountVo1 = new CoverageAmountVo();
        coverageAmountVo1.setSpcId(0);
        coverageVo1.setAmounts(Arrays.asList(coverageAmountVo1));
        final DutyFactorFlowVo dutyFactorFlowVo1 = new DutyFactorFlowVo();
        dutyFactorFlowVo1.setSpcId(0);
        dutyFactorFlowVo1.setDutyCode("dutyCode");
        dutyFactorFlowVo1.setDutyFactorName("dutyFactorName");
        dutyFactorFlowVo1.setDutyFactorCode("dutyFactorCode");
        coverageVo1.setDutyFlowFactor(Arrays.asList(dutyFactorFlowVo1));
        final List<CoverageVo> coverageVos = Arrays.asList(coverageVo1);
        when(mockPolicyMapper.getCoverage(0)).thenReturn(coverageVos);

        when(mockPolicyMapper.getCoverageAmounts(0)).thenReturn(Collections.emptyList());

        // Configure PolicyMapper.getDutyFactorFlows(...).
        final DutyFactorFlowDTO dutyFactorFlowDTO = new DutyFactorFlowDTO();
        dutyFactorFlowDTO.setId(0);
        dutyFactorFlowDTO.setSpcId(0);
        dutyFactorFlowDTO.setDutyCode("dutyCode");
        dutyFactorFlowDTO.setDutyFactorCode("dutyFactorCode");
        dutyFactorFlowDTO.setDutyFactorName("dutyFactorName");
        dutyFactorFlowDTO.setFactorValue("factorValue");
        dutyFactorFlowDTO.setFactorName("factorName");
        dutyFactorFlowDTO.setOptionName("optionName");
        dutyFactorFlowDTO.setFlow("flow");
        dutyFactorFlowDTO.setIsDefault(0);
        final List<DutyFactorFlowDTO> dutyFactorFlowDTOS = Arrays.asList(dutyFactorFlowDTO);
        when(mockPolicyMapper.getDutyFactorFlows(0)).thenReturn(dutyFactorFlowDTOS);

        // Configure PolicyMapper.queryQuoteLimit(...).
        final QuoteLimitDTO quoteLimitDTO = new QuoteLimitDTO();
        quoteLimitDTO.setSpqlId(0);
        quoteLimitDTO.setProductId(0);
        quoteLimitDTO.setLimitType("limitType");
        quoteLimitDTO.setOccupationGroup("occupationGroup");
        quoteLimitDTO.setMinPerQty(0);
        final List<QuoteLimitDTO> quoteLimitDTOS = Arrays.asList(quoteLimitDTO);
        when(mockPolicyMapper.queryQuoteLimit(0)).thenReturn(quoteLimitDTOS);

        // Configure ProductMapper.queryPremiumFactor(...).
        final PremiumFlow premiumFlow = new PremiumFlow();
        premiumFlow.setId(0);
        premiumFlow.setProductId(0);
        premiumFlow.setPlanId(0);
        premiumFlow.setFactorValue("enterpriseRiskFactor");
        premiumFlow.setFlow("flow");
        final List<PremiumFlow> premiumFlows = Arrays.asList(premiumFlow);
        when(mockProductMapper.queryPremiumFactor(0, 0, "channelCode")).thenReturn(premiumFlows);

        policyServiceUnderTest.queryProductInfo(0, "region");

    }

    @Test
    public void testQueryProductInfo_PolicyMapperGetDutyFactorFlowsReturnsNoItems() {
        // Setup
        final ProductDetailVo expectedResult = new ProductDetailVo();
        expectedResult.setValidPeriod("validPeriod");
        expectedResult.setPolicyHolderCondition("policyHolderCondition");
        expectedResult.setProductSpecialsJoin(new String[]{"productSpecialsJoin"});
        expectedResult.setProductTags(new String[]{"productTags"});
        final PlanVo planVo = new PlanVo();
        planVo.setId(0);
        planVo.setPlanName("planName");
        planVo.setPlanCode("planCode");
        planVo.setPlanType("planType");
        planVo.setMinPremium(new BigDecimal("0.00"));
        planVo.setCommission(new BigDecimal("0.00"));
        final CoverageVo coverageVo = new CoverageVo();
        coverageVo.setSpcId(0);
        final CoverageAmountVo coverageAmountVo = new CoverageAmountVo();
        coverageAmountVo.setSpcId(0);
        coverageVo.setAmounts(Arrays.asList(coverageAmountVo));
        final DutyFactorFlowVo dutyFactorFlowVo = new DutyFactorFlowVo();
        dutyFactorFlowVo.setSpcId(0);
        dutyFactorFlowVo.setDutyCode("dutyCode");
        dutyFactorFlowVo.setDutyFactorName("dutyFactorName");
        dutyFactorFlowVo.setDutyFactorCode("dutyFactorCode");
        coverageVo.setDutyFlowFactor(Arrays.asList(dutyFactorFlowVo));
        planVo.setCoverages(Arrays.asList(coverageVo));
        expectedResult.setPlans(Arrays.asList(planVo));
        expectedResult.setSalesMode(new String[]{"salesMode"});
        final ShowTips tips = new ShowTips();
        tips.setMessage("此产品暂不支持对公转账，如有此需求请登录i云保进行询价及投保，保费以i云保展示为准。");
        expectedResult.setTips(tips);
        final TimeFactorItem timeFactorItem = new TimeFactorItem();
        timeFactorItem.setFactorValue("enterpriseRiskFactor");
        timeFactorItem.setTimeUnit("unit");
        timeFactorItem.setFlow("flow");
        expectedResult.setTimeFactorItems(Arrays.asList(timeFactorItem));
        expectedResult.setProductVersion("productVersion");

        // Configure PolicyMapper.getProductDetailById(...).
        final ProductDTO productDTO = new ProductDTO();
        productDTO.setProductTags("productTags");
        productDTO.setAttentions("attentions");
        productDTO.setHealthNotification("content");
        productDTO.setProductSpecialsJoin("productSpecialsJoin");
        productDTO.setGlProductNotice("glProductNotice");
        productDTO.setSalesMode("salesMode");
        when(mockPolicyMapper.getProductDetailById(0)).thenReturn(productDTO);

        when(mockProductVersionMapper.getMaxVersion(0)).thenReturn(0);

        // Configure PolicyMapper.getPlans(...).
        final PlanDTO planDTO = new PlanDTO();
        planDTO.setId(0);
        planDTO.setPlanName("planName");
        planDTO.setPlanCode("planCode");
        planDTO.setPlanType("planType");
        planDTO.setMinPremium(new BigDecimal("0.00"));
        final List<PlanDTO> planDTOS = Arrays.asList(planDTO);
        when(mockPolicyMapper.getPlans(0, "region")).thenReturn(planDTOS);

        // Configure SmCommissionMapper.getCommissionSettingByPlanIds(...).
        final SmCommissionSettingVO smCommissionSettingVO = new SmCommissionSettingVO();
        smCommissionSettingVO.setId(0);
        smCommissionSettingVO.setPlanId(0);
        smCommissionSettingVO.setPaymentProportion(new BigDecimal("0.00"));
        smCommissionSettingVO.setSettlementProportion(new BigDecimal("0.00"));
        smCommissionSettingVO.setSaleLevel("saleLevel");
        final List<SmCommissionSettingVO> smCommissionSettingVOS = Arrays.asList(smCommissionSettingVO);
        when(mockCommissionMapper.getCommissionSettingByPlanIds(Arrays.asList(0),
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).thenReturn(smCommissionSettingVOS);

        // Configure PolicyMapper.getCoverage(...).
        final CoverageVo coverageVo1 = new CoverageVo();
        coverageVo1.setSpcId(0);
        final CoverageAmountVo coverageAmountVo1 = new CoverageAmountVo();
        coverageAmountVo1.setSpcId(0);
        coverageVo1.setAmounts(Arrays.asList(coverageAmountVo1));
        final DutyFactorFlowVo dutyFactorFlowVo1 = new DutyFactorFlowVo();
        dutyFactorFlowVo1.setSpcId(0);
        dutyFactorFlowVo1.setDutyCode("dutyCode");
        dutyFactorFlowVo1.setDutyFactorName("dutyFactorName");
        dutyFactorFlowVo1.setDutyFactorCode("dutyFactorCode");
        coverageVo1.setDutyFlowFactor(Arrays.asList(dutyFactorFlowVo1));
        final List<CoverageVo> coverageVos = Arrays.asList(coverageVo1);
        when(mockPolicyMapper.getCoverage(0)).thenReturn(coverageVos);

        // Configure PolicyMapper.getCoverageAmounts(...).
        final CoverageAmountVo coverageAmountVo2 = new CoverageAmountVo();
        coverageAmountVo2.setSpcaId(0);
        coverageAmountVo2.setSpcId(0);
        coverageAmountVo2.setCvgAmount(new BigDecimal("0.00"));
        coverageAmountVo2.setCvgNotice("cvgNotice");
        final List<CoverageAmountVo> coverageAmountVos = Arrays.asList(coverageAmountVo2);
        when(mockPolicyMapper.getCoverageAmounts(0)).thenReturn(coverageAmountVos);

        when(mockPolicyMapper.getDutyFactorFlows(0)).thenReturn(Collections.emptyList());

        // Configure PolicyMapper.queryQuoteLimit(...).
        final QuoteLimitDTO quoteLimitDTO = new QuoteLimitDTO();
        quoteLimitDTO.setSpqlId(0);
        quoteLimitDTO.setProductId(0);
        quoteLimitDTO.setLimitType("limitType");
        quoteLimitDTO.setOccupationGroup("occupationGroup");
        quoteLimitDTO.setMinPerQty(0);
        final List<QuoteLimitDTO> quoteLimitDTOS = Arrays.asList(quoteLimitDTO);
        when(mockPolicyMapper.queryQuoteLimit(0)).thenReturn(quoteLimitDTOS);

        // Configure ProductMapper.queryPremiumFactor(...).
        final PremiumFlow premiumFlow = new PremiumFlow();
        premiumFlow.setId(0);
        premiumFlow.setProductId(0);
        premiumFlow.setPlanId(0);
        premiumFlow.setFactorValue("enterpriseRiskFactor");
        premiumFlow.setFlow("flow");
        final List<PremiumFlow> premiumFlows = Arrays.asList(premiumFlow);
        when(mockProductMapper.queryPremiumFactor(0, 0, "channelCode")).thenReturn(premiumFlows);

        // Run the test
        policyServiceUnderTest.queryProductInfo(0, "region");
    }

    @Test
    public void testQueryProductInfo_PolicyMapperQueryQuoteLimitReturnsNoItems() {
        // Setup
        final ProductDetailVo expectedResult = new ProductDetailVo();
        expectedResult.setValidPeriod("validPeriod");
        expectedResult.setPolicyHolderCondition("policyHolderCondition");
        expectedResult.setProductSpecialsJoin(new String[]{"productSpecialsJoin"});
        expectedResult.setProductTags(new String[]{"productTags"});
        final PlanVo planVo = new PlanVo();
        planVo.setId(0);
        planVo.setPlanName("planName");
        planVo.setPlanCode("planCode");
        planVo.setPlanType("planType");
        planVo.setMinPremium(new BigDecimal("0.00"));
        planVo.setCommission(new BigDecimal("0.00"));
        final CoverageVo coverageVo = new CoverageVo();
        coverageVo.setSpcId(0);
        final CoverageAmountVo coverageAmountVo = new CoverageAmountVo();
        coverageAmountVo.setSpcId(0);
        coverageVo.setAmounts(Arrays.asList(coverageAmountVo));
        final DutyFactorFlowVo dutyFactorFlowVo = new DutyFactorFlowVo();
        dutyFactorFlowVo.setSpcId(0);
        dutyFactorFlowVo.setDutyCode("dutyCode");
        dutyFactorFlowVo.setDutyFactorName("dutyFactorName");
        dutyFactorFlowVo.setDutyFactorCode("dutyFactorCode");
        coverageVo.setDutyFlowFactor(Arrays.asList(dutyFactorFlowVo));
        planVo.setCoverages(Arrays.asList(coverageVo));
        expectedResult.setPlans(Arrays.asList(planVo));
        expectedResult.setSalesMode(new String[]{"salesMode"});
        final ShowTips tips = new ShowTips();
        tips.setMessage("此产品暂不支持对公转账，如有此需求请登录i云保进行询价及投保，保费以i云保展示为准。");
        expectedResult.setTips(tips);
        final TimeFactorItem timeFactorItem = new TimeFactorItem();
        timeFactorItem.setFactorValue("enterpriseRiskFactor");
        timeFactorItem.setTimeUnit("unit");
        timeFactorItem.setFlow("flow");
        expectedResult.setTimeFactorItems(Arrays.asList(timeFactorItem));
        expectedResult.setProductVersion("productVersion");

        // Configure PolicyMapper.getProductDetailById(...).
        final ProductDTO productDTO = new ProductDTO();
        productDTO.setProductTags("productTags");
        productDTO.setAttentions("attentions");
        productDTO.setHealthNotification("content");
        productDTO.setProductSpecialsJoin("productSpecialsJoin");
        productDTO.setGlProductNotice("glProductNotice");
        productDTO.setSalesMode("salesMode");
        when(mockPolicyMapper.getProductDetailById(0)).thenReturn(productDTO);

        when(mockProductVersionMapper.getMaxVersion(0)).thenReturn(0);

        // Configure PolicyMapper.getPlans(...).
        final PlanDTO planDTO = new PlanDTO();
        planDTO.setId(0);
        planDTO.setPlanName("planName");
        planDTO.setPlanCode("planCode");
        planDTO.setPlanType("planType");
        planDTO.setMinPremium(new BigDecimal("0.00"));
        final List<PlanDTO> planDTOS = Arrays.asList(planDTO);
        when(mockPolicyMapper.getPlans(0, "region")).thenReturn(planDTOS);

        // Configure SmCommissionMapper.getCommissionSettingByPlanIds(...).
        final SmCommissionSettingVO smCommissionSettingVO = new SmCommissionSettingVO();
        smCommissionSettingVO.setId(0);
        smCommissionSettingVO.setPlanId(0);
        smCommissionSettingVO.setPaymentProportion(new BigDecimal("0.00"));
        smCommissionSettingVO.setSettlementProportion(new BigDecimal("0.00"));
        smCommissionSettingVO.setSaleLevel("saleLevel");
        final List<SmCommissionSettingVO> smCommissionSettingVOS = Arrays.asList(smCommissionSettingVO);
        when(mockCommissionMapper.getCommissionSettingByPlanIds(Arrays.asList(0),
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).thenReturn(smCommissionSettingVOS);

        // Configure PolicyMapper.getCoverage(...).
        final CoverageVo coverageVo1 = new CoverageVo();
        coverageVo1.setSpcId(0);
        final CoverageAmountVo coverageAmountVo1 = new CoverageAmountVo();
        coverageAmountVo1.setSpcId(0);
        coverageVo1.setAmounts(Arrays.asList(coverageAmountVo1));
        final DutyFactorFlowVo dutyFactorFlowVo1 = new DutyFactorFlowVo();
        dutyFactorFlowVo1.setSpcId(0);
        dutyFactorFlowVo1.setDutyCode("dutyCode");
        dutyFactorFlowVo1.setDutyFactorName("dutyFactorName");
        dutyFactorFlowVo1.setDutyFactorCode("dutyFactorCode");
        coverageVo1.setDutyFlowFactor(Arrays.asList(dutyFactorFlowVo1));
        final List<CoverageVo> coverageVos = Arrays.asList(coverageVo1);
        when(mockPolicyMapper.getCoverage(0)).thenReturn(coverageVos);

        // Configure PolicyMapper.getCoverageAmounts(...).
        final CoverageAmountVo coverageAmountVo2 = new CoverageAmountVo();
        coverageAmountVo2.setSpcaId(0);
        coverageAmountVo2.setSpcId(0);
        coverageAmountVo2.setCvgAmount(new BigDecimal("0.00"));
        coverageAmountVo2.setCvgNotice("cvgNotice");
        final List<CoverageAmountVo> coverageAmountVos = Arrays.asList(coverageAmountVo2);
        when(mockPolicyMapper.getCoverageAmounts(0)).thenReturn(coverageAmountVos);

        // Configure PolicyMapper.getDutyFactorFlows(...).
        final DutyFactorFlowDTO dutyFactorFlowDTO = new DutyFactorFlowDTO();
        dutyFactorFlowDTO.setId(0);
        dutyFactorFlowDTO.setSpcId(0);
        dutyFactorFlowDTO.setDutyCode("dutyCode");
        dutyFactorFlowDTO.setDutyFactorCode("dutyFactorCode");
        dutyFactorFlowDTO.setDutyFactorName("dutyFactorName");
        dutyFactorFlowDTO.setFactorValue("factorValue");
        dutyFactorFlowDTO.setFactorName("factorName");
        dutyFactorFlowDTO.setOptionName("optionName");
        dutyFactorFlowDTO.setFlow("flow");
        dutyFactorFlowDTO.setIsDefault(0);
        final List<DutyFactorFlowDTO> dutyFactorFlowDTOS = Arrays.asList(dutyFactorFlowDTO);
        when(mockPolicyMapper.getDutyFactorFlows(0)).thenReturn(dutyFactorFlowDTOS);

        when(mockPolicyMapper.queryQuoteLimit(0)).thenReturn(Collections.emptyList());

        // Configure ProductMapper.queryPremiumFactor(...).
        final PremiumFlow premiumFlow = new PremiumFlow();
        premiumFlow.setId(0);
        premiumFlow.setProductId(0);
        premiumFlow.setPlanId(0);
        premiumFlow.setFactorValue("enterpriseRiskFactor");
        premiumFlow.setFlow("flow");
        final List<PremiumFlow> premiumFlows = Arrays.asList(premiumFlow);
        when(mockProductMapper.queryPremiumFactor(0, 0, "channelCode")).thenReturn(premiumFlows);

        // Run the test
        policyServiceUnderTest.queryProductInfo(0, "region");
    }

    @Test
    public void testQueryProductInfo_ProductMapperReturnsNull() {
        // Setup
        final ProductDetailVo expectedResult = new ProductDetailVo();
        expectedResult.setValidPeriod("validPeriod");
        expectedResult.setPolicyHolderCondition("policyHolderCondition");
        expectedResult.setProductSpecialsJoin(new String[]{"productSpecialsJoin"});
        expectedResult.setProductTags(new String[]{"productTags"});
        final PlanVo planVo = new PlanVo();
        planVo.setId(0);
        planVo.setPlanName("planName");
        planVo.setPlanCode("planCode");
        planVo.setPlanType("planType");
        planVo.setMinPremium(new BigDecimal("0.00"));
        planVo.setCommission(new BigDecimal("0.00"));
        final CoverageVo coverageVo = new CoverageVo();
        coverageVo.setSpcId(0);
        final CoverageAmountVo coverageAmountVo = new CoverageAmountVo();
        coverageAmountVo.setSpcId(0);
        coverageVo.setAmounts(Arrays.asList(coverageAmountVo));
        final DutyFactorFlowVo dutyFactorFlowVo = new DutyFactorFlowVo();
        dutyFactorFlowVo.setSpcId(0);
        dutyFactorFlowVo.setDutyCode("dutyCode");
        dutyFactorFlowVo.setDutyFactorName("dutyFactorName");
        dutyFactorFlowVo.setDutyFactorCode("dutyFactorCode");
        coverageVo.setDutyFlowFactor(Arrays.asList(dutyFactorFlowVo));
        planVo.setCoverages(Arrays.asList(coverageVo));
        expectedResult.setPlans(Arrays.asList(planVo));
        expectedResult.setSalesMode(new String[]{"salesMode"});
        final ShowTips tips = new ShowTips();
        tips.setMessage("此产品暂不支持对公转账，如有此需求请登录i云保进行询价及投保，保费以i云保展示为准。");
        expectedResult.setTips(tips);
        final TimeFactorItem timeFactorItem = new TimeFactorItem();
        timeFactorItem.setFactorValue("enterpriseRiskFactor");
        timeFactorItem.setTimeUnit("unit");
        timeFactorItem.setFlow("flow");
        expectedResult.setTimeFactorItems(Arrays.asList(timeFactorItem));
        expectedResult.setProductVersion("productVersion");

        // Configure PolicyMapper.getProductDetailById(...).
        final ProductDTO productDTO = new ProductDTO();
        productDTO.setProductTags("productTags");
        productDTO.setAttentions("attentions");
        productDTO.setHealthNotification("content");
        productDTO.setProductSpecialsJoin("productSpecialsJoin");
        productDTO.setGlProductNotice("glProductNotice");
        productDTO.setSalesMode("salesMode");
        when(mockPolicyMapper.getProductDetailById(0)).thenReturn(productDTO);

        when(mockProductVersionMapper.getMaxVersion(0)).thenReturn(0);

        // Configure PolicyMapper.getPlans(...).
        final PlanDTO planDTO = new PlanDTO();
        planDTO.setId(0);
        planDTO.setPlanName("planName");
        planDTO.setPlanCode("planCode");
        planDTO.setPlanType("planType");
        planDTO.setMinPremium(new BigDecimal("0.00"));
        final List<PlanDTO> planDTOS = Arrays.asList(planDTO);
        when(mockPolicyMapper.getPlans(0, "region")).thenReturn(planDTOS);

        // Configure SmCommissionMapper.getCommissionSettingByPlanIds(...).
        final SmCommissionSettingVO smCommissionSettingVO = new SmCommissionSettingVO();
        smCommissionSettingVO.setId(0);
        smCommissionSettingVO.setPlanId(0);
        smCommissionSettingVO.setPaymentProportion(new BigDecimal("0.00"));
        smCommissionSettingVO.setSettlementProportion(new BigDecimal("0.00"));
        smCommissionSettingVO.setSaleLevel("saleLevel");
        final List<SmCommissionSettingVO> smCommissionSettingVOS = Arrays.asList(smCommissionSettingVO);
        when(mockCommissionMapper.getCommissionSettingByPlanIds(Arrays.asList(0),
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).thenReturn(smCommissionSettingVOS);

        // Configure PolicyMapper.getCoverage(...).
        final CoverageVo coverageVo1 = new CoverageVo();
        coverageVo1.setSpcId(0);
        final CoverageAmountVo coverageAmountVo1 = new CoverageAmountVo();
        coverageAmountVo1.setSpcId(0);
        coverageVo1.setAmounts(Arrays.asList(coverageAmountVo1));
        final DutyFactorFlowVo dutyFactorFlowVo1 = new DutyFactorFlowVo();
        dutyFactorFlowVo1.setSpcId(0);
        dutyFactorFlowVo1.setDutyCode("dutyCode");
        dutyFactorFlowVo1.setDutyFactorName("dutyFactorName");
        dutyFactorFlowVo1.setDutyFactorCode("dutyFactorCode");
        coverageVo1.setDutyFlowFactor(Arrays.asList(dutyFactorFlowVo1));
        final List<CoverageVo> coverageVos = Arrays.asList(coverageVo1);
        when(mockPolicyMapper.getCoverage(0)).thenReturn(coverageVos);

        // Configure PolicyMapper.getCoverageAmounts(...).
        final CoverageAmountVo coverageAmountVo2 = new CoverageAmountVo();
        coverageAmountVo2.setSpcaId(0);
        coverageAmountVo2.setSpcId(0);
        coverageAmountVo2.setCvgAmount(new BigDecimal("0.00"));
        coverageAmountVo2.setCvgNotice("cvgNotice");
        final List<CoverageAmountVo> coverageAmountVos = Arrays.asList(coverageAmountVo2);
        when(mockPolicyMapper.getCoverageAmounts(0)).thenReturn(coverageAmountVos);

        // Configure PolicyMapper.getDutyFactorFlows(...).
        final DutyFactorFlowDTO dutyFactorFlowDTO = new DutyFactorFlowDTO();
        dutyFactorFlowDTO.setId(0);
        dutyFactorFlowDTO.setSpcId(0);
        dutyFactorFlowDTO.setDutyCode("dutyCode");
        dutyFactorFlowDTO.setDutyFactorCode("dutyFactorCode");
        dutyFactorFlowDTO.setDutyFactorName("dutyFactorName");
        dutyFactorFlowDTO.setFactorValue("factorValue");
        dutyFactorFlowDTO.setFactorName("factorName");
        dutyFactorFlowDTO.setOptionName("optionName");
        dutyFactorFlowDTO.setFlow("flow");
        dutyFactorFlowDTO.setIsDefault(0);
        final List<DutyFactorFlowDTO> dutyFactorFlowDTOS = Arrays.asList(dutyFactorFlowDTO);
        when(mockPolicyMapper.getDutyFactorFlows(0)).thenReturn(dutyFactorFlowDTOS);

        // Configure PolicyMapper.queryQuoteLimit(...).
        final QuoteLimitDTO quoteLimitDTO = new QuoteLimitDTO();
        quoteLimitDTO.setSpqlId(0);
        quoteLimitDTO.setProductId(0);
        quoteLimitDTO.setLimitType("limitType");
        quoteLimitDTO.setOccupationGroup("occupationGroup");
        quoteLimitDTO.setMinPerQty(0);
        final List<QuoteLimitDTO> quoteLimitDTOS = Arrays.asList(quoteLimitDTO);
        when(mockPolicyMapper.queryQuoteLimit(0)).thenReturn(quoteLimitDTOS);

        when(mockProductMapper.queryPremiumFactor(0, 0, "channelCode")).thenReturn(null);

        // Run the test
        policyServiceUnderTest.queryProductInfo(0, "region");

    }

    @Test
    public void testQueryProductInfo_ProductMapperReturnsNoItems() {
        // Setup
        final ProductDetailVo expectedResult = new ProductDetailVo();
        expectedResult.setValidPeriod("validPeriod");
        expectedResult.setPolicyHolderCondition("policyHolderCondition");
        expectedResult.setProductSpecialsJoin(new String[]{"productSpecialsJoin"});
        expectedResult.setProductTags(new String[]{"productTags"});
        final PlanVo planVo = new PlanVo();
        planVo.setId(0);
        planVo.setPlanName("planName");
        planVo.setPlanCode("planCode");
        planVo.setPlanType("planType");
        planVo.setMinPremium(new BigDecimal("0.00"));
        planVo.setCommission(new BigDecimal("0.00"));
        final CoverageVo coverageVo = new CoverageVo();
        coverageVo.setSpcId(0);
        final CoverageAmountVo coverageAmountVo = new CoverageAmountVo();
        coverageAmountVo.setSpcId(0);
        coverageVo.setAmounts(Arrays.asList(coverageAmountVo));
        final DutyFactorFlowVo dutyFactorFlowVo = new DutyFactorFlowVo();
        dutyFactorFlowVo.setSpcId(0);
        dutyFactorFlowVo.setDutyCode("dutyCode");
        dutyFactorFlowVo.setDutyFactorName("dutyFactorName");
        dutyFactorFlowVo.setDutyFactorCode("dutyFactorCode");
        coverageVo.setDutyFlowFactor(Arrays.asList(dutyFactorFlowVo));
        planVo.setCoverages(Arrays.asList(coverageVo));
        expectedResult.setPlans(Arrays.asList(planVo));
        expectedResult.setSalesMode(new String[]{"salesMode"});
        final ShowTips tips = new ShowTips();
        tips.setMessage("此产品暂不支持对公转账，如有此需求请登录i云保进行询价及投保，保费以i云保展示为准。");
        expectedResult.setTips(tips);
        final TimeFactorItem timeFactorItem = new TimeFactorItem();
        timeFactorItem.setFactorValue("enterpriseRiskFactor");
        timeFactorItem.setTimeUnit("unit");
        timeFactorItem.setFlow("flow");
        expectedResult.setTimeFactorItems(Arrays.asList(timeFactorItem));
        expectedResult.setProductVersion("productVersion");

        // Configure PolicyMapper.getProductDetailById(...).
        final ProductDTO productDTO = new ProductDTO();
        productDTO.setProductTags("productTags");
        productDTO.setAttentions("attentions");
        productDTO.setHealthNotification("content");
        productDTO.setProductSpecialsJoin("productSpecialsJoin");
        productDTO.setGlProductNotice("glProductNotice");
        productDTO.setSalesMode("salesMode");
        when(mockPolicyMapper.getProductDetailById(0)).thenReturn(productDTO);

        when(mockProductVersionMapper.getMaxVersion(0)).thenReturn(0);

        // Configure PolicyMapper.getPlans(...).
        final PlanDTO planDTO = new PlanDTO();
        planDTO.setId(0);
        planDTO.setPlanName("planName");
        planDTO.setPlanCode("planCode");
        planDTO.setPlanType("planType");
        planDTO.setMinPremium(new BigDecimal("0.00"));
        final List<PlanDTO> planDTOS = Arrays.asList(planDTO);
        when(mockPolicyMapper.getPlans(0, "region")).thenReturn(planDTOS);

        // Configure SmCommissionMapper.getCommissionSettingByPlanIds(...).
        final SmCommissionSettingVO smCommissionSettingVO = new SmCommissionSettingVO();
        smCommissionSettingVO.setId(0);
        smCommissionSettingVO.setPlanId(0);
        smCommissionSettingVO.setPaymentProportion(new BigDecimal("0.00"));
        smCommissionSettingVO.setSettlementProportion(new BigDecimal("0.00"));
        smCommissionSettingVO.setSaleLevel("saleLevel");
        final List<SmCommissionSettingVO> smCommissionSettingVOS = Arrays.asList(smCommissionSettingVO);
        when(mockCommissionMapper.getCommissionSettingByPlanIds(Arrays.asList(0),
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).thenReturn(smCommissionSettingVOS);

        // Configure PolicyMapper.getCoverage(...).
        final CoverageVo coverageVo1 = new CoverageVo();
        coverageVo1.setSpcId(0);
        final CoverageAmountVo coverageAmountVo1 = new CoverageAmountVo();
        coverageAmountVo1.setSpcId(0);
        coverageVo1.setAmounts(Arrays.asList(coverageAmountVo1));
        final DutyFactorFlowVo dutyFactorFlowVo1 = new DutyFactorFlowVo();
        dutyFactorFlowVo1.setSpcId(0);
        dutyFactorFlowVo1.setDutyCode("dutyCode");
        dutyFactorFlowVo1.setDutyFactorName("dutyFactorName");
        dutyFactorFlowVo1.setDutyFactorCode("dutyFactorCode");
        coverageVo1.setDutyFlowFactor(Arrays.asList(dutyFactorFlowVo1));
        final List<CoverageVo> coverageVos = Arrays.asList(coverageVo1);
        when(mockPolicyMapper.getCoverage(0)).thenReturn(coverageVos);

        // Configure PolicyMapper.getCoverageAmounts(...).
        final CoverageAmountVo coverageAmountVo2 = new CoverageAmountVo();
        coverageAmountVo2.setSpcaId(0);
        coverageAmountVo2.setSpcId(0);
        coverageAmountVo2.setCvgAmount(new BigDecimal("0.00"));
        coverageAmountVo2.setCvgNotice("cvgNotice");
        final List<CoverageAmountVo> coverageAmountVos = Arrays.asList(coverageAmountVo2);
        when(mockPolicyMapper.getCoverageAmounts(0)).thenReturn(coverageAmountVos);

        // Configure PolicyMapper.getDutyFactorFlows(...).
        final DutyFactorFlowDTO dutyFactorFlowDTO = new DutyFactorFlowDTO();
        dutyFactorFlowDTO.setId(0);
        dutyFactorFlowDTO.setSpcId(0);
        dutyFactorFlowDTO.setDutyCode("dutyCode");
        dutyFactorFlowDTO.setDutyFactorCode("dutyFactorCode");
        dutyFactorFlowDTO.setDutyFactorName("dutyFactorName");
        dutyFactorFlowDTO.setFactorValue("factorValue");
        dutyFactorFlowDTO.setFactorName("factorName");
        dutyFactorFlowDTO.setOptionName("optionName");
        dutyFactorFlowDTO.setFlow("flow");
        dutyFactorFlowDTO.setIsDefault(0);
        final List<DutyFactorFlowDTO> dutyFactorFlowDTOS = Arrays.asList(dutyFactorFlowDTO);
        when(mockPolicyMapper.getDutyFactorFlows(0)).thenReturn(dutyFactorFlowDTOS);

        // Configure PolicyMapper.queryQuoteLimit(...).
        final QuoteLimitDTO quoteLimitDTO = new QuoteLimitDTO();
        quoteLimitDTO.setSpqlId(0);
        quoteLimitDTO.setProductId(0);
        quoteLimitDTO.setLimitType("limitType");
        quoteLimitDTO.setOccupationGroup("occupationGroup");
        quoteLimitDTO.setMinPerQty(0);
        final List<QuoteLimitDTO> quoteLimitDTOS = Arrays.asList(quoteLimitDTO);
        when(mockPolicyMapper.queryQuoteLimit(0)).thenReturn(quoteLimitDTOS);

        when(mockProductMapper.queryPremiumFactor(0, 0, "channelCode")).thenReturn(Collections.emptyList());

        // Run the test
        policyServiceUnderTest.queryProductInfo(0, "region");
    }

    @Test
    public void testFixPeriod2ShortTerm() {
        assertThat(policyServiceUnderTest.fixPeriod2ShortTerm("period")).isEqualTo("period");
    }

    @Test
    public void testBuildQuoteLimitText() {
        // Setup
        // Configure PolicyMapper.queryQuoteLimit(...).
        final QuoteLimitDTO quoteLimitDTO = new QuoteLimitDTO();
        quoteLimitDTO.setSpqlId(0);
        quoteLimitDTO.setProductId(0);
        quoteLimitDTO.setLimitType("limitType");
        quoteLimitDTO.setOccupationGroup("occupationGroup");
        quoteLimitDTO.setMinPerQty(0);
        final List<QuoteLimitDTO> quoteLimitDTOS = Arrays.asList(quoteLimitDTO);
        when(mockPolicyMapper.queryQuoteLimit(0)).thenReturn(quoteLimitDTOS);

        // Run the test
        policyServiceUnderTest.buildQuoteLimitText(0);

    }

    @Test
    public void testBuildQuoteLimitText_PolicyMapperReturnsNoItems() {
        // Setup
        when(mockPolicyMapper.queryQuoteLimit(0)).thenReturn(Collections.emptyList());

        // Run the test
        policyServiceUnderTest.buildQuoteLimitText(0);

    }

    @Test
    public void testQueryNotice() {
        // Setup
        when(mockPolicyMapper.getNotice(0)).thenReturn("result");

        // Run the test
         policyServiceUnderTest.queryNotice(0);
    }




    @Test
    public void testQuerySelfConfirm_PolicyMapperReturnsNoItems() {
        // Setup
        when(mockPolicyMapper.querySelfConfirm(0)).thenReturn(Collections.emptyList());

        // Run the test
        final List<SelfConfirmVo> result = policyServiceUnderTest.querySelfConfirm(0);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testBuildCoverage() {
        // Setup
        final CoverageVo coverageVo = new CoverageVo();
        coverageVo.setSpcId(0);
        final CoverageAmountVo coverageAmountVo = new CoverageAmountVo();
        coverageAmountVo.setSpcId(0);
        coverageVo.setAmounts(Arrays.asList(coverageAmountVo));
        final DutyFactorFlowVo dutyFactorFlowVo = new DutyFactorFlowVo();
        dutyFactorFlowVo.setSpcId(0);
        dutyFactorFlowVo.setDutyCode("dutyCode");
        dutyFactorFlowVo.setDutyFactorName("dutyFactorName");
        dutyFactorFlowVo.setDutyFactorCode("dutyFactorCode");
        coverageVo.setDutyFlowFactor(Arrays.asList(dutyFactorFlowVo));
        final List<CoverageVo> expectedResult = Arrays.asList(coverageVo);

        // Configure PolicyMapper.getCoverage(...).
        final CoverageVo coverageVo1 = new CoverageVo();
        coverageVo1.setSpcId(0);
        final CoverageAmountVo coverageAmountVo1 = new CoverageAmountVo();
        coverageAmountVo1.setSpcId(0);
        coverageVo1.setAmounts(Arrays.asList(coverageAmountVo1));
        final DutyFactorFlowVo dutyFactorFlowVo1 = new DutyFactorFlowVo();
        dutyFactorFlowVo1.setSpcId(0);
        dutyFactorFlowVo1.setDutyCode("dutyCode");
        dutyFactorFlowVo1.setDutyFactorName("dutyFactorName");
        dutyFactorFlowVo1.setDutyFactorCode("dutyFactorCode");
        coverageVo1.setDutyFlowFactor(Arrays.asList(dutyFactorFlowVo1));
        final List<CoverageVo> coverageVos = Arrays.asList(coverageVo1);
        when(mockPolicyMapper.getCoverage(0)).thenReturn(coverageVos);

        // Configure PolicyMapper.getCoverageAmounts(...).
        final CoverageAmountVo coverageAmountVo2 = new CoverageAmountVo();
        coverageAmountVo2.setSpcaId(0);
        coverageAmountVo2.setSpcId(0);
        coverageAmountVo2.setCvgAmount(new BigDecimal("0.00"));
        coverageAmountVo2.setCvgNotice("cvgNotice");
        final List<CoverageAmountVo> coverageAmountVos = Arrays.asList(coverageAmountVo2);
        when(mockPolicyMapper.getCoverageAmounts(0)).thenReturn(coverageAmountVos);

        // Configure PolicyMapper.getDutyFactorFlows(...).
        final DutyFactorFlowDTO dutyFactorFlowDTO = new DutyFactorFlowDTO();
        dutyFactorFlowDTO.setId(0);
        dutyFactorFlowDTO.setSpcId(0);
        dutyFactorFlowDTO.setDutyCode("dutyCode");
        dutyFactorFlowDTO.setDutyFactorCode("dutyFactorCode");
        dutyFactorFlowDTO.setDutyFactorName("dutyFactorName");
        dutyFactorFlowDTO.setFactorValue("factorValue");
        dutyFactorFlowDTO.setFactorName("factorName");
        dutyFactorFlowDTO.setOptionName("optionName");
        dutyFactorFlowDTO.setFlow("flow");
        dutyFactorFlowDTO.setIsDefault(0);
        final List<DutyFactorFlowDTO> dutyFactorFlowDTOS = Arrays.asList(dutyFactorFlowDTO);
        when(mockPolicyMapper.getDutyFactorFlows(0)).thenReturn(dutyFactorFlowDTOS);

        policyServiceUnderTest.buildCoverage(0);

    }

    @Test
    public void testBuildCoverage_PolicyMapperGetCoverageReturnsNull() {
        // Setup
        when(mockPolicyMapper.getCoverage(0)).thenReturn(null);

        // Run the test
        final List<CoverageVo> result = policyServiceUnderTest.buildCoverage(0);

        // Verify the results
        assertThat(result).isNull();
    }

    @Test
    public void testBuildCoverage_PolicyMapperGetCoverageReturnsNoItems() {
        // Setup
        when(mockPolicyMapper.getCoverage(0)).thenReturn(Collections.emptyList());

        // Configure PolicyMapper.getCoverageAmounts(...).
        final CoverageAmountVo coverageAmountVo = new CoverageAmountVo();
        coverageAmountVo.setSpcaId(0);
        coverageAmountVo.setSpcId(0);
        coverageAmountVo.setCvgAmount(new BigDecimal("0.00"));
        coverageAmountVo.setCvgNotice("cvgNotice");
        final List<CoverageAmountVo> coverageAmountVos = Arrays.asList(coverageAmountVo);
        when(mockPolicyMapper.getCoverageAmounts(0)).thenReturn(coverageAmountVos);

        // Configure PolicyMapper.getDutyFactorFlows(...).
        final DutyFactorFlowDTO dutyFactorFlowDTO = new DutyFactorFlowDTO();
        dutyFactorFlowDTO.setId(0);
        dutyFactorFlowDTO.setSpcId(0);
        dutyFactorFlowDTO.setDutyCode("dutyCode");
        dutyFactorFlowDTO.setDutyFactorCode("dutyFactorCode");
        dutyFactorFlowDTO.setDutyFactorName("dutyFactorName");
        dutyFactorFlowDTO.setFactorValue("factorValue");
        dutyFactorFlowDTO.setFactorName("factorName");
        dutyFactorFlowDTO.setOptionName("optionName");
        dutyFactorFlowDTO.setFlow("flow");
        dutyFactorFlowDTO.setIsDefault(0);
        final List<DutyFactorFlowDTO> dutyFactorFlowDTOS = Arrays.asList(dutyFactorFlowDTO);
        when(mockPolicyMapper.getDutyFactorFlows(0)).thenReturn(dutyFactorFlowDTOS);

        // Run the test
        final List<CoverageVo> result = policyServiceUnderTest.buildCoverage(0);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testBuildCoverage_PolicyMapperGetCoverageAmountsReturnsNoItems() {
        // Setup
        // Configure PolicyMapper.getCoverage(...).
        final CoverageVo coverageVo = new CoverageVo();
        coverageVo.setSpcId(0);
        final CoverageAmountVo coverageAmountVo = new CoverageAmountVo();
        coverageAmountVo.setSpcId(0);
        coverageVo.setAmounts(Arrays.asList(coverageAmountVo));
        final DutyFactorFlowVo dutyFactorFlowVo = new DutyFactorFlowVo();
        dutyFactorFlowVo.setSpcId(0);
        dutyFactorFlowVo.setDutyCode("dutyCode");
        dutyFactorFlowVo.setDutyFactorName("dutyFactorName");
        dutyFactorFlowVo.setDutyFactorCode("dutyFactorCode");
        coverageVo.setDutyFlowFactor(Arrays.asList(dutyFactorFlowVo));
        final List<CoverageVo> coverageVos = Arrays.asList(coverageVo);
        when(mockPolicyMapper.getCoverage(0)).thenReturn(coverageVos);

        when(mockPolicyMapper.getCoverageAmounts(0)).thenReturn(Collections.emptyList());

        // Configure PolicyMapper.getDutyFactorFlows(...).
        final DutyFactorFlowDTO dutyFactorFlowDTO = new DutyFactorFlowDTO();
        dutyFactorFlowDTO.setId(0);
        dutyFactorFlowDTO.setSpcId(0);
        dutyFactorFlowDTO.setDutyCode("dutyCode");
        dutyFactorFlowDTO.setDutyFactorCode("dutyFactorCode");
        dutyFactorFlowDTO.setDutyFactorName("dutyFactorName");
        dutyFactorFlowDTO.setFactorValue("factorValue");
        dutyFactorFlowDTO.setFactorName("factorName");
        dutyFactorFlowDTO.setOptionName("optionName");
        dutyFactorFlowDTO.setFlow("flow");
        dutyFactorFlowDTO.setIsDefault(0);
        final List<DutyFactorFlowDTO> dutyFactorFlowDTOS = Arrays.asList(dutyFactorFlowDTO);
        when(mockPolicyMapper.getDutyFactorFlows(0)).thenReturn(dutyFactorFlowDTOS);

        policyServiceUnderTest.buildCoverage(0);
    }

    @Test
    public void testBuildCoverage_PolicyMapperGetDutyFactorFlowsReturnsNoItems() {
        // Setup
        // Configure PolicyMapper.getCoverage(...).
        final CoverageVo coverageVo = new CoverageVo();
        coverageVo.setSpcId(0);
        final CoverageAmountVo coverageAmountVo = new CoverageAmountVo();
        coverageAmountVo.setSpcId(0);
        coverageVo.setAmounts(Arrays.asList(coverageAmountVo));
        final DutyFactorFlowVo dutyFactorFlowVo = new DutyFactorFlowVo();
        dutyFactorFlowVo.setSpcId(0);
        dutyFactorFlowVo.setDutyCode("dutyCode");
        dutyFactorFlowVo.setDutyFactorName("dutyFactorName");
        dutyFactorFlowVo.setDutyFactorCode("dutyFactorCode");
        coverageVo.setDutyFlowFactor(Arrays.asList(dutyFactorFlowVo));
        final List<CoverageVo> coverageVos = Arrays.asList(coverageVo);
        when(mockPolicyMapper.getCoverage(0)).thenReturn(coverageVos);

        // Configure PolicyMapper.getCoverageAmounts(...).
        final CoverageAmountVo coverageAmountVo1 = new CoverageAmountVo();
        coverageAmountVo1.setSpcaId(0);
        coverageAmountVo1.setSpcId(0);
        coverageAmountVo1.setCvgAmount(new BigDecimal("0.00"));
        coverageAmountVo1.setCvgNotice("cvgNotice");
        final List<CoverageAmountVo> coverageAmountVos = Arrays.asList(coverageAmountVo1);
        when(mockPolicyMapper.getCoverageAmounts(0)).thenReturn(coverageAmountVos);

        when(mockPolicyMapper.getDutyFactorFlows(0)).thenReturn(Collections.emptyList());

        policyServiceUnderTest.buildCoverage(0);
        Assert.assertTrue(Boolean.TRUE);
    }

    @Test
    public void testConvertFlow2Map() {
        // Setup
        final DutyFactorFlowDTO dutyFactorFlowDTO = new DutyFactorFlowDTO();
        dutyFactorFlowDTO.setId(0);
        dutyFactorFlowDTO.setSpcId(0);
        dutyFactorFlowDTO.setDutyCode("dutyCode");
        dutyFactorFlowDTO.setDutyFactorCode("dutyFactorCode");
        dutyFactorFlowDTO.setDutyFactorName("dutyFactorName");
        dutyFactorFlowDTO.setFactorValue("factorValue");
        dutyFactorFlowDTO.setFactorName("factorName");
        dutyFactorFlowDTO.setOptionName("optionName");
        dutyFactorFlowDTO.setFlow("flow");
        dutyFactorFlowDTO.setIsDefault(0);
        final List<DutyFactorFlowDTO> flows = Arrays.asList(dutyFactorFlowDTO);
        final Map<Integer, Collection<DutyFactorFlowVo>> expectedResult = new HashMap<>();

        policyServiceUnderTest.convertFlow2Map(flows);
    }

    @Test
    public void testConvert2Vo() {
        // Setup
        final PlanDTO planDTO = new PlanDTO();
        planDTO.setId(0);
        planDTO.setPlanName("planName");
        planDTO.setPlanCode("planCode");
        planDTO.setPlanType("planType");
        planDTO.setMinPremium(new BigDecimal("0.00"));
        final List<PlanDTO> planDTOS = Arrays.asList(planDTO);
        final PlanVo planVo = new PlanVo();
        planVo.setId(0);
        planVo.setPlanName("planName");
        planVo.setPlanCode("planCode");
        planVo.setPlanType("planType");
        planVo.setMinPremium(new BigDecimal("0.00"));
        planVo.setCommission(new BigDecimal("0.00"));
        final CoverageVo coverageVo = new CoverageVo();
        coverageVo.setSpcId(0);
        final CoverageAmountVo coverageAmountVo = new CoverageAmountVo();
        coverageAmountVo.setSpcId(0);
        coverageVo.setAmounts(Arrays.asList(coverageAmountVo));
        final DutyFactorFlowVo dutyFactorFlowVo = new DutyFactorFlowVo();
        dutyFactorFlowVo.setSpcId(0);
        dutyFactorFlowVo.setDutyCode("dutyCode");
        dutyFactorFlowVo.setDutyFactorName("dutyFactorName");
        dutyFactorFlowVo.setDutyFactorCode("dutyFactorCode");
        coverageVo.setDutyFlowFactor(Arrays.asList(dutyFactorFlowVo));
        planVo.setCoverages(Arrays.asList(coverageVo));
        final List<PlanVo> expectedResult = Arrays.asList(planVo);

        // Configure SmCommissionMapper.getCommissionSettingByPlanIds(...).
        final SmCommissionSettingVO smCommissionSettingVO = new SmCommissionSettingVO();
        smCommissionSettingVO.setId(0);
        smCommissionSettingVO.setPlanId(0);
        smCommissionSettingVO.setPaymentProportion(new BigDecimal("0.00"));
        smCommissionSettingVO.setSettlementProportion(new BigDecimal("0.00"));
        smCommissionSettingVO.setSaleLevel("saleLevel");
        final List<SmCommissionSettingVO> smCommissionSettingVOS = Arrays.asList(smCommissionSettingVO);
        when(mockCommissionMapper.getCommissionSettingByPlanIds(Arrays.asList(0),
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).thenReturn(smCommissionSettingVOS);

        policyServiceUnderTest.convert2Vo(planDTOS);
    }

    @Test
    public void testConvert2Vo_SmCommissionMapperReturnsNull() {
        // Setup
        final PlanDTO planDTO = new PlanDTO();
        planDTO.setId(0);
        planDTO.setPlanName("planName");
        planDTO.setPlanCode("planCode");
        planDTO.setPlanType("planType");
        planDTO.setMinPremium(new BigDecimal("0.00"));
        final List<PlanDTO> planDTOS = Arrays.asList(planDTO);
        final PlanVo planVo = new PlanVo();
        planVo.setId(0);
        planVo.setPlanName("planName");
        planVo.setPlanCode("planCode");
        planVo.setPlanType("planType");
        planVo.setMinPremium(new BigDecimal("0.00"));
        planVo.setCommission(new BigDecimal("0.00"));
        final CoverageVo coverageVo = new CoverageVo();
        coverageVo.setSpcId(0);
        final CoverageAmountVo coverageAmountVo = new CoverageAmountVo();
        coverageAmountVo.setSpcId(0);
        coverageVo.setAmounts(Arrays.asList(coverageAmountVo));
        final DutyFactorFlowVo dutyFactorFlowVo = new DutyFactorFlowVo();
        dutyFactorFlowVo.setSpcId(0);
        dutyFactorFlowVo.setDutyCode("dutyCode");
        dutyFactorFlowVo.setDutyFactorName("dutyFactorName");
        dutyFactorFlowVo.setDutyFactorCode("dutyFactorCode");
        coverageVo.setDutyFlowFactor(Arrays.asList(dutyFactorFlowVo));
        planVo.setCoverages(Arrays.asList(coverageVo));
        final List<PlanVo> expectedResult = Arrays.asList(planVo);
        when(mockCommissionMapper.getCommissionSettingByPlanIds(Arrays.asList(0),
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).thenReturn(null);

         policyServiceUnderTest.convert2Vo(planDTOS);
    }

    @Test
    public void testConvert2Vo_SmCommissionMapperReturnsNoItems() {
        // Setup
        final PlanDTO planDTO = new PlanDTO();
        planDTO.setId(0);
        planDTO.setPlanName("planName");
        planDTO.setPlanCode("planCode");
        planDTO.setPlanType("planType");
        planDTO.setMinPremium(new BigDecimal("0.00"));
        final List<PlanDTO> planDTOS = Arrays.asList(planDTO);
        when(mockCommissionMapper.getCommissionSettingByPlanIds(Arrays.asList(0),
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).thenReturn(Collections.emptyList());

        policyServiceUnderTest.convert2Vo(planDTOS);
        Assert.assertTrue(Boolean.TRUE);
    }

    @Test
    public void testGetPlans() {
        // Setup
        final PlanDTO planDTO = new PlanDTO();
        planDTO.setId(0);
        planDTO.setPlanName("planName");
        planDTO.setPlanCode("planCode");
        planDTO.setPlanType("planType");
        planDTO.setMinPremium(new BigDecimal("0.00"));
        final List<PlanDTO> expectedResult = Arrays.asList(planDTO);

        // Configure PolicyMapper.getPlans(...).
        final PlanDTO planDTO1 = new PlanDTO();
        planDTO1.setId(0);
        planDTO1.setPlanName("planName");
        planDTO1.setPlanCode("planCode");
        planDTO1.setPlanType("planType");
        planDTO1.setMinPremium(new BigDecimal("0.00"));
        final List<PlanDTO> planDTOS = Arrays.asList(planDTO1);
        when(mockPolicyMapper.getPlans(0, "region")).thenReturn(planDTOS);

        // Run the test
        final List<PlanDTO> result = policyServiceUnderTest.getPlans(0, "region");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetPlans_PolicyMapperReturnsNoItems() {
        // Setup
        final PlanDTO planDTO = new PlanDTO();
        planDTO.setId(0);
        planDTO.setPlanName("planName");
        planDTO.setPlanCode("planCode");
        planDTO.setPlanType("planType");
        planDTO.setMinPremium(new BigDecimal("0.00"));
        final List<PlanDTO> expectedResult = Arrays.asList(planDTO);
        when(mockPolicyMapper.getPlans(0, "region")).thenReturn(Collections.emptyList());

        policyServiceUnderTest.getPlans(0, "region");
    }

    @Test
    public void testQueryHealth() {
        // Setup
        final HealthNoticeVo expectedResult = new HealthNoticeVo();
        expectedResult.setContent("content");
        final QuestionVo questionVo = new QuestionVo();
        questionVo.setCode("code");
        questionVo.setQuestion("question");
        expectedResult.setQuestions(Arrays.asList(questionVo));

        // Configure PolicyMapper.getProductDetailById(...).
        final ProductDTO productDTO = new ProductDTO();
        productDTO.setProductTags("productTags");
        productDTO.setAttentions("attentions");
        productDTO.setHealthNotification("content");
        productDTO.setProductSpecialsJoin("productSpecialsJoin");
        productDTO.setGlProductNotice("glProductNotice");
        productDTO.setSalesMode("salesMode");
        when(mockPolicyMapper.getProductDetailById(0)).thenReturn(productDTO);

        // Configure PolicyMapper.queryHealth(...).
        final QuestionVo questionVo1 = new QuestionVo();
        questionVo1.setCode("code");
        questionVo1.setQuestion("question");
        final List<QuestionVo> questionVos = Arrays.asList(questionVo1);
        when(mockPolicyMapper.queryHealth(0)).thenReturn(questionVos);

        policyServiceUnderTest.queryHealth(0);
    }

    @Test
    public void testQueryHealth_PolicyMapperGetProductDetailByIdReturnsNull() {
        // Setup
        final HealthNoticeVo expectedResult = new HealthNoticeVo();
        expectedResult.setContent("content");
        final QuestionVo questionVo = new QuestionVo();
        questionVo.setCode("code");
        questionVo.setQuestion("question");
        expectedResult.setQuestions(Arrays.asList(questionVo));

        when(mockPolicyMapper.getProductDetailById(0)).thenReturn(null);

        // Configure PolicyMapper.queryHealth(...).
        final QuestionVo questionVo1 = new QuestionVo();
        questionVo1.setCode("code");
        questionVo1.setQuestion("question");
        final List<QuestionVo> questionVos = Arrays.asList(questionVo1);
        when(mockPolicyMapper.queryHealth(0)).thenReturn(questionVos);
        policyServiceUnderTest.queryHealth(0);
    }

    @Test
    public void testQueryHealth_PolicyMapperQueryHealthReturnsNoItems() {
        // Setup
        final HealthNoticeVo expectedResult = new HealthNoticeVo();
        expectedResult.setContent("content");
        final QuestionVo questionVo = new QuestionVo();
        questionVo.setCode("code");
        questionVo.setQuestion("question");
        expectedResult.setQuestions(Arrays.asList(questionVo));

        // Configure PolicyMapper.getProductDetailById(...).
        final ProductDTO productDTO = new ProductDTO();
        productDTO.setProductTags("productTags");
        productDTO.setAttentions("attentions");
        productDTO.setHealthNotification("content");
        productDTO.setProductSpecialsJoin("productSpecialsJoin");
        productDTO.setGlProductNotice("glProductNotice");
        productDTO.setSalesMode("salesMode");
        when(mockPolicyMapper.getProductDetailById(0)).thenReturn(productDTO);

        when(mockPolicyMapper.queryHealth(0)).thenReturn(Collections.emptyList());

        policyServiceUnderTest.queryHealth(0);
    }

    @Test
    public void testQuotePrice4Group() {
        // Setup
        final GroupUnderwriting req = new GroupUnderwriting();
        final SmCommissionSettingVO commission = new SmCommissionSettingVO();
        commission.setPlanId(0);
        commission.setPaymentProportion(new BigDecimal("0.00"));
        req.setCommission(commission);
        final Agent agent = new Agent();
        agent.setJobCode("jobCode");
        agent.setRecommendId("recommendId");
        agent.setRecommendJobCode("customerAdminJobCode");
        agent.setRecommendMainJobNumber("customerAdminMainJobNumber");
        agent.setCustomerAdminMainJobNumber("customerAdminMainJobNumber");
        agent.setCustomerAdminJobCode("customerAdminJobCode");
        agent.setRecommendOrgCode("customerAdminOrgCode");
        agent.setCustomerAdminOrgCode("customerAdminOrgCode");
        agent.setRecommendMasterName("recommendMasterName");
        agent.setRecommendAdminName("recommendAdminName");
        agent.setRecommendEntryDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        agent.setRecommendPostName("postName");
        req.setAgent(agent);
        req.setEnterpriseRiskFactor("enterpriseRiskFactor");
        final SysDutyConfig sysDutyConfig = new SysDutyConfig();
        req.setSysDutys(Arrays.asList(sysDutyConfig));
        req.setChannel("channel");
        req.setProductId(0);
        req.setBizCode("bizCode");
        req.setStartTime("startTime");
        req.setSubChannel("subChannel");
        final GroupInsured groupInsured = new GroupInsured();
        groupInsured.setActiveBranchId("activeBranchId");
        groupInsured.setIdNumber("idNumber");
        req.setInsuredList(Arrays.asList(groupInsured));
        final ProductReq product = new ProductReq();
        product.setPlanId(0);
        req.setProduct(product);
        req.setSubmitTime("submitTime");

        final GroupQuoteResponse expectedResult = new GroupQuoteResponse();
        expectedResult.setOrderId("orderId");
        expectedResult.setPolicyNo("policyNo");
        expectedResult.setProposalNo("proposalNo");
        expectedResult.setEffectiveDate("effectiveDate");
        expectedResult.setExpireDate("expireDate");

        // Configure SmCommissionMapper.listCommissionSettingsByProductId(...).
        final SmCommissionSettingVO smCommissionSettingVO = new SmCommissionSettingVO();
        smCommissionSettingVO.setId(0);
        smCommissionSettingVO.setPlanId(0);
        smCommissionSettingVO.setPaymentProportion(new BigDecimal("0.00"));
        smCommissionSettingVO.setSettlementProportion(new BigDecimal("0.00"));
        smCommissionSettingVO.setSaleLevel("saleLevel");
        final List<SmCommissionSettingVO> smCommissionSettingVOS = Arrays.asList(smCommissionSettingVO);
        when(mockCommissionMapper.listCommissionSettingsByProductId(0,
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).thenReturn(smCommissionSettingVOS);

        // Configure ProductMapper.queryPremiumFactor(...).
        final PremiumFlow premiumFlow = new PremiumFlow();
        premiumFlow.setId(0);
        premiumFlow.setProductId(0);
        premiumFlow.setPlanId(0);
        premiumFlow.setFactorValue("enterpriseRiskFactor");
        premiumFlow.setFlow("flow");
        final List<PremiumFlow> premiumFlows = Arrays.asList(premiumFlow);
        when(mockProductMapper.queryPremiumFactor(0, 0, "channelCode")).thenReturn(premiumFlows);

        // Configure SmOrderMapper.queryDutyConfig(...).
        final SysDutyConfig sysDutyConfig1 = new SysDutyConfig();
        sysDutyConfig1.setChannel("channel");
        sysDutyConfig1.setType(0);
        sysDutyConfig1.setMainCode("mainCode");
        sysDutyConfig1.setCode("code");
        sysDutyConfig1.setExtendCode("extendCode");
        final List<SysDutyConfig> sysDutyConfigs = Arrays.asList(sysDutyConfig1);
        when(mockOrderMapper.queryDutyConfig("channel", 0)).thenReturn(sysDutyConfigs);
        try {
            policyServiceUnderTest.quotePrice4Group("channel", req);
        }catch (Exception e){
            Assert.assertTrue(e instanceof MSBizNormalException);
        }
    }

    @Test
    public void testQuotePrice4Group_SmCommissionMapperReturnsNoItems() {
        // Setup
        final GroupUnderwriting req = new GroupUnderwriting();
        final SmCommissionSettingVO commission = new SmCommissionSettingVO();
        commission.setPlanId(0);
        commission.setPaymentProportion(new BigDecimal("0.00"));
        req.setCommission(commission);
        final Agent agent = new Agent();
        agent.setJobCode("jobCode");
        agent.setRecommendId("recommendId");
        agent.setRecommendJobCode("customerAdminJobCode");
        agent.setRecommendMainJobNumber("customerAdminMainJobNumber");
        agent.setCustomerAdminMainJobNumber("customerAdminMainJobNumber");
        agent.setCustomerAdminJobCode("customerAdminJobCode");
        agent.setRecommendOrgCode("customerAdminOrgCode");
        agent.setCustomerAdminOrgCode("customerAdminOrgCode");
        agent.setRecommendMasterName("recommendMasterName");
        agent.setRecommendAdminName("recommendAdminName");
        agent.setRecommendEntryDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        agent.setRecommendPostName("postName");
        req.setAgent(agent);
        req.setEnterpriseRiskFactor("enterpriseRiskFactor");
        final SysDutyConfig sysDutyConfig = new SysDutyConfig();
        req.setSysDutys(Arrays.asList(sysDutyConfig));
        req.setChannel("channel");
        req.setProductId(0);
        req.setBizCode("bizCode");
        req.setStartTime("startTime");
        req.setSubChannel("subChannel");
        final GroupInsured groupInsured = new GroupInsured();
        groupInsured.setActiveBranchId("activeBranchId");
        groupInsured.setIdNumber("idNumber");
        req.setInsuredList(Arrays.asList(groupInsured));
        final ProductReq product = new ProductReq();
        product.setPlanId(0);
        req.setProduct(product);
        req.setSubmitTime("submitTime");

        final GroupQuoteResponse expectedResult = new GroupQuoteResponse();
        expectedResult.setOrderId("orderId");
        expectedResult.setPolicyNo("policyNo");
        expectedResult.setProposalNo("proposalNo");
        expectedResult.setEffectiveDate("effectiveDate");
        expectedResult.setExpireDate("expireDate");

        when(mockCommissionMapper.listCommissionSettingsByProductId(0,
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).thenReturn(Collections.emptyList());

        // Configure ProductMapper.queryPremiumFactor(...).
        final PremiumFlow premiumFlow = new PremiumFlow();
        premiumFlow.setId(0);
        premiumFlow.setProductId(0);
        premiumFlow.setPlanId(0);
        premiumFlow.setFactorValue("enterpriseRiskFactor");
        premiumFlow.setFlow("flow");
        final List<PremiumFlow> premiumFlows = Arrays.asList(premiumFlow);
        when(mockProductMapper.queryPremiumFactor(0, 0, "channelCode")).thenReturn(premiumFlows);

        // Configure SmOrderMapper.queryDutyConfig(...).
        final SysDutyConfig sysDutyConfig1 = new SysDutyConfig();
        sysDutyConfig1.setChannel("channel");
        sysDutyConfig1.setType(0);
        sysDutyConfig1.setMainCode("mainCode");
        sysDutyConfig1.setCode("code");
        sysDutyConfig1.setExtendCode("extendCode");
        final List<SysDutyConfig> sysDutyConfigs = Arrays.asList(sysDutyConfig1);
        when(mockOrderMapper.queryDutyConfig("channel", 0)).thenReturn(sysDutyConfigs);

        // Run the test
        try {
            policyServiceUnderTest.quotePrice4Group("channel", req);
        }catch (Exception e){
            Assert.assertTrue(e instanceof MSBizNormalException);
        }
    }

    @Test
    public void testQuotePrice4Group_ProductMapperReturnsNoItems() {
        // Setup
        final GroupUnderwriting req = new GroupUnderwriting();
        final SmCommissionSettingVO commission = new SmCommissionSettingVO();
        commission.setPlanId(0);
        commission.setPaymentProportion(new BigDecimal("0.00"));
        req.setCommission(commission);
        final Agent agent = new Agent();
        agent.setJobCode("jobCode");
        agent.setRecommendId("recommendId");
        agent.setRecommendJobCode("customerAdminJobCode");
        agent.setRecommendMainJobNumber("customerAdminMainJobNumber");
        agent.setCustomerAdminMainJobNumber("customerAdminMainJobNumber");
        agent.setCustomerAdminJobCode("customerAdminJobCode");
        agent.setRecommendOrgCode("customerAdminOrgCode");
        agent.setCustomerAdminOrgCode("customerAdminOrgCode");
        agent.setRecommendMasterName("recommendMasterName");
        agent.setRecommendAdminName("recommendAdminName");
        agent.setRecommendEntryDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        agent.setRecommendPostName("postName");
        req.setAgent(agent);
        req.setEnterpriseRiskFactor("enterpriseRiskFactor");
        final SysDutyConfig sysDutyConfig = new SysDutyConfig();
        req.setSysDutys(Arrays.asList(sysDutyConfig));
        req.setChannel("channel");
        req.setProductId(0);
        req.setBizCode("bizCode");
        req.setStartTime("startTime");
        req.setSubChannel("subChannel");
        final GroupInsured groupInsured = new GroupInsured();
        groupInsured.setActiveBranchId("activeBranchId");
        groupInsured.setIdNumber("idNumber");
        req.setInsuredList(Arrays.asList(groupInsured));
        final ProductReq product = new ProductReq();
        product.setPlanId(0);
        req.setProduct(product);
        req.setSubmitTime("submitTime");

        final GroupQuoteResponse expectedResult = new GroupQuoteResponse();
        expectedResult.setOrderId("orderId");
        expectedResult.setPolicyNo("policyNo");
        expectedResult.setProposalNo("proposalNo");
        expectedResult.setEffectiveDate("effectiveDate");
        expectedResult.setExpireDate("expireDate");

        // Configure SmCommissionMapper.listCommissionSettingsByProductId(...).
        final SmCommissionSettingVO smCommissionSettingVO = new SmCommissionSettingVO();
        smCommissionSettingVO.setId(0);
        smCommissionSettingVO.setPlanId(0);
        smCommissionSettingVO.setPaymentProportion(new BigDecimal("0.00"));
        smCommissionSettingVO.setSettlementProportion(new BigDecimal("0.00"));
        smCommissionSettingVO.setSaleLevel("saleLevel");
        final List<SmCommissionSettingVO> smCommissionSettingVOS = Arrays.asList(smCommissionSettingVO);
        when(mockCommissionMapper.listCommissionSettingsByProductId(0,
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).thenReturn(smCommissionSettingVOS);

        when(mockProductMapper.queryPremiumFactor(0, 0, "channelCode")).thenReturn(Collections.emptyList());

        // Configure SmOrderMapper.queryDutyConfig(...).
        final SysDutyConfig sysDutyConfig1 = new SysDutyConfig();
        sysDutyConfig1.setChannel("channel");
        sysDutyConfig1.setType(0);
        sysDutyConfig1.setMainCode("mainCode");
        sysDutyConfig1.setCode("code");
        sysDutyConfig1.setExtendCode("extendCode");
        final List<SysDutyConfig> sysDutyConfigs = Arrays.asList(sysDutyConfig1);
        when(mockOrderMapper.queryDutyConfig("channel", 0)).thenReturn(sysDutyConfigs);

        try {
            policyServiceUnderTest.quotePrice4Group("channel", req);
        }catch (Exception e){
            Assert.assertTrue(e instanceof MSBizNormalException);
        }

    }

    @Test
    public void testQuotePrice4Group_SmOrderMapperReturnsNull() {
        // Setup
        final GroupUnderwriting req = new GroupUnderwriting();
        final SmCommissionSettingVO commission = new SmCommissionSettingVO();
        commission.setPlanId(0);
        commission.setPaymentProportion(new BigDecimal("0.00"));
        req.setCommission(commission);
        final Agent agent = new Agent();
        agent.setJobCode("jobCode");
        agent.setRecommendId("recommendId");
        agent.setRecommendJobCode("customerAdminJobCode");
        agent.setRecommendMainJobNumber("customerAdminMainJobNumber");
        agent.setCustomerAdminMainJobNumber("customerAdminMainJobNumber");
        agent.setCustomerAdminJobCode("customerAdminJobCode");
        agent.setRecommendOrgCode("customerAdminOrgCode");
        agent.setCustomerAdminOrgCode("customerAdminOrgCode");
        agent.setRecommendMasterName("recommendMasterName");
        agent.setRecommendAdminName("recommendAdminName");
        agent.setRecommendEntryDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        agent.setRecommendPostName("postName");
        req.setAgent(agent);
        req.setEnterpriseRiskFactor("enterpriseRiskFactor");
        final SysDutyConfig sysDutyConfig = new SysDutyConfig();
        req.setSysDutys(Arrays.asList(sysDutyConfig));
        req.setChannel("channel");
        req.setProductId(0);
        req.setBizCode("bizCode");
        req.setStartTime("startTime");
        req.setSubChannel("subChannel");
        final GroupInsured groupInsured = new GroupInsured();
        groupInsured.setActiveBranchId("activeBranchId");
        groupInsured.setIdNumber("idNumber");
        req.setInsuredList(Arrays.asList(groupInsured));
        final ProductReq product = new ProductReq();
        product.setPlanId(0);
        req.setProduct(product);
        req.setSubmitTime("submitTime");

        final GroupQuoteResponse expectedResult = new GroupQuoteResponse();
        expectedResult.setOrderId("orderId");
        expectedResult.setPolicyNo("policyNo");
        expectedResult.setProposalNo("proposalNo");
        expectedResult.setEffectiveDate("effectiveDate");
        expectedResult.setExpireDate("expireDate");

        // Configure SmCommissionMapper.listCommissionSettingsByProductId(...).
        final SmCommissionSettingVO smCommissionSettingVO = new SmCommissionSettingVO();
        smCommissionSettingVO.setId(0);
        smCommissionSettingVO.setPlanId(0);
        smCommissionSettingVO.setPaymentProportion(new BigDecimal("0.00"));
        smCommissionSettingVO.setSettlementProportion(new BigDecimal("0.00"));
        smCommissionSettingVO.setSaleLevel("saleLevel");

        final PremiumFlow premiumFlow = new PremiumFlow();
        premiumFlow.setId(0);
        premiumFlow.setProductId(0);
        premiumFlow.setPlanId(0);
        premiumFlow.setFactorValue("enterpriseRiskFactor");
        premiumFlow.setFlow("flow");
        final List<PremiumFlow> premiumFlows = Arrays.asList(premiumFlow);
        when(mockProductMapper.queryPremiumFactor(0, 0, "channelCode")).thenReturn(premiumFlows);

        when(mockOrderMapper.queryDutyConfig("channel", 0)).thenReturn(null);

        try {
            policyServiceUnderTest.quotePrice4Group("channel", req);
        }catch (Exception e){
            Assert.assertTrue(e instanceof MSBizNormalException);
        }
    }

    @Test
    public void testQuotePrice4Group_SmOrderMapperReturnsNoItems() {
        // Setup
        final GroupUnderwriting req = new GroupUnderwriting();
        final SmCommissionSettingVO commission = new SmCommissionSettingVO();
        commission.setPlanId(0);
        commission.setPaymentProportion(new BigDecimal("0.00"));
        req.setCommission(commission);
        final Agent agent = new Agent();
        agent.setJobCode("jobCode");
        agent.setRecommendId("recommendId");
        agent.setRecommendJobCode("customerAdminJobCode");
        agent.setRecommendMainJobNumber("customerAdminMainJobNumber");
        agent.setCustomerAdminMainJobNumber("customerAdminMainJobNumber");
        agent.setCustomerAdminJobCode("customerAdminJobCode");
        agent.setRecommendOrgCode("customerAdminOrgCode");
        agent.setCustomerAdminOrgCode("customerAdminOrgCode");
        agent.setRecommendMasterName("recommendMasterName");
        agent.setRecommendAdminName("recommendAdminName");
        agent.setRecommendEntryDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        agent.setRecommendPostName("postName");
        req.setAgent(agent);
        req.setEnterpriseRiskFactor("enterpriseRiskFactor");
        final SysDutyConfig sysDutyConfig = new SysDutyConfig();
        req.setSysDutys(Arrays.asList(sysDutyConfig));
        req.setChannel("channel");
        req.setProductId(0);
        req.setBizCode("bizCode");
        req.setStartTime("startTime");
        req.setSubChannel("subChannel");
        final GroupInsured groupInsured = new GroupInsured();
        groupInsured.setActiveBranchId("activeBranchId");
        groupInsured.setIdNumber("idNumber");
        req.setInsuredList(Arrays.asList(groupInsured));
        final ProductReq product = new ProductReq();
        product.setPlanId(0);
        req.setProduct(product);
        req.setSubmitTime("submitTime");

        final GroupQuoteResponse expectedResult = new GroupQuoteResponse();
        expectedResult.setOrderId("orderId");
        expectedResult.setPolicyNo("policyNo");
        expectedResult.setProposalNo("proposalNo");
        expectedResult.setEffectiveDate("effectiveDate");
        expectedResult.setExpireDate("expireDate");

        // Configure SmCommissionMapper.listCommissionSettingsByProductId(...).
        final SmCommissionSettingVO smCommissionSettingVO = new SmCommissionSettingVO();
        smCommissionSettingVO.setId(0);
        smCommissionSettingVO.setPlanId(0);
        smCommissionSettingVO.setPaymentProportion(new BigDecimal("0.00"));
        smCommissionSettingVO.setSettlementProportion(new BigDecimal("0.00"));
        smCommissionSettingVO.setSaleLevel("saleLevel");
        final List<SmCommissionSettingVO> smCommissionSettingVOS = Arrays.asList(smCommissionSettingVO);
        when(mockCommissionMapper.listCommissionSettingsByProductId(0,
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).thenReturn(smCommissionSettingVOS);

        // Configure ProductMapper.queryPremiumFactor(...).
        final PremiumFlow premiumFlow = new PremiumFlow();
        premiumFlow.setId(0);
        premiumFlow.setProductId(0);
        premiumFlow.setPlanId(0);
        premiumFlow.setFactorValue("enterpriseRiskFactor");
        premiumFlow.setFlow("flow");
        final List<PremiumFlow> premiumFlows = Arrays.asList(premiumFlow);
        when(mockProductMapper.queryPremiumFactor(0, 0, "channelCode")).thenReturn(premiumFlows);

        when(mockOrderMapper.queryDutyConfig("channel", 0)).thenReturn(Collections.emptyList());

        try {
            policyServiceUnderTest.quotePrice4Group("channel", req);
        }catch (Exception e){
            Assert.assertTrue(e instanceof MSBizNormalException);
        }

    }

    @Test
    public void testGetCommissionMap_SmCommissionMapperReturnsNoItems() {
        // Setup
        final Map<Integer, SmCommissionSettingVO> expectedResult = new HashMap<>();
        when(mockCommissionMapper.listCommissionSettingsByProductId(0,
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).thenReturn(Collections.emptyList());

        // Run the test
        final Map<Integer, SmCommissionSettingVO> result = policyServiceUnderTest.getCommissionMap(0,
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testUnderwriting() {
        // Setup
        final GroupUnderwriting req = new GroupUnderwriting();
        final SmCommissionSettingVO commission = new SmCommissionSettingVO();
        commission.setPlanId(0);
        commission.setPaymentProportion(new BigDecimal("0.00"));
        req.setCommission(commission);
        final Agent agent = new Agent();
        agent.setJobCode("jobCode");
        agent.setRecommendId("recommendId");
        agent.setRecommendJobCode("customerAdminJobCode");
        agent.setRecommendMainJobNumber("customerAdminMainJobNumber");
        agent.setCustomerAdminMainJobNumber("customerAdminMainJobNumber");
        agent.setCustomerAdminJobCode("customerAdminJobCode");
        agent.setRecommendOrgCode("customerAdminOrgCode");
        agent.setCustomerAdminOrgCode("customerAdminOrgCode");
        agent.setRecommendMasterName("recommendMasterName");
        agent.setRecommendAdminName("recommendAdminName");
        agent.setRecommendEntryDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        agent.setRecommendPostName("postName");
        req.setAgent(agent);
        req.setEnterpriseRiskFactor("enterpriseRiskFactor");
        final SysDutyConfig sysDutyConfig = new SysDutyConfig();
        req.setSysDutys(Arrays.asList(sysDutyConfig));
        req.setChannel("channel");
        req.setProductId(0);
        req.setBizCode("bizCode");
        req.setStartTime("startTime");
        req.setSubChannel("subChannel");
        final GroupInsured groupInsured = new GroupInsured();
        groupInsured.setActiveBranchId("activeBranchId");
        groupInsured.setIdNumber("idNumber");
        req.setInsuredList(Arrays.asList(groupInsured));
        final ProductReq product = new ProductReq();
        product.setPlanId(0);
        req.setProduct(product);
        req.setSubmitTime("submitTime");

        final GroupQuoteResponse expectedResult = new GroupQuoteResponse();
        expectedResult.setOrderId("orderId");
        expectedResult.setPolicyNo("policyNo");
        expectedResult.setProposalNo("proposalNo");
        expectedResult.setEffectiveDate("effectiveDate");
        expectedResult.setExpireDate("expireDate");

        // Configure SmProductService.getPlanById(...).
        final SmPlanVO smPlanVO = new SmPlanVO();
        smPlanVO.setId(0);
        smPlanVO.setPlanId(0);
        smPlanVO.setProductStatus(0);
        smPlanVO.setChannel("channel");
        smPlanVO.setProductId(0);
        when(mockProductService.getPlanById(0)).thenReturn(smPlanVO);

        when(mockNotifyService.isMaintenance("channel", "subChannel")).thenReturn(false);

        // Configure SmProductService.getProductById(...).
        final SmProductDetailVO smProductDetailVO = new SmProductDetailVO();
        smProductDetailVO.setState(0);
        smProductDetailVO.setProductCode("productCode");
        smProductDetailVO.setProductAttrCode("productAttrCode");
        smProductDetailVO.setIntroduceImageUrl("introduceImageUrl");
        smProductDetailVO.setHeadImageUrl("headImageUrl");
        when(mockProductService.getProductById(0)).thenReturn(smProductDetailVO);

        // Configure SmCommissionMapper.listCommissionSettingsByProductId(...).
        final SmCommissionSettingVO smCommissionSettingVO = new SmCommissionSettingVO();
        smCommissionSettingVO.setId(0);
        smCommissionSettingVO.setPlanId(0);
        smCommissionSettingVO.setPaymentProportion(new BigDecimal("0.00"));
        smCommissionSettingVO.setSettlementProportion(new BigDecimal("0.00"));
        smCommissionSettingVO.setSaleLevel("saleLevel");
        final List<SmCommissionSettingVO> smCommissionSettingVOS = Arrays.asList(smCommissionSettingVO);
        when(mockCommissionMapper.listCommissionSettingsByProductId(0,
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).thenReturn(smCommissionSettingVOS);

        // Configure ProductMapper.queryPremiumFactor(...).
        final PremiumFlow premiumFlow = new PremiumFlow();
        premiumFlow.setId(0);
        premiumFlow.setProductId(0);
        premiumFlow.setPlanId(0);
        premiumFlow.setFactorValue("enterpriseRiskFactor");
        premiumFlow.setFlow("flow");
        final List<PremiumFlow> premiumFlows = Arrays.asList(premiumFlow);
        when(mockProductMapper.queryPremiumFactor(0, 0, "channelCode")).thenReturn(premiumFlows);

        when(mockUserMapper.getMainJobNumberByBizCode("bizCode")).thenReturn("recommendId");

        // Configure SmOrderMapper.queryDutyConfig(...).
        final SysDutyConfig sysDutyConfig1 = new SysDutyConfig();
        sysDutyConfig1.setChannel("channel");
        sysDutyConfig1.setType(0);
        sysDutyConfig1.setMainCode("mainCode");
        sysDutyConfig1.setCode("code");
        sysDutyConfig1.setExtendCode("extendCode");
        final List<SysDutyConfig> sysDutyConfigs = Arrays.asList(sysDutyConfig1);
        when(mockOrderMapper.queryDutyConfig("channel", 0)).thenReturn(sysDutyConfigs);

        try {
            policyServiceUnderTest.underwriting("channel", req);
        }catch (Exception e){
            Assert.assertTrue(e instanceof MSBizNormalException);
        }

    }

    @Test
    public void testUnderwriting_SmProductServiceGetPlanByIdReturnsNull() {
        // Setup
        final GroupUnderwriting req = new GroupUnderwriting();
        final SmCommissionSettingVO commission = new SmCommissionSettingVO();
        commission.setPlanId(0);
        commission.setPaymentProportion(new BigDecimal("0.00"));
        req.setCommission(commission);
        final Agent agent = new Agent();
        agent.setJobCode("jobCode");
        agent.setRecommendId("recommendId");
        agent.setRecommendJobCode("customerAdminJobCode");
        agent.setRecommendMainJobNumber("customerAdminMainJobNumber");
        agent.setCustomerAdminMainJobNumber("customerAdminMainJobNumber");
        agent.setCustomerAdminJobCode("customerAdminJobCode");
        agent.setRecommendOrgCode("customerAdminOrgCode");
        agent.setCustomerAdminOrgCode("customerAdminOrgCode");
        agent.setRecommendMasterName("recommendMasterName");
        agent.setRecommendAdminName("recommendAdminName");
        agent.setRecommendEntryDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        agent.setRecommendPostName("postName");
        req.setAgent(agent);
        req.setEnterpriseRiskFactor("enterpriseRiskFactor");
        final SysDutyConfig sysDutyConfig = new SysDutyConfig();
        req.setSysDutys(Arrays.asList(sysDutyConfig));
        req.setChannel("channel");
        req.setProductId(0);
        req.setBizCode("bizCode");
        req.setStartTime("startTime");
        req.setSubChannel("subChannel");
        final GroupInsured groupInsured = new GroupInsured();
        groupInsured.setActiveBranchId("activeBranchId");
        groupInsured.setIdNumber("idNumber");
        req.setInsuredList(Arrays.asList(groupInsured));
        final ProductReq product = new ProductReq();
        product.setPlanId(0);
        req.setProduct(product);
        req.setSubmitTime("submitTime");

        when(mockProductService.getPlanById(0)).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> policyServiceUnderTest.underwriting("channel", req))
                .isInstanceOf(MSBizNormalException.class);
    }

    @Test
    public void testUnderwriting_SysNotifyServiceReturnsTrue() {
        // Setup
        final GroupUnderwriting req = new GroupUnderwriting();
        final SmCommissionSettingVO commission = new SmCommissionSettingVO();
        commission.setPlanId(0);
        commission.setPaymentProportion(new BigDecimal("0.00"));
        req.setCommission(commission);
        final Agent agent = new Agent();
        agent.setJobCode("jobCode");
        agent.setRecommendId("recommendId");
        agent.setRecommendJobCode("customerAdminJobCode");
        agent.setRecommendMainJobNumber("customerAdminMainJobNumber");
        agent.setCustomerAdminMainJobNumber("customerAdminMainJobNumber");
        agent.setCustomerAdminJobCode("customerAdminJobCode");
        agent.setRecommendOrgCode("customerAdminOrgCode");
        agent.setCustomerAdminOrgCode("customerAdminOrgCode");
        agent.setRecommendMasterName("recommendMasterName");
        agent.setRecommendAdminName("recommendAdminName");
        agent.setRecommendEntryDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        agent.setRecommendPostName("postName");
        req.setAgent(agent);
        req.setEnterpriseRiskFactor("enterpriseRiskFactor");
        final SysDutyConfig sysDutyConfig = new SysDutyConfig();
        req.setSysDutys(Arrays.asList(sysDutyConfig));
        req.setChannel("channel");
        req.setProductId(0);
        req.setBizCode("bizCode");
        req.setStartTime("startTime");
        req.setSubChannel("subChannel");
        final GroupInsured groupInsured = new GroupInsured();
        groupInsured.setActiveBranchId("activeBranchId");
        groupInsured.setIdNumber("idNumber");
        req.setInsuredList(Arrays.asList(groupInsured));
        final ProductReq product = new ProductReq();
        product.setPlanId(0);
        req.setProduct(product);
        req.setSubmitTime("submitTime");

        // Configure SmProductService.getPlanById(...).
        final SmPlanVO smPlanVO = new SmPlanVO();
        smPlanVO.setId(0);
        smPlanVO.setPlanId(0);
        smPlanVO.setProductStatus(0);
        smPlanVO.setChannel("channel");
        smPlanVO.setProductId(0);
        when(mockProductService.getPlanById(0)).thenReturn(smPlanVO);

        when(mockNotifyService.isMaintenance("channel", "subChannel")).thenReturn(true);

        // Run the test
        assertThatThrownBy(() -> policyServiceUnderTest.underwriting("channel", req)).isInstanceOf(BizException.class);
    }

    @Test
    public void testUnderwriting_SmProductServiceGetProductByIdReturnsNull() {
        // Setup
        final GroupUnderwriting req = new GroupUnderwriting();
        final SmCommissionSettingVO commission = new SmCommissionSettingVO();
        commission.setPlanId(0);
        commission.setPaymentProportion(new BigDecimal("0.00"));
        req.setCommission(commission);
        final Agent agent = new Agent();
        agent.setJobCode("jobCode");
        agent.setRecommendId("recommendId");
        agent.setRecommendJobCode("customerAdminJobCode");
        agent.setRecommendMainJobNumber("customerAdminMainJobNumber");
        agent.setCustomerAdminMainJobNumber("customerAdminMainJobNumber");
        agent.setCustomerAdminJobCode("customerAdminJobCode");
        agent.setRecommendOrgCode("customerAdminOrgCode");
        agent.setCustomerAdminOrgCode("customerAdminOrgCode");
        agent.setRecommendMasterName("recommendMasterName");
        agent.setRecommendAdminName("recommendAdminName");
        agent.setRecommendEntryDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        agent.setRecommendPostName("postName");
        req.setAgent(agent);
        req.setEnterpriseRiskFactor("enterpriseRiskFactor");
        final SysDutyConfig sysDutyConfig = new SysDutyConfig();
        req.setSysDutys(Arrays.asList(sysDutyConfig));
        req.setChannel("channel");
        req.setProductId(0);
        req.setBizCode("bizCode");
        req.setStartTime("startTime");
        req.setSubChannel("subChannel");
        final GroupInsured groupInsured = new GroupInsured();
        groupInsured.setActiveBranchId("activeBranchId");
        groupInsured.setIdNumber("idNumber");
        req.setInsuredList(Arrays.asList(groupInsured));
        final ProductReq product = new ProductReq();
        product.setPlanId(0);
        req.setProduct(product);
        req.setSubmitTime("submitTime");

        // Configure SmProductService.getPlanById(...).
        final SmPlanVO smPlanVO = new SmPlanVO();
        smPlanVO.setId(0);
        smPlanVO.setPlanId(0);
        smPlanVO.setProductStatus(0);
        smPlanVO.setChannel("channel");
        smPlanVO.setProductId(0);
        when(mockProductService.getPlanById(0)).thenReturn(smPlanVO);

        when(mockNotifyService.isMaintenance("channel", "subChannel")).thenReturn(false);
        when(mockProductService.getProductById(0)).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> policyServiceUnderTest.underwriting("channel", req))
                .isInstanceOf(MSBizNormalException.class);
    }

    @Test
    public void testUnderwriting_SmCommissionMapperReturnsNoItems() {
        // Setup
        final GroupUnderwriting req = new GroupUnderwriting();
        final SmCommissionSettingVO commission = new SmCommissionSettingVO();
        commission.setPlanId(0);
        commission.setPaymentProportion(new BigDecimal("0.00"));
        req.setCommission(commission);
        final Agent agent = new Agent();
        agent.setJobCode("jobCode");
        agent.setRecommendId("recommendId");
        agent.setRecommendJobCode("customerAdminJobCode");
        agent.setRecommendMainJobNumber("customerAdminMainJobNumber");
        agent.setCustomerAdminMainJobNumber("customerAdminMainJobNumber");
        agent.setCustomerAdminJobCode("customerAdminJobCode");
        agent.setRecommendOrgCode("customerAdminOrgCode");
        agent.setCustomerAdminOrgCode("customerAdminOrgCode");
        agent.setRecommendMasterName("recommendMasterName");
        agent.setRecommendAdminName("recommendAdminName");
        agent.setRecommendEntryDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        agent.setRecommendPostName("postName");
        req.setAgent(agent);
        req.setEnterpriseRiskFactor("enterpriseRiskFactor");
        final SysDutyConfig sysDutyConfig = new SysDutyConfig();
        req.setSysDutys(Arrays.asList(sysDutyConfig));
        req.setChannel("channel");
        req.setProductId(0);
        req.setBizCode("bizCode");
        req.setStartTime("startTime");
        req.setSubChannel("subChannel");
        final GroupInsured groupInsured = new GroupInsured();
        groupInsured.setActiveBranchId("activeBranchId");
        groupInsured.setIdNumber("idNumber");
        req.setInsuredList(Arrays.asList(groupInsured));
        final ProductReq product = new ProductReq();
        product.setPlanId(0);
        req.setProduct(product);
        req.setSubmitTime("submitTime");

        final GroupQuoteResponse expectedResult = new GroupQuoteResponse();
        expectedResult.setOrderId("orderId");
        expectedResult.setPolicyNo("policyNo");
        expectedResult.setProposalNo("proposalNo");
        expectedResult.setEffectiveDate("effectiveDate");
        expectedResult.setExpireDate("expireDate");

        // Configure SmProductService.getPlanById(...).
        final SmPlanVO smPlanVO = new SmPlanVO();
        smPlanVO.setId(0);
        smPlanVO.setPlanId(0);
        smPlanVO.setProductStatus(0);
        smPlanVO.setChannel("channel");
        smPlanVO.setProductId(0);
        when(mockProductService.getPlanById(0)).thenReturn(smPlanVO);

        when(mockNotifyService.isMaintenance("channel", "subChannel")).thenReturn(false);

        // Configure SmProductService.getProductById(...).
        final SmProductDetailVO smProductDetailVO = new SmProductDetailVO();
        smProductDetailVO.setState(0);
        smProductDetailVO.setProductCode("productCode");
        smProductDetailVO.setProductAttrCode("productAttrCode");
        smProductDetailVO.setIntroduceImageUrl("introduceImageUrl");
        smProductDetailVO.setHeadImageUrl("headImageUrl");
        when(mockProductService.getProductById(0)).thenReturn(smProductDetailVO);

        when(mockCommissionMapper.listCommissionSettingsByProductId(0,
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).thenReturn(Collections.emptyList());

        // Configure ProductMapper.queryPremiumFactor(...).
        final PremiumFlow premiumFlow = new PremiumFlow();
        premiumFlow.setId(0);
        premiumFlow.setProductId(0);
        premiumFlow.setPlanId(0);
        premiumFlow.setFactorValue("enterpriseRiskFactor");
        premiumFlow.setFlow("flow");
        final List<PremiumFlow> premiumFlows = Arrays.asList(premiumFlow);
        when(mockProductMapper.queryPremiumFactor(0, 0, "channelCode")).thenReturn(premiumFlows);

        when(mockUserMapper.getMainJobNumberByBizCode("bizCode")).thenReturn("recommendId");

        // Configure SmOrderMapper.queryDutyConfig(...).
        final SysDutyConfig sysDutyConfig1 = new SysDutyConfig();
        sysDutyConfig1.setChannel("channel");
        sysDutyConfig1.setType(0);
        sysDutyConfig1.setMainCode("mainCode");
        sysDutyConfig1.setCode("code");
        sysDutyConfig1.setExtendCode("extendCode");
        final List<SysDutyConfig> sysDutyConfigs = Arrays.asList(sysDutyConfig1);
        when(mockOrderMapper.queryDutyConfig("channel", 0)).thenReturn(sysDutyConfigs);

        try {
            policyServiceUnderTest.underwriting("channel", req);
        }catch (Exception e){
            Assert.assertTrue(e instanceof MSBizNormalException);
        }
    }

    @Test
    public void testUnderwriting_ProductMapperReturnsNoItems() {
        // Setup
        final GroupUnderwriting req = new GroupUnderwriting();
        final SmCommissionSettingVO commission = new SmCommissionSettingVO();
        commission.setPlanId(0);
        commission.setPaymentProportion(new BigDecimal("0.00"));
        req.setCommission(commission);
        final Agent agent = new Agent();
        agent.setJobCode("jobCode");
        agent.setRecommendId("recommendId");
        agent.setRecommendJobCode("customerAdminJobCode");
        agent.setRecommendMainJobNumber("customerAdminMainJobNumber");
        agent.setCustomerAdminMainJobNumber("customerAdminMainJobNumber");
        agent.setCustomerAdminJobCode("customerAdminJobCode");
        agent.setRecommendOrgCode("customerAdminOrgCode");
        agent.setCustomerAdminOrgCode("customerAdminOrgCode");
        agent.setRecommendMasterName("recommendMasterName");
        agent.setRecommendAdminName("recommendAdminName");
        agent.setRecommendEntryDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        agent.setRecommendPostName("postName");
        req.setAgent(agent);
        req.setEnterpriseRiskFactor("enterpriseRiskFactor");
        final SysDutyConfig sysDutyConfig = new SysDutyConfig();
        req.setSysDutys(Arrays.asList(sysDutyConfig));
        req.setChannel("channel");
        req.setProductId(0);
        req.setBizCode("bizCode");
        req.setStartTime("startTime");
        req.setSubChannel("subChannel");
        final GroupInsured groupInsured = new GroupInsured();
        groupInsured.setActiveBranchId("activeBranchId");
        groupInsured.setIdNumber("idNumber");
        req.setInsuredList(Arrays.asList(groupInsured));
        final ProductReq product = new ProductReq();
        product.setPlanId(0);
        req.setProduct(product);
        req.setSubmitTime("submitTime");

        final GroupQuoteResponse expectedResult = new GroupQuoteResponse();
        expectedResult.setOrderId("orderId");
        expectedResult.setPolicyNo("policyNo");
        expectedResult.setProposalNo("proposalNo");
        expectedResult.setEffectiveDate("effectiveDate");
        expectedResult.setExpireDate("expireDate");

        // Configure SmProductService.getPlanById(...).
        final SmPlanVO smPlanVO = new SmPlanVO();
        smPlanVO.setId(0);
        smPlanVO.setPlanId(0);
        smPlanVO.setProductStatus(0);
        smPlanVO.setChannel("channel");
        smPlanVO.setProductId(0);
        when(mockProductService.getPlanById(0)).thenReturn(smPlanVO);

        when(mockNotifyService.isMaintenance("channel", "subChannel")).thenReturn(false);

        // Configure SmProductService.getProductById(...).
        final SmProductDetailVO smProductDetailVO = new SmProductDetailVO();
        smProductDetailVO.setState(0);
        smProductDetailVO.setProductCode("productCode");
        smProductDetailVO.setProductAttrCode("productAttrCode");
        smProductDetailVO.setIntroduceImageUrl("introduceImageUrl");
        smProductDetailVO.setHeadImageUrl("headImageUrl");
        when(mockProductService.getProductById(0)).thenReturn(smProductDetailVO);

        // Configure SmCommissionMapper.listCommissionSettingsByProductId(...).
        final SmCommissionSettingVO smCommissionSettingVO = new SmCommissionSettingVO();
        smCommissionSettingVO.setId(0);
        smCommissionSettingVO.setPlanId(0);
        smCommissionSettingVO.setPaymentProportion(new BigDecimal("0.00"));
        smCommissionSettingVO.setSettlementProportion(new BigDecimal("0.00"));
        smCommissionSettingVO.setSaleLevel("saleLevel");
        final List<SmCommissionSettingVO> smCommissionSettingVOS = Arrays.asList(smCommissionSettingVO);
        when(mockCommissionMapper.listCommissionSettingsByProductId(0,
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).thenReturn(smCommissionSettingVOS);

        when(mockProductMapper.queryPremiumFactor(0, 0, "channelCode")).thenReturn(Collections.emptyList());
        when(mockUserMapper.getMainJobNumberByBizCode("bizCode")).thenReturn("recommendId");

        // Configure SmOrderMapper.queryDutyConfig(...).
        final SysDutyConfig sysDutyConfig1 = new SysDutyConfig();
        sysDutyConfig1.setChannel("channel");
        sysDutyConfig1.setType(0);
        sysDutyConfig1.setMainCode("mainCode");
        sysDutyConfig1.setCode("code");
        sysDutyConfig1.setExtendCode("extendCode");
        final List<SysDutyConfig> sysDutyConfigs = Arrays.asList(sysDutyConfig1);
        when(mockOrderMapper.queryDutyConfig("channel", 0)).thenReturn(sysDutyConfigs);

        try {
            policyServiceUnderTest.underwriting("channel", req);
        }catch (Exception e){
            Assert.assertTrue(e instanceof MSBizNormalException);
        }
    }

    @Test
    public void testUnderwriting_SmOrderMapperReturnsNull() {
        // Setup
        final GroupUnderwriting req = new GroupUnderwriting();
        final SmCommissionSettingVO commission = new SmCommissionSettingVO();
        commission.setPlanId(0);
        commission.setPaymentProportion(new BigDecimal("0.00"));
        req.setCommission(commission);
        final Agent agent = new Agent();
        agent.setJobCode("jobCode");
        agent.setRecommendId("recommendId");
        agent.setRecommendJobCode("customerAdminJobCode");
        agent.setRecommendMainJobNumber("customerAdminMainJobNumber");
        agent.setCustomerAdminMainJobNumber("customerAdminMainJobNumber");
        agent.setCustomerAdminJobCode("customerAdminJobCode");
        agent.setRecommendOrgCode("customerAdminOrgCode");
        agent.setCustomerAdminOrgCode("customerAdminOrgCode");
        agent.setRecommendMasterName("recommendMasterName");
        agent.setRecommendAdminName("recommendAdminName");
        agent.setRecommendEntryDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        agent.setRecommendPostName("postName");
        req.setAgent(agent);
        req.setEnterpriseRiskFactor("enterpriseRiskFactor");
        final SysDutyConfig sysDutyConfig = new SysDutyConfig();
        req.setSysDutys(Arrays.asList(sysDutyConfig));
        req.setChannel("channel");
        req.setProductId(0);
        req.setBizCode("bizCode");
        req.setStartTime("startTime");
        req.setSubChannel("subChannel");
        final GroupInsured groupInsured = new GroupInsured();
        groupInsured.setActiveBranchId("activeBranchId");
        groupInsured.setIdNumber("idNumber");
        req.setInsuredList(Arrays.asList(groupInsured));
        final ProductReq product = new ProductReq();
        product.setPlanId(0);
        req.setProduct(product);
        req.setSubmitTime("submitTime");

        final GroupQuoteResponse expectedResult = new GroupQuoteResponse();
        expectedResult.setOrderId("orderId");
        expectedResult.setPolicyNo("policyNo");
        expectedResult.setProposalNo("proposalNo");
        expectedResult.setEffectiveDate("effectiveDate");
        expectedResult.setExpireDate("expireDate");

        // Configure SmProductService.getPlanById(...).
        final SmPlanVO smPlanVO = new SmPlanVO();
        smPlanVO.setId(0);
        smPlanVO.setPlanId(0);
        smPlanVO.setProductStatus(0);
        smPlanVO.setChannel("channel");
        smPlanVO.setProductId(0);
        when(mockProductService.getPlanById(0)).thenReturn(smPlanVO);

        when(mockNotifyService.isMaintenance("channel", "subChannel")).thenReturn(false);

        // Configure SmProductService.getProductById(...).
        final SmProductDetailVO smProductDetailVO = new SmProductDetailVO();
        smProductDetailVO.setState(0);
        smProductDetailVO.setProductCode("productCode");
        smProductDetailVO.setProductAttrCode("productAttrCode");
        smProductDetailVO.setIntroduceImageUrl("introduceImageUrl");
        smProductDetailVO.setHeadImageUrl("headImageUrl");
        when(mockProductService.getProductById(0)).thenReturn(smProductDetailVO);

        // Configure SmCommissionMapper.listCommissionSettingsByProductId(...).
        final SmCommissionSettingVO smCommissionSettingVO = new SmCommissionSettingVO();
        smCommissionSettingVO.setId(0);
        smCommissionSettingVO.setPlanId(0);
        smCommissionSettingVO.setPaymentProportion(new BigDecimal("0.00"));
        smCommissionSettingVO.setSettlementProportion(new BigDecimal("0.00"));
        smCommissionSettingVO.setSaleLevel("saleLevel");
        final List<SmCommissionSettingVO> smCommissionSettingVOS = Arrays.asList(smCommissionSettingVO);
        when(mockCommissionMapper.listCommissionSettingsByProductId(0,
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).thenReturn(smCommissionSettingVOS);

        // Configure ProductMapper.queryPremiumFactor(...).
        final PremiumFlow premiumFlow = new PremiumFlow();
        premiumFlow.setId(0);
        premiumFlow.setProductId(0);
        premiumFlow.setPlanId(0);
        premiumFlow.setFactorValue("enterpriseRiskFactor");
        premiumFlow.setFlow("flow");
        final List<PremiumFlow> premiumFlows = Arrays.asList(premiumFlow);
        when(mockProductMapper.queryPremiumFactor(0, 0, "channelCode")).thenReturn(premiumFlows);

        when(mockUserMapper.getMainJobNumberByBizCode("bizCode")).thenReturn("recommendId");
        when(mockOrderMapper.queryDutyConfig("channel", 0)).thenReturn(null);

        try {
            policyServiceUnderTest.underwriting("channel", req);
        }catch (Exception e){
            Assert.assertTrue(e instanceof MSBizNormalException);
        }

    }

    @Test
    public void testUnderwriting_SmOrderMapperReturnsNoItems() {
        // Setup
        final GroupUnderwriting req = new GroupUnderwriting();
        final SmCommissionSettingVO commission = new SmCommissionSettingVO();
        commission.setPlanId(0);
        commission.setPaymentProportion(new BigDecimal("0.00"));
        req.setCommission(commission);
        final Agent agent = new Agent();
        agent.setJobCode("jobCode");
        agent.setRecommendId("recommendId");
        agent.setRecommendJobCode("customerAdminJobCode");
        agent.setRecommendMainJobNumber("customerAdminMainJobNumber");
        agent.setCustomerAdminMainJobNumber("customerAdminMainJobNumber");
        agent.setCustomerAdminJobCode("customerAdminJobCode");
        agent.setRecommendOrgCode("customerAdminOrgCode");
        agent.setCustomerAdminOrgCode("customerAdminOrgCode");
        agent.setRecommendMasterName("recommendMasterName");
        agent.setRecommendAdminName("recommendAdminName");
        agent.setRecommendEntryDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        agent.setRecommendPostName("postName");
        req.setAgent(agent);
        req.setEnterpriseRiskFactor("enterpriseRiskFactor");
        final SysDutyConfig sysDutyConfig = new SysDutyConfig();
        req.setSysDutys(Arrays.asList(sysDutyConfig));
        req.setChannel("channel");
        req.setProductId(0);
        req.setBizCode("bizCode");
        req.setStartTime("startTime");
        req.setSubChannel("subChannel");
        final GroupInsured groupInsured = new GroupInsured();
        groupInsured.setActiveBranchId("activeBranchId");
        groupInsured.setIdNumber("idNumber");
        req.setInsuredList(Arrays.asList(groupInsured));
        final ProductReq product = new ProductReq();
        product.setPlanId(0);
        req.setProduct(product);
        req.setSubmitTime("submitTime");

        final GroupQuoteResponse expectedResult = new GroupQuoteResponse();
        expectedResult.setOrderId("orderId");
        expectedResult.setPolicyNo("policyNo");
        expectedResult.setProposalNo("proposalNo");
        expectedResult.setEffectiveDate("effectiveDate");
        expectedResult.setExpireDate("expireDate");

        // Configure SmProductService.getPlanById(...).
        final SmPlanVO smPlanVO = new SmPlanVO();
        smPlanVO.setId(0);
        smPlanVO.setPlanId(0);
        smPlanVO.setProductStatus(0);
        smPlanVO.setChannel("channel");
        smPlanVO.setProductId(0);
        when(mockProductService.getPlanById(0)).thenReturn(smPlanVO);

        when(mockNotifyService.isMaintenance("channel", "subChannel")).thenReturn(false);

        // Configure SmProductService.getProductById(...).
        final SmProductDetailVO smProductDetailVO = new SmProductDetailVO();
        smProductDetailVO.setState(0);
        smProductDetailVO.setProductCode("productCode");
        smProductDetailVO.setProductAttrCode("productAttrCode");
        smProductDetailVO.setIntroduceImageUrl("introduceImageUrl");
        smProductDetailVO.setHeadImageUrl("headImageUrl");
        when(mockProductService.getProductById(0)).thenReturn(smProductDetailVO);

        // Configure SmCommissionMapper.listCommissionSettingsByProductId(...).
        final SmCommissionSettingVO smCommissionSettingVO = new SmCommissionSettingVO();
        smCommissionSettingVO.setId(0);
        smCommissionSettingVO.setPlanId(0);
        smCommissionSettingVO.setPaymentProportion(new BigDecimal("0.00"));
        smCommissionSettingVO.setSettlementProportion(new BigDecimal("0.00"));
        smCommissionSettingVO.setSaleLevel("saleLevel");
        final List<SmCommissionSettingVO> smCommissionSettingVOS = Arrays.asList(smCommissionSettingVO);
        when(mockCommissionMapper.listCommissionSettingsByProductId(0,
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).thenReturn(smCommissionSettingVOS);

        // Configure ProductMapper.queryPremiumFactor(...).
        final PremiumFlow premiumFlow = new PremiumFlow();
        premiumFlow.setId(0);
        premiumFlow.setProductId(0);
        premiumFlow.setPlanId(0);
        premiumFlow.setFactorValue("enterpriseRiskFactor");
        premiumFlow.setFlow("flow");
        final List<PremiumFlow> premiumFlows = Arrays.asList(premiumFlow);
        when(mockProductMapper.queryPremiumFactor(0, 0, "channelCode")).thenReturn(premiumFlows);

        when(mockUserMapper.getMainJobNumberByBizCode("bizCode")).thenReturn("recommendId");
        when(mockOrderMapper.queryDutyConfig("channel", 0)).thenReturn(Collections.emptyList());

        try {
            policyServiceUnderTest.underwriting("channel", req);
        }catch (Exception e){
            Assert.assertTrue(e instanceof MSBizNormalException);
        }

    }

    @Test
    public void testGenActiveBranchId() {
        // Setup
        final GroupInsured groupInsured = new GroupInsured();
        groupInsured.setEndorId("endorId");
        groupInsured.setActiveBranchId("activeBranchId");
        groupInsured.setBranchId("branchId");
        groupInsured.setPolicyNo("policyNo");
        groupInsured.setIdNumber("idNumber");
        final List<GroupInsured> insuredList = Arrays.asList(groupInsured);

        policyServiceUnderTest.genActiveBranchId(insuredList);

    }

}
