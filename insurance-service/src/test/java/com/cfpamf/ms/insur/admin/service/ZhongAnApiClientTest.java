package com.cfpamf.ms.insur.admin.service;

import com.alibaba.fastjson.JSONObject;
import com.zhongan.scorpoin.common.ZhongAnApiClient;
import com.zhongan.scorpoin.common.ZhongAnOpenException;
import com.zhongan.scorpoin.common.dto.CommonRequest;
import com.zhongan.scorpoin.common.dto.CommonResponse;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

/**
 * <AUTHOR> 2020/10/10 09:27
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class ZhongAnApiClientTest {

    public static void main(String[] args) throws ZhongAnOpenException {

        //#    地址：http://opengw.daily.zhongan.com/Gateway.do
        //    gateway: http://opengw.daily.zhongan.com/Gateway.do
        //    app-key: 10001
        //#    公钥：
        //    public-key: MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDIgHnOn7LLILlKETd6BFRJ0GqgS2Y3mn1wMQmyh9zEyWlz5p1zrahRahbXAfCfSqshSNfqOmAQzSHRVjCqjsAw1jyqrXaPdKBmr90DIpIxmIyKXv4GGAkPyJ/6FTFY99uhpiq0qadD/uSzQsefWo0aTvP/65zi3eof7TcZ32oWpwIDAQAB
        //#    私钥：
        //    private-key: MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAO8h8JCJAMb1nd0uBkzZuWyNnL+atBzJKvIG7escD45ODf0AWKr8vSqLZ01HD86+a496CGjsae6GybK8C1MqiMSwaAsIv31nKD6U8xF607MPrD3r2lyjwUnmqBZY++R6yFNYz9ZDXcdiwCudESRsXunPJq7zfnnglCtEH+qqW8/VAgMBAAECgYEAnVc2gtMyKLbZTPuId65WG7+9oDB5S+ttD1xR1P1cmuRuvcYpkS/Eg6a/rJASLZULDpdbyzWqqaAUPD8QMINvAr3ZtkbwH5R0F/4aqOlx/5B0Okjsp3eSK2bQ8J2m/MmFKZxr6Aily7YUDdxcGcjLizsGi1KDkWS22JRufEeUNA0CQQD+g1XJ7ELqmUtrS4m4XnadB25f0g5QC0tMjoa3d9soMzK3q+Drkv8EZVpGSmSHEo1VlE7HUcnKNvK1BO5Nm4iXAkEA8IeZxaWmEcsRqiuFz8xmYGtKcYTmHgheGF4D+fnnFozSNP+3sS1lfgFQrjUkqUyZOoG1hPc6SDhGS4nbXwiscwJASO+gPR58yrgledkK3ZAMk9GWWtVajqu953GMv7UUU//gD+yspzXX6Q2WgkA9cMvrPtQig1I37sAya5e/JvRkfwJARzzCDEmdP9PW7YFqZjrxb0kXiTuFNAviYnEl2FltWb5nW48JBo6dao5VKONQclvfXfagnjriphUUrLatpB3bhQJAKRfJS6jDAIVKt7So5HOdzk4ipxgrMjG/QtZ1grO+VQectk4+tCwdJhOrr5blvdPQvFVqXBQfXuE7cibZrGs4sQ==
        //#    接口版本一般都是1.0.0，如有变化开发人员会告知。
        //    version: 1.0.0
        //    env: dev
        //    channel-code: B02AYKH
        //  new ZhongAnApiClient(zaApiProperties.getEnv(), zaApiProperties.getAppKey(), zaApiProperties.getPrivateKey(),
        //                zaApiProperties.getVersion());

//        prd
//        ZhongAnApiClient client = new ZhongAnApiClient("prd", "9771c33fba4b8e7ff2491c6e51da42a0",
//                "MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAM49Nce/opeZSJQkxjCfSlWpawJ23M2Q8ox4OZruLaAegofQdb/JEth3GCbSUi0Rf6sREyHDLc1UCepbbI8Ykvoe4WVbMdQQ8f/JfI2lc++pT+0omXho+koX42A2j5TMMLEq3mktEXkZNe7wgLhRA/r4Af1VFWwL9SGyPs2b8XO1AgMBAAECgYA95LjDHYvaGzJg4zhnVyaj1pg6wRk6yAlChsIi8N/Ycc+Smsj7qYhyVit1WyE2TU4XNKSdOUoBmPrwzkciGqngxHoFlOAGzTlOApMnxpR+xBtIYQrQADzpQWeaFklCCmNuc7My6/P4YyTuE3XG0TrbE4Cs6ggkXy+wJfuk+mnuAQJBAOpvZsqaPoBfxFm2ftrm+NFAoHZtH2XMtsHa1HG8PDTgwqQdAkiWMTKV/Os8hWre7C+G6bmgWFzTfeBR8dxWieECQQDhNdVYIi77WoiLnOV7Tig+wYJeuYhMk6d0bYFU2AcQIl1UmJn6hOiH2cXdcGx+uS01xFNZymECHv5JNQhx4yxVAkB7Si8C6e9oz/SRQbqhIMtGMPiyj1ztyqVqec00K0x2eqWibWcgCWzCL8Mda2y28ZnAvPD6IGuYFdmjOTU7LX/BAkEAgC/7InI6mblbVVRVzoybHbVZdn9WVHgK7ZtS7S8VPhrBUuCVbGHwVTsgg711E+BlGmCILOijtOhdRuAX0XNEDQJAB8ddVz3WKo7yaIMqRXXJExM7ZPYl2E+KTIlUDaaPoMeED+3J2FdElNzaMynWGxb/6L83xQCqSQC2Ff2eS9kMnw==\n");
        //dev

        ZhongAnApiClient client = new ZhongAnApiClient("dev", "10001",
                "MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAO8h8JCJAMb1nd0uBkzZuWyNnL+atBzJKvIG7escD45ODf0AWKr8vSqLZ01HD86+a496CGjsae6GybK8C1MqiMSwaAsIv31nKD6U8xF607MPrD3r2lyjwUnmqBZY++R6yFNYz9ZDXcdiwCudESRsXunPJq7zfnnglCtEH+qqW8/VAgMBAAECgYEAnVc2gtMyKLbZTPuId65WG7+9oDB5S+ttD1xR1P1cmuRuvcYpkS/Eg6a/rJASLZULDpdbyzWqqaAUPD8QMINvAr3ZtkbwH5R0F/4aqOlx/5B0Okjsp3eSK2bQ8J2m/MmFKZxr6Aily7YUDdxcGcjLizsGi1KDkWS22JRufEeUNA0CQQD+g1XJ7ELqmUtrS4m4XnadB25f0g5QC0tMjoa3d9soMzK3q+Drkv8EZVpGSmSHEo1VlE7HUcnKNvK1BO5Nm4iXAkEA8IeZxaWmEcsRqiuFz8xmYGtKcYTmHgheGF4D+fnnFozSNP+3sS1lfgFQrjUkqUyZOoG1hPc6SDhGS4nbXwiscwJASO+gPR58yrgledkK3ZAMk9GWWtVajqu953GMv7UUU//gD+yspzXX6Q2WgkA9cMvrPtQig1I37sAya5e/JvRkfwJARzzCDEmdP9PW7YFqZjrxb0kXiTuFNAviYnEl2FltWb5nW48JBo6dao5VKONQclvfXfagnjriphUUrLatpB3bhQJAKRfJS6jDAIVKt7So5HOdzk4ipxgrMjG/QtZ1grO+VQectk4+tCwdJhOrr5blvdPQvFVqXBQfXuE7cibZrGs4sQ==\n");

        CommonRequest request = new CommonRequest("zhongan.health.personal.virtualproduct.generateProductCode");


        //个人版
        JSONObject jsonObject = JSONObject.parseObject(" {\n" +
                "\t\t\"channelCode\": \"B02AYKH\",\n" +
                "\t\t\"productSeriesCode\": \"AM98\",\n" +
                "\t\t\"clauses\": [{\n" +
                "\t\t\t\"clauseCode\": \"79H\",\n" +
                "\t\t\t\"liabilities\": [{\n" +
                "\t\t\t\t\"liabilityCode\": \"ZXG027\"\n" +
                "\t\t\t},\n" +
                "\t\t\t{\n" +
                "\t\t\t\t\"liabilityCode\": \"ZXG129\"\n" +
                "\t\t\t}]\n" +
                "\t\t},\n" +
                "\t\t{\n" +
                "\t\t\t\"clauseCode\": \"79I\",\n" +
                "\t\t\t\"liabilities\": [{\n" +
                "\t\t\t\t\"liabilityCode\": \"FXG054\"\n" +
                "\t\t\t}]\n" +
                "\t\t},\n" +
                "\t\t{\n" +
                "\t\t\t\"clauseCode\": \"79J\",\n" +
                "\t\t\t\"liabilities\": [{\n" +
                "\t\t\t\t\"liabilityCode\": \"FXG055\"\n" +
                "\t\t\t}]\n" +
                "\t\t}]\n" +
                "\t}");

//        //家庭版
        String str = "{\n" +
                "\t\t\"channelCode\": \"B02AYKH\",\n" +
                "\t\t\"productSeriesCode\": \"AM98\",\n" +
                "\t\t\"clauses\": [{\n" +
                "\t\t\t\"clauseCode\": \"79H\",\n" +
                "\t\t\t\"liabilities\": [{\n" +
                "\t\t\t\t\"liabilityCode\": \"ZXG027\"\n" +
                "\t\t\t},\n" +
                "\t\t\t{\n" +
                "\t\t\t\t\"liabilityCode\": \"ZXG129\"\n" +
                "\t\t\t}]\n" +
                "\t\t},\n" +
                "\t\t{\n" +
                "\t\t\t\"clauseCode\": \"79I\",\n" +
                "\t\t\t\"liabilities\": [{\n" +
                "\t\t\t\t\"liabilityCode\": \"FXG054\"\n" +
                "\t\t\t}]\n" +
                "\t\t},\n" +
                "\t\t{\n" +
                "\t\t\t\"clauseCode\": \"79J\",\n" +
                "\t\t\t\"liabilities\": [{\n" +
                "\t\t\t\t\"liabilityCode\": \"FXG055\"\n" +
                "\t\t\t}]\n" +
                "\t\t},{\n" +
                "\t\t\t\"clauseCode\": \"79M\",\n" +
                "\t\t\t\"liabilities\": [{\n" +
                "\t\t\t\t\"liabilityCode\": \"FXG035\"\n" +
                "\t\t\t}]\n" +
                "\t\t}]\n" +
                "\t}\n" ;
//        System.err.println(str);
        // 孝欣保老年人综合意外2020基础版-不含骨折（AP98）
// AP98$670603401
//        str = "{\"channelCode\":\"B02AYKH\",\"productSeriesCode\":\"AP98\",\"clauses\":[{\"clauseCode\":\"779\",\"liabilities\":[{\"liabilityCode\":\"ZXF034\"}]},{\"clauseCode\":\"793\",\"liabilities\":[{\"liabilityCode\":\"FXF047\"}]},{\"clauseCode\":\"794\",\"liabilities\":[{\"liabilityCode\":\"FXF019\"}]},{\"clauseCode\":\"7A7\",\"liabilities\":[{\"liabilityCode\":\"ZXF029\"},{\"liabilityCode\":\"ZXF030\"}]},{\"clauseCode\":\"76R\",\"liabilities\":[{\"liabilityCode\":\"ZXF046\"}]},{\"clauseCode\":\"7A8\",\"liabilities\":[{\"liabilityCode\":\"FXF054\"},{\"liabilityCode\":\"FXF055\"}]}]}";
//        System.err.println(str);
        // 孝欣保老年人综合意外2020基础版-含骨折（AP98）
        //AP98$171825248
//        str= "{\"channelCode\":\"B02AYKH\",\"productSeriesCode\":\"AP98\",\"clauses\":[{\"clauseCode\":\"779\",\"liabilities\":[{\"liabilityCode\":\"ZXF034\"}]},{\"clauseCode\":\"793\",\"liabilities\":[{\"liabilityCode\":\"FXF047\"}]},{\"clauseCode\":\"794\",\"liabilities\":[{\"liabilityCode\":\"FXF019\"}]},{\"clauseCode\":\"7A7\",\"liabilities\":[{\"liabilityCode\":\"ZXF029\"},{\"liabilityCode\":\"ZXF030\"}]},{\"clauseCode\":\"76R\",\"liabilities\":[{\"liabilityCode\":\"ZXF046\"}]},{\"clauseCode\":\"74K\",\"liabilities\":[{\"liabilityCode\":\"ZXF027\"},{\"liabilityCode\":\"ZXF028\"}]},{\"clauseCode\":\"7A8\",\"liabilities\":[{\"liabilityCode\":\"FXF054\"},{\"liabilityCode\":\"FXF055\"}]}]}";

        // 孝欣保老年人综合意外2020卓越版-不含骨折（AP99）
        //AP99$678362760
//        str = "{\"channelCode\":\"B02AYKH\",\"productSeriesCode\":\"AP99\",\"clauses\":[{\"clauseCode\":\"779\",\"liabilities\":[{\"liabilityCode\":\"ZXF034\"}]},{\"clauseCode\":\"793\",\"liabilities\":[{\"liabilityCode\":\"FXF047\"}]},{\"clauseCode\":\"794\",\"liabilities\":[{\"liabilityCode\":\"FXF019\"}]},{\"clauseCode\":\"7A7\",\"liabilities\":[{\"liabilityCode\":\"ZXF029\"},{\"liabilityCode\":\"ZXF030\"}]},{\"clauseCode\":\"76R\",\"liabilities\":[{\"liabilityCode\":\"ZXF046\"}]},{\"clauseCode\":\"7A8\",\"liabilities\":[{\"liabilityCode\":\"FXF054\"},{\"liabilityCode\":\"FXF055\"}]}]}";
//        System.err.println(str);
//
//        //孝欣保老年人综合意外2020卓越版-含骨折（AP99）
//        //AP99$1338521567
//       str = "{\"channelCode\":\"B02AYKH\",\"productSeriesCode\":\"AP99\",\"clauses\":[{\"clauseCode\":\"779\",\"liabilities\":[{\"liabilityCode\":\"ZXF034\"}]},{\"clauseCode\":\"793\",\"liabilities\":[{\"liabilityCode\":\"FXF047\"}]},{\"clauseCode\":\"794\",\"liabilities\":[{\"liabilityCode\":\"FXF019\"}]},{\"clauseCode\":\"7A7\",\"liabilities\":[{\"liabilityCode\":\"ZXF029\"},{\"liabilityCode\":\"ZXF030\"}]},{\"clauseCode\":\"76R\",\"liabilities\":[{\"liabilityCode\":\"ZXF046\"}]},{\"clauseCode\":\"74K\",\"liabilities\":[{\"liabilityCode\":\"ZXF027\"},{\"liabilityCode\":\"ZXF028\"}]},{\"clauseCode\":\"7A8\",\"liabilities\":[{\"liabilityCode\":\"FXF054\"},{\"liabilityCode\":\"FXF055\"}]}]}";


        //"尊享e生2021版（年缴版A）"
        //AV25$478216799
//        str=  "{\"channelCode\":\"B02AYKH\",\"productSeriesCode\":\"AV25\",\"clauses\":[{\"clauseCode\":\"7G2\",\"liabilities\":[{\"liabilityCode\":\"ZXG027\"},{\"liabilityCode\":\"ZXG129\"}]},{\"clauseCode\":\"7G3\",\"liabilities\":[{\"liabilityCode\":\"FXG055\"}]},{\"clauseCode\":\"7G4\",\"liabilities\":[{\"liabilityCode\":\"FXG054\"}]},{\"clauseCode\":\"7G5\",\"liabilities\":[{\"liabilityCode\":\"FXG065\"}]},{\"clauseCode\":\"7F9\",\"liabilities\":[{\"liabilityCode\":\"ZXG001\"}]},{\"clauseCode\":\"7G6\",\"liabilities\":[{\"liabilityCode\":\"FXG022\"}]},{\"clauseCode\":\"7G9\",\"liabilities\":[{\"liabilityCode\":\"FXG071\"}]},{\"clauseCode\":\"7G7\",\"liabilities\":[{\"liabilityCode\":\"FXG057\"}]},{\"clauseCode\":\"7G8\",\"liabilities\":[{\"liabilityCode\":\"FXG068\"},{\"liabilityCode\":\"FXG069\"},{\"liabilityCode\":\"FXG070\"}]},{\"clauseCode\":\"7B1\",\"liabilities\":[{\"liabilityCode\":\"ZXG175\"}]},{\"clauseCode\":\"7B2\",\"liabilities\":[{\"liabilityCode\":\"FXG061\"}]},{\"clauseCode\":\"7B7\",\"liabilities\":[{\"liabilityCode\":\"ZXG130\"}]},{\"clauseCode\":\"7F5\",\"liabilities\":[{\"liabilityCode\":\"FXG005\"}]},{\"clauseCode\":\"79Y\",\"liabilities\":[{\"liabilityCode\":\"FXG058\"}]}]}\n";

        //"尊享e生2021版（年缴版B）"
        //AV26$558987155
//        str = "{\"channelCode\":\"B02AYKH\",\"productSeriesCode\":\"AV26\",\"clauses\":[{\"clauseCode\":\"7G2\",\"liabilities\":[{\"liabilityCode\":\"ZXG027\"},{\"liabilityCode\":\"ZXG129\"}]},{\"clauseCode\":\"7G3\",\"liabilities\":[{\"liabilityCode\":\"FXG055\"}]},{\"clauseCode\":\"7G4\",\"liabilities\":[{\"liabilityCode\":\"FXG054\"}]},{\"clauseCode\":\"79Y\",\"liabilities\":[{\"liabilityCode\":\"FXG058\"}]}]}\n";


        //"尊享e生2021版（年缴版B）" 不带79Y
        // AV26$70351252
      //  str ="{\"channelCode\":\"B02AYKH\",\"productSeriesCode\":\"AV26\",\"clauses\":[{\"clauseCode\":\"7G2\",\"liabilities\":[{\"liabilityCode\":\"ZXG027\"},{\"liabilityCode\":\"ZXG129\"}]},{\"clauseCode\":\"7G3\",\"liabilities\":[{\"liabilityCode\":\"FXG055\"}]},{\"clauseCode\":\"7G4\",\"liabilities\":[{\"liabilityCode\":\"FXG054\"}]}]}\n";


        //"尊享e生2021版（年缴版A）AV25"
        // 7G2
        //	7G2
        //	7G3
        //	7G4
        str = "{\"channelCode\":\"B02AYKH\",\"productSeriesCode\":\"AV25\",\"clauses\":[{\"clauseCode\":\"7G2\",\"liabilities\":[{\"liabilityCode\":\"ZXG027\"},{\"liabilityCode\":\"ZXG129\"}]},{\"clauseCode\":\"7G3\",\"liabilities\":[{\"liabilityCode\":\"FXG055\"}]},{\"clauseCode\":\"7G4\",\"liabilities\":[{\"liabilityCode\":\"FXG054\"}]}]}\n";

        jsonObject = JSONObject.parseObject(str);
        request.setParams(jsonObject);

        System.err.println(jsonObject);
//        System.err.println();
        CommonResponse call = client.call(request);
        System.err.println(call.getBizContent());

        //个人版
        // AM98$566777246


    //家庭
        //AM98$946177052
        //	{
        //		"channelCode":"B02AYKH",
        //		"productSeries":"AM98",
        //		"productCode":"AM98$1423107866",
        //		"msgCode":"0",
        //		"msgInfo":"生成虚拟计划成功"
        //	}
    }
}
