package com.cfpamf.ms.insur.admin.service.order;

import com.cfpamf.ms.insur.admin.dao.safes.order.SmOrderExtendThDistMapper;
import com.cfpamf.ms.insur.admin.pojo.po.order.SmOrderShare;
import com.cfpamf.ms.insur.admin.pojo.po.order.extend.SmOrderExtendThDist;
import com.cfpamf.ms.insur.admin.service.BaseTest;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.powermock.modules.junit4.PowerMockRunner;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;

@RunWith(PowerMockRunner.class)
public class SmOrderExtendThDistServiceTest extends BaseTest {

    @Mock private SmOrderExtendThDistMapper mockThDistMapper;

    private SmOrderExtendThDistService smOrderExtendThDistServiceUnderTest;

    @Before
    public void setUp() throws Exception {
        smOrderExtendThDistServiceUnderTest = new SmOrderExtendThDistService(mockThDistMapper);
    }

    @Test
    public void testSave() {
        // Setup
        final SmOrderExtendThDist dist = new SmOrderExtendThDist();
        dist.setFhOrderId("fhOrderId");
        dist.setJobNumber("jobNumber");
        dist.setShareIdNumber("shareIdNumber");
        dist.setShareName("shareName");

        // Run the test
        smOrderExtendThDistServiceUnderTest.save(dist);

        // Verify the results
        // Confirm SmOrderExtendThDistMapper.insertUseGeneratedKeys(...).
        final SmOrderExtendThDist record = new SmOrderExtendThDist();
        record.setFhOrderId("fhOrderId");
        record.setJobNumber("jobNumber");
        record.setShareIdNumber("shareIdNumber");
        record.setShareName("shareName");
    }

    @Test
    public void testGetByOrderId() {
        // Setup
        final SmOrderShare expectedResult = new SmOrderShare();
        expectedResult.setFhOrderId("orderId");
        expectedResult.setFirstJobNumber("firstJobNumber");
        expectedResult.setFirstIdNumber("firstIdNumber");
        expectedResult.setShareIdNumber("shareIdNumber");
        expectedResult.setShareName("fhOrderId");

        // Configure SmOrderExtendThDistMapper.selectByOrderId(...).
        final SmOrderExtendThDist smOrderExtendThDist = new SmOrderExtendThDist();
        smOrderExtendThDist.setFhOrderId("fhOrderId");
        smOrderExtendThDist.setJobNumber("jobNumber");
        smOrderExtendThDist.setShareIdNumber("shareIdNumber");
        smOrderExtendThDist.setShareName("shareName");
        when(mockThDistMapper.selectByOrderId("orderId")).thenReturn(smOrderExtendThDist);

        // Run the test
        final SmOrderShare result = smOrderExtendThDistServiceUnderTest.getByOrderId("orderId");

        // Verify the results
    }
}
