package com.cfpamf.ms.insur.admin.service;

import com.cfpamf.ms.bms.facade.vo.JwtUserInfo;
import com.cfpamf.ms.insur.admin.dao.safes.*;
import com.cfpamf.ms.insur.admin.enums.EnumCancelState;
import com.cfpamf.ms.insur.admin.enums.EnumFlowInsCancel;
import com.cfpamf.ms.insur.admin.pojo.dto.cancel.ActJumpDTO;
import com.cfpamf.ms.insur.admin.pojo.dto.cancel.CancelDTO;
import com.cfpamf.ms.insur.admin.pojo.dto.cancel.SmCancelCompanyDTO;
import com.cfpamf.ms.insur.admin.pojo.po.SmCancel;
import com.cfpamf.ms.insur.admin.pojo.po.SmCancelBankDeposit;
import com.cfpamf.ms.insur.admin.pojo.po.SmCancelFile;
import com.cfpamf.ms.insur.admin.pojo.query.SmCancelQuery;
import com.cfpamf.ms.insur.admin.pojo.vo.cancel.CancelOrderDetail;
import com.cfpamf.ms.insur.admin.pojo.vo.cancel.SmCancelFileGroupVO;
import com.cfpamf.ms.insur.base.activiti.ActivitiCommonService;
import com.cfpamf.ms.insur.base.dao.MyMappler;
import com.cfpamf.ms.insur.base.pojo.dto.SmAuditEmailDTO;
import com.cfpamf.ms.insur.base.util.HttpRequestUtil;
import com.cfpamf.ms.insur.base.util.RedisUtil;
import com.cfpamf.ms.insur.weixin.pojo.dto.WxCancelDTO;
import com.cfpamf.ms.insur.weixin.pojo.query.WxCancelQuery;
import com.cfpamf.ms.insur.weixin.pojo.vo.cancel.WxCancelPolicyVO;
import com.github.jsonzou.jmockdata.JMockData;
import com.github.jsonzou.jmockdata.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.activiti.engine.impl.persistence.entity.ExecutionEntityImpl;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.beans.factory.annotation.Autowired;
import tk.mybatis.mapper.entity.Config;
import tk.mybatis.mapper.mapperhelper.EntityHelper;

import javax.servlet.http.HttpServletResponse;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR> 2020/3/9 16:36
 */
@Slf4j
@RunWith(PowerMockRunner.class)
@PrepareForTest({HttpRequestUtil.class})
public class SmCancelServiceTest extends BaseTest {

    @InjectMocks
    private SmCancelService smCancelService;

    @Mock
    private SmCancelEmailService smCancelEmailService;

    @Mock
    private SmCancelMapper cancelMapper;

    @Mock
    private CompanyMapper companyMapper;

    @Mock
    private OrgPicExtraMapper orgPicExtraMapper;

    @Mock
    private ActivitiCommonService activitiCommonService;

    @Mock
    private UserService userService;

    @Mock
    private RedisUtil<String, Integer> redisUtil;

    @Mock
    private SmOrderMapper orderMapper;
    @Mock
    private SmCancelFileMapper fileMapper;
    @Mock
    private SmCancelBankDepositMapper bankDepositMapper;
    @Mock
    private SmOrderCoreService orderCoreService;

    @Mock
    private SmCmpySettingService cmpySettingService;

    @Before
    public void setUp() throws Exception {
        super.setUp();
        EntityHelper.initEntityNameMap(SmCancel.class, new Config());
        EntityHelper.initEntityNameMap(SmCancelFile.class, new Config());
        EntityHelper.initEntityNameMap(SmCancelBankDeposit.class, new Config());
        JwtUserInfo zhnxtest = JwtUserInfo.builder()
                .userId(1).jobNumber("ZHNXTEST")
                .build();
        PowerMockito.mockStatic(HttpRequestUtil.class);
        Mockito.when(HttpRequestUtil.getUser())
                .thenReturn(zhnxtest);
        Mockito.when(HttpRequestUtil.getUserOrThrowExp())
                .thenReturn(zhnxtest);
        Mockito.when(redisUtil.get(Mockito.anyString()))
                .thenReturn(123);

        CancelOrderDetail mock = JMockData.mock(CancelOrderDetail.class, con());
        mock.setInsuredBirthday("1996-08-12");
        mock.setApplicantIdNumber("43052819980912021X");
        mock.setInsuredIdNumber("43052819980912021X");
        Mockito.when(cancelMapper.getCancelOrderDetailByInsuredId(Mockito.anyInt()))
                .thenReturn(mock);
        CancelOrderDetail mock2 = JMockData.mock(CancelOrderDetail.class, con());
        mock2.setInsuredBirthday("1996-08-12");
        mock2.setApplicantIdNumber("43052819980912021X");
        mock2.setInsuredIdNumber("43052819980912021X");
        List<CancelOrderDetail> list = Collections.singletonList(mock2);
        Mockito.when(cancelMapper.getCancelOrderDetail(Mockito.anyInt()))
                .thenReturn(list);

        WxCancelPolicyVO policyVO = JMockData.mock(WxCancelPolicyVO.class, con());
        policyVO.setApplicantIdNumber("43052819980912021X");
        policyVO.setInsuredIdNumber("43052819980912021X");
        Mockito.when(cancelMapper.listCanCancelPolicy(Mockito.any()))
                .thenReturn(Collections.singletonList(policyVO));

        Mockito.when(cancelMapper.listAdminQuery(Mockito.any()))
                .thenReturn(Collections.singletonList(policyVO));
        log.info("set up success");
    }

    @Test
    public void apply() throws Exception {
        Mockito.when(activitiCommonService.startProcessInstanceByKey(Mockito.anyString(),
                        Mockito.anyString(), Mockito.anyMap()))
                .thenReturn(new ExecutionEntityImpl());
        Mockito.when(cancelMapper.select(Mockito.any()))
                .thenReturn(Collections.emptyList());
        smCancelService.apply(JMockData.mock(WxCancelDTO.class));
    }

    @Test
    public void rePushMessage() {
//        super.resetMapper();
        smCancelService.rePushMessage(JMockData.mock(SmCancel.class, con()));
    }

    @Test
    public void reApply() {
//        super.resetMapper();
        SmCancel mock = JMockData.mock(SmCancel.class, con());
        mock.setTaskKey(EnumFlowInsCancel.SAFE_CENTER.getEventId());
        Mockito.when(cancelMapper.selectByPrimaryKeyMustExists(Mockito.any()))
                .thenReturn(mock);
        smCancelService.reApply(JMockData.mock(WxCancelDTO.class));
    }

    @Test
    public void saveCancelFile() {
        super.resetMapper();
        smCancelService.saveCancelFile(1, "ZHNXTEST",
                JMockData.mock(new TypeReference<List<SmCancelFileGroupVO>>() {
                }));
    }

    @Test
    public void getCancelFile() {
        super.resetMapper();
        smCancelService.reApply(JMockData.mock(WxCancelDTO.class));
    }

    @Test
    public void fileTypes() {
//        super.resetMapper();

        CancelOrderDetail mock = JMockData.mock(CancelOrderDetail.class, con());
        mock.setInsuredBirthday("1996-08-12");
        mock.setApplicantIdNumber("43052819980912021X");
        mock.setInsuredIdNumber("43052819980912021X");
        Mockito.when(cancelMapper.getCancelOrderDetailByInsuredId(Mockito.anyInt()))
                .thenReturn(mock);
        smCancelService.fileTypes(1);
    }

    @Test
    public void audit() {
//        super.resetMapper();
        SmCancel mock = JMockData.mock(SmCancel.class, con());
        mock.setTaskKey(EnumFlowInsCancel.SAFE_CENTER.getEventId());
        Mockito.when(cancelMapper.selectByPrimaryKeyMustExists(Mockito.any()))
                .thenReturn(mock);
        //smCancelService.audit(1, JMockData.mock(SmAuditDTO.class));
    }

    @Test
    public void getDefaultPath() {
        SmCancel mock = JMockData.mock(SmCancel.class, con());
        mock.setTaskKey(EnumFlowInsCancel.SAFE_CENTER.getEventId());
        Mockito.when(cancelMapper.selectByPrimaryKeyMustExists(Mockito.any()))
                .thenReturn(mock);

        mock = JMockData.mock(SmCancel.class, con());
        mock.setTaskKey(EnumFlowInsCancel.CANCEL_END.getEventId());
        mock.setState(EnumCancelState.CANCELED.getKey());
        Mockito.when(cancelMapper.selectOne(Mockito.any()))
                .thenReturn(mock);
        smCancelService.getDefaultPath("10001");
    }

    @Test
    public void safeCenterReject() {
        try {
            smCancelService.safeCenterReject(Mockito.mock(ExecutionEntityImpl.class));
        } catch (Exception e) {
            // 依赖工作流环境 无法走通
        }

    }

    @Test
    public void commit2Company() {
        try {
            smCancelService.commit2Company(Mockito.mock(ExecutionEntityImpl.class));

        } catch (Exception e) {
            // 依赖工作流环境 无法走通
        }
    }

    @Test
    public void actCancelCancel() {
        try {
            smCancelService.actCancelCancel(Mockito.mock(ExecutionEntityImpl.class));
        } catch (Exception e) {
            // 依赖工作流环境 无法走通
        }

    }

    @Test
    public void adminAuthPageQuery() {
        super.resetMapper();
        smCancelService.adminAuthPageQuery(JMockData.mock(SmCancelQuery.class, con()));
    }

    @Test
    public void adminAuthQuery() {
        super.resetMapper();
        smCancelService.adminAuthQuery(JMockData.mock(SmCancelQuery.class, con()));
    }

    @Test
    public void pageCancel() {
        super.resetMapper();
        smCancelService.pageCancel(JMockData.mock(WxCancelQuery.class, con()));
    }

    @Test
    public void listCanCancelPolicy() {
        super.resetMapper();
        smCancelService.listCanCancelPolicy(JMockData.mock(WxCancelQuery.class, con()));
    }

    @Test
    public void getCancelOrderDetail() {
        super.resetMapper();
        smCancelService.getCancelOrderDetail(1);
    }

    @Test
    public void getCancelEmailTemplate() {
        super.resetMapper();
        smCancelService.getCancelEmailTemplate(1, 1);
    }

    @Test
    public void cancelCancel() {
//        super.resetMapper();
        smCancelService.cancelCancel(JMockData.mock(CancelDTO.class));
    }

    @Test
    public void getCanCancelDetailById() {
//        super.resetMapper();
        WxCancelPolicyVO mock = JMockData.mock(WxCancelPolicyVO.class, con());
        mock.setApplicantIdNumber("43052819980912021X");
        mock.setInsuredIdNumber("43052819980912021X");
        Mockito.when(cancelMapper.listCanCancelPolicy(Mockito.any()))
                .thenReturn(Collections.singletonList(mock));
        smCancelService.getCanCancelDetailById(1);
    }

    @Test
    public void getCanCancelDetail() {
        WxCancelPolicyVO mock = JMockData.mock(WxCancelPolicyVO.class, con());
        mock.setApplicantIdNumber("43052819980912021X");
        mock.setInsuredIdNumber("43052819980912021X");
        Mockito.when(cancelMapper.listCanCancelPolicy(Mockito.any()))
                .thenReturn(Collections.singletonList(mock));
        smCancelService.getCanCancelDetail(1);
    }

    @Test
    public void getCancelDetailVO() {
//        super.resetMapper();
        WxCancelPolicyVO mock = JMockData.mock(WxCancelPolicyVO.class, con());
        mock.setApplicantIdNumber("43052819980912021X");
        mock.setInsuredIdNumber("43052819980912021X");
        Mockito.when(cancelMapper.listCanCancelPolicy(Mockito.any()))
                .thenReturn(Collections.singletonList(mock));
        smCancelService.getCancelDetailVO(1, 1);
        smCancelService.getCancelDetailVO(1, null);
    }

    @Test
    public void sendEmailAndAudit() {
//        super.resetMapper();
        SmCancel mock = JMockData.mock(SmCancel.class, con());
        mock.setTaskKey(EnumFlowInsCancel.SAFE_CENTER.getEventId());
        Mockito.when(cancelMapper.selectByPrimaryKeyMustExists(Mockito.any()))
                .thenReturn(mock);
        smCancelService.sendEmailAndAudit(1, 1, JMockData.mock(SmAuditEmailDTO.class));
    }

    @Test
    public void jump2Center() {
        SmCancel mock = JMockData.mock(SmCancel.class, con());
        mock.setTaskKey(EnumFlowInsCancel.ORG_ADMIN_AUDIT.getEventId());
        Mockito.when(cancelMapper.selectByPrimaryKeyMustExists(Mockito.any()))
                .thenReturn(mock);
        smCancelService.jump2Center(1);
    }

    @Test
    public void download() {
        try {
            smCancelService.download(new SmCancelQuery(), Mockito.mock(HttpServletResponse.class));
        } catch (Exception e) {
            // 响应模拟 无法走通
        }
    }

    @Test
    public void downloadCancelFile() {
        smCancelService.downloadCancelFile(1, Mockito.mock(HttpServletResponse.class));
    }

    @Test
    public void jumpUtApply() {
        super.resetMapper();
        smCancelService.jumpUtApply(1, JMockData.mock(ActJumpDTO.class), "ZHNXTEST");
    }

    @Test
    public void refState() {
//        super.resetMapper();
        Mockito.when(orderCoreService.updateOrderPolicyInfo(Mockito.anyString()))
                .thenReturn(Collections.singletonMap("key", "4"));
        smCancelService.refState("123", "jj");


    }

    @Test
    public void uploadCompanyCn() {
//        super.resetMapper();
        smCancelService.uploadCompanyCn(JMockData.mock(SmCancelCompanyDTO.class, con()));
    }

    @Test
    public void testCLz() {

        System.err.println(MyMappler.class.isAssignableFrom(SmCancelMapper.class));
    }
}
