package com.cfpamf.ms.insur.admin.service.order;

import com.cfpamf.ms.insur.admin.dao.safes.SmOrderMapper;
import com.cfpamf.ms.insur.admin.dao.safes.SmProductMapper;
import com.cfpamf.ms.insur.admin.external.OrderSubmitRequest;
import com.cfpamf.ms.insur.admin.external.tk.TkPayOrderServiceAdapterImpl;
import com.cfpamf.ms.insur.admin.pojo.po.SmOrderInsured;
import com.cfpamf.ms.insur.admin.pojo.vo.SmPlanVO;
import com.cfpamf.ms.insur.admin.service.BaseTest;
import com.cfpamf.ms.insur.base.event.EventBusEngine;
import com.cfpamf.ms.insur.base.util.HttpRequestUtil;
import com.github.jsonzou.jmockdata.JMockData;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.aop.framework.AopContext;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Collections;

/**
 * <AUTHOR> 2021/12/27 16:19
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({HttpRequestUtil.class, AopContext.class})
public class TkPayOrderServiceTest extends BaseTest {

    @InjectMocks
    TkPayOrderService tkPayOrderService;
    @org.mockito.Mock
    TkPayOrderServiceAdapterImpl adapter;
    @org.mockito.Mock
    com.cfpamf.ms.insur.admin.dao.safes.order.SmOrderDDDMapper dddMapper;
    @org.mockito.Mock
    com.cfpamf.ms.insur.admin.dao.safes.order.SmOrderExtendTkMapper extendTkMapper;
    @org.mockito.Mock
    com.cfpamf.ms.insur.admin.dao.safes.order.SmOrderInsuredMapper insuredMapper;

    @Mock
    EventBusEngine busEngine;
    @Mock
    SmOrderMapper orderMapper;

    @Mock
    protected SmProductMapper productMapper;

    @Override
    @Before
    public void setUp() throws Exception {
        super.setUp();
        PowerMockito.mockStatic(AopContext.class);
        PowerMockito.when(AopContext.currentProxy()).thenReturn(tkPayOrderService);
    }

    @Test
    public void handSyncPayCallback() throws IOException {
        MockHttpServletRequest request = new MockHttpServletRequest();
        MockHttpServletResponse response = new MockHttpServletResponse();
        request.setContent("{\"requestId\":\"TKCNUPPTEST100001332912\",\"responseCode\":\"000_000_000\",\"responseData\":{\"familyProposalNo\":\"006405NS102021381048857736750\",\"familyNo\":\"FH211227001409100116750\",\"familyUrl\":\"http://ecuat.taikang.com/sp-greedy/api/v2/?api_s=document.property&api_m=policy.download&applicantName=72AE83D87BE78AF112B127C36D77DF88CF77D74D30D72AE70C81D80&policyNo=F43B115BF71C85A19CE92AE40D95BE62B28A53CF59B125C97A73AE115D21DF44DE16CE53A41D64AE26A31AF34C71A42AF128AF64AF114AF108B34\",\"orderList\":[{\"orderNo\":\"TKPAY21122702643532P-1\",\"policyNo\":\"H211227001409100116750\"}]},\"responseMsg\":\"成功\",\"responseTime\":\"20211227115601\"}".getBytes(StandardCharsets.UTF_8));
        tkPayOrderService.handSyncPayCallback(",oSaZD,", JMockData.mock(java.lang.Object.class), response, request);
        Assert.assertEquals(302, response.getStatus());
    }

    @Test
    public void handAsyncPayCallback() throws IOException {
        MockHttpServletRequest request = new MockHttpServletRequest();
        MockHttpServletResponse response = new MockHttpServletResponse();
        request.setContent("{\"requestId\":\"TKCNUPPTEST100001332912\",\"responseCode\":\"000_000_000\",\"responseData\":{\"familyProposalNo\":\"006405NS102021381048857736750\",\"familyNo\":\"FH211227001409100116750\",\"familyUrl\":\"http://ecuat.taikang.com/sp-greedy/api/v2/?api_s=document.property&api_m=policy.download&applicantName=72AE83D87BE78AF112B127C36D77DF88CF77D74D30D72AE70C81D80&policyNo=F43B115BF71C85A19CE92AE40D95BE62B28A53CF59B125C97A73AE115D21DF44DE16CE53A41D64AE26A31AF34C71A42AF128AF64AF114AF108B34\",\"orderList\":[{\"orderNo\":\"TKPAY21122702643532P-1\",\"policyNo\":\"H211227001409100116750\"}]},\"responseMsg\":\"成功\",\"responseTime\":\"20211227115601\"}".getBytes(StandardCharsets.UTF_8));


        SmOrderInsured mock = JMockData.mock(SmOrderInsured.class, con());
        mock.setFhOrderId("TKPAY21122702643532P");
        Mockito.when(insuredMapper.selectByOrderId(Mockito.anyString()))
                .thenReturn(Collections.singletonList(mock));
        tkPayOrderService.handAsyncPayCallback("TKPAY21122702643532P", null, response, request);
        Assert.assertEquals(200, response.getStatus());
    }

    @Test
    public void issueTransactional() {
        try {
            tkPayOrderService.issueTransactional(JMockData.mock(com.cfpamf.ms.insur.admin.external.tk.model.TkIssueNotify.class), JMockData.mock(com.cfpamf.ms.insur.admin.pojo.po.order.SmOrderExtendTk.class));
        } catch (Exception e) {

        }
    }

    @Test
    public void support() {
        try {
            tkPayOrderService.support(",EzYkO,");
        } catch (Exception e) {

        }
    }

    @Test
    public void getOrderInfo() {
        try {
            tkPayOrderService.getOrderInfo(",BziwV,");
        } catch (Exception e) {

        }
    }

    @Test
    public void getOrderPayUrl() {
        tkPayOrderService.getOrderPayUrl(",AjwTZ,");
    }

    @Test
    public void channelSubmitOrder() {
        tkPayOrderService.channelSubmitOrder(",AjwTZ,", new SmPlanVO(), new OrderSubmitRequest(), new OrderSubmitRequest());
    }
}
