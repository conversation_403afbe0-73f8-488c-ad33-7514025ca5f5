package com.cfpamf.ms.insur.admin.service.order;

import com.cfpamf.ms.insur.admin.constant.AmConstants;
import com.cfpamf.ms.insur.admin.constant.SmConstants;
import com.cfpamf.ms.insur.admin.dao.safes.SmOrderMapper;
import com.cfpamf.ms.insur.admin.external.ChannelOrderService;
import com.cfpamf.ms.insur.admin.external.OrderQueryResponse;
import com.cfpamf.ms.insur.admin.service.BaseTest;
import com.cfpamf.ms.insur.base.event.EventBusEngine;
import com.cfpamf.ms.insur.base.util.HttpRequestUtil;
import com.github.jsonzou.jmockdata.JMockData;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;

import java.io.IOException;

/**
 * <AUTHOR> 2020/3/24 17:50
 */
@Slf4j
@RunWith(PowerMockRunner.class)
@PrepareForTest({HttpRequestUtil.class})
public class CicOrderServiceTest extends BaseTest {
    @InjectMocks
    CicOrderService cicOrderService;

    @Mock
    SmOrderMapper orderMapper;

    @Mock
    EventBusEngine busEngine;

    @Before
    public void setUp() throws Exception {
        super.setUp();
        ChannelOrderService orderService = Mockito.mock(ChannelOrderService.class);

        OrderQueryResponse mockData = JMockData.mock(OrderQueryResponse.class);
        mockData.setNoticeCode(AmConstants.API_FANHUA_SUCCESS_0);
        mockData.getOrderInfo().setOrderState(SmConstants.ORDER_STATUS_PAYED);
        Mockito.when(orderService.queryChannelOrderInfo(Mockito.any())).
                thenReturn(mockData);
        Mockito.when(mock.getBean(Mockito.anyString(), (Class<Object>) Mockito.any()))
                .thenAnswer(a -> {
                    Class<?> beanClass = a.getArgument(1);
                    if (beanClass == ChannelOrderService.class) {
                        return orderService;
                    }
                    return Mockito.mock(beanClass);
                });
    }

    @Test
    public void handAsyncPayCallback() throws IOException {
        cicOrderService.handAsyncPayCallback(",jdcuw,",
                "<?xml version=\"1.0\" encoding=\"GBK\"?>\n" +
                        "<INSUREQ>\n" +
                        "<HEAD>\n" +
                        "<TRANSRNO>1002</TRANSRNO>\n" +
                        "<PARTNERCODE>ZHNX</PARTNERCODE>\n" +
                        "<PARTNERSUBCODE>15</PARTNERSUBCODE>\n" +
                        "</HEAD>\n" +
                        "<MAIN>\n" +
                        "<BASE>\n" +
                        "<SERIALNUMBER>CIC20032401258868</SERIALNUMBER>\n" +
                        "<TRANSDATE>2020-03-24 17:53:40</TRANSDATE>\n" +
                        "<PAYRESULT>0000</PAYRESULT>\n" +
                        "<PAYREMARK>成功</PAYREMARK>\n" +
                        "<PAYMENTTYPE>7</PAYMENTTYPE>\n" +
                        "<PAYDATE>2020-03-24 17:53:30</PAYDATE>\n" +
                        "<PAYAMT>80.00</PAYAMT>\n" +
                        "<PAYID>ZHNX202003240002057421</PAYID>\n" +
                        "<PAYNO>0720200324014050</PAYNO>\n" +
                        "</BASE>\n" +
                        "<POLICYLIST>\n" +
                        "\n" +
                        "<POLICY>\n" +
                        "<APPNO>05202015040118191400150982</APPNO>\n" +
                        "<POLNO>01202015040118191400145111</POLNO>\n" +
                        "<EURL><![CDATA[http://ptn.cic.cn/facade/print/epolicy?polno=01202015040118191400145111&sign=3B91931E39BDBF9A0DE053DCED406B64]]></EURL>\n" +
                        "<RECEIPT_URL><![CDATA[]]></RECEIPT_URL>\n" +
                        "<POLICYTYPE>3</POLICYTYPE>\n" +
                        "</POLICY>\n" +
                        "\n" +
                        "</POLICYLIST>\n" +
                        "</MAIN>\n" +
                        "</INSUREQ>"
                , new MockHttpServletResponse(), new MockHttpServletRequest());
    }

    @Test
    public void support() {
        cicOrderService.support(",IUvDK,");
    }

    @Test
    public void handSyncPayCallback() throws IOException {
        cicOrderService.handSyncPayCallback(",WuXlt,", JMockData.mock(java.lang.Object.class), new MockHttpServletResponse(), new MockHttpServletRequest());
    }
}
