package com.cfpamf.ms.insur.admin.service.product.transform;

import com.cfpamf.ms.insur.admin.config.TestPremiumProperties;
import com.cfpamf.ms.insur.admin.pojo.form.product.TestPremiumDutyForm;
import com.cfpamf.ms.insur.admin.pojo.form.product.TestPremiumForm;
import com.github.jsonzou.jmockdata.JMockData;
import org.junit.Assert;
import org.junit.Test;

import java.util.Collections;

/**
 * <AUTHOR> 2021/8/13 13:32
 */
public class DefaultTransformTest {

    @Test
    public void apply() {
        final DefaultTransform defaultTransform = new DefaultTransform();
        TestPremiumProperties premiumProperties = JMockData.mock(TestPremiumProperties.class);
        defaultTransform.setTestPremiumProperties(premiumProperties);
        final TestPremiumForm mock = JMockData.mock(TestPremiumForm.class);
        for (TestPremiumDutyForm premiumDutyForm : mock.getProduct().getPremiumDutyForms()) {
            premiumDutyForm.getFactorConfig().setParams("{\"baseAmount\":15000}");
        }
        mock.getPerson().getDuties()
                .forEach(s -> s.getFactorConfig().setParams("{\"baseAmount\":15000}"));
        final String validPeriod = defaultTransform.apply("validPeriod", mock, new TestPremiumDutyForm(),
                Collections.emptyList());
        Assert.assertNotNull(validPeriod);
        final String sex = defaultTransform.apply("sex", mock, new TestPremiumDutyForm(),
                Collections.emptyList());
        Assert.assertNull(sex);
    }
}
