//package com.cfpamf.ms.insur.admin.test;
//
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.http.ResponseEntity;
//import org.springframework.web.client.RestTemplate;
//
///**
// * <AUTHOR> 2020/3/27 11:03
// */
//@Slf4j
//public class SentinelTest {
//
//    public static void main(String[] args) {
//        for (int i = 0; i < 32; i++) {
//            new Thread(() -> {
//
//                for (int j = 0; j < 100; j++) {
//                    RestTemplate restTemplate = new RestTemplate();
//                    ResponseEntity<String> forEntity = restTemplate.getForEntity("http://localhost:10056/wx/cancel/6/sentinel", String.class);
////                    log.warn(forEntity.getBody());
//                    System.err.println(forEntity.getBody());
//                }
//            }).run();
//        }
//
//        try {
//            Thread.sleep(3000000000L);
//        } catch (InterruptedException e) {
//            e.printStackTrace();
//        }
//    }
//}
