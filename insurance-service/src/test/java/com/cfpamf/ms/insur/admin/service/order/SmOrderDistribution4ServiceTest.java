//package com.cfpamf.ms.insur.admin.service.order;
//
//import com.cfpamf.common.ms.exception.MSBizNormalException;
//import com.cfpamf.ms.distribution.dto.InsuranceVillageAgentOrderDTO;
//import com.cfpamf.ms.distribution.dto.InsuranceVillageAgentRefundOrderDTO;
//import com.cfpamf.ms.distribution.dto.OrderCommissionDTO;
//import com.cfpamf.ms.insur.admin.dao.safes.SmOrderMapper;
//import com.cfpamf.ms.insur.admin.dao.safes.order.SmOrderDistributionBatchPushPushMapper;
//import com.cfpamf.ms.insur.admin.dao.safes.order.SmOrderDistributionMapper;
//import com.cfpamf.ms.insur.admin.distribution.config.dao.ProductDistributionConfigMapper;
//import com.cfpamf.ms.insur.admin.distribution.config.entity.ProductDistributionConfig;
//import com.cfpamf.ms.insur.admin.external.fh.dto.FhProposer;
//import com.cfpamf.ms.insur.admin.pojo.dto.commission.CommissionQueryResultDTO;
//import com.cfpamf.ms.insur.admin.pojo.po.SmCancel;
//import com.cfpamf.ms.insur.admin.pojo.po.order.SmOrderDistribution;
//import com.cfpamf.ms.insur.admin.pojo.po.order.SmOrderDistributionBatchPush;
//import com.cfpamf.ms.insur.admin.pojo.po.order.SmOrderVillageActivity;
//import com.cfpamf.ms.insur.admin.pojo.vo.SmOrderListVO;
//import com.cfpamf.ms.insur.admin.service.BaseTest;
//import com.cfpamf.ms.insur.admin.service.DistributionProxyService;
//import com.cfpamf.ms.insur.admin.service.commission.CommissionQueryService;
//import com.cfpamf.ms.insur.base.util.HttpRequestUtil;
//import io.swagger.annotations.Example;
//import org.junit.Before;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.mockito.Mock;
//import org.mockito.Mockito;
//import org.powermock.api.mockito.PowerMockito;
//import org.powermock.core.classloader.annotations.PrepareForTest;
//import org.powermock.modules.junit4.PowerMockRunner;
//import org.springframework.aop.framework.AopContext;
//import tk.mybatis.mapper.entity.Config;
//import tk.mybatis.mapper.mapperhelper.EntityHelper;
//
//import java.math.BigDecimal;
//import java.time.LocalDate;
//import java.util.*;
//
//import static org.mockito.ArgumentMatchers.any;
//import static org.mockito.ArgumentMatchers.eq;
//import static org.mockito.Mockito.*;
//
//@RunWith(PowerMockRunner.class)
//@PrepareForTest({HttpRequestUtil.class, AopContext.class})
//public class SmOrderDistribution4ServiceTest extends BaseTest {
//
//    @Mock private SmOrderMapper mockMapper;
//    @Mock private SmOrderDistributionMapper mockDistributionMapper;
//    @Mock private CommissionQueryService mockCommissionQueryService;
//    @Mock private DistributionProxyService mockProxyService;
//    @Mock private SmOrderDistributionBatchPushPushMapper mockDistributionBatchPushPushMapper;
//    @Mock private ProductDistributionConfigMapper mockProductDistributionConfigMapper;
//
//    private SmOrderDistribution4Service smOrderDistribution4ServiceUnderTest;
//
//    @Before
//    public void setUp() throws Exception {
//        smOrderDistribution4ServiceUnderTest = new SmOrderDistribution4Service(mockMapper,
//                                                                               mockDistributionMapper,
//                                                                               mockCommissionQueryService,
//                                                                               mockProxyService,
//                                                                               mockDistributionBatchPushPushMapper,
//                                                                               mockProductDistributionConfigMapper);
//        super.setUp();
//        EntityHelper.initEntityNameMap(SmOrderDistributionBatchPush.class, new Config());
//
//    }
//
//    @Test
//    public void testDistributionSettlement() {
//        // Setup
//        // Configure SmOrderDistributionBatchPushPushMapper.selectByState(...).
//        final SmOrderDistributionBatchPush smOrderDistributionBatchPush = new SmOrderDistributionBatchPush();
//        smOrderDistributionBatchPush.setId(0);
//        smOrderDistributionBatchPush.setOrderId("fhOrderId");
//        smOrderDistributionBatchPush.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
//        smOrderDistributionBatchPush.setLevelOneUserIdNumber("levelOneUserIdNumber");
//        smOrderDistributionBatchPush.setLevelTwoUserIdNumber("levelTwoUserIdNumber");
//        smOrderDistributionBatchPush.setLevelOneOrderCommission(new BigDecimal("0.00"));
//        smOrderDistributionBatchPush.setLevelTwoOrderCommission(new BigDecimal("0.00"));
//        smOrderDistributionBatchPush.setCustomerIdNo("customerIdNo");
//        smOrderDistributionBatchPush.setCustomerName("customerName");
//        smOrderDistributionBatchPush.setCustomerPhoneNum("cellPhone");
//        smOrderDistributionBatchPush.setPayAmount(new BigDecimal("0.00"));
//        smOrderDistributionBatchPush.setPolicyStatus("policyStatus");
//        smOrderDistributionBatchPush.setProductMapperName("mapperName");
//        smOrderDistributionBatchPush.setProductId(0);
//        smOrderDistributionBatchPush.setCommissionSettlementDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
//        smOrderDistributionBatchPush.setPushState(0);
//        final List<SmOrderDistributionBatchPush> smOrderDistributionBatchPushes = Arrays.asList(smOrderDistributionBatchPush);
//        when(mockDistributionBatchPushPushMapper.selectByState(Arrays.asList(0))).thenReturn(smOrderDistributionBatchPushes);
//
//        // Configure DistributionProxyService.createOrder4(...).
//        final InsuranceVillageAgentOrderDTO insuranceVillageAgentOrderDTO = new InsuranceVillageAgentOrderDTO();
//        insuranceVillageAgentOrderDTO.setOrderId("fhOrderId");
//        insuranceVillageAgentOrderDTO.setProductId("productId");
//        insuranceVillageAgentOrderDTO.setProductName("mapperName");
//        insuranceVillageAgentOrderDTO.setCommodityNum(0);
//        insuranceVillageAgentOrderDTO.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
//        insuranceVillageAgentOrderDTO.setLevelOneUserIdNumber("levelOneUserIdNumber");
//        insuranceVillageAgentOrderDTO.setLevelTwoUserIdNumber("levelTwoUserIdNumber");
//        insuranceVillageAgentOrderDTO.setLevelOneOrderCommission(new BigDecimal("0.00"));
//        insuranceVillageAgentOrderDTO.setLevelTwoOrderCommission(new BigDecimal("0.00"));
//        insuranceVillageAgentOrderDTO.setCustomerIdNo("customerIdNo");
//        insuranceVillageAgentOrderDTO.setCustomerName("customerName");
//        insuranceVillageAgentOrderDTO.setCustomerPhoneNum("cellPhone");
//        insuranceVillageAgentOrderDTO.setPayAmount(new BigDecimal("0.00"));
//        insuranceVillageAgentOrderDTO.setOrderStatus(0);
//        insuranceVillageAgentOrderDTO.setCommissionSettlementDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
//        final List<InsuranceVillageAgentOrderDTO> body = Arrays.asList(insuranceVillageAgentOrderDTO);
//        when(mockProxyService.createOrder4(body)).thenReturn(new HashMap<>());
//
//        // Configure SmOrderDistributionMapper.selectOne(...).
//        final SmOrderDistribution smOrderDistribution = new SmOrderDistribution();
//        smOrderDistribution.setId(0);
//        smOrderDistribution.setFhOrderId("fhOrderId");
//        smOrderDistribution.setPolicyNo("policyNo");
//        smOrderDistribution.setDistributionAmount(new BigDecimal("0.00"));
//        smOrderDistribution.setDistributionOrderNo("distributionOrderNo");
//        smOrderDistribution.setDistributionState("distributionState");
//        smOrderDistribution.setDistributionCustName("distributionCustName");
//        smOrderDistribution.setDistributionCustMobile("distributionCustMobile");
//        smOrderDistribution.setDistributionLevel("distributionLevel");
//        smOrderDistribution.setIdNumber("idNumber");
//        smOrderDistribution.setDistributionType(0);
//        final SmOrderDistribution record = new SmOrderDistribution();
//        record.setId(0);
//        record.setFhOrderId("fhOrderId");
//        record.setPolicyNo("policyNo");
//        record.setDistributionAmount(new BigDecimal("0.00"));
//        record.setDistributionOrderNo("distributionOrderNo");
//        record.setDistributionState("distributionState");
//        record.setDistributionCustName("distributionCustName");
//        record.setDistributionCustMobile("distributionCustMobile");
//        record.setDistributionLevel("distributionLevel");
//        record.setIdNumber("idNumber");
//        record.setDistributionType(0);
//        when(mockDistributionMapper.selectOne(record)).thenReturn(smOrderDistribution);
//        PowerMockito.mockStatic(AopContext.class);
//        when(AopContext.currentProxy()).thenReturn(smOrderDistribution4ServiceUnderTest);
//        when(mockDistributionMapper.selectByExample(any(Object.class))).thenReturn(Collections.emptyList());
//
//        // Run the test
//        smOrderDistribution4ServiceUnderTest.distributionSettlement(LocalDate.of(2020, 1, 1), LocalDate.of(2020, 1, 1));
//
//        // Verify the results
//
//        // Confirm SmOrderDistributionMapper.updateByPrimaryKeySelective(...).
//        final SmOrderDistribution record1 = new SmOrderDistribution();
//        record1.setId(0);
//        record1.setFhOrderId("fhOrderId");
//        record1.setPolicyNo("policyNo");
//        record1.setDistributionAmount(new BigDecimal("0.00"));
//        record1.setDistributionOrderNo("distributionOrderNo");
//        record1.setDistributionState("distributionState");
//        record1.setDistributionCustName("distributionCustName");
//        record1.setDistributionCustMobile("distributionCustMobile");
//        record1.setDistributionLevel("distributionLevel");
//        record1.setIdNumber("idNumber");
//        record1.setDistributionType(0);
//
//        // Confirm SmOrderDistributionBatchPushPushMapper.updateByExampleSelective(...).
//        final SmOrderDistributionBatchPush record2 = new SmOrderDistributionBatchPush();
//        record2.setId(0);
//        record2.setOrderId("fhOrderId");
//        record2.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
//        record2.setLevelOneUserIdNumber("levelOneUserIdNumber");
//        record2.setLevelTwoUserIdNumber("levelTwoUserIdNumber");
//        record2.setLevelOneOrderCommission(new BigDecimal("0.00"));
//        record2.setLevelTwoOrderCommission(new BigDecimal("0.00"));
//        record2.setCustomerIdNo("customerIdNo");
//        record2.setCustomerName("customerName");
//        record2.setCustomerPhoneNum("cellPhone");
//        record2.setPayAmount(new BigDecimal("0.00"));
//        record2.setPolicyStatus("policyStatus");
//        record2.setProductMapperName("mapperName");
//        record2.setProductId(0);
//        record2.setCommissionSettlementDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
//        record2.setPushState(0);
//
//        // Confirm DistributionProxyService.refund4(...).
//        final InsuranceVillageAgentRefundOrderDTO insuranceVillageAgentRefundOrderDTO = new InsuranceVillageAgentRefundOrderDTO();
//        insuranceVillageAgentRefundOrderDTO.setRefundOrderId("refundOrderId");
//        insuranceVillageAgentRefundOrderDTO.setOrderId("fhOrderId");
//        insuranceVillageAgentRefundOrderDTO.setRefundTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
//        insuranceVillageAgentRefundOrderDTO.setReturnQty(0);
//        insuranceVillageAgentRefundOrderDTO.setLevelOneOrderCommission(new BigDecimal("0.00"));
//        insuranceVillageAgentRefundOrderDTO.setLevelTwoOrderCommission(new BigDecimal("0.00"));
//        insuranceVillageAgentRefundOrderDTO.setRefundMoney(new BigDecimal("0.00"));
//        final List<InsuranceVillageAgentRefundOrderDTO> body1 = Arrays.asList(insuranceVillageAgentRefundOrderDTO);
//    }
//
//    @Test
//    public void testDistributionSettlement_SmOrderDistributionBatchPushPushMapperSelectByStateReturnsNoItems() {
//        // Setup
//        when(mockDistributionBatchPushPushMapper.selectByState(Arrays.asList(0))).thenReturn(Collections.emptyList());
//
//        // Configure DistributionProxyService.createOrder4(...).
//        final InsuranceVillageAgentOrderDTO insuranceVillageAgentOrderDTO = new InsuranceVillageAgentOrderDTO();
//        insuranceVillageAgentOrderDTO.setOrderId("fhOrderId");
//        insuranceVillageAgentOrderDTO.setProductId("productId");
//        insuranceVillageAgentOrderDTO.setProductName("mapperName");
//        insuranceVillageAgentOrderDTO.setCommodityNum(0);
//        insuranceVillageAgentOrderDTO.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
//        insuranceVillageAgentOrderDTO.setLevelOneUserIdNumber("levelOneUserIdNumber");
//        insuranceVillageAgentOrderDTO.setLevelTwoUserIdNumber("levelTwoUserIdNumber");
//        insuranceVillageAgentOrderDTO.setLevelOneOrderCommission(new BigDecimal("0.00"));
//        insuranceVillageAgentOrderDTO.setLevelTwoOrderCommission(new BigDecimal("0.00"));
//        insuranceVillageAgentOrderDTO.setCustomerIdNo("customerIdNo");
//        insuranceVillageAgentOrderDTO.setCustomerName("customerName");
//        insuranceVillageAgentOrderDTO.setCustomerPhoneNum("cellPhone");
//        insuranceVillageAgentOrderDTO.setPayAmount(new BigDecimal("0.00"));
//        insuranceVillageAgentOrderDTO.setOrderStatus(0);
//        insuranceVillageAgentOrderDTO.setCommissionSettlementDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
//        final List<InsuranceVillageAgentOrderDTO> body = Arrays.asList(insuranceVillageAgentOrderDTO);
//        when(mockProxyService.createOrder4(body)).thenReturn(new HashMap<>());
//
//        // Configure SmOrderDistributionMapper.selectOne(...).
//        final SmOrderDistribution smOrderDistribution = new SmOrderDistribution();
//        smOrderDistribution.setId(0);
//        smOrderDistribution.setFhOrderId("fhOrderId");
//        smOrderDistribution.setPolicyNo("policyNo");
//        smOrderDistribution.setDistributionAmount(new BigDecimal("0.00"));
//        smOrderDistribution.setDistributionOrderNo("distributionOrderNo");
//        smOrderDistribution.setDistributionState("distributionState");
//        smOrderDistribution.setDistributionCustName("distributionCustName");
//        smOrderDistribution.setDistributionCustMobile("distributionCustMobile");
//        smOrderDistribution.setDistributionLevel("distributionLevel");
//        smOrderDistribution.setIdNumber("idNumber");
//        smOrderDistribution.setDistributionType(0);
//        final SmOrderDistribution record = new SmOrderDistribution();
//        record.setId(0);
//        record.setFhOrderId("fhOrderId");
//        record.setPolicyNo("policyNo");
//        record.setDistributionAmount(new BigDecimal("0.00"));
//        record.setDistributionOrderNo("distributionOrderNo");
//        record.setDistributionState("distributionState");
//        record.setDistributionCustName("distributionCustName");
//        record.setDistributionCustMobile("distributionCustMobile");
//        record.setDistributionLevel("distributionLevel");
//        record.setIdNumber("idNumber");
//        record.setDistributionType(0);
//        PowerMockito.mockStatic(AopContext.class);
//        when(AopContext.currentProxy()).thenReturn(smOrderDistribution4ServiceUnderTest);
//        when(mockDistributionMapper.selectOne(record)).thenReturn(smOrderDistribution);
//
//        when(mockDistributionMapper.selectByExample(any(Object.class))).thenReturn(Collections.emptyList());
//
//
//        // Run the test
//        smOrderDistribution4ServiceUnderTest.distributionSettlement(LocalDate.of(2020, 1, 1), LocalDate.of(2020, 1, 1));
//    }
//
//    @Test
//    public void testPushLastMonthAllDate5() {
//        // Setup
//        final SmOrderDistributionBatchPush smOrderDistributionBatchPush = new SmOrderDistributionBatchPush();
//        smOrderDistributionBatchPush.setId(0);
//        smOrderDistributionBatchPush.setOrderId("fhOrderId");
//        smOrderDistributionBatchPush.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
//        smOrderDistributionBatchPush.setLevelOneUserIdNumber("levelOneUserIdNumber");
//        smOrderDistributionBatchPush.setLevelTwoUserIdNumber("levelTwoUserIdNumber");
//        smOrderDistributionBatchPush.setLevelOneOrderCommission(new BigDecimal("0.00"));
//        smOrderDistributionBatchPush.setLevelTwoOrderCommission(new BigDecimal("0.00"));
//        smOrderDistributionBatchPush.setCustomerIdNo("customerIdNo");
//        smOrderDistributionBatchPush.setCustomerName("customerName");
//        smOrderDistributionBatchPush.setCustomerPhoneNum("cellPhone");
//        smOrderDistributionBatchPush.setPayAmount(new BigDecimal("0.00"));
//        smOrderDistributionBatchPush.setPolicyStatus("policyStatus");
//        smOrderDistributionBatchPush.setProductMapperName("mapperName");
//        smOrderDistributionBatchPush.setProductId(0);
//        smOrderDistributionBatchPush.setCommissionSettlementDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
//        smOrderDistributionBatchPush.setPushState(0);
//        final List<SmOrderDistributionBatchPush> distributions = Arrays.asList(smOrderDistributionBatchPush);
//
//        // Configure DistributionProxyService.createOrder4(...).
//        final InsuranceVillageAgentOrderDTO insuranceVillageAgentOrderDTO = new InsuranceVillageAgentOrderDTO();
//        insuranceVillageAgentOrderDTO.setOrderId("fhOrderId");
//        insuranceVillageAgentOrderDTO.setProductId("productId");
//        insuranceVillageAgentOrderDTO.setProductName("mapperName");
//        insuranceVillageAgentOrderDTO.setCommodityNum(0);
//        insuranceVillageAgentOrderDTO.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
//        insuranceVillageAgentOrderDTO.setLevelOneUserIdNumber("levelOneUserIdNumber");
//        insuranceVillageAgentOrderDTO.setLevelTwoUserIdNumber("levelTwoUserIdNumber");
//        insuranceVillageAgentOrderDTO.setLevelOneOrderCommission(new BigDecimal("0.00"));
//        insuranceVillageAgentOrderDTO.setLevelTwoOrderCommission(new BigDecimal("0.00"));
//        insuranceVillageAgentOrderDTO.setCustomerIdNo("customerIdNo");
//        insuranceVillageAgentOrderDTO.setCustomerName("customerName");
//        insuranceVillageAgentOrderDTO.setCustomerPhoneNum("cellPhone");
//        insuranceVillageAgentOrderDTO.setPayAmount(new BigDecimal("0.00"));
//        insuranceVillageAgentOrderDTO.setOrderStatus(0);
//        insuranceVillageAgentOrderDTO.setCommissionSettlementDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
//        final List<InsuranceVillageAgentOrderDTO> body = Arrays.asList(insuranceVillageAgentOrderDTO);
//        when(mockProxyService.createOrder4(body)).thenReturn(new HashMap<>());
//
//        // Configure SmOrderDistributionMapper.selectOne(...).
//        final SmOrderDistribution smOrderDistribution = new SmOrderDistribution();
//        smOrderDistribution.setId(0);
//        smOrderDistribution.setFhOrderId("fhOrderId");
//        smOrderDistribution.setPolicyNo("policyNo");
//        smOrderDistribution.setDistributionAmount(new BigDecimal("0.00"));
//        smOrderDistribution.setDistributionOrderNo("distributionOrderNo");
//        smOrderDistribution.setDistributionState("distributionState");
//        smOrderDistribution.setDistributionCustName("distributionCustName");
//        smOrderDistribution.setDistributionCustMobile("distributionCustMobile");
//        smOrderDistribution.setDistributionLevel("distributionLevel");
//        smOrderDistribution.setIdNumber("idNumber");
//        smOrderDistribution.setDistributionType(0);
//        final SmOrderDistribution record = new SmOrderDistribution();
//        record.setId(0);
//        record.setFhOrderId("fhOrderId");
//        record.setPolicyNo("policyNo");
//        record.setDistributionAmount(new BigDecimal("0.00"));
//        record.setDistributionOrderNo("distributionOrderNo");
//        record.setDistributionState("distributionState");
//        record.setDistributionCustName("distributionCustName");
//        record.setDistributionCustMobile("distributionCustMobile");
//        record.setDistributionLevel("distributionLevel");
//        record.setIdNumber("idNumber");
//        record.setDistributionType(0);
//        PowerMockito.mockStatic(AopContext.class);
//        when(AopContext.currentProxy()).thenReturn(smOrderDistribution4ServiceUnderTest);
//        when(mockDistributionMapper.selectOne(record)).thenReturn(smOrderDistribution);
//
//        // Run the test
//        smOrderDistribution4ServiceUnderTest.pushLastMonthAllDate5("batchNo", distributions);
//
//        // Verify the results
//        // Confirm SmOrderDistributionMapper.updateByPrimaryKeySelective(...).
//        final SmOrderDistribution record1 = new SmOrderDistribution();
//        record1.setId(0);
//        record1.setFhOrderId("fhOrderId");
//        record1.setPolicyNo("policyNo");
//        record1.setDistributionAmount(new BigDecimal("0.00"));
//        record1.setDistributionOrderNo("distributionOrderNo");
//        record1.setDistributionState("distributionState");
//        record1.setDistributionCustName("distributionCustName");
//        record1.setDistributionCustMobile("distributionCustMobile");
//        record1.setDistributionLevel("distributionLevel");
//        record1.setIdNumber("idNumber");
//        record1.setDistributionType(0);
//
//        // Confirm SmOrderDistributionBatchPushPushMapper.updateByExampleSelective(...).
//        final SmOrderDistributionBatchPush record2 = new SmOrderDistributionBatchPush();
//        record2.setId(0);
//        record2.setOrderId("fhOrderId");
//        record2.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
//        record2.setLevelOneUserIdNumber("levelOneUserIdNumber");
//        record2.setLevelTwoUserIdNumber("levelTwoUserIdNumber");
//        record2.setLevelOneOrderCommission(new BigDecimal("0.00"));
//        record2.setLevelTwoOrderCommission(new BigDecimal("0.00"));
//        record2.setCustomerIdNo("customerIdNo");
//        record2.setCustomerName("customerName");
//        record2.setCustomerPhoneNum("cellPhone");
//        record2.setPayAmount(new BigDecimal("0.00"));
//        record2.setPolicyStatus("policyStatus");
//        record2.setProductMapperName("mapperName");
//        record2.setProductId(0);
//        record2.setCommissionSettlementDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
//        record2.setPushState(0);
//    }
//
//    @Test
//    public void testPushLastMonthAllDate5Cancel() {
//        // Setup
//        final SmOrderDistributionBatchPush smOrderDistributionBatchPush = new SmOrderDistributionBatchPush();
//        smOrderDistributionBatchPush.setId(0);
//        smOrderDistributionBatchPush.setOrderId("fhOrderId");
//        smOrderDistributionBatchPush.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
//        smOrderDistributionBatchPush.setLevelOneUserIdNumber("levelOneUserIdNumber");
//        smOrderDistributionBatchPush.setLevelTwoUserIdNumber("levelTwoUserIdNumber");
//        smOrderDistributionBatchPush.setLevelOneOrderCommission(new BigDecimal("0.00"));
//        smOrderDistributionBatchPush.setLevelTwoOrderCommission(new BigDecimal("0.00"));
//        smOrderDistributionBatchPush.setCustomerIdNo("customerIdNo");
//        smOrderDistributionBatchPush.setCustomerName("customerName");
//        smOrderDistributionBatchPush.setCustomerPhoneNum("cellPhone");
//        smOrderDistributionBatchPush.setPayAmount(new BigDecimal("0.00"));
//        smOrderDistributionBatchPush.setPolicyStatus("policyStatus");
//        smOrderDistributionBatchPush.setProductMapperName("mapperName");
//        smOrderDistributionBatchPush.setProductId(0);
//        smOrderDistributionBatchPush.setCommissionSettlementDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
//        PowerMockito.mockStatic(AopContext.class);
//        when(AopContext.currentProxy()).thenReturn(smOrderDistribution4ServiceUnderTest);
//        smOrderDistributionBatchPush.setPushState(0);
//        final List<SmOrderDistributionBatchPush> distributions = Arrays.asList(smOrderDistributionBatchPush);
//
//        // Run the test
//        smOrderDistribution4ServiceUnderTest.pushLastMonthAllDate5Cancel("batchNo", distributions);
//
//        // Verify the results
//        // Confirm DistributionProxyService.refund4(...).
//        final InsuranceVillageAgentRefundOrderDTO insuranceVillageAgentRefundOrderDTO = new InsuranceVillageAgentRefundOrderDTO();
//        insuranceVillageAgentRefundOrderDTO.setRefundOrderId("refundOrderId");
//        insuranceVillageAgentRefundOrderDTO.setOrderId("fhOrderId");
//        insuranceVillageAgentRefundOrderDTO.setRefundTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
//        insuranceVillageAgentRefundOrderDTO.setReturnQty(0);
//        insuranceVillageAgentRefundOrderDTO.setLevelOneOrderCommission(new BigDecimal("0.00"));
//        insuranceVillageAgentRefundOrderDTO.setLevelTwoOrderCommission(new BigDecimal("0.00"));
//        insuranceVillageAgentRefundOrderDTO.setRefundMoney(new BigDecimal("0.00"));
//        final List<InsuranceVillageAgentRefundOrderDTO> body = Arrays.asList(insuranceVillageAgentRefundOrderDTO);
//
//        // Confirm SmOrderDistributionBatchPushPushMapper.updateByExampleSelective(...).
//        final SmOrderDistributionBatchPush record = new SmOrderDistributionBatchPush();
//        record.setId(0);
//        record.setOrderId("fhOrderId");
//        record.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
//        record.setLevelOneUserIdNumber("levelOneUserIdNumber");
//        record.setLevelTwoUserIdNumber("levelTwoUserIdNumber");
//        record.setLevelOneOrderCommission(new BigDecimal("0.00"));
//        record.setLevelTwoOrderCommission(new BigDecimal("0.00"));
//        record.setCustomerIdNo("customerIdNo");
//        record.setCustomerName("customerName");
//        record.setCustomerPhoneNum("cellPhone");
//        record.setPayAmount(new BigDecimal("0.00"));
//        record.setPolicyStatus("policyStatus");
//        record.setProductMapperName("mapperName");
//        record.setProductId(0);
//        record.setCommissionSettlementDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
//        record.setPushState(0);
//    }
//
//    @Test
//    public void testDistribution() {
//        // Setup
//        final SmOrderListVO orderInfo = new SmOrderListVO();
//        orderInfo.setInsuredId(0);
//        orderInfo.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
//        orderInfo.setSubmitTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
//        orderInfo.setProductId(0);
//        orderInfo.setPolicyNo("policyNo");
//
//        final SmOrderVillageActivity activity = new SmOrderVillageActivity();
//        activity.setId(0L);
//        activity.setFhOrderId("fhOrderId");
//        activity.setActivityCode("activityCode");
//        activity.setVillageRepresentativeIdNumber("levelTwoUserIdNumber");
//        activity.setManagerIdNumber("levelOneUserIdNumber");
//
//        // Configure CommissionQueryService.getCommissionByOrderId(...).
//        final CommissionQueryResultDTO commissionQueryResultDTO = new CommissionQueryResultDTO();
//        commissionQueryResultDTO.setOrderId("orderId");
//        commissionQueryResultDTO.setPolicyStatus("policyStatus");
//        commissionQueryResultDTO.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
//        commissionQueryResultDTO.setAmount(new BigDecimal("0.00"));
//        commissionQueryResultDTO.setPaymentAmount(new BigDecimal("0.00"));
//        final List<CommissionQueryResultDTO> commissionQueryResultDTOS = Arrays.asList(commissionQueryResultDTO);
//        when(mockCommissionQueryService.getCommissionByOrderId(Arrays.asList("value"))).thenReturn(commissionQueryResultDTOS);
//
//        // Configure SmOrderDistributionMapper.selectByFhOrderId(...).
//        final SmOrderDistribution smOrderDistribution = new SmOrderDistribution();
//        smOrderDistribution.setId(0);
//        smOrderDistribution.setFhOrderId("fhOrderId");
//        smOrderDistribution.setPolicyNo("policyNo");
//        smOrderDistribution.setDistributionAmount(new BigDecimal("0.00"));
//        smOrderDistribution.setDistributionOrderNo("distributionOrderNo");
//        smOrderDistribution.setDistributionState("distributionState");
//        smOrderDistribution.setDistributionCustName("distributionCustName");
//        smOrderDistribution.setDistributionCustMobile("distributionCustMobile");
//        smOrderDistribution.setDistributionLevel("distributionLevel");
//        smOrderDistribution.setIdNumber("idNumber");
//        smOrderDistribution.setDistributionType(0);
//        final List<SmOrderDistribution> smOrderDistributions = Arrays.asList(smOrderDistribution);
//        when(mockDistributionMapper.selectByFhOrderId("fhOrderId")).thenReturn(smOrderDistributions);
//
//        // Run the test
//        smOrderDistribution4ServiceUnderTest.distribution("fhOrderId", orderInfo, activity);
//
//        // Verify the results
//    }
//
//    @Test
//    public void testDistribution_CommissionQueryServiceReturnsNoItems() {
//        // Setup
//        final SmOrderListVO orderInfo = new SmOrderListVO();
//        orderInfo.setInsuredId(0);
//        orderInfo.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
//        orderInfo.setSubmitTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
//        orderInfo.setProductId(0);
//        orderInfo.setPolicyNo("policyNo");
//
//        final SmOrderVillageActivity activity = new SmOrderVillageActivity();
//        activity.setId(0L);
//        activity.setFhOrderId("fhOrderId");
//        activity.setActivityCode("activityCode");
//        activity.setVillageRepresentativeIdNumber("levelTwoUserIdNumber");
//        activity.setManagerIdNumber("levelOneUserIdNumber");
//
//        when(mockCommissionQueryService.getCommissionByOrderId(Arrays.asList("value"))).thenReturn(Collections.emptyList());
//
//        // Run the test
//        smOrderDistribution4ServiceUnderTest.distribution("fhOrderId", orderInfo, activity);
//
//        // Verify the results
//    }
//
//    @Test
//    public void testDistribution_SmOrderDistributionMapperSelectByFhOrderIdReturnsNoItems() {
//        // Setup
//        final SmOrderListVO orderInfo = new SmOrderListVO();
//        orderInfo.setInsuredId(0);
//        orderInfo.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
//        orderInfo.setSubmitTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
//        orderInfo.setProductId(0);
//        orderInfo.setPolicyNo("policyNo");
//
//        final SmOrderVillageActivity activity = new SmOrderVillageActivity();
//        activity.setId(0L);
//        activity.setFhOrderId("fhOrderId");
//        activity.setActivityCode("activityCode");
//        activity.setVillageRepresentativeIdNumber("levelTwoUserIdNumber");
//        activity.setManagerIdNumber("levelOneUserIdNumber");
//
//        // Configure CommissionQueryService.getCommissionByOrderId(...).
//        final CommissionQueryResultDTO commissionQueryResultDTO = new CommissionQueryResultDTO();
//        commissionQueryResultDTO.setOrderId("orderId");
//        commissionQueryResultDTO.setPolicyStatus("policyStatus");
//        commissionQueryResultDTO.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
//        commissionQueryResultDTO.setAmount(new BigDecimal("0.00"));
//        commissionQueryResultDTO.setPaymentAmount(new BigDecimal("0.00"));
//        final List<CommissionQueryResultDTO> commissionQueryResultDTOS = Arrays.asList(commissionQueryResultDTO);
//        when(mockCommissionQueryService.getCommissionByOrderId(Arrays.asList("value"))).thenReturn(commissionQueryResultDTOS);
//
//        when(mockDistributionMapper.selectByFhOrderId("fhOrderId")).thenReturn(Collections.emptyList());
//
//        // Configure SmOrderMapper.getOrderApplicant(...).
//        final FhProposer fhProposer = new FhProposer();
//        fhProposer.setPersonName("customerName");
//        fhProposer.setPersonGender("personGender");
//        fhProposer.setIdType("idType");
//        fhProposer.setIdNumber("customerIdNo");
//        fhProposer.setCellPhone("cellPhone");
//        when(mockMapper.getOrderApplicant("fhOrderId")).thenReturn(fhProposer);
//
//        // Configure ProductDistributionConfigMapper.getByProductId(...).
//        final ProductDistributionConfig productDistributionConfig = new ProductDistributionConfig();
//        productDistributionConfig.setId(0);
//        productDistributionConfig.setProductId(0L);
//        productDistributionConfig.setMapperName("mapperName");
//        when(mockProductDistributionConfigMapper.getByProductId(0L)).thenReturn(productDistributionConfig);
//
//        // Configure DistributionProxyService.createOrder4(...).
//        final OrderCommissionDTO orderCommissionDTO = new OrderCommissionDTO();
//        orderCommissionDTO.setOrderId("distributionOrderNo");
//        orderCommissionDTO.setOrderCommission(new BigDecimal("0.00"));
//        orderCommissionDTO.setCommissionPaymentStatus(0);
//        orderCommissionDTO.setUniqueId("idNumber");
//        orderCommissionDTO.setUserName("distributionCustName");
//        orderCommissionDTO.setUserMobile("distributionCustMobile");
//        orderCommissionDTO.setLevel("distributionLevel");
//        final List<OrderCommissionDTO> orderCommissionDTOS = Arrays.asList(orderCommissionDTO);
//        final InsuranceVillageAgentOrderDTO body = new InsuranceVillageAgentOrderDTO();
//        body.setOrderId("fhOrderId");
//        body.setProductId("productId");
//        body.setProductName("mapperName");
//        body.setCommodityNum(0);
//        body.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
//        body.setLevelOneUserIdNumber("levelOneUserIdNumber");
//        body.setLevelTwoUserIdNumber("levelTwoUserIdNumber");
//        body.setLevelOneOrderCommission(new BigDecimal("0.00"));
//        body.setLevelTwoOrderCommission(new BigDecimal("0.00"));
//        body.setCustomerIdNo("customerIdNo");
//        body.setCustomerName("customerName");
//        body.setCustomerPhoneNum("cellPhone");
//        body.setPayAmount(new BigDecimal("0.00"));
//        body.setOrderStatus(0);
//        body.setCommissionSettlementDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
//        when(mockProxyService.createOrder4(body)).thenReturn(orderCommissionDTOS);
//
//        // Run the test
//        smOrderDistribution4ServiceUnderTest.distribution("fhOrderId", orderInfo, activity);
//
//        // Verify the results
//        // Confirm SmOrderDistributionMapper.insertList(...).
//        final SmOrderDistribution smOrderDistribution = new SmOrderDistribution();
//        smOrderDistribution.setId(0);
//        smOrderDistribution.setFhOrderId("fhOrderId");
//        smOrderDistribution.setPolicyNo("policyNo");
//        smOrderDistribution.setDistributionAmount(new BigDecimal("0.00"));
//        smOrderDistribution.setDistributionOrderNo("distributionOrderNo");
//        smOrderDistribution.setDistributionState("distributionState");
//        smOrderDistribution.setDistributionCustName("distributionCustName");
//        smOrderDistribution.setDistributionCustMobile("distributionCustMobile");
//        smOrderDistribution.setDistributionLevel("distributionLevel");
//        smOrderDistribution.setIdNumber("idNumber");
//        smOrderDistribution.setDistributionType(0);
//        final List<SmOrderDistribution> recordList = Arrays.asList(smOrderDistribution);
//    }
//
//    @Test
//    public void testDistribution_DistributionProxyServiceReturnsNoItems() {
//        // Setup
//        final SmOrderListVO orderInfo = new SmOrderListVO();
//        orderInfo.setInsuredId(0);
//        orderInfo.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
//        orderInfo.setSubmitTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
//        orderInfo.setProductId(0);
//        orderInfo.setPolicyNo("policyNo");
//
//        final SmOrderVillageActivity activity = new SmOrderVillageActivity();
//        activity.setId(0L);
//        activity.setFhOrderId("fhOrderId");
//        activity.setActivityCode("activityCode");
//        activity.setVillageRepresentativeIdNumber("levelTwoUserIdNumber");
//        activity.setManagerIdNumber("levelOneUserIdNumber");
//
//        // Configure CommissionQueryService.getCommissionByOrderId(...).
//        final CommissionQueryResultDTO commissionQueryResultDTO = new CommissionQueryResultDTO();
//        commissionQueryResultDTO.setOrderId("orderId");
//        commissionQueryResultDTO.setPolicyStatus("policyStatus");
//        commissionQueryResultDTO.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
//        commissionQueryResultDTO.setAmount(new BigDecimal("0.00"));
//        commissionQueryResultDTO.setPaymentAmount(new BigDecimal("0.00"));
//        final List<CommissionQueryResultDTO> commissionQueryResultDTOS = Arrays.asList(commissionQueryResultDTO);
//        when(mockCommissionQueryService.getCommissionByOrderId(Arrays.asList("value"))).thenReturn(commissionQueryResultDTOS);
//
//        when(mockDistributionMapper.selectByFhOrderId("fhOrderId")).thenReturn(Collections.emptyList());
//
//        // Configure SmOrderMapper.getOrderApplicant(...).
//        final FhProposer fhProposer = new FhProposer();
//        fhProposer.setPersonName("customerName");
//        fhProposer.setPersonGender("personGender");
//        fhProposer.setIdType("idType");
//        fhProposer.setIdNumber("customerIdNo");
//        fhProposer.setCellPhone("cellPhone");
//        when(mockMapper.getOrderApplicant("fhOrderId")).thenReturn(fhProposer);
//
//        // Configure ProductDistributionConfigMapper.getByProductId(...).
//        final ProductDistributionConfig productDistributionConfig = new ProductDistributionConfig();
//        productDistributionConfig.setId(0);
//        productDistributionConfig.setProductId(0L);
//        productDistributionConfig.setMapperName("mapperName");
//        when(mockProductDistributionConfigMapper.getByProductId(0L)).thenReturn(productDistributionConfig);
//
//        // Configure DistributionProxyService.createOrder4(...).
//        final InsuranceVillageAgentOrderDTO body = new InsuranceVillageAgentOrderDTO();
//        body.setOrderId("fhOrderId");
//        body.setProductId("productId");
//        body.setProductName("mapperName");
//        body.setCommodityNum(0);
//        body.setPayTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
//        body.setLevelOneUserIdNumber("levelOneUserIdNumber");
//        body.setLevelTwoUserIdNumber("levelTwoUserIdNumber");
//        body.setLevelOneOrderCommission(new BigDecimal("0.00"));
//        body.setLevelTwoOrderCommission(new BigDecimal("0.00"));
//        body.setCustomerIdNo("customerIdNo");
//        body.setCustomerName("customerName");
//        body.setCustomerPhoneNum("cellPhone");
//        body.setPayAmount(new BigDecimal("0.00"));
//        body.setOrderStatus(0);
//        body.setCommissionSettlementDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
//        when(mockProxyService.createOrder4(body)).thenReturn(Collections.emptyList());
//
//        // Run the test
//        smOrderDistribution4ServiceUnderTest.distribution("fhOrderId", orderInfo, activity);
//    }
//}
