package com.cfpamf.ms.insur.admin.external.ygibao.model;

import com.cfpamf.ms.insur.admin.external.OrderPrePayRequest;
import com.cfpamf.ms.insur.admin.external.OrderQueryRequest;
import com.cfpamf.ms.insur.admin.external.OrderSubmitRequest;
import com.cfpamf.ms.insur.admin.external.whale.EnumWhaleCompany;
import com.cfpamf.ms.insur.admin.external.ygibao.YgOrderServiceAdapterImpl;
import com.cfpamf.ms.insur.admin.pojo.dto.SmCreateOrderSubmitRequest;
import com.cfpamf.ms.insur.admin.service.BaseTest;
import com.cfpamf.ms.insur.base.util.HttpRequestUtil;
import com.github.jsonzou.jmockdata.JMockData;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

/**
 * <AUTHOR> 2021/11/2 16:25
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({HttpRequestUtil.class})
public class YgOrderServiceAdapterTest extends BaseTest {
    @InjectMocks
    YgOrderServiceAdapterImpl ygOrderServiceAdapter;
    @org.mockito.Mock
    com.cfpamf.ms.insur.admin.service.OrderNoGenerator orderNoGenerator;
    @org.mockito.Mock
    com.cfpamf.ms.insur.admin.service.SmXjxhService whaleService;

    @Test
    public void genDynamicUrl() {
        ygOrderServiceAdapter.genDynamicUrl(",mlmat,", ",jwZTB,", "xianghu", null);
    }

    @Test
    public void submitChannelOrder() {
        try {
            ygOrderServiceAdapter.submitChannelOrder(new OrderSubmitRequest());
        } catch (UnsupportedOperationException e) {
// not support
        }
    }

    @Test
    public void prePayChannelOrder() {
        try {
            ygOrderServiceAdapter.prePayChannelOrder(new OrderPrePayRequest());
        } catch (Exception e) {

        }
    }

    @Test
    public void queryChannelOrderInfo() {
        try {
            ygOrderServiceAdapter.queryChannelOrderInfo(new OrderQueryRequest());
        } catch (Exception e) {

        }
    }

    @Test
    public void getOrderPayedCallbackResp() {
        try {
            ygOrderServiceAdapter.getOrderPayedCallbackResp(",VlgtV,");
        } catch (Exception e) {

        }
    }

    @Test
    public void cvtByNotify() {
        YgPolicyContractInfoVo mock = JMockData.mock(YgPolicyContractInfoVo.class, con());
        mock.setCompanyCode(EnumWhaleCompany.YGCX.getWhaleCompanyCode());
        mock.setEnforceTime("2020-08-11 00:00:00");
        YgPolicyInsuredInfoList insuredInfoList = mock.getPolicyInsuredInfoList().get(0);
        insuredInfoList.setInsuredBirthday("1995-08-08");
        YgPolicyProductInfoList productInfo = insuredInfoList.getPolicyProductInfoList().get(0);

        productInfo.setMainInsurance(1);
        productInfo.setInsuredPeriod(30);
        productInfo.setInsuredPeriodType("INSURED_PERIOD_TYPE:0");
        SmCreateOrderSubmitRequest smCreateOrderSubmitRequest = ygOrderServiceAdapter.cvtByNotify(mock, ",Xekfr,", ",jozpA,", "12");
        Assert.assertEquals("2050-08-10 23:59:59", smCreateOrderSubmitRequest.getOrderInfo().getEndTime());
    }

    @Test
    public void cvtByNotifyYgrs() {
        YgPolicyContractInfoVo mock = JMockData.mock(YgPolicyContractInfoVo.class, con());
        mock.setCompanyCode(EnumWhaleCompany.YGRS.getWhaleCompanyCode());
        mock.setEnforceTime("2020-08-11 00:00:00");
        YgPolicyInsuredInfoList insuredInfoList = mock.getPolicyInsuredInfoList().get(0);
        insuredInfoList.setInsuredBirthday("1995-08-08");
        YgPolicyProductInfoList productInfo = insuredInfoList.getPolicyProductInfoList().get(0);

        productInfo.setMainInsurance(1);
        productInfo.setInsuredPeriod(30);
        productInfo.setInsuredPeriodType("INSURED_PERIOD_TYPE:0");
        SmCreateOrderSubmitRequest smCreateOrderSubmitRequest = ygOrderServiceAdapter.cvtByNotify(mock, ",Xekfr,", ",jozpA,", null);
        Assert.assertEquals("2050-08-10 23:59:59", smCreateOrderSubmitRequest.getOrderInfo().getEndTime());
    }
}
