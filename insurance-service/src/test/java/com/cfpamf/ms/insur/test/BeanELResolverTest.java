package com.cfpamf.ms.insur.test;

import com.cfpamf.ms.insur.admin.external.ygibao.model.YgPolicyContractInfoVo;
import com.github.jsonzou.jmockdata.JMockData;
import org.junit.Test;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.spel.standard.SpelExpression;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;

/**
 * <AUTHOR> 2021/12/16 14:34
 */
public class BeanELResolverTest {

    @Test
    public void testEl(){

        SpelExpressionParser expressionParser = new SpelExpressionParser();
        YgPolicyContractInfoVo mock = JMockData.mock(YgPolicyContractInfoVo.class);
        EvaluationContext context = new StandardEvaluationContext(mock);
        SpelExpression spelExpression = expressionParser.parseRaw("#root.policyNo");
        Object value = spelExpression.getValue(context);
        System.err.println(value);
    }
}
