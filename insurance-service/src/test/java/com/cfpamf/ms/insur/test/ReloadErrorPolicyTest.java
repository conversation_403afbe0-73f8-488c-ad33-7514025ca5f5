//package com.cfpamf.ms.insur.base.config;
//
//import com.aliyun.oss.OSS;
//import com.aliyun.oss.OSSClientBuilder;
//import com.aliyun.oss.model.CannedAccessControlList;
//import com.aliyun.oss.model.ObjectMetadata;
//import com.cfpamf.ms.insur.admin.dao.safes.SmPolicyCacheMapper;
//import com.cfpamf.ms.insur.admin.pojo.po.SmPolicyCache;
//import com.cfpamf.ms.insur.base.constant.BaseConstants;
//import lombok.Cleanup;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.codec.binary.Hex;
//import org.apache.http.client.HttpClient;
//import org.apache.http.impl.client.HttpClientBuilder;
//import org.apache.http.impl.client.LaxRedirectStrategy;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.CommandLineRunner;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.context.annotation.Profile;
//import org.springframework.core.io.Resource;
//import org.springframework.http.ResponseEntity;
//import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
//import org.springframework.web.client.RestTemplate;
//
//import java.io.IOException;
//import java.io.InputStream;
//import java.util.List;
//import java.util.Objects;
//import java.util.zip.ZipEntry;
//import java.util.zip.ZipInputStream;
//
///**
// 重新加载下载出错所有的保单文件
// * <AUTHOR> 2020/4/10 14:28
// */
//@Slf4j
//@Profile("localtest")
//@Configuration
//public class LocalTestConfig implements CommandLineRunner {
//
//    @Autowired
//    SmPolicyCacheMapper policyCacheMapper;
//
//    /**
//     * 构造https请求参数
//     *
//     * @return
//     */
//    private static RestTemplate getHttpsTemplate() {
//        // RestTemplate 支持服务器内302重定向
//        HttpComponentsClientHttpRequestFactory factory = new HttpComponentsClientHttpRequestFactory();
//        HttpClient httpClient = HttpClientBuilder.create()
//                .setRedirectStrategy(new LaxRedirectStrategy())
//                .build();
//        factory.setHttpClient(httpClient);
//        return new RestTemplate(factory);
//    }
//
//    @Override
//    public void run(String... args) throws Exception {
//
//        RestTemplate restTemplate = getHttpsTemplate();
//
//        OSS oss = new OSSClientBuilder().build("https://oss-cn-beijing.aliyuncs.com",
//                "LTAI4FeQAMY7LFxtNFEPZQax",
//                "******************************");
//
//        String bucket = "safesfiles";
//        List<SmPolicyCache> smPolicyCaches =
//                policyCacheMapper.listAllError();
//        System.err.println(smPolicyCaches.size());
//
//        smPolicyCaches.parallelStream().forEach(a -> {
//            String ossKey = a.getOssKey();
//            if (!oss.doesObjectExist(bucket, ossKey)) {
//                log.info("oss key is not exists {}", a.getOssKey());
//                try {
//                    ResponseEntity<Resource> responseEntity = restTemplate.getForEntity(a.getSourceUrl(), Resource.class);
//                    @Cleanup
//                    InputStream is = null;
//
//                    is = responseEntity.getBody().getInputStream();
//
//                    //判断文件类型
//                    byte[] headBytes = new byte[4];
//                    is.read(headBytes, 0, 4);
//                    String headHexStr = Hex.encodeHexString(headBytes);
//                    is.reset();
//
//                    ObjectMetadata metadata = new ObjectMetadata();
//                    metadata.setContentLength(is.available());
//
//                    metadata.setCacheControl("public");
//                    metadata.setHeader("Pragma", "cache");
//                    metadata.setContentEncoding("utf-8");
//                    metadata.setContentType("application/pdf");
//                    metadata.setObjectAcl(CannedAccessControlList.PublicRead);
//
//                    // PDF 类别
//                    if (Objects.equals(BaseConstants.PDF_HEAD_HEX_STR, headHexStr)) {
//                        oss.putObject(bucket,
//                                ossKey, is, metadata);
//                    } else {// 统一安装zip处理
//                        @Cleanup
//                        ZipInputStream zis = new ZipInputStream(is);
//                        ZipEntry ze = null;
//                        if (((ze = zis.getNextEntry()) != null) && !ze.isDirectory()) {
//                            oss.putObject(bucket,
//                                    ossKey, zis, metadata);
//                        }
//                    }
//                } catch (IOException e) {
//                    e.printStackTrace();
//                }
//            } else {
//                log.info("oss key is exists {}", a.getOssKey());
//            }
//
//        });
//    }
//}
