package com.cfpamf.ms.insur.admin.service.product.transform;

import com.alibaba.fastjson.JSONArray;
import com.cfpamf.ms.insur.admin.pojo.form.product.TestPremiumDutyForm;
import com.cfpamf.ms.insur.admin.pojo.form.product.TestPremiumForm;
import com.cfpamf.ms.insur.admin.pojo.po.product.risk.SysRiskFactor;
import com.github.jsonzou.jmockdata.JMockData;
import org.junit.Assert;
import org.junit.Test;

import java.util.List;

/**
 * <AUTHOR> 2021/12/6 13:49
 */
public class InsuredNumTransformTest {

    InsuredNumTransform transform = new InsuredNumTransform();

    @Test
    public void testSupportField() {
        Assert.assertEquals("insuredNum", transform.supportField());
    }

    @Test
    public void testApply() {
        String s = "[\n" +
                "  {\n" +
                "    \"id\": 3355,\n" +
                "    \"version\": 1,\n" +
                "    \"riskKey\": \"dc5bf2b8e29b477f990a815249888e86\",\n" +
                "    \"factorConfigKey\": \"2db6558f630541e1b9b988892e135554\",\n" +
                "    \"targetId\": \"45\",\n" +
                "    \"targetType\": 1,\n" +
                "    \"fieldCode\": \"insuredNum\",\n" +
                "    \"optionalName\": \"4-7人\",\n" +
                "    \"optionalValue\": \"4-7\",\n" +
                "    \"value1\": \"4\",\n" +
                "    \"value2\": \"7\",\n" +
                "    \"updateTime\": \"2021-12-03 15:53:35\",\n" +
                "    \"enabledFlag\": 0,\n" +
                "    \"createTime\": \"2021-12-03 15:53:35\"\n" +
                "  },\n" +
                "  {\n" +
                "    \"id\": 3354,\n" +
                "    \"version\": 1,\n" +
                "    \"riskKey\": \"dc5bf2b8e29b477f990a815249888e86\",\n" +
                "    \"factorConfigKey\": \"2db6558f630541e1b9b988892e135554\",\n" +
                "    \"targetId\": \"45\",\n" +
                "    \"targetType\": 1,\n" +
                "    \"fieldCode\": \"insuredNum\",\n" +
                "    \"optionalName\": \"3人\",\n" +
                "    \"optionalValue\": \"3\",\n" +
                "    \"value1\": \"3\",\n" +
                "    \"value2\": \"3\",\n" +
                "    \"updateTime\": \"2021-12-03 15:53:35\",\n" +
                "    \"enabledFlag\": 0,\n" +
                "    \"createTime\": \"2021-12-03 15:53:35\"\n" +
                "  },\n" +
                "  {\n" +
                "    \"id\": 3353,\n" +
                "    \"version\": 1,\n" +
                "    \"riskKey\": \"dc5bf2b8e29b477f990a815249888e86\",\n" +
                "    \"factorConfigKey\": \"2db6558f630541e1b9b988892e135554\",\n" +
                "    \"targetId\": \"45\",\n" +
                "    \"targetType\": 1,\n" +
                "    \"fieldCode\": \"insuredNum\",\n" +
                "    \"optionalName\": \"2人\",\n" +
                "    \"optionalValue\": \"2\",\n" +
                "    \"value1\": \"2\",\n" +
                "    \"value2\": \"2\",\n" +
                "    \"updateTime\": \"2021-12-03 15:53:35\",\n" +
                "    \"enabledFlag\": 0,\n" +
                "    \"createTime\": \"2021-12-03 15:53:35\"\n" +
                "  },\n" +
                "  {\n" +
                "    \"id\": 3352,\n" +
                "    \"version\": 1,\n" +
                "    \"riskKey\": \"dc5bf2b8e29b477f990a815249888e86\",\n" +
                "    \"factorConfigKey\": \"2db6558f630541e1b9b988892e135554\",\n" +
                "    \"targetId\": \"45\",\n" +
                "    \"targetType\": 1,\n" +
                "    \"fieldCode\": \"insuredNum\",\n" +
                "    \"optionalName\": \"1人\",\n" +
                "    \"optionalValue\": \"1\",\n" +
                "    \"value1\": \"1\",\n" +
                "    \"value2\": \"1\",\n" +
                "    \"updateTime\": \"2021-12-03 15:53:35\",\n" +
                "    \"enabledFlag\": 0,\n" +
                "    \"createTime\": \"2021-12-03 15:53:35\"\n" +
                "  }\n" +
                "]";

        List<SysRiskFactor> factors = JSONArray.parseArray(s, SysRiskFactor.class);
        TestPremiumForm mock = JMockData.mock(TestPremiumForm.class);
        mock.getPerson().getTestExtends().setInsuredNum(1);
        String insuredNum = transform.apply("insuredNum", mock, JMockData.mock(TestPremiumDutyForm.class), factors);
        Assert.assertEquals("1", insuredNum);
    }
}
