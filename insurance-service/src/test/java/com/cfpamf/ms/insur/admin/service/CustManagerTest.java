package com.cfpamf.ms.insur.admin.service;

import com.alibaba.fastjson.JSON;
import com.cfpamf.cmis.common.base.CommonResult;
import com.cfpamf.cmis.common.base.PageBean;
import com.cfpamf.common.ms.result.Result;
import com.cfpamf.common.ms.vo.PageVO;
import com.cfpamf.ms.customer.facade.api.AuthenticationFacade;
import com.cfpamf.ms.customer.facade.api.ManageFacade;
import com.cfpamf.ms.customer.facade.enums.CappSMSEnum;
import com.cfpamf.ms.customer.facade.enums.UserBizCodeEnum;
import com.cfpamf.ms.customer.facade.request.*;
import com.cfpamf.ms.customer.facade.request.manage.QueryCustBaseInfoPageWithAuthcReq;
import com.cfpamf.ms.customer.facade.vo.CustBindLoanVo;
import com.cfpamf.ms.customer.facade.vo.CustCertifyVo;
import com.cfpamf.ms.customer.facade.vo.CustInfoVo;
import com.cfpamf.ms.customer.facade.vo.UserInfoVo;
import org.junit.FixMethodOrder;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.MethodSorters;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

/**
 * <AUTHOR>
 **/
/*@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("dev")
@FixMethodOrder(MethodSorters.NAME_ASCENDING)*/
public class CustManagerTest {
    /*@Autowired
    private AuthenticationFacade facade;
    @Autowired
    private ManageFacade manageFacade;


    @Test
    public void custBaseInfoPageWithAuthc() {
        QueryCustBaseInfoPageWithAuthcReq query = new QueryCustBaseInfoPageWithAuthcReq();
        query.setOperator("HNYS0010");
        query.setPageNumber(1);
        query.setPageSize(10);
        query.setCustName("张");
        Result<PageVO<CustInfoVo>> result = manageFacade.custBaseInfoPageWithAuthc(query);
        System.out.println(JSON.toJSONString(result));
    }

    @Test
    public void listCustInfoByManager() {
        Result<List<CustInfoVo>> result = facade.listCustInfoByManager("HNYS0010");
//        System.out.println(JSON.toJSONString(result));
    }

    @Test
    public void findCustForBind() {
        // 不通
        CustBindLoanRequest request = new CustBindLoanRequest();
        request.setIdNo("3622011990091811");
        request.setPageIndex(1);
        request.setPageSize(10);
        CommonResult<PageBean<CustBindLoanVo>> result = facade.findCustForBind(request);
        CustInfoQueryByCustNameAndMobileReq req = new CustInfoQueryByCustNameAndMobileReq();
        req.setCustName("田华");
        // 可以通
        CommonResult<List<CustInfoVo>> commonResult = facade.queryCustInfoByCustNameAndMobile(req);
//        System.out.println(JSON.toJSONString(result));
    }

    @Test
    public void userInfo() {
        UserInfoReq request = new UserInfoReq();
        request.setBizCode(UserBizCodeEnum.INSURANCE);
        request.setMobile("15574307649");
        Result<UserInfoVo> result = facade.userInfo(request);
//        System.out.println(JSON.toJSONString(result));
    }

    *//**
     * 注册
     *//*
    @Test
    public void register() {
        CustRegisterRequest request = new CustRegisterRequest();
        request.setMobile("***********");
        request.setBizCode(UserBizCodeEnum.INSURANCE);
        request.setPassword("191811");
        request.setChannel("INSURANCE");
        CommonResult<UserInfoVo> result = facade.register(request);
//        System.out.println(JSON.toJSONString(result));
    }

    *//**
     * 注册
     *//*
    @Test
    public void login() {
        CustLoginRequest request = new CustLoginRequest();
        request.setMobile("***********");
        request.setBizCode(UserBizCodeEnum.INSURANCE);
        request.setPassword("191811");
        request.setChannel("INSURANCE");
        CommonResult<UserInfoVo> result = facade.login(null, null, null, null, null, null, request);
//        System.out.println(JSON.toJSONString(result));
    }

    *//**
     * 实名认证
     *//*
    @Test
    public void certification() {
        CustCertificationRequest request = new CustCertificationRequest();
        request.setCustName("张娜义");
        request.setIdNo("362201199009191811");
        request.setIdType("01");
//        request.setUserNo("U2019051400000016815");
        CommonResult<CustCertifyVo> result = facade.certification(request);
//        System.out.println(JSON.toJSONString(result));
    }

    @Test
    public void queryCustInfoByBankCardNo() {
        CustInfoQueryByAcctNoReq request = new CustInfoQueryByAcctNoReq();
        request.setAcctNo("362201199009191811");
        request.setAcctType("03");
        Result<CustInfoVo> result = facade.queryCustInfoByBankCardNo(request);
//        System.out.println(JSON.toJSONString(result));
    }

//    @Test
//    public void test() {
//        CommonResult<List<CustomerPolicyVO>> result = customerFacade.getCustomerPolicyList(Arrays.asList("362201199009191811", "360902201609181812"));
//        System.out.println();
//    }

    @Test
    public void sendCappSMSCode() {
        CappSendSmsRequest request = new CappSendSmsRequest();
        request.setMobile("***********");
        request.setType(CappSMSEnum.REGIST);
        CommonResult result = facade.sendCappSMSCode(request);
//        System.out.println(JSON.toJSONString(result));
    }

    @Test
    public void checkCappSMSCode() {
        CappCheckSmsRequest request = new CappCheckSmsRequest();
        request.setMobile("***********");
        request.setType(CappSMSEnum.REGIST);
        request.setCode("947525");
        CommonResult result = facade.checkCappSMSCode(request);
//        System.out.println(JSON.toJSONString(result));
    }*/
}
