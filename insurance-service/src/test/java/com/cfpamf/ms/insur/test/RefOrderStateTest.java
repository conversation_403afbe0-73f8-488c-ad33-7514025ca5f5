package com.cfpamf.ms.insur.test;

import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.URL;
import java.net.URLConnection;
import java.util.*;

/**
 * CIC20031901243455\n" +
 * "CIC20031901243694\n" +
 * "CIC20031901243726\n" +
 * "CIC20031901243729\n" +
 * "CIC20031901243748\n" +
 * "CIC20031901243755\n" +
 * "CIC20031901243771\n" +
 * "CIC20031901243868\n" +
 * "CIC20031901243875\n" +
 * "CIC20031901243954\n" +
 * "CIC20031901244068\n" +
 * "CIC20031901244087\n" +
 * "CIC20031901244188\n" +
 * "CIC20031901244212\n" +
 * "CIC20031901244244\n" +
 * "CIC20031901
 * （dms 导出需要刷洗你的订单号 然后执行脚本）
 * 根据订单号 刷新订单状态
 * <AUTHOR> 2020/4/3 13:58
 */
public class RefOrderStateTest {

    public static void main(String[] args) throws IOException {

        List<String> allNotPay =
                Arrays.asList(("YGCX22041403481405J\n" +
                        "YGCX22041403481421J\n" +
                        "YGCX22041403481432J\n" +
                        "YGCX22041403481446J\n" +
                        "YGCX22041403481450J\n" +
                        "YGCX22041403481458J\n" +
                        "YGCX22041403481463J\n" +
                        "YGCX22041403481467J\n" +
                        "YGCX22041403481468J\n" +
                        "YGCX22041403481471J\n" +
                        "YGCX22041403481473J\n" +
                        "YGCX22041403481474J\n" +
                        "YGCX22041403481483J\n" +
                        "YGCX22041403481485J\n" +
                        "YGCX22041403481495J\n" +
                        "YGCX22041403481498J\n" +
                        "YGCX22041403481510J\n" +
                        "YGCX22041403481514J\n" +
                        "YGCX22041403481520J\n" +
                        "YGCX22041403481532J\n" +
                        "YGCX22041403481542J").split("\n"));
//        List<String> allNotPay = IOUtils.readLines(new FileInputStream("/Users/<USER>/doc/zhnx/保险/process/中华联合/081需要刷新的订单-运维网络升级保单乱码.csv"));

//        String[] split = str.split("\n");
        RestTemplate restTemplate = new RestTemplate();

        HttpHeaders requestHeaders = new HttpHeaders();
        requestHeaders.set("Accept", "application/json, text/plain, */*");
//        requestHeaders.set("authorization", "");
        requestHeaders.set("authorization", "eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiI2NTM1IiwiZW1wbG95ZWVJZCI6IjY2ODEiLCJoclVzZXJJZCI6IjEyMTQxNjk4NiIsImFjY291bnQiOiIxNTU3NTE0MjM3NyIsInVzZXJOYW1lIjoi6YOR6Z2WIiwiam9iTnVtYmVyIjoiWkhOWDA5NzYwIiwibWFzdGVySm9iTnVtYmVyIjoiWkhOWDA5NzYwIiwib3JnSWQiOiI5NjEiLCJock9yZ0lkIjoiOTAwMTA1MjIyIiwiaHJPcmdDb2RlIjoiQlhEU1oiLCJock9yZ05hbWUiOiLkv53pmanmioDmnK_nu4QiLCJhcmVhT3JnQ29kZSI6IiIsImhyT3JnVHJlZVBhdGgiOiI5MDAxMDUxNTMvMjgyNzE3LzM2MDMxNy80NDc5NDEvODA3MzUvOTAwMTA1MjIyIiwidXNlclR5cGUiOiIzIiwicmFuZG9tQ29kZSI6IjQwRkFCMTM3LTg4RDgtNDhEOS1BMUZCLTVBQkMwQTQzNkJDNSIsInVzZVNzbyI6InRydWUiLCJzeXN0ZW1JZCI6IjMiLCJjaGFubmVsIjoiIiwiY3JlYXRlVGltZSI6IjE2NDk5MjY2NDk0MzgiLCJncmF5IjowfQ.30ztpaU0Q2sffOaEw7umwGor5mWzmmb8sKwRHXBDwkPdSGENAghHCDCuViOXInWcBYaamGEzdpGm3whoje-bug");
        requestHeaders.set("User-Agent", "" +
                "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_3) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36");
        final String host = "https://bms.cfpamf.org.cn/api/insurance/micro/back/order/order/";

        List<String> lis = Collections.synchronizedList(new ArrayList<>());
        allNotPay.parallelStream()
                .filter(StringUtils::isNotBlank).parallel()
                .forEach(s -> {
//                    String params = "userId=CNBJ0409&password=&fhOrderId="+s+"&authorization="+authorization;
//                    String ret = sendGet(host,s,params);
//                    if(!Objects.isNull(ret)){
//                        lis.add(ret);
//                    }
//                    try {
//                        Thread.sleep(2000L);
//                    } catch (InterruptedException e) {
//                        e.printStackTrace();
//                    }
                    HttpEntity<Object> objectHttpEntity = new HttpEntity<>(requestHeaders);
                    ResponseEntity<String> stringResponseEntity = restTemplate.postForEntity(host + s, objectHttpEntity, String.class);
                    String body = stringResponseEntity.getBody();
                    System.err.println("body=" + body);

                    try {
                        Thread.sleep(1000L);
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                });

        System.err.println(lis);
    }

    public static String sendGet(String url, String s,String param){
        String result = "";
        String urlName = url + "?" + param;
        try{
            URL realUrl = new URL(urlName);
            //打开和URL之间的连接
            URLConnection conn = realUrl.openConnection();
            //设置通用的请求属性
            conn.setRequestProperty("accept", "*/*");
            conn.setRequestProperty("connection", "Keep-Alive");
            conn.setRequestProperty("user-agent",
                    "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1;SV1)");
            //建立实际的连接
            conn.connect();
            //获取所有的响应头字段
            Map<String,List<String>> map = conn.getHeaderFields();

            // 定义 BufferedReader输入流来读取URL的响应
            BufferedReader in = new BufferedReader(new InputStreamReader(conn.getInputStream()));
            String line;
            while ((line = in.readLine()) != null) {
                result += line;
            }
        } catch (Exception e) {

            System.out.println("发送GET请求出现异常" + e);
            e.printStackTrace();
            return s;
        }
        return null;


    }



}
