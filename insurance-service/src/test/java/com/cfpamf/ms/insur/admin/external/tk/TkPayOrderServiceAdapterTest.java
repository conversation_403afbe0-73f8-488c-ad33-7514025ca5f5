package com.cfpamf.ms.insur.admin.external.tk;

import com.cfpamf.ms.insur.admin.dao.safes.SmOrderMapper;
import com.cfpamf.ms.insur.admin.external.OrderSubmitRequest;
import com.cfpamf.ms.insur.admin.external.tk.api.TkApiService;
import com.cfpamf.ms.insur.admin.pojo.vo.SmPlanVO;
import com.cfpamf.ms.insur.admin.service.BaseTest;
import com.cfpamf.ms.insur.admin.service.OrderNoGenerator;
import com.cfpamf.ms.insur.admin.service.aicheck.AkFamilyQuestionnaireService;
import com.cfpamf.ms.insur.base.util.HttpRequestUtil;
import com.github.jsonzou.jmockdata.JMockData;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

@RunWith(PowerMockRunner.class)
@PrepareForTest({HttpRequestUtil.class})
public class TkPayOrderServiceAdapterTest extends BaseTest {
    @InjectMocks
    TkPayOrderServiceAdapterImpl tkPayOrderServiceAdapter;
    @org.mockito.Mock
    com.cfpamf.ms.insur.admin.dao.safes.order.SmOrderExtendTkMapper extendTkMapper;
    @org.mockito.Mock
    com.cfpamf.ms.insur.admin.service.SmProductService productService;
    @org.mockito.Mock
    com.fasterxml.jackson.databind.ObjectMapper jsonMapper;

    @Mock
    SmOrderMapper orderMapper;
    @Mock
    TkApiService apiService;

    @Mock
    OrderNoGenerator orderNoGenerator;
    @Mock
    AkFamilyQuestionnaireService questionnaireService;
    @Test
    public void prePayChannelOrder() {
        SmPlanVO mock = JMockData.mock(SmPlanVO.class);
        mock.setFhProductId("1-1-1");
        Mockito.when(productService.getPlanById(Mockito.anyInt()))
                .thenReturn(mock);
        tkPayOrderServiceAdapter.prePayChannelOrder(JMockData.mock(com.cfpamf.ms.insur.admin.external.OrderPrePayRequest.class));
    }

    @Test
    public void submitChannelOrder() {
        OrderSubmitRequest mock = JMockData.mock(OrderSubmitRequest.class, con());
        mock.setQuestionnaireId("1");
        tkPayOrderServiceAdapter.submitChannelOrder(mock);
    }

    @Test
    public void queryChannelOrderInfo() {
        try {
            tkPayOrderServiceAdapter.queryChannelOrderInfo(JMockData.mock(com.cfpamf.ms.insur.admin.external.OrderQueryRequest.class));
        } catch (Exception e) {

        }
    }

    @Test
    public void parseNotify() {
        try {
            tkPayOrderServiceAdapter.parseNotify(",rnvUe,");
        } catch (Exception e) {

        }
    }

    @Test
    public void properties() {
        try {
            tkPayOrderServiceAdapter.properties();
        } catch (Exception e) {

        }
    }
}

