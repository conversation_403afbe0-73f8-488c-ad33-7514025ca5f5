package com.cfpamf.ms.insur.admin.service.product;

import com.cfpamf.ms.insur.admin.dao.safes.SmCancelMapper;
import com.cfpamf.ms.insur.admin.dao.safes.SmOrderMapper;
import com.cfpamf.ms.insur.admin.dao.safes.SmRefundBankDepositMapper;
import com.cfpamf.ms.insur.admin.dao.safes.order.SmCancelRefundMapper;
import com.cfpamf.ms.insur.admin.dao.safes.order.SmOrderPaymentMapper;
import com.cfpamf.ms.insur.admin.dao.safes.order.SmOrderPolicyMapper;
import com.cfpamf.ms.insur.admin.dao.safes.sys.SystemNotifyMapper;
import com.cfpamf.ms.insur.admin.external.zhongan.ZaOrderServiceAdapter;
import com.cfpamf.ms.insur.admin.external.zhongan.api.ZaApiService;
import com.cfpamf.ms.insur.admin.pojo.vo.SmBaseOrderVO;
import com.cfpamf.ms.insur.admin.service.SmCancelRefundService;
import com.cfpamf.ms.insur.admin.service.SmCancelService;
import com.cfpamf.ms.insur.admin.service.SmProductService;
import com.cfpamf.ms.insur.admin.service.order.ZaOrderService;
import com.cfpamf.ms.insur.admin.service.sys.SysNotifyService;
import com.cfpamf.ms.insur.base.controller.ApplicationTest;
import com.cfpamf.ms.insur.base.event.EventBusEngine;
import com.cfpamf.ms.insur.base.service.InsurPayService;
import com.cfpamf.ms.insur.base.util.HttpRequestUtil;
import com.cfpamf.ms.insur.base.util.LocalDateUtil;
import com.cfpamf.ms.insur.weixin.constant.za.EnumPayWay;
import com.cfpamf.ms.insur.weixin.pojo.vo.apply.zhongan.callback.ZaPayNotify;
import com.cfpamf.ms.insur.weixin.pojo.vo.apply.zhongan.item.ChargeInfo;
import com.cfpamf.ms.insur.weixin.pojo.vo.apply.zhongan.request.ZaGroupInsureReq;
import com.cfpamf.ms.insur.weixin.pojo.vo.apply.zhongan.request.ZaQuoteResp;
import com.github.jsonzou.jmockdata.JMockData;
import com.github.jsonzou.jmockdata.TypeReference;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.Date;

@RunWith(PowerMockRunner.class)
@PrepareForTest({HttpRequestUtil.class})
public class OrderServiceTest extends ApplicationTest {


    @InjectMocks
    private SmProductService productService;

    @InjectMocks
    SysNotifyService notifyService;

    @Mock
    SystemNotifyMapper systemNotifyMapper;

    @InjectMocks
    SmCancelRefundService refundService;

    @Mock
    SmOrderMapper orderMapper;

    @InjectMocks
    InsurPayService payService;

    @Mock
    SmCancelRefundMapper refundMapper;

    @Mock
    SmCancelMapper cancelMapper;

    @InjectMocks
    SmCancelService cancelService;

    @InjectMocks
    ZaOrderService zaOrderService;

    @Mock
    SmRefundBankDepositMapper bankDepositMapper;

    @Mock
    SmOrderPaymentMapper orderPaymentMapper;

    @Mock
    SmOrderPolicyMapper smOrderPolicyMapper;

    @Mock
    EventBusEngine eventBusEngine;

    @InjectMocks
    ZaOrderServiceAdapter adapter;

    @Mock
    ZaApiService apiService;

    @Test
    public void redoNotify(){
        String orderId = "ZA21090902641182P";
        SmBaseOrderVO order = orderMapper.getBaseOrderInfoByOrderId(orderId);
        String channel = order.getChannel();
        String msg = orderMapper.getNotifyMsgFromZA(orderId);
        if(StringUtils.isBlank(msg)){
            return;
        }

        TypeReference<ZaPayNotify> tr = new TypeReference<ZaPayNotify>() {
        };
        ZaPayNotify notify = JMockData.mock(tr);
        String appNo = orderMapper.getAppNo(orderId);
        ZaGroupInsureReq req = new ZaGroupInsureReq();
        req.setChannelCode("XIAOJING");
        req.setChannelOrderNo(orderId);
        req.setProposalNo(appNo);
        ChargeInfo payment = new ChargeInfo();
        payment.setPayMoney(notify.getAmt());
        payment.setPayTradeNo(notify.getZa_order_no());
        payment.setPayWay(EnumPayWay.getCode(notify.getPay_channel()));
        Date payTime = new Date();
        payment.setPayTime(LocalDateUtil.format(payTime));
        req.setPayment(payment);
        ZaQuoteResp resp = adapter.groupInsue(req);
        int policyState = -1;
        if(resp.isSuccess()){
            zaOrderService.afterApply(payTime,resp.getChannelOrderNo(),resp.getPolicyNo(),null);
            policyState=10;
        }
        orderMapper.updateNotifyState(orderId,policyState,null);
    }

}
