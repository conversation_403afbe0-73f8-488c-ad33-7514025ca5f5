package com.cfpamf.ms.insur.admin.external.cic;

import com.cfpamf.common.ms.util.DateUtil;
import com.cfpamf.ms.insur.admin.external.OrderQueryRequest;
import com.cfpamf.ms.insur.admin.external.OrderQueryResponse;
import org.junit.FixMethodOrder;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.MethodSorters;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

/*@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("dev")
@FixMethodOrder(MethodSorters.NAME_ASCENDING)*/
public class CicOrderServiceImplTest {

    /*@Autowired
    private CicOrderServiceAdapter service;

    @Test
    public void submitChannelOrder() {
    }

    @Test
    public void getChannelOrderByOrderId() {
        try {
            OrderQueryRequest dto = new OrderQueryRequest();
            dto.setSubmitTime(DateUtil.parseDate("2019-01-17 09:09:06", DateUtil.CN_LONG_FORMAT));
            dto.setOrderId("190117090906NBTJ");
            dto.setPayId("ZHNX201901170000004556");
            OrderQueryResponse resp = service.queryChannelOrderInfo(dto);
//            System.out.println();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void getChannelOrderByOrderId2() {
        try {
            OrderQueryRequest dto = new OrderQueryRequest();
            dto.setSubmitTime(DateUtil.parseDate("2019-01-18 13:56:17", DateUtil.CN_LONG_FORMAT));
            dto.setOrderId("CIC190118LXGOFEV");
            dto.setPayId("ZHNX201901180000004585");
            OrderQueryResponse resp = service.queryChannelOrderInfo(dto);
//            System.out.println();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void getOrderPayUrl() {
    }

    @Test
    public void getOrderPayedCallbackResp() {
    }*/
}