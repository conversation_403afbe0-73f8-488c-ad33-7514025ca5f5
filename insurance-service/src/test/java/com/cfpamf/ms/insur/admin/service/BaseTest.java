package com.cfpamf.ms.insur.admin.service;

import com.alibaba.fastjson.util.ParameterizedTypeImpl;
import com.cfpamf.common.ms.result.ErrorContext;
import com.cfpamf.common.ms.util.DateUtil;
import com.cfpamf.ms.bms.facade.util.JwtHelper;
import com.cfpamf.ms.bms.facade.vo.JwtUserInfo;
import com.cfpamf.ms.insur.base.dao.MyMappler;
import com.cfpamf.ms.insur.base.util.HttpRequestUtil;
import com.cfpamf.ms.insur.base.util.SpringFactoryUtil;
import com.github.jsonzou.jmockdata.*;
import org.junit.Before;
import org.mockito.MockSettings;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.springframework.context.ApplicationContext;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import static java.lang.reflect.Modifier.isFinal;
import static java.lang.reflect.Modifier.isStatic;

/**
 * <AUTHOR> 2019/12/24 17:44
 */
public class BaseTest {

    protected void mockJwt(JwtHelper jwtHelper) {
        Mockito.when(jwtHelper.getCurrentUserId()).thenReturn(1);
        JwtUserInfo build = JwtUserInfo.builder().employeeId(1).userId(1).hrOrgId(1).build();
        Mockito.when(jwtHelper.getJwtUserInfo()).thenReturn(build);
    }

    protected ApplicationContext mock;

    @Before
    public void setUp() throws Exception {

        mock = Mockito.mock(ApplicationContext.class);
        Mockito.when(mock.getBean((Class<Object>) Mockito.any()))
                .thenAnswer(a -> Mockito.mock(a.getArgument(0)));

//        Mockito.when(mock.getBean(Mockito.anyString(), (Class<Object>) Mockito.any()))
//                .thenAnswer(a -> Mockito.mock(a.getArgumentAt(1, Class.class)));
        new SpringFactoryUtil().setApplicationContext(mock);

        JwtUserInfo zhnxtest = JwtUserInfo.builder()
                .userId(1).jobNumber("ZHNXTEST")
                .build();
        PowerMockito.mockStatic(HttpRequestUtil.class);
        Mockito.when(HttpRequestUtil.getUser())
                .thenReturn(zhnxtest);
        Mockito.when(HttpRequestUtil.getUserId())
                .thenReturn("admin");
        Mockito.when(HttpRequestUtil.getUserOrThrowExp())
                .thenReturn(zhnxtest);
        Mockito.when(HttpRequestUtil.getWxOpenId())
                .thenReturn("oK6-1wF85NO0cFxRkFisD1avtDEI");
        Mockito.when(HttpRequestUtil.getToken())
                .thenReturn("eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiI2OTQiLCJlbXBsb3llZUlkIjoiNTE4MyIsImhyVXNlcklkIjoiMTE4ODA5OTY5IiwiYWNjb3VudCI6IjE1MTk3MDE4OTc2IiwidXNlck5hbWUiOiLkvZXnu6rmhI8iLCJqb2JOdW1iZXIiOiJaSE5YMDgwMTgiLCJvcmdJZCI6IjI0MyIsImhyT3JnSWQiOiIzMDkzMTciLCJock9yZ0NvZGUiOiJITlBKIiwiaHJPcmdOYW1lIjoi5bmz5rGfIiwiaHJPcmdUcmVlUGF0aCI6IjkwMDEwNTE1My8yODI3MzIvMTQ3MTYxMS8yODQ1MTcvMTQ0NDU3NS8zMDkzMTciLCJ1c2VyVHlwZSI6IjIiLCJyYW5kb21Db2RlIjoiMjhCMkMzODYtNUVGQi00MzY2LUEzRjEtQzJGODY5NEVGMUVDIn0.UNINiduyZvZZReqoOK073RwwSfYsGDIssZ-NWhU9bV9my4mFa8wCccXlwGKkP_VRe1Lz51MfE5UsfFNIdTkeDA");
    }

    protected void resetMapper() {
        try {
            for (Field declaredField : this.getClass().getDeclaredFields()) {

                int modifiers = declaredField.getModifiers();
                if (!(isFinal(modifiers) || isStatic(modifiers))) {
                    if (!declaredField.isAccessible()) {
                        declaredField.setAccessible(true);
                    }

                    if (MyMappler.class.isAssignableFrom(declaredField.getType())) {
                        if (!declaredField.isAccessible()) {
                            declaredField.setAccessible(true);
                        }
                        declaredField.set(this, initMapper(declaredField.getType()));
                    }
                }
            }
        } catch (Exception e) {

        }

    }

    protected MockConfig con() {
        return defaultMockConfig();
    }

    public static MockConfig defaultMockConfig() {
        MockConfig mockConfig = new MockConfig();

        mockConfig.globalConfig()
                .registerMocker(new Mocker<LocalDateTime>() {
                                    @Override
                                    public LocalDateTime mock(DataConfig mockConfig) {
                                        return LocalDateTime.now();
                                    }
                                },
                        LocalDateTime.class);

        mockConfig.globalConfig()
                .registerMocker(new Mocker<LocalDate>() {
                                    @Override
                                    public LocalDate mock(DataConfig mockConfig) {
                                        return LocalDate.now();
                                    }
                                },
                        LocalDate.class);
        mockConfig.globalConfig()
                .registerMocker(new Mocker<JwtUserInfo>() {
                                    @Override
                                    public JwtUserInfo mock(DataConfig mockConfig) {
                                        return JwtUserInfo.builder().employeeId(1).userId(1).hrOrgId(1).build();
                                    }
                                },
                        JwtUserInfo.class);
        mockConfig.globalConfig()
                .registerMocker(new Mocker<ErrorContext>() {
                                    @Override
                                    public ErrorContext mock(DataConfig mockConfig) {
                                        return new ErrorContext();
                                    }
                                },
                        ErrorContext.class);
        return mockConfig;
    }

    protected <T> T initMapper(Class<T> clz) {
        MockSettings mockSettings = Mockito.withSettings();
        mockSettings.defaultAnswer(invocation -> {

            ParameterizedType type = (ParameterizedType) clz.getGenericInterfaces()[0];
            Method method = invocation.getMethod();
            Class<?> returnType = method.getReturnType();
            if (returnType != void.class && returnType != Void.class) {

                Type genericReturnType = method.getGenericReturnType();

                Map<String, Type> val = new HashMap<>(1);
                val.put("val", genericReturnType);
                Object mock = null;
                if (Objects.equals(returnType.toString(), genericReturnType.toString())) {
                    mock = JMockData.mock(returnType, con());
                } else {
                    String s = genericReturnType.toString();
                    if (s.contains("<T>")) {
                        if (genericReturnType instanceof ParameterizedType) {
                            ParameterizedType tmp = (ParameterizedType) genericReturnType;
                            genericReturnType = new ParameterizedTypeImpl(
                                    type.getActualTypeArguments(), tmp.getOwnerType(), tmp.getRawType());
                            val.put("val", genericReturnType);
                        }

                    } else if (Objects.equals("T", s)) {
                        val.put("val", type.getActualTypeArguments()[0]);
                    }
                    mock = JMockData.mock(new TypeReference<Object>() {
                        @Override
                        public Type getType() {
                            return val.get("val");
                        }

                    }, con());
                }
                return mock;
            }
            return null;
        });
        return Mockito.mock(clz, mockSettings);
    }
}
