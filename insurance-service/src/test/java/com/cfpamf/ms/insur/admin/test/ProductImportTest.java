//package com.cfpamf.ms.insur.admin.test;
//
//import org.apache.poi.ss.usermodel.Workbook;
//import org.apache.poi.xssf.usermodel.*;
//
//import java.io.File;
//import java.io.FileInputStream;
//import java.io.IOException;
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;
//
///**
// * <AUTHOR>
// **/
//public class ProductImportTest {
//
//    public static void main(String[] args) throws IOException {
//        Workbook workbook = new XSSFWorkbook(new FileInputStream(new File("C:\\Users\\<USER>\\Desktop\\产品模板.xlsx")));
//        XSSFSheet sheet = (XSSFSheet) workbook.getSheet("产品信息");
//        Map<String, XSSFPictureData> map = new HashMap<String, XSSFPictureData>();
//        List<XSSFShape> list = sheet.getDrawingPatriarch().getShapes();
//        for (XSSFShape shape : list) {
//            if (shape instanceof XSSFPicture) {
//                XSSFPicture picture = (XSSFPicture) shape;
//                XSSFClientAnchor cAnchor = picture.getClientAnchor();
//                XSSFPictureData pdata = picture.getPictureData();
//                String key = cAnchor.getRow1() + "-" + cAnchor.getCol1(); // 行号-列号
//                pdata.getData();
//                map.put(key, pdata);
//            }
//        }
//        System.out.println();
//    }
//}
