package com.cfpamf.ms.insur.admin.service;

import com.cfpamf.ms.bms.facade.vo.UserDetailVO;
import com.cfpamf.ms.bms.facade.vo.UserPartTimerVO;
import com.cfpamf.ms.insur.admin.dao.safes.HrUserHandoverMapper;
import com.cfpamf.ms.insur.admin.dao.safes.SystemMsgCenterMapper;
import com.cfpamf.ms.insur.admin.dao.safes.SystemMsgContentMapper;
import com.cfpamf.ms.insur.admin.pojo.po.HrUserHandover;
import com.cfpamf.ms.insur.admin.pojo.po.SystemMsgCenter;
import com.cfpamf.ms.insur.admin.pojo.po.SystemMsgContent;
import com.cfpamf.ms.insur.admin.pojo.po.UserPost;
import com.cfpamf.ms.insur.admin.pojo.vo.AuthUserVO;
import com.cfpamf.ms.insur.base.service.BmsService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@RunWith(SpringJUnit4ClassRunner.class)
public class HrUserHandoverManagerServiceTest {

    @Mock
    private HrUserHandoverMapper mockHandoverMapper;
    @Mock
    private SystemMsgCenterMapper mockSystemMsgCenterMapper;
    @Mock
    private SystemMsgContentMapper mockSystemMsgContentMapper;
    @Mock
    private UserService mockUserService;
    @Mock
    private CustomerService mockCustomerService;
    @Mock
    private BmsService mockBmsService;
    @Mock
    private UserPostService mockUserPostService;

    @InjectMocks
    private HrUserHandoverManagerService hrUserHandoverManagerServiceUnderTest;

    @Test
    public void testListUnDoRecord() {
        // Setup
        final HrUserHandover hrUserHandover = new HrUserHandover();
        hrUserHandover.setId(0);
        hrUserHandover.setDimEmployeeCode("dimEmployeeCode");
        hrUserHandover.setDimIdNumber("dimIdNumber");
        hrUserHandover.setDimEmployeeName("dimEmployeeName");
        hrUserHandover.setDimJobPositionCode("dimJobPositionCode");
        hrUserHandover.setDimDeptCode("dimDeptCode");
        hrUserHandover.setDimServiceType(0);
        hrUserHandover.setRecEmployeeCode("CNBJ0621");
        hrUserHandover.setRecEmployeeName("recEmployeeName");
        hrUserHandover.setRecDeptCode("KHFWZ");
        hrUserHandover.setStatus(0);
        hrUserHandover.setResultCode(0);
        hrUserHandover.setResultMsg("系统原因");
        hrUserHandover.setDimCurrJobCode("dimCurrJobCode");
        hrUserHandover.setRecCurrJobCode("dimCurrJobCode");
        final List<HrUserHandover> expectedResult = Arrays.asList(hrUserHandover);

        // Configure HrUserHandoverMapper.select(...).
        final HrUserHandover hrUserHandover1 = new HrUserHandover();
        hrUserHandover1.setId(0);
        hrUserHandover1.setDimEmployeeCode("dimEmployeeCode");
        hrUserHandover1.setDimIdNumber("dimIdNumber");
        hrUserHandover1.setDimEmployeeName("dimEmployeeName");
        hrUserHandover1.setDimJobPositionCode("dimJobPositionCode");
        hrUserHandover1.setDimDeptCode("dimDeptCode");
        hrUserHandover1.setDimServiceType(0);
        hrUserHandover1.setRecEmployeeCode("CNBJ0621");
        hrUserHandover1.setRecEmployeeName("recEmployeeName");
        hrUserHandover1.setRecDeptCode("KHFWZ");
        hrUserHandover1.setStatus(0);
        hrUserHandover1.setResultCode(0);
        hrUserHandover1.setResultMsg("系统原因");
        hrUserHandover1.setDimCurrJobCode("dimCurrJobCode");
        hrUserHandover1.setRecCurrJobCode("dimCurrJobCode");
        final List<HrUserHandover> hrUserHandovers = Arrays.asList(hrUserHandover1);
        when(mockHandoverMapper.select(Mockito.any())).thenReturn(hrUserHandovers);

        // Run the test
        final List<HrUserHandover> result = hrUserHandoverManagerServiceUnderTest.listUnDoRecord();

        // Verify the results
        assertThat(result).isNotEmpty();
    }

    @Test
    public void testListUnDoRecord_HrUserHandoverMapperReturnsNoItems() {
        // Setup
        when(mockHandoverMapper.select(Mockito.any())).thenReturn(Collections.emptyList());

        // Run the test
        final List<HrUserHandover> result = hrUserHandoverManagerServiceUnderTest.listUnDoRecord();

        // Verify the results
        assertThat(result).isEmpty();
    }

    @Test
    public void testDoOperator() {
        // Setup
        final HrUserHandover handover = new HrUserHandover();
        handover.setId(0);
        handover.setDimEmployeeCode("dimEmployeeCode");
        handover.setDimIdNumber("dimIdNumber");
        handover.setDimEmployeeName("dimEmployeeName");
        handover.setDimJobPositionCode("dimJobPositionCode");
        handover.setDimDeptCode("dimDeptCode");
        handover.setDimServiceType(0);
        handover.setRecEmployeeCode("CNBJ0621");
        handover.setRecEmployeeName("recEmployeeName");
        handover.setRecDeptCode("KHFWZ");
        handover.setStatus(0);
        handover.setResultCode(0);
        handover.setResultMsg("系统原因");
        handover.setDimCurrJobCode("dimCurrJobCode");
        handover.setRecCurrJobCode("dimCurrJobCode");

        // Configure UserService.getAuthUserByUserId(...).
        final AuthUserVO authUserVO = new AuthUserVO();
        authUserVO.setId(0);
        authUserVO.setRegionName("regionName");
        authUserVO.setRegionCode("regionCode");
        authUserVO.setOrgCode("orgCode");
        authUserVO.setOrganizationName("organizationName");
        authUserVO.setOrganizationFullName("organizationFullName");
        authUserVO.setUserId("userId");
        authUserVO.setAgentId(0);
        authUserVO.setAgentTopUserId("agentTopUserId");
        authUserVO.setUserName("userName");
        authUserVO.setUserMobile("userMobile");
        authUserVO.setWxNickName("wxNickName");
        authUserVO.setPostName("postName");
        authUserVO.setStatus("status");
        authUserVO.setJobCode("dimCurrJobCode");

        // Configure UserPostService.listUserPostByJobNumber(...).
        final UserPost userPost = new UserPost();
        userPost.setId(0);
        userPost.setJobCode("jobCode");
        userPost.setMainJobNumber("mainJobNumber");
        userPost.setJobNumber("jobNumber");
        userPost.setHrUserId(0);
        userPost.setBmsUserId(0);
        userPost.setRegionCode("regionCode");
        userPost.setRegionName("regionName");
        userPost.setHrRegionId("hrRegionId");
        userPost.setHrOrgId("hrOrgId");
        userPost.setOrgCode("orgCode");
        userPost.setServiceType(0);
        userPost.setEmployeeStatus(0);
        userPost.setServiceStatus(0);
        userPost.setUserAdminId(0);
        final List<UserPost> userPosts = Arrays.asList(userPost);

        when(mockSystemMsgContentMapper.insertSelective(Mockito.any())).thenReturn(0);

        // Configure BmsService.getUserDetailVOByHrUserId(...).
        final UserDetailVO userDetailVO = new UserDetailVO();
        userDetailVO.setJobNumber("CNBJ0621");
        userDetailVO.setPostId(0);
        userDetailVO.setIsHeadOrg(false);
        final UserPartTimerVO userPartTimerVO = new UserPartTimerVO();
        userPartTimerVO.setRowNo(0);
        userPartTimerVO.setUserId(0);
        userPartTimerVO.setEmployeeId(0);
        userPartTimerVO.setHrUserId(0);
        userPartTimerVO.setUserAdminId("userAdminId");
        userPartTimerVO.setUserAdminName("userAdminName");
        userPartTimerVO.setUserMasterId("userMasterId");
        userPartTimerVO.setUserMasterName("userMasterName");
        userPartTimerVO.setPostId(0);
        userPartTimerVO.setPostName("postName");
        userPartTimerVO.setHrPostId(0);
        userDetailVO.setUserPartTimerList(Arrays.asList(userPartTimerVO));

        when(mockSystemMsgCenterMapper.insertList(Mockito.any())).thenReturn(0);
        when(mockHandoverMapper.updateByPrimaryKeySelective(Mockito.any())).thenReturn(0);

        // Run the test
        hrUserHandoverManagerServiceUnderTest.doOperator(handover);

    }

    @Test
    public void testDoOperator_UserServiceReturnsNull() {
        // Setup
        final HrUserHandover handover = new HrUserHandover();
        handover.setId(0);
        handover.setDimEmployeeCode("dimEmployeeCode");
        handover.setDimIdNumber("dimIdNumber");
        handover.setDimEmployeeName("dimEmployeeName");
        handover.setDimJobPositionCode("dimJobPositionCode");
        handover.setDimDeptCode("dimDeptCode");
        handover.setDimServiceType(0);
        handover.setRecEmployeeCode("CNBJ0621");
        handover.setRecEmployeeName("recEmployeeName");
        handover.setRecDeptCode("KHFWZ");
        handover.setStatus(0);
        handover.setResultCode(0);
        handover.setResultMsg("系统原因");
        handover.setDimCurrJobCode("dimCurrJobCode");
        handover.setRecCurrJobCode("dimCurrJobCode");

        when(mockUserService.getAuthUserByUserId("dimEmployeeCode")).thenReturn(null);
        when(mockSystemMsgContentMapper.insertSelective(new SystemMsgContent())).thenReturn(0);

        // Configure BmsService.getUserDetailVOByHrUserId(...).
        final UserDetailVO userDetailVO = new UserDetailVO();
        userDetailVO.setJobNumber("CNBJ0621");
        userDetailVO.setPostId(0);
        userDetailVO.setIsHeadOrg(false);
        final UserPartTimerVO userPartTimerVO = new UserPartTimerVO();
        userPartTimerVO.setRowNo(0);
        userPartTimerVO.setUserId(0);
        userPartTimerVO.setEmployeeId(0);
        userPartTimerVO.setHrUserId(0);
        userPartTimerVO.setUserAdminId("userAdminId");
        userPartTimerVO.setUserAdminName("userAdminName");
        userPartTimerVO.setUserMasterId("userMasterId");
        userPartTimerVO.setUserMasterName("userMasterName");
        userPartTimerVO.setPostId(0);
        userPartTimerVO.setPostName("postName");
        userPartTimerVO.setHrPostId(0);
        userDetailVO.setUserPartTimerList(Arrays.asList(userPartTimerVO));
        when(mockBmsService.getUserDetailVOByHrUserId(0)).thenReturn(userDetailVO);

        when(mockSystemMsgCenterMapper.insertList(Arrays.asList(new SystemMsgCenter()))).thenReturn(0);
        when(mockHandoverMapper.updateByPrimaryKeySelective(new HrUserHandover())).thenReturn(0);

        // Run the test
        hrUserHandoverManagerServiceUnderTest.doOperator(handover);

    }

    @Test
    public void testDoOperator_UserPostServiceReturnsNoItems() {
        // Setup
        final HrUserHandover handover = new HrUserHandover();
        handover.setId(0);
        handover.setDimEmployeeCode("dimEmployeeCode");
        handover.setDimIdNumber("dimIdNumber");
        handover.setDimEmployeeName("dimEmployeeName");
        handover.setDimJobPositionCode("dimJobPositionCode");
        handover.setDimDeptCode("dimDeptCode");
        handover.setDimServiceType(0);
        handover.setRecEmployeeCode("CNBJ0621");
        handover.setRecEmployeeName("recEmployeeName");
        handover.setRecDeptCode("KHFWZ");
        handover.setStatus(0);
        handover.setResultCode(0);
        handover.setResultMsg("系统原因");
        handover.setDimCurrJobCode("dimCurrJobCode");
        handover.setRecCurrJobCode("dimCurrJobCode");

        // Configure UserService.getAuthUserByUserId(...).
        final AuthUserVO authUserVO = new AuthUserVO();
        authUserVO.setId(0);
        authUserVO.setRegionName("regionName");
        authUserVO.setRegionCode("regionCode");
        authUserVO.setOrgCode("orgCode");
        authUserVO.setOrganizationName("organizationName");
        authUserVO.setOrganizationFullName("organizationFullName");
        authUserVO.setUserId("userId");
        authUserVO.setAgentId(0);
        authUserVO.setAgentTopUserId("agentTopUserId");
        authUserVO.setUserName("userName");
        authUserVO.setUserMobile("userMobile");
        authUserVO.setWxNickName("wxNickName");
        authUserVO.setPostName("postName");
        authUserVO.setStatus("status");
        authUserVO.setJobCode("dimCurrJobCode");
        when(mockUserService.getAuthUserByUserId("dimEmployeeCode")).thenReturn(authUserVO);

        when(mockUserPostService.listUserPostByJobNumber("dimEmployeeCode")).thenReturn(Collections.emptyList());
        when(mockSystemMsgContentMapper.insertSelective(new SystemMsgContent())).thenReturn(0);

        // Configure BmsService.getUserDetailVOByHrUserId(...).
        final UserDetailVO userDetailVO = new UserDetailVO();
        userDetailVO.setJobNumber("CNBJ0621");
        userDetailVO.setPostId(0);
        userDetailVO.setIsHeadOrg(false);
        final UserPartTimerVO userPartTimerVO = new UserPartTimerVO();
        userPartTimerVO.setRowNo(0);
        userPartTimerVO.setUserId(0);
        userPartTimerVO.setEmployeeId(0);
        userPartTimerVO.setHrUserId(0);
        userPartTimerVO.setUserAdminId("userAdminId");
        userPartTimerVO.setUserAdminName("userAdminName");
        userPartTimerVO.setUserMasterId("userMasterId");
        userPartTimerVO.setUserMasterName("userMasterName");
        userPartTimerVO.setPostId(0);
        userPartTimerVO.setPostName("postName");
        userPartTimerVO.setHrPostId(0);
        userDetailVO.setUserPartTimerList(Arrays.asList(userPartTimerVO));
        when(mockBmsService.getUserDetailVOByHrUserId(0)).thenReturn(userDetailVO);

        when(mockSystemMsgCenterMapper.insertList(Arrays.asList(new SystemMsgCenter()))).thenReturn(0);
        when(mockHandoverMapper.updateByPrimaryKeySelective(new HrUserHandover())).thenReturn(0);

        // Run the test
        hrUserHandoverManagerServiceUnderTest.doOperator(handover);
    }

    @Test
    public void testDoOperator_BmsServiceReturnsNull() {
        // Setup
        final HrUserHandover handover = new HrUserHandover();
        handover.setId(0);
        handover.setDimEmployeeCode("dimEmployeeCode");
        handover.setDimIdNumber("dimIdNumber");
        handover.setDimEmployeeName("dimEmployeeName");
        handover.setDimJobPositionCode("dimJobPositionCode");
        handover.setDimDeptCode("dimDeptCode");
        handover.setDimServiceType(0);
        handover.setRecEmployeeCode("CNBJ0621");
        handover.setRecEmployeeName("recEmployeeName");
        handover.setRecDeptCode("KHFWZ");
        handover.setStatus(0);
        handover.setResultCode(0);
        handover.setResultMsg("系统原因");
        handover.setDimCurrJobCode("dimCurrJobCode");
        handover.setRecCurrJobCode("dimCurrJobCode");

        // Configure UserService.getAuthUserByUserId(...).
        final AuthUserVO authUserVO = new AuthUserVO();
        authUserVO.setId(0);
        authUserVO.setRegionName("regionName");
        authUserVO.setRegionCode("regionCode");
        authUserVO.setOrgCode("orgCode");
        authUserVO.setOrganizationName("organizationName");
        authUserVO.setOrganizationFullName("organizationFullName");
        authUserVO.setUserId("userId");
        authUserVO.setAgentId(0);
        authUserVO.setAgentTopUserId("agentTopUserId");
        authUserVO.setUserName("userName");
        authUserVO.setUserMobile("userMobile");
        authUserVO.setWxNickName("wxNickName");
        authUserVO.setPostName("postName");
        authUserVO.setStatus("status");
        authUserVO.setJobCode("dimCurrJobCode");
        when(mockUserService.getAuthUserByUserId("dimEmployeeCode")).thenReturn(authUserVO);

        // Configure UserPostService.listUserPostByJobNumber(...).
        final UserPost userPost = new UserPost();
        userPost.setId(0);
        userPost.setJobCode("jobCode");
        userPost.setMainJobNumber("mainJobNumber");
        userPost.setJobNumber("jobNumber");
        userPost.setHrUserId(0);
        userPost.setBmsUserId(0);
        userPost.setRegionCode("regionCode");
        userPost.setRegionName("regionName");
        userPost.setHrRegionId("hrRegionId");
        userPost.setHrOrgId("hrOrgId");
        userPost.setOrgCode("orgCode");
        userPost.setServiceType(0);
        userPost.setEmployeeStatus(0);
        userPost.setServiceStatus(0);
        userPost.setUserAdminId(0);
        final List<UserPost> userPosts = Arrays.asList(userPost);
        when(mockUserPostService.listUserPostByJobNumber("dimEmployeeCode")).thenReturn(userPosts);

        when(mockSystemMsgContentMapper.insertSelective(new SystemMsgContent())).thenReturn(0);
        when(mockBmsService.getUserDetailVOByHrUserId(0)).thenReturn(null);
        when(mockSystemMsgCenterMapper.insertList(Arrays.asList(new SystemMsgCenter()))).thenReturn(0);
        when(mockHandoverMapper.updateByPrimaryKeySelective(new HrUserHandover())).thenReturn(0);

        // Run the test
        hrUserHandoverManagerServiceUnderTest.doOperator(handover);

    }
}
