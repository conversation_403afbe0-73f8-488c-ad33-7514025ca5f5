package com.cfpamf.ms.insur.base.util;

import com.cfpamf.ms.insur.base.exception.BizException;
import com.cfpamf.ms.insur.base.exception.ExcptEnum;
import org.junit.Test;

import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.SecretKey;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import java.security.InvalidAlgorithmParameterException;
import java.security.InvalidKeyException;
import java.security.spec.AlgorithmParameterSpec;
import java.util.Map;

public class RsaCoderUtilTest {

    /**
     * charset
     */
    static String charset = "UTF-8";
    /**
     * 加密密钥
     */
    private static SecretKey key;
    /**
     * 算法参数
     */
    private static AlgorithmParameterSpec paramSpec;
    /**
     * 加密算法
     */
    private static Cipher ecipher;
    /**
     * 用户密钥
     */
    private static String tKey = "ceshiMiMa1234567";
    /**
     * 用户算法参数
     */
    private static String tIv = "ceshiJieMa123456";

    /**
     * 加密，使用指定数据源生成密钥，使用用户数据作为算法参数进行AES加密
     *
     * @param msg 加密的数据
     * @return
     */
    public static String encrypt(String msg) {
        String str = "";
        try {
            // 用密钥和一组算法参数初始化此 cipher
            ecipher.init(Cipher.ENCRYPT_MODE, key, paramSpec);
            int mod = 16 - msg.getBytes(charset).length % 16;
            if (mod != 0) {
                msg = rch(msg, " ", mod);
            }
            // 加密并转换成16进制字符串
            str = asHex(ecipher.doFinal(msg.getBytes(charset)));
        } catch (UnsupportedEncodingException | BadPaddingException | InvalidKeyException
                | InvalidAlgorithmParameterException | IllegalBlockSizeException e) {
        }
        return str;
    }

    /**
     * 解密，对生成的16进制的字符串进行解密
     *
     * @param value 解密的数据
     * @return
     */
    public static String decrypt(String value) {
        try {
            ecipher.init(Cipher.DECRYPT_MODE, key, paramSpec);
            if (value != null && !"".equals(value)) {
                return new String(ecipher.doFinal(asBin(value)), StandardCharsets.UTF_8).trim();
            } else {
                return value;
            }
        } catch (BadPaddingException | InvalidKeyException
                | InvalidAlgorithmParameterException | IllegalBlockSizeException e) {
        }
        return "";
    }

    /**
     * 将字节数组转换成16进制字符串
     *
     * @param buf
     * @return
     */
    private static String asHex(byte[] buf) {
        StringBuilder strbuf = new StringBuilder(buf.length * 2);
        int i;
        for (i = 0; i < buf.length; i++) {
            // 小于十前面补零
            if (((int) buf[i] & 0xff) < 0x10) {
                strbuf.append("0");
            }
            strbuf.append(Long.toString((int) buf[i] & 0xff, 16));
        }
        return strbuf.toString();
    }

    /**
     * 将16进制字符串转换成字节数组
     *
     * @param src
     * @return
     */
    private static byte[] asBin(String src) {
        if (src.length() < 1) {
            throw new BizException(ExcptEnum.ENCRYPT_ERROR_501001);
        }
        byte[] encrypted = new byte[src.length() / 2];
        for (int i = 0; i < src.length() / 2; i++) {
            int high = Integer.parseInt(src.substring(i * 2, i * 2 + 1), 16);
            int low = Integer.parseInt(src.substring(i * 2 + 1, i * 2 + 2), 16);
            encrypted[i] = (byte) (high * 16 + low);
        }
        return encrypted;
    }

    /**
     * lch
     *
     * @param sourString
     * @param cChar
     * @param cLen
     * @return
     */
    public static String lch(String sourString, String cChar, int cLen) {
        StringBuilder tReturn = new StringBuilder();
        for (int i = 0; i < cLen; i++) {
            tReturn.append(cChar);
        }
        tReturn.append(sourString);
        return tReturn.toString();
    }

    /**
     * rch
     *
     * @param sourString
     * @param cChar
     * @param cLen
     * @return
     */
    public static String rch(String sourString, String cChar, int cLen) {
        StringBuilder tReturn = new StringBuilder();
        for (int i = 0; i < cLen; i++) {
            tReturn.append(cChar);
        }
        return sourString + tReturn.toString();
    }

    @Test
    public void testAll() throws Exception {
        Map<String, Object> keyMap = RSAUtil.genKeyPair();
        String publicKey = RSAUtil.getPublicKey(keyMap);
        String privateKey = RSAUtil.getPrivateKey(keyMap);
//        System.err.println("公钥: \n\r" + publicKey);
//        System.err.println("私钥： \n\r" + privateKey);
//        System.err.println("公钥加密——私钥解密");
        String source = "1234567890e523452";
//        System.out.println("\r加密前文字：\r\n[" + source + "]");
        byte[] data = source.getBytes();
        String encryptBASE64 = RSAUtil.encryptBASE64(RSAUtil.encryptByPublicKey(data, publicKey));
//        System.out.println("加密后文字：\r\n[" + encryptBASE64 + "]");
        byte[] decodedData = RSAUtil.decryptByPrivateKey(RSAUtil.decryptBASE64(encryptBASE64), privateKey);
        String target = new String(decodedData);
//        System.out.println("解密后文字: \r\n[" + target + "]");
//        System.out.println("解密后文字相等: \r\n[" + Objects.equals(source, target) + "]");

//        System.out.println("========================================================================================");

        try {
            byte[] keyValue = tKey.getBytes();
            byte[] iv = tIv.getBytes();
            // 加解密模式
            String mode = "AES/CBC/NoPadding";
            key = new SecretKeySpec(keyValue, "AES");
            // 使用iv中的字节作为IV来构造一个 算法参数。
            paramSpec = new IvParameterSpec(iv);
            // 生成一个实现指定转换的 Cipher 对象
            ecipher = Cipher.getInstance(mode);
        } catch (Exception e) {
        }
        String testPay = "{\"header\":{\"channelId\":\"05167\",\"version\":\"0\",\"signature\":\"954894j3k3\",\"password\":\"zhnx\"},\"body\":{\"orderInfo\":{\"orderId\":\"18670318Z9OB0726\"}}}";
//        System.out.println(encrypt(testPay));
        String ds = "7abe6df62b9a1136adad8f9b7167a16fcf3629c633179ee0533d484a6c351522c91a01e94475e9069dd86271ce4530a5d9c9cc74b8422584128ae31ad7cafa308126aa95417c901d3b58ac067ddf8771c28c674914e5d5eff5753f34c8a4924c1e485ad9c6476b4de62e7bb26993ae6fdb9953181db88a64f51d7dfa8444e49d7820ecb0a4e730073f7278f481b88d7045e68e812fa550e7c82c70b9fb688d0eb7e14b9d05a20150bc189db9461d25c90175f00e435da28bd0dedc0ff3ff28dca696e2c35976645ff147d1da94953cafaa20891e63810c5db72728036d72a4fe";
//        System.out.println(decrypt(ds));
    }

    @Test
    public void test2() throws Exception {
        String pk = "MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBALDXw/rFMFHADQAF0tR58IqUDMqHxZxG8xzwK6pftqAfQMnrCHvQcA5rD40C/kThrLxttB2PNVHofZoVmd/lXYZcRBrSRHdxN1aWipvByobzL7W5bAaKfn/RCM9Vl0c1ak+9RTOD7AgXhgYTpc6EybyYHIfhtdeZhSUltQxWXvOPAgMBAAECgYEAp75zCqsDE3h9nFjpzFxCe+DFZVN2ca+nKfrzIHAb1jTvCtkjufZUZIjPPm3pbMM4S6Av6p4N8Lz8by0wQX3awGWFjNiUgYL5F7gvOeKvc3qXkY9T0Yojq1A42CHSJ4J6RIseyGfvPRcTD2AhI1Vrk1jKBuCTfRgjJ8gcRTtHARkCQQDZLH4fGJa88t5KzmpyPOaEdhEvRSiKrDTReqkUMK28tRjZFo+5lZeGD8Aim3pVVEmY7ROy2moYprigOTGmDUZtAkEA0HVsplFD7mh1gbtFFxjg7wgh8IRvOSs7tVOMPJ9lBFtUQ5Jm7jhKjILvNtPEP4pMyr17wf+dVyj0ADTOz1oUawJAQz4nfvFaGv8IHRl0lBKPPYxhHcXIG8feQs478+huovNf0jfOIKRU2kmeMBj789e5QgSGCObf2uokWFfdK5EIsQJAdfmezdm8Glcf+ZXkmhfIZT8zBg3NbItdNVfdTAnhf7+4Rq26dyOAfVk0zNC4hkVdBHiMa9SgVU2mGd/JE2yllQJALfNi4tc+d7lPtfApqraVpjpFbOz4JtnAMRVkS1cbtdJRtSY+Vx4o9Xdme/zq1cVRSPoOlak9y35KtRM7PEs3lg==";
        byte[] ss = RSAUtil.decryptByPrivateKey(RSAUtil.decryptBASE64("Ezrwdaqt2x6a5jvOWAMtjxrrXbgMAIj21YYdKli7rhOF5EvC6vbPtmpYq4NDW/28UZIpLHdtLCaJzJCEBxPXIbK9lBonnaZAdK/DyVscSNjLAJg4yJOo8pmCh1LPWvsEt/IbrDHm9hfFll2xbvD3aMVZ9EwWFKA+ntlAYeGqVWE="), pk);
//        System.out.println(new String(ss));
    }

}