package com.cfpamf.ms.insur.base.controller;

import org.junit.After;
import org.junit.Before;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.context.web.WebAppConfiguration;

//@RunWith(SpringRunner.class)
//@SpringBootTest
//@ActiveProfiles("dev")
//@WebAppConfiguration
public class ApplicationTest {

//    @Before
    public void init() {
        System.out.println("开始测试----------");
    }

//    @After
    public void after() {
        System.out.println("结束测试-----------");
    }

}

