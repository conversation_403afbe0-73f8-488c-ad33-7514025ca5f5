//package com.cfpamf.ms.insur.admin.job;
//
//import com.cfpamf.ms.insur.base.infeface.VoidConsumer;
//
///**
// * <AUTHOR>
// **/
//abstract class AbstractJobTest {
//
//    void testJob(VoidConsumer consumer) {
//        long startTime = System.currentTimeMillis();
//        long maxTestTime = 2000;
//        Thread jobThread = new Thread(consumer::accept);
//        jobThread.start();
//        while (System.currentTimeMillis() - startTime < maxTestTime) {
//        }
//        if (!jobThread.isInterrupted()) {
//            jobThread.interrupt();
////            System.out.println(this.getClass().getName() + " interrupt");
//        }
//    }
//}
