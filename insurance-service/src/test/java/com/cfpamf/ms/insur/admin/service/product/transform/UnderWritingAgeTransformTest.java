package com.cfpamf.ms.insur.admin.service.product.transform;

import com.alibaba.fastjson.JSONArray;
import com.cfpamf.ms.insur.admin.pojo.form.product.TestPremiumDutyForm;
import com.cfpamf.ms.insur.admin.pojo.form.product.TestPremiumForm;
import com.cfpamf.ms.insur.admin.pojo.po.product.risk.SysRiskFactor;
import com.github.jsonzou.jmockdata.JMockData;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.List;

/**
 * <AUTHOR> 2021/8/13 13:38
 */
@RunWith(SpringJUnit4ClassRunner.class)
public class UnderWritingAgeTransformTest {

    @InjectMocks
    UnderWritingAgeTransform ageTransform;

    @Before
    public void setUp() throws Exception {

    }

    @Test
    public void apply() {

        List<SysRiskFactor> factorList = null;
        // init data
        {
            factorList = JSONArray.parseArray("[\n" +
                    "  {\n" +
                    "    \"id\": 1393,\n" +
                    "    \"version\": 1,\n" +
                    "    \"riskKey\": \"ff79f7e3da7a4fa0b25ccf1eab0cfe13\",\n" +
                    "    \"factorConfigKey\": \"538442ba362c444cb8d632d74f47470b\",\n" +
                    "    \"targetId\": \"29\",\n" +
                    "    \"targetType\": 1,\n" +
                    "    \"fieldCode\": \"underWritingAge\",\n" +
                    "    \"optionalName\": \"30天-4周岁\",\n" +
                    "    \"optionalValue\": \"[30D,4Y]\",\n" +
                    "    \"value1\": \"30\",\n" +
                    "    \"value2\": \"4\",\n" +
                    "    \"unit1\": \"天\",\n" +
                    "    \"unit2\": \"年\"\n" +
                    "  },\n" +
                    "  {\n" +
                    "    \"id\": 1394,\n" +
                    "    \"version\": 1,\n" +
                    "    \"riskKey\": \"ff79f7e3da7a4fa0b25ccf1eab0cfe13\",\n" +
                    "    \"factorConfigKey\": \"538442ba362c444cb8d632d74f47470b\",\n" +
                    "    \"targetId\": \"29\",\n" +
                    "    \"targetType\": 1,\n" +
                    "    \"fieldCode\": \"underWritingAge\",\n" +
                    "    \"optionalName\": \"5-10周岁\",\n" +
                    "    \"optionalValue\": \"[5Y,10Y]\",\n" +
                    "    \"value1\": \"5\",\n" +
                    "    \"value2\": \"10\",\n" +
                    "    \"unit1\": \"年\",\n" +
                    "    \"unit2\": \"年\"\n" +
                    "  },\n" +
                    "  {\n" +
                    "    \"id\": 1395,\n" +
                    "    \"version\": 1,\n" +
                    "    \"riskKey\": \"ff79f7e3da7a4fa0b25ccf1eab0cfe13\",\n" +
                    "    \"factorConfigKey\": \"538442ba362c444cb8d632d74f47470b\",\n" +
                    "    \"targetId\": \"29\",\n" +
                    "    \"targetType\": 1,\n" +
                    "    \"fieldCode\": \"underWritingAge\",\n" +
                    "    \"optionalName\": \"11-15周岁\",\n" +
                    "    \"optionalValue\": \"[11Y,15Y]\",\n" +
                    "    \"value1\": \"11\",\n" +
                    "    \"value2\": \"15\",\n" +
                    "    \"unit1\": \"年\",\n" +
                    "    \"unit2\": \"年\"\n" +
                    "  },\n" +
                    "  {\n" +
                    "    \"id\": 1396,\n" +
                    "    \"version\": 1,\n" +
                    "    \"riskKey\": \"ff79f7e3da7a4fa0b25ccf1eab0cfe13\",\n" +
                    "    \"factorConfigKey\": \"538442ba362c444cb8d632d74f47470b\",\n" +
                    "    \"targetId\": \"29\",\n" +
                    "    \"targetType\": 1,\n" +
                    "    \"fieldCode\": \"underWritingAge\",\n" +
                    "    \"optionalName\": \"16-17周岁\",\n" +
                    "    \"optionalValue\": \"[16Y,17Y]\",\n" +
                    "    \"value1\": \"16\",\n" +
                    "    \"value2\": \"17\",\n" +
                    "    \"unit1\": \"年\",\n" +
                    "    \"unit2\": \"年\"\n" +
                    "  },\n" +
                    "  {\n" +
                    "    \"id\": 1397,\n" +
                    "    \"version\": 1,\n" +
                    "    \"riskKey\": \"ff79f7e3da7a4fa0b25ccf1eab0cfe13\",\n" +
                    "    \"factorConfigKey\": \"538442ba362c444cb8d632d74f47470b\",\n" +
                    "    \"targetId\": \"29\",\n" +
                    "    \"targetType\": 1,\n" +
                    "    \"fieldCode\": \"underWritingAge\",\n" +
                    "    \"optionalName\": \"18-20周岁\",\n" +
                    "    \"optionalValue\": \"[18Y,20Y]\",\n" +
                    "    \"value1\": \"18\",\n" +
                    "    \"value2\": \"20\",\n" +
                    "    \"unit1\": \"年\",\n" +
                    "    \"unit2\": \"年\"\n" +
                    "  },\n" +
                    "  {\n" +
                    "    \"id\": 1398,\n" +
                    "    \"version\": 1,\n" +
                    "    \"riskKey\": \"ff79f7e3da7a4fa0b25ccf1eab0cfe13\",\n" +
                    "    \"factorConfigKey\": \"538442ba362c444cb8d632d74f47470b\",\n" +
                    "    \"targetId\": \"29\",\n" +
                    "    \"targetType\": 1,\n" +
                    "    \"fieldCode\": \"underWritingAge\",\n" +
                    "    \"optionalName\": \"21-25周岁\",\n" +
                    "    \"optionalValue\": \"[21Y,25Y]\",\n" +
                    "    \"value1\": \"21\",\n" +
                    "    \"value2\": \"25\",\n" +
                    "    \"unit1\": \"年\",\n" +
                    "    \"unit2\": \"年\"\n" +
                    "  },\n" +
                    "  {\n" +
                    "    \"id\": 1399,\n" +
                    "    \"version\": 1,\n" +
                    "    \"riskKey\": \"ff79f7e3da7a4fa0b25ccf1eab0cfe13\",\n" +
                    "    \"factorConfigKey\": \"538442ba362c444cb8d632d74f47470b\",\n" +
                    "    \"targetId\": \"29\",\n" +
                    "    \"targetType\": 1,\n" +
                    "    \"fieldCode\": \"underWritingAge\",\n" +
                    "    \"optionalName\": \"26-30周岁\",\n" +
                    "    \"optionalValue\": \"[26Y,30Y]\",\n" +
                    "    \"value1\": \"26\",\n" +
                    "    \"value2\": \"30\",\n" +
                    "    \"unit1\": \"年\",\n" +
                    "    \"unit2\": \"年\"\n" +
                    "  },\n" +
                    "  {\n" +
                    "    \"id\": 1400,\n" +
                    "    \"version\": 1,\n" +
                    "    \"riskKey\": \"ff79f7e3da7a4fa0b25ccf1eab0cfe13\",\n" +
                    "    \"factorConfigKey\": \"538442ba362c444cb8d632d74f47470b\",\n" +
                    "    \"targetId\": \"29\",\n" +
                    "    \"targetType\": 1,\n" +
                    "    \"fieldCode\": \"underWritingAge\",\n" +
                    "    \"optionalName\": \"31-35周岁\",\n" +
                    "    \"optionalValue\": \"[31Y,35Y]\",\n" +
                    "    \"value1\": \"31\",\n" +
                    "    \"value2\": \"35\",\n" +
                    "    \"unit1\": \"年\",\n" +
                    "    \"unit2\": \"年\"\n" +
                    "  },\n" +
                    "  {\n" +
                    "    \"id\": 1401,\n" +
                    "    \"version\": 1,\n" +
                    "    \"riskKey\": \"ff79f7e3da7a4fa0b25ccf1eab0cfe13\",\n" +
                    "    \"factorConfigKey\": \"538442ba362c444cb8d632d74f47470b\",\n" +
                    "    \"targetId\": \"29\",\n" +
                    "    \"targetType\": 1,\n" +
                    "    \"fieldCode\": \"underWritingAge\",\n" +
                    "    \"optionalName\": \"36-40周岁\",\n" +
                    "    \"optionalValue\": \"[36Y,40Y]\",\n" +
                    "    \"value1\": \"36\",\n" +
                    "    \"value2\": \"40\",\n" +
                    "    \"unit1\": \"年\",\n" +
                    "    \"unit2\": \"年\"\n" +
                    "  },\n" +
                    "  {\n" +
                    "    \"id\": 1402,\n" +
                    "    \"version\": 1,\n" +
                    "    \"riskKey\": \"ff79f7e3da7a4fa0b25ccf1eab0cfe13\",\n" +
                    "    \"factorConfigKey\": \"538442ba362c444cb8d632d74f47470b\",\n" +
                    "    \"targetId\": \"29\",\n" +
                    "    \"targetType\": 1,\n" +
                    "    \"fieldCode\": \"underWritingAge\",\n" +
                    "    \"optionalName\": \"41-45周岁\",\n" +
                    "    \"optionalValue\": \"[41Y,45Y]\",\n" +
                    "    \"value1\": \"41\",\n" +
                    "    \"value2\": \"45\",\n" +
                    "    \"unit1\": \"年\",\n" +
                    "    \"unit2\": \"年\"\n" +
                    "  },\n" +
                    "  {\n" +
                    "    \"id\": 1403,\n" +
                    "    \"version\": 1,\n" +
                    "    \"riskKey\": \"ff79f7e3da7a4fa0b25ccf1eab0cfe13\",\n" +
                    "    \"factorConfigKey\": \"538442ba362c444cb8d632d74f47470b\",\n" +
                    "    \"targetId\": \"29\",\n" +
                    "    \"targetType\": 1,\n" +
                    "    \"fieldCode\": \"underWritingAge\",\n" +
                    "    \"optionalName\": \"46-50周岁\",\n" +
                    "    \"optionalValue\": \"[46Y,50Y]\",\n" +
                    "    \"value1\": \"46\",\n" +
                    "    \"value2\": \"50\",\n" +
                    "    \"unit1\": \"年\",\n" +
                    "    \"unit2\": \"年\"\n" +
                    "  },\n" +
                    "  {\n" +
                    "    \"id\": 1404,\n" +
                    "    \"version\": 1,\n" +
                    "    \"riskKey\": \"ff79f7e3da7a4fa0b25ccf1eab0cfe13\",\n" +
                    "    \"factorConfigKey\": \"538442ba362c444cb8d632d74f47470b\",\n" +
                    "    \"targetId\": \"29\",\n" +
                    "    \"targetType\": 1,\n" +
                    "    \"fieldCode\": \"underWritingAge\",\n" +
                    "    \"optionalName\": \"51-55周岁\",\n" +
                    "    \"optionalValue\": \"[51Y,55Y]\",\n" +
                    "    \"value1\": \"51\",\n" +
                    "    \"value2\": \"55\",\n" +
                    "    \"unit1\": \"年\",\n" +
                    "    \"unit2\": \"年\"\n" +
                    "  },\n" +
                    "  {\n" +
                    "    \"id\": 1405,\n" +
                    "    \"version\": 1,\n" +
                    "    \"riskKey\": \"ff79f7e3da7a4fa0b25ccf1eab0cfe13\",\n" +
                    "    \"factorConfigKey\": \"538442ba362c444cb8d632d74f47470b\",\n" +
                    "    \"targetId\": \"29\",\n" +
                    "    \"targetType\": 1,\n" +
                    "    \"fieldCode\": \"underWritingAge\",\n" +
                    "    \"optionalName\": \"56-60周岁\",\n" +
                    "    \"optionalValue\": \"[56Y,60Y]\",\n" +
                    "    \"value1\": \"56\",\n" +
                    "    \"value2\": \"60\",\n" +
                    "    \"unit1\": \"年\",\n" +
                    "    \"unit2\": \"年\"\n" +
                    "  },\n" +
                    "  {\n" +
                    "    \"id\": 1406,\n" +
                    "    \"version\": 1,\n" +
                    "    \"riskKey\": \"ff79f7e3da7a4fa0b25ccf1eab0cfe13\",\n" +
                    "    \"factorConfigKey\": \"538442ba362c444cb8d632d74f47470b\",\n" +
                    "    \"targetId\": \"29\",\n" +
                    "    \"targetType\": 1,\n" +
                    "    \"fieldCode\": \"underWritingAge\",\n" +
                    "    \"optionalName\": \"61-65周岁\",\n" +
                    "    \"optionalValue\": \"[61Y,65Y]\",\n" +
                    "    \"value1\": \"61\",\n" +
                    "    \"value2\": \"65\",\n" +
                    "    \"unit1\": \"年\",\n" +
                    "    \"unit2\": \"年\"\n" +
                    "  },\n" +
                    "  {\n" +
                    "    \"id\": 1407,\n" +
                    "    \"version\": 1,\n" +
                    "    \"riskKey\": \"ff79f7e3da7a4fa0b25ccf1eab0cfe13\",\n" +
                    "    \"factorConfigKey\": \"538442ba362c444cb8d632d74f47470b\",\n" +
                    "    \"targetId\": \"29\",\n" +
                    "    \"targetType\": 1,\n" +
                    "    \"fieldCode\": \"underWritingAge\",\n" +
                    "    \"optionalName\": \"66-70周岁\",\n" +
                    "    \"optionalValue\": \"[66Y,70Y]\",\n" +
                    "    \"value1\": \"66\",\n" +
                    "    \"value2\": \"70\",\n" +
                    "    \"unit1\": \"年\",\n" +
                    "    \"unit2\": \"年\"\n" +
                    "  },\n" +
                    "  {\n" +
                    "    \"id\": 1408,\n" +
                    "    \"version\": 1,\n" +
                    "    \"riskKey\": \"ff79f7e3da7a4fa0b25ccf1eab0cfe13\",\n" +
                    "    \"factorConfigKey\": \"538442ba362c444cb8d632d74f47470b\",\n" +
                    "    \"targetId\": \"29\",\n" +
                    "    \"targetType\": 1,\n" +
                    "    \"fieldCode\": \"underWritingAge\",\n" +
                    "    \"optionalName\": \"71-75周岁\",\n" +
                    "    \"optionalValue\": \"[71Y,75Y]\",\n" +
                    "    \"value1\": \"71\",\n" +
                    "    \"value2\": \"75\",\n" +
                    "    \"unit1\": \"年\",\n" +
                    "    \"unit2\": \"年\"\n" +
                    "  },\n" +
                    "  {\n" +
                    "    \"id\": 1409,\n" +
                    "    \"version\": 1,\n" +
                    "    \"riskKey\": \"ff79f7e3da7a4fa0b25ccf1eab0cfe13\",\n" +
                    "    \"factorConfigKey\": \"538442ba362c444cb8d632d74f47470b\",\n" +
                    "    \"targetId\": \"29\",\n" +
                    "    \"targetType\": 1,\n" +
                    "    \"fieldCode\": \"underWritingAge\",\n" +
                    "    \"optionalName\": \"76-80周岁\",\n" +
                    "    \"optionalValue\": \"[76Y,80Y]\",\n" +
                    "    \"value1\": \"76\",\n" +
                    "    \"value2\": \"80\",\n" +
                    "    \"unit1\": \"年\",\n" +
                    "    \"unit2\": \"年\"\n" +
                    "  },\n" +
                    "  {\n" +
                    "    \"id\": 1410,\n" +
                    "    \"version\": 1,\n" +
                    "    \"riskKey\": \"ff79f7e3da7a4fa0b25ccf1eab0cfe13\",\n" +
                    "    \"factorConfigKey\": \"538442ba362c444cb8d632d74f47470b\",\n" +
                    "    \"targetId\": \"29\",\n" +
                    "    \"targetType\": 1,\n" +
                    "    \"fieldCode\": \"underWritingAge\",\n" +
                    "    \"optionalName\": \"81-85周岁\",\n" +
                    "    \"optionalValue\": \"[81Y,85Y]\",\n" +
                    "    \"value1\": \"81\",\n" +
                    "    \"value2\": \"85\",\n" +
                    "    \"unit1\": \"年\",\n" +
                    "    \"unit2\": \"年\"\n" +
                    "  },\n" +
                    "  {\n" +
                    "    \"id\": 1411,\n" +
                    "    \"version\": 1,\n" +
                    "    \"riskKey\": \"ff79f7e3da7a4fa0b25ccf1eab0cfe13\",\n" +
                    "    \"factorConfigKey\": \"538442ba362c444cb8d632d74f47470b\",\n" +
                    "    \"targetId\": \"29\",\n" +
                    "    \"targetType\": 1,\n" +
                    "    \"fieldCode\": \"underWritingAge\",\n" +
                    "    \"optionalName\": \"86-90周岁\",\n" +
                    "    \"optionalValue\": \"[86Y,90Y]\",\n" +
                    "    \"value1\": \"86\",\n" +
                    "    \"value2\": \"90\",\n" +
                    "    \"unit1\": \"年\",\n" +
                    "    \"unit2\": \"年\"\n" +
                    "  },\n" +
                    "  {\n" +
                    "    \"id\": 1412,\n" +
                    "    \"version\": 1,\n" +
                    "    \"riskKey\": \"ff79f7e3da7a4fa0b25ccf1eab0cfe13\",\n" +
                    "    \"factorConfigKey\": \"538442ba362c444cb8d632d74f47470b\",\n" +
                    "    \"targetId\": \"29\",\n" +
                    "    \"targetType\": 1,\n" +
                    "    \"fieldCode\": \"underWritingAge\",\n" +
                    "    \"optionalName\": \"91-95周岁\",\n" +
                    "    \"optionalValue\": \"[91Y,95Y]\",\n" +
                    "    \"value1\": \"91\",\n" +
                    "    \"value2\": \"95\",\n" +
                    "    \"unit1\": \"年\",\n" +
                    "    \"unit2\": \"年\"\n" +
                    "  },\n" +
                    "  {\n" +
                    "    \"id\": 1413,\n" +
                    "    \"version\": 1,\n" +
                    "    \"riskKey\": \"ff79f7e3da7a4fa0b25ccf1eab0cfe13\",\n" +
                    "    \"factorConfigKey\": \"538442ba362c444cb8d632d74f47470b\",\n" +
                    "    \"targetId\": \"29\",\n" +
                    "    \"targetType\": 1,\n" +
                    "    \"fieldCode\": \"underWritingAge\",\n" +
                    "    \"optionalName\": \"96-100周岁\",\n" +
                    "    \"optionalValue\": \"[96Y,100Y]\",\n" +
                    "    \"value1\": \"96\",\n" +
                    "    \"value2\": \"100\",\n" +
                    "    \"unit1\": \"年\",\n" +
                    "    \"unit2\": \"年\"\n" +
                    "  },\n" +
                    "  {\n" +
                    "    \"id\": 1414,\n" +
                    "    \"version\": 1,\n" +
                    "    \"riskKey\": \"ff79f7e3da7a4fa0b25ccf1eab0cfe13\",\n" +
                    "    \"factorConfigKey\": \"538442ba362c444cb8d632d74f47470b\",\n" +
                    "    \"targetId\": \"29\",\n" +
                    "    \"targetType\": 1,\n" +
                    "    \"fieldCode\": \"underWritingAge\",\n" +
                    "    \"optionalName\": \"101-105周岁\",\n" +
                    "    \"optionalValue\": \"[101Y,105Y]\",\n" +
                    "    \"value1\": \"101\",\n" +
                    "    \"value2\": \"105\",\n" +
                    "    \"unit1\": \"年\",\n" +
                    "    \"unit2\": \"年\"\n" +
                    "  },\n" +
                    "  {\n" +
                    "    \"id\": 1415,\n" +
                    "    \"version\": 1,\n" +
                    "    \"riskKey\": \"ff79f7e3da7a4fa0b25ccf1eab0cfe13\",\n" +
                    "    \"factorConfigKey\": \"538442ba362c444cb8d632d74f47470b\",\n" +
                    "    \"targetId\": \"29\",\n" +
                    "    \"targetType\": 1,\n" +
                    "    \"fieldCode\": \"amount\",\n" +
                    "    \"optionalName\": \"50000\",\n" +
                    "    \"optionalValue\": \"50000\"\n" +
                    "  },\n" +
                    "  {\n" +
                    "    \"id\": 1416,\n" +
                    "    \"version\": 1,\n" +
                    "    \"riskKey\": \"ff79f7e3da7a4fa0b25ccf1eab0cfe13\",\n" +
                    "    \"factorConfigKey\": \"538442ba362c444cb8d632d74f47470b\",\n" +
                    "    \"targetId\": \"29\",\n" +
                    "    \"targetType\": 1,\n" +
                    "    \"fieldCode\": \"sex\",\n" +
                    "    \"optionalName\": \"男\",\n" +
                    "    \"optionalValue\": \"1\"\n" +
                    "  },\n" +
                    "  {\n" +
                    "    \"id\": 1417,\n" +
                    "    \"version\": 1,\n" +
                    "    \"riskKey\": \"ff79f7e3da7a4fa0b25ccf1eab0cfe13\",\n" +
                    "    \"factorConfigKey\": \"538442ba362c444cb8d632d74f47470b\",\n" +
                    "    \"targetId\": \"29\",\n" +
                    "    \"targetType\": 1,\n" +
                    "    \"fieldCode\": \"sex\",\n" +
                    "    \"optionalName\": \"女\",\n" +
                    "    \"optionalValue\": \"2\"\n" +
                    "  }\n" +
                    "]", SysRiskFactor.class);
        }
        final TestPremiumForm mock = JMockData.mock(TestPremiumForm.class);
        mock.getPerson().setBirthday("1995-08-12");
        mock.getOrderInfo().setStartTime("2021-08-22 23:22:22");
        final String underWritingAge = ageTransform.apply("underWritingAge", mock, new TestPremiumDutyForm(), factorList);
        //这个判断几年后就有问题
        Assert.assertEquals("26Y-30Y", underWritingAge);

    }
}
