package com.cfpamf.ms.insur.admin.job.renewal;

import java.time.LocalDateTime;

import com.cfpamf.ms.insur.admin.job.renewal.dto.RenewalRemindDTO;
import com.cfpamf.ms.insur.base.util.SmsSenderUtil;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.Mockito;

/**
 * <AUTHOR>
 * @date 2021/12/22 14:15
 */

public class RenewalRemindMessageHelperTest {
    @Test
    public void testCreateSms() {
        RenewalRemindDTO renewalRemindDTO = new RenewalRemindDTO();
        renewalRemindDTO.setApplicantName("1");
        renewalRemindDTO.setApplicantMobile("1");
        renewalRemindDTO.setProductName("1");
        renewalRemindDTO.setEndTime(LocalDateTime.of(2021, 12, 21, 1, 1, 1));
        renewalRemindDTO.setCustomerAdminName("1");
        renewalRemindDTO.setCustomerAdminMobile("1");
        renewalRemindDTO.setGraceDay(2);
        renewalRemindDTO.setExpireDay(10);

        SmsSenderUtil.SmsSelfMsgDTO sms = RenewalRemindMessageHelper.createSms(renewalRemindDTO);

        Assert.assertEquals(sms.getSelfMsg(), "亲爱的1，您投保的1宽限期仅剩余-8天，可联系您的客户经理1为您提供续购服务，1，感谢支持！");
        RenewalRemindDTO renewalRemindDTO2 = new RenewalRemindDTO();
        renewalRemindDTO2.setApplicantName("1");
        renewalRemindDTO2.setApplicantMobile("1");
        renewalRemindDTO2.setProductName("1");
        renewalRemindDTO2.setEndTime(LocalDateTime.of(2021, 12, 21, 1, 1, 1));
        renewalRemindDTO2.setCustomerAdminName("1");
        renewalRemindDTO2.setCustomerAdminMobile("1");
        renewalRemindDTO2.setGraceDay(10);
        renewalRemindDTO2.setExpireDay(-10);
        SmsSenderUtil.SmsSelfMsgDTO sms2 = RenewalRemindMessageHelper.createSms(renewalRemindDTO2);
        Assert.assertEquals(sms2.getSelfMsg(), "亲爱的1，您投保的1将在12月21日到期。可联系顾问1提供续购服务，1，感谢支持！");


    }
}
