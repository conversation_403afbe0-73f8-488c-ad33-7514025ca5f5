package com.cfpamf.ms.insur.admin.service;

import org.junit.Test;

import static org.junit.Assert.*;

public class SmOrderRenewBindServiceTest {

    @Test
    public void getById() {
    }

    @Test
    public void listByFhOrderIds() {
    }

    @Test
    public void listByFhOrderId() {
    }

    @Test
    public void getByFhOrderId() {
    }

    @Test
    public void getByPolicyNo() {
    }

    @Test
    public void addOrderRenewBindInfo() {
    }

    @Test
    public void addPreOrderRenewBindInfo() {
    }

    @Test
    public void updateValidateFlag() {
    }

    @Test
    public void updateOrderBindStatus() {
    }

    @Test
    public void updatePolicyNoByFhOrderId() {
    }

    @Test
    public void addFhOrderIdToRenewBindInfo() {
    }

    @Test
    public void validateProductCanRenewBind() {
    }

    @Test
    public void getCanRenewBindProducts() {
    }

    @Test
    public void syncCallback() {
    }
}