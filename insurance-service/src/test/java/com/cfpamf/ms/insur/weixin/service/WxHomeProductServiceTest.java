package com.cfpamf.ms.insur.weixin.service;

import com.cfpamf.ms.insur.admin.enums.product.EnumProductCreateType;
import com.cfpamf.ms.insur.admin.pojo.vo.SmPlanVO;
import com.cfpamf.ms.insur.admin.pojo.vo.SmProductCoveragePremiumDTO;
import com.cfpamf.ms.insur.admin.service.BaseTest;
import com.cfpamf.ms.insur.admin.service.sys.SystemShareKnowledgeService;
import com.cfpamf.ms.insur.base.exception.BizException;
import com.cfpamf.ms.insur.base.exception.ExcptEnum;
import com.cfpamf.ms.insur.base.util.HttpRequestUtil;
import com.cfpamf.ms.insur.weixin.dao.safes.PolicyMapper;
import com.cfpamf.ms.insur.weixin.pojo.dto.WxGlProductQuoteDTO;
import com.cfpamf.ms.insur.weixin.pojo.query.policy.CoverageQuery;
import com.cfpamf.ms.insur.weixin.pojo.vo.WxGlProductQuoteResultVO;
import com.cfpamf.ms.insur.weixin.pojo.vo.WxProductDetailVO;
import com.cfpamf.ms.insur.weixin.service.context.RFQHolder;
import com.cfpamf.ms.insur.weixin.service.policy.InquiryService;
import com.github.jsonzou.jmockdata.JMockData;
import com.github.jsonzou.jmockdata.TypeReference;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@RunWith(PowerMockRunner.class)
@PrepareForTest({HttpRequestUtil.class})
public class WxHomeProductServiceTest extends BaseTest {
    @InjectMocks
    WxHomeProductService wxHomeProductService;
    @Mock
    private InquiryService inquiryService;
    @Mock
    private PolicyMapper policyMapper;
    @Mock
    com.cfpamf.ms.insur.admin.service.SmProductService productService;
    @Mock
    com.cfpamf.ms.insur.admin.service.SmCmpySettingService cmpySettingService;
    @Mock
    com.cfpamf.ms.insur.admin.service.SmProductPosterService posterService;
    @Mock
    com.cfpamf.ms.insur.admin.service.OccupationService occupationService;
    @Mock
    com.cfpamf.ms.insur.admin.service.SystemSettingService systemSettingService;
    @Mock
    com.cfpamf.ms.insur.admin.dao.safes.SmOccupationMapper occupationMapper;
    @Mock
    com.cfpamf.ms.insur.weixin.dao.safes.WxProductMapper wxProductMapper;
    @Mock
    com.cfpamf.ms.insur.admin.dao.safes.SmProductMapper smProductMapper;
    @Mock
    com.cfpamf.ms.insur.admin.dao.safes.SmCommissionMapper commissionMapper;
    @Mock
    com.cfpamf.ms.insur.weixin.dao.safes.WxProductPosterMapper wxProductPosterMapper;
    @Mock
    com.cfpamf.ms.insur.weixin.dao.safes.WxUserSettingMapper wxUserSettingMapper;
    @Mock
    com.cfpamf.ms.insur.weixin.dao.safes.WxGlProductQuoteMapper productQuoteMapper;
    @Mock
    SystemShareKnowledgeService shareKnowledgeService;

    @Test
    public void getWxProductsByPage() {
        try {
            wxHomeProductService.getWxProductsByPage(JMockData.mock(com.cfpamf.ms.insur.weixin.pojo.query.WxProductQuery.class));
        } catch (Exception e) {

        }
    }

    @Test
    public void getWxGroupProductsByPage() {
        try {
            wxHomeProductService.getWxGroupProductsByPage(JMockData.mock(com.cfpamf.ms.insur.weixin.pojo.query.WxProductQuery.class));
        } catch (Exception e) {

        }
    }

    @Test
    public void getWxGroupProductsDetail() {
        try {
            wxHomeProductService.getWxGroupProductsDetail(",EBjdQ,", ",QsTNt,", JMockData.mock(int.class));
        } catch (Exception e) {

        }
    }

    @Test
    public void getWxHotProductsByPage() {
        try {
            wxHomeProductService.getWxHotProductsByPage(JMockData.mock(com.cfpamf.ms.insur.weixin.pojo.query.WxProductQuery.class));
        } catch (Exception e) {

        }
    }

    @Test
    public void getOrgSalesProductIds() {
        try {
            wxHomeProductService.getOrgSalesProductIds(",wCmps,");
        } catch (Exception e) {

        }
    }

    @Test
    public void getWxProductDetailById() {
        try {
            wxHomeProductService.getWxProductDetailByIdAndRegionName(JMockData.mock(int.class), "是", "8,9");
        } catch (Exception e) {

        }
    }

    @Test
    public void getWxProductPlanCoverages() {
        try {
            wxHomeProductService.getWxProductPlanCoverages(JMockData.mock(int.class));
        } catch (Exception e) {

        }
    }

    @Test
    public void getWxProductPlans() {
        try {
            wxHomeProductService.getWxProductPlans(JMockData.mock(int.class), "湖南");
        } catch (Exception e) {

        }
    }

    @Test
    public void getWxProductCms() {
        try {
            wxHomeProductService.getWxProductCms(JMockData.mock(int.class), ",MaUlX,", ",Kxpyh,");
        } catch (Exception e) {

        }
    }

    @Test
    public void getWxFactorOptionalsVo() {
        try {
            wxHomeProductService.getWxFactorOptionalsVo(JMockData.mock(int.class));
        } catch (Exception e) {

        }
    }

    @Test
    public void getPlanFactorPrice() {
        try {
            wxHomeProductService.getPlanFactorPrice(JMockData.mock(com.cfpamf.ms.insur.admin.pojo.query.SmPlanFactorQuery.class));
        } catch (Exception e) {

        }
    }

    @Test
    public void getAllPlanFactorPrices() {
        try {
            wxHomeProductService.getAllPlanFactorPrices(JMockData.mock(int.class));
        } catch (Exception e) {

        }
    }

    @Test
    public void getProductCompanyId() {
        try {
            wxHomeProductService.getProductCompanyId(JMockData.mock(int.class));
        } catch (Exception e) {

        }
    }

    @Test
    public void getPlanById() {
        try {
            wxHomeProductService.getPlanById(JMockData.mock(int.class));
        } catch (Exception e) {

        }
    }

    @Test
    public void getWxFormFieldSettings() {
        try {
            wxHomeProductService.getWxFormFieldSettings(JMockData.mock(int.class));
        } catch (Exception e) {

        }
    }

    @Test
    public void getCompanySettings() {
        try {
            wxHomeProductService.getCompanySettings(JMockData.mock(int.class), ",buEMQ,");
        } catch (Exception e) {

        }
    }

    @Test
    public void getProductFormFields() {
        try {
            wxHomeProductService.getProductFormFields(JMockData.mock(int.class));
        } catch (Exception e) {

        }
    }

    @Test
    public void getOccupationHotList() {
        try {
            wxHomeProductService.getOccupationHotList(JMockData.mock(int.class), JMockData.mock(int.class));
        } catch (Exception e) {

        }
    }

    @Test
    public void getWxProductPoster() {
        try {
            wxHomeProductService.getWxProductPoster(JMockData.mock(com.cfpamf.ms.insur.admin.pojo.query.PosterGenQuery.class));
        } catch (Exception e) {

        }
    }

    @Test
    public void getWxProductRenews() {
        try {
            wxHomeProductService.getWxProductRenews(JMockData.mock(int.class), ",mxtuJ,", ",zUTAP,");
        } catch (Exception e) {

        }
    }

    @Test
    public void getWxHomeLbtImage() {
        try {
            wxHomeProductService.getWxHomeLbtImage(",xVkQk,");
        } catch (Exception e) {

        }
    }

    @Test
    public void getWxGlProductCoverages() {
        try {
            wxHomeProductService.getWxGlProductCoverages(",wwbXi,", ",zxSsn,", JMockData.mock(int.class));
        } catch (Exception e) {

        }
    }

    @Test
    public void getQrcodeBase64() {
        try {
            wxHomeProductService.getQrcodeBase64(JMockData.mock(com.cfpamf.ms.insur.admin.pojo.dto.QrcodeDTO.class));
        } catch (Exception e) {

        }
    }

    @Test
    public void checkWxGlProductQuote() {
        try {
            wxHomeProductService.checkWxGlProductQuote(JMockData.mock(com.cfpamf.ms.insur.weixin.pojo.dto.WxGlProductQuoteDTO.class));
        } catch (Exception e) {

        }
    }

    @Test
    public void getWxGlProductQuote() {
        try {
            wxHomeProductService.getWxGlProductQuote(JMockData.mock(com.cfpamf.ms.insur.weixin.pojo.dto.WxGlProductQuoteDTO.class));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void queryPremiumTable() {
        Integer productId = 454;
        Integer planId = 918;
        List<SmProductCoveragePremiumDTO> data = wxHomeProductService.queryPremiumTable(productId,planId);
        System.out.println(data);
        Assert.assertTrue(data!=null);
    }

    @Test
    public void buildPermiumTable() {
        RFQHolder holder = JMockData.mock(RFQHolder.class);
        TypeReference<List<CoverageQuery>> tr = new TypeReference<List<CoverageQuery>>() {};
        List<CoverageQuery> coverages=JMockData.mock(tr);

        TypeReference<List<SmProductCoveragePremiumDTO>> tr1 = new TypeReference<List<SmProductCoveragePremiumDTO>>() {};
        List<SmProductCoveragePremiumDTO> coveragePremiums=JMockData.mock(tr1);

        List<WxGlProductQuoteResultVO.ProductPremium> data = wxHomeProductService.buildPermiumTable(holder,coverages,new ArrayList<>(),coveragePremiums);
        System.out.println(data);
        Assert.assertTrue(data!=null);
    }

    @Test
    public void tryPremium() {
        Integer companyId = 207;
        WxGlProductQuoteDTO data=JMockData.mock(WxGlProductQuoteDTO.class);
        RFQHolder holder=JMockData.mock(RFQHolder.class);

        TypeReference<List<SmProductCoveragePremiumDTO>> tr1 = new TypeReference<List<SmProductCoveragePremiumDTO>>() {};
        List<SmProductCoveragePremiumDTO> coveragePremiums=JMockData.mock(tr1);
        List<String> jobclassList = new ArrayList<>();
        jobclassList.add("1");
        jobclassList.add("2");

        Map<String,BigDecimal> rtn =
                wxHomeProductService.tryPremiumByOne(data,holder,jobclassList,coveragePremiums);
        System.out.println(rtn);
        Assert.assertTrue(rtn!=null);
    }

    @Test
    public void getWxGlProductQuotePlanList() {
        try {
            wxHomeProductService.getWxGlProductQuotePlanList(JMockData.mock(com.cfpamf.ms.insur.weixin.pojo.query.WxGlProductQuoteQuery.class));
        } catch (Exception e) {

        }
    }

    @Test
    public void saveWxGlProductQuotePlan() {
        try {
            wxHomeProductService.saveWxGlProductQuotePlan(JMockData.mock(com.cfpamf.ms.insur.weixin.pojo.dto.WxGlProductQuotePlanDTO.class));
        } catch (Exception e) {

        }
    }

    @Test
    public void getWxGlProductQuotePlan() {
        try {
            wxHomeProductService.getWxGlProductQuotePlan(JMockData.mock(int.class), ",ghtDZ,", ",cRDKu,");
        } catch (Exception e) {

        }
    }

    @Test
    public void deleteWxGlProductQuotePlan() {
        try {
            wxHomeProductService.deleteWxGlProductQuotePlan(JMockData.mock(int.class), ",EwaPe,", ",SKqLw,");
        } catch (Exception e) {

        }
    }


    @Test
    public void getWxProductDetailByIdAndRegionName(){
        WxProductDetailVO detailVo = null;
        Integer productId = 100;
        String regionName = "湖南区域";
        String planIds = "";
        Mockito.when(wxProductMapper.getWxProductDetailById(Mockito.anyInt())).thenReturn(detailVo);
        try{
            wxHomeProductService.getWxProductDetailByIdAndRegionName(productId,regionName,planIds);
        }catch (Exception e){
            System.out.println(1111);
        }
        detailVo = new WxProductDetailVO();
        List<SmPlanVO> planVOList = new ArrayList<>();
        detailVo.setCreateType(EnumProductCreateType.PERSON_LONG_INSURANCE.name());
        Mockito.when(wxProductMapper.getWxProductDetailById(Mockito.anyInt())).thenReturn(detailVo);
        Mockito.when(productService.getProductPlansByOrgLimit(Mockito.anyInt(), Mockito.anyString())).thenReturn(planVOList);
        try{
            wxHomeProductService.getWxProductDetailByIdAndRegionName(productId,regionName,planIds);
        }catch (BizException e){
            System.out.println(2222);
            Assert.assertEquals(ExcptEnum.NO_DATA_PERMISSION_801010.getCode(),e.getCode());
        }

    }
}

