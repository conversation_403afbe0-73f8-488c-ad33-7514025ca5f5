package com.cfpamf.ms.insur.admin.service;

import com.cfpamf.common.ms.result.Result;
import com.github.jsonzou.jmockdata.JMockData;
import com.github.jsonzou.jmockdata.TypeReference;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mockito;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR> 2021/5/25 14:24
 */
@RunWith(SpringJUnit4ClassRunner.class)
public class DistributionProxyServiceTest {

    @InjectMocks
    DistributionProxyService distributionProxyService;
    @org.mockito.Mock
    com.cfpamf.ms.distribution.api.DistributionApi distributionApi;

    @Test
    public void isCreateDistributionOrder() {
        try {
            distributionProxyService.isCreateDistributionOrder(",KfLTI,", ",vZtRV,");
        } catch (Exception e) {

        }
    }


    @Test
    public void queryWhaleAgent() {
        Mockito.when(distributionApi.queryWhaleAgent(Mockito.anyString()))
                .thenReturn(new Result<>(true,null, Collections.emptyList()));
        distributionProxyService.queryWhaleAgent(JMockData.mock(new TypeReference<List<String>>() {
        }));
    }

    @Test
    public void queryUpUserInfo() {
        try {
            distributionProxyService.queryUpUserInfo(",ffcXf,");
        } catch (Exception e) {

        }
    }

    @Test
    public void createOrder() {
        try {
            distributionProxyService.createOrder(JMockData.mock(com.cfpamf.ms.distribution.dto.OrderSubmitMsgBody.class));
        } catch (Exception e) {

        }
    }
}
