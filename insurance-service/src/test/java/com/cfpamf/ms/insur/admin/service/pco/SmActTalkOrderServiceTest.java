package com.cfpamf.ms.insur.admin.service.pco;

import com.cfpamf.ms.insur.admin.dao.safes.SmOrderMapper;
import com.cfpamf.ms.insur.admin.dao.safes.order.SmOrderDDDMapper;
import com.cfpamf.ms.insur.admin.dao.safes.pco.SmActTalkMapper;
import com.cfpamf.ms.insur.admin.dao.safes.pco.SmOrderTalkMapper;
import com.cfpamf.ms.insur.admin.pojo.po.order.SmOrderShare;
import com.cfpamf.ms.insur.admin.pojo.po.pco.SmActTalkPerson;
import com.cfpamf.ms.insur.admin.service.BaseTest;
import com.cfpamf.ms.insur.admin.service.DistributionProxyService;
import com.cfpamf.ms.insur.admin.service.SmOrderManageService;
import com.cfpamf.ms.insur.admin.service.UserService;
import com.cfpamf.ms.insur.base.service.BmsService;
import com.cfpamf.ms.insur.base.service.DLockTemplate;
import com.github.jsonzou.jmockdata.JMockData;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * <AUTHOR> 2021/10/15 16:02
 */
@RunWith(SpringJUnit4ClassRunner.class)
public class SmActTalkOrderServiceTest extends BaseTest {

    @InjectMocks
    SmActTalkOrderService service;

    @Mock
    SmOrderTalkMapper orderTalkMapper;

    @Mock
    SmActTalkMapper talkMapper;

    @Mock
    SmOrderMapper orderMapper;

    @Mock
    DLockTemplate template;

    @Mock
    SmOrderDDDMapper orderDDDMapper;

    @Mock
    DistributionProxyService proxyService;

    @Mock
    BmsService bmsService;

    @Mock
    SmOrderManageService orderManageService;

    @Mock
    UserService userService;
    @Override
    @Before
    public void setUp() throws Exception {
        super.resetMapper();
    }


    @Test
    public void genTalkOrder() {

        service.genTalkOrder("test");
        service.orderTackInfo("12");
    }

    @Test
    public void getTalkInvite() {
        SmOrderShare talkInvite = service.getTalkInvite("1234");
        Assert.assertNotNull(talkInvite);
    }

    @Test
    public void orderRecommendIdChange() {
        service.orderRecommendIdChange("1234", JMockData.mock(SmActTalkPerson.class));
        SmActTalkPerson mock = JMockData.mock(SmActTalkPerson.class);
        mock.setInviteType(2);
        service.orderRecommendIdChange("1234", mock);

    }

}
