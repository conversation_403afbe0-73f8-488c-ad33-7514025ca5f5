package com.cfpamf.ms.insur.admin.service.product;

import com.cfpamf.ms.insur.admin.dao.safes.*;
import com.cfpamf.ms.insur.admin.dao.safes.order.SmOrderExtendZaMapper;
import com.cfpamf.ms.insur.admin.dao.safes.order.SmOrderItemMapper;
import com.cfpamf.ms.insur.admin.enums.EnumChannel;
import com.cfpamf.ms.insur.admin.external.ChannelOrderService;
import com.cfpamf.ms.insur.admin.external.OrderPrePayRequest;
import com.cfpamf.ms.insur.admin.external.zhongan.ZaApiProperties;
import com.cfpamf.ms.insur.admin.external.zhongan.ZaOrderServiceAdapter;
import com.cfpamf.ms.insur.admin.external.zhongan.api.ZaApiService;
import com.cfpamf.ms.insur.admin.pojo.dto.order.za.PaymentDTO;
import com.cfpamf.ms.insur.admin.pojo.po.UserPost;
import com.cfpamf.ms.insur.admin.pojo.vo.SmCommissionSettingVO;
import com.cfpamf.ms.insur.admin.service.SmProductHistoryService;
import com.cfpamf.ms.insur.admin.service.SmProductService;
import com.cfpamf.ms.insur.admin.service.UserPostService;
import com.cfpamf.ms.insur.admin.service.sys.SysNotifyService;
import com.cfpamf.ms.insur.base.constant.ChannelConstant;
import com.cfpamf.ms.insur.base.controller.ApplicationTest;
import com.cfpamf.ms.insur.base.exception.BizException;
import com.cfpamf.ms.insur.base.exception.ExcptEnum;
import com.cfpamf.ms.insur.base.util.HttpRequestUtil;
import com.cfpamf.ms.insur.base.util.LocalDateUtil;
import com.cfpamf.ms.insur.weixin.dao.safes.PolicyMapper;
import com.cfpamf.ms.insur.weixin.pojo.dto.product.DutyFactorFlowDTO;
import com.cfpamf.ms.insur.weixin.pojo.dto.product.PlanDTO;
import com.cfpamf.ms.insur.weixin.pojo.dto.product.ProductDTO;
import com.cfpamf.ms.insur.weixin.pojo.vo.WxSessionVO;
import com.cfpamf.ms.insur.weixin.pojo.vo.apply.common.*;
import com.cfpamf.ms.insur.weixin.pojo.vo.apply.zhongan.pay.Commodity;
import com.cfpamf.ms.insur.weixin.pojo.vo.apply.zhongan.pay.PaymentReq;
import com.cfpamf.ms.insur.weixin.pojo.vo.product.*;
import com.cfpamf.ms.insur.weixin.service.policy.PolicyService;
import com.cfpamf.ms.insur.weixin.service.policy.ServiceConfig;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.jsonzou.jmockdata.JMockData;
import com.github.jsonzou.jmockdata.TypeReference;
import org.apache.commons.lang3.StringUtils;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@RunWith(PowerMockRunner.class)
@PrepareForTest({HttpRequestUtil.class})
public class PolicyServiceTest extends ApplicationTest {

    @InjectMocks
    private SmProductHistoryService historyService;

    @InjectMocks
    private PolicyService policyService;

    @InjectMocks
    private ZaOrderServiceAdapter adapter;

    @InjectMocks
    UserPostService userPostService;

    @Mock
    private UserPostMapper userPostMapper;

    @Mock
    private AuthUserMapper userMapper;

    @Mock
    private OrgMapper orgMapper;

    @Mock
    private SysNotifyService notifyService;

    @Mock
    private SmProductService productService;

    @Mock
    private PolicyMapper policyMapper;

    @Mock
    private SmCommissionMapper commissionMapper;

    @Mock
    SmOrderMapper orderMapper;

    @Mock
    SmOrderItemMapper orderItemMapper;

    @Mock
    SmOrderExtendZaMapper extendZaMapper;

    @Mock
    ZaApiService apiService;

    @Mock
    ObjectMapper jsonMapper;

    private Integer productId = 288;

    private String region = "area";

    /**
     * 投保-查询产品基本信息
     */
    @Test
    public void queryProductInfo(){
        ProductDTO productDTO= policyMapper.getProductDetailById(productId);
        if (productDTO == null) {
            throw new BizException(ExcptEnum.PRODUCT_NOT_ONLINE_201001);
        }
        List<PlanVo> plans = policyService.convert2Vo(policyService.getPlans(productId, region));
        List<CoverageVo> coverages = policyService.buildCoverage(productId);
        plans.forEach(p->{
            p.setCoverages(coverages);
        });
        ProductDetailVo detail = ProductDetailVo.build(productDTO);
        detail.setPlans(plans);
        detail.setPolicyHolderCondition(policyService.buildQuoteLimitText(productId));
        Assert.assertNotNull("获取产品信息失败",detail);
    }

    /**
     * 查询投保告知
     * @return
     */
    @Test
    public void queryNotice(){
    }

    /**
     * 查询产品-投保须知，常见问题，保险条款
     * @return
     */
    @Test
    public void queryExtend(){
        ProductExtendVo vo= new ProductExtendVo();
        ProductDTO productDTO= policyMapper.getProductDetailById(productId);
        if(productDTO!=null){
            vo.setAttentions(productDTO.getAttentions());
            vo.setProductNotice(productDTO.getGlProductNotice());
        }
        ClauseMasterVo clause = new ClauseMasterVo();
        clause.setAttach(policyMapper.queryProductClausesByProductId(productId));
        String content = policyMapper.getProductClauseContent(productId);
        clause.setContent(content);
        vo.setClause(clause);
        Assert.assertNotNull("获取产品信息失败",vo);
    }

    @Test
    public void buildCoverage() {
        policyService.buildCoverage(productId);
        Assert.assertTrue("",true);
    }

    @Test
    public void convertFlow2Map(){
        TypeReference<List<DutyFactorFlowDTO>> tr = new TypeReference<List<DutyFactorFlowDTO>>() {
        };
        List<DutyFactorFlowDTO> flows = JMockData.mock(tr);
        policyService.convertFlow2Map(flows);
        Assert.assertTrue("",true);
    }

    @Test
    public void convert2Vo() {
        TypeReference<List<PlanDTO>> tr = new TypeReference<List<PlanDTO>>() {
        };
        List<PlanDTO> flows = JMockData.mock(tr);
        policyService.convert2Vo(flows);
        Assert.assertTrue("",true);
    }

    @Test
    public void getCommition(){
        TypeReference<List<Integer>> tr = new TypeReference<List<Integer>>() {
        };
        List<Integer> planIds = JMockData.mock(tr);
        List<SmCommissionSettingVO> data =  commissionMapper.getCommissionSettingByPlanIds(planIds, new Date());
        if(data!=null){
            data.stream().collect(Collectors.toMap(SmCommissionSettingVO::getPlanId, Function.identity()));
        }
        Assert.assertTrue("",true);
    }

    @Test
    public void underwriting(){
        GroupUnderwriting req = JMockData.mock(GroupUnderwriting.class);
    }

    @Test
    public void underwriting2(){
        GroupUnderwriting req = JMockData.mock(GroupUnderwriting.class);
        req.init();
        /**
         * ☆设置推荐人信息
         */
        WxSessionVO session = JMockData.mock(WxSessionVO.class);
        if (session.getAgentId() != null) {
//            req.setAgentId(session.getAgentId());
        }
//        req.setJobCode(session.getJobCode());
        req.setSubChannel(ChannelConstant.ORDER_CHANNEL_XIANGZHU);
        if (!StringUtils.isBlank(session.getUserId())) {
//            req.setRecommendId(session.getUserId());
//            req.setRecommendOrgCode(session.getOrgCode());
//            req.setRecommendMainJobNumber(session.getMainJobNumber());
        }
        if (StringUtils.isNotBlank(req.getBizCode())) {
            String jobNumberByBizCode = userMapper.getMainJobNumberByBizCode(req.getBizCode());
            if (StringUtils.isEmpty(jobNumberByBizCode)) {
                throw new BizException(ExcptEnum.PARAMS_ERROR.getCode(), "推荐码不存在！");
            }
//            req.setRecommendId(jobNumberByBizCode);
        }
        req.setSubmitTime(LocalDateUtil.formatNow4Simple());
        req.setChannel(EnumChannel.ZA.getCode());
        Agent agent = req.getAgent();
        if (StringUtils.isNotBlank(agent.getRecommendId())) {
            TypeReference<List<UserPost>> tr = new TypeReference<List<UserPost>>(){};
                List<UserPost> postList =  JMockData.mock(tr);
                UserPost userPost;
                if (postList!=null&&postList.size()>0) {
                    Optional<UserPost> op = postList.stream().filter(p -> Objects.equals(agent.getJobCode(), p.getJobCode())).findFirst();
                    if (op.isPresent()) {
                        userPost = op.get();
                    } else {
                        userPost = postList.get(0);
                    }
                    agent.setRecommendJobCode(userPost.getJobCode());
                    agent.setRecommendMainJobNumber(userPost.getMainJobNumber());
                    agent.setRecommendOrgCode(userPost.getOrgCode());
                    agent.setCustomerAdminJobCode(userPost.getJobCode());
                    agent.setCustomerAdminMainJobNumber(userPost.getMainJobNumber());
                    agent.setCustomerAdminOrgCode(userPost.getOrgCode());
                    agent.setRecommendMasterName(userPost.getUserMasterName());
                    agent.setRecommendAdminName(userPost.getUserAdminName());
                    agent.setRecommendEntryDate(userPost.getEntryDate());
                    agent.setRecommendPostName(userPost.getPostName());

                }
        }
    }

    @Test
    public void getPayHtml(){
        PaymentDTO payment = orderMapper.queryPayment("ZA21090702641133P");
        if(payment!=null){
            BigDecimal amount = payment.getTotalAmount();
            PaymentReq req = new PaymentReq();
            ZaApiProperties prop = apiService.getZaApiProperties();
            req.setOutTradeNo("abc");
            req.setOrderAmt(String.valueOf(amount));
            req.setPayChannel(prop.getPayChannel().split(","));
            req.setSignType("MD5");
            req.setRequestCharset("UTF-8");
            req.setTradeType("H5");
            req.setSrcType(prop.getSrcType());
            req.setOrderType(prop.getOrderType());
            req.setShowUrl(prop.getShowUrl());
            req.setNotifyUrl(prop.getNotifyUrl());
            req.setMerchantCode(prop.getMerchantCode());

            Commodity commodity = new Commodity();
            commodity.setSubject(prop.getCommoditySubject());
            commodity.setPrice(amount+"元");
            req.setCommodity(commodity);
        }
        Assert.assertTrue("获取支付信息",true);
    }

    @Test
    public void seeFeePayChannel() {
        OrderPrePayRequest req = JMockData.mock(OrderPrePayRequest.class);
        adapter.seeFeePayChannel(req);
        Assert.assertTrue("获取支付信息",true);
    }

    @Test
    public void endor(){
    }

    @Test
    public void endorCalPremium() {
        GroupEndorsement req = JMockData.mock(GroupEndorsement.class);
        req.setTotalPremium(new BigDecimal("200"));
//        GroupEndorResponse resp = adapter.endorCalPremium(req);
//        System.out.print(resp);
        Assert.assertTrue("Throw Some Error",true);
        GroupEndorResponse resp = null;
        try {
            resp = adapter.endorCalPremium(req);
        } catch (Exception e) {
            System.out.print(resp);
            Assert.assertTrue("Throw Some Error",true);
        }

    }

    @Test
    public void queryEPolicyUrl(){

        String policyNo = "HA1100001435005305";
        String url = adapter.queryEPolicyUrl(policyNo,null);
        Assert.assertTrue("Some Error",true);
    }

}
