package com.cfpamf.ms.insur.base.util;

import org.junit.FixMethodOrder;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.MethodSorters;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.math.BigDecimal;
import java.util.Collections;

/*@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("dev")
@FixMethodOrder(MethodSorters.NAME_ASCENDING)*/
public class SmsSenderUtilTest {

    /*@Autowired
    private SmsSenderUtil senderUtil;

    @Test
    public void sendUwsMessage() {
        try {
            senderUtil.sendUwsHcMessage(Collections.singletonList("15574307649"), SmsSenderUtil.SmsUwsHcDTO.builder()
                    .personName("张娜义")
                    .downloadUrl("http://ptn.cic.cn/")
                    .orderAmount(new BigDecimal(100))
                    .policyNo("01201915020800191400084978")
                    .productName("中和助农保险")
                    .contactName("张娜义")
                    .contactMobile("15574307649")
                    .build()
            );
            System.out.println();
        } catch (Exception e) {

        }
    }*/
}