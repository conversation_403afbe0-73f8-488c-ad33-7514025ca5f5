package com.cfpamf.ms.insur.admin.service.correct;

import com.cfpamf.ms.insur.admin.dao.safes.AuthUserMapper;
import com.cfpamf.ms.insur.admin.dao.safes.SmOrderMapper;
import com.cfpamf.ms.insur.admin.dao.safes.UserPostMapper;
import com.cfpamf.ms.insur.admin.dao.safes.order.SmOrderInsuredMapper;
import com.cfpamf.ms.insur.admin.dao.safes.order.SmOrderWhaleCorrectNotifyMapper;
import com.cfpamf.ms.insur.admin.enums.correct.EnumWhaleCorrectProject;
import com.cfpamf.ms.insur.admin.external.whale.WhaleOrderServiceAdapterImpl;
import com.cfpamf.ms.insur.admin.external.whale.model.OpenPreservationCustomerManagerChangeVo;
import com.cfpamf.ms.insur.admin.external.whale.model.PreservationDetail;
import com.cfpamf.ms.insur.admin.pojo.dto.SmOrderCorrectDTO;
import com.cfpamf.ms.insur.admin.pojo.dto.SmSaveOrderCorrectDTO;
import com.cfpamf.ms.insur.admin.pojo.po.SmOrder;
import com.cfpamf.ms.insur.admin.pojo.po.SmOrderInsured;
import com.cfpamf.ms.insur.admin.pojo.po.UserPost;
import com.cfpamf.ms.insur.admin.pojo.vo.SmOrderToCorrectVO;
import com.cfpamf.ms.insur.admin.service.BaseTest;
import com.cfpamf.ms.insur.admin.service.SmOrderManageService;
import com.cfpamf.ms.insur.admin.service.UserPostService;
import com.cfpamf.ms.insur.base.exception.BizException;
import com.cfpamf.ms.insur.base.util.HttpRequestUtil;
import com.cfpamf.ms.insur.weixin.pojo.vo.WxUserVO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.time.LocalDateTime;
import java.util.*;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(PowerMockRunner.class)
@PrepareForTest({HttpRequestUtil.class})
public class CustomerManagerChangeCorrectServiceTest extends BaseTest {

    @Mock private SmOrderMapper mockOrderMapper;
    @Mock private SmOrderInsuredMapper mockInsuredMapper;
    @Mock private SmOrderManageService mockOrderManageService;
    @Mock private AuthUserMapper mockAuthUserMapper;
    @Mock private UserPostMapper mockUserPostMapper;
    @Mock private UserPostService mockUserPostService;
    @Mock private WhaleOrderServiceAdapterImpl adapter;
    @Mock private SmOrderWhaleCorrectNotifyMapper smOrderWhaleCorrectNotifyMapper;

    @InjectMocks private CustomerManagerChangeCorrectService customerManagerChangeCorrectServiceUnderTest;

    @Test
    public void testQueryCorrectContextParams() {
        // Setup
        final PreservationDetail expectedResult = new PreservationDetail();
        expectedResult.setContractCode("contractCode");
        expectedResult.setPolicyCode("policyCode");
        expectedResult.setPreservationType("preservationType");
        expectedResult.setCustomerManagerChannelCode("customerManagerChannelCode");
        expectedResult.setPolicyProductType("policyProductType");
        final OpenPreservationCustomerManagerChangeVo customerManagerChangeVo = new OpenPreservationCustomerManagerChangeVo();
        customerManagerChangeVo.setChannelCode("channelCode");
        customerManagerChangeVo.setCustomerManagerChannelCode("customerManagerChannelCode");
        customerManagerChangeVo.setCustomerManagerChannelOrgCode("customerManagerChannelOrgCode");
        expectedResult.setCustomerManagerChangeVo(customerManagerChangeVo);

        // Run the test
        final PreservationDetail result = customerManagerChangeCorrectServiceUnderTest.queryCorrectContextParams("preservationCode");

        // Verify the results
//        assertEquals(expectedResult, result);
    }

    @Test
    public void testDoCorrect() {
        // Setup
        final PreservationDetail preservationDetail = new PreservationDetail();
        preservationDetail.setContractCode("contractCode");
        preservationDetail.setPolicyCode("policyCode");
        preservationDetail.setPreservationType("preservationType");
        preservationDetail.setCustomerManagerChannelCode("customerManagerChannelCode");
        preservationDetail.setPolicyProductType("policyProductType");
        final OpenPreservationCustomerManagerChangeVo customerManagerChangeVo = new OpenPreservationCustomerManagerChangeVo();
        customerManagerChangeVo.setChannelCode("zhnx");
        customerManagerChangeVo.setCustomerManagerChannelCode("customerManagerChannelCode");
        customerManagerChangeVo.setCustomerManagerChannelOrgCode("customerManagerChannelOrgCode");
        preservationDetail.setCustomerManagerChangeVo(customerManagerChangeVo);

        // Configure SmOrderInsuredMapper.selectByPolicyNoList(...).
        final SmOrderInsured orderInsured = new SmOrderInsured();
        orderInsured.setId(0);
        orderInsured.setEndorId("endorId");
        orderInsured.setFhOrderId("fhOrderId");
        orderInsured.setAppStatus("appStatus");
        orderInsured.setPolicyNo("policyNo");
        final List<SmOrderInsured> insureds = Arrays.asList(orderInsured);
        when(mockInsuredMapper.selectByPolicyNoList(Arrays.asList("value"))).thenReturn(insureds);

        // Configure SmOrderMapper.listSmOrderByOrderIds(...).
        final SmOrder order = new SmOrder();
        order.setId(0);
        order.setFhOrderId("fhOrderId");
        order.setProductId(0);
        order.setRecommendId("recommendId");
        order.setPaymentTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<SmOrder> smOrders = Arrays.asList(order);
        when(mockOrderMapper.listSmOrderByOrderIds(Arrays.asList("value"))).thenReturn(smOrders);

        // Configure AuthUserMapper.getUserByUserId(...).
        final WxUserVO wxUserVO = new WxUserVO();
        wxUserVO.setId(0);
        wxUserVO.setOrgPath("orgPath");
        wxUserVO.setRegionName("regionName");
        wxUserVO.setBranchRegionName("branchRegionName");
        wxUserVO.setMainJobNumber("mainJobNumber");
        when(mockAuthUserMapper.getUserByUserId("customerManagerChannelCode")).thenReturn(wxUserVO);

        // Configure UserPostService.listUserPostByJobNumber(...).
        final UserPost userPost = new UserPost();
        userPost.setJobCode("jobCode");
        userPost.setMainJobNumber("mainJobNumber");
        userPost.setJobNumber("jobNumber");
        userPost.setOrgCode("newRecommendOrgCode");
        userPost.setServiceType(0);
        final List<UserPost> userPosts = Arrays.asList(userPost);
        when(mockUserPostService.listUserPostByJobNumber("customerManagerChannelCode")).thenReturn(userPosts);

        // Run the test
        try{
            customerManagerChangeCorrectServiceUnderTest.doCorrect(preservationDetail);
        }catch(Exception e) {

        }

        // Verify the results
        // Confirm SmOrderManageService.correctRecommend(...).
        final SmOrderCorrectDTO dto = new SmOrderCorrectDTO();
        dto.setPolicyNo("policyNo");
        dto.setFieldCode("code");
        dto.setOldValue("customerManagerChannelCode");
        dto.setNewValue("customerManagerChannelCode");
        dto.setIdNumber("idNumber");
        dto.setFhOrderId("fhOrderId");
        dto.setNewRecommendMainJobNumber("mainJobNumber");
        dto.setNewRecommendOrgCode("newRecommendOrgCode");
        final SmOrderToCorrectVO toCorrectVO = new SmOrderToCorrectVO();
        toCorrectVO.setFhOrderId("fhOrderId");
        toCorrectVO.setRecommendId("recommendId");
        toCorrectVO.setPolicyNo("policyNo");
        toCorrectVO.setAppStatus("1");
        toCorrectVO.setPaymentTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
//        verify(mockOrderManageService).correctRecommend(dto, toCorrectVO);

        // Confirm SmOrderMapper.updateOrderErrorRecommendInfo(...).
        final SmOrderCorrectDTO dto1 = new SmOrderCorrectDTO();
        dto1.setPolicyNo("policyNo");
        dto1.setFieldCode("code");
        dto1.setOldValue("customerManagerChannelCode");
        dto1.setNewValue("customerManagerChannelCode");
        dto1.setIdNumber("idNumber");
        dto1.setFhOrderId("fhOrderId");
        dto1.setNewRecommendMainJobNumber("mainJobNumber");
        dto1.setNewRecommendOrgCode("newRecommendOrgCode");
//        verify(mockOrderMapper).updateOrderErrorRecommendInfo(dto1);

        // Confirm SmOrderMapper.updateOrderErrorCommissionInfo(...).
        final SmOrderCorrectDTO dto2 = new SmOrderCorrectDTO();
        dto2.setPolicyNo("policyNo");
        dto2.setFieldCode("code");
        dto2.setOldValue("customerManagerChannelCode");
        dto2.setNewValue("customerManagerChannelCode");
        dto2.setIdNumber("idNumber");
        dto2.setFhOrderId("fhOrderId");
        dto2.setNewRecommendMainJobNumber("mainJobNumber");
        dto2.setNewRecommendOrgCode("newRecommendOrgCode");
//        verify(mockOrderMapper).updateOrderErrorCommissionInfo(dto2);

        // Confirm SmOrderManageService.updateNewCommissionInfo(...).
        final SmOrderCorrectDTO dto3 = new SmOrderCorrectDTO();
        dto3.setPolicyNo("policyNo");
        dto3.setFieldCode("code");
        dto3.setOldValue("customerManagerChannelCode");
        dto3.setNewValue("customerManagerChannelCode");
        dto3.setIdNumber("idNumber");
        dto3.setFhOrderId("fhOrderId");
        dto3.setNewRecommendMainJobNumber("mainJobNumber");
        dto3.setNewRecommendOrgCode("newRecommendOrgCode");
//        verify(mockOrderManageService).updateNewCommissionInfo(dto3);

        // Confirm SmOrderMapper.insertOrderCorrect(...).
        final SmSaveOrderCorrectDTO dto4 = new SmSaveOrderCorrectDTO();
        dto4.setFhOrderId("fhOrderId");
        dto4.setPolicyNo("policyNo");
        dto4.setFieldCode("code");
        dto4.setFieldName("name");
        dto4.setIdNumber("idNumber");
        dto4.setOldValue("customerManagerChannelCode");
        dto4.setNewValue("customerManagerChannelCode");
        dto4.setUpdateBy("SYSTERM");
//        verify(mockOrderMapper).insertOrderCorrect(dto4);
    }

    @Test(expected = BizException.class)
    public void testDoCorrect_SmOrderInsuredMapperReturnsNoItems() {
        // Setup
        final PreservationDetail preservationDetail = new PreservationDetail();
        preservationDetail.setContractCode("contractCode");
        preservationDetail.setPolicyCode("policyCode");
        preservationDetail.setPreservationType("preservationType");
        preservationDetail.setCustomerManagerChannelCode("customerManagerChannelCode");
        preservationDetail.setPolicyProductType("policyProductType");
        final OpenPreservationCustomerManagerChangeVo customerManagerChangeVo = new OpenPreservationCustomerManagerChangeVo();
        customerManagerChangeVo.setChannelCode("channelCode");
        customerManagerChangeVo.setCustomerManagerChannelCode("customerManagerChannelCode");
        customerManagerChangeVo.setCustomerManagerChannelOrgCode("customerManagerChannelOrgCode");
        preservationDetail.setCustomerManagerChangeVo(customerManagerChangeVo);

        when(mockInsuredMapper.selectByPolicyNoList(Arrays.asList("value"))).thenReturn(Collections.emptyList());

        // Run the test
        customerManagerChangeCorrectServiceUnderTest.doCorrect(preservationDetail);
    }

    @Test(expected = BizException.class)
    public void testDoCorrect_SmOrderMapperListSmOrderByOrderIdsReturnsNoItems() {
        // Setup
        final PreservationDetail preservationDetail = new PreservationDetail();
        preservationDetail.setContractCode("contractCode");
        preservationDetail.setPolicyCode("policyCode");
        preservationDetail.setPreservationType("preservationType");
        preservationDetail.setCustomerManagerChannelCode("customerManagerChannelCode");
        preservationDetail.setPolicyProductType("policyProductType");
        final OpenPreservationCustomerManagerChangeVo customerManagerChangeVo = new OpenPreservationCustomerManagerChangeVo();
        customerManagerChangeVo.setChannelCode("channelCode");
        customerManagerChangeVo.setCustomerManagerChannelCode("customerManagerChannelCode");
        customerManagerChangeVo.setCustomerManagerChannelOrgCode("customerManagerChannelOrgCode");
        preservationDetail.setCustomerManagerChangeVo(customerManagerChangeVo);

        // Configure SmOrderInsuredMapper.selectByPolicyNoList(...).
        final SmOrderInsured orderInsured = new SmOrderInsured();
        orderInsured.setId(0);
        orderInsured.setEndorId("endorId");
        orderInsured.setFhOrderId("fhOrderId");
        orderInsured.setAppStatus("appStatus");
        orderInsured.setPolicyNo("policyNo");
        final List<SmOrderInsured> insureds = Arrays.asList(orderInsured);
        when(mockInsuredMapper.selectByPolicyNoList(Arrays.asList("value"))).thenReturn(insureds);

        when(mockOrderMapper.listSmOrderByOrderIds(Arrays.asList("value"))).thenReturn(Collections.emptyList());

        // Run the test
        customerManagerChangeCorrectServiceUnderTest.doCorrect(preservationDetail);
    }

    @Test(expected = BizException.class)
    public void testDoCorrect_UserPostServiceReturnsNoItems() {
        // Setup
        final PreservationDetail preservationDetail = new PreservationDetail();
        preservationDetail.setContractCode("contractCode");
        preservationDetail.setPolicyCode("policyCode");
        preservationDetail.setPreservationType("preservationType");
        preservationDetail.setCustomerManagerChannelCode("customerManagerChannelCode");
        preservationDetail.setPolicyProductType("policyProductType");
        final OpenPreservationCustomerManagerChangeVo customerManagerChangeVo = new OpenPreservationCustomerManagerChangeVo();
        customerManagerChangeVo.setChannelCode("channelCode");
        customerManagerChangeVo.setCustomerManagerChannelCode("customerManagerChannelCode");
        customerManagerChangeVo.setCustomerManagerChannelOrgCode("customerManagerChannelOrgCode");
        preservationDetail.setCustomerManagerChangeVo(customerManagerChangeVo);

        // Configure SmOrderInsuredMapper.selectByPolicyNoList(...).
        final SmOrderInsured orderInsured = new SmOrderInsured();
        orderInsured.setId(0);
        orderInsured.setEndorId("endorId");
        orderInsured.setFhOrderId("fhOrderId");
        orderInsured.setAppStatus("appStatus");
        orderInsured.setPolicyNo("policyNo");
        final List<SmOrderInsured> insureds = Arrays.asList(orderInsured);
        when(mockInsuredMapper.selectByPolicyNoList(Arrays.asList("value"))).thenReturn(insureds);

        // Configure SmOrderMapper.listSmOrderByOrderIds(...).
        final SmOrder order = new SmOrder();
        order.setId(0);
        order.setFhOrderId("fhOrderId");
        order.setProductId(0);
        order.setRecommendId("recommendId");
        order.setPaymentTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<SmOrder> smOrders = Arrays.asList(order);
        when(mockOrderMapper.listSmOrderByOrderIds(Arrays.asList("value"))).thenReturn(smOrders);

        // Configure AuthUserMapper.getUserByUserId(...).
        final WxUserVO wxUserVO = new WxUserVO();
        wxUserVO.setId(0);
        wxUserVO.setOrgPath("orgPath");
        wxUserVO.setRegionName("regionName");
        wxUserVO.setBranchRegionName("branchRegionName");
        wxUserVO.setMainJobNumber("mainJobNumber");
        when(mockAuthUserMapper.getUserByUserId("customerManagerChannelCode")).thenReturn(wxUserVO);

        when(mockUserPostService.listUserPostByJobNumber("customerManagerChannelCode")).thenReturn(Collections.emptyList());

        // Run the test
        customerManagerChangeCorrectServiceUnderTest.doCorrect(preservationDetail);
    }

    @Test
    public void testCorrectProject() {
        assertEquals(EnumWhaleCorrectProject.CUSTOMER_MANAGER_CHANGE.getCode(), customerManagerChangeCorrectServiceUnderTest.correctProject());
    }
}
