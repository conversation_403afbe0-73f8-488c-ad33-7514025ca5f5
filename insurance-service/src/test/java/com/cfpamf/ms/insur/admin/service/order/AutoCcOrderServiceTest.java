package com.cfpamf.ms.insur.admin.service.order;

import com.cfpamf.ms.insur.admin.config.ProductProperties;
import com.cfpamf.ms.insur.admin.config.ProductSpecialRuleProperties;
import com.cfpamf.ms.insur.admin.dao.safes.*;
import com.cfpamf.ms.insur.admin.dao.safes.auto.*;
import com.cfpamf.ms.insur.admin.dao.safes.order.*;
import com.cfpamf.ms.insur.admin.enums.EnumChannel;
import com.cfpamf.ms.insur.admin.external.ChannelOrderService;
import com.cfpamf.ms.insur.admin.external.OrderSubmitRequest;
import com.cfpamf.ms.insur.admin.external.auto.AutoCvtHelper;
import com.cfpamf.ms.insur.admin.external.auto.cc.AutoCcOrderServiceAdapterImpl;
import com.cfpamf.ms.insur.admin.external.auto.cc.model.AutoCcOrder;
import com.cfpamf.ms.insur.admin.external.auto.cc.model.AutoCcUser;
import com.cfpamf.ms.insur.admin.external.auto.cc.model.UrlModel;
import com.cfpamf.ms.insur.admin.external.fh.dto.*;
import com.cfpamf.ms.insur.admin.pojo.dto.SmCreateOrderSubmitRequest;
import com.cfpamf.ms.insur.admin.pojo.dto.auto.AutoOrderDTO;
import com.cfpamf.ms.insur.admin.pojo.dto.auto.AutoOrderPolicyDTO;
import com.cfpamf.ms.insur.admin.pojo.dto.order.SmOrderCarInfoDTO;
import com.cfpamf.ms.insur.admin.pojo.dto.order.SmOrderHouseDTO;
import com.cfpamf.ms.insur.admin.pojo.form.product.TestPremiumDutyForm;
import com.cfpamf.ms.insur.admin.pojo.form.product.TestPremiumProductForm;
import com.cfpamf.ms.insur.admin.pojo.po.auto.order.*;
import com.cfpamf.ms.insur.admin.pojo.po.order.extend.SmOrderExtendCc;
import com.cfpamf.ms.insur.admin.renewal.service.OrderRenewalRecordService;
import com.cfpamf.ms.insur.admin.service.*;
import com.cfpamf.ms.insur.base.event.EventBusEngine;
import com.cfpamf.ms.insur.base.infeface.CustomerCenterService;
import com.cfpamf.ms.insur.base.service.BusinessTokenService;
import com.cfpamf.ms.insur.base.util.HttpRequestUtil;
import com.cfpamf.ms.insur.common.service.monitor.BusinessMonitorService;
import com.cfpamf.ms.insur.weixin.dao.safes.EndorMapper;
import com.cfpamf.ms.pay.facade.config.PayFacadeConfigProperties;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.slf4j.Logger;
import org.springframework.aop.framework.AopContext;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;

import java.math.BigDecimal;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(PowerMockRunner.class)
@PrepareForTest({HttpRequestUtil.class,AopContext.class})
public class AutoCcOrderServiceTest extends BaseTest {

    @Mock private AutoCcOrderServiceAdapterImpl mockAdapter;
    @Mock private AutoOrderExtendMapper mockAutoOrderExtendMapper;
    @Mock private SmOrderExtendCcMapper mockExtendCcMapper;
    @Mock private AutoCvtHelper mockAutoCvtHelper;
    @Mock private AutoOrderMapper mockAutoOrderMapper;
    @Mock private AutoOrderPolicyMapper mockAutoOrderPolicyMapper;
    @Mock private AutoOrderPersonMapper mockAutoOrderPersonMapper;
    @Mock private AutoOrderCarMapper mockAutoOrderCarMapper;
    @Mock private AutoOrderReceiverMapper mockAutoOrderReceiverMapper;
    @Mock private AutoOrderRiskMapper mockAutoOrderRiskMapper;
    private int YEAR_NODE;
    private DateTimeFormatter FMT_PARSE;
    private DateTimeFormatter FMT_SUB;
    @Mock private BusinessMonitorService monitorService;
    @Mock private SmProductService productService;
    @Mock private BusinessTokenService tokenService;
    @Mock private CustomerCenterService ccService;
    @Mock private SmCommissionMapper cmsMapper;
    @Mock private SmOrderMapper orderMapper;
    @Mock private SmOrderItemMapper orderItemMapper;
    @Mock private SmProductFormBuyLimitMapper formBuyLimitMapper;
    @Mock private AuthUserMapper userMapper;
    @Mock private OrderRenewalRecordService orderRenewalRecordService;
    @Mock private OrderCoreService orderCoreService;
    @Mock private SmChannelCallbackMapper channelCallbackMapper;
    @Mock private EventBusEngine busEngine;
    @Mock private SmOrderCarMapper carMapper;
    @Mock private SmOrderHouseMapper houseMapper;
    @Mock private SmOrderGroupNotifyService smOrderGroupNotifyService;
    @Mock private SmCommonSettingService commonSettingService;
    @Mock private SmOrderItemMapper smOrderItemMapper;
    @Mock private SmOrderRenewBindService smOrderRenewBindService;
    @Mock private SmXjxhService smXjxhService;
    @Mock private SmOrderPolicyMapper smOrderPolicyMapper;
    @Mock private ChOrderPersonalNotifyService chOrderPersonalNotifyService;
    @Mock private SmOrderRiskDutyMapper riskDutyMapper;
    @Mock private SmProductMapper productMapper;
    @Mock private SmOrderPolicyService smOrderPolicyService;
    @Mock private RenewalManagerService renewalManagerService;
    @Mock private SmOrderInsuredMapper insuredMapper;
    private long tokenLockTime;
    private int phoneLimit;
    @Mock private ProductProperties properties;
    @Mock private ProductSpecialRuleProperties productSpecialRuleProperties;
    @Mock private EndorMapper paymentMapper;
    @Mock private PayFacadeConfigProperties payFacadeConfigProperties;
    @Mock private TempOrderNewProductMapper tempOrderNewProductMapper;
    @Mock private Logger log;

    @InjectMocks private AutoCcOrderService autoCcOrderServiceUnderTest;

    @Test
    public void testOrderService() {
        // Setup
        // Run the test
        final ChannelOrderService result = autoCcOrderServiceUnderTest.orderService();

        // Verify the results
    }

    @Test
    public void testSupport() {
        assertFalse(autoCcOrderServiceUnderTest.support("channel"));
    }

    @Test
    public void testChannel() {
        assertEquals(EnumChannel.AUTO_CC.getCode(), autoCcOrderServiceUnderTest.channel());
    }

    @Test
    public void testGetJumpUrl() {
        // Setup
        when(mockAdapter.genJumpUrl("channelUserId", "phone", "name")).thenReturn("result");

        // Run the test
        final String result = autoCcOrderServiceUnderTest.getJumpUrl("channelUserId", "phone", "name");

        // Verify the results
        assertEquals("result", result);
    }

    @Test
    public void testGetJumpModel() {
        // Setup
        final UrlModel expectedResult = new UrlModel();
        expectedResult.setUrl("url");
        expectedResult.setQueryMap(new HashMap<>());

        // Configure AutoCcOrderServiceAdapter.getJumpModel(...).
        final UrlModel urlModel = new UrlModel();
        urlModel.setUrl("url");
        urlModel.setQueryMap(new HashMap<>());
        when(mockAdapter.getJumpModel("channelUserId", "phone", "name")).thenReturn(urlModel);

        // Run the test
        final UrlModel result = autoCcOrderServiceUnderTest.getJumpModel("channelUserId", "phone", "name");

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testOrderSync() throws Exception {
        // Setup
        final MockHttpServletRequest request = new MockHttpServletRequest();
        final MockHttpServletResponse response = new MockHttpServletResponse();

        // Configure AutoCcOrderServiceAdapter.parse(...).
        final AutoCcOrder autoCcOrder = new AutoCcOrder();
        autoCcOrder.setUid("uid");
        autoCcOrder.setOrderNo("appNo");
        autoCcOrder.setStatus("status");
        final AutoCcUser user = new AutoCcUser();
        user.setMobile("mobile");
        autoCcOrder.setUser(user);
        when(mockAdapter.parse("str")).thenReturn(autoCcOrder);

        // Configure SmOrderExtendCcMapper.selectLastSuccess(...).
        final SmOrderExtendCc smOrderExtendCc = new SmOrderExtendCc();
        smOrderExtendCc.setFhOrderId("fhOrderId");
        smOrderExtendCc.setBatchNo("batchNo");
        smOrderExtendCc.setCcOrderNo("appNo");
        smOrderExtendCc.setStatus("status");
        smOrderExtendCc.setNotifyContent("str");
        smOrderExtendCc.setState(0);
        when(mockExtendCcMapper.selectLastSuccess("appNo")).thenReturn(smOrderExtendCc);

        // Configure AutoCcOrderServiceAdapter.cvtByNotify(...).
        final AutoOrderDTO autoOrderDTO = new AutoOrderDTO();
        autoOrderDTO.setOrderNo("fhOrderId");
        autoOrderDTO.setPlanCode("planCode");
        final AutoOrder order = new AutoOrder();
        order.setAppNo("appNo");
        order.setProductId(0);
        order.setPlanId(0);
        order.setPayStatus("-1");
        autoOrderDTO.setOrder(order);
        final AutoOrderPerson autoOrderPerson = new AutoOrderPerson();
        autoOrderDTO.setPeople(Arrays.asList(autoOrderPerson));
        final AutoOrderPolicyDTO autoOrderPolicyDTO = new AutoOrderPolicyDTO();
        final AutoOrderPolicy policy = new AutoOrderPolicy();
        policy.setPolicyNo("policyNo");
        policy.setSubPolicyNo("subPolicyNo");
        policy.setPlanId(0);
        policy.setPolicyState("-2");
        policy.setBusinessScore("businessScore");
        autoOrderPolicyDTO.setPolicy(policy);
        final AutoOrderRisk autoOrderRisk = new AutoOrderRisk();
        autoOrderRisk.setRiskName("riskName");
        autoOrderRisk.setPremium(new BigDecimal("0.00"));
        autoOrderRisk.setAmount(new BigDecimal("0.00"));
        autoOrderPolicyDTO.setRiskList(Arrays.asList(autoOrderRisk));
        autoOrderDTO.setPolicies(Arrays.asList(autoOrderPolicyDTO));
        final AutoOrderExtend extend = new AutoOrderExtend();
        extend.setOrderNo("fhOrderId");
        extend.setContent("str");
        autoOrderDTO.setExtend(extend);
        final AutoOrderCar car = new AutoOrderCar();
        car.setOwnerPersonName("ownerPersonName");
        car.setOwnerPersonGender("ownerPersonGender");
        car.setOwnerIdType("ownerIdType");
        car.setOwnerIdNumber("ownerIdNumber");
        car.setFirstRegDate("firstRegDate");
        car.setFullLoad("fullLoad");
        car.setTransferDate("transferDate");
        car.setSeatCnt("approvedNum");
        car.setPrice("price");
        car.setCarUserType("carUserType");
        car.setDisplacement("displacement");
        car.setIsNewCar("isNewCar");
        autoOrderDTO.setCar(car);
        final List<AutoOrderDTO> autoOrderDTOS = Arrays.asList(autoOrderDTO);
        final AutoCcOrder order1 = new AutoCcOrder();
        order1.setUid("uid");
        order1.setOrderNo("appNo");
        order1.setStatus("status");
        final AutoCcUser user1 = new AutoCcUser();
        user1.setMobile("mobile");
        order1.setUser(user1);
        when(mockAdapter.cvtByNotify(order1)).thenReturn(autoOrderDTOS);

        // Configure AutoCvtHelper.cvtOrderInfo(...).
        final OrderSubmitRequest orderSubmitRequest = new OrderSubmitRequest();
        final FhProduct productInfo = new FhProduct();
        productInfo.setProductId("fhProductId");
        productInfo.setRecommendId("recommendId");
        productInfo.setRecommendMainJobNumber("recommendMainJobNumber");
        productInfo.setRecommendOrgCode("recommendOrgCode");
        orderSubmitRequest.setProductInfo(productInfo);
        final FhOrderInfo orderInfo = new FhOrderInfo();
        orderInfo.setSubmitTime("submitTime");
        orderInfo.setStartTime("startTime");
        orderInfo.setEndTime("endTime");
        orderSubmitRequest.setOrderInfo(orderInfo);
        final FhProposer proposerInfo = new FhProposer();
        proposerInfo.setPersonName("insuredName");
        proposerInfo.setPersonGender("insuredPersonGender");
        proposerInfo.setIdType("idType");
        proposerInfo.setIdNumber("idNumber");
        proposerInfo.setBirthday("birthday");
        proposerInfo.setCellPhone("cellPhone");
        proposerInfo.setEmail("email");
        proposerInfo.setAddress("address");
        proposerInfo.setAppStatus("-2");
        proposerInfo.setPolicyNo("insIdNumber");
        proposerInfo.setDownloadURL("downloadUrl");
        orderSubmitRequest.setProposerInfo(proposerInfo);
        final FhInsuredPerson fhInsuredPerson = new FhInsuredPerson();
        fhInsuredPerson.setPersonName("insuredName");
        fhInsuredPerson.setIdType("idType");
        fhInsuredPerson.setIdNumber("idNumber");
        fhInsuredPerson.setBirthday("birthday");
        fhInsuredPerson.setCellPhone("cellPhone");
        fhInsuredPerson.setAppStatus("-2");
        fhInsuredPerson.setPolicyNo("insIdNumber");
        fhInsuredPerson.setRelationship("insuredAppntShipCode");
        fhInsuredPerson.setFlightNo("flightNo");
        fhInsuredPerson.setFlightTime("flightTime");
        fhInsuredPerson.setOccupationCode("");
        fhInsuredPerson.setOccupationGroup("occupationGroup");
        fhInsuredPerson.setDestinationCountryText("destinationCountryText");
        fhInsuredPerson.setAnnualIncome("annualIncome");
        fhInsuredPerson.setStudentType("studentType");
        fhInsuredPerson.setSchoolType("schoolType");
        fhInsuredPerson.setSchoolNature("schoolNature");
        fhInsuredPerson.setSchoolName("schoolName");
        fhInsuredPerson.setSchoolClass("schoolClass");
        fhInsuredPerson.setStudentId("studentId");
        fhInsuredPerson.setIdPeriodStart("idPeriodStart");
        fhInsuredPerson.setIdPeriodEnd("idPeriodEnd");
        fhInsuredPerson.setSmoke("smoke");
        fhInsuredPerson.setOldPolicyNo("oldPolicyNo");
        final TestPremiumDutyForm testPremiumDutyForm = new TestPremiumDutyForm();
        fhInsuredPerson.setDuties(Arrays.asList(testPremiumDutyForm));
        final TestPremiumProductForm product = new TestPremiumProductForm();
        product.setProductId(0);
        product.setPlanId(0);
        product.setPlanCode("planCode");
        product.setPremium(new BigDecimal("0.00"));
        product.setQty(0);
        final TestPremiumDutyForm testPremiumDutyForm1 = new TestPremiumDutyForm();
        product.setPremiumDutyForms(Arrays.asList(testPremiumDutyForm1));
        fhInsuredPerson.setProduct(product);
        orderSubmitRequest.setInsuredPerson(Arrays.asList(fhInsuredPerson));
        final FhProperty propertyInfo = new FhProperty();
        propertyInfo.setPropertyInfoIsExist("0");
        orderSubmitRequest.setPropertyInfo(propertyInfo);
        final SmOrderHouseDTO house = new SmOrderHouseDTO();
        orderSubmitRequest.setHouse(house);
        final SmOrderCarInfoDTO carInfo = new SmOrderCarInfoDTO();
        orderSubmitRequest.setCarInfo(carInfo);
        orderSubmitRequest.setProductType("code");
        orderSubmitRequest.setBizCode("bizCode");
        orderSubmitRequest.setOrderOutType("orderOutType");
        orderSubmitRequest.setToken("token");
        orderSubmitRequest.setPlanId(0);
        orderSubmitRequest.setQty(0);
        orderSubmitRequest.setPreOrderId("preOrderId");
        orderSubmitRequest.setRenew(false);
        orderSubmitRequest.setRealRenewFlag(false);
        orderSubmitRequest.setAppNo("appNo");
        orderSubmitRequest.setAgentId(0);
        orderSubmitRequest.setJobCode("jobCode");
        final SmCreateOrderSubmitRequest smCreateOrderSubmitRequest = new SmCreateOrderSubmitRequest(orderSubmitRequest);
        final AutoOrderDTO orderDTO = new AutoOrderDTO();
        orderDTO.setOrderNo("fhOrderId");
        orderDTO.setPlanCode("planCode");
        final AutoOrder order2 = new AutoOrder();
        order2.setAppNo("appNo");
        order2.setProductId(0);
        order2.setPlanId(0);
        order2.setPayStatus("-1");
        orderDTO.setOrder(order2);
        final AutoOrderPerson autoOrderPerson1 = new AutoOrderPerson();
        orderDTO.setPeople(Arrays.asList(autoOrderPerson1));
        final AutoOrderPolicyDTO autoOrderPolicyDTO1 = new AutoOrderPolicyDTO();
        final AutoOrderPolicy policy1 = new AutoOrderPolicy();
        policy1.setPolicyNo("policyNo");
        policy1.setSubPolicyNo("subPolicyNo");
        policy1.setPlanId(0);
        policy1.setPolicyState("-2");
        policy1.setBusinessScore("businessScore");
        autoOrderPolicyDTO1.setPolicy(policy1);
        final AutoOrderRisk autoOrderRisk1 = new AutoOrderRisk();
        autoOrderRisk1.setRiskName("riskName");
        autoOrderRisk1.setPremium(new BigDecimal("0.00"));
        autoOrderRisk1.setAmount(new BigDecimal("0.00"));
        autoOrderPolicyDTO1.setRiskList(Arrays.asList(autoOrderRisk1));
        orderDTO.setPolicies(Arrays.asList(autoOrderPolicyDTO1));
        final AutoOrderExtend extend1 = new AutoOrderExtend();
        extend1.setOrderNo("fhOrderId");
        extend1.setContent("str");
        orderDTO.setExtend(extend1);
        final AutoOrderCar car1 = new AutoOrderCar();
        car1.setOwnerPersonName("ownerPersonName");
        car1.setOwnerPersonGender("ownerPersonGender");
        car1.setOwnerIdType("ownerIdType");
        car1.setOwnerIdNumber("ownerIdNumber");
        car1.setFirstRegDate("firstRegDate");
        car1.setFullLoad("fullLoad");
        car1.setTransferDate("transferDate");
        car1.setSeatCnt("approvedNum");
        car1.setPrice("price");
        car1.setCarUserType("carUserType");
        car1.setDisplacement("displacement");
        car1.setIsNewCar("isNewCar");
        orderDTO.setCar(car1);
        when(mockAutoCvtHelper.cvtOrderInfo(orderDTO)).thenReturn(smCreateOrderSubmitRequest);
        PowerMockito.mockStatic(AopContext.class);
        when(AopContext.currentProxy()).thenReturn(autoCcOrderServiceUnderTest);
        // Run the test
        autoCcOrderServiceUnderTest.orderSync(request, response);

    }

    @Test
    public void testOrderSync_AutoCcOrderServiceAdapterCvtByNotifyReturnsNoItems() throws Exception {
        // Setup
        final MockHttpServletRequest request = new MockHttpServletRequest();
        final MockHttpServletResponse response = new MockHttpServletResponse();

        // Configure AutoCcOrderServiceAdapter.parse(...).
        final AutoCcOrder autoCcOrder = new AutoCcOrder();
        autoCcOrder.setUid("uid");
        autoCcOrder.setOrderNo("appNo");
        autoCcOrder.setStatus("status");
        final AutoCcUser user = new AutoCcUser();
        user.setMobile("mobile");
        autoCcOrder.setUser(user);
        when(mockAdapter.parse("str")).thenReturn(autoCcOrder);

        // Configure AutoCcOrderServiceAdapter.cvtByNotify(...).
        final AutoCcOrder order = new AutoCcOrder();
        order.setUid("uid");
        order.setOrderNo("appNo");
        order.setStatus("status");
        final AutoCcUser user1 = new AutoCcUser();
        user1.setMobile("mobile");
        order.setUser(user1);
        when(mockAdapter.cvtByNotify(order)).thenReturn(Collections.emptyList());
        PowerMockito.mockStatic(AopContext.class);
        when(AopContext.currentProxy()).thenReturn(autoCcOrderServiceUnderTest);
        // Run the test
        autoCcOrderServiceUnderTest.orderSync(request, response);




    }

    @Test
    public void testSaveDate() {
        // Setup
        final AutoCcOrder order = new AutoCcOrder();
        order.setUid("uid");
        order.setOrderNo("appNo");
        order.setStatus("status");
        final AutoCcUser user = new AutoCcUser();
        user.setMobile("mobile");
        order.setUser(user);

        final SmOrderExtendCc extendCc = new SmOrderExtendCc();
        extendCc.setFhOrderId("fhOrderId");
        extendCc.setBatchNo("batchNo");
        extendCc.setCcOrderNo("appNo");
        extendCc.setStatus("status");
        extendCc.setNotifyContent("str");
        extendCc.setState(0);

        // Configure AutoCcOrderServiceAdapter.cvtByNotify(...).
        final AutoOrderDTO autoOrderDTO = new AutoOrderDTO();
        autoOrderDTO.setOrderNo("fhOrderId");
        autoOrderDTO.setPlanCode("planCode");
        final AutoOrder order1 = new AutoOrder();
        order1.setAppNo("appNo");
        order1.setProductId(0);
        order1.setPlanId(0);
        order1.setPayStatus("-1");
        autoOrderDTO.setOrder(order1);
        final AutoOrderPerson autoOrderPerson = new AutoOrderPerson();
        autoOrderDTO.setPeople(Arrays.asList(autoOrderPerson));
        final AutoOrderPolicyDTO autoOrderPolicyDTO = new AutoOrderPolicyDTO();
        final AutoOrderPolicy policy = new AutoOrderPolicy();
        policy.setPolicyNo("policyNo");
        policy.setSubPolicyNo("subPolicyNo");
        policy.setPlanId(0);
        policy.setPolicyState("-2");
        policy.setBusinessScore("businessScore");
        autoOrderPolicyDTO.setPolicy(policy);
        final AutoOrderRisk autoOrderRisk = new AutoOrderRisk();
        autoOrderRisk.setRiskName("riskName");
        autoOrderRisk.setPremium(new BigDecimal("0.00"));
        autoOrderRisk.setAmount(new BigDecimal("0.00"));
        autoOrderPolicyDTO.setRiskList(Arrays.asList(autoOrderRisk));
        autoOrderDTO.setPolicies(Arrays.asList(autoOrderPolicyDTO));
        final AutoOrderExtend extend = new AutoOrderExtend();
        extend.setOrderNo("fhOrderId");
        extend.setContent("str");
        autoOrderDTO.setExtend(extend);
        final AutoOrderCar car = new AutoOrderCar();
        car.setOwnerPersonName("ownerPersonName");
        car.setOwnerPersonGender("ownerPersonGender");
        car.setOwnerIdType("ownerIdType");
        car.setOwnerIdNumber("ownerIdNumber");
        car.setFirstRegDate("firstRegDate");
        car.setFullLoad("fullLoad");
        car.setTransferDate("transferDate");
        car.setSeatCnt("approvedNum");
        car.setPrice("price");
        car.setCarUserType("carUserType");
        car.setDisplacement("displacement");
        car.setIsNewCar("isNewCar");
        autoOrderDTO.setCar(car);
        final List<AutoOrderDTO> autoOrderDTOS = Arrays.asList(autoOrderDTO);
        final AutoCcOrder order2 = new AutoCcOrder();
        order2.setUid("uid");
        order2.setOrderNo("appNo");
        order2.setStatus("status");
        final AutoCcUser user1 = new AutoCcUser();
        user1.setMobile("mobile");
        order2.setUser(user1);
        when(mockAdapter.cvtByNotify(order2)).thenReturn(autoOrderDTOS);

        // Configure AutoCvtHelper.cvtOrderInfo(...).
        final OrderSubmitRequest orderSubmitRequest = new OrderSubmitRequest();
        final FhProduct productInfo = new FhProduct();
        productInfo.setProductId("fhProductId");
        productInfo.setRecommendId("recommendId");
        productInfo.setRecommendMainJobNumber("recommendMainJobNumber");
        productInfo.setRecommendOrgCode("recommendOrgCode");
        orderSubmitRequest.setProductInfo(productInfo);
        final FhOrderInfo orderInfo = new FhOrderInfo();
        orderInfo.setSubmitTime("submitTime");
        orderInfo.setStartTime("startTime");
        orderInfo.setEndTime("endTime");
        orderSubmitRequest.setOrderInfo(orderInfo);
        final FhProposer proposerInfo = new FhProposer();
        proposerInfo.setPersonName("insuredName");
        proposerInfo.setPersonGender("insuredPersonGender");
        proposerInfo.setIdType("idType");
        proposerInfo.setIdNumber("idNumber");
        proposerInfo.setBirthday("birthday");
        proposerInfo.setCellPhone("cellPhone");
        proposerInfo.setEmail("email");
        proposerInfo.setAddress("address");
        proposerInfo.setAppStatus("-2");
        proposerInfo.setPolicyNo("insIdNumber");
        proposerInfo.setDownloadURL("downloadUrl");
        orderSubmitRequest.setProposerInfo(proposerInfo);
        final FhInsuredPerson fhInsuredPerson = new FhInsuredPerson();
        fhInsuredPerson.setPersonName("insuredName");
        fhInsuredPerson.setIdType("idType");
        fhInsuredPerson.setIdNumber("idNumber");
        fhInsuredPerson.setBirthday("birthday");
        fhInsuredPerson.setCellPhone("cellPhone");
        fhInsuredPerson.setAppStatus("-2");
        fhInsuredPerson.setPolicyNo("insIdNumber");
        fhInsuredPerson.setRelationship("insuredAppntShipCode");
        fhInsuredPerson.setFlightNo("flightNo");
        fhInsuredPerson.setFlightTime("flightTime");
        fhInsuredPerson.setOccupationCode("");
        fhInsuredPerson.setOccupationGroup("occupationGroup");
        fhInsuredPerson.setDestinationCountryText("destinationCountryText");
        fhInsuredPerson.setAnnualIncome("annualIncome");
        fhInsuredPerson.setStudentType("studentType");
        fhInsuredPerson.setSchoolType("schoolType");
        fhInsuredPerson.setSchoolNature("schoolNature");
        fhInsuredPerson.setSchoolName("schoolName");
        fhInsuredPerson.setSchoolClass("schoolClass");
        fhInsuredPerson.setStudentId("studentId");
        fhInsuredPerson.setIdPeriodStart("idPeriodStart");
        fhInsuredPerson.setIdPeriodEnd("idPeriodEnd");
        fhInsuredPerson.setSmoke("smoke");
        fhInsuredPerson.setOldPolicyNo("oldPolicyNo");
        final TestPremiumDutyForm testPremiumDutyForm = new TestPremiumDutyForm();
        fhInsuredPerson.setDuties(Arrays.asList(testPremiumDutyForm));
        final TestPremiumProductForm product = new TestPremiumProductForm();
        product.setProductId(0);
        product.setPlanId(0);
        product.setPlanCode("planCode");
        product.setPremium(new BigDecimal("0.00"));
        product.setQty(0);
        final TestPremiumDutyForm testPremiumDutyForm1 = new TestPremiumDutyForm();
        product.setPremiumDutyForms(Arrays.asList(testPremiumDutyForm1));
        fhInsuredPerson.setProduct(product);
        orderSubmitRequest.setInsuredPerson(Arrays.asList(fhInsuredPerson));
        final FhProperty propertyInfo = new FhProperty();
        propertyInfo.setPropertyInfoIsExist("0");
        orderSubmitRequest.setPropertyInfo(propertyInfo);
        final SmOrderHouseDTO house = new SmOrderHouseDTO();
        orderSubmitRequest.setHouse(house);
        final SmOrderCarInfoDTO carInfo = new SmOrderCarInfoDTO();
        orderSubmitRequest.setCarInfo(carInfo);
        orderSubmitRequest.setProductType("code");
        orderSubmitRequest.setBizCode("bizCode");
        orderSubmitRequest.setOrderOutType("orderOutType");
        orderSubmitRequest.setToken("token");
        orderSubmitRequest.setPlanId(0);
        orderSubmitRequest.setQty(0);
        orderSubmitRequest.setPreOrderId("preOrderId");
        orderSubmitRequest.setRenew(false);
        orderSubmitRequest.setRealRenewFlag(false);
        orderSubmitRequest.setAppNo("appNo");
        orderSubmitRequest.setAgentId(0);
        orderSubmitRequest.setJobCode("jobCode");
        final SmCreateOrderSubmitRequest smCreateOrderSubmitRequest = new SmCreateOrderSubmitRequest(orderSubmitRequest);
        final AutoOrderDTO orderDTO = new AutoOrderDTO();
        orderDTO.setOrderNo("fhOrderId");
        orderDTO.setPlanCode("planCode");
        final AutoOrder order3 = new AutoOrder();
        order3.setAppNo("appNo");
        order3.setProductId(0);
        order3.setPlanId(0);
        order3.setPayStatus("-1");
        orderDTO.setOrder(order3);
        final AutoOrderPerson autoOrderPerson1 = new AutoOrderPerson();
        orderDTO.setPeople(Arrays.asList(autoOrderPerson1));
        final AutoOrderPolicyDTO autoOrderPolicyDTO1 = new AutoOrderPolicyDTO();
        final AutoOrderPolicy policy1 = new AutoOrderPolicy();
        policy1.setPolicyNo("policyNo");
        policy1.setSubPolicyNo("subPolicyNo");
        policy1.setPlanId(0);
        policy1.setPolicyState("-2");
        policy1.setBusinessScore("businessScore");
        autoOrderPolicyDTO1.setPolicy(policy1);
        final AutoOrderRisk autoOrderRisk1 = new AutoOrderRisk();
        autoOrderRisk1.setRiskName("riskName");
        autoOrderRisk1.setPremium(new BigDecimal("0.00"));
        autoOrderRisk1.setAmount(new BigDecimal("0.00"));
        autoOrderPolicyDTO1.setRiskList(Arrays.asList(autoOrderRisk1));
        orderDTO.setPolicies(Arrays.asList(autoOrderPolicyDTO1));
        final AutoOrderExtend extend1 = new AutoOrderExtend();
        extend1.setOrderNo("fhOrderId");
        extend1.setContent("str");
        orderDTO.setExtend(extend1);
        final AutoOrderCar car1 = new AutoOrderCar();
        car1.setOwnerPersonName("ownerPersonName");
        car1.setOwnerPersonGender("ownerPersonGender");
        car1.setOwnerIdType("ownerIdType");
        car1.setOwnerIdNumber("ownerIdNumber");
        car1.setFirstRegDate("firstRegDate");
        car1.setFullLoad("fullLoad");
        car1.setTransferDate("transferDate");
        car1.setSeatCnt("approvedNum");
        car1.setPrice("price");
        car1.setCarUserType("carUserType");
        car1.setDisplacement("displacement");
        car1.setIsNewCar("isNewCar");
        orderDTO.setCar(car1);
        when(mockAutoCvtHelper.cvtOrderInfo(orderDTO)).thenReturn(smCreateOrderSubmitRequest);

        // Run the test
        final List<String> result = autoCcOrderServiceUnderTest.saveDate(order, extendCc, "str");
    }

    @Test
    public void testSaveDate_AutoCcOrderServiceAdapterReturnsNoItems() {
        // Setup
        final AutoCcOrder order = new AutoCcOrder();
        order.setUid("uid");
        order.setOrderNo("appNo");
        order.setStatus("status");
        final AutoCcUser user = new AutoCcUser();
        user.setMobile("mobile");
        order.setUser(user);

        final SmOrderExtendCc extendCc = new SmOrderExtendCc();
        extendCc.setFhOrderId("fhOrderId");
        extendCc.setBatchNo("batchNo");
        extendCc.setCcOrderNo("appNo");
        extendCc.setStatus("status");
        extendCc.setNotifyContent("str");
        extendCc.setState(0);

        // Configure AutoCcOrderServiceAdapter.cvtByNotify(...).
        final AutoCcOrder order1 = new AutoCcOrder();
        order1.setUid("uid");
        order1.setOrderNo("appNo");
        order1.setStatus("status");
        final AutoCcUser user1 = new AutoCcUser();
        user1.setMobile("mobile");
        order1.setUser(user1);
        when(mockAdapter.cvtByNotify(order1)).thenReturn(Collections.emptyList());

        // Run the test
        final List<String> result = autoCcOrderServiceUnderTest.saveDate(order, extendCc, "str");
    }

    @Test
    public void testSaveSmInfo() {
        // Setup
        final OrderSubmitRequest orderSubmitRequest = new OrderSubmitRequest();
        final FhProduct productInfo = new FhProduct();
        productInfo.setProductId("fhProductId");
        productInfo.setRecommendId("recommendId");
        productInfo.setRecommendMainJobNumber("recommendMainJobNumber");
        productInfo.setRecommendOrgCode("recommendOrgCode");
        orderSubmitRequest.setProductInfo(productInfo);
        final FhOrderInfo orderInfo = new FhOrderInfo();
        orderInfo.setSubmitTime("submitTime");
        orderInfo.setStartTime("startTime");
        orderInfo.setEndTime("endTime");
        orderSubmitRequest.setOrderInfo(orderInfo);
        final FhProposer proposerInfo = new FhProposer();
        proposerInfo.setPersonName("insuredName");
        proposerInfo.setPersonGender("insuredPersonGender");
        proposerInfo.setIdType("idType");
        proposerInfo.setIdNumber("idNumber");
        proposerInfo.setBirthday("birthday");
        proposerInfo.setCellPhone("cellPhone");
        proposerInfo.setEmail("email");
        proposerInfo.setAddress("address");
        proposerInfo.setAppStatus("-2");
        proposerInfo.setPolicyNo("insIdNumber");
        proposerInfo.setDownloadURL("downloadUrl");
        orderSubmitRequest.setProposerInfo(proposerInfo);
        final FhInsuredPerson fhInsuredPerson = new FhInsuredPerson();
        fhInsuredPerson.setPersonName("insuredName");
        fhInsuredPerson.setIdType("idType");
        fhInsuredPerson.setIdNumber("idNumber");
        fhInsuredPerson.setBirthday("birthday");
        fhInsuredPerson.setCellPhone("cellPhone");
        fhInsuredPerson.setAppStatus("-2");
        fhInsuredPerson.setPolicyNo("insIdNumber");
        fhInsuredPerson.setRelationship("insuredAppntShipCode");
        fhInsuredPerson.setFlightNo("flightNo");
        fhInsuredPerson.setFlightTime("flightTime");
        fhInsuredPerson.setOccupationCode("");
        fhInsuredPerson.setOccupationGroup("occupationGroup");
        fhInsuredPerson.setDestinationCountryText("destinationCountryText");
        fhInsuredPerson.setAnnualIncome("annualIncome");
        fhInsuredPerson.setStudentType("studentType");
        fhInsuredPerson.setSchoolType("schoolType");
        fhInsuredPerson.setSchoolNature("schoolNature");
        fhInsuredPerson.setSchoolName("schoolName");
        fhInsuredPerson.setSchoolClass("schoolClass");
        fhInsuredPerson.setStudentId("studentId");
        fhInsuredPerson.setIdPeriodStart("idPeriodStart");
        fhInsuredPerson.setIdPeriodEnd("idPeriodEnd");
        fhInsuredPerson.setSmoke("smoke");
        fhInsuredPerson.setOldPolicyNo("oldPolicyNo");
        final TestPremiumDutyForm testPremiumDutyForm = new TestPremiumDutyForm();
        fhInsuredPerson.setDuties(Arrays.asList(testPremiumDutyForm));
        final TestPremiumProductForm product = new TestPremiumProductForm();
        product.setProductId(0);
        product.setPlanId(0);
        product.setPlanCode("planCode");
        product.setPremium(new BigDecimal("0.00"));
        product.setQty(0);
        final TestPremiumDutyForm testPremiumDutyForm1 = new TestPremiumDutyForm();
        product.setPremiumDutyForms(Arrays.asList(testPremiumDutyForm1));
        fhInsuredPerson.setProduct(product);
        orderSubmitRequest.setInsuredPerson(Arrays.asList(fhInsuredPerson));
        final FhProperty propertyInfo = new FhProperty();
        propertyInfo.setPropertyInfoIsExist("0");
        orderSubmitRequest.setPropertyInfo(propertyInfo);
        final SmOrderHouseDTO house = new SmOrderHouseDTO();
        orderSubmitRequest.setHouse(house);
        final SmOrderCarInfoDTO carInfo = new SmOrderCarInfoDTO();
        orderSubmitRequest.setCarInfo(carInfo);
        orderSubmitRequest.setProductType("code");
        orderSubmitRequest.setBizCode("bizCode");
        orderSubmitRequest.setOrderOutType("orderOutType");
        orderSubmitRequest.setToken("token");
        orderSubmitRequest.setPlanId(0);
        orderSubmitRequest.setQty(0);
        orderSubmitRequest.setPreOrderId("preOrderId");
        orderSubmitRequest.setRenew(false);
        orderSubmitRequest.setRealRenewFlag(false);
        orderSubmitRequest.setAppNo("appNo");
        orderSubmitRequest.setAgentId(0);
        orderSubmitRequest.setJobCode("jobCode");
        final SmCreateOrderSubmitRequest dto = new SmCreateOrderSubmitRequest(orderSubmitRequest);
        final AutoOrderDTO orderDTO = new AutoOrderDTO();
        orderDTO.setOrderNo("fhOrderId");
        orderDTO.setPlanCode("planCode");
        final AutoOrder order = new AutoOrder();
        order.setAppNo("appNo");
        order.setProductId(0);
        order.setPlanId(0);
        order.setPayStatus("-1");
        orderDTO.setOrder(order);
        final AutoOrderPerson autoOrderPerson = new AutoOrderPerson();
        orderDTO.setPeople(Arrays.asList(autoOrderPerson));
        final AutoOrderPolicyDTO autoOrderPolicyDTO = new AutoOrderPolicyDTO();
        final AutoOrderPolicy policy = new AutoOrderPolicy();
        policy.setPolicyNo("policyNo");
        policy.setSubPolicyNo("subPolicyNo");
        policy.setPlanId(0);
        policy.setPolicyState("-2");
        policy.setBusinessScore("businessScore");
        autoOrderPolicyDTO.setPolicy(policy);
        final AutoOrderRisk autoOrderRisk = new AutoOrderRisk();
        autoOrderRisk.setRiskName("riskName");
        autoOrderRisk.setPremium(new BigDecimal("0.00"));
        autoOrderRisk.setAmount(new BigDecimal("0.00"));
        autoOrderPolicyDTO.setRiskList(Arrays.asList(autoOrderRisk));
        orderDTO.setPolicies(Arrays.asList(autoOrderPolicyDTO));
        final AutoOrderExtend extend = new AutoOrderExtend();
        extend.setOrderNo("fhOrderId");
        extend.setContent("str");
        orderDTO.setExtend(extend);
        final AutoOrderCar car = new AutoOrderCar();
        car.setOwnerPersonName("ownerPersonName");
        car.setOwnerPersonGender("ownerPersonGender");
        car.setOwnerIdType("ownerIdType");
        car.setOwnerIdNumber("ownerIdNumber");
        car.setFirstRegDate("firstRegDate");
        car.setFullLoad("fullLoad");
        car.setTransferDate("transferDate");
        car.setSeatCnt("approvedNum");
        car.setPrice("price");
        car.setCarUserType("carUserType");
        car.setDisplacement("displacement");
        car.setIsNewCar("isNewCar");
        orderDTO.setCar(car);

        // Run the test
        autoCcOrderServiceUnderTest.saveSmInfo(dto, orderDTO);

        // Verify the results
    }

    @Test
    public void testSaveAutoInfo() {
        // Setup
        final AutoOrderDTO dto = new AutoOrderDTO();
        dto.setOrderNo("fhOrderId");
        dto.setPlanCode("planCode");
        final AutoOrder order = new AutoOrder();
        order.setAppNo("appNo");
        order.setProductId(0);
        order.setPlanId(0);
        order.setPayStatus("-1");
        dto.setOrder(order);
        final AutoOrderPerson autoOrderPerson = new AutoOrderPerson();
        dto.setPeople(Arrays.asList(autoOrderPerson));
        final AutoOrderPolicyDTO autoOrderPolicyDTO = new AutoOrderPolicyDTO();
        final AutoOrderPolicy policy = new AutoOrderPolicy();
        policy.setPolicyNo("policyNo");
        policy.setSubPolicyNo("subPolicyNo");
        policy.setPlanId(0);
        policy.setPolicyState("-2");
        policy.setBusinessScore("businessScore");
        autoOrderPolicyDTO.setPolicy(policy);
        final AutoOrderRisk autoOrderRisk = new AutoOrderRisk();
        autoOrderRisk.setRiskName("riskName");
        autoOrderRisk.setPremium(new BigDecimal("0.00"));
        autoOrderRisk.setAmount(new BigDecimal("0.00"));
        autoOrderPolicyDTO.setRiskList(Arrays.asList(autoOrderRisk));
        dto.setPolicies(Arrays.asList(autoOrderPolicyDTO));
        final AutoOrderExtend extend = new AutoOrderExtend();
        extend.setOrderNo("fhOrderId");
        extend.setContent("str");
        dto.setExtend(extend);
        final AutoOrderCar car = new AutoOrderCar();
        car.setOwnerPersonName("ownerPersonName");
        car.setOwnerPersonGender("ownerPersonGender");
        car.setOwnerIdType("ownerIdType");
        car.setOwnerIdNumber("ownerIdNumber");
        car.setFirstRegDate("firstRegDate");
        car.setFullLoad("fullLoad");
        car.setTransferDate("transferDate");
        car.setSeatCnt("approvedNum");
        car.setPrice("price");
        car.setCarUserType("carUserType");
        car.setDisplacement("displacement");
        car.setIsNewCar("isNewCar");
        dto.setCar(car);

        // Run the test
        autoCcOrderServiceUnderTest.saveAutoInfo(dto);

    }
}
