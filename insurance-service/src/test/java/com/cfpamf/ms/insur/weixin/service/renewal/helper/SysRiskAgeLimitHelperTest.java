package com.cfpamf.ms.insur.weixin.service.renewal.helper;

import com.cfpamf.ms.insur.admin.pojo.po.product.risk.SysRiskLimit;
import com.cfpamf.ms.insur.admin.service.product.helper.SysRiskAgeLimitHelper;
import com.cfpamf.ms.insur.weixin.pojo.vo.risk.SysRiskAgeLimitVo;
import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.junit.Test;

/**
 * <AUTHOR>
 * @date 2021/11/8 15:44
 */
public class SysRiskAgeLimitHelperTest {
    @Test
    public void getRiskAgeLimitTest() {
        SysRiskLimit sysRiskLimit = new SysRiskLimit();
        sysRiskLimit.setLimitAge("2D-40Y");
        sysRiskLimit.setRenewAge("50Y");

        SysRiskLimit sysRiskLimit2 = new SysRiskLimit();
        sysRiskLimit2.setLimitAge("2M-39Y");
        sysRiskLimit2.setRenewAge("49Y");

        SysRiskLimit sysRiskLimit3 = new SysRiskLimit();
        sysRiskLimit3.setLimitAge("1Y-38Y");
        sysRiskLimit3.setRenewAge("48Y");
        SysRiskAgeLimitVo riskAgeLimit = SysRiskAgeLimitHelper.getRiskAgeLimit(Lists.newArrayList(sysRiskLimit, sysRiskLimit2, sysRiskLimit3));
        String newOrderLimitAge = riskAgeLimit.getNewOrderLimitAge();
        String renewalOrderLimitAge = riskAgeLimit.getRenewalOrderLimitAge();
        Assert.assertEquals(newOrderLimitAge, "1Y-38Y");
        Assert.assertEquals(renewalOrderLimitAge, "48Y");
    }
}
