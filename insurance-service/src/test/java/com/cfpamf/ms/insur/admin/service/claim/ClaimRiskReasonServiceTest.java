package com.cfpamf.ms.insur.admin.service.claim;

import com.alibaba.fastjson.JSON;
import com.cfpamf.ms.insur.admin.dao.safes.claim.SmClaimRiskReasonMapper;
import com.cfpamf.ms.insur.admin.pojo.vo.claim.ClaimRiskReasonVo;
import com.cfpamf.ms.insur.admin.service.claim.impl.ClaimRiskReasonServiceImpl;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/11/12 11:30
 */
@RunWith(PowerMockRunner.class)
public class ClaimRiskReasonServiceTest {

    @Mock
    SmClaimRiskReasonMapper smClaimRiskReasonMapper;

    @InjectMocks
    ClaimRiskReasonServiceImpl claimRiskReasonService;


    public String dataJson = "[\n" +
            "        {\n" +
            "            \"id\": 1,\n" +
            "            \"claimRiskTypeArrJson\": \"[\\\"1\\\", \\\"6\\\", \\\"4\\\", \\\"5\\\"]\",\n" +
            "            \"lastReasonId\": 0,\n" +
            "            \"reason\": \"交通意外\",\n" +
            "            \"level\": \"1\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"id\": 2,\n" +
            "            \"claimRiskTypeArrJson\": \"[\\\"1\\\", \\\"6\\\", \\\"4\\\", \\\"5\\\"]\",\n" +
            "            \"lastReasonId\": 0,\n" +
            "            \"reason\": \"外物致伤\",\n" +
            "            \"level\": \"1\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"id\": 3,\n" +
            "            \"claimRiskTypeArrJson\": \"[\\\"1\\\", \\\"6\\\", \\\"4\\\", \\\"5\\\"]\",\n" +
            "            \"lastReasonId\": 0,\n" +
            "            \"reason\": \"动物致伤\",\n" +
            "            \"level\": \"1\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"id\": 4,\n" +
            "            \"claimRiskTypeArrJson\": \"[\\\"1\\\", \\\"6\\\", \\\"4\\\", \\\"5\\\"]\",\n" +
            "            \"lastReasonId\": 0,\n" +
            "            \"reason\": \"普通意外致伤\",\n" +
            "            \"level\": \"1\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"id\": 5,\n" +
            "            \"claimRiskTypeArrJson\": \"[\\\"1\\\", \\\"6\\\", \\\"4\\\", \\\"5\\\"]\",\n" +
            "            \"lastReasonId\": 0,\n" +
            "            \"reason\": \"特殊原因致伤\",\n" +
            "            \"level\": \"1\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"id\": 6,\n" +
            "            \"claimRiskTypeArrJson\": \"[\\\"1\\\", \\\"6\\\", \\\"4\\\", \\\"5\\\"]\",\n" +
            "            \"lastReasonId\": 0,\n" +
            "            \"reason\": \"溺水\",\n" +
            "            \"level\": \"1\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"id\": 7,\n" +
            "            \"claimRiskTypeArrJson\": \"[\\\"1\\\", \\\"6\\\", \\\"4\\\", \\\"5\\\"]\",\n" +
            "            \"lastReasonId\": 0,\n" +
            "            \"reason\": \"他人致伤\",\n" +
            "            \"level\": \"1\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"id\": 8,\n" +
            "            \"claimRiskTypeArrJson\": \"[\\\"1\\\", \\\"6\\\", \\\"4\\\", \\\"8\\\",\\\"5\\\"]\",\n" +
            "            \"lastReasonId\": 0,\n" +
            "            \"reason\": \"其他\",\n" +
            "            \"level\": \"1\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"id\": 9,\n" +
            "            \"claimRiskTypeArrJson\": null,\n" +
            "            \"lastReasonId\": 1,\n" +
            "            \"reason\": \"自行车\",\n" +
            "            \"level\": \"2\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"id\": 10,\n" +
            "            \"claimRiskTypeArrJson\": null,\n" +
            "            \"lastReasonId\": 1,\n" +
            "            \"reason\": \"电动车\",\n" +
            "            \"level\": \"2\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"id\": 11,\n" +
            "            \"claimRiskTypeArrJson\": null,\n" +
            "            \"lastReasonId\": 1,\n" +
            "            \"reason\": \"私家汽车、客车、非营运货车\",\n" +
            "            \"level\": \"2\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"id\": 12,\n" +
            "            \"claimRiskTypeArrJson\": null,\n" +
            "            \"lastReasonId\": 1,\n" +
            "            \"reason\": \"营运货车\",\n" +
            "            \"level\": \"2\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"id\": 13,\n" +
            "            \"claimRiskTypeArrJson\": null,\n" +
            "            \"lastReasonId\": 1,\n" +
            "            \"reason\": \"农用车\",\n" +
            "            \"level\": \"2\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"id\": 14,\n" +
            "            \"claimRiskTypeArrJson\": null,\n" +
            "            \"lastReasonId\": 1,\n" +
            "            \"reason\": \"工地用车\",\n" +
            "            \"level\": \"2\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"id\": 15,\n" +
            "            \"claimRiskTypeArrJson\": null,\n" +
            "            \"lastReasonId\": 1,\n" +
            "            \"reason\": \"行人\",\n" +
            "            \"level\": \"2\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"id\": 16,\n" +
            "            \"claimRiskTypeArrJson\": null,\n" +
            "            \"lastReasonId\": 1,\n" +
            "            \"reason\": \"摩托车\",\n" +
            "            \"level\": \"2\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"id\": 17,\n" +
            "            \"claimRiskTypeArrJson\": null,\n" +
            "            \"lastReasonId\": 1,\n" +
            "            \"reason\": \"其他\",\n" +
            "            \"level\": \"2\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"id\": 18,\n" +
            "            \"claimRiskTypeArrJson\": null,\n" +
            "            \"lastReasonId\": 2,\n" +
            "            \"reason\": \"机器器械意外\",\n" +
            "            \"level\": \"2\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"id\": 19,\n" +
            "            \"claimRiskTypeArrJson\": null,\n" +
            "            \"lastReasonId\": 2,\n" +
            "            \"reason\": \"其他工具致伤\",\n" +
            "            \"level\": \"2\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"id\": 20,\n" +
            "            \"claimRiskTypeArrJson\": null,\n" +
            "            \"lastReasonId\": 3,\n" +
            "            \"reason\": \"动物咬/抓/顶伤\",\n" +
            "            \"level\": \"2\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"id\": 21,\n" +
            "            \"claimRiskTypeArrJson\": null,\n" +
            "            \"lastReasonId\": 3,\n" +
            "            \"reason\": \"动物中毒\",\n" +
            "            \"level\": \"2\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"id\": 22,\n" +
            "            \"claimRiskTypeArrJson\": null,\n" +
            "            \"lastReasonId\": 4,\n" +
            "            \"reason\": \"摔伤\",\n" +
            "            \"level\": \"2\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"id\": 23,\n" +
            "            \"claimRiskTypeArrJson\": null,\n" +
            "            \"lastReasonId\": 4,\n" +
            "            \"reason\": \"磕伤\",\n" +
            "            \"level\": \"2\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"id\": 24,\n" +
            "            \"claimRiskTypeArrJson\": null,\n" +
            "            \"lastReasonId\": 4,\n" +
            "            \"reason\": \"高空坠落\",\n" +
            "            \"level\": \"2\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"id\": 25,\n" +
            "            \"claimRiskTypeArrJson\": null,\n" +
            "            \"lastReasonId\": 5,\n" +
            "            \"reason\": \"电击伤\",\n" +
            "            \"level\": \"2\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"id\": 26,\n" +
            "            \"claimRiskTypeArrJson\": null,\n" +
            "            \"lastReasonId\": 5,\n" +
            "            \"reason\": \"烧烫伤\",\n" +
            "            \"level\": \"2\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"id\": 27,\n" +
            "            \"claimRiskTypeArrJson\": null,\n" +
            "            \"lastReasonId\": 5,\n" +
            "            \"reason\": \"中毒\",\n" +
            "            \"level\": \"2\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"id\": 28,\n" +
            "            \"claimRiskTypeArrJson\": null,\n" +
            "            \"lastReasonId\": 18,\n" +
            "            \"reason\": \"农业农用器物\",\n" +
            "            \"level\": \"3\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"id\": 29,\n" +
            "            \"claimRiskTypeArrJson\": null,\n" +
            "            \"lastReasonId\": 18,\n" +
            "            \"reason\": \"工地/工厂用机械\",\n" +
            "            \"level\": \"3\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"id\": 30,\n" +
            "            \"claimRiskTypeArrJson\": null,\n" +
            "            \"lastReasonId\": 18,\n" +
            "            \"reason\": \"日常生活用具\",\n" +
            "            \"level\": \"3\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"id\": 31,\n" +
            "            \"claimRiskTypeArrJson\": null,\n" +
            "            \"lastReasonId\": 19,\n" +
            "            \"reason\": \"物体砸伤\",\n" +
            "            \"level\": \"3\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"id\": 32,\n" +
            "            \"claimRiskTypeArrJson\": null,\n" +
            "            \"lastReasonId\": 19,\n" +
            "            \"reason\": \"外物挤压\",\n" +
            "            \"level\": \"3\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"id\": 33,\n" +
            "            \"claimRiskTypeArrJson\": null,\n" +
            "            \"lastReasonId\": 19,\n" +
            "            \"reason\": \"小物体内\",\n" +
            "            \"level\": \"3\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"id\": 34,\n" +
            "            \"claimRiskTypeArrJson\": null,\n" +
            "            \"lastReasonId\": 27,\n" +
            "            \"reason\": \"食物/药品中毒\",\n" +
            "            \"level\": \"3\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"id\": 35,\n" +
            "            \"claimRiskTypeArrJson\": null,\n" +
            "            \"lastReasonId\": 27,\n" +
            "            \"reason\": \"集体食物中毒\",\n" +
            "            \"level\": \"3\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"id\": 36,\n" +
            "            \"claimRiskTypeArrJson\": null,\n" +
            "            \"lastReasonId\": 27,\n" +
            "            \"reason\": \"气体中毒\",\n" +
            "            \"level\": \"3\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"id\": 37,\n" +
            "            \"claimRiskTypeArrJson\": \"[\\\"8\\\"]\",\n" +
            "            \"lastReasonId\": 0,\n" +
            "            \"reason\": \"猝死\",\n" +
            "            \"level\": \"1\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"id\": 38,\n" +
            "            \"claimRiskTypeArrJson\": \"[\\\"8\\\"]\",\n" +
            "            \"lastReasonId\": 0,\n" +
            "            \"reason\": \"心肌梗死\",\n" +
            "            \"level\": \"1\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"id\": 39,\n" +
            "            \"claimRiskTypeArrJson\": \"[\\\"8\\\"]\",\n" +
            "            \"lastReasonId\": 0,\n" +
            "            \"reason\": \"休克\",\n" +
            "            \"level\": \"1\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"id\": 40,\n" +
            "            \"claimRiskTypeArrJson\": \"[\\\"8\\\"]\",\n" +
            "            \"lastReasonId\": 0,\n" +
            "            \"reason\": \"脑疝\",\n" +
            "            \"level\": \"1\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"id\": 41,\n" +
            "            \"claimRiskTypeArrJson\": \"[\\\"8\\\"]\",\n" +
            "            \"lastReasonId\": 0,\n" +
            "            \"reason\": \"脑出血\",\n" +
            "            \"level\": \"1\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"id\": 42,\n" +
            "            \"claimRiskTypeArrJson\": \"[\\\"2\\\"]\",\n" +
            "            \"lastReasonId\": 0,\n" +
            "            \"reason\": \"感冒，支气管肺炎等常见疾病\",\n" +
            "            \"level\": \"1\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"id\": 43,\n" +
            "            \"claimRiskTypeArrJson\": \"[\\\"2\\\"]\",\n" +
            "            \"lastReasonId\": 0,\n" +
            "            \"reason\": \"急性突发疾病\",\n" +
            "            \"level\": \"1\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"id\": 44,\n" +
            "            \"claimRiskTypeArrJson\": \"[\\\"2\\\"]\",\n" +
            "            \"lastReasonId\": 0,\n" +
            "            \"reason\": \"慢性感染疾病\",\n" +
            "            \"level\": \"1\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"id\": 45,\n" +
            "            \"claimRiskTypeArrJson\": \"[\\\"2\\\"]\",\n" +
            "            \"lastReasonId\": 0,\n" +
            "            \"reason\": \"其他疾病\",\n" +
            "            \"level\": \"1\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"id\": 46,\n" +
            "            \"claimRiskTypeArrJson\": \"[\\\"3\\\"]\",\n" +
            "            \"lastReasonId\": 0,\n" +
            "            \"reason\": \"恶性肿瘤\",\n" +
            "            \"level\": \"1\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"id\": 47,\n" +
            "            \"claimRiskTypeArrJson\": \"[\\\"3\\\"]\",\n" +
            "            \"lastReasonId\": 0,\n" +
            "            \"reason\": \"白血病\",\n" +
            "            \"level\": \"1\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"id\": 48,\n" +
            "            \"claimRiskTypeArrJson\": \"[\\\"3\\\"]\",\n" +
            "            \"lastReasonId\": 0,\n" +
            "            \"reason\": \"其他重大疾病\",\n" +
            "            \"level\": \"1\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"id\": 49,\n" +
            "            \"claimRiskTypeArrJson\": \"[\\\"7\\\"]\",\n" +
            "            \"lastReasonId\": 0,\n" +
            "            \"reason\": \"家财\",\n" +
            "            \"level\": \"1\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"id\": 50,\n" +
            "            \"claimRiskTypeArrJson\": null,\n" +
            "            \"lastReasonId\": 46,\n" +
            "            \"reason\": \"肺癌\",\n" +
            "            \"level\": \"2\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"id\": 51,\n" +
            "            \"claimRiskTypeArrJson\": null,\n" +
            "            \"lastReasonId\": 46,\n" +
            "            \"reason\": \"宫颈癌\",\n" +
            "            \"level\": \"2\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"id\": 52,\n" +
            "            \"claimRiskTypeArrJson\": null,\n" +
            "            \"lastReasonId\": 46,\n" +
            "            \"reason\": \"前列腺癌\",\n" +
            "            \"level\": \"2\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"id\": 53,\n" +
            "            \"claimRiskTypeArrJson\": null,\n" +
            "            \"lastReasonId\": 46,\n" +
            "            \"reason\": \"结肠癌\",\n" +
            "            \"level\": \"2\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"id\": 54,\n" +
            "            \"claimRiskTypeArrJson\": null,\n" +
            "            \"lastReasonId\": 46,\n" +
            "            \"reason\": \"淋巴瘤\",\n" +
            "            \"level\": \"2\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"id\": 55,\n" +
            "            \"claimRiskTypeArrJson\": null,\n" +
            "            \"lastReasonId\": 46,\n" +
            "            \"reason\": \"胃癌\",\n" +
            "            \"level\": \"2\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"id\": 56,\n" +
            "            \"claimRiskTypeArrJson\": null,\n" +
            "            \"lastReasonId\": 46,\n" +
            "            \"reason\": \"其他\",\n" +
            "            \"level\": \"2\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"id\": 57,\n" +
            "            \"claimRiskTypeArrJson\": null,\n" +
            "            \"lastReasonId\": 49,\n" +
            "            \"reason\": \"房屋损毁\",\n" +
            "            \"level\": \"2\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"id\": 58,\n" +
            "            \"claimRiskTypeArrJson\": null,\n" +
            "            \"lastReasonId\": 49,\n" +
            "            \"reason\": \"其他财产损失\",\n" +
            "            \"level\": \"2\"\n" +
            "        }\n" +
            "    ]";
        @Test
        public void  getByRiskTypeTest(){
            List<ClaimRiskReasonVo> claimRiskReasonVos = JSON.parseArray(dataJson, ClaimRiskReasonVo.class);
            Mockito.when(smClaimRiskReasonMapper.getAll()).thenReturn(claimRiskReasonVos);
           claimRiskReasonService.getByRiskType("2");
        }

    @Test
    public void  getAllTest(){
        List<ClaimRiskReasonVo> claimRiskReasonVos = JSON.parseArray(dataJson, ClaimRiskReasonVo.class);
        Mockito.when(smClaimRiskReasonMapper.getAll()).thenReturn(claimRiskReasonVos);
        claimRiskReasonService.getAll();
    }

    @Test
    public void  getByLastReasonId(){
        List<ClaimRiskReasonVo> claimRiskReasonVos = JSON.parseArray(dataJson, ClaimRiskReasonVo.class);
        Mockito.when(smClaimRiskReasonMapper.getByLastReasonId(2)).thenReturn(claimRiskReasonVos);
        claimRiskReasonService.getByLastReasonId(2);
    }
}
