package com.cfpamf.ms.insur.base.util;

import org.junit.Assert;
import org.junit.Test;

import java.math.BigDecimal;

public class NumberCompareUtilTest {

    @Test
    public void compareAndExpect() {
//        System.out.println(new BigDecimal(100).compareTo(new BigDecimal(10)));
//        System.out.println(new BigDecimal(10).compareTo(new BigDecimal(100)));
//        System.out.println(new BigDecimal(100).compareTo(new BigDecimal(100)));
        Assert.assertTrue(NumCompareUtil.compareAndExpect(new BigDecimal(100), new BigDecimal(100), "="));
        Assert.assertTrue(NumCompareUtil.compareAndExpect(new BigDecimal(100), new BigDecimal(100), ">="));
        Assert.assertTrue(NumCompareUtil.compareAndExpect(new BigDecimal(100), new BigDecimal(100), "<="));
        Assert.assertTrue(NumCompareUtil.compareAndExpect(new BigDecimal(10), new BigDecimal(100), "<="));
        Assert.assertTrue(NumCompareUtil.compareAndExpect(new BigDecimal(10), new BigDecimal(100), "<"));
        Assert.assertTrue(NumCompareUtil.compareAndExpect(new BigDecimal(100), new BigDecimal(10), ">"));
        Assert.assertTrue(NumCompareUtil.compareAndExpect(new BigDecimal(100), new BigDecimal(10), ">="));
    }
}