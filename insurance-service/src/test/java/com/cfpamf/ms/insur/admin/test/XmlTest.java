package com.cfpamf.ms.insur.admin.test;

import com.cfpamf.ms.insur.admin.external.cic.dto.InsureRet;
import com.cfpamf.ms.insur.admin.external.cic.util.XmlMapperUtil;

/**
 * <AUTHOR>
 **/
public class XmlTest {

    public static void main(String[] args) {
        String xml = "<?xml version=\"1.0\" encoding=\"GBK\" ?>\n" +
                "<INSUREQRET>\n" +
                "\t<HEAD>\n" +
                "\t\t<TRANSRNO>CIC001</TRANSRNO>\n" +
                "\t\t<RESULTCODE>01</RESULTCODE>\n" +
                "\t\t<ERRINFO>非有效证件，请更换有效证件投保！</ERRINFO>\n" +
                "\t</HEAD>\n" +
                "\t<MAIN>\n" +
                "\t</MAIN>\n" +
                "\n" +
                "</INSUREQRET>\n";

        InsureRet ret = XmlMapperUtil.xmlToBean(xml, InsureRet.class);
    }
}
