package com.cfpamf.ms.insur.admin.service;

import com.cfpamf.ms.insur.admin.dao.safes.AuthUserMapper;
import com.cfpamf.ms.insur.admin.dao.safes.SmClaimHastenMapper;
import com.cfpamf.ms.insur.admin.pojo.dto.ClaimProgressDTO;
import com.cfpamf.ms.insur.admin.pojo.vo.AuthUserVO;
import com.cfpamf.ms.insur.base.config.ClaimConfig;
import com.cfpamf.ms.insur.base.config.WechatConfig;
import com.cfpamf.ms.insur.weixin.service.WxMpServiceProxy;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

/**
 * Create By zhengjing on 2019-08-23 16:35
 */
@RunWith(SpringJUnit4ClassRunner.class)
public class SmClaimHastenServiceTest {

    @InjectMocks
    SmClaimHastenService hastenService;
    ObjectMapper mapper = new ObjectMapper();
    /**
     * 微信参数配置
     */
    @Mock
    private WechatConfig wechatConfig;
    @Mock
    private SmClaimHastenMapper hastenMapper;
    /**
     * 微信代理service
     */
    @Mock
    private WxMpServiceProxy serviceProxy;
    @Mock
    private ClaimConfig claimConfig;
    @Mock
    private AuthUserMapper authUserMapper;

    @Before
    public void setUp() throws Exception {


        Mockito.when(wechatConfig.getBackJumpUrl())
                .thenReturn("https://open.weixin.qq.com/connect/oauth2/authorize?appid=wxbd10d23b6f05cf8c&redirect_uri=%s&response_type=code&scope=snsapi_userinfo&state=%s#wechat_redirect");

        Mockito.when(wechatConfig.getBackClaimTodoUrl())
                .thenReturn("https://www.baidu.com");
        ClaimConfig.User user = new ClaimConfig.User();
        user.setUserId("1234");
        Mockito.when(claimConfig.getApprover())
                .thenReturn(user);

        Mockito.when(authUserMapper.getAuthUserByUserId(Mockito.anyString()))
                .thenReturn(new AuthUserVO());
        List<ClaimProgressDTO> dtos = Lists.newArrayListWithCapacity(3);
        String str = "{\"claimId\":1122,\"dataJson\":null,\"settlement\":null,\"createBy\":\"ZHNX07902\",\"claimState\":\"stepDataPrepare1\",\"customerName\":\"张娜义\",\"claimNo\":\"LP20190819004\",\"processorUserId\":\"ZHNX07902\",\"wxOpenId\":null,\"ocode\":null,\"sname\":null,\"scode\":null,\"ovalue\":\"1\",\"oname\":null,\"otime\":null,\"otype\":null}";
        int id = 1;
        ClaimProgressDTO dto = mapper.readValue(str, ClaimProgressDTO.class);
        dto.setClaimStateTime(LocalDateTime.now().minusDays(4L));
        dtos.add(dto);
        //未到时见数据
        dto = mapper.readValue(str, ClaimProgressDTO.class);
        dto.setClaimId(id++);
        dto.setClaimStateTime(LocalDateTime.now().minusDays(1L));
        dto.setHastenTime(LocalDateTime.now().minusDays(1L));
        dtos.add(dto);

        dto = mapper.readValue(str, ClaimProgressDTO.class);
        dto.setClaimId(id++);
        dto.setClaimStateTime(LocalDateTime.now().minusDays(4L));
        dto.setWxOpenId("tetst");
        dtos.add(dto);

        dto = mapper.readValue(str, ClaimProgressDTO.class);
        dto.setClaimId(id++);
        dto.setClaimStateTime(LocalDateTime.now().minusDays(4L));
        dto.setWxOpenId("tetst");
        dto.setClaimState("stepDataPrepare1");
        dtos.add(dto);

        dto = mapper.readValue(str, ClaimProgressDTO.class);
        dto.setClaimId(id++);
        dto.setClaimStateTime(LocalDateTime.now().minusDays(4L));
        dto.setWxOpenId("tetst");
        dto.setClaimState("stepPostExpress");
        dtos.add(dto);

        dto = mapper.readValue(str, ClaimProgressDTO.class);
        dto.setClaimId(id++);
        dto.setClaimStateTime(LocalDateTime.now().minusDays(4L));
        dto.setWxOpenId("tetst");
        dto.setClaimState("stepDataCheckByPic");
        dtos.add(dto);
        Mockito.when(hastenMapper.lastResult(Mockito.any()))
                .thenReturn(dtos);
    }

    @Test
    public void hasten() {
        hastenService.hasten(Arrays.asList(1, 2, 3, 4, 5, 6), "TEST001");
    }


    @Test
    public void hasten1() {
    }
}
