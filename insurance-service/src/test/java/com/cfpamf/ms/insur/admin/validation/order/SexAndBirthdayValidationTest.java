package com.cfpamf.ms.insur.admin.validation.order;

import com.cfpamf.ms.insur.admin.pojo.dto.excelorder.ExcelShortTermOrderDTO;
import com.cfpamf.ms.insur.admin.validation.SmCarOrderValidation;
import com.cfpamf.ms.insur.admin.validation.ValidContext;
import com.cfpamf.ms.insur.admin.validation.ValidationResult;
import com.cfpamf.ms.insur.base.util.HttpRequestUtil;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

/**
 * @author: yangdonglin
 * @create: 2023-06-26 16:51
 * @description: 性别生日校验测试
 */

@RunWith(PowerMockRunner.class)
@PrepareForTest({HttpRequestUtil.class})
public class SexAndBirthdayValidationTest {

    @InjectMocks
    private SexAndBirthdayValidation sexAndBirthdayValidation;
    @InjectMocks
    private SmCarOrderValidation smCarOrderValidation;

    @Test
    public void testValidSexAndBirthdayShort() {
        //类型为营业执照
        final ExcelShortTermOrderDTO dto1 = new ExcelShortTermOrderDTO();
        dto1.setApplicantIdType("营业执照");
        dto1.setApplicantSex("M");
        dto1.setApplicantBirthday("1990-01-01");
        dto1.setInsuredIdType("ID_TYPE_2");
        dto1.setInsuredSex("F");
        dto1.setInsuredBirthday("1995-01-01");
        final ValidationResult<ExcelShortTermOrderDTO> result1 = new ValidationResult<>(null,dto1);
        final List<ValidationResult<ExcelShortTermOrderDTO>> list1 = new ArrayList<>();
        list1.add(result1);
        final ValidContext<ExcelShortTermOrderDTO> context1 = new ValidContext<>(null,list1);
        //调用被测方法
        sexAndBirthdayValidation.validSexAndBirthdayShort(context1);
        //验证结果
        assertFalse(result1.isSuccess());

        final ExcelShortTermOrderDTO dto2 = new ExcelShortTermOrderDTO();
        dto2.setApplicantIdType("ID_TYPE_2");
        dto2.setApplicantSex(" ");
        dto2.setApplicantBirthday(" ");
        dto2.setInsuredIdType("营业执照");
        dto2.setInsuredSex("");
        dto2.setInsuredBirthday("");
        final ValidationResult<ExcelShortTermOrderDTO> result2 = new ValidationResult<>(null,dto2);
        final List<ValidationResult<ExcelShortTermOrderDTO>> list2 = new ArrayList<>();
        list2.add(result2);
        final ValidContext<ExcelShortTermOrderDTO> context2 = new ValidContext<>(null,list2);
        //调用被测方法
        sexAndBirthdayValidation.validSexAndBirthdayShort(context2);
        //验证结果
        assertFalse(result2.isSuccess());

        //类型为营业执照
        final ExcelShortTermOrderDTO dto3 = new ExcelShortTermOrderDTO();
        dto3.setApplicantIdType("ID_TYPE_2");
        dto3.setApplicantSex("M");
        dto3.setApplicantBirthday("1990-01-01");
        dto3.setInsuredIdType("营业执照");
        dto3.setInsuredSex("");
        dto3.setInsuredBirthday("");
        final ValidationResult<ExcelShortTermOrderDTO> result3 = new ValidationResult<>(null,dto3);
        final List<ValidationResult<ExcelShortTermOrderDTO>> list3 = new ArrayList<>();
        list3.add(result3);
        final ValidContext<ExcelShortTermOrderDTO> context3 = new ValidContext<>(null,list3);
        //调用被测方法
        sexAndBirthdayValidation.validSexAndBirthdayShort(context3);
        //验证结果
        assertTrue(result3.isSuccess());

        final ExcelShortTermOrderDTO dto4 = new ExcelShortTermOrderDTO();
        dto4.setApplicantIdType("营业执照");
        dto4.setApplicantSex(null);
        dto4.setApplicantBirthday(null);
        dto4.setInsuredIdType("ID_TYPE_2");
        dto4.setInsuredSex("F");
        dto4.setInsuredBirthday("1995-01-01");
        final ValidationResult<ExcelShortTermOrderDTO> result4 = new ValidationResult<>(null,dto4);
        final List<ValidationResult<ExcelShortTermOrderDTO>> list4 = new ArrayList<>();
        list4.add(result4);
        final ValidContext<ExcelShortTermOrderDTO> context4 = new ValidContext<>(null,list4);
        //调用被测方法
        sexAndBirthdayValidation.validSexAndBirthdayShort(context4);
        //验证结果
        assertTrue(result4.isSuccess());
    }
}
