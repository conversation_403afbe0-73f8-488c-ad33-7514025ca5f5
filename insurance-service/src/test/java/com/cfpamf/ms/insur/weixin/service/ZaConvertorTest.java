package com.cfpamf.ms.insur.weixin.service;

import com.cfpamf.ms.insur.admin.enums.EnumChannel;
import com.cfpamf.ms.insur.admin.external.zhongan.ZaApiProperties;
import com.cfpamf.ms.insur.weixin.common.IdGenerator;
import com.cfpamf.ms.insur.weixin.pojo.vo.apply.common.GroupApplicant;
import com.cfpamf.ms.insur.weixin.pojo.vo.apply.common.GroupEndorsement;
import com.cfpamf.ms.insur.weixin.pojo.vo.apply.common.GroupInsured;
import com.cfpamf.ms.insur.weixin.pojo.vo.apply.common.GroupUnderwriting;
import com.cfpamf.ms.insur.weixin.pojo.vo.apply.zhongan.item.EndorInsured;
import com.cfpamf.ms.insur.weixin.pojo.vo.apply.zhongan.order.Clause;
import com.cfpamf.ms.insur.weixin.pojo.vo.apply.zhongan.order.Insured;
import com.cfpamf.ms.insur.weixin.pojo.vo.apply.zhongan.order.OrgHolder;
import com.cfpamf.ms.insur.weixin.pojo.vo.apply.zhongan.order.ProductReq;
import com.cfpamf.ms.insur.weixin.pojo.vo.apply.zhongan.request.ZaGroupEndorReq;
import com.cfpamf.ms.insur.weixin.pojo.vo.apply.zhongan.request.ZaGroupUnderwritingReq;
import com.github.jsonzou.jmockdata.JMockData;
import com.github.jsonzou.jmockdata.TypeReference;
import org.apache.commons.lang3.StringUtils;
import org.junit.Assert;
import org.junit.Test;

import java.lang.reflect.Type;
import java.util.*;

public class ZaConvertorTest {
    @Test
    public void convertApplyBean(){

        ZaApiProperties prop = JMockData.mock(ZaApiProperties.class);
        TypeReference<Map<String,String>> tr = new TypeReference<Map<String, String>>() {};
        Map<String,String> cvgRiskMap =JMockData.mock(tr);
        GroupUnderwriting req = JMockData.mock(GroupUnderwriting.class);
        ZaGroupUnderwritingReq data = new ZaGroupUnderwritingReq();

        String orderId = StringUtils.isBlank(req.getOrderId())? IdGenerator.getNextNo(EnumChannel.ZA.getCode()):req.getOrderId();

        data.setChannelOrderNo(orderId);
        data.setChannelCode(prop.getGroupChannelCode());
        data.setProductCateCode(prop.getGroupChannelCode());
        data.setEffectiveDate(req.getStartTime());
        data.setExpireDate(req.getEndTime());
        data.setTotalPremium(req.getTotalAmount());

        data.setCoveragePeriodType(mapPeriodType(req.getValidPeriod()));
        data.setCoveragePeriod(mapPeriod(req.getValidPeriod()));
        data.setOrgHolder(convertOrgBean(req.getApplicant()));
        Set<String> ocpGroups = fullInsured(orderId,data,req.getInsuredList());
        List<ProductReq> products = genVirtualProduct(orderId,ocpGroups,req.getProduct(),cvgRiskMap);
        data.setProductList(products);
        data.setInvoiceInfo(req.getInvoiceInfo());
        data.setHealthDeclarationList(data.getHealthDeclarationList());
        Assert.assertTrue(true);
    }

    private static List<ProductReq> genVirtualProduct(String orderId,Set<String>ocpGroups,ProductReq product,Map<String,String> cvgRiskMap){
        List<ProductReq> data = new ArrayList<>();
        List<Clause> cls = product.getClauseList();
        if(cls!=null) {
            for (Clause c : cls) {
                String baseClauseCode = cvgRiskMap.get(c.getClauseCode());
                if(StringUtils.isNotBlank(baseClauseCode)) {
                    c.setBaseClauseCode(baseClauseCode);
                }
            }
        }
        String suffix = "计划%s";
        for(String entry:ocpGroups){
            ProductReq vr = product.clone();
            vr.setProductCode(orderId+entry);
            vr.setProductName(vr.getProductName()+String.format(suffix,entry));
            vr.setProfessionType(entry);
            vr.setHasSocialInsurance("N");
            data.add(vr);

        }
        return data;
    }

    /**
     * Vo转换
     * @param prop
     * @param req
     * @return
     */
    public static ZaGroupUnderwritingReq convertGroupApplyBean(ZaApiProperties prop,
                                                               Map<String,String> cvgRiskMap,
                                                               GroupUnderwriting req) {
        ZaGroupUnderwritingReq data = new ZaGroupUnderwritingReq();

        return data;
    }

    /**
     * 值	含义
     * 1	无
     * 2	年
     * 3	月
     * 4	日
     * 5	保终身
     * 6	保到某确定年龄
     * @param validPeriod
     * @return
     */
    private static Long mapPeriod(String validPeriod){
        if(StringUtils.isBlank(validPeriod)){
            return null;
        }
        String s = validPeriod.substring(0,validPeriod.length()-1);
        String numberReg="^\\d+(\\.\\d*)?$";
        if(s.matches(numberReg)){
            return Long.parseLong(s);
        }
        return null;
    }

    /**
     * 值	含义
     * 1	无
     * 2	年
     * 3	月
     * 4	日
     * 5	保终身
     * 6	保到某确定年龄
     * @param validPeriod
     * @return
     */
    private static Integer mapPeriodType(String validPeriod){
        if(StringUtils.isBlank(validPeriod)){
            return null;
        }
        String type = validPeriod.substring(validPeriod.length()-1);
        switch (type){
            case "年":
                return Integer.valueOf(2);
            case "月":
                return Integer.valueOf(3);
            case "日":
                return Integer.valueOf(4);
            default:
                return 1;
        }
    }
    /**
     * 企业联系人
     * @param applicant
     * @return
     */
    public static OrgHolder convertOrgBean(GroupApplicant applicant){
        OrgHolder holder = new OrgHolder();
        holder.setOrgName(applicant.getPersonName());
        holder.setOrgCertType(applicant.getIdType());
        holder.setOrgCertNo(applicant.getIdNumber());
        holder.setContactPerson(applicant.getLinker());
        if(StringUtils.isBlank(applicant.getLinker())){
            holder.setContactPerson(applicant.getCellPhone());
        }
        holder.setOrgProvinceCode(applicant.getProviceCode());
        holder.setOrgCityCode(applicant.getCityCode());
        holder.setOrgCountryCode(applicant.getCountryCode());
        holder.setOrgAddress(applicant.getAddress());
        holder.setContactMobile(applicant.getCellPhone());
        holder.setContactEmail(applicant.getEmail());
        return holder;

    }

    /**
     * 被保人信息
     * ★被保人的计划编码为[订单号+职业类别]
     * @param insuredList
     * @return
     */
    private static Set<String> fullInsured(String orderId, ZaGroupUnderwritingReq data, List<GroupInsured> insuredList) {
        Set<String> ocpGroups =  new HashSet<>();
        if(insuredList==null||insuredList.size()==0){
            return ocpGroups;
        }
        Insured[] insureds = new Insured[insuredList.size()];
        Insured entry;
        for(int i=0;i<insuredList.size();i++){
            entry=new Insured();
            GroupInsured ge = insuredList.get(i);
            entry.setProductCode(orderId+ge.getOccupationGroup());
            entry.setName(ge.getPersonName());
            entry.setCertType(ge.getIdType());
            entry.setCertNo(ge.getIdNumber());
            entry.setHasSocialInsurance("N");
            entry.setProfession(ge.getOccupationCode());
            entry.setRelationToMaster("1");
            insureds[i]=entry;
            ocpGroups.add(ge.getOccupationGroup());
        }
        data.setInsuredList(insureds);
        return ocpGroups;
    }
    /**
     * 被保人信息
     * @param insuredList
     * @return
     */
    private static String findHiProfession(GroupInsured[] insuredList) {
        String prof = "0";
        for(GroupInsured i:insuredList){
            if(i.getOccupationGroup()!=null&&prof.compareTo(i.getOccupationGroup())<0){
                prof=i.getOccupationGroup();
            }
        }
        return prof;
    }

    public static ZaGroupEndorReq convertGroupEndor(ZaApiProperties prop, GroupEndorsement req) {
        ZaGroupEndorReq endor = new ZaGroupEndorReq();
        endor.setChannelCode(prop.getGroupChannelCode());
        endor.setGroupPolicyNo(req.getPolicyNo());
        List<EndorInsured> insuredPersons = new ArrayList<>();
        List<GroupInsured> ins = req.getInsuredList();
        for(GroupInsured entry:ins){
            EndorInsured item = new EndorInsured();
            item.setPlanCode(entry.getProductCode());
            item.setName(entry.getPersonName());
            item.setGender(entry.getPersonGender());
            item.setCertType(entry.getIdType());
            item.setCertNo(entry.getIdNumber());
            item.setBirthday(entry.getBirthday());
            item.setOpType(entry.getOpType());
            insuredPersons.add(item);
        }
        endor.setInsuredPersons(insuredPersons);
        return endor;
    }
}
