package com.cfpamf.ms.insur.admin.external.tk.api;

import com.cfpamf.ms.insur.admin.external.OrderSubmitRequest;
import com.cfpamf.ms.insur.admin.external.fh.dto.FhInsuredPerson;
import com.cfpamf.ms.insur.admin.pojo.dto.aicheck.FamilyMemberQuestionnaireResultDTO;
import com.cfpamf.ms.insur.admin.service.BaseTest;
import com.cfpamf.ms.insur.base.util.HttpRequestUtil;
import com.github.jsonzou.jmockdata.JMockData;
import com.github.jsonzou.jmockdata.TypeReference;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.List;

/**
 * <AUTHOR> 2021/12/27 16:54
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({HttpRequestUtil.class})
public class TkApiServiceTest extends BaseTest {
    @InjectMocks
    TkApiService tkApiService;
    @org.mockito.Mock
    com.cfpamf.ms.insur.admin.external.tk.client.TkOrderClient orderClient;
    @org.mockito.Mock
    com.cfpamf.ms.insur.admin.external.tk.TkApiProperties properties;
    @org.mockito.Mock
    com.cfpamf.ms.insur.admin.service.SmProductOrderService productOrderService;
    @org.mockito.Mock
    com.cfpamf.ms.insur.admin.external.tk.client.TkOnlineServiceClient onlineServiceClient;
    @org.mockito.Mock
    com.fasterxml.jackson.databind.ObjectMapper mapper;

    @Test
    public void advanceCheck() {
        OrderSubmitRequest mock = JMockData.mock(OrderSubmitRequest.class, con());
        mock.getProductInfo().setProductId("1-1-1");
        mock.getProposerInfo().setPersonGender("1");
        mock.getOrderInfo().setStartTime("2022-11-11 11:11:11");
        for (FhInsuredPerson person : mock.getInsuredPerson()) {
            person.setPersonGender("1");
            person.setIsSecurity("1");
            person.setRelationship("1");
        }
        mock.getOrderInfo().setEndTime("2022-11-11 11:11:11");
        tkApiService.advanceCheck(mock, ",GUgjf,", JMockData.mock(new TypeReference<List<FamilyMemberQuestionnaireResultDTO>>() {
        },con()));
    }

    @Test
    public void getPayLink() {
            tkApiService.getPayLink(",SdXpn,", ",trhUI,", JMockData.mock(com.cfpamf.ms.insur.admin.external.tk.model.TkProductModel.class), JMockData.mock(com.cfpamf.ms.insur.admin.pojo.vo.SmOrderListVO.class), ",qHWKl,");
    }

    @Test
    public void refundNotify() {
        try {
            tkApiService.refundNotify(JMockData.mock(com.cfpamf.ms.insur.admin.pojo.po.order.SmCancelRefund.class));
        } catch (Exception e) {

        }
    }

    @Test
    public void acceptPolicy() {
        try {
            tkApiService.acceptPolicy(",gSlMA,", ",CaPJy,", JMockData.mock(com.cfpamf.ms.pay.facade.vo.QueryOrderVO.class));
        } catch (Exception e) {

        }
    }

    @Test
    public void parseCallback() {
        try {
            tkApiService.parseCallback(",gtohq,");
        } catch (Exception e) {

        }
    }

    @Test
    public void advanceIssue() {
        try {
            tkApiService.advanceIssue(",bJeeb,", ",AxYeP,", JMockData.mock(com.cfpamf.ms.insur.admin.external.tk.model.TkPayCallback.class));
        } catch (Exception e) {

        }
    }

    @Test
    public void onlineServiceSign() {
        try {
            tkApiService.onlineServiceSign();
        } catch (Exception e) {

        }
    }

    @Test
    public void check() {
        try {
            tkApiService.check(JMockData.mock(com.cfpamf.ms.insur.admin.external.OrderSubmitRequest.class), JMockData.mock(java.util.List.class));
        } catch (Exception e) {

        }
    }
}
