package com.cfpamf.ms.insur.weixin.TkGroup;

import com.alibaba.fastjson.JSON;
import com.cfpamf.ms.insur.admin.external.tk.TkApiProperties;
import com.cfpamf.ms.insur.admin.external.tk.client.TkGroupClient;
import com.cfpamf.ms.insur.admin.external.tk.model.TkRespBox;
import com.cfpamf.ms.insur.weixin.pojo.vo.apply.common.GroupUnderwriting;
import com.cfpamf.ms.insur.weixin.pojo.vo.apply.tk.request.*;
import com.cfpamf.ms.insur.weixin.pojo.vo.apply.tk.response.*;
import com.cfpamf.ms.insur.weixin.zaGroup.ZaMain;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.jsonzou.jmockdata.JMockData;
import org.junit.Test;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;

public class TkMain {

    private TkGroupClient apiClient = TkApiFactory.tkGroupClient();

    private TkApiProperties apiProperties = TkApiFactory.properties;

    private TKMessageBuilderTest messageBuilder = new TKMessageBuilderTest();

    @Test
    public void registerApplicant() {
        String demoFile = "/taikang/reg_applicant2.json";
        String data = readFile(demoFile);
        TKGroupApplicantRequest vo = JSON.parseObject(data, TKGroupApplicantRequest.class);
        System.out.println(JSON.toJSONString(vo));

        TkRespBox<TKEnterpriseRegResponse> response = apiClient.enterpriseReg(vo);
        System.err.println(response);
    }


    @Test
    public void quote() {
        String demoFile = "/taikang/quote1.json";
        String data = readFile(demoFile);
        TKGroupQuoteRequest vo = JSON.parseObject(data, TKGroupQuoteRequest.class);
        System.out.println(JSON.toJSONString(vo));
        TkRespBox<TKQuoteResponse> response = apiClient.quote(vo);
        System.err.println(JSON.toJSONString(response));
    }

    @Test
    public void createPolicy() {
        String demoFile = "/taikang/underwriter2.json";
        String data = readFile(demoFile);
        TKGroupCreateRequest request = JSON.parseObject(data, TKGroupCreateRequest.class);
        System.out.println(JSON.toJSONString(request));

        TkRespBox<TKCreatePolicyResponse> response = apiClient.createPolicy(request);
        System.err.println(JSON.toJSONString(response));
    }

    @Test
    public void addQuote() {
        String demoFile = "/taikang/addquote1.json";
        String data = readFile(demoFile);
        TKGroupQuoteRequest request = JSON.parseObject(data, TKGroupQuoteRequest.class);
        System.err.println(JSON.toJSONString(request));

        TkRespBox<TKQuoteResponse> response = apiClient.quote(request);
        System.err.println(JSON.toJSONString(response));
    }

    @Test
    public void addcommit() throws JsonProcessingException {
        String demoFile = "/taikang/addcommit1.json";
        String data = readFile(demoFile);
        TKGroupEndorRequest request = JSON.parseObject(data, TKGroupEndorRequest.class);
        System.err.println(JSON.toJSONString(request));

        TkRespBox<TKCreatePolicyResponse> response = apiClient.addCommit(request);
        System.err.println(JSON.toJSONString(response));
    }

    @Test
    public void pushOfflinePay(){
        String demoFile = "/taikang/offlinepay1.json";
        String data = readFile(demoFile);
        TKPayNoticeRequest request = JSON.parseObject(data, TKPayNoticeRequest.class);
        System.err.println(JSON.toJSONString(request));

        TkRespBox<Void> response = apiClient.transferAccount(request);
        System.err.println(JSON.toJSONString(response));
    }

    @Test
    public void delQuote(){
        String demoFile = "/taikang/delquote1.json";
        String data = readFile(demoFile);
        TKDeductionRequest request = JSON.parseObject(data, TKDeductionRequest.class);
        System.err.println(JSON.toJSONString(request));

        TkRespBox<TKDeductionResponse> response = apiClient.calPremium4Deduction(request);
        System.err.println(JSON.toJSONString(response));
    }

    @Test
    public void delCommit() throws JsonProcessingException {
        String demoFile = "/taikang/delcommit.json";
        String data = readFile(demoFile);
        TKDeductionRequest request = JSON.parseObject(data, TKDeductionRequest.class);
        System.err.println(JSON.toJSONString(request));

        ObjectMapper mapper = new ObjectMapper();
        String value = mapper.writeValueAsString(request);

        TkRespBox<TKDeductionResponse> response = apiClient.deductionCommit(request);
        System.err.println(JSON.toJSONString(response));

    }

    @Test
    public void replace() throws JsonProcessingException {
        String demoFile = "/taikang/replace.json";
        String data = readFile(demoFile);
        TKMemberChangeRequest request = JSON.parseObject(data, TKMemberChangeRequest.class);
        System.err.println(JSON.toJSONString(request));

        ObjectMapper mapper = new ObjectMapper();
        String value = mapper.writeValueAsString(request);

        TkRespBox<TKMemberChangeResponse> response = apiClient.memberChange(request);
        System.err.println(JSON.toJSONString(response));

    }

    @Test
    public void openInvoice() {
        String demoFile = "/taikang/openinvoice.json";
        String data = readFile(demoFile);
        TKInvoiceRequest request = JSON.parseObject(data, TKInvoiceRequest.class);
        System.err.println(JSON.toJSONString(request));

        TkRespBox<TKInvoiceResponse> response = apiClient.openInvoice(request);
        System.err.println(JSON.toJSONString(response));

    }

    /**********************************************************************************/
    public static String readFile(String file) {
        InputStream is = new ZaMain().getClass().getResourceAsStream(file);
        BufferedReader br = new BufferedReader(new InputStreamReader(is));
        String s = "";
        String temp = "";
        try {
            while ((s = br.readLine()) != null) {
                temp = temp + s;
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return temp;
    }
}
