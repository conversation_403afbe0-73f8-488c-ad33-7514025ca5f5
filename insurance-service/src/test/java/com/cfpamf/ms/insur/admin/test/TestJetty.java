//package com.cfpamf.ms.insur.admin.test;
//
//import org.eclipse.jetty.server.Handler;
//import org.eclipse.jetty.server.Request;
//import org.eclipse.jetty.server.Server;
//import org.eclipse.jetty.server.handler.AbstractHandler;
//import org.eclipse.jetty.server.handler.HandlerCollection;
//
//import javax.servlet.ServletException;
//import javax.servlet.http.HttpServletRequest;
//import javax.servlet.http.HttpServletResponse;
//import java.io.IOException;
//import java.io.OutputStream;
//
///**
// * <AUTHOR>
// */
//public class TestJetty {
//
//    public static void main(String[] args) throws InterruptedException {
//
//        Server server = new Server(9999);
//        HandlerCollection handlerc = new HandlerCollection();
//        handlerc.setHandlers(new Handler[]{new JettyServerHandler()});
//        server.setHandler(handlerc);
//        try {
//            try {
//                server.start();
//                server.join();
//            } catch (Exception var7) {
//            }
//        } finally {
//            ;
//        }
//    }
//
//
//    public static class JettyServerHandler extends AbstractHandler {
//
//        @Override
//        public void handle(String s, Request baseRequest, HttpServletRequest httpServletRequest, HttpServletResponse response) throws IOException, ServletException {
////            System.out.println("request=" + new String(HttpClientUtil.readBytes(baseRequest)));
//            response.setContentType("text/html;charset=utf-8");
//            response.setStatus(200);
//            baseRequest.setHandled(true);
//            OutputStream out = response.getOutputStream();
//            out.write("xxxxxxxxxxxxx".getBytes());
//            out.flush();
//        }
//    }
//}
