package com.cfpamf.ms.insur.admin.service.order;

import com.cfpamf.ms.insur.admin.config.ProductProperties;
import com.cfpamf.ms.insur.admin.config.ProductSpecialRuleProperties;
import com.cfpamf.ms.insur.admin.dao.safes.*;
import com.cfpamf.ms.insur.admin.dao.safes.auto.*;
import com.cfpamf.ms.insur.admin.dao.safes.order.*;
import com.cfpamf.ms.insur.admin.external.ChannelOrderService;
import com.cfpamf.ms.insur.admin.external.OrderSubmitRequest;
import com.cfpamf.ms.insur.admin.external.OrderSubmitResponse;
import com.cfpamf.ms.insur.admin.external.auto.AutoCvtHelper;
import com.cfpamf.ms.insur.admin.external.fh.dto.*;
import com.cfpamf.ms.insur.admin.external.tk.auto.TkAutoServiceAdapterImpl;
import com.cfpamf.ms.insur.admin.external.tk.auto.model.*;
import com.cfpamf.ms.insur.admin.external.tk.auto.util.TkAutoCvtHelper;
import com.cfpamf.ms.insur.admin.pojo.dto.SmCreateOrderSubmitRequest;
import com.cfpamf.ms.insur.admin.pojo.dto.auto.AutoOrderDTO;
import com.cfpamf.ms.insur.admin.pojo.dto.auto.AutoOrderPolicyDTO;
import com.cfpamf.ms.insur.admin.pojo.dto.order.SmOrderCarInfoDTO;
import com.cfpamf.ms.insur.admin.pojo.dto.order.SmOrderHouseDTO;
import com.cfpamf.ms.insur.admin.pojo.form.product.TestPremiumDutyForm;
import com.cfpamf.ms.insur.admin.pojo.form.product.TestPremiumProductForm;
import com.cfpamf.ms.insur.admin.pojo.po.auto.AmPolicyTk;
import com.cfpamf.ms.insur.admin.pojo.po.auto.AmPolicyTkDetail;
import com.cfpamf.ms.insur.admin.pojo.po.auto.AmSyncTkRecord;
import com.cfpamf.ms.insur.admin.pojo.po.auto.order.*;
import com.cfpamf.ms.insur.admin.pojo.vo.SmPlanVO;
import com.cfpamf.ms.insur.admin.renewal.service.OrderRenewalRecordService;
import com.cfpamf.ms.insur.admin.service.*;
import com.cfpamf.ms.insur.base.event.EventBusEngine;
import com.cfpamf.ms.insur.base.infeface.CustomerCenterService;
import com.cfpamf.ms.insur.base.service.BusinessTokenService;
import com.cfpamf.ms.insur.base.util.HttpRequestUtil;
import com.cfpamf.ms.insur.common.service.monitor.BusinessMonitorService;
import com.cfpamf.ms.insur.weixin.dao.safes.EndorMapper;
import com.cfpamf.ms.pay.facade.config.PayFacadeConfigProperties;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.slf4j.Logger;
import org.springframework.aop.framework.AopContext;
import tk.mybatis.mapper.entity.Config;
import tk.mybatis.mapper.mapperhelper.EntityHelper;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

@RunWith(PowerMockRunner.class)
@PrepareForTest({HttpRequestUtil.class,AopContext.class})
public class TkAutoOrderServiceTest extends BaseTest {

    @Mock private TkAutoServiceAdapterImpl mockAdapter;
    @Mock private TkAutoCvtHelper mockCvtHelper;
    @Mock private SmOrderMapper mockOrderMapper;
    @Mock private AmPolicyTkMapper mockPolicyTkMapper;
    @Mock private AmPolicyTkDetailMapper mockPolicyTkDetailMapper;
    @Mock private AmSyncTkRecordMapper mockRecordMapper;
    @Mock private AutoOrderExtendMapper autoOrderExtendMapper;
    @Mock private AutoCvtHelper autoCvtHelper;
    @Mock private AutoOrderMapper autoOrderMapper;
    @Mock private AutoOrderPolicyMapper autoOrderPolicyMapper;
    @Mock private AutoOrderPersonMapper autoOrderPersonMapper;
    @Mock private AutoOrderCarMapper autoOrderCarMapper;
    @Mock private AutoOrderReceiverMapper autoOrderReceiverMapper;
    @Mock private AutoOrderRiskMapper autoOrderRiskMapper;
    @Mock private Logger log;
    private int YEAR_NODE;
    private DateTimeFormatter FMT_PARSE;
    private DateTimeFormatter FMT_SUB;
    @Mock private BusinessMonitorService monitorService;
    @Mock private SmProductService productService;
    @Mock private BusinessTokenService tokenService;
    @Mock private CustomerCenterService ccService;
    @Mock private SmCommissionMapper cmsMapper;
    @Mock private SmOrderItemMapper orderItemMapper;
    @Mock private SmProductFormBuyLimitMapper formBuyLimitMapper;
    @Mock private AuthUserMapper userMapper;
    @Mock private OrderRenewalRecordService orderRenewalRecordService;
    @Mock private OrderCoreService orderCoreService;
    @Mock private SmChannelCallbackMapper channelCallbackMapper;
    @Mock private EventBusEngine busEngine;
    @Mock private SmOrderCarMapper carMapper;
    @Mock private SmOrderHouseMapper houseMapper;
    @Mock private SmOrderGroupNotifyService smOrderGroupNotifyService;
    @Mock private SmCommonSettingService commonSettingService;
    @Mock private SmOrderItemMapper smOrderItemMapper;
    @Mock private SmOrderRenewBindService smOrderRenewBindService;
    @Mock private SmXjxhService smXjxhService;
    @Mock private SmOrderPolicyMapper smOrderPolicyMapper;
    @Mock private ChOrderPersonalNotifyService chOrderPersonalNotifyService;
    @Mock private SmOrderRiskDutyMapper riskDutyMapper;
    @Mock private SmProductMapper productMapper;
    @Mock private SmOrderPolicyService smOrderPolicyService;
    @Mock private RenewalManagerService renewalManagerService;
    @Mock private SmOrderInsuredMapper insuredMapper;
    private long tokenLockTime;
    private int phoneLimit;
    @Mock private ProductProperties properties;
    @Mock private ProductSpecialRuleProperties productSpecialRuleProperties;
    @Mock private EndorMapper paymentMapper;
    @Mock private PayFacadeConfigProperties payFacadeConfigProperties;
    @Mock private TempOrderNewProductMapper tempOrderNewProductMapper;
    @InjectMocks private TkAutoOrderService tkAutoOrderServiceUnderTest;

    @Before
    public void setUp() throws Exception {
        super.setUp();
        EntityHelper.initEntityNameMap(AmPolicyTk.class, new Config());
        PowerMockito.mockStatic(AopContext.class);
        when(AopContext.currentProxy()).thenReturn(tkAutoOrderServiceUnderTest);
        Mockito.when(mock.getBean(Mockito.anyString(), (Class<Object>) Mockito.any()))
                .thenAnswer(a -> Mockito.mock(a.getArgument(0)));
    }

    @Test
    public void testGetJumpAutoUri() throws Exception {
        // Setup
        when(mockAdapter.getAutoQueryString("tkUserId")).thenReturn("result");

        try{
            // Run the test
            final String result = tkAutoOrderServiceUnderTest.getJumpAutoUri("tkUserId", 0);
        } catch(Exception e) {
        }


    }

    @Test
    public void testSync() throws Exception {
        // Setup
        // Configure TkAutoServiceAdapter.queryTkList(...).
        final TkAutoRespHelper<TkAutoQueryPolicyResp> tkAutoQueryPolicyRespTkAutoRespHelper = new TkAutoRespHelper<>();
        tkAutoQueryPolicyRespTkAutoRespHelper.setAppCode("1");
        tkAutoQueryPolicyRespTkAutoRespHelper.setAppMessage("appMessage");
        final TkAutoQueryPolicyResp tkAutoQueryPolicyResp = new TkAutoQueryPolicyResp();
        final TkAutoQueryPolicyDto tkAutoQueryPolicyDto = new TkAutoQueryPolicyDto();
        tkAutoQueryPolicyDto.setSubPolicyNo("subPolicyNo");
        tkAutoQueryPolicyResp.setQueryPolicyDtoList(Arrays.asList(tkAutoQueryPolicyDto));
        tkAutoQueryPolicyRespTkAutoRespHelper.setData(tkAutoQueryPolicyResp);
        tkAutoQueryPolicyRespTkAutoRespHelper.setContent("content");
        when(mockAdapter.queryTkList(LocalDate.of(2020, 1, 1), LocalDate.of(2020, 1, 1))).thenReturn(tkAutoQueryPolicyRespTkAutoRespHelper);

        // Configure AmPolicyTkMapper.selectByExample(...).
        final AmPolicyTk amPolicyTk = new AmPolicyTk();
        amPolicyTk.setPolicyNo("policyNo");
        amPolicyTk.setSubPolicyNo("subPolicyNo");
        amPolicyTk.setRiskName("riskName");
        amPolicyTk.setRiskCode("riskCode");
        amPolicyTk.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk.setPolicyUrl("policyDownLoadUrl");
        amPolicyTk.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk.setBenchmarkPremium(new BigDecimal("0.00"));
        amPolicyTk.setState(0);
        amPolicyTk.setRemark("msg");
        final List<AmPolicyTk> amPolicyTks = Arrays.asList(amPolicyTk);
        when(mockPolicyTkMapper.selectByExample(any(Object.class))).thenReturn(amPolicyTks);

        // Configure TkAutoServiceAdapter.queryTkDetail(...).
        final TkAutoRespHelper<TkAutoQueryPolicyDetailResp> tkAutoQueryPolicyDetailRespTkAutoRespHelper = new TkAutoRespHelper<>();
        tkAutoQueryPolicyDetailRespTkAutoRespHelper.setAppCode("1");
        tkAutoQueryPolicyDetailRespTkAutoRespHelper.setAppMessage("appMessage");
        final TkAutoQueryPolicyDetailResp tkAutoQueryPolicyDetailResp = new TkAutoQueryPolicyDetailResp();
        final TkAutoRiskInfoD tkAutoRiskInfoD = new TkAutoRiskInfoD();
        tkAutoRiskInfoD.setRiskCode("riskCode");
        final TkAutoBaseInfoD baseInfoD = new TkAutoBaseInfoD();
        baseInfoD.setSubPolicyNo("subPolicyNo");
        baseInfoD.setRiskName("riskName");
        baseInfoD.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD.setPremium(new BigDecimal("0.00"));
        baseInfoD.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TkAutoPolicyViewList policyViewList = new TkAutoPolicyViewList();
        final TkAutoPolicyData policyData = new TkAutoPolicyData();
        policyData.setPolicyDownLoadUrl("policyDownLoadUrl");
        policyViewList.setPolicyData(policyData);
        baseInfoD.setPolicyViewList(policyViewList);
        tkAutoRiskInfoD.setBaseInfoD(baseInfoD);
        final TkAutoRelatedInfoD tkAutoRelatedInfoD = new TkAutoRelatedInfoD();
        tkAutoRelatedInfoD.setInsureFlag("insureFlag");
        tkAutoRelatedInfoD.setInsureTypeCode("insureTypeCode");
        tkAutoRiskInfoD.setRelatedInfoDList(Arrays.asList(tkAutoRelatedInfoD));
        tkAutoQueryPolicyDetailResp.setRiskInfoList(Arrays.asList(tkAutoRiskInfoD));
        tkAutoQueryPolicyDetailRespTkAutoRespHelper.setData(tkAutoQueryPolicyDetailResp);
        tkAutoQueryPolicyDetailRespTkAutoRespHelper.setContent("content");
        when(mockAdapter.queryTkDetail("policyNo")).thenReturn(tkAutoQueryPolicyDetailRespTkAutoRespHelper);

        // Configure TkAutoCvtHelper.findDrivingTkAutoRiskInfoD(...).
        final TkAutoRiskInfoD tkAutoRiskInfoD1 = new TkAutoRiskInfoD();
        tkAutoRiskInfoD1.setRiskCode("riskCode");
        final TkAutoBaseInfoD baseInfoD1 = new TkAutoBaseInfoD();
        baseInfoD1.setSubPolicyNo("subPolicyNo");
        baseInfoD1.setRiskName("riskName");
        baseInfoD1.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD1.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD1.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD1.setPremium(new BigDecimal("0.00"));
        baseInfoD1.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TkAutoPolicyViewList policyViewList1 = new TkAutoPolicyViewList();
        final TkAutoPolicyData policyData1 = new TkAutoPolicyData();
        policyData1.setPolicyDownLoadUrl("policyDownLoadUrl");
        policyViewList1.setPolicyData(policyData1);
        baseInfoD1.setPolicyViewList(policyViewList1);
        tkAutoRiskInfoD1.setBaseInfoD(baseInfoD1);
        final TkAutoRelatedInfoD tkAutoRelatedInfoD1 = new TkAutoRelatedInfoD();
        tkAutoRelatedInfoD1.setInsureFlag("insureFlag");
        tkAutoRelatedInfoD1.setInsureTypeCode("insureTypeCode");
        tkAutoRiskInfoD1.setRelatedInfoDList(Arrays.asList(tkAutoRelatedInfoD1));
        final TkAutoRiskInfoD tkAutoRiskInfoD2 = new TkAutoRiskInfoD();
        tkAutoRiskInfoD2.setRiskCode("riskCode");
        final TkAutoBaseInfoD baseInfoD2 = new TkAutoBaseInfoD();
        baseInfoD2.setSubPolicyNo("subPolicyNo");
        baseInfoD2.setRiskName("riskName");
        baseInfoD2.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD2.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD2.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD2.setPremium(new BigDecimal("0.00"));
        baseInfoD2.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TkAutoPolicyViewList policyViewList2 = new TkAutoPolicyViewList();
        final TkAutoPolicyData policyData2 = new TkAutoPolicyData();
        policyData2.setPolicyDownLoadUrl("policyDownLoadUrl");
        policyViewList2.setPolicyData(policyData2);
        baseInfoD2.setPolicyViewList(policyViewList2);
        tkAutoRiskInfoD2.setBaseInfoD(baseInfoD2);
        final TkAutoRelatedInfoD tkAutoRelatedInfoD2 = new TkAutoRelatedInfoD();
        tkAutoRelatedInfoD2.setInsureFlag("insureFlag");
        tkAutoRelatedInfoD2.setInsureTypeCode("insureTypeCode");
        tkAutoRiskInfoD2.setRelatedInfoDList(Arrays.asList(tkAutoRelatedInfoD2));
        final List<TkAutoRiskInfoD> tkAutoRiskInfoDList = Arrays.asList(tkAutoRiskInfoD2);
        when(mockCvtHelper.findDrivingTkAutoRiskInfoD(tkAutoRiskInfoDList)).thenReturn(tkAutoRiskInfoD1);

        // Configure TkAutoCvtHelper.getRelatedInfoDList(...).
        final TkAutoRelatedInfoD tkAutoRelatedInfoD3 = new TkAutoRelatedInfoD();
        tkAutoRelatedInfoD3.setInsuredName("insuredName");
        tkAutoRelatedInfoD3.setInsureFlag("insureFlag");
        tkAutoRelatedInfoD3.setInsureTypeCode("insureTypeCode");
        tkAutoRelatedInfoD3.setInsureType("insureType");
        tkAutoRelatedInfoD3.setCertiType("certiType");
        final List<TkAutoRelatedInfoD> tkAutoRelatedInfoDS = Arrays.asList(tkAutoRelatedInfoD3);
        final TkAutoRiskInfoD tkAutoRiskInfoD3 = new TkAutoRiskInfoD();
        tkAutoRiskInfoD3.setRiskCode("riskCode");
        final TkAutoBaseInfoD baseInfoD3 = new TkAutoBaseInfoD();
        baseInfoD3.setSubPolicyNo("subPolicyNo");
        baseInfoD3.setRiskName("riskName");
        baseInfoD3.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD3.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD3.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD3.setPremium(new BigDecimal("0.00"));
        baseInfoD3.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TkAutoPolicyViewList policyViewList3 = new TkAutoPolicyViewList();
        final TkAutoPolicyData policyData3 = new TkAutoPolicyData();
        policyData3.setPolicyDownLoadUrl("policyDownLoadUrl");
        policyViewList3.setPolicyData(policyData3);
        baseInfoD3.setPolicyViewList(policyViewList3);
        tkAutoRiskInfoD3.setBaseInfoD(baseInfoD3);
        final TkAutoRelatedInfoD tkAutoRelatedInfoD4 = new TkAutoRelatedInfoD();
        tkAutoRelatedInfoD4.setInsureFlag("insureFlag");
        tkAutoRelatedInfoD4.setInsureTypeCode("insureTypeCode");
        tkAutoRiskInfoD3.setRelatedInfoDList(Arrays.asList(tkAutoRelatedInfoD4));
        final List<TkAutoRiskInfoD> tkAutoRiskInfoDList1 = Arrays.asList(tkAutoRiskInfoD3);
        when(mockCvtHelper.getRelatedInfoDList(tkAutoRiskInfoDList1)).thenReturn(tkAutoRelatedInfoDS);

        // Run the test
        tkAutoOrderServiceUnderTest.sync(LocalDate.of(2020, 1, 1), LocalDate.of(2020, 1, 1));

        // Verify the results
        // Confirm AmSyncTkRecordMapper.insertUseGeneratedKeys(...).
        final AmSyncTkRecord record = new AmSyncTkRecord();
        record.setBatchNo("batchNo");
        record.setStartDate(LocalDate.of(2020, 1, 1));
        record.setEndDate(LocalDate.of(2020, 1, 1));
        record.setOrderCount(0);
        record.setRespContent("respContent");

        // Confirm AmPolicyTkMapper.insertList(...).
        final AmPolicyTk amPolicyTk1 = new AmPolicyTk();
        amPolicyTk1.setPolicyNo("policyNo");
        amPolicyTk1.setSubPolicyNo("subPolicyNo");
        amPolicyTk1.setRiskName("riskName");
        amPolicyTk1.setRiskCode("riskCode");
        amPolicyTk1.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk1.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk1.setPolicyUrl("policyDownLoadUrl");
        amPolicyTk1.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk1.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk1.setBenchmarkPremium(new BigDecimal("0.00"));
        amPolicyTk1.setState(0);
        amPolicyTk1.setRemark("msg");
        final List<AmPolicyTk> recordList = Arrays.asList(amPolicyTk1);

        // Confirm AmPolicyTkMapper.updateByExampleSelective(...).
        final AmPolicyTk record1 = new AmPolicyTk();
        record1.setPolicyNo("policyNo");
        record1.setSubPolicyNo("subPolicyNo");
        record1.setRiskName("riskName");
        record1.setRiskCode("riskCode");
        record1.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record1.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record1.setPolicyUrl("policyDownLoadUrl");
        record1.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record1.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record1.setBenchmarkPremium(new BigDecimal("0.00"));
        record1.setState(0);
        record1.setRemark("msg");

        // Confirm AmPolicyTkMapper.insertUseGeneratedKeys(...).
        final AmPolicyTk record2 = new AmPolicyTk();
        record2.setPolicyNo("policyNo");
        record2.setSubPolicyNo("subPolicyNo");
        record2.setRiskName("riskName");
        record2.setRiskCode("riskCode");
        record2.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record2.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record2.setPolicyUrl("policyDownLoadUrl");
        record2.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record2.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record2.setBenchmarkPremium(new BigDecimal("0.00"));
        record2.setState(0);
        record2.setRemark("msg");

        // Confirm AmPolicyTkDetailMapper.insertUseGeneratedKeys(...).
        final AmPolicyTkDetail record3 = new AmPolicyTkDetail();
        record3.setPolicyNo("policyNo");
        record3.setContent("content");
    }

    @Test
    public void testSync_AmPolicyTkMapperSelectByExampleReturnsNoItems() throws Exception {
        // Setup
        // Configure TkAutoServiceAdapter.queryTkList(...).
        final TkAutoRespHelper<TkAutoQueryPolicyResp> tkAutoQueryPolicyRespTkAutoRespHelper = new TkAutoRespHelper<>();
        tkAutoQueryPolicyRespTkAutoRespHelper.setAppCode("1");
        tkAutoQueryPolicyRespTkAutoRespHelper.setAppMessage("appMessage");
        final TkAutoQueryPolicyResp tkAutoQueryPolicyResp = new TkAutoQueryPolicyResp();
        final TkAutoQueryPolicyDto tkAutoQueryPolicyDto = new TkAutoQueryPolicyDto();
        tkAutoQueryPolicyDto.setSubPolicyNo("subPolicyNo");
        tkAutoQueryPolicyResp.setQueryPolicyDtoList(Arrays.asList(tkAutoQueryPolicyDto));
        tkAutoQueryPolicyRespTkAutoRespHelper.setData(tkAutoQueryPolicyResp);
        tkAutoQueryPolicyRespTkAutoRespHelper.setContent("content");
        when(mockAdapter.queryTkList(LocalDate.of(2020, 1, 1), LocalDate.of(2020, 1, 1))).thenReturn(tkAutoQueryPolicyRespTkAutoRespHelper);

        when(mockPolicyTkMapper.selectByExample(any(Object.class))).thenReturn(Collections.emptyList());

        // Configure TkAutoServiceAdapter.queryTkDetail(...).
        final TkAutoRespHelper<TkAutoQueryPolicyDetailResp> tkAutoQueryPolicyDetailRespTkAutoRespHelper = new TkAutoRespHelper<>();
        tkAutoQueryPolicyDetailRespTkAutoRespHelper.setAppCode("1");
        tkAutoQueryPolicyDetailRespTkAutoRespHelper.setAppMessage("appMessage");
        final TkAutoQueryPolicyDetailResp tkAutoQueryPolicyDetailResp = new TkAutoQueryPolicyDetailResp();
        final TkAutoRiskInfoD tkAutoRiskInfoD = new TkAutoRiskInfoD();
        tkAutoRiskInfoD.setRiskCode("riskCode");
        final TkAutoBaseInfoD baseInfoD = new TkAutoBaseInfoD();
        baseInfoD.setSubPolicyNo("subPolicyNo");
        baseInfoD.setRiskName("riskName");
        baseInfoD.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD.setPremium(new BigDecimal("0.00"));
        baseInfoD.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TkAutoPolicyViewList policyViewList = new TkAutoPolicyViewList();
        final TkAutoPolicyData policyData = new TkAutoPolicyData();
        policyData.setPolicyDownLoadUrl("policyDownLoadUrl");
        policyViewList.setPolicyData(policyData);
        baseInfoD.setPolicyViewList(policyViewList);
        tkAutoRiskInfoD.setBaseInfoD(baseInfoD);
        final TkAutoRelatedInfoD tkAutoRelatedInfoD = new TkAutoRelatedInfoD();
        tkAutoRelatedInfoD.setInsureFlag("insureFlag");
        tkAutoRelatedInfoD.setInsureTypeCode("insureTypeCode");
        tkAutoRiskInfoD.setRelatedInfoDList(Arrays.asList(tkAutoRelatedInfoD));
        tkAutoQueryPolicyDetailResp.setRiskInfoList(Arrays.asList(tkAutoRiskInfoD));
        tkAutoQueryPolicyDetailRespTkAutoRespHelper.setData(tkAutoQueryPolicyDetailResp);
        tkAutoQueryPolicyDetailRespTkAutoRespHelper.setContent("content");
        when(mockAdapter.queryTkDetail("policyNo")).thenReturn(tkAutoQueryPolicyDetailRespTkAutoRespHelper);

        // Configure TkAutoCvtHelper.findDrivingTkAutoRiskInfoD(...).
        final TkAutoRiskInfoD tkAutoRiskInfoD1 = new TkAutoRiskInfoD();
        tkAutoRiskInfoD1.setRiskCode("riskCode");
        final TkAutoBaseInfoD baseInfoD1 = new TkAutoBaseInfoD();
        baseInfoD1.setSubPolicyNo("subPolicyNo");
        baseInfoD1.setRiskName("riskName");
        baseInfoD1.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD1.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD1.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD1.setPremium(new BigDecimal("0.00"));
        baseInfoD1.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TkAutoPolicyViewList policyViewList1 = new TkAutoPolicyViewList();
        final TkAutoPolicyData policyData1 = new TkAutoPolicyData();
        policyData1.setPolicyDownLoadUrl("policyDownLoadUrl");
        policyViewList1.setPolicyData(policyData1);
        baseInfoD1.setPolicyViewList(policyViewList1);
        tkAutoRiskInfoD1.setBaseInfoD(baseInfoD1);
        final TkAutoRelatedInfoD tkAutoRelatedInfoD1 = new TkAutoRelatedInfoD();
        tkAutoRelatedInfoD1.setInsureFlag("insureFlag");
        tkAutoRelatedInfoD1.setInsureTypeCode("insureTypeCode");
        tkAutoRiskInfoD1.setRelatedInfoDList(Arrays.asList(tkAutoRelatedInfoD1));
        final TkAutoRiskInfoD tkAutoRiskInfoD2 = new TkAutoRiskInfoD();
        tkAutoRiskInfoD2.setRiskCode("riskCode");
        final TkAutoBaseInfoD baseInfoD2 = new TkAutoBaseInfoD();
        baseInfoD2.setSubPolicyNo("subPolicyNo");
        baseInfoD2.setRiskName("riskName");
        baseInfoD2.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD2.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD2.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD2.setPremium(new BigDecimal("0.00"));
        baseInfoD2.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TkAutoPolicyViewList policyViewList2 = new TkAutoPolicyViewList();
        final TkAutoPolicyData policyData2 = new TkAutoPolicyData();
        policyData2.setPolicyDownLoadUrl("policyDownLoadUrl");
        policyViewList2.setPolicyData(policyData2);
        baseInfoD2.setPolicyViewList(policyViewList2);
        tkAutoRiskInfoD2.setBaseInfoD(baseInfoD2);
        final TkAutoRelatedInfoD tkAutoRelatedInfoD2 = new TkAutoRelatedInfoD();
        tkAutoRelatedInfoD2.setInsureFlag("insureFlag");
        tkAutoRelatedInfoD2.setInsureTypeCode("insureTypeCode");
        tkAutoRiskInfoD2.setRelatedInfoDList(Arrays.asList(tkAutoRelatedInfoD2));
        final List<TkAutoRiskInfoD> tkAutoRiskInfoDList = Arrays.asList(tkAutoRiskInfoD2);
        when(mockCvtHelper.findDrivingTkAutoRiskInfoD(tkAutoRiskInfoDList)).thenReturn(tkAutoRiskInfoD1);

        // Configure TkAutoCvtHelper.getRelatedInfoDList(...).
        final TkAutoRelatedInfoD tkAutoRelatedInfoD3 = new TkAutoRelatedInfoD();
        tkAutoRelatedInfoD3.setInsuredName("insuredName");
        tkAutoRelatedInfoD3.setInsureFlag("insureFlag");
        tkAutoRelatedInfoD3.setInsureTypeCode("insureTypeCode");
        tkAutoRelatedInfoD3.setInsureType("insureType");
        tkAutoRelatedInfoD3.setCertiType("certiType");
        final List<TkAutoRelatedInfoD> tkAutoRelatedInfoDS = Arrays.asList(tkAutoRelatedInfoD3);
        final TkAutoRiskInfoD tkAutoRiskInfoD3 = new TkAutoRiskInfoD();
        tkAutoRiskInfoD3.setRiskCode("riskCode");
        final TkAutoBaseInfoD baseInfoD3 = new TkAutoBaseInfoD();
        baseInfoD3.setSubPolicyNo("subPolicyNo");
        baseInfoD3.setRiskName("riskName");
        baseInfoD3.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD3.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD3.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD3.setPremium(new BigDecimal("0.00"));
        baseInfoD3.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TkAutoPolicyViewList policyViewList3 = new TkAutoPolicyViewList();
        final TkAutoPolicyData policyData3 = new TkAutoPolicyData();
        policyData3.setPolicyDownLoadUrl("policyDownLoadUrl");
        policyViewList3.setPolicyData(policyData3);
        baseInfoD3.setPolicyViewList(policyViewList3);
        tkAutoRiskInfoD3.setBaseInfoD(baseInfoD3);
        final TkAutoRelatedInfoD tkAutoRelatedInfoD4 = new TkAutoRelatedInfoD();
        tkAutoRelatedInfoD4.setInsureFlag("insureFlag");
        tkAutoRelatedInfoD4.setInsureTypeCode("insureTypeCode");
        tkAutoRiskInfoD3.setRelatedInfoDList(Arrays.asList(tkAutoRelatedInfoD4));
        final List<TkAutoRiskInfoD> tkAutoRiskInfoDList1 = Arrays.asList(tkAutoRiskInfoD3);
        when(mockCvtHelper.getRelatedInfoDList(tkAutoRiskInfoDList1)).thenReturn(tkAutoRelatedInfoDS);

        // Run the test
        tkAutoOrderServiceUnderTest.sync(LocalDate.of(2020, 1, 1), LocalDate.of(2020, 1, 1));

        // Verify the results
        // Confirm AmSyncTkRecordMapper.insertUseGeneratedKeys(...).
        final AmSyncTkRecord record = new AmSyncTkRecord();
        record.setBatchNo("batchNo");
        record.setStartDate(LocalDate.of(2020, 1, 1));
        record.setEndDate(LocalDate.of(2020, 1, 1));
        record.setOrderCount(0);
        record.setRespContent("respContent");

        // Confirm AmPolicyTkMapper.insertList(...).
        final AmPolicyTk amPolicyTk = new AmPolicyTk();
        amPolicyTk.setPolicyNo("policyNo");
        amPolicyTk.setSubPolicyNo("subPolicyNo");
        amPolicyTk.setRiskName("riskName");
        amPolicyTk.setRiskCode("riskCode");
        amPolicyTk.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk.setPolicyUrl("policyDownLoadUrl");
        amPolicyTk.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk.setBenchmarkPremium(new BigDecimal("0.00"));
        amPolicyTk.setState(0);
        amPolicyTk.setRemark("msg");
        final List<AmPolicyTk> recordList = Arrays.asList(amPolicyTk);

        // Confirm AmPolicyTkMapper.updateByExampleSelective(...).
        final AmPolicyTk record1 = new AmPolicyTk();
        record1.setPolicyNo("policyNo");
        record1.setSubPolicyNo("subPolicyNo");
        record1.setRiskName("riskName");
        record1.setRiskCode("riskCode");
        record1.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record1.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record1.setPolicyUrl("policyDownLoadUrl");
        record1.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record1.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record1.setBenchmarkPremium(new BigDecimal("0.00"));
        record1.setState(0);
        record1.setRemark("msg");

        // Confirm AmPolicyTkMapper.insertUseGeneratedKeys(...).
        final AmPolicyTk record2 = new AmPolicyTk();
        record2.setPolicyNo("policyNo");
        record2.setSubPolicyNo("subPolicyNo");
        record2.setRiskName("riskName");
        record2.setRiskCode("riskCode");
        record2.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record2.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record2.setPolicyUrl("policyDownLoadUrl");
        record2.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record2.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record2.setBenchmarkPremium(new BigDecimal("0.00"));
        record2.setState(0);
        record2.setRemark("msg");

        // Confirm AmPolicyTkDetailMapper.insertUseGeneratedKeys(...).
        final AmPolicyTkDetail record3 = new AmPolicyTkDetail();
        record3.setPolicyNo("policyNo");
        record3.setContent("content");
    }

    @Test
    public void testSync_TkAutoCvtHelperFindDrivingTkAutoRiskInfoDReturnsNull() throws Exception {
        // Setup
        // Configure TkAutoServiceAdapter.queryTkList(...).
        final TkAutoRespHelper<TkAutoQueryPolicyResp> tkAutoQueryPolicyRespTkAutoRespHelper = new TkAutoRespHelper<>();
        tkAutoQueryPolicyRespTkAutoRespHelper.setAppCode("1");
        tkAutoQueryPolicyRespTkAutoRespHelper.setAppMessage("appMessage");
        final TkAutoQueryPolicyResp tkAutoQueryPolicyResp = new TkAutoQueryPolicyResp();
        final TkAutoQueryPolicyDto tkAutoQueryPolicyDto = new TkAutoQueryPolicyDto();
        tkAutoQueryPolicyDto.setSubPolicyNo("subPolicyNo");
        tkAutoQueryPolicyResp.setQueryPolicyDtoList(Arrays.asList(tkAutoQueryPolicyDto));
        tkAutoQueryPolicyRespTkAutoRespHelper.setData(tkAutoQueryPolicyResp);
        tkAutoQueryPolicyRespTkAutoRespHelper.setContent("content");
        when(mockAdapter.queryTkList(LocalDate.of(2020, 1, 1), LocalDate.of(2020, 1, 1))).thenReturn(tkAutoQueryPolicyRespTkAutoRespHelper);

        // Configure AmPolicyTkMapper.selectByExample(...).
        final AmPolicyTk amPolicyTk = new AmPolicyTk();
        amPolicyTk.setPolicyNo("policyNo");
        amPolicyTk.setSubPolicyNo("subPolicyNo");
        amPolicyTk.setRiskName("riskName");
        amPolicyTk.setRiskCode("riskCode");
        amPolicyTk.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk.setPolicyUrl("policyDownLoadUrl");
        amPolicyTk.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk.setBenchmarkPremium(new BigDecimal("0.00"));
        amPolicyTk.setState(0);
        amPolicyTk.setRemark("msg");
        final List<AmPolicyTk> amPolicyTks = Arrays.asList(amPolicyTk);
        when(mockPolicyTkMapper.selectByExample(any(Object.class))).thenReturn(amPolicyTks);

        // Configure TkAutoServiceAdapter.queryTkDetail(...).
        final TkAutoRespHelper<TkAutoQueryPolicyDetailResp> tkAutoQueryPolicyDetailRespTkAutoRespHelper = new TkAutoRespHelper<>();
        tkAutoQueryPolicyDetailRespTkAutoRespHelper.setAppCode("1");
        tkAutoQueryPolicyDetailRespTkAutoRespHelper.setAppMessage("appMessage");
        final TkAutoQueryPolicyDetailResp tkAutoQueryPolicyDetailResp = new TkAutoQueryPolicyDetailResp();
        final TkAutoRiskInfoD tkAutoRiskInfoD = new TkAutoRiskInfoD();
        tkAutoRiskInfoD.setRiskCode("riskCode");
        final TkAutoBaseInfoD baseInfoD = new TkAutoBaseInfoD();
        baseInfoD.setSubPolicyNo("subPolicyNo");
        baseInfoD.setRiskName("riskName");
        baseInfoD.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD.setPremium(new BigDecimal("0.00"));
        baseInfoD.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TkAutoPolicyViewList policyViewList = new TkAutoPolicyViewList();
        final TkAutoPolicyData policyData = new TkAutoPolicyData();
        policyData.setPolicyDownLoadUrl("policyDownLoadUrl");
        policyViewList.setPolicyData(policyData);
        baseInfoD.setPolicyViewList(policyViewList);
        tkAutoRiskInfoD.setBaseInfoD(baseInfoD);
        final TkAutoRelatedInfoD tkAutoRelatedInfoD = new TkAutoRelatedInfoD();
        tkAutoRelatedInfoD.setInsureFlag("insureFlag");
        tkAutoRelatedInfoD.setInsureTypeCode("insureTypeCode");
        tkAutoRiskInfoD.setRelatedInfoDList(Arrays.asList(tkAutoRelatedInfoD));
        tkAutoQueryPolicyDetailResp.setRiskInfoList(Arrays.asList(tkAutoRiskInfoD));
        tkAutoQueryPolicyDetailRespTkAutoRespHelper.setData(tkAutoQueryPolicyDetailResp);
        tkAutoQueryPolicyDetailRespTkAutoRespHelper.setContent("content");
        when(mockAdapter.queryTkDetail("policyNo")).thenReturn(tkAutoQueryPolicyDetailRespTkAutoRespHelper);

        // Configure TkAutoCvtHelper.findDrivingTkAutoRiskInfoD(...).
        final TkAutoRiskInfoD tkAutoRiskInfoD1 = new TkAutoRiskInfoD();
        tkAutoRiskInfoD1.setRiskCode("riskCode");
        final TkAutoBaseInfoD baseInfoD1 = new TkAutoBaseInfoD();
        baseInfoD1.setSubPolicyNo("subPolicyNo");
        baseInfoD1.setRiskName("riskName");
        baseInfoD1.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD1.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD1.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD1.setPremium(new BigDecimal("0.00"));
        baseInfoD1.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TkAutoPolicyViewList policyViewList1 = new TkAutoPolicyViewList();
        final TkAutoPolicyData policyData1 = new TkAutoPolicyData();
        policyData1.setPolicyDownLoadUrl("policyDownLoadUrl");
        policyViewList1.setPolicyData(policyData1);
        baseInfoD1.setPolicyViewList(policyViewList1);
        tkAutoRiskInfoD1.setBaseInfoD(baseInfoD1);
        final TkAutoRelatedInfoD tkAutoRelatedInfoD1 = new TkAutoRelatedInfoD();
        tkAutoRelatedInfoD1.setInsureFlag("insureFlag");
        tkAutoRelatedInfoD1.setInsureTypeCode("insureTypeCode");
        tkAutoRiskInfoD1.setRelatedInfoDList(Arrays.asList(tkAutoRelatedInfoD1));
        final List<TkAutoRiskInfoD> tkAutoRiskInfoDList = Arrays.asList(tkAutoRiskInfoD1);
        when(mockCvtHelper.findDrivingTkAutoRiskInfoD(tkAutoRiskInfoDList)).thenReturn(null);

        // Run the test
        tkAutoOrderServiceUnderTest.sync(LocalDate.of(2020, 1, 1), LocalDate.of(2020, 1, 1));

        // Verify the results
        // Confirm AmSyncTkRecordMapper.insertUseGeneratedKeys(...).
        final AmSyncTkRecord record = new AmSyncTkRecord();
        record.setBatchNo("batchNo");
        record.setStartDate(LocalDate.of(2020, 1, 1));
        record.setEndDate(LocalDate.of(2020, 1, 1));
        record.setOrderCount(0);
        record.setRespContent("respContent");

        // Confirm AmPolicyTkMapper.insertList(...).
        final AmPolicyTk amPolicyTk1 = new AmPolicyTk();
        amPolicyTk1.setPolicyNo("policyNo");
        amPolicyTk1.setSubPolicyNo("subPolicyNo");
        amPolicyTk1.setRiskName("riskName");
        amPolicyTk1.setRiskCode("riskCode");
        amPolicyTk1.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk1.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk1.setPolicyUrl("policyDownLoadUrl");
        amPolicyTk1.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk1.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk1.setBenchmarkPremium(new BigDecimal("0.00"));
        amPolicyTk1.setState(0);
        amPolicyTk1.setRemark("msg");
        final List<AmPolicyTk> recordList = Arrays.asList(amPolicyTk1);

        // Confirm AmPolicyTkMapper.updateByExampleSelective(...).
        final AmPolicyTk record1 = new AmPolicyTk();
        record1.setPolicyNo("policyNo");
        record1.setSubPolicyNo("subPolicyNo");
        record1.setRiskName("riskName");
        record1.setRiskCode("riskCode");
        record1.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record1.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record1.setPolicyUrl("policyDownLoadUrl");
        record1.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record1.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record1.setBenchmarkPremium(new BigDecimal("0.00"));
        record1.setState(0);
        record1.setRemark("msg");

        // Confirm AmPolicyTkMapper.insertUseGeneratedKeys(...).
        final AmPolicyTk record2 = new AmPolicyTk();
        record2.setPolicyNo("policyNo");
        record2.setSubPolicyNo("subPolicyNo");
        record2.setRiskName("riskName");
        record2.setRiskCode("riskCode");
        record2.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record2.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record2.setPolicyUrl("policyDownLoadUrl");
        record2.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record2.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record2.setBenchmarkPremium(new BigDecimal("0.00"));
        record2.setState(0);
        record2.setRemark("msg");

        // Confirm AmPolicyTkDetailMapper.insertUseGeneratedKeys(...).
        final AmPolicyTkDetail record3 = new AmPolicyTkDetail();
        record3.setPolicyNo("policyNo");
        record3.setContent("content");
    }

    @Test
    public void testSync_TkAutoCvtHelperGetRelatedInfoDListReturnsNoItems() throws Exception {
        // Setup
        // Configure TkAutoServiceAdapter.queryTkList(...).
        final TkAutoRespHelper<TkAutoQueryPolicyResp> tkAutoQueryPolicyRespTkAutoRespHelper = new TkAutoRespHelper<>();
        tkAutoQueryPolicyRespTkAutoRespHelper.setAppCode("1");
        tkAutoQueryPolicyRespTkAutoRespHelper.setAppMessage("appMessage");
        final TkAutoQueryPolicyResp tkAutoQueryPolicyResp = new TkAutoQueryPolicyResp();
        final TkAutoQueryPolicyDto tkAutoQueryPolicyDto = new TkAutoQueryPolicyDto();
        tkAutoQueryPolicyDto.setSubPolicyNo("subPolicyNo");
        tkAutoQueryPolicyResp.setQueryPolicyDtoList(Arrays.asList(tkAutoQueryPolicyDto));
        tkAutoQueryPolicyRespTkAutoRespHelper.setData(tkAutoQueryPolicyResp);
        tkAutoQueryPolicyRespTkAutoRespHelper.setContent("content");
        when(mockAdapter.queryTkList(LocalDate.of(2020, 1, 1), LocalDate.of(2020, 1, 1))).thenReturn(tkAutoQueryPolicyRespTkAutoRespHelper);

        // Configure AmPolicyTkMapper.selectByExample(...).
        final AmPolicyTk amPolicyTk = new AmPolicyTk();
        amPolicyTk.setPolicyNo("policyNo");
        amPolicyTk.setSubPolicyNo("subPolicyNo");
        amPolicyTk.setRiskName("riskName");
        amPolicyTk.setRiskCode("riskCode");
        amPolicyTk.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk.setPolicyUrl("policyDownLoadUrl");
        amPolicyTk.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk.setBenchmarkPremium(new BigDecimal("0.00"));
        amPolicyTk.setState(0);
        amPolicyTk.setRemark("msg");
        final List<AmPolicyTk> amPolicyTks = Arrays.asList(amPolicyTk);
        when(mockPolicyTkMapper.selectByExample(any(Object.class))).thenReturn(amPolicyTks);

        // Configure TkAutoServiceAdapter.queryTkDetail(...).
        final TkAutoRespHelper<TkAutoQueryPolicyDetailResp> tkAutoQueryPolicyDetailRespTkAutoRespHelper = new TkAutoRespHelper<>();
        tkAutoQueryPolicyDetailRespTkAutoRespHelper.setAppCode("1");
        tkAutoQueryPolicyDetailRespTkAutoRespHelper.setAppMessage("appMessage");
        final TkAutoQueryPolicyDetailResp tkAutoQueryPolicyDetailResp = new TkAutoQueryPolicyDetailResp();
        final TkAutoRiskInfoD tkAutoRiskInfoD = new TkAutoRiskInfoD();
        tkAutoRiskInfoD.setRiskCode("riskCode");
        final TkAutoBaseInfoD baseInfoD = new TkAutoBaseInfoD();
        baseInfoD.setSubPolicyNo("subPolicyNo");
        baseInfoD.setRiskName("riskName");
        baseInfoD.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD.setPremium(new BigDecimal("0.00"));
        baseInfoD.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TkAutoPolicyViewList policyViewList = new TkAutoPolicyViewList();
        final TkAutoPolicyData policyData = new TkAutoPolicyData();
        policyData.setPolicyDownLoadUrl("policyDownLoadUrl");
        policyViewList.setPolicyData(policyData);
        baseInfoD.setPolicyViewList(policyViewList);
        tkAutoRiskInfoD.setBaseInfoD(baseInfoD);
        final TkAutoRelatedInfoD tkAutoRelatedInfoD = new TkAutoRelatedInfoD();
        tkAutoRelatedInfoD.setInsureFlag("insureFlag");
        tkAutoRelatedInfoD.setInsureTypeCode("insureTypeCode");
        tkAutoRiskInfoD.setRelatedInfoDList(Arrays.asList(tkAutoRelatedInfoD));
        tkAutoQueryPolicyDetailResp.setRiskInfoList(Arrays.asList(tkAutoRiskInfoD));
        tkAutoQueryPolicyDetailRespTkAutoRespHelper.setData(tkAutoQueryPolicyDetailResp);
        tkAutoQueryPolicyDetailRespTkAutoRespHelper.setContent("content");
        when(mockAdapter.queryTkDetail("policyNo")).thenReturn(tkAutoQueryPolicyDetailRespTkAutoRespHelper);

        // Configure TkAutoCvtHelper.findDrivingTkAutoRiskInfoD(...).
        final TkAutoRiskInfoD tkAutoRiskInfoD1 = new TkAutoRiskInfoD();
        tkAutoRiskInfoD1.setRiskCode("riskCode");
        final TkAutoBaseInfoD baseInfoD1 = new TkAutoBaseInfoD();
        baseInfoD1.setSubPolicyNo("subPolicyNo");
        baseInfoD1.setRiskName("riskName");
        baseInfoD1.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD1.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD1.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD1.setPremium(new BigDecimal("0.00"));
        baseInfoD1.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TkAutoPolicyViewList policyViewList1 = new TkAutoPolicyViewList();
        final TkAutoPolicyData policyData1 = new TkAutoPolicyData();
        policyData1.setPolicyDownLoadUrl("policyDownLoadUrl");
        policyViewList1.setPolicyData(policyData1);
        baseInfoD1.setPolicyViewList(policyViewList1);
        tkAutoRiskInfoD1.setBaseInfoD(baseInfoD1);
        final TkAutoRelatedInfoD tkAutoRelatedInfoD1 = new TkAutoRelatedInfoD();
        tkAutoRelatedInfoD1.setInsureFlag("insureFlag");
        tkAutoRelatedInfoD1.setInsureTypeCode("insureTypeCode");
        tkAutoRiskInfoD1.setRelatedInfoDList(Arrays.asList(tkAutoRelatedInfoD1));
        final TkAutoRiskInfoD tkAutoRiskInfoD2 = new TkAutoRiskInfoD();
        tkAutoRiskInfoD2.setRiskCode("riskCode");
        final TkAutoBaseInfoD baseInfoD2 = new TkAutoBaseInfoD();
        baseInfoD2.setSubPolicyNo("subPolicyNo");
        baseInfoD2.setRiskName("riskName");
        baseInfoD2.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD2.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD2.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD2.setPremium(new BigDecimal("0.00"));
        baseInfoD2.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TkAutoPolicyViewList policyViewList2 = new TkAutoPolicyViewList();
        final TkAutoPolicyData policyData2 = new TkAutoPolicyData();
        policyData2.setPolicyDownLoadUrl("policyDownLoadUrl");
        policyViewList2.setPolicyData(policyData2);
        baseInfoD2.setPolicyViewList(policyViewList2);
        tkAutoRiskInfoD2.setBaseInfoD(baseInfoD2);
        final TkAutoRelatedInfoD tkAutoRelatedInfoD2 = new TkAutoRelatedInfoD();
        tkAutoRelatedInfoD2.setInsureFlag("insureFlag");
        tkAutoRelatedInfoD2.setInsureTypeCode("insureTypeCode");
        tkAutoRiskInfoD2.setRelatedInfoDList(Arrays.asList(tkAutoRelatedInfoD2));
        final List<TkAutoRiskInfoD> tkAutoRiskInfoDList = Arrays.asList(tkAutoRiskInfoD2);
        when(mockCvtHelper.findDrivingTkAutoRiskInfoD(tkAutoRiskInfoDList)).thenReturn(tkAutoRiskInfoD1);

        // Configure TkAutoCvtHelper.getRelatedInfoDList(...).
        final TkAutoRiskInfoD tkAutoRiskInfoD3 = new TkAutoRiskInfoD();
        tkAutoRiskInfoD3.setRiskCode("riskCode");
        final TkAutoBaseInfoD baseInfoD3 = new TkAutoBaseInfoD();
        baseInfoD3.setSubPolicyNo("subPolicyNo");
        baseInfoD3.setRiskName("riskName");
        baseInfoD3.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD3.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD3.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD3.setPremium(new BigDecimal("0.00"));
        baseInfoD3.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TkAutoPolicyViewList policyViewList3 = new TkAutoPolicyViewList();
        final TkAutoPolicyData policyData3 = new TkAutoPolicyData();
        policyData3.setPolicyDownLoadUrl("policyDownLoadUrl");
        policyViewList3.setPolicyData(policyData3);
        baseInfoD3.setPolicyViewList(policyViewList3);
        tkAutoRiskInfoD3.setBaseInfoD(baseInfoD3);
        final TkAutoRelatedInfoD tkAutoRelatedInfoD3 = new TkAutoRelatedInfoD();
        tkAutoRelatedInfoD3.setInsureFlag("insureFlag");
        tkAutoRelatedInfoD3.setInsureTypeCode("insureTypeCode");
        tkAutoRiskInfoD3.setRelatedInfoDList(Arrays.asList(tkAutoRelatedInfoD3));
        final List<TkAutoRiskInfoD> tkAutoRiskInfoDList1 = Arrays.asList(tkAutoRiskInfoD3);
        when(mockCvtHelper.getRelatedInfoDList(tkAutoRiskInfoDList1)).thenReturn(Collections.emptyList());

        // Run the test
        tkAutoOrderServiceUnderTest.sync(LocalDate.of(2020, 1, 1), LocalDate.of(2020, 1, 1));

        // Verify the results
        // Confirm AmSyncTkRecordMapper.insertUseGeneratedKeys(...).
        final AmSyncTkRecord record = new AmSyncTkRecord();
        record.setBatchNo("batchNo");
        record.setStartDate(LocalDate.of(2020, 1, 1));
        record.setEndDate(LocalDate.of(2020, 1, 1));
        record.setOrderCount(0);
        record.setRespContent("respContent");

        // Confirm AmPolicyTkMapper.insertList(...).
        final AmPolicyTk amPolicyTk1 = new AmPolicyTk();
        amPolicyTk1.setPolicyNo("policyNo");
        amPolicyTk1.setSubPolicyNo("subPolicyNo");
        amPolicyTk1.setRiskName("riskName");
        amPolicyTk1.setRiskCode("riskCode");
        amPolicyTk1.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk1.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk1.setPolicyUrl("policyDownLoadUrl");
        amPolicyTk1.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk1.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk1.setBenchmarkPremium(new BigDecimal("0.00"));
        amPolicyTk1.setState(0);
        amPolicyTk1.setRemark("msg");
        final List<AmPolicyTk> recordList = Arrays.asList(amPolicyTk1);

        // Confirm AmPolicyTkMapper.updateByExampleSelective(...).
        final AmPolicyTk record1 = new AmPolicyTk();
        record1.setPolicyNo("policyNo");
        record1.setSubPolicyNo("subPolicyNo");
        record1.setRiskName("riskName");
        record1.setRiskCode("riskCode");
        record1.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record1.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record1.setPolicyUrl("policyDownLoadUrl");
        record1.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record1.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record1.setBenchmarkPremium(new BigDecimal("0.00"));
        record1.setState(0);
        record1.setRemark("msg");

        // Confirm AmPolicyTkMapper.insertUseGeneratedKeys(...).
        final AmPolicyTk record2 = new AmPolicyTk();
        record2.setPolicyNo("policyNo");
        record2.setSubPolicyNo("subPolicyNo");
        record2.setRiskName("riskName");
        record2.setRiskCode("riskCode");
        record2.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record2.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record2.setPolicyUrl("policyDownLoadUrl");
        record2.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record2.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record2.setBenchmarkPremium(new BigDecimal("0.00"));
        record2.setState(0);
        record2.setRemark("msg");

        // Confirm AmPolicyTkDetailMapper.insertUseGeneratedKeys(...).
        final AmPolicyTkDetail record3 = new AmPolicyTkDetail();
        record3.setPolicyNo("policyNo");
        record3.setContent("content");
    }

    @Test
    public void testSync_TkAutoCvtHelperCvtOrderInfoReturnsNoItems() throws Exception {
        // Setup
        // Configure TkAutoServiceAdapter.queryTkList(...).
        final TkAutoRespHelper<TkAutoQueryPolicyResp> tkAutoQueryPolicyRespTkAutoRespHelper = new TkAutoRespHelper<>();
        tkAutoQueryPolicyRespTkAutoRespHelper.setAppCode("1");
        tkAutoQueryPolicyRespTkAutoRespHelper.setAppMessage("appMessage");
        final TkAutoQueryPolicyResp tkAutoQueryPolicyResp = new TkAutoQueryPolicyResp();
        final TkAutoQueryPolicyDto tkAutoQueryPolicyDto = new TkAutoQueryPolicyDto();
        tkAutoQueryPolicyDto.setSubPolicyNo("subPolicyNo");
        tkAutoQueryPolicyResp.setQueryPolicyDtoList(Arrays.asList(tkAutoQueryPolicyDto));
        tkAutoQueryPolicyRespTkAutoRespHelper.setData(tkAutoQueryPolicyResp);
        tkAutoQueryPolicyRespTkAutoRespHelper.setContent("content");
        when(mockAdapter.queryTkList(LocalDate.of(2020, 1, 1), LocalDate.of(2020, 1, 1))).thenReturn(tkAutoQueryPolicyRespTkAutoRespHelper);

        // Configure AmPolicyTkMapper.selectByExample(...).
        final AmPolicyTk amPolicyTk = new AmPolicyTk();
        amPolicyTk.setPolicyNo("policyNo");
        amPolicyTk.setSubPolicyNo("subPolicyNo");
        amPolicyTk.setRiskName("riskName");
        amPolicyTk.setRiskCode("riskCode");
        amPolicyTk.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk.setPolicyUrl("policyDownLoadUrl");
        amPolicyTk.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk.setBenchmarkPremium(new BigDecimal("0.00"));
        amPolicyTk.setState(0);
        amPolicyTk.setRemark("msg");
        final List<AmPolicyTk> amPolicyTks = Arrays.asList(amPolicyTk);
        when(mockPolicyTkMapper.selectByExample(any(Object.class))).thenReturn(amPolicyTks);

        // Configure TkAutoServiceAdapter.queryTkDetail(...).
        final TkAutoRespHelper<TkAutoQueryPolicyDetailResp> tkAutoQueryPolicyDetailRespTkAutoRespHelper = new TkAutoRespHelper<>();
        tkAutoQueryPolicyDetailRespTkAutoRespHelper.setAppCode("1");
        tkAutoQueryPolicyDetailRespTkAutoRespHelper.setAppMessage("appMessage");
        final TkAutoQueryPolicyDetailResp tkAutoQueryPolicyDetailResp = new TkAutoQueryPolicyDetailResp();
        final TkAutoRiskInfoD tkAutoRiskInfoD = new TkAutoRiskInfoD();
        tkAutoRiskInfoD.setRiskCode("riskCode");
        final TkAutoBaseInfoD baseInfoD = new TkAutoBaseInfoD();
        baseInfoD.setSubPolicyNo("subPolicyNo");
        baseInfoD.setRiskName("riskName");
        baseInfoD.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD.setPremium(new BigDecimal("0.00"));
        baseInfoD.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TkAutoPolicyViewList policyViewList = new TkAutoPolicyViewList();
        final TkAutoPolicyData policyData = new TkAutoPolicyData();
        policyData.setPolicyDownLoadUrl("policyDownLoadUrl");
        policyViewList.setPolicyData(policyData);
        baseInfoD.setPolicyViewList(policyViewList);
        tkAutoRiskInfoD.setBaseInfoD(baseInfoD);
        final TkAutoRelatedInfoD tkAutoRelatedInfoD = new TkAutoRelatedInfoD();
        tkAutoRelatedInfoD.setInsureFlag("insureFlag");
        tkAutoRelatedInfoD.setInsureTypeCode("insureTypeCode");
        tkAutoRiskInfoD.setRelatedInfoDList(Arrays.asList(tkAutoRelatedInfoD));
        tkAutoQueryPolicyDetailResp.setRiskInfoList(Arrays.asList(tkAutoRiskInfoD));
        tkAutoQueryPolicyDetailRespTkAutoRespHelper.setData(tkAutoQueryPolicyDetailResp);
        tkAutoQueryPolicyDetailRespTkAutoRespHelper.setContent("content");
        when(mockAdapter.queryTkDetail("policyNo")).thenReturn(tkAutoQueryPolicyDetailRespTkAutoRespHelper);

        // Configure TkAutoCvtHelper.findDrivingTkAutoRiskInfoD(...).
        final TkAutoRiskInfoD tkAutoRiskInfoD1 = new TkAutoRiskInfoD();
        tkAutoRiskInfoD1.setRiskCode("riskCode");
        final TkAutoBaseInfoD baseInfoD1 = new TkAutoBaseInfoD();
        baseInfoD1.setSubPolicyNo("subPolicyNo");
        baseInfoD1.setRiskName("riskName");
        baseInfoD1.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD1.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD1.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD1.setPremium(new BigDecimal("0.00"));
        baseInfoD1.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TkAutoPolicyViewList policyViewList1 = new TkAutoPolicyViewList();
        final TkAutoPolicyData policyData1 = new TkAutoPolicyData();
        policyData1.setPolicyDownLoadUrl("policyDownLoadUrl");
        policyViewList1.setPolicyData(policyData1);
        baseInfoD1.setPolicyViewList(policyViewList1);
        tkAutoRiskInfoD1.setBaseInfoD(baseInfoD1);
        final TkAutoRelatedInfoD tkAutoRelatedInfoD1 = new TkAutoRelatedInfoD();
        tkAutoRelatedInfoD1.setInsureFlag("insureFlag");
        tkAutoRelatedInfoD1.setInsureTypeCode("insureTypeCode");
        tkAutoRiskInfoD1.setRelatedInfoDList(Arrays.asList(tkAutoRelatedInfoD1));
        final TkAutoRiskInfoD tkAutoRiskInfoD2 = new TkAutoRiskInfoD();
        tkAutoRiskInfoD2.setRiskCode("riskCode");
        final TkAutoBaseInfoD baseInfoD2 = new TkAutoBaseInfoD();
        baseInfoD2.setSubPolicyNo("subPolicyNo");
        baseInfoD2.setRiskName("riskName");
        baseInfoD2.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD2.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD2.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD2.setPremium(new BigDecimal("0.00"));
        baseInfoD2.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TkAutoPolicyViewList policyViewList2 = new TkAutoPolicyViewList();
        final TkAutoPolicyData policyData2 = new TkAutoPolicyData();
        policyData2.setPolicyDownLoadUrl("policyDownLoadUrl");
        policyViewList2.setPolicyData(policyData2);
        baseInfoD2.setPolicyViewList(policyViewList2);
        tkAutoRiskInfoD2.setBaseInfoD(baseInfoD2);
        final TkAutoRelatedInfoD tkAutoRelatedInfoD2 = new TkAutoRelatedInfoD();
        tkAutoRelatedInfoD2.setInsureFlag("insureFlag");
        tkAutoRelatedInfoD2.setInsureTypeCode("insureTypeCode");
        tkAutoRiskInfoD2.setRelatedInfoDList(Arrays.asList(tkAutoRelatedInfoD2));
        final List<TkAutoRiskInfoD> tkAutoRiskInfoDList = Arrays.asList(tkAutoRiskInfoD2);
        when(mockCvtHelper.findDrivingTkAutoRiskInfoD(tkAutoRiskInfoDList)).thenReturn(tkAutoRiskInfoD1);

        // Configure TkAutoCvtHelper.getRelatedInfoDList(...).
        final TkAutoRelatedInfoD tkAutoRelatedInfoD3 = new TkAutoRelatedInfoD();
        tkAutoRelatedInfoD3.setInsuredName("insuredName");
        tkAutoRelatedInfoD3.setInsureFlag("insureFlag");
        tkAutoRelatedInfoD3.setInsureTypeCode("insureTypeCode");
        tkAutoRelatedInfoD3.setInsureType("insureType");
        tkAutoRelatedInfoD3.setCertiType("certiType");
        final List<TkAutoRelatedInfoD> tkAutoRelatedInfoDS = Arrays.asList(tkAutoRelatedInfoD3);
        final TkAutoRiskInfoD tkAutoRiskInfoD3 = new TkAutoRiskInfoD();
        tkAutoRiskInfoD3.setRiskCode("riskCode");
        final TkAutoBaseInfoD baseInfoD3 = new TkAutoBaseInfoD();
        baseInfoD3.setSubPolicyNo("subPolicyNo");
        baseInfoD3.setRiskName("riskName");
        baseInfoD3.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD3.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD3.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD3.setPremium(new BigDecimal("0.00"));
        baseInfoD3.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TkAutoPolicyViewList policyViewList3 = new TkAutoPolicyViewList();
        final TkAutoPolicyData policyData3 = new TkAutoPolicyData();
        policyData3.setPolicyDownLoadUrl("policyDownLoadUrl");
        policyViewList3.setPolicyData(policyData3);
        baseInfoD3.setPolicyViewList(policyViewList3);
        tkAutoRiskInfoD3.setBaseInfoD(baseInfoD3);
        final TkAutoRelatedInfoD tkAutoRelatedInfoD4 = new TkAutoRelatedInfoD();
        tkAutoRelatedInfoD4.setInsureFlag("insureFlag");
        tkAutoRelatedInfoD4.setInsureTypeCode("insureTypeCode");
        tkAutoRiskInfoD3.setRelatedInfoDList(Arrays.asList(tkAutoRelatedInfoD4));
        final List<TkAutoRiskInfoD> tkAutoRiskInfoDList1 = Arrays.asList(tkAutoRiskInfoD3);
        when(mockCvtHelper.getRelatedInfoDList(tkAutoRiskInfoDList1)).thenReturn(tkAutoRelatedInfoDS);

        // Configure AmPolicyTkMapper.selectOneByPolicyNo(...).
        final AmPolicyTk amPolicyTk1 = new AmPolicyTk();
        amPolicyTk1.setPolicyNo("policyNo");
        amPolicyTk1.setSubPolicyNo("subPolicyNo");
        amPolicyTk1.setRiskName("riskName");
        amPolicyTk1.setRiskCode("riskCode");
        amPolicyTk1.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk1.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk1.setPolicyUrl("policyDownLoadUrl");
        amPolicyTk1.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk1.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk1.setBenchmarkPremium(new BigDecimal("0.00"));
        amPolicyTk1.setState(0);
        amPolicyTk1.setRemark("msg");
        when(mockPolicyTkMapper.selectOneByPolicyNo("policyNo")).thenReturn(amPolicyTk1);

        // Configure TkAutoCvtHelper.cvtOrderInfo(...).
        final AmPolicyTk tk = new AmPolicyTk();
        tk.setPolicyNo("policyNo");
        tk.setSubPolicyNo("subPolicyNo");
        tk.setRiskName("riskName");
        tk.setRiskCode("riskCode");
        tk.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        tk.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        tk.setPolicyUrl("policyDownLoadUrl");
        tk.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        tk.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        tk.setBenchmarkPremium(new BigDecimal("0.00"));
        tk.setState(0);
        tk.setRemark("msg");
        final TkAutoQueryPolicyDetailResp detailResp = new TkAutoQueryPolicyDetailResp();
        final TkAutoRiskInfoD tkAutoRiskInfoD4 = new TkAutoRiskInfoD();
        tkAutoRiskInfoD4.setRiskCode("riskCode");
        final TkAutoBaseInfoD baseInfoD4 = new TkAutoBaseInfoD();
        baseInfoD4.setSubPolicyNo("subPolicyNo");
        baseInfoD4.setRiskName("riskName");
        baseInfoD4.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD4.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD4.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD4.setPremium(new BigDecimal("0.00"));
        baseInfoD4.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TkAutoPolicyViewList policyViewList4 = new TkAutoPolicyViewList();
        final TkAutoPolicyData policyData4 = new TkAutoPolicyData();
        policyData4.setPolicyDownLoadUrl("policyDownLoadUrl");
        policyViewList4.setPolicyData(policyData4);
        baseInfoD4.setPolicyViewList(policyViewList4);
        tkAutoRiskInfoD4.setBaseInfoD(baseInfoD4);
        final TkAutoRelatedInfoD tkAutoRelatedInfoD5 = new TkAutoRelatedInfoD();
        tkAutoRelatedInfoD5.setInsureFlag("insureFlag");
        tkAutoRelatedInfoD5.setInsureTypeCode("insureTypeCode");
        tkAutoRiskInfoD4.setRelatedInfoDList(Arrays.asList(tkAutoRelatedInfoD5));
        detailResp.setRiskInfoList(Arrays.asList(tkAutoRiskInfoD4));
        when(mockCvtHelper.cvtOrderInfo(tk, detailResp, new HashMap<>())).thenReturn(Collections.emptyList());

        // Configure TkAutoCvtHelper.cvtAutoInfoList(...).
        final AutoOrderDTO autoOrderDTO = new AutoOrderDTO();
        autoOrderDTO.setOrderNo("orderNo");
        final AutoOrder order = new AutoOrder();
        order.setPayStatus("-1");
        order.setPaymentTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        order.setCommissionId(0);
        autoOrderDTO.setOrder(order);
        final AutoOrderPerson autoOrderPerson = new AutoOrderPerson();
        autoOrderDTO.setPeople(Arrays.asList(autoOrderPerson));
        final AutoOrderPolicyDTO autoOrderPolicyDTO = new AutoOrderPolicyDTO();
        final AutoOrderPolicy policy = new AutoOrderPolicy();
        policy.setPolicyNo("policyNo");
        policy.setSubPolicyNo("subPolicyNo");
        policy.setPolicyState("-2");
        policy.setBusinessScore("businessScore");
        autoOrderPolicyDTO.setPolicy(policy);
        final AutoOrderRisk autoOrderRisk = new AutoOrderRisk();
        autoOrderRisk.setRiskName("riskName");
        autoOrderRisk.setPremium(new BigDecimal("0.00"));
        autoOrderRisk.setAmount(new BigDecimal("0.00"));
        autoOrderPolicyDTO.setRiskList(Arrays.asList(autoOrderRisk));
        autoOrderDTO.setPolicies(Arrays.asList(autoOrderPolicyDTO));
        final AutoOrderExtend extend = new AutoOrderExtend();
        autoOrderDTO.setExtend(extend);
        final AutoOrderCar car = new AutoOrderCar();
        car.setOwnerPersonName("ownerPersonName");
        car.setOwnerPersonGender("ownerPersonGender");
        car.setOwnerIdType("ownerIdType");
        car.setOwnerIdNumber("ownerIdNumber");
        car.setFirstRegDate("firstRegDate");
        car.setFullLoad("fullLoad");
        car.setTransferDate("transferDate");
        car.setSeatCnt("approvedNum");
        car.setPrice("price");
        car.setCarUserType("carUserType");
        car.setDisplacement("displacement");
        car.setIsNewCar("isNewCar");
        autoOrderDTO.setCar(car);
        final List<AutoOrderDTO> autoOrderDTOS = Arrays.asList(autoOrderDTO);
        final AmPolicyTk tk1 = new AmPolicyTk();
        tk1.setPolicyNo("policyNo");
        tk1.setSubPolicyNo("subPolicyNo");
        tk1.setRiskName("riskName");
        tk1.setRiskCode("riskCode");
        tk1.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        tk1.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        tk1.setPolicyUrl("policyDownLoadUrl");
        tk1.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        tk1.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        tk1.setBenchmarkPremium(new BigDecimal("0.00"));
        tk1.setState(0);
        tk1.setRemark("msg");
        final TkAutoQueryPolicyDetailResp detailResp1 = new TkAutoQueryPolicyDetailResp();
        final TkAutoRiskInfoD tkAutoRiskInfoD5 = new TkAutoRiskInfoD();
        tkAutoRiskInfoD5.setRiskCode("riskCode");
        final TkAutoBaseInfoD baseInfoD5 = new TkAutoBaseInfoD();
        baseInfoD5.setSubPolicyNo("subPolicyNo");
        baseInfoD5.setRiskName("riskName");
        baseInfoD5.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD5.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD5.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD5.setPremium(new BigDecimal("0.00"));
        baseInfoD5.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TkAutoPolicyViewList policyViewList5 = new TkAutoPolicyViewList();
        final TkAutoPolicyData policyData5 = new TkAutoPolicyData();
        policyData5.setPolicyDownLoadUrl("policyDownLoadUrl");
        policyViewList5.setPolicyData(policyData5);
        baseInfoD5.setPolicyViewList(policyViewList5);
        tkAutoRiskInfoD5.setBaseInfoD(baseInfoD5);
        final TkAutoRelatedInfoD tkAutoRelatedInfoD6 = new TkAutoRelatedInfoD();
        tkAutoRelatedInfoD6.setInsureFlag("insureFlag");
        tkAutoRelatedInfoD6.setInsureTypeCode("insureTypeCode");
        tkAutoRiskInfoD5.setRelatedInfoDList(Arrays.asList(tkAutoRelatedInfoD6));
        detailResp1.setRiskInfoList(Arrays.asList(tkAutoRiskInfoD5));
        when(mockCvtHelper.cvtAutoInfoList(tk1, Arrays.asList("value"), detailResp1, new HashMap<>())).thenReturn(autoOrderDTOS);

        // Run the test
        tkAutoOrderServiceUnderTest.sync(LocalDate.of(2020, 1, 1), LocalDate.of(2020, 1, 1));

        // Verify the results
        // Confirm AmSyncTkRecordMapper.insertUseGeneratedKeys(...).
        final AmSyncTkRecord record = new AmSyncTkRecord();
        record.setBatchNo("batchNo");
        record.setStartDate(LocalDate.of(2020, 1, 1));
        record.setEndDate(LocalDate.of(2020, 1, 1));
        record.setOrderCount(0);
        record.setRespContent("respContent");

        // Confirm AmPolicyTkMapper.insertList(...).
        final AmPolicyTk amPolicyTk2 = new AmPolicyTk();
        amPolicyTk2.setPolicyNo("policyNo");
        amPolicyTk2.setSubPolicyNo("subPolicyNo");
        amPolicyTk2.setRiskName("riskName");
        amPolicyTk2.setRiskCode("riskCode");
        amPolicyTk2.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk2.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk2.setPolicyUrl("policyDownLoadUrl");
        amPolicyTk2.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk2.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk2.setBenchmarkPremium(new BigDecimal("0.00"));
        amPolicyTk2.setState(0);
        amPolicyTk2.setRemark("msg");
        final List<AmPolicyTk> recordList = Arrays.asList(amPolicyTk2);

        // Confirm AmPolicyTkMapper.updateByExampleSelective(...).
        final AmPolicyTk record1 = new AmPolicyTk();
        record1.setPolicyNo("policyNo");
        record1.setSubPolicyNo("subPolicyNo");
        record1.setRiskName("riskName");
        record1.setRiskCode("riskCode");
        record1.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record1.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record1.setPolicyUrl("policyDownLoadUrl");
        record1.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record1.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record1.setBenchmarkPremium(new BigDecimal("0.00"));
        record1.setState(0);
        record1.setRemark("msg");

        // Confirm AmPolicyTkMapper.insertUseGeneratedKeys(...).
        final AmPolicyTk record2 = new AmPolicyTk();
        record2.setPolicyNo("policyNo");
        record2.setSubPolicyNo("subPolicyNo");
        record2.setRiskName("riskName");
        record2.setRiskCode("riskCode");
        record2.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record2.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record2.setPolicyUrl("policyDownLoadUrl");
        record2.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record2.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record2.setBenchmarkPremium(new BigDecimal("0.00"));
        record2.setState(0);
        record2.setRemark("msg");

        // Confirm AmPolicyTkDetailMapper.insertUseGeneratedKeys(...).
        final AmPolicyTkDetail record3 = new AmPolicyTkDetail();
        record3.setPolicyNo("policyNo");
        record3.setContent("content");
    }

    @Test
    public void testSync_TkAutoCvtHelperCvtAutoInfoListReturnsNoItems() throws Exception {
        // Setup
        // Configure TkAutoServiceAdapter.queryTkList(...).
        final TkAutoRespHelper<TkAutoQueryPolicyResp> tkAutoQueryPolicyRespTkAutoRespHelper = new TkAutoRespHelper<>();
        tkAutoQueryPolicyRespTkAutoRespHelper.setAppCode("1");
        tkAutoQueryPolicyRespTkAutoRespHelper.setAppMessage("appMessage");
        final TkAutoQueryPolicyResp tkAutoQueryPolicyResp = new TkAutoQueryPolicyResp();
        final TkAutoQueryPolicyDto tkAutoQueryPolicyDto = new TkAutoQueryPolicyDto();
        tkAutoQueryPolicyDto.setSubPolicyNo("subPolicyNo");
        tkAutoQueryPolicyResp.setQueryPolicyDtoList(Arrays.asList(tkAutoQueryPolicyDto));
        tkAutoQueryPolicyRespTkAutoRespHelper.setData(tkAutoQueryPolicyResp);
        tkAutoQueryPolicyRespTkAutoRespHelper.setContent("content");
        when(mockAdapter.queryTkList(LocalDate.of(2020, 1, 1), LocalDate.of(2020, 1, 1))).thenReturn(tkAutoQueryPolicyRespTkAutoRespHelper);

        // Configure AmPolicyTkMapper.selectByExample(...).
        final AmPolicyTk amPolicyTk = new AmPolicyTk();
        amPolicyTk.setPolicyNo("policyNo");
        amPolicyTk.setSubPolicyNo("subPolicyNo");
        amPolicyTk.setRiskName("riskName");
        amPolicyTk.setRiskCode("riskCode");
        amPolicyTk.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk.setPolicyUrl("policyDownLoadUrl");
        amPolicyTk.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk.setBenchmarkPremium(new BigDecimal("0.00"));
        amPolicyTk.setState(0);
        amPolicyTk.setRemark("msg");
        final List<AmPolicyTk> amPolicyTks = Arrays.asList(amPolicyTk);
        when(mockPolicyTkMapper.selectByExample(any(Object.class))).thenReturn(amPolicyTks);

        // Configure TkAutoServiceAdapter.queryTkDetail(...).
        final TkAutoRespHelper<TkAutoQueryPolicyDetailResp> tkAutoQueryPolicyDetailRespTkAutoRespHelper = new TkAutoRespHelper<>();
        tkAutoQueryPolicyDetailRespTkAutoRespHelper.setAppCode("1");
        tkAutoQueryPolicyDetailRespTkAutoRespHelper.setAppMessage("appMessage");
        final TkAutoQueryPolicyDetailResp tkAutoQueryPolicyDetailResp = new TkAutoQueryPolicyDetailResp();
        final TkAutoRiskInfoD tkAutoRiskInfoD = new TkAutoRiskInfoD();
        tkAutoRiskInfoD.setRiskCode("riskCode");
        final TkAutoBaseInfoD baseInfoD = new TkAutoBaseInfoD();
        baseInfoD.setSubPolicyNo("subPolicyNo");
        baseInfoD.setRiskName("riskName");
        baseInfoD.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD.setPremium(new BigDecimal("0.00"));
        baseInfoD.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TkAutoPolicyViewList policyViewList = new TkAutoPolicyViewList();
        final TkAutoPolicyData policyData = new TkAutoPolicyData();
        policyData.setPolicyDownLoadUrl("policyDownLoadUrl");
        policyViewList.setPolicyData(policyData);
        baseInfoD.setPolicyViewList(policyViewList);
        tkAutoRiskInfoD.setBaseInfoD(baseInfoD);
        final TkAutoRelatedInfoD tkAutoRelatedInfoD = new TkAutoRelatedInfoD();
        tkAutoRelatedInfoD.setInsureFlag("insureFlag");
        tkAutoRelatedInfoD.setInsureTypeCode("insureTypeCode");
        tkAutoRiskInfoD.setRelatedInfoDList(Arrays.asList(tkAutoRelatedInfoD));
        tkAutoQueryPolicyDetailResp.setRiskInfoList(Arrays.asList(tkAutoRiskInfoD));
        tkAutoQueryPolicyDetailRespTkAutoRespHelper.setData(tkAutoQueryPolicyDetailResp);
        tkAutoQueryPolicyDetailRespTkAutoRespHelper.setContent("content");
        when(mockAdapter.queryTkDetail("policyNo")).thenReturn(tkAutoQueryPolicyDetailRespTkAutoRespHelper);

        // Configure TkAutoCvtHelper.findDrivingTkAutoRiskInfoD(...).
        final TkAutoRiskInfoD tkAutoRiskInfoD1 = new TkAutoRiskInfoD();
        tkAutoRiskInfoD1.setRiskCode("riskCode");
        final TkAutoBaseInfoD baseInfoD1 = new TkAutoBaseInfoD();
        baseInfoD1.setSubPolicyNo("subPolicyNo");
        baseInfoD1.setRiskName("riskName");
        baseInfoD1.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD1.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD1.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD1.setPremium(new BigDecimal("0.00"));
        baseInfoD1.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TkAutoPolicyViewList policyViewList1 = new TkAutoPolicyViewList();
        final TkAutoPolicyData policyData1 = new TkAutoPolicyData();
        policyData1.setPolicyDownLoadUrl("policyDownLoadUrl");
        policyViewList1.setPolicyData(policyData1);
        baseInfoD1.setPolicyViewList(policyViewList1);
        tkAutoRiskInfoD1.setBaseInfoD(baseInfoD1);
        final TkAutoRelatedInfoD tkAutoRelatedInfoD1 = new TkAutoRelatedInfoD();
        tkAutoRelatedInfoD1.setInsureFlag("insureFlag");
        tkAutoRelatedInfoD1.setInsureTypeCode("insureTypeCode");
        tkAutoRiskInfoD1.setRelatedInfoDList(Arrays.asList(tkAutoRelatedInfoD1));
        final TkAutoRiskInfoD tkAutoRiskInfoD2 = new TkAutoRiskInfoD();
        tkAutoRiskInfoD2.setRiskCode("riskCode");
        final TkAutoBaseInfoD baseInfoD2 = new TkAutoBaseInfoD();
        baseInfoD2.setSubPolicyNo("subPolicyNo");
        baseInfoD2.setRiskName("riskName");
        baseInfoD2.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD2.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD2.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD2.setPremium(new BigDecimal("0.00"));
        baseInfoD2.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TkAutoPolicyViewList policyViewList2 = new TkAutoPolicyViewList();
        final TkAutoPolicyData policyData2 = new TkAutoPolicyData();
        policyData2.setPolicyDownLoadUrl("policyDownLoadUrl");
        policyViewList2.setPolicyData(policyData2);
        baseInfoD2.setPolicyViewList(policyViewList2);
        tkAutoRiskInfoD2.setBaseInfoD(baseInfoD2);
        final TkAutoRelatedInfoD tkAutoRelatedInfoD2 = new TkAutoRelatedInfoD();
        tkAutoRelatedInfoD2.setInsureFlag("insureFlag");
        tkAutoRelatedInfoD2.setInsureTypeCode("insureTypeCode");
        tkAutoRiskInfoD2.setRelatedInfoDList(Arrays.asList(tkAutoRelatedInfoD2));
        final List<TkAutoRiskInfoD> tkAutoRiskInfoDList = Arrays.asList(tkAutoRiskInfoD2);
        when(mockCvtHelper.findDrivingTkAutoRiskInfoD(tkAutoRiskInfoDList)).thenReturn(tkAutoRiskInfoD1);

        // Configure TkAutoCvtHelper.getRelatedInfoDList(...).
        final TkAutoRelatedInfoD tkAutoRelatedInfoD3 = new TkAutoRelatedInfoD();
        tkAutoRelatedInfoD3.setInsuredName("insuredName");
        tkAutoRelatedInfoD3.setInsureFlag("insureFlag");
        tkAutoRelatedInfoD3.setInsureTypeCode("insureTypeCode");
        tkAutoRelatedInfoD3.setInsureType("insureType");
        tkAutoRelatedInfoD3.setCertiType("certiType");
        final List<TkAutoRelatedInfoD> tkAutoRelatedInfoDS = Arrays.asList(tkAutoRelatedInfoD3);
        final TkAutoRiskInfoD tkAutoRiskInfoD3 = new TkAutoRiskInfoD();
        tkAutoRiskInfoD3.setRiskCode("riskCode");
        final TkAutoBaseInfoD baseInfoD3 = new TkAutoBaseInfoD();
        baseInfoD3.setSubPolicyNo("subPolicyNo");
        baseInfoD3.setRiskName("riskName");
        baseInfoD3.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD3.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD3.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD3.setPremium(new BigDecimal("0.00"));
        baseInfoD3.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TkAutoPolicyViewList policyViewList3 = new TkAutoPolicyViewList();
        final TkAutoPolicyData policyData3 = new TkAutoPolicyData();
        policyData3.setPolicyDownLoadUrl("policyDownLoadUrl");
        policyViewList3.setPolicyData(policyData3);
        baseInfoD3.setPolicyViewList(policyViewList3);
        tkAutoRiskInfoD3.setBaseInfoD(baseInfoD3);
        final TkAutoRelatedInfoD tkAutoRelatedInfoD4 = new TkAutoRelatedInfoD();
        tkAutoRelatedInfoD4.setInsureFlag("insureFlag");
        tkAutoRelatedInfoD4.setInsureTypeCode("insureTypeCode");
        tkAutoRiskInfoD3.setRelatedInfoDList(Arrays.asList(tkAutoRelatedInfoD4));
        final List<TkAutoRiskInfoD> tkAutoRiskInfoDList1 = Arrays.asList(tkAutoRiskInfoD3);
        when(mockCvtHelper.getRelatedInfoDList(tkAutoRiskInfoDList1)).thenReturn(tkAutoRelatedInfoDS);

        // Configure AmPolicyTkMapper.selectOneByPolicyNo(...).
        final AmPolicyTk amPolicyTk1 = new AmPolicyTk();
        amPolicyTk1.setPolicyNo("policyNo");
        amPolicyTk1.setSubPolicyNo("subPolicyNo");
        amPolicyTk1.setRiskName("riskName");
        amPolicyTk1.setRiskCode("riskCode");
        amPolicyTk1.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk1.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk1.setPolicyUrl("policyDownLoadUrl");
        amPolicyTk1.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk1.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk1.setBenchmarkPremium(new BigDecimal("0.00"));
        amPolicyTk1.setState(0);
        amPolicyTk1.setRemark("msg");
        when(mockPolicyTkMapper.selectOneByPolicyNo("policyNo")).thenReturn(amPolicyTk1);

        // Configure TkAutoCvtHelper.cvtOrderInfo(...).
        final OrderSubmitRequest orderSubmitRequest = new OrderSubmitRequest();
        final FhProduct productInfo = new FhProduct();
        productInfo.setProductId("fhProductId");
        productInfo.setRecommendId("recommendId");
        productInfo.setRecommendMainJobNumber("recommendMainJobNumber");
        productInfo.setRecommendOrgCode("recommendOrgCode");
        orderSubmitRequest.setProductInfo(productInfo);
        final FhOrderInfo orderInfo = new FhOrderInfo();
        orderInfo.setSubmitTime("submitTime");
        orderInfo.setStartTime("startTime");
        orderInfo.setEndTime("endTime");
        orderSubmitRequest.setOrderInfo(orderInfo);
        final FhProposer proposerInfo = new FhProposer();
        proposerInfo.setPersonName("insuredName");
        proposerInfo.setPersonGender("insuredPersonGender");
        proposerInfo.setIdType("idType");
        proposerInfo.setIdNumber("idNumber");
        proposerInfo.setBirthday("birthday");
        proposerInfo.setCellPhone("cellPhone");
        proposerInfo.setEmail("email");
        proposerInfo.setAddress("address");
        proposerInfo.setAppStatus("-2");
        proposerInfo.setPolicyNo("insIdNumber");
        proposerInfo.setDownloadURL("downloadURL");
        orderSubmitRequest.setProposerInfo(proposerInfo);
        final FhInsuredPerson fhInsuredPerson = new FhInsuredPerson();
        fhInsuredPerson.setPersonName("insuredName");
        fhInsuredPerson.setIdType("idType");
        fhInsuredPerson.setIdNumber("idNumber");
        fhInsuredPerson.setBirthday("birthday");
        fhInsuredPerson.setCellPhone("cellPhone");
        fhInsuredPerson.setAppStatus("-2");
        fhInsuredPerson.setPolicyNo("insIdNumber");
        fhInsuredPerson.setRelationship("99");
        fhInsuredPerson.setFlightNo("flightNo");
        fhInsuredPerson.setFlightTime("flightTime");
        fhInsuredPerson.setOccupationCode("");
        fhInsuredPerson.setOccupationGroup("occupationGroup");
        fhInsuredPerson.setDestinationCountryText("destinationCountryText");
        fhInsuredPerson.setAnnualIncome("annualIncome");
        fhInsuredPerson.setStudentType("studentType");
        fhInsuredPerson.setSchoolType("schoolType");
        fhInsuredPerson.setSchoolNature("schoolNature");
        fhInsuredPerson.setSchoolName("schoolName");
        fhInsuredPerson.setSchoolClass("schoolClass");
        fhInsuredPerson.setStudentId("studentId");
        fhInsuredPerson.setIdPeriodStart("idPeriodStart");
        fhInsuredPerson.setIdPeriodEnd("idPeriodEnd");
        fhInsuredPerson.setSmoke("smoke");
        fhInsuredPerson.setOldPolicyNo("oldPolicyNo");
        final TestPremiumDutyForm testPremiumDutyForm = new TestPremiumDutyForm();
        fhInsuredPerson.setDuties(Arrays.asList(testPremiumDutyForm));
        final TestPremiumProductForm product = new TestPremiumProductForm();
        product.setProductId(0);
        product.setPlanId(0);
        product.setPlanCode("planCode");
        product.setPremium(new BigDecimal("0.00"));
        product.setQty(0);
        final TestPremiumDutyForm testPremiumDutyForm1 = new TestPremiumDutyForm();
        product.setPremiumDutyForms(Arrays.asList(testPremiumDutyForm1));
        fhInsuredPerson.setProduct(product);
        orderSubmitRequest.setInsuredPerson(Arrays.asList(fhInsuredPerson));
        final FhProperty propertyInfo = new FhProperty();
        propertyInfo.setPropertyInfoIsExist("0");
        orderSubmitRequest.setPropertyInfo(propertyInfo);
        final SmOrderHouseDTO house = new SmOrderHouseDTO();
        orderSubmitRequest.setHouse(house);
        final SmOrderCarInfoDTO carInfo = new SmOrderCarInfoDTO();
        orderSubmitRequest.setCarInfo(carInfo);
        orderSubmitRequest.setProductType("code");
        orderSubmitRequest.setBizCode("bizCode");
        orderSubmitRequest.setOrderOutType("orderOutType");
        orderSubmitRequest.setToken("token");
        orderSubmitRequest.setPlanId(0);
        orderSubmitRequest.setQty(0);
        orderSubmitRequest.setPreOrderId("preOrderId");
        orderSubmitRequest.setRenew(false);
        orderSubmitRequest.setRealRenewFlag(false);
        orderSubmitRequest.setAppNo("appNo");
        orderSubmitRequest.setAgentId(0);
        orderSubmitRequest.setJobCode("jobCode");
        final List<SmCreateOrderSubmitRequest> smCreateOrderSubmitRequests = Arrays.asList(new SmCreateOrderSubmitRequest(orderSubmitRequest));
        final AmPolicyTk tk = new AmPolicyTk();
        tk.setPolicyNo("policyNo");
        tk.setSubPolicyNo("subPolicyNo");
        tk.setRiskName("riskName");
        tk.setRiskCode("riskCode");
        tk.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        tk.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        tk.setPolicyUrl("policyDownLoadUrl");
        tk.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        tk.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        tk.setBenchmarkPremium(new BigDecimal("0.00"));
        tk.setState(0);
        tk.setRemark("msg");
        final TkAutoQueryPolicyDetailResp detailResp = new TkAutoQueryPolicyDetailResp();
        final TkAutoRiskInfoD tkAutoRiskInfoD4 = new TkAutoRiskInfoD();
        tkAutoRiskInfoD4.setRiskCode("riskCode");
        final TkAutoBaseInfoD baseInfoD4 = new TkAutoBaseInfoD();
        baseInfoD4.setSubPolicyNo("subPolicyNo");
        baseInfoD4.setRiskName("riskName");
        baseInfoD4.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD4.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD4.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD4.setPremium(new BigDecimal("0.00"));
        baseInfoD4.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TkAutoPolicyViewList policyViewList4 = new TkAutoPolicyViewList();
        final TkAutoPolicyData policyData4 = new TkAutoPolicyData();
        policyData4.setPolicyDownLoadUrl("policyDownLoadUrl");
        policyViewList4.setPolicyData(policyData4);
        baseInfoD4.setPolicyViewList(policyViewList4);
        tkAutoRiskInfoD4.setBaseInfoD(baseInfoD4);
        final TkAutoRelatedInfoD tkAutoRelatedInfoD5 = new TkAutoRelatedInfoD();
        tkAutoRelatedInfoD5.setInsureFlag("insureFlag");
        tkAutoRelatedInfoD5.setInsureTypeCode("insureTypeCode");
        tkAutoRiskInfoD4.setRelatedInfoDList(Arrays.asList(tkAutoRelatedInfoD5));
        detailResp.setRiskInfoList(Arrays.asList(tkAutoRiskInfoD4));
        when(mockCvtHelper.cvtOrderInfo(tk, detailResp, new HashMap<>())).thenReturn(smCreateOrderSubmitRequests);

        // Configure TkAutoCvtHelper.cvtAutoInfoList(...).
        final AmPolicyTk tk1 = new AmPolicyTk();
        tk1.setPolicyNo("policyNo");
        tk1.setSubPolicyNo("subPolicyNo");
        tk1.setRiskName("riskName");
        tk1.setRiskCode("riskCode");
        tk1.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        tk1.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        tk1.setPolicyUrl("policyDownLoadUrl");
        tk1.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        tk1.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        tk1.setBenchmarkPremium(new BigDecimal("0.00"));
        tk1.setState(0);
        tk1.setRemark("msg");
        final TkAutoQueryPolicyDetailResp detailResp1 = new TkAutoQueryPolicyDetailResp();
        final TkAutoRiskInfoD tkAutoRiskInfoD5 = new TkAutoRiskInfoD();
        tkAutoRiskInfoD5.setRiskCode("riskCode");
        final TkAutoBaseInfoD baseInfoD5 = new TkAutoBaseInfoD();
        baseInfoD5.setSubPolicyNo("subPolicyNo");
        baseInfoD5.setRiskName("riskName");
        baseInfoD5.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD5.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD5.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD5.setPremium(new BigDecimal("0.00"));
        baseInfoD5.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TkAutoPolicyViewList policyViewList5 = new TkAutoPolicyViewList();
        final TkAutoPolicyData policyData5 = new TkAutoPolicyData();
        policyData5.setPolicyDownLoadUrl("policyDownLoadUrl");
        policyViewList5.setPolicyData(policyData5);
        baseInfoD5.setPolicyViewList(policyViewList5);
        tkAutoRiskInfoD5.setBaseInfoD(baseInfoD5);
        final TkAutoRelatedInfoD tkAutoRelatedInfoD6 = new TkAutoRelatedInfoD();
        tkAutoRelatedInfoD6.setInsureFlag("insureFlag");
        tkAutoRelatedInfoD6.setInsureTypeCode("insureTypeCode");
        tkAutoRiskInfoD5.setRelatedInfoDList(Arrays.asList(tkAutoRelatedInfoD6));
        detailResp1.setRiskInfoList(Arrays.asList(tkAutoRiskInfoD5));
        when(mockCvtHelper.cvtAutoInfoList(tk1, Arrays.asList("value"), detailResp1, new HashMap<>())).thenReturn(Collections.emptyList());

        when(mockOrderMapper.updateOrderPaymentTime("orderId")).thenReturn(0);
        when(mockOrderMapper.getFhProductIdByOrderId("orderId")).thenReturn(Arrays.asList("value"));

        // Run the test
        tkAutoOrderServiceUnderTest.sync(LocalDate.of(2020, 1, 1), LocalDate.of(2020, 1, 1));

        // Verify the results
        // Confirm AmSyncTkRecordMapper.insertUseGeneratedKeys(...).
        final AmSyncTkRecord record = new AmSyncTkRecord();
        record.setBatchNo("batchNo");
        record.setStartDate(LocalDate.of(2020, 1, 1));
        record.setEndDate(LocalDate.of(2020, 1, 1));
        record.setOrderCount(0);
        record.setRespContent("respContent");

        // Confirm AmPolicyTkMapper.insertList(...).
        final AmPolicyTk amPolicyTk2 = new AmPolicyTk();
        amPolicyTk2.setPolicyNo("policyNo");
        amPolicyTk2.setSubPolicyNo("subPolicyNo");
        amPolicyTk2.setRiskName("riskName");
        amPolicyTk2.setRiskCode("riskCode");
        amPolicyTk2.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk2.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk2.setPolicyUrl("policyDownLoadUrl");
        amPolicyTk2.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk2.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk2.setBenchmarkPremium(new BigDecimal("0.00"));
        amPolicyTk2.setState(0);
        amPolicyTk2.setRemark("msg");
        final List<AmPolicyTk> recordList = Arrays.asList(amPolicyTk2);

        // Confirm AmPolicyTkMapper.updateByExampleSelective(...).
        final AmPolicyTk record1 = new AmPolicyTk();
        record1.setPolicyNo("policyNo");
        record1.setSubPolicyNo("subPolicyNo");
        record1.setRiskName("riskName");
        record1.setRiskCode("riskCode");
        record1.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record1.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record1.setPolicyUrl("policyDownLoadUrl");
        record1.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record1.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record1.setBenchmarkPremium(new BigDecimal("0.00"));
        record1.setState(0);
        record1.setRemark("msg");

        // Confirm AmPolicyTkMapper.insertUseGeneratedKeys(...).
        final AmPolicyTk record2 = new AmPolicyTk();
        record2.setPolicyNo("policyNo");
        record2.setSubPolicyNo("subPolicyNo");
        record2.setRiskName("riskName");
        record2.setRiskCode("riskCode");
        record2.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record2.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record2.setPolicyUrl("policyDownLoadUrl");
        record2.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record2.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record2.setBenchmarkPremium(new BigDecimal("0.00"));
        record2.setState(0);
        record2.setRemark("msg");

        // Confirm AmPolicyTkDetailMapper.insertUseGeneratedKeys(...).
        final AmPolicyTkDetail record3 = new AmPolicyTkDetail();
        record3.setPolicyNo("policyNo");
        record3.setContent("content");

        // Confirm SmOrderMapper.insertOrder(...).
        final OrderSubmitRequest orderSubmitRequest1 = new OrderSubmitRequest();
        final FhProduct productInfo1 = new FhProduct();
        productInfo1.setProductId("fhProductId");
        productInfo1.setRecommendId("recommendId");
        productInfo1.setRecommendMainJobNumber("recommendMainJobNumber");
        productInfo1.setRecommendOrgCode("recommendOrgCode");
        orderSubmitRequest1.setProductInfo(productInfo1);
        final FhOrderInfo orderInfo1 = new FhOrderInfo();
        orderInfo1.setSubmitTime("submitTime");
        orderInfo1.setStartTime("startTime");
        orderInfo1.setEndTime("endTime");
        orderSubmitRequest1.setOrderInfo(orderInfo1);
        final FhProposer proposerInfo1 = new FhProposer();
        proposerInfo1.setPersonName("insuredName");
        proposerInfo1.setPersonGender("insuredPersonGender");
        proposerInfo1.setIdType("idType");
        proposerInfo1.setIdNumber("idNumber");
        proposerInfo1.setBirthday("birthday");
        proposerInfo1.setCellPhone("cellPhone");
        proposerInfo1.setEmail("email");
        proposerInfo1.setAddress("address");
        proposerInfo1.setAppStatus("-2");
        proposerInfo1.setPolicyNo("insIdNumber");
        proposerInfo1.setDownloadURL("downloadURL");
        orderSubmitRequest1.setProposerInfo(proposerInfo1);
        final FhInsuredPerson fhInsuredPerson1 = new FhInsuredPerson();
        fhInsuredPerson1.setPersonName("insuredName");
        fhInsuredPerson1.setIdType("idType");
        fhInsuredPerson1.setIdNumber("idNumber");
        fhInsuredPerson1.setBirthday("birthday");
        fhInsuredPerson1.setCellPhone("cellPhone");
        fhInsuredPerson1.setAppStatus("-2");
        fhInsuredPerson1.setPolicyNo("insIdNumber");
        fhInsuredPerson1.setRelationship("99");
        fhInsuredPerson1.setFlightNo("flightNo");
        fhInsuredPerson1.setFlightTime("flightTime");
        fhInsuredPerson1.setOccupationCode("");
        fhInsuredPerson1.setOccupationGroup("occupationGroup");
        fhInsuredPerson1.setDestinationCountryText("destinationCountryText");
        fhInsuredPerson1.setAnnualIncome("annualIncome");
        fhInsuredPerson1.setStudentType("studentType");
        fhInsuredPerson1.setSchoolType("schoolType");
        fhInsuredPerson1.setSchoolNature("schoolNature");
        fhInsuredPerson1.setSchoolName("schoolName");
        fhInsuredPerson1.setSchoolClass("schoolClass");
        fhInsuredPerson1.setStudentId("studentId");
        fhInsuredPerson1.setIdPeriodStart("idPeriodStart");
        fhInsuredPerson1.setIdPeriodEnd("idPeriodEnd");
        fhInsuredPerson1.setSmoke("smoke");
        fhInsuredPerson1.setOldPolicyNo("oldPolicyNo");
        final TestPremiumDutyForm testPremiumDutyForm2 = new TestPremiumDutyForm();
        fhInsuredPerson1.setDuties(Arrays.asList(testPremiumDutyForm2));
        final TestPremiumProductForm product1 = new TestPremiumProductForm();
        product1.setProductId(0);
        product1.setPlanId(0);
        product1.setPlanCode("planCode");
        product1.setPremium(new BigDecimal("0.00"));
        product1.setQty(0);
        final TestPremiumDutyForm testPremiumDutyForm3 = new TestPremiumDutyForm();
        product1.setPremiumDutyForms(Arrays.asList(testPremiumDutyForm3));
        fhInsuredPerson1.setProduct(product1);
        orderSubmitRequest1.setInsuredPerson(Arrays.asList(fhInsuredPerson1));
        final FhProperty propertyInfo1 = new FhProperty();
        propertyInfo1.setPropertyInfoIsExist("0");
        orderSubmitRequest1.setPropertyInfo(propertyInfo1);
        final SmOrderHouseDTO house1 = new SmOrderHouseDTO();
        orderSubmitRequest1.setHouse(house1);
        final SmOrderCarInfoDTO carInfo1 = new SmOrderCarInfoDTO();
        orderSubmitRequest1.setCarInfo(carInfo1);
        orderSubmitRequest1.setProductType("code");
        orderSubmitRequest1.setBizCode("bizCode");
        orderSubmitRequest1.setOrderOutType("orderOutType");
        orderSubmitRequest1.setToken("token");
        orderSubmitRequest1.setPlanId(0);
        orderSubmitRequest1.setQty(0);
        orderSubmitRequest1.setPreOrderId("preOrderId");
        orderSubmitRequest1.setRenew(false);
        orderSubmitRequest1.setRealRenewFlag(false);
        orderSubmitRequest1.setAppNo("appNo");
        orderSubmitRequest1.setAgentId(0);
        orderSubmitRequest1.setJobCode("jobCode");
        final SmCreateOrderSubmitRequest dto = new SmCreateOrderSubmitRequest(orderSubmitRequest1);

        // Confirm SmOrderMapper.insertOrderApplicant(...).
        final OrderSubmitRequest orderSubmitRequest2 = new OrderSubmitRequest();
        final FhProduct productInfo2 = new FhProduct();
        productInfo2.setProductId("fhProductId");
        productInfo2.setRecommendId("recommendId");
        productInfo2.setRecommendMainJobNumber("recommendMainJobNumber");
        productInfo2.setRecommendOrgCode("recommendOrgCode");
        orderSubmitRequest2.setProductInfo(productInfo2);
        final FhOrderInfo orderInfo2 = new FhOrderInfo();
        orderInfo2.setSubmitTime("submitTime");
        orderInfo2.setStartTime("startTime");
        orderInfo2.setEndTime("endTime");
        orderSubmitRequest2.setOrderInfo(orderInfo2);
        final FhProposer proposerInfo2 = new FhProposer();
        proposerInfo2.setPersonName("insuredName");
        proposerInfo2.setPersonGender("insuredPersonGender");
        proposerInfo2.setIdType("idType");
        proposerInfo2.setIdNumber("idNumber");
        proposerInfo2.setBirthday("birthday");
        proposerInfo2.setCellPhone("cellPhone");
        proposerInfo2.setEmail("email");
        proposerInfo2.setAddress("address");
        proposerInfo2.setAppStatus("-2");
        proposerInfo2.setPolicyNo("insIdNumber");
        proposerInfo2.setDownloadURL("downloadURL");
        orderSubmitRequest2.setProposerInfo(proposerInfo2);
        final FhInsuredPerson fhInsuredPerson2 = new FhInsuredPerson();
        fhInsuredPerson2.setPersonName("insuredName");
        fhInsuredPerson2.setIdType("idType");
        fhInsuredPerson2.setIdNumber("idNumber");
        fhInsuredPerson2.setBirthday("birthday");
        fhInsuredPerson2.setCellPhone("cellPhone");
        fhInsuredPerson2.setAppStatus("-2");
        fhInsuredPerson2.setPolicyNo("insIdNumber");
        fhInsuredPerson2.setRelationship("99");
        fhInsuredPerson2.setFlightNo("flightNo");
        fhInsuredPerson2.setFlightTime("flightTime");
        fhInsuredPerson2.setOccupationCode("");
        fhInsuredPerson2.setOccupationGroup("occupationGroup");
        fhInsuredPerson2.setDestinationCountryText("destinationCountryText");
        fhInsuredPerson2.setAnnualIncome("annualIncome");
        fhInsuredPerson2.setStudentType("studentType");
        fhInsuredPerson2.setSchoolType("schoolType");
        fhInsuredPerson2.setSchoolNature("schoolNature");
        fhInsuredPerson2.setSchoolName("schoolName");
        fhInsuredPerson2.setSchoolClass("schoolClass");
        fhInsuredPerson2.setStudentId("studentId");
        fhInsuredPerson2.setIdPeriodStart("idPeriodStart");
        fhInsuredPerson2.setIdPeriodEnd("idPeriodEnd");
        fhInsuredPerson2.setSmoke("smoke");
        fhInsuredPerson2.setOldPolicyNo("oldPolicyNo");
        final TestPremiumDutyForm testPremiumDutyForm4 = new TestPremiumDutyForm();
        fhInsuredPerson2.setDuties(Arrays.asList(testPremiumDutyForm4));
        final TestPremiumProductForm product2 = new TestPremiumProductForm();
        product2.setProductId(0);
        product2.setPlanId(0);
        product2.setPlanCode("planCode");
        product2.setPremium(new BigDecimal("0.00"));
        product2.setQty(0);
        final TestPremiumDutyForm testPremiumDutyForm5 = new TestPremiumDutyForm();
        product2.setPremiumDutyForms(Arrays.asList(testPremiumDutyForm5));
        fhInsuredPerson2.setProduct(product2);
        orderSubmitRequest2.setInsuredPerson(Arrays.asList(fhInsuredPerson2));
        final FhProperty propertyInfo2 = new FhProperty();
        propertyInfo2.setPropertyInfoIsExist("0");
        orderSubmitRequest2.setPropertyInfo(propertyInfo2);
        final SmOrderHouseDTO house2 = new SmOrderHouseDTO();
        orderSubmitRequest2.setHouse(house2);
        final SmOrderCarInfoDTO carInfo2 = new SmOrderCarInfoDTO();
        orderSubmitRequest2.setCarInfo(carInfo2);
        orderSubmitRequest2.setProductType("code");
        orderSubmitRequest2.setBizCode("bizCode");
        orderSubmitRequest2.setOrderOutType("orderOutType");
        orderSubmitRequest2.setToken("token");
        orderSubmitRequest2.setPlanId(0);
        orderSubmitRequest2.setQty(0);
        orderSubmitRequest2.setPreOrderId("preOrderId");
        orderSubmitRequest2.setRenew(false);
        orderSubmitRequest2.setRealRenewFlag(false);
        orderSubmitRequest2.setAppNo("appNo");
        orderSubmitRequest2.setAgentId(0);
        orderSubmitRequest2.setJobCode("jobCode");
        final SmCreateOrderSubmitRequest dto1 = new SmCreateOrderSubmitRequest(orderSubmitRequest2);

        // Confirm SmOrderMapper.insertOrderInsured(...).
        final OrderSubmitRequest orderSubmitRequest3 = new OrderSubmitRequest();
        final FhProduct productInfo3 = new FhProduct();
        productInfo3.setProductId("fhProductId");
        productInfo3.setRecommendId("recommendId");
        productInfo3.setRecommendMainJobNumber("recommendMainJobNumber");
        productInfo3.setRecommendOrgCode("recommendOrgCode");
        orderSubmitRequest3.setProductInfo(productInfo3);
        final FhOrderInfo orderInfo3 = new FhOrderInfo();
        orderInfo3.setSubmitTime("submitTime");
        orderInfo3.setStartTime("startTime");
        orderInfo3.setEndTime("endTime");
        orderSubmitRequest3.setOrderInfo(orderInfo3);
        final FhProposer proposerInfo3 = new FhProposer();
        proposerInfo3.setPersonName("insuredName");
        proposerInfo3.setPersonGender("insuredPersonGender");
        proposerInfo3.setIdType("idType");
        proposerInfo3.setIdNumber("idNumber");
        proposerInfo3.setBirthday("birthday");
        proposerInfo3.setCellPhone("cellPhone");
        proposerInfo3.setEmail("email");
        proposerInfo3.setAddress("address");
        proposerInfo3.setAppStatus("-2");
        proposerInfo3.setPolicyNo("insIdNumber");
        proposerInfo3.setDownloadURL("downloadURL");
        orderSubmitRequest3.setProposerInfo(proposerInfo3);
        final FhInsuredPerson fhInsuredPerson3 = new FhInsuredPerson();
        fhInsuredPerson3.setPersonName("insuredName");
        fhInsuredPerson3.setIdType("idType");
        fhInsuredPerson3.setIdNumber("idNumber");
        fhInsuredPerson3.setBirthday("birthday");
        fhInsuredPerson3.setCellPhone("cellPhone");
        fhInsuredPerson3.setAppStatus("-2");
        fhInsuredPerson3.setPolicyNo("insIdNumber");
        fhInsuredPerson3.setRelationship("99");
        fhInsuredPerson3.setFlightNo("flightNo");
        fhInsuredPerson3.setFlightTime("flightTime");
        fhInsuredPerson3.setOccupationCode("");
        fhInsuredPerson3.setOccupationGroup("occupationGroup");
        fhInsuredPerson3.setDestinationCountryText("destinationCountryText");
        fhInsuredPerson3.setAnnualIncome("annualIncome");
        fhInsuredPerson3.setStudentType("studentType");
        fhInsuredPerson3.setSchoolType("schoolType");
        fhInsuredPerson3.setSchoolNature("schoolNature");
        fhInsuredPerson3.setSchoolName("schoolName");
        fhInsuredPerson3.setSchoolClass("schoolClass");
        fhInsuredPerson3.setStudentId("studentId");
        fhInsuredPerson3.setIdPeriodStart("idPeriodStart");
        fhInsuredPerson3.setIdPeriodEnd("idPeriodEnd");
        fhInsuredPerson3.setSmoke("smoke");
        fhInsuredPerson3.setOldPolicyNo("oldPolicyNo");
        final TestPremiumDutyForm testPremiumDutyForm6 = new TestPremiumDutyForm();
        fhInsuredPerson3.setDuties(Arrays.asList(testPremiumDutyForm6));
        final TestPremiumProductForm product3 = new TestPremiumProductForm();
        product3.setProductId(0);
        product3.setPlanId(0);
        product3.setPlanCode("planCode");
        product3.setPremium(new BigDecimal("0.00"));
        product3.setQty(0);
        final TestPremiumDutyForm testPremiumDutyForm7 = new TestPremiumDutyForm();
        product3.setPremiumDutyForms(Arrays.asList(testPremiumDutyForm7));
        fhInsuredPerson3.setProduct(product3);
        orderSubmitRequest3.setInsuredPerson(Arrays.asList(fhInsuredPerson3));
        final FhProperty propertyInfo3 = new FhProperty();
        propertyInfo3.setPropertyInfoIsExist("0");
        orderSubmitRequest3.setPropertyInfo(propertyInfo3);
        final SmOrderHouseDTO house3 = new SmOrderHouseDTO();
        orderSubmitRequest3.setHouse(house3);
        final SmOrderCarInfoDTO carInfo3 = new SmOrderCarInfoDTO();
        orderSubmitRequest3.setCarInfo(carInfo3);
        orderSubmitRequest3.setProductType("code");
        orderSubmitRequest3.setBizCode("bizCode");
        orderSubmitRequest3.setOrderOutType("orderOutType");
        orderSubmitRequest3.setToken("token");
        orderSubmitRequest3.setPlanId(0);
        orderSubmitRequest3.setQty(0);
        orderSubmitRequest3.setPreOrderId("preOrderId");
        orderSubmitRequest3.setRenew(false);
        orderSubmitRequest3.setRealRenewFlag(false);
        orderSubmitRequest3.setAppNo("appNo");
        orderSubmitRequest3.setAgentId(0);
        orderSubmitRequest3.setJobCode("jobCode");
        final SmCreateOrderSubmitRequest dto2 = new SmCreateOrderSubmitRequest(orderSubmitRequest3);
    }

    @Test
    public void testSync_SmOrderMapperGetFhProductIdByOrderIdReturnsNoItems() throws Exception {
        // Setup
        // Configure TkAutoServiceAdapter.queryTkList(...).
        final TkAutoRespHelper<TkAutoQueryPolicyResp> tkAutoQueryPolicyRespTkAutoRespHelper = new TkAutoRespHelper<>();
        tkAutoQueryPolicyRespTkAutoRespHelper.setAppCode("1");
        tkAutoQueryPolicyRespTkAutoRespHelper.setAppMessage("appMessage");
        final TkAutoQueryPolicyResp tkAutoQueryPolicyResp = new TkAutoQueryPolicyResp();
        final TkAutoQueryPolicyDto tkAutoQueryPolicyDto = new TkAutoQueryPolicyDto();
        tkAutoQueryPolicyDto.setSubPolicyNo("subPolicyNo");
        tkAutoQueryPolicyResp.setQueryPolicyDtoList(Arrays.asList(tkAutoQueryPolicyDto));
        tkAutoQueryPolicyRespTkAutoRespHelper.setData(tkAutoQueryPolicyResp);
        tkAutoQueryPolicyRespTkAutoRespHelper.setContent("content");
        when(mockAdapter.queryTkList(LocalDate.of(2020, 1, 1), LocalDate.of(2020, 1, 1))).thenReturn(tkAutoQueryPolicyRespTkAutoRespHelper);

        // Configure AmPolicyTkMapper.selectByExample(...).
        final AmPolicyTk amPolicyTk = new AmPolicyTk();
        amPolicyTk.setPolicyNo("policyNo");
        amPolicyTk.setSubPolicyNo("subPolicyNo");
        amPolicyTk.setRiskName("riskName");
        amPolicyTk.setRiskCode("riskCode");
        amPolicyTk.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk.setPolicyUrl("policyDownLoadUrl");
        amPolicyTk.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk.setBenchmarkPremium(new BigDecimal("0.00"));
        amPolicyTk.setState(0);
        amPolicyTk.setRemark("msg");
        final List<AmPolicyTk> amPolicyTks = Arrays.asList(amPolicyTk);
        when(mockPolicyTkMapper.selectByExample(any(Object.class))).thenReturn(amPolicyTks);

        // Configure TkAutoServiceAdapter.queryTkDetail(...).
        final TkAutoRespHelper<TkAutoQueryPolicyDetailResp> tkAutoQueryPolicyDetailRespTkAutoRespHelper = new TkAutoRespHelper<>();
        tkAutoQueryPolicyDetailRespTkAutoRespHelper.setAppCode("1");
        tkAutoQueryPolicyDetailRespTkAutoRespHelper.setAppMessage("appMessage");
        final TkAutoQueryPolicyDetailResp tkAutoQueryPolicyDetailResp = new TkAutoQueryPolicyDetailResp();
        final TkAutoRiskInfoD tkAutoRiskInfoD = new TkAutoRiskInfoD();
        tkAutoRiskInfoD.setRiskCode("riskCode");
        final TkAutoBaseInfoD baseInfoD = new TkAutoBaseInfoD();
        baseInfoD.setSubPolicyNo("subPolicyNo");
        baseInfoD.setRiskName("riskName");
        baseInfoD.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD.setPremium(new BigDecimal("0.00"));
        baseInfoD.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TkAutoPolicyViewList policyViewList = new TkAutoPolicyViewList();
        final TkAutoPolicyData policyData = new TkAutoPolicyData();
        policyData.setPolicyDownLoadUrl("policyDownLoadUrl");
        policyViewList.setPolicyData(policyData);
        baseInfoD.setPolicyViewList(policyViewList);
        tkAutoRiskInfoD.setBaseInfoD(baseInfoD);
        final TkAutoRelatedInfoD tkAutoRelatedInfoD = new TkAutoRelatedInfoD();
        tkAutoRelatedInfoD.setInsureFlag("insureFlag");
        tkAutoRelatedInfoD.setInsureTypeCode("insureTypeCode");
        tkAutoRiskInfoD.setRelatedInfoDList(Arrays.asList(tkAutoRelatedInfoD));
        tkAutoQueryPolicyDetailResp.setRiskInfoList(Arrays.asList(tkAutoRiskInfoD));
        tkAutoQueryPolicyDetailRespTkAutoRespHelper.setData(tkAutoQueryPolicyDetailResp);
        tkAutoQueryPolicyDetailRespTkAutoRespHelper.setContent("content");
        when(mockAdapter.queryTkDetail("policyNo")).thenReturn(tkAutoQueryPolicyDetailRespTkAutoRespHelper);

        // Configure TkAutoCvtHelper.findDrivingTkAutoRiskInfoD(...).
        final TkAutoRiskInfoD tkAutoRiskInfoD1 = new TkAutoRiskInfoD();
        tkAutoRiskInfoD1.setRiskCode("riskCode");
        final TkAutoBaseInfoD baseInfoD1 = new TkAutoBaseInfoD();
        baseInfoD1.setSubPolicyNo("subPolicyNo");
        baseInfoD1.setRiskName("riskName");
        baseInfoD1.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD1.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD1.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD1.setPremium(new BigDecimal("0.00"));
        baseInfoD1.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TkAutoPolicyViewList policyViewList1 = new TkAutoPolicyViewList();
        final TkAutoPolicyData policyData1 = new TkAutoPolicyData();
        policyData1.setPolicyDownLoadUrl("policyDownLoadUrl");
        policyViewList1.setPolicyData(policyData1);
        baseInfoD1.setPolicyViewList(policyViewList1);
        tkAutoRiskInfoD1.setBaseInfoD(baseInfoD1);
        final TkAutoRelatedInfoD tkAutoRelatedInfoD1 = new TkAutoRelatedInfoD();
        tkAutoRelatedInfoD1.setInsureFlag("insureFlag");
        tkAutoRelatedInfoD1.setInsureTypeCode("insureTypeCode");
        tkAutoRiskInfoD1.setRelatedInfoDList(Arrays.asList(tkAutoRelatedInfoD1));
        final TkAutoRiskInfoD tkAutoRiskInfoD2 = new TkAutoRiskInfoD();
        tkAutoRiskInfoD2.setRiskCode("riskCode");
        final TkAutoBaseInfoD baseInfoD2 = new TkAutoBaseInfoD();
        baseInfoD2.setSubPolicyNo("subPolicyNo");
        baseInfoD2.setRiskName("riskName");
        baseInfoD2.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD2.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD2.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD2.setPremium(new BigDecimal("0.00"));
        baseInfoD2.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TkAutoPolicyViewList policyViewList2 = new TkAutoPolicyViewList();
        final TkAutoPolicyData policyData2 = new TkAutoPolicyData();
        policyData2.setPolicyDownLoadUrl("policyDownLoadUrl");
        policyViewList2.setPolicyData(policyData2);
        baseInfoD2.setPolicyViewList(policyViewList2);
        tkAutoRiskInfoD2.setBaseInfoD(baseInfoD2);
        final TkAutoRelatedInfoD tkAutoRelatedInfoD2 = new TkAutoRelatedInfoD();
        tkAutoRelatedInfoD2.setInsureFlag("insureFlag");
        tkAutoRelatedInfoD2.setInsureTypeCode("insureTypeCode");
        tkAutoRiskInfoD2.setRelatedInfoDList(Arrays.asList(tkAutoRelatedInfoD2));
        final List<TkAutoRiskInfoD> tkAutoRiskInfoDList = Arrays.asList(tkAutoRiskInfoD2);
        when(mockCvtHelper.findDrivingTkAutoRiskInfoD(tkAutoRiskInfoDList)).thenReturn(tkAutoRiskInfoD1);

        // Configure TkAutoCvtHelper.getRelatedInfoDList(...).
        final TkAutoRelatedInfoD tkAutoRelatedInfoD3 = new TkAutoRelatedInfoD();
        tkAutoRelatedInfoD3.setInsuredName("insuredName");
        tkAutoRelatedInfoD3.setInsureFlag("insureFlag");
        tkAutoRelatedInfoD3.setInsureTypeCode("insureTypeCode");
        tkAutoRelatedInfoD3.setInsureType("insureType");
        tkAutoRelatedInfoD3.setCertiType("certiType");
        final List<TkAutoRelatedInfoD> tkAutoRelatedInfoDS = Arrays.asList(tkAutoRelatedInfoD3);
        final TkAutoRiskInfoD tkAutoRiskInfoD3 = new TkAutoRiskInfoD();
        tkAutoRiskInfoD3.setRiskCode("riskCode");
        final TkAutoBaseInfoD baseInfoD3 = new TkAutoBaseInfoD();
        baseInfoD3.setSubPolicyNo("subPolicyNo");
        baseInfoD3.setRiskName("riskName");
        baseInfoD3.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD3.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD3.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD3.setPremium(new BigDecimal("0.00"));
        baseInfoD3.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TkAutoPolicyViewList policyViewList3 = new TkAutoPolicyViewList();
        final TkAutoPolicyData policyData3 = new TkAutoPolicyData();
        policyData3.setPolicyDownLoadUrl("policyDownLoadUrl");
        policyViewList3.setPolicyData(policyData3);
        baseInfoD3.setPolicyViewList(policyViewList3);
        tkAutoRiskInfoD3.setBaseInfoD(baseInfoD3);
        final TkAutoRelatedInfoD tkAutoRelatedInfoD4 = new TkAutoRelatedInfoD();
        tkAutoRelatedInfoD4.setInsureFlag("insureFlag");
        tkAutoRelatedInfoD4.setInsureTypeCode("insureTypeCode");
        tkAutoRiskInfoD3.setRelatedInfoDList(Arrays.asList(tkAutoRelatedInfoD4));
        final List<TkAutoRiskInfoD> tkAutoRiskInfoDList1 = Arrays.asList(tkAutoRiskInfoD3);
        when(mockCvtHelper.getRelatedInfoDList(tkAutoRiskInfoDList1)).thenReturn(tkAutoRelatedInfoDS);

        // Configure AmPolicyTkMapper.selectOneByPolicyNo(...).
        final AmPolicyTk amPolicyTk1 = new AmPolicyTk();
        amPolicyTk1.setPolicyNo("policyNo");
        amPolicyTk1.setSubPolicyNo("subPolicyNo");
        amPolicyTk1.setRiskName("riskName");
        amPolicyTk1.setRiskCode("riskCode");
        amPolicyTk1.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk1.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk1.setPolicyUrl("policyDownLoadUrl");
        amPolicyTk1.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk1.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk1.setBenchmarkPremium(new BigDecimal("0.00"));
        amPolicyTk1.setState(0);
        amPolicyTk1.setRemark("msg");
        when(mockPolicyTkMapper.selectOneByPolicyNo("policyNo")).thenReturn(amPolicyTk1);

        // Configure TkAutoCvtHelper.cvtOrderInfo(...).
        final OrderSubmitRequest orderSubmitRequest = new OrderSubmitRequest();
        final FhProduct productInfo = new FhProduct();
        productInfo.setProductId("fhProductId");
        productInfo.setRecommendId("recommendId");
        productInfo.setRecommendMainJobNumber("recommendMainJobNumber");
        productInfo.setRecommendOrgCode("recommendOrgCode");
        orderSubmitRequest.setProductInfo(productInfo);
        final FhOrderInfo orderInfo = new FhOrderInfo();
        orderInfo.setSubmitTime("submitTime");
        orderInfo.setStartTime("startTime");
        orderInfo.setEndTime("endTime");
        orderSubmitRequest.setOrderInfo(orderInfo);
        final FhProposer proposerInfo = new FhProposer();
        proposerInfo.setPersonName("insuredName");
        proposerInfo.setPersonGender("insuredPersonGender");
        proposerInfo.setIdType("idType");
        proposerInfo.setIdNumber("idNumber");
        proposerInfo.setBirthday("birthday");
        proposerInfo.setCellPhone("cellPhone");
        proposerInfo.setEmail("email");
        proposerInfo.setAddress("address");
        proposerInfo.setAppStatus("-2");
        proposerInfo.setPolicyNo("insIdNumber");
        proposerInfo.setDownloadURL("downloadURL");
        orderSubmitRequest.setProposerInfo(proposerInfo);
        final FhInsuredPerson fhInsuredPerson = new FhInsuredPerson();
        fhInsuredPerson.setPersonName("insuredName");
        fhInsuredPerson.setIdType("idType");
        fhInsuredPerson.setIdNumber("idNumber");
        fhInsuredPerson.setBirthday("birthday");
        fhInsuredPerson.setCellPhone("cellPhone");
        fhInsuredPerson.setAppStatus("-2");
        fhInsuredPerson.setPolicyNo("insIdNumber");
        fhInsuredPerson.setRelationship("99");
        fhInsuredPerson.setFlightNo("flightNo");
        fhInsuredPerson.setFlightTime("flightTime");
        fhInsuredPerson.setOccupationCode("");
        fhInsuredPerson.setOccupationGroup("occupationGroup");
        fhInsuredPerson.setDestinationCountryText("destinationCountryText");
        fhInsuredPerson.setAnnualIncome("annualIncome");
        fhInsuredPerson.setStudentType("studentType");
        fhInsuredPerson.setSchoolType("schoolType");
        fhInsuredPerson.setSchoolNature("schoolNature");
        fhInsuredPerson.setSchoolName("schoolName");
        fhInsuredPerson.setSchoolClass("schoolClass");
        fhInsuredPerson.setStudentId("studentId");
        fhInsuredPerson.setIdPeriodStart("idPeriodStart");
        fhInsuredPerson.setIdPeriodEnd("idPeriodEnd");
        fhInsuredPerson.setSmoke("smoke");
        fhInsuredPerson.setOldPolicyNo("oldPolicyNo");
        final TestPremiumDutyForm testPremiumDutyForm = new TestPremiumDutyForm();
        fhInsuredPerson.setDuties(Arrays.asList(testPremiumDutyForm));
        final TestPremiumProductForm product = new TestPremiumProductForm();
        product.setProductId(0);
        product.setPlanId(0);
        product.setPlanCode("planCode");
        product.setPremium(new BigDecimal("0.00"));
        product.setQty(0);
        final TestPremiumDutyForm testPremiumDutyForm1 = new TestPremiumDutyForm();
        product.setPremiumDutyForms(Arrays.asList(testPremiumDutyForm1));
        fhInsuredPerson.setProduct(product);
        orderSubmitRequest.setInsuredPerson(Arrays.asList(fhInsuredPerson));
        final FhProperty propertyInfo = new FhProperty();
        propertyInfo.setPropertyInfoIsExist("0");
        orderSubmitRequest.setPropertyInfo(propertyInfo);
        final SmOrderHouseDTO house = new SmOrderHouseDTO();
        orderSubmitRequest.setHouse(house);
        final SmOrderCarInfoDTO carInfo = new SmOrderCarInfoDTO();
        orderSubmitRequest.setCarInfo(carInfo);
        orderSubmitRequest.setProductType("code");
        orderSubmitRequest.setBizCode("bizCode");
        orderSubmitRequest.setOrderOutType("orderOutType");
        orderSubmitRequest.setToken("token");
        orderSubmitRequest.setPlanId(0);
        orderSubmitRequest.setQty(0);
        orderSubmitRequest.setPreOrderId("preOrderId");
        orderSubmitRequest.setRenew(false);
        orderSubmitRequest.setRealRenewFlag(false);
        orderSubmitRequest.setAppNo("appNo");
        orderSubmitRequest.setAgentId(0);
        orderSubmitRequest.setJobCode("jobCode");
        final List<SmCreateOrderSubmitRequest> smCreateOrderSubmitRequests = Arrays.asList(new SmCreateOrderSubmitRequest(orderSubmitRequest));
        final AmPolicyTk tk = new AmPolicyTk();
        tk.setPolicyNo("policyNo");
        tk.setSubPolicyNo("subPolicyNo");
        tk.setRiskName("riskName");
        tk.setRiskCode("riskCode");
        tk.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        tk.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        tk.setPolicyUrl("policyDownLoadUrl");
        tk.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        tk.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        tk.setBenchmarkPremium(new BigDecimal("0.00"));
        tk.setState(0);
        tk.setRemark("msg");
        final TkAutoQueryPolicyDetailResp detailResp = new TkAutoQueryPolicyDetailResp();
        final TkAutoRiskInfoD tkAutoRiskInfoD4 = new TkAutoRiskInfoD();
        tkAutoRiskInfoD4.setRiskCode("riskCode");
        final TkAutoBaseInfoD baseInfoD4 = new TkAutoBaseInfoD();
        baseInfoD4.setSubPolicyNo("subPolicyNo");
        baseInfoD4.setRiskName("riskName");
        baseInfoD4.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD4.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD4.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD4.setPremium(new BigDecimal("0.00"));
        baseInfoD4.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TkAutoPolicyViewList policyViewList4 = new TkAutoPolicyViewList();
        final TkAutoPolicyData policyData4 = new TkAutoPolicyData();
        policyData4.setPolicyDownLoadUrl("policyDownLoadUrl");
        policyViewList4.setPolicyData(policyData4);
        baseInfoD4.setPolicyViewList(policyViewList4);
        tkAutoRiskInfoD4.setBaseInfoD(baseInfoD4);
        final TkAutoRelatedInfoD tkAutoRelatedInfoD5 = new TkAutoRelatedInfoD();
        tkAutoRelatedInfoD5.setInsureFlag("insureFlag");
        tkAutoRelatedInfoD5.setInsureTypeCode("insureTypeCode");
        tkAutoRiskInfoD4.setRelatedInfoDList(Arrays.asList(tkAutoRelatedInfoD5));
        detailResp.setRiskInfoList(Arrays.asList(tkAutoRiskInfoD4));
        when(mockCvtHelper.cvtOrderInfo(tk, detailResp, new HashMap<>())).thenReturn(smCreateOrderSubmitRequests);

        // Configure TkAutoCvtHelper.cvtAutoInfoList(...).
        final AutoOrderDTO autoOrderDTO = new AutoOrderDTO();
        autoOrderDTO.setOrderNo("orderNo");
        final AutoOrder order = new AutoOrder();
        order.setPayStatus("-1");
        order.setPaymentTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        order.setCommissionId(0);
        autoOrderDTO.setOrder(order);
        final AutoOrderPerson autoOrderPerson = new AutoOrderPerson();
        autoOrderDTO.setPeople(Arrays.asList(autoOrderPerson));
        final AutoOrderPolicyDTO autoOrderPolicyDTO = new AutoOrderPolicyDTO();
        final AutoOrderPolicy policy = new AutoOrderPolicy();
        policy.setPolicyNo("policyNo");
        policy.setSubPolicyNo("subPolicyNo");
        policy.setPolicyState("-2");
        policy.setBusinessScore("businessScore");
        autoOrderPolicyDTO.setPolicy(policy);
        final AutoOrderRisk autoOrderRisk = new AutoOrderRisk();
        autoOrderRisk.setRiskName("riskName");
        autoOrderRisk.setPremium(new BigDecimal("0.00"));
        autoOrderRisk.setAmount(new BigDecimal("0.00"));
        autoOrderPolicyDTO.setRiskList(Arrays.asList(autoOrderRisk));
        autoOrderDTO.setPolicies(Arrays.asList(autoOrderPolicyDTO));
        final AutoOrderExtend extend = new AutoOrderExtend();
        autoOrderDTO.setExtend(extend);
        final AutoOrderCar car = new AutoOrderCar();
        car.setOwnerPersonName("ownerPersonName");
        car.setOwnerPersonGender("ownerPersonGender");
        car.setOwnerIdType("ownerIdType");
        car.setOwnerIdNumber("ownerIdNumber");
        car.setFirstRegDate("firstRegDate");
        car.setFullLoad("fullLoad");
        car.setTransferDate("transferDate");
        car.setSeatCnt("approvedNum");
        car.setPrice("price");
        car.setCarUserType("carUserType");
        car.setDisplacement("displacement");
        car.setIsNewCar("isNewCar");
        autoOrderDTO.setCar(car);
        final List<AutoOrderDTO> autoOrderDTOS = Arrays.asList(autoOrderDTO);
        final AmPolicyTk tk1 = new AmPolicyTk();
        tk1.setPolicyNo("policyNo");
        tk1.setSubPolicyNo("subPolicyNo");
        tk1.setRiskName("riskName");
        tk1.setRiskCode("riskCode");
        tk1.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        tk1.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        tk1.setPolicyUrl("policyDownLoadUrl");
        tk1.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        tk1.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        tk1.setBenchmarkPremium(new BigDecimal("0.00"));
        tk1.setState(0);
        tk1.setRemark("msg");
        final TkAutoQueryPolicyDetailResp detailResp1 = new TkAutoQueryPolicyDetailResp();
        final TkAutoRiskInfoD tkAutoRiskInfoD5 = new TkAutoRiskInfoD();
        tkAutoRiskInfoD5.setRiskCode("riskCode");
        final TkAutoBaseInfoD baseInfoD5 = new TkAutoBaseInfoD();
        baseInfoD5.setSubPolicyNo("subPolicyNo");
        baseInfoD5.setRiskName("riskName");
        baseInfoD5.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD5.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD5.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD5.setPremium(new BigDecimal("0.00"));
        baseInfoD5.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TkAutoPolicyViewList policyViewList5 = new TkAutoPolicyViewList();
        final TkAutoPolicyData policyData5 = new TkAutoPolicyData();
        policyData5.setPolicyDownLoadUrl("policyDownLoadUrl");
        policyViewList5.setPolicyData(policyData5);
        baseInfoD5.setPolicyViewList(policyViewList5);
        tkAutoRiskInfoD5.setBaseInfoD(baseInfoD5);
        final TkAutoRelatedInfoD tkAutoRelatedInfoD6 = new TkAutoRelatedInfoD();
        tkAutoRelatedInfoD6.setInsureFlag("insureFlag");
        tkAutoRelatedInfoD6.setInsureTypeCode("insureTypeCode");
        tkAutoRiskInfoD5.setRelatedInfoDList(Arrays.asList(tkAutoRelatedInfoD6));
        detailResp1.setRiskInfoList(Arrays.asList(tkAutoRiskInfoD5));
        when(mockCvtHelper.cvtAutoInfoList(tk1, Arrays.asList("value"), detailResp1, new HashMap<>())).thenReturn(autoOrderDTOS);

        when(mockOrderMapper.updateOrderPaymentTime("orderId")).thenReturn(0);
        when(mockOrderMapper.getFhProductIdByOrderId("orderId")).thenReturn(Collections.emptyList());

        // Run the test
        tkAutoOrderServiceUnderTest.sync(LocalDate.of(2020, 1, 1), LocalDate.of(2020, 1, 1));

        // Verify the results
        // Confirm AmSyncTkRecordMapper.insertUseGeneratedKeys(...).
        final AmSyncTkRecord record = new AmSyncTkRecord();
        record.setBatchNo("batchNo");
        record.setStartDate(LocalDate.of(2020, 1, 1));
        record.setEndDate(LocalDate.of(2020, 1, 1));
        record.setOrderCount(0);
        record.setRespContent("respContent");

        // Confirm AmPolicyTkMapper.insertList(...).
        final AmPolicyTk amPolicyTk2 = new AmPolicyTk();
        amPolicyTk2.setPolicyNo("policyNo");
        amPolicyTk2.setSubPolicyNo("subPolicyNo");
        amPolicyTk2.setRiskName("riskName");
        amPolicyTk2.setRiskCode("riskCode");
        amPolicyTk2.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk2.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk2.setPolicyUrl("policyDownLoadUrl");
        amPolicyTk2.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk2.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk2.setBenchmarkPremium(new BigDecimal("0.00"));
        amPolicyTk2.setState(0);
        amPolicyTk2.setRemark("msg");
        final List<AmPolicyTk> recordList = Arrays.asList(amPolicyTk2);

        // Confirm AmPolicyTkMapper.updateByExampleSelective(...).
        final AmPolicyTk record1 = new AmPolicyTk();
        record1.setPolicyNo("policyNo");
        record1.setSubPolicyNo("subPolicyNo");
        record1.setRiskName("riskName");
        record1.setRiskCode("riskCode");
        record1.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record1.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record1.setPolicyUrl("policyDownLoadUrl");
        record1.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record1.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record1.setBenchmarkPremium(new BigDecimal("0.00"));
        record1.setState(0);
        record1.setRemark("msg");

        // Confirm AmPolicyTkMapper.insertUseGeneratedKeys(...).
        final AmPolicyTk record2 = new AmPolicyTk();
        record2.setPolicyNo("policyNo");
        record2.setSubPolicyNo("subPolicyNo");
        record2.setRiskName("riskName");
        record2.setRiskCode("riskCode");
        record2.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record2.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record2.setPolicyUrl("policyDownLoadUrl");
        record2.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record2.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record2.setBenchmarkPremium(new BigDecimal("0.00"));
        record2.setState(0);
        record2.setRemark("msg");

        // Confirm AmPolicyTkDetailMapper.insertUseGeneratedKeys(...).
        final AmPolicyTkDetail record3 = new AmPolicyTkDetail();
        record3.setPolicyNo("policyNo");
        record3.setContent("content");

        // Confirm SmOrderMapper.insertOrder(...).
        final OrderSubmitRequest orderSubmitRequest1 = new OrderSubmitRequest();
        final FhProduct productInfo1 = new FhProduct();
        productInfo1.setProductId("fhProductId");
        productInfo1.setRecommendId("recommendId");
        productInfo1.setRecommendMainJobNumber("recommendMainJobNumber");
        productInfo1.setRecommendOrgCode("recommendOrgCode");
        orderSubmitRequest1.setProductInfo(productInfo1);
        final FhOrderInfo orderInfo1 = new FhOrderInfo();
        orderInfo1.setSubmitTime("submitTime");
        orderInfo1.setStartTime("startTime");
        orderInfo1.setEndTime("endTime");
        orderSubmitRequest1.setOrderInfo(orderInfo1);
        final FhProposer proposerInfo1 = new FhProposer();
        proposerInfo1.setPersonName("insuredName");
        proposerInfo1.setPersonGender("insuredPersonGender");
        proposerInfo1.setIdType("idType");
        proposerInfo1.setIdNumber("idNumber");
        proposerInfo1.setBirthday("birthday");
        proposerInfo1.setCellPhone("cellPhone");
        proposerInfo1.setEmail("email");
        proposerInfo1.setAddress("address");
        proposerInfo1.setAppStatus("-2");
        proposerInfo1.setPolicyNo("insIdNumber");
        proposerInfo1.setDownloadURL("downloadURL");
        orderSubmitRequest1.setProposerInfo(proposerInfo1);
        final FhInsuredPerson fhInsuredPerson1 = new FhInsuredPerson();
        fhInsuredPerson1.setPersonName("insuredName");
        fhInsuredPerson1.setIdType("idType");
        fhInsuredPerson1.setIdNumber("idNumber");
        fhInsuredPerson1.setBirthday("birthday");
        fhInsuredPerson1.setCellPhone("cellPhone");
        fhInsuredPerson1.setAppStatus("-2");
        fhInsuredPerson1.setPolicyNo("insIdNumber");
        fhInsuredPerson1.setRelationship("99");
        fhInsuredPerson1.setFlightNo("flightNo");
        fhInsuredPerson1.setFlightTime("flightTime");
        fhInsuredPerson1.setOccupationCode("");
        fhInsuredPerson1.setOccupationGroup("occupationGroup");
        fhInsuredPerson1.setDestinationCountryText("destinationCountryText");
        fhInsuredPerson1.setAnnualIncome("annualIncome");
        fhInsuredPerson1.setStudentType("studentType");
        fhInsuredPerson1.setSchoolType("schoolType");
        fhInsuredPerson1.setSchoolNature("schoolNature");
        fhInsuredPerson1.setSchoolName("schoolName");
        fhInsuredPerson1.setSchoolClass("schoolClass");
        fhInsuredPerson1.setStudentId("studentId");
        fhInsuredPerson1.setIdPeriodStart("idPeriodStart");
        fhInsuredPerson1.setIdPeriodEnd("idPeriodEnd");
        fhInsuredPerson1.setSmoke("smoke");
        fhInsuredPerson1.setOldPolicyNo("oldPolicyNo");
        final TestPremiumDutyForm testPremiumDutyForm2 = new TestPremiumDutyForm();
        fhInsuredPerson1.setDuties(Arrays.asList(testPremiumDutyForm2));
        final TestPremiumProductForm product1 = new TestPremiumProductForm();
        product1.setProductId(0);
        product1.setPlanId(0);
        product1.setPlanCode("planCode");
        product1.setPremium(new BigDecimal("0.00"));
        product1.setQty(0);
        final TestPremiumDutyForm testPremiumDutyForm3 = new TestPremiumDutyForm();
        product1.setPremiumDutyForms(Arrays.asList(testPremiumDutyForm3));
        fhInsuredPerson1.setProduct(product1);
        orderSubmitRequest1.setInsuredPerson(Arrays.asList(fhInsuredPerson1));
        final FhProperty propertyInfo1 = new FhProperty();
        propertyInfo1.setPropertyInfoIsExist("0");
        orderSubmitRequest1.setPropertyInfo(propertyInfo1);
        final SmOrderHouseDTO house1 = new SmOrderHouseDTO();
        orderSubmitRequest1.setHouse(house1);
        final SmOrderCarInfoDTO carInfo1 = new SmOrderCarInfoDTO();
        orderSubmitRequest1.setCarInfo(carInfo1);
        orderSubmitRequest1.setProductType("code");
        orderSubmitRequest1.setBizCode("bizCode");
        orderSubmitRequest1.setOrderOutType("orderOutType");
        orderSubmitRequest1.setToken("token");
        orderSubmitRequest1.setPlanId(0);
        orderSubmitRequest1.setQty(0);
        orderSubmitRequest1.setPreOrderId("preOrderId");
        orderSubmitRequest1.setRenew(false);
        orderSubmitRequest1.setRealRenewFlag(false);
        orderSubmitRequest1.setAppNo("appNo");
        orderSubmitRequest1.setAgentId(0);
        orderSubmitRequest1.setJobCode("jobCode");
        final SmCreateOrderSubmitRequest dto = new SmCreateOrderSubmitRequest(orderSubmitRequest1);

        // Confirm SmOrderMapper.insertOrderApplicant(...).
        final OrderSubmitRequest orderSubmitRequest2 = new OrderSubmitRequest();
        final FhProduct productInfo2 = new FhProduct();
        productInfo2.setProductId("fhProductId");
        productInfo2.setRecommendId("recommendId");
        productInfo2.setRecommendMainJobNumber("recommendMainJobNumber");
        productInfo2.setRecommendOrgCode("recommendOrgCode");
        orderSubmitRequest2.setProductInfo(productInfo2);
        final FhOrderInfo orderInfo2 = new FhOrderInfo();
        orderInfo2.setSubmitTime("submitTime");
        orderInfo2.setStartTime("startTime");
        orderInfo2.setEndTime("endTime");
        orderSubmitRequest2.setOrderInfo(orderInfo2);
        final FhProposer proposerInfo2 = new FhProposer();
        proposerInfo2.setPersonName("insuredName");
        proposerInfo2.setPersonGender("insuredPersonGender");
        proposerInfo2.setIdType("idType");
        proposerInfo2.setIdNumber("idNumber");
        proposerInfo2.setBirthday("birthday");
        proposerInfo2.setCellPhone("cellPhone");
        proposerInfo2.setEmail("email");
        proposerInfo2.setAddress("address");
        proposerInfo2.setAppStatus("-2");
        proposerInfo2.setPolicyNo("insIdNumber");
        proposerInfo2.setDownloadURL("downloadURL");
        orderSubmitRequest2.setProposerInfo(proposerInfo2);
        final FhInsuredPerson fhInsuredPerson2 = new FhInsuredPerson();
        fhInsuredPerson2.setPersonName("insuredName");
        fhInsuredPerson2.setIdType("idType");
        fhInsuredPerson2.setIdNumber("idNumber");
        fhInsuredPerson2.setBirthday("birthday");
        fhInsuredPerson2.setCellPhone("cellPhone");
        fhInsuredPerson2.setAppStatus("-2");
        fhInsuredPerson2.setPolicyNo("insIdNumber");
        fhInsuredPerson2.setRelationship("99");
        fhInsuredPerson2.setFlightNo("flightNo");
        fhInsuredPerson2.setFlightTime("flightTime");
        fhInsuredPerson2.setOccupationCode("");
        fhInsuredPerson2.setOccupationGroup("occupationGroup");
        fhInsuredPerson2.setDestinationCountryText("destinationCountryText");
        fhInsuredPerson2.setAnnualIncome("annualIncome");
        fhInsuredPerson2.setStudentType("studentType");
        fhInsuredPerson2.setSchoolType("schoolType");
        fhInsuredPerson2.setSchoolNature("schoolNature");
        fhInsuredPerson2.setSchoolName("schoolName");
        fhInsuredPerson2.setSchoolClass("schoolClass");
        fhInsuredPerson2.setStudentId("studentId");
        fhInsuredPerson2.setIdPeriodStart("idPeriodStart");
        fhInsuredPerson2.setIdPeriodEnd("idPeriodEnd");
        fhInsuredPerson2.setSmoke("smoke");
        fhInsuredPerson2.setOldPolicyNo("oldPolicyNo");
        final TestPremiumDutyForm testPremiumDutyForm4 = new TestPremiumDutyForm();
        fhInsuredPerson2.setDuties(Arrays.asList(testPremiumDutyForm4));
        final TestPremiumProductForm product2 = new TestPremiumProductForm();
        product2.setProductId(0);
        product2.setPlanId(0);
        product2.setPlanCode("planCode");
        product2.setPremium(new BigDecimal("0.00"));
        product2.setQty(0);
        final TestPremiumDutyForm testPremiumDutyForm5 = new TestPremiumDutyForm();
        product2.setPremiumDutyForms(Arrays.asList(testPremiumDutyForm5));
        fhInsuredPerson2.setProduct(product2);
        orderSubmitRequest2.setInsuredPerson(Arrays.asList(fhInsuredPerson2));
        final FhProperty propertyInfo2 = new FhProperty();
        propertyInfo2.setPropertyInfoIsExist("0");
        orderSubmitRequest2.setPropertyInfo(propertyInfo2);
        final SmOrderHouseDTO house2 = new SmOrderHouseDTO();
        orderSubmitRequest2.setHouse(house2);
        final SmOrderCarInfoDTO carInfo2 = new SmOrderCarInfoDTO();
        orderSubmitRequest2.setCarInfo(carInfo2);
        orderSubmitRequest2.setProductType("code");
        orderSubmitRequest2.setBizCode("bizCode");
        orderSubmitRequest2.setOrderOutType("orderOutType");
        orderSubmitRequest2.setToken("token");
        orderSubmitRequest2.setPlanId(0);
        orderSubmitRequest2.setQty(0);
        orderSubmitRequest2.setPreOrderId("preOrderId");
        orderSubmitRequest2.setRenew(false);
        orderSubmitRequest2.setRealRenewFlag(false);
        orderSubmitRequest2.setAppNo("appNo");
        orderSubmitRequest2.setAgentId(0);
        orderSubmitRequest2.setJobCode("jobCode");
        final SmCreateOrderSubmitRequest dto1 = new SmCreateOrderSubmitRequest(orderSubmitRequest2);

        // Confirm SmOrderMapper.insertOrderInsured(...).
        final OrderSubmitRequest orderSubmitRequest3 = new OrderSubmitRequest();
        final FhProduct productInfo3 = new FhProduct();
        productInfo3.setProductId("fhProductId");
        productInfo3.setRecommendId("recommendId");
        productInfo3.setRecommendMainJobNumber("recommendMainJobNumber");
        productInfo3.setRecommendOrgCode("recommendOrgCode");
        orderSubmitRequest3.setProductInfo(productInfo3);
        final FhOrderInfo orderInfo3 = new FhOrderInfo();
        orderInfo3.setSubmitTime("submitTime");
        orderInfo3.setStartTime("startTime");
        orderInfo3.setEndTime("endTime");
        orderSubmitRequest3.setOrderInfo(orderInfo3);
        final FhProposer proposerInfo3 = new FhProposer();
        proposerInfo3.setPersonName("insuredName");
        proposerInfo3.setPersonGender("insuredPersonGender");
        proposerInfo3.setIdType("idType");
        proposerInfo3.setIdNumber("idNumber");
        proposerInfo3.setBirthday("birthday");
        proposerInfo3.setCellPhone("cellPhone");
        proposerInfo3.setEmail("email");
        proposerInfo3.setAddress("address");
        proposerInfo3.setAppStatus("-2");
        proposerInfo3.setPolicyNo("insIdNumber");
        proposerInfo3.setDownloadURL("downloadURL");
        orderSubmitRequest3.setProposerInfo(proposerInfo3);
        final FhInsuredPerson fhInsuredPerson3 = new FhInsuredPerson();
        fhInsuredPerson3.setPersonName("insuredName");
        fhInsuredPerson3.setIdType("idType");
        fhInsuredPerson3.setIdNumber("idNumber");
        fhInsuredPerson3.setBirthday("birthday");
        fhInsuredPerson3.setCellPhone("cellPhone");
        fhInsuredPerson3.setAppStatus("-2");
        fhInsuredPerson3.setPolicyNo("insIdNumber");
        fhInsuredPerson3.setRelationship("99");
        fhInsuredPerson3.setFlightNo("flightNo");
        fhInsuredPerson3.setFlightTime("flightTime");
        fhInsuredPerson3.setOccupationCode("");
        fhInsuredPerson3.setOccupationGroup("occupationGroup");
        fhInsuredPerson3.setDestinationCountryText("destinationCountryText");
        fhInsuredPerson3.setAnnualIncome("annualIncome");
        fhInsuredPerson3.setStudentType("studentType");
        fhInsuredPerson3.setSchoolType("schoolType");
        fhInsuredPerson3.setSchoolNature("schoolNature");
        fhInsuredPerson3.setSchoolName("schoolName");
        fhInsuredPerson3.setSchoolClass("schoolClass");
        fhInsuredPerson3.setStudentId("studentId");
        fhInsuredPerson3.setIdPeriodStart("idPeriodStart");
        fhInsuredPerson3.setIdPeriodEnd("idPeriodEnd");
        fhInsuredPerson3.setSmoke("smoke");
        fhInsuredPerson3.setOldPolicyNo("oldPolicyNo");
        final TestPremiumDutyForm testPremiumDutyForm6 = new TestPremiumDutyForm();
        fhInsuredPerson3.setDuties(Arrays.asList(testPremiumDutyForm6));
        final TestPremiumProductForm product3 = new TestPremiumProductForm();
        product3.setProductId(0);
        product3.setPlanId(0);
        product3.setPlanCode("planCode");
        product3.setPremium(new BigDecimal("0.00"));
        product3.setQty(0);
        final TestPremiumDutyForm testPremiumDutyForm7 = new TestPremiumDutyForm();
        product3.setPremiumDutyForms(Arrays.asList(testPremiumDutyForm7));
        fhInsuredPerson3.setProduct(product3);
        orderSubmitRequest3.setInsuredPerson(Arrays.asList(fhInsuredPerson3));
        final FhProperty propertyInfo3 = new FhProperty();
        propertyInfo3.setPropertyInfoIsExist("0");
        orderSubmitRequest3.setPropertyInfo(propertyInfo3);
        final SmOrderHouseDTO house3 = new SmOrderHouseDTO();
        orderSubmitRequest3.setHouse(house3);
        final SmOrderCarInfoDTO carInfo3 = new SmOrderCarInfoDTO();
        orderSubmitRequest3.setCarInfo(carInfo3);
        orderSubmitRequest3.setProductType("code");
        orderSubmitRequest3.setBizCode("bizCode");
        orderSubmitRequest3.setOrderOutType("orderOutType");
        orderSubmitRequest3.setToken("token");
        orderSubmitRequest3.setPlanId(0);
        orderSubmitRequest3.setQty(0);
        orderSubmitRequest3.setPreOrderId("preOrderId");
        orderSubmitRequest3.setRenew(false);
        orderSubmitRequest3.setRealRenewFlag(false);
        orderSubmitRequest3.setAppNo("appNo");
        orderSubmitRequest3.setAgentId(0);
        orderSubmitRequest3.setJobCode("jobCode");
        final SmCreateOrderSubmitRequest dto2 = new SmCreateOrderSubmitRequest(orderSubmitRequest3);
    }

    @Test
    public void testSyncPolicyDetail() throws Exception {
        // Setup
        // Configure TkAutoServiceAdapter.queryTkDetail(...).
        final TkAutoRespHelper<TkAutoQueryPolicyDetailResp> tkAutoQueryPolicyDetailRespTkAutoRespHelper = new TkAutoRespHelper<>();
        tkAutoQueryPolicyDetailRespTkAutoRespHelper.setAppCode("1");
        tkAutoQueryPolicyDetailRespTkAutoRespHelper.setAppMessage("appMessage");
        final TkAutoQueryPolicyDetailResp tkAutoQueryPolicyDetailResp = new TkAutoQueryPolicyDetailResp();
        final TkAutoRiskInfoD tkAutoRiskInfoD = new TkAutoRiskInfoD();
        tkAutoRiskInfoD.setRiskCode("riskCode");
        final TkAutoBaseInfoD baseInfoD = new TkAutoBaseInfoD();
        baseInfoD.setSubPolicyNo("subPolicyNo");
        baseInfoD.setRiskName("riskName");
        baseInfoD.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD.setPremium(new BigDecimal("0.00"));
        baseInfoD.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TkAutoPolicyViewList policyViewList = new TkAutoPolicyViewList();
        final TkAutoPolicyData policyData = new TkAutoPolicyData();
        policyData.setPolicyDownLoadUrl("policyDownLoadUrl");
        policyViewList.setPolicyData(policyData);
        baseInfoD.setPolicyViewList(policyViewList);
        tkAutoRiskInfoD.setBaseInfoD(baseInfoD);
        final TkAutoRelatedInfoD tkAutoRelatedInfoD = new TkAutoRelatedInfoD();
        tkAutoRelatedInfoD.setInsureFlag("insureFlag");
        tkAutoRelatedInfoD.setInsureTypeCode("insureTypeCode");
        tkAutoRiskInfoD.setRelatedInfoDList(Arrays.asList(tkAutoRelatedInfoD));
        tkAutoQueryPolicyDetailResp.setRiskInfoList(Arrays.asList(tkAutoRiskInfoD));
        tkAutoQueryPolicyDetailRespTkAutoRespHelper.setData(tkAutoQueryPolicyDetailResp);
        tkAutoQueryPolicyDetailRespTkAutoRespHelper.setContent("content");
        when(mockAdapter.queryTkDetail("policyNo")).thenReturn(tkAutoQueryPolicyDetailRespTkAutoRespHelper);

        // Configure TkAutoCvtHelper.findDrivingTkAutoRiskInfoD(...).
        final TkAutoRiskInfoD tkAutoRiskInfoD1 = new TkAutoRiskInfoD();
        tkAutoRiskInfoD1.setRiskCode("riskCode");
        final TkAutoBaseInfoD baseInfoD1 = new TkAutoBaseInfoD();
        baseInfoD1.setSubPolicyNo("subPolicyNo");
        baseInfoD1.setRiskName("riskName");
        baseInfoD1.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD1.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD1.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD1.setPremium(new BigDecimal("0.00"));
        baseInfoD1.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TkAutoPolicyViewList policyViewList1 = new TkAutoPolicyViewList();
        final TkAutoPolicyData policyData1 = new TkAutoPolicyData();
        policyData1.setPolicyDownLoadUrl("policyDownLoadUrl");
        policyViewList1.setPolicyData(policyData1);
        baseInfoD1.setPolicyViewList(policyViewList1);
        tkAutoRiskInfoD1.setBaseInfoD(baseInfoD1);
        final TkAutoRelatedInfoD tkAutoRelatedInfoD1 = new TkAutoRelatedInfoD();
        tkAutoRelatedInfoD1.setInsureFlag("insureFlag");
        tkAutoRelatedInfoD1.setInsureTypeCode("insureTypeCode");
        tkAutoRiskInfoD1.setRelatedInfoDList(Arrays.asList(tkAutoRelatedInfoD1));
        final TkAutoRiskInfoD tkAutoRiskInfoD2 = new TkAutoRiskInfoD();
        tkAutoRiskInfoD2.setRiskCode("riskCode");
        final TkAutoBaseInfoD baseInfoD2 = new TkAutoBaseInfoD();
        baseInfoD2.setSubPolicyNo("subPolicyNo");
        baseInfoD2.setRiskName("riskName");
        baseInfoD2.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD2.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD2.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD2.setPremium(new BigDecimal("0.00"));
        baseInfoD2.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TkAutoPolicyViewList policyViewList2 = new TkAutoPolicyViewList();
        final TkAutoPolicyData policyData2 = new TkAutoPolicyData();
        policyData2.setPolicyDownLoadUrl("policyDownLoadUrl");
        policyViewList2.setPolicyData(policyData2);
        baseInfoD2.setPolicyViewList(policyViewList2);
        tkAutoRiskInfoD2.setBaseInfoD(baseInfoD2);
        final TkAutoRelatedInfoD tkAutoRelatedInfoD2 = new TkAutoRelatedInfoD();
        tkAutoRelatedInfoD2.setInsureFlag("insureFlag");
        tkAutoRelatedInfoD2.setInsureTypeCode("insureTypeCode");
        tkAutoRiskInfoD2.setRelatedInfoDList(Arrays.asList(tkAutoRelatedInfoD2));
        final List<TkAutoRiskInfoD> tkAutoRiskInfoDList = Arrays.asList(tkAutoRiskInfoD2);
        when(mockCvtHelper.findDrivingTkAutoRiskInfoD(tkAutoRiskInfoDList)).thenReturn(tkAutoRiskInfoD1);

        // Configure TkAutoCvtHelper.getRelatedInfoDList(...).
        final TkAutoRelatedInfoD tkAutoRelatedInfoD3 = new TkAutoRelatedInfoD();
        tkAutoRelatedInfoD3.setInsuredName("insuredName");
        tkAutoRelatedInfoD3.setInsureFlag("insureFlag");
        tkAutoRelatedInfoD3.setInsureTypeCode("insureTypeCode");
        tkAutoRelatedInfoD3.setInsureType("insureType");
        tkAutoRelatedInfoD3.setCertiType("certiType");
        final List<TkAutoRelatedInfoD> tkAutoRelatedInfoDS = Arrays.asList(tkAutoRelatedInfoD3);
        final TkAutoRiskInfoD tkAutoRiskInfoD3 = new TkAutoRiskInfoD();
        tkAutoRiskInfoD3.setRiskCode("riskCode");
        final TkAutoBaseInfoD baseInfoD3 = new TkAutoBaseInfoD();
        baseInfoD3.setSubPolicyNo("subPolicyNo");
        baseInfoD3.setRiskName("riskName");
        baseInfoD3.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD3.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD3.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD3.setPremium(new BigDecimal("0.00"));
        baseInfoD3.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TkAutoPolicyViewList policyViewList3 = new TkAutoPolicyViewList();
        final TkAutoPolicyData policyData3 = new TkAutoPolicyData();
        policyData3.setPolicyDownLoadUrl("policyDownLoadUrl");
        policyViewList3.setPolicyData(policyData3);
        baseInfoD3.setPolicyViewList(policyViewList3);
        tkAutoRiskInfoD3.setBaseInfoD(baseInfoD3);
        final TkAutoRelatedInfoD tkAutoRelatedInfoD4 = new TkAutoRelatedInfoD();
        tkAutoRelatedInfoD4.setInsureFlag("insureFlag");
        tkAutoRelatedInfoD4.setInsureTypeCode("insureTypeCode");
        tkAutoRiskInfoD3.setRelatedInfoDList(Arrays.asList(tkAutoRelatedInfoD4));
        final List<TkAutoRiskInfoD> tkAutoRiskInfoDList1 = Arrays.asList(tkAutoRiskInfoD3);
        when(mockCvtHelper.getRelatedInfoDList(tkAutoRiskInfoDList1)).thenReturn(tkAutoRelatedInfoDS);

        // Run the test
        final List<String> result = tkAutoOrderServiceUnderTest.syncPolicyDetail("policyNo");

    }

    @Test
    public void testSyncPolicyDetail_TkAutoCvtHelperFindDrivingTkAutoRiskInfoDReturnsNull() throws Exception {
        // Setup
        // Configure TkAutoServiceAdapter.queryTkDetail(...).
        final TkAutoRespHelper<TkAutoQueryPolicyDetailResp> tkAutoQueryPolicyDetailRespTkAutoRespHelper = new TkAutoRespHelper<>();
        tkAutoQueryPolicyDetailRespTkAutoRespHelper.setAppCode("1");
        tkAutoQueryPolicyDetailRespTkAutoRespHelper.setAppMessage("appMessage");
        final TkAutoQueryPolicyDetailResp tkAutoQueryPolicyDetailResp = new TkAutoQueryPolicyDetailResp();
        final TkAutoRiskInfoD tkAutoRiskInfoD = new TkAutoRiskInfoD();
        tkAutoRiskInfoD.setRiskCode("riskCode");
        final TkAutoBaseInfoD baseInfoD = new TkAutoBaseInfoD();
        baseInfoD.setSubPolicyNo("subPolicyNo");
        baseInfoD.setRiskName("riskName");
        baseInfoD.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD.setPremium(new BigDecimal("0.00"));
        baseInfoD.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TkAutoPolicyViewList policyViewList = new TkAutoPolicyViewList();
        final TkAutoPolicyData policyData = new TkAutoPolicyData();
        policyData.setPolicyDownLoadUrl("policyDownLoadUrl");
        policyViewList.setPolicyData(policyData);
        baseInfoD.setPolicyViewList(policyViewList);
        tkAutoRiskInfoD.setBaseInfoD(baseInfoD);
        final TkAutoRelatedInfoD tkAutoRelatedInfoD = new TkAutoRelatedInfoD();
        tkAutoRelatedInfoD.setInsureFlag("insureFlag");
        tkAutoRelatedInfoD.setInsureTypeCode("insureTypeCode");
        tkAutoRiskInfoD.setRelatedInfoDList(Arrays.asList(tkAutoRelatedInfoD));
        tkAutoQueryPolicyDetailResp.setRiskInfoList(Arrays.asList(tkAutoRiskInfoD));
        tkAutoQueryPolicyDetailRespTkAutoRespHelper.setData(tkAutoQueryPolicyDetailResp);
        tkAutoQueryPolicyDetailRespTkAutoRespHelper.setContent("content");
        when(mockAdapter.queryTkDetail("policyNo")).thenReturn(tkAutoQueryPolicyDetailRespTkAutoRespHelper);

        // Configure TkAutoCvtHelper.findDrivingTkAutoRiskInfoD(...).
        final TkAutoRiskInfoD tkAutoRiskInfoD1 = new TkAutoRiskInfoD();
        tkAutoRiskInfoD1.setRiskCode("riskCode");
        final TkAutoBaseInfoD baseInfoD1 = new TkAutoBaseInfoD();
        baseInfoD1.setSubPolicyNo("subPolicyNo");
        baseInfoD1.setRiskName("riskName");
        baseInfoD1.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD1.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD1.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD1.setPremium(new BigDecimal("0.00"));
        baseInfoD1.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TkAutoPolicyViewList policyViewList1 = new TkAutoPolicyViewList();
        final TkAutoPolicyData policyData1 = new TkAutoPolicyData();
        policyData1.setPolicyDownLoadUrl("policyDownLoadUrl");
        policyViewList1.setPolicyData(policyData1);
        baseInfoD1.setPolicyViewList(policyViewList1);
        tkAutoRiskInfoD1.setBaseInfoD(baseInfoD1);
        final TkAutoRelatedInfoD tkAutoRelatedInfoD1 = new TkAutoRelatedInfoD();
        tkAutoRelatedInfoD1.setInsureFlag("insureFlag");
        tkAutoRelatedInfoD1.setInsureTypeCode("insureTypeCode");
        tkAutoRiskInfoD1.setRelatedInfoDList(Arrays.asList(tkAutoRelatedInfoD1));
        final List<TkAutoRiskInfoD> tkAutoRiskInfoDList = Arrays.asList(tkAutoRiskInfoD1);
        when(mockCvtHelper.findDrivingTkAutoRiskInfoD(tkAutoRiskInfoDList)).thenReturn(null);

        // Run the test
        final List<String> result = tkAutoOrderServiceUnderTest.syncPolicyDetail("policyNo");

        // Verify the results
        assertNull(result);

        // Confirm AmPolicyTkMapper.updateByExampleSelective(...).
        final AmPolicyTk record = new AmPolicyTk();
        record.setPolicyNo("policyNo");
        record.setSubPolicyNo("subPolicyNo");
        record.setRiskName("riskName");
        record.setRiskCode("riskCode");
        record.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record.setPolicyUrl("policyDownLoadUrl");
        record.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record.setBenchmarkPremium(new BigDecimal("0.00"));
        record.setState(0);
        record.setRemark("msg");

        // Confirm AmPolicyTkMapper.insertUseGeneratedKeys(...).
        final AmPolicyTk record1 = new AmPolicyTk();
        record1.setPolicyNo("policyNo");
        record1.setSubPolicyNo("subPolicyNo");
        record1.setRiskName("riskName");
        record1.setRiskCode("riskCode");
        record1.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record1.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record1.setPolicyUrl("policyDownLoadUrl");
        record1.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record1.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record1.setBenchmarkPremium(new BigDecimal("0.00"));
        record1.setState(0);
        record1.setRemark("msg");

        // Confirm AmPolicyTkDetailMapper.insertUseGeneratedKeys(...).
        final AmPolicyTkDetail record2 = new AmPolicyTkDetail();
        record2.setPolicyNo("policyNo");
        record2.setContent("content");
    }

    @Test
    public void testSyncPolicyDetail_TkAutoCvtHelperGetRelatedInfoDListReturnsNoItems() throws Exception {
        // Setup
        // Configure TkAutoServiceAdapter.queryTkDetail(...).
        final TkAutoRespHelper<TkAutoQueryPolicyDetailResp> tkAutoQueryPolicyDetailRespTkAutoRespHelper = new TkAutoRespHelper<>();
        tkAutoQueryPolicyDetailRespTkAutoRespHelper.setAppCode("1");
        tkAutoQueryPolicyDetailRespTkAutoRespHelper.setAppMessage("appMessage");
        final TkAutoQueryPolicyDetailResp tkAutoQueryPolicyDetailResp = new TkAutoQueryPolicyDetailResp();
        final TkAutoRiskInfoD tkAutoRiskInfoD = new TkAutoRiskInfoD();
        tkAutoRiskInfoD.setRiskCode("riskCode");
        final TkAutoBaseInfoD baseInfoD = new TkAutoBaseInfoD();
        baseInfoD.setSubPolicyNo("subPolicyNo");
        baseInfoD.setRiskName("riskName");
        baseInfoD.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD.setPremium(new BigDecimal("0.00"));
        baseInfoD.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TkAutoPolicyViewList policyViewList = new TkAutoPolicyViewList();
        final TkAutoPolicyData policyData = new TkAutoPolicyData();
        policyData.setPolicyDownLoadUrl("policyDownLoadUrl");
        policyViewList.setPolicyData(policyData);
        baseInfoD.setPolicyViewList(policyViewList);
        tkAutoRiskInfoD.setBaseInfoD(baseInfoD);
        final TkAutoRelatedInfoD tkAutoRelatedInfoD = new TkAutoRelatedInfoD();
        tkAutoRelatedInfoD.setInsureFlag("insureFlag");
        tkAutoRelatedInfoD.setInsureTypeCode("insureTypeCode");
        tkAutoRiskInfoD.setRelatedInfoDList(Arrays.asList(tkAutoRelatedInfoD));
        tkAutoQueryPolicyDetailResp.setRiskInfoList(Arrays.asList(tkAutoRiskInfoD));
        tkAutoQueryPolicyDetailRespTkAutoRespHelper.setData(tkAutoQueryPolicyDetailResp);
        tkAutoQueryPolicyDetailRespTkAutoRespHelper.setContent("content");
        when(mockAdapter.queryTkDetail("policyNo")).thenReturn(tkAutoQueryPolicyDetailRespTkAutoRespHelper);

        // Configure TkAutoCvtHelper.findDrivingTkAutoRiskInfoD(...).
        final TkAutoRiskInfoD tkAutoRiskInfoD1 = new TkAutoRiskInfoD();
        tkAutoRiskInfoD1.setRiskCode("riskCode");
        final TkAutoBaseInfoD baseInfoD1 = new TkAutoBaseInfoD();
        baseInfoD1.setSubPolicyNo("subPolicyNo");
        baseInfoD1.setRiskName("riskName");
        baseInfoD1.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD1.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD1.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD1.setPremium(new BigDecimal("0.00"));
        baseInfoD1.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TkAutoPolicyViewList policyViewList1 = new TkAutoPolicyViewList();
        final TkAutoPolicyData policyData1 = new TkAutoPolicyData();
        policyData1.setPolicyDownLoadUrl("policyDownLoadUrl");
        policyViewList1.setPolicyData(policyData1);
        baseInfoD1.setPolicyViewList(policyViewList1);
        tkAutoRiskInfoD1.setBaseInfoD(baseInfoD1);
        final TkAutoRelatedInfoD tkAutoRelatedInfoD1 = new TkAutoRelatedInfoD();
        tkAutoRelatedInfoD1.setInsureFlag("insureFlag");
        tkAutoRelatedInfoD1.setInsureTypeCode("insureTypeCode");
        tkAutoRiskInfoD1.setRelatedInfoDList(Arrays.asList(tkAutoRelatedInfoD1));
        final TkAutoRiskInfoD tkAutoRiskInfoD2 = new TkAutoRiskInfoD();
        tkAutoRiskInfoD2.setRiskCode("riskCode");
        final TkAutoBaseInfoD baseInfoD2 = new TkAutoBaseInfoD();
        baseInfoD2.setSubPolicyNo("subPolicyNo");
        baseInfoD2.setRiskName("riskName");
        baseInfoD2.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD2.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD2.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD2.setPremium(new BigDecimal("0.00"));
        baseInfoD2.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TkAutoPolicyViewList policyViewList2 = new TkAutoPolicyViewList();
        final TkAutoPolicyData policyData2 = new TkAutoPolicyData();
        policyData2.setPolicyDownLoadUrl("policyDownLoadUrl");
        policyViewList2.setPolicyData(policyData2);
        baseInfoD2.setPolicyViewList(policyViewList2);
        tkAutoRiskInfoD2.setBaseInfoD(baseInfoD2);
        final TkAutoRelatedInfoD tkAutoRelatedInfoD2 = new TkAutoRelatedInfoD();
        tkAutoRelatedInfoD2.setInsureFlag("insureFlag");
        tkAutoRelatedInfoD2.setInsureTypeCode("insureTypeCode");
        tkAutoRiskInfoD2.setRelatedInfoDList(Arrays.asList(tkAutoRelatedInfoD2));
        final List<TkAutoRiskInfoD> tkAutoRiskInfoDList = Arrays.asList(tkAutoRiskInfoD2);
        when(mockCvtHelper.findDrivingTkAutoRiskInfoD(tkAutoRiskInfoDList)).thenReturn(tkAutoRiskInfoD1);

        // Configure TkAutoCvtHelper.getRelatedInfoDList(...).
        final TkAutoRiskInfoD tkAutoRiskInfoD3 = new TkAutoRiskInfoD();
        tkAutoRiskInfoD3.setRiskCode("riskCode");
        final TkAutoBaseInfoD baseInfoD3 = new TkAutoBaseInfoD();
        baseInfoD3.setSubPolicyNo("subPolicyNo");
        baseInfoD3.setRiskName("riskName");
        baseInfoD3.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD3.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD3.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD3.setPremium(new BigDecimal("0.00"));
        baseInfoD3.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TkAutoPolicyViewList policyViewList3 = new TkAutoPolicyViewList();
        final TkAutoPolicyData policyData3 = new TkAutoPolicyData();
        policyData3.setPolicyDownLoadUrl("policyDownLoadUrl");
        policyViewList3.setPolicyData(policyData3);
        baseInfoD3.setPolicyViewList(policyViewList3);
        tkAutoRiskInfoD3.setBaseInfoD(baseInfoD3);
        final TkAutoRelatedInfoD tkAutoRelatedInfoD3 = new TkAutoRelatedInfoD();
        tkAutoRelatedInfoD3.setInsureFlag("insureFlag");
        tkAutoRelatedInfoD3.setInsureTypeCode("insureTypeCode");
        tkAutoRiskInfoD3.setRelatedInfoDList(Arrays.asList(tkAutoRelatedInfoD3));
        final List<TkAutoRiskInfoD> tkAutoRiskInfoDList1 = Arrays.asList(tkAutoRiskInfoD3);
        when(mockCvtHelper.getRelatedInfoDList(tkAutoRiskInfoDList1)).thenReturn(Collections.emptyList());

        // Run the test
        final List<String> result = tkAutoOrderServiceUnderTest.syncPolicyDetail("policyNo");

        // Verify the results
        assertNull(result);

        // Confirm AmPolicyTkMapper.updateByExampleSelective(...).
        final AmPolicyTk record = new AmPolicyTk();
        record.setPolicyNo("policyNo");
        record.setSubPolicyNo("subPolicyNo");
        record.setRiskName("riskName");
        record.setRiskCode("riskCode");
        record.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record.setPolicyUrl("policyDownLoadUrl");
        record.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record.setBenchmarkPremium(new BigDecimal("0.00"));
        record.setState(0);
        record.setRemark("msg");

        // Confirm AmPolicyTkMapper.insertUseGeneratedKeys(...).
        final AmPolicyTk record1 = new AmPolicyTk();
        record1.setPolicyNo("policyNo");
        record1.setSubPolicyNo("subPolicyNo");
        record1.setRiskName("riskName");
        record1.setRiskCode("riskCode");
        record1.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record1.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record1.setPolicyUrl("policyDownLoadUrl");
        record1.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record1.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record1.setBenchmarkPremium(new BigDecimal("0.00"));
        record1.setState(0);
        record1.setRemark("msg");

        // Confirm AmPolicyTkDetailMapper.insertUseGeneratedKeys(...).
        final AmPolicyTkDetail record2 = new AmPolicyTkDetail();
        record2.setPolicyNo("policyNo");
        record2.setContent("content");
    }

    @Test
    public void testSyncPolicyDetail_TkAutoCvtHelperCvtOrderInfoReturnsNoItems() throws Exception {
        // Setup
        // Configure TkAutoServiceAdapter.queryTkDetail(...).
        final TkAutoRespHelper<TkAutoQueryPolicyDetailResp> tkAutoQueryPolicyDetailRespTkAutoRespHelper = new TkAutoRespHelper<>();
        tkAutoQueryPolicyDetailRespTkAutoRespHelper.setAppCode("1");
        tkAutoQueryPolicyDetailRespTkAutoRespHelper.setAppMessage("appMessage");
        final TkAutoQueryPolicyDetailResp tkAutoQueryPolicyDetailResp = new TkAutoQueryPolicyDetailResp();
        final TkAutoRiskInfoD tkAutoRiskInfoD = new TkAutoRiskInfoD();
        tkAutoRiskInfoD.setRiskCode("riskCode");
        final TkAutoBaseInfoD baseInfoD = new TkAutoBaseInfoD();
        baseInfoD.setSubPolicyNo("subPolicyNo");
        baseInfoD.setRiskName("riskName");
        baseInfoD.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD.setPremium(new BigDecimal("0.00"));
        baseInfoD.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TkAutoPolicyViewList policyViewList = new TkAutoPolicyViewList();
        final TkAutoPolicyData policyData = new TkAutoPolicyData();
        policyData.setPolicyDownLoadUrl("policyDownLoadUrl");
        policyViewList.setPolicyData(policyData);
        baseInfoD.setPolicyViewList(policyViewList);
        tkAutoRiskInfoD.setBaseInfoD(baseInfoD);
        final TkAutoRelatedInfoD tkAutoRelatedInfoD = new TkAutoRelatedInfoD();
        tkAutoRelatedInfoD.setInsureFlag("insureFlag");
        tkAutoRelatedInfoD.setInsureTypeCode("insureTypeCode");
        tkAutoRiskInfoD.setRelatedInfoDList(Arrays.asList(tkAutoRelatedInfoD));
        tkAutoQueryPolicyDetailResp.setRiskInfoList(Arrays.asList(tkAutoRiskInfoD));
        tkAutoQueryPolicyDetailRespTkAutoRespHelper.setData(tkAutoQueryPolicyDetailResp);
        tkAutoQueryPolicyDetailRespTkAutoRespHelper.setContent("content");
        when(mockAdapter.queryTkDetail("policyNo")).thenReturn(tkAutoQueryPolicyDetailRespTkAutoRespHelper);

        // Configure TkAutoCvtHelper.findDrivingTkAutoRiskInfoD(...).
        final TkAutoRiskInfoD tkAutoRiskInfoD1 = new TkAutoRiskInfoD();
        tkAutoRiskInfoD1.setRiskCode("riskCode");
        final TkAutoBaseInfoD baseInfoD1 = new TkAutoBaseInfoD();
        baseInfoD1.setSubPolicyNo("subPolicyNo");
        baseInfoD1.setRiskName("riskName");
        baseInfoD1.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD1.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD1.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD1.setPremium(new BigDecimal("0.00"));
        baseInfoD1.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TkAutoPolicyViewList policyViewList1 = new TkAutoPolicyViewList();
        final TkAutoPolicyData policyData1 = new TkAutoPolicyData();
        policyData1.setPolicyDownLoadUrl("policyDownLoadUrl");
        policyViewList1.setPolicyData(policyData1);
        baseInfoD1.setPolicyViewList(policyViewList1);
        tkAutoRiskInfoD1.setBaseInfoD(baseInfoD1);
        final TkAutoRelatedInfoD tkAutoRelatedInfoD1 = new TkAutoRelatedInfoD();
        tkAutoRelatedInfoD1.setInsureFlag("insureFlag");
        tkAutoRelatedInfoD1.setInsureTypeCode("insureTypeCode");
        tkAutoRiskInfoD1.setRelatedInfoDList(Arrays.asList(tkAutoRelatedInfoD1));
        final TkAutoRiskInfoD tkAutoRiskInfoD2 = new TkAutoRiskInfoD();
        tkAutoRiskInfoD2.setRiskCode("riskCode");
        final TkAutoBaseInfoD baseInfoD2 = new TkAutoBaseInfoD();
        baseInfoD2.setSubPolicyNo("subPolicyNo");
        baseInfoD2.setRiskName("riskName");
        baseInfoD2.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD2.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD2.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD2.setPremium(new BigDecimal("0.00"));
        baseInfoD2.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TkAutoPolicyViewList policyViewList2 = new TkAutoPolicyViewList();
        final TkAutoPolicyData policyData2 = new TkAutoPolicyData();
        policyData2.setPolicyDownLoadUrl("policyDownLoadUrl");
        policyViewList2.setPolicyData(policyData2);
        baseInfoD2.setPolicyViewList(policyViewList2);
        tkAutoRiskInfoD2.setBaseInfoD(baseInfoD2);
        final TkAutoRelatedInfoD tkAutoRelatedInfoD2 = new TkAutoRelatedInfoD();
        tkAutoRelatedInfoD2.setInsureFlag("insureFlag");
        tkAutoRelatedInfoD2.setInsureTypeCode("insureTypeCode");
        tkAutoRiskInfoD2.setRelatedInfoDList(Arrays.asList(tkAutoRelatedInfoD2));
        final List<TkAutoRiskInfoD> tkAutoRiskInfoDList = Arrays.asList(tkAutoRiskInfoD2);
        when(mockCvtHelper.findDrivingTkAutoRiskInfoD(tkAutoRiskInfoDList)).thenReturn(tkAutoRiskInfoD1);

        // Configure TkAutoCvtHelper.getRelatedInfoDList(...).
        final TkAutoRelatedInfoD tkAutoRelatedInfoD3 = new TkAutoRelatedInfoD();
        tkAutoRelatedInfoD3.setInsuredName("insuredName");
        tkAutoRelatedInfoD3.setInsureFlag("insureFlag");
        tkAutoRelatedInfoD3.setInsureTypeCode("insureTypeCode");
        tkAutoRelatedInfoD3.setInsureType("insureType");
        tkAutoRelatedInfoD3.setCertiType("certiType");
        final List<TkAutoRelatedInfoD> tkAutoRelatedInfoDS = Arrays.asList(tkAutoRelatedInfoD3);
        final TkAutoRiskInfoD tkAutoRiskInfoD3 = new TkAutoRiskInfoD();
        tkAutoRiskInfoD3.setRiskCode("riskCode");
        final TkAutoBaseInfoD baseInfoD3 = new TkAutoBaseInfoD();
        baseInfoD3.setSubPolicyNo("subPolicyNo");
        baseInfoD3.setRiskName("riskName");
        baseInfoD3.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD3.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD3.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD3.setPremium(new BigDecimal("0.00"));
        baseInfoD3.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TkAutoPolicyViewList policyViewList3 = new TkAutoPolicyViewList();
        final TkAutoPolicyData policyData3 = new TkAutoPolicyData();
        policyData3.setPolicyDownLoadUrl("policyDownLoadUrl");
        policyViewList3.setPolicyData(policyData3);
        baseInfoD3.setPolicyViewList(policyViewList3);
        tkAutoRiskInfoD3.setBaseInfoD(baseInfoD3);
        final TkAutoRelatedInfoD tkAutoRelatedInfoD4 = new TkAutoRelatedInfoD();
        tkAutoRelatedInfoD4.setInsureFlag("insureFlag");
        tkAutoRelatedInfoD4.setInsureTypeCode("insureTypeCode");
        tkAutoRiskInfoD3.setRelatedInfoDList(Arrays.asList(tkAutoRelatedInfoD4));
        final List<TkAutoRiskInfoD> tkAutoRiskInfoDList1 = Arrays.asList(tkAutoRiskInfoD3);
        when(mockCvtHelper.getRelatedInfoDList(tkAutoRiskInfoDList1)).thenReturn(tkAutoRelatedInfoDS);

        // Configure AmPolicyTkMapper.selectOneByPolicyNo(...).
        final AmPolicyTk amPolicyTk = new AmPolicyTk();
        amPolicyTk.setPolicyNo("policyNo");
        amPolicyTk.setSubPolicyNo("subPolicyNo");
        amPolicyTk.setRiskName("riskName");
        amPolicyTk.setRiskCode("riskCode");
        amPolicyTk.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk.setPolicyUrl("policyDownLoadUrl");
        amPolicyTk.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk.setBenchmarkPremium(new BigDecimal("0.00"));
        amPolicyTk.setState(0);
        amPolicyTk.setRemark("msg");
        when(mockPolicyTkMapper.selectOneByPolicyNo("policyNo")).thenReturn(amPolicyTk);

        // Configure TkAutoCvtHelper.cvtOrderInfo(...).
        final AmPolicyTk tk = new AmPolicyTk();
        tk.setPolicyNo("policyNo");
        tk.setSubPolicyNo("subPolicyNo");
        tk.setRiskName("riskName");
        tk.setRiskCode("riskCode");
        tk.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        tk.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        tk.setPolicyUrl("policyDownLoadUrl");
        tk.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        tk.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        tk.setBenchmarkPremium(new BigDecimal("0.00"));
        tk.setState(0);
        tk.setRemark("msg");
        final TkAutoQueryPolicyDetailResp detailResp = new TkAutoQueryPolicyDetailResp();
        final TkAutoRiskInfoD tkAutoRiskInfoD4 = new TkAutoRiskInfoD();
        tkAutoRiskInfoD4.setRiskCode("riskCode");
        final TkAutoBaseInfoD baseInfoD4 = new TkAutoBaseInfoD();
        baseInfoD4.setSubPolicyNo("subPolicyNo");
        baseInfoD4.setRiskName("riskName");
        baseInfoD4.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD4.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD4.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD4.setPremium(new BigDecimal("0.00"));
        baseInfoD4.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TkAutoPolicyViewList policyViewList4 = new TkAutoPolicyViewList();
        final TkAutoPolicyData policyData4 = new TkAutoPolicyData();
        policyData4.setPolicyDownLoadUrl("policyDownLoadUrl");
        policyViewList4.setPolicyData(policyData4);
        baseInfoD4.setPolicyViewList(policyViewList4);
        tkAutoRiskInfoD4.setBaseInfoD(baseInfoD4);
        final TkAutoRelatedInfoD tkAutoRelatedInfoD5 = new TkAutoRelatedInfoD();
        tkAutoRelatedInfoD5.setInsureFlag("insureFlag");
        tkAutoRelatedInfoD5.setInsureTypeCode("insureTypeCode");
        tkAutoRiskInfoD4.setRelatedInfoDList(Arrays.asList(tkAutoRelatedInfoD5));
        detailResp.setRiskInfoList(Arrays.asList(tkAutoRiskInfoD4));
        when(mockCvtHelper.cvtOrderInfo(tk, detailResp, new HashMap<>())).thenReturn(Collections.emptyList());

        // Configure TkAutoCvtHelper.cvtAutoInfoList(...).
        final AutoOrderDTO autoOrderDTO = new AutoOrderDTO();
        autoOrderDTO.setOrderNo("orderNo");
        final AutoOrder order = new AutoOrder();
        order.setPayStatus("-1");
        order.setPaymentTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        order.setCommissionId(0);
        autoOrderDTO.setOrder(order);
        final AutoOrderPerson autoOrderPerson = new AutoOrderPerson();
        autoOrderDTO.setPeople(Arrays.asList(autoOrderPerson));
        final AutoOrderPolicyDTO autoOrderPolicyDTO = new AutoOrderPolicyDTO();
        final AutoOrderPolicy policy = new AutoOrderPolicy();
        policy.setPolicyNo("policyNo");
        policy.setSubPolicyNo("subPolicyNo");
        policy.setPolicyState("-2");
        policy.setBusinessScore("businessScore");
        autoOrderPolicyDTO.setPolicy(policy);
        final AutoOrderRisk autoOrderRisk = new AutoOrderRisk();
        autoOrderRisk.setRiskName("riskName");
        autoOrderRisk.setPremium(new BigDecimal("0.00"));
        autoOrderRisk.setAmount(new BigDecimal("0.00"));
        autoOrderPolicyDTO.setRiskList(Arrays.asList(autoOrderRisk));
        autoOrderDTO.setPolicies(Arrays.asList(autoOrderPolicyDTO));
        final AutoOrderExtend extend = new AutoOrderExtend();
        autoOrderDTO.setExtend(extend);
        final AutoOrderCar car = new AutoOrderCar();
        car.setOwnerPersonName("ownerPersonName");
        car.setOwnerPersonGender("ownerPersonGender");
        car.setOwnerIdType("ownerIdType");
        car.setOwnerIdNumber("ownerIdNumber");
        car.setFirstRegDate("firstRegDate");
        car.setFullLoad("fullLoad");
        car.setTransferDate("transferDate");
        car.setSeatCnt("approvedNum");
        car.setPrice("price");
        car.setCarUserType("carUserType");
        car.setDisplacement("displacement");
        car.setIsNewCar("isNewCar");
        autoOrderDTO.setCar(car);
        final List<AutoOrderDTO> autoOrderDTOS = Arrays.asList(autoOrderDTO);
        final AmPolicyTk tk1 = new AmPolicyTk();
        tk1.setPolicyNo("policyNo");
        tk1.setSubPolicyNo("subPolicyNo");
        tk1.setRiskName("riskName");
        tk1.setRiskCode("riskCode");
        tk1.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        tk1.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        tk1.setPolicyUrl("policyDownLoadUrl");
        tk1.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        tk1.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        tk1.setBenchmarkPremium(new BigDecimal("0.00"));
        tk1.setState(0);
        tk1.setRemark("msg");
        final TkAutoQueryPolicyDetailResp detailResp1 = new TkAutoQueryPolicyDetailResp();
        final TkAutoRiskInfoD tkAutoRiskInfoD5 = new TkAutoRiskInfoD();
        tkAutoRiskInfoD5.setRiskCode("riskCode");
        final TkAutoBaseInfoD baseInfoD5 = new TkAutoBaseInfoD();
        baseInfoD5.setSubPolicyNo("subPolicyNo");
        baseInfoD5.setRiskName("riskName");
        baseInfoD5.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD5.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD5.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD5.setPremium(new BigDecimal("0.00"));
        baseInfoD5.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TkAutoPolicyViewList policyViewList5 = new TkAutoPolicyViewList();
        final TkAutoPolicyData policyData5 = new TkAutoPolicyData();
        policyData5.setPolicyDownLoadUrl("policyDownLoadUrl");
        policyViewList5.setPolicyData(policyData5);
        baseInfoD5.setPolicyViewList(policyViewList5);
        tkAutoRiskInfoD5.setBaseInfoD(baseInfoD5);
        final TkAutoRelatedInfoD tkAutoRelatedInfoD6 = new TkAutoRelatedInfoD();
        tkAutoRelatedInfoD6.setInsureFlag("insureFlag");
        tkAutoRelatedInfoD6.setInsureTypeCode("insureTypeCode");
        tkAutoRiskInfoD5.setRelatedInfoDList(Arrays.asList(tkAutoRelatedInfoD6));
        detailResp1.setRiskInfoList(Arrays.asList(tkAutoRiskInfoD5));
        when(mockCvtHelper.cvtAutoInfoList(tk1, Arrays.asList("value"), detailResp1, new HashMap<>())).thenReturn(autoOrderDTOS);

        // Run the test
        final List<String> result = tkAutoOrderServiceUnderTest.syncPolicyDetail("policyNo");

        // Verify the results

        // Confirm AmPolicyTkMapper.updateByExampleSelective(...).
        final AmPolicyTk record = new AmPolicyTk();
        record.setPolicyNo("policyNo");
        record.setSubPolicyNo("subPolicyNo");
        record.setRiskName("riskName");
        record.setRiskCode("riskCode");
        record.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record.setPolicyUrl("policyDownLoadUrl");
        record.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record.setBenchmarkPremium(new BigDecimal("0.00"));
        record.setState(0);
        record.setRemark("msg");

        // Confirm AmPolicyTkMapper.insertUseGeneratedKeys(...).
        final AmPolicyTk record1 = new AmPolicyTk();
        record1.setPolicyNo("policyNo");
        record1.setSubPolicyNo("subPolicyNo");
        record1.setRiskName("riskName");
        record1.setRiskCode("riskCode");
        record1.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record1.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record1.setPolicyUrl("policyDownLoadUrl");
        record1.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record1.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record1.setBenchmarkPremium(new BigDecimal("0.00"));
        record1.setState(0);
        record1.setRemark("msg");

        // Confirm AmPolicyTkDetailMapper.insertUseGeneratedKeys(...).
        final AmPolicyTkDetail record2 = new AmPolicyTkDetail();
        record2.setPolicyNo("policyNo");
        record2.setContent("content");
    }

    @Test
    public void testSyncPolicyDetail_TkAutoCvtHelperCvtAutoInfoListReturnsNoItems() throws Exception {
        // Setup
        // Configure TkAutoServiceAdapter.queryTkDetail(...).
        final TkAutoRespHelper<TkAutoQueryPolicyDetailResp> tkAutoQueryPolicyDetailRespTkAutoRespHelper = new TkAutoRespHelper<>();
        tkAutoQueryPolicyDetailRespTkAutoRespHelper.setAppCode("1");
        tkAutoQueryPolicyDetailRespTkAutoRespHelper.setAppMessage("appMessage");
        final TkAutoQueryPolicyDetailResp tkAutoQueryPolicyDetailResp = new TkAutoQueryPolicyDetailResp();
        final TkAutoRiskInfoD tkAutoRiskInfoD = new TkAutoRiskInfoD();
        tkAutoRiskInfoD.setRiskCode("riskCode");
        final TkAutoBaseInfoD baseInfoD = new TkAutoBaseInfoD();
        baseInfoD.setSubPolicyNo("subPolicyNo");
        baseInfoD.setRiskName("riskName");
        baseInfoD.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD.setPremium(new BigDecimal("0.00"));
        baseInfoD.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TkAutoPolicyViewList policyViewList = new TkAutoPolicyViewList();
        final TkAutoPolicyData policyData = new TkAutoPolicyData();
        policyData.setPolicyDownLoadUrl("policyDownLoadUrl");
        policyViewList.setPolicyData(policyData);
        baseInfoD.setPolicyViewList(policyViewList);
        tkAutoRiskInfoD.setBaseInfoD(baseInfoD);
        final TkAutoRelatedInfoD tkAutoRelatedInfoD = new TkAutoRelatedInfoD();
        tkAutoRelatedInfoD.setInsureFlag("insureFlag");
        tkAutoRelatedInfoD.setInsureTypeCode("insureTypeCode");
        tkAutoRiskInfoD.setRelatedInfoDList(Arrays.asList(tkAutoRelatedInfoD));
        tkAutoQueryPolicyDetailResp.setRiskInfoList(Arrays.asList(tkAutoRiskInfoD));
        tkAutoQueryPolicyDetailRespTkAutoRespHelper.setData(tkAutoQueryPolicyDetailResp);
        tkAutoQueryPolicyDetailRespTkAutoRespHelper.setContent("content");
        when(mockAdapter.queryTkDetail("policyNo")).thenReturn(tkAutoQueryPolicyDetailRespTkAutoRespHelper);

        // Configure TkAutoCvtHelper.findDrivingTkAutoRiskInfoD(...).
        final TkAutoRiskInfoD tkAutoRiskInfoD1 = new TkAutoRiskInfoD();
        tkAutoRiskInfoD1.setRiskCode("riskCode");
        final TkAutoBaseInfoD baseInfoD1 = new TkAutoBaseInfoD();
        baseInfoD1.setSubPolicyNo("subPolicyNo");
        baseInfoD1.setRiskName("riskName");
        baseInfoD1.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD1.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD1.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD1.setPremium(new BigDecimal("0.00"));
        baseInfoD1.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TkAutoPolicyViewList policyViewList1 = new TkAutoPolicyViewList();
        final TkAutoPolicyData policyData1 = new TkAutoPolicyData();
        policyData1.setPolicyDownLoadUrl("policyDownLoadUrl");
        policyViewList1.setPolicyData(policyData1);
        baseInfoD1.setPolicyViewList(policyViewList1);
        tkAutoRiskInfoD1.setBaseInfoD(baseInfoD1);
        final TkAutoRelatedInfoD tkAutoRelatedInfoD1 = new TkAutoRelatedInfoD();
        tkAutoRelatedInfoD1.setInsureFlag("insureFlag");
        tkAutoRelatedInfoD1.setInsureTypeCode("insureTypeCode");
        tkAutoRiskInfoD1.setRelatedInfoDList(Arrays.asList(tkAutoRelatedInfoD1));
        final TkAutoRiskInfoD tkAutoRiskInfoD2 = new TkAutoRiskInfoD();
        tkAutoRiskInfoD2.setRiskCode("riskCode");
        final TkAutoBaseInfoD baseInfoD2 = new TkAutoBaseInfoD();
        baseInfoD2.setSubPolicyNo("subPolicyNo");
        baseInfoD2.setRiskName("riskName");
        baseInfoD2.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD2.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD2.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD2.setPremium(new BigDecimal("0.00"));
        baseInfoD2.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TkAutoPolicyViewList policyViewList2 = new TkAutoPolicyViewList();
        final TkAutoPolicyData policyData2 = new TkAutoPolicyData();
        policyData2.setPolicyDownLoadUrl("policyDownLoadUrl");
        policyViewList2.setPolicyData(policyData2);
        baseInfoD2.setPolicyViewList(policyViewList2);
        tkAutoRiskInfoD2.setBaseInfoD(baseInfoD2);
        final TkAutoRelatedInfoD tkAutoRelatedInfoD2 = new TkAutoRelatedInfoD();
        tkAutoRelatedInfoD2.setInsureFlag("insureFlag");
        tkAutoRelatedInfoD2.setInsureTypeCode("insureTypeCode");
        tkAutoRiskInfoD2.setRelatedInfoDList(Arrays.asList(tkAutoRelatedInfoD2));
        final List<TkAutoRiskInfoD> tkAutoRiskInfoDList = Arrays.asList(tkAutoRiskInfoD2);
        when(mockCvtHelper.findDrivingTkAutoRiskInfoD(tkAutoRiskInfoDList)).thenReturn(tkAutoRiskInfoD1);

        // Configure TkAutoCvtHelper.getRelatedInfoDList(...).
        final TkAutoRelatedInfoD tkAutoRelatedInfoD3 = new TkAutoRelatedInfoD();
        tkAutoRelatedInfoD3.setInsuredName("insuredName");
        tkAutoRelatedInfoD3.setInsureFlag("insureFlag");
        tkAutoRelatedInfoD3.setInsureTypeCode("insureTypeCode");
        tkAutoRelatedInfoD3.setInsureType("insureType");
        tkAutoRelatedInfoD3.setCertiType("certiType");
        final List<TkAutoRelatedInfoD> tkAutoRelatedInfoDS = Arrays.asList(tkAutoRelatedInfoD3);
        final TkAutoRiskInfoD tkAutoRiskInfoD3 = new TkAutoRiskInfoD();
        tkAutoRiskInfoD3.setRiskCode("riskCode");
        final TkAutoBaseInfoD baseInfoD3 = new TkAutoBaseInfoD();
        baseInfoD3.setSubPolicyNo("subPolicyNo");
        baseInfoD3.setRiskName("riskName");
        baseInfoD3.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD3.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD3.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD3.setPremium(new BigDecimal("0.00"));
        baseInfoD3.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TkAutoPolicyViewList policyViewList3 = new TkAutoPolicyViewList();
        final TkAutoPolicyData policyData3 = new TkAutoPolicyData();
        policyData3.setPolicyDownLoadUrl("policyDownLoadUrl");
        policyViewList3.setPolicyData(policyData3);
        baseInfoD3.setPolicyViewList(policyViewList3);
        tkAutoRiskInfoD3.setBaseInfoD(baseInfoD3);
        final TkAutoRelatedInfoD tkAutoRelatedInfoD4 = new TkAutoRelatedInfoD();
        tkAutoRelatedInfoD4.setInsureFlag("insureFlag");
        tkAutoRelatedInfoD4.setInsureTypeCode("insureTypeCode");
        tkAutoRiskInfoD3.setRelatedInfoDList(Arrays.asList(tkAutoRelatedInfoD4));
        final List<TkAutoRiskInfoD> tkAutoRiskInfoDList1 = Arrays.asList(tkAutoRiskInfoD3);
        when(mockCvtHelper.getRelatedInfoDList(tkAutoRiskInfoDList1)).thenReturn(tkAutoRelatedInfoDS);

        // Configure AmPolicyTkMapper.selectOneByPolicyNo(...).
        final AmPolicyTk amPolicyTk = new AmPolicyTk();
        amPolicyTk.setPolicyNo("policyNo");
        amPolicyTk.setSubPolicyNo("subPolicyNo");
        amPolicyTk.setRiskName("riskName");
        amPolicyTk.setRiskCode("riskCode");
        amPolicyTk.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk.setPolicyUrl("policyDownLoadUrl");
        amPolicyTk.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk.setBenchmarkPremium(new BigDecimal("0.00"));
        amPolicyTk.setState(0);
        amPolicyTk.setRemark("msg");
        when(mockPolicyTkMapper.selectOneByPolicyNo("policyNo")).thenReturn(amPolicyTk);

        // Configure TkAutoCvtHelper.cvtOrderInfo(...).
        final OrderSubmitRequest orderSubmitRequest = new OrderSubmitRequest();
        final FhProduct productInfo = new FhProduct();
        productInfo.setProductId("fhProductId");
        productInfo.setRecommendId("recommendId");
        productInfo.setRecommendMainJobNumber("recommendMainJobNumber");
        productInfo.setRecommendOrgCode("recommendOrgCode");
        orderSubmitRequest.setProductInfo(productInfo);
        final FhOrderInfo orderInfo = new FhOrderInfo();
        orderInfo.setSubmitTime("submitTime");
        orderInfo.setStartTime("startTime");
        orderInfo.setEndTime("endTime");
        orderSubmitRequest.setOrderInfo(orderInfo);
        final FhProposer proposerInfo = new FhProposer();
        proposerInfo.setPersonName("insuredName");
        proposerInfo.setPersonGender("insuredPersonGender");
        proposerInfo.setIdType("idType");
        proposerInfo.setIdNumber("idNumber");
        proposerInfo.setBirthday("birthday");
        proposerInfo.setCellPhone("cellPhone");
        proposerInfo.setEmail("email");
        proposerInfo.setAddress("address");
        proposerInfo.setAppStatus("-2");
        proposerInfo.setPolicyNo("insIdNumber");
        proposerInfo.setDownloadURL("downloadURL");
        orderSubmitRequest.setProposerInfo(proposerInfo);
        final FhInsuredPerson fhInsuredPerson = new FhInsuredPerson();
        fhInsuredPerson.setPersonName("insuredName");
        fhInsuredPerson.setIdType("idType");
        fhInsuredPerson.setIdNumber("idNumber");
        fhInsuredPerson.setBirthday("birthday");
        fhInsuredPerson.setCellPhone("cellPhone");
        fhInsuredPerson.setAppStatus("-2");
        fhInsuredPerson.setPolicyNo("insIdNumber");
        fhInsuredPerson.setRelationship("99");
        fhInsuredPerson.setFlightNo("flightNo");
        fhInsuredPerson.setFlightTime("flightTime");
        fhInsuredPerson.setOccupationCode("");
        fhInsuredPerson.setOccupationGroup("occupationGroup");
        fhInsuredPerson.setDestinationCountryText("destinationCountryText");
        fhInsuredPerson.setAnnualIncome("annualIncome");
        fhInsuredPerson.setStudentType("studentType");
        fhInsuredPerson.setSchoolType("schoolType");
        fhInsuredPerson.setSchoolNature("schoolNature");
        fhInsuredPerson.setSchoolName("schoolName");
        fhInsuredPerson.setSchoolClass("schoolClass");
        fhInsuredPerson.setStudentId("studentId");
        fhInsuredPerson.setIdPeriodStart("idPeriodStart");
        fhInsuredPerson.setIdPeriodEnd("idPeriodEnd");
        fhInsuredPerson.setSmoke("smoke");
        fhInsuredPerson.setOldPolicyNo("oldPolicyNo");
        final TestPremiumDutyForm testPremiumDutyForm = new TestPremiumDutyForm();
        fhInsuredPerson.setDuties(Arrays.asList(testPremiumDutyForm));
        final TestPremiumProductForm product = new TestPremiumProductForm();
        product.setProductId(0);
        product.setPlanId(0);
        product.setPlanCode("planCode");
        product.setPremium(new BigDecimal("0.00"));
        product.setQty(0);
        final TestPremiumDutyForm testPremiumDutyForm1 = new TestPremiumDutyForm();
        product.setPremiumDutyForms(Arrays.asList(testPremiumDutyForm1));
        fhInsuredPerson.setProduct(product);
        orderSubmitRequest.setInsuredPerson(Arrays.asList(fhInsuredPerson));
        final FhProperty propertyInfo = new FhProperty();
        propertyInfo.setPropertyInfoIsExist("0");
        orderSubmitRequest.setPropertyInfo(propertyInfo);
        final SmOrderHouseDTO house = new SmOrderHouseDTO();
        orderSubmitRequest.setHouse(house);
        final SmOrderCarInfoDTO carInfo = new SmOrderCarInfoDTO();
        orderSubmitRequest.setCarInfo(carInfo);
        orderSubmitRequest.setProductType("code");
        orderSubmitRequest.setBizCode("bizCode");
        orderSubmitRequest.setOrderOutType("orderOutType");
        orderSubmitRequest.setToken("token");
        orderSubmitRequest.setPlanId(0);
        orderSubmitRequest.setQty(0);
        orderSubmitRequest.setPreOrderId("preOrderId");
        orderSubmitRequest.setRenew(false);
        orderSubmitRequest.setRealRenewFlag(false);
        orderSubmitRequest.setAppNo("appNo");
        orderSubmitRequest.setAgentId(0);
        orderSubmitRequest.setJobCode("jobCode");
        final List<SmCreateOrderSubmitRequest> smCreateOrderSubmitRequests = Arrays.asList(new SmCreateOrderSubmitRequest(orderSubmitRequest));
        final AmPolicyTk tk = new AmPolicyTk();
        tk.setPolicyNo("policyNo");
        tk.setSubPolicyNo("subPolicyNo");
        tk.setRiskName("riskName");
        tk.setRiskCode("riskCode");
        tk.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        tk.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        tk.setPolicyUrl("policyDownLoadUrl");
        tk.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        tk.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        tk.setBenchmarkPremium(new BigDecimal("0.00"));
        tk.setState(0);
        tk.setRemark("msg");
        final TkAutoQueryPolicyDetailResp detailResp = new TkAutoQueryPolicyDetailResp();
        final TkAutoRiskInfoD tkAutoRiskInfoD4 = new TkAutoRiskInfoD();
        tkAutoRiskInfoD4.setRiskCode("riskCode");
        final TkAutoBaseInfoD baseInfoD4 = new TkAutoBaseInfoD();
        baseInfoD4.setSubPolicyNo("subPolicyNo");
        baseInfoD4.setRiskName("riskName");
        baseInfoD4.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD4.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD4.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD4.setPremium(new BigDecimal("0.00"));
        baseInfoD4.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TkAutoPolicyViewList policyViewList4 = new TkAutoPolicyViewList();
        final TkAutoPolicyData policyData4 = new TkAutoPolicyData();
        policyData4.setPolicyDownLoadUrl("policyDownLoadUrl");
        policyViewList4.setPolicyData(policyData4);
        baseInfoD4.setPolicyViewList(policyViewList4);
        tkAutoRiskInfoD4.setBaseInfoD(baseInfoD4);
        final TkAutoRelatedInfoD tkAutoRelatedInfoD5 = new TkAutoRelatedInfoD();
        tkAutoRelatedInfoD5.setInsureFlag("insureFlag");
        tkAutoRelatedInfoD5.setInsureTypeCode("insureTypeCode");
        tkAutoRiskInfoD4.setRelatedInfoDList(Arrays.asList(tkAutoRelatedInfoD5));
        detailResp.setRiskInfoList(Arrays.asList(tkAutoRiskInfoD4));
        when(mockCvtHelper.cvtOrderInfo(tk, detailResp, new HashMap<>())).thenReturn(smCreateOrderSubmitRequests);

        // Configure TkAutoCvtHelper.cvtAutoInfoList(...).
        final AmPolicyTk tk1 = new AmPolicyTk();
        tk1.setPolicyNo("policyNo");
        tk1.setSubPolicyNo("subPolicyNo");
        tk1.setRiskName("riskName");
        tk1.setRiskCode("riskCode");
        tk1.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        tk1.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        tk1.setPolicyUrl("policyDownLoadUrl");
        tk1.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        tk1.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        tk1.setBenchmarkPremium(new BigDecimal("0.00"));
        tk1.setState(0);
        tk1.setRemark("msg");
        final TkAutoQueryPolicyDetailResp detailResp1 = new TkAutoQueryPolicyDetailResp();
        final TkAutoRiskInfoD tkAutoRiskInfoD5 = new TkAutoRiskInfoD();
        tkAutoRiskInfoD5.setRiskCode("riskCode");
        final TkAutoBaseInfoD baseInfoD5 = new TkAutoBaseInfoD();
        baseInfoD5.setSubPolicyNo("subPolicyNo");
        baseInfoD5.setRiskName("riskName");
        baseInfoD5.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD5.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD5.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD5.setPremium(new BigDecimal("0.00"));
        baseInfoD5.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TkAutoPolicyViewList policyViewList5 = new TkAutoPolicyViewList();
        final TkAutoPolicyData policyData5 = new TkAutoPolicyData();
        policyData5.setPolicyDownLoadUrl("policyDownLoadUrl");
        policyViewList5.setPolicyData(policyData5);
        baseInfoD5.setPolicyViewList(policyViewList5);
        tkAutoRiskInfoD5.setBaseInfoD(baseInfoD5);
        final TkAutoRelatedInfoD tkAutoRelatedInfoD6 = new TkAutoRelatedInfoD();
        tkAutoRelatedInfoD6.setInsureFlag("insureFlag");
        tkAutoRelatedInfoD6.setInsureTypeCode("insureTypeCode");
        tkAutoRiskInfoD5.setRelatedInfoDList(Arrays.asList(tkAutoRelatedInfoD6));
        detailResp1.setRiskInfoList(Arrays.asList(tkAutoRiskInfoD5));
        when(mockCvtHelper.cvtAutoInfoList(tk1, Arrays.asList("value"), detailResp1, new HashMap<>())).thenReturn(Collections.emptyList());

        when(mockOrderMapper.updateOrderPaymentTime("orderId")).thenReturn(0);

        // Run the test
        final List<String> result = tkAutoOrderServiceUnderTest.syncPolicyDetail("policyNo");

        // Verify the results

        // Confirm AmPolicyTkMapper.updateByExampleSelective(...).
        final AmPolicyTk record = new AmPolicyTk();
        record.setPolicyNo("policyNo");
        record.setSubPolicyNo("subPolicyNo");
        record.setRiskName("riskName");
        record.setRiskCode("riskCode");
        record.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record.setPolicyUrl("policyDownLoadUrl");
        record.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record.setBenchmarkPremium(new BigDecimal("0.00"));
        record.setState(0);
        record.setRemark("msg");

        // Confirm AmPolicyTkMapper.insertUseGeneratedKeys(...).
        final AmPolicyTk record1 = new AmPolicyTk();
        record1.setPolicyNo("policyNo");
        record1.setSubPolicyNo("subPolicyNo");
        record1.setRiskName("riskName");
        record1.setRiskCode("riskCode");
        record1.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record1.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record1.setPolicyUrl("policyDownLoadUrl");
        record1.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record1.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record1.setBenchmarkPremium(new BigDecimal("0.00"));
        record1.setState(0);
        record1.setRemark("msg");

        // Confirm AmPolicyTkDetailMapper.insertUseGeneratedKeys(...).
        final AmPolicyTkDetail record2 = new AmPolicyTkDetail();
        record2.setPolicyNo("policyNo");
        record2.setContent("content");

        // Confirm SmOrderMapper.insertOrder(...).
        final OrderSubmitRequest orderSubmitRequest1 = new OrderSubmitRequest();
        final FhProduct productInfo1 = new FhProduct();
        productInfo1.setProductId("fhProductId");
        productInfo1.setRecommendId("recommendId");
        productInfo1.setRecommendMainJobNumber("recommendMainJobNumber");
        productInfo1.setRecommendOrgCode("recommendOrgCode");
        orderSubmitRequest1.setProductInfo(productInfo1);
        final FhOrderInfo orderInfo1 = new FhOrderInfo();
        orderInfo1.setSubmitTime("submitTime");
        orderInfo1.setStartTime("startTime");
        orderInfo1.setEndTime("endTime");
        orderSubmitRequest1.setOrderInfo(orderInfo1);
        final FhProposer proposerInfo1 = new FhProposer();
        proposerInfo1.setPersonName("insuredName");
        proposerInfo1.setPersonGender("insuredPersonGender");
        proposerInfo1.setIdType("idType");
        proposerInfo1.setIdNumber("idNumber");
        proposerInfo1.setBirthday("birthday");
        proposerInfo1.setCellPhone("cellPhone");
        proposerInfo1.setEmail("email");
        proposerInfo1.setAddress("address");
        proposerInfo1.setAppStatus("-2");
        proposerInfo1.setPolicyNo("insIdNumber");
        proposerInfo1.setDownloadURL("downloadURL");
        orderSubmitRequest1.setProposerInfo(proposerInfo1);
        final FhInsuredPerson fhInsuredPerson1 = new FhInsuredPerson();
        fhInsuredPerson1.setPersonName("insuredName");
        fhInsuredPerson1.setIdType("idType");
        fhInsuredPerson1.setIdNumber("idNumber");
        fhInsuredPerson1.setBirthday("birthday");
        fhInsuredPerson1.setCellPhone("cellPhone");
        fhInsuredPerson1.setAppStatus("-2");
        fhInsuredPerson1.setPolicyNo("insIdNumber");
        fhInsuredPerson1.setRelationship("99");
        fhInsuredPerson1.setFlightNo("flightNo");
        fhInsuredPerson1.setFlightTime("flightTime");
        fhInsuredPerson1.setOccupationCode("");
        fhInsuredPerson1.setOccupationGroup("occupationGroup");
        fhInsuredPerson1.setDestinationCountryText("destinationCountryText");
        fhInsuredPerson1.setAnnualIncome("annualIncome");
        fhInsuredPerson1.setStudentType("studentType");
        fhInsuredPerson1.setSchoolType("schoolType");
        fhInsuredPerson1.setSchoolNature("schoolNature");
        fhInsuredPerson1.setSchoolName("schoolName");
        fhInsuredPerson1.setSchoolClass("schoolClass");
        fhInsuredPerson1.setStudentId("studentId");
        fhInsuredPerson1.setIdPeriodStart("idPeriodStart");
        fhInsuredPerson1.setIdPeriodEnd("idPeriodEnd");
        fhInsuredPerson1.setSmoke("smoke");
        fhInsuredPerson1.setOldPolicyNo("oldPolicyNo");
        final TestPremiumDutyForm testPremiumDutyForm2 = new TestPremiumDutyForm();
        fhInsuredPerson1.setDuties(Arrays.asList(testPremiumDutyForm2));
        final TestPremiumProductForm product1 = new TestPremiumProductForm();
        product1.setProductId(0);
        product1.setPlanId(0);
        product1.setPlanCode("planCode");
        product1.setPremium(new BigDecimal("0.00"));
        product1.setQty(0);
        final TestPremiumDutyForm testPremiumDutyForm3 = new TestPremiumDutyForm();
        product1.setPremiumDutyForms(Arrays.asList(testPremiumDutyForm3));
        fhInsuredPerson1.setProduct(product1);
        orderSubmitRequest1.setInsuredPerson(Arrays.asList(fhInsuredPerson1));
        final FhProperty propertyInfo1 = new FhProperty();
        propertyInfo1.setPropertyInfoIsExist("0");
        orderSubmitRequest1.setPropertyInfo(propertyInfo1);
        final SmOrderHouseDTO house1 = new SmOrderHouseDTO();
        orderSubmitRequest1.setHouse(house1);
        final SmOrderCarInfoDTO carInfo1 = new SmOrderCarInfoDTO();
        orderSubmitRequest1.setCarInfo(carInfo1);
        orderSubmitRequest1.setProductType("code");
        orderSubmitRequest1.setBizCode("bizCode");
        orderSubmitRequest1.setOrderOutType("orderOutType");
        orderSubmitRequest1.setToken("token");
        orderSubmitRequest1.setPlanId(0);
        orderSubmitRequest1.setQty(0);
        orderSubmitRequest1.setPreOrderId("preOrderId");
        orderSubmitRequest1.setRenew(false);
        orderSubmitRequest1.setRealRenewFlag(false);
        orderSubmitRequest1.setAppNo("appNo");
        orderSubmitRequest1.setAgentId(0);
        orderSubmitRequest1.setJobCode("jobCode");
        final SmCreateOrderSubmitRequest dto = new SmCreateOrderSubmitRequest(orderSubmitRequest1);

        // Confirm SmOrderMapper.insertOrderApplicant(...).
        final OrderSubmitRequest orderSubmitRequest2 = new OrderSubmitRequest();
        final FhProduct productInfo2 = new FhProduct();
        productInfo2.setProductId("fhProductId");
        productInfo2.setRecommendId("recommendId");
        productInfo2.setRecommendMainJobNumber("recommendMainJobNumber");
        productInfo2.setRecommendOrgCode("recommendOrgCode");
        orderSubmitRequest2.setProductInfo(productInfo2);
        final FhOrderInfo orderInfo2 = new FhOrderInfo();
        orderInfo2.setSubmitTime("submitTime");
        orderInfo2.setStartTime("startTime");
        orderInfo2.setEndTime("endTime");
        orderSubmitRequest2.setOrderInfo(orderInfo2);
        final FhProposer proposerInfo2 = new FhProposer();
        proposerInfo2.setPersonName("insuredName");
        proposerInfo2.setPersonGender("insuredPersonGender");
        proposerInfo2.setIdType("idType");
        proposerInfo2.setIdNumber("idNumber");
        proposerInfo2.setBirthday("birthday");
        proposerInfo2.setCellPhone("cellPhone");
        proposerInfo2.setEmail("email");
        proposerInfo2.setAddress("address");
        proposerInfo2.setAppStatus("-2");
        proposerInfo2.setPolicyNo("insIdNumber");
        proposerInfo2.setDownloadURL("downloadURL");
        orderSubmitRequest2.setProposerInfo(proposerInfo2);
        final FhInsuredPerson fhInsuredPerson2 = new FhInsuredPerson();
        fhInsuredPerson2.setPersonName("insuredName");
        fhInsuredPerson2.setIdType("idType");
        fhInsuredPerson2.setIdNumber("idNumber");
        fhInsuredPerson2.setBirthday("birthday");
        fhInsuredPerson2.setCellPhone("cellPhone");
        fhInsuredPerson2.setAppStatus("-2");
        fhInsuredPerson2.setPolicyNo("insIdNumber");
        fhInsuredPerson2.setRelationship("99");
        fhInsuredPerson2.setFlightNo("flightNo");
        fhInsuredPerson2.setFlightTime("flightTime");
        fhInsuredPerson2.setOccupationCode("");
        fhInsuredPerson2.setOccupationGroup("occupationGroup");
        fhInsuredPerson2.setDestinationCountryText("destinationCountryText");
        fhInsuredPerson2.setAnnualIncome("annualIncome");
        fhInsuredPerson2.setStudentType("studentType");
        fhInsuredPerson2.setSchoolType("schoolType");
        fhInsuredPerson2.setSchoolNature("schoolNature");
        fhInsuredPerson2.setSchoolName("schoolName");
        fhInsuredPerson2.setSchoolClass("schoolClass");
        fhInsuredPerson2.setStudentId("studentId");
        fhInsuredPerson2.setIdPeriodStart("idPeriodStart");
        fhInsuredPerson2.setIdPeriodEnd("idPeriodEnd");
        fhInsuredPerson2.setSmoke("smoke");
        fhInsuredPerson2.setOldPolicyNo("oldPolicyNo");
        final TestPremiumDutyForm testPremiumDutyForm4 = new TestPremiumDutyForm();
        fhInsuredPerson2.setDuties(Arrays.asList(testPremiumDutyForm4));
        final TestPremiumProductForm product2 = new TestPremiumProductForm();
        product2.setProductId(0);
        product2.setPlanId(0);
        product2.setPlanCode("planCode");
        product2.setPremium(new BigDecimal("0.00"));
        product2.setQty(0);
        final TestPremiumDutyForm testPremiumDutyForm5 = new TestPremiumDutyForm();
        product2.setPremiumDutyForms(Arrays.asList(testPremiumDutyForm5));
        fhInsuredPerson2.setProduct(product2);
        orderSubmitRequest2.setInsuredPerson(Arrays.asList(fhInsuredPerson2));
        final FhProperty propertyInfo2 = new FhProperty();
        propertyInfo2.setPropertyInfoIsExist("0");
        orderSubmitRequest2.setPropertyInfo(propertyInfo2);
        final SmOrderHouseDTO house2 = new SmOrderHouseDTO();
        orderSubmitRequest2.setHouse(house2);
        final SmOrderCarInfoDTO carInfo2 = new SmOrderCarInfoDTO();
        orderSubmitRequest2.setCarInfo(carInfo2);
        orderSubmitRequest2.setProductType("code");
        orderSubmitRequest2.setBizCode("bizCode");
        orderSubmitRequest2.setOrderOutType("orderOutType");
        orderSubmitRequest2.setToken("token");
        orderSubmitRequest2.setPlanId(0);
        orderSubmitRequest2.setQty(0);
        orderSubmitRequest2.setPreOrderId("preOrderId");
        orderSubmitRequest2.setRenew(false);
        orderSubmitRequest2.setRealRenewFlag(false);
        orderSubmitRequest2.setAppNo("appNo");
        orderSubmitRequest2.setAgentId(0);
        orderSubmitRequest2.setJobCode("jobCode");
        final SmCreateOrderSubmitRequest dto1 = new SmCreateOrderSubmitRequest(orderSubmitRequest2);

        // Confirm SmOrderMapper.insertOrderInsured(...).
        final OrderSubmitRequest orderSubmitRequest3 = new OrderSubmitRequest();
        final FhProduct productInfo3 = new FhProduct();
        productInfo3.setProductId("fhProductId");
        productInfo3.setRecommendId("recommendId");
        productInfo3.setRecommendMainJobNumber("recommendMainJobNumber");
        productInfo3.setRecommendOrgCode("recommendOrgCode");
        orderSubmitRequest3.setProductInfo(productInfo3);
        final FhOrderInfo orderInfo3 = new FhOrderInfo();
        orderInfo3.setSubmitTime("submitTime");
        orderInfo3.setStartTime("startTime");
        orderInfo3.setEndTime("endTime");
        orderSubmitRequest3.setOrderInfo(orderInfo3);
        final FhProposer proposerInfo3 = new FhProposer();
        proposerInfo3.setPersonName("insuredName");
        proposerInfo3.setPersonGender("insuredPersonGender");
        proposerInfo3.setIdType("idType");
        proposerInfo3.setIdNumber("idNumber");
        proposerInfo3.setBirthday("birthday");
        proposerInfo3.setCellPhone("cellPhone");
        proposerInfo3.setEmail("email");
        proposerInfo3.setAddress("address");
        proposerInfo3.setAppStatus("-2");
        proposerInfo3.setPolicyNo("insIdNumber");
        proposerInfo3.setDownloadURL("downloadURL");
        orderSubmitRequest3.setProposerInfo(proposerInfo3);
        final FhInsuredPerson fhInsuredPerson3 = new FhInsuredPerson();
        fhInsuredPerson3.setPersonName("insuredName");
        fhInsuredPerson3.setIdType("idType");
        fhInsuredPerson3.setIdNumber("idNumber");
        fhInsuredPerson3.setBirthday("birthday");
        fhInsuredPerson3.setCellPhone("cellPhone");
        fhInsuredPerson3.setAppStatus("-2");
        fhInsuredPerson3.setPolicyNo("insIdNumber");
        fhInsuredPerson3.setRelationship("99");
        fhInsuredPerson3.setFlightNo("flightNo");
        fhInsuredPerson3.setFlightTime("flightTime");
        fhInsuredPerson3.setOccupationCode("");
        fhInsuredPerson3.setOccupationGroup("occupationGroup");
        fhInsuredPerson3.setDestinationCountryText("destinationCountryText");
        fhInsuredPerson3.setAnnualIncome("annualIncome");
        fhInsuredPerson3.setStudentType("studentType");
        fhInsuredPerson3.setSchoolType("schoolType");
        fhInsuredPerson3.setSchoolNature("schoolNature");
        fhInsuredPerson3.setSchoolName("schoolName");
        fhInsuredPerson3.setSchoolClass("schoolClass");
        fhInsuredPerson3.setStudentId("studentId");
        fhInsuredPerson3.setIdPeriodStart("idPeriodStart");
        fhInsuredPerson3.setIdPeriodEnd("idPeriodEnd");
        fhInsuredPerson3.setSmoke("smoke");
        fhInsuredPerson3.setOldPolicyNo("oldPolicyNo");
        final TestPremiumDutyForm testPremiumDutyForm6 = new TestPremiumDutyForm();
        fhInsuredPerson3.setDuties(Arrays.asList(testPremiumDutyForm6));
        final TestPremiumProductForm product3 = new TestPremiumProductForm();
        product3.setProductId(0);
        product3.setPlanId(0);
        product3.setPlanCode("planCode");
        product3.setPremium(new BigDecimal("0.00"));
        product3.setQty(0);
        final TestPremiumDutyForm testPremiumDutyForm7 = new TestPremiumDutyForm();
        product3.setPremiumDutyForms(Arrays.asList(testPremiumDutyForm7));
        fhInsuredPerson3.setProduct(product3);
        orderSubmitRequest3.setInsuredPerson(Arrays.asList(fhInsuredPerson3));
        final FhProperty propertyInfo3 = new FhProperty();
        propertyInfo3.setPropertyInfoIsExist("0");
        orderSubmitRequest3.setPropertyInfo(propertyInfo3);
        final SmOrderHouseDTO house3 = new SmOrderHouseDTO();
        orderSubmitRequest3.setHouse(house3);
        final SmOrderCarInfoDTO carInfo3 = new SmOrderCarInfoDTO();
        orderSubmitRequest3.setCarInfo(carInfo3);
        orderSubmitRequest3.setProductType("code");
        orderSubmitRequest3.setBizCode("bizCode");
        orderSubmitRequest3.setOrderOutType("orderOutType");
        orderSubmitRequest3.setToken("token");
        orderSubmitRequest3.setPlanId(0);
        orderSubmitRequest3.setQty(0);
        orderSubmitRequest3.setPreOrderId("preOrderId");
        orderSubmitRequest3.setRenew(false);
        orderSubmitRequest3.setRealRenewFlag(false);
        orderSubmitRequest3.setAppNo("appNo");
        orderSubmitRequest3.setAgentId(0);
        orderSubmitRequest3.setJobCode("jobCode");
        final SmCreateOrderSubmitRequest dto2 = new SmCreateOrderSubmitRequest(orderSubmitRequest3);
    }

    @Test
    public void testSyncDetail() throws Exception {
        // Setup
        // Configure AmPolicyTkMapper.selectByExample(...).
        final AmPolicyTk amPolicyTk = new AmPolicyTk();
        amPolicyTk.setPolicyNo("policyNo");
        amPolicyTk.setSubPolicyNo("subPolicyNo");
        amPolicyTk.setRiskName("riskName");
        amPolicyTk.setRiskCode("riskCode");
        amPolicyTk.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk.setPolicyUrl("policyDownLoadUrl");
        amPolicyTk.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk.setBenchmarkPremium(new BigDecimal("0.00"));
        amPolicyTk.setState(0);
        amPolicyTk.setRemark("msg");
        final List<AmPolicyTk> amPolicyTks = Arrays.asList(amPolicyTk);
        when(mockPolicyTkMapper.selectByExample(any(Object.class))).thenReturn(amPolicyTks);

        // Configure TkAutoServiceAdapter.queryTkDetail(...).
        final TkAutoRespHelper<TkAutoQueryPolicyDetailResp> tkAutoQueryPolicyDetailRespTkAutoRespHelper = new TkAutoRespHelper<>();
        tkAutoQueryPolicyDetailRespTkAutoRespHelper.setAppCode("1");
        tkAutoQueryPolicyDetailRespTkAutoRespHelper.setAppMessage("appMessage");
        final TkAutoQueryPolicyDetailResp tkAutoQueryPolicyDetailResp = new TkAutoQueryPolicyDetailResp();
        final TkAutoRiskInfoD tkAutoRiskInfoD = new TkAutoRiskInfoD();
        tkAutoRiskInfoD.setRiskCode("riskCode");
        final TkAutoBaseInfoD baseInfoD = new TkAutoBaseInfoD();
        baseInfoD.setSubPolicyNo("subPolicyNo");
        baseInfoD.setRiskName("riskName");
        baseInfoD.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD.setPremium(new BigDecimal("0.00"));
        baseInfoD.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TkAutoPolicyViewList policyViewList = new TkAutoPolicyViewList();
        final TkAutoPolicyData policyData = new TkAutoPolicyData();
        policyData.setPolicyDownLoadUrl("policyDownLoadUrl");
        policyViewList.setPolicyData(policyData);
        baseInfoD.setPolicyViewList(policyViewList);
        tkAutoRiskInfoD.setBaseInfoD(baseInfoD);
        final TkAutoRelatedInfoD tkAutoRelatedInfoD = new TkAutoRelatedInfoD();
        tkAutoRelatedInfoD.setInsureFlag("insureFlag");
        tkAutoRelatedInfoD.setInsureTypeCode("insureTypeCode");
        tkAutoRiskInfoD.setRelatedInfoDList(Arrays.asList(tkAutoRelatedInfoD));
        tkAutoQueryPolicyDetailResp.setRiskInfoList(Arrays.asList(tkAutoRiskInfoD));
        tkAutoQueryPolicyDetailRespTkAutoRespHelper.setData(tkAutoQueryPolicyDetailResp);
        tkAutoQueryPolicyDetailRespTkAutoRespHelper.setContent("content");
        when(mockAdapter.queryTkDetail("policyNo")).thenReturn(tkAutoQueryPolicyDetailRespTkAutoRespHelper);

        // Configure TkAutoCvtHelper.findDrivingTkAutoRiskInfoD(...).
        final TkAutoRiskInfoD tkAutoRiskInfoD1 = new TkAutoRiskInfoD();
        tkAutoRiskInfoD1.setRiskCode("riskCode");
        final TkAutoBaseInfoD baseInfoD1 = new TkAutoBaseInfoD();
        baseInfoD1.setSubPolicyNo("subPolicyNo");
        baseInfoD1.setRiskName("riskName");
        baseInfoD1.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD1.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD1.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD1.setPremium(new BigDecimal("0.00"));
        baseInfoD1.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TkAutoPolicyViewList policyViewList1 = new TkAutoPolicyViewList();
        final TkAutoPolicyData policyData1 = new TkAutoPolicyData();
        policyData1.setPolicyDownLoadUrl("policyDownLoadUrl");
        policyViewList1.setPolicyData(policyData1);
        baseInfoD1.setPolicyViewList(policyViewList1);
        tkAutoRiskInfoD1.setBaseInfoD(baseInfoD1);
        final TkAutoRelatedInfoD tkAutoRelatedInfoD1 = new TkAutoRelatedInfoD();
        tkAutoRelatedInfoD1.setInsureFlag("insureFlag");
        tkAutoRelatedInfoD1.setInsureTypeCode("insureTypeCode");
        tkAutoRiskInfoD1.setRelatedInfoDList(Arrays.asList(tkAutoRelatedInfoD1));
        final TkAutoRiskInfoD tkAutoRiskInfoD2 = new TkAutoRiskInfoD();
        tkAutoRiskInfoD2.setRiskCode("riskCode");
        final TkAutoBaseInfoD baseInfoD2 = new TkAutoBaseInfoD();
        baseInfoD2.setSubPolicyNo("subPolicyNo");
        baseInfoD2.setRiskName("riskName");
        baseInfoD2.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD2.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD2.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD2.setPremium(new BigDecimal("0.00"));
        baseInfoD2.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TkAutoPolicyViewList policyViewList2 = new TkAutoPolicyViewList();
        final TkAutoPolicyData policyData2 = new TkAutoPolicyData();
        policyData2.setPolicyDownLoadUrl("policyDownLoadUrl");
        policyViewList2.setPolicyData(policyData2);
        baseInfoD2.setPolicyViewList(policyViewList2);
        tkAutoRiskInfoD2.setBaseInfoD(baseInfoD2);
        final TkAutoRelatedInfoD tkAutoRelatedInfoD2 = new TkAutoRelatedInfoD();
        tkAutoRelatedInfoD2.setInsureFlag("insureFlag");
        tkAutoRelatedInfoD2.setInsureTypeCode("insureTypeCode");
        tkAutoRiskInfoD2.setRelatedInfoDList(Arrays.asList(tkAutoRelatedInfoD2));
        final List<TkAutoRiskInfoD> tkAutoRiskInfoDList = Arrays.asList(tkAutoRiskInfoD2);
        when(mockCvtHelper.findDrivingTkAutoRiskInfoD(tkAutoRiskInfoDList)).thenReturn(tkAutoRiskInfoD1);

        // Configure TkAutoCvtHelper.getRelatedInfoDList(...).
        final TkAutoRelatedInfoD tkAutoRelatedInfoD3 = new TkAutoRelatedInfoD();
        tkAutoRelatedInfoD3.setInsuredName("insuredName");
        tkAutoRelatedInfoD3.setInsureFlag("insureFlag");
        tkAutoRelatedInfoD3.setInsureTypeCode("insureTypeCode");
        tkAutoRelatedInfoD3.setInsureType("insureType");
        tkAutoRelatedInfoD3.setCertiType("certiType");
        final List<TkAutoRelatedInfoD> tkAutoRelatedInfoDS = Arrays.asList(tkAutoRelatedInfoD3);
        final TkAutoRiskInfoD tkAutoRiskInfoD3 = new TkAutoRiskInfoD();
        tkAutoRiskInfoD3.setRiskCode("riskCode");
        final TkAutoBaseInfoD baseInfoD3 = new TkAutoBaseInfoD();
        baseInfoD3.setSubPolicyNo("subPolicyNo");
        baseInfoD3.setRiskName("riskName");
        baseInfoD3.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD3.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD3.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD3.setPremium(new BigDecimal("0.00"));
        baseInfoD3.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TkAutoPolicyViewList policyViewList3 = new TkAutoPolicyViewList();
        final TkAutoPolicyData policyData3 = new TkAutoPolicyData();
        policyData3.setPolicyDownLoadUrl("policyDownLoadUrl");
        policyViewList3.setPolicyData(policyData3);
        baseInfoD3.setPolicyViewList(policyViewList3);
        tkAutoRiskInfoD3.setBaseInfoD(baseInfoD3);
        final TkAutoRelatedInfoD tkAutoRelatedInfoD4 = new TkAutoRelatedInfoD();
        tkAutoRelatedInfoD4.setInsureFlag("insureFlag");
        tkAutoRelatedInfoD4.setInsureTypeCode("insureTypeCode");
        tkAutoRiskInfoD3.setRelatedInfoDList(Arrays.asList(tkAutoRelatedInfoD4));
        final List<TkAutoRiskInfoD> tkAutoRiskInfoDList1 = Arrays.asList(tkAutoRiskInfoD3);
        when(mockCvtHelper.getRelatedInfoDList(tkAutoRiskInfoDList1)).thenReturn(tkAutoRelatedInfoDS);

        // Run the test
        tkAutoOrderServiceUnderTest.syncDetail();


    }

    @Test
    public void testSyncDetail_AmPolicyTkMapperSelectByExampleReturnsNoItems() throws Exception {
        // Setup
        when(mockPolicyTkMapper.selectByExample(any(Object.class))).thenReturn(Collections.emptyList());

        // Run the test
        tkAutoOrderServiceUnderTest.syncDetail();

        // Verify the results
    }

    @Test
    public void testSyncDetail_TkAutoCvtHelperFindDrivingTkAutoRiskInfoDReturnsNull() throws Exception {
        // Setup
        // Configure AmPolicyTkMapper.selectByExample(...).
        final AmPolicyTk amPolicyTk = new AmPolicyTk();
        amPolicyTk.setPolicyNo("policyNo");
        amPolicyTk.setSubPolicyNo("subPolicyNo");
        amPolicyTk.setRiskName("riskName");
        amPolicyTk.setRiskCode("riskCode");
        amPolicyTk.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk.setPolicyUrl("policyDownLoadUrl");
        amPolicyTk.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk.setBenchmarkPremium(new BigDecimal("0.00"));
        amPolicyTk.setState(0);
        amPolicyTk.setRemark("msg");
        final List<AmPolicyTk> amPolicyTks = Arrays.asList(amPolicyTk);
        when(mockPolicyTkMapper.selectByExample(any(Object.class))).thenReturn(amPolicyTks);

        // Configure TkAutoServiceAdapter.queryTkDetail(...).
        final TkAutoRespHelper<TkAutoQueryPolicyDetailResp> tkAutoQueryPolicyDetailRespTkAutoRespHelper = new TkAutoRespHelper<>();
        tkAutoQueryPolicyDetailRespTkAutoRespHelper.setAppCode("1");
        tkAutoQueryPolicyDetailRespTkAutoRespHelper.setAppMessage("appMessage");
        final TkAutoQueryPolicyDetailResp tkAutoQueryPolicyDetailResp = new TkAutoQueryPolicyDetailResp();
        final TkAutoRiskInfoD tkAutoRiskInfoD = new TkAutoRiskInfoD();
        tkAutoRiskInfoD.setRiskCode("riskCode");
        final TkAutoBaseInfoD baseInfoD = new TkAutoBaseInfoD();
        baseInfoD.setSubPolicyNo("subPolicyNo");
        baseInfoD.setRiskName("riskName");
        baseInfoD.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD.setPremium(new BigDecimal("0.00"));
        baseInfoD.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TkAutoPolicyViewList policyViewList = new TkAutoPolicyViewList();
        final TkAutoPolicyData policyData = new TkAutoPolicyData();
        policyData.setPolicyDownLoadUrl("policyDownLoadUrl");
        policyViewList.setPolicyData(policyData);
        baseInfoD.setPolicyViewList(policyViewList);
        tkAutoRiskInfoD.setBaseInfoD(baseInfoD);
        final TkAutoRelatedInfoD tkAutoRelatedInfoD = new TkAutoRelatedInfoD();
        tkAutoRelatedInfoD.setInsureFlag("insureFlag");
        tkAutoRelatedInfoD.setInsureTypeCode("insureTypeCode");
        tkAutoRiskInfoD.setRelatedInfoDList(Arrays.asList(tkAutoRelatedInfoD));
        tkAutoQueryPolicyDetailResp.setRiskInfoList(Arrays.asList(tkAutoRiskInfoD));
        tkAutoQueryPolicyDetailRespTkAutoRespHelper.setData(tkAutoQueryPolicyDetailResp);
        tkAutoQueryPolicyDetailRespTkAutoRespHelper.setContent("content");
        when(mockAdapter.queryTkDetail("policyNo")).thenReturn(tkAutoQueryPolicyDetailRespTkAutoRespHelper);

        // Configure TkAutoCvtHelper.findDrivingTkAutoRiskInfoD(...).
        final TkAutoRiskInfoD tkAutoRiskInfoD1 = new TkAutoRiskInfoD();
        tkAutoRiskInfoD1.setRiskCode("riskCode");
        final TkAutoBaseInfoD baseInfoD1 = new TkAutoBaseInfoD();
        baseInfoD1.setSubPolicyNo("subPolicyNo");
        baseInfoD1.setRiskName("riskName");
        baseInfoD1.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD1.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD1.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD1.setPremium(new BigDecimal("0.00"));
        baseInfoD1.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TkAutoPolicyViewList policyViewList1 = new TkAutoPolicyViewList();
        final TkAutoPolicyData policyData1 = new TkAutoPolicyData();
        policyData1.setPolicyDownLoadUrl("policyDownLoadUrl");
        policyViewList1.setPolicyData(policyData1);
        baseInfoD1.setPolicyViewList(policyViewList1);
        tkAutoRiskInfoD1.setBaseInfoD(baseInfoD1);
        final TkAutoRelatedInfoD tkAutoRelatedInfoD1 = new TkAutoRelatedInfoD();
        tkAutoRelatedInfoD1.setInsureFlag("insureFlag");
        tkAutoRelatedInfoD1.setInsureTypeCode("insureTypeCode");
        tkAutoRiskInfoD1.setRelatedInfoDList(Arrays.asList(tkAutoRelatedInfoD1));
        final List<TkAutoRiskInfoD> tkAutoRiskInfoDList = Arrays.asList(tkAutoRiskInfoD1);
        when(mockCvtHelper.findDrivingTkAutoRiskInfoD(tkAutoRiskInfoDList)).thenReturn(null);

        // Run the test
        tkAutoOrderServiceUnderTest.syncDetail();

        // Verify the results
        // Confirm AmPolicyTkMapper.updateByExampleSelective(...).
        final AmPolicyTk record = new AmPolicyTk();
        record.setPolicyNo("policyNo");
        record.setSubPolicyNo("subPolicyNo");
        record.setRiskName("riskName");
        record.setRiskCode("riskCode");
        record.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record.setPolicyUrl("policyDownLoadUrl");
        record.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record.setBenchmarkPremium(new BigDecimal("0.00"));
        record.setState(0);
        record.setRemark("msg");

        // Confirm AmPolicyTkMapper.insertUseGeneratedKeys(...).
        final AmPolicyTk record1 = new AmPolicyTk();
        record1.setPolicyNo("policyNo");
        record1.setSubPolicyNo("subPolicyNo");
        record1.setRiskName("riskName");
        record1.setRiskCode("riskCode");
        record1.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record1.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record1.setPolicyUrl("policyDownLoadUrl");
        record1.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record1.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record1.setBenchmarkPremium(new BigDecimal("0.00"));
        record1.setState(0);
        record1.setRemark("msg");

        // Confirm AmPolicyTkDetailMapper.insertUseGeneratedKeys(...).
        final AmPolicyTkDetail record2 = new AmPolicyTkDetail();
        record2.setPolicyNo("policyNo");
        record2.setContent("content");
    }

    @Test
    public void testSyncDetail_TkAutoCvtHelperGetRelatedInfoDListReturnsNoItems() throws Exception {
        // Setup
        // Configure AmPolicyTkMapper.selectByExample(...).
        final AmPolicyTk amPolicyTk = new AmPolicyTk();
        amPolicyTk.setPolicyNo("policyNo");
        amPolicyTk.setSubPolicyNo("subPolicyNo");
        amPolicyTk.setRiskName("riskName");
        amPolicyTk.setRiskCode("riskCode");
        amPolicyTk.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk.setPolicyUrl("policyDownLoadUrl");
        amPolicyTk.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk.setBenchmarkPremium(new BigDecimal("0.00"));
        amPolicyTk.setState(0);
        amPolicyTk.setRemark("msg");
        final List<AmPolicyTk> amPolicyTks = Arrays.asList(amPolicyTk);
        when(mockPolicyTkMapper.selectByExample(any(Object.class))).thenReturn(amPolicyTks);

        // Configure TkAutoServiceAdapter.queryTkDetail(...).
        final TkAutoRespHelper<TkAutoQueryPolicyDetailResp> tkAutoQueryPolicyDetailRespTkAutoRespHelper = new TkAutoRespHelper<>();
        tkAutoQueryPolicyDetailRespTkAutoRespHelper.setAppCode("1");
        tkAutoQueryPolicyDetailRespTkAutoRespHelper.setAppMessage("appMessage");
        final TkAutoQueryPolicyDetailResp tkAutoQueryPolicyDetailResp = new TkAutoQueryPolicyDetailResp();
        final TkAutoRiskInfoD tkAutoRiskInfoD = new TkAutoRiskInfoD();
        tkAutoRiskInfoD.setRiskCode("riskCode");
        final TkAutoBaseInfoD baseInfoD = new TkAutoBaseInfoD();
        baseInfoD.setSubPolicyNo("subPolicyNo");
        baseInfoD.setRiskName("riskName");
        baseInfoD.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD.setPremium(new BigDecimal("0.00"));
        baseInfoD.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TkAutoPolicyViewList policyViewList = new TkAutoPolicyViewList();
        final TkAutoPolicyData policyData = new TkAutoPolicyData();
        policyData.setPolicyDownLoadUrl("policyDownLoadUrl");
        policyViewList.setPolicyData(policyData);
        baseInfoD.setPolicyViewList(policyViewList);
        tkAutoRiskInfoD.setBaseInfoD(baseInfoD);
        final TkAutoRelatedInfoD tkAutoRelatedInfoD = new TkAutoRelatedInfoD();
        tkAutoRelatedInfoD.setInsureFlag("insureFlag");
        tkAutoRelatedInfoD.setInsureTypeCode("insureTypeCode");
        tkAutoRiskInfoD.setRelatedInfoDList(Arrays.asList(tkAutoRelatedInfoD));
        tkAutoQueryPolicyDetailResp.setRiskInfoList(Arrays.asList(tkAutoRiskInfoD));
        tkAutoQueryPolicyDetailRespTkAutoRespHelper.setData(tkAutoQueryPolicyDetailResp);
        tkAutoQueryPolicyDetailRespTkAutoRespHelper.setContent("content");
        when(mockAdapter.queryTkDetail("policyNo")).thenReturn(tkAutoQueryPolicyDetailRespTkAutoRespHelper);

        // Configure TkAutoCvtHelper.findDrivingTkAutoRiskInfoD(...).
        final TkAutoRiskInfoD tkAutoRiskInfoD1 = new TkAutoRiskInfoD();
        tkAutoRiskInfoD1.setRiskCode("riskCode");
        final TkAutoBaseInfoD baseInfoD1 = new TkAutoBaseInfoD();
        baseInfoD1.setSubPolicyNo("subPolicyNo");
        baseInfoD1.setRiskName("riskName");
        baseInfoD1.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD1.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD1.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD1.setPremium(new BigDecimal("0.00"));
        baseInfoD1.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TkAutoPolicyViewList policyViewList1 = new TkAutoPolicyViewList();
        final TkAutoPolicyData policyData1 = new TkAutoPolicyData();
        policyData1.setPolicyDownLoadUrl("policyDownLoadUrl");
        policyViewList1.setPolicyData(policyData1);
        baseInfoD1.setPolicyViewList(policyViewList1);
        tkAutoRiskInfoD1.setBaseInfoD(baseInfoD1);
        final TkAutoRelatedInfoD tkAutoRelatedInfoD1 = new TkAutoRelatedInfoD();
        tkAutoRelatedInfoD1.setInsureFlag("insureFlag");
        tkAutoRelatedInfoD1.setInsureTypeCode("insureTypeCode");
        tkAutoRiskInfoD1.setRelatedInfoDList(Arrays.asList(tkAutoRelatedInfoD1));
        final TkAutoRiskInfoD tkAutoRiskInfoD2 = new TkAutoRiskInfoD();
        tkAutoRiskInfoD2.setRiskCode("riskCode");
        final TkAutoBaseInfoD baseInfoD2 = new TkAutoBaseInfoD();
        baseInfoD2.setSubPolicyNo("subPolicyNo");
        baseInfoD2.setRiskName("riskName");
        baseInfoD2.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD2.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD2.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD2.setPremium(new BigDecimal("0.00"));
        baseInfoD2.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TkAutoPolicyViewList policyViewList2 = new TkAutoPolicyViewList();
        final TkAutoPolicyData policyData2 = new TkAutoPolicyData();
        policyData2.setPolicyDownLoadUrl("policyDownLoadUrl");
        policyViewList2.setPolicyData(policyData2);
        baseInfoD2.setPolicyViewList(policyViewList2);
        tkAutoRiskInfoD2.setBaseInfoD(baseInfoD2);
        final TkAutoRelatedInfoD tkAutoRelatedInfoD2 = new TkAutoRelatedInfoD();
        tkAutoRelatedInfoD2.setInsureFlag("insureFlag");
        tkAutoRelatedInfoD2.setInsureTypeCode("insureTypeCode");
        tkAutoRiskInfoD2.setRelatedInfoDList(Arrays.asList(tkAutoRelatedInfoD2));
        final List<TkAutoRiskInfoD> tkAutoRiskInfoDList = Arrays.asList(tkAutoRiskInfoD2);
        when(mockCvtHelper.findDrivingTkAutoRiskInfoD(tkAutoRiskInfoDList)).thenReturn(tkAutoRiskInfoD1);

        // Configure TkAutoCvtHelper.getRelatedInfoDList(...).
        final TkAutoRiskInfoD tkAutoRiskInfoD3 = new TkAutoRiskInfoD();
        tkAutoRiskInfoD3.setRiskCode("riskCode");
        final TkAutoBaseInfoD baseInfoD3 = new TkAutoBaseInfoD();
        baseInfoD3.setSubPolicyNo("subPolicyNo");
        baseInfoD3.setRiskName("riskName");
        baseInfoD3.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD3.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD3.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD3.setPremium(new BigDecimal("0.00"));
        baseInfoD3.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TkAutoPolicyViewList policyViewList3 = new TkAutoPolicyViewList();
        final TkAutoPolicyData policyData3 = new TkAutoPolicyData();
        policyData3.setPolicyDownLoadUrl("policyDownLoadUrl");
        policyViewList3.setPolicyData(policyData3);
        baseInfoD3.setPolicyViewList(policyViewList3);
        tkAutoRiskInfoD3.setBaseInfoD(baseInfoD3);
        final TkAutoRelatedInfoD tkAutoRelatedInfoD3 = new TkAutoRelatedInfoD();
        tkAutoRelatedInfoD3.setInsureFlag("insureFlag");
        tkAutoRelatedInfoD3.setInsureTypeCode("insureTypeCode");
        tkAutoRiskInfoD3.setRelatedInfoDList(Arrays.asList(tkAutoRelatedInfoD3));
        final List<TkAutoRiskInfoD> tkAutoRiskInfoDList1 = Arrays.asList(tkAutoRiskInfoD3);
        when(mockCvtHelper.getRelatedInfoDList(tkAutoRiskInfoDList1)).thenReturn(Collections.emptyList());

        // Run the test
        tkAutoOrderServiceUnderTest.syncDetail();

        // Verify the results
        // Confirm AmPolicyTkMapper.updateByExampleSelective(...).
        final AmPolicyTk record = new AmPolicyTk();
        record.setPolicyNo("policyNo");
        record.setSubPolicyNo("subPolicyNo");
        record.setRiskName("riskName");
        record.setRiskCode("riskCode");
        record.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record.setPolicyUrl("policyDownLoadUrl");
        record.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record.setBenchmarkPremium(new BigDecimal("0.00"));
        record.setState(0);
        record.setRemark("msg");

        // Confirm AmPolicyTkMapper.insertUseGeneratedKeys(...).
        final AmPolicyTk record1 = new AmPolicyTk();
        record1.setPolicyNo("policyNo");
        record1.setSubPolicyNo("subPolicyNo");
        record1.setRiskName("riskName");
        record1.setRiskCode("riskCode");
        record1.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record1.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record1.setPolicyUrl("policyDownLoadUrl");
        record1.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record1.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record1.setBenchmarkPremium(new BigDecimal("0.00"));
        record1.setState(0);
        record1.setRemark("msg");

        // Confirm AmPolicyTkDetailMapper.insertUseGeneratedKeys(...).
        final AmPolicyTkDetail record2 = new AmPolicyTkDetail();
        record2.setPolicyNo("policyNo");
        record2.setContent("content");
    }

    @Test
    public void testSyncDetail_TkAutoCvtHelperCvtOrderInfoReturnsNoItems() throws Exception {
        // Setup
        // Configure AmPolicyTkMapper.selectByExample(...).
        final AmPolicyTk amPolicyTk = new AmPolicyTk();
        amPolicyTk.setPolicyNo("policyNo");
        amPolicyTk.setSubPolicyNo("subPolicyNo");
        amPolicyTk.setRiskName("riskName");
        amPolicyTk.setRiskCode("riskCode");
        amPolicyTk.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk.setPolicyUrl("policyDownLoadUrl");
        amPolicyTk.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk.setBenchmarkPremium(new BigDecimal("0.00"));
        amPolicyTk.setState(0);
        amPolicyTk.setRemark("msg");
        final List<AmPolicyTk> amPolicyTks = Arrays.asList(amPolicyTk);
        when(mockPolicyTkMapper.selectByExample(any(Object.class))).thenReturn(amPolicyTks);

        // Configure TkAutoServiceAdapter.queryTkDetail(...).
        final TkAutoRespHelper<TkAutoQueryPolicyDetailResp> tkAutoQueryPolicyDetailRespTkAutoRespHelper = new TkAutoRespHelper<>();
        tkAutoQueryPolicyDetailRespTkAutoRespHelper.setAppCode("1");
        tkAutoQueryPolicyDetailRespTkAutoRespHelper.setAppMessage("appMessage");
        final TkAutoQueryPolicyDetailResp tkAutoQueryPolicyDetailResp = new TkAutoQueryPolicyDetailResp();
        final TkAutoRiskInfoD tkAutoRiskInfoD = new TkAutoRiskInfoD();
        tkAutoRiskInfoD.setRiskCode("riskCode");
        final TkAutoBaseInfoD baseInfoD = new TkAutoBaseInfoD();
        baseInfoD.setSubPolicyNo("subPolicyNo");
        baseInfoD.setRiskName("riskName");
        baseInfoD.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD.setPremium(new BigDecimal("0.00"));
        baseInfoD.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TkAutoPolicyViewList policyViewList = new TkAutoPolicyViewList();
        final TkAutoPolicyData policyData = new TkAutoPolicyData();
        policyData.setPolicyDownLoadUrl("policyDownLoadUrl");
        policyViewList.setPolicyData(policyData);
        baseInfoD.setPolicyViewList(policyViewList);
        tkAutoRiskInfoD.setBaseInfoD(baseInfoD);
        final TkAutoRelatedInfoD tkAutoRelatedInfoD = new TkAutoRelatedInfoD();
        tkAutoRelatedInfoD.setInsureFlag("insureFlag");
        tkAutoRelatedInfoD.setInsureTypeCode("insureTypeCode");
        tkAutoRiskInfoD.setRelatedInfoDList(Arrays.asList(tkAutoRelatedInfoD));
        tkAutoQueryPolicyDetailResp.setRiskInfoList(Arrays.asList(tkAutoRiskInfoD));
        tkAutoQueryPolicyDetailRespTkAutoRespHelper.setData(tkAutoQueryPolicyDetailResp);
        tkAutoQueryPolicyDetailRespTkAutoRespHelper.setContent("content");
        when(mockAdapter.queryTkDetail("policyNo")).thenReturn(tkAutoQueryPolicyDetailRespTkAutoRespHelper);

        // Configure TkAutoCvtHelper.findDrivingTkAutoRiskInfoD(...).
        final TkAutoRiskInfoD tkAutoRiskInfoD1 = new TkAutoRiskInfoD();
        tkAutoRiskInfoD1.setRiskCode("riskCode");
        final TkAutoBaseInfoD baseInfoD1 = new TkAutoBaseInfoD();
        baseInfoD1.setSubPolicyNo("subPolicyNo");
        baseInfoD1.setRiskName("riskName");
        baseInfoD1.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD1.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD1.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD1.setPremium(new BigDecimal("0.00"));
        baseInfoD1.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TkAutoPolicyViewList policyViewList1 = new TkAutoPolicyViewList();
        final TkAutoPolicyData policyData1 = new TkAutoPolicyData();
        policyData1.setPolicyDownLoadUrl("policyDownLoadUrl");
        policyViewList1.setPolicyData(policyData1);
        baseInfoD1.setPolicyViewList(policyViewList1);
        tkAutoRiskInfoD1.setBaseInfoD(baseInfoD1);
        final TkAutoRelatedInfoD tkAutoRelatedInfoD1 = new TkAutoRelatedInfoD();
        tkAutoRelatedInfoD1.setInsureFlag("insureFlag");
        tkAutoRelatedInfoD1.setInsureTypeCode("insureTypeCode");
        tkAutoRiskInfoD1.setRelatedInfoDList(Arrays.asList(tkAutoRelatedInfoD1));
        final TkAutoRiskInfoD tkAutoRiskInfoD2 = new TkAutoRiskInfoD();
        tkAutoRiskInfoD2.setRiskCode("riskCode");
        final TkAutoBaseInfoD baseInfoD2 = new TkAutoBaseInfoD();
        baseInfoD2.setSubPolicyNo("subPolicyNo");
        baseInfoD2.setRiskName("riskName");
        baseInfoD2.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD2.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD2.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD2.setPremium(new BigDecimal("0.00"));
        baseInfoD2.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TkAutoPolicyViewList policyViewList2 = new TkAutoPolicyViewList();
        final TkAutoPolicyData policyData2 = new TkAutoPolicyData();
        policyData2.setPolicyDownLoadUrl("policyDownLoadUrl");
        policyViewList2.setPolicyData(policyData2);
        baseInfoD2.setPolicyViewList(policyViewList2);
        tkAutoRiskInfoD2.setBaseInfoD(baseInfoD2);
        final TkAutoRelatedInfoD tkAutoRelatedInfoD2 = new TkAutoRelatedInfoD();
        tkAutoRelatedInfoD2.setInsureFlag("insureFlag");
        tkAutoRelatedInfoD2.setInsureTypeCode("insureTypeCode");
        tkAutoRiskInfoD2.setRelatedInfoDList(Arrays.asList(tkAutoRelatedInfoD2));
        final List<TkAutoRiskInfoD> tkAutoRiskInfoDList = Arrays.asList(tkAutoRiskInfoD2);
        when(mockCvtHelper.findDrivingTkAutoRiskInfoD(tkAutoRiskInfoDList)).thenReturn(tkAutoRiskInfoD1);

        // Configure TkAutoCvtHelper.getRelatedInfoDList(...).
        final TkAutoRelatedInfoD tkAutoRelatedInfoD3 = new TkAutoRelatedInfoD();
        tkAutoRelatedInfoD3.setInsuredName("insuredName");
        tkAutoRelatedInfoD3.setInsureFlag("insureFlag");
        tkAutoRelatedInfoD3.setInsureTypeCode("insureTypeCode");
        tkAutoRelatedInfoD3.setInsureType("insureType");
        tkAutoRelatedInfoD3.setCertiType("certiType");
        final List<TkAutoRelatedInfoD> tkAutoRelatedInfoDS = Arrays.asList(tkAutoRelatedInfoD3);
        final TkAutoRiskInfoD tkAutoRiskInfoD3 = new TkAutoRiskInfoD();
        tkAutoRiskInfoD3.setRiskCode("riskCode");
        final TkAutoBaseInfoD baseInfoD3 = new TkAutoBaseInfoD();
        baseInfoD3.setSubPolicyNo("subPolicyNo");
        baseInfoD3.setRiskName("riskName");
        baseInfoD3.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD3.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD3.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD3.setPremium(new BigDecimal("0.00"));
        baseInfoD3.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TkAutoPolicyViewList policyViewList3 = new TkAutoPolicyViewList();
        final TkAutoPolicyData policyData3 = new TkAutoPolicyData();
        policyData3.setPolicyDownLoadUrl("policyDownLoadUrl");
        policyViewList3.setPolicyData(policyData3);
        baseInfoD3.setPolicyViewList(policyViewList3);
        tkAutoRiskInfoD3.setBaseInfoD(baseInfoD3);
        final TkAutoRelatedInfoD tkAutoRelatedInfoD4 = new TkAutoRelatedInfoD();
        tkAutoRelatedInfoD4.setInsureFlag("insureFlag");
        tkAutoRelatedInfoD4.setInsureTypeCode("insureTypeCode");
        tkAutoRiskInfoD3.setRelatedInfoDList(Arrays.asList(tkAutoRelatedInfoD4));
        final List<TkAutoRiskInfoD> tkAutoRiskInfoDList1 = Arrays.asList(tkAutoRiskInfoD3);
        when(mockCvtHelper.getRelatedInfoDList(tkAutoRiskInfoDList1)).thenReturn(tkAutoRelatedInfoDS);

        // Configure AmPolicyTkMapper.selectOneByPolicyNo(...).
        final AmPolicyTk amPolicyTk1 = new AmPolicyTk();
        amPolicyTk1.setPolicyNo("policyNo");
        amPolicyTk1.setSubPolicyNo("subPolicyNo");
        amPolicyTk1.setRiskName("riskName");
        amPolicyTk1.setRiskCode("riskCode");
        amPolicyTk1.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk1.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk1.setPolicyUrl("policyDownLoadUrl");
        amPolicyTk1.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk1.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk1.setBenchmarkPremium(new BigDecimal("0.00"));
        amPolicyTk1.setState(0);
        amPolicyTk1.setRemark("msg");
        when(mockPolicyTkMapper.selectOneByPolicyNo("policyNo")).thenReturn(amPolicyTk1);

        // Configure TkAutoCvtHelper.cvtOrderInfo(...).
        final AmPolicyTk tk = new AmPolicyTk();
        tk.setPolicyNo("policyNo");
        tk.setSubPolicyNo("subPolicyNo");
        tk.setRiskName("riskName");
        tk.setRiskCode("riskCode");
        tk.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        tk.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        tk.setPolicyUrl("policyDownLoadUrl");
        tk.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        tk.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        tk.setBenchmarkPremium(new BigDecimal("0.00"));
        tk.setState(0);
        tk.setRemark("msg");
        final TkAutoQueryPolicyDetailResp detailResp = new TkAutoQueryPolicyDetailResp();
        final TkAutoRiskInfoD tkAutoRiskInfoD4 = new TkAutoRiskInfoD();
        tkAutoRiskInfoD4.setRiskCode("riskCode");
        final TkAutoBaseInfoD baseInfoD4 = new TkAutoBaseInfoD();
        baseInfoD4.setSubPolicyNo("subPolicyNo");
        baseInfoD4.setRiskName("riskName");
        baseInfoD4.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD4.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD4.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD4.setPremium(new BigDecimal("0.00"));
        baseInfoD4.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TkAutoPolicyViewList policyViewList4 = new TkAutoPolicyViewList();
        final TkAutoPolicyData policyData4 = new TkAutoPolicyData();
        policyData4.setPolicyDownLoadUrl("policyDownLoadUrl");
        policyViewList4.setPolicyData(policyData4);
        baseInfoD4.setPolicyViewList(policyViewList4);
        tkAutoRiskInfoD4.setBaseInfoD(baseInfoD4);
        final TkAutoRelatedInfoD tkAutoRelatedInfoD5 = new TkAutoRelatedInfoD();
        tkAutoRelatedInfoD5.setInsureFlag("insureFlag");
        tkAutoRelatedInfoD5.setInsureTypeCode("insureTypeCode");
        tkAutoRiskInfoD4.setRelatedInfoDList(Arrays.asList(tkAutoRelatedInfoD5));
        detailResp.setRiskInfoList(Arrays.asList(tkAutoRiskInfoD4));
        when(mockCvtHelper.cvtOrderInfo(tk, detailResp, new HashMap<>())).thenReturn(Collections.emptyList());

        // Configure TkAutoCvtHelper.cvtAutoInfoList(...).
        final AutoOrderDTO autoOrderDTO = new AutoOrderDTO();
        autoOrderDTO.setOrderNo("orderNo");
        final AutoOrder order = new AutoOrder();
        order.setPayStatus("-1");
        order.setPaymentTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        order.setCommissionId(0);
        autoOrderDTO.setOrder(order);
        final AutoOrderPerson autoOrderPerson = new AutoOrderPerson();
        autoOrderDTO.setPeople(Arrays.asList(autoOrderPerson));
        final AutoOrderPolicyDTO autoOrderPolicyDTO = new AutoOrderPolicyDTO();
        final AutoOrderPolicy policy = new AutoOrderPolicy();
        policy.setPolicyNo("policyNo");
        policy.setSubPolicyNo("subPolicyNo");
        policy.setPolicyState("-2");
        policy.setBusinessScore("businessScore");
        autoOrderPolicyDTO.setPolicy(policy);
        final AutoOrderRisk autoOrderRisk = new AutoOrderRisk();
        autoOrderRisk.setRiskName("riskName");
        autoOrderRisk.setPremium(new BigDecimal("0.00"));
        autoOrderRisk.setAmount(new BigDecimal("0.00"));
        autoOrderPolicyDTO.setRiskList(Arrays.asList(autoOrderRisk));
        autoOrderDTO.setPolicies(Arrays.asList(autoOrderPolicyDTO));
        final AutoOrderExtend extend = new AutoOrderExtend();
        autoOrderDTO.setExtend(extend);
        final AutoOrderCar car = new AutoOrderCar();
        car.setOwnerPersonName("ownerPersonName");
        car.setOwnerPersonGender("ownerPersonGender");
        car.setOwnerIdType("ownerIdType");
        car.setOwnerIdNumber("ownerIdNumber");
        car.setFirstRegDate("firstRegDate");
        car.setFullLoad("fullLoad");
        car.setTransferDate("transferDate");
        car.setSeatCnt("approvedNum");
        car.setPrice("price");
        car.setCarUserType("carUserType");
        car.setDisplacement("displacement");
        car.setIsNewCar("isNewCar");
        autoOrderDTO.setCar(car);
        final List<AutoOrderDTO> autoOrderDTOS = Arrays.asList(autoOrderDTO);
        final AmPolicyTk tk1 = new AmPolicyTk();
        tk1.setPolicyNo("policyNo");
        tk1.setSubPolicyNo("subPolicyNo");
        tk1.setRiskName("riskName");
        tk1.setRiskCode("riskCode");
        tk1.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        tk1.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        tk1.setPolicyUrl("policyDownLoadUrl");
        tk1.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        tk1.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        tk1.setBenchmarkPremium(new BigDecimal("0.00"));
        tk1.setState(0);
        tk1.setRemark("msg");
        final TkAutoQueryPolicyDetailResp detailResp1 = new TkAutoQueryPolicyDetailResp();
        final TkAutoRiskInfoD tkAutoRiskInfoD5 = new TkAutoRiskInfoD();
        tkAutoRiskInfoD5.setRiskCode("riskCode");
        final TkAutoBaseInfoD baseInfoD5 = new TkAutoBaseInfoD();
        baseInfoD5.setSubPolicyNo("subPolicyNo");
        baseInfoD5.setRiskName("riskName");
        baseInfoD5.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD5.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD5.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD5.setPremium(new BigDecimal("0.00"));
        baseInfoD5.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TkAutoPolicyViewList policyViewList5 = new TkAutoPolicyViewList();
        final TkAutoPolicyData policyData5 = new TkAutoPolicyData();
        policyData5.setPolicyDownLoadUrl("policyDownLoadUrl");
        policyViewList5.setPolicyData(policyData5);
        baseInfoD5.setPolicyViewList(policyViewList5);
        tkAutoRiskInfoD5.setBaseInfoD(baseInfoD5);
        final TkAutoRelatedInfoD tkAutoRelatedInfoD6 = new TkAutoRelatedInfoD();
        tkAutoRelatedInfoD6.setInsureFlag("insureFlag");
        tkAutoRelatedInfoD6.setInsureTypeCode("insureTypeCode");
        tkAutoRiskInfoD5.setRelatedInfoDList(Arrays.asList(tkAutoRelatedInfoD6));
        detailResp1.setRiskInfoList(Arrays.asList(tkAutoRiskInfoD5));
        when(mockCvtHelper.cvtAutoInfoList(tk1, Arrays.asList("value"), detailResp1, new HashMap<>())).thenReturn(autoOrderDTOS);

        // Run the test
        tkAutoOrderServiceUnderTest.syncDetail();

        // Verify the results
        // Confirm AmPolicyTkMapper.updateByExampleSelective(...).
        final AmPolicyTk record = new AmPolicyTk();
        record.setPolicyNo("policyNo");
        record.setSubPolicyNo("subPolicyNo");
        record.setRiskName("riskName");
        record.setRiskCode("riskCode");
        record.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record.setPolicyUrl("policyDownLoadUrl");
        record.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record.setBenchmarkPremium(new BigDecimal("0.00"));
        record.setState(0);
        record.setRemark("msg");

        // Confirm AmPolicyTkMapper.insertUseGeneratedKeys(...).
        final AmPolicyTk record1 = new AmPolicyTk();
        record1.setPolicyNo("policyNo");
        record1.setSubPolicyNo("subPolicyNo");
        record1.setRiskName("riskName");
        record1.setRiskCode("riskCode");
        record1.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record1.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record1.setPolicyUrl("policyDownLoadUrl");
        record1.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record1.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record1.setBenchmarkPremium(new BigDecimal("0.00"));
        record1.setState(0);
        record1.setRemark("msg");

        // Confirm AmPolicyTkDetailMapper.insertUseGeneratedKeys(...).
        final AmPolicyTkDetail record2 = new AmPolicyTkDetail();
        record2.setPolicyNo("policyNo");
        record2.setContent("content");
    }

    @Test
    public void testSyncDetail_TkAutoCvtHelperCvtAutoInfoListReturnsNoItems() throws Exception {
        // Setup
        // Configure AmPolicyTkMapper.selectByExample(...).
        final AmPolicyTk amPolicyTk = new AmPolicyTk();
        amPolicyTk.setPolicyNo("policyNo");
        amPolicyTk.setSubPolicyNo("subPolicyNo");
        amPolicyTk.setRiskName("riskName");
        amPolicyTk.setRiskCode("riskCode");
        amPolicyTk.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk.setPolicyUrl("policyDownLoadUrl");
        amPolicyTk.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk.setBenchmarkPremium(new BigDecimal("0.00"));
        amPolicyTk.setState(0);
        amPolicyTk.setRemark("msg");
        final List<AmPolicyTk> amPolicyTks = Arrays.asList(amPolicyTk);
        when(mockPolicyTkMapper.selectByExample(any(Object.class))).thenReturn(amPolicyTks);

        // Configure TkAutoServiceAdapter.queryTkDetail(...).
        final TkAutoRespHelper<TkAutoQueryPolicyDetailResp> tkAutoQueryPolicyDetailRespTkAutoRespHelper = new TkAutoRespHelper<>();
        tkAutoQueryPolicyDetailRespTkAutoRespHelper.setAppCode("1");
        tkAutoQueryPolicyDetailRespTkAutoRespHelper.setAppMessage("appMessage");
        final TkAutoQueryPolicyDetailResp tkAutoQueryPolicyDetailResp = new TkAutoQueryPolicyDetailResp();
        final TkAutoRiskInfoD tkAutoRiskInfoD = new TkAutoRiskInfoD();
        tkAutoRiskInfoD.setRiskCode("riskCode");
        final TkAutoBaseInfoD baseInfoD = new TkAutoBaseInfoD();
        baseInfoD.setSubPolicyNo("subPolicyNo");
        baseInfoD.setRiskName("riskName");
        baseInfoD.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD.setPremium(new BigDecimal("0.00"));
        baseInfoD.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TkAutoPolicyViewList policyViewList = new TkAutoPolicyViewList();
        final TkAutoPolicyData policyData = new TkAutoPolicyData();
        policyData.setPolicyDownLoadUrl("policyDownLoadUrl");
        policyViewList.setPolicyData(policyData);
        baseInfoD.setPolicyViewList(policyViewList);
        tkAutoRiskInfoD.setBaseInfoD(baseInfoD);
        final TkAutoRelatedInfoD tkAutoRelatedInfoD = new TkAutoRelatedInfoD();
        tkAutoRelatedInfoD.setInsureFlag("insureFlag");
        tkAutoRelatedInfoD.setInsureTypeCode("insureTypeCode");
        tkAutoRiskInfoD.setRelatedInfoDList(Arrays.asList(tkAutoRelatedInfoD));
        tkAutoQueryPolicyDetailResp.setRiskInfoList(Arrays.asList(tkAutoRiskInfoD));
        tkAutoQueryPolicyDetailRespTkAutoRespHelper.setData(tkAutoQueryPolicyDetailResp);
        tkAutoQueryPolicyDetailRespTkAutoRespHelper.setContent("content");
        when(mockAdapter.queryTkDetail("policyNo")).thenReturn(tkAutoQueryPolicyDetailRespTkAutoRespHelper);

        // Configure TkAutoCvtHelper.findDrivingTkAutoRiskInfoD(...).
        final TkAutoRiskInfoD tkAutoRiskInfoD1 = new TkAutoRiskInfoD();
        tkAutoRiskInfoD1.setRiskCode("riskCode");
        final TkAutoBaseInfoD baseInfoD1 = new TkAutoBaseInfoD();
        baseInfoD1.setSubPolicyNo("subPolicyNo");
        baseInfoD1.setRiskName("riskName");
        baseInfoD1.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD1.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD1.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD1.setPremium(new BigDecimal("0.00"));
        baseInfoD1.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TkAutoPolicyViewList policyViewList1 = new TkAutoPolicyViewList();
        final TkAutoPolicyData policyData1 = new TkAutoPolicyData();
        policyData1.setPolicyDownLoadUrl("policyDownLoadUrl");
        policyViewList1.setPolicyData(policyData1);
        baseInfoD1.setPolicyViewList(policyViewList1);
        tkAutoRiskInfoD1.setBaseInfoD(baseInfoD1);
        final TkAutoRelatedInfoD tkAutoRelatedInfoD1 = new TkAutoRelatedInfoD();
        tkAutoRelatedInfoD1.setInsureFlag("insureFlag");
        tkAutoRelatedInfoD1.setInsureTypeCode("insureTypeCode");
        tkAutoRiskInfoD1.setRelatedInfoDList(Arrays.asList(tkAutoRelatedInfoD1));
        final TkAutoRiskInfoD tkAutoRiskInfoD2 = new TkAutoRiskInfoD();
        tkAutoRiskInfoD2.setRiskCode("riskCode");
        final TkAutoBaseInfoD baseInfoD2 = new TkAutoBaseInfoD();
        baseInfoD2.setSubPolicyNo("subPolicyNo");
        baseInfoD2.setRiskName("riskName");
        baseInfoD2.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD2.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD2.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD2.setPremium(new BigDecimal("0.00"));
        baseInfoD2.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TkAutoPolicyViewList policyViewList2 = new TkAutoPolicyViewList();
        final TkAutoPolicyData policyData2 = new TkAutoPolicyData();
        policyData2.setPolicyDownLoadUrl("policyDownLoadUrl");
        policyViewList2.setPolicyData(policyData2);
        baseInfoD2.setPolicyViewList(policyViewList2);
        tkAutoRiskInfoD2.setBaseInfoD(baseInfoD2);
        final TkAutoRelatedInfoD tkAutoRelatedInfoD2 = new TkAutoRelatedInfoD();
        tkAutoRelatedInfoD2.setInsureFlag("insureFlag");
        tkAutoRelatedInfoD2.setInsureTypeCode("insureTypeCode");
        tkAutoRiskInfoD2.setRelatedInfoDList(Arrays.asList(tkAutoRelatedInfoD2));
        final List<TkAutoRiskInfoD> tkAutoRiskInfoDList = Arrays.asList(tkAutoRiskInfoD2);
        when(mockCvtHelper.findDrivingTkAutoRiskInfoD(tkAutoRiskInfoDList)).thenReturn(tkAutoRiskInfoD1);

        // Configure TkAutoCvtHelper.getRelatedInfoDList(...).
        final TkAutoRelatedInfoD tkAutoRelatedInfoD3 = new TkAutoRelatedInfoD();
        tkAutoRelatedInfoD3.setInsuredName("insuredName");
        tkAutoRelatedInfoD3.setInsureFlag("insureFlag");
        tkAutoRelatedInfoD3.setInsureTypeCode("insureTypeCode");
        tkAutoRelatedInfoD3.setInsureType("insureType");
        tkAutoRelatedInfoD3.setCertiType("certiType");
        final List<TkAutoRelatedInfoD> tkAutoRelatedInfoDS = Arrays.asList(tkAutoRelatedInfoD3);
        final TkAutoRiskInfoD tkAutoRiskInfoD3 = new TkAutoRiskInfoD();
        tkAutoRiskInfoD3.setRiskCode("riskCode");
        final TkAutoBaseInfoD baseInfoD3 = new TkAutoBaseInfoD();
        baseInfoD3.setSubPolicyNo("subPolicyNo");
        baseInfoD3.setRiskName("riskName");
        baseInfoD3.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD3.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD3.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD3.setPremium(new BigDecimal("0.00"));
        baseInfoD3.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TkAutoPolicyViewList policyViewList3 = new TkAutoPolicyViewList();
        final TkAutoPolicyData policyData3 = new TkAutoPolicyData();
        policyData3.setPolicyDownLoadUrl("policyDownLoadUrl");
        policyViewList3.setPolicyData(policyData3);
        baseInfoD3.setPolicyViewList(policyViewList3);
        tkAutoRiskInfoD3.setBaseInfoD(baseInfoD3);
        final TkAutoRelatedInfoD tkAutoRelatedInfoD4 = new TkAutoRelatedInfoD();
        tkAutoRelatedInfoD4.setInsureFlag("insureFlag");
        tkAutoRelatedInfoD4.setInsureTypeCode("insureTypeCode");
        tkAutoRiskInfoD3.setRelatedInfoDList(Arrays.asList(tkAutoRelatedInfoD4));
        final List<TkAutoRiskInfoD> tkAutoRiskInfoDList1 = Arrays.asList(tkAutoRiskInfoD3);
        when(mockCvtHelper.getRelatedInfoDList(tkAutoRiskInfoDList1)).thenReturn(tkAutoRelatedInfoDS);

        // Configure AmPolicyTkMapper.selectOneByPolicyNo(...).
        final AmPolicyTk amPolicyTk1 = new AmPolicyTk();
        amPolicyTk1.setPolicyNo("policyNo");
        amPolicyTk1.setSubPolicyNo("subPolicyNo");
        amPolicyTk1.setRiskName("riskName");
        amPolicyTk1.setRiskCode("riskCode");
        amPolicyTk1.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk1.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk1.setPolicyUrl("policyDownLoadUrl");
        amPolicyTk1.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk1.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk1.setBenchmarkPremium(new BigDecimal("0.00"));
        amPolicyTk1.setState(0);
        amPolicyTk1.setRemark("msg");
        when(mockPolicyTkMapper.selectOneByPolicyNo("policyNo")).thenReturn(amPolicyTk1);

        // Configure TkAutoCvtHelper.cvtOrderInfo(...).
        final OrderSubmitRequest orderSubmitRequest = new OrderSubmitRequest();
        final FhProduct productInfo = new FhProduct();
        productInfo.setProductId("fhProductId");
        productInfo.setRecommendId("recommendId");
        productInfo.setRecommendMainJobNumber("recommendMainJobNumber");
        productInfo.setRecommendOrgCode("recommendOrgCode");
        orderSubmitRequest.setProductInfo(productInfo);
        final FhOrderInfo orderInfo = new FhOrderInfo();
        orderInfo.setSubmitTime("submitTime");
        orderInfo.setStartTime("startTime");
        orderInfo.setEndTime("endTime");
        orderSubmitRequest.setOrderInfo(orderInfo);
        final FhProposer proposerInfo = new FhProposer();
        proposerInfo.setPersonName("insuredName");
        proposerInfo.setPersonGender("insuredPersonGender");
        proposerInfo.setIdType("idType");
        proposerInfo.setIdNumber("idNumber");
        proposerInfo.setBirthday("birthday");
        proposerInfo.setCellPhone("cellPhone");
        proposerInfo.setEmail("email");
        proposerInfo.setAddress("address");
        proposerInfo.setAppStatus("-2");
        proposerInfo.setPolicyNo("insIdNumber");
        proposerInfo.setDownloadURL("downloadURL");
        orderSubmitRequest.setProposerInfo(proposerInfo);
        final FhInsuredPerson fhInsuredPerson = new FhInsuredPerson();
        fhInsuredPerson.setPersonName("insuredName");
        fhInsuredPerson.setIdType("idType");
        fhInsuredPerson.setIdNumber("idNumber");
        fhInsuredPerson.setBirthday("birthday");
        fhInsuredPerson.setCellPhone("cellPhone");
        fhInsuredPerson.setAppStatus("-2");
        fhInsuredPerson.setPolicyNo("insIdNumber");
        fhInsuredPerson.setRelationship("99");
        fhInsuredPerson.setFlightNo("flightNo");
        fhInsuredPerson.setFlightTime("flightTime");
        fhInsuredPerson.setOccupationCode("");
        fhInsuredPerson.setOccupationGroup("occupationGroup");
        fhInsuredPerson.setDestinationCountryText("destinationCountryText");
        fhInsuredPerson.setAnnualIncome("annualIncome");
        fhInsuredPerson.setStudentType("studentType");
        fhInsuredPerson.setSchoolType("schoolType");
        fhInsuredPerson.setSchoolNature("schoolNature");
        fhInsuredPerson.setSchoolName("schoolName");
        fhInsuredPerson.setSchoolClass("schoolClass");
        fhInsuredPerson.setStudentId("studentId");
        fhInsuredPerson.setIdPeriodStart("idPeriodStart");
        fhInsuredPerson.setIdPeriodEnd("idPeriodEnd");
        fhInsuredPerson.setSmoke("smoke");
        fhInsuredPerson.setOldPolicyNo("oldPolicyNo");
        final TestPremiumDutyForm testPremiumDutyForm = new TestPremiumDutyForm();
        fhInsuredPerson.setDuties(Arrays.asList(testPremiumDutyForm));
        final TestPremiumProductForm product = new TestPremiumProductForm();
        product.setProductId(0);
        product.setPlanId(0);
        product.setPlanCode("planCode");
        product.setPremium(new BigDecimal("0.00"));
        product.setQty(0);
        final TestPremiumDutyForm testPremiumDutyForm1 = new TestPremiumDutyForm();
        product.setPremiumDutyForms(Arrays.asList(testPremiumDutyForm1));
        fhInsuredPerson.setProduct(product);
        orderSubmitRequest.setInsuredPerson(Arrays.asList(fhInsuredPerson));
        final FhProperty propertyInfo = new FhProperty();
        propertyInfo.setPropertyInfoIsExist("0");
        orderSubmitRequest.setPropertyInfo(propertyInfo);
        final SmOrderHouseDTO house = new SmOrderHouseDTO();
        orderSubmitRequest.setHouse(house);
        final SmOrderCarInfoDTO carInfo = new SmOrderCarInfoDTO();
        orderSubmitRequest.setCarInfo(carInfo);
        orderSubmitRequest.setProductType("code");
        orderSubmitRequest.setBizCode("bizCode");
        orderSubmitRequest.setOrderOutType("orderOutType");
        orderSubmitRequest.setToken("token");
        orderSubmitRequest.setPlanId(0);
        orderSubmitRequest.setQty(0);
        orderSubmitRequest.setPreOrderId("preOrderId");
        orderSubmitRequest.setRenew(false);
        orderSubmitRequest.setRealRenewFlag(false);
        orderSubmitRequest.setAppNo("appNo");
        orderSubmitRequest.setAgentId(0);
        orderSubmitRequest.setJobCode("jobCode");
        final List<SmCreateOrderSubmitRequest> smCreateOrderSubmitRequests = Arrays.asList(new SmCreateOrderSubmitRequest(orderSubmitRequest));
        final AmPolicyTk tk = new AmPolicyTk();
        tk.setPolicyNo("policyNo");
        tk.setSubPolicyNo("subPolicyNo");
        tk.setRiskName("riskName");
        tk.setRiskCode("riskCode");
        tk.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        tk.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        tk.setPolicyUrl("policyDownLoadUrl");
        tk.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        tk.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        tk.setBenchmarkPremium(new BigDecimal("0.00"));
        tk.setState(0);
        tk.setRemark("msg");
        final TkAutoQueryPolicyDetailResp detailResp = new TkAutoQueryPolicyDetailResp();
        final TkAutoRiskInfoD tkAutoRiskInfoD4 = new TkAutoRiskInfoD();
        tkAutoRiskInfoD4.setRiskCode("riskCode");
        final TkAutoBaseInfoD baseInfoD4 = new TkAutoBaseInfoD();
        baseInfoD4.setSubPolicyNo("subPolicyNo");
        baseInfoD4.setRiskName("riskName");
        baseInfoD4.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD4.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD4.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD4.setPremium(new BigDecimal("0.00"));
        baseInfoD4.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TkAutoPolicyViewList policyViewList4 = new TkAutoPolicyViewList();
        final TkAutoPolicyData policyData4 = new TkAutoPolicyData();
        policyData4.setPolicyDownLoadUrl("policyDownLoadUrl");
        policyViewList4.setPolicyData(policyData4);
        baseInfoD4.setPolicyViewList(policyViewList4);
        tkAutoRiskInfoD4.setBaseInfoD(baseInfoD4);
        final TkAutoRelatedInfoD tkAutoRelatedInfoD5 = new TkAutoRelatedInfoD();
        tkAutoRelatedInfoD5.setInsureFlag("insureFlag");
        tkAutoRelatedInfoD5.setInsureTypeCode("insureTypeCode");
        tkAutoRiskInfoD4.setRelatedInfoDList(Arrays.asList(tkAutoRelatedInfoD5));
        detailResp.setRiskInfoList(Arrays.asList(tkAutoRiskInfoD4));
        when(mockCvtHelper.cvtOrderInfo(tk, detailResp, new HashMap<>())).thenReturn(smCreateOrderSubmitRequests);

        // Configure TkAutoCvtHelper.cvtAutoInfoList(...).
        final AmPolicyTk tk1 = new AmPolicyTk();
        tk1.setPolicyNo("policyNo");
        tk1.setSubPolicyNo("subPolicyNo");
        tk1.setRiskName("riskName");
        tk1.setRiskCode("riskCode");
        tk1.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        tk1.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        tk1.setPolicyUrl("policyDownLoadUrl");
        tk1.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        tk1.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        tk1.setBenchmarkPremium(new BigDecimal("0.00"));
        tk1.setState(0);
        tk1.setRemark("msg");
        final TkAutoQueryPolicyDetailResp detailResp1 = new TkAutoQueryPolicyDetailResp();
        final TkAutoRiskInfoD tkAutoRiskInfoD5 = new TkAutoRiskInfoD();
        tkAutoRiskInfoD5.setRiskCode("riskCode");
        final TkAutoBaseInfoD baseInfoD5 = new TkAutoBaseInfoD();
        baseInfoD5.setSubPolicyNo("subPolicyNo");
        baseInfoD5.setRiskName("riskName");
        baseInfoD5.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD5.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD5.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD5.setPremium(new BigDecimal("0.00"));
        baseInfoD5.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TkAutoPolicyViewList policyViewList5 = new TkAutoPolicyViewList();
        final TkAutoPolicyData policyData5 = new TkAutoPolicyData();
        policyData5.setPolicyDownLoadUrl("policyDownLoadUrl");
        policyViewList5.setPolicyData(policyData5);
        baseInfoD5.setPolicyViewList(policyViewList5);
        tkAutoRiskInfoD5.setBaseInfoD(baseInfoD5);
        final TkAutoRelatedInfoD tkAutoRelatedInfoD6 = new TkAutoRelatedInfoD();
        tkAutoRelatedInfoD6.setInsureFlag("insureFlag");
        tkAutoRelatedInfoD6.setInsureTypeCode("insureTypeCode");
        tkAutoRiskInfoD5.setRelatedInfoDList(Arrays.asList(tkAutoRelatedInfoD6));
        detailResp1.setRiskInfoList(Arrays.asList(tkAutoRiskInfoD5));
        when(mockCvtHelper.cvtAutoInfoList(tk1, Arrays.asList("value"), detailResp1, new HashMap<>())).thenReturn(Collections.emptyList());

        when(mockOrderMapper.updateOrderPaymentTime("orderId")).thenReturn(0);
        when(mockOrderMapper.getFhProductIdByOrderId("orderId")).thenReturn(Arrays.asList("value"));

        // Run the test
        tkAutoOrderServiceUnderTest.syncDetail();

        // Verify the results
        // Confirm AmPolicyTkMapper.updateByExampleSelective(...).
        final AmPolicyTk record = new AmPolicyTk();
        record.setPolicyNo("policyNo");
        record.setSubPolicyNo("subPolicyNo");
        record.setRiskName("riskName");
        record.setRiskCode("riskCode");
        record.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record.setPolicyUrl("policyDownLoadUrl");
        record.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record.setBenchmarkPremium(new BigDecimal("0.00"));
        record.setState(0);
        record.setRemark("msg");

        // Confirm AmPolicyTkMapper.insertUseGeneratedKeys(...).
        final AmPolicyTk record1 = new AmPolicyTk();
        record1.setPolicyNo("policyNo");
        record1.setSubPolicyNo("subPolicyNo");
        record1.setRiskName("riskName");
        record1.setRiskCode("riskCode");
        record1.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record1.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record1.setPolicyUrl("policyDownLoadUrl");
        record1.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record1.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record1.setBenchmarkPremium(new BigDecimal("0.00"));
        record1.setState(0);
        record1.setRemark("msg");

        // Confirm AmPolicyTkDetailMapper.insertUseGeneratedKeys(...).
        final AmPolicyTkDetail record2 = new AmPolicyTkDetail();
        record2.setPolicyNo("policyNo");
        record2.setContent("content");

        // Confirm SmOrderMapper.insertOrder(...).
        final OrderSubmitRequest orderSubmitRequest1 = new OrderSubmitRequest();
        final FhProduct productInfo1 = new FhProduct();
        productInfo1.setProductId("fhProductId");
        productInfo1.setRecommendId("recommendId");
        productInfo1.setRecommendMainJobNumber("recommendMainJobNumber");
        productInfo1.setRecommendOrgCode("recommendOrgCode");
        orderSubmitRequest1.setProductInfo(productInfo1);
        final FhOrderInfo orderInfo1 = new FhOrderInfo();
        orderInfo1.setSubmitTime("submitTime");
        orderInfo1.setStartTime("startTime");
        orderInfo1.setEndTime("endTime");
        orderSubmitRequest1.setOrderInfo(orderInfo1);
        final FhProposer proposerInfo1 = new FhProposer();
        proposerInfo1.setPersonName("insuredName");
        proposerInfo1.setPersonGender("insuredPersonGender");
        proposerInfo1.setIdType("idType");
        proposerInfo1.setIdNumber("idNumber");
        proposerInfo1.setBirthday("birthday");
        proposerInfo1.setCellPhone("cellPhone");
        proposerInfo1.setEmail("email");
        proposerInfo1.setAddress("address");
        proposerInfo1.setAppStatus("-2");
        proposerInfo1.setPolicyNo("insIdNumber");
        proposerInfo1.setDownloadURL("downloadURL");
        orderSubmitRequest1.setProposerInfo(proposerInfo1);
        final FhInsuredPerson fhInsuredPerson1 = new FhInsuredPerson();
        fhInsuredPerson1.setPersonName("insuredName");
        fhInsuredPerson1.setIdType("idType");
        fhInsuredPerson1.setIdNumber("idNumber");
        fhInsuredPerson1.setBirthday("birthday");
        fhInsuredPerson1.setCellPhone("cellPhone");
        fhInsuredPerson1.setAppStatus("-2");
        fhInsuredPerson1.setPolicyNo("insIdNumber");
        fhInsuredPerson1.setRelationship("99");
        fhInsuredPerson1.setFlightNo("flightNo");
        fhInsuredPerson1.setFlightTime("flightTime");
        fhInsuredPerson1.setOccupationCode("");
        fhInsuredPerson1.setOccupationGroup("occupationGroup");
        fhInsuredPerson1.setDestinationCountryText("destinationCountryText");
        fhInsuredPerson1.setAnnualIncome("annualIncome");
        fhInsuredPerson1.setStudentType("studentType");
        fhInsuredPerson1.setSchoolType("schoolType");
        fhInsuredPerson1.setSchoolNature("schoolNature");
        fhInsuredPerson1.setSchoolName("schoolName");
        fhInsuredPerson1.setSchoolClass("schoolClass");
        fhInsuredPerson1.setStudentId("studentId");
        fhInsuredPerson1.setIdPeriodStart("idPeriodStart");
        fhInsuredPerson1.setIdPeriodEnd("idPeriodEnd");
        fhInsuredPerson1.setSmoke("smoke");
        fhInsuredPerson1.setOldPolicyNo("oldPolicyNo");
        final TestPremiumDutyForm testPremiumDutyForm2 = new TestPremiumDutyForm();
        fhInsuredPerson1.setDuties(Arrays.asList(testPremiumDutyForm2));
        final TestPremiumProductForm product1 = new TestPremiumProductForm();
        product1.setProductId(0);
        product1.setPlanId(0);
        product1.setPlanCode("planCode");
        product1.setPremium(new BigDecimal("0.00"));
        product1.setQty(0);
        final TestPremiumDutyForm testPremiumDutyForm3 = new TestPremiumDutyForm();
        product1.setPremiumDutyForms(Arrays.asList(testPremiumDutyForm3));
        fhInsuredPerson1.setProduct(product1);
        orderSubmitRequest1.setInsuredPerson(Arrays.asList(fhInsuredPerson1));
        final FhProperty propertyInfo1 = new FhProperty();
        propertyInfo1.setPropertyInfoIsExist("0");
        orderSubmitRequest1.setPropertyInfo(propertyInfo1);
        final SmOrderHouseDTO house1 = new SmOrderHouseDTO();
        orderSubmitRequest1.setHouse(house1);
        final SmOrderCarInfoDTO carInfo1 = new SmOrderCarInfoDTO();
        orderSubmitRequest1.setCarInfo(carInfo1);
        orderSubmitRequest1.setProductType("code");
        orderSubmitRequest1.setBizCode("bizCode");
        orderSubmitRequest1.setOrderOutType("orderOutType");
        orderSubmitRequest1.setToken("token");
        orderSubmitRequest1.setPlanId(0);
        orderSubmitRequest1.setQty(0);
        orderSubmitRequest1.setPreOrderId("preOrderId");
        orderSubmitRequest1.setRenew(false);
        orderSubmitRequest1.setRealRenewFlag(false);
        orderSubmitRequest1.setAppNo("appNo");
        orderSubmitRequest1.setAgentId(0);
        orderSubmitRequest1.setJobCode("jobCode");
        final SmCreateOrderSubmitRequest dto = new SmCreateOrderSubmitRequest(orderSubmitRequest1);

        // Confirm SmOrderMapper.insertOrderApplicant(...).
        final OrderSubmitRequest orderSubmitRequest2 = new OrderSubmitRequest();
        final FhProduct productInfo2 = new FhProduct();
        productInfo2.setProductId("fhProductId");
        productInfo2.setRecommendId("recommendId");
        productInfo2.setRecommendMainJobNumber("recommendMainJobNumber");
        productInfo2.setRecommendOrgCode("recommendOrgCode");
        orderSubmitRequest2.setProductInfo(productInfo2);
        final FhOrderInfo orderInfo2 = new FhOrderInfo();
        orderInfo2.setSubmitTime("submitTime");
        orderInfo2.setStartTime("startTime");
        orderInfo2.setEndTime("endTime");
        orderSubmitRequest2.setOrderInfo(orderInfo2);
        final FhProposer proposerInfo2 = new FhProposer();
        proposerInfo2.setPersonName("insuredName");
        proposerInfo2.setPersonGender("insuredPersonGender");
        proposerInfo2.setIdType("idType");
        proposerInfo2.setIdNumber("idNumber");
        proposerInfo2.setBirthday("birthday");
        proposerInfo2.setCellPhone("cellPhone");
        proposerInfo2.setEmail("email");
        proposerInfo2.setAddress("address");
        proposerInfo2.setAppStatus("-2");
        proposerInfo2.setPolicyNo("insIdNumber");
        proposerInfo2.setDownloadURL("downloadURL");
        orderSubmitRequest2.setProposerInfo(proposerInfo2);
        final FhInsuredPerson fhInsuredPerson2 = new FhInsuredPerson();
        fhInsuredPerson2.setPersonName("insuredName");
        fhInsuredPerson2.setIdType("idType");
        fhInsuredPerson2.setIdNumber("idNumber");
        fhInsuredPerson2.setBirthday("birthday");
        fhInsuredPerson2.setCellPhone("cellPhone");
        fhInsuredPerson2.setAppStatus("-2");
        fhInsuredPerson2.setPolicyNo("insIdNumber");
        fhInsuredPerson2.setRelationship("99");
        fhInsuredPerson2.setFlightNo("flightNo");
        fhInsuredPerson2.setFlightTime("flightTime");
        fhInsuredPerson2.setOccupationCode("");
        fhInsuredPerson2.setOccupationGroup("occupationGroup");
        fhInsuredPerson2.setDestinationCountryText("destinationCountryText");
        fhInsuredPerson2.setAnnualIncome("annualIncome");
        fhInsuredPerson2.setStudentType("studentType");
        fhInsuredPerson2.setSchoolType("schoolType");
        fhInsuredPerson2.setSchoolNature("schoolNature");
        fhInsuredPerson2.setSchoolName("schoolName");
        fhInsuredPerson2.setSchoolClass("schoolClass");
        fhInsuredPerson2.setStudentId("studentId");
        fhInsuredPerson2.setIdPeriodStart("idPeriodStart");
        fhInsuredPerson2.setIdPeriodEnd("idPeriodEnd");
        fhInsuredPerson2.setSmoke("smoke");
        fhInsuredPerson2.setOldPolicyNo("oldPolicyNo");
        final TestPremiumDutyForm testPremiumDutyForm4 = new TestPremiumDutyForm();
        fhInsuredPerson2.setDuties(Arrays.asList(testPremiumDutyForm4));
        final TestPremiumProductForm product2 = new TestPremiumProductForm();
        product2.setProductId(0);
        product2.setPlanId(0);
        product2.setPlanCode("planCode");
        product2.setPremium(new BigDecimal("0.00"));
        product2.setQty(0);
        final TestPremiumDutyForm testPremiumDutyForm5 = new TestPremiumDutyForm();
        product2.setPremiumDutyForms(Arrays.asList(testPremiumDutyForm5));
        fhInsuredPerson2.setProduct(product2);
        orderSubmitRequest2.setInsuredPerson(Arrays.asList(fhInsuredPerson2));
        final FhProperty propertyInfo2 = new FhProperty();
        propertyInfo2.setPropertyInfoIsExist("0");
        orderSubmitRequest2.setPropertyInfo(propertyInfo2);
        final SmOrderHouseDTO house2 = new SmOrderHouseDTO();
        orderSubmitRequest2.setHouse(house2);
        final SmOrderCarInfoDTO carInfo2 = new SmOrderCarInfoDTO();
        orderSubmitRequest2.setCarInfo(carInfo2);
        orderSubmitRequest2.setProductType("code");
        orderSubmitRequest2.setBizCode("bizCode");
        orderSubmitRequest2.setOrderOutType("orderOutType");
        orderSubmitRequest2.setToken("token");
        orderSubmitRequest2.setPlanId(0);
        orderSubmitRequest2.setQty(0);
        orderSubmitRequest2.setPreOrderId("preOrderId");
        orderSubmitRequest2.setRenew(false);
        orderSubmitRequest2.setRealRenewFlag(false);
        orderSubmitRequest2.setAppNo("appNo");
        orderSubmitRequest2.setAgentId(0);
        orderSubmitRequest2.setJobCode("jobCode");
        final SmCreateOrderSubmitRequest dto1 = new SmCreateOrderSubmitRequest(orderSubmitRequest2);

        // Confirm SmOrderMapper.insertOrderInsured(...).
        final OrderSubmitRequest orderSubmitRequest3 = new OrderSubmitRequest();
        final FhProduct productInfo3 = new FhProduct();
        productInfo3.setProductId("fhProductId");
        productInfo3.setRecommendId("recommendId");
        productInfo3.setRecommendMainJobNumber("recommendMainJobNumber");
        productInfo3.setRecommendOrgCode("recommendOrgCode");
        orderSubmitRequest3.setProductInfo(productInfo3);
        final FhOrderInfo orderInfo3 = new FhOrderInfo();
        orderInfo3.setSubmitTime("submitTime");
        orderInfo3.setStartTime("startTime");
        orderInfo3.setEndTime("endTime");
        orderSubmitRequest3.setOrderInfo(orderInfo3);
        final FhProposer proposerInfo3 = new FhProposer();
        proposerInfo3.setPersonName("insuredName");
        proposerInfo3.setPersonGender("insuredPersonGender");
        proposerInfo3.setIdType("idType");
        proposerInfo3.setIdNumber("idNumber");
        proposerInfo3.setBirthday("birthday");
        proposerInfo3.setCellPhone("cellPhone");
        proposerInfo3.setEmail("email");
        proposerInfo3.setAddress("address");
        proposerInfo3.setAppStatus("-2");
        proposerInfo3.setPolicyNo("insIdNumber");
        proposerInfo3.setDownloadURL("downloadURL");
        orderSubmitRequest3.setProposerInfo(proposerInfo3);
        final FhInsuredPerson fhInsuredPerson3 = new FhInsuredPerson();
        fhInsuredPerson3.setPersonName("insuredName");
        fhInsuredPerson3.setIdType("idType");
        fhInsuredPerson3.setIdNumber("idNumber");
        fhInsuredPerson3.setBirthday("birthday");
        fhInsuredPerson3.setCellPhone("cellPhone");
        fhInsuredPerson3.setAppStatus("-2");
        fhInsuredPerson3.setPolicyNo("insIdNumber");
        fhInsuredPerson3.setRelationship("99");
        fhInsuredPerson3.setFlightNo("flightNo");
        fhInsuredPerson3.setFlightTime("flightTime");
        fhInsuredPerson3.setOccupationCode("");
        fhInsuredPerson3.setOccupationGroup("occupationGroup");
        fhInsuredPerson3.setDestinationCountryText("destinationCountryText");
        fhInsuredPerson3.setAnnualIncome("annualIncome");
        fhInsuredPerson3.setStudentType("studentType");
        fhInsuredPerson3.setSchoolType("schoolType");
        fhInsuredPerson3.setSchoolNature("schoolNature");
        fhInsuredPerson3.setSchoolName("schoolName");
        fhInsuredPerson3.setSchoolClass("schoolClass");
        fhInsuredPerson3.setStudentId("studentId");
        fhInsuredPerson3.setIdPeriodStart("idPeriodStart");
        fhInsuredPerson3.setIdPeriodEnd("idPeriodEnd");
        fhInsuredPerson3.setSmoke("smoke");
        fhInsuredPerson3.setOldPolicyNo("oldPolicyNo");
        final TestPremiumDutyForm testPremiumDutyForm6 = new TestPremiumDutyForm();
        fhInsuredPerson3.setDuties(Arrays.asList(testPremiumDutyForm6));
        final TestPremiumProductForm product3 = new TestPremiumProductForm();
        product3.setProductId(0);
        product3.setPlanId(0);
        product3.setPlanCode("planCode");
        product3.setPremium(new BigDecimal("0.00"));
        product3.setQty(0);
        final TestPremiumDutyForm testPremiumDutyForm7 = new TestPremiumDutyForm();
        product3.setPremiumDutyForms(Arrays.asList(testPremiumDutyForm7));
        fhInsuredPerson3.setProduct(product3);
        orderSubmitRequest3.setInsuredPerson(Arrays.asList(fhInsuredPerson3));
        final FhProperty propertyInfo3 = new FhProperty();
        propertyInfo3.setPropertyInfoIsExist("0");
        orderSubmitRequest3.setPropertyInfo(propertyInfo3);
        final SmOrderHouseDTO house3 = new SmOrderHouseDTO();
        orderSubmitRequest3.setHouse(house3);
        final SmOrderCarInfoDTO carInfo3 = new SmOrderCarInfoDTO();
        orderSubmitRequest3.setCarInfo(carInfo3);
        orderSubmitRequest3.setProductType("code");
        orderSubmitRequest3.setBizCode("bizCode");
        orderSubmitRequest3.setOrderOutType("orderOutType");
        orderSubmitRequest3.setToken("token");
        orderSubmitRequest3.setPlanId(0);
        orderSubmitRequest3.setQty(0);
        orderSubmitRequest3.setPreOrderId("preOrderId");
        orderSubmitRequest3.setRenew(false);
        orderSubmitRequest3.setRealRenewFlag(false);
        orderSubmitRequest3.setAppNo("appNo");
        orderSubmitRequest3.setAgentId(0);
        orderSubmitRequest3.setJobCode("jobCode");
        final SmCreateOrderSubmitRequest dto2 = new SmCreateOrderSubmitRequest(orderSubmitRequest3);
    }

    @Test
    public void testSyncDetail_SmOrderMapperGetFhProductIdByOrderIdReturnsNoItems() throws Exception {
        // Setup
        // Configure AmPolicyTkMapper.selectByExample(...).
        final AmPolicyTk amPolicyTk = new AmPolicyTk();
        amPolicyTk.setPolicyNo("policyNo");
        amPolicyTk.setSubPolicyNo("subPolicyNo");
        amPolicyTk.setRiskName("riskName");
        amPolicyTk.setRiskCode("riskCode");
        amPolicyTk.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk.setPolicyUrl("policyDownLoadUrl");
        amPolicyTk.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk.setBenchmarkPremium(new BigDecimal("0.00"));
        amPolicyTk.setState(0);
        amPolicyTk.setRemark("msg");
        final List<AmPolicyTk> amPolicyTks = Arrays.asList(amPolicyTk);
        when(mockPolicyTkMapper.selectByExample(any(Object.class))).thenReturn(amPolicyTks);

        // Configure TkAutoServiceAdapter.queryTkDetail(...).
        final TkAutoRespHelper<TkAutoQueryPolicyDetailResp> tkAutoQueryPolicyDetailRespTkAutoRespHelper = new TkAutoRespHelper<>();
        tkAutoQueryPolicyDetailRespTkAutoRespHelper.setAppCode("1");
        tkAutoQueryPolicyDetailRespTkAutoRespHelper.setAppMessage("appMessage");
        final TkAutoQueryPolicyDetailResp tkAutoQueryPolicyDetailResp = new TkAutoQueryPolicyDetailResp();
        final TkAutoRiskInfoD tkAutoRiskInfoD = new TkAutoRiskInfoD();
        tkAutoRiskInfoD.setRiskCode("riskCode");
        final TkAutoBaseInfoD baseInfoD = new TkAutoBaseInfoD();
        baseInfoD.setSubPolicyNo("subPolicyNo");
        baseInfoD.setRiskName("riskName");
        baseInfoD.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD.setPremium(new BigDecimal("0.00"));
        baseInfoD.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TkAutoPolicyViewList policyViewList = new TkAutoPolicyViewList();
        final TkAutoPolicyData policyData = new TkAutoPolicyData();
        policyData.setPolicyDownLoadUrl("policyDownLoadUrl");
        policyViewList.setPolicyData(policyData);
        baseInfoD.setPolicyViewList(policyViewList);
        tkAutoRiskInfoD.setBaseInfoD(baseInfoD);
        final TkAutoRelatedInfoD tkAutoRelatedInfoD = new TkAutoRelatedInfoD();
        tkAutoRelatedInfoD.setInsureFlag("insureFlag");
        tkAutoRelatedInfoD.setInsureTypeCode("insureTypeCode");
        tkAutoRiskInfoD.setRelatedInfoDList(Arrays.asList(tkAutoRelatedInfoD));
        tkAutoQueryPolicyDetailResp.setRiskInfoList(Arrays.asList(tkAutoRiskInfoD));
        tkAutoQueryPolicyDetailRespTkAutoRespHelper.setData(tkAutoQueryPolicyDetailResp);
        tkAutoQueryPolicyDetailRespTkAutoRespHelper.setContent("content");
        when(mockAdapter.queryTkDetail("policyNo")).thenReturn(tkAutoQueryPolicyDetailRespTkAutoRespHelper);

        // Configure TkAutoCvtHelper.findDrivingTkAutoRiskInfoD(...).
        final TkAutoRiskInfoD tkAutoRiskInfoD1 = new TkAutoRiskInfoD();
        tkAutoRiskInfoD1.setRiskCode("riskCode");
        final TkAutoBaseInfoD baseInfoD1 = new TkAutoBaseInfoD();
        baseInfoD1.setSubPolicyNo("subPolicyNo");
        baseInfoD1.setRiskName("riskName");
        baseInfoD1.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD1.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD1.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD1.setPremium(new BigDecimal("0.00"));
        baseInfoD1.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TkAutoPolicyViewList policyViewList1 = new TkAutoPolicyViewList();
        final TkAutoPolicyData policyData1 = new TkAutoPolicyData();
        policyData1.setPolicyDownLoadUrl("policyDownLoadUrl");
        policyViewList1.setPolicyData(policyData1);
        baseInfoD1.setPolicyViewList(policyViewList1);
        tkAutoRiskInfoD1.setBaseInfoD(baseInfoD1);
        final TkAutoRelatedInfoD tkAutoRelatedInfoD1 = new TkAutoRelatedInfoD();
        tkAutoRelatedInfoD1.setInsureFlag("insureFlag");
        tkAutoRelatedInfoD1.setInsureTypeCode("insureTypeCode");
        tkAutoRiskInfoD1.setRelatedInfoDList(Arrays.asList(tkAutoRelatedInfoD1));
        final TkAutoRiskInfoD tkAutoRiskInfoD2 = new TkAutoRiskInfoD();
        tkAutoRiskInfoD2.setRiskCode("riskCode");
        final TkAutoBaseInfoD baseInfoD2 = new TkAutoBaseInfoD();
        baseInfoD2.setSubPolicyNo("subPolicyNo");
        baseInfoD2.setRiskName("riskName");
        baseInfoD2.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD2.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD2.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD2.setPremium(new BigDecimal("0.00"));
        baseInfoD2.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TkAutoPolicyViewList policyViewList2 = new TkAutoPolicyViewList();
        final TkAutoPolicyData policyData2 = new TkAutoPolicyData();
        policyData2.setPolicyDownLoadUrl("policyDownLoadUrl");
        policyViewList2.setPolicyData(policyData2);
        baseInfoD2.setPolicyViewList(policyViewList2);
        tkAutoRiskInfoD2.setBaseInfoD(baseInfoD2);
        final TkAutoRelatedInfoD tkAutoRelatedInfoD2 = new TkAutoRelatedInfoD();
        tkAutoRelatedInfoD2.setInsureFlag("insureFlag");
        tkAutoRelatedInfoD2.setInsureTypeCode("insureTypeCode");
        tkAutoRiskInfoD2.setRelatedInfoDList(Arrays.asList(tkAutoRelatedInfoD2));
        final List<TkAutoRiskInfoD> tkAutoRiskInfoDList = Arrays.asList(tkAutoRiskInfoD2);
        when(mockCvtHelper.findDrivingTkAutoRiskInfoD(tkAutoRiskInfoDList)).thenReturn(tkAutoRiskInfoD1);

        // Configure TkAutoCvtHelper.getRelatedInfoDList(...).
        final TkAutoRelatedInfoD tkAutoRelatedInfoD3 = new TkAutoRelatedInfoD();
        tkAutoRelatedInfoD3.setInsuredName("insuredName");
        tkAutoRelatedInfoD3.setInsureFlag("insureFlag");
        tkAutoRelatedInfoD3.setInsureTypeCode("insureTypeCode");
        tkAutoRelatedInfoD3.setInsureType("insureType");
        tkAutoRelatedInfoD3.setCertiType("certiType");
        final List<TkAutoRelatedInfoD> tkAutoRelatedInfoDS = Arrays.asList(tkAutoRelatedInfoD3);
        final TkAutoRiskInfoD tkAutoRiskInfoD3 = new TkAutoRiskInfoD();
        tkAutoRiskInfoD3.setRiskCode("riskCode");
        final TkAutoBaseInfoD baseInfoD3 = new TkAutoBaseInfoD();
        baseInfoD3.setSubPolicyNo("subPolicyNo");
        baseInfoD3.setRiskName("riskName");
        baseInfoD3.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD3.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD3.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD3.setPremium(new BigDecimal("0.00"));
        baseInfoD3.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TkAutoPolicyViewList policyViewList3 = new TkAutoPolicyViewList();
        final TkAutoPolicyData policyData3 = new TkAutoPolicyData();
        policyData3.setPolicyDownLoadUrl("policyDownLoadUrl");
        policyViewList3.setPolicyData(policyData3);
        baseInfoD3.setPolicyViewList(policyViewList3);
        tkAutoRiskInfoD3.setBaseInfoD(baseInfoD3);
        final TkAutoRelatedInfoD tkAutoRelatedInfoD4 = new TkAutoRelatedInfoD();
        tkAutoRelatedInfoD4.setInsureFlag("insureFlag");
        tkAutoRelatedInfoD4.setInsureTypeCode("insureTypeCode");
        tkAutoRiskInfoD3.setRelatedInfoDList(Arrays.asList(tkAutoRelatedInfoD4));
        final List<TkAutoRiskInfoD> tkAutoRiskInfoDList1 = Arrays.asList(tkAutoRiskInfoD3);
        when(mockCvtHelper.getRelatedInfoDList(tkAutoRiskInfoDList1)).thenReturn(tkAutoRelatedInfoDS);

        // Configure AmPolicyTkMapper.selectOneByPolicyNo(...).
        final AmPolicyTk amPolicyTk1 = new AmPolicyTk();
        amPolicyTk1.setPolicyNo("policyNo");
        amPolicyTk1.setSubPolicyNo("subPolicyNo");
        amPolicyTk1.setRiskName("riskName");
        amPolicyTk1.setRiskCode("riskCode");
        amPolicyTk1.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk1.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk1.setPolicyUrl("policyDownLoadUrl");
        amPolicyTk1.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk1.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk1.setBenchmarkPremium(new BigDecimal("0.00"));
        amPolicyTk1.setState(0);
        amPolicyTk1.setRemark("msg");
        when(mockPolicyTkMapper.selectOneByPolicyNo("policyNo")).thenReturn(amPolicyTk1);

        // Configure TkAutoCvtHelper.cvtOrderInfo(...).
        final OrderSubmitRequest orderSubmitRequest = new OrderSubmitRequest();
        final FhProduct productInfo = new FhProduct();
        productInfo.setProductId("fhProductId");
        productInfo.setRecommendId("recommendId");
        productInfo.setRecommendMainJobNumber("recommendMainJobNumber");
        productInfo.setRecommendOrgCode("recommendOrgCode");
        orderSubmitRequest.setProductInfo(productInfo);
        final FhOrderInfo orderInfo = new FhOrderInfo();
        orderInfo.setSubmitTime("submitTime");
        orderInfo.setStartTime("startTime");
        orderInfo.setEndTime("endTime");
        orderSubmitRequest.setOrderInfo(orderInfo);
        final FhProposer proposerInfo = new FhProposer();
        proposerInfo.setPersonName("insuredName");
        proposerInfo.setPersonGender("insuredPersonGender");
        proposerInfo.setIdType("idType");
        proposerInfo.setIdNumber("idNumber");
        proposerInfo.setBirthday("birthday");
        proposerInfo.setCellPhone("cellPhone");
        proposerInfo.setEmail("email");
        proposerInfo.setAddress("address");
        proposerInfo.setAppStatus("-2");
        proposerInfo.setPolicyNo("insIdNumber");
        proposerInfo.setDownloadURL("downloadURL");
        orderSubmitRequest.setProposerInfo(proposerInfo);
        final FhInsuredPerson fhInsuredPerson = new FhInsuredPerson();
        fhInsuredPerson.setPersonName("insuredName");
        fhInsuredPerson.setIdType("idType");
        fhInsuredPerson.setIdNumber("idNumber");
        fhInsuredPerson.setBirthday("birthday");
        fhInsuredPerson.setCellPhone("cellPhone");
        fhInsuredPerson.setAppStatus("-2");
        fhInsuredPerson.setPolicyNo("insIdNumber");
        fhInsuredPerson.setRelationship("99");
        fhInsuredPerson.setFlightNo("flightNo");
        fhInsuredPerson.setFlightTime("flightTime");
        fhInsuredPerson.setOccupationCode("");
        fhInsuredPerson.setOccupationGroup("occupationGroup");
        fhInsuredPerson.setDestinationCountryText("destinationCountryText");
        fhInsuredPerson.setAnnualIncome("annualIncome");
        fhInsuredPerson.setStudentType("studentType");
        fhInsuredPerson.setSchoolType("schoolType");
        fhInsuredPerson.setSchoolNature("schoolNature");
        fhInsuredPerson.setSchoolName("schoolName");
        fhInsuredPerson.setSchoolClass("schoolClass");
        fhInsuredPerson.setStudentId("studentId");
        fhInsuredPerson.setIdPeriodStart("idPeriodStart");
        fhInsuredPerson.setIdPeriodEnd("idPeriodEnd");
        fhInsuredPerson.setSmoke("smoke");
        fhInsuredPerson.setOldPolicyNo("oldPolicyNo");
        final TestPremiumDutyForm testPremiumDutyForm = new TestPremiumDutyForm();
        fhInsuredPerson.setDuties(Arrays.asList(testPremiumDutyForm));
        final TestPremiumProductForm product = new TestPremiumProductForm();
        product.setProductId(0);
        product.setPlanId(0);
        product.setPlanCode("planCode");
        product.setPremium(new BigDecimal("0.00"));
        product.setQty(0);
        final TestPremiumDutyForm testPremiumDutyForm1 = new TestPremiumDutyForm();
        product.setPremiumDutyForms(Arrays.asList(testPremiumDutyForm1));
        fhInsuredPerson.setProduct(product);
        orderSubmitRequest.setInsuredPerson(Arrays.asList(fhInsuredPerson));
        final FhProperty propertyInfo = new FhProperty();
        propertyInfo.setPropertyInfoIsExist("0");
        orderSubmitRequest.setPropertyInfo(propertyInfo);
        final SmOrderHouseDTO house = new SmOrderHouseDTO();
        orderSubmitRequest.setHouse(house);
        final SmOrderCarInfoDTO carInfo = new SmOrderCarInfoDTO();
        orderSubmitRequest.setCarInfo(carInfo);
        orderSubmitRequest.setProductType("code");
        orderSubmitRequest.setBizCode("bizCode");
        orderSubmitRequest.setOrderOutType("orderOutType");
        orderSubmitRequest.setToken("token");
        orderSubmitRequest.setPlanId(0);
        orderSubmitRequest.setQty(0);
        orderSubmitRequest.setPreOrderId("preOrderId");
        orderSubmitRequest.setRenew(false);
        orderSubmitRequest.setRealRenewFlag(false);
        orderSubmitRequest.setAppNo("appNo");
        orderSubmitRequest.setAgentId(0);
        orderSubmitRequest.setJobCode("jobCode");
        final List<SmCreateOrderSubmitRequest> smCreateOrderSubmitRequests = Arrays.asList(new SmCreateOrderSubmitRequest(orderSubmitRequest));
        final AmPolicyTk tk = new AmPolicyTk();
        tk.setPolicyNo("policyNo");
        tk.setSubPolicyNo("subPolicyNo");
        tk.setRiskName("riskName");
        tk.setRiskCode("riskCode");
        tk.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        tk.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        tk.setPolicyUrl("policyDownLoadUrl");
        tk.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        tk.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        tk.setBenchmarkPremium(new BigDecimal("0.00"));
        tk.setState(0);
        tk.setRemark("msg");
        final TkAutoQueryPolicyDetailResp detailResp = new TkAutoQueryPolicyDetailResp();
        final TkAutoRiskInfoD tkAutoRiskInfoD4 = new TkAutoRiskInfoD();
        tkAutoRiskInfoD4.setRiskCode("riskCode");
        final TkAutoBaseInfoD baseInfoD4 = new TkAutoBaseInfoD();
        baseInfoD4.setSubPolicyNo("subPolicyNo");
        baseInfoD4.setRiskName("riskName");
        baseInfoD4.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD4.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD4.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD4.setPremium(new BigDecimal("0.00"));
        baseInfoD4.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TkAutoPolicyViewList policyViewList4 = new TkAutoPolicyViewList();
        final TkAutoPolicyData policyData4 = new TkAutoPolicyData();
        policyData4.setPolicyDownLoadUrl("policyDownLoadUrl");
        policyViewList4.setPolicyData(policyData4);
        baseInfoD4.setPolicyViewList(policyViewList4);
        tkAutoRiskInfoD4.setBaseInfoD(baseInfoD4);
        final TkAutoRelatedInfoD tkAutoRelatedInfoD5 = new TkAutoRelatedInfoD();
        tkAutoRelatedInfoD5.setInsureFlag("insureFlag");
        tkAutoRelatedInfoD5.setInsureTypeCode("insureTypeCode");
        tkAutoRiskInfoD4.setRelatedInfoDList(Arrays.asList(tkAutoRelatedInfoD5));
        detailResp.setRiskInfoList(Arrays.asList(tkAutoRiskInfoD4));
        when(mockCvtHelper.cvtOrderInfo(tk, detailResp, new HashMap<>())).thenReturn(smCreateOrderSubmitRequests);

        // Configure TkAutoCvtHelper.cvtAutoInfoList(...).
        final AutoOrderDTO autoOrderDTO = new AutoOrderDTO();
        autoOrderDTO.setOrderNo("orderNo");
        final AutoOrder order = new AutoOrder();
        order.setPayStatus("-1");
        order.setPaymentTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        order.setCommissionId(0);
        autoOrderDTO.setOrder(order);
        final AutoOrderPerson autoOrderPerson = new AutoOrderPerson();
        autoOrderDTO.setPeople(Arrays.asList(autoOrderPerson));
        final AutoOrderPolicyDTO autoOrderPolicyDTO = new AutoOrderPolicyDTO();
        final AutoOrderPolicy policy = new AutoOrderPolicy();
        policy.setPolicyNo("policyNo");
        policy.setSubPolicyNo("subPolicyNo");
        policy.setPolicyState("-2");
        policy.setBusinessScore("businessScore");
        autoOrderPolicyDTO.setPolicy(policy);
        final AutoOrderRisk autoOrderRisk = new AutoOrderRisk();
        autoOrderRisk.setRiskName("riskName");
        autoOrderRisk.setPremium(new BigDecimal("0.00"));
        autoOrderRisk.setAmount(new BigDecimal("0.00"));
        autoOrderPolicyDTO.setRiskList(Arrays.asList(autoOrderRisk));
        autoOrderDTO.setPolicies(Arrays.asList(autoOrderPolicyDTO));
        final AutoOrderExtend extend = new AutoOrderExtend();
        autoOrderDTO.setExtend(extend);
        final AutoOrderCar car = new AutoOrderCar();
        car.setOwnerPersonName("ownerPersonName");
        car.setOwnerPersonGender("ownerPersonGender");
        car.setOwnerIdType("ownerIdType");
        car.setOwnerIdNumber("ownerIdNumber");
        car.setFirstRegDate("firstRegDate");
        car.setFullLoad("fullLoad");
        car.setTransferDate("transferDate");
        car.setSeatCnt("approvedNum");
        car.setPrice("price");
        car.setCarUserType("carUserType");
        car.setDisplacement("displacement");
        car.setIsNewCar("isNewCar");
        autoOrderDTO.setCar(car);
        final List<AutoOrderDTO> autoOrderDTOS = Arrays.asList(autoOrderDTO);
        final AmPolicyTk tk1 = new AmPolicyTk();
        tk1.setPolicyNo("policyNo");
        tk1.setSubPolicyNo("subPolicyNo");
        tk1.setRiskName("riskName");
        tk1.setRiskCode("riskCode");
        tk1.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        tk1.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        tk1.setPolicyUrl("policyDownLoadUrl");
        tk1.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        tk1.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        tk1.setBenchmarkPremium(new BigDecimal("0.00"));
        tk1.setState(0);
        tk1.setRemark("msg");
        final TkAutoQueryPolicyDetailResp detailResp1 = new TkAutoQueryPolicyDetailResp();
        final TkAutoRiskInfoD tkAutoRiskInfoD5 = new TkAutoRiskInfoD();
        tkAutoRiskInfoD5.setRiskCode("riskCode");
        final TkAutoBaseInfoD baseInfoD5 = new TkAutoBaseInfoD();
        baseInfoD5.setSubPolicyNo("subPolicyNo");
        baseInfoD5.setRiskName("riskName");
        baseInfoD5.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD5.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD5.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD5.setPremium(new BigDecimal("0.00"));
        baseInfoD5.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TkAutoPolicyViewList policyViewList5 = new TkAutoPolicyViewList();
        final TkAutoPolicyData policyData5 = new TkAutoPolicyData();
        policyData5.setPolicyDownLoadUrl("policyDownLoadUrl");
        policyViewList5.setPolicyData(policyData5);
        baseInfoD5.setPolicyViewList(policyViewList5);
        tkAutoRiskInfoD5.setBaseInfoD(baseInfoD5);
        final TkAutoRelatedInfoD tkAutoRelatedInfoD6 = new TkAutoRelatedInfoD();
        tkAutoRelatedInfoD6.setInsureFlag("insureFlag");
        tkAutoRelatedInfoD6.setInsureTypeCode("insureTypeCode");
        tkAutoRiskInfoD5.setRelatedInfoDList(Arrays.asList(tkAutoRelatedInfoD6));
        detailResp1.setRiskInfoList(Arrays.asList(tkAutoRiskInfoD5));
        when(mockCvtHelper.cvtAutoInfoList(tk1, Arrays.asList("value"), detailResp1, new HashMap<>())).thenReturn(autoOrderDTOS);

        when(mockOrderMapper.updateOrderPaymentTime("orderId")).thenReturn(0);
        when(mockOrderMapper.getFhProductIdByOrderId("orderId")).thenReturn(Collections.emptyList());

        // Run the test
        tkAutoOrderServiceUnderTest.syncDetail();

        // Verify the results
        // Confirm AmPolicyTkMapper.updateByExampleSelective(...).
        final AmPolicyTk record = new AmPolicyTk();
        record.setPolicyNo("policyNo");
        record.setSubPolicyNo("subPolicyNo");
        record.setRiskName("riskName");
        record.setRiskCode("riskCode");
        record.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record.setPolicyUrl("policyDownLoadUrl");
        record.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record.setBenchmarkPremium(new BigDecimal("0.00"));
        record.setState(0);
        record.setRemark("msg");

        // Confirm AmPolicyTkMapper.insertUseGeneratedKeys(...).
        final AmPolicyTk record1 = new AmPolicyTk();
        record1.setPolicyNo("policyNo");
        record1.setSubPolicyNo("subPolicyNo");
        record1.setRiskName("riskName");
        record1.setRiskCode("riskCode");
        record1.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record1.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record1.setPolicyUrl("policyDownLoadUrl");
        record1.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record1.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record1.setBenchmarkPremium(new BigDecimal("0.00"));
        record1.setState(0);
        record1.setRemark("msg");

        // Confirm AmPolicyTkDetailMapper.insertUseGeneratedKeys(...).
        final AmPolicyTkDetail record2 = new AmPolicyTkDetail();
        record2.setPolicyNo("policyNo");
        record2.setContent("content");

        // Confirm SmOrderMapper.insertOrder(...).
        final OrderSubmitRequest orderSubmitRequest1 = new OrderSubmitRequest();
        final FhProduct productInfo1 = new FhProduct();
        productInfo1.setProductId("fhProductId");
        productInfo1.setRecommendId("recommendId");
        productInfo1.setRecommendMainJobNumber("recommendMainJobNumber");
        productInfo1.setRecommendOrgCode("recommendOrgCode");
        orderSubmitRequest1.setProductInfo(productInfo1);
        final FhOrderInfo orderInfo1 = new FhOrderInfo();
        orderInfo1.setSubmitTime("submitTime");
        orderInfo1.setStartTime("startTime");
        orderInfo1.setEndTime("endTime");
        orderSubmitRequest1.setOrderInfo(orderInfo1);
        final FhProposer proposerInfo1 = new FhProposer();
        proposerInfo1.setPersonName("insuredName");
        proposerInfo1.setPersonGender("insuredPersonGender");
        proposerInfo1.setIdType("idType");
        proposerInfo1.setIdNumber("idNumber");
        proposerInfo1.setBirthday("birthday");
        proposerInfo1.setCellPhone("cellPhone");
        proposerInfo1.setEmail("email");
        proposerInfo1.setAddress("address");
        proposerInfo1.setAppStatus("-2");
        proposerInfo1.setPolicyNo("insIdNumber");
        proposerInfo1.setDownloadURL("downloadURL");
        orderSubmitRequest1.setProposerInfo(proposerInfo1);
        final FhInsuredPerson fhInsuredPerson1 = new FhInsuredPerson();
        fhInsuredPerson1.setPersonName("insuredName");
        fhInsuredPerson1.setIdType("idType");
        fhInsuredPerson1.setIdNumber("idNumber");
        fhInsuredPerson1.setBirthday("birthday");
        fhInsuredPerson1.setCellPhone("cellPhone");
        fhInsuredPerson1.setAppStatus("-2");
        fhInsuredPerson1.setPolicyNo("insIdNumber");
        fhInsuredPerson1.setRelationship("99");
        fhInsuredPerson1.setFlightNo("flightNo");
        fhInsuredPerson1.setFlightTime("flightTime");
        fhInsuredPerson1.setOccupationCode("");
        fhInsuredPerson1.setOccupationGroup("occupationGroup");
        fhInsuredPerson1.setDestinationCountryText("destinationCountryText");
        fhInsuredPerson1.setAnnualIncome("annualIncome");
        fhInsuredPerson1.setStudentType("studentType");
        fhInsuredPerson1.setSchoolType("schoolType");
        fhInsuredPerson1.setSchoolNature("schoolNature");
        fhInsuredPerson1.setSchoolName("schoolName");
        fhInsuredPerson1.setSchoolClass("schoolClass");
        fhInsuredPerson1.setStudentId("studentId");
        fhInsuredPerson1.setIdPeriodStart("idPeriodStart");
        fhInsuredPerson1.setIdPeriodEnd("idPeriodEnd");
        fhInsuredPerson1.setSmoke("smoke");
        fhInsuredPerson1.setOldPolicyNo("oldPolicyNo");
        final TestPremiumDutyForm testPremiumDutyForm2 = new TestPremiumDutyForm();
        fhInsuredPerson1.setDuties(Arrays.asList(testPremiumDutyForm2));
        final TestPremiumProductForm product1 = new TestPremiumProductForm();
        product1.setProductId(0);
        product1.setPlanId(0);
        product1.setPlanCode("planCode");
        product1.setPremium(new BigDecimal("0.00"));
        product1.setQty(0);
        final TestPremiumDutyForm testPremiumDutyForm3 = new TestPremiumDutyForm();
        product1.setPremiumDutyForms(Arrays.asList(testPremiumDutyForm3));
        fhInsuredPerson1.setProduct(product1);
        orderSubmitRequest1.setInsuredPerson(Arrays.asList(fhInsuredPerson1));
        final FhProperty propertyInfo1 = new FhProperty();
        propertyInfo1.setPropertyInfoIsExist("0");
        orderSubmitRequest1.setPropertyInfo(propertyInfo1);
        final SmOrderHouseDTO house1 = new SmOrderHouseDTO();
        orderSubmitRequest1.setHouse(house1);
        final SmOrderCarInfoDTO carInfo1 = new SmOrderCarInfoDTO();
        orderSubmitRequest1.setCarInfo(carInfo1);
        orderSubmitRequest1.setProductType("code");
        orderSubmitRequest1.setBizCode("bizCode");
        orderSubmitRequest1.setOrderOutType("orderOutType");
        orderSubmitRequest1.setToken("token");
        orderSubmitRequest1.setPlanId(0);
        orderSubmitRequest1.setQty(0);
        orderSubmitRequest1.setPreOrderId("preOrderId");
        orderSubmitRequest1.setRenew(false);
        orderSubmitRequest1.setRealRenewFlag(false);
        orderSubmitRequest1.setAppNo("appNo");
        orderSubmitRequest1.setAgentId(0);
        orderSubmitRequest1.setJobCode("jobCode");
        final SmCreateOrderSubmitRequest dto = new SmCreateOrderSubmitRequest(orderSubmitRequest1);

        // Confirm SmOrderMapper.insertOrderApplicant(...).
        final OrderSubmitRequest orderSubmitRequest2 = new OrderSubmitRequest();
        final FhProduct productInfo2 = new FhProduct();
        productInfo2.setProductId("fhProductId");
        productInfo2.setRecommendId("recommendId");
        productInfo2.setRecommendMainJobNumber("recommendMainJobNumber");
        productInfo2.setRecommendOrgCode("recommendOrgCode");
        orderSubmitRequest2.setProductInfo(productInfo2);
        final FhOrderInfo orderInfo2 = new FhOrderInfo();
        orderInfo2.setSubmitTime("submitTime");
        orderInfo2.setStartTime("startTime");
        orderInfo2.setEndTime("endTime");
        orderSubmitRequest2.setOrderInfo(orderInfo2);
        final FhProposer proposerInfo2 = new FhProposer();
        proposerInfo2.setPersonName("insuredName");
        proposerInfo2.setPersonGender("insuredPersonGender");
        proposerInfo2.setIdType("idType");
        proposerInfo2.setIdNumber("idNumber");
        proposerInfo2.setBirthday("birthday");
        proposerInfo2.setCellPhone("cellPhone");
        proposerInfo2.setEmail("email");
        proposerInfo2.setAddress("address");
        proposerInfo2.setAppStatus("-2");
        proposerInfo2.setPolicyNo("insIdNumber");
        proposerInfo2.setDownloadURL("downloadURL");
        orderSubmitRequest2.setProposerInfo(proposerInfo2);
        final FhInsuredPerson fhInsuredPerson2 = new FhInsuredPerson();
        fhInsuredPerson2.setPersonName("insuredName");
        fhInsuredPerson2.setIdType("idType");
        fhInsuredPerson2.setIdNumber("idNumber");
        fhInsuredPerson2.setBirthday("birthday");
        fhInsuredPerson2.setCellPhone("cellPhone");
        fhInsuredPerson2.setAppStatus("-2");
        fhInsuredPerson2.setPolicyNo("insIdNumber");
        fhInsuredPerson2.setRelationship("99");
        fhInsuredPerson2.setFlightNo("flightNo");
        fhInsuredPerson2.setFlightTime("flightTime");
        fhInsuredPerson2.setOccupationCode("");
        fhInsuredPerson2.setOccupationGroup("occupationGroup");
        fhInsuredPerson2.setDestinationCountryText("destinationCountryText");
        fhInsuredPerson2.setAnnualIncome("annualIncome");
        fhInsuredPerson2.setStudentType("studentType");
        fhInsuredPerson2.setSchoolType("schoolType");
        fhInsuredPerson2.setSchoolNature("schoolNature");
        fhInsuredPerson2.setSchoolName("schoolName");
        fhInsuredPerson2.setSchoolClass("schoolClass");
        fhInsuredPerson2.setStudentId("studentId");
        fhInsuredPerson2.setIdPeriodStart("idPeriodStart");
        fhInsuredPerson2.setIdPeriodEnd("idPeriodEnd");
        fhInsuredPerson2.setSmoke("smoke");
        fhInsuredPerson2.setOldPolicyNo("oldPolicyNo");
        final TestPremiumDutyForm testPremiumDutyForm4 = new TestPremiumDutyForm();
        fhInsuredPerson2.setDuties(Arrays.asList(testPremiumDutyForm4));
        final TestPremiumProductForm product2 = new TestPremiumProductForm();
        product2.setProductId(0);
        product2.setPlanId(0);
        product2.setPlanCode("planCode");
        product2.setPremium(new BigDecimal("0.00"));
        product2.setQty(0);
        final TestPremiumDutyForm testPremiumDutyForm5 = new TestPremiumDutyForm();
        product2.setPremiumDutyForms(Arrays.asList(testPremiumDutyForm5));
        fhInsuredPerson2.setProduct(product2);
        orderSubmitRequest2.setInsuredPerson(Arrays.asList(fhInsuredPerson2));
        final FhProperty propertyInfo2 = new FhProperty();
        propertyInfo2.setPropertyInfoIsExist("0");
        orderSubmitRequest2.setPropertyInfo(propertyInfo2);
        final SmOrderHouseDTO house2 = new SmOrderHouseDTO();
        orderSubmitRequest2.setHouse(house2);
        final SmOrderCarInfoDTO carInfo2 = new SmOrderCarInfoDTO();
        orderSubmitRequest2.setCarInfo(carInfo2);
        orderSubmitRequest2.setProductType("code");
        orderSubmitRequest2.setBizCode("bizCode");
        orderSubmitRequest2.setOrderOutType("orderOutType");
        orderSubmitRequest2.setToken("token");
        orderSubmitRequest2.setPlanId(0);
        orderSubmitRequest2.setQty(0);
        orderSubmitRequest2.setPreOrderId("preOrderId");
        orderSubmitRequest2.setRenew(false);
        orderSubmitRequest2.setRealRenewFlag(false);
        orderSubmitRequest2.setAppNo("appNo");
        orderSubmitRequest2.setAgentId(0);
        orderSubmitRequest2.setJobCode("jobCode");
        final SmCreateOrderSubmitRequest dto1 = new SmCreateOrderSubmitRequest(orderSubmitRequest2);

        // Confirm SmOrderMapper.insertOrderInsured(...).
        final OrderSubmitRequest orderSubmitRequest3 = new OrderSubmitRequest();
        final FhProduct productInfo3 = new FhProduct();
        productInfo3.setProductId("fhProductId");
        productInfo3.setRecommendId("recommendId");
        productInfo3.setRecommendMainJobNumber("recommendMainJobNumber");
        productInfo3.setRecommendOrgCode("recommendOrgCode");
        orderSubmitRequest3.setProductInfo(productInfo3);
        final FhOrderInfo orderInfo3 = new FhOrderInfo();
        orderInfo3.setSubmitTime("submitTime");
        orderInfo3.setStartTime("startTime");
        orderInfo3.setEndTime("endTime");
        orderSubmitRequest3.setOrderInfo(orderInfo3);
        final FhProposer proposerInfo3 = new FhProposer();
        proposerInfo3.setPersonName("insuredName");
        proposerInfo3.setPersonGender("insuredPersonGender");
        proposerInfo3.setIdType("idType");
        proposerInfo3.setIdNumber("idNumber");
        proposerInfo3.setBirthday("birthday");
        proposerInfo3.setCellPhone("cellPhone");
        proposerInfo3.setEmail("email");
        proposerInfo3.setAddress("address");
        proposerInfo3.setAppStatus("-2");
        proposerInfo3.setPolicyNo("insIdNumber");
        proposerInfo3.setDownloadURL("downloadURL");
        orderSubmitRequest3.setProposerInfo(proposerInfo3);
        final FhInsuredPerson fhInsuredPerson3 = new FhInsuredPerson();
        fhInsuredPerson3.setPersonName("insuredName");
        fhInsuredPerson3.setIdType("idType");
        fhInsuredPerson3.setIdNumber("idNumber");
        fhInsuredPerson3.setBirthday("birthday");
        fhInsuredPerson3.setCellPhone("cellPhone");
        fhInsuredPerson3.setAppStatus("-2");
        fhInsuredPerson3.setPolicyNo("insIdNumber");
        fhInsuredPerson3.setRelationship("99");
        fhInsuredPerson3.setFlightNo("flightNo");
        fhInsuredPerson3.setFlightTime("flightTime");
        fhInsuredPerson3.setOccupationCode("");
        fhInsuredPerson3.setOccupationGroup("occupationGroup");
        fhInsuredPerson3.setDestinationCountryText("destinationCountryText");
        fhInsuredPerson3.setAnnualIncome("annualIncome");
        fhInsuredPerson3.setStudentType("studentType");
        fhInsuredPerson3.setSchoolType("schoolType");
        fhInsuredPerson3.setSchoolNature("schoolNature");
        fhInsuredPerson3.setSchoolName("schoolName");
        fhInsuredPerson3.setSchoolClass("schoolClass");
        fhInsuredPerson3.setStudentId("studentId");
        fhInsuredPerson3.setIdPeriodStart("idPeriodStart");
        fhInsuredPerson3.setIdPeriodEnd("idPeriodEnd");
        fhInsuredPerson3.setSmoke("smoke");
        fhInsuredPerson3.setOldPolicyNo("oldPolicyNo");
        final TestPremiumDutyForm testPremiumDutyForm6 = new TestPremiumDutyForm();
        fhInsuredPerson3.setDuties(Arrays.asList(testPremiumDutyForm6));
        final TestPremiumProductForm product3 = new TestPremiumProductForm();
        product3.setProductId(0);
        product3.setPlanId(0);
        product3.setPlanCode("planCode");
        product3.setPremium(new BigDecimal("0.00"));
        product3.setQty(0);
        final TestPremiumDutyForm testPremiumDutyForm7 = new TestPremiumDutyForm();
        product3.setPremiumDutyForms(Arrays.asList(testPremiumDutyForm7));
        fhInsuredPerson3.setProduct(product3);
        orderSubmitRequest3.setInsuredPerson(Arrays.asList(fhInsuredPerson3));
        final FhProperty propertyInfo3 = new FhProperty();
        propertyInfo3.setPropertyInfoIsExist("0");
        orderSubmitRequest3.setPropertyInfo(propertyInfo3);
        final SmOrderHouseDTO house3 = new SmOrderHouseDTO();
        orderSubmitRequest3.setHouse(house3);
        final SmOrderCarInfoDTO carInfo3 = new SmOrderCarInfoDTO();
        orderSubmitRequest3.setCarInfo(carInfo3);
        orderSubmitRequest3.setProductType("code");
        orderSubmitRequest3.setBizCode("bizCode");
        orderSubmitRequest3.setOrderOutType("orderOutType");
        orderSubmitRequest3.setToken("token");
        orderSubmitRequest3.setPlanId(0);
        orderSubmitRequest3.setQty(0);
        orderSubmitRequest3.setPreOrderId("preOrderId");
        orderSubmitRequest3.setRenew(false);
        orderSubmitRequest3.setRealRenewFlag(false);
        orderSubmitRequest3.setAppNo("appNo");
        orderSubmitRequest3.setAgentId(0);
        orderSubmitRequest3.setJobCode("jobCode");
        final SmCreateOrderSubmitRequest dto2 = new SmCreateOrderSubmitRequest(orderSubmitRequest3);
    }

    @Test
    public void testSyncError() throws Exception {
        // Setup
        // Run the test
        tkAutoOrderServiceUnderTest.syncError("policyNo", "msg");

    }

    @Test
    public void testSyncSuccess() throws Exception {
        // Setup
        final TkAutoQueryPolicyDetailResp tkDetail = new TkAutoQueryPolicyDetailResp();
        final TkAutoRiskInfoD tkAutoRiskInfoD = new TkAutoRiskInfoD();
        tkAutoRiskInfoD.setRiskCode("riskCode");
        final TkAutoBaseInfoD baseInfoD = new TkAutoBaseInfoD();
        baseInfoD.setSubPolicyNo("subPolicyNo");
        baseInfoD.setRiskName("riskName");
        baseInfoD.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD.setPremium(new BigDecimal("0.00"));
        baseInfoD.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TkAutoPolicyViewList policyViewList = new TkAutoPolicyViewList();
        final TkAutoPolicyData policyData = new TkAutoPolicyData();
        policyData.setPolicyDownLoadUrl("policyDownLoadUrl");
        policyViewList.setPolicyData(policyData);
        baseInfoD.setPolicyViewList(policyViewList);
        tkAutoRiskInfoD.setBaseInfoD(baseInfoD);
        final TkAutoRelatedInfoD tkAutoRelatedInfoD = new TkAutoRelatedInfoD();
        tkAutoRelatedInfoD.setInsureFlag("insureFlag");
        tkAutoRelatedInfoD.setInsureTypeCode("insureTypeCode");
        tkAutoRiskInfoD.setRelatedInfoDList(Arrays.asList(tkAutoRelatedInfoD));
        tkDetail.setRiskInfoList(Arrays.asList(tkAutoRiskInfoD));

        // Configure AmPolicyTkMapper.updateByExampleSelective(...).
        final AmPolicyTk record = new AmPolicyTk();
        record.setPolicyNo("policyNo");
        record.setSubPolicyNo("subPolicyNo");
        record.setRiskName("riskName");
        record.setRiskCode("riskCode");
        record.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record.setPolicyUrl("policyDownLoadUrl");
        record.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record.setBenchmarkPremium(new BigDecimal("0.00"));
        record.setState(0);
        record.setRemark("msg");
        when(mockPolicyTkMapper.updateByExampleSelective(eq(record), any(Object.class))).thenReturn(0);

        // Configure TkAutoCvtHelper.findDrivingTkAutoRiskInfoD(...).
        final TkAutoRiskInfoD tkAutoRiskInfoD1 = new TkAutoRiskInfoD();
        tkAutoRiskInfoD1.setRiskCode("riskCode");
        final TkAutoBaseInfoD baseInfoD1 = new TkAutoBaseInfoD();
        baseInfoD1.setSubPolicyNo("subPolicyNo");
        baseInfoD1.setRiskName("riskName");
        baseInfoD1.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD1.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD1.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD1.setPremium(new BigDecimal("0.00"));
        baseInfoD1.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TkAutoPolicyViewList policyViewList1 = new TkAutoPolicyViewList();
        final TkAutoPolicyData policyData1 = new TkAutoPolicyData();
        policyData1.setPolicyDownLoadUrl("policyDownLoadUrl");
        policyViewList1.setPolicyData(policyData1);
        baseInfoD1.setPolicyViewList(policyViewList1);
        tkAutoRiskInfoD1.setBaseInfoD(baseInfoD1);
        final TkAutoRelatedInfoD tkAutoRelatedInfoD1 = new TkAutoRelatedInfoD();
        tkAutoRelatedInfoD1.setInsureFlag("insureFlag");
        tkAutoRelatedInfoD1.setInsureTypeCode("insureTypeCode");
        tkAutoRiskInfoD1.setRelatedInfoDList(Arrays.asList(tkAutoRelatedInfoD1));
        final TkAutoRiskInfoD tkAutoRiskInfoD2 = new TkAutoRiskInfoD();
        tkAutoRiskInfoD2.setRiskCode("riskCode");
        final TkAutoBaseInfoD baseInfoD2 = new TkAutoBaseInfoD();
        baseInfoD2.setSubPolicyNo("subPolicyNo");
        baseInfoD2.setRiskName("riskName");
        baseInfoD2.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD2.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD2.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD2.setPremium(new BigDecimal("0.00"));
        baseInfoD2.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TkAutoPolicyViewList policyViewList2 = new TkAutoPolicyViewList();
        final TkAutoPolicyData policyData2 = new TkAutoPolicyData();
        policyData2.setPolicyDownLoadUrl("policyDownLoadUrl");
        policyViewList2.setPolicyData(policyData2);
        baseInfoD2.setPolicyViewList(policyViewList2);
        tkAutoRiskInfoD2.setBaseInfoD(baseInfoD2);
        final TkAutoRelatedInfoD tkAutoRelatedInfoD2 = new TkAutoRelatedInfoD();
        tkAutoRelatedInfoD2.setInsureFlag("insureFlag");
        tkAutoRelatedInfoD2.setInsureTypeCode("insureTypeCode");
        tkAutoRiskInfoD2.setRelatedInfoDList(Arrays.asList(tkAutoRelatedInfoD2));
        final List<TkAutoRiskInfoD> tkAutoRiskInfoDList = Arrays.asList(tkAutoRiskInfoD2);
        when(mockCvtHelper.findDrivingTkAutoRiskInfoD(tkAutoRiskInfoDList)).thenReturn(tkAutoRiskInfoD1);

        // Configure TkAutoCvtHelper.getRelatedInfoDList(...).
        final TkAutoRelatedInfoD tkAutoRelatedInfoD3 = new TkAutoRelatedInfoD();
        tkAutoRelatedInfoD3.setInsuredName("insuredName");
        tkAutoRelatedInfoD3.setInsureFlag("insureFlag");
        tkAutoRelatedInfoD3.setInsureTypeCode("insureTypeCode");
        tkAutoRelatedInfoD3.setInsureType("insureType");
        tkAutoRelatedInfoD3.setCertiType("certiType");
        final List<TkAutoRelatedInfoD> tkAutoRelatedInfoDS = Arrays.asList(tkAutoRelatedInfoD3);
        final TkAutoRiskInfoD tkAutoRiskInfoD3 = new TkAutoRiskInfoD();
        tkAutoRiskInfoD3.setRiskCode("riskCode");
        final TkAutoBaseInfoD baseInfoD3 = new TkAutoBaseInfoD();
        baseInfoD3.setSubPolicyNo("subPolicyNo");
        baseInfoD3.setRiskName("riskName");
        baseInfoD3.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD3.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD3.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD3.setPremium(new BigDecimal("0.00"));
        baseInfoD3.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TkAutoPolicyViewList policyViewList3 = new TkAutoPolicyViewList();
        final TkAutoPolicyData policyData3 = new TkAutoPolicyData();
        policyData3.setPolicyDownLoadUrl("policyDownLoadUrl");
        policyViewList3.setPolicyData(policyData3);
        baseInfoD3.setPolicyViewList(policyViewList3);
        tkAutoRiskInfoD3.setBaseInfoD(baseInfoD3);
        final TkAutoRelatedInfoD tkAutoRelatedInfoD4 = new TkAutoRelatedInfoD();
        tkAutoRelatedInfoD4.setInsureFlag("insureFlag");
        tkAutoRelatedInfoD4.setInsureTypeCode("insureTypeCode");
        tkAutoRiskInfoD3.setRelatedInfoDList(Arrays.asList(tkAutoRelatedInfoD4));
        final List<TkAutoRiskInfoD> tkAutoRiskInfoDList1 = Arrays.asList(tkAutoRiskInfoD3);
        when(mockCvtHelper.getRelatedInfoDList(tkAutoRiskInfoDList1)).thenReturn(tkAutoRelatedInfoDS);

        // Run the test
        final List<String> result = tkAutoOrderServiceUnderTest.syncSuccess("policyNo", tkDetail, "content");

        // Verify the results
        assertNull(result);

        // Confirm AmPolicyTkMapper.insertUseGeneratedKeys(...).
        final AmPolicyTk record1 = new AmPolicyTk();
        record1.setPolicyNo("policyNo");
        record1.setSubPolicyNo("subPolicyNo");
        record1.setRiskName("riskName");
        record1.setRiskCode("riskCode");
        record1.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record1.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record1.setPolicyUrl("policyDownLoadUrl");
        record1.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record1.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record1.setBenchmarkPremium(new BigDecimal("0.00"));
        record1.setState(0);
        record1.setRemark("msg");

        // Confirm AmPolicyTkDetailMapper.insertUseGeneratedKeys(...).
        final AmPolicyTkDetail record2 = new AmPolicyTkDetail();
        record2.setPolicyNo("policyNo");
        record2.setContent("content");
    }

    @Test
    public void testSyncSuccess_TkAutoCvtHelperFindDrivingTkAutoRiskInfoDReturnsNull() throws Exception {
        // Setup
        final TkAutoQueryPolicyDetailResp tkDetail = new TkAutoQueryPolicyDetailResp();
        final TkAutoRiskInfoD tkAutoRiskInfoD = new TkAutoRiskInfoD();
        tkAutoRiskInfoD.setRiskCode("riskCode");
        final TkAutoBaseInfoD baseInfoD = new TkAutoBaseInfoD();
        baseInfoD.setSubPolicyNo("subPolicyNo");
        baseInfoD.setRiskName("riskName");
        baseInfoD.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD.setPremium(new BigDecimal("0.00"));
        baseInfoD.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TkAutoPolicyViewList policyViewList = new TkAutoPolicyViewList();
        final TkAutoPolicyData policyData = new TkAutoPolicyData();
        policyData.setPolicyDownLoadUrl("policyDownLoadUrl");
        policyViewList.setPolicyData(policyData);
        baseInfoD.setPolicyViewList(policyViewList);
        tkAutoRiskInfoD.setBaseInfoD(baseInfoD);
        final TkAutoRelatedInfoD tkAutoRelatedInfoD = new TkAutoRelatedInfoD();
        tkAutoRelatedInfoD.setInsureFlag("insureFlag");
        tkAutoRelatedInfoD.setInsureTypeCode("insureTypeCode");
        tkAutoRiskInfoD.setRelatedInfoDList(Arrays.asList(tkAutoRelatedInfoD));
        tkDetail.setRiskInfoList(Arrays.asList(tkAutoRiskInfoD));

        // Configure AmPolicyTkMapper.updateByExampleSelective(...).
        final AmPolicyTk record = new AmPolicyTk();
        record.setPolicyNo("policyNo");
        record.setSubPolicyNo("subPolicyNo");
        record.setRiskName("riskName");
        record.setRiskCode("riskCode");
        record.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record.setPolicyUrl("policyDownLoadUrl");
        record.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record.setBenchmarkPremium(new BigDecimal("0.00"));
        record.setState(0);
        record.setRemark("msg");
        when(mockPolicyTkMapper.updateByExampleSelective(eq(record), any(Object.class))).thenReturn(0);

        // Configure TkAutoCvtHelper.findDrivingTkAutoRiskInfoD(...).
        final TkAutoRiskInfoD tkAutoRiskInfoD1 = new TkAutoRiskInfoD();
        tkAutoRiskInfoD1.setRiskCode("riskCode");
        final TkAutoBaseInfoD baseInfoD1 = new TkAutoBaseInfoD();
        baseInfoD1.setSubPolicyNo("subPolicyNo");
        baseInfoD1.setRiskName("riskName");
        baseInfoD1.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD1.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD1.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD1.setPremium(new BigDecimal("0.00"));
        baseInfoD1.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TkAutoPolicyViewList policyViewList1 = new TkAutoPolicyViewList();
        final TkAutoPolicyData policyData1 = new TkAutoPolicyData();
        policyData1.setPolicyDownLoadUrl("policyDownLoadUrl");
        policyViewList1.setPolicyData(policyData1);
        baseInfoD1.setPolicyViewList(policyViewList1);
        tkAutoRiskInfoD1.setBaseInfoD(baseInfoD1);
        final TkAutoRelatedInfoD tkAutoRelatedInfoD1 = new TkAutoRelatedInfoD();
        tkAutoRelatedInfoD1.setInsureFlag("insureFlag");
        tkAutoRelatedInfoD1.setInsureTypeCode("insureTypeCode");
        tkAutoRiskInfoD1.setRelatedInfoDList(Arrays.asList(tkAutoRelatedInfoD1));
        final List<TkAutoRiskInfoD> tkAutoRiskInfoDList = Arrays.asList(tkAutoRiskInfoD1);
        when(mockCvtHelper.findDrivingTkAutoRiskInfoD(tkAutoRiskInfoDList)).thenReturn(null);

        // Run the test
        final List<String> result = tkAutoOrderServiceUnderTest.syncSuccess("policyNo", tkDetail, "content");

        // Verify the results
        assertNull(result);

        // Confirm AmPolicyTkMapper.insertUseGeneratedKeys(...).
        final AmPolicyTk record1 = new AmPolicyTk();
        record1.setPolicyNo("policyNo");
        record1.setSubPolicyNo("subPolicyNo");
        record1.setRiskName("riskName");
        record1.setRiskCode("riskCode");
        record1.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record1.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record1.setPolicyUrl("policyDownLoadUrl");
        record1.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record1.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record1.setBenchmarkPremium(new BigDecimal("0.00"));
        record1.setState(0);
        record1.setRemark("msg");

        // Confirm AmPolicyTkDetailMapper.insertUseGeneratedKeys(...).
        final AmPolicyTkDetail record2 = new AmPolicyTkDetail();
        record2.setPolicyNo("policyNo");
        record2.setContent("content");
    }

    @Test
    public void testSyncSuccess_TkAutoCvtHelperGetRelatedInfoDListReturnsNoItems() throws Exception {
        // Setup
        final TkAutoQueryPolicyDetailResp tkDetail = new TkAutoQueryPolicyDetailResp();
        final TkAutoRiskInfoD tkAutoRiskInfoD = new TkAutoRiskInfoD();
        tkAutoRiskInfoD.setRiskCode("riskCode");
        final TkAutoBaseInfoD baseInfoD = new TkAutoBaseInfoD();
        baseInfoD.setSubPolicyNo("subPolicyNo");
        baseInfoD.setRiskName("riskName");
        baseInfoD.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD.setPremium(new BigDecimal("0.00"));
        baseInfoD.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TkAutoPolicyViewList policyViewList = new TkAutoPolicyViewList();
        final TkAutoPolicyData policyData = new TkAutoPolicyData();
        policyData.setPolicyDownLoadUrl("policyDownLoadUrl");
        policyViewList.setPolicyData(policyData);
        baseInfoD.setPolicyViewList(policyViewList);
        tkAutoRiskInfoD.setBaseInfoD(baseInfoD);
        final TkAutoRelatedInfoD tkAutoRelatedInfoD = new TkAutoRelatedInfoD();
        tkAutoRelatedInfoD.setInsureFlag("insureFlag");
        tkAutoRelatedInfoD.setInsureTypeCode("insureTypeCode");
        tkAutoRiskInfoD.setRelatedInfoDList(Arrays.asList(tkAutoRelatedInfoD));
        tkDetail.setRiskInfoList(Arrays.asList(tkAutoRiskInfoD));

        // Configure AmPolicyTkMapper.updateByExampleSelective(...).
        final AmPolicyTk record = new AmPolicyTk();
        record.setPolicyNo("policyNo");
        record.setSubPolicyNo("subPolicyNo");
        record.setRiskName("riskName");
        record.setRiskCode("riskCode");
        record.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record.setPolicyUrl("policyDownLoadUrl");
        record.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record.setBenchmarkPremium(new BigDecimal("0.00"));
        record.setState(0);
        record.setRemark("msg");
        when(mockPolicyTkMapper.updateByExampleSelective(eq(record), any(Object.class))).thenReturn(0);

        // Configure TkAutoCvtHelper.findDrivingTkAutoRiskInfoD(...).
        final TkAutoRiskInfoD tkAutoRiskInfoD1 = new TkAutoRiskInfoD();
        tkAutoRiskInfoD1.setRiskCode("riskCode");
        final TkAutoBaseInfoD baseInfoD1 = new TkAutoBaseInfoD();
        baseInfoD1.setSubPolicyNo("subPolicyNo");
        baseInfoD1.setRiskName("riskName");
        baseInfoD1.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD1.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD1.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD1.setPremium(new BigDecimal("0.00"));
        baseInfoD1.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TkAutoPolicyViewList policyViewList1 = new TkAutoPolicyViewList();
        final TkAutoPolicyData policyData1 = new TkAutoPolicyData();
        policyData1.setPolicyDownLoadUrl("policyDownLoadUrl");
        policyViewList1.setPolicyData(policyData1);
        baseInfoD1.setPolicyViewList(policyViewList1);
        tkAutoRiskInfoD1.setBaseInfoD(baseInfoD1);
        final TkAutoRelatedInfoD tkAutoRelatedInfoD1 = new TkAutoRelatedInfoD();
        tkAutoRelatedInfoD1.setInsureFlag("insureFlag");
        tkAutoRelatedInfoD1.setInsureTypeCode("insureTypeCode");
        tkAutoRiskInfoD1.setRelatedInfoDList(Arrays.asList(tkAutoRelatedInfoD1));
        final TkAutoRiskInfoD tkAutoRiskInfoD2 = new TkAutoRiskInfoD();
        tkAutoRiskInfoD2.setRiskCode("riskCode");
        final TkAutoBaseInfoD baseInfoD2 = new TkAutoBaseInfoD();
        baseInfoD2.setSubPolicyNo("subPolicyNo");
        baseInfoD2.setRiskName("riskName");
        baseInfoD2.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD2.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD2.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD2.setPremium(new BigDecimal("0.00"));
        baseInfoD2.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TkAutoPolicyViewList policyViewList2 = new TkAutoPolicyViewList();
        final TkAutoPolicyData policyData2 = new TkAutoPolicyData();
        policyData2.setPolicyDownLoadUrl("policyDownLoadUrl");
        policyViewList2.setPolicyData(policyData2);
        baseInfoD2.setPolicyViewList(policyViewList2);
        tkAutoRiskInfoD2.setBaseInfoD(baseInfoD2);
        final TkAutoRelatedInfoD tkAutoRelatedInfoD2 = new TkAutoRelatedInfoD();
        tkAutoRelatedInfoD2.setInsureFlag("insureFlag");
        tkAutoRelatedInfoD2.setInsureTypeCode("insureTypeCode");
        tkAutoRiskInfoD2.setRelatedInfoDList(Arrays.asList(tkAutoRelatedInfoD2));
        final List<TkAutoRiskInfoD> tkAutoRiskInfoDList = Arrays.asList(tkAutoRiskInfoD2);
        when(mockCvtHelper.findDrivingTkAutoRiskInfoD(tkAutoRiskInfoDList)).thenReturn(tkAutoRiskInfoD1);

        // Configure TkAutoCvtHelper.getRelatedInfoDList(...).
        final TkAutoRiskInfoD tkAutoRiskInfoD3 = new TkAutoRiskInfoD();
        tkAutoRiskInfoD3.setRiskCode("riskCode");
        final TkAutoBaseInfoD baseInfoD3 = new TkAutoBaseInfoD();
        baseInfoD3.setSubPolicyNo("subPolicyNo");
        baseInfoD3.setRiskName("riskName");
        baseInfoD3.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD3.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD3.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD3.setPremium(new BigDecimal("0.00"));
        baseInfoD3.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TkAutoPolicyViewList policyViewList3 = new TkAutoPolicyViewList();
        final TkAutoPolicyData policyData3 = new TkAutoPolicyData();
        policyData3.setPolicyDownLoadUrl("policyDownLoadUrl");
        policyViewList3.setPolicyData(policyData3);
        baseInfoD3.setPolicyViewList(policyViewList3);
        tkAutoRiskInfoD3.setBaseInfoD(baseInfoD3);
        final TkAutoRelatedInfoD tkAutoRelatedInfoD3 = new TkAutoRelatedInfoD();
        tkAutoRelatedInfoD3.setInsureFlag("insureFlag");
        tkAutoRelatedInfoD3.setInsureTypeCode("insureTypeCode");
        tkAutoRiskInfoD3.setRelatedInfoDList(Arrays.asList(tkAutoRelatedInfoD3));
        final List<TkAutoRiskInfoD> tkAutoRiskInfoDList1 = Arrays.asList(tkAutoRiskInfoD3);
        when(mockCvtHelper.getRelatedInfoDList(tkAutoRiskInfoDList1)).thenReturn(Collections.emptyList());

        // Run the test
        final List<String> result = tkAutoOrderServiceUnderTest.syncSuccess("policyNo", tkDetail, "content");

        // Verify the results
        assertNull(result);

        // Confirm AmPolicyTkMapper.insertUseGeneratedKeys(...).
        final AmPolicyTk record1 = new AmPolicyTk();
        record1.setPolicyNo("policyNo");
        record1.setSubPolicyNo("subPolicyNo");
        record1.setRiskName("riskName");
        record1.setRiskCode("riskCode");
        record1.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record1.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record1.setPolicyUrl("policyDownLoadUrl");
        record1.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record1.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record1.setBenchmarkPremium(new BigDecimal("0.00"));
        record1.setState(0);
        record1.setRemark("msg");

        // Confirm AmPolicyTkDetailMapper.insertUseGeneratedKeys(...).
        final AmPolicyTkDetail record2 = new AmPolicyTkDetail();
        record2.setPolicyNo("policyNo");
        record2.setContent("content");
    }

    @Test
    public void testSyncSuccess_TkAutoCvtHelperCvtOrderInfoReturnsNoItems() throws Exception {
        // Setup
        final TkAutoQueryPolicyDetailResp tkDetail = new TkAutoQueryPolicyDetailResp();
        final TkAutoRiskInfoD tkAutoRiskInfoD = new TkAutoRiskInfoD();
        tkAutoRiskInfoD.setRiskCode("riskCode");
        final TkAutoBaseInfoD baseInfoD = new TkAutoBaseInfoD();
        baseInfoD.setSubPolicyNo("subPolicyNo");
        baseInfoD.setRiskName("riskName");
        baseInfoD.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD.setPremium(new BigDecimal("0.00"));
        baseInfoD.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TkAutoPolicyViewList policyViewList = new TkAutoPolicyViewList();
        final TkAutoPolicyData policyData = new TkAutoPolicyData();
        policyData.setPolicyDownLoadUrl("policyDownLoadUrl");
        policyViewList.setPolicyData(policyData);
        baseInfoD.setPolicyViewList(policyViewList);
        tkAutoRiskInfoD.setBaseInfoD(baseInfoD);
        final TkAutoRelatedInfoD tkAutoRelatedInfoD = new TkAutoRelatedInfoD();
        tkAutoRelatedInfoD.setInsureFlag("insureFlag");
        tkAutoRelatedInfoD.setInsureTypeCode("insureTypeCode");
        tkAutoRiskInfoD.setRelatedInfoDList(Arrays.asList(tkAutoRelatedInfoD));
        tkDetail.setRiskInfoList(Arrays.asList(tkAutoRiskInfoD));

        // Configure AmPolicyTkMapper.updateByExampleSelective(...).
        final AmPolicyTk record = new AmPolicyTk();
        record.setPolicyNo("policyNo");
        record.setSubPolicyNo("subPolicyNo");
        record.setRiskName("riskName");
        record.setRiskCode("riskCode");
        record.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record.setPolicyUrl("policyDownLoadUrl");
        record.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record.setBenchmarkPremium(new BigDecimal("0.00"));
        record.setState(0);
        record.setRemark("msg");
        when(mockPolicyTkMapper.updateByExampleSelective(eq(record), any(Object.class))).thenReturn(0);

        // Configure TkAutoCvtHelper.findDrivingTkAutoRiskInfoD(...).
        final TkAutoRiskInfoD tkAutoRiskInfoD1 = new TkAutoRiskInfoD();
        tkAutoRiskInfoD1.setRiskCode("riskCode");
        final TkAutoBaseInfoD baseInfoD1 = new TkAutoBaseInfoD();
        baseInfoD1.setSubPolicyNo("subPolicyNo");
        baseInfoD1.setRiskName("riskName");
        baseInfoD1.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD1.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD1.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD1.setPremium(new BigDecimal("0.00"));
        baseInfoD1.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TkAutoPolicyViewList policyViewList1 = new TkAutoPolicyViewList();
        final TkAutoPolicyData policyData1 = new TkAutoPolicyData();
        policyData1.setPolicyDownLoadUrl("policyDownLoadUrl");
        policyViewList1.setPolicyData(policyData1);
        baseInfoD1.setPolicyViewList(policyViewList1);
        tkAutoRiskInfoD1.setBaseInfoD(baseInfoD1);
        final TkAutoRelatedInfoD tkAutoRelatedInfoD1 = new TkAutoRelatedInfoD();
        tkAutoRelatedInfoD1.setInsureFlag("insureFlag");
        tkAutoRelatedInfoD1.setInsureTypeCode("insureTypeCode");
        tkAutoRiskInfoD1.setRelatedInfoDList(Arrays.asList(tkAutoRelatedInfoD1));
        final TkAutoRiskInfoD tkAutoRiskInfoD2 = new TkAutoRiskInfoD();
        tkAutoRiskInfoD2.setRiskCode("riskCode");
        final TkAutoBaseInfoD baseInfoD2 = new TkAutoBaseInfoD();
        baseInfoD2.setSubPolicyNo("subPolicyNo");
        baseInfoD2.setRiskName("riskName");
        baseInfoD2.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD2.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD2.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD2.setPremium(new BigDecimal("0.00"));
        baseInfoD2.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TkAutoPolicyViewList policyViewList2 = new TkAutoPolicyViewList();
        final TkAutoPolicyData policyData2 = new TkAutoPolicyData();
        policyData2.setPolicyDownLoadUrl("policyDownLoadUrl");
        policyViewList2.setPolicyData(policyData2);
        baseInfoD2.setPolicyViewList(policyViewList2);
        tkAutoRiskInfoD2.setBaseInfoD(baseInfoD2);
        final TkAutoRelatedInfoD tkAutoRelatedInfoD2 = new TkAutoRelatedInfoD();
        tkAutoRelatedInfoD2.setInsureFlag("insureFlag");
        tkAutoRelatedInfoD2.setInsureTypeCode("insureTypeCode");
        tkAutoRiskInfoD2.setRelatedInfoDList(Arrays.asList(tkAutoRelatedInfoD2));
        final List<TkAutoRiskInfoD> tkAutoRiskInfoDList = Arrays.asList(tkAutoRiskInfoD2);
        when(mockCvtHelper.findDrivingTkAutoRiskInfoD(tkAutoRiskInfoDList)).thenReturn(tkAutoRiskInfoD1);

        // Configure TkAutoCvtHelper.getRelatedInfoDList(...).
        final TkAutoRelatedInfoD tkAutoRelatedInfoD3 = new TkAutoRelatedInfoD();
        tkAutoRelatedInfoD3.setInsuredName("insuredName");
        tkAutoRelatedInfoD3.setInsureFlag("insureFlag");
        tkAutoRelatedInfoD3.setInsureTypeCode("insureTypeCode");
        tkAutoRelatedInfoD3.setInsureType("insureType");
        tkAutoRelatedInfoD3.setCertiType("certiType");
        final List<TkAutoRelatedInfoD> tkAutoRelatedInfoDS = Arrays.asList(tkAutoRelatedInfoD3);
        final TkAutoRiskInfoD tkAutoRiskInfoD3 = new TkAutoRiskInfoD();
        tkAutoRiskInfoD3.setRiskCode("riskCode");
        final TkAutoBaseInfoD baseInfoD3 = new TkAutoBaseInfoD();
        baseInfoD3.setSubPolicyNo("subPolicyNo");
        baseInfoD3.setRiskName("riskName");
        baseInfoD3.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD3.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD3.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD3.setPremium(new BigDecimal("0.00"));
        baseInfoD3.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TkAutoPolicyViewList policyViewList3 = new TkAutoPolicyViewList();
        final TkAutoPolicyData policyData3 = new TkAutoPolicyData();
        policyData3.setPolicyDownLoadUrl("policyDownLoadUrl");
        policyViewList3.setPolicyData(policyData3);
        baseInfoD3.setPolicyViewList(policyViewList3);
        tkAutoRiskInfoD3.setBaseInfoD(baseInfoD3);
        final TkAutoRelatedInfoD tkAutoRelatedInfoD4 = new TkAutoRelatedInfoD();
        tkAutoRelatedInfoD4.setInsureFlag("insureFlag");
        tkAutoRelatedInfoD4.setInsureTypeCode("insureTypeCode");
        tkAutoRiskInfoD3.setRelatedInfoDList(Arrays.asList(tkAutoRelatedInfoD4));
        final List<TkAutoRiskInfoD> tkAutoRiskInfoDList1 = Arrays.asList(tkAutoRiskInfoD3);
        when(mockCvtHelper.getRelatedInfoDList(tkAutoRiskInfoDList1)).thenReturn(tkAutoRelatedInfoDS);

        // Configure AmPolicyTkMapper.selectOneByPolicyNo(...).
        final AmPolicyTk amPolicyTk = new AmPolicyTk();
        amPolicyTk.setPolicyNo("policyNo");
        amPolicyTk.setSubPolicyNo("subPolicyNo");
        amPolicyTk.setRiskName("riskName");
        amPolicyTk.setRiskCode("riskCode");
        amPolicyTk.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk.setPolicyUrl("policyDownLoadUrl");
        amPolicyTk.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk.setBenchmarkPremium(new BigDecimal("0.00"));
        amPolicyTk.setState(0);
        amPolicyTk.setRemark("msg");
        when(mockPolicyTkMapper.selectOneByPolicyNo("policyNo")).thenReturn(amPolicyTk);

        // Configure TkAutoCvtHelper.cvtOrderInfo(...).
        final AmPolicyTk tk = new AmPolicyTk();
        tk.setPolicyNo("policyNo");
        tk.setSubPolicyNo("subPolicyNo");
        tk.setRiskName("riskName");
        tk.setRiskCode("riskCode");
        tk.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        tk.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        tk.setPolicyUrl("policyDownLoadUrl");
        tk.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        tk.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        tk.setBenchmarkPremium(new BigDecimal("0.00"));
        tk.setState(0);
        tk.setRemark("msg");
        final TkAutoQueryPolicyDetailResp detailResp = new TkAutoQueryPolicyDetailResp();
        final TkAutoRiskInfoD tkAutoRiskInfoD4 = new TkAutoRiskInfoD();
        tkAutoRiskInfoD4.setRiskCode("riskCode");
        final TkAutoBaseInfoD baseInfoD4 = new TkAutoBaseInfoD();
        baseInfoD4.setSubPolicyNo("subPolicyNo");
        baseInfoD4.setRiskName("riskName");
        baseInfoD4.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD4.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD4.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD4.setPremium(new BigDecimal("0.00"));
        baseInfoD4.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TkAutoPolicyViewList policyViewList4 = new TkAutoPolicyViewList();
        final TkAutoPolicyData policyData4 = new TkAutoPolicyData();
        policyData4.setPolicyDownLoadUrl("policyDownLoadUrl");
        policyViewList4.setPolicyData(policyData4);
        baseInfoD4.setPolicyViewList(policyViewList4);
        tkAutoRiskInfoD4.setBaseInfoD(baseInfoD4);
        final TkAutoRelatedInfoD tkAutoRelatedInfoD5 = new TkAutoRelatedInfoD();
        tkAutoRelatedInfoD5.setInsureFlag("insureFlag");
        tkAutoRelatedInfoD5.setInsureTypeCode("insureTypeCode");
        tkAutoRiskInfoD4.setRelatedInfoDList(Arrays.asList(tkAutoRelatedInfoD5));
        detailResp.setRiskInfoList(Arrays.asList(tkAutoRiskInfoD4));
        when(mockCvtHelper.cvtOrderInfo(tk, detailResp, new HashMap<>())).thenReturn(Collections.emptyList());

        // Configure TkAutoCvtHelper.cvtAutoInfoList(...).
        final AutoOrderDTO autoOrderDTO = new AutoOrderDTO();
        autoOrderDTO.setOrderNo("orderNo");
        final AutoOrder order = new AutoOrder();
        order.setPayStatus("-1");
        order.setPaymentTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        order.setCommissionId(0);
        autoOrderDTO.setOrder(order);
        final AutoOrderPerson autoOrderPerson = new AutoOrderPerson();
        autoOrderDTO.setPeople(Arrays.asList(autoOrderPerson));
        final AutoOrderPolicyDTO autoOrderPolicyDTO = new AutoOrderPolicyDTO();
        final AutoOrderPolicy policy = new AutoOrderPolicy();
        policy.setPolicyNo("policyNo");
        policy.setSubPolicyNo("subPolicyNo");
        policy.setPolicyState("-2");
        policy.setBusinessScore("businessScore");
        autoOrderPolicyDTO.setPolicy(policy);
        final AutoOrderRisk autoOrderRisk = new AutoOrderRisk();
        autoOrderRisk.setRiskName("riskName");
        autoOrderRisk.setPremium(new BigDecimal("0.00"));
        autoOrderRisk.setAmount(new BigDecimal("0.00"));
        autoOrderPolicyDTO.setRiskList(Arrays.asList(autoOrderRisk));
        autoOrderDTO.setPolicies(Arrays.asList(autoOrderPolicyDTO));
        final AutoOrderExtend extend = new AutoOrderExtend();
        autoOrderDTO.setExtend(extend);
        final AutoOrderCar car = new AutoOrderCar();
        car.setOwnerPersonName("ownerPersonName");
        car.setOwnerPersonGender("ownerPersonGender");
        car.setOwnerIdType("ownerIdType");
        car.setOwnerIdNumber("ownerIdNumber");
        car.setFirstRegDate("firstRegDate");
        car.setFullLoad("fullLoad");
        car.setTransferDate("transferDate");
        car.setSeatCnt("approvedNum");
        car.setPrice("price");
        car.setCarUserType("carUserType");
        car.setDisplacement("displacement");
        car.setIsNewCar("isNewCar");
        autoOrderDTO.setCar(car);
        final List<AutoOrderDTO> autoOrderDTOS = Arrays.asList(autoOrderDTO);
        final AmPolicyTk tk1 = new AmPolicyTk();
        tk1.setPolicyNo("policyNo");
        tk1.setSubPolicyNo("subPolicyNo");
        tk1.setRiskName("riskName");
        tk1.setRiskCode("riskCode");
        tk1.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        tk1.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        tk1.setPolicyUrl("policyDownLoadUrl");
        tk1.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        tk1.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        tk1.setBenchmarkPremium(new BigDecimal("0.00"));
        tk1.setState(0);
        tk1.setRemark("msg");
        final TkAutoQueryPolicyDetailResp detailResp1 = new TkAutoQueryPolicyDetailResp();
        final TkAutoRiskInfoD tkAutoRiskInfoD5 = new TkAutoRiskInfoD();
        tkAutoRiskInfoD5.setRiskCode("riskCode");
        final TkAutoBaseInfoD baseInfoD5 = new TkAutoBaseInfoD();
        baseInfoD5.setSubPolicyNo("subPolicyNo");
        baseInfoD5.setRiskName("riskName");
        baseInfoD5.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD5.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD5.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD5.setPremium(new BigDecimal("0.00"));
        baseInfoD5.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TkAutoPolicyViewList policyViewList5 = new TkAutoPolicyViewList();
        final TkAutoPolicyData policyData5 = new TkAutoPolicyData();
        policyData5.setPolicyDownLoadUrl("policyDownLoadUrl");
        policyViewList5.setPolicyData(policyData5);
        baseInfoD5.setPolicyViewList(policyViewList5);
        tkAutoRiskInfoD5.setBaseInfoD(baseInfoD5);
        final TkAutoRelatedInfoD tkAutoRelatedInfoD6 = new TkAutoRelatedInfoD();
        tkAutoRelatedInfoD6.setInsureFlag("insureFlag");
        tkAutoRelatedInfoD6.setInsureTypeCode("insureTypeCode");
        tkAutoRiskInfoD5.setRelatedInfoDList(Arrays.asList(tkAutoRelatedInfoD6));
        detailResp1.setRiskInfoList(Arrays.asList(tkAutoRiskInfoD5));
        when(mockCvtHelper.cvtAutoInfoList(tk1, Arrays.asList("value"), detailResp1, new HashMap<>())).thenReturn(autoOrderDTOS);

        // Run the test
        final List<String> result = tkAutoOrderServiceUnderTest.syncSuccess("policyNo", tkDetail, "content");

        // Verify the results

        // Confirm AmPolicyTkMapper.insertUseGeneratedKeys(...).
        final AmPolicyTk record1 = new AmPolicyTk();
        record1.setPolicyNo("policyNo");
        record1.setSubPolicyNo("subPolicyNo");
        record1.setRiskName("riskName");
        record1.setRiskCode("riskCode");
        record1.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record1.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record1.setPolicyUrl("policyDownLoadUrl");
        record1.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record1.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record1.setBenchmarkPremium(new BigDecimal("0.00"));
        record1.setState(0);
        record1.setRemark("msg");

        // Confirm AmPolicyTkDetailMapper.insertUseGeneratedKeys(...).
        final AmPolicyTkDetail record2 = new AmPolicyTkDetail();
        record2.setPolicyNo("policyNo");
        record2.setContent("content");
    }

    @Test
    public void testSyncSuccess_TkAutoCvtHelperCvtAutoInfoListReturnsNoItems() throws Exception {
        // Setup
        final TkAutoQueryPolicyDetailResp tkDetail = new TkAutoQueryPolicyDetailResp();
        final TkAutoRiskInfoD tkAutoRiskInfoD = new TkAutoRiskInfoD();
        tkAutoRiskInfoD.setRiskCode("riskCode");
        final TkAutoBaseInfoD baseInfoD = new TkAutoBaseInfoD();
        baseInfoD.setSubPolicyNo("subPolicyNo");
        baseInfoD.setRiskName("riskName");
        baseInfoD.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD.setPremium(new BigDecimal("0.00"));
        baseInfoD.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TkAutoPolicyViewList policyViewList = new TkAutoPolicyViewList();
        final TkAutoPolicyData policyData = new TkAutoPolicyData();
        policyData.setPolicyDownLoadUrl("policyDownLoadUrl");
        policyViewList.setPolicyData(policyData);
        baseInfoD.setPolicyViewList(policyViewList);
        tkAutoRiskInfoD.setBaseInfoD(baseInfoD);
        final TkAutoRelatedInfoD tkAutoRelatedInfoD = new TkAutoRelatedInfoD();
        tkAutoRelatedInfoD.setInsureFlag("insureFlag");
        tkAutoRelatedInfoD.setInsureTypeCode("insureTypeCode");
        tkAutoRiskInfoD.setRelatedInfoDList(Arrays.asList(tkAutoRelatedInfoD));
        tkDetail.setRiskInfoList(Arrays.asList(tkAutoRiskInfoD));

        // Configure AmPolicyTkMapper.updateByExampleSelective(...).
        final AmPolicyTk record = new AmPolicyTk();
        record.setPolicyNo("policyNo");
        record.setSubPolicyNo("subPolicyNo");
        record.setRiskName("riskName");
        record.setRiskCode("riskCode");
        record.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record.setPolicyUrl("policyDownLoadUrl");
        record.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record.setBenchmarkPremium(new BigDecimal("0.00"));
        record.setState(0);
        record.setRemark("msg");
        when(mockPolicyTkMapper.updateByExampleSelective(eq(record), any(Object.class))).thenReturn(0);

        // Configure TkAutoCvtHelper.findDrivingTkAutoRiskInfoD(...).
        final TkAutoRiskInfoD tkAutoRiskInfoD1 = new TkAutoRiskInfoD();
        tkAutoRiskInfoD1.setRiskCode("riskCode");
        final TkAutoBaseInfoD baseInfoD1 = new TkAutoBaseInfoD();
        baseInfoD1.setSubPolicyNo("subPolicyNo");
        baseInfoD1.setRiskName("riskName");
        baseInfoD1.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD1.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD1.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD1.setPremium(new BigDecimal("0.00"));
        baseInfoD1.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TkAutoPolicyViewList policyViewList1 = new TkAutoPolicyViewList();
        final TkAutoPolicyData policyData1 = new TkAutoPolicyData();
        policyData1.setPolicyDownLoadUrl("policyDownLoadUrl");
        policyViewList1.setPolicyData(policyData1);
        baseInfoD1.setPolicyViewList(policyViewList1);
        tkAutoRiskInfoD1.setBaseInfoD(baseInfoD1);
        final TkAutoRelatedInfoD tkAutoRelatedInfoD1 = new TkAutoRelatedInfoD();
        tkAutoRelatedInfoD1.setInsureFlag("insureFlag");
        tkAutoRelatedInfoD1.setInsureTypeCode("insureTypeCode");
        tkAutoRiskInfoD1.setRelatedInfoDList(Arrays.asList(tkAutoRelatedInfoD1));
        final TkAutoRiskInfoD tkAutoRiskInfoD2 = new TkAutoRiskInfoD();
        tkAutoRiskInfoD2.setRiskCode("riskCode");
        final TkAutoBaseInfoD baseInfoD2 = new TkAutoBaseInfoD();
        baseInfoD2.setSubPolicyNo("subPolicyNo");
        baseInfoD2.setRiskName("riskName");
        baseInfoD2.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD2.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD2.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD2.setPremium(new BigDecimal("0.00"));
        baseInfoD2.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TkAutoPolicyViewList policyViewList2 = new TkAutoPolicyViewList();
        final TkAutoPolicyData policyData2 = new TkAutoPolicyData();
        policyData2.setPolicyDownLoadUrl("policyDownLoadUrl");
        policyViewList2.setPolicyData(policyData2);
        baseInfoD2.setPolicyViewList(policyViewList2);
        tkAutoRiskInfoD2.setBaseInfoD(baseInfoD2);
        final TkAutoRelatedInfoD tkAutoRelatedInfoD2 = new TkAutoRelatedInfoD();
        tkAutoRelatedInfoD2.setInsureFlag("insureFlag");
        tkAutoRelatedInfoD2.setInsureTypeCode("insureTypeCode");
        tkAutoRiskInfoD2.setRelatedInfoDList(Arrays.asList(tkAutoRelatedInfoD2));
        final List<TkAutoRiskInfoD> tkAutoRiskInfoDList = Arrays.asList(tkAutoRiskInfoD2);
        when(mockCvtHelper.findDrivingTkAutoRiskInfoD(tkAutoRiskInfoDList)).thenReturn(tkAutoRiskInfoD1);

        // Configure TkAutoCvtHelper.getRelatedInfoDList(...).
        final TkAutoRelatedInfoD tkAutoRelatedInfoD3 = new TkAutoRelatedInfoD();
        tkAutoRelatedInfoD3.setInsuredName("insuredName");
        tkAutoRelatedInfoD3.setInsureFlag("insureFlag");
        tkAutoRelatedInfoD3.setInsureTypeCode("insureTypeCode");
        tkAutoRelatedInfoD3.setInsureType("insureType");
        tkAutoRelatedInfoD3.setCertiType("certiType");
        final List<TkAutoRelatedInfoD> tkAutoRelatedInfoDS = Arrays.asList(tkAutoRelatedInfoD3);
        final TkAutoRiskInfoD tkAutoRiskInfoD3 = new TkAutoRiskInfoD();
        tkAutoRiskInfoD3.setRiskCode("riskCode");
        final TkAutoBaseInfoD baseInfoD3 = new TkAutoBaseInfoD();
        baseInfoD3.setSubPolicyNo("subPolicyNo");
        baseInfoD3.setRiskName("riskName");
        baseInfoD3.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD3.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD3.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD3.setPremium(new BigDecimal("0.00"));
        baseInfoD3.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TkAutoPolicyViewList policyViewList3 = new TkAutoPolicyViewList();
        final TkAutoPolicyData policyData3 = new TkAutoPolicyData();
        policyData3.setPolicyDownLoadUrl("policyDownLoadUrl");
        policyViewList3.setPolicyData(policyData3);
        baseInfoD3.setPolicyViewList(policyViewList3);
        tkAutoRiskInfoD3.setBaseInfoD(baseInfoD3);
        final TkAutoRelatedInfoD tkAutoRelatedInfoD4 = new TkAutoRelatedInfoD();
        tkAutoRelatedInfoD4.setInsureFlag("insureFlag");
        tkAutoRelatedInfoD4.setInsureTypeCode("insureTypeCode");
        tkAutoRiskInfoD3.setRelatedInfoDList(Arrays.asList(tkAutoRelatedInfoD4));
        final List<TkAutoRiskInfoD> tkAutoRiskInfoDList1 = Arrays.asList(tkAutoRiskInfoD3);
        when(mockCvtHelper.getRelatedInfoDList(tkAutoRiskInfoDList1)).thenReturn(tkAutoRelatedInfoDS);

        // Configure AmPolicyTkMapper.selectOneByPolicyNo(...).
        final AmPolicyTk amPolicyTk = new AmPolicyTk();
        amPolicyTk.setPolicyNo("policyNo");
        amPolicyTk.setSubPolicyNo("subPolicyNo");
        amPolicyTk.setRiskName("riskName");
        amPolicyTk.setRiskCode("riskCode");
        amPolicyTk.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk.setPolicyUrl("policyDownLoadUrl");
        amPolicyTk.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk.setBenchmarkPremium(new BigDecimal("0.00"));
        amPolicyTk.setState(0);
        amPolicyTk.setRemark("msg");
        when(mockPolicyTkMapper.selectOneByPolicyNo("policyNo")).thenReturn(amPolicyTk);

        // Configure TkAutoCvtHelper.cvtOrderInfo(...).
        final OrderSubmitRequest orderSubmitRequest = new OrderSubmitRequest();
        final FhProduct productInfo = new FhProduct();
        productInfo.setProductId("fhProductId");
        productInfo.setRecommendId("recommendId");
        productInfo.setRecommendMainJobNumber("recommendMainJobNumber");
        productInfo.setRecommendOrgCode("recommendOrgCode");
        orderSubmitRequest.setProductInfo(productInfo);
        final FhOrderInfo orderInfo = new FhOrderInfo();
        orderInfo.setSubmitTime("submitTime");
        orderInfo.setStartTime("startTime");
        orderInfo.setEndTime("endTime");
        orderSubmitRequest.setOrderInfo(orderInfo);
        final FhProposer proposerInfo = new FhProposer();
        proposerInfo.setPersonName("insuredName");
        proposerInfo.setPersonGender("insuredPersonGender");
        proposerInfo.setIdType("idType");
        proposerInfo.setIdNumber("idNumber");
        proposerInfo.setBirthday("birthday");
        proposerInfo.setCellPhone("cellPhone");
        proposerInfo.setEmail("email");
        proposerInfo.setAddress("address");
        proposerInfo.setAppStatus("-2");
        proposerInfo.setPolicyNo("insIdNumber");
        proposerInfo.setDownloadURL("downloadURL");
        orderSubmitRequest.setProposerInfo(proposerInfo);
        final FhInsuredPerson fhInsuredPerson = new FhInsuredPerson();
        fhInsuredPerson.setPersonName("insuredName");
        fhInsuredPerson.setIdType("idType");
        fhInsuredPerson.setIdNumber("idNumber");
        fhInsuredPerson.setBirthday("birthday");
        fhInsuredPerson.setCellPhone("cellPhone");
        fhInsuredPerson.setAppStatus("-2");
        fhInsuredPerson.setPolicyNo("insIdNumber");
        fhInsuredPerson.setRelationship("99");
        fhInsuredPerson.setFlightNo("flightNo");
        fhInsuredPerson.setFlightTime("flightTime");
        fhInsuredPerson.setOccupationCode("");
        fhInsuredPerson.setOccupationGroup("occupationGroup");
        fhInsuredPerson.setDestinationCountryText("destinationCountryText");
        fhInsuredPerson.setAnnualIncome("annualIncome");
        fhInsuredPerson.setStudentType("studentType");
        fhInsuredPerson.setSchoolType("schoolType");
        fhInsuredPerson.setSchoolNature("schoolNature");
        fhInsuredPerson.setSchoolName("schoolName");
        fhInsuredPerson.setSchoolClass("schoolClass");
        fhInsuredPerson.setStudentId("studentId");
        fhInsuredPerson.setIdPeriodStart("idPeriodStart");
        fhInsuredPerson.setIdPeriodEnd("idPeriodEnd");
        fhInsuredPerson.setSmoke("smoke");
        fhInsuredPerson.setOldPolicyNo("oldPolicyNo");
        final TestPremiumDutyForm testPremiumDutyForm = new TestPremiumDutyForm();
        fhInsuredPerson.setDuties(Arrays.asList(testPremiumDutyForm));
        final TestPremiumProductForm product = new TestPremiumProductForm();
        product.setProductId(0);
        product.setPlanId(0);
        product.setPlanCode("planCode");
        product.setPremium(new BigDecimal("0.00"));
        product.setQty(0);
        final TestPremiumDutyForm testPremiumDutyForm1 = new TestPremiumDutyForm();
        product.setPremiumDutyForms(Arrays.asList(testPremiumDutyForm1));
        fhInsuredPerson.setProduct(product);
        orderSubmitRequest.setInsuredPerson(Arrays.asList(fhInsuredPerson));
        final FhProperty propertyInfo = new FhProperty();
        propertyInfo.setPropertyInfoIsExist("0");
        orderSubmitRequest.setPropertyInfo(propertyInfo);
        final SmOrderHouseDTO house = new SmOrderHouseDTO();
        orderSubmitRequest.setHouse(house);
        final SmOrderCarInfoDTO carInfo = new SmOrderCarInfoDTO();
        orderSubmitRequest.setCarInfo(carInfo);
        orderSubmitRequest.setProductType("code");
        orderSubmitRequest.setBizCode("bizCode");
        orderSubmitRequest.setOrderOutType("orderOutType");
        orderSubmitRequest.setToken("token");
        orderSubmitRequest.setPlanId(0);
        orderSubmitRequest.setQty(0);
        orderSubmitRequest.setPreOrderId("preOrderId");
        orderSubmitRequest.setRenew(false);
        orderSubmitRequest.setRealRenewFlag(false);
        orderSubmitRequest.setAppNo("appNo");
        orderSubmitRequest.setAgentId(0);
        orderSubmitRequest.setJobCode("jobCode");
        final List<SmCreateOrderSubmitRequest> smCreateOrderSubmitRequests = Arrays.asList(new SmCreateOrderSubmitRequest(orderSubmitRequest));
        final AmPolicyTk tk = new AmPolicyTk();
        tk.setPolicyNo("policyNo");
        tk.setSubPolicyNo("subPolicyNo");
        tk.setRiskName("riskName");
        tk.setRiskCode("riskCode");
        tk.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        tk.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        tk.setPolicyUrl("policyDownLoadUrl");
        tk.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        tk.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        tk.setBenchmarkPremium(new BigDecimal("0.00"));
        tk.setState(0);
        tk.setRemark("msg");
        final TkAutoQueryPolicyDetailResp detailResp = new TkAutoQueryPolicyDetailResp();
        final TkAutoRiskInfoD tkAutoRiskInfoD4 = new TkAutoRiskInfoD();
        tkAutoRiskInfoD4.setRiskCode("riskCode");
        final TkAutoBaseInfoD baseInfoD4 = new TkAutoBaseInfoD();
        baseInfoD4.setSubPolicyNo("subPolicyNo");
        baseInfoD4.setRiskName("riskName");
        baseInfoD4.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD4.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD4.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD4.setPremium(new BigDecimal("0.00"));
        baseInfoD4.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TkAutoPolicyViewList policyViewList4 = new TkAutoPolicyViewList();
        final TkAutoPolicyData policyData4 = new TkAutoPolicyData();
        policyData4.setPolicyDownLoadUrl("policyDownLoadUrl");
        policyViewList4.setPolicyData(policyData4);
        baseInfoD4.setPolicyViewList(policyViewList4);
        tkAutoRiskInfoD4.setBaseInfoD(baseInfoD4);
        final TkAutoRelatedInfoD tkAutoRelatedInfoD5 = new TkAutoRelatedInfoD();
        tkAutoRelatedInfoD5.setInsureFlag("insureFlag");
        tkAutoRelatedInfoD5.setInsureTypeCode("insureTypeCode");
        tkAutoRiskInfoD4.setRelatedInfoDList(Arrays.asList(tkAutoRelatedInfoD5));
        detailResp.setRiskInfoList(Arrays.asList(tkAutoRiskInfoD4));
        when(mockCvtHelper.cvtOrderInfo(tk, detailResp, new HashMap<>())).thenReturn(smCreateOrderSubmitRequests);

        // Configure TkAutoCvtHelper.cvtAutoInfoList(...).
        final AmPolicyTk tk1 = new AmPolicyTk();
        tk1.setPolicyNo("policyNo");
        tk1.setSubPolicyNo("subPolicyNo");
        tk1.setRiskName("riskName");
        tk1.setRiskCode("riskCode");
        tk1.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        tk1.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        tk1.setPolicyUrl("policyDownLoadUrl");
        tk1.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        tk1.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        tk1.setBenchmarkPremium(new BigDecimal("0.00"));
        tk1.setState(0);
        tk1.setRemark("msg");
        final TkAutoQueryPolicyDetailResp detailResp1 = new TkAutoQueryPolicyDetailResp();
        final TkAutoRiskInfoD tkAutoRiskInfoD5 = new TkAutoRiskInfoD();
        tkAutoRiskInfoD5.setRiskCode("riskCode");
        final TkAutoBaseInfoD baseInfoD5 = new TkAutoBaseInfoD();
        baseInfoD5.setSubPolicyNo("subPolicyNo");
        baseInfoD5.setRiskName("riskName");
        baseInfoD5.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD5.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD5.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD5.setPremium(new BigDecimal("0.00"));
        baseInfoD5.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TkAutoPolicyViewList policyViewList5 = new TkAutoPolicyViewList();
        final TkAutoPolicyData policyData5 = new TkAutoPolicyData();
        policyData5.setPolicyDownLoadUrl("policyDownLoadUrl");
        policyViewList5.setPolicyData(policyData5);
        baseInfoD5.setPolicyViewList(policyViewList5);
        tkAutoRiskInfoD5.setBaseInfoD(baseInfoD5);
        final TkAutoRelatedInfoD tkAutoRelatedInfoD6 = new TkAutoRelatedInfoD();
        tkAutoRelatedInfoD6.setInsureFlag("insureFlag");
        tkAutoRelatedInfoD6.setInsureTypeCode("insureTypeCode");
        tkAutoRiskInfoD5.setRelatedInfoDList(Arrays.asList(tkAutoRelatedInfoD6));
        detailResp1.setRiskInfoList(Arrays.asList(tkAutoRiskInfoD5));
        when(mockCvtHelper.cvtAutoInfoList(tk1, Arrays.asList("value"), detailResp1, new HashMap<>())).thenReturn(Collections.emptyList());

        when(mockOrderMapper.updateOrderPaymentTime("orderId")).thenReturn(0);

        // Run the test
        final List<String> result = tkAutoOrderServiceUnderTest.syncSuccess("policyNo", tkDetail, "content");

        // Verify the results

        // Confirm AmPolicyTkMapper.insertUseGeneratedKeys(...).
        final AmPolicyTk record1 = new AmPolicyTk();
        record1.setPolicyNo("policyNo");
        record1.setSubPolicyNo("subPolicyNo");
        record1.setRiskName("riskName");
        record1.setRiskCode("riskCode");
        record1.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record1.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record1.setPolicyUrl("policyDownLoadUrl");
        record1.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record1.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record1.setBenchmarkPremium(new BigDecimal("0.00"));
        record1.setState(0);
        record1.setRemark("msg");

        // Confirm AmPolicyTkDetailMapper.insertUseGeneratedKeys(...).
        final AmPolicyTkDetail record2 = new AmPolicyTkDetail();
        record2.setPolicyNo("policyNo");
        record2.setContent("content");

        // Confirm SmOrderMapper.insertOrder(...).
        final OrderSubmitRequest orderSubmitRequest1 = new OrderSubmitRequest();
        final FhProduct productInfo1 = new FhProduct();
        productInfo1.setProductId("fhProductId");
        productInfo1.setRecommendId("recommendId");
        productInfo1.setRecommendMainJobNumber("recommendMainJobNumber");
        productInfo1.setRecommendOrgCode("recommendOrgCode");
        orderSubmitRequest1.setProductInfo(productInfo1);
        final FhOrderInfo orderInfo1 = new FhOrderInfo();
        orderInfo1.setSubmitTime("submitTime");
        orderInfo1.setStartTime("startTime");
        orderInfo1.setEndTime("endTime");
        orderSubmitRequest1.setOrderInfo(orderInfo1);
        final FhProposer proposerInfo1 = new FhProposer();
        proposerInfo1.setPersonName("insuredName");
        proposerInfo1.setPersonGender("insuredPersonGender");
        proposerInfo1.setIdType("idType");
        proposerInfo1.setIdNumber("idNumber");
        proposerInfo1.setBirthday("birthday");
        proposerInfo1.setCellPhone("cellPhone");
        proposerInfo1.setEmail("email");
        proposerInfo1.setAddress("address");
        proposerInfo1.setAppStatus("-2");
        proposerInfo1.setPolicyNo("insIdNumber");
        proposerInfo1.setDownloadURL("downloadURL");
        orderSubmitRequest1.setProposerInfo(proposerInfo1);
        final FhInsuredPerson fhInsuredPerson1 = new FhInsuredPerson();
        fhInsuredPerson1.setPersonName("insuredName");
        fhInsuredPerson1.setIdType("idType");
        fhInsuredPerson1.setIdNumber("idNumber");
        fhInsuredPerson1.setBirthday("birthday");
        fhInsuredPerson1.setCellPhone("cellPhone");
        fhInsuredPerson1.setAppStatus("-2");
        fhInsuredPerson1.setPolicyNo("insIdNumber");
        fhInsuredPerson1.setRelationship("99");
        fhInsuredPerson1.setFlightNo("flightNo");
        fhInsuredPerson1.setFlightTime("flightTime");
        fhInsuredPerson1.setOccupationCode("");
        fhInsuredPerson1.setOccupationGroup("occupationGroup");
        fhInsuredPerson1.setDestinationCountryText("destinationCountryText");
        fhInsuredPerson1.setAnnualIncome("annualIncome");
        fhInsuredPerson1.setStudentType("studentType");
        fhInsuredPerson1.setSchoolType("schoolType");
        fhInsuredPerson1.setSchoolNature("schoolNature");
        fhInsuredPerson1.setSchoolName("schoolName");
        fhInsuredPerson1.setSchoolClass("schoolClass");
        fhInsuredPerson1.setStudentId("studentId");
        fhInsuredPerson1.setIdPeriodStart("idPeriodStart");
        fhInsuredPerson1.setIdPeriodEnd("idPeriodEnd");
        fhInsuredPerson1.setSmoke("smoke");
        fhInsuredPerson1.setOldPolicyNo("oldPolicyNo");
        final TestPremiumDutyForm testPremiumDutyForm2 = new TestPremiumDutyForm();
        fhInsuredPerson1.setDuties(Arrays.asList(testPremiumDutyForm2));
        final TestPremiumProductForm product1 = new TestPremiumProductForm();
        product1.setProductId(0);
        product1.setPlanId(0);
        product1.setPlanCode("planCode");
        product1.setPremium(new BigDecimal("0.00"));
        product1.setQty(0);
        final TestPremiumDutyForm testPremiumDutyForm3 = new TestPremiumDutyForm();
        product1.setPremiumDutyForms(Arrays.asList(testPremiumDutyForm3));
        fhInsuredPerson1.setProduct(product1);
        orderSubmitRequest1.setInsuredPerson(Arrays.asList(fhInsuredPerson1));
        final FhProperty propertyInfo1 = new FhProperty();
        propertyInfo1.setPropertyInfoIsExist("0");
        orderSubmitRequest1.setPropertyInfo(propertyInfo1);
        final SmOrderHouseDTO house1 = new SmOrderHouseDTO();
        orderSubmitRequest1.setHouse(house1);
        final SmOrderCarInfoDTO carInfo1 = new SmOrderCarInfoDTO();
        orderSubmitRequest1.setCarInfo(carInfo1);
        orderSubmitRequest1.setProductType("code");
        orderSubmitRequest1.setBizCode("bizCode");
        orderSubmitRequest1.setOrderOutType("orderOutType");
        orderSubmitRequest1.setToken("token");
        orderSubmitRequest1.setPlanId(0);
        orderSubmitRequest1.setQty(0);
        orderSubmitRequest1.setPreOrderId("preOrderId");
        orderSubmitRequest1.setRenew(false);
        orderSubmitRequest1.setRealRenewFlag(false);
        orderSubmitRequest1.setAppNo("appNo");
        orderSubmitRequest1.setAgentId(0);
        orderSubmitRequest1.setJobCode("jobCode");
        final SmCreateOrderSubmitRequest dto = new SmCreateOrderSubmitRequest(orderSubmitRequest1);

        // Confirm SmOrderMapper.insertOrderApplicant(...).
        final OrderSubmitRequest orderSubmitRequest2 = new OrderSubmitRequest();
        final FhProduct productInfo2 = new FhProduct();
        productInfo2.setProductId("fhProductId");
        productInfo2.setRecommendId("recommendId");
        productInfo2.setRecommendMainJobNumber("recommendMainJobNumber");
        productInfo2.setRecommendOrgCode("recommendOrgCode");
        orderSubmitRequest2.setProductInfo(productInfo2);
        final FhOrderInfo orderInfo2 = new FhOrderInfo();
        orderInfo2.setSubmitTime("submitTime");
        orderInfo2.setStartTime("startTime");
        orderInfo2.setEndTime("endTime");
        orderSubmitRequest2.setOrderInfo(orderInfo2);
        final FhProposer proposerInfo2 = new FhProposer();
        proposerInfo2.setPersonName("insuredName");
        proposerInfo2.setPersonGender("insuredPersonGender");
        proposerInfo2.setIdType("idType");
        proposerInfo2.setIdNumber("idNumber");
        proposerInfo2.setBirthday("birthday");
        proposerInfo2.setCellPhone("cellPhone");
        proposerInfo2.setEmail("email");
        proposerInfo2.setAddress("address");
        proposerInfo2.setAppStatus("-2");
        proposerInfo2.setPolicyNo("insIdNumber");
        proposerInfo2.setDownloadURL("downloadURL");
        orderSubmitRequest2.setProposerInfo(proposerInfo2);
        final FhInsuredPerson fhInsuredPerson2 = new FhInsuredPerson();
        fhInsuredPerson2.setPersonName("insuredName");
        fhInsuredPerson2.setIdType("idType");
        fhInsuredPerson2.setIdNumber("idNumber");
        fhInsuredPerson2.setBirthday("birthday");
        fhInsuredPerson2.setCellPhone("cellPhone");
        fhInsuredPerson2.setAppStatus("-2");
        fhInsuredPerson2.setPolicyNo("insIdNumber");
        fhInsuredPerson2.setRelationship("99");
        fhInsuredPerson2.setFlightNo("flightNo");
        fhInsuredPerson2.setFlightTime("flightTime");
        fhInsuredPerson2.setOccupationCode("");
        fhInsuredPerson2.setOccupationGroup("occupationGroup");
        fhInsuredPerson2.setDestinationCountryText("destinationCountryText");
        fhInsuredPerson2.setAnnualIncome("annualIncome");
        fhInsuredPerson2.setStudentType("studentType");
        fhInsuredPerson2.setSchoolType("schoolType");
        fhInsuredPerson2.setSchoolNature("schoolNature");
        fhInsuredPerson2.setSchoolName("schoolName");
        fhInsuredPerson2.setSchoolClass("schoolClass");
        fhInsuredPerson2.setStudentId("studentId");
        fhInsuredPerson2.setIdPeriodStart("idPeriodStart");
        fhInsuredPerson2.setIdPeriodEnd("idPeriodEnd");
        fhInsuredPerson2.setSmoke("smoke");
        fhInsuredPerson2.setOldPolicyNo("oldPolicyNo");
        final TestPremiumDutyForm testPremiumDutyForm4 = new TestPremiumDutyForm();
        fhInsuredPerson2.setDuties(Arrays.asList(testPremiumDutyForm4));
        final TestPremiumProductForm product2 = new TestPremiumProductForm();
        product2.setProductId(0);
        product2.setPlanId(0);
        product2.setPlanCode("planCode");
        product2.setPremium(new BigDecimal("0.00"));
        product2.setQty(0);
        final TestPremiumDutyForm testPremiumDutyForm5 = new TestPremiumDutyForm();
        product2.setPremiumDutyForms(Arrays.asList(testPremiumDutyForm5));
        fhInsuredPerson2.setProduct(product2);
        orderSubmitRequest2.setInsuredPerson(Arrays.asList(fhInsuredPerson2));
        final FhProperty propertyInfo2 = new FhProperty();
        propertyInfo2.setPropertyInfoIsExist("0");
        orderSubmitRequest2.setPropertyInfo(propertyInfo2);
        final SmOrderHouseDTO house2 = new SmOrderHouseDTO();
        orderSubmitRequest2.setHouse(house2);
        final SmOrderCarInfoDTO carInfo2 = new SmOrderCarInfoDTO();
        orderSubmitRequest2.setCarInfo(carInfo2);
        orderSubmitRequest2.setProductType("code");
        orderSubmitRequest2.setBizCode("bizCode");
        orderSubmitRequest2.setOrderOutType("orderOutType");
        orderSubmitRequest2.setToken("token");
        orderSubmitRequest2.setPlanId(0);
        orderSubmitRequest2.setQty(0);
        orderSubmitRequest2.setPreOrderId("preOrderId");
        orderSubmitRequest2.setRenew(false);
        orderSubmitRequest2.setRealRenewFlag(false);
        orderSubmitRequest2.setAppNo("appNo");
        orderSubmitRequest2.setAgentId(0);
        orderSubmitRequest2.setJobCode("jobCode");
        final SmCreateOrderSubmitRequest dto1 = new SmCreateOrderSubmitRequest(orderSubmitRequest2);

        // Confirm SmOrderMapper.insertOrderInsured(...).
        final OrderSubmitRequest orderSubmitRequest3 = new OrderSubmitRequest();
        final FhProduct productInfo3 = new FhProduct();
        productInfo3.setProductId("fhProductId");
        productInfo3.setRecommendId("recommendId");
        productInfo3.setRecommendMainJobNumber("recommendMainJobNumber");
        productInfo3.setRecommendOrgCode("recommendOrgCode");
        orderSubmitRequest3.setProductInfo(productInfo3);
        final FhOrderInfo orderInfo3 = new FhOrderInfo();
        orderInfo3.setSubmitTime("submitTime");
        orderInfo3.setStartTime("startTime");
        orderInfo3.setEndTime("endTime");
        orderSubmitRequest3.setOrderInfo(orderInfo3);
        final FhProposer proposerInfo3 = new FhProposer();
        proposerInfo3.setPersonName("insuredName");
        proposerInfo3.setPersonGender("insuredPersonGender");
        proposerInfo3.setIdType("idType");
        proposerInfo3.setIdNumber("idNumber");
        proposerInfo3.setBirthday("birthday");
        proposerInfo3.setCellPhone("cellPhone");
        proposerInfo3.setEmail("email");
        proposerInfo3.setAddress("address");
        proposerInfo3.setAppStatus("-2");
        proposerInfo3.setPolicyNo("insIdNumber");
        proposerInfo3.setDownloadURL("downloadURL");
        orderSubmitRequest3.setProposerInfo(proposerInfo3);
        final FhInsuredPerson fhInsuredPerson3 = new FhInsuredPerson();
        fhInsuredPerson3.setPersonName("insuredName");
        fhInsuredPerson3.setIdType("idType");
        fhInsuredPerson3.setIdNumber("idNumber");
        fhInsuredPerson3.setBirthday("birthday");
        fhInsuredPerson3.setCellPhone("cellPhone");
        fhInsuredPerson3.setAppStatus("-2");
        fhInsuredPerson3.setPolicyNo("insIdNumber");
        fhInsuredPerson3.setRelationship("99");
        fhInsuredPerson3.setFlightNo("flightNo");
        fhInsuredPerson3.setFlightTime("flightTime");
        fhInsuredPerson3.setOccupationCode("");
        fhInsuredPerson3.setOccupationGroup("occupationGroup");
        fhInsuredPerson3.setDestinationCountryText("destinationCountryText");
        fhInsuredPerson3.setAnnualIncome("annualIncome");
        fhInsuredPerson3.setStudentType("studentType");
        fhInsuredPerson3.setSchoolType("schoolType");
        fhInsuredPerson3.setSchoolNature("schoolNature");
        fhInsuredPerson3.setSchoolName("schoolName");
        fhInsuredPerson3.setSchoolClass("schoolClass");
        fhInsuredPerson3.setStudentId("studentId");
        fhInsuredPerson3.setIdPeriodStart("idPeriodStart");
        fhInsuredPerson3.setIdPeriodEnd("idPeriodEnd");
        fhInsuredPerson3.setSmoke("smoke");
        fhInsuredPerson3.setOldPolicyNo("oldPolicyNo");
        final TestPremiumDutyForm testPremiumDutyForm6 = new TestPremiumDutyForm();
        fhInsuredPerson3.setDuties(Arrays.asList(testPremiumDutyForm6));
        final TestPremiumProductForm product3 = new TestPremiumProductForm();
        product3.setProductId(0);
        product3.setPlanId(0);
        product3.setPlanCode("planCode");
        product3.setPremium(new BigDecimal("0.00"));
        product3.setQty(0);
        final TestPremiumDutyForm testPremiumDutyForm7 = new TestPremiumDutyForm();
        product3.setPremiumDutyForms(Arrays.asList(testPremiumDutyForm7));
        fhInsuredPerson3.setProduct(product3);
        orderSubmitRequest3.setInsuredPerson(Arrays.asList(fhInsuredPerson3));
        final FhProperty propertyInfo3 = new FhProperty();
        propertyInfo3.setPropertyInfoIsExist("0");
        orderSubmitRequest3.setPropertyInfo(propertyInfo3);
        final SmOrderHouseDTO house3 = new SmOrderHouseDTO();
        orderSubmitRequest3.setHouse(house3);
        final SmOrderCarInfoDTO carInfo3 = new SmOrderCarInfoDTO();
        orderSubmitRequest3.setCarInfo(carInfo3);
        orderSubmitRequest3.setProductType("code");
        orderSubmitRequest3.setBizCode("bizCode");
        orderSubmitRequest3.setOrderOutType("orderOutType");
        orderSubmitRequest3.setToken("token");
        orderSubmitRequest3.setPlanId(0);
        orderSubmitRequest3.setQty(0);
        orderSubmitRequest3.setPreOrderId("preOrderId");
        orderSubmitRequest3.setRenew(false);
        orderSubmitRequest3.setRealRenewFlag(false);
        orderSubmitRequest3.setAppNo("appNo");
        orderSubmitRequest3.setAgentId(0);
        orderSubmitRequest3.setJobCode("jobCode");
        final SmCreateOrderSubmitRequest dto2 = new SmCreateOrderSubmitRequest(orderSubmitRequest3);
    }

    @Test
    public void testUpdateByPolicyNo() throws Exception {
        // Setup
        final AmPolicyTk update = new AmPolicyTk();
        update.setPolicyNo("policyNo");
        update.setSubPolicyNo("subPolicyNo");
        update.setRiskName("riskName");
        update.setRiskCode("riskCode");
        update.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        update.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        update.setPolicyUrl("policyDownLoadUrl");
        update.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        update.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        update.setBenchmarkPremium(new BigDecimal("0.00"));
        update.setState(0);
        update.setRemark("msg");

        // Run the test
        tkAutoOrderServiceUnderTest.updateByPolicyNo("policyNo", update);

        // Verify the results
        // Confirm AmPolicyTkMapper.updateByExampleSelective(...).
        final AmPolicyTk record = new AmPolicyTk();
        record.setPolicyNo("policyNo");
        record.setSubPolicyNo("subPolicyNo");
        record.setRiskName("riskName");
        record.setRiskCode("riskCode");
        record.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record.setPolicyUrl("policyDownLoadUrl");
        record.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record.setBenchmarkPremium(new BigDecimal("0.00"));
        record.setState(0);
        record.setRemark("msg");
    }

    @Test
    public void testUpdateBySubPolicyNo() throws Exception {
        // Setup
        final AmPolicyTk update = new AmPolicyTk();
        update.setPolicyNo("policyNo");
        update.setSubPolicyNo("subPolicyNo");
        update.setRiskName("riskName");
        update.setRiskCode("riskCode");
        update.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        update.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        update.setPolicyUrl("policyDownLoadUrl");
        update.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        update.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        update.setBenchmarkPremium(new BigDecimal("0.00"));
        update.setState(0);
        update.setRemark("msg");

        // Configure AmPolicyTkMapper.updateByExampleSelective(...).
        final AmPolicyTk record = new AmPolicyTk();
        record.setPolicyNo("policyNo");
        record.setSubPolicyNo("subPolicyNo");
        record.setRiskName("riskName");
        record.setRiskCode("riskCode");
        record.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record.setPolicyUrl("policyDownLoadUrl");
        record.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        record.setBenchmarkPremium(new BigDecimal("0.00"));
        record.setState(0);
        record.setRemark("msg");
        when(mockPolicyTkMapper.updateByExampleSelective(eq(record), any(Object.class))).thenReturn(0);

        // Run the test
        final int result = tkAutoOrderServiceUnderTest.updateBySubPolicyNo("subPolicyNo", update);

        // Verify the results
    }

    @Test
    public void testSelectNotSync() throws Exception {
        // Setup
        final AmPolicyTk amPolicyTk = new AmPolicyTk();
        amPolicyTk.setPolicyNo("policyNo");
        amPolicyTk.setSubPolicyNo("subPolicyNo");
        amPolicyTk.setRiskName("riskName");
        amPolicyTk.setRiskCode("riskCode");
        amPolicyTk.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk.setPolicyUrl("policyDownLoadUrl");
        amPolicyTk.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk.setBenchmarkPremium(new BigDecimal("0.00"));
        amPolicyTk.setState(0);
        amPolicyTk.setRemark("msg");
        final List<AmPolicyTk> expectedResult = Arrays.asList(amPolicyTk);

        // Configure AmPolicyTkMapper.selectByExample(...).
        final AmPolicyTk amPolicyTk1 = new AmPolicyTk();
        amPolicyTk1.setPolicyNo("policyNo");
        amPolicyTk1.setSubPolicyNo("subPolicyNo");
        amPolicyTk1.setRiskName("riskName");
        amPolicyTk1.setRiskCode("riskCode");
        amPolicyTk1.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk1.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk1.setPolicyUrl("policyDownLoadUrl");
        amPolicyTk1.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk1.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk1.setBenchmarkPremium(new BigDecimal("0.00"));
        amPolicyTk1.setState(0);
        amPolicyTk1.setRemark("msg");
        final List<AmPolicyTk> amPolicyTks = Arrays.asList(amPolicyTk1);
        when(mockPolicyTkMapper.selectByExample(any(Object.class))).thenReturn(amPolicyTks);

        // Run the test
        final List<AmPolicyTk> result = tkAutoOrderServiceUnderTest.selectNotSync();

        // Verify the results
    }

    @Test
    public void testSelectNotSync_AmPolicyTkMapperReturnsNoItems() throws Exception {
        // Setup
        when(mockPolicyTkMapper.selectByExample(any(Object.class))).thenReturn(Collections.emptyList());

        // Run the test
        final List<AmPolicyTk> result = tkAutoOrderServiceUnderTest.selectNotSync();

        // Verify the results
    }

    @Test
    public void testInsertTkList() throws Exception {
        // Setup
        final AmPolicyTk amPolicyTk = new AmPolicyTk();
        amPolicyTk.setPolicyNo("policyNo");
        amPolicyTk.setSubPolicyNo("subPolicyNo");
        amPolicyTk.setRiskName("riskName");
        amPolicyTk.setRiskCode("riskCode");
        amPolicyTk.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk.setPolicyUrl("policyDownLoadUrl");
        amPolicyTk.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk.setBenchmarkPremium(new BigDecimal("0.00"));
        amPolicyTk.setState(0);
        amPolicyTk.setRemark("msg");
        final List<AmPolicyTk> notExists = Arrays.asList(amPolicyTk);

        // Run the test
        tkAutoOrderServiceUnderTest.insertTkList(notExists);

        // Verify the results
        // Confirm AmPolicyTkMapper.insertList(...).
        final AmPolicyTk amPolicyTk1 = new AmPolicyTk();
        amPolicyTk1.setPolicyNo("policyNo");
        amPolicyTk1.setSubPolicyNo("subPolicyNo");
        amPolicyTk1.setRiskName("riskName");
        amPolicyTk1.setRiskCode("riskCode");
        amPolicyTk1.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk1.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk1.setPolicyUrl("policyDownLoadUrl");
        amPolicyTk1.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk1.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk1.setBenchmarkPremium(new BigDecimal("0.00"));
        amPolicyTk1.setState(0);
        amPolicyTk1.setRemark("msg");
        final List<AmPolicyTk> recordList = Arrays.asList(amPolicyTk1);
    }

    @Test
    public void testSelectTkRecordByDate() throws Exception {
        // Setup
        final AmPolicyTk amPolicyTk = new AmPolicyTk();
        amPolicyTk.setPolicyNo("policyNo");
        amPolicyTk.setSubPolicyNo("subPolicyNo");
        amPolicyTk.setRiskName("riskName");
        amPolicyTk.setRiskCode("riskCode");
        amPolicyTk.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk.setPolicyUrl("policyDownLoadUrl");
        amPolicyTk.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk.setBenchmarkPremium(new BigDecimal("0.00"));
        amPolicyTk.setState(0);
        amPolicyTk.setRemark("msg");
        final List<AmPolicyTk> expectedResult = Arrays.asList(amPolicyTk);

        // Configure AmPolicyTkMapper.selectByExample(...).
        final AmPolicyTk amPolicyTk1 = new AmPolicyTk();
        amPolicyTk1.setPolicyNo("policyNo");
        amPolicyTk1.setSubPolicyNo("subPolicyNo");
        amPolicyTk1.setRiskName("riskName");
        amPolicyTk1.setRiskCode("riskCode");
        amPolicyTk1.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk1.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk1.setPolicyUrl("policyDownLoadUrl");
        amPolicyTk1.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk1.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        amPolicyTk1.setBenchmarkPremium(new BigDecimal("0.00"));
        amPolicyTk1.setState(0);
        amPolicyTk1.setRemark("msg");
        final List<AmPolicyTk> amPolicyTks = Arrays.asList(amPolicyTk1);
        when(mockPolicyTkMapper.selectByExample(any(Object.class))).thenReturn(amPolicyTks);

        // Run the test
        final List<AmPolicyTk> result = tkAutoOrderServiceUnderTest.selectTkRecordByDate(LocalDate.of(2020, 1, 1), LocalDate.of(2020, 1, 1));

        // Verify the results
    }

    @Test
    public void testSelectTkRecordByDate_AmPolicyTkMapperReturnsNoItems() throws Exception {
        // Setup
        when(mockPolicyTkMapper.selectByExample(any(Object.class))).thenReturn(Collections.emptyList());

        // Run the test
        final List<AmPolicyTk> result = tkAutoOrderServiceUnderTest.selectTkRecordByDate(LocalDate.of(2020, 1, 1), LocalDate.of(2020, 1, 1));

        // Verify the results
    }

    @Test
    public void testChannel() throws Exception {
    }

    @Test
    public void testOrderService() throws Exception {
        // Setup
        // Run the test
        final ChannelOrderService result = tkAutoOrderServiceUnderTest.orderService();

        // Verify the results
    }

    @Test
    public void testSupport() throws Exception {
        assertFalse(tkAutoOrderServiceUnderTest.support("channel"));
    }

    @Test
    public void testUpdateOrderPolicyInfo() throws Exception {
        // Setup
        final Map<String, String> expectedResult = new HashMap<>();

        // Run the test
        final Map<String, String> result = tkAutoOrderServiceUnderTest.updateOrderPolicyInfo("orderId");

        // Verify the results
    }

    @Test(expected = UnsupportedOperationException.class)
    public void testSubmitSuccessAfter() throws Exception {
        tkAutoOrderServiceUnderTest.submitSuccessAfter("userUniqueId", new SmPlanVO(), new OrderSubmitRequest(), new OrderSubmitRequest(), new OrderSubmitResponse());
    }

    @Test(expected = UnsupportedOperationException.class)
    public void testSubmitOrder() throws Exception {
        tkAutoOrderServiceUnderTest.submitOrder("userUniqueId", new OrderSubmitRequest());
    }

    @Test
    public void testSaveOrderInfo() throws Exception {
        // Setup
        final OrderSubmitRequest dto = new OrderSubmitRequest();
        final FhProduct productInfo = new FhProduct();
        productInfo.setProductId("fhProductId");
        productInfo.setRecommendId("recommendId");
        productInfo.setRecommendMainJobNumber("recommendMainJobNumber");
        productInfo.setRecommendOrgCode("recommendOrgCode");
        dto.setProductInfo(productInfo);
        final FhOrderInfo orderInfo = new FhOrderInfo();
        orderInfo.setSubmitTime("submitTime");
        orderInfo.setStartTime("startTime");
        orderInfo.setEndTime("endTime");
        dto.setOrderInfo(orderInfo);
        final FhProposer proposerInfo = new FhProposer();
        proposerInfo.setPersonName("insuredName");
        proposerInfo.setPersonGender("insuredPersonGender");
        proposerInfo.setIdType("idType");
        proposerInfo.setIdNumber("idNumber");
        proposerInfo.setBirthday("birthday");
        proposerInfo.setCellPhone("cellPhone");
        proposerInfo.setEmail("email");
        proposerInfo.setAddress("address");
        proposerInfo.setAppStatus("-2");
        proposerInfo.setPolicyNo("insIdNumber");
        proposerInfo.setDownloadURL("downloadURL");
        dto.setProposerInfo(proposerInfo);
        final FhInsuredPerson fhInsuredPerson = new FhInsuredPerson();
        fhInsuredPerson.setPersonName("insuredName");
        fhInsuredPerson.setIdType("idType");
        fhInsuredPerson.setIdNumber("idNumber");
        fhInsuredPerson.setBirthday("birthday");
        fhInsuredPerson.setCellPhone("cellPhone");
        fhInsuredPerson.setAppStatus("-2");
        fhInsuredPerson.setPolicyNo("insIdNumber");
        fhInsuredPerson.setRelationship("99");
        fhInsuredPerson.setFlightNo("flightNo");
        fhInsuredPerson.setFlightTime("flightTime");
        fhInsuredPerson.setOccupationCode("");
        fhInsuredPerson.setOccupationGroup("occupationGroup");
        fhInsuredPerson.setDestinationCountryText("destinationCountryText");
        fhInsuredPerson.setAnnualIncome("annualIncome");
        fhInsuredPerson.setStudentType("studentType");
        fhInsuredPerson.setSchoolType("schoolType");
        fhInsuredPerson.setSchoolNature("schoolNature");
        fhInsuredPerson.setSchoolName("schoolName");
        fhInsuredPerson.setSchoolClass("schoolClass");
        fhInsuredPerson.setStudentId("studentId");
        fhInsuredPerson.setIdPeriodStart("idPeriodStart");
        fhInsuredPerson.setIdPeriodEnd("idPeriodEnd");
        fhInsuredPerson.setSmoke("smoke");
        fhInsuredPerson.setOldPolicyNo("oldPolicyNo");
        final TestPremiumDutyForm testPremiumDutyForm = new TestPremiumDutyForm();
        fhInsuredPerson.setDuties(Arrays.asList(testPremiumDutyForm));
        final TestPremiumProductForm product = new TestPremiumProductForm();
        product.setProductId(0);
        product.setPlanId(0);
        product.setPlanCode("planCode");
        product.setPremium(new BigDecimal("0.00"));
        product.setQty(0);
        final TestPremiumDutyForm testPremiumDutyForm1 = new TestPremiumDutyForm();
        product.setPremiumDutyForms(Arrays.asList(testPremiumDutyForm1));
        fhInsuredPerson.setProduct(product);
        dto.setInsuredPerson(Arrays.asList(fhInsuredPerson));
        final FhProperty propertyInfo = new FhProperty();
        propertyInfo.setPropertyInfoIsExist("0");
        dto.setPropertyInfo(propertyInfo);
        final SmOrderHouseDTO house = new SmOrderHouseDTO();
        dto.setHouse(house);
        final SmOrderCarInfoDTO carInfo = new SmOrderCarInfoDTO();
        dto.setCarInfo(carInfo);
        dto.setProductType("code");
        dto.setBizCode("bizCode");
        dto.setOrderOutType("orderOutType");
        dto.setToken("token");
        dto.setPlanId(0);
        dto.setQty(0);
        dto.setPreOrderId("preOrderId");
        dto.setRenew(false);
        dto.setRealRenewFlag(false);
        dto.setAppNo("appNo");
        dto.setAgentId(0);
        dto.setJobCode("jobCode");

        final OrderSubmitResponse smOrderResp = new OrderSubmitResponse();
        smOrderResp.setNoticeCode("-1");
        smOrderResp.setNoticeMsg("noticeMsg");
        smOrderResp.setOrderId("orderId");
        smOrderResp.setAppNo("appNo");
        final OrderSubmitResponse.ReturnMap returnMap = new OrderSubmitResponse.ReturnMap();
        returnMap.setMsg("msg");
        returnMap.setRenewOrderSn("orderId");
        smOrderResp.setReturnMap(returnMap);

        final SmPlanVO planVo = new SmPlanVO();
        planVo.setId(0);
        planVo.setPlanId(0);
        planVo.setChannel("channel");
        planVo.setProductId(0);
        planVo.setFhProductId("fhProductId");
        planVo.setBuyLimit(0);
        planVo.setPlanCode("fhProductId");

        // Run the test
        tkAutoOrderServiceUnderTest.saveOrderInfo(dto, smOrderResp, planVo);

        // Verify the results
        // Confirm SmOrderMapper.insertOrder(...).
        final OrderSubmitRequest orderSubmitRequest = new OrderSubmitRequest();
        final FhProduct productInfo1 = new FhProduct();
        productInfo1.setProductId("fhProductId");
        productInfo1.setRecommendId("recommendId");
        productInfo1.setRecommendMainJobNumber("recommendMainJobNumber");
        productInfo1.setRecommendOrgCode("recommendOrgCode");
        orderSubmitRequest.setProductInfo(productInfo1);
        final FhOrderInfo orderInfo1 = new FhOrderInfo();
        orderInfo1.setSubmitTime("submitTime");
        orderInfo1.setStartTime("startTime");
        orderInfo1.setEndTime("endTime");
        orderSubmitRequest.setOrderInfo(orderInfo1);
        final FhProposer proposerInfo1 = new FhProposer();
        proposerInfo1.setPersonName("insuredName");
        proposerInfo1.setPersonGender("insuredPersonGender");
        proposerInfo1.setIdType("idType");
        proposerInfo1.setIdNumber("idNumber");
        proposerInfo1.setBirthday("birthday");
        proposerInfo1.setCellPhone("cellPhone");
        proposerInfo1.setEmail("email");
        proposerInfo1.setAddress("address");
        proposerInfo1.setAppStatus("-2");
        proposerInfo1.setPolicyNo("insIdNumber");
        proposerInfo1.setDownloadURL("downloadURL");
        orderSubmitRequest.setProposerInfo(proposerInfo1);
        final FhInsuredPerson fhInsuredPerson1 = new FhInsuredPerson();
        fhInsuredPerson1.setPersonName("insuredName");
        fhInsuredPerson1.setIdType("idType");
        fhInsuredPerson1.setIdNumber("idNumber");
        fhInsuredPerson1.setBirthday("birthday");
        fhInsuredPerson1.setCellPhone("cellPhone");
        fhInsuredPerson1.setAppStatus("-2");
        fhInsuredPerson1.setPolicyNo("insIdNumber");
        fhInsuredPerson1.setRelationship("99");
        fhInsuredPerson1.setFlightNo("flightNo");
        fhInsuredPerson1.setFlightTime("flightTime");
        fhInsuredPerson1.setOccupationCode("");
        fhInsuredPerson1.setOccupationGroup("occupationGroup");
        fhInsuredPerson1.setDestinationCountryText("destinationCountryText");
        fhInsuredPerson1.setAnnualIncome("annualIncome");
        fhInsuredPerson1.setStudentType("studentType");
        fhInsuredPerson1.setSchoolType("schoolType");
        fhInsuredPerson1.setSchoolNature("schoolNature");
        fhInsuredPerson1.setSchoolName("schoolName");
        fhInsuredPerson1.setSchoolClass("schoolClass");
        fhInsuredPerson1.setStudentId("studentId");
        fhInsuredPerson1.setIdPeriodStart("idPeriodStart");
        fhInsuredPerson1.setIdPeriodEnd("idPeriodEnd");
        fhInsuredPerson1.setSmoke("smoke");
        fhInsuredPerson1.setOldPolicyNo("oldPolicyNo");
        final TestPremiumDutyForm testPremiumDutyForm2 = new TestPremiumDutyForm();
        fhInsuredPerson1.setDuties(Arrays.asList(testPremiumDutyForm2));
        final TestPremiumProductForm product1 = new TestPremiumProductForm();
        product1.setProductId(0);
        product1.setPlanId(0);
        product1.setPlanCode("planCode");
        product1.setPremium(new BigDecimal("0.00"));
        product1.setQty(0);
        final TestPremiumDutyForm testPremiumDutyForm3 = new TestPremiumDutyForm();
        product1.setPremiumDutyForms(Arrays.asList(testPremiumDutyForm3));
        fhInsuredPerson1.setProduct(product1);
        orderSubmitRequest.setInsuredPerson(Arrays.asList(fhInsuredPerson1));
        final FhProperty propertyInfo1 = new FhProperty();
        propertyInfo1.setPropertyInfoIsExist("0");
        orderSubmitRequest.setPropertyInfo(propertyInfo1);
        final SmOrderHouseDTO house1 = new SmOrderHouseDTO();
        orderSubmitRequest.setHouse(house1);
        final SmOrderCarInfoDTO carInfo1 = new SmOrderCarInfoDTO();
        orderSubmitRequest.setCarInfo(carInfo1);
        orderSubmitRequest.setProductType("code");
        orderSubmitRequest.setBizCode("bizCode");
        orderSubmitRequest.setOrderOutType("orderOutType");
        orderSubmitRequest.setToken("token");
        orderSubmitRequest.setPlanId(0);
        orderSubmitRequest.setQty(0);
        orderSubmitRequest.setPreOrderId("preOrderId");
        orderSubmitRequest.setRenew(false);
        orderSubmitRequest.setRealRenewFlag(false);
        orderSubmitRequest.setAppNo("appNo");
        orderSubmitRequest.setAgentId(0);
        orderSubmitRequest.setJobCode("jobCode");
        final SmCreateOrderSubmitRequest dto1 = new SmCreateOrderSubmitRequest(orderSubmitRequest);

        // Confirm SmOrderMapper.insertOrderApplicant(...).
        final OrderSubmitRequest orderSubmitRequest1 = new OrderSubmitRequest();
        final FhProduct productInfo2 = new FhProduct();
        productInfo2.setProductId("fhProductId");
        productInfo2.setRecommendId("recommendId");
        productInfo2.setRecommendMainJobNumber("recommendMainJobNumber");
        productInfo2.setRecommendOrgCode("recommendOrgCode");
        orderSubmitRequest1.setProductInfo(productInfo2);
        final FhOrderInfo orderInfo2 = new FhOrderInfo();
        orderInfo2.setSubmitTime("submitTime");
        orderInfo2.setStartTime("startTime");
        orderInfo2.setEndTime("endTime");
        orderSubmitRequest1.setOrderInfo(orderInfo2);
        final FhProposer proposerInfo2 = new FhProposer();
        proposerInfo2.setPersonName("insuredName");
        proposerInfo2.setPersonGender("insuredPersonGender");
        proposerInfo2.setIdType("idType");
        proposerInfo2.setIdNumber("idNumber");
        proposerInfo2.setBirthday("birthday");
        proposerInfo2.setCellPhone("cellPhone");
        proposerInfo2.setEmail("email");
        proposerInfo2.setAddress("address");
        proposerInfo2.setAppStatus("-2");
        proposerInfo2.setPolicyNo("insIdNumber");
        proposerInfo2.setDownloadURL("downloadURL");
        orderSubmitRequest1.setProposerInfo(proposerInfo2);
        final FhInsuredPerson fhInsuredPerson2 = new FhInsuredPerson();
        fhInsuredPerson2.setPersonName("insuredName");
        fhInsuredPerson2.setIdType("idType");
        fhInsuredPerson2.setIdNumber("idNumber");
        fhInsuredPerson2.setBirthday("birthday");
        fhInsuredPerson2.setCellPhone("cellPhone");
        fhInsuredPerson2.setAppStatus("-2");
        fhInsuredPerson2.setPolicyNo("insIdNumber");
        fhInsuredPerson2.setRelationship("99");
        fhInsuredPerson2.setFlightNo("flightNo");
        fhInsuredPerson2.setFlightTime("flightTime");
        fhInsuredPerson2.setOccupationCode("");
        fhInsuredPerson2.setOccupationGroup("occupationGroup");
        fhInsuredPerson2.setDestinationCountryText("destinationCountryText");
        fhInsuredPerson2.setAnnualIncome("annualIncome");
        fhInsuredPerson2.setStudentType("studentType");
        fhInsuredPerson2.setSchoolType("schoolType");
        fhInsuredPerson2.setSchoolNature("schoolNature");
        fhInsuredPerson2.setSchoolName("schoolName");
        fhInsuredPerson2.setSchoolClass("schoolClass");
        fhInsuredPerson2.setStudentId("studentId");
        fhInsuredPerson2.setIdPeriodStart("idPeriodStart");
        fhInsuredPerson2.setIdPeriodEnd("idPeriodEnd");
        fhInsuredPerson2.setSmoke("smoke");
        fhInsuredPerson2.setOldPolicyNo("oldPolicyNo");
        final TestPremiumDutyForm testPremiumDutyForm4 = new TestPremiumDutyForm();
        fhInsuredPerson2.setDuties(Arrays.asList(testPremiumDutyForm4));
        final TestPremiumProductForm product2 = new TestPremiumProductForm();
        product2.setProductId(0);
        product2.setPlanId(0);
        product2.setPlanCode("planCode");
        product2.setPremium(new BigDecimal("0.00"));
        product2.setQty(0);
        final TestPremiumDutyForm testPremiumDutyForm5 = new TestPremiumDutyForm();
        product2.setPremiumDutyForms(Arrays.asList(testPremiumDutyForm5));
        fhInsuredPerson2.setProduct(product2);
        orderSubmitRequest1.setInsuredPerson(Arrays.asList(fhInsuredPerson2));
        final FhProperty propertyInfo2 = new FhProperty();
        propertyInfo2.setPropertyInfoIsExist("0");
        orderSubmitRequest1.setPropertyInfo(propertyInfo2);
        final SmOrderHouseDTO house2 = new SmOrderHouseDTO();
        orderSubmitRequest1.setHouse(house2);
        final SmOrderCarInfoDTO carInfo2 = new SmOrderCarInfoDTO();
        orderSubmitRequest1.setCarInfo(carInfo2);
        orderSubmitRequest1.setProductType("code");
        orderSubmitRequest1.setBizCode("bizCode");
        orderSubmitRequest1.setOrderOutType("orderOutType");
        orderSubmitRequest1.setToken("token");
        orderSubmitRequest1.setPlanId(0);
        orderSubmitRequest1.setQty(0);
        orderSubmitRequest1.setPreOrderId("preOrderId");
        orderSubmitRequest1.setRenew(false);
        orderSubmitRequest1.setRealRenewFlag(false);
        orderSubmitRequest1.setAppNo("appNo");
        orderSubmitRequest1.setAgentId(0);
        orderSubmitRequest1.setJobCode("jobCode");
        final SmCreateOrderSubmitRequest dto2 = new SmCreateOrderSubmitRequest(orderSubmitRequest1);

        // Confirm SmOrderMapper.insertOrderInsured(...).
        final OrderSubmitRequest orderSubmitRequest2 = new OrderSubmitRequest();
        final FhProduct productInfo3 = new FhProduct();
        productInfo3.setProductId("fhProductId");
        productInfo3.setRecommendId("recommendId");
        productInfo3.setRecommendMainJobNumber("recommendMainJobNumber");
        productInfo3.setRecommendOrgCode("recommendOrgCode");
        orderSubmitRequest2.setProductInfo(productInfo3);
        final FhOrderInfo orderInfo3 = new FhOrderInfo();
        orderInfo3.setSubmitTime("submitTime");
        orderInfo3.setStartTime("startTime");
        orderInfo3.setEndTime("endTime");
        orderSubmitRequest2.setOrderInfo(orderInfo3);
        final FhProposer proposerInfo3 = new FhProposer();
        proposerInfo3.setPersonName("insuredName");
        proposerInfo3.setPersonGender("insuredPersonGender");
        proposerInfo3.setIdType("idType");
        proposerInfo3.setIdNumber("idNumber");
        proposerInfo3.setBirthday("birthday");
        proposerInfo3.setCellPhone("cellPhone");
        proposerInfo3.setEmail("email");
        proposerInfo3.setAddress("address");
        proposerInfo3.setAppStatus("-2");
        proposerInfo3.setPolicyNo("insIdNumber");
        proposerInfo3.setDownloadURL("downloadURL");
        orderSubmitRequest2.setProposerInfo(proposerInfo3);
        final FhInsuredPerson fhInsuredPerson3 = new FhInsuredPerson();
        fhInsuredPerson3.setPersonName("insuredName");
        fhInsuredPerson3.setIdType("idType");
        fhInsuredPerson3.setIdNumber("idNumber");
        fhInsuredPerson3.setBirthday("birthday");
        fhInsuredPerson3.setCellPhone("cellPhone");
        fhInsuredPerson3.setAppStatus("-2");
        fhInsuredPerson3.setPolicyNo("insIdNumber");
        fhInsuredPerson3.setRelationship("99");
        fhInsuredPerson3.setFlightNo("flightNo");
        fhInsuredPerson3.setFlightTime("flightTime");
        fhInsuredPerson3.setOccupationCode("");
        fhInsuredPerson3.setOccupationGroup("occupationGroup");
        fhInsuredPerson3.setDestinationCountryText("destinationCountryText");
        fhInsuredPerson3.setAnnualIncome("annualIncome");
        fhInsuredPerson3.setStudentType("studentType");
        fhInsuredPerson3.setSchoolType("schoolType");
        fhInsuredPerson3.setSchoolNature("schoolNature");
        fhInsuredPerson3.setSchoolName("schoolName");
        fhInsuredPerson3.setSchoolClass("schoolClass");
        fhInsuredPerson3.setStudentId("studentId");
        fhInsuredPerson3.setIdPeriodStart("idPeriodStart");
        fhInsuredPerson3.setIdPeriodEnd("idPeriodEnd");
        fhInsuredPerson3.setSmoke("smoke");
        fhInsuredPerson3.setOldPolicyNo("oldPolicyNo");
        final TestPremiumDutyForm testPremiumDutyForm6 = new TestPremiumDutyForm();
        fhInsuredPerson3.setDuties(Arrays.asList(testPremiumDutyForm6));
        final TestPremiumProductForm product3 = new TestPremiumProductForm();
        product3.setProductId(0);
        product3.setPlanId(0);
        product3.setPlanCode("planCode");
        product3.setPremium(new BigDecimal("0.00"));
        product3.setQty(0);
        final TestPremiumDutyForm testPremiumDutyForm7 = new TestPremiumDutyForm();
        product3.setPremiumDutyForms(Arrays.asList(testPremiumDutyForm7));
        fhInsuredPerson3.setProduct(product3);
        orderSubmitRequest2.setInsuredPerson(Arrays.asList(fhInsuredPerson3));
        final FhProperty propertyInfo3 = new FhProperty();
        propertyInfo3.setPropertyInfoIsExist("0");
        orderSubmitRequest2.setPropertyInfo(propertyInfo3);
        final SmOrderHouseDTO house3 = new SmOrderHouseDTO();
        orderSubmitRequest2.setHouse(house3);
        final SmOrderCarInfoDTO carInfo3 = new SmOrderCarInfoDTO();
        orderSubmitRequest2.setCarInfo(carInfo3);
        orderSubmitRequest2.setProductType("code");
        orderSubmitRequest2.setBizCode("bizCode");
        orderSubmitRequest2.setOrderOutType("orderOutType");
        orderSubmitRequest2.setToken("token");
        orderSubmitRequest2.setPlanId(0);
        orderSubmitRequest2.setQty(0);
        orderSubmitRequest2.setPreOrderId("preOrderId");
        orderSubmitRequest2.setRenew(false);
        orderSubmitRequest2.setRealRenewFlag(false);
        orderSubmitRequest2.setAppNo("appNo");
        orderSubmitRequest2.setAgentId(0);
        orderSubmitRequest2.setJobCode("jobCode");
        final SmCreateOrderSubmitRequest dto3 = new SmCreateOrderSubmitRequest(orderSubmitRequest2);
    }

    @Test
    public void testSaveOrderInfoForTkDriving() throws Exception {
        // Setup
        final OrderSubmitRequest dto = new OrderSubmitRequest();
        final FhProduct productInfo = new FhProduct();
        productInfo.setProductId("fhProductId");
        productInfo.setRecommendId("recommendId");
        productInfo.setRecommendMainJobNumber("recommendMainJobNumber");
        productInfo.setRecommendOrgCode("recommendOrgCode");
        dto.setProductInfo(productInfo);
        final FhOrderInfo orderInfo = new FhOrderInfo();
        orderInfo.setSubmitTime("submitTime");
        orderInfo.setStartTime("startTime");
        orderInfo.setEndTime("endTime");
        dto.setOrderInfo(orderInfo);
        final FhProposer proposerInfo = new FhProposer();
        proposerInfo.setPersonName("insuredName");
        proposerInfo.setPersonGender("insuredPersonGender");
        proposerInfo.setIdType("idType");
        proposerInfo.setIdNumber("idNumber");
        proposerInfo.setBirthday("birthday");
        proposerInfo.setCellPhone("cellPhone");
        proposerInfo.setEmail("email");
        proposerInfo.setAddress("address");
        proposerInfo.setAppStatus("-2");
        proposerInfo.setPolicyNo("insIdNumber");
        proposerInfo.setDownloadURL("downloadURL");
        dto.setProposerInfo(proposerInfo);
        final FhInsuredPerson fhInsuredPerson = new FhInsuredPerson();
        fhInsuredPerson.setPersonName("insuredName");
        fhInsuredPerson.setIdType("idType");
        fhInsuredPerson.setIdNumber("idNumber");
        fhInsuredPerson.setBirthday("birthday");
        fhInsuredPerson.setCellPhone("cellPhone");
        fhInsuredPerson.setAppStatus("-2");
        fhInsuredPerson.setPolicyNo("insIdNumber");
        fhInsuredPerson.setRelationship("99");
        fhInsuredPerson.setFlightNo("flightNo");
        fhInsuredPerson.setFlightTime("flightTime");
        fhInsuredPerson.setOccupationCode("");
        fhInsuredPerson.setOccupationGroup("occupationGroup");
        fhInsuredPerson.setDestinationCountryText("destinationCountryText");
        fhInsuredPerson.setAnnualIncome("annualIncome");
        fhInsuredPerson.setStudentType("studentType");
        fhInsuredPerson.setSchoolType("schoolType");
        fhInsuredPerson.setSchoolNature("schoolNature");
        fhInsuredPerson.setSchoolName("schoolName");
        fhInsuredPerson.setSchoolClass("schoolClass");
        fhInsuredPerson.setStudentId("studentId");
        fhInsuredPerson.setIdPeriodStart("idPeriodStart");
        fhInsuredPerson.setIdPeriodEnd("idPeriodEnd");
        fhInsuredPerson.setSmoke("smoke");
        fhInsuredPerson.setOldPolicyNo("oldPolicyNo");
        final TestPremiumDutyForm testPremiumDutyForm = new TestPremiumDutyForm();
        fhInsuredPerson.setDuties(Arrays.asList(testPremiumDutyForm));
        final TestPremiumProductForm product = new TestPremiumProductForm();
        product.setProductId(0);
        product.setPlanId(0);
        product.setPlanCode("planCode");
        product.setPremium(new BigDecimal("0.00"));
        product.setQty(0);
        final TestPremiumDutyForm testPremiumDutyForm1 = new TestPremiumDutyForm();
        product.setPremiumDutyForms(Arrays.asList(testPremiumDutyForm1));
        fhInsuredPerson.setProduct(product);
        dto.setInsuredPerson(Arrays.asList(fhInsuredPerson));
        final FhProperty propertyInfo = new FhProperty();
        propertyInfo.setPropertyInfoIsExist("0");
        dto.setPropertyInfo(propertyInfo);
        final SmOrderHouseDTO house = new SmOrderHouseDTO();
        dto.setHouse(house);
        final SmOrderCarInfoDTO carInfo = new SmOrderCarInfoDTO();
        dto.setCarInfo(carInfo);
        dto.setProductType("code");
        dto.setBizCode("bizCode");
        dto.setOrderOutType("orderOutType");
        dto.setToken("token");
        dto.setPlanId(0);
        dto.setQty(0);
        dto.setPreOrderId("preOrderId");
        dto.setRenew(false);
        dto.setRealRenewFlag(false);
        dto.setAppNo("appNo");
        dto.setAgentId(0);
        dto.setJobCode("jobCode");

        final OrderSubmitResponse smOrderResp = new OrderSubmitResponse();
        smOrderResp.setNoticeCode("-1");
        smOrderResp.setNoticeMsg("noticeMsg");
        smOrderResp.setOrderId("orderId");
        smOrderResp.setAppNo("appNo");
        final OrderSubmitResponse.ReturnMap returnMap = new OrderSubmitResponse.ReturnMap();
        returnMap.setMsg("msg");
        returnMap.setRenewOrderSn("orderId");
        smOrderResp.setReturnMap(returnMap);

        final SmPlanVO planVo = new SmPlanVO();
        planVo.setId(0);
        planVo.setPlanId(0);
        planVo.setChannel("channel");
        planVo.setProductId(0);
        planVo.setFhProductId("fhProductId");
        planVo.setBuyLimit(0);
        planVo.setPlanCode("fhProductId");

        final TkAutoRiskInfoD drivingTkAutoRiskInfoD = new TkAutoRiskInfoD();
        drivingTkAutoRiskInfoD.setRiskCode("riskCode");
        final TkAutoBaseInfoD baseInfoD = new TkAutoBaseInfoD();
        baseInfoD.setSubPolicyNo("subPolicyNo");
        baseInfoD.setRiskName("riskName");
        baseInfoD.setIssueDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseInfoD.setPremium(new BigDecimal("0.00"));
        baseInfoD.setInputDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final TkAutoPolicyViewList policyViewList = new TkAutoPolicyViewList();
        final TkAutoPolicyData policyData = new TkAutoPolicyData();
        policyData.setPolicyDownLoadUrl("policyDownLoadUrl");
        policyViewList.setPolicyData(policyData);
        baseInfoD.setPolicyViewList(policyViewList);
        drivingTkAutoRiskInfoD.setBaseInfoD(baseInfoD);
        final TkAutoRelatedInfoD tkAutoRelatedInfoD = new TkAutoRelatedInfoD();
        tkAutoRelatedInfoD.setInsureFlag("insureFlag");
        tkAutoRelatedInfoD.setInsureTypeCode("insureTypeCode");
        drivingTkAutoRiskInfoD.setRelatedInfoDList(Arrays.asList(tkAutoRelatedInfoD));

        when(mockOrderMapper.updateOrderPaymentTime("orderId")).thenReturn(0);

        try{
            // Run the test
            tkAutoOrderServiceUnderTest.saveOrderInfoForTkDriving(dto, smOrderResp, planVo, drivingTkAutoRiskInfoD);
        } catch(Exception e) {
        } finally {
        }

        // Verify the results
        // Confirm SmOrderMapper.insertOrder(...).
        final OrderSubmitRequest orderSubmitRequest = new OrderSubmitRequest();
        final FhProduct productInfo1 = new FhProduct();
        productInfo1.setProductId("fhProductId");
        productInfo1.setRecommendId("recommendId");
        productInfo1.setRecommendMainJobNumber("recommendMainJobNumber");
        productInfo1.setRecommendOrgCode("recommendOrgCode");
        orderSubmitRequest.setProductInfo(productInfo1);
        final FhOrderInfo orderInfo1 = new FhOrderInfo();
        orderInfo1.setSubmitTime("submitTime");
        orderInfo1.setStartTime("startTime");
        orderInfo1.setEndTime("endTime");
        orderSubmitRequest.setOrderInfo(orderInfo1);
        final FhProposer proposerInfo1 = new FhProposer();
        proposerInfo1.setPersonName("insuredName");
        proposerInfo1.setPersonGender("insuredPersonGender");
        proposerInfo1.setIdType("idType");
        proposerInfo1.setIdNumber("idNumber");
        proposerInfo1.setBirthday("birthday");
        proposerInfo1.setCellPhone("cellPhone");
        proposerInfo1.setEmail("email");
        proposerInfo1.setAddress("address");
        proposerInfo1.setAppStatus("-2");
        proposerInfo1.setPolicyNo("insIdNumber");
        proposerInfo1.setDownloadURL("downloadURL");
        orderSubmitRequest.setProposerInfo(proposerInfo1);
        final FhInsuredPerson fhInsuredPerson1 = new FhInsuredPerson();
        fhInsuredPerson1.setPersonName("insuredName");
        fhInsuredPerson1.setIdType("idType");
        fhInsuredPerson1.setIdNumber("idNumber");
        fhInsuredPerson1.setBirthday("birthday");
        fhInsuredPerson1.setCellPhone("cellPhone");
        fhInsuredPerson1.setAppStatus("-2");
        fhInsuredPerson1.setPolicyNo("insIdNumber");
        fhInsuredPerson1.setRelationship("99");
        fhInsuredPerson1.setFlightNo("flightNo");
        fhInsuredPerson1.setFlightTime("flightTime");
        fhInsuredPerson1.setOccupationCode("");
        fhInsuredPerson1.setOccupationGroup("occupationGroup");
        fhInsuredPerson1.setDestinationCountryText("destinationCountryText");
        fhInsuredPerson1.setAnnualIncome("annualIncome");
        fhInsuredPerson1.setStudentType("studentType");
        fhInsuredPerson1.setSchoolType("schoolType");
        fhInsuredPerson1.setSchoolNature("schoolNature");
        fhInsuredPerson1.setSchoolName("schoolName");
        fhInsuredPerson1.setSchoolClass("schoolClass");
        fhInsuredPerson1.setStudentId("studentId");
        fhInsuredPerson1.setIdPeriodStart("idPeriodStart");
        fhInsuredPerson1.setIdPeriodEnd("idPeriodEnd");
        fhInsuredPerson1.setSmoke("smoke");
        fhInsuredPerson1.setOldPolicyNo("oldPolicyNo");
        final TestPremiumDutyForm testPremiumDutyForm2 = new TestPremiumDutyForm();
        fhInsuredPerson1.setDuties(Arrays.asList(testPremiumDutyForm2));
        final TestPremiumProductForm product1 = new TestPremiumProductForm();
        product1.setProductId(0);
        product1.setPlanId(0);
        product1.setPlanCode("planCode");
        product1.setPremium(new BigDecimal("0.00"));
        product1.setQty(0);
        final TestPremiumDutyForm testPremiumDutyForm3 = new TestPremiumDutyForm();
        product1.setPremiumDutyForms(Arrays.asList(testPremiumDutyForm3));
        fhInsuredPerson1.setProduct(product1);
        orderSubmitRequest.setInsuredPerson(Arrays.asList(fhInsuredPerson1));
        final FhProperty propertyInfo1 = new FhProperty();
        propertyInfo1.setPropertyInfoIsExist("0");
        orderSubmitRequest.setPropertyInfo(propertyInfo1);
        final SmOrderHouseDTO house1 = new SmOrderHouseDTO();
        orderSubmitRequest.setHouse(house1);
        final SmOrderCarInfoDTO carInfo1 = new SmOrderCarInfoDTO();
        orderSubmitRequest.setCarInfo(carInfo1);
        orderSubmitRequest.setProductType("code");
        orderSubmitRequest.setBizCode("bizCode");
        orderSubmitRequest.setOrderOutType("orderOutType");
        orderSubmitRequest.setToken("token");
        orderSubmitRequest.setPlanId(0);
        orderSubmitRequest.setQty(0);
        orderSubmitRequest.setPreOrderId("preOrderId");
        orderSubmitRequest.setRenew(false);
        orderSubmitRequest.setRealRenewFlag(false);
        orderSubmitRequest.setAppNo("appNo");
        orderSubmitRequest.setAgentId(0);
        orderSubmitRequest.setJobCode("jobCode");
        final SmCreateOrderSubmitRequest dto1 = new SmCreateOrderSubmitRequest(orderSubmitRequest);

        // Confirm SmOrderMapper.insertOrderApplicant(...).
        final OrderSubmitRequest orderSubmitRequest1 = new OrderSubmitRequest();
        final FhProduct productInfo2 = new FhProduct();
        productInfo2.setProductId("fhProductId");
        productInfo2.setRecommendId("recommendId");
        productInfo2.setRecommendMainJobNumber("recommendMainJobNumber");
        productInfo2.setRecommendOrgCode("recommendOrgCode");
        orderSubmitRequest1.setProductInfo(productInfo2);
        final FhOrderInfo orderInfo2 = new FhOrderInfo();
        orderInfo2.setSubmitTime("submitTime");
        orderInfo2.setStartTime("startTime");
        orderInfo2.setEndTime("endTime");
        orderSubmitRequest1.setOrderInfo(orderInfo2);
        final FhProposer proposerInfo2 = new FhProposer();
        proposerInfo2.setPersonName("insuredName");
        proposerInfo2.setPersonGender("insuredPersonGender");
        proposerInfo2.setIdType("idType");
        proposerInfo2.setIdNumber("idNumber");
        proposerInfo2.setBirthday("birthday");
        proposerInfo2.setCellPhone("cellPhone");
        proposerInfo2.setEmail("email");
        proposerInfo2.setAddress("address");
        proposerInfo2.setAppStatus("-2");
        proposerInfo2.setPolicyNo("insIdNumber");
        proposerInfo2.setDownloadURL("downloadURL");
        orderSubmitRequest1.setProposerInfo(proposerInfo2);
        final FhInsuredPerson fhInsuredPerson2 = new FhInsuredPerson();
        fhInsuredPerson2.setPersonName("insuredName");
        fhInsuredPerson2.setIdType("idType");
        fhInsuredPerson2.setIdNumber("idNumber");
        fhInsuredPerson2.setBirthday("birthday");
        fhInsuredPerson2.setCellPhone("cellPhone");
        fhInsuredPerson2.setAppStatus("-2");
        fhInsuredPerson2.setPolicyNo("insIdNumber");
        fhInsuredPerson2.setRelationship("99");
        fhInsuredPerson2.setFlightNo("flightNo");
        fhInsuredPerson2.setFlightTime("flightTime");
        fhInsuredPerson2.setOccupationCode("");
        fhInsuredPerson2.setOccupationGroup("occupationGroup");
        fhInsuredPerson2.setDestinationCountryText("destinationCountryText");
        fhInsuredPerson2.setAnnualIncome("annualIncome");
        fhInsuredPerson2.setStudentType("studentType");
        fhInsuredPerson2.setSchoolType("schoolType");
        fhInsuredPerson2.setSchoolNature("schoolNature");
        fhInsuredPerson2.setSchoolName("schoolName");
        fhInsuredPerson2.setSchoolClass("schoolClass");
        fhInsuredPerson2.setStudentId("studentId");
        fhInsuredPerson2.setIdPeriodStart("idPeriodStart");
        fhInsuredPerson2.setIdPeriodEnd("idPeriodEnd");
        fhInsuredPerson2.setSmoke("smoke");
        fhInsuredPerson2.setOldPolicyNo("oldPolicyNo");
        final TestPremiumDutyForm testPremiumDutyForm4 = new TestPremiumDutyForm();
        fhInsuredPerson2.setDuties(Arrays.asList(testPremiumDutyForm4));
        final TestPremiumProductForm product2 = new TestPremiumProductForm();
        product2.setProductId(0);
        product2.setPlanId(0);
        product2.setPlanCode("planCode");
        product2.setPremium(new BigDecimal("0.00"));
        product2.setQty(0);
        final TestPremiumDutyForm testPremiumDutyForm5 = new TestPremiumDutyForm();
        product2.setPremiumDutyForms(Arrays.asList(testPremiumDutyForm5));
        fhInsuredPerson2.setProduct(product2);
        orderSubmitRequest1.setInsuredPerson(Arrays.asList(fhInsuredPerson2));
        final FhProperty propertyInfo2 = new FhProperty();
        propertyInfo2.setPropertyInfoIsExist("0");
        orderSubmitRequest1.setPropertyInfo(propertyInfo2);
        final SmOrderHouseDTO house2 = new SmOrderHouseDTO();
        orderSubmitRequest1.setHouse(house2);
        final SmOrderCarInfoDTO carInfo2 = new SmOrderCarInfoDTO();
        orderSubmitRequest1.setCarInfo(carInfo2);
        orderSubmitRequest1.setProductType("code");
        orderSubmitRequest1.setBizCode("bizCode");
        orderSubmitRequest1.setOrderOutType("orderOutType");
        orderSubmitRequest1.setToken("token");
        orderSubmitRequest1.setPlanId(0);
        orderSubmitRequest1.setQty(0);
        orderSubmitRequest1.setPreOrderId("preOrderId");
        orderSubmitRequest1.setRenew(false);
        orderSubmitRequest1.setRealRenewFlag(false);
        orderSubmitRequest1.setAppNo("appNo");
        orderSubmitRequest1.setAgentId(0);
        orderSubmitRequest1.setJobCode("jobCode");
        final SmCreateOrderSubmitRequest dto2 = new SmCreateOrderSubmitRequest(orderSubmitRequest1);

        // Confirm SmOrderMapper.insertOrderInsured(...).
        final OrderSubmitRequest orderSubmitRequest2 = new OrderSubmitRequest();
        final FhProduct productInfo3 = new FhProduct();
        productInfo3.setProductId("fhProductId");
        productInfo3.setRecommendId("recommendId");
        productInfo3.setRecommendMainJobNumber("recommendMainJobNumber");
        productInfo3.setRecommendOrgCode("recommendOrgCode");
        orderSubmitRequest2.setProductInfo(productInfo3);
        final FhOrderInfo orderInfo3 = new FhOrderInfo();
        orderInfo3.setSubmitTime("submitTime");
        orderInfo3.setStartTime("startTime");
        orderInfo3.setEndTime("endTime");
        orderSubmitRequest2.setOrderInfo(orderInfo3);
        final FhProposer proposerInfo3 = new FhProposer();
        proposerInfo3.setPersonName("insuredName");
        proposerInfo3.setPersonGender("insuredPersonGender");
        proposerInfo3.setIdType("idType");
        proposerInfo3.setIdNumber("idNumber");
        proposerInfo3.setBirthday("birthday");
        proposerInfo3.setCellPhone("cellPhone");
        proposerInfo3.setEmail("email");
        proposerInfo3.setAddress("address");
        proposerInfo3.setAppStatus("-2");
        proposerInfo3.setPolicyNo("insIdNumber");
        proposerInfo3.setDownloadURL("downloadURL");
        orderSubmitRequest2.setProposerInfo(proposerInfo3);
        final FhInsuredPerson fhInsuredPerson3 = new FhInsuredPerson();
        fhInsuredPerson3.setPersonName("insuredName");
        fhInsuredPerson3.setIdType("idType");
        fhInsuredPerson3.setIdNumber("idNumber");
        fhInsuredPerson3.setBirthday("birthday");
        fhInsuredPerson3.setCellPhone("cellPhone");
        fhInsuredPerson3.setAppStatus("-2");
        fhInsuredPerson3.setPolicyNo("insIdNumber");
        fhInsuredPerson3.setRelationship("99");
        fhInsuredPerson3.setFlightNo("flightNo");
        fhInsuredPerson3.setFlightTime("flightTime");
        fhInsuredPerson3.setOccupationCode("");
        fhInsuredPerson3.setOccupationGroup("occupationGroup");
        fhInsuredPerson3.setDestinationCountryText("destinationCountryText");
        fhInsuredPerson3.setAnnualIncome("annualIncome");
        fhInsuredPerson3.setStudentType("studentType");
        fhInsuredPerson3.setSchoolType("schoolType");
        fhInsuredPerson3.setSchoolNature("schoolNature");
        fhInsuredPerson3.setSchoolName("schoolName");
        fhInsuredPerson3.setSchoolClass("schoolClass");
        fhInsuredPerson3.setStudentId("studentId");
        fhInsuredPerson3.setIdPeriodStart("idPeriodStart");
        fhInsuredPerson3.setIdPeriodEnd("idPeriodEnd");
        fhInsuredPerson3.setSmoke("smoke");
        fhInsuredPerson3.setOldPolicyNo("oldPolicyNo");
        final TestPremiumDutyForm testPremiumDutyForm6 = new TestPremiumDutyForm();
        fhInsuredPerson3.setDuties(Arrays.asList(testPremiumDutyForm6));
        final TestPremiumProductForm product3 = new TestPremiumProductForm();
        product3.setProductId(0);
        product3.setPlanId(0);
        product3.setPlanCode("planCode");
        product3.setPremium(new BigDecimal("0.00"));
        product3.setQty(0);
        final TestPremiumDutyForm testPremiumDutyForm7 = new TestPremiumDutyForm();
        product3.setPremiumDutyForms(Arrays.asList(testPremiumDutyForm7));
        fhInsuredPerson3.setProduct(product3);
        orderSubmitRequest2.setInsuredPerson(Arrays.asList(fhInsuredPerson3));
        final FhProperty propertyInfo3 = new FhProperty();
        propertyInfo3.setPropertyInfoIsExist("0");
        orderSubmitRequest2.setPropertyInfo(propertyInfo3);
        final SmOrderHouseDTO house3 = new SmOrderHouseDTO();
        orderSubmitRequest2.setHouse(house3);
        final SmOrderCarInfoDTO carInfo3 = new SmOrderCarInfoDTO();
        orderSubmitRequest2.setCarInfo(carInfo3);
        orderSubmitRequest2.setProductType("code");
        orderSubmitRequest2.setBizCode("bizCode");
        orderSubmitRequest2.setOrderOutType("orderOutType");
        orderSubmitRequest2.setToken("token");
        orderSubmitRequest2.setPlanId(0);
        orderSubmitRequest2.setQty(0);
        orderSubmitRequest2.setPreOrderId("preOrderId");
        orderSubmitRequest2.setRenew(false);
        orderSubmitRequest2.setRealRenewFlag(false);
        orderSubmitRequest2.setAppNo("appNo");
        orderSubmitRequest2.setAgentId(0);
        orderSubmitRequest2.setJobCode("jobCode");
        final SmCreateOrderSubmitRequest dto3 = new SmCreateOrderSubmitRequest(orderSubmitRequest2);
    }
}
