package com.cfpamf.ms.insur.admin.service.product;

import com.cfpamf.ms.insur.admin.dao.safes.product.risk.*;
import com.cfpamf.ms.insur.admin.pojo.dto.product.SysRiskDTO;
import com.cfpamf.ms.insur.admin.pojo.po.product.risk.SysRisk;
import com.cfpamf.ms.insur.admin.pojo.po.product.risk.SysRiskClause;
import com.cfpamf.ms.insur.admin.service.BaseTest;
import com.cfpamf.ms.insur.base.exception.BizException;
import com.github.jsonzou.jmockdata.JMockData;
import com.github.jsonzou.jmockdata.TypeReference;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import tk.mybatis.mapper.entity.Config;
import tk.mybatis.mapper.mapperhelper.EntityHelper;

import java.util.List;

@RunWith(SpringJUnit4ClassRunner.class)
public class SysRiskServiceTest extends BaseTest {
    @InjectMocks
    SysRiskService sysRiskService;

    @Mock
    SysRiskMapper riskMapper;

    @Mock
    SysRiskAmountMapper amountMapper;

    @Mock
    SysRiskAnnuityMapper annuityMapper;

    @Mock
    SysRiskLimitMapper limitMapper;

    @Mock
    SysRiskClassifyMapper classifyMapper;

    @Mock
    SysRiskDutyAmountMapper dutyAmountMapper;

    @Mock
    SysRiskDutyMapper dutyMapper;

    @Mock
    SysRiskClauseMapper clauseMapper;


    @Mock
    SysRiskVersionMapper versionMapper;


    /**
     * 费率表相关
     */
    @Mock
    SysRiskFactorMapper factorMapper;
    @Mock
    SysRiskFactorConfigMapper factorConfigMapper;
    @Mock
    SysRiskFactorPriceMapper factorPriceMapper;
    @Mock
    SysRiskFactorFileMapper factorFileMapper;


    @Before
    public void setUp() throws Exception {
        EntityHelper.initEntityNameMap(SysRisk.class, new Config());
    }

    @Test
    public void saveRisk() {
        SysRiskDTO mock = JMockData.mock(SysRiskDTO.class);
        mock.setRiskId(null);
        mock.getRisk().setId(null);
        try {
            sysRiskService.saveRisk(mock);
        } catch (BizException e) {
            //险种编码已存在
            Assert.assertEquals("801033", e.getCode());
        }

        Mockito.when(riskMapper.selectCountByExample(Mockito.any()))
                .thenReturn(0);
        sysRiskService.saveRisk(JMockData.mock(com.cfpamf.ms.insur.admin.pojo.dto.product.SysRiskDTO.class));

    }

    @Test
    public void getRisk() {

        sysRiskService.getRisk("sdfsd", 1);
    }

    @Test
    public void classifies() {

        sysRiskService.classifies();
    }

    @Test
    public void deleteRisk() {

        final SysRisk mock = JMockData.mock(SysRisk.class);
        mock.setVersion(2);
        Mockito.when(riskMapper.selectByPrimaryKeyMustExists(Mockito.any()))
                .thenReturn(mock);
        try {
            sysRiskService.deleteRisk(Mockito.anyInt());
        } catch (BizException e) {
            Assert.assertEquals("801033", e.getCode());
        }
        mock.setVersion(1);
        Mockito.when(riskMapper.selectByPrimaryKeyMustExists(Mockito.any()))
                .thenReturn(mock);
        sysRiskService.deleteRisk(Mockito.anyInt());
    }

    @Test
    public void update() {
        try {
            sysRiskService.update(JMockData.mock(com.cfpamf.ms.insur.admin.pojo.dto.product.SysRiskDTO.class));
        } catch (Exception e) {

        }
    }

    @Test
    public void insert() {
        try {
            sysRiskService.insert(JMockData.mock(com.cfpamf.ms.insur.admin.pojo.dto.product.SysRiskDTO.class));
        } catch (Exception e) {

        }
    }

    @Test
    public void listDutyByRisk() {
        try {
            sysRiskService.listDutyByRisk(",XVZwO,", 23143);
        } catch (Exception e) {

        }
    }

    @Test
    public void pageQuery() {
        try {
            sysRiskService.pageQuery(JMockData.mock(com.cfpamf.ms.insur.admin.pojo.query.product.SysRiskQuery.class));
        } catch (Exception e) {

        }
    }

    @Test
    public void saveDuty() {
        try {
            sysRiskService.saveDuty(JMockData.mock(com.cfpamf.ms.insur.admin.pojo.dto.product.SysDutyDTO.class));
        } catch (Exception e) {

        }
    }

    @Test
    public void deleteDuty() {
        try {
            sysRiskService.deleteDuty(7585);
        } catch (Exception e) {

        }
    }

    @Test
    public void getClauses() {
        try {
            sysRiskService.getClauses(",QCeZk,", 4185);
        } catch (Exception e) {

        }
    }

    @Test
    public void saveClause() {

        final List<SysRiskClause> mock = JMockData.mock(new TypeReference<List<SysRiskClause>>() {
        });
        sysRiskService.saveClause(",yXurl,", 6331, mock);
    }

    @Test
    public void pushVersion() {
        try {
            sysRiskService.pushVersion(",dZMem,", 20521, ",VnlXX,");
        } catch (Exception e) {

        }
    }

    @Test
    public void pushNewVersion() {
        resetMapper();
        sysRiskService.pushNewVersion(JMockData.mock(com.cfpamf.ms.insur.admin.pojo.po.product.risk.SysRisk.class));
    }
}


