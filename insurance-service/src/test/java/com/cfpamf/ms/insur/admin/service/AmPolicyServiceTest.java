package com.cfpamf.ms.insur.admin.service;

import com.cfpamf.ms.insur.base.util.HttpRequestUtil;
import com.github.jsonzou.jmockdata.JMockData;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.mock.web.MockHttpServletResponse;

@RunWith(PowerMockRunner.class)
@PrepareForTest({HttpRequestUtil.class})
public class AmPolicyServiceTest extends BaseTest {
    @InjectMocks
    AmPolicyService amPolicyService;
    @Mock
    com.cfpamf.ms.insur.admin.dao.safes.AmPolicyMapper policyMapper;
    @Mock
    com.cfpamf.ms.insur.admin.dao.safes.AmImageMapper imageMapper;
    @Mock
    com.cfpamf.ms.insur.admin.dao.safes.AmSupplyMapper supplyMapper;
    @Mock
    com.cfpamf.ms.insur.admin.dao.safes.AmRiskKindMapper riskKindMapper;
    @Mock
    com.cfpamf.ms.insur.admin.dao.safes.AuthUserMapper authUserMapper;
    @Mock
    com.cfpamf.ms.insur.admin.service.AmAuthService authService;
    @Mock
    com.cfpamf.ms.insur.base.service.DLockTemplate lockTemplate;
    @Mock
    com.cfpamf.ms.insur.base.util.PermissionUtil permissionQueryUtil;
    @Mock
    com.cfpamf.ms.insur.base.config.BmsConfig bmsConfig;

    @Test
    public void handleCallBack() throws InterruptedException {
        amPolicyService.handleCallBack(JMockData.mock(com.cfpamf.ms.insur.admin.pojo.dto.AmPolicyDTO.class));
    }

    @Test
    public void getPolicysByPage() {
        amPolicyService.getPolicysByPage(JMockData.mock(com.cfpamf.ms.insur.admin.pojo.query.AmPolicyQuery.class), JMockData.mock(com.cfpamf.ms.insur.base.bean.Pageable.class));
    }

    @Test
    public void getPolicyById() {
        try {
            amPolicyService.getPolicyById(JMockData.mock(int.class));

        } catch (Exception e) {

        }
    }

    @Test
    public void getDashboardVo() {
        amPolicyService.getDashboardVo();
    }

    @Test
    public void downloadPolicys() {
        try {
            amPolicyService.downloadPolicys(JMockData.mock(com.cfpamf.ms.insur.admin.pojo.query.AmPolicyQuery.class), new MockHttpServletResponse());

        } catch (Exception e) {

        }
    }
}
