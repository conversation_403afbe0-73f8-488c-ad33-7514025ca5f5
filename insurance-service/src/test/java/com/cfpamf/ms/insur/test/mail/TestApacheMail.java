package com.cfpamf.ms.insur.test.mail;

import com.cfpamf.ms.insur.base.util.ExcelBuilderUtil;
import org.apache.commons.mail.EmailException;
import org.apache.commons.mail.HtmlEmail;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;

/**
 * apache邮件发送测试
 *
 * <AUTHOR>
 */
public class TestApacheMail {

    public static void main(String[] args) throws EmailException {

        //创建一个HtmlEmail实例对象
        HtmlEmail email = new HtmlEmail();
        //邮箱的SMTP服务器，一般123邮箱的是smtp.123.com,qq邮箱为smtp.qq.com
        email.setHostName("smtpcom.263xmail.com");
        //设置发送的字符类型
        email.setCharset("utf-8");
        //设置收件人
        email.addTo("<EMAIL>");
        //发送人的邮箱为自己的，用户名可以随便填
        email.setFrom("<EMAIL>", "张娜义");
        //设置发送人到的邮箱和用户名和授权码(授权码是自己设置的)
        email.setAuthentication("<EMAIL>", "B3690D3f516f585D");
        //设置发送主题
        email.setSubject("2019-12-04保单");
        //设置发送内容
        email.setHtmlMsg(getHtmlMsg());
        // 附件
        email.attach(getFile());
        // SSL配置
        email.setSSLOnConnect(true);
        //发送邮件
        email.send();
    }

    private static File getFile() {
        try {
            File file = new File("保单.xlsx");
            ExcelBuilderUtil.newInstance().createSheet("测试")
                    .write(new FileOutputStream(file));
            return file;
        } catch (IOException e) {
            throw new RuntimeException("");
        }
    }


    private static String getHtmlMsg() {
        return "<div>\n" +
                "<p style=\"margin:0px;\">信息技术部、人力资源部、财务核算中心：</p><p style=\"margin:0px;\"><br></p>" +
                "<p style=\"margin:0px;\">&nbsp; &nbsp;附件中分支机构人口密度明细表12月2日，请查收。</p>" +
                "<p style=\"margin:0px;\">雷云霞（Yunxia Lei）</p><p style=\"margin:0px;\"><br></p>" +
                "<p style=\"margin:0px;\">行政文化中心<br></p><p style=\"margin:0px;\"><br></p>" +
                "<p style=\"margin:0px;\">中和农信项目管理有限公司</p>" +
                "<p style=\"margin:0px;\">地址（add）：北京市东城区安定门外大街138号皇城国际A座18层<br></p>" +
                "<p style=\"margin:0px;\">电话（Tel）：86 10 88578681-8224<br></p></div>\n" +
                "</div>";
    }
}
