//package com.cfpamf.ms.insur.weixin.service;
//
//
//import com.cfpamf.ms.insur.admin.receiver.service.OrderReceiverInfoService;
//import com.cfpamf.ms.insur.admin.renewal.service.RenewalConfigService;
//import com.cfpamf.ms.insur.admin.service.ProductRenewRuleService;
//import com.cfpamf.ms.insur.admin.service.SmOrderCoreService;
//import com.cfpamf.ms.insur.admin.service.SmOrderRenewBindService;
//import com.cfpamf.ms.insur.admin.service.SmProductService;
//import com.cfpamf.ms.insur.base.service.BusinessTokenService;
//import com.cfpamf.ms.insur.base.util.HttpRequestUtil;
//import com.cfpamf.ms.insur.weixin.dao.safes.WxOrderMapper;
//import com.cfpamf.ms.insur.weixin.dao.safes.WxUserSettingMapper;
//import com.cfpamf.ms.insur.weixin.pojo.query.order.OrderDetailQuery;
//import com.cfpamf.ms.insur.weixin.pojo.vo.WxSessionVO;
//import com.google.code.kaptcha.Producer;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.mockito.InjectMocks;
//import org.mockito.Mock;
//import org.powermock.core.classloader.annotations.PrepareForTest;
//import org.powermock.modules.junit4.PowerMockRunner;
//import org.springframework.beans.factory.annotation.Autowired;
//
//@RunWith(PowerMockRunner.class)
//@PrepareForTest({HttpRequestUtil.class})
//public class WxAbstractServiceTest {
//
//    @InjectMocks
//    WxCcOrderService orderService;
//
//    @Mock
//    SmProductService productService;
//    @Mock
//    ProductRenewRuleService productRenewRuleService;
//
//    /**
//     * 业务token service
//     */
//    @Mock
//    private BusinessTokenService tokenService;
//
//    /**
//     * 订单mapper
//     */
//    @Mock
//    private WxOrderMapper orderMapper;
//
//    /**
//     * 下载service
//     */
//    @Mock
//    private WxDownloadService wxDownloadService;
//
//    /**
//     * 微信用户设置mapper
//     */
//    @Mock
//    private WxUserSettingMapper wusMapper;
//
//    /**
//     * google验证马生成器
//     */
//    @Mock
//    private Producer captchaProducer;
//
//    @Mock
//    private OrderReceiverInfoService orderReceiverInfoService;
//    @Mock
//    private SmOrderRenewBindService smOrderRenewBindService;
//    @Mock
//    private RenewalConfigService renewalConfigService;
//
//
//}
