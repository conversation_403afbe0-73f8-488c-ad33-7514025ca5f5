package com.cfpamf.ms.insur.admin.event.handler;

import com.cfpamf.ms.insur.admin.enums.EnumFlowInsCancel;
import com.cfpamf.ms.insur.admin.event.WxCancelNotifyEvent;
import com.cfpamf.ms.insur.admin.service.BaseTest;
import com.cfpamf.ms.insur.base.util.HttpRequestUtil;
import com.github.jsonzou.jmockdata.JMockData;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

/**
 * <AUTHOR> 2020/3/12 09:51
 */
@Slf4j
@RunWith(PowerMockRunner.class)
@PrepareForTest({HttpRequestUtil.class})
public class SmCancelWxMessageHandlerTest extends BaseTest {
    @InjectMocks
    SmCancelWxMessageHandler smCancelWxMessageHandler;
    @Mock
    com.cfpamf.ms.insur.base.config.WechatConfig wechatConfig;
    @Mock
    com.cfpamf.ms.insur.admin.service.SmCancelService smCancelService;
    @Mock
    com.cfpamf.ms.insur.admin.dao.safes.SmCancelMapper cancelMapper;
    @Mock
    com.cfpamf.ms.insur.base.activiti.ActivitiCommonService activitiCommonService;
    @Mock
    com.cfpamf.ms.insur.admin.service.UserService userService;
    @Mock
    com.cfpamf.ms.insur.weixin.service.WxMpServiceProxy serviceProxy;

    @Test
    public void handlerPushMessage() {
        smCancelWxMessageHandler.handlerPushMessage(JMockData.mock(com.cfpamf.ms.insur.admin.event.WxCancelNotifyEvent.class));
    }


    @Test
    public void handlerPushMessage1() {
        WxCancelNotifyEvent mock = JMockData.mock(WxCancelNotifyEvent.class);
        mock.setTaskKey(EnumFlowInsCancel.UT_APPLY.getEventId());
        smCancelWxMessageHandler.handlerPushMessage(mock);
    }

    @Test
    public void handlerPushMessage2() {
        WxCancelNotifyEvent mock = JMockData.mock(WxCancelNotifyEvent.class);
        mock.setTaskKey(EnumFlowInsCancel.ORG_ADMIN_AUDIT.getEventId());
        smCancelWxMessageHandler.handlerPushMessage(mock);
    }

    @Test
    public void handlerPushMessage3() {
        WxCancelNotifyEvent mock = JMockData.mock(WxCancelNotifyEvent.class);
        mock.setTaskKey(EnumFlowInsCancel.SAFE_CENTER.getEventId());
        smCancelWxMessageHandler.handlerPushMessage(mock);
    }

    @Test
    public void handlerPushMessage4() {
        WxCancelNotifyEvent mock = JMockData.mock(WxCancelNotifyEvent.class);
        mock.setTaskKey(EnumFlowInsCancel.COMPANY_CN_UPLOAD.getEventId());
        smCancelWxMessageHandler.handlerPushMessage(mock);
    }

    @Test
    public void handlerPushMessage5() {
        WxCancelNotifyEvent mock = JMockData.mock(WxCancelNotifyEvent.class);
        mock.setTaskKey(EnumFlowInsCancel.CANCEL_END.getEventId());
        smCancelWxMessageHandler.handlerPushMessage(mock);
    }
}
