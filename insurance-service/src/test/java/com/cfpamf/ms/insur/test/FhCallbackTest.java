package com.cfpamf.ms.insur.test;

import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * CIC20031901243455\n" +
 * "CIC20031901243694\n" +
 * "CIC20031901243726\n" +
 * "CIC20031901243729\n" +
 * "CIC20031901243748\n" +
 * "CIC20031901243755\n" +
 * "CIC20031901243771\n" +
 * "CIC20031901243868\n" +
 * "CIC20031901243875\n" +
 * "CIC20031901243954\n" +
 * "CIC20031901244068\n" +
 * "CIC20031901244087\n" +
 * "CIC20031901244188\n" +
 * "CIC20031901244212\n" +
 * "CIC20031901244244\n" +
 * "CIC20031901
 * （dms 导出需要刷洗你的订单号 然后执行脚本）
 * 根据订单号 刷新订单状态
 * <AUTHOR> 2020/4/3 13:58
 */
public class FhCallbackTest {

    public static void main(String[] args) throws IOException {

        List<String> allNotPay =
                Arrays.asList(("CIC20122802111377\n" +
                        "CIC20122802111380\n" +
                        "CIC20122802111516\n" +
                        "CIC20122802112090\n" +
                        "CIC20122802112107\n" +
                        "CIC20122902113971\n" +
                        "CIC20122902113993\n" +
                        "CIC20122902113996\n" +
                        "CIC20122902114003\n" +
                        "CIC20122902114025\n" +
                        "CIC20122902114097\n" +
                        "CIC20122902114112\n" +
                        "CIC20122902114117\n" +
                        "CIC20122902114125\n" +
                        "CIC20122902114130\n" +
                        "CIC20122902114154\n" +
                        "CIC20122902114194\n" +
                        "CIC20122902114197\n" +
                        "CIC20122902114269\n" +
                        "CIC20122902114273\n" +
                        "CIC20122902114291\n" +
                        "CIC20122902114310\n" +
                        "CIC20122902114330\n" +
                        "CIC20122902114352\n" +
                        "CIC20122902114375\n" +
                        "CIC20122902114444\n" +
                        "CIC20122902114451\n" +
                        "CIC20122902114510\n" +
                        "CIC20122902114541\n" +
                        "CIC20122902114594\n" +
                        "CIC20122902114884\n" +
                        "CIC20122902114890\n" +
                        "CIC20122902114896\n" +
                        "CIC20122902114901\n" +
                        "CIC20122902114907\n" +
                        "CIC20122902115091\n" +
                        "CIC20122902115104\n" +
                        "CIC20122902115115\n" +
                        "CIC20122902115117\n" +
                        "CIC20122902115132\n" +
                        "CIC20122902115137\n" +
                        "CIC20122902115142\n" +
                        "CIC20122902115154\n" +
                        "CIC20122902115210\n" +
                        "CIC20122902115213\n" +
                        "CIC20122902115231\n" +
                        "CIC20122902115244\n" +
                        "CIC20122902115254\n" +
                        "CIC20122902115283\n" +
                        "CIC20122902115289\n" +
                        "CIC20122902115292\n" +
                        "CIC20122902115297\n" +
                        "CIC20122902115358\n" +
                        "CIC20122902115362\n" +
                        "CIC20122902115365\n" +
                        "CIC20122902115367\n" +
                        "CIC20122902115379\n" +
                        "CIC20122902115389\n" +
                        "CIC20122902115393\n" +
                        "CIC20122902115404\n" +
                        "CIC20122902115416\n" +
                        "CIC20122902115429\n" +
                        "CIC20122902115465\n" +
                        "CIC20122902115490\n" +
                        "CIC20122902115500\n" +
                        "CIC20122902115506\n" +
                        "CIC20122902115520\n" +
                        "CIC20122902115521\n" +
                        "CIC20122902115561\n" +
                        "CIC20122902115566\n" +
                        "CIC20122902115571\n" +
                        "CIC20122902115577\n" +
                        "CIC20122902115589\n" +
                        "CIC20122902115592\n" +
                        "CIC20122902115634\n" +
                        "CIC20122902115647\n" +
                        "CIC20122902115661\n" +
                        "CIC20122902115667\n" +
                        "CIC20122902115672\n" +
                        "CIC20122902115695\n" +
                        "CIC20122902115698\n" +
                        "CIC20122902115704\n" +
                        "CIC20122902115729\n" +
                        "CIC20122902115733\n" +
                        "CIC20122902115745\n" +
                        "CIC20122902115751\n" +
                        "CIC20122902115771\n" +
                        "CIC20122902115777\n" +
                        "CIC20122902115809\n" +
                        "CIC20122902115815\n" +
                        "CIC20122902115899\n" +
                        "CIC20122902115912\n" +
                        "CIC20122902115915\n" +
                        "CIC20122902115916\n" +
                        "CIC20122902115917\n" +
                        "CIC20122902115918\n" +
                        "CIC20122902115919\n" +
                        "CIC20122902115920\n" +
                        "CIC20122902115921\n" +
                        "CIC20122902115923\n" +
                        "CIC20122902115925\n" +
                        "CIC20122902115933\n" +
                        "CIC20122902116011\n" +
                        "CIC20122902116029\n" +
                        "CIC20122902116033\n" +
                        "CIC20122902116034\n" +
                        "CIC20122902116037\n" +
                        "CIC20122902116038\n" +
                        "CIC20122902116051\n" +
                        "CIC20122902116066\n" +
                        "CIC20122902116069\n" +
                        "CIC20122902116082\n" +
                        "CIC20122902116087\n" +
                        "CIC20122902116091\n" +
                        "CIC20122902116123\n" +
                        "CIC20122902116162\n" +
                        "CIC20122902116172\n" +
                        "CIC20122902116177\n" +
                        "CIC20122902116194\n" +
                        "CIC20122902116211\n" +
                        "CIC20122902116226\n" +
                        "CIC20122902116229\n" +
                        "CIC20122902116248\n" +
                        "CIC20122902116255\n" +
                        "CIC20122902116256\n" +
                        "CIC20122902116259\n" +
                        "CIC20122902116264\n" +
                        "CIC20122902116293\n" +
                        "CIC20122902116326\n" +
                        "CIC20122902116332\n" +
                        "CIC20122902116396\n" +
                        "CIC20122902116411\n" +
                        "CIC20122902116417\n" +
                        "CIC20122902116430\n" +
                        "CIC20122902116438\n" +
                        "CIC20122902116442\n" +
                        "CIC20122902116449\n" +
                        "CIC20122902116476\n" +
                        "CIC20122902116483\n" +
                        "CIC20122902116510\n" +
                        "CIC20122902116517\n" +
                        "CIC20122902116521\n" +
                        "CIC20122902116560\n" +
                        "CIC20122902116602\n" +
                        "CIC20122902116610\n" +
                        "CIC20122902116613\n" +
                        "CIC20122902116623\n" +
                        "CIC20122902116640\n" +
                        "CIC20122902116666\n" +
                        "CIC20122902116708\n" +
                        "CIC20122902116717\n" +
                        "CIC20122902116740\n" +
                        "CIC20122902116746\n" +
                        "CIC20122902116749\n" +
                        "CIC20122902116784\n" +
                        "CIC20122902116785\n" +
                        "CIC20122902116798\n" +
                        "CIC20122902116800\n" +
                        "CIC20122902116809\n" +
                        "CIC20122902116812\n" +
                        "CIC20122902116813\n" +
                        "CIC20122902116822\n" +
                        "CIC20122902116828\n" +
                        "CIC20122902116832\n" +
                        "CIC20122902116853\n" +
                        "CIC20122902116894\n" +
                        "CIC20122902116914\n" +
                        "CIC20122902116948\n" +
                        "CIC20122902117034\n" +
                        "CIC20122902117057\n" +
                        "CIC20122902117076\n" +
                        "CIC20122902117093\n" +
                        "CIC20122902117094\n" +
                        "CIC20122902117110\n" +
                        "CIC20122902117115\n" +
                        "CIC20122902117140\n" +
                        "CIC20122902117141\n" +
                        "CIC20122902117143\n" +
                        "CIC20122902117155\n" +
                        "CIC20122902117163\n" +
                        "CIC20122902117171\n" +
                        "CIC20122902117189\n" +
                        "CIC20122902117194\n" +
                        "CIC20122902117198\n" +
                        "CIC20122902117199\n" +
                        "CIC20122902117210\n" +
                        "CIC20122902117227\n" +
                        "CIC20122902117234\n" +
                        "CIC20122902117255\n" +
                        "CIC20122902117270\n" +
                        "CIC20122902117271\n" +
                        "CIC20122902117272\n" +
                        "CIC20122902117283\n" +
                        "CIC20122902117291\n" +
                        "CIC20122902117293\n" +
                        "CIC20122902117295\n" +
                        "CIC20122902117307\n" +
                        "CIC20122902117312\n" +
                        "CIC20122902117355\n" +
                        "CIC20122902117387\n" +
                        "CIC20122902117483\n" +
                        "CIC20122902117512\n" +
                        "CIC20122902117531\n" +
                        "CIC20122902117533\n" +
                        "CIC20122902117534\n" +
                        "CIC20122902117535\n" +
                        "CIC20122902117557\n" +
                        "CIC20122902117576\n" +
                        "CIC20122902117596\n" +
                        "CIC20122902117597\n" +
                        "CIC20122902117599\n" +
                        "CIC20122902117616\n" +
                        "CIC20122902117640\n" +
                        "CIC20122902117641\n" +
                        "CIC20122902117644\n" +
                        "CIC20122902117669\n" +
                        "CIC20122902117690\n" +
                        "CIC20122902117691\n" +
                        "CIC20122902117702\n" +
                        "CIC20122902117713\n" +
                        "CIC20122902117716\n" +
                        "CIC20122902117719\n" +
                        "CIC20123002117612\n" +
                        "CIC20123002117616\n" +
                        "CIC20123002117618\n" +
                        "CIC20123002117627\n" +
                        "CIC20123002117629\n" +
                        "CIC20123002117628\n" +
                        "CIC20123002117635\n" +
                        "CIC20123002117637\n" +
                        "CIC20123002117678\n" +
                        "CIC20123002117681\n" +
                        "CIC20123002117686\n" +
                        "CIC20123002117743\n" +
                        "CIC20123002117798\n" +
                        "CIC20123002117799\n" +
                        "CIC20123002117826\n" +
                        "CIC20123002117834\n" +
                        "CIC20123002117843\n" +
                        "CIC20123002117887\n" +
                        "CIC20123002117911\n" +
                        "CIC20123002117916\n" +
                        "CIC20123002117935\n" +
                        "CIC20123002117940\n" +
                        "CIC20123002117961\n" +
                        "CIC20123002117967\n" +
                        "CIC20123002117984\n" +
                        "CIC20123002117989\n" +
                        "CIC20123002118002\n" +
                        "CIC20123002118019\n" +
                        "CIC20123002118024\n" +
                        "CIC20123002118078\n" +
                        "CIC20123002118107\n" +
                        "CIC20123002118119\n" +
                        "CIC20123002118137\n" +
                        "CIC20123002118140\n" +
                        "CIC20123002118149\n" +
                        "CIC20123002118197\n" +
                        "CIC20123002118221\n" +
                        "CIC20123002118225\n" +
                        "CIC20123002118238\n" +
                        "CIC20123002118240\n" +
                        "CIC20123002118254\n" +
                        "CIC20123002118273\n" +
                        "CIC20123002118284\n" +
                        "CIC20123002118285\n" +
                        "CIC20123002118292\n" +
                        "CIC20123002118309\n" +
                        "CIC20123002118319\n" +
                        "CIC20123002118342\n" +
                        "CIC20123002118357\n" +
                        "CIC20123002118360\n" +
                        "CIC20123002118362\n" +
                        "CIC20123002118363\n" +
                        "CIC20123002118369\n" +
                        "CIC20123002118387\n" +
                        "CIC20123002118400\n" +
                        "CIC20123002118409\n" +
                        "CIC20123002118411\n" +
                        "CIC20123002118419\n" +
                        "CIC20123002118420\n" +
                        "CIC20123002118424\n" +
                        "CIC20123002118427\n" +
                        "CIC20123002118428\n" +
                        "CIC20123002118447\n" +
                        "CIC20123002118456\n" +
                        "CIC20123002118460\n" +
                        "CIC20123002118475\n" +
                        "CIC20123002118481\n" +
                        "CIC20123002118483\n" +
                        "CIC20123002118490\n" +
                        "CIC20123002118513\n" +
                        "CIC20123002118525\n" +
                        "CIC20123002118549\n" +
                        "CIC20123002118569\n" +
                        "CIC20123002118572\n" +
                        "CIC20123002118578\n" +
                        "CIC20123002118580\n" +
                        "CIC20123002118585\n" +
                        "CIC20123002118592\n" +
                        "CIC20123002118593\n" +
                        "CIC20123002118600\n" +
                        "CIC20123002118613\n" +
                        "CIC20123002118615\n" +
                        "CIC20123002118619\n" +
                        "CIC20123002118636\n" +
                        "CIC20123002118657\n" +
                        "CIC20123002118667\n" +
                        "CIC20123002118668\n" +
                        "CIC20123002118686\n" +
                        "CIC20123002118706\n" +
                        "CIC20123002118734\n" +
                        "CIC20123002118752\n" +
                        "CIC20123002118766\n" +
                        "CIC20123002118767\n" +
                        "CIC20123002118769\n" +
                        "CIC20123002118770\n" +
                        "CIC20123002118821\n" +
                        "CIC20123002118825\n" +
                        "CIC20123002118840\n" +
                        "CIC20123002118849\n" +
                        "CIC20123002118850\n" +
                        "CIC20123002118852\n" +
                        "CIC20123002118853\n" +
                        "CIC20123002118863\n" +
                        "CIC20123002118873\n" +
                        "CIC20123002118875\n" +
                        "CIC20123002118883\n" +
                        "CIC20123002118901\n" +
                        "CIC20123002118905\n" +
                        "CIC20123002118916\n" +
                        "CIC20123002118922\n" +
                        "CIC20123002118925\n" +
                        "CIC20123002118926\n" +
                        "CIC20123002118932\n" +
                        "CIC20123002118942\n" +
                        "CIC20123002118944\n" +
                        "CIC20123002118973\n" +
                        "CIC20123002118981\n" +
                        "CIC20123002118989\n" +
                        "CIC20123002119002\n" +
                        "CIC20123002119004\n" +
                        "CIC20123002119007\n" +
                        "CIC20123002119024\n" +
                        "CIC20123002119028\n" +
                        "CIC20123002119031\n" +
                        "CIC20123002119038\n" +
                        "CIC20123002119041\n" +
                        "CIC20123002119059\n" +
                        "CIC20123002119074\n" +
                        "CIC20123002119075\n" +
                        "CIC20123002119107\n" +
                        "CIC20123002119113\n" +
                        "CIC20123002119114\n" +
                        "CIC20123002119127\n" +
                        "CIC20123002119130\n" +
                        "CIC20123002119137\n" +
                        "CIC20123002119148\n" +
                        "CIC20123002119151\n" +
                        "CIC20123002119161\n" +
                        "CIC20123002119164\n" +
                        "CIC20123002119185\n" +
                        "CIC20123002119187\n" +
                        "CIC20123002119194\n" +
                        "CIC20123002119255\n" +
                        "CIC20123002119273\n" +
                        "CIC20123002119275\n" +
                        "CIC20123002119290\n" +
                        "CIC20123002119304\n" +
                        "CIC20123002119322\n" +
                        "CIC20123002119324\n" +
                        "CIC20123002119331\n" +
                        "CIC20123002119340\n" +
                        "CIC20123002119357\n" +
                        "CIC20123002119358\n" +
                        "CIC20123002119373\n" +
                        "CIC20123002119374\n" +
                        "CIC20123002119427\n" +
                        "CIC20123002119433\n" +
                        "CIC20123002119438\n" +
                        "CIC20123002119451\n" +
                        "CIC20123002119455\n" +
                        "CIC20123002119458\n" +
                        "CIC20123002119467\n" +
                        "CIC20123002119468\n" +
                        "CIC20123002119469\n" +
                        "CIC20123002119478\n" +
                        "CIC20123002119500\n" +
                        "CIC20123002119519\n" +
                        "CIC20123002119526\n" +
                        "CIC20123002119531\n" +
                        "CIC20123002119538\n" +
                        "CIC20123002119550\n" +
                        "CIC20123002119554\n" +
                        "CIC20123002119556\n" +
                        "CIC20123002119595\n" +
                        "CIC20123002119600\n" +
                        "CIC20123002119607\n" +
                        "CIC20123002119609\n" +
                        "CIC20123002119618\n" +
                        "CIC20123002119623\n" +
                        "CIC20123002119628\n" +
                        "CIC20123002119631\n" +
                        "CIC20123002119632\n" +
                        "CIC20123002119637\n" +
                        "CIC20123002119651\n" +
                        "CIC20123002119653\n" +
                        "CIC20123002119661\n" +
                        "CIC20123002119660\n" +
                        "CIC20123002119672\n" +
                        "CIC20123002119678\n" +
                        "CIC20123002119684\n" +
                        "CIC20123002119685\n" +
                        "CIC20123002119692\n" +
                        "CIC20123002119693\n" +
                        "CIC20123002119730\n" +
                        "CIC20123002119752\n" +
                        "CIC20123002119769\n" +
                        "CIC20123002119783\n" +
                        "CIC20123002119791\n" +
                        "CIC20123002119814\n" +
                        "CIC20123002119830\n" +
                        "CIC20123002119857\n" +
                        "CIC20123002119874\n" +
                        "CIC20123002119881\n" +
                        "CIC20123002119912\n" +
                        "CIC20123002119939\n" +
                        "CIC20123002119956\n" +
                        "CIC20123002119960\n" +
                        "CIC20123002119986\n" +
                        "CIC20123002119994\n" +
                        "CIC20123002119996\n" +
                        "CIC20123002120014\n" +
                        "CIC20123002120015\n" +
                        "CIC20123002120037\n" +
                        "CIC20123002120066\n" +
                        "CIC20123002120074\n" +
                        "CIC20123002120096\n" +
                        "CIC20123002120144\n" +
                        "CIC20123002120145\n" +
                        "CIC20123002120149\n" +
                        "CIC20123002120187\n" +
                        "CIC20123002120199\n" +
                        "CIC20123002120209\n" +
                        "CIC20123002120211\n" +
                        "CIC20123002120228\n" +
                        "CIC20123002120232\n" +
                        "CIC20123002120241\n" +
                        "CIC20123002120259\n" +
                        "CIC20123002120269\n" +
                        "CIC20123002120291\n" +
                        "CIC20123002120305\n" +
                        "CIC20123002120371\n" +
                        "CIC20123002120372\n" +
                        "CIC20123002120384\n" +
                        "CIC20123002120395\n" +
                        "CIC20123002120397\n" +
                        "CIC20123002120400\n" +
                        "CIC20123002120406\n" +
                        "CIC20123002120407\n" +
                        "CIC20123002120432\n" +
                        "CIC20123002120436\n" +
                        "CIC20123002120439\n" +
                        "CIC20123002120467\n" +
                        "CIC20123002120509\n" +
                        "CIC20123002120567\n" +
                        "CIC20123002120580\n" +
                        "CIC20123002120590\n" +
                        "CIC20123002120591\n" +
                        "CIC20123002120609\n" +
                        "CIC20123002120610\n" +
                        "CIC20123002120615\n" +
                        "CIC20123002120616\n" +
                        "CIC20123002120664\n" +
                        "CIC20123002120672\n" +
                        "CIC20123002120679\n" +
                        "CIC20123002120690\n" +
                        "CIC20123002120710\n" +
                        "CIC20123002120746\n" +
                        "CIC20123002120750\n" +
                        "CIC20123002120768\n" +
                        "CIC20123002120775\n" +
                        "CIC20123002120778\n" +
                        "CIC20123002120797\n" +
                        "CIC20123002120802\n" +
                        "CIC20123002120818\n" +
                        "CIC20123002120838\n" +
                        "CIC20123002120853\n" +
                        "CIC20123002120862\n" +
                        "CIC20123002120875\n" +
                        "CIC20123002120876\n" +
                        "CIC20123002120895\n" +
                        "CIC20123002120899\n" +
                        "CIC20123002120903\n" +
                        "CIC20123002120919\n" +
                        "CIC20123002120932\n" +
                        "CIC20123002120935\n" +
                        "CIC20123002120956\n" +
                        "CIC20123002120972\n" +
                        "CIC20123002120982\n" +
                        "CIC20123002120985\n" +
                        "CIC20123002120986\n" +
                        "CIC20123002120989\n" +
                        "CIC20123002121003\n" +
                        "CIC20123002121031\n" +
                        "CIC20123002121043\n" +
                        "CIC20123002121059\n" +
                        "CIC20123002121078\n" +
                        "CIC20123002121097\n" +
                        "CIC20123002121117\n" +
                        "CIC20123002121135\n" +
                        "CIC20123002121158\n" +
                        "CIC20123002121163\n" +
                        "CIC20123002121173\n" +
                        "CIC20123002121175\n" +
                        "CIC20123002121182\n" +
                        "CIC20123002121189\n" +
                        "CIC20123002121190\n" +
                        "CIC20123002121261\n" +
                        "CIC20123002121264\n" +
                        "CIC20123002121274\n" +
                        "CIC20123002121275\n" +
                        "CIC20123002121282\n" +
                        "CIC20123002121296\n" +
                        "CIC20123002121306\n" +
                        "CIC20123002121328\n" +
                        "CIC20123002121329\n" +
                        "CIC20123002121337\n" +
                        "CIC20123002121347\n" +
                        "CIC20123002121366\n" +
                        "CIC20123002121373\n" +
                        "CIC20123002121395\n" +
                        "CIC20123002121403\n" +
                        "CIC20123002121423\n" +
                        "CIC20123002121491\n" +
                        "CIC20123002121493\n" +
                        "CIC20123002121499\n" +
                        "CIC20123002121510\n" +
                        "CIC20123002121530\n" +
                        "CIC20123002121535\n" +
                        "CIC20123102121819\n" +
                        "CIC20123102121820\n" +
                        "CIC20123102121836\n" +
                        "CIC20123102121838\n" +
                        "CIC20123102121840\n" +
                        "CIC20123102121848\n" +
                        "CIC20123102121852\n" +
                        "CIC20123102121856\n" +
                        "CIC20123102121871\n" +
                        "CIC20123102121880\n" +
                        "CIC20123102121883\n" +
                        "CIC20123102121891\n" +
                        "CIC20123102121897\n" +
                        "CIC20123102121898\n" +
                        "CIC20123102121908\n" +
                        "CIC20123102121913\n" +
                        "CIC20123102121916\n" +
                        "CIC20123102121946\n" +
                        "CIC20123102121959\n" +
                        "CIC20123102121961\n" +
                        "CIC20123102121963\n" +
                        "CIC20123102121966\n" +
                        "CIC20123102121981\n" +
                        "CIC20123102121991\n" +
                        "CIC20123102121995\n" +
                        "CIC20123102122011\n" +
                        "CIC20123102122017\n" +
                        "CIC20123102122050\n" +
                        "CIC20123102122085").split("\n"));
//        List<String> allNotPay = IOUtils.readLines(new FileInputStream("/Users/<USER>/doc/zhnx/保险/process/中华联合/081需要刷新的订单-运维网络升级保单乱码.csv"));

//        String[] split = str.split("\n");
        RestTemplate restTemplate = new RestTemplate();

        HttpHeaders requestHeaders = new HttpHeaders();
        requestHeaders.set("Accept", "application/json, text/plain, */*");
        requestHeaders.set("authorization", "eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiI2NTM1IiwiZW1wbG95ZWVJZCI6IjY2ODEiLCJoclVzZXJJZCI6IjEyMTQxNjk4NiIsImFjY291bnQiOiIxNTU3NTE0MjM3NyIsInVzZXJOYW1lIjoi6YOR6Z2WIiwiam9iTnVtYmVyIjoiWkhOWDA5NzYwIiwibWFzdGVySm9iTnVtYmVyIjoiWkhOWDA5NzYwIiwib3JnSWQiOiIxMiIsImhyT3JnSWQiOiI4MDczNSIsImhyT3JnQ29kZSI6IjExMCIsImhyT3JnTmFtZSI6IuW8gOWPkeS4reW_g1_liJvmlrDkuJrliqHlj4rov5DokKXlubPlj7Dnu4QiLCJock9yZ1RyZWVQYXRoIjoiOTAwMTA1MTUzLzI4MjcxNy8zNjAzMTcvNDQ3OTQxLzgwNzM1IiwidXNlclR5cGUiOiIzIiwicmFuZG9tQ29kZSI6IjhDRjNDNjAxLTc5ODItNEVGQS04MEJBLTdDNzRENzE1Qzg4MiIsInVzZVNzbyI6InRydWUiLCJzeXN0ZW1JZCI6IjMiLCJjcmVhdGVUaW1lIjoiMTYwOTM3NzYyMzUwOSIsImdyYXkiOjB9.yeLQlp5UZfvcMFvrqq63MOn1yZ4tUBV5mteXrk7F9eoI4OmxM3C4xNG6QYW6C4rdNTVj6lCqsyI65P2qDMwNKQ");
//        requestHeaders.set("authorization", "");
        requestHeaders.set("User-Agent", "" +
                "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_3) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36");
        final String host = "https://bms.cfpamf.org.cn/api/insurance/micro/back/order/order/";

        List<String> lis = Collections.synchronizedList(new ArrayList<>());
        allNotPay.parallelStream()
                .filter(StringUtils::isNotBlank).parallel()
                .forEach(s -> {
                    HttpEntity<Object> objectHttpEntity = new HttpEntity<>(requestHeaders);
                    ResponseEntity<String> stringResponseEntity = restTemplate.postForEntity(host + s, objectHttpEntity, String.class);
                    String body = stringResponseEntity.getBody();
                    if (!StringUtils.contains(body, "success")) {
                        System.err.println("ref fail :" + s);
                        lis.add(s);
                    }
                    try {
                        Thread.sleep(1000L);
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                });

        System.err.println(lis);
    }

    //5491_1582037429267LDYQKP
    //5491_1582037525486HNMNYF
    //1481_1582037702271OMXWEI
    //1481_1582037739851SZKITV
    //1481_1582038618146VYRDQR
    //1481_1582038621247SLIDHG
    //1481_1582038651240WXLBUY
    //1481_1582038655243YUXDGS
    //1481_1582039241460RTUITI
    //1481_1582039281126FMQXZL
    //6535_1582039325404VGACEZ
    //5491_1582039537420YCNJYR
    //5491_1582039642568DPYSQH
    //6535_1582039765785PGFVLH
    //1481_1582041466248KZTQSX
    //5491_1582041471684DBEXSO
    //5491_1582041474162YWXDOD
    //5491_1582041478843JAQRPT
    //1481_1582041479641YNNCFV
    //1481_1582041480939UEZZEY
    //1481_1582041481648WMCKWL
    //1481_1582041482533FHFVJC
    //1481_1582041483169QTCNSN
    //1481_1582041484255IQWNAM
    //1481_1582041484898XOZZGH
    //1481_1582041485985MMTLCT
    //1481_1582041486893JTWBCH
    //1481_1582041487515OWODAK
    //5491_1582041957345LEQOCK
    //121_1582080749813SFRJMP
    //687_1582080951467VBEFXE
    //5491_1582081045973AVAILD
    //2200_1582081316906ITZQCW
    //121_1582081373053GFUXFE
    //2200_1582081390040DKDDAB
    //687_1582081434095JSYOYU
    //121_1582081451657TIJSDF
    //2200_1582081453130OBNKGV
    //687_1582081454915KJPNVE
    //2200_1582081460812YPWHOM
    //2200_1582081470750YIUYGG
    //2200_1582081495870ETBBMI
    //1481_1582081564698KRUIYV
    //2200_1582081599905VLVBQN
    //6535_1582081645140UEHBKB
    //2200_1582081650320WVLRKT
    //1481_1582082729791YZCBJB
    //1481_1582082753602QPPZSD
    //687_1582096402534HTUYZA
    //687_1582096410595SWCUUU
    //6642_1582165733340DNJWSZ
    //5491_1589268943618LBTWJY
    //121_1589269101460UGXKLX
    //2200_1589269958630FVSVTX
    //8947_1589276339972KGKVMC
    //5974_1589281418479FVQEQI
    //5973_1589281496827EWQJJD
    //5974_1589281533723VIANUI
    //6538_1589281582242JBSGOC
    //10837_1589281941054DADPFP
    //10885_1589282060475HLMRNY
    //9755_1589282286525WDHOWR
    //3502_1589282417081XBOLFB
    //9704_1589282777283NZYSBW
    //10875_1589283005135YEOWKT
    //4773_1589283944259GXTTNV
    //10659_1589284016120VQGZFA
    //10659_1589284040308UTQDFE
    //8496_1589284047962JGHLDC
    //2280_1589284215533KDOCUI
    //1623_1589284395109JDGAOM
    //9575_1589284452161XEBIUP
    //3516_1589284479082ZHNHGB
    //3991_1589284549029QKCUKM
    //1661_1589284717841OGAYTL
    //5904_1589284729305HABTDV
    //10607_1589284768293ANPRBE
    //9070_1589284776226KLYTUX
    //8329_1589284792544BJDAZR
    //4139_1589284911363XEJFIZ
    //1629_1589285485038EESDDM
    //6562_1589285917078KRRNFM
    //9878_1589286475535HESZFF
    //1220_1589286596957VQHABJ
    //3153_1589286832884AOFYAJ
    //3368_1589287270179OWYKIP
    //8889_1589287717869OBTWPH
    //1140_1589290126891WSQEPK
    //9360_1589290174721GBPPGX
    //6186_1589290654382WQTWCV
    //4102_1589290681356USJXED
    //3758_1589290717809FCIIIN
    //6862_1589291386355IBDLXF
    //6862_1589291530578KUOMHZ
    //373_1589292590211OHFFGW
    //10845_1589293303925OUYDNY
    //1998_1589295407468ZOTXWJ
    //3229_1589297638412UQYPBT
    //1174_1589319800462MSCRFL
    //3692_1589322560992QOCJLF
    //3826_1589322831023WDYVZT
    //2408_1589323985409KEWRVE
    //1492_1589327938743BQKNLG
    //9763_1589331725187HBFBSS
    //9763_1589331780543BNWEKD
    //9763_1589333001911OEZGUG
    //1101_1589333045278XUWIAR
    //888_1589334973421COUKUZ
    //2093_1589339325134RAIJZR
    //9124_1589339699919DHHLOP
    //3500_1589342839728RXBXOA
    //4083_1589345936305LPMEVP

    //支付异常数据
//    public static void main(String[] args) throws IOException {
//
//        List<String> allNotPay = IOUtils.readLines(new FileInputStream("/Users/<USER>/gitwork/zhnx/insurance/insurance-biz/src/test/resources/allNotPay.csv"));
//
////        String[] split = str.split("\n");
//        RestTemplate restTemplate = new RestTemplate();
//
//        HttpHeaders requestHeaders = new HttpHeaders();
//        requestHeaders.set("Accept", "application/json, text/plain, */*");
//        requestHeaders.set("authorization", "eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiI2NTM1IiwiZW1wbG95ZWVJZCI6IjY2ODEiLCJoclVzZXJJZCI6IjEyMTQxNjk4NiIsImFjY291bnQiOiIxNTU3NTE0MjM3NyIsInVzZXJOYW1lIjoi6YOR6Z2WIiwiam9iTnVtYmVyIjoiWkhOWDA5NzYwIiwib3JnSWQiOiIxMiIsImhyT3JnSWQiOiI4MDczNSIsImhyT3JnQ29kZSI6IjExMCIsImhyT3JnTmFtZSI6IuW8gOWPkeS4reW_g1_liJvmlrDkuJrliqHlj4rov5DokKXlubPlj7Dnu4QiLCJock9yZ1RyZWVQYXRoIjoiOTAwMTA1MTUzLzI4MjcxNy8zNjAzMTcvNDQ3OTQxLzgwNzM1IiwidXNlclR5cGUiOiIyIiwicmFuZG9tQ29kZSI6IkJEOEUwRjY2LURFRjAtNEVCNC1BODNBLUQ0M0NCNTY4N0REMSIsInVzZVNzbyI6ImZhbHNlIiwic3lzdGVtSWQiOiIwIiwiY3JlYXRlVGltZSI6IlR1ZSBBcHIgMjEgMTY6Mjc6MzUgQ1NUIDIwMjAifQ.1_mMfzt8X3Pphowd1YquO2u537iVGlfdFsJhmrvuGekjmEqCe0p1cGK9pTPSMaIE-VIncEdRX7Xj4tW2HdTUvw");
//        requestHeaders.set("User-Agent", "" +
//                "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_3) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36");
//        final String host = "https://bms.cfpamf.org.cn/api/insurance/micro/back/cic/error/process?orderId=";
//
//        List<String> lis = Collections.synchronizedList(new ArrayList<>());
//        allNotPay.parallelStream()
//                .filter(StringUtils::isNotBlank).parallel()
//                .forEach(s -> {
//                    HttpEntity<Object> objectHttpEntity = new HttpEntity<>(requestHeaders);
//                    ResponseEntity<String> stringResponseEntity = restTemplate.postForEntity(host + s, objectHttpEntity, String.class);
//                    String body = stringResponseEntity.getBody();
//                    if (!StringUtils.contains(body, "success")) {
//                        System.err.println("ref fail :" + s);
//                        lis.add(s);
//                    }
//                    try {
//                        Thread.sleep(1000L);
//                    } catch (InterruptedException e) {
//                        e.printStackTrace();
//                    }
//                });
//
//        System.err.println(lis);
//    }
}
