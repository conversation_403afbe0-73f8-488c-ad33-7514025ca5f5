package com.cfpamf.ms.insur.admin.service.order;

import com.cfpamf.ms.insur.admin.dao.safes.order.SmOrderDrainageMapper;
import com.cfpamf.ms.insur.admin.external.OrderSubmitRequest;
import com.cfpamf.ms.insur.admin.external.fh.dto.FhProduct;
import com.cfpamf.ms.insur.admin.external.ygibao.model.YgPolicyContractInfoVo;
import com.cfpamf.ms.insur.admin.pojo.dto.SmCreateOrderSubmitRequest;
import com.cfpamf.ms.insur.admin.pojo.po.SmOrderDrainage;
import com.cfpamf.ms.insur.admin.pojo.po.order.SmOrderShare;
import com.cfpamf.ms.insur.admin.service.BaseTest;
import com.cfpamf.ms.insur.base.util.HttpRequestUtil;
import com.github.jsonzou.jmockdata.JMockData;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import tk.mybatis.mapper.entity.Example;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@RunWith(PowerMockRunner.class)
@PrepareForTest(HttpRequestUtil.class)
public class LifeCappServiceTest extends BaseTest {

    @Mock private SmOrderDrainageMapper mockOrderDrainageMapper;

    @InjectMocks private LifeCappService lifeCappServiceUnderTest;

    @Test
    public void testRelateLifeParams() {
        // Setup
        final YgPolicyContractInfoVo req = new YgPolicyContractInfoVo();
        req.setApplicantPolicyNo("applicantPolicyNo");
        req.setApplicantTime("applicantTime");
        req.setChannelUserCode("channelUserCode");
        req.setChannelCode("channelCode");
        req.setManageWno("managerNo");

        // Run the test
        lifeCappServiceUnderTest.relateLifeParams(req, "fhOrderId");

        // Verify the results
        // Confirm SmOrderDrainageMapper.insertSelective(...).
        final SmOrderDrainage record = new SmOrderDrainage();
        record.setId(0);
        record.setOrderId("fhOrderId");
        record.setDrainageShareCode("channelUserCode");
        record.setDrainageShareName("drainageShareName");
        record.setDrainageIdNumber("drainageIdNumber");
        record.setDrainagePlatform("channelCode");
        record.setManagerNo("managerNo");
    }

    @Test
    public void testRelateWhaleParams() {
        // Setup
        final OrderSubmitRequest orderSubmitRequest = new OrderSubmitRequest();
        final FhProduct productInfo = new FhProduct();
        productInfo.setRecommendId("recommendId");
        orderSubmitRequest.setProductInfo(productInfo);
        orderSubmitRequest.setOrderId("orderId");
        orderSubmitRequest.setOrderType(0);
        orderSubmitRequest.setProductType("productType");
        final SmCreateOrderSubmitRequest submitRequest = new SmCreateOrderSubmitRequest(orderSubmitRequest);

        // Configure SmOrderDrainageMapper.selectByExample(...).
        final SmOrderDrainage smOrderDrainage = new SmOrderDrainage();
        smOrderDrainage.setId(0);
        smOrderDrainage.setOrderId("fhOrderId");
        smOrderDrainage.setDrainageShareCode("channelUserCode");
        smOrderDrainage.setDrainageShareName("drainageShareName");
        smOrderDrainage.setDrainageIdNumber("drainageIdNumber");
        smOrderDrainage.setDrainagePlatform("channelCode");
        smOrderDrainage.setManagerNo("managerNo");
        final List<SmOrderDrainage> smOrderDrainages = Arrays.asList(smOrderDrainage);
        when(mockOrderDrainageMapper.selectByExample(any(Object.class))).thenReturn(smOrderDrainages);

        // Run the test
        lifeCappServiceUnderTest.relateWhaleParams(submitRequest);

        // Verify the results
        // Confirm SmOrderDrainageMapper.updateByPrimaryKeySelective(...).
        final SmOrderDrainage record = new SmOrderDrainage();
        record.setId(0);
        record.setOrderId("fhOrderId");
        record.setDrainageShareCode("channelUserCode");
        record.setDrainageShareName("drainageShareName");
        record.setDrainageIdNumber("drainageIdNumber");
        record.setDrainagePlatform("channelCode");
        record.setManagerNo("managerNo");
    }

    @Test
    public void testRelateWhaleParams_SmOrderDrainageMapperSelectByExampleReturnsNull() {
        // Setup
        final OrderSubmitRequest orderSubmitRequest = new OrderSubmitRequest();
        final FhProduct productInfo = new FhProduct();
        productInfo.setRecommendId("recommendId");
        orderSubmitRequest.setProductInfo(productInfo);
        orderSubmitRequest.setOrderId("orderId");
        orderSubmitRequest.setOrderType(0);
        orderSubmitRequest.setProductType("productType");
        final SmCreateOrderSubmitRequest submitRequest = new SmCreateOrderSubmitRequest(orderSubmitRequest);
        when(mockOrderDrainageMapper.selectByExample(any(Object.class))).thenReturn(null);

        // Run the test
        lifeCappServiceUnderTest.relateWhaleParams(submitRequest);

        // Verify the results
        // Confirm SmOrderDrainageMapper.insertSelective(...).
    }

    @Test
    public void testRelateWhaleParams_SmOrderDrainageMapperSelectByExampleReturnsNoItems() {
        // Setup
        final OrderSubmitRequest orderSubmitRequest = new OrderSubmitRequest();
        final FhProduct productInfo = new FhProduct();
        productInfo.setRecommendId("recommendId");
        orderSubmitRequest.setProductInfo(productInfo);
        orderSubmitRequest.setOrderId("orderId");
        orderSubmitRequest.setOrderType(0);
        orderSubmitRequest.setProductType("productType");
        final SmCreateOrderSubmitRequest submitRequest = new SmCreateOrderSubmitRequest(orderSubmitRequest);
        when(mockOrderDrainageMapper.selectByExample(any(Object.class))).thenReturn(Collections.emptyList());

        // Run the test
        lifeCappServiceUnderTest.relateWhaleParams(submitRequest);

    }

    @Test
    public void testRelateWhaleGroupParams() {
        // Setup
        final OrderSubmitRequest orderSubmitRequest = new OrderSubmitRequest();
        final FhProduct productInfo = new FhProduct();
        productInfo.setRecommendId("recommendId");
        orderSubmitRequest.setProductInfo(productInfo);
        orderSubmitRequest.setOrderId("orderId");
        orderSubmitRequest.setOrderType(0);
        orderSubmitRequest.setProductType("productType");
        final List<SmCreateOrderSubmitRequest> submitRequestList = Arrays.asList(new SmCreateOrderSubmitRequest(orderSubmitRequest));

        // Configure SmOrderDrainageMapper.selectByExample(...).
        final SmOrderDrainage smOrderDrainage = new SmOrderDrainage();
        smOrderDrainage.setId(0);
        smOrderDrainage.setOrderId("fhOrderId");
        smOrderDrainage.setDrainageShareCode("channelUserCode");
        smOrderDrainage.setDrainageShareName("drainageShareName");
        smOrderDrainage.setDrainageIdNumber("drainageIdNumber");
        smOrderDrainage.setDrainagePlatform("channelCode");
        smOrderDrainage.setManagerNo("managerNo");
        final List<SmOrderDrainage> smOrderDrainages = Arrays.asList(smOrderDrainage);
        when(mockOrderDrainageMapper.selectByExample(any(Object.class))).thenReturn(smOrderDrainages);

        // Run the test
        lifeCappServiceUnderTest.relateWhaleGroupParams(submitRequestList, true);

        // Verify the results
        // Confirm SmOrderDrainageMapper.updateByPrimaryKeySelective(...).
        final SmOrderDrainage record = new SmOrderDrainage();
        record.setId(0);
        record.setOrderId("fhOrderId");
        record.setDrainageShareCode("channelUserCode");
        record.setDrainageShareName("drainageShareName");
        record.setDrainageIdNumber("drainageIdNumber");
        record.setDrainagePlatform("channelCode");
        record.setManagerNo("managerNo");

    }

    @Test
    public void testRelateWhaleGroupParams_SmOrderDrainageMapperSelectByExampleReturnsNull() {
        // Setup
        final OrderSubmitRequest orderSubmitRequest = new OrderSubmitRequest();
        final FhProduct productInfo = new FhProduct();
        productInfo.setRecommendId("recommendId");
        orderSubmitRequest.setProductInfo(productInfo);
        orderSubmitRequest.setOrderId("orderId");
        orderSubmitRequest.setOrderType(0);
        orderSubmitRequest.setProductType("productType");
        final List<SmCreateOrderSubmitRequest> submitRequestList = Arrays.asList(new SmCreateOrderSubmitRequest(orderSubmitRequest));
        when(mockOrderDrainageMapper.selectByExample(any(Object.class))).thenReturn(null);

        // Run the test
        lifeCappServiceUnderTest.relateWhaleGroupParams(submitRequestList, false);

        // Verify the results
        // Confirm SmOrderDrainageMapper.insertList(...).
        final SmOrderDrainage smOrderDrainage = new SmOrderDrainage();
        smOrderDrainage.setId(0);
        smOrderDrainage.setOrderId("fhOrderId");
        smOrderDrainage.setDrainageShareCode("channelUserCode");
        smOrderDrainage.setDrainageShareName("drainageShareName");
        smOrderDrainage.setDrainageIdNumber("drainageIdNumber");
        smOrderDrainage.setDrainagePlatform("channelCode");
        smOrderDrainage.setManagerNo("managerNo");
        final List<SmOrderDrainage> recordList = Arrays.asList(smOrderDrainage);
    }

    @Test
    public void testRelateWhaleGroupParams_SmOrderDrainageMapperSelectByExampleReturnsNoItems() {
        // Setup
        final OrderSubmitRequest orderSubmitRequest = new OrderSubmitRequest();
        final FhProduct productInfo = new FhProduct();
        productInfo.setRecommendId("recommendId");
        orderSubmitRequest.setProductInfo(productInfo);
        orderSubmitRequest.setOrderId("orderId");
        orderSubmitRequest.setOrderType(0);
        orderSubmitRequest.setProductType("productType");
        final List<SmCreateOrderSubmitRequest> submitRequestList = Arrays.asList(new SmCreateOrderSubmitRequest(orderSubmitRequest));
        when(mockOrderDrainageMapper.selectByExample(any(Object.class))).thenReturn(Collections.emptyList());

        // Run the test
        lifeCappServiceUnderTest.relateWhaleGroupParams(submitRequestList, false);

    }

    @Test
    public void testUpdateOrderDrainageInfo() {
        // Setup
        final SmOrderDrainage orderDrainage = new SmOrderDrainage();
        orderDrainage.setId(0);
        orderDrainage.setOrderId("fhOrderId");
        orderDrainage.setDrainageShareCode("channelUserCode");
        orderDrainage.setDrainageShareName("drainageShareName");
        orderDrainage.setDrainageIdNumber("drainageIdNumber");
        orderDrainage.setDrainagePlatform("channelCode");
        orderDrainage.setManagerNo("managerNo");

        // Run the test
        lifeCappServiceUnderTest.updateOrderDrainageInfo(orderDrainage);

        // Verify the results
        // Confirm SmOrderDrainageMapper.updateByPrimaryKeySelective(...).
        final SmOrderDrainage record = new SmOrderDrainage();
        record.setId(0);
        record.setOrderId("fhOrderId");
        record.setDrainageShareCode("channelUserCode");
        record.setDrainageShareName("drainageShareName");
        record.setDrainageIdNumber("drainageIdNumber");
        record.setDrainagePlatform("channelCode");
        record.setManagerNo("managerNo");
    }

    @Test
    public void testQueryByOrderId() {
        // Setup
        final SmOrderDrainage expectedResult = new SmOrderDrainage();
        expectedResult.setId(0);
        expectedResult.setOrderId("fhOrderId");
        expectedResult.setDrainageShareCode("channelUserCode");
        expectedResult.setDrainageShareName("drainageShareName");
        expectedResult.setDrainageIdNumber("drainageIdNumber");
        expectedResult.setDrainagePlatform("channelCode");
        expectedResult.setManagerNo("managerNo");

        // Configure SmOrderDrainageMapper.selectByExample(...).
        final SmOrderDrainage smOrderDrainage = new SmOrderDrainage();
        smOrderDrainage.setId(0);
        smOrderDrainage.setOrderId("fhOrderId");
        smOrderDrainage.setDrainageShareCode("channelUserCode");
        smOrderDrainage.setDrainageShareName("drainageShareName");
        smOrderDrainage.setDrainageIdNumber("drainageIdNumber");
        smOrderDrainage.setDrainagePlatform("channelCode");
        smOrderDrainage.setManagerNo("managerNo");
        final List<SmOrderDrainage> smOrderDrainages = Arrays.asList(smOrderDrainage);
        when(mockOrderDrainageMapper.selectByExample(any(Object.class))).thenReturn(smOrderDrainages);
        LifeCappService spy = Mockito.spy(lifeCappServiceUnderTest);
        // Setup
        doReturn(null).when(spy).queryByOrderId(any());
        // Run the test
        final SmOrderDrainage result = spy.queryByOrderId("orderId");

    }

    @Test
    public void testQueryByOrderId_SmOrderDrainageMapperReturnsNull() {
        // Setup
        when(mockOrderDrainageMapper.selectByExample(any(Object.class))).thenReturn(null);
        LifeCappService spy = Mockito.spy(lifeCappServiceUnderTest);
        // Setup
        doReturn(null).when(spy).queryByOrderId(any());
        // Run the test
        final SmOrderDrainage result = spy.queryByOrderId("orderId");

        // Verify the results
        assertNull(result);
    }

    @Test
    public void testQueryByOrderId_SmOrderDrainageMapperReturnsNoItems() {
        // Setup
        final SmOrderDrainage expectedResult = new SmOrderDrainage();
        expectedResult.setId(0);
        expectedResult.setOrderId("fhOrderId");
        expectedResult.setDrainageShareCode("channelUserCode");
        expectedResult.setDrainageShareName("drainageShareName");
        expectedResult.setDrainageIdNumber("drainageIdNumber");
        expectedResult.setDrainagePlatform("channelCode");
        expectedResult.setManagerNo("managerNo");

        when(mockOrderDrainageMapper.selectByExample(any(Object.class))).thenReturn(Collections.emptyList());
        LifeCappService spy = Mockito.spy(lifeCappServiceUnderTest);
        // Setup
        doReturn(null).when(spy).queryByOrderId(any());
        // Run the test
        final SmOrderDrainage result = spy.queryByOrderId("orderId");

        // Verify the results
    }

    @Test
    public void testQueryDistributeShare() {
        // Setup
        final SmOrderShare expectedResult = new SmOrderShare();
        expectedResult.setId(0);
        expectedResult.setFhOrderId("orderId");
        expectedResult.setFirstJobNumber("firstJobNumber");
        expectedResult.setShareIdNumber("drainageIdNumber");
        expectedResult.setShareName("drainageShareName");

        // Configure SmOrderDrainageMapper.selectByExample(...).
        final SmOrderDrainage smOrderDrainage = new SmOrderDrainage();
        smOrderDrainage.setId(0);
        smOrderDrainage.setOrderId("fhOrderId");
        smOrderDrainage.setDrainageShareCode("channelUserCode");
        smOrderDrainage.setDrainageShareName("drainageShareName");
        smOrderDrainage.setDrainageIdNumber("drainageIdNumber");
        smOrderDrainage.setDrainagePlatform("channelCode");
        smOrderDrainage.setManagerNo("managerNo");
        final List<SmOrderDrainage> smOrderDrainages = Arrays.asList(smOrderDrainage);
        when(mockOrderDrainageMapper.selectByExample(any(Object.class))).thenReturn(smOrderDrainages);
        LifeCappService spy = Mockito.spy(lifeCappServiceUnderTest);
        // Setup
        doReturn(null).when(spy).queryByOrderId(any());
        // Run the test
        final SmOrderShare result = spy.queryDistributeShare("orderId");


    }

    @Test
    public void testQueryDistributeShare_SmOrderDrainageMapperReturnsNull() {

        LifeCappService spy = Mockito.spy(lifeCappServiceUnderTest);
        // Setup
        doReturn(JMockData.mock(SmOrderDrainage.class)).when(spy).queryByOrderId(any());

        // Run the test
        final SmOrderShare result = spy.queryDistributeShare("orderId");

        // Verify the results
    }

    @Test
    public void testQueryDistributeShare_SmOrderDrainageMapperReturnsNoItems() {
        // Setup
        final SmOrderShare expectedResult = new SmOrderShare();
        expectedResult.setId(0);
        expectedResult.setFhOrderId("orderId");
        expectedResult.setFirstJobNumber("firstJobNumber");
        expectedResult.setShareIdNumber("drainageIdNumber");
        expectedResult.setShareName("drainageShareName");

        when(mockOrderDrainageMapper.selectByExample(any(Object.class))).thenReturn(Collections.emptyList());
        LifeCappService spy = Mockito.spy(lifeCappServiceUnderTest);
        // Setup
        doReturn(null).when(spy).queryByOrderId(any());
        // Run the test
        final SmOrderShare result = spy.queryDistributeShare("orderId");

        // Verify the results
    }
}
