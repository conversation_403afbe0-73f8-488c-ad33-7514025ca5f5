package com.cfpamf.ms.insur.weixin.zaGroup;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cfpamf.ms.insur.admin.external.zhongan.api.PaymentIntentUtil;
import com.cfpamf.ms.insur.admin.external.zhongan.model.ZaAcceptPolicyReq;
import com.cfpamf.ms.insur.admin.external.zhongan.model.accept.ZaChargeInfo;
import com.cfpamf.ms.insur.weixin.pojo.vo.apply.zhongan.request.*;
import com.zhongan.filegateway.common.FileGatewayClient;
import com.zhongan.filegateway.common.FileUploadRequest;
import com.zhongan.filegateway.common.FileUploadResponse;
import com.zhongan.scorpoin.common.ZhongAnApiClient;
import com.zhongan.scorpoin.common.ZhongAnOpenException;
import com.zhongan.scorpoin.common.dto.CommonRequest;
import com.zhongan.scorpoin.common.dto.CommonResponse;
import org.junit.Test;

import java.io.*;
import java.math.BigDecimal;
import java.util.*;

public class ZaMain {
    public static void main(String[]args) {
//        quote();
//        uploadFile();
//        underwriting();
//        getPayment();
//        accept();
//        queryEPolicyUrl();
//        endor();
//        endoruw();
//        endorCommit();
//        endorEffective();
    }

    private static String env = "dev";
    private static String appKey = "10001";
    private static String secret = "MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAO8h8JCJAMb1nd0uBkzZuWyNnL+atBzJKvIG7escD45ODf0AWKr8vSqLZ01HD86+a496CGjsae6GybK8C1MqiMSwaAsIv31nKD6U8xF607MPrD3r2lyjwUnmqBZY++R6yFNYz9ZDXcdiwCudESRsXunPJq7zfnnglCtEH+qqW8/VAgMBAAECgYEAnVc2gtMyKLbZTPuId65WG7+9oDB5S+ttD1xR1P1cmuRuvcYpkS/Eg6a/rJASLZULDpdbyzWqqaAUPD8QMINvAr3ZtkbwH5R0F/4aqOlx/5B0Okjsp3eSK2bQ8J2m/MmFKZxr6Aily7YUDdxcGcjLizsGi1KDkWS22JRufEeUNA0CQQD+g1XJ7ELqmUtrS4m4XnadB25f0g5QC0tMjoa3d9soMzK3q+Drkv8EZVpGSmSHEo1VlE7HUcnKNvK1BO5Nm4iXAkEA8IeZxaWmEcsRqiuFz8xmYGtKcYTmHgheGF4D+fnnFozSNP+3sS1lfgFQrjUkqUyZOoG1hPc6SDhGS4nbXwiscwJASO+gPR58yrgledkK3ZAMk9GWWtVajqu953GMv7UUU//gD+yspzXX6Q2WgkA9cMvrPtQig1I37sAya5e/JvRkfwJARzzCDEmdP9PW7YFqZjrxb0kXiTuFNAviYnEl2FltWb5nW48JBo6dao5VKONQclvfXfagnjriphUUrLatpB3bhQJAKRfJS6jDAIVKt7So5HOdzk4ipxgrMjG/QtZ1grO+VQectk4+tCwdJhOrr5blvdPQvFVqXBQfXuE7cibZrGs4sQ==";
    private static String version = "1.0.0";
    //private static String version = "1.0.0.zxy";

    @Test
    public void checkPay(){
        String env="dev";
        String appKey = "10001";
        String secret = "MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAO8h8JCJAMb1nd0uBkzZuWyNnL+atBzJKvIG7escD45ODf0AWKr8vSqLZ01HD86+a496CGjsae6GybK8C1MqiMSwaAsIv31nKD6U8xF607MPrD3r2lyjwUnmqBZY++R6yFNYz9ZDXcdiwCudESRsXunPJq7zfnnglCtEH+qqW8/VAgMBAAECgYEAnVc2gtMyKLbZTPuId65WG7+9oDB5S+ttD1xR1P1cmuRuvcYpkS/Eg6a/rJASLZULDpdbyzWqqaAUPD8QMINvAr3ZtkbwH5R0F/4aqOlx/5B0Okjsp3eSK2bQ8J2m/MmFKZxr6Aily7YUDdxcGcjLizsGi1KDkWS22JRufEeUNA0CQQD+g1XJ7ELqmUtrS4m4XnadB25f0g5QC0tMjoa3d9soMzK3q+Drkv8EZVpGSmSHEo1VlE7HUcnKNvK1BO5Nm4iXAkEA8IeZxaWmEcsRqiuFz8xmYGtKcYTmHgheGF4D+fnnFozSNP+3sS1lfgFQrjUkqUyZOoG1hPc6SDhGS4nbXwiscwJASO+gPR58yrgledkK3ZAMk9GWWtVajqu953GMv7UUU//gD+yspzXX6Q2WgkA9cMvrPtQig1I37sAya5e/JvRkfwJARzzCDEmdP9PW7YFqZjrxb0kXiTuFNAviYnEl2FltWb5nW48JBo6dao5VKONQclvfXfagnjriphUUrLatpB3bhQJAKRfJS6jDAIVKt7So5HOdzk4ipxgrMjG/QtZ1grO+VQectk4+tCwdJhOrr5blvdPQvFVqXBQfXuE7cibZrGs4sQ==";
        String version = "1.0.0";
        ZhongAnApiClient client = new ZhongAnApiClient(env,appKey,secret,version);
        String proxyUrl = "com.zhongan.brave.troops.findTradeElementByOutTradeNo";

        String orderId = "ZA21122702643528P";
        String merchantCode = "10001";
        Map<String,String> paramMap = new HashMap<>();
        paramMap.put("outTradeNo",orderId);
        paramMap.put("merchantCode",merchantCode);

        String resp = call(client, proxyUrl, paramMap);
        System.out.println(resp);
    }

    /**
     * 报价
     */
    @Test
    public void acceptPolicy(){
        String env="dev";
        String appKey = "10001";
        String secret = "MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAO8h8JCJAMb1nd0uBkzZuWyNnL+atBzJKvIG7escD45ODf0AWKr8vSqLZ01HD86+a496CGjsae6GybK8C1MqiMSwaAsIv31nKD6U8xF607MPrD3r2lyjwUnmqBZY++R6yFNYz9ZDXcdiwCudESRsXunPJq7zfnnglCtEH+qqW8/VAgMBAAECgYEAnVc2gtMyKLbZTPuId65WG7+9oDB5S+ttD1xR1P1cmuRuvcYpkS/Eg6a/rJASLZULDpdbyzWqqaAUPD8QMINvAr3ZtkbwH5R0F/4aqOlx/5B0Okjsp3eSK2bQ8J2m/MmFKZxr6Aily7YUDdxcGcjLizsGi1KDkWS22JRufEeUNA0CQQD+g1XJ7ELqmUtrS4m4XnadB25f0g5QC0tMjoa3d9soMzK3q+Drkv8EZVpGSmSHEo1VlE7HUcnKNvK1BO5Nm4iXAkEA8IeZxaWmEcsRqiuFz8xmYGtKcYTmHgheGF4D+fnnFozSNP+3sS1lfgFQrjUkqUyZOoG1hPc6SDhGS4nbXwiscwJASO+gPR58yrgledkK3ZAMk9GWWtVajqu953GMv7UUU//gD+yspzXX6Q2WgkA9cMvrPtQig1I37sAya5e/JvRkfwJARzzCDEmdP9PW7YFqZjrxb0kXiTuFNAviYnEl2FltWb5nW48JBo6dao5VKONQclvfXfagnjriphUUrLatpB3bhQJAKRfJS6jDAIVKt7So5HOdzk4ipxgrMjG/QtZ1grO+VQectk4+tCwdJhOrr5blvdPQvFVqXBQfXuE7cibZrGs4sQ==";
        String version = "1.0.0";
        ZhongAnApiClient client = new ZhongAnApiClient(env,appKey,secret,version);
        String proxyUrl = "zhongan.health.personal.policy.multiAcceptPolicy";

        ZaAcceptPolicyReq acceptPolicyReq = new ZaAcceptPolicyReq();
        acceptPolicyReq.setChannelCode("B02AYKH");
        acceptPolicyReq.setChannelOrderNo("ZA21123102643724P");

        ZaChargeInfo zaPayment = new ZaChargeInfo();
        zaPayment.setIsCashByZADesk(false);
        zaPayment.setPayTradeNo("");
        zaPayment.setPayTime("20211227111916");
        //1	支付宝 2	银行转账 3	其他 4	微信支付
        zaPayment.setPayWay("4");
        zaPayment.setPayMoney(new BigDecimal(403));

        acceptPolicyReq.setPayment(zaPayment);
        Map<String,Object> map = new HashMap<>();
        map.put("infoJson",acceptPolicyReq);
        String resp = call(client,proxyUrl,map);
        System.out.println(resp);
    }
    /**
     * 报价
     */
    @Test
    public  void quote(){
        ZhongAnApiClient client = new ZhongAnApiClient(env,appKey,secret,version);
        String quoteUrl = "zhongan.health.group.proposal.quotePrice";

        String demoFile = "/zhongan/quote_debug.json";
        String jsonData = readFile(demoFile);
        ZaGroupQuoteReq req = JSON.parseObject(jsonData,ZaGroupQuoteReq.class);
        String result = call(client,quoteUrl,req);
        System.err.println("==================================================================");
        System.out.println(result);
    }

    /**
     * 核保
     */
    @Test
    public  void underwriting(){
        ZhongAnApiClient client = new ZhongAnApiClient(env,appKey,secret,version);
        String quoteUrl = "zhongan.health.group.proposal.apply";

        String demoFile = "/zhongan/underwriting_debug.json";
        String jsonData = readFile(demoFile);
        ZaGroupUnderwritingReq req = JSON.parseObject(jsonData,ZaGroupUnderwritingReq.class);
        String result = call(client,quoteUrl,req);
        System.err.println("==================================================================");
        System.err.print(result);
    }

    @Test
    public void accept(){
        ZhongAnApiClient client = new ZhongAnApiClient(env,appKey,secret,version);
        String quoteUrl = "zhongan.health.group.policy.acceptPolicy";

        String demoFile = "/zhongan/accept.json";
        String jsonData = readFile(demoFile);
        ZaGroupInsureReq req = JSON.parseObject(jsonData,ZaGroupInsureReq.class);
        String result = call(client,quoteUrl,req);
        System.err.print(result);
    }

    @Test
    public  void queryEPolicyUrl(){
        ZhongAnApiClient client = new ZhongAnApiClient(env,appKey,secret,version);
        String quoteUrl = "zhongan.health.policy.queryEPolicyURL";
        String demoFile = "/zhongan/epolicy.json";
        String jsonData = readFile(demoFile);
        ZaWrapper req = JSON.parseObject(jsonData,ZaWrapper.class);
        System.err.println("==================================================================");
        String result = call(client,quoteUrl,req);
        System.err.println("==================================================================");
        System.err.println(result);
    }

    @Test
    public    void endor(){
        ZhongAnApiClient client = new ZhongAnApiClient(env,appKey,secret,version);
        String proxyUrl = "zhongan.health.group.endorsement.calculatedPremium";

        String demoFile = "/zhongan/endor_debug.json";
        String jsonData = readFile(demoFile);
        ZaGroupEndorReq req = JSON.parseObject(jsonData,ZaGroupEndorReq.class);
        String result = call(client,proxyUrl,req);
        System.err.print(result);
    }

    public  static void endoruw(){
        ZhongAnApiClient client = new ZhongAnApiClient(env,appKey,secret,version);
        String proxyUrl = "zhongan.health.group.endorsement.underwriting";

        String demoFile = "/zhongan/endor_uw_debug.json";
        String jsonData = readFile(demoFile);
        ZaGroupEndorReq req = JSON.parseObject(jsonData,ZaGroupEndorReq.class);
        String result = call(client,proxyUrl,req);
        System.err.print(result);
    }

    public  static void endorCommit(){
        ZhongAnApiClient client = new ZhongAnApiClient(env,appKey,secret,version);
        String proxyUrl = "zhongan.health.group.endorsement.submit";

        String demoFile = "/zhongan/endor_commit_debug.json";
        String jsonData = readFile(demoFile);
        ZaGroupEndorCommit req = JSON.parseObject(jsonData,ZaGroupEndorCommit.class);
        String result = call(client,proxyUrl,req);
        JSONObject jsonObject= JSON.parseObject(result);
        System.err.print(jsonObject.toJSONString());
    }

    public  static void endorEffective(){
        ZhongAnApiClient client = new ZhongAnApiClient(env,appKey,secret,version);
        String proxyUrl = "zhongan.health.group.endorsement.effective";

        String demoFile = "/zhongan/endor_effect_debug.json";
        String jsonData = readFile(demoFile);
        ZaGroupEndorEffective req = JSON.parseObject(jsonData,ZaGroupEndorEffective.class);
        String result = call(client,proxyUrl,req);
        System.err.print(result);
    }

    public static String endorEffective(ZaGroupEndorEffective req){
        ZhongAnApiClient client = new ZhongAnApiClient(env,appKey,secret,version);
        String proxyUrl = "zhongan.health.group.endorsement.effective";
        return call(client,proxyUrl,req);
    }


    /**
     * 线下支付-调试
     * @return
     */
    @Test
    public void apply4OfflinePay(){
        ZhongAnApiClient client = new ZhongAnApiClient(env,appKey,secret,version);
        String proxyUrl = "zhongan.health.group.policy.offlinePayment";

        String demoFile = "/zhongan/offline_apply.json";
        String jsonData = readFile(demoFile);
        ZaOfflinePayRequest req = JSON.parseObject(jsonData,ZaOfflinePayRequest.class);
        String result = call(client,proxyUrl,req);
        JSONObject jsonObject= JSON.parseObject(result);
        System.err.print(jsonObject.toJSONString());
    }

    /**
     * 线下支付-调试
     * @return
             */
    @Test
    public void endor4OfflinePay(){
        ZhongAnApiClient client = new ZhongAnApiClient(env,appKey,secret,version);
        String proxyUrl = "zhongan.health.group.endorsement.saveVoucher";

        String demoFile = "/zhongan/offline_endor.json";
        String jsonData = readFile(demoFile);
        ZaOfflinePayEndorRequest req = JSON.parseObject(jsonData,ZaOfflinePayEndorRequest.class);
        String result = call(client,proxyUrl,req);
        JSONObject jsonObject= JSON.parseObject(result);
        System.err.print(jsonObject.toJSONString());
    }


    public static ZhongAnApiClient mockClient(){
        String env="dev";
        String appKey = "10001";
        String secret = "MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAO8h8JCJAMb1nd0uBkzZuWyNnL+atBzJKvIG7escD45ODf0AWKr8vSqLZ01HD86+a496CGjsae6GybK8C1MqiMSwaAsIv31nKD6U8xF607MPrD3r2lyjwUnmqBZY++R6yFNYz9ZDXcdiwCudESRsXunPJq7zfnnglCtEH+qqW8/VAgMBAAECgYEAnVc2gtMyKLbZTPuId65WG7+9oDB5S+ttD1xR1P1cmuRuvcYpkS/Eg6a/rJASLZULDpdbyzWqqaAUPD8QMINvAr3ZtkbwH5R0F/4aqOlx/5B0Okjsp3eSK2bQ8J2m/MmFKZxr6Aily7YUDdxcGcjLizsGi1KDkWS22JRufEeUNA0CQQD+g1XJ7ELqmUtrS4m4XnadB25f0g5QC0tMjoa3d9soMzK3q+Drkv8EZVpGSmSHEo1VlE7HUcnKNvK1BO5Nm4iXAkEA8IeZxaWmEcsRqiuFz8xmYGtKcYTmHgheGF4D+fnnFozSNP+3sS1lfgFQrjUkqUyZOoG1hPc6SDhGS4nbXwiscwJASO+gPR58yrgledkK3ZAMk9GWWtVajqu953GMv7UUU//gD+yspzXX6Q2WgkA9cMvrPtQig1I37sAya5e/JvRkfwJARzzCDEmdP9PW7YFqZjrxb0kXiTuFNAviYnEl2FltWb5nW48JBo6dao5VKONQclvfXfagnjriphUUrLatpB3bhQJAKRfJS6jDAIVKt7So5HOdzk4ipxgrMjG/QtZ1grO+VQectk4+tCwdJhOrr5blvdPQvFVqXBQfXuE7cibZrGs4sQ==";
        String version = "1.0.0";
        ZhongAnApiClient client = new ZhongAnApiClient(env,appKey,secret,version);
        return client;

    }
    /**
     * 获取支付信息
     */
    public  static void getPayment(){
        String demoFile = "/zhongan/pay_endor.json";
        String jsonData = readFile(demoFile);
        Map<String,String> req = JSON.parseObject(jsonData,Map.class);
        System.out.println(PaymentIntentUtil.buildV2("http://cashier.itest.zhongan.com/zanc/gateway.do", req,
                "test"));
    }

    /**
     * 上传文件到众安
     */
    @Test
    public  void uploadFile()   {
        String env="tst";
        String appKey = "10001";
        String url ="http://filegw-daily.zhongan.com/file/upload";
        FileGatewayClient client = new FileGatewayClient(env, url);

        FileUploadRequest req = new FileUploadRequest();
        req.setAppKey(appKey);
        String localFile ="D:\\temp\\yyzz.jpg";
        req.addFile(new File(localFile));

        FileUploadResponse response = client.call(req);
        System.err.println(JSON.toJSONString(response));
    }

    public static String call(ZhongAnApiClient client, String serviceName, Object body)  {
        try {
            CommonRequest request = new CommonRequest(serviceName);
            JSONObject jsonObject = (JSONObject) JSONObject.toJSON(body);
            request.setParams(jsonObject);
            CommonResponse call = client.call(request);
            System.err.println(JSON.toJSONString(call));
            return call.getBizContent();
        } catch (ZhongAnOpenException e) {
            e.printStackTrace();
            return null;
        }
    }
    public static String readFile(String file){
        InputStream is = new ZaMain().getClass().getResourceAsStream(file);
        BufferedReader br = new BufferedReader(new InputStreamReader(is));
        String s="";
        String temp = "";
        try {
            while((s=br.readLine())!=null) {
                temp = temp+s;
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return temp;
    }
}
