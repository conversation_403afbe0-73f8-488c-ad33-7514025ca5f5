package com.cfpamf.ms.insur.admin.test;

import lombok.SneakyThrows;

import java.net.URLDecoder;

/**
 * <AUTHOR> 2020/4/26 13:39
 */
public class TestXmlDecoder {
    @SneakyThrows
    public static void main(String[] args) {

        String decode = URLDecoder.decode("%3C%3Fxml+version%3D%221.0%22+encoding%3D%22GBK%22%3F%3E%0D%0A%3Cbets%3E%0D%0A++%3Cpubarea%3E%0D%0A++++%3Cpkg_id%3E20200426122054476%3C%2Fpkg_id%3E%0D%0A++++%3Ccorp_code%3Ezhnx%3C%2Fcorp_code%3E%0D%0A++++%3Cchannel_code%3E9999%3C%2Fchannel_code%3E%0D%0A++++%3Ctrans_type%3E301%3C%2Ftrans_type%3E%0D%0A++++%3Csubmit_time%3E2020-04-26+12%3A20%3A54%3C%2Fsubmit_time%3E%0D%0A++%3C%2Fpubarea%3E%0D%0A++%3Cbody%3E%0D%0A++++%3Ctotalnum%3E1%3C%2Ftotalnum%3E%0D%0A++++%3Creq_list%3E%0D%0A++++++%3Creq_seq%3E1%3C%2Freq_seq%3E%0D%0A++++++%3Ctrans_id%3EE000%3C%2Ftrans_id%3E%0D%0A++++++%3Cpid%3EGSC20042600736962%3C%2Fpid%3E%0D%0A++++++%3Cpkg_id+%2F%3E%0D%0A++++++%3Cpkg_totalnum+%2F%3E%0D%0A++++++%3Cpay_acc_no+%2F%3E%0D%0A++++++%3Ctrans_amt+%2F%3E%0D%0A++++++%3Crecv_acc_no+%2F%3E%0D%0A++++++%3Cresp_code%3E2%3C%2Fresp_code%3E%0D%0A++++++%3Cbankresp_code+%2F%3E%0D%0A++++++%3Cresp_desc%3E%E4%BF%9D%E5%8D%95%E7%BC%B4%E8%B4%B9%E6%9F%A5%E8%AF%A2%E6%9C%AA%E9%80%9A%E8%BF%87%EF%BC%8C%E8%AF%B7%E6%A0%B8%E5%AF%B9%E5%90%8E%E9%87%8D%E6%96%B0%E6%8F%90%E4%BA%A4%E3%80%82%3C%2Fresp_desc%3E%0D%0A++++++%3Cmerchant_no%3E00999901%3C%2Fmerchant_no%3E%0D%0A++++++%3Cchannel_code+%2F%3E%0D%0A++++++%3Ctrans_method%3E10%3C%2Ftrans_method%3E%0D%0A++++++%3Cconfirm_code%3E0%3C%2Fconfirm_code%3E%0D%0A++++++%3Cbatch_id+%2F%3E%0D%0A++++++%3Cinsur_list%3E%0D%0A++++++++%3Cinsur%3E%0D%0A++++++++++%3Cinsur_no%3EzhnxGSC20042600736962%3C%2Finsur_no%3E%0D%0A++++++++++%3Cpolicy_no+%2F%3E%0D%0A++++++++++%3Cinsur_sta%3E0%3C%2Finsur_sta%3E%0D%0A++++++++++%3Csta_msg%3E%E7%B3%BB%E7%BB%9F%E7%B9%81%E5%BF%99%EF%BC%8C%E8%AF%B7%E7%A8%8D%E5%90%8E%E5%86%8D%E8%AF%95%E3%80%82%3C%2Fsta_msg%3E%0D%0A++++++++%3C%2Finsur%3E%0D%0A++++++++%3Cinsur%3E%0D%0A++++++++++%3Cinsur_no%3E927012020320197000110%3C%2Finsur_no%3E%0D%0A++++++++++%3Cpolicy_no+%2F%3E%0D%0A++++++++++%3Cinsur_sta%3E0%3C%2Finsur_sta%3E%0D%0A++++++++++%3Csta_msg%3E%E7%BC%B4%E8%B4%B9%E6%9F%A5%E8%AF%A2%E5%A4%B1%E8%B4%A5%EF%BC%8C%E5%A4%B1%E8%B4%A5%E5%8E%9F%E5%9B%A0%E6%98%AF%EF%BC%9A%E6%AD%A4%E4%B8%9A%E5%8A%A1%E5%8D%95%E5%8F%B7%EF%BC%9A927012020320197000110%E6%94%B6%E4%BB%98%E4%B8%8D%E5%AD%98%E5%9C%A8%3C%2Fsta_msg%3E%0D%0A++++++++%3C%2Finsur%3E%0D%0A++++++%3C%2Finsur_list%3E%0D%0A++++%3C%2Freq_list%3E%0D%0A++%3C%2Fbody%3E%0D%0A++%3Csignarea%3E%0D%0A++++%3Csign_time%3E20200426122054476%3C%2Fsign_time%3E%0D%0A++++%3Csign_content%3E23AB6FA860BB3E71C4FE76BC737A04D2%3C%2Fsign_content%3E%0D%0A++%3C%2Fsignarea%3E%0D%0A%3C%2Fbets%3E%0D%0A%0D%0A", "UTF-8");

        System.err.println(decode);
    }
}
