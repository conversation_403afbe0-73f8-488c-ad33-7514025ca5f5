package com.cfpamf.ms.insur.admin.service;

import com.cfpamf.cmis.common.utils.DateUtils;
import com.cfpamf.common.ms.util.DateUtil;
import com.cfpamf.ms.insur.admin.dao.safes.SmOrderMapper;
import com.cfpamf.ms.insur.admin.dao.safes.order.SmOrderWaitRenewalMapper;
import com.cfpamf.ms.insur.admin.pojo.convertor.OrderConvertor;
import com.cfpamf.ms.insur.admin.pojo.dto.order.OrderRenewalDTO;
import com.cfpamf.ms.insur.admin.pojo.dto.order.OrderWaitRenewalDTO;
import com.cfpamf.ms.insur.admin.pojo.po.order.SmOrderWaitRenewal;
import com.cfpamf.ms.insur.admin.renewal.service.OrderRenewalRecordService;
import com.cfpamf.ms.insur.admin.service.order.SmOrderServiceWrapper;
import com.cfpamf.ms.insur.base.util.HttpRequestUtil;
import com.cfpamf.ms.insur.base.util.SmsSenderUtil;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.beans.factory.annotation.Autowired;
import tk.mybatis.mapper.entity.Config;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.mapperhelper.EntityHelper;

import java.util.ArrayList;
import java.util.Date;

@RunWith(PowerMockRunner.class)
@PrepareForTest({HttpRequestUtil.class,DateUtil.class})
public class RenewalManagerServiceTest extends BaseTest {
    @InjectMocks
    RenewalManagerService renewalManagerService;
    @Mock
    OrderWaitRenewalDTO orderWaitRenewalDTO;
    @Mock
    private ThirdGatewayServiceProxyService thirdGatewayServiceProxyService;
    @Mock
    private SmOrderMapper smOrderMapper;
    @Mock
    private SmOrderServiceWrapper smOrderServiceWrapper;
    @Mock
    private SmOrderWaitRenewalMapper smOrderWaitRenewalMapper;
    @Mock
    private SmsSenderUtil smsSenderUtil;
    @Mock
    private OrderRenewalRecordService orderRenewalRecordService;


    @Before
    public void setUp() throws Exception {
        orderWaitRenewalDTO = new OrderWaitRenewalDTO();
        orderWaitRenewalDTO.setApplicationName("张三");
        orderWaitRenewalDTO.setApplicationMobile("13888888888");
        orderWaitRenewalDTO.setCurrCustomerAdminId("CNBJ0409");
        orderWaitRenewalDTO.setCurrCustomerAdminName("张健");
        orderWaitRenewalDTO.setCurrCustomerAdminMobile("13873106340");
        orderWaitRenewalDTO.setShortUrl("http://n.zhnx/dads");
        orderWaitRenewalDTO.setProductName("众安百万医疗");
        orderWaitRenewalDTO.setPlanName("家庭计划");
        orderWaitRenewalDTO.setGrace(12);
        orderWaitRenewalDTO.setEndTime(DateUtil.parseDate("2021-10-11 09:00:00"));
        orderWaitRenewalDTO.setRenewalEndDate(DateUtil.parseDate("2021-10-23 09:00:00"));


    }

    @After
    public void tearDown() throws Exception {
    }

    @Test
    public void genWaitRenewalOrderShortUrl() {

    }

    @Test
    public void addSmOrderWaitRenewalInfo() {
        String shortUrl = "http://n.zhnx/aaaa";
        Mockito.when(thirdGatewayServiceProxyService.genShortUrl(Mockito.anyString())).thenReturn(shortUrl);
        Mockito.when(smOrderWaitRenewalMapper.insertSelective(Mockito.any())).thenReturn(1);
        renewalManagerService.addSmOrderWaitRenewalInfo(orderWaitRenewalDTO);
    }

    @Test
    public void listSmsNotifyWaitRenewalOrderByPage() {
        Mockito.when(smOrderWaitRenewalMapper.listSmsNotifyWaitRenewalOrder()).thenReturn(new ArrayList<>());
        renewalManagerService.listSmsNotifyWaitRenewalOrderByPage(1000);
    }

    @Test
    public void sendWaitRenewalSms() {
        String shortUrl = "http://n.zhnx/aaaa";
        Mockito.when(smOrderServiceWrapper.getRenewalUrl(Mockito.anyString(),Mockito.anyString(),Mockito.anyString())).thenReturn("");
        Mockito.when(thirdGatewayServiceProxyService.genShortUrl(Mockito.anyString())).thenReturn(shortUrl);
        Mockito.doNothing().when(smsSenderUtil).sendCustRenewMessage(Mockito.anyString(),Mockito.any());
        Mockito.when(smOrderWaitRenewalMapper.updateByPrimaryKeySelective(Mockito.any())).thenReturn(1);
        OrderWaitRenewalDTO dto = orderWaitRenewalDTO;
        dto.setCustomerAdminId("");
        renewalManagerService.sendWaitRenewalSms(dto);
    }


    @Test
    public void createSms() {
        String expect1="亲爱的张三，您投保的众安百万医疗家庭计划已于2021-10-11到期，续保宽限期仅剩余12天，点击http://n.zhnx/dads 立即重新投保，宽限期内续保无需重新计算等待期。如有疑问，可联系您的客户经理张健，13873106340，感谢您的支持！";
        PowerMockito.mockStatic(DateUtil.class);
        PowerMockito.when(DateUtil.getNow()).thenReturn(DateUtils.parse("2021-10-12 09:00:00"));
        PowerMockito.when(DateUtil.format(Mockito.any(Date.class),Mockito.anyString())).thenReturn("2021-10-11");
        SmsSenderUtil.SmsSelfMsgDTO dto = renewalManagerService.createSms(orderWaitRenewalDTO);
        Assert.assertEquals(expect1,dto.getSelfMsg());
        String expect2 = "亲爱的张三，您投保的众安百万医疗家庭计划已于2021-10-11到期，续保宽限期仅剩余12天，点击http://n.zhnx/dads 立即重新投保，宽限期内续保无需重新计算等待期。";
        orderWaitRenewalDTO.setCurrCustomerAdminId("");
        dto = renewalManagerService.createSms(orderWaitRenewalDTO);
        Assert.assertEquals(expect2,dto.getSelfMsg());


        /*String expect3="亲爱的张三，您投保的众安百万医疗家庭计划将在2021-10-13到期，点击http://n.zhnx/dads 立即重新投保，无需重新计算等待期。如有疑问，可联系您的客户经理张健，13873106340，感谢您的支持！";
        PowerMockito.when(DateUtil.getNow()).thenReturn(DateUtils.parse("2021-10-10 09:00:00"));
        PowerMockito.when(DateUtil.format(Mockito.any(Date.class),Mockito.anyString())).thenReturn("2021-10-13");

        orderWaitRenewalDTO.setCurrCustomerAdminId("CNBJ0409");
        orderWaitRenewalDTO.setEndTime(DateUtil.parseDate("2021-10-13 09:00:00"));
        dto = renewalManagerService.createSms(orderWaitRenewalDTO);
        Assert.assertEquals(expect3,dto.getSelfMsg());

        String expect4 = "亲爱的张三，您投保的众安百万医疗家庭计划将在2021-10-13到期，点击http://n.zhnx/dads 立即重新投保，无需重新计算等待期。";
        orderWaitRenewalDTO.setCurrCustomerAdminId("");
        dto = renewalManagerService.createSms(orderWaitRenewalDTO);
        Assert.assertEquals(expect4,dto.getSelfMsg());*/
    }

    @Test
    public void renewalOrderCallback(){
        OrderRenewalDTO dto = new OrderRenewalDTO();
        Config config = new Config();
        EntityHelper.initEntityNameMap(SmOrderWaitRenewal.class, config);

        Mockito.when(smOrderWaitRenewalMapper.updateByExampleSelective(Mockito.any(SmOrderWaitRenewal.class),Mockito.any(Example.class))).thenReturn(1);
        Mockito.doNothing().when(orderRenewalRecordService).addCallBackRenewalOrder(Mockito.any(OrderRenewalDTO.class));
        renewalManagerService.renewalOrderCallback(dto);
    }
}