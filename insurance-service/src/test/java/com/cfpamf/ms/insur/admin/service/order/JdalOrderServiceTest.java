package com.cfpamf.ms.insur.admin.service.order;

import com.cfpamf.ms.insur.admin.config.ProductProperties;
import com.cfpamf.ms.insur.admin.config.ProductSpecialRuleProperties;
import com.cfpamf.ms.insur.admin.dao.safes.*;
import com.cfpamf.ms.insur.admin.dao.safes.order.*;
import com.cfpamf.ms.insur.admin.enums.EnumChannel;
import com.cfpamf.ms.insur.admin.external.ChannelOrderService;
import com.cfpamf.ms.insur.admin.external.OrderSubmitRequest;
import com.cfpamf.ms.insur.admin.external.OrderSubmitResponse;
import com.cfpamf.ms.insur.admin.external.fh.dto.*;
import com.cfpamf.ms.insur.admin.external.jdal.JdalOrderServiceAdapterImpl;
import com.cfpamf.ms.insur.admin.external.jdal.model.PolicyInfo;
import com.cfpamf.ms.insur.admin.pojo.dto.SmCreateOrderSubmitRequest;
import com.cfpamf.ms.insur.admin.pojo.dto.order.SmOrderCarInfoDTO;
import com.cfpamf.ms.insur.admin.pojo.dto.order.SmOrderHouseDTO;
import com.cfpamf.ms.insur.admin.pojo.form.product.TestPremiumDutyForm;
import com.cfpamf.ms.insur.admin.pojo.form.product.TestPremiumProductForm;
import com.cfpamf.ms.insur.admin.pojo.vo.SmPlanVO;
import com.cfpamf.ms.insur.admin.renewal.service.OrderRenewalRecordService;
import com.cfpamf.ms.insur.admin.service.*;
import com.cfpamf.ms.insur.base.event.EventBusEngine;
import com.cfpamf.ms.insur.base.infeface.CustomerCenterService;
import com.cfpamf.ms.insur.base.service.BusinessTokenService;
import com.cfpamf.ms.insur.base.util.HttpRequestUtil;
import com.cfpamf.ms.insur.common.service.monitor.BusinessMonitorService;
import com.cfpamf.ms.insur.weixin.dao.safes.EndorMapper;
import com.cfpamf.ms.pay.facade.config.PayFacadeConfigProperties;
import com.fasterxml.jackson.core.JsonParseException;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.http.HttpRequest;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.slf4j.Logger;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;

import java.io.IOException;
import java.math.BigDecimal;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.HashMap;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(PowerMockRunner.class)
@PrepareForTest(HttpRequestUtil.class)
public class JdalOrderServiceTest extends BaseTest {

    @Mock private JdalOrderServiceAdapterImpl mockAdapter;
    @Mock private ObjectMapper mockJsonMapper;
    @Mock private SmOrderExtendXjxhMapper mockSmOrderExtendXjxhMapper;
    @Mock private SmXjxhService mockSmXjxhService;
    private int YEAR_NODE;
    private DateTimeFormatter FMT_PARSE;
    private DateTimeFormatter FMT_SUB;
    @Mock private BusinessMonitorService monitorService;
    @Mock private SmProductService productService;
    @Mock private BusinessTokenService tokenService;
    @Mock private CustomerCenterService ccService;
    @Mock private SmCommissionMapper cmsMapper;
    @Mock private SmOrderMapper orderMapper;
    @Mock private SmOrderItemMapper orderItemMapper;
    @Mock private SmProductFormBuyLimitMapper formBuyLimitMapper;
    @Mock private AuthUserMapper userMapper;
    @Mock private OrderRenewalRecordService orderRenewalRecordService;
    @Mock private OrderCoreService orderCoreService;
    @Mock private SmChannelCallbackMapper channelCallbackMapper;
    @Mock private EventBusEngine busEngine;
    @Mock private SmOrderCarMapper carMapper;
    @Mock private SmOrderHouseMapper houseMapper;
    @Mock private SmOrderGroupNotifyService smOrderGroupNotifyService;
    @Mock private SmCommonSettingService commonSettingService;
    @Mock private SmOrderItemMapper smOrderItemMapper;
    @Mock private SmOrderRenewBindService smOrderRenewBindService;
    @Mock private SmXjxhService smXjxhService;
    @Mock private SmOrderPolicyMapper smOrderPolicyMapper;
    @Mock private ChOrderPersonalNotifyService chOrderPersonalNotifyService;
    @Mock private SmOrderRiskDutyMapper riskDutyMapper;
    @Mock private SmProductMapper productMapper;
    @Mock private SmOrderPolicyService smOrderPolicyService;
    @Mock private RenewalManagerService renewalManagerService;
    @Mock private SmOrderInsuredMapper insuredMapper;
    private long tokenLockTime;
    private int phoneLimit;
    @Mock private ProductProperties properties;
    @Mock private ProductSpecialRuleProperties productSpecialRuleProperties;
    @Mock private EndorMapper paymentMapper;
    @Mock private PayFacadeConfigProperties payFacadeConfigProperties;
    @Mock private TempOrderNewProductMapper tempOrderNewProductMapper;
    @Mock private Logger log;

    @InjectMocks private JdalOrderService jdalOrderServiceUnderTest;

    @Test
    public void testChannel() {
        assertEquals(EnumChannel.JDAL.getCode(), jdalOrderServiceUnderTest.channel());
    }

    @Test
    public void testOrderService() {
        // Setup
        // Run the test
        final ChannelOrderService result = jdalOrderServiceUnderTest.orderService();

        // Verify the results
    }

    @Test
    public void testSupport() {
        assertFalse(jdalOrderServiceUnderTest.support("channel"));
    }

    @Test
    public void testHandAsyncPayCallback() throws Exception {
        // Setup
        final MockHttpServletResponse response = new MockHttpServletResponse();
        final MockHttpServletRequest request = new MockHttpServletRequest();
        when(mockAdapter.aesDecrypt("object")).thenReturn("content");

        // Configure ObjectMapper.readValue(...).
        final PolicyInfo policyInfo = new PolicyInfo();
        policyInfo.setPolicyNo("policyNo");
        policyInfo.setPlanCode("planCode");
        policyInfo.setInsName("insName");
        policyInfo.setProductCode("productCode");
        policyInfo.setInRemark("inRemark");
        when(mockJsonMapper.readValue("content", PolicyInfo.class)).thenReturn(policyInfo);

        when(mockSmXjxhService.decodeXjxhRemark("inRemark")).thenReturn(new HashMap<>());

        // Configure JdalOrderServiceAdapter.cvtNotify(...).
        final OrderSubmitRequest orderSubmitRequest = new OrderSubmitRequest();
        final FhProduct productInfo = new FhProduct();
        productInfo.setProductId("fhProductId");
        productInfo.setRecommendId("recommendId");
        productInfo.setRecommendMainJobNumber("recommendMainJobNumber");
        productInfo.setRecommendOrgCode("recommendOrgCode");
        orderSubmitRequest.setProductInfo(productInfo);
        final FhOrderInfo orderInfo = new FhOrderInfo();
        orderInfo.setSubmitTime("submitTime");
        orderInfo.setStartTime("startTime");
        orderInfo.setEndTime("endTime");
        orderSubmitRequest.setOrderInfo(orderInfo);
        final FhProposer proposerInfo = new FhProposer();
        proposerInfo.setPersonName("insuredName");
        proposerInfo.setPersonGender("insuredPersonGender");
        proposerInfo.setIdType("idType");
        proposerInfo.setIdNumber("idNumber");
        proposerInfo.setBirthday("birthday");
        proposerInfo.setCellPhone("cellPhone");
        proposerInfo.setEmail("email");
        proposerInfo.setAddress("address");
        proposerInfo.setAppStatus("-2");
        proposerInfo.setPolicyNo("insIdNumber");
        proposerInfo.setDownloadURL("downloadURL");
        orderSubmitRequest.setProposerInfo(proposerInfo);
        final FhInsuredPerson fhInsuredPerson = new FhInsuredPerson();
        fhInsuredPerson.setPersonName("insuredName");
        fhInsuredPerson.setPersonGender("insuredPersonGender");
        fhInsuredPerson.setIdType("idType");
        fhInsuredPerson.setIdNumber("idNumber");
        fhInsuredPerson.setBirthday("birthday");
        fhInsuredPerson.setCellPhone("cellPhone");
        fhInsuredPerson.setAppStatus("-2");
        fhInsuredPerson.setRelationship("insuredAppntShipCode");
        fhInsuredPerson.setFlightNo("flightNo");
        fhInsuredPerson.setFlightTime("flightTime");
        fhInsuredPerson.setOccupationCode("");
        fhInsuredPerson.setOccupationGroup("occupationGroup");
        fhInsuredPerson.setDestinationCountryText("destinationCountryText");
        fhInsuredPerson.setAnnualIncome("annualIncome");
        fhInsuredPerson.setStudentType("studentType");
        fhInsuredPerson.setSchoolType("schoolType");
        fhInsuredPerson.setSchoolNature("schoolNature");
        fhInsuredPerson.setSchoolName("schoolName");
        fhInsuredPerson.setSchoolClass("schoolClass");
        fhInsuredPerson.setStudentId("studentId");
        fhInsuredPerson.setIdPeriodStart("idPeriodStart");
        fhInsuredPerson.setIdPeriodEnd("idPeriodEnd");
        fhInsuredPerson.setSmoke("smoke");
        fhInsuredPerson.setOldPolicyNo("oldPolicyNo");
        final TestPremiumDutyForm testPremiumDutyForm = new TestPremiumDutyForm();
        fhInsuredPerson.setDuties(Arrays.asList(testPremiumDutyForm));
        final TestPremiumProductForm product = new TestPremiumProductForm();
        product.setProductId(0);
        product.setPlanId(0);
        product.setPlanCode("planCode");
        product.setPremium(new BigDecimal("0.00"));
        product.setQty(0);
        final TestPremiumDutyForm testPremiumDutyForm1 = new TestPremiumDutyForm();
        product.setPremiumDutyForms(Arrays.asList(testPremiumDutyForm1));
        fhInsuredPerson.setProduct(product);
        orderSubmitRequest.setInsuredPerson(Arrays.asList(fhInsuredPerson));
        final FhProperty propertyInfo = new FhProperty();
        propertyInfo.setPropertyInfoIsExist("0");
        orderSubmitRequest.setPropertyInfo(propertyInfo);
        final SmOrderHouseDTO house = new SmOrderHouseDTO();
        orderSubmitRequest.setHouse(house);
        final SmOrderCarInfoDTO carInfo = new SmOrderCarInfoDTO();
        orderSubmitRequest.setCarInfo(carInfo);
        orderSubmitRequest.setProductType("code");
        orderSubmitRequest.setBizCode("bizCode");
        orderSubmitRequest.setOrderOutType("orderOutType");
        orderSubmitRequest.setToken("token");
        orderSubmitRequest.setPlanId(0);
        orderSubmitRequest.setQty(0);
        orderSubmitRequest.setPreOrderId("preOrderId");
        orderSubmitRequest.setRenew(false);
        orderSubmitRequest.setRealRenewFlag(false);
        orderSubmitRequest.setAppNo("appNo");
        orderSubmitRequest.setAgentId(0);
        orderSubmitRequest.setJobCode("jobCode");
        final SmCreateOrderSubmitRequest smCreateOrderSubmitRequest = new SmCreateOrderSubmitRequest(orderSubmitRequest);
        final PolicyInfo policyInfo1 = new PolicyInfo();
        policyInfo1.setPolicyNo("policyNo");
        policyInfo1.setPlanCode("planCode");
        policyInfo1.setInsName("insName");
        policyInfo1.setProductCode("productCode");
        policyInfo1.setInRemark("inRemark");
        when(mockAdapter.cvtNotify(policyInfo1, new HashMap<>())).thenReturn(smCreateOrderSubmitRequest);

        // Run the test
        jdalOrderServiceUnderTest.handAsyncPayCallback("orderIdParams", "object", response, request);

    }

    @Test
    public void testHandAsyncPayCallback_ObjectMapperThrowsIOException() throws Exception {
        // Setup
        final MockHttpServletResponse response = new MockHttpServletResponse();
        final MockHttpServletRequest request = new MockHttpServletRequest();
        when(mockAdapter.aesDecrypt("object")).thenReturn("content");
        when(mockJsonMapper.readValue("content", PolicyInfo.class)).thenThrow(IOException.class);

        // Run the test
        jdalOrderServiceUnderTest.handAsyncPayCallback("orderIdParams", "object", response, request);

        // Verify the results
    }

    @Test
    public void testHandAsyncPayCallback_ObjectMapperThrowsJsonParseException() throws Exception {
        // Setup
        final MockHttpServletResponse response = new MockHttpServletResponse();
        final MockHttpServletRequest request = new MockHttpServletRequest();
        when(mockAdapter.aesDecrypt("object")).thenReturn("content");
        when(mockJsonMapper.readValue("content", PolicyInfo.class)).thenThrow(JsonParseException.class);

        // Run the test
        jdalOrderServiceUnderTest.handAsyncPayCallback("orderIdParams", "object", response, request);

        // Verify the results
    }

    @Test
    public void testHandAsyncPayCallback_ObjectMapperThrowsJsonMappingException() throws Exception {
        // Setup
        final MockHttpServletResponse response = new MockHttpServletResponse();
        final MockHttpServletRequest request = new MockHttpServletRequest();
        when(mockAdapter.aesDecrypt("object")).thenReturn("content");
        when(mockJsonMapper.readValue("content", PolicyInfo.class)).thenThrow(JsonMappingException.class);

        // Run the test
        jdalOrderServiceUnderTest.handAsyncPayCallback("orderIdParams", "object", response, request);

        // Verify the results
    }

    @Test
    public void testHandAsyncPayCallback_SmXjxhServiceDecodeXjxhRemarkThrowsIOException() throws Exception {
        // Setup
        final MockHttpServletResponse response = new MockHttpServletResponse();
        final MockHttpServletRequest request = new MockHttpServletRequest();
        when(mockAdapter.aesDecrypt("object")).thenReturn("content");

        // Configure ObjectMapper.readValue(...).
        final PolicyInfo policyInfo = new PolicyInfo();
        policyInfo.setPolicyNo("policyNo");
        policyInfo.setPlanCode("planCode");
        policyInfo.setInsName("insName");
        policyInfo.setProductCode("productCode");
        policyInfo.setInRemark("inRemark");
        when(mockJsonMapper.readValue("content", PolicyInfo.class)).thenReturn(policyInfo);

        when(mockSmXjxhService.decodeXjxhRemark("inRemark")).thenThrow(IOException.class);

        // Run the test
        jdalOrderServiceUnderTest.handAsyncPayCallback("orderIdParams", "object", response, request);

        // Verify the results
    }

    @Test
    public void testSaveOrderInfo() {
        // Setup
        final OrderSubmitRequest dto = new OrderSubmitRequest();
        final FhProduct productInfo = new FhProduct();
        productInfo.setProductId("fhProductId");
        productInfo.setRecommendId("recommendId");
        productInfo.setRecommendMainJobNumber("recommendMainJobNumber");
        productInfo.setRecommendOrgCode("recommendOrgCode");
        dto.setProductInfo(productInfo);
        final FhOrderInfo orderInfo = new FhOrderInfo();
        orderInfo.setSubmitTime("submitTime");
        orderInfo.setStartTime("startTime");
        orderInfo.setEndTime("endTime");
        dto.setOrderInfo(orderInfo);
        final FhProposer proposerInfo = new FhProposer();
        proposerInfo.setPersonName("insuredName");
        proposerInfo.setPersonGender("insuredPersonGender");
        proposerInfo.setIdType("idType");
        proposerInfo.setIdNumber("idNumber");
        proposerInfo.setBirthday("birthday");
        proposerInfo.setCellPhone("cellPhone");
        proposerInfo.setEmail("email");
        proposerInfo.setAddress("address");
        proposerInfo.setAppStatus("-2");
        proposerInfo.setPolicyNo("insIdNumber");
        proposerInfo.setDownloadURL("downloadURL");
        dto.setProposerInfo(proposerInfo);
        final FhInsuredPerson fhInsuredPerson = new FhInsuredPerson();
        fhInsuredPerson.setPersonName("insuredName");
        fhInsuredPerson.setPersonGender("insuredPersonGender");
        fhInsuredPerson.setIdType("idType");
        fhInsuredPerson.setIdNumber("idNumber");
        fhInsuredPerson.setBirthday("birthday");
        fhInsuredPerson.setCellPhone("cellPhone");
        fhInsuredPerson.setAppStatus("-2");
        fhInsuredPerson.setRelationship("insuredAppntShipCode");
        fhInsuredPerson.setFlightNo("flightNo");
        fhInsuredPerson.setFlightTime("flightTime");
        fhInsuredPerson.setOccupationCode("");
        fhInsuredPerson.setOccupationGroup("occupationGroup");
        fhInsuredPerson.setDestinationCountryText("destinationCountryText");
        fhInsuredPerson.setAnnualIncome("annualIncome");
        fhInsuredPerson.setStudentType("studentType");
        fhInsuredPerson.setSchoolType("schoolType");
        fhInsuredPerson.setSchoolNature("schoolNature");
        fhInsuredPerson.setSchoolName("schoolName");
        fhInsuredPerson.setSchoolClass("schoolClass");
        fhInsuredPerson.setStudentId("studentId");
        fhInsuredPerson.setIdPeriodStart("idPeriodStart");
        fhInsuredPerson.setIdPeriodEnd("idPeriodEnd");
        fhInsuredPerson.setSmoke("smoke");
        fhInsuredPerson.setOldPolicyNo("oldPolicyNo");
        final TestPremiumDutyForm testPremiumDutyForm = new TestPremiumDutyForm();
        fhInsuredPerson.setDuties(Arrays.asList(testPremiumDutyForm));
        final TestPremiumProductForm product = new TestPremiumProductForm();
        product.setProductId(0);
        product.setPlanId(0);
        product.setPlanCode("planCode");
        product.setPremium(new BigDecimal("0.00"));
        product.setQty(0);
        final TestPremiumDutyForm testPremiumDutyForm1 = new TestPremiumDutyForm();
        product.setPremiumDutyForms(Arrays.asList(testPremiumDutyForm1));
        fhInsuredPerson.setProduct(product);
        dto.setInsuredPerson(Arrays.asList(fhInsuredPerson));
        final FhProperty propertyInfo = new FhProperty();
        propertyInfo.setPropertyInfoIsExist("0");
        dto.setPropertyInfo(propertyInfo);
        final SmOrderHouseDTO house = new SmOrderHouseDTO();
        dto.setHouse(house);
        final SmOrderCarInfoDTO carInfo = new SmOrderCarInfoDTO();
        dto.setCarInfo(carInfo);
        dto.setProductType("code");
        dto.setBizCode("bizCode");
        dto.setOrderOutType("orderOutType");
        dto.setToken("token");
        dto.setPlanId(0);
        dto.setQty(0);
        dto.setPreOrderId("preOrderId");
        dto.setRenew(false);
        dto.setRealRenewFlag(false);
        dto.setAppNo("appNo");
        dto.setAgentId(0);
        dto.setJobCode("jobCode");

        final OrderSubmitResponse smOrderResp = new OrderSubmitResponse();
        smOrderResp.setNoticeCode("-1");
        smOrderResp.setNoticeMsg("noticeMsg");
        smOrderResp.setOrderId("orderId");
        smOrderResp.setAppNo("appNo");
        final OrderSubmitResponse.ReturnMap returnMap = new OrderSubmitResponse.ReturnMap();
        returnMap.setMsg("msg");
        returnMap.setRenewOrderSn("orderId");
        smOrderResp.setReturnMap(returnMap);

        final SmPlanVO planVo = new SmPlanVO();
        planVo.setId(0);
        planVo.setPlanId(0);
        planVo.setChannel("channel");
        planVo.setProductId(0);
        planVo.setFhProductId("fhProductId");
        planVo.setBuyLimit(0);
        planVo.setPlanCode("fhProductId");

        // Run the test
        jdalOrderServiceUnderTest.saveOrderInfo(dto, smOrderResp, planVo);

        // Verify the results
    }
}
