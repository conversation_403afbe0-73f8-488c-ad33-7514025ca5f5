package com.cfpamf.ms.insur.weixin.TkGroup;

import com.cfpamf.ms.insur.admin.external.common.codec.JacksonDecoder;
import com.cfpamf.ms.insur.admin.external.tk.TkApiProperties;
import com.cfpamf.ms.insur.admin.external.tk.TkConsts;
import com.cfpamf.ms.insur.admin.external.tk.client.TkGroupClient;
import com.cfpamf.ms.insur.admin.external.tk.codec.TKGroupEncoder;
import com.cfpamf.ms.insur.admin.external.tk.codec.TkErrorDecoder;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import feign.Feign;
import feign.Request;
import feign.Retryer;
import feign.httpclient.ApacheHttpClient;
import org.apache.http.config.Registry;
import org.apache.http.config.RegistryBuilder;
import org.apache.http.conn.socket.ConnectionSocketFactory;
import org.apache.http.conn.socket.PlainConnectionSocketFactory;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.conn.ssl.TrustStrategy;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.ssl.SSLContextBuilder;
import org.springframework.cloud.openfeign.support.SpringMvcContract;

import javax.net.ssl.SSLContext;
import java.security.cert.X509Certificate;
import java.time.LocalDate;
import java.time.LocalDateTime;

public class TkApiFactory {
    private static ObjectMapper objectMapper;

    public static TkApiProperties properties;

    private static String groupApi = "http://tkoh-t.tk.cn/groupPolicyTest/";

    static {
        objectMapper = new ObjectMapper();
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        objectMapper.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
        JavaTimeModule timeModule = new JavaTimeModule();
        timeModule.addDeserializer(LocalDateTime.class, new LocalDateTimeDeserializer(TkConsts.FMT_DATETIME));
        timeModule.addSerializer(LocalDateTime.class, new LocalDateTimeSerializer(TkConsts.FMT_DATETIME));
        timeModule.addDeserializer(LocalDate.class, new LocalDateDeserializer(TkConsts.FMT_DATE));
        timeModule.addSerializer(LocalDate.class, new LocalDateSerializer(TkConsts.FMT_DATE));
        objectMapper.registerModule(timeModule);

        properties = new TkApiProperties();
        properties.setChannelCodeAlias("yongtong");
        properties.setToken("1izSlPOezflX0061MQsrrO6MFK21vyNX");
        properties.setChannelCode("105000000600");
        properties.setProductCodeTY("11N00569");
        properties.setComboCodeTY("11N00574");
    }

    public static TkGroupClient tkGroupClient() {
        return Feign.builder()
                .options(new Request.Options(10_000, 30_000))
                .encoder(new TKGroupEncoder(properties, objectMapper))
                .decoder(new JacksonDecoder(objectMapper))
                .client(buildClient())
                .errorDecoder(new TkErrorDecoder())
                .retryer(Retryer.NEVER_RETRY)
                .contract(new SpringMvcContract())
                .target(TkGroupClient.class, groupApi);
    }

    /**
     * 构造客户端
     *
     * @return
     */
    private static ApacheHttpClient buildClient() {

        Registry<ConnectionSocketFactory> socketFactoryRegistry = RegistryBuilder.<ConnectionSocketFactory>create()
                .register("http", PlainConnectionSocketFactory.INSTANCE).register("https",
                        createSSL()).build();
        //创建ConnectionManager，添加Connection配置信息
        PoolingHttpClientConnectionManager connectionManager = new PoolingHttpClientConnectionManager
                (socketFactoryRegistry);

        return new ApacheHttpClient(HttpClientBuilder.create()
                .setConnectionManager(connectionManager).build());
    }

    private static SSLConnectionSocketFactory createSSL() {
        SSLContext sslContext = null;
        try {
            sslContext = new SSLContextBuilder().loadTrustMaterial(null, new TrustStrategy() {
                @Override
                public boolean isTrusted(X509Certificate[] chain, String authType) {
                    return true;
                }

            }).build();
        } catch (Exception e) {
            e.printStackTrace();
        }
        SSLConnectionSocketFactory sslConnectionSocketFactory = new SSLConnectionSocketFactory(sslContext);
        return sslConnectionSocketFactory;
    }
}
