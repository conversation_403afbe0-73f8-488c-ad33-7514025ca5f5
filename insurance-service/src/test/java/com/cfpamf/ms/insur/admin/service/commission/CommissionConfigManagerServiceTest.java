package com.cfpamf.ms.insur.admin.service.commission;

import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.ms.insur.admin.dao.safes.SmProductVersionMapper;
import com.cfpamf.ms.insur.admin.dao.safes.commission.SystemCommissionConfigDetailMapper;
import com.cfpamf.ms.insur.admin.dao.safes.commission.SystemCommissionConfigMapper;
import com.cfpamf.ms.insur.admin.dao.safes.product.PlanMapper;
import com.cfpamf.ms.insur.admin.dao.safes.product.SmPlanHistoryMapper;
import com.cfpamf.ms.insur.admin.pojo.dto.commission.CommissionConfigDTO;
import com.cfpamf.ms.insur.admin.pojo.dto.commission.CommissionConfigImportDTO;
import com.cfpamf.ms.insur.admin.pojo.dto.commission.CommissionConfigItemDTO;
import com.cfpamf.ms.insur.admin.pojo.dto.product.SmPlanRiskDTO;
import com.cfpamf.ms.insur.admin.pojo.po.SmCommonSetting;
import com.cfpamf.ms.insur.admin.pojo.po.commission.SystemCommissionConfig;
import com.cfpamf.ms.insur.admin.pojo.po.commission.SystemCommissionConfigDetail;
import com.cfpamf.ms.insur.admin.pojo.po.product.SmPlanHistory;
import com.cfpamf.ms.insur.admin.service.SmCommonSettingService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class CommissionConfigManagerServiceTest {

    @Mock
    private SystemCommissionConfigMapper mockSystemCommissionConfigMapper;
    @Mock
    private SystemCommissionConfigDetailMapper mockSystemCommissionConfigDetailMapper;
    @Mock
    private SystemCommissionConfigDetailMapper mockDetailMapper;
    @Mock
    private PlanMapper mockPlanMapper;
    @Mock
    private SmProductVersionMapper mockProductVersionMapper;
    @Mock
    private SmPlanHistoryMapper mockSmPlanHistoryMapper;
    @Mock
    private SmCommonSettingService mockSettingService;

    @Mock
    private CommissionConfigManagerService commissionConfigManagerServiceUnderTest;

    @Test
    public void testDeleteConfigByConfigId() throws Exception {
        // Setup
        // Configure SystemCommissionConfigMapper.selectByPrimaryKey(...).
        final SystemCommissionConfig systemCommissionConfig = new SystemCommissionConfig();
        systemCommissionConfig.setId(0);
        systemCommissionConfig.setType(0);
        systemCommissionConfig.setProductId(0);
        systemCommissionConfig.setStartTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        systemCommissionConfig.setEndTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        systemCommissionConfig.setRelatedCommissionFactor("relatedCommissionFactor");
        systemCommissionConfig.setCreateBy("createBy");
        systemCommissionConfig.setUpdateBy("createBy");
        systemCommissionConfig.setReleaseFlag(0);
        mockSystemCommissionConfigMapper.selectByPrimaryKey(0);

        // Run the test
        commissionConfigManagerServiceUnderTest.deleteConfigByConfigId(0);

        // Verify the results
        mockSystemCommissionConfigDetailMapper.deleteByConfigId(0);
        mockSystemCommissionConfigMapper.deleteByPrimaryKey(0);
    }

    @Test
    public void testSaveConfig() throws Exception {
        // Setup
        final CommissionConfigDTO dto = new CommissionConfigDTO();
        dto.setId(0);
        dto.setType(0);
        dto.setProductId(0);
        dto.setStartTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        dto.setEndTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        dto.setCommissionFactorLst(Arrays.asList("value"));
        final CommissionConfigItemDTO itemDTO = new CommissionConfigItemDTO();
        itemDTO.setPeriodNum(0);
        itemDTO.setPlanId(0);
        itemDTO.setRiskId(0);
        itemDTO.setPayWayList(Arrays.asList(0));
        itemDTO.setCoveredYearsList(Arrays.asList("value"));
        itemDTO.setValidPeriodList(Arrays.asList("value"));
        itemDTO.setCommissionRate(new BigDecimal("0.00"));
        dto.setFirstPeriodList(Arrays.asList(itemDTO));
        final CommissionConfigItemDTO itemDTO1 = new CommissionConfigItemDTO();
        itemDTO1.setPeriodNum(0);
        itemDTO1.setPlanId(0);
        itemDTO1.setRiskId(0);
        itemDTO1.setPayWayList(Arrays.asList(0));
        itemDTO1.setCoveredYearsList(Arrays.asList("value"));
        itemDTO1.setValidPeriodList(Arrays.asList("value"));
        itemDTO1.setCommissionRate(new BigDecimal("0.00"));
        dto.setOtherPeriodList(Arrays.asList(itemDTO1));
        dto.setOperator("createBy");
        dto.setReleaseFlag(0);

        // Configure SystemCommissionConfigMapper.selectRepeatTimeRecord(...).
        final SystemCommissionConfig systemCommissionConfig = new SystemCommissionConfig();
        systemCommissionConfig.setId(0);
        systemCommissionConfig.setType(0);
        systemCommissionConfig.setProductId(0);
        systemCommissionConfig.setStartTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        systemCommissionConfig.setEndTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        systemCommissionConfig.setRelatedCommissionFactor("relatedCommissionFactor");
        systemCommissionConfig.setCreateBy("createBy");
        systemCommissionConfig.setUpdateBy("createBy");
        systemCommissionConfig.setReleaseFlag(0);
        final List<SystemCommissionConfig> systemCommissionConfigs = Arrays.asList(systemCommissionConfig);
        mockSystemCommissionConfigMapper.selectRepeatTimeRecord(0, 0,
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0);

        mockProductVersionMapper.getMaxVersion(0);

        // Configure SmPlanHistoryMapper.selectByProductAndVersion(...).
        final SmPlanHistory smPlanHistory = new SmPlanHistory();
        smPlanHistory.setId(0);
        smPlanHistory.setPlanId(0);
        smPlanHistory.setVersion(0);
        smPlanHistory.setProductId(0);
        smPlanHistory.setPlanName("planName");
        final List<SmPlanHistory> smPlanHistories = Arrays.asList(smPlanHistory);
        mockSmPlanHistoryMapper.selectByProductAndVersion(0, 0);

        // Configure SystemCommissionConfigMapper.selectByPrimaryKey(...).
        final SystemCommissionConfig systemCommissionConfig1 = new SystemCommissionConfig();
        systemCommissionConfig1.setId(0);
        systemCommissionConfig1.setType(0);
        systemCommissionConfig1.setProductId(0);
        systemCommissionConfig1.setStartTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        systemCommissionConfig1.setEndTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        systemCommissionConfig1.setRelatedCommissionFactor("relatedCommissionFactor");
        systemCommissionConfig1.setCreateBy("createBy");
        systemCommissionConfig1.setUpdateBy("createBy");
        systemCommissionConfig1.setReleaseFlag(0);
        mockSystemCommissionConfigMapper.selectByPrimaryKey(0);

        // Run the test
        commissionConfigManagerServiceUnderTest.saveConfig(dto);

        // Verify the results
        // Confirm SystemCommissionConfigMapper.insertSelective(...).
        final SystemCommissionConfig t = new SystemCommissionConfig();
        t.setId(0);
        t.setType(0);
        t.setProductId(0);
        t.setStartTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        t.setEndTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        t.setRelatedCommissionFactor("relatedCommissionFactor");
        t.setCreateBy("createBy");
        t.setUpdateBy("createBy");
        t.setReleaseFlag(0);
        mockSystemCommissionConfigMapper.insertSelective(t);

        // Confirm SystemCommissionConfigDetailMapper.insertList(...).
        final SystemCommissionConfigDetail systemCommissionConfigDetail = new SystemCommissionConfigDetail();
        systemCommissionConfigDetail.setId(0);
        systemCommissionConfigDetail.setConfigId(0);
        systemCommissionConfigDetail.setPayWay("payWay");
        systemCommissionConfigDetail.setCoveredYears("coveredYears");
        systemCommissionConfigDetail.setValidPeriod("validPeriod");
        systemCommissionConfigDetail.setCreateBy("createBy");
        systemCommissionConfigDetail.setUpdateBy("createBy");
        final List<SystemCommissionConfigDetail> list = Arrays.asList(systemCommissionConfigDetail);
        mockSystemCommissionConfigDetailMapper.insertList(list);

        // Confirm SystemCommissionConfigMapper.updateByPrimaryKeySelective(...).
        final SystemCommissionConfig t1 = new SystemCommissionConfig();
        t1.setId(0);
        t1.setType(0);
        t1.setProductId(0);
        t1.setStartTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        t1.setEndTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        t1.setRelatedCommissionFactor("relatedCommissionFactor");
        t1.setCreateBy("createBy");
        t1.setUpdateBy("createBy");
        t1.setReleaseFlag(0);
        mockSystemCommissionConfigMapper.updateByPrimaryKeySelective(t1);
        mockSystemCommissionConfigDetailMapper.deleteByConfigId(0);
    }

    @Test
    public void testSaveConfig_SystemCommissionConfigMapperSelectRepeatTimeRecordReturnsNoItems() throws Exception {
        // Setup
        final CommissionConfigDTO dto = new CommissionConfigDTO();
        dto.setId(0);
        dto.setType(0);
        dto.setProductId(0);
        dto.setStartTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        dto.setEndTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        dto.setCommissionFactorLst(Arrays.asList("value"));
        final CommissionConfigItemDTO itemDTO = new CommissionConfigItemDTO();
        itemDTO.setPeriodNum(0);
        itemDTO.setPlanId(0);
        itemDTO.setRiskId(0);
        itemDTO.setPayWayList(Arrays.asList(0));
        itemDTO.setCoveredYearsList(Arrays.asList("value"));
        itemDTO.setValidPeriodList(Arrays.asList("value"));
        itemDTO.setCommissionRate(new BigDecimal("0.00"));
        dto.setFirstPeriodList(Arrays.asList(itemDTO));
        final CommissionConfigItemDTO itemDTO1 = new CommissionConfigItemDTO();
        itemDTO1.setPeriodNum(0);
        itemDTO1.setPlanId(0);
        itemDTO1.setRiskId(0);
        itemDTO1.setPayWayList(Arrays.asList(0));
        itemDTO1.setCoveredYearsList(Arrays.asList("value"));
        itemDTO1.setValidPeriodList(Arrays.asList("value"));
        itemDTO1.setCommissionRate(new BigDecimal("0.00"));
        dto.setOtherPeriodList(Arrays.asList(itemDTO1));
        dto.setOperator("createBy");
        dto.setReleaseFlag(0);

        mockSystemCommissionConfigMapper.selectRepeatTimeRecord(0, 0,
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0);
        mockProductVersionMapper.getMaxVersion(0);

        // Configure SmPlanHistoryMapper.selectByProductAndVersion(...).
        final SmPlanHistory smPlanHistory = new SmPlanHistory();
        smPlanHistory.setId(0);
        smPlanHistory.setPlanId(0);
        smPlanHistory.setVersion(0);
        smPlanHistory.setProductId(0);
        smPlanHistory.setPlanName("planName");
        final List<SmPlanHistory> smPlanHistories = Arrays.asList(smPlanHistory);
        mockSmPlanHistoryMapper.selectByProductAndVersion(0, 0);

        // Run the test
        commissionConfigManagerServiceUnderTest.saveConfig(dto);

        // Verify the results
        // Confirm SystemCommissionConfigMapper.insertSelective(...).
        final SystemCommissionConfig t = new SystemCommissionConfig();
        t.setId(0);
        t.setType(0);
        t.setProductId(0);
        t.setStartTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        t.setEndTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        t.setRelatedCommissionFactor("relatedCommissionFactor");
        t.setCreateBy("createBy");
        t.setUpdateBy("createBy");
        t.setReleaseFlag(0);
        mockSystemCommissionConfigMapper.insertSelective(t);

        // Confirm SystemCommissionConfigDetailMapper.insertList(...).
        final SystemCommissionConfigDetail systemCommissionConfigDetail = new SystemCommissionConfigDetail();
        systemCommissionConfigDetail.setId(0);
        systemCommissionConfigDetail.setConfigId(0);
        systemCommissionConfigDetail.setPayWay("payWay");
        systemCommissionConfigDetail.setCoveredYears("coveredYears");
        systemCommissionConfigDetail.setValidPeriod("validPeriod");
        systemCommissionConfigDetail.setCreateBy("createBy");
        systemCommissionConfigDetail.setUpdateBy("createBy");
        final List<SystemCommissionConfigDetail> list = Arrays.asList(systemCommissionConfigDetail);
        mockSystemCommissionConfigDetailMapper.insertList(list);
    }

    @Test
    public void testSaveConfig_SmPlanHistoryMapperReturnsNoItems() throws Exception {
        // Setup
        final CommissionConfigDTO dto = new CommissionConfigDTO();
        dto.setId(0);
        dto.setType(0);
        dto.setProductId(0);
        dto.setStartTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        dto.setEndTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        dto.setCommissionFactorLst(Arrays.asList("value"));
        final CommissionConfigItemDTO itemDTO = new CommissionConfigItemDTO();
        itemDTO.setPeriodNum(0);
        itemDTO.setPlanId(0);
        itemDTO.setRiskId(0);
        itemDTO.setPayWayList(Arrays.asList(0));
        itemDTO.setCoveredYearsList(Arrays.asList("value"));
        itemDTO.setValidPeriodList(Arrays.asList("value"));
        itemDTO.setCommissionRate(new BigDecimal("0.00"));
        dto.setFirstPeriodList(Arrays.asList(itemDTO));
        final CommissionConfigItemDTO itemDTO1 = new CommissionConfigItemDTO();
        itemDTO1.setPeriodNum(0);
        itemDTO1.setPlanId(0);
        itemDTO1.setRiskId(0);
        itemDTO1.setPayWayList(Arrays.asList(0));
        itemDTO1.setCoveredYearsList(Arrays.asList("value"));
        itemDTO1.setValidPeriodList(Arrays.asList("value"));
        itemDTO1.setCommissionRate(new BigDecimal("0.00"));
        dto.setOtherPeriodList(Arrays.asList(itemDTO1));
        dto.setOperator("createBy");
        dto.setReleaseFlag(0);

        mockSystemCommissionConfigMapper.selectRepeatTimeRecord(0, 0,
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0);
        mockProductVersionMapper.getMaxVersion(0);
        mockSmPlanHistoryMapper.selectByProductAndVersion(0, 0);

        // Run the test
        commissionConfigManagerServiceUnderTest.saveConfig(dto);
    }

    @Test
    public void testImportConfig() throws Exception {
        // Setup
        final CommissionConfigImportDTO commissionConfigImportDTO = new CommissionConfigImportDTO();
        commissionConfigImportDTO.setProductId(0);
        commissionConfigImportDTO.setInsureTypeSame(0);
        commissionConfigImportDTO.setSendPeriod(0);
        commissionConfigImportDTO.setCommissionFactorLst(Arrays.asList("value"));
        commissionConfigImportDTO.setFileURL("fileURL");

        final CommissionConfigItemDTO itemDTO = new CommissionConfigItemDTO();
        itemDTO.setPeriodNum(0);
        itemDTO.setPlanId(0);
        itemDTO.setRiskId(0);
        itemDTO.setPayWayList(Arrays.asList(0));
        itemDTO.setCoveredYearsList(Arrays.asList("value"));
        itemDTO.setValidPeriodList(Arrays.asList("value"));
        itemDTO.setCommissionRate(new BigDecimal("0.00"));
        final List<CommissionConfigItemDTO> expectedResult = Arrays.asList(itemDTO);
        mockProductVersionMapper.getMaxVersion(0);

        // Configure SmPlanHistoryMapper.queryPlanRiskByProductId(...).
        final SmPlanRiskDTO smPlanRiskDTO = new SmPlanRiskDTO();
        smPlanRiskDTO.setPlanId(0);
        smPlanRiskDTO.setPlanName("planName");
        smPlanRiskDTO.setRiskId(0);
        smPlanRiskDTO.setPlanRiskName("planRiskName");
        smPlanRiskDTO.setPayWay("payWay");
        smPlanRiskDTO.setCoveredYears("coveredYears");
        smPlanRiskDTO.setValidPeriod("validPeriod");
        final List<SmPlanRiskDTO> smPlanRiskDTOS = Arrays.asList(smPlanRiskDTO);
        mockSmPlanHistoryMapper.queryPlanRiskByProductId(0, 0);

        // Configure SmCommonSettingService.queryByFieldCode(...).
        final SmCommonSetting smCommonSetting = new SmCommonSetting();
        smCommonSetting.setId(0);
        smCommonSetting.setFieldCode("fieldCode");
        smCommonSetting.setOptionCode("optionCode");
        smCommonSetting.setOptionName("optionName");
        final List<SmCommonSetting> smCommonSettings = Arrays.asList(smCommonSetting);
        mockSettingService.queryByFieldCode("validPeriod");

        // Run the test
        commissionConfigManagerServiceUnderTest.importConfig(
                commissionConfigImportDTO);
    }

    @Test
    public void testImportConfig_SmPlanHistoryMapperReturnsNoItems() throws Exception {
        // Setup
        final CommissionConfigImportDTO commissionConfigImportDTO = new CommissionConfigImportDTO();
        commissionConfigImportDTO.setProductId(0);
        commissionConfigImportDTO.setInsureTypeSame(0);
        commissionConfigImportDTO.setSendPeriod(0);
        commissionConfigImportDTO.setCommissionFactorLst(Arrays.asList("value"));
        commissionConfigImportDTO.setFileURL("fileURL");

        mockProductVersionMapper.getMaxVersion(0);
        mockSmPlanHistoryMapper.queryPlanRiskByProductId(0, 0);

        // Run the test
        commissionConfigManagerServiceUnderTest.importConfig(commissionConfigImportDTO);
    }

    @Test
    public void testImportConfig_SmCommonSettingServiceReturnsNoItems() throws Exception {
        // Setup
        final CommissionConfigImportDTO commissionConfigImportDTO = new CommissionConfigImportDTO();
        commissionConfigImportDTO.setProductId(0);
        commissionConfigImportDTO.setInsureTypeSame(0);
        commissionConfigImportDTO.setSendPeriod(0);
        commissionConfigImportDTO.setCommissionFactorLst(Arrays.asList("value"));
        commissionConfigImportDTO.setFileURL("fileURL");

        mockProductVersionMapper.getMaxVersion(0);

        // Configure SmPlanHistoryMapper.queryPlanRiskByProductId(...).
        final SmPlanRiskDTO smPlanRiskDTO = new SmPlanRiskDTO();
        smPlanRiskDTO.setPlanId(0);
        smPlanRiskDTO.setPlanName("planName");
        smPlanRiskDTO.setRiskId(0);
        smPlanRiskDTO.setPlanRiskName("planRiskName");
        smPlanRiskDTO.setPayWay("payWay");
        smPlanRiskDTO.setCoveredYears("coveredYears");
        smPlanRiskDTO.setValidPeriod("validPeriod");
        final List<SmPlanRiskDTO> smPlanRiskDTOS = Arrays.asList(smPlanRiskDTO);
        mockSmPlanHistoryMapper.queryPlanRiskByProductId(0, 0);

        mockSettingService.queryByFieldCode("validPeriod");

        // Run the test
        commissionConfigManagerServiceUnderTest.importConfig(
                commissionConfigImportDTO);
    }

    @Test
    public void testAddTempConfig() throws Exception {
        // Setup
        final CommissionConfigDTO addDTO = new CommissionConfigDTO();
        addDTO.setId(0);
        addDTO.setType(0);
        addDTO.setProductId(0);
        addDTO.setStartTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        addDTO.setEndTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        addDTO.setCommissionFactorLst(Arrays.asList("value"));
        final CommissionConfigItemDTO itemDTO = new CommissionConfigItemDTO();
        itemDTO.setPeriodNum(0);
        itemDTO.setPlanId(0);
        itemDTO.setRiskId(0);
        itemDTO.setPayWayList(Arrays.asList(0));
        itemDTO.setCoveredYearsList(Arrays.asList("value"));
        itemDTO.setValidPeriodList(Arrays.asList("value"));
        itemDTO.setCommissionRate(new BigDecimal("0.00"));
        addDTO.setFirstPeriodList(Arrays.asList(itemDTO));
        final CommissionConfigItemDTO itemDTO1 = new CommissionConfigItemDTO();
        itemDTO1.setPeriodNum(0);
        itemDTO1.setPlanId(0);
        itemDTO1.setRiskId(0);
        itemDTO1.setPayWayList(Arrays.asList(0));
        itemDTO1.setCoveredYearsList(Arrays.asList("value"));
        itemDTO1.setValidPeriodList(Arrays.asList("value"));
        itemDTO1.setCommissionRate(new BigDecimal("0.00"));
        addDTO.setOtherPeriodList(Arrays.asList(itemDTO1));
        addDTO.setOperator("createBy");
        addDTO.setReleaseFlag(0);

        // Run the test
        commissionConfigManagerServiceUnderTest.addTempConfig(addDTO);

        // Verify the results
        // Confirm SystemCommissionConfigMapper.insertSelective(...).
        final SystemCommissionConfig t = new SystemCommissionConfig();
        t.setId(0);
        t.setType(0);
        t.setProductId(0);
        t.setStartTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        t.setEndTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        t.setRelatedCommissionFactor("relatedCommissionFactor");
        t.setCreateBy("createBy");
        t.setUpdateBy("createBy");
        t.setReleaseFlag(0);
        mockSystemCommissionConfigMapper.insertSelective(t);

        // Confirm SystemCommissionConfigDetailMapper.insertList(...).
        final SystemCommissionConfigDetail systemCommissionConfigDetail = new SystemCommissionConfigDetail();
        systemCommissionConfigDetail.setId(0);
        systemCommissionConfigDetail.setConfigId(0);
        systemCommissionConfigDetail.setPayWay("payWay");
        systemCommissionConfigDetail.setCoveredYears("coveredYears");
        systemCommissionConfigDetail.setValidPeriod("validPeriod");
        systemCommissionConfigDetail.setCreateBy("createBy");
        systemCommissionConfigDetail.setUpdateBy("createBy");
        final List<SystemCommissionConfigDetail> list = Arrays.asList(systemCommissionConfigDetail);
        mockSystemCommissionConfigDetailMapper.insertList(list);
    }

    @Test
    public void testEditTempConfig() throws Exception {
        // Setup
        final CommissionConfigDTO dto = new CommissionConfigDTO();
        dto.setId(0);
        dto.setType(0);
        dto.setProductId(0);
        dto.setStartTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        dto.setEndTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        dto.setCommissionFactorLst(Arrays.asList("value"));
        final CommissionConfigItemDTO itemDTO = new CommissionConfigItemDTO();
        itemDTO.setPeriodNum(0);
        itemDTO.setPlanId(0);
        itemDTO.setRiskId(0);
        itemDTO.setPayWayList(Arrays.asList(0));
        itemDTO.setCoveredYearsList(Arrays.asList("value"));
        itemDTO.setValidPeriodList(Arrays.asList("value"));
        itemDTO.setCommissionRate(new BigDecimal("0.00"));
        dto.setFirstPeriodList(Arrays.asList(itemDTO));
        final CommissionConfigItemDTO itemDTO1 = new CommissionConfigItemDTO();
        itemDTO1.setPeriodNum(0);
        itemDTO1.setPlanId(0);
        itemDTO1.setRiskId(0);
        itemDTO1.setPayWayList(Arrays.asList(0));
        itemDTO1.setCoveredYearsList(Arrays.asList("value"));
        itemDTO1.setValidPeriodList(Arrays.asList("value"));
        itemDTO1.setCommissionRate(new BigDecimal("0.00"));
        dto.setOtherPeriodList(Arrays.asList(itemDTO1));
        dto.setOperator("createBy");
        dto.setReleaseFlag(0);

        // Configure SystemCommissionConfigMapper.selectByPrimaryKey(...).
        final SystemCommissionConfig systemCommissionConfig = new SystemCommissionConfig();
        systemCommissionConfig.setId(0);
        systemCommissionConfig.setType(0);
        systemCommissionConfig.setProductId(0);
        systemCommissionConfig.setStartTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        systemCommissionConfig.setEndTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        systemCommissionConfig.setRelatedCommissionFactor("relatedCommissionFactor");
        systemCommissionConfig.setCreateBy("createBy");
        systemCommissionConfig.setUpdateBy("createBy");
        systemCommissionConfig.setReleaseFlag(0);
        mockSystemCommissionConfigMapper.selectByPrimaryKey(0);

        // Run the test
        commissionConfigManagerServiceUnderTest.editTempConfig(dto);

        // Verify the results
        // Confirm SystemCommissionConfigMapper.updateByPrimaryKeySelective(...).
        final SystemCommissionConfig t = new SystemCommissionConfig();
        t.setId(0);
        t.setType(0);
        t.setProductId(0);
        t.setStartTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        t.setEndTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        t.setRelatedCommissionFactor("relatedCommissionFactor");
        t.setCreateBy("createBy");
        t.setUpdateBy("createBy");
        t.setReleaseFlag(0);
        mockSystemCommissionConfigMapper.updateByPrimaryKeySelective(t);
        mockSystemCommissionConfigDetailMapper.deleteByConfigId(0);

        // Confirm SystemCommissionConfigDetailMapper.insertList(...).
        final SystemCommissionConfigDetail systemCommissionConfigDetail = new SystemCommissionConfigDetail();
        systemCommissionConfigDetail.setId(0);
        systemCommissionConfigDetail.setConfigId(0);
        systemCommissionConfigDetail.setPayWay("payWay");
        systemCommissionConfigDetail.setCoveredYears("coveredYears");
        systemCommissionConfigDetail.setValidPeriod("validPeriod");
        systemCommissionConfigDetail.setCreateBy("createBy");
        systemCommissionConfigDetail.setUpdateBy("createBy");
        final List<SystemCommissionConfigDetail> list = Arrays.asList(systemCommissionConfigDetail);
        mockSystemCommissionConfigDetailMapper.insertList(list);
    }

    @Test
    public void testAddConfig_ThrowsMSBizNormalException() throws Exception {
        // Setup
        final CommissionConfigDTO addDTO = new CommissionConfigDTO();
        addDTO.setId(0);
        addDTO.setType(0);
        addDTO.setProductId(0);
        addDTO.setStartTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        addDTO.setEndTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        addDTO.setCommissionFactorLst(Arrays.asList("value"));
        final CommissionConfigItemDTO itemDTO = new CommissionConfigItemDTO();
        itemDTO.setPeriodNum(0);
        itemDTO.setPlanId(0);
        itemDTO.setRiskId(0);
        itemDTO.setPayWayList(Arrays.asList(0));
        itemDTO.setCoveredYearsList(Arrays.asList("value"));
        itemDTO.setValidPeriodList(Arrays.asList("value"));
        itemDTO.setCommissionRate(new BigDecimal("0.00"));
        addDTO.setFirstPeriodList(Arrays.asList(itemDTO));
        final CommissionConfigItemDTO itemDTO1 = new CommissionConfigItemDTO();
        itemDTO1.setPeriodNum(0);
        itemDTO1.setPlanId(0);
        itemDTO1.setRiskId(0);
        itemDTO1.setPayWayList(Arrays.asList(0));
        itemDTO1.setCoveredYearsList(Arrays.asList("value"));
        itemDTO1.setValidPeriodList(Arrays.asList("value"));
        itemDTO1.setCommissionRate(new BigDecimal("0.00"));
        addDTO.setOtherPeriodList(Arrays.asList(itemDTO1));
        addDTO.setOperator("createBy");
        addDTO.setReleaseFlag(0);

        // Configure SystemCommissionConfigMapper.selectRepeatTimeRecord(...).
        final SystemCommissionConfig systemCommissionConfig = new SystemCommissionConfig();
        systemCommissionConfig.setId(0);
        systemCommissionConfig.setType(0);
        systemCommissionConfig.setProductId(0);
        systemCommissionConfig.setStartTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        systemCommissionConfig.setEndTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        systemCommissionConfig.setRelatedCommissionFactor("relatedCommissionFactor");
        systemCommissionConfig.setCreateBy("createBy");
        systemCommissionConfig.setUpdateBy("createBy");
        systemCommissionConfig.setReleaseFlag(0);
        final List<SystemCommissionConfig> systemCommissionConfigs = Arrays.asList(systemCommissionConfig);
        mockSystemCommissionConfigMapper.selectRepeatTimeRecord(0, 0,
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0);

        // Run the test
         commissionConfigManagerServiceUnderTest.addConfig(addDTO);
    }

    @Test
    public void testAddConfig_SystemCommissionConfigMapperSelectRepeatTimeRecordReturnsNoItems() throws Exception {
        // Setup
        final CommissionConfigDTO addDTO = new CommissionConfigDTO();
        addDTO.setId(0);
        addDTO.setType(0);
        addDTO.setProductId(0);
        addDTO.setStartTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        addDTO.setEndTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        addDTO.setCommissionFactorLst(Arrays.asList("value"));
        final CommissionConfigItemDTO itemDTO = new CommissionConfigItemDTO();
        itemDTO.setPeriodNum(0);
        itemDTO.setPlanId(0);
        itemDTO.setRiskId(0);
        itemDTO.setPayWayList(Arrays.asList(0));
        itemDTO.setCoveredYearsList(Arrays.asList("value"));
        itemDTO.setValidPeriodList(Arrays.asList("value"));
        itemDTO.setCommissionRate(new BigDecimal("0.00"));
        addDTO.setFirstPeriodList(Arrays.asList(itemDTO));
        final CommissionConfigItemDTO itemDTO1 = new CommissionConfigItemDTO();
        itemDTO1.setPeriodNum(0);
        itemDTO1.setPlanId(0);
        itemDTO1.setRiskId(0);
        itemDTO1.setPayWayList(Arrays.asList(0));
        itemDTO1.setCoveredYearsList(Arrays.asList("value"));
        itemDTO1.setValidPeriodList(Arrays.asList("value"));
        itemDTO1.setCommissionRate(new BigDecimal("0.00"));
        addDTO.setOtherPeriodList(Arrays.asList(itemDTO1));
        addDTO.setOperator("createBy");
        addDTO.setReleaseFlag(0);

        mockSystemCommissionConfigMapper.selectRepeatTimeRecord(0, 0,
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0);
        mockProductVersionMapper.getMaxVersion(0);

        // Configure SmPlanHistoryMapper.selectByProductAndVersion(...).
        final SmPlanHistory smPlanHistory = new SmPlanHistory();
        smPlanHistory.setId(0);
        smPlanHistory.setPlanId(0);
        smPlanHistory.setVersion(0);
        smPlanHistory.setProductId(0);
        smPlanHistory.setPlanName("planName");
        final List<SmPlanHistory> smPlanHistories = Arrays.asList(smPlanHistory);
        mockSmPlanHistoryMapper.selectByProductAndVersion(0, 0);

        // Run the test
        commissionConfigManagerServiceUnderTest.addConfig(addDTO);

        // Verify the results
        // Confirm SystemCommissionConfigMapper.insertSelective(...).
        final SystemCommissionConfig t = new SystemCommissionConfig();
        t.setId(0);
        t.setType(0);
        t.setProductId(0);
        t.setStartTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        t.setEndTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        t.setRelatedCommissionFactor("relatedCommissionFactor");
        t.setCreateBy("createBy");
        t.setUpdateBy("createBy");
        t.setReleaseFlag(0);
        mockSystemCommissionConfigMapper.insertSelective(t);

        // Confirm SystemCommissionConfigDetailMapper.insertList(...).
        final SystemCommissionConfigDetail systemCommissionConfigDetail = new SystemCommissionConfigDetail();
        systemCommissionConfigDetail.setId(0);
        systemCommissionConfigDetail.setConfigId(0);
        systemCommissionConfigDetail.setPayWay("payWay");
        systemCommissionConfigDetail.setCoveredYears("coveredYears");
        systemCommissionConfigDetail.setValidPeriod("validPeriod");
        systemCommissionConfigDetail.setCreateBy("createBy");
        systemCommissionConfigDetail.setUpdateBy("createBy");
        final List<SystemCommissionConfigDetail> list = Arrays.asList(systemCommissionConfigDetail);
        mockSystemCommissionConfigDetailMapper.insertList(list);
    }

    @Test
    public void testAddConfig_SmPlanHistoryMapperReturnsNoItems() throws Exception {
        // Setup
        final CommissionConfigDTO addDTO = new CommissionConfigDTO();
        addDTO.setId(0);
        addDTO.setType(0);
        addDTO.setProductId(0);
        addDTO.setStartTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        addDTO.setEndTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        addDTO.setCommissionFactorLst(Arrays.asList("value"));
        final CommissionConfigItemDTO itemDTO = new CommissionConfigItemDTO();
        itemDTO.setPeriodNum(0);
        itemDTO.setPlanId(0);
        itemDTO.setRiskId(0);
        itemDTO.setPayWayList(Arrays.asList(0));
        itemDTO.setCoveredYearsList(Arrays.asList("value"));
        itemDTO.setValidPeriodList(Arrays.asList("value"));
        itemDTO.setCommissionRate(new BigDecimal("0.00"));
        addDTO.setFirstPeriodList(Arrays.asList(itemDTO));
        final CommissionConfigItemDTO itemDTO1 = new CommissionConfigItemDTO();
        itemDTO1.setPeriodNum(0);
        itemDTO1.setPlanId(0);
        itemDTO1.setRiskId(0);
        itemDTO1.setPayWayList(Arrays.asList(0));
        itemDTO1.setCoveredYearsList(Arrays.asList("value"));
        itemDTO1.setValidPeriodList(Arrays.asList("value"));
        itemDTO1.setCommissionRate(new BigDecimal("0.00"));
        addDTO.setOtherPeriodList(Arrays.asList(itemDTO1));
        addDTO.setOperator("createBy");
        addDTO.setReleaseFlag(0);

        mockSystemCommissionConfigMapper.selectRepeatTimeRecord(0, 0,
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0);
        mockProductVersionMapper.getMaxVersion(0);
        mockSmPlanHistoryMapper.selectByProductAndVersion(0, 0);

        // Run the test
        commissionConfigManagerServiceUnderTest.addConfig(addDTO);
    }

    @Test
    public void testPublishConfig() {
        // Setup
        // Run the test
        commissionConfigManagerServiceUnderTest.publishConfig(0);

        // Verify the results
        // Confirm SystemCommissionConfigMapper.updateByPrimaryKeySelective(...).
        final SystemCommissionConfig t = new SystemCommissionConfig();
        t.setId(0);
        t.setType(0);
        t.setProductId(0);
        t.setStartTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        t.setEndTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        t.setRelatedCommissionFactor("relatedCommissionFactor");
        t.setCreateBy("createBy");
        t.setUpdateBy("createBy");
        t.setReleaseFlag(0);
        mockSystemCommissionConfigMapper.updateByPrimaryKeySelective(t);
    }

    @Test
    public void testEditConfig() throws Exception {
        // Setup
        final CommissionConfigDTO dto = new CommissionConfigDTO();
        dto.setId(0);
        dto.setType(0);
        dto.setProductId(0);
        dto.setStartTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        dto.setEndTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        dto.setCommissionFactorLst(Arrays.asList("value"));
        final CommissionConfigItemDTO itemDTO = new CommissionConfigItemDTO();
        itemDTO.setPeriodNum(0);
        itemDTO.setPlanId(0);
        itemDTO.setRiskId(0);
        itemDTO.setPayWayList(Arrays.asList(0));
        itemDTO.setCoveredYearsList(Arrays.asList("value"));
        itemDTO.setValidPeriodList(Arrays.asList("value"));
        itemDTO.setCommissionRate(new BigDecimal("0.00"));
        dto.setFirstPeriodList(Arrays.asList(itemDTO));
        final CommissionConfigItemDTO itemDTO1 = new CommissionConfigItemDTO();
        itemDTO1.setPeriodNum(0);
        itemDTO1.setPlanId(0);
        itemDTO1.setRiskId(0);
        itemDTO1.setPayWayList(Arrays.asList(0));
        itemDTO1.setCoveredYearsList(Arrays.asList("value"));
        itemDTO1.setValidPeriodList(Arrays.asList("value"));
        itemDTO1.setCommissionRate(new BigDecimal("0.00"));
        dto.setOtherPeriodList(Arrays.asList(itemDTO1));
        dto.setOperator("createBy");
        dto.setReleaseFlag(0);

        // Configure SystemCommissionConfigMapper.selectByPrimaryKey(...).
        final SystemCommissionConfig systemCommissionConfig = new SystemCommissionConfig();
        systemCommissionConfig.setId(0);
        systemCommissionConfig.setType(0);
        systemCommissionConfig.setProductId(0);
        systemCommissionConfig.setStartTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        systemCommissionConfig.setEndTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        systemCommissionConfig.setRelatedCommissionFactor("relatedCommissionFactor");
        systemCommissionConfig.setCreateBy("createBy");
        systemCommissionConfig.setUpdateBy("createBy");
        systemCommissionConfig.setReleaseFlag(0);
        mockSystemCommissionConfigMapper.selectByPrimaryKey(0);

        // Configure SystemCommissionConfigMapper.selectRepeatTimeRecord(...).
        final SystemCommissionConfig systemCommissionConfig1 = new SystemCommissionConfig();
        systemCommissionConfig1.setId(0);
        systemCommissionConfig1.setType(0);
        systemCommissionConfig1.setProductId(0);
        systemCommissionConfig1.setStartTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        systemCommissionConfig1.setEndTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        systemCommissionConfig1.setRelatedCommissionFactor("relatedCommissionFactor");
        systemCommissionConfig1.setCreateBy("createBy");
        systemCommissionConfig1.setUpdateBy("createBy");
        systemCommissionConfig1.setReleaseFlag(0);
        final List<SystemCommissionConfig> systemCommissionConfigs = Arrays.asList(systemCommissionConfig1);
        mockSystemCommissionConfigMapper.selectRepeatTimeRecord(0, 0,
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0);

        mockProductVersionMapper.getMaxVersion(0);

        // Configure SmPlanHistoryMapper.selectByProductAndVersion(...).
        final SmPlanHistory smPlanHistory = new SmPlanHistory();
        smPlanHistory.setId(0);
        smPlanHistory.setPlanId(0);
        smPlanHistory.setVersion(0);
        smPlanHistory.setProductId(0);
        smPlanHistory.setPlanName("planName");
        final List<SmPlanHistory> smPlanHistories = Arrays.asList(smPlanHistory);
        mockSmPlanHistoryMapper.selectByProductAndVersion(0, 0);

        // Run the test
        commissionConfigManagerServiceUnderTest.editConfig(dto);

        // Verify the results
        // Confirm SystemCommissionConfigMapper.updateByPrimaryKeySelective(...).
        final SystemCommissionConfig t = new SystemCommissionConfig();
        t.setId(0);
        t.setType(0);
        t.setProductId(0);
        t.setStartTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        t.setEndTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        t.setRelatedCommissionFactor("relatedCommissionFactor");
        t.setCreateBy("createBy");
        t.setUpdateBy("createBy");
        t.setReleaseFlag(0);
        mockSystemCommissionConfigMapper.updateByPrimaryKeySelective(t);
        mockSystemCommissionConfigDetailMapper.deleteByConfigId(0);

        // Confirm SystemCommissionConfigDetailMapper.insertList(...).
        final SystemCommissionConfigDetail systemCommissionConfigDetail = new SystemCommissionConfigDetail();
        systemCommissionConfigDetail.setId(0);
        systemCommissionConfigDetail.setConfigId(0);
        systemCommissionConfigDetail.setPayWay("payWay");
        systemCommissionConfigDetail.setCoveredYears("coveredYears");
        systemCommissionConfigDetail.setValidPeriod("validPeriod");
        systemCommissionConfigDetail.setCreateBy("createBy");
        systemCommissionConfigDetail.setUpdateBy("createBy");
        final List<SystemCommissionConfigDetail> list = Arrays.asList(systemCommissionConfigDetail);
        mockSystemCommissionConfigDetailMapper.insertList(list);
    }

    @Test
    public void testEditConfig_SystemCommissionConfigMapperSelectRepeatTimeRecordReturnsNoItems() throws Exception {
        // Setup
        final CommissionConfigDTO dto = new CommissionConfigDTO();
        dto.setId(0);
        dto.setType(0);
        dto.setProductId(0);
        dto.setStartTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        dto.setEndTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        dto.setCommissionFactorLst(Arrays.asList("value"));
        final CommissionConfigItemDTO itemDTO = new CommissionConfigItemDTO();
        itemDTO.setPeriodNum(0);
        itemDTO.setPlanId(0);
        itemDTO.setRiskId(0);
        itemDTO.setPayWayList(Arrays.asList(0));
        itemDTO.setCoveredYearsList(Arrays.asList("value"));
        itemDTO.setValidPeriodList(Arrays.asList("value"));
        itemDTO.setCommissionRate(new BigDecimal("0.00"));
        dto.setFirstPeriodList(Arrays.asList(itemDTO));
        final CommissionConfigItemDTO itemDTO1 = new CommissionConfigItemDTO();
        itemDTO1.setPeriodNum(0);
        itemDTO1.setPlanId(0);
        itemDTO1.setRiskId(0);
        itemDTO1.setPayWayList(Arrays.asList(0));
        itemDTO1.setCoveredYearsList(Arrays.asList("value"));
        itemDTO1.setValidPeriodList(Arrays.asList("value"));
        itemDTO1.setCommissionRate(new BigDecimal("0.00"));
        dto.setOtherPeriodList(Arrays.asList(itemDTO1));
        dto.setOperator("createBy");
        dto.setReleaseFlag(0);

        // Configure SystemCommissionConfigMapper.selectByPrimaryKey(...).
        final SystemCommissionConfig systemCommissionConfig = new SystemCommissionConfig();
        systemCommissionConfig.setId(0);
        systemCommissionConfig.setType(0);
        systemCommissionConfig.setProductId(0);
        systemCommissionConfig.setStartTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        systemCommissionConfig.setEndTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        systemCommissionConfig.setRelatedCommissionFactor("relatedCommissionFactor");
        systemCommissionConfig.setCreateBy("createBy");
        systemCommissionConfig.setUpdateBy("createBy");
        systemCommissionConfig.setReleaseFlag(0);
        mockSystemCommissionConfigMapper.selectByPrimaryKey(0);

        mockSystemCommissionConfigMapper.selectRepeatTimeRecord(0, 0,
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0);
        mockProductVersionMapper.getMaxVersion(0);

        // Configure SmPlanHistoryMapper.selectByProductAndVersion(...).
        final SmPlanHistory smPlanHistory = new SmPlanHistory();
        smPlanHistory.setId(0);
        smPlanHistory.setPlanId(0);
        smPlanHistory.setVersion(0);
        smPlanHistory.setProductId(0);
        smPlanHistory.setPlanName("planName");
        final List<SmPlanHistory> smPlanHistories = Arrays.asList(smPlanHistory);
        mockSmPlanHistoryMapper.selectByProductAndVersion(0, 0);

        // Run the test
        commissionConfigManagerServiceUnderTest.editConfig(dto);

        // Verify the results
        // Confirm SystemCommissionConfigMapper.updateByPrimaryKeySelective(...).
        final SystemCommissionConfig t = new SystemCommissionConfig();
        t.setId(0);
        t.setType(0);
        t.setProductId(0);
        t.setStartTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        t.setEndTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        t.setRelatedCommissionFactor("relatedCommissionFactor");
        t.setCreateBy("createBy");
        t.setUpdateBy("createBy");
        t.setReleaseFlag(0);
        mockSystemCommissionConfigMapper.updateByPrimaryKeySelective(t);
        mockSystemCommissionConfigDetailMapper.deleteByConfigId(0);

        // Confirm SystemCommissionConfigDetailMapper.insertList(...).
        final SystemCommissionConfigDetail systemCommissionConfigDetail = new SystemCommissionConfigDetail();
        systemCommissionConfigDetail.setId(0);
        systemCommissionConfigDetail.setConfigId(0);
        systemCommissionConfigDetail.setPayWay("payWay");
        systemCommissionConfigDetail.setCoveredYears("coveredYears");
        systemCommissionConfigDetail.setValidPeriod("validPeriod");
        systemCommissionConfigDetail.setCreateBy("createBy");
        systemCommissionConfigDetail.setUpdateBy("createBy");
        final List<SystemCommissionConfigDetail> list = Arrays.asList(systemCommissionConfigDetail);
        mockSystemCommissionConfigDetailMapper.insertList(list);
    }

    @Test
    public void testEditConfig_SmPlanHistoryMapperReturnsNoItems() throws Exception {
        // Setup
        final CommissionConfigDTO dto = new CommissionConfigDTO();
        dto.setId(0);
        dto.setType(0);
        dto.setProductId(0);
        dto.setStartTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        dto.setEndTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        dto.setCommissionFactorLst(Arrays.asList("value"));
        final CommissionConfigItemDTO itemDTO = new CommissionConfigItemDTO();
        itemDTO.setPeriodNum(0);
        itemDTO.setPlanId(0);
        itemDTO.setRiskId(0);
        itemDTO.setPayWayList(Arrays.asList(0));
        itemDTO.setCoveredYearsList(Arrays.asList("value"));
        itemDTO.setValidPeriodList(Arrays.asList("value"));
        itemDTO.setCommissionRate(new BigDecimal("0.00"));
        dto.setFirstPeriodList(Arrays.asList(itemDTO));
        final CommissionConfigItemDTO itemDTO1 = new CommissionConfigItemDTO();
        itemDTO1.setPeriodNum(0);
        itemDTO1.setPlanId(0);
        itemDTO1.setRiskId(0);
        itemDTO1.setPayWayList(Arrays.asList(0));
        itemDTO1.setCoveredYearsList(Arrays.asList("value"));
        itemDTO1.setValidPeriodList(Arrays.asList("value"));
        itemDTO1.setCommissionRate(new BigDecimal("0.00"));
        dto.setOtherPeriodList(Arrays.asList(itemDTO1));
        dto.setOperator("createBy");
        dto.setReleaseFlag(0);

        // Configure SystemCommissionConfigMapper.selectByPrimaryKey(...).
        final SystemCommissionConfig systemCommissionConfig = new SystemCommissionConfig();
        systemCommissionConfig.setId(0);
        systemCommissionConfig.setType(0);
        systemCommissionConfig.setProductId(0);
        systemCommissionConfig.setStartTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        systemCommissionConfig.setEndTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        systemCommissionConfig.setRelatedCommissionFactor("relatedCommissionFactor");
        systemCommissionConfig.setCreateBy("createBy");
        systemCommissionConfig.setUpdateBy("createBy");
        systemCommissionConfig.setReleaseFlag(0);
        mockSystemCommissionConfigMapper.selectByPrimaryKey(0);

        mockSystemCommissionConfigMapper.selectRepeatTimeRecord(0, 0,
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0);
        mockProductVersionMapper.getMaxVersion(0);
        mockSmPlanHistoryMapper.selectByProductAndVersion(0, 0);

        // Run the test
        commissionConfigManagerServiceUnderTest.editConfig(dto);
    }
}
