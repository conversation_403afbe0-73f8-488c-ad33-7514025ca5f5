package com.cfpamf.ms.insur.admin.service.order;

import com.cfpamf.ms.insur.admin.config.ProductProperties;
import com.cfpamf.ms.insur.admin.config.ProductSpecialRuleProperties;
import com.cfpamf.ms.insur.admin.dao.safes.*;
import com.cfpamf.ms.insur.admin.dao.safes.order.*;
import com.cfpamf.ms.insur.admin.enums.EnumChannel;
import com.cfpamf.ms.insur.admin.external.ChannelOrderService;
import com.cfpamf.ms.insur.admin.external.OrderQueryResponse;
import com.cfpamf.ms.insur.admin.external.gsc.GscOrderServiceAdapterImpl;
import com.cfpamf.ms.insur.admin.external.gsc.model.resp.GscPayResp;
import com.cfpamf.ms.insur.admin.renewal.service.OrderRenewalRecordService;
import com.cfpamf.ms.insur.admin.service.*;
import com.cfpamf.ms.insur.base.event.EventBusEngine;
import com.cfpamf.ms.insur.base.infeface.CustomerCenterService;
import com.cfpamf.ms.insur.base.service.BusinessTokenService;
import com.cfpamf.ms.insur.base.util.HttpRequestUtil;
import com.cfpamf.ms.insur.common.service.monitor.BusinessMonitorService;
import com.cfpamf.ms.insur.weixin.dao.safes.EndorMapper;
import com.cfpamf.ms.pay.facade.config.PayFacadeConfigProperties;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.slf4j.Logger;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;

import java.time.format.DateTimeFormatter;
import java.util.*;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.mockito.Mockito.when;

@RunWith(PowerMockRunner.class)
@PrepareForTest({HttpRequestUtil.class})
public class GscOrderServiceTest extends BaseTest {

    @Mock private GscOrderServiceAdapterImpl mockAdapter;
    private int YEAR_NODE;
    private DateTimeFormatter FMT_PARSE;
    private DateTimeFormatter FMT_SUB;
    @Mock private BusinessMonitorService monitorService;
    @Mock private SmProductService productService;
    @Mock private BusinessTokenService tokenService;
    @Mock private CustomerCenterService ccService;
    @Mock private SmCommissionMapper cmsMapper;
    @Mock private SmOrderMapper orderMapper;
    @Mock private SmOrderItemMapper orderItemMapper;
    @Mock private SmProductFormBuyLimitMapper formBuyLimitMapper;
    @Mock private AuthUserMapper userMapper;
    @Mock private OrderRenewalRecordService orderRenewalRecordService;
    @Mock private OrderCoreService orderCoreService;
    @Mock private SmChannelCallbackMapper channelCallbackMapper;
    @Mock private EventBusEngine busEngine;
    @Mock private SmOrderCarMapper carMapper;
    @Mock private SmOrderHouseMapper houseMapper;
    @Mock private SmOrderGroupNotifyService smOrderGroupNotifyService;
    @Mock private SmCommonSettingService commonSettingService;
    @Mock private SmOrderItemMapper smOrderItemMapper;
    @Mock private SmOrderRenewBindService smOrderRenewBindService;
    @Mock private SmXjxhService smXjxhService;
    @Mock private SmOrderPolicyMapper smOrderPolicyMapper;
    @Mock private ChOrderPersonalNotifyService chOrderPersonalNotifyService;
    @Mock private SmOrderRiskDutyMapper riskDutyMapper;
    @Mock private SmProductMapper productMapper;
    @Mock private SmOrderPolicyService smOrderPolicyService;
    @Mock private RenewalManagerService renewalManagerService;
    @Mock private SmOrderInsuredMapper insuredMapper;
    private long tokenLockTime;
    private int phoneLimit;
    @Mock private ProductProperties properties;
    @Mock private ProductSpecialRuleProperties productSpecialRuleProperties;
    @Mock private EndorMapper paymentMapper;
    @Mock private PayFacadeConfigProperties payFacadeConfigProperties;
    @Mock private TempOrderNewProductMapper tempOrderNewProductMapper;
    @Mock private Logger log;

    @InjectMocks private GscOrderService gscOrderServiceUnderTest;

    @Test
    public void testProcessPolicySuccess() {
        // Setup
        final OrderQueryResponse resp = new OrderQueryResponse();
        resp.setOrderQueryType("code");
        resp.setNoticeCode("0");
        resp.setEvent(false);
        resp.setOrderId("orderId");
        resp.setOrderstatus("0");
        resp.setOrderType(0);
        resp.setProductId(0);
        resp.setChannel("channel");
        resp.setChannelName("channelName");
        resp.setSubChannel("subChannel");
        resp.setSubChannelName("subChannelName");
        resp.setProductAttrCode("productAttrCode");
        resp.setProductAttrCodeName("productAttrCodeName");
        resp.setProductCreateType("productCreateType");
        resp.setPlanId(0);
        resp.setProductName("productName");
        resp.setPlanName("planName");
        resp.setValidPeriod("validPeriod");
        final OrderQueryResponse.OrderInfo orderInfo = new OrderQueryResponse.OrderInfo();
        orderInfo.setOrderState("0");
        orderInfo.setOrderStateName("orderStateName");
        orderInfo.setOrderId("fhOrderId");
        orderInfo.setOrderNum("orderNum");
        orderInfo.setTotalAmount("totalAmount");
        orderInfo.setSubmitTime("submitTime");
        orderInfo.setStartTime("startTime");
        orderInfo.setEndTime("endTime");
        orderInfo.setCompanyFullName("companyFullName");
        orderInfo.setInsureComName("companyFullName");
        orderInfo.setRecommendId("recommendId");
        orderInfo.setProductId("productId");
        orderInfo.setUnderWritingAge("underWritingAge");
        orderInfo.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderInfo.setPaymentTime("paymentTime");
        orderInfo.setPayStatus("0");
        orderInfo.setPayStatusName("payStatusName");
        orderInfo.setPayWay("payWay");
        orderInfo.setRecommendChannelName("recommendChannelName");
        orderInfo.setRecommendChannel("recommendChannel");
        orderInfo.setDrainageShareCode("drainageShareCode");
        orderInfo.setDrainageShareName("drainageShareName");
        orderInfo.setDrainageShareAccount("drainageShareAccount");
        orderInfo.setDrainagePlatform("drainagePlatform");
        orderInfo.setCustomerAdminId("customerAdminId");
        resp.setOrderInfo(orderInfo);
        final OrderQueryResponse.AppntInfo appntInfo = new OrderQueryResponse.AppntInfo();
        appntInfo.setAppntName("applicantPersonName");
        appntInfo.setAppntIdType("aplicantIdType");
        appntInfo.setAppntIdTypeCode("aplicantIdType");
        appntInfo.setAppntIdNo("aplicantIdNumber");
        appntInfo.setAppntBirthday("applicantBirthday");
        appntInfo.setAppntSexName("appntSexName");
        appntInfo.setAppntSexCode("applicantPersonGender");
        appntInfo.setAppntMobile("applicantCellPhone");
        appntInfo.setAppntEmail("applicantEmail");
        appntInfo.setIdPeriodStart("applicantIdPeriodStart");
        appntInfo.setIdPeriodEnd("applicantIdPeriodEnd");
        appntInfo.setArea("applicantArea");
        resp.setAppntInfo(appntInfo);
        final OrderQueryResponse.PolicyInfo policyInfo = new OrderQueryResponse.PolicyInfo();
        policyInfo.setInsuredSn("insuredSn");
        policyInfo.setAppStatus("-2");
        policyInfo.setPolicyNo("insIdNumber");
        policyInfo.setDownloadURL("downloadURL");
        resp.setPolicyInfo(policyInfo);
        final OrderQueryResponse.InsuredInfo insuredInfo = new OrderQueryResponse.InsuredInfo();
        insuredInfo.setInsuredSn("insuredSn");
        insuredInfo.setInsuredIdNo("idNumber");
        resp.setInsuredInfos(Arrays.asList(insuredInfo));
        final OrderQueryResponse.PolicyInfo policyInfo1 = new OrderQueryResponse.PolicyInfo();
        policyInfo1.setInsuredSn("insuredSn");
        policyInfo1.setAppStatus("-2");
        policyInfo1.setPolicyNo("insIdNumber");
        policyInfo1.setDownloadURL("downloadURL");
        resp.setPolicyInfos(Arrays.asList(policyInfo1));

        final Map<String, String> expectedResult = new HashMap<>();

        // Run the test
        final Map<String, String> result = gscOrderServiceUnderTest.processPolicySuccess("orderId", "orderChannel", resp);

        // Verify the results
        assertEquals(expectedResult, result);
    }


    @Test
    public void testHandAsyncPayCallback() throws Exception {
        // Setup
        final MockHttpServletResponse response = new MockHttpServletResponse();
        final MockHttpServletRequest request = new MockHttpServletRequest();

        // Configure GscOrderServiceAdapter.parseNotify(...).
        final GscPayResp gscPayResp = new GscPayResp();
        gscPayResp.setTotalnum("totalnum");
        final GscPayResp.RespModel respModel = new GscPayResp.RespModel();
        respModel.setReqSeq("reqSeq");
        respModel.setTransId("transId");
        respModel.setPid("pid");
        gscPayResp.setReqList(Arrays.asList(respModel));
        when(mockAdapter.parseNotify("decode")).thenReturn(gscPayResp);

        // Run the test
        try{
            gscOrderServiceUnderTest.handAsyncPayCallback("orderId", "object", response, request);
        } catch(Exception e) {
        }

        // Verify the results
    }

    @Test
    public void testHandSyncPayCallback() throws Exception {
        // Setup
        final MockHttpServletResponse response = new MockHttpServletResponse();
        final MockHttpServletRequest request = new MockHttpServletRequest();
        when(mockAdapter.getPayRedirectUrl("orderId", "object")).thenReturn("result");

        // Run the test
        gscOrderServiceUnderTest.handSyncPayCallback("orderId", "object", response, request);

        // Verify the results
    }

    @Test
    public void testChannel() {
        assertEquals(EnumChannel.GSC.getCode(), gscOrderServiceUnderTest.channel());
    }

    @Test
    public void testOrderService() {
        // Setup
        // Run the test
        final ChannelOrderService result = gscOrderServiceUnderTest.orderService();

        // Verify the results
    }

    @Test
    public void testSupport() {
        assertFalse(gscOrderServiceUnderTest.support("channel"));
    }
}
