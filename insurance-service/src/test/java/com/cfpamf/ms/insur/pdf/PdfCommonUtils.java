package com.cfpamf.ms.insur.pdf;

import com.itextpdf.text.Document;
import com.itextpdf.text.PageSize;
import com.itextpdf.text.html.simpleparser.HTMLWorker;
import com.itextpdf.text.pdf.PdfWriter;
import com.itextpdf.tool.xml.XMLWorker;
import com.itextpdf.tool.xml.XMLWorkerHelper;
import org.junit.Test;
import org.springframework.core.io.ClassPathResource;

import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.io.StringReader;
import java.nio.charset.StandardCharsets;

public class PdfCommonUtils {

    private String resourcePath = "tempFile" + File.separatorChar + "pay.ftl";
    @Test
    public void convertHtml2PDF() throws Exception {
        Document document = new Document(PageSize.LETTER);
        PdfWriter.getInstance(document, new FileOutputStream("d://testpdf1.pdf"));
        document.open();
        HTMLWorker htmlWorker = new HTMLWorker(document);
        InputStream is = new ClassPathResource("ftl/tkpay.ftl").getInputStream();
        is.read();
        htmlWorker.parse(new StringReader("<h1>This is a test!</h1>"));
        document.close();
    }

    @Test
    public void convertHtml2PDFByXmlWork() throws Exception {
        Document document = new Document(PageSize.A4);
        PdfWriter pdfWriter = PdfWriter.getInstance(document, new FileOutputStream("d://testpdf1.pdf"));
        document.open();
        XMLWorkerHelper xmlWorkerHelper = XMLWorkerHelper.getInstance();
        InputStream is = new ClassPathResource(resourcePath).getInputStream();
        xmlWorkerHelper.parseXHtml(pdfWriter, document, is, StandardCharsets.UTF_8);
        document.close();
    }

}
