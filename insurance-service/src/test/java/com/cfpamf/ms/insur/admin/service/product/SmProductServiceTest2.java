package com.cfpamf.ms.insur.admin.service.product;

import com.cfpamf.ms.insur.admin.constant.SmConstants;
import com.cfpamf.ms.insur.admin.convertedpremium.service.ConvertedPremiumConfigService;
import com.cfpamf.ms.insur.admin.dao.safes.*;
import com.cfpamf.ms.insur.admin.dao.safes.product.SmProductAttrHistoryMapper;
import com.cfpamf.ms.insur.admin.dao.safes.product.SmProductAttrMapper;
import com.cfpamf.ms.insur.admin.dao.safes.product.SmProductConfirmMapper;
import com.cfpamf.ms.insur.admin.dao.safes.sys.SysDutyConfigMapper;
import com.cfpamf.ms.insur.admin.enums.EnumChannel;
import com.cfpamf.ms.insur.admin.enums.EnumProductApiType;
import com.cfpamf.ms.insur.admin.enums.product.EnumProductCreateType;
import com.cfpamf.ms.insur.admin.pojo.dto.SmProductCoverageDTO;
import com.cfpamf.ms.insur.admin.pojo.dto.SmProductNotificationDTO;
import com.cfpamf.ms.insur.admin.pojo.dto.product.QuestionDTO;
import com.cfpamf.ms.insur.admin.pojo.dto.product.SysDutyConfigDTO;
import com.cfpamf.ms.insur.admin.pojo.po.product.risk.SysRisk;
import com.cfpamf.ms.insur.admin.pojo.vo.SmProductCoverageVO;
import com.cfpamf.ms.insur.admin.pojo.vo.SmProductDetailVO;
import com.cfpamf.ms.insur.admin.pojo.vo.product.*;
import com.cfpamf.ms.insur.admin.service.BaseTest;
import com.cfpamf.ms.insur.admin.service.DictionaryService;
import com.cfpamf.ms.insur.admin.service.SmProductHistoryService;
import com.cfpamf.ms.insur.admin.service.SmProductService;
import com.cfpamf.ms.insur.admin.service.aicheck.AkProductQuestionnaireService;
import com.cfpamf.ms.insur.base.controller.ApplicationTest;
import com.cfpamf.ms.insur.base.util.HttpRequestUtil;
import com.cfpamf.ms.insur.base.util.RedisUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.jsonzou.jmockdata.JMockData;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import tk.mybatis.mapper.entity.Config;
import tk.mybatis.mapper.mapperhelper.EntityHelper;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@RunWith(PowerMockRunner.class)
@PrepareForTest({HttpRequestUtil.class})
public class SmProductServiceTest2 extends BaseTest {

    @InjectMocks
    private SmProductService productService;

    @InjectMocks
    private SmProductHistoryService historyService;

    @Mock
    private AkProductQuestionnaireService akProductQuestionnaireService;

    @Mock
    private SmProductMapper mapper;

    @Mock
    private SmProductAttrMapper productAttrMapper;

    @Mock
    private SmProductAttrHistoryMapper productAttrHistoryMapper;

    @Mock
    private SmProductVersionMapper productVersionMapper;

    @Mock
    private SmProductHistoryMapper historyMapper;

    @Mock
    private SmPlanSaleOrgMapper planSaleOrgMapper;

    @Mock
    private DictionaryService dictionaryService;

    @Mock
    private SmOccupationMapper occupationMapper;

    @Mock
    private SmCommissionMapper commissionMapper;

    @Mock
    private SmProductNotifyMapper notifyMapper;

    @Mock
    private RedisUtil<String, String> redisUtil;

    @Mock
    private ObjectMapper jsonMapper;

    @Mock
    private SmProductConfirmMapper confirmMapper;

    @Mock
    private SysDutyConfigMapper dutyConfigMapper;


    @Before
    public void setUp() throws Exception {
        super.setUp();
        EntityHelper.initEntityNameMap(SysRisk.class, new Config());
        SmProductDetailVO vo = JMockData.mock(SmProductDetailVO.class);
        vo.setProductAttrCode(SmConstants.PRODUCT_ATTR_GROUP);
        Mockito.when(historyMapper.getProductById(Mockito.anyInt(),Mockito.anyInt()))
                .thenReturn(vo);
    }

    @Test
    public void saveProductBaseInfo(){
        BasicProductVo vo =JMockData.mock(BasicProductVo.class);
        int r = productService.saveProductBaseInfo(vo);
        Assert.assertTrue("产品保存失败",r>0);
    }

    @Test
    public void saveProductHealthNotification() throws Exception {
        SmProductNotificationDTO dto = JMockData.mock(SmProductNotificationDTO.class);
        productService.saveProductHealthNotification(dto);
        Assert.assertTrue("健康告知信息保存失败",true);
    }

    @Test
    public void getProductCoverageList()  {
        Integer productId = 288;
        Integer version = null;
        List<SmProductCoverageVO> data = historyService.getProductCoverageList(productId,version);
        Assert.assertTrue("责任列表查询失败",data!=null);
    }

    @Test
    public void saveProductCoverage()  {
        Integer productId = 288;
        productService.saveProductCoverage(productId,mockCoverageList(productId));
        Assert.assertTrue("责任列表查询失败",true);
    }

    @Test
    public void getPlanDutyTable()  {
        Integer productId = 288;
        List<DutyTableVO> data = historyService.getPlanDutyTable(productId,null);
        Assert.assertTrue("责任列表查询失败",data!=null);
    }

    @Test
    public void getCoverageTable()  {
        Integer productId = 288;
        List<CoverageTableVO> data = historyService.getCoverageTable(productId,null);
        Assert.assertTrue("获取产品计划的费率表失败",data!=null);
    }

    @Test
    public void savePremiumFile()  {
        Integer productId = 288;
        boolean rs= productService.savePremiumFile(productId,mockPremiumFile(productId));
        Assert.assertTrue("获取产品计划的费率表失败",rs);
    }

    @Test
    public void listDuty()  {
        Integer productId = 288;
        List<SysDutyConfigDTO> data= historyService.listDuty(productId,1,7);
        Assert.assertNotNull("获取产品计划的费率表失败",data);
    }

    @Test
    public void getPremiumFactor()  {
        Integer productId = 288;
        PremiumFlowFactorVo data= historyService.getPremiumFactor(productId,7);
        Assert.assertNotNull("费率因子获取失败",data);
    }


    private List<PremiumFile> mockPremiumFile(Integer productId){
        List<PremiumFile> pf = new ArrayList<>();
        PremiumFile p = new PremiumFile();
        p.setUrl("http://www.baidu.com");
        p.setName("A");
        pf.add(p);
        return pf;
    }
    private List<SmProductCoverageDTO> mockCoverageList(Integer productId){
        List<SmProductCoverageDTO> dtos = new ArrayList<>();
        SmProductCoverageDTO vo = new SmProductCoverageDTO();
        vo.setSpcId(null);
        vo.setRiskCode("R001");
        vo.setProductId(productId);
        vo.setOperator("10086");
        vo.setCvgCode("D001");
        vo.setCvgItemName("人身意外险");
        vo.setCvgRespType("100");
        vo.setMandatory(1);
        vo.setPlanId(109);
        vo.setPlanName("a套餐");
        dtos.add(vo);
        return dtos;
    }

    private SmProductNotificationDTO moceNotificationBean(){
        SmProductNotificationDTO dto = new SmProductNotificationDTO();
        dto.setProductId(288);
        dto.setAiCheck(true);
        dto.setAiCheckWay(2);

        List<QuestionDTO> questions = new ArrayList<>();
        QuestionDTO q = new QuestionDTO();
        q.setCode("Q1");
        q.setProductId(288);
        q.setQuestion("你今年过的好吗");
        questions.add(q);
        dto.setQuestions(questions);
        dto.setModifyBy("SuperMan");
        return dto;
    }
    private BasicProductVo mockProductBean(){
        BasicProductVo vo = new BasicProductVo();
        vo.setApiType(EnumProductApiType.API.getType());
        vo.setChannel(EnumChannel.TK.getCode());
        vo.setProductCategoryId("218");
        vo.setProductAttrCode(SmConstants.PRODUCT_ATTR_GROUP);
        vo.setCompanyId("213");
        vo.setProductName("泰康新版团险");
        vo.setHeadImageUrl(null);
        vo.setProductTags("不信拉倒");
        vo.setProductFeature("没啥特色，骗点零花钱花花。。。");
        vo.setThumbnailImageUrl("https://safesfiles-test.oss-cn-beijing.aliyuncs.com/image/1627980283217/7a774b46d263455b84a08244a79bcef3/zsh.jpg");
        vo.setIntroduceImageUrl(null);
        vo.setEffectWaitingDayMin(1);
        vo.setEffectWaitingDayMax(30);
        vo.setBuyLimit(1);
        vo.setMiniPremium(new BigDecimal(100));
        vo.setValidPeriod("10年");
        vo.setGlOcpnGroup("1,2,5");
        vo.setGlUwaFrom("10周岁");
        vo.setGlUwaTo("60周岁");
        vo.setGlProductIntroduce(null);
        vo.setProductType("accident");
        vo.setProductCode("TX");
        vo.setCreateType(EnumProductCreateType.DEFAULT_TYPE);
        return vo;
    }


}
