package com.cfpamf.ms.insur.admin.test;

import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.springframework.http.RequestEntity;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

import java.net.URI;
import java.net.URISyntaxException;
import java.util.HashMap;

/**
 * <AUTHOR>
 */
public class TestGetPayQrCode {

    private RestTemplate restTemplate = new RestTemplate();

    public static void main(String[] args) throws Exception {
        TestGetPayQrCode test = new TestGetPayQrCode();
        test.testCic();
    }

    public void testCic() throws URISyntaxException {
        String httpUrl = "https://epay.cic.cn/testppf/payment/goPay.do?payID=ZHNX201908140000009252&transNo=CIC190814QVVDPAU";
        RequestEntity requestEntity = RequestEntity.get(new URI(httpUrl)).build();
        ResponseEntity responseEntity = restTemplate.exchange(requestEntity, String.class);
        String body = responseEntity.getBody().toString();
        System.out.println(body);
        Document document = Jsoup.parse(body);

        HashMap postMap = new HashMap<>();
        postMap.put("formId", "WeChat");
        postMap.put("paymentType", "7");
        postMap.put("submitType", "WeChat");
        postMap.put("transNo", "CIC190814QVVDPAU");
        postMap.put("wechatFlag", "wap");
//        restTemplate.patchForObject()
    }

    public void testFH() {

    }
}
