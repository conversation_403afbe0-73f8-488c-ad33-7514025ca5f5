package com.cfpamf.ms.insur.admin.test;

import com.cfpamf.ms.insur.admin.external.whale.model.WhaleContract;
import com.cfpamf.ms.insur.admin.external.whale.model.WhaleResp;
import com.cfpamf.ms.insur.weixin.pojo.vo.claim.evaluation.ClaimEvaluationVo;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.github.jsonzou.jmockdata.JMockData;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;
import org.mockito.Mock;

import java.io.IOException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * <AUTHOR> 2022/3/29 19:01
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class TestWahle {

    public static void main(String[] args) throws IOException {
        String  str= "{\"code\": 20000,\"data\": {\"agentInfoList\": [{\"agentCode\": \"ag202110291055078aHpkq\",\"agentName\": \"贾臻\",\"businessCode\": \"XJ1000010200003\",\"commissionRate\": 100.000000,\"mainFlag\": 1,\"orgCode\": \"OR20220211102411pk0D9P\",\"orgName\": \"渠道业务部\",\"position\": \"AGENT_POSITION:1\"}],\"applicantInfo\": {\"applicantAddress\": \"\",\"applicantAge\": null,\"applicantAnnualIncome\": \"\",\"applicantBirthday\": \"1984-11-24 00:00:00\",\"applicantCareer\": \"\",\"applicantCityCode\": \"\",\"applicantCompany\": \"\",\"applicantEducation\": \"\",\"applicantEmail\": \"\",\"applicantGender\": 0,\"applicantIdCard\": \"371427198411244943\",\"applicantIdCardPermanent\": null,\"applicantIdCardValidityEnd\": null,\"applicantIdCardValidityStart\": null,\"applicantIdType\": \"POLICY_ID_TYPE:0\",\"applicantIndustryCategory\": \"\",\"applicantMarital\": \"\",\"applicantMedicare\": null,\"applicantMobile\": \"***********\",\"applicantName\": \"王红梅\",\"applicantNation\": \"\",\"applicantNationality\": \"\",\"applicantOccupationalCategory\": \"\",\"applicantPosition\": \"\",\"applicantPostcode\": \"\",\"applicantProvinceCode\": \"\",\"applicantRegionCode\": \"\",\"applicantTelephone\": \"\",\"applicantType\": 1,\"bankCode\": \"BANK_LIST:ABC\",\"bankName\": \"\",\"cardName\": \"\",\"cardNo\": \"6228481329056092473\",\"companyContactMobile\": \"\",\"companyContactName\": \"\",\"companyEmployeeNum\": \"\",\"companyNature\": \"\",\"companySocialSecurityNum\": \"\",\"legalPersonIdCard\": \"\",\"legalPersonIdCardPermanent\": null,\"legalPersonIdCardValidityEnd\": null,\"legalPersonIdCardValidityStart\": null,\"legalPersonIdType\": \"\",\"legalPersonName\": \"\",\"paymentPhase\": null},\"channelInfo\": {\"branchName\": \"\",\"channelBranchCode\": \"B20220121173903A4PE5i\",\"channelCode\": \"zhnx\",\"channelName\": \"\",\"policyReferrerBusinessCode\": \"\",\"policyReferrerCode\": \"\",\"policyReferrerName\": \"\",\"referrerCode\": \"R20210712154241fj6OHj\",\"referrerName\": \"刘志伟\",\"referrerType\": 0,\"referrerWno\": \"ZHNX31300\"},\"contractAttachList\": [{\"fileCode\": \"oss20220228101734l1FtoD\",\"fileName\": \"86110020220006969186-贾臻.pdf\",\"filePath\": \"https://oss-xjxhserver.xiaowhale.com/policy/policy-attach/20220228/618b1fc325234e56935e03c27b30b0e2/86110020220006969186-贾臻.pdf\",\"fileType\": \"file\"},{\"fileCode\": \"oss20220228162450oFElMC\",\"fileName\": \"86110020220006969186-贾臻-纸质.pdf\",\"filePath\": \"https://oss-xjxhserver.xiaowhale.com/policy/policy-attach/20220228/de6fd23cb57d45b4a1ee0649409b1c67/86110020220006969186-贾臻-纸质.pdf\",\"fileType\": \"file\"}],\"contractBaseInfo\": {\"adminPolicyStatus\": \"POLICY_STATUS:5\",\"applicantPolicyNo\": \"\",\"companyCode\": \"ZHLH000000\",\"companyName\": \"中华联合人寿保险股份有限公司\",\"insuranceSubjectMatter\": \"\",\"mainProductCode\": \"XZ20210428286\",\"mainProductName\": \"中华福（2021版）终身重大疾病保险\",\"policyNo\": \"86110020220006969186\",\"policyStatus\": \"POLICY_STATUS:6\",\"portfolioName\": \"中华福（2021版）终身重大疾病保险等\",\"salesType\": 1},\"contractCode\": \"ct20220228101748vBd2dt\",\"contractExtendInfo\": {\"applicantTime\": \"2022-02-23 00:00:00\",\"approvedRecordTime\": \"2022-03-02 16:50:55\",\"approvedTime\": \"2022-02-23 00:00:00\",\"cancelTime\": null,\"contractComment\": \"\",\"declineReason\": \"\",\"declineTime\": null,\"discontinueTime\": null,\"enforc eTime\": \"2022-02-24 00:00:00\",\"failureTime\": null,\"firstPaymentTime\": null,\"hesitatePeriod\": null,\"orderTime\": \"2022-02-28 00:00:00\",\"overHesitatePeriod\": null,\"postponeApprovedTime\": null,\"receiptRecordTime\": null,\"receiptSignTime\": \"2022-02-23 00:00:00\",\"replyEffectiveTime\": null,\"revisitFailReason\": \"\",\"revisitRecordTime\": null,\"revisitResult\": 1,\"revisitTime\": \"2022-02-24 00:00:00\",\"revisitType\": \"\",\"signDate\": null,\"surrenderAmount\": null,\"surrenderReason\": \"\",\"surrenderTime\": null,\"terminationReason\": \"\",\"terminationTime\": null,\"underwritingFinishTime\": null,\"underwritingType\": \"\"},\"groupInsuredInfoList\": [],\"insuredInfoList\": [{\"insuredAddress\": \"\",\"insuredBirthday\": \"2016-03-17 00:00:00\",\"insuredCareer\": \"\",\"insuredCityCode\": \"\",\"insuredCompany\": \"\",\"insuredEducation\": \"\",\"insuredEmail\": \"\",\"insuredGender\": 0,\"insuredIdCard\": \"371581201603171767\",\"insuredIdCardPermanent\": null,\"insuredIdCardValidityEnd\": null,\"insuredIdCardValidityStart\": null,\"insuredIdType\": \"POLICY_ID_TYPE:8\",\"insuredMarital\": \"\",\"insuredMedicare\": null,\"insuredMobile\": \"\",\"insuredName\": \"刘梦洁\",\"insuredNation\": \"\",\"insuredNationality\": \"\",\"insuredOccupationalCategory\": \"\",\"insuredPolicyAge\": null,\"insuredPosition\": \"\",\"insuredPostcode\": \"\",\"insuredProvinceCode\": \"\",\"insuredRegionCode\": \"\",\"insuredRelation\": \"POLICY_RELATION:CHILD\",\"productInfoList\": [{\"additionalRisksType\": 0,\"annDrawAge\": \"\",\"beneficiaryList\": [],\"beneficiaryType\": 1,\"copies\": 1,\"coverage\": 200000.000,\"deductible\": \"\",\"insuredPeriod\": null,\"insuredPeriodType\": \"INSURED_PERIOD_TYPE:4\",\"mainInsurance\": 1,\"paymentPeriod\": 20,\"paymentPeriodType\": \"PAYMENT_PERIOD_TYPE:1\",\"periodType\": \"PERIOD_TYPE:1\",\"premium\": 1888.000,\"prodTypeCode\": \"PRODUCT:PROTECTION_TYPE:ZJ\",\"productCode\": \"XZ20210428286\",\"productName\": \"中华福（2021版）终身重大疾病保险\"}]},{\"insuredAddress\": \"\",\"insuredBirthday\": \"1984-11-24 00:00:00\",\"insuredCareer\": \"\",\"insuredCityCode\": \"\",\"insuredCompany\": \"\",\"insuredEducation\": \"\",\"insuredEmail\": \"\",\"insuredGender\": 0,\"insuredIdCard\": \"371427198411244943\",\"insuredIdCardPermanent\": null,\"insuredIdCardValidityEnd\": null,\"insuredIdCardValidityStart\": null,\"insuredIdType\": \"POLICY_ID_TYPE:0\",\"insuredMarital\": \"\",\"insuredMedicare\": null,\"insuredMobile\": \"***********\",\"insuredName\": \"王红梅\",\"insuredNation\": \"\",\"insuredNationality\": \"\",\"insuredOccupationalCategory\": \"\",\"insuredPolicyAge\": null,\"insuredPosition\": \"\",\"insuredPostcode\": \"\",\"insuredProvinceCode\": \"\",\"insuredRegionCode\": \"\",\"insuredRelation\": \"POLICY_RELATION:ME\",\"productInfoList\": [{\"additionalRisksType\": 1,\"annDrawAge\": \"\",\"beneficiaryList\": [],\"beneficiaryType\": 1,\"copies\": 1,\"coverage\": 1888.000,\"deductible\": \"\",\"insuredPeriod\": null,\"insuredPeriodType\": \"INSURED_PERIOD_TYPE:4\",\"mainInsurance\": 0,\"paymentPeriod\": 19,\"paymentPeriodType\": \"PAYMENT_PERIOD_TYPE:1\",\"periodType\": \"PERIOD_TYPE:1\",\"premium\": 102.910,\"prodTypeCode\": \"PRODUCT:PROTECTION_TYPE:ZJ\",\"productCode\": \"ZLRSTBHM21102801\",\"productName\": \"中华附加豁免保险费重大疾病保险\"}]}],\"policyProductType\": \"PRODUCT:PRODUCTGROUP:1\",\"policyType\": 1,\"productInfoList\": [{\"additionalRisksType\": 0,\"annDrawAge\": \"\",\"copies\": 1,\"coverage\": 200000.000,\"deductible\": \"\",\"insuredPeriod\": null,\"insuredPeriodType\": \"INSURED_PERIOD_TYPE:4\",\"mainInsurance\": 1,\"paymentPeriod\": 20,\"paymentPeriodType\": \"PAYMENT_PERIOD_TYPE:1\",\"periodType\": \"PERIOD_TYPE:1\",\"premium\": 1888.000,\"prodTypeCode\": \"PRODUCT:PROTECTION_TYPE:ZJ\",\"productCode\": \"XZ20210428286\",\"productName\": \"中华福（2021版）终身重大疾病保险\"},{\"additionalRisksType\": 1,\"annDrawAge\": \"\",\"copies\": 1,\"coverage\": 1888.000,\"deductible\": \"\",\"insuredPeriod\": null,\"insuredPeriodType\": \"INSURED_PERIOD_TYPE:4\",\"mainInsurance\": 0,\"paymentPeriod\": 19,\"paymentPeriodType\": \"PAYMENT_PERIOD_TYPE:1\",\"periodType\": \"PERIOD_TYPE:1\",\"premium\": 102.910,\"prodTypeCode\": \"PRODUCT:PROTECTION_TYPE:ZJ\",\"productCode\": \"ZLRSTBHM21102801\",\"productName\": \"中华附加豁免保险费重大疾病保险\"}],\"vehicleInfo\": null},\"msg\": \"success\",\"success\": true,\"trace\": \"\"}";
        ObjectMapper  objectMapper = new ObjectMapper();
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        objectMapper.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
        JavaTimeModule timeModule = new JavaTimeModule();
        DateTimeFormatter AUTO_FMT_DATETIME = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        timeModule.addDeserializer(LocalDateTime.class, new LocalDateTimeDeserializer(AUTO_FMT_DATETIME));
        timeModule.addSerializer(LocalDateTime.class, new LocalDateTimeSerializer(AUTO_FMT_DATETIME));

        timeModule.addDeserializer(LocalDate.class, new LocalDateDeserializer(AUTO_FMT_DATETIME));
        timeModule.addSerializer(LocalDate.class, new LocalDateSerializer(AUTO_FMT_DATETIME));

        objectMapper.registerModule(timeModule);
        WhaleResp<WhaleContract> o = objectMapper.readValue(str, new TypeReference<WhaleResp<WhaleContract>>() {
        });
        System.err.println(o);

    }
}
