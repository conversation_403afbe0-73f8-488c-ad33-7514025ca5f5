package com.cfpamf.ms.insur.admin.test;

import com.alibaba.fastjson.util.ParameterizedTypeImpl;
import com.cfpamf.common.ms.result.ErrorContext;
import com.cfpamf.ms.bms.facade.vo.JwtUserInfo;
import com.cfpamf.ms.insur.admin.service.order.SmOrderRiskService;
import com.cfpamf.ms.insur.base.dao.MyMappler;
import com.cfpamf.ms.insur.weixin.pojo.vo.claim.evaluation.ClaimEvaluationVo;
import com.github.jsonzou.jmockdata.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.RandomUtils;
import org.junit.Test;
import org.mockito.MockSettings;
import org.mockito.Mockito;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.io.support.ResourcePatternResolver;
import org.springframework.core.type.ClassMetadata;
import org.springframework.core.type.classreading.MetadataReader;
import org.springframework.core.type.classreading.MetadataReaderFactory;
import org.springframework.core.type.classreading.SimpleMetadataReaderFactory;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.awt.*;
import java.awt.datatransfer.StringSelection;
import java.lang.reflect.*;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static java.lang.reflect.Modifier.isFinal;
import static java.lang.reflect.Modifier.isStatic;

/**
 * <AUTHOR> 2019/12/25 16:35
 */
@Slf4j
public class AutoTest {

    MockConfig mockConfig;

    private String prefix;

    public AutoTest() {
        this("");
    }

    public AutoTest(String prefix) {
        this.prefix = prefix;
        setUp();
    }

    public void setUp() {

        mockConfig = new MockConfig();
        //注册LocalDateTime
        mockConfig.globalConfig()
                .registerMocker(new Mocker<LocalDateTime>() {
                                    @Override
                                    public LocalDateTime mock(DataConfig mockConfig) {
                                        return LocalDateTime.now();
                                    }
                                },
                        LocalDateTime.class);

        mockConfig.globalConfig()
                .registerMocker(new Mocker<LocalDate>() {
                                    @Override
                                    public LocalDate mock(DataConfig mockConfig) {
                                        return LocalDate.now();
                                    }
                                },
                        LocalDate.class);
        //注册JwtUserInfo
        mockConfig.globalConfig()
                .registerMocker(new Mocker<JwtUserInfo>() {
                                    @Override
                                    public JwtUserInfo mock(DataConfig mockConfig) {
                                        return JwtUserInfo.builder().employeeId(1).userId(1).hrOrgId(1).build();
                                    }
                                },
                        JwtUserInfo.class);
        mockConfig.globalConfig()
                .registerMocker(new Mocker<ErrorContext>() {
                                    @Override
                                    public ErrorContext mock(DataConfig mockConfig) {
                                        return new ErrorContext();
                                    }
                                },
                        ErrorContext.class);


    }

    public void testCommon() throws Exception {

        setUp();
        String name = prefix;
        String s = name.replaceAll("\\.", "/");

        ResourcePatternResolver resourcePatternResolver = new PathMatchingResourcePatternResolver();
        //这里特别注意一下类路径必须这样写
        //获取指定包下的所有类
        String res = "classpath*:" + s + "/**";
        Resource[] resources = resourcePatternResolver.getResources(res);

        List<ClassMetadata> metadataes = new ArrayList<>();
        MetadataReaderFactory metadata = new SimpleMetadataReaderFactory();

        for (Resource resource : resources) {
            if (resource.getFilename().contains(".")) {
                MetadataReader metadataReader = metadata.getMetadataReader(resource);
                ClassMetadata classMetadata = metadataReader.getClassMetadata();

                if (!(classMetadata.isAbstract() || classMetadata.isInterface())
                        && !classMetadata.getClassName().contains("Test")
                        && !classMetadata.getClassName().contains("test")
                ) {
                    metadataes.add(classMetadata);
                }
            }
        }
        metadataes.forEach(this::testClass);

    }

    public void testClass(Class<?> clz) {

        log.info("开始测试：" + clz.getName());
        try {
            Class<?> testClass = clz;

            Object testObj = null;
            try {
                testObj = getTestObj(testClass);
            } catch (Exception e) {
                log.warn("build test Obj fail  ", e);
            }
            if (testObj == null) {
                log.warn("初始化测试对象失败{}", testClass.getName());
                return;
            }
            //"hashCode",
            List<String> igNames = Arrays.asList("hashCode", "getClass",
                    "equals", "clone", "toString"
                    , "notify", "notifyAll", "wait", "finalize");
            List<Method> collect = Stream.of(testClass.getMethods())
                    .filter(a -> !igNames.contains(a.getName())).collect(Collectors.toList());
            for (Method method : collect) {
                try {
                    log.info("开始测试 ：{}", method.getName());
                    Class<?>[] methodParameterTypes = method.getParameterTypes();
                    Type[] genericParameterTypes = method.getGenericParameterTypes();
                    Object[] meps = new Object[methodParameterTypes.length];

                    for (int i = 0; i < methodParameterTypes.length; i++) {
                        if (Objects.equals(methodParameterTypes[i].toString(),
                                genericParameterTypes[i].toString())) {
                            meps[i] = JMockData.mock(methodParameterTypes[i], mockConfig);
                        } else {
                            Type type = genericParameterTypes[i];
                            meps[i] = JMockData.mock(new TypeReference<Object>() {
                                @Override
                                public Type getType() {
                                    return type;
                                }

                            }, mockConfig);
                        }
                    }
                    method.invoke(testObj, meps);

                } catch (Exception e) {
                    log.error("自动测试出错:{}#{}", clz.getName(), method.getName(), e);
                } finally {
                    log.info("测试over ：{}", method.getName());
                }

            }

        } catch (Exception e) {
            log.error("自动测试出错:{}", clz.getName(), e);
        }

    }

    private void testClass(ClassMetadata metadata) {
        try {
            testClass(Class.forName(metadata.getClassName()));
        } catch (ClassNotFoundException e) {
            log.warn("class not found:", e);
        }

    }

    /**
     * 获取测试对象 相当于
     * <code>
     *
     * @return
     * @throws Exception
     * @InjectMocks private T service;
     * </code>
     */
    private <T> T getTestObj(Class<T> testClass) throws Exception {
        MockSettings mockSettings = Mockito.withSettings();
        mockSettings.defaultAnswer(invocation -> {
            Object mock1 = invocation.getMock();
            ParameterizedType type = null;

            if (mock1 instanceof MyMappler) {
                Class<?> aClass = mock1.getClass();
                String name = aClass.getName();
                String realClaz = name.substring(0, name.indexOf("$$EnhancerByMockitoWithCGLIB"));
                Class<?> aClass1 = Class.forName(realClaz);
                type = (ParameterizedType) aClass1.getGenericInterfaces()[0];
            }
            Method method = invocation.getMethod();
            Class<?> returnType = method.getReturnType();
            if (returnType != void.class && returnType != Void.class) {

                Type genericReturnType = method.getGenericReturnType();

                Map<String, Type> val = new HashMap<>(1);
                val.put("val", genericReturnType);
                Object mock = null;
                if (Objects.equals(returnType.toString(), genericReturnType.toString())) {
                    mock = JMockData.mock(returnType, mockConfig);
                } else {
                    String s = genericReturnType.toString();
                    if (s.contains("<T>")) {
                        if (genericReturnType instanceof ParameterizedType) {
                            ParameterizedType tmp = (ParameterizedType) genericReturnType;
                            genericReturnType = new ParameterizedTypeImpl(
                                    type.getActualTypeArguments(), tmp.getOwnerType(), tmp.getRawType());
                            val.put("val", genericReturnType);
                        }

                    } else if (Objects.equals(s, "T")) {
                        val.put("val", type);
                    }
                    mock = JMockData.mock(new TypeReference<Object>() {
                        @Override
                        public Type getType() {
                            return val.get("val");
                        }

                    }, mockConfig);
                }
                return mock;
            }
            return null;
        });


        Constructor<?>[] constructors = testClass.getConstructors();
        if (constructors.length > 0) {
            Constructor<?> constructor = constructors[0];
            Class<?>[] parameterTypes = constructor.getParameterTypes();

            Object[] pars = Stream.of(parameterTypes).map(type -> Mockito.mock(type, mockSettings))
                    .toArray();
            T testIns = (T) constructor.newInstance(pars);

            for (Field declaredField : testClass.getDeclaredFields()) {

                int modifiers = declaredField.getModifiers();
                if (!(isFinal(modifiers) || isStatic(modifiers))) {
                    if (!declaredField.isAccessible()) {
                        declaredField.setAccessible(true);
                    }
                    if (declaredField.get(testIns) == null) {
                        if (declaredField.getType() == String.class) {
                            declaredField.set(testIns, "tessString");
                        } else {
                            declaredField.set(testIns, Mockito.mock(declaredField.getType(), mockSettings));
                        }
                    }
                }
            }
            return testIns;
        }

        log.warn("{} not public constructor cannot auto test", testClass.getName());
        return null;
    }

    private String getName(String name, int i, Set<String> s) {

        String res = name;
        if (i != 0) {
            res = res + i;
        }
        if (s.contains(res)) {
            return getName(name, ++i, s);
        }
        s.add(res);
        return res;
    }

//    @Test
    public void testGenTestClass(Class<?> css) {
//        Class<?> css = ActivitiCommonService.class;

        String simpleName = css.getSimpleName();

        String testClass = simpleName.substring(0, 1).toLowerCase() + simpleName.substring(1);

        StringBuilder res = new StringBuilder();

        res.append("" +
                "@RunWith(PowerMockRunner.class)\n" +
                "@PrepareForTest({HttpRequestUtil.class})\n" +
                "public class " + css.getSimpleName() + "Test extends BaseTest {");
        res.append("   @InjectMocks\n    ").append(css.getSimpleName()).append(" ").append(testClass)
                .append(";\n");
        String template = "    @Test\n" +
                "    public void ${methodName}() ${exp} {\n" +
                "  try {" +
                "        ${testClass}.${meRealName}(${pas});\n" +
                "   } catch (Exception e) {\n" +
                "\n" +
                "        }" +
                "    }";
        Method[] declaredMethods = css.getDeclaredMethods();

        final Set<String> sss = new HashSet<>();
        String join = Stream.of(declaredMethods)
                .filter(f -> Modifier.isPublic(f.getModifiers()))
                .map(me -> {

                    String meName = getName(me.getName(), 0, sss);
                    StringJoiner sj = new StringJoiner(",");

                    Class<?>[] parameterTypes = me.getParameterTypes();

                    Class<?>[] exceptionTypes = me.getExceptionTypes();
                    String exp = "";
                    if (ArrayUtils.isNotEmpty(exceptionTypes)) {
                        String collect = Stream.of(exceptionTypes).map(Class::getSimpleName)
                                .collect(Collectors.joining(","));
                        exp = " throws  " + collect;
                    }
                    for (Class<?> parameterType : parameterTypes) {
                        String name = parameterType.getName();
                        if (parameterType == String.class) {
                            sj.add("\"").add(RandomStringUtils.randomAlphabetic(5)).add("\"");
                        } else if (parameterType == Integer.class) {
                            sj.add(RandomUtils.nextInt(1, 23434) + "");
                        } else if (parameterType == HttpServletRequest.class) {
                            sj.add("new MockHttpServletRequest()");
                        } else if (parameterType == HttpServletResponse.class) {
                            sj.add("new MockHttpServletResponse()");
                        } else {
                            sj.add("JMockData.mock(" + name + ".class)");
                        }
                    }
                    ;
                    Map<String, Object> map = new HashMap<>();
                    map.put("pas", sj.toString());
                    map.put("methodName", meName);
                    map.put("meRealName", me.getName());
                    map.put("exp", exp);
                    map.put("testClass", testClass);
                    return strFormatUsingDict(template, map);
                }).collect(Collectors.joining("\n\n"));

        Field[] declaredFields = css.getDeclaredFields();

        String fres = Arrays.stream(declaredFields)
                .filter(fe -> !Modifier.isStatic(fe.getModifiers())).map(fe -> {
                    StringBuilder sb = new StringBuilder();
//            if (fe.getAnnotation(Autowired.class) != null
//                    || fe.getAnnotation(javax.annotation.Resource.class) != null) {
                    sb.append("    @org.mockito.Mock\n");
//            }
                    sb.append("    ").append(fe.getType().getName()).append(" ")
                            .append(fe.getName()).append(";");
                    return sb.toString();
                }).collect(Collectors.joining("\n"));

        res.append(fres).append(join);
        res.append("\n}");
        StringSelection stsel = new StringSelection(res.toString());
        Toolkit.getDefaultToolkit().getSystemClipboard().setContents(stsel, stsel);
        System.err.println(res);
    }

    /**
     * 模板替换
     *
     * @param template
     * @param dict
     * @return
     */
    public static String strFormatUsingDict(String template, Map<String, Object> dict) {
        String patternString = "\\$\\{(" + org.apache.commons.lang3.StringUtils.join(dict.keySet(), "|") + ")\\}";

        Pattern pattern = Pattern.compile(patternString);
        Matcher matcher = pattern.matcher(template);

        StringBuffer sb = new StringBuffer();
        while (matcher.find()) {
            matcher.appendReplacement(sb, dict.getOrDefault(matcher.group(1), "").toString());
        }
        matcher.appendTail(sb);
        return sb.toString();
    }


}
