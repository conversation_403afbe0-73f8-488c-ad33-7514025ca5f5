package com.cfpamf.ms.insur.admin.service.commission;

import com.cfpamf.ms.insur.admin.dao.safes.SmOrderMapper;
import com.cfpamf.ms.insur.admin.dao.safes.commission.*;
import com.cfpamf.ms.insur.admin.dao.safes.order.TempOrderNewProductMapper;
import com.cfpamf.ms.insur.admin.dao.safes.reconciliation.SmReconciliationPolicyMapper;
import com.cfpamf.ms.insur.admin.pojo.dto.commission.*;
import com.cfpamf.ms.insur.admin.pojo.dto.order.OrderCommissionInsuredInfoDTO;
import com.cfpamf.ms.insur.admin.pojo.dto.order.OrderRiskPremiumDTO;
import com.cfpamf.ms.insur.admin.pojo.dto.renewal.RenewalTermDTO;
import com.cfpamf.ms.insur.admin.pojo.po.SmTaskPremiumConfig;
import com.cfpamf.ms.insur.admin.pojo.po.commission.SmCommissionCalcProcessPO;
import com.cfpamf.ms.insur.admin.pojo.po.commission.SmCommissionDetailItemPO;
import com.cfpamf.ms.insur.admin.pojo.po.commission.SmCommissionDetailPO;
import com.cfpamf.ms.insur.admin.pojo.po.commission.SmCommissionPO;
import com.cfpamf.ms.insur.admin.pojo.po.order.SmOrderPolicy;
import com.cfpamf.ms.insur.admin.pojo.po.order.TempOrderNewProductPO;
import com.cfpamf.ms.insur.admin.pojo.query.SmOrderQuery;
import com.cfpamf.ms.insur.admin.pojo.vo.SmCompanySettingVO;
import com.cfpamf.ms.insur.admin.pojo.vo.SmOrderSummaryVO;
import com.cfpamf.ms.insur.admin.pojo.vo.commission.SmLongInsuranceCommissionDetailVO;
import com.cfpamf.ms.insur.admin.pojo.vo.commission.SmLongInsuranceCommissionVO;
import com.cfpamf.ms.insur.admin.service.SmCmpySettingService;
import com.cfpamf.ms.insur.admin.service.SmProductService;
import com.cfpamf.ms.insur.admin.service.SmTaskPremiumConfigService;
import com.cfpamf.ms.insur.admin.service.order.SmOrderPolicyService;
import com.cfpamf.ms.insur.admin.service.order.SmOrderRiskService;
import com.github.pagehelper.PageInfo;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.mock.web.MockHttpServletResponse;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class CommissionManagerServiceTest {

    @Mock
    private SmCmpySettingService mockSmCmpySettingService;
    @Mock
    private SmOrderCommissionMapper mockOrderCommissionMapper;
    @Mock
    private SmOrderCommissionDetailMapper mockOrderCommissionDetailMapper;
    @Mock
    private SmOrderCommissionItemMapper mockSmOrderCommissionItemMapper;
    @Mock
    private CommissionCalcService mockCommissionCalcService;
    @Mock
    private SmOrderMapper mockSmOrderMapper;
    @Mock
    private SmProductService mockProductService;
    @Mock
    private TempOrderNewProductMapper mockTempOrderNewProductMapper;
    @Mock
    private SmOrderRiskService mockSmOrderRiskService;
    @Mock
    private SmOrderPolicyService mockSmOrderPolicyService;
    @Mock
    private CommissionCalcProcessService mockCalcProcessService;
    @Mock
    private SmOrderCommissionCalcProcessMapper mockCommissionCalcProcessMapper;
    @Mock
    private AddCommissionQueryService mockAddCommissionQueryService;
    @Mock
    private CommissionExternalService mockExternalService;
    @Mock
    private CommissionMessageService mockCommissionMessageService;
    @Mock
    private SmTaskPremiumConfigService mockPremiumConfigService;
    @Mock
    private SmAddCommissionDetailMapper mockAddCommissionDetailMapper;
    @Mock
    private SmReconciliationPolicyMapper mockSmReconciliationPolicyMapper;

    @Mock
    private CommissionManagerService commissionManagerServiceUnderTest;

    @Test
    public void testCalcCarOrderImportCommission() {
        // Setup
        final CalcCommissionItemResultDTO calcCommissionItemResultDTO = new CalcCommissionItemResultDTO();
        calcCommissionItemResultDTO.setOrderId("orderId");
        calcCommissionItemResultDTO.setPolicyNo("policyNo");
        calcCommissionItemResultDTO.setInsuredIdNumber("insuredIdNumber");
        calcCommissionItemResultDTO.setPolicyStatus("policyStatus");
        calcCommissionItemResultDTO.setRiskStatus("riskStatus");
        final List<CalcCommissionItemResultDTO> successData = Arrays.asList(calcCommissionItemResultDTO);

        // Configure CommissionCalcProcessService.addSmCommissionItem(...).
        final SmCommissionDetailItemPO smCommissionDetailItemPO = new SmCommissionDetailItemPO();
        smCommissionDetailItemPO.setId(0);
        smCommissionDetailItemPO.setOrderId("orderId");
        smCommissionDetailItemPO.setPlanId(0);
        smCommissionDetailItemPO.setRiskId(0);
        smCommissionDetailItemPO.setRiskName("riskName");
        smCommissionDetailItemPO.setAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setCommissionType(0);
        smCommissionDetailItemPO.setCommissionRate(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setCommissionAmount(new BigDecimal("0.00"));
        final List<SmCommissionDetailItemPO> smCommissionDetailItemPOS = Arrays.asList(smCommissionDetailItemPO);
        final CalcCommissionItemResultDTO calcCommissionItemResultDTO1 = new CalcCommissionItemResultDTO();
        calcCommissionItemResultDTO1.setOrderId("orderId");
        calcCommissionItemResultDTO1.setPolicyNo("policyNo");
        calcCommissionItemResultDTO1.setInsuredIdNumber("insuredIdNumber");
        calcCommissionItemResultDTO1.setPolicyStatus("policyStatus");
        calcCommissionItemResultDTO1.setRiskStatus("riskStatus");
        final List<CalcCommissionItemResultDTO> itemResults = Arrays.asList(calcCommissionItemResultDTO1);
        mockCalcProcessService.addSmCommissionItem(itemResults, Boolean.TRUE);

        // Run the test
        commissionManagerServiceUnderTest.calcCarOrderImportCommission(successData);

        // Verify the results
        // Confirm CommissionCalcProcessService.addSmCommissionDetail(...).
        final SmCommissionDetailItemPO smCommissionDetailItemPO1 = new SmCommissionDetailItemPO();
        smCommissionDetailItemPO1.setId(0);
        smCommissionDetailItemPO1.setOrderId("orderId");
        smCommissionDetailItemPO1.setPlanId(0);
        smCommissionDetailItemPO1.setRiskId(0);
        smCommissionDetailItemPO1.setRiskName("riskName");
        smCommissionDetailItemPO1.setAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO1.setCommissionType(0);
        smCommissionDetailItemPO1.setCommissionRate(new BigDecimal("0.00"));
        smCommissionDetailItemPO1.setCommissionAmount(new BigDecimal("0.00"));
        final List<SmCommissionDetailItemPO> itemPOList = Arrays.asList(smCommissionDetailItemPO1);
        mockCalcProcessService.addSmCommissionDetail(itemPOList, new CalcErrorDTO("errorMsg","errorMsgNoAlarm"), Boolean.TRUE);
    }

    @Test
    public void testCalcCarOrderImportCommission_CommissionCalcProcessServiceAddSmCommissionItemReturnsNoItems() {
        // Setup
        final CalcCommissionItemResultDTO calcCommissionItemResultDTO = new CalcCommissionItemResultDTO();
        calcCommissionItemResultDTO.setOrderId("orderId");
        calcCommissionItemResultDTO.setPolicyNo("policyNo");
        calcCommissionItemResultDTO.setInsuredIdNumber("insuredIdNumber");
        calcCommissionItemResultDTO.setPolicyStatus("policyStatus");
        calcCommissionItemResultDTO.setRiskStatus("riskStatus");
        final List<CalcCommissionItemResultDTO> successData = Arrays.asList(calcCommissionItemResultDTO);

        // Configure CommissionCalcProcessService.addSmCommissionItem(...).
        final CalcCommissionItemResultDTO calcCommissionItemResultDTO1 = new CalcCommissionItemResultDTO();
        calcCommissionItemResultDTO1.setOrderId("orderId");
        calcCommissionItemResultDTO1.setPolicyNo("policyNo");
        calcCommissionItemResultDTO1.setInsuredIdNumber("insuredIdNumber");
        calcCommissionItemResultDTO1.setPolicyStatus("policyStatus");
        calcCommissionItemResultDTO1.setRiskStatus("riskStatus");
        final List<CalcCommissionItemResultDTO> itemResults = Arrays.asList(calcCommissionItemResultDTO1);
        mockCalcProcessService.addSmCommissionItem(itemResults, Boolean.TRUE);

        // Run the test
        commissionManagerServiceUnderTest.calcCarOrderImportCommission(successData);

        // Verify the results
        // Confirm CommissionCalcProcessService.addSmCommissionDetail(...).
        final SmCommissionDetailItemPO smCommissionDetailItemPO = new SmCommissionDetailItemPO();
        smCommissionDetailItemPO.setId(0);
        smCommissionDetailItemPO.setOrderId("orderId");
        smCommissionDetailItemPO.setPlanId(0);
        smCommissionDetailItemPO.setRiskId(0);
        smCommissionDetailItemPO.setRiskName("riskName");
        smCommissionDetailItemPO.setAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setCommissionType(0);
        smCommissionDetailItemPO.setCommissionRate(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setCommissionAmount(new BigDecimal("0.00"));
        final List<SmCommissionDetailItemPO> itemPOList = Arrays.asList(smCommissionDetailItemPO);
        mockCalcProcessService.addSmCommissionDetail(itemPOList, new CalcErrorDTO("errorMsg","errorMsgNoAlarm"), Boolean.TRUE);
    }

    @Test
    public void testCalcOrderCommission1() {
        // Setup
        // Configure SmOrderMapper.getOrderCommissionInsuredByOrderId(...).
        final OrderCommissionInsuredInfoDTO orderCommissionInsuredInfoDTO = new OrderCommissionInsuredInfoDTO();
        orderCommissionInsuredInfoDTO.setFhOrderId("orderId");
        orderCommissionInsuredInfoDTO.setCompanyId(0);
        orderCommissionInsuredInfoDTO.setProductId(0);
        orderCommissionInsuredInfoDTO.setRecommendId("commissionUserId");
        orderCommissionInsuredInfoDTO.setProductType("productType");
        orderCommissionInsuredInfoDTO.setProductAttrCode("productAttrCode");
        orderCommissionInsuredInfoDTO.setPlanId(0);
        orderCommissionInsuredInfoDTO.setAppStatus("appStatus");
        orderCommissionInsuredInfoDTO.setChannel("channel");
        orderCommissionInsuredInfoDTO.setPaymentTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderCommissionInsuredInfoDTO.setSubmitTime("submitTime");
        orderCommissionInsuredInfoDTO.setPayStatus("payStatus");
        final List<OrderCommissionInsuredInfoDTO> orderCommissionInsuredInfoDTOS = Arrays.asList(
                orderCommissionInsuredInfoDTO);
        mockSmOrderMapper.getOrderCommissionInsuredByOrderId("fhOrderId");

        // Configure TempOrderNewProductMapper.getByOrderId(...).
        final TempOrderNewProductPO tempOrderNewProductPO = new TempOrderNewProductPO();
        tempOrderNewProductPO.setId(0);
        tempOrderNewProductPO.setOrderId("orderId");
        tempOrderNewProductPO.setProductId(0);
        tempOrderNewProductPO.setPlanId(0);
        tempOrderNewProductPO.setFhProductId("fhProductId");
        mockTempOrderNewProductMapper.getByOrderId("orderId");

        // Run the test
        commissionManagerServiceUnderTest.calcOrderCommission("messageNo", "fhOrderId", false);

        // Verify the results
        mockCommissionMessageService.updateCommissionMessageDoneStatus("messageNo", 9);

        // Confirm CommissionCalcProcessService.calcInsureCommission(...).
        final CommissionCalcProcessDTO processDTO = new CommissionCalcProcessDTO();
        processDTO.setErrorMsg("errorMsg");
        processDTO.setOrderId("orderId");
        processDTO.setCompanyId(0);
        processDTO.setProductId(0);
        processDTO.setProductType("productType");
        processDTO.setProductAttrCode("productAttrCode");
        processDTO.setChannel("channel");
        processDTO.setMatchingTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        processDTO.setTermNum(0);
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO = new CommissionCalcOrderInfoDTO();
        processDTO.setOrderInfo(Arrays.asList(commissionCalcOrderInfoDTO));
        final CalcCommissionItemResultDTO calcCommissionItemResultDTO = new CalcCommissionItemResultDTO();
        processDTO.setItemResults(Arrays.asList(calcCommissionItemResultDTO));
        processDTO.setCommissionUserId("commissionUserId");
        processDTO.setBusinessType("businessType");
        final OrderCommissionInsuredInfoDTO orderCommissionInsuredInfoDTO1 = new OrderCommissionInsuredInfoDTO();
        orderCommissionInsuredInfoDTO1.setFhOrderId("orderId");
        orderCommissionInsuredInfoDTO1.setCompanyId(0);
        orderCommissionInsuredInfoDTO1.setProductId(0);
        orderCommissionInsuredInfoDTO1.setRecommendId("commissionUserId");
        orderCommissionInsuredInfoDTO1.setProductType("productType");
        orderCommissionInsuredInfoDTO1.setProductAttrCode("productAttrCode");
        orderCommissionInsuredInfoDTO1.setPlanId(0);
        orderCommissionInsuredInfoDTO1.setAppStatus("appStatus");
        orderCommissionInsuredInfoDTO1.setChannel("channel");
        orderCommissionInsuredInfoDTO1.setPaymentTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderCommissionInsuredInfoDTO1.setSubmitTime("submitTime");
        orderCommissionInsuredInfoDTO1.setPayStatus("payStatus");
        final List<OrderCommissionInsuredInfoDTO> insuredInfoDTOList = Arrays.asList(orderCommissionInsuredInfoDTO1);
        mockCalcProcessService.calcInsureCommission(processDTO, insuredInfoDTOList, false);

        // Confirm SmOrderCommissionCalcProcessMapper.insert(...).
        final SmCommissionCalcProcessPO t = new SmCommissionCalcProcessPO();
        t.setId(0);
        t.setOrderId("orderId");
        t.setTermNum(0);
        t.setOrderInfo("orderInfo");
        t.setCommissionRetInfo("commissionRetInfo");
        t.setCalcRetStatus(0);
        t.setDealStatus(0);
        t.setErrorMsg("errorMsg");
        t.setCalcTime(0L);
        mockCommissionCalcProcessMapper.insert(t);
    }

    @Test
    public void testCalcOrderCommission1_SmOrderMapperReturnsNoItems() {
        // Setup
        mockSmOrderMapper.getOrderCommissionInsuredByOrderId("fhOrderId");

        // Run the test
        commissionManagerServiceUnderTest.calcOrderCommission("messageNo", "fhOrderId", false);

        // Verify the results
    }

    @Test
    public void testCalcOrderRenewCommission() {
        // Setup
        // Configure SmOrderMapper.getOrderCommissionInsuredByOrderId(...).
        final OrderCommissionInsuredInfoDTO orderCommissionInsuredInfoDTO = new OrderCommissionInsuredInfoDTO();
        orderCommissionInsuredInfoDTO.setFhOrderId("orderId");
        orderCommissionInsuredInfoDTO.setCompanyId(0);
        orderCommissionInsuredInfoDTO.setProductId(0);
        orderCommissionInsuredInfoDTO.setRecommendId("commissionUserId");
        orderCommissionInsuredInfoDTO.setProductType("productType");
        orderCommissionInsuredInfoDTO.setProductAttrCode("productAttrCode");
        orderCommissionInsuredInfoDTO.setPlanId(0);
        orderCommissionInsuredInfoDTO.setAppStatus("appStatus");
        orderCommissionInsuredInfoDTO.setChannel("channel");
        orderCommissionInsuredInfoDTO.setPaymentTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderCommissionInsuredInfoDTO.setSubmitTime("submitTime");
        orderCommissionInsuredInfoDTO.setPayStatus("payStatus");
        final List<OrderCommissionInsuredInfoDTO> orderCommissionInsuredInfoDTOS = Arrays.asList(
                orderCommissionInsuredInfoDTO);
        mockSmOrderMapper.getOrderCommissionInsuredByOrderId("fhOrderId");

        // Configure CommissionExternalService.listSmOrderRenewalTerm(...).
        final RenewalTermDTO renewalTermDTO = new RenewalTermDTO();
        renewalTermDTO.setOrderId("orderId");
        renewalTermDTO.setPolicyNo("policyNo");
        renewalTermDTO.setOrderAmount(new BigDecimal("0.00"));
        renewalTermDTO.setRenewalStatus(0);
        renewalTermDTO.setCustomerAdminId("commissionUserId");
        final List<RenewalTermDTO> renewalTermDTOS = Arrays.asList(renewalTermDTO);
        mockExternalService.listSmOrderRenewalTerm("fhOrderId", 0);

        // Run the test
        commissionManagerServiceUnderTest.calcOrderRenewCommission("messageNo", "fhOrderId", 0);

        // Verify the results
        // Confirm CommissionCalcProcessService.calcRenewalCommission(...).
        final CommissionCalcProcessDTO processDTO = new CommissionCalcProcessDTO();
        processDTO.setErrorMsg("errorMsg");
        processDTO.setOrderId("orderId");
        processDTO.setCompanyId(0);
        processDTO.setProductId(0);
        processDTO.setProductType("productType");
        processDTO.setProductAttrCode("productAttrCode");
        processDTO.setChannel("channel");
        processDTO.setMatchingTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        processDTO.setTermNum(0);
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO = new CommissionCalcOrderInfoDTO();
        processDTO.setOrderInfo(Arrays.asList(commissionCalcOrderInfoDTO));
        final CalcCommissionItemResultDTO calcCommissionItemResultDTO = new CalcCommissionItemResultDTO();
        processDTO.setItemResults(Arrays.asList(calcCommissionItemResultDTO));
        processDTO.setCommissionUserId("commissionUserId");
        processDTO.setBusinessType("businessType");
        final OrderCommissionInsuredInfoDTO orderCommissionInsuredInfoDTO1 = new OrderCommissionInsuredInfoDTO();
        orderCommissionInsuredInfoDTO1.setFhOrderId("orderId");
        orderCommissionInsuredInfoDTO1.setCompanyId(0);
        orderCommissionInsuredInfoDTO1.setProductId(0);
        orderCommissionInsuredInfoDTO1.setRecommendId("commissionUserId");
        orderCommissionInsuredInfoDTO1.setProductType("productType");
        orderCommissionInsuredInfoDTO1.setProductAttrCode("productAttrCode");
        orderCommissionInsuredInfoDTO1.setPlanId(0);
        orderCommissionInsuredInfoDTO1.setAppStatus("appStatus");
        orderCommissionInsuredInfoDTO1.setChannel("channel");
        orderCommissionInsuredInfoDTO1.setPaymentTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderCommissionInsuredInfoDTO1.setSubmitTime("submitTime");
        orderCommissionInsuredInfoDTO1.setPayStatus("payStatus");
        final List<OrderCommissionInsuredInfoDTO> insuredInfoDTOList = Arrays.asList(orderCommissionInsuredInfoDTO1);
        final RenewalTermDTO renewalTermDTO1 = new RenewalTermDTO();
        renewalTermDTO1.setOrderId("orderId");
        renewalTermDTO1.setPolicyNo("policyNo");
        renewalTermDTO1.setOrderAmount(new BigDecimal("0.00"));
        renewalTermDTO1.setRenewalStatus(0);
        renewalTermDTO1.setCustomerAdminId("commissionUserId");
        final List<RenewalTermDTO> renewalTermDTOList = Arrays.asList(renewalTermDTO1);
        mockCalcProcessService.calcRenewalCommission(processDTO, insuredInfoDTOList, renewalTermDTOList);

        // Confirm SmOrderCommissionCalcProcessMapper.insert(...).
        final SmCommissionCalcProcessPO t = new SmCommissionCalcProcessPO();
        t.setId(0);
        t.setOrderId("orderId");
        t.setTermNum(0);
        t.setOrderInfo("orderInfo");
        t.setCommissionRetInfo("commissionRetInfo");
        t.setCalcRetStatus(0);
        t.setDealStatus(0);
        t.setErrorMsg("errorMsg");
        t.setCalcTime(0L);
        mockCommissionCalcProcessMapper.insert(t);
        mockCommissionMessageService.updateCommissionMessageDoneStatus("messageNo", 0);
    }

    @Test
    public void testCalcOrderRenewCommission_SmOrderMapperReturnsNoItems() {
        // Setup
        mockSmOrderMapper.getOrderCommissionInsuredByOrderId("fhOrderId");

        // Run the test
        commissionManagerServiceUnderTest.calcOrderRenewCommission("messageNo", "fhOrderId", 0);

        // Verify the results
    }

    @Test
    public void testCalcOrderRenewCommission_CommissionExternalServiceReturnsNoItems() {
        // Setup
        // Configure SmOrderMapper.getOrderCommissionInsuredByOrderId(...).
        final OrderCommissionInsuredInfoDTO orderCommissionInsuredInfoDTO = new OrderCommissionInsuredInfoDTO();
        orderCommissionInsuredInfoDTO.setFhOrderId("orderId");
        orderCommissionInsuredInfoDTO.setCompanyId(0);
        orderCommissionInsuredInfoDTO.setProductId(0);
        orderCommissionInsuredInfoDTO.setRecommendId("commissionUserId");
        orderCommissionInsuredInfoDTO.setProductType("productType");
        orderCommissionInsuredInfoDTO.setProductAttrCode("productAttrCode");
        orderCommissionInsuredInfoDTO.setPlanId(0);
        orderCommissionInsuredInfoDTO.setAppStatus("appStatus");
        orderCommissionInsuredInfoDTO.setChannel("channel");
        orderCommissionInsuredInfoDTO.setPaymentTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderCommissionInsuredInfoDTO.setSubmitTime("submitTime");
        orderCommissionInsuredInfoDTO.setPayStatus("payStatus");
        final List<OrderCommissionInsuredInfoDTO> orderCommissionInsuredInfoDTOS = Arrays.asList(
                orderCommissionInsuredInfoDTO);
        mockSmOrderMapper.getOrderCommissionInsuredByOrderId("fhOrderId");

        mockExternalService.listSmOrderRenewalTerm("fhOrderId", 0);

        // Run the test
        commissionManagerServiceUnderTest.calcOrderRenewCommission("messageNo", "fhOrderId", 0);

        // Verify the results
    }

    @Test
    public void testDoCalcCommission() {
        // Setup
        // Configure SmOrderMapper.getOrderCommissionInsuredByOrderId(...).
        final OrderCommissionInsuredInfoDTO orderCommissionInsuredInfoDTO = new OrderCommissionInsuredInfoDTO();
        orderCommissionInsuredInfoDTO.setFhOrderId("orderId");
        orderCommissionInsuredInfoDTO.setCompanyId(0);
        orderCommissionInsuredInfoDTO.setProductId(0);
        orderCommissionInsuredInfoDTO.setRecommendId("commissionUserId");
        orderCommissionInsuredInfoDTO.setProductType("productType");
        orderCommissionInsuredInfoDTO.setProductAttrCode("productAttrCode");
        orderCommissionInsuredInfoDTO.setPlanId(0);
        orderCommissionInsuredInfoDTO.setAppStatus("appStatus");
        orderCommissionInsuredInfoDTO.setChannel("channel");
        orderCommissionInsuredInfoDTO.setPaymentTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderCommissionInsuredInfoDTO.setSubmitTime("submitTime");
        orderCommissionInsuredInfoDTO.setPayStatus("payStatus");
        final List<OrderCommissionInsuredInfoDTO> orderCommissionInsuredInfoDTOS = Arrays.asList(
                orderCommissionInsuredInfoDTO);
        mockSmOrderMapper.getOrderCommissionInsuredByOrderId("fhOrderId");

        // Configure TempOrderNewProductMapper.getByOrderId(...).
        final TempOrderNewProductPO tempOrderNewProductPO = new TempOrderNewProductPO();
        tempOrderNewProductPO.setId(0);
        tempOrderNewProductPO.setOrderId("orderId");
        tempOrderNewProductPO.setProductId(0);
        tempOrderNewProductPO.setPlanId(0);
        tempOrderNewProductPO.setFhProductId("fhProductId");
        mockTempOrderNewProductMapper.getByOrderId("orderId");

        // Run the test
        commissionManagerServiceUnderTest.doCalcCommission("messageNo", "fhOrderId", 0, false);

        // Verify the results
        mockCommissionMessageService.updateCommissionMessageDoneStatus("messageNo", 9);

        // Confirm CommissionCalcProcessService.calcInsureCommission(...).
        final CommissionCalcProcessDTO processDTO = new CommissionCalcProcessDTO();
        processDTO.setErrorMsg("errorMsg");
        processDTO.setOrderId("orderId");
        processDTO.setCompanyId(0);
        processDTO.setProductId(0);
        processDTO.setProductType("productType");
        processDTO.setProductAttrCode("productAttrCode");
        processDTO.setChannel("channel");
        processDTO.setMatchingTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        processDTO.setTermNum(0);
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO = new CommissionCalcOrderInfoDTO();
        processDTO.setOrderInfo(Arrays.asList(commissionCalcOrderInfoDTO));
        final CalcCommissionItemResultDTO calcCommissionItemResultDTO = new CalcCommissionItemResultDTO();
        processDTO.setItemResults(Arrays.asList(calcCommissionItemResultDTO));
        processDTO.setCommissionUserId("commissionUserId");
        processDTO.setBusinessType("businessType");
        final OrderCommissionInsuredInfoDTO orderCommissionInsuredInfoDTO1 = new OrderCommissionInsuredInfoDTO();
        orderCommissionInsuredInfoDTO1.setFhOrderId("orderId");
        orderCommissionInsuredInfoDTO1.setCompanyId(0);
        orderCommissionInsuredInfoDTO1.setProductId(0);
        orderCommissionInsuredInfoDTO1.setRecommendId("commissionUserId");
        orderCommissionInsuredInfoDTO1.setProductType("productType");
        orderCommissionInsuredInfoDTO1.setProductAttrCode("productAttrCode");
        orderCommissionInsuredInfoDTO1.setPlanId(0);
        orderCommissionInsuredInfoDTO1.setAppStatus("appStatus");
        orderCommissionInsuredInfoDTO1.setChannel("channel");
        orderCommissionInsuredInfoDTO1.setPaymentTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderCommissionInsuredInfoDTO1.setSubmitTime("submitTime");
        orderCommissionInsuredInfoDTO1.setPayStatus("payStatus");
        final List<OrderCommissionInsuredInfoDTO> insuredInfoDTOList = Arrays.asList(orderCommissionInsuredInfoDTO1);
        mockCalcProcessService.calcInsureCommission(processDTO, insuredInfoDTOList, false);

        // Confirm SmOrderCommissionCalcProcessMapper.insert(...).
        final SmCommissionCalcProcessPO t = new SmCommissionCalcProcessPO();
        t.setId(0);
        t.setOrderId("orderId");
        t.setTermNum(0);
        t.setOrderInfo("orderInfo");
        t.setCommissionRetInfo("commissionRetInfo");
        t.setCalcRetStatus(0);
        t.setDealStatus(0);
        t.setErrorMsg("errorMsg");
        t.setCalcTime(0L);
        mockCommissionCalcProcessMapper.insert(t);
    }

    @Test
    public void testDoCalcCommission_SmOrderMapperReturnsNoItems() {
        // Setup
        mockSmOrderMapper.getOrderCommissionInsuredByOrderId("fhOrderId");

        // Run the test
        commissionManagerServiceUnderTest.doCalcCommission("messageNo", "fhOrderId", 0, false);

        // Verify the results
    }

    @Test
    public void testManualUpdateCommissionJson() {
        // Setup
        // Run the test
        commissionManagerServiceUnderTest.manualUpdateCommissionJson("fhOrderId", 0);

        // Verify the results
        mockCalcProcessService.updateCommissionJson("fhOrderId", 0);
    }

    @Test
    public void testManualOldCommissionToNewCommission() {
        // Setup
        final ManualQueryOldCommissionDTO query = new ManualQueryOldCommissionDTO();
        query.setUserId("userId");
        query.setPassword("password");
        query.setAccountStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setAccountEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setProductIds(Arrays.asList(0));

        // Configure CommissionExternalService.listOldOrderCommission(...).
        final SmOrderCommissionDTO smOrderCommissionDTO = new SmOrderCommissionDTO();
        smOrderCommissionDTO.setId(0);
        smOrderCommissionDTO.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smOrderCommissionDTO.setFhOrderId("businessId");
        smOrderCommissionDTO.setPlanId(0);
        smOrderCommissionDTO.setRecommendId("commissionUserId");
        smOrderCommissionDTO.setTotalAmount(new BigDecimal("0.00"));
        smOrderCommissionDTO.setInsIdNumber("insIdNumber");
        smOrderCommissionDTO.setAppStatus("appStatus");
        smOrderCommissionDTO.setPolicyNo("policyNo");
        smOrderCommissionDTO.setPaymentProportion(new BigDecimal("0.00"));
        smOrderCommissionDTO.setPaymentAmount(new BigDecimal("0.00"));
        smOrderCommissionDTO.setSettlementProportion(new BigDecimal("0.00"));
        smOrderCommissionDTO.setSettlementAmount(new BigDecimal("0.00"));
        smOrderCommissionDTO.setConvertedProportion(new BigDecimal("0.00"));
        smOrderCommissionDTO.setConvertedAmount(new BigDecimal("0.00"));
        final List<SmOrderCommissionDTO> smOrderCommissionDTOS = Arrays.asList(smOrderCommissionDTO);
        final ManualQueryOldCommissionDTO dto = new ManualQueryOldCommissionDTO();
        dto.setUserId("userId");
        dto.setPassword("password");
        dto.setAccountStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        dto.setAccountEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        dto.setProductIds(Arrays.asList(0));
        mockExternalService.listOldOrderCommission(dto);

        // Run the test
        commissionManagerServiceUnderTest.manualOldCommissionToNewCommission(query);

        // Verify the results
        // Confirm CommissionCalcProcessService.batchCopyOldCommissionToNew(...).
        final SmCommissionDetailPO smCommissionDetailPO = new SmCommissionDetailPO();
        smCommissionDetailPO.setId(0);
        smCommissionDetailPO.setOrderId("businessId");
        smCommissionDetailPO.setPolicyNo("policyNo");
        smCommissionDetailPO.setInsuredIdNumber("insIdNumber");
        smCommissionDetailPO.setPolicyStatus("appStatus");
        smCommissionDetailPO.setPlanId(0);
        smCommissionDetailPO.setRiskId(0);
        smCommissionDetailPO.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smCommissionDetailPO.setAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setPaymentCommissionId(0);
        smCommissionDetailPO.setPaymentRate(new BigDecimal("0.00"));
        smCommissionDetailPO.setPaymentAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setSettlementCommissionId(0);
        smCommissionDetailPO.setSettlementRate(new BigDecimal("0.00"));
        smCommissionDetailPO.setSettlementAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setConversionCommissionId(0);
        smCommissionDetailPO.setConversionRate(new BigDecimal("0.00"));
        smCommissionDetailPO.setConversionAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setTermNum(0);
        smCommissionDetailPO.setCommissionUserId("commissionUserId");
        smCommissionDetailPO.setBusinessType("businessType");
        smCommissionDetailPO.setBusinessId("businessId");
        final List<SmCommissionDetailPO> detailPOS = Arrays.asList(smCommissionDetailPO);
        mockCalcProcessService.batchCopyOldCommissionToNew(detailPOS);
    }

    @Test
    public void testManualOldCommissionToNewCommission_CommissionExternalServiceReturnsNoItems() {
        // Setup
        final ManualQueryOldCommissionDTO query = new ManualQueryOldCommissionDTO();
        query.setUserId("userId");
        query.setPassword("password");
        query.setAccountStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setAccountEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setProductIds(Arrays.asList(0));

        // Configure CommissionExternalService.listOldOrderCommission(...).
        final ManualQueryOldCommissionDTO dto = new ManualQueryOldCommissionDTO();
        dto.setUserId("userId");
        dto.setPassword("password");
        dto.setAccountStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        dto.setAccountEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        dto.setProductIds(Arrays.asList(0));
        mockExternalService.listOldOrderCommission(dto);

        // Run the test
        commissionManagerServiceUnderTest.manualOldCommissionToNewCommission(query);

        // Verify the results
    }

    @Test
    public void testCalcCommission() {
        // Setup
        // Configure SmOrderMapper.getOrderCommissionInsuredByOrderId(...).
        final OrderCommissionInsuredInfoDTO orderCommissionInsuredInfoDTO = new OrderCommissionInsuredInfoDTO();
        orderCommissionInsuredInfoDTO.setFhOrderId("orderId");
        orderCommissionInsuredInfoDTO.setCompanyId(0);
        orderCommissionInsuredInfoDTO.setProductId(0);
        orderCommissionInsuredInfoDTO.setRecommendId("commissionUserId");
        orderCommissionInsuredInfoDTO.setProductType("productType");
        orderCommissionInsuredInfoDTO.setProductAttrCode("productAttrCode");
        orderCommissionInsuredInfoDTO.setPlanId(0);
        orderCommissionInsuredInfoDTO.setAppStatus("appStatus");
        orderCommissionInsuredInfoDTO.setChannel("channel");
        orderCommissionInsuredInfoDTO.setPaymentTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderCommissionInsuredInfoDTO.setSubmitTime("submitTime");
        orderCommissionInsuredInfoDTO.setPayStatus("payStatus");
        final List<OrderCommissionInsuredInfoDTO> orderCommissionInsuredInfoDTOS = Arrays.asList(
                orderCommissionInsuredInfoDTO);
        mockSmOrderMapper.getOrderCommissionInsuredByOrderId("fhOrderId");

        // Configure TempOrderNewProductMapper.getByOrderId(...).
        final TempOrderNewProductPO tempOrderNewProductPO = new TempOrderNewProductPO();
        tempOrderNewProductPO.setId(0);
        tempOrderNewProductPO.setOrderId("orderId");
        tempOrderNewProductPO.setProductId(0);
        tempOrderNewProductPO.setPlanId(0);
        tempOrderNewProductPO.setFhProductId("fhProductId");
        mockTempOrderNewProductMapper.getByOrderId("orderId");

        // Configure SmOrderPolicyService.getOrderPolicyByFhOrderId(...).
        final SmOrderPolicy smOrderPolicy = new SmOrderPolicy();
        smOrderPolicy.setId(0);
        smOrderPolicy.setFhOrderId("fhOrderId");
        smOrderPolicy.setChannel("channel");
        smOrderPolicy.setPayType("payType");
        smOrderPolicy.setPayPeriod("payPeriod");
        smOrderPolicy.setPayUnit("payUnit");
        smOrderPolicy.setValidPeriod("validPeriod");
        smOrderPolicy.setValidUnit("validUnit");
        smOrderPolicy.setVisitTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        smOrderPolicy.setVisitStatus("visitStatus");
        smOrderPolicy.setSurrenderType("surrenderType");
        smOrderPolicy.setExtend("extend");
        mockSmOrderPolicyService.getOrderPolicyByFhOrderId("fhOrderId");

        // Configure SmOrderRiskService.listRiskPremiumByFhOrderId(...).
        final OrderRiskPremiumDTO orderRiskPremiumDTO = new OrderRiskPremiumDTO();
        orderRiskPremiumDTO.setFhOrderId("fhOrderId");
        orderRiskPremiumDTO.setInsuredIdNumber("insuredIdNumber");
        orderRiskPremiumDTO.setRiskId(0L);
        orderRiskPremiumDTO.setRiskName("riskName");
        orderRiskPremiumDTO.setRiskType(0);
        final List<OrderRiskPremiumDTO> orderRiskPremiumDTOS = Arrays.asList(orderRiskPremiumDTO);
        mockSmOrderRiskService.listRiskPremiumByFhOrderId("fhOrderId");

        // Configure SmCmpySettingService.listCompanySettingByChannel(...).
        final SmCompanySettingVO smCompanySettingVO = new SmCompanySettingVO();
        smCompanySettingVO.setFieldCode("fieldCode");
        smCompanySettingVO.setFhProductId("fhProductId");
        smCompanySettingVO.setOptionCode("optionCode");
        smCompanySettingVO.setCommonCode("commonCode");
        smCompanySettingVO.setChannelName("channelName");
        final List<SmCompanySettingVO> smCompanySettingVOS = Arrays.asList(smCompanySettingVO);
        mockSmCmpySettingService.listCompanySettingByChannel(0, "channel");

        // Configure CommissionCalcService.calcOrderCommissionV1(...).
        final CalcCommissionResultDTO calcCommissionResultDTO = new CalcCommissionResultDTO();
        calcCommissionResultDTO.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        calcCommissionResultDTO.setOrderId("businessId");
        calcCommissionResultDTO.setPolicyNo("policyNo");
        calcCommissionResultDTO.setPolicyStatus("appStatus");
        calcCommissionResultDTO.setInsuredIdNumber("insIdNumber");
        calcCommissionResultDTO.setPlanId(0);
        calcCommissionResultDTO.setAmount(new BigDecimal("0.00"));
        calcCommissionResultDTO.setRiskId(0);
        final CommissionDTO commissionDTO = new CommissionDTO();
        commissionDTO.setType(0);
        commissionDTO.setCommissionId(0);
        commissionDTO.setCommissionRate(new BigDecimal("0.00"));
        commissionDTO.setCommissionAmt(new BigDecimal("0.00"));
        calcCommissionResultDTO.setCommissionList(Arrays.asList(commissionDTO));
        final List<CalcCommissionResultDTO> calcCommissionResultDTOS = Arrays.asList(calcCommissionResultDTO);
        final OrderCommissionInsuredInfoDTO orderCommissionInsuredInfoDTO1 = new OrderCommissionInsuredInfoDTO();
        orderCommissionInsuredInfoDTO1.setFhOrderId("orderId");
        orderCommissionInsuredInfoDTO1.setCompanyId(0);
        orderCommissionInsuredInfoDTO1.setProductId(0);
        orderCommissionInsuredInfoDTO1.setRecommendId("commissionUserId");
        orderCommissionInsuredInfoDTO1.setProductType("productType");
        orderCommissionInsuredInfoDTO1.setProductAttrCode("productAttrCode");
        orderCommissionInsuredInfoDTO1.setPlanId(0);
        orderCommissionInsuredInfoDTO1.setAppStatus("appStatus");
        orderCommissionInsuredInfoDTO1.setChannel("channel");
        orderCommissionInsuredInfoDTO1.setPaymentTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderCommissionInsuredInfoDTO1.setSubmitTime("submitTime");
        orderCommissionInsuredInfoDTO1.setPayStatus("payStatus");
        final List<OrderCommissionInsuredInfoDTO> insuredInfoDTOList = Arrays.asList(orderCommissionInsuredInfoDTO1);
        final CommissionPlanParamDTO commissionPlanParamDTO = new CommissionPlanParamDTO();
        commissionPlanParamDTO.setPlanId(0);
        commissionPlanParamDTO.setRiskId(0);
        commissionPlanParamDTO.setPayWay("payType");
        commissionPlanParamDTO.setCoveredYears("coveredYears");
        commissionPlanParamDTO.setValidPeriod("validPeriod");
        final List<CommissionPlanParamDTO> commissionConfigParamList = Arrays.asList(commissionPlanParamDTO);
        mockCommissionCalcService.calcOrderCommissionV1(insuredInfoDTOList, commissionConfigParamList);

        // Configure SmOrderCommissionMapper.getByOrderId(...).
        final SmCommissionPO smCommissionPO = new SmCommissionPO();
        smCommissionPO.setId(0);
        smCommissionPO.setOrderId("businessId");
        smCommissionPO.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smCommissionPO.setAmount(new BigDecimal("0.00"));
        smCommissionPO.setPaymentAmount(new BigDecimal("0.00"));
        smCommissionPO.setSettlementAmount(new BigDecimal("0.00"));
        mockOrderCommissionMapper.getByOrderId("businessId");

        // Run the test
        commissionManagerServiceUnderTest.calcCommission("fhOrderId", 0);

        // Verify the results
        // Confirm CommissionCalcService.queryCommissionRate(...).
        final CommissionPlanParamDTO commissionPlanParamDTO1 = new CommissionPlanParamDTO();
        commissionPlanParamDTO1.setPlanId(0);
        commissionPlanParamDTO1.setRiskId(0);
        commissionPlanParamDTO1.setPayWay("payType");
        commissionPlanParamDTO1.setCoveredYears("coveredYears");
        commissionPlanParamDTO1.setValidPeriod("validPeriod");
        final List<CommissionPlanParamDTO> commissionConfigParamList1 = Arrays.asList(commissionPlanParamDTO1);
        mockCommissionCalcService.queryCommissionRate(0,
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0, commissionConfigParamList1);

        // Confirm SmOrderCommissionDetailMapper.batchInsertCommissionDetail(...).
        final SmCommissionDetailPO smCommissionDetailPO = new SmCommissionDetailPO();
        smCommissionDetailPO.setId(0);
        smCommissionDetailPO.setOrderId("businessId");
        smCommissionDetailPO.setPolicyNo("policyNo");
        smCommissionDetailPO.setInsuredIdNumber("insIdNumber");
        smCommissionDetailPO.setPolicyStatus("appStatus");
        smCommissionDetailPO.setPlanId(0);
        smCommissionDetailPO.setRiskId(0);
        smCommissionDetailPO.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smCommissionDetailPO.setAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setPaymentCommissionId(0);
        smCommissionDetailPO.setPaymentRate(new BigDecimal("0.00"));
        smCommissionDetailPO.setPaymentAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setSettlementCommissionId(0);
        smCommissionDetailPO.setSettlementRate(new BigDecimal("0.00"));
        smCommissionDetailPO.setSettlementAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setConversionCommissionId(0);
        smCommissionDetailPO.setConversionRate(new BigDecimal("0.00"));
        smCommissionDetailPO.setConversionAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setTermNum(0);
        smCommissionDetailPO.setCommissionUserId("commissionUserId");
        smCommissionDetailPO.setBusinessType("businessType");
        smCommissionDetailPO.setBusinessId("businessId");
        final List<SmCommissionDetailPO> commissionConfigDetailList = Arrays.asList(smCommissionDetailPO);
        mockOrderCommissionDetailMapper.batchInsertCommissionDetail(commissionConfigDetailList);

        // Confirm SmOrderCommissionMapper.updateByPrimaryKeySelective(...).
        final SmCommissionPO t = new SmCommissionPO();
        t.setId(0);
        t.setOrderId("businessId");
        t.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        t.setAmount(new BigDecimal("0.00"));
        t.setPaymentAmount(new BigDecimal("0.00"));
        t.setSettlementAmount(new BigDecimal("0.00"));
        mockOrderCommissionMapper.updateByPrimaryKeySelective(t);

        // Confirm SmOrderCommissionMapper.insertSelective(...).
        final SmCommissionPO t1 = new SmCommissionPO();
        t1.setId(0);
        t1.setOrderId("businessId");
        t1.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        t1.setAmount(new BigDecimal("0.00"));
        t1.setPaymentAmount(new BigDecimal("0.00"));
        t1.setSettlementAmount(new BigDecimal("0.00"));
        mockOrderCommissionMapper.insertSelective(t1);
    }

    @Test
    public void testCalcCommission_SmOrderMapperReturnsNoItems() {
        // Setup
        mockSmOrderMapper.getOrderCommissionInsuredByOrderId("fhOrderId");

        // Run the test
        commissionManagerServiceUnderTest.calcCommission("fhOrderId", 0);

        // Verify the results
    }

    @Test
    public void testCalcCommission_SmOrderRiskServiceReturnsNoItems() {
        // Setup
        // Configure SmOrderMapper.getOrderCommissionInsuredByOrderId(...).
        final OrderCommissionInsuredInfoDTO orderCommissionInsuredInfoDTO = new OrderCommissionInsuredInfoDTO();
        orderCommissionInsuredInfoDTO.setFhOrderId("orderId");
        orderCommissionInsuredInfoDTO.setCompanyId(0);
        orderCommissionInsuredInfoDTO.setProductId(0);
        orderCommissionInsuredInfoDTO.setRecommendId("commissionUserId");
        orderCommissionInsuredInfoDTO.setProductType("productType");
        orderCommissionInsuredInfoDTO.setProductAttrCode("productAttrCode");
        orderCommissionInsuredInfoDTO.setPlanId(0);
        orderCommissionInsuredInfoDTO.setAppStatus("appStatus");
        orderCommissionInsuredInfoDTO.setChannel("channel");
        orderCommissionInsuredInfoDTO.setPaymentTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderCommissionInsuredInfoDTO.setSubmitTime("submitTime");
        orderCommissionInsuredInfoDTO.setPayStatus("payStatus");
        final List<OrderCommissionInsuredInfoDTO> orderCommissionInsuredInfoDTOS = Arrays.asList(
                orderCommissionInsuredInfoDTO);
        mockSmOrderMapper.getOrderCommissionInsuredByOrderId("fhOrderId");

        // Configure TempOrderNewProductMapper.getByOrderId(...).
        final TempOrderNewProductPO tempOrderNewProductPO = new TempOrderNewProductPO();
        tempOrderNewProductPO.setId(0);
        tempOrderNewProductPO.setOrderId("orderId");
        tempOrderNewProductPO.setProductId(0);
        tempOrderNewProductPO.setPlanId(0);
        tempOrderNewProductPO.setFhProductId("fhProductId");
        mockTempOrderNewProductMapper.getByOrderId("orderId");

        // Configure SmOrderPolicyService.getOrderPolicyByFhOrderId(...).
        final SmOrderPolicy smOrderPolicy = new SmOrderPolicy();
        smOrderPolicy.setId(0);
        smOrderPolicy.setFhOrderId("fhOrderId");
        smOrderPolicy.setChannel("channel");
        smOrderPolicy.setPayType("payType");
        smOrderPolicy.setPayPeriod("payPeriod");
        smOrderPolicy.setPayUnit("payUnit");
        smOrderPolicy.setValidPeriod("validPeriod");
        smOrderPolicy.setValidUnit("validUnit");
        smOrderPolicy.setVisitTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        smOrderPolicy.setVisitStatus("visitStatus");
        smOrderPolicy.setSurrenderType("surrenderType");
        smOrderPolicy.setExtend("extend");
        mockSmOrderPolicyService.getOrderPolicyByFhOrderId("fhOrderId");

        mockSmOrderRiskService.listRiskPremiumByFhOrderId("fhOrderId");

        // Configure SmCmpySettingService.listCompanySettingByChannel(...).
        final SmCompanySettingVO smCompanySettingVO = new SmCompanySettingVO();
        smCompanySettingVO.setFieldCode("fieldCode");
        smCompanySettingVO.setFhProductId("fhProductId");
        smCompanySettingVO.setOptionCode("optionCode");
        smCompanySettingVO.setCommonCode("commonCode");
        smCompanySettingVO.setChannelName("channelName");
        final List<SmCompanySettingVO> smCompanySettingVOS = Arrays.asList(smCompanySettingVO);
        mockSmCmpySettingService.listCompanySettingByChannel(0, "channel");

        // Configure CommissionCalcService.calcOrderCommissionV1(...).
        final CalcCommissionResultDTO calcCommissionResultDTO = new CalcCommissionResultDTO();
        calcCommissionResultDTO.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        calcCommissionResultDTO.setOrderId("businessId");
        calcCommissionResultDTO.setPolicyNo("policyNo");
        calcCommissionResultDTO.setPolicyStatus("appStatus");
        calcCommissionResultDTO.setInsuredIdNumber("insIdNumber");
        calcCommissionResultDTO.setPlanId(0);
        calcCommissionResultDTO.setAmount(new BigDecimal("0.00"));
        calcCommissionResultDTO.setRiskId(0);
        final CommissionDTO commissionDTO = new CommissionDTO();
        commissionDTO.setType(0);
        commissionDTO.setCommissionId(0);
        commissionDTO.setCommissionRate(new BigDecimal("0.00"));
        commissionDTO.setCommissionAmt(new BigDecimal("0.00"));
        calcCommissionResultDTO.setCommissionList(Arrays.asList(commissionDTO));
        final List<CalcCommissionResultDTO> calcCommissionResultDTOS = Arrays.asList(calcCommissionResultDTO);
        final OrderCommissionInsuredInfoDTO orderCommissionInsuredInfoDTO1 = new OrderCommissionInsuredInfoDTO();
        orderCommissionInsuredInfoDTO1.setFhOrderId("orderId");
        orderCommissionInsuredInfoDTO1.setCompanyId(0);
        orderCommissionInsuredInfoDTO1.setProductId(0);
        orderCommissionInsuredInfoDTO1.setRecommendId("commissionUserId");
        orderCommissionInsuredInfoDTO1.setProductType("productType");
        orderCommissionInsuredInfoDTO1.setProductAttrCode("productAttrCode");
        orderCommissionInsuredInfoDTO1.setPlanId(0);
        orderCommissionInsuredInfoDTO1.setAppStatus("appStatus");
        orderCommissionInsuredInfoDTO1.setChannel("channel");
        orderCommissionInsuredInfoDTO1.setPaymentTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderCommissionInsuredInfoDTO1.setSubmitTime(LocalDateTime.now().toString());
        orderCommissionInsuredInfoDTO1.setPayStatus("payStatus");
        final List<OrderCommissionInsuredInfoDTO> insuredInfoDTOList = Arrays.asList(orderCommissionInsuredInfoDTO1);
        final CommissionPlanParamDTO commissionPlanParamDTO = new CommissionPlanParamDTO();
        commissionPlanParamDTO.setPlanId(0);
        commissionPlanParamDTO.setRiskId(0);
        commissionPlanParamDTO.setPayWay("payType");
        commissionPlanParamDTO.setCoveredYears("coveredYears");
        commissionPlanParamDTO.setValidPeriod("validPeriod");
        final List<CommissionPlanParamDTO> commissionConfigParamList = Arrays.asList(commissionPlanParamDTO);
        mockCommissionCalcService.calcOrderCommissionV1(insuredInfoDTOList, commissionConfigParamList);

        // Configure SmOrderCommissionMapper.getByOrderId(...).
        final SmCommissionPO smCommissionPO = new SmCommissionPO();
        smCommissionPO.setId(0);
        smCommissionPO.setOrderId("businessId");
        smCommissionPO.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smCommissionPO.setAmount(new BigDecimal("0.00"));
        smCommissionPO.setPaymentAmount(new BigDecimal("0.00"));
        smCommissionPO.setSettlementAmount(new BigDecimal("0.00"));
        mockOrderCommissionMapper.getByOrderId("businessId");

        // Run the test
        commissionManagerServiceUnderTest.calcCommission("fhOrderId", 0);

        // Verify the results
        // Confirm CommissionCalcService.queryCommissionRate(...).
        final CommissionPlanParamDTO commissionPlanParamDTO1 = new CommissionPlanParamDTO();
        commissionPlanParamDTO1.setPlanId(0);
        commissionPlanParamDTO1.setRiskId(0);
        commissionPlanParamDTO1.setPayWay("payType");
        commissionPlanParamDTO1.setCoveredYears("coveredYears");
        commissionPlanParamDTO1.setValidPeriod("validPeriod");
        final List<CommissionPlanParamDTO> commissionConfigParamList1 = Arrays.asList(commissionPlanParamDTO1);
        mockCommissionCalcService.queryCommissionRate(0,
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0, commissionConfigParamList1);

        // Confirm SmOrderCommissionDetailMapper.batchInsertCommissionDetail(...).
        final SmCommissionDetailPO smCommissionDetailPO = new SmCommissionDetailPO();
        smCommissionDetailPO.setId(0);
        smCommissionDetailPO.setOrderId("businessId");
        smCommissionDetailPO.setPolicyNo("policyNo");
        smCommissionDetailPO.setInsuredIdNumber("insIdNumber");
        smCommissionDetailPO.setPolicyStatus("appStatus");
        smCommissionDetailPO.setPlanId(0);
        smCommissionDetailPO.setRiskId(0);
        smCommissionDetailPO.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smCommissionDetailPO.setAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setPaymentCommissionId(0);
        smCommissionDetailPO.setPaymentRate(new BigDecimal("0.00"));
        smCommissionDetailPO.setPaymentAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setSettlementCommissionId(0);
        smCommissionDetailPO.setSettlementRate(new BigDecimal("0.00"));
        smCommissionDetailPO.setSettlementAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setConversionCommissionId(0);
        smCommissionDetailPO.setConversionRate(new BigDecimal("0.00"));
        smCommissionDetailPO.setConversionAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setTermNum(0);
        smCommissionDetailPO.setCommissionUserId("commissionUserId");
        smCommissionDetailPO.setBusinessType("businessType");
        smCommissionDetailPO.setBusinessId("businessId");
        final List<SmCommissionDetailPO> commissionConfigDetailList = Arrays.asList(smCommissionDetailPO);
        mockOrderCommissionDetailMapper.batchInsertCommissionDetail(commissionConfigDetailList);

        // Confirm SmOrderCommissionMapper.updateByPrimaryKeySelective(...).
        final SmCommissionPO t = new SmCommissionPO();
        t.setId(0);
        t.setOrderId("businessId");
        t.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        t.setAmount(new BigDecimal("0.00"));
        t.setPaymentAmount(new BigDecimal("0.00"));
        t.setSettlementAmount(new BigDecimal("0.00"));
        mockOrderCommissionMapper.updateByPrimaryKeySelective(t);

        // Confirm SmOrderCommissionMapper.insertSelective(...).
        final SmCommissionPO t1 = new SmCommissionPO();
        t1.setId(0);
        t1.setOrderId("businessId");
        t1.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        t1.setAmount(new BigDecimal("0.00"));
        t1.setPaymentAmount(new BigDecimal("0.00"));
        t1.setSettlementAmount(new BigDecimal("0.00"));
        mockOrderCommissionMapper.insertSelective(t1);
    }

    @Test
    public void testCalcCommission_SmCmpySettingServiceReturnsNoItems() {
        // Setup
        // Configure SmOrderMapper.getOrderCommissionInsuredByOrderId(...).
        final OrderCommissionInsuredInfoDTO orderCommissionInsuredInfoDTO = new OrderCommissionInsuredInfoDTO();
        orderCommissionInsuredInfoDTO.setFhOrderId("orderId");
        orderCommissionInsuredInfoDTO.setCompanyId(0);
        orderCommissionInsuredInfoDTO.setProductId(0);
        orderCommissionInsuredInfoDTO.setRecommendId("commissionUserId");
        orderCommissionInsuredInfoDTO.setProductType("productType");
        orderCommissionInsuredInfoDTO.setProductAttrCode("productAttrCode");
        orderCommissionInsuredInfoDTO.setPlanId(0);
        orderCommissionInsuredInfoDTO.setAppStatus("appStatus");
        orderCommissionInsuredInfoDTO.setChannel("channel");
        orderCommissionInsuredInfoDTO.setPaymentTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderCommissionInsuredInfoDTO.setSubmitTime("submitTime");
        orderCommissionInsuredInfoDTO.setPayStatus("payStatus");
        final List<OrderCommissionInsuredInfoDTO> orderCommissionInsuredInfoDTOS = Arrays.asList(
                orderCommissionInsuredInfoDTO);
        mockSmOrderMapper.getOrderCommissionInsuredByOrderId("fhOrderId");

        // Configure TempOrderNewProductMapper.getByOrderId(...).
        final TempOrderNewProductPO tempOrderNewProductPO = new TempOrderNewProductPO();
        tempOrderNewProductPO.setId(0);
        tempOrderNewProductPO.setOrderId("orderId");
        tempOrderNewProductPO.setProductId(0);
        tempOrderNewProductPO.setPlanId(0);
        tempOrderNewProductPO.setFhProductId("fhProductId");
        mockTempOrderNewProductMapper.getByOrderId("orderId");

        // Configure SmOrderPolicyService.getOrderPolicyByFhOrderId(...).
        final SmOrderPolicy smOrderPolicy = new SmOrderPolicy();
        smOrderPolicy.setId(0);
        smOrderPolicy.setFhOrderId("fhOrderId");
        smOrderPolicy.setChannel("channel");
        smOrderPolicy.setPayType("payType");
        smOrderPolicy.setPayPeriod("payPeriod");
        smOrderPolicy.setPayUnit("payUnit");
        smOrderPolicy.setValidPeriod("validPeriod");
        smOrderPolicy.setValidUnit("validUnit");
        smOrderPolicy.setVisitTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        smOrderPolicy.setVisitStatus("visitStatus");
        smOrderPolicy.setSurrenderType("surrenderType");
        smOrderPolicy.setExtend("extend");
        mockSmOrderPolicyService.getOrderPolicyByFhOrderId("fhOrderId");

        // Configure SmOrderRiskService.listRiskPremiumByFhOrderId(...).
        final OrderRiskPremiumDTO orderRiskPremiumDTO = new OrderRiskPremiumDTO();
        orderRiskPremiumDTO.setFhOrderId("fhOrderId");
        orderRiskPremiumDTO.setInsuredIdNumber("insuredIdNumber");
        orderRiskPremiumDTO.setRiskId(0L);
        orderRiskPremiumDTO.setRiskName("riskName");
        orderRiskPremiumDTO.setRiskType(0);
        final List<OrderRiskPremiumDTO> orderRiskPremiumDTOS = Arrays.asList(orderRiskPremiumDTO);
        mockSmOrderRiskService.listRiskPremiumByFhOrderId("fhOrderId");

        mockSmCmpySettingService.listCompanySettingByChannel(0, "channel");

        // Configure CommissionCalcService.calcOrderCommissionV1(...).
        final CalcCommissionResultDTO calcCommissionResultDTO = new CalcCommissionResultDTO();
        calcCommissionResultDTO.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        calcCommissionResultDTO.setOrderId("businessId");
        calcCommissionResultDTO.setPolicyNo("policyNo");
        calcCommissionResultDTO.setPolicyStatus("appStatus");
        calcCommissionResultDTO.setInsuredIdNumber("insIdNumber");
        calcCommissionResultDTO.setPlanId(0);
        calcCommissionResultDTO.setAmount(new BigDecimal("0.00"));
        calcCommissionResultDTO.setRiskId(0);
        final CommissionDTO commissionDTO = new CommissionDTO();
        commissionDTO.setType(0);
        commissionDTO.setCommissionId(0);
        commissionDTO.setCommissionRate(new BigDecimal("0.00"));
        commissionDTO.setCommissionAmt(new BigDecimal("0.00"));
        calcCommissionResultDTO.setCommissionList(Arrays.asList(commissionDTO));
        final List<CalcCommissionResultDTO> calcCommissionResultDTOS = Arrays.asList(calcCommissionResultDTO);
        final OrderCommissionInsuredInfoDTO orderCommissionInsuredInfoDTO1 = new OrderCommissionInsuredInfoDTO();
        orderCommissionInsuredInfoDTO1.setFhOrderId("orderId");
        orderCommissionInsuredInfoDTO1.setCompanyId(0);
        orderCommissionInsuredInfoDTO1.setProductId(0);
        orderCommissionInsuredInfoDTO1.setRecommendId("commissionUserId");
        orderCommissionInsuredInfoDTO1.setProductType("productType");
        orderCommissionInsuredInfoDTO1.setProductAttrCode("productAttrCode");
        orderCommissionInsuredInfoDTO1.setPlanId(0);
        orderCommissionInsuredInfoDTO1.setAppStatus("appStatus");
        orderCommissionInsuredInfoDTO1.setChannel("channel");
        orderCommissionInsuredInfoDTO1.setPaymentTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderCommissionInsuredInfoDTO1.setSubmitTime("submitTime");
        orderCommissionInsuredInfoDTO1.setPayStatus("payStatus");
        final List<OrderCommissionInsuredInfoDTO> insuredInfoDTOList = Arrays.asList(orderCommissionInsuredInfoDTO1);
        final CommissionPlanParamDTO commissionPlanParamDTO = new CommissionPlanParamDTO();
        commissionPlanParamDTO.setPlanId(0);
        commissionPlanParamDTO.setRiskId(0);
        commissionPlanParamDTO.setPayWay("payType");
        commissionPlanParamDTO.setCoveredYears("coveredYears");
        commissionPlanParamDTO.setValidPeriod("validPeriod");
        final List<CommissionPlanParamDTO> commissionConfigParamList = Arrays.asList(commissionPlanParamDTO);
        mockCommissionCalcService.calcOrderCommissionV1(insuredInfoDTOList, commissionConfigParamList);

        // Configure SmOrderCommissionMapper.getByOrderId(...).
        final SmCommissionPO smCommissionPO = new SmCommissionPO();
        smCommissionPO.setId(0);
        smCommissionPO.setOrderId("businessId");
        smCommissionPO.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smCommissionPO.setAmount(new BigDecimal("0.00"));
        smCommissionPO.setPaymentAmount(new BigDecimal("0.00"));
        smCommissionPO.setSettlementAmount(new BigDecimal("0.00"));
        mockOrderCommissionMapper.getByOrderId("businessId");

        // Run the test
        commissionManagerServiceUnderTest.calcCommission("fhOrderId", 0);

        // Verify the results
        // Confirm CommissionCalcService.queryCommissionRate(...).
        final CommissionPlanParamDTO commissionPlanParamDTO1 = new CommissionPlanParamDTO();
        commissionPlanParamDTO1.setPlanId(0);
        commissionPlanParamDTO1.setRiskId(0);
        commissionPlanParamDTO1.setPayWay("payType");
        commissionPlanParamDTO1.setCoveredYears("coveredYears");
        commissionPlanParamDTO1.setValidPeriod("validPeriod");
        final List<CommissionPlanParamDTO> commissionConfigParamList1 = Arrays.asList(commissionPlanParamDTO1);
        mockCommissionCalcService.queryCommissionRate(0,
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0, commissionConfigParamList1);

        // Confirm SmOrderCommissionDetailMapper.batchInsertCommissionDetail(...).
        final SmCommissionDetailPO smCommissionDetailPO = new SmCommissionDetailPO();
        smCommissionDetailPO.setId(0);
        smCommissionDetailPO.setOrderId("businessId");
        smCommissionDetailPO.setPolicyNo("policyNo");
        smCommissionDetailPO.setInsuredIdNumber("insIdNumber");
        smCommissionDetailPO.setPolicyStatus("appStatus");
        smCommissionDetailPO.setPlanId(0);
        smCommissionDetailPO.setRiskId(0);
        smCommissionDetailPO.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smCommissionDetailPO.setAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setPaymentCommissionId(0);
        smCommissionDetailPO.setPaymentRate(new BigDecimal("0.00"));
        smCommissionDetailPO.setPaymentAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setSettlementCommissionId(0);
        smCommissionDetailPO.setSettlementRate(new BigDecimal("0.00"));
        smCommissionDetailPO.setSettlementAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setConversionCommissionId(0);
        smCommissionDetailPO.setConversionRate(new BigDecimal("0.00"));
        smCommissionDetailPO.setConversionAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setTermNum(0);
        smCommissionDetailPO.setCommissionUserId("commissionUserId");
        smCommissionDetailPO.setBusinessType("businessType");
        smCommissionDetailPO.setBusinessId("businessId");
        final List<SmCommissionDetailPO> commissionConfigDetailList = Arrays.asList(smCommissionDetailPO);
        mockOrderCommissionDetailMapper.batchInsertCommissionDetail(commissionConfigDetailList);

        // Confirm SmOrderCommissionMapper.updateByPrimaryKeySelective(...).
        final SmCommissionPO t = new SmCommissionPO();
        t.setId(0);
        t.setOrderId("businessId");
        t.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        t.setAmount(new BigDecimal("0.00"));
        t.setPaymentAmount(new BigDecimal("0.00"));
        t.setSettlementAmount(new BigDecimal("0.00"));
        mockOrderCommissionMapper.updateByPrimaryKeySelective(t);

        // Confirm SmOrderCommissionMapper.insertSelective(...).
        final SmCommissionPO t1 = new SmCommissionPO();
        t1.setId(0);
        t1.setOrderId("businessId");
        t1.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        t1.setAmount(new BigDecimal("0.00"));
        t1.setPaymentAmount(new BigDecimal("0.00"));
        t1.setSettlementAmount(new BigDecimal("0.00"));
        mockOrderCommissionMapper.insertSelective(t1);
    }

    @Test
    public void testCalcCommission_CommissionCalcServiceCalcOrderCommissionV1ReturnsNoItems() {
        // Setup
        // Configure SmOrderMapper.getOrderCommissionInsuredByOrderId(...).
        final OrderCommissionInsuredInfoDTO orderCommissionInsuredInfoDTO = new OrderCommissionInsuredInfoDTO();
        orderCommissionInsuredInfoDTO.setFhOrderId("orderId");
        orderCommissionInsuredInfoDTO.setCompanyId(0);
        orderCommissionInsuredInfoDTO.setProductId(0);
        orderCommissionInsuredInfoDTO.setRecommendId("commissionUserId");
        orderCommissionInsuredInfoDTO.setProductType("productType");
        orderCommissionInsuredInfoDTO.setProductAttrCode("productAttrCode");
        orderCommissionInsuredInfoDTO.setPlanId(0);
        orderCommissionInsuredInfoDTO.setAppStatus("appStatus");
        orderCommissionInsuredInfoDTO.setChannel("channel");
        orderCommissionInsuredInfoDTO.setPaymentTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderCommissionInsuredInfoDTO.setSubmitTime("submitTime");
        orderCommissionInsuredInfoDTO.setPayStatus("payStatus");
        final List<OrderCommissionInsuredInfoDTO> orderCommissionInsuredInfoDTOS = Arrays.asList(
                orderCommissionInsuredInfoDTO);
        mockSmOrderMapper.getOrderCommissionInsuredByOrderId("fhOrderId");

        // Configure TempOrderNewProductMapper.getByOrderId(...).
        final TempOrderNewProductPO tempOrderNewProductPO = new TempOrderNewProductPO();
        tempOrderNewProductPO.setId(0);
        tempOrderNewProductPO.setOrderId("orderId");
        tempOrderNewProductPO.setProductId(0);
        tempOrderNewProductPO.setPlanId(0);
        tempOrderNewProductPO.setFhProductId("fhProductId");
        mockTempOrderNewProductMapper.getByOrderId("orderId");

        // Configure SmOrderPolicyService.getOrderPolicyByFhOrderId(...).
        final SmOrderPolicy smOrderPolicy = new SmOrderPolicy();
        smOrderPolicy.setId(0);
        smOrderPolicy.setFhOrderId("fhOrderId");
        smOrderPolicy.setChannel("channel");
        smOrderPolicy.setPayType("payType");
        smOrderPolicy.setPayPeriod("payPeriod");
        smOrderPolicy.setPayUnit("payUnit");
        smOrderPolicy.setValidPeriod("validPeriod");
        smOrderPolicy.setValidUnit("validUnit");
        smOrderPolicy.setVisitTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        smOrderPolicy.setVisitStatus("visitStatus");
        smOrderPolicy.setSurrenderType("surrenderType");
        smOrderPolicy.setExtend("extend");
        mockSmOrderPolicyService.getOrderPolicyByFhOrderId("fhOrderId");

        // Configure SmOrderRiskService.listRiskPremiumByFhOrderId(...).
        final OrderRiskPremiumDTO orderRiskPremiumDTO = new OrderRiskPremiumDTO();
        orderRiskPremiumDTO.setFhOrderId("fhOrderId");
        orderRiskPremiumDTO.setInsuredIdNumber("insuredIdNumber");
        orderRiskPremiumDTO.setRiskId(0L);
        orderRiskPremiumDTO.setRiskName("riskName");
        orderRiskPremiumDTO.setRiskType(0);
        final List<OrderRiskPremiumDTO> orderRiskPremiumDTOS = Arrays.asList(orderRiskPremiumDTO);
        mockSmOrderRiskService.listRiskPremiumByFhOrderId("fhOrderId");

        // Configure SmCmpySettingService.listCompanySettingByChannel(...).
        final SmCompanySettingVO smCompanySettingVO = new SmCompanySettingVO();
        smCompanySettingVO.setFieldCode("fieldCode");
        smCompanySettingVO.setFhProductId("fhProductId");
        smCompanySettingVO.setOptionCode("optionCode");
        smCompanySettingVO.setCommonCode("commonCode");
        smCompanySettingVO.setChannelName("channelName");
        final List<SmCompanySettingVO> smCompanySettingVOS = Arrays.asList(smCompanySettingVO);
        mockSmCmpySettingService.listCompanySettingByChannel(0, "channel");

        // Configure CommissionCalcService.calcOrderCommissionV1(...).
        final OrderCommissionInsuredInfoDTO orderCommissionInsuredInfoDTO1 = new OrderCommissionInsuredInfoDTO();
        orderCommissionInsuredInfoDTO1.setFhOrderId("orderId");
        orderCommissionInsuredInfoDTO1.setCompanyId(0);
        orderCommissionInsuredInfoDTO1.setProductId(0);
        orderCommissionInsuredInfoDTO1.setRecommendId("commissionUserId");
        orderCommissionInsuredInfoDTO1.setProductType("productType");
        orderCommissionInsuredInfoDTO1.setProductAttrCode("productAttrCode");
        orderCommissionInsuredInfoDTO1.setPlanId(0);
        orderCommissionInsuredInfoDTO1.setAppStatus("appStatus");
        orderCommissionInsuredInfoDTO1.setChannel("channel");
        orderCommissionInsuredInfoDTO1.setPaymentTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        orderCommissionInsuredInfoDTO1.setSubmitTime(LocalDateTime.now().toString());
        orderCommissionInsuredInfoDTO1.setPayStatus("payStatus");
        final List<OrderCommissionInsuredInfoDTO> insuredInfoDTOList = Arrays.asList(orderCommissionInsuredInfoDTO1);
        final CommissionPlanParamDTO commissionPlanParamDTO = new CommissionPlanParamDTO();
        commissionPlanParamDTO.setPlanId(0);
        commissionPlanParamDTO.setRiskId(0);
        commissionPlanParamDTO.setPayWay("payType");
        commissionPlanParamDTO.setCoveredYears("coveredYears");
        commissionPlanParamDTO.setValidPeriod("validPeriod");
        final List<CommissionPlanParamDTO> commissionConfigParamList = Arrays.asList(commissionPlanParamDTO);
        mockCommissionCalcService.calcOrderCommissionV1(insuredInfoDTOList, commissionConfigParamList);

        // Configure SmOrderCommissionMapper.getByOrderId(...).
        final SmCommissionPO smCommissionPO = new SmCommissionPO();
        smCommissionPO.setId(0);
        smCommissionPO.setOrderId("businessId");
        smCommissionPO.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smCommissionPO.setAmount(new BigDecimal("0.00"));
        smCommissionPO.setPaymentAmount(new BigDecimal("0.00"));
        smCommissionPO.setSettlementAmount(new BigDecimal("0.00"));
        mockOrderCommissionMapper.getByOrderId("businessId");

        // Run the test
        commissionManagerServiceUnderTest.calcCommission("fhOrderId", 0);

        // Verify the results
        // Confirm CommissionCalcService.queryCommissionRate(...).
        final CommissionPlanParamDTO commissionPlanParamDTO1 = new CommissionPlanParamDTO();
        commissionPlanParamDTO1.setPlanId(0);
        commissionPlanParamDTO1.setRiskId(0);
        commissionPlanParamDTO1.setPayWay("payType");
        commissionPlanParamDTO1.setCoveredYears("coveredYears");
        commissionPlanParamDTO1.setValidPeriod("validPeriod");
        final List<CommissionPlanParamDTO> commissionConfigParamList1 = Arrays.asList(commissionPlanParamDTO1);
        mockCommissionCalcService.queryCommissionRate(0,
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0, commissionConfigParamList1);

        // Confirm SmOrderCommissionDetailMapper.batchInsertCommissionDetail(...).
        final SmCommissionDetailPO smCommissionDetailPO = new SmCommissionDetailPO();
        smCommissionDetailPO.setId(0);
        smCommissionDetailPO.setOrderId("businessId");
        smCommissionDetailPO.setPolicyNo("policyNo");
        smCommissionDetailPO.setInsuredIdNumber("insIdNumber");
        smCommissionDetailPO.setPolicyStatus("appStatus");
        smCommissionDetailPO.setPlanId(0);
        smCommissionDetailPO.setRiskId(0);
        smCommissionDetailPO.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smCommissionDetailPO.setAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setPaymentCommissionId(0);
        smCommissionDetailPO.setPaymentRate(new BigDecimal("0.00"));
        smCommissionDetailPO.setPaymentAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setSettlementCommissionId(0);
        smCommissionDetailPO.setSettlementRate(new BigDecimal("0.00"));
        smCommissionDetailPO.setSettlementAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setConversionCommissionId(0);
        smCommissionDetailPO.setConversionRate(new BigDecimal("0.00"));
        smCommissionDetailPO.setConversionAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setTermNum(0);
        smCommissionDetailPO.setCommissionUserId("commissionUserId");
        smCommissionDetailPO.setBusinessType("businessType");
        smCommissionDetailPO.setBusinessId("businessId");
        final List<SmCommissionDetailPO> commissionConfigDetailList = Arrays.asList(smCommissionDetailPO);
        mockOrderCommissionDetailMapper.batchInsertCommissionDetail(commissionConfigDetailList);

        // Confirm SmOrderCommissionMapper.updateByPrimaryKeySelective(...).
        final SmCommissionPO t = new SmCommissionPO();
        t.setId(0);
        t.setOrderId("businessId");
        t.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        t.setAmount(new BigDecimal("0.00"));
        t.setPaymentAmount(new BigDecimal("0.00"));
        t.setSettlementAmount(new BigDecimal("0.00"));
        mockOrderCommissionMapper.updateByPrimaryKeySelective(t);

        // Confirm SmOrderCommissionMapper.insertSelective(...).
        final SmCommissionPO t1 = new SmCommissionPO();
        t1.setId(0);
        t1.setOrderId("businessId");
        t1.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        t1.setAmount(new BigDecimal("0.00"));
        t1.setPaymentAmount(new BigDecimal("0.00"));
        t1.setSettlementAmount(new BigDecimal("0.00"));
        mockOrderCommissionMapper.insertSelective(t1);
    }

    @Test
    public void testCalcOrderCommission2() {
        // Setup
        final CalcCommissionParamDTO param = new CalcCommissionParamDTO();
        param.setOrderId("orderId");
        param.setCompanyId(0);
        param.setChannel("channel");
        param.setPolicyNo("policyNo");
        param.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        // Configure CommissionCalcService.calcOrderCommission(...).
        final CalcCommissionResultDTO calcCommissionResultDTO = new CalcCommissionResultDTO();
        calcCommissionResultDTO.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        calcCommissionResultDTO.setOrderId("businessId");
        calcCommissionResultDTO.setPolicyNo("policyNo");
        calcCommissionResultDTO.setPolicyStatus("appStatus");
        calcCommissionResultDTO.setInsuredIdNumber("insIdNumber");
        calcCommissionResultDTO.setPlanId(0);
        calcCommissionResultDTO.setAmount(new BigDecimal("0.00"));
        calcCommissionResultDTO.setRiskId(0);
        final CommissionDTO commissionDTO = new CommissionDTO();
        commissionDTO.setType(0);
        commissionDTO.setCommissionId(0);
        commissionDTO.setCommissionRate(new BigDecimal("0.00"));
        commissionDTO.setCommissionAmt(new BigDecimal("0.00"));
        calcCommissionResultDTO.setCommissionList(Arrays.asList(commissionDTO));
        final List<CalcCommissionResultDTO> calcCommissionResultDTOS = Arrays.asList(calcCommissionResultDTO);
        final CalcCommissionParamDTO param1 = new CalcCommissionParamDTO();
        param1.setOrderId("orderId");
        param1.setCompanyId(0);
        param1.setChannel("channel");
        param1.setPolicyNo("policyNo");
        param1.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        mockCommissionCalcService.calcOrderCommission(param1);

        // Configure SmOrderCommissionMapper.getByOrderId(...).
        final SmCommissionPO smCommissionPO = new SmCommissionPO();
        smCommissionPO.setId(0);
        smCommissionPO.setOrderId("businessId");
        smCommissionPO.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smCommissionPO.setAmount(new BigDecimal("0.00"));
        smCommissionPO.setPaymentAmount(new BigDecimal("0.00"));
        smCommissionPO.setSettlementAmount(new BigDecimal("0.00"));
        mockOrderCommissionMapper.getByOrderId("businessId");

        // Run the test
        commissionManagerServiceUnderTest.calcOrderCommission(param);

        // Verify the results
        // Confirm SmOrderCommissionDetailMapper.batchInsertCommissionDetail(...).
        final SmCommissionDetailPO smCommissionDetailPO = new SmCommissionDetailPO();
        smCommissionDetailPO.setId(0);
        smCommissionDetailPO.setOrderId("businessId");
        smCommissionDetailPO.setPolicyNo("policyNo");
        smCommissionDetailPO.setInsuredIdNumber("insIdNumber");
        smCommissionDetailPO.setPolicyStatus("appStatus");
        smCommissionDetailPO.setPlanId(0);
        smCommissionDetailPO.setRiskId(0);
        smCommissionDetailPO.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smCommissionDetailPO.setAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setPaymentCommissionId(0);
        smCommissionDetailPO.setPaymentRate(new BigDecimal("0.00"));
        smCommissionDetailPO.setPaymentAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setSettlementCommissionId(0);
        smCommissionDetailPO.setSettlementRate(new BigDecimal("0.00"));
        smCommissionDetailPO.setSettlementAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setConversionCommissionId(0);
        smCommissionDetailPO.setConversionRate(new BigDecimal("0.00"));
        smCommissionDetailPO.setConversionAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setTermNum(0);
        smCommissionDetailPO.setCommissionUserId("commissionUserId");
        smCommissionDetailPO.setBusinessType("businessType");
        smCommissionDetailPO.setBusinessId("businessId");
        final List<SmCommissionDetailPO> commissionConfigDetailList = Arrays.asList(smCommissionDetailPO);
        mockOrderCommissionDetailMapper.batchInsertCommissionDetail(commissionConfigDetailList);

        // Confirm SmOrderCommissionMapper.updateByPrimaryKeySelective(...).
        final SmCommissionPO t = new SmCommissionPO();
        t.setId(0);
        t.setOrderId("businessId");
        t.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        t.setAmount(new BigDecimal("0.00"));
        t.setPaymentAmount(new BigDecimal("0.00"));
        t.setSettlementAmount(new BigDecimal("0.00"));
        mockOrderCommissionMapper.updateByPrimaryKeySelective(t);

        // Confirm SmOrderCommissionMapper.insertSelective(...).
        final SmCommissionPO t1 = new SmCommissionPO();
        t1.setId(0);
        t1.setOrderId("businessId");
        t1.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        t1.setAmount(new BigDecimal("0.00"));
        t1.setPaymentAmount(new BigDecimal("0.00"));
        t1.setSettlementAmount(new BigDecimal("0.00"));
        mockOrderCommissionMapper.insertSelective(t1);
    }

    @Test
    public void testCalcOrderCommission2_CommissionCalcServiceReturnsNoItems() {
        // Setup
        final CalcCommissionParamDTO param = new CalcCommissionParamDTO();
        param.setOrderId("orderId");
        param.setCompanyId(0);
        param.setChannel("channel");
        param.setPolicyNo("policyNo");
        param.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        // Configure CommissionCalcService.calcOrderCommission(...).
        final CalcCommissionParamDTO param1 = new CalcCommissionParamDTO();
        param1.setOrderId("orderId");
        param1.setCompanyId(0);
        param1.setChannel("channel");
        param1.setPolicyNo("policyNo");
        param1.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        mockCommissionCalcService.calcOrderCommission(param1);

        // Configure SmOrderCommissionMapper.getByOrderId(...).
        final SmCommissionPO smCommissionPO = new SmCommissionPO();
        smCommissionPO.setId(0);
        smCommissionPO.setOrderId("businessId");
        smCommissionPO.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smCommissionPO.setAmount(new BigDecimal("0.00"));
        smCommissionPO.setPaymentAmount(new BigDecimal("0.00"));
        smCommissionPO.setSettlementAmount(new BigDecimal("0.00"));
        mockOrderCommissionMapper.getByOrderId("businessId");

        // Run the test
        commissionManagerServiceUnderTest.calcOrderCommission(param);

        // Verify the results
        // Confirm SmOrderCommissionDetailMapper.batchInsertCommissionDetail(...).
        final SmCommissionDetailPO smCommissionDetailPO = new SmCommissionDetailPO();
        smCommissionDetailPO.setId(0);
        smCommissionDetailPO.setOrderId("businessId");
        smCommissionDetailPO.setPolicyNo("policyNo");
        smCommissionDetailPO.setInsuredIdNumber("insIdNumber");
        smCommissionDetailPO.setPolicyStatus("appStatus");
        smCommissionDetailPO.setPlanId(0);
        smCommissionDetailPO.setRiskId(0);
        smCommissionDetailPO.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smCommissionDetailPO.setAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setPaymentCommissionId(0);
        smCommissionDetailPO.setPaymentRate(new BigDecimal("0.00"));
        smCommissionDetailPO.setPaymentAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setSettlementCommissionId(0);
        smCommissionDetailPO.setSettlementRate(new BigDecimal("0.00"));
        smCommissionDetailPO.setSettlementAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setConversionCommissionId(0);
        smCommissionDetailPO.setConversionRate(new BigDecimal("0.00"));
        smCommissionDetailPO.setConversionAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setTermNum(0);
        smCommissionDetailPO.setCommissionUserId("commissionUserId");
        smCommissionDetailPO.setBusinessType("businessType");
        smCommissionDetailPO.setBusinessId("businessId");
        final List<SmCommissionDetailPO> commissionConfigDetailList = Arrays.asList(smCommissionDetailPO);
        mockOrderCommissionDetailMapper.batchInsertCommissionDetail(commissionConfigDetailList);

        // Confirm SmOrderCommissionMapper.updateByPrimaryKeySelective(...).
        final SmCommissionPO t = new SmCommissionPO();
        t.setId(0);
        t.setOrderId("businessId");
        t.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        t.setAmount(new BigDecimal("0.00"));
        t.setPaymentAmount(new BigDecimal("0.00"));
        t.setSettlementAmount(new BigDecimal("0.00"));
        mockOrderCommissionMapper.updateByPrimaryKeySelective(t);

        // Confirm SmOrderCommissionMapper.insertSelective(...).
        final SmCommissionPO t1 = new SmCommissionPO();
        t1.setId(0);
        t1.setOrderId("businessId");
        t1.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        t1.setAmount(new BigDecimal("0.00"));
        t1.setPaymentAmount(new BigDecimal("0.00"));
        t1.setSettlementAmount(new BigDecimal("0.00"));
        mockOrderCommissionMapper.insertSelective(t1);
    }

    @Test
    public void testGetCommonSettingInfo() {
        // Setup
        final SmCompanySettingVO smCompanySettingVO = new SmCompanySettingVO();
        smCompanySettingVO.setFieldCode("fieldCode");
        smCompanySettingVO.setFhProductId("fhProductId");
        smCompanySettingVO.setOptionCode("optionCode");
        smCompanySettingVO.setCommonCode("commonCode");
        smCompanySettingVO.setChannelName("channelName");
        final List<SmCompanySettingVO> settings = Arrays.asList(smCompanySettingVO);

        // Run the test
        CommissionManagerService.getCommonSettingInfo(settings, "fieldCode", "code", "productId");
    }

    @Test
    public void testQueryCommission() {
        // Setup
        final SmOrderQuery query = new SmOrderQuery();
        query.setPage(0);
        query.setSize(0);
        query.setQueryPage(false);
        query.setProductType("productType");
        query.setCreateDateStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        // Configure SmOrderCommissionDetailMapper.queryCommission(...).
        final SmLongInsuranceCommissionVO smLongInsuranceCommissionVO = new SmLongInsuranceCommissionVO();
        smLongInsuranceCommissionVO.setTermNum(0);
        smLongInsuranceCommissionVO.setAppStatus("appStatus");
        smLongInsuranceCommissionVO.setValidPeriod("validPeriod");
        smLongInsuranceCommissionVO.setProductId(0);
        smLongInsuranceCommissionVO.setPolicyNo("policyNo");
        smLongInsuranceCommissionVO.setPayPeriod("payPeriod");
        smLongInsuranceCommissionVO.setChannel("channel");
        smLongInsuranceCommissionVO.setInsuredIdNumber("insuredIdNumber");
        smLongInsuranceCommissionVO.setTotalAmount(new BigDecimal("0.00"));
        smLongInsuranceCommissionVO.setOriginalAmount(new BigDecimal("0.00"));
        smLongInsuranceCommissionVO.setConvertedAmount(new BigDecimal("0.00"));
        smLongInsuranceCommissionVO.setTaskPremium(new BigDecimal("0.00"));
        smLongInsuranceCommissionVO.setAddCommissionAmount(new BigDecimal("0.00"));
        smLongInsuranceCommissionVO.setFhOrderId("fhOrderId");
        smLongInsuranceCommissionVO.setPaymentTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smLongInsuranceCommissionVO.setReconciliationResult("describe");
        smLongInsuranceCommissionVO.setVisitStatus("visitStatus");
        smLongInsuranceCommissionVO.setSurrenderType("surrenderType");
        smLongInsuranceCommissionVO.setVisitTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        smLongInsuranceCommissionVO.setReceiptSignTime("receiptSignTime");
        final List<SmLongInsuranceCommissionVO> smLongInsuranceCommissionVOS = Arrays.asList(
                smLongInsuranceCommissionVO);
        final SmOrderQuery query1 = new SmOrderQuery();
        query1.setPage(0);
        query1.setSize(0);
        query1.setQueryPage(false);
        query1.setProductType("productType");
        query1.setCreateDateStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        mockOrderCommissionDetailMapper.queryCommission(query1);

        // Configure SmOrderPolicyService.listOrderPolicyByFhOrderIds(...).
        final SmOrderPolicy smOrderPolicy = new SmOrderPolicy();
        smOrderPolicy.setId(0);
        smOrderPolicy.setFhOrderId("fhOrderId");
        smOrderPolicy.setChannel("channel");
        smOrderPolicy.setPayType("payType");
        smOrderPolicy.setPayPeriod("payPeriod");
        smOrderPolicy.setPayUnit("payUnit");
        smOrderPolicy.setValidPeriod("validPeriod");
        smOrderPolicy.setValidUnit("validUnit");
        smOrderPolicy.setVisitTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        smOrderPolicy.setVisitStatus("visitStatus");
        smOrderPolicy.setSurrenderType("surrenderType");
        smOrderPolicy.setExtend("extend");
        final List<SmOrderPolicy> smOrderPolicies = Arrays.asList(smOrderPolicy);
        mockSmOrderPolicyService.listOrderPolicyByFhOrderIds(Arrays.asList("value"));

        mockAddCommissionQueryService.getLongInsuredPersonAddCommissionMap(Arrays.asList("value"));
        mockAddCommissionQueryService.getLongInsuredPersonAddCommissionProportionMap(
                Arrays.asList("value"));

        // Configure SmTaskPremiumConfigService.queryTaskPremiumConfigByProductIds(...).
        final SmTaskPremiumConfig smTaskPremiumConfig = new SmTaskPremiumConfig();
        smTaskPremiumConfig.setId(0);
        smTaskPremiumConfig.setProductId(0);
        smTaskPremiumConfig.setPremiumStandard("premiumStandard");
        smTaskPremiumConfig.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        smTaskPremiumConfig.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<SmTaskPremiumConfig> smTaskPremiumConfigs = Arrays.asList(smTaskPremiumConfig);
        mockPremiumConfigService.queryTaskPremiumConfigByProductIds(Arrays.asList(0));

        mockAddCommissionQueryService.getInsuredPersonAddCommissionUuid("fhOrderId", "policyNo", "insuredIdNumber",
                "termNum");
        mockSmReconciliationPolicyMapper.getSmReconciliationNewestPolicy(LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), "channel", Arrays.asList("value"));
        mockSmReconciliationPolicyMapper.getPolicyKey("policyNo", "appStatus");

        // Configure SmOrderCommissionDetailMapper.queryCommissionCount(...).
        final SmOrderQuery query2 = new SmOrderQuery();
        query2.setPage(0);
        query2.setSize(0);
        query2.setQueryPage(false);
        query2.setProductType("productType");
        query2.setCreateDateStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        mockOrderCommissionDetailMapper.queryCommissionCount(query2);

        // Run the test
        final PageInfo<SmLongInsuranceCommissionVO> result = commissionManagerServiceUnderTest.queryCommission(query);

        // Verify the results
    }

    @Test
    public void testQueryCommission_SmOrderCommissionDetailMapperQueryCommissionReturnsNoItems() {
        // Setup
        final SmOrderQuery query = new SmOrderQuery();
        query.setPage(0);
        query.setSize(0);
        query.setQueryPage(false);
        query.setProductType("productType");
        query.setCreateDateStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        // Configure SmOrderCommissionDetailMapper.queryCommission(...).
        final SmOrderQuery query1 = new SmOrderQuery();
        query1.setPage(0);
        query1.setSize(0);
        query1.setQueryPage(false);
        query1.setProductType("productType");
        query1.setCreateDateStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        mockOrderCommissionDetailMapper.queryCommission(query1);

        // Run the test
        final PageInfo<SmLongInsuranceCommissionVO> result = commissionManagerServiceUnderTest.queryCommission(query);

        // Verify the results
    }

    @Test
    public void testQueryCommission_SmOrderPolicyServiceReturnsNoItems() {
        // Setup
        final SmOrderQuery query = new SmOrderQuery();
        query.setPage(0);
        query.setSize(0);
        query.setQueryPage(false);
        query.setProductType("productType");
        query.setCreateDateStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        // Configure SmOrderCommissionDetailMapper.queryCommission(...).
        final SmLongInsuranceCommissionVO smLongInsuranceCommissionVO = new SmLongInsuranceCommissionVO();
        smLongInsuranceCommissionVO.setTermNum(0);
        smLongInsuranceCommissionVO.setAppStatus("appStatus");
        smLongInsuranceCommissionVO.setValidPeriod("validPeriod");
        smLongInsuranceCommissionVO.setProductId(0);
        smLongInsuranceCommissionVO.setPolicyNo("policyNo");
        smLongInsuranceCommissionVO.setPayPeriod("payPeriod");
        smLongInsuranceCommissionVO.setChannel("channel");
        smLongInsuranceCommissionVO.setInsuredIdNumber("insuredIdNumber");
        smLongInsuranceCommissionVO.setTotalAmount(new BigDecimal("0.00"));
        smLongInsuranceCommissionVO.setOriginalAmount(new BigDecimal("0.00"));
        smLongInsuranceCommissionVO.setConvertedAmount(new BigDecimal("0.00"));
        smLongInsuranceCommissionVO.setTaskPremium(new BigDecimal("0.00"));
        smLongInsuranceCommissionVO.setAddCommissionAmount(new BigDecimal("0.00"));
        smLongInsuranceCommissionVO.setFhOrderId("fhOrderId");
        smLongInsuranceCommissionVO.setPaymentTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smLongInsuranceCommissionVO.setReconciliationResult("describe");
        smLongInsuranceCommissionVO.setVisitStatus("visitStatus");
        smLongInsuranceCommissionVO.setSurrenderType("surrenderType");
        smLongInsuranceCommissionVO.setVisitTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        smLongInsuranceCommissionVO.setReceiptSignTime("receiptSignTime");
        final List<SmLongInsuranceCommissionVO> smLongInsuranceCommissionVOS = Arrays.asList(
                smLongInsuranceCommissionVO);
        final SmOrderQuery query1 = new SmOrderQuery();
        query1.setPage(0);
        query1.setSize(0);
        query1.setQueryPage(false);
        query1.setProductType("productType");
        query1.setCreateDateStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        mockOrderCommissionDetailMapper.queryCommission(query1);

        mockSmOrderPolicyService.listOrderPolicyByFhOrderIds(Arrays.asList("value"));
        mockAddCommissionQueryService.getLongInsuredPersonAddCommissionMap(Arrays.asList("value"));
        mockAddCommissionQueryService.getLongInsuredPersonAddCommissionProportionMap(
                Arrays.asList("value"));

        // Configure SmTaskPremiumConfigService.queryTaskPremiumConfigByProductIds(...).
        final SmTaskPremiumConfig smTaskPremiumConfig = new SmTaskPremiumConfig();
        smTaskPremiumConfig.setId(0);
        smTaskPremiumConfig.setProductId(0);
        smTaskPremiumConfig.setPremiumStandard("premiumStandard");
        smTaskPremiumConfig.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        smTaskPremiumConfig.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<SmTaskPremiumConfig> smTaskPremiumConfigs = Arrays.asList(smTaskPremiumConfig);
        mockPremiumConfigService.queryTaskPremiumConfigByProductIds(Arrays.asList(0));

        mockAddCommissionQueryService.getInsuredPersonAddCommissionUuid("fhOrderId", "policyNo", "insuredIdNumber",
                "termNum");
        mockSmReconciliationPolicyMapper.getSmReconciliationNewestPolicy(LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), "channel", Arrays.asList("value"));
        mockSmReconciliationPolicyMapper.getPolicyKey("policyNo", "appStatus");

        // Configure SmOrderCommissionDetailMapper.queryCommissionCount(...).
        final SmOrderQuery query2 = new SmOrderQuery();
        query2.setPage(0);
        query2.setSize(0);
        query2.setQueryPage(false);
        query2.setProductType("productType");
        query2.setCreateDateStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        mockOrderCommissionDetailMapper.queryCommissionCount(query2);

        // Run the test
        final PageInfo<SmLongInsuranceCommissionVO> result = commissionManagerServiceUnderTest.queryCommission(query);

        // Verify the results
    }

    @Test
    public void testQueryCommission_SmTaskPremiumConfigServiceReturnsNoItems() {
        // Setup
        final SmOrderQuery query = new SmOrderQuery();
        query.setPage(0);
        query.setSize(0);
        query.setQueryPage(false);
        query.setProductType("productType");
        query.setCreateDateStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        // Configure SmOrderCommissionDetailMapper.queryCommission(...).
        final SmLongInsuranceCommissionVO smLongInsuranceCommissionVO = new SmLongInsuranceCommissionVO();
        smLongInsuranceCommissionVO.setTermNum(0);
        smLongInsuranceCommissionVO.setAppStatus("appStatus");
        smLongInsuranceCommissionVO.setValidPeriod("validPeriod");
        smLongInsuranceCommissionVO.setProductId(0);
        smLongInsuranceCommissionVO.setPolicyNo("policyNo");
        smLongInsuranceCommissionVO.setPayPeriod("payPeriod");
        smLongInsuranceCommissionVO.setChannel("channel");
        smLongInsuranceCommissionVO.setInsuredIdNumber("insuredIdNumber");
        smLongInsuranceCommissionVO.setTotalAmount(new BigDecimal("0.00"));
        smLongInsuranceCommissionVO.setOriginalAmount(new BigDecimal("0.00"));
        smLongInsuranceCommissionVO.setConvertedAmount(new BigDecimal("0.00"));
        smLongInsuranceCommissionVO.setTaskPremium(new BigDecimal("0.00"));
        smLongInsuranceCommissionVO.setAddCommissionAmount(new BigDecimal("0.00"));
        smLongInsuranceCommissionVO.setFhOrderId("fhOrderId");
        smLongInsuranceCommissionVO.setPaymentTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smLongInsuranceCommissionVO.setReconciliationResult("describe");
        smLongInsuranceCommissionVO.setVisitStatus("visitStatus");
        smLongInsuranceCommissionVO.setSurrenderType("surrenderType");
        smLongInsuranceCommissionVO.setVisitTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        smLongInsuranceCommissionVO.setReceiptSignTime("receiptSignTime");
        final List<SmLongInsuranceCommissionVO> smLongInsuranceCommissionVOS = Arrays.asList(
                smLongInsuranceCommissionVO);
        final SmOrderQuery query1 = new SmOrderQuery();
        query1.setPage(0);
        query1.setSize(0);
        query1.setQueryPage(false);
        query1.setProductType("productType");
        query1.setCreateDateStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        mockOrderCommissionDetailMapper.queryCommission(query1);

        // Configure SmOrderPolicyService.listOrderPolicyByFhOrderIds(...).
        final SmOrderPolicy smOrderPolicy = new SmOrderPolicy();
        smOrderPolicy.setId(0);
        smOrderPolicy.setFhOrderId("fhOrderId");
        smOrderPolicy.setChannel("channel");
        smOrderPolicy.setPayType("payType");
        smOrderPolicy.setPayPeriod("payPeriod");
        smOrderPolicy.setPayUnit("payUnit");
        smOrderPolicy.setValidPeriod("validPeriod");
        smOrderPolicy.setValidUnit("validUnit");
        smOrderPolicy.setVisitTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        smOrderPolicy.setVisitStatus("visitStatus");
        smOrderPolicy.setSurrenderType("surrenderType");
        smOrderPolicy.setExtend("extend");
        final List<SmOrderPolicy> smOrderPolicies = Arrays.asList(smOrderPolicy);
        mockSmOrderPolicyService.listOrderPolicyByFhOrderIds(Arrays.asList("value"));

        mockAddCommissionQueryService.getLongInsuredPersonAddCommissionMap(Arrays.asList("value"));
        mockAddCommissionQueryService.getLongInsuredPersonAddCommissionProportionMap(
                Arrays.asList("value"));
        mockPremiumConfigService.queryTaskPremiumConfigByProductIds(Arrays.asList(0));
        mockAddCommissionQueryService.getInsuredPersonAddCommissionUuid("fhOrderId", "policyNo", "insuredIdNumber",
                "termNum");
        mockSmReconciliationPolicyMapper.getSmReconciliationNewestPolicy(LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), "channel", Arrays.asList("value"));
        mockSmReconciliationPolicyMapper.getPolicyKey("policyNo", "appStatus");

        // Configure SmOrderCommissionDetailMapper.queryCommissionCount(...).
        final SmOrderQuery query2 = new SmOrderQuery();
        query2.setPage(0);
        query2.setSize(0);
        query2.setQueryPage(false);
        query2.setProductType("productType");
        query2.setCreateDateStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        mockOrderCommissionDetailMapper.queryCommissionCount(query2);

        // Run the test
        final PageInfo<SmLongInsuranceCommissionVO> result = commissionManagerServiceUnderTest.queryCommission(query);

        // Verify the results
    }

    @Test
    public void testGetCommissionDetail() {
        // Setup
        final SmLongInsuranceCommissionDetailVO smLongInsuranceCommissionDetailVO = new SmLongInsuranceCommissionDetailVO();
        smLongInsuranceCommissionDetailVO.setPlanId(0);
        smLongInsuranceCommissionDetailVO.setPlanName("planName");
        smLongInsuranceCommissionDetailVO.setRiskId(0);
        smLongInsuranceCommissionDetailVO.setRiskName("riskName");
        smLongInsuranceCommissionDetailVO.setConvertedAmount(new BigDecimal("0.00"));
        smLongInsuranceCommissionDetailVO.setSettlementCommission(new BigDecimal("0.00"));
        smLongInsuranceCommissionDetailVO.setSettlementRate(new BigDecimal("0.00"));
        smLongInsuranceCommissionDetailVO.setAddCommissionProportion(new BigDecimal("0.00"));
        smLongInsuranceCommissionDetailVO.setAddCommissionAmount(new BigDecimal("0.00"));
        smLongInsuranceCommissionDetailVO.setPayCommissionAmount(new BigDecimal("0.00"));
        smLongInsuranceCommissionDetailVO.setPayCommissionRate(new BigDecimal("0.00"));
        smLongInsuranceCommissionDetailVO.setAmount(new BigDecimal("0.00"));
        final List<SmLongInsuranceCommissionDetailVO> expectedResult = Arrays.asList(smLongInsuranceCommissionDetailVO);

        // Run the test
        commissionManagerServiceUnderTest.getCommissionDetail(
                "policyNo", "insuredIdNumber", "appStatus", 0);

    }

    @Test
    public void testGetCommissionDetail_SmOrderCommissionItemMapperReturnsNoItems() {
        // Setup
        mockSmOrderCommissionItemMapper.getSmCommissionDetailItemDTO("policyNo", "insuredIdNumber", "appStatus",
                0);

        // Run the test
        commissionManagerServiceUnderTest.getCommissionDetail(
                "policyNo", "insuredIdNumber", "appStatus", 0);

    }

    @Test
    public void testGetCommissionDetail_SmAddCommissionDetailMapperReturnsNoItems() {
        // Setup
        // Configure SmOrderCommissionItemMapper.getSmCommissionDetailItemDTO(...).
        final SmCommissionDetailItemDTO smCommissionDetailItemDTO = new SmCommissionDetailItemDTO();
        smCommissionDetailItemDTO.setId(0);
        smCommissionDetailItemDTO.setOrderId("orderId");
        smCommissionDetailItemDTO.setPlanId(0);
        smCommissionDetailItemDTO.setRiskId(0);
        smCommissionDetailItemDTO.setRiskName("riskName");
        smCommissionDetailItemDTO.setAmount(new BigDecimal("0.00"));
        smCommissionDetailItemDTO.setCommissionType(0);
        smCommissionDetailItemDTO.setCommissionRate(new BigDecimal("0.00"));
        smCommissionDetailItemDTO.setCommissionAmount(new BigDecimal("0.00"));
        smCommissionDetailItemDTO.setPlanName("planName");
        final List<SmCommissionDetailItemDTO> smCommissionDetailItemDTOS = Arrays.asList(smCommissionDetailItemDTO);
        mockSmOrderCommissionItemMapper.getSmCommissionDetailItemDTO("policyNo", "insuredIdNumber", "appStatus",
                0);

        mockAddCommissionDetailMapper.getAddCommission("policyNo", "insuredIdNumber", "appStatus", 0);

        // Run the test
        commissionManagerServiceUnderTest.getCommissionDetail(
                "policyNo", "insuredIdNumber", "appStatus", 0);

    }

    @Test
    public void testDownloadLongInsuranceOrderCommission() {
        // Setup
        final SmOrderQuery query = new SmOrderQuery();
        query.setPage(0);
        query.setSize(0);
        query.setQueryPage(false);
        query.setProductType("productType");
        query.setCreateDateStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        final MockHttpServletResponse response = new MockHttpServletResponse();

        // Configure SmOrderCommissionDetailMapper.queryCommission(...).
        final SmLongInsuranceCommissionVO smLongInsuranceCommissionVO = new SmLongInsuranceCommissionVO();
        smLongInsuranceCommissionVO.setTermNum(0);
        smLongInsuranceCommissionVO.setAppStatus("appStatus");
        smLongInsuranceCommissionVO.setValidPeriod("validPeriod");
        smLongInsuranceCommissionVO.setProductId(0);
        smLongInsuranceCommissionVO.setPolicyNo("policyNo");
        smLongInsuranceCommissionVO.setPayPeriod("payPeriod");
        smLongInsuranceCommissionVO.setChannel("channel");
        smLongInsuranceCommissionVO.setInsuredIdNumber("insuredIdNumber");
        smLongInsuranceCommissionVO.setTotalAmount(new BigDecimal("0.00"));
        smLongInsuranceCommissionVO.setOriginalAmount(new BigDecimal("0.00"));
        smLongInsuranceCommissionVO.setConvertedAmount(new BigDecimal("0.00"));
        smLongInsuranceCommissionVO.setTaskPremium(new BigDecimal("0.00"));
        smLongInsuranceCommissionVO.setAddCommissionAmount(new BigDecimal("0.00"));
        smLongInsuranceCommissionVO.setFhOrderId("fhOrderId");
        smLongInsuranceCommissionVO.setPaymentTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smLongInsuranceCommissionVO.setReconciliationResult("describe");
        smLongInsuranceCommissionVO.setVisitStatus("visitStatus");
        smLongInsuranceCommissionVO.setSurrenderType("surrenderType");
        smLongInsuranceCommissionVO.setVisitTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        smLongInsuranceCommissionVO.setReceiptSignTime("receiptSignTime");
        final List<SmLongInsuranceCommissionVO> smLongInsuranceCommissionVOS = Arrays.asList(
                smLongInsuranceCommissionVO);
        final SmOrderQuery query1 = new SmOrderQuery();
        query1.setPage(0);
        query1.setSize(0);
        query1.setQueryPage(false);
        query1.setProductType("productType");
        query1.setCreateDateStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        mockOrderCommissionDetailMapper.queryCommission(query1);

        // Configure SmOrderPolicyService.listOrderPolicyByFhOrderIds(...).
        final SmOrderPolicy smOrderPolicy = new SmOrderPolicy();
        smOrderPolicy.setId(0);
        smOrderPolicy.setFhOrderId("fhOrderId");
        smOrderPolicy.setChannel("channel");
        smOrderPolicy.setPayType("payType");
        smOrderPolicy.setPayPeriod("payPeriod");
        smOrderPolicy.setPayUnit("payUnit");
        smOrderPolicy.setValidPeriod("validPeriod");
        smOrderPolicy.setValidUnit("validUnit");
        smOrderPolicy.setVisitTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        smOrderPolicy.setVisitStatus("visitStatus");
        smOrderPolicy.setSurrenderType("surrenderType");
        smOrderPolicy.setExtend("extend");
        final List<SmOrderPolicy> smOrderPolicies = Arrays.asList(smOrderPolicy);
        mockSmOrderPolicyService.listOrderPolicyByFhOrderIds(Arrays.asList("value"));

        mockAddCommissionQueryService.getLongInsuredPersonAddCommissionMap(Arrays.asList("value"));
        mockAddCommissionQueryService.getLongInsuredPersonAddCommissionProportionMap(
                Arrays.asList("value"));

        // Configure SmTaskPremiumConfigService.queryTaskPremiumConfigByProductIds(...).
        final SmTaskPremiumConfig smTaskPremiumConfig = new SmTaskPremiumConfig();
        smTaskPremiumConfig.setId(0);
        smTaskPremiumConfig.setProductId(0);
        smTaskPremiumConfig.setPremiumStandard("premiumStandard");
        smTaskPremiumConfig.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        smTaskPremiumConfig.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<SmTaskPremiumConfig> smTaskPremiumConfigs = Arrays.asList(smTaskPremiumConfig);
        mockPremiumConfigService.queryTaskPremiumConfigByProductIds(Arrays.asList(0));

        mockAddCommissionQueryService.getInsuredPersonAddCommissionUuid("fhOrderId", "policyNo", "insuredIdNumber",
                "termNum");
        mockSmReconciliationPolicyMapper.getSmReconciliationNewestPolicy(LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), "channel", Arrays.asList("value"));
        mockSmReconciliationPolicyMapper.getPolicyKey("policyNo", "appStatus");

        // Configure SmOrderCommissionDetailMapper.queryCommissionCount(...).
        final SmOrderQuery query2 = new SmOrderQuery();
        query2.setPage(0);
        query2.setSize(0);
        query2.setQueryPage(false);
        query2.setProductType("productType");
        query2.setCreateDateStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        mockOrderCommissionDetailMapper.queryCommissionCount(query2);
    }

    @Test
    public void testDownloadLongInsuranceOrderCommission_SmOrderCommissionDetailMapperQueryCommissionReturnsNoItems() {
        // Setup
        final SmOrderQuery query = new SmOrderQuery();
        query.setPage(0);
        query.setSize(0);
        query.setQueryPage(false);
        query.setProductType("productType");
        query.setCreateDateStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        final MockHttpServletResponse response = new MockHttpServletResponse();

        // Configure SmOrderCommissionDetailMapper.queryCommission(...).
        final SmOrderQuery query1 = new SmOrderQuery();
        query1.setPage(0);
        query1.setSize(0);
        query1.setQueryPage(false);
        query1.setProductType("productType");
        query1.setCreateDateStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        mockOrderCommissionDetailMapper.queryCommission(query1);

        // Configure SmOrderPolicyService.listOrderPolicyByFhOrderIds(...).
        final SmOrderPolicy smOrderPolicy = new SmOrderPolicy();
        smOrderPolicy.setId(0);
        smOrderPolicy.setFhOrderId("fhOrderId");
        smOrderPolicy.setChannel("channel");
        smOrderPolicy.setPayType("payType");
        smOrderPolicy.setPayPeriod("payPeriod");
        smOrderPolicy.setPayUnit("payUnit");
        smOrderPolicy.setValidPeriod("validPeriod");
        smOrderPolicy.setValidUnit("validUnit");
        smOrderPolicy.setVisitTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        smOrderPolicy.setVisitStatus("visitStatus");
        smOrderPolicy.setSurrenderType("surrenderType");
        smOrderPolicy.setExtend("extend");
        final List<SmOrderPolicy> smOrderPolicies = Arrays.asList(smOrderPolicy);
        mockSmOrderPolicyService.listOrderPolicyByFhOrderIds(Arrays.asList("value"));

        mockAddCommissionQueryService.getLongInsuredPersonAddCommissionMap(Arrays.asList("value"));
        mockAddCommissionQueryService.getLongInsuredPersonAddCommissionProportionMap(
                Arrays.asList("value"));

        // Configure SmTaskPremiumConfigService.queryTaskPremiumConfigByProductIds(...).
        final SmTaskPremiumConfig smTaskPremiumConfig = new SmTaskPremiumConfig();
        smTaskPremiumConfig.setId(0);
        smTaskPremiumConfig.setProductId(0);
        smTaskPremiumConfig.setPremiumStandard("premiumStandard");
        smTaskPremiumConfig.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        smTaskPremiumConfig.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<SmTaskPremiumConfig> smTaskPremiumConfigs = Arrays.asList(smTaskPremiumConfig);
        mockPremiumConfigService.queryTaskPremiumConfigByProductIds(Arrays.asList(0));

        mockAddCommissionQueryService.getInsuredPersonAddCommissionUuid("fhOrderId", "policyNo", "insuredIdNumber",
                "termNum");
        mockSmReconciliationPolicyMapper.getSmReconciliationNewestPolicy(LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), "channel", Arrays.asList("value"));
        mockSmReconciliationPolicyMapper.getPolicyKey("policyNo", "appStatus");

        // Configure SmOrderCommissionDetailMapper.queryCommissionCount(...).
        final SmOrderQuery query2 = new SmOrderQuery();
        query2.setPage(0);
        query2.setSize(0);
        query2.setQueryPage(false);
        query2.setProductType("productType");
        query2.setCreateDateStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        mockOrderCommissionDetailMapper.queryCommissionCount(query2);
    }

    @Test
    public void testDownloadLongInsuranceOrderCommission_SmOrderPolicyServiceReturnsNoItems() {
        // Setup
        final SmOrderQuery query = new SmOrderQuery();
        query.setPage(0);
        query.setSize(0);
        query.setQueryPage(false);
        query.setProductType("productType");
        query.setCreateDateStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        final MockHttpServletResponse response = new MockHttpServletResponse();

        // Configure SmOrderCommissionDetailMapper.queryCommission(...).
        final SmLongInsuranceCommissionVO smLongInsuranceCommissionVO = new SmLongInsuranceCommissionVO();
        smLongInsuranceCommissionVO.setTermNum(0);
        smLongInsuranceCommissionVO.setAppStatus("appStatus");
        smLongInsuranceCommissionVO.setValidPeriod("validPeriod");
        smLongInsuranceCommissionVO.setProductId(0);
        smLongInsuranceCommissionVO.setPolicyNo("policyNo");
        smLongInsuranceCommissionVO.setPayPeriod("payPeriod");
        smLongInsuranceCommissionVO.setChannel("channel");
        smLongInsuranceCommissionVO.setInsuredIdNumber("insuredIdNumber");
        smLongInsuranceCommissionVO.setTotalAmount(new BigDecimal("0.00"));
        smLongInsuranceCommissionVO.setOriginalAmount(new BigDecimal("0.00"));
        smLongInsuranceCommissionVO.setConvertedAmount(new BigDecimal("0.00"));
        smLongInsuranceCommissionVO.setTaskPremium(new BigDecimal("0.00"));
        smLongInsuranceCommissionVO.setAddCommissionAmount(new BigDecimal("0.00"));
        smLongInsuranceCommissionVO.setFhOrderId("fhOrderId");
        smLongInsuranceCommissionVO.setPaymentTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smLongInsuranceCommissionVO.setReconciliationResult("describe");
        smLongInsuranceCommissionVO.setVisitStatus("visitStatus");
        smLongInsuranceCommissionVO.setSurrenderType("surrenderType");
        smLongInsuranceCommissionVO.setVisitTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        smLongInsuranceCommissionVO.setReceiptSignTime("receiptSignTime");
        final List<SmLongInsuranceCommissionVO> smLongInsuranceCommissionVOS = Arrays.asList(
                smLongInsuranceCommissionVO);
        final SmOrderQuery query1 = new SmOrderQuery();
        query1.setPage(0);
        query1.setSize(0);
        query1.setQueryPage(false);
        query1.setProductType("productType");
        query1.setCreateDateStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        mockOrderCommissionDetailMapper.queryCommission(query1);

        mockSmOrderPolicyService.listOrderPolicyByFhOrderIds(Arrays.asList("value"));
        mockAddCommissionQueryService.getLongInsuredPersonAddCommissionMap(Arrays.asList("value"));
        mockAddCommissionQueryService.getLongInsuredPersonAddCommissionProportionMap(
                Arrays.asList("value"));

        // Configure SmTaskPremiumConfigService.queryTaskPremiumConfigByProductIds(...).
        final SmTaskPremiumConfig smTaskPremiumConfig = new SmTaskPremiumConfig();
        smTaskPremiumConfig.setId(0);
        smTaskPremiumConfig.setProductId(0);
        smTaskPremiumConfig.setPremiumStandard("premiumStandard");
        smTaskPremiumConfig.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        smTaskPremiumConfig.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<SmTaskPremiumConfig> smTaskPremiumConfigs = Arrays.asList(smTaskPremiumConfig);
        mockPremiumConfigService.queryTaskPremiumConfigByProductIds(Arrays.asList(0));

        mockAddCommissionQueryService.getInsuredPersonAddCommissionUuid("fhOrderId", "policyNo", "insuredIdNumber",
                "termNum");
        mockSmReconciliationPolicyMapper.getSmReconciliationNewestPolicy(LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), "channel", Arrays.asList("value"));
        mockSmReconciliationPolicyMapper.getPolicyKey("policyNo", "appStatus");

        // Configure SmOrderCommissionDetailMapper.queryCommissionCount(...).
        final SmOrderQuery query2 = new SmOrderQuery();
        query2.setPage(0);
        query2.setSize(0);
        query2.setQueryPage(false);
        query2.setProductType("productType");
        query2.setCreateDateStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        mockOrderCommissionDetailMapper.queryCommissionCount(query2);
    }

    @Test
    public void testDownloadLongInsuranceOrderCommission_SmTaskPremiumConfigServiceReturnsNoItems() {
        // Setup
        final SmOrderQuery query = new SmOrderQuery();
        query.setPage(0);
        query.setSize(0);
        query.setQueryPage(false);
        query.setProductType("productType");
        query.setCreateDateStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        final MockHttpServletResponse response = new MockHttpServletResponse();

        // Configure SmOrderCommissionDetailMapper.queryCommission(...).
        final SmLongInsuranceCommissionVO smLongInsuranceCommissionVO = new SmLongInsuranceCommissionVO();
        smLongInsuranceCommissionVO.setTermNum(0);
        smLongInsuranceCommissionVO.setAppStatus("appStatus");
        smLongInsuranceCommissionVO.setValidPeriod("validPeriod");
        smLongInsuranceCommissionVO.setProductId(0);
        smLongInsuranceCommissionVO.setPolicyNo("policyNo");
        smLongInsuranceCommissionVO.setPayPeriod("payPeriod");
        smLongInsuranceCommissionVO.setChannel("channel");
        smLongInsuranceCommissionVO.setInsuredIdNumber("insuredIdNumber");
        smLongInsuranceCommissionVO.setTotalAmount(new BigDecimal("0.00"));
        smLongInsuranceCommissionVO.setOriginalAmount(new BigDecimal("0.00"));
        smLongInsuranceCommissionVO.setConvertedAmount(new BigDecimal("0.00"));
        smLongInsuranceCommissionVO.setTaskPremium(new BigDecimal("0.00"));
        smLongInsuranceCommissionVO.setAddCommissionAmount(new BigDecimal("0.00"));
        smLongInsuranceCommissionVO.setFhOrderId("fhOrderId");
        smLongInsuranceCommissionVO.setPaymentTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smLongInsuranceCommissionVO.setReconciliationResult("describe");
        smLongInsuranceCommissionVO.setVisitStatus("visitStatus");
        smLongInsuranceCommissionVO.setSurrenderType("surrenderType");
        smLongInsuranceCommissionVO.setVisitTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        smLongInsuranceCommissionVO.setReceiptSignTime("receiptSignTime");
        final List<SmLongInsuranceCommissionVO> smLongInsuranceCommissionVOS = Arrays.asList(
                smLongInsuranceCommissionVO);
        final SmOrderQuery query1 = new SmOrderQuery();
        query1.setPage(0);
        query1.setSize(0);
        query1.setQueryPage(false);
        query1.setProductType("productType");
        query1.setCreateDateStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        mockOrderCommissionDetailMapper.queryCommission(query1);

        // Configure SmOrderPolicyService.listOrderPolicyByFhOrderIds(...).
        final SmOrderPolicy smOrderPolicy = new SmOrderPolicy();
        smOrderPolicy.setId(0);
        smOrderPolicy.setFhOrderId("fhOrderId");
        smOrderPolicy.setChannel("channel");
        smOrderPolicy.setPayType("payType");
        smOrderPolicy.setPayPeriod("payPeriod");
        smOrderPolicy.setPayUnit("payUnit");
        smOrderPolicy.setValidPeriod("validPeriod");
        smOrderPolicy.setValidUnit("validUnit");
        smOrderPolicy.setVisitTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        smOrderPolicy.setVisitStatus("visitStatus");
        smOrderPolicy.setSurrenderType("surrenderType");
        smOrderPolicy.setExtend("extend");
        final List<SmOrderPolicy> smOrderPolicies = Arrays.asList(smOrderPolicy);
        mockSmOrderPolicyService.listOrderPolicyByFhOrderIds(Arrays.asList("value"));

        mockAddCommissionQueryService.getLongInsuredPersonAddCommissionMap(Arrays.asList("value"));
        mockAddCommissionQueryService.getLongInsuredPersonAddCommissionProportionMap(
                Arrays.asList("value"));
        mockPremiumConfigService.queryTaskPremiumConfigByProductIds(Arrays.asList(0));
        mockAddCommissionQueryService.getInsuredPersonAddCommissionUuid("fhOrderId", "policyNo", "insuredIdNumber",
                "termNum");
        mockSmReconciliationPolicyMapper.getSmReconciliationNewestPolicy(LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), "channel", Arrays.asList("value"));
        mockSmReconciliationPolicyMapper.getPolicyKey("policyNo", "appStatus");

        // Configure SmOrderCommissionDetailMapper.queryCommissionCount(...).
        final SmOrderQuery query2 = new SmOrderQuery();
        query2.setPage(0);
        query2.setSize(0);
        query2.setQueryPage(false);
        query2.setProductType("productType");
        query2.setCreateDateStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        mockOrderCommissionDetailMapper.queryCommissionCount(query2);

        // Run the test
        commissionManagerServiceUnderTest.downloadLongInsuranceOrderCommission(query, response);

        // Verify the results
    }

    @Test
    public void testUpdateCommissionInfo() {
        // Setup
        final UpdateCommissionInfoDTO updateDTO = new UpdateCommissionInfoDTO();
        updateDTO.setOrderId("orderId");
        updateDTO.setPolicyNo("policyNo");
        updateDTO.setFieldCode("fieldCode");
        updateDTO.setOldValue("oldValue");
        updateDTO.setNewValue("newValue");
        updateDTO.setPaymentTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        // Run the test
        commissionManagerServiceUnderTest.updateCommissionInfo(updateDTO);

        // Verify the results
        mockSmOrderCommissionItemMapper.updateCommissionInsuredIdNumber("orderId", "policyNo", "oldValue",
                "newValue");
        mockOrderCommissionDetailMapper.updateCommissionInsuredIdNumber("orderId", "policyNo", "oldValue",
                "newValue");
        mockSmOrderCommissionItemMapper.updateCommissionUserId("orderId", "policyNo", "oldValue", "newValue",
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        mockOrderCommissionDetailMapper.updateCommissionUserId("orderId", "policyNo", "oldValue", "newValue",
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
    }

    @Test
    public void testUpdateCommissionUserId() {
        // Setup
        // Run the test
        commissionManagerServiceUnderTest.updateCommissionUserId("orderId", 0, "newCommissionUserId");

        // Verify the results
        mockSmOrderCommissionItemMapper.updateCommissionUserIdByOrderIdAndTermNum("orderId", 0,
                "newCommissionUserId");
        mockOrderCommissionDetailMapper.updateCommissionUserIdByOrderIdAndTermNum("orderId", 0,
                "newCommissionUserId");
    }

    @Test
    public void testQueryLongCommissionSummary() {
        // Setup
        final SmOrderQuery query = new SmOrderQuery();
        query.setPage(0);
        query.setSize(0);
        query.setQueryPage(false);
        query.setProductType("productType");
        query.setCreateDateStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        final SmOrderSummaryVO expectedResult = new SmOrderSummaryVO();
        expectedResult.setTotalQty(0);
        expectedResult.setExpiredQty(0);
        expectedResult.setOrderPayedQty(0);
        expectedResult.setOrderUnPayQty(0);
        expectedResult.setOrderCancelQty(0);

        // Configure SmOrderCommissionDetailMapper.queryLongCommissionSummary(...).
        final SmOrderSummaryVO smOrderSummaryVO = new SmOrderSummaryVO();
        smOrderSummaryVO.setTotalQty(0);
        smOrderSummaryVO.setExpiredQty(0);
        smOrderSummaryVO.setOrderPayedQty(0);
        smOrderSummaryVO.setOrderUnPayQty(0);
        smOrderSummaryVO.setOrderCancelQty(0);
        final SmOrderQuery query1 = new SmOrderQuery();
        query1.setPage(0);
        query1.setSize(0);
        query1.setQueryPage(false);
        query1.setProductType("productType");
        query1.setCreateDateStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        mockOrderCommissionDetailMapper.queryLongCommissionSummary(query1);

        // Run the test
        commissionManagerServiceUnderTest.queryLongCommissionSummary(query);

    }

    @Test
    public void testInitReconciliationResult() {
        // Setup
        final SmLongInsuranceCommissionVO smLongInsuranceCommissionVO = new SmLongInsuranceCommissionVO();
        smLongInsuranceCommissionVO.setTermNum(0);
        smLongInsuranceCommissionVO.setAppStatus("appStatus");
        smLongInsuranceCommissionVO.setValidPeriod("validPeriod");
        smLongInsuranceCommissionVO.setProductId(0);
        smLongInsuranceCommissionVO.setPolicyNo("policyNo");
        smLongInsuranceCommissionVO.setPayPeriod("payPeriod");
        smLongInsuranceCommissionVO.setChannel("channel");
        smLongInsuranceCommissionVO.setInsuredIdNumber("insuredIdNumber");
        smLongInsuranceCommissionVO.setTotalAmount(new BigDecimal("0.00"));
        smLongInsuranceCommissionVO.setOriginalAmount(new BigDecimal("0.00"));
        smLongInsuranceCommissionVO.setConvertedAmount(new BigDecimal("0.00"));
        smLongInsuranceCommissionVO.setTaskPremium(new BigDecimal("0.00"));
        smLongInsuranceCommissionVO.setAddCommissionAmount(new BigDecimal("0.00"));
        smLongInsuranceCommissionVO.setFhOrderId("fhOrderId");
        smLongInsuranceCommissionVO.setPaymentTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smLongInsuranceCommissionVO.setReconciliationResult("describe");
        smLongInsuranceCommissionVO.setVisitStatus("visitStatus");
        smLongInsuranceCommissionVO.setSurrenderType("surrenderType");
        smLongInsuranceCommissionVO.setVisitTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        smLongInsuranceCommissionVO.setReceiptSignTime("receiptSignTime");
        final List<SmLongInsuranceCommissionVO> longInsuranceCommissionVos = Arrays.asList(smLongInsuranceCommissionVO);
        mockSmReconciliationPolicyMapper.getSmReconciliationNewestPolicy(LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), "channel", Arrays.asList("value"));
        mockSmReconciliationPolicyMapper.getPolicyKey("policyNo", "appStatus");

        // Run the test
        commissionManagerServiceUnderTest.initReconciliationResult(longInsuranceCommissionVos);

        // Verify the results
    }
}
