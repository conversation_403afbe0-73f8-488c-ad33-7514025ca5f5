package com.cfpamf.ms.insur.test.redis;

import org.junit.FixMethodOrder;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.MethodSorters;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.SetOperations;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("dev")
@FixMethodOrder(MethodSorters.NAME_ASCENDING)
public class RedisCollectionDiff {

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    @Test
    public void test() throws IOException {
        SetOperations<String, String> operations = redisTemplate.opsForSet();
        redisTemplate.delete("old1");
        redisTemplate.delete("new1");
        redisTemplate.delete("union");


        List<String> oldl = new ArrayList<>();
        List<String> new1 = new ArrayList<>();
        for (int i = 0; i < 200000; i++) {
            oldl.add("19008159C6ET1101_" + String.valueOf(i));
            new1.add("19008159C6ET1101_" + String.valueOf(i));
        }
        for (int i = 0; i < 50; i++) {
            oldl.add("19008159C6ET1101_" + i + "11111111");
            new1.add("19008159C6ET1101_" + i + "99999999");
        }
        operations.add("old1", oldl.toArray(new String[oldl.size()]));
        operations.add("new1", new1.toArray(new String[new1.size()]));

        long startTime = System.currentTimeMillis();
        Set<String> union = operations.union("old1", "new1");
        operations.add("union", new ArrayList<>(union).toArray(new String[union.size()]));
        Set<String> diff1 = operations.difference("union", "old1");
        Set<String> diff2 = operations.difference("union", "new1");
        System.out.println("redis cost time=" + (System.currentTimeMillis() - startTime) / 1000.00 + "  size=" + diff1.size());

        Set<String> oldSet = new HashSet<>(oldl);
        Set<String> newSet = new HashSet<>(new1);
        startTime = System.currentTimeMillis();
        List<String> diff = new ArrayList<>();
        for (String s1 : oldSet) {
            if (!newSet.contains(s1)) {
                diff.add(s1);
            }
        }

        for (String s1 : newSet) {
            if (!oldSet.contains(s1)) {
                diff.add(s1);
            }
        }
        System.out.println("setdiff cost time=" + (System.currentTimeMillis() - startTime) / 1000.00 + "  size=" + diff.size());

//        System.in.read();
    }
}
