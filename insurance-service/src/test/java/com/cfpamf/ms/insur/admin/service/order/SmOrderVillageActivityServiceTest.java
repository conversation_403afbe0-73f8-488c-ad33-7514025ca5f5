package com.cfpamf.ms.insur.admin.service.order;

import com.cfpamf.ms.insur.admin.dao.safes.order.SmOrderVillageActivityMapper;
import com.cfpamf.ms.insur.admin.external.whale.model.ContractBaseInfo;
import com.cfpamf.ms.insur.admin.external.whale.model.WhaleContract;
import com.cfpamf.ms.insur.admin.external.whale.model.WhaleContractActivity;
import com.cfpamf.ms.insur.admin.pojo.po.order.SmOrderVillageActivity;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class SmOrderVillageActivityServiceTest {

    @Mock
    private SmOrderVillageActivityMapper mockSmOrderVillageActivityMapper;

    @InjectMocks
    private SmOrderVillageActivityService smOrderVillageActivityServiceUnderTest;

    @Test
    public void testWhaleSynCreate() {
        // Setup
        final WhaleContract whaleContract = new WhaleContract();
        final ContractBaseInfo contractBaseInfo = new ContractBaseInfo();
        contractBaseInfo.setPolicyNo("test");
        whaleContract.setContractBaseInfo(contractBaseInfo);
        final WhaleContractActivity whaleContractActivity = new WhaleContractActivity();
        whaleContractActivity.setType("200");
        whaleContractActivity.setActivityCode("activityCode");
        whaleContractActivity.setVillageRepresentativeIdNumber("431123199712018017");
        whaleContractActivity.setVillageRepresentativeName("test");
        whaleContractActivity.setManagerIdNumber("4311231997120180172");
        whaleContractActivity.setManagerName("test2");
        whaleContractActivity.setManagerSource("1");
        whaleContractActivity.setManagerCode("managerCode");
        whaleContract.setPolicyActivities(Arrays.asList(whaleContractActivity));

        // Configure SmOrderVillageActivityMapper.select(...).
        final SmOrderVillageActivity smOrderVillageActivity = new SmOrderVillageActivity();
        smOrderVillageActivity.setFhOrderId("fhOrderId");
        smOrderVillageActivity.setActivityCode("activityCode");
        smOrderVillageActivity.setDeleted(0);
        smOrderVillageActivity.setCreateUser("SYSTEM");
        smOrderVillageActivity.setUpdateUser("SYSTEM");
        smOrderVillageActivity.setType("code");
        smOrderVillageActivity.setVillageRepresentativeIdNumber("villageRepresentativeIdNumber");
        smOrderVillageActivity.setVillageRepresentativeName("villageRepresentativeName");
        smOrderVillageActivity.setManagerIdNumber("managerIdNumber");
        smOrderVillageActivity.setManagerName("managerName");
        smOrderVillageActivity.setManagerSource("managerSource");
        smOrderVillageActivity.setManagerCode("managerCode");
        final List<SmOrderVillageActivity> smOrderVillageActivities = Arrays.asList(smOrderVillageActivity);
        final SmOrderVillageActivity t = new SmOrderVillageActivity();
        t.setFhOrderId("fhOrderId");
        t.setActivityCode("activityCode");
        t.setDeleted(0);
        t.setCreateUser("SYSTEM");
        t.setUpdateUser("SYSTEM");
        t.setType("code");
        t.setVillageRepresentativeIdNumber("villageRepresentativeIdNumber");
        t.setVillageRepresentativeName("villageRepresentativeName");
        t.setManagerIdNumber("managerIdNumber");
        t.setManagerName("managerName");
        t.setManagerSource("managerSource");
        t.setManagerCode("managerCode");
        when(mockSmOrderVillageActivityMapper.select(t)).thenReturn(smOrderVillageActivities);

        // Run the test
        smOrderVillageActivityServiceUnderTest.whaleSynCreate(whaleContract, "fhOrderIdtest");

        // Verify the results
    }

    @Test
    public void smOrderVillageActivityMapperSelectReturnsNoItems() {
        // Setup
        final WhaleContract whaleContract = new WhaleContract();
        final ContractBaseInfo contractBaseInfo = new ContractBaseInfo();
        contractBaseInfo.setPolicyNo("policyNo");
        whaleContract.setContractBaseInfo(contractBaseInfo);
        final WhaleContractActivity whaleContractActivity = new WhaleContractActivity();
        whaleContractActivity.setType("type");
        whaleContractActivity.setActivityCode("activityCode");
        whaleContractActivity.setVillageRepresentativeIdNumber("villageRepresentativeIdNumber");
        whaleContractActivity.setVillageRepresentativeName("villageRepresentativeName");
        whaleContractActivity.setManagerIdNumber("managerIdNumber");
        whaleContractActivity.setManagerName("managerName");
        whaleContractActivity.setManagerSource("managerSource");
        whaleContractActivity.setManagerCode("managerCode");
        whaleContract.setPolicyActivities(Arrays.asList(whaleContractActivity));

        // Configure SmOrderVillageActivityMapper.select(...).
        final SmOrderVillageActivity t = new SmOrderVillageActivity();
        t.setFhOrderId("fhOrderId");
        t.setActivityCode("activityCode");
        t.setDeleted(0);
        t.setCreateUser("SYSTEM");
        t.setUpdateUser("SYSTEM");
        t.setType("code");
        t.setVillageRepresentativeIdNumber("villageRepresentativeIdNumber");
        t.setVillageRepresentativeName("villageRepresentativeName");
        t.setManagerIdNumber("managerIdNumber");
        t.setManagerName("managerName");
        t.setManagerSource("managerSource");
        t.setManagerCode("managerCode");
        when(mockSmOrderVillageActivityMapper.select(t)).thenReturn(Collections.emptyList());

        // Run the test
        smOrderVillageActivityServiceUnderTest.whaleSynCreate(whaleContract, "fhOrderId");

        // Verify the results
        // Confirm SmOrderVillageActivityMapper.insert(...).
        final SmOrderVillageActivity t1 = new SmOrderVillageActivity();
        t1.setFhOrderId("fhOrderId");
        t1.setActivityCode("activityCode");
        t1.setDeleted(0);
        t1.setCreateUser("SYSTEM");
        t1.setUpdateUser("SYSTEM");
        t1.setType("code");
        t1.setVillageRepresentativeIdNumber("villageRepresentativeIdNumber");
        t1.setVillageRepresentativeName("villageRepresentativeName");
        t1.setManagerIdNumber("managerIdNumber");
        t1.setManagerName("managerName");
        t1.setManagerSource("managerSource");
        t1.setManagerCode("managerCode");
        verify(mockSmOrderVillageActivityMapper).insert(t1);
    }

    @Test
    public void testWhaleSynCreateCheckFour() {
        // Setup
        final WhaleContract whaleContract = new WhaleContract();
        final ContractBaseInfo contractBaseInfo = new ContractBaseInfo();
        contractBaseInfo.setPolicyNo("policyNo");
        whaleContract.setContractBaseInfo(contractBaseInfo);
        final WhaleContractActivity whaleContractActivity = new WhaleContractActivity();
        whaleContractActivity.setType("type");
        whaleContractActivity.setActivityCode("activityCode");
        whaleContractActivity.setVillageRepresentativeIdNumber("villageRepresentativeIdNumber");
        whaleContractActivity.setVillageRepresentativeName("villageRepresentativeName");
        whaleContractActivity.setManagerIdNumber("managerIdNumber");
        whaleContractActivity.setManagerName("managerName");
        whaleContractActivity.setManagerSource("managerSource");
        whaleContractActivity.setManagerCode("managerCode");
        whaleContract.setPolicyActivities(Arrays.asList(whaleContractActivity));

        // Run the test
        final boolean result = smOrderVillageActivityServiceUnderTest.whaleSynCreateCheckFour(whaleContract);

        // Verify the results
        assertThat(result).isFalse();
    }
}
