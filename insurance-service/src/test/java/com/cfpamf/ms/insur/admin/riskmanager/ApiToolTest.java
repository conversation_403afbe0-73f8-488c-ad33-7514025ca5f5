package com.cfpamf.ms.insur.admin.riskmanager;

import com.cfpamf.ms.insur.admin.pojo.open.ApiTool;
import com.cfpamf.ms.insur.admin.pojo.open.TKBizContent;
import com.cfpamf.ms.insur.admin.pojo.open.TKRequest;
import com.cfpamf.ms.insur.admin.pojo.open.TkResponse;
import com.cfpamf.ms.insur.weixin.common.IdGenerator;
import org.apache.commons.lang3.StringUtils;
import org.junit.Assert;
import org.junit.Test;

import java.util.Map;

public class ApiToolTest {

    private String bizSecret="MWvULJ0jAcA9mEhroTICfvdmJIquj+WN1U8Mx9zVyn8RFkyk37PvH5sZZOqsYwgtAA6DLs/7tod6oghCyvXFPAQtFVP7JN4pV9bg1rlJBmQRyaKYhvJkpE+YCIG8pU2YacWM7a/Rn+3Gg/t5gTgLucDmSiNgg73A63nAkeZ6VmwIDAQAB";

    private String appSecret="BiQKBgQCCu1BYNIOv12DONMzpYudctkUgSrDYLEnw9JPqCiB5LMnci1xKxDmECcXHQkrtdE1fpa24oeYD6tCOXlb+Rvp7oaIrc/BvcFOOqlpvAEHEVbisOM0rIc0ad4NaexDHWaErPrurUaDbCgc1yl5UxC4vyBPptLz0Vh2DlNC+Q7XXTZQIDAQAB";
    @Test
    public void parseField(){
        TKBizContent content =new TKBizContent();
        content.setOrderId("2121");
        content.setModelType("tk");
        content.setCidNumber("2323");
        Map fieldMap = ApiTool.parseField(content);
        System.err.println(fieldMap);
        Assert.assertTrue(fieldMap.size()==0);
    }
    @Test
    public void parseField2(){
        TKRequest content =new TKRequest();
        content.setData("123");
        content.setSource("tk");
        content.setRequest_no(IdGenerator.getUuid());
        content.setSign("123");
        Map fieldMap = ApiTool.parseField(content);
        System.err.println(fieldMap);
        Assert.assertTrue(fieldMap.size()==4);
    }

    @Test
    public void parseData(){
        String data="26hqcmv+W4Lv8C0yEOTCyTQBeEDwMfta9R0zzqLoSHhxG/6kD+SbkuLpxKBRih7Qb+oKkaSN8Wh+2ZBGGPx3XuS8fHe8FgfxO+fF7Om2f0+nEIkt7psU/45AB5cxXYI30n0J1fxTawCOX6coj8ift/PLwLcoxURi+tDo3wjY7vYlpwGgqW5DiQG2YxuZ0Sfn";
        String fieldMap = ApiTool.decrypt(data,bizSecret);
        System.err.println(fieldMap);
        Assert.assertTrue(StringUtils.isNotBlank(fieldMap));
    }

    @Test
    public void verifyResponse(){
        TkResponse response = new TkResponse();
        response.setMessage("success");
        response.setSign("YAsstB2s8ztdQUaldvYZbw==");
        response.setCode("200");
        response.setData("26hqcmv+W4Lv8C0yEOTCyTQBeEDwMfta9R0zzqLoSHhxG/6kD+SbkuLpxKBRih7Qb+oKkaSN8Wh+2ZBGGPx3XuS8fHe8FgfxO+fF7Om2f0+nEIkt7psU/45AB5cxXYI30n0J1fxTawCOX6coj8ift/PLwLcoxURi+tDo3wjY7vYlpwGgqW5DiQG2YxuZ0Sfn");
        response.setRequest_no("86b7f0d476ce4193bdaa0aab1ccbdc58");

        boolean rtn = response.verify(appSecret);
        System.err.println(rtn);
        Assert.assertTrue(rtn);
    }

    @Test
    public void encryptAndDecrypt(){
        TKBizContent content =new TKBizContent();
        content.setOrderId("2121");
        content.setModelType("tk");
        content.setCidNumber("2323");
        String requestNo = IdGenerator.getUuid();
        String timestamp = System.currentTimeMillis()+"";

        TKRequest request = new TKRequest();
        request.setTimestamp(timestamp);
        request.setRequest_no(requestNo);
        request.setSource("tk");
        request.encrypt(content,bizSecret,appSecret);

        boolean rtn = request.verify(appSecret);
        Assert.assertTrue(rtn);
    }
}
