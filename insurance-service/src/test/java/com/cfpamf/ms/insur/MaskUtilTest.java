package com.cfpamf.ms.insur;

import com.alibaba.fastjson.JSON;
import com.cfpamf.ms.insur.admin.external.Beneficiary;
import com.cfpamf.ms.insur.admin.external.OrderQueryResponse;
import com.cfpamf.ms.insur.admin.external.OrderReceiver;
import com.cfpamf.ms.insur.admin.external.OrderRenewBindInfo;
import com.cfpamf.ms.insur.admin.pojo.dto.order.SmOrderCarInfoDTO;
import com.cfpamf.ms.insur.admin.pojo.dto.order.SmOrderDistributionDTO;
import com.cfpamf.ms.insur.admin.pojo.dto.order.SmOrderHouseDTO;
import com.cfpamf.ms.insur.admin.pojo.vo.SmPropertyVO;
import com.cfpamf.ms.insur.admin.receiver.vo.SmOrderReceiverInfoVo;
import com.cfpamf.ms.insur.admin.service.BaseTest;
import com.cfpamf.ms.insur.base.util.DataMaskUtil;
import com.cfpamf.ms.insur.base.util.HttpRequestUtil;
import com.cfpamf.ms.insur.weixin.pojo.vo.WxOrderQueryDetailVO;
import com.cfpamf.ms.insur.weixin.pojo.vo.claim.evaluation.ClaimEvaluationVo;
import com.github.jsonzou.jmockdata.DataConfig;
import com.github.jsonzou.jmockdata.JMockData;
import com.github.jsonzou.jmockdata.MockConfig;
import com.github.jsonzou.jmockdata.Mocker;
import com.itextpdf.text.pdf.qrcode.MaskUtil;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@RunWith(PowerMockRunner.class)
@PrepareForTest({HttpRequestUtil.class})
public class MaskUtilTest extends BaseTest{

    @Test
    public void mask(){
        WxOrderQueryDetailVO vo = new WxOrderQueryDetailVO();
        OrderQueryResponse.AppntInfo  appntInfo = new OrderQueryResponse.AppntInfo();
        appntInfo.setAddress("中华人民共和国北京天安门北门");
        appntInfo.setAppntIdNo("432509199305204109");
        appntInfo.setAppntEmail("<EMAIL>");
        appntInfo.setAppntMobile("***********");
        appntInfo.setAreaName("湖南省长沙市岳麓区芯城科技园");
        appntInfo.setAddress("湖南省长沙市岳麓区芯城科技园");
        vo.setAppntInfo(appntInfo);
        List<OrderQueryResponse.InsuredInfo> insureds=new ArrayList<>();
        OrderQueryResponse.InsuredInfo entry = new OrderQueryResponse.InsuredInfo();
        entry.setAddress("湖南省长沙市岳麓区芯城科技园");
        entry.setAreaName("湖南省长沙市岳麓区芯城科技园");
        entry.setInsuredIdNo("432509199305204109");
        insureds.add(entry);
        vo.setInsuredInfos(insureds);

        MockConfig config = new MockConfig();
        config.registerMocker(new Mocker<LocalDateTime>() {
                                    @Override
                                    public LocalDateTime mock(DataConfig mockConfig) {
                                        return LocalDateTime.now();
                                    }
                                },
                        LocalDateTime.class);

        SmOrderReceiverInfoVo v2 = JMockData.mock(SmOrderReceiverInfoVo.class,config);
        DataMaskUtil.maskObject(v2);
        System.err.println(JSON.toJSON(v2));

    }

    @Test
    public void mockOne(){
        ClaimEvaluationVo vo = JMockData.mock(ClaimEvaluationVo.class);
        System.err.println(JSON.toJSONString(vo));
    }

    @Test
    public void testReg(){
                LocalDateTime t1 = LocalDateTime.now();
        System.out.println(t1.getClass().getName().matches("java2.*"));
        String reg =  "(\\d)(\\w+)(\\w{2})";
        String idNo = "432509199305204109";
        System.err.println(idNo.replaceAll(reg,"$1*********$3"));

        String reg2 = "\\d+(\\d{4})";
        String bankNo ="************";
        System.err.println(bankNo.replaceAll(reg2,"********$1"));
        System.err.println(DataMaskUtil.maskBank(bankNo));

        String hourseNo ="************";
        String reg3 = "(\\w{1})\\w+(\\w{2})";
        System.err.println( hourseNo.replaceAll(reg3,"******"));

            String address="湖南省新城科技园";
        String address2="长沙市巴音郭楞蒙古自治州";
        String areg = "^(\\S+省)(\\S+市)?(\\S+区|\\S+县)?\\S+$";
        System.err.println( address.matches(areg));
        System.err.println( address.replaceAll(areg,"$1$2$3*********"));
    }

    private void revertVo(WxOrderQueryDetailVO data) {
        OrderQueryResponse.AppntInfo appntInfo = data.getAppntInfo();
        appntInfo.setAreaName("湖南省长沙市岳麓区芯城科技园");
        appntInfo.setAppntMobile("***********");
        appntInfo.setAppntEmail("<EMAIL>");
        appntInfo.setAddress("湖南省长沙市岳麓区芯城科技园");
        data.setAppntInfo(appntInfo);

        List<OrderQueryResponse.InsuredInfo> insuredInfos=data.getInsuredInfos();
        for(OrderQueryResponse.InsuredInfo entry:insuredInfos){
            entry.setInsuredIdNo("******************");
            entry.setAreaName("湖南省长沙市岳麓区芯城科技园");
            entry.setAddress("湖南省长沙市岳麓区芯城科技园");
            entry.setInsuredEmail("<EMAIL>");
            entry.setInsuredMobile("***********");
        }
        data.setInsuredInfos(insuredInfos);

        SmPropertyVO propInfo = data.getPropertyInfo();
        propInfo.setHourseNo("2jkj2k3h");
        propInfo.setPropertyAdress("湖南省长沙市岳麓区芯城科技园");
        propInfo.setCarPlateNo("2jkj2k3hsadfaf23");
        propInfo.setCarDrivingNo("2jkj2k3hsadfaf23");
        propInfo.setLicenseNumber("2jkj2k3hsadfaf23");
        propInfo.setChassisNumber("2jkj2k3hsadfaf23");
        propInfo.setEngineNo("2jkj2k3hsadfaf23");
        propInfo.setHouseKeepingCensusAddress("湖南省长沙市岳麓区芯城科技园");
        propInfo.setHouseKeepingerIDNo("******************");
        propInfo.setHouseKeepingerAddress("湖南省长沙市岳麓区芯城科技园");
        data.setPropertyInfo(propInfo);

        SmOrderCarInfoDTO carInfoDTO = data.getCarInfoDTO();
        carInfoDTO.setCarPlateNo("湘A.88888");
        carInfoDTO.setChassisNumber("2jkj2k3hsadfaf23");
        carInfoDTO.setEngineNo("sdfjljfsdf");
        data.setCarInfoDTO(carInfoDTO);

        SmOrderHouseDTO houseDTO = data.getHouseDTO();
        houseDTO.setHouseNo("2jkj2k3hsadfaf23");
        data.setHouseDTO(houseDTO);

        List<Beneficiary> beneficiaryList = data.getBeneficiaryList();
        for(Beneficiary entry:beneficiaryList){
            entry.setBeneIdNumber("2jkj2k3hsadfaf23");
            entry.setBeneCell("2jkj2k3hsadfaf23");
            entry.setBeneEmail("<EMAIL>");
        }
        data.setBeneficiaryList(beneficiaryList);


        OrderReceiver orderReceiver = data.getOrderReceiver();
        orderReceiver.setReceiverCell("***********");
        orderReceiver.setReceiverAddress("湖南省长沙市岳麓区芯城科技园");
        data.setOrderReceiver(orderReceiver);

        OrderRenewBindInfo orderRenewBindInfo = data.getOrderRenewBindInfo();
        orderRenewBindInfo.setAccountNo("****************");
        data.setOrderRenewBindInfo(orderRenewBindInfo);

        SmOrderDistributionDTO orderDistributionDTO = data.getOrderDistributionDTO();
        orderDistributionDTO.setMobile("***********");
        data.setOrderDistributionDTO(orderDistributionDTO);
    }
}
