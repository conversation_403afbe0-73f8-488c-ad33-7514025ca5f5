package com.cfpamf.ms.insur.admin.external.zhongan;

import com.cfpamf.ms.insur.admin.constant.SmConstants;
import com.cfpamf.ms.insur.admin.dao.safes.AuthUserMapper;
import com.cfpamf.ms.insur.admin.dao.safes.SmOrderMapper;
import com.cfpamf.ms.insur.admin.dao.safes.order.SmOrderExtendZaMapper;
import com.cfpamf.ms.insur.admin.enums.EnumChannel;
import com.cfpamf.ms.insur.admin.external.AICheckQueryRequest;
import com.cfpamf.ms.insur.admin.external.OrderSubmitRequest;
import com.cfpamf.ms.insur.admin.external.OrderSubmitResponse;
import com.cfpamf.ms.insur.admin.external.common.model.OrderPreAiCheckResp;
import com.cfpamf.ms.insur.admin.external.zhongan.api.ZaApiService;
import com.cfpamf.ms.insur.admin.external.zhongan.model.ZaCheckRes;
import com.cfpamf.ms.insur.admin.external.zhongan.model.group.ZaGroupEndorsementRes;
import com.cfpamf.ms.insur.admin.external.zhongan.model.group.ZaGroupHoldInfo;
import com.cfpamf.ms.insur.admin.external.zhongan.model.group.ZaGroupOrderInfo;
import com.cfpamf.ms.insur.admin.external.zhongan.model.group.ZaGroupOrderRes;
import com.cfpamf.ms.insur.admin.external.zhongan.model.notify.ZaAcceptNotify;
import com.cfpamf.ms.insur.admin.external.zhongan.model.notify.ZaCustomerDTO;
import com.cfpamf.ms.insur.admin.external.zhongan.model.wenjuan.ZaCreateFamilyQuestionnaireBody;
import com.cfpamf.ms.insur.admin.pojo.dto.SmCreateOrderSubmitRequest;
import com.cfpamf.ms.insur.admin.pojo.vo.SmPlanVO;
import com.cfpamf.ms.insur.admin.service.BaseTest;
import com.cfpamf.ms.insur.admin.service.OrderNoGenerator;
import com.cfpamf.ms.insur.base.util.HttpRequestUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.jsonzou.jmockdata.JMockData;
import lombok.Getter;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 * 中华联合保险接口适配器 单元测试
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({HttpRequestUtil.class})
public class ZaOrderServiceAdapterTest extends BaseTest {
    @InjectMocks
    ZaOrderServiceAdapter zaOrderServiceAdapter;
    @Mock
    SmOrderMapper orderMapper;
    @Mock
    SmOrderExtendZaMapper extendZaMapper;

    @Mock
    protected ZaApiProperties zaApiProperties;
    @Mock
    ZaApiService apiService;
    @Mock
    ObjectMapper jsonMapper;
    @Mock
    AuthUserMapper userMapper;
    @Mock
    OrderNoGenerator orderNoGenerator;


    /**
     * 提交订单
     * 【场景】：订单-带智能核保-成功响应场景
     */
    @Test
    public void submitChannelOrder() {

        OrderSubmitRequest orderSubmitRequest = new OrderSubmitRequest();
        String questionnaireId = "智能核保问卷id";
        orderSubmitRequest.setQuestionnaireId(questionnaireId);
        //mock核保返回
        String channelOrderNo = "渠道订单号";
        ZaCheckRes zaCheckRes = new ZaCheckRes();
        zaCheckRes.setChannelOrderNo(channelOrderNo);
        Mockito.when(apiService.check(orderSubmitRequest)).thenReturn(zaCheckRes);
        Mockito.when(zaApiProperties.getOrderOutType()).thenReturn("non");
        OrderSubmitResponse response = zaOrderServiceAdapter.submitChannelOrder(orderSubmitRequest);

        //验证行为
//        Mockito.verify(extendZaMapper);
        //验证数据
//        Assert.assertEquals("non",orderSubmitRequest.getOrderOutType() );
        Assert.assertEquals(response.getAppNo(), channelOrderNo);
        Assert.assertEquals(response.getOrderId(), channelOrderNo);
        Assert.assertEquals(response.getNoticeCode(), SmConstants.SM_ORDER_API_INVOKE_SUCCESS);
    }

    /**
     * 提交订单
     * 【场景】：订单-不带带智能核保-成功响应场景
     */
    @Test
    public void submitChannelOrderNoQuestionnaireId() {

        OrderSubmitRequest orderSubmitRequest = new OrderSubmitRequest();

        //mock核保返回
        String channelOrderNo = "渠道订单号";
        ZaCheckRes zaCheckRes = new ZaCheckRes();
        zaCheckRes.setChannelOrderNo(channelOrderNo);
        Mockito.when(apiService.check(orderSubmitRequest)).thenReturn(zaCheckRes);
        OrderSubmitResponse response = zaOrderServiceAdapter.submitChannelOrder(orderSubmitRequest);

        //验证行为
        Mockito.verifyZeroInteractions(extendZaMapper);
        //验证数据
//        Assert.assertEquals(orderSubmitRequest.getOrderOutType(), "non");
        Assert.assertEquals(response.getAppNo(), channelOrderNo);
        Assert.assertEquals(response.getOrderId(), channelOrderNo);
        Assert.assertEquals(response.getNoticeCode(), SmConstants.SM_ORDER_API_INVOKE_SUCCESS);
    }

    @Test
    public void aiCheck() {
        //mock订单号
        String orderId = "orderId";
        Mockito.when(orderNoGenerator.getNextNo(EnumChannel.ZA.getCode())).thenReturn(orderId);
        //mock ZaCreateFamilyQuestionnaireBody
        ZaCreateFamilyQuestionnaireBody familyQuestionnaireBody = new ZaCreateFamilyQuestionnaireBody();
        String familyQuestionnaireId = "familyQuestionnaireId";
        String redirectPage = "redirectPage";
        familyQuestionnaireBody.setFamilyQuestionnaireId(familyQuestionnaireId);
        familyQuestionnaireBody.setRedirectPage(redirectPage);
        Mockito.when(apiService.createFamily(Mockito.any(), Mockito.any())).thenReturn(familyQuestionnaireBody);


        OrderPreAiCheckResp orderPreAiCheckResp = zaOrderServiceAdapter.aiCheck(JMockData.mock(OrderSubmitRequest.class, con()), ",RTPDu,");
        //验证行为
        Mockito.verify(extendZaMapper).insert(Mockito.any());
        //验证数据
        Assert.assertEquals(orderPreAiCheckResp.getOrderId(), orderId);
        Assert.assertEquals(orderPreAiCheckResp.getQuestionnaireId(), familyQuestionnaireId);
        Assert.assertEquals(orderPreAiCheckResp.getRedirectPage(), redirectPage);
    }

    @Test
    public void aiCheckQuery() {
        try {
            zaOrderServiceAdapter.aiCheckQuery(JMockData.mock(AICheckQueryRequest.class));
        } catch (Exception e) {

        }
    }

    @Test
    public void decryptAes() throws IOException {
        try {
            zaOrderServiceAdapter.decryptAes(",lKEMI,");
        } catch (Exception e) {

        }
    }

    @Test
    public void decryptRefundAes() throws IOException {
        try {
            zaOrderServiceAdapter.decryptRefundAes(",kLHpH,");
        } catch (Exception e) {

        }
    }

    @Test
    public void cvtNotify() {
        try {
            zaOrderServiceAdapter.cvtNotify(JMockData.mock(ZaAcceptNotify.class));
        } catch (Exception e) {

        }
    }

    @Test
    public void cvtPeople() {
        try {
            ZaCustomerDTO mock = JMockData.mock(ZaCustomerDTO.class);
            mock.setCertNo("110101199003071516");
            mock.setCertType("I");
            zaOrderServiceAdapter.cvtPeople(mock);
        } catch (Exception e) {

        }
    }

    @Test
    public void decrypt() {
        try {
            zaOrderServiceAdapter.decrypt(",EoZGL,");
        } catch (Exception e) {

        }
    }

    @Test
    public void testQueryChannelGroupOrder() {
        String groupPolicyNo = "团单号";
        ZaGroupOrderRes groupOrderRes = JMockData.mock(ZaGroupOrderRes.class);
        Mockito.when(apiService.queryGroupOrder(groupPolicyNo)).thenReturn(groupOrderRes);
        zaOrderServiceAdapter.queryChannelGroupOrder(groupPolicyNo);
    }
    @Test
    public void testQueryChannelGroupEndorsement() {
        String groupPolicyNo = "批单号";
        ZaGroupEndorsementRes groupOrderRes = JMockData.mock(ZaGroupEndorsementRes.class);
        Mockito.when(apiService.queryGroupEndorsement(groupPolicyNo)).thenReturn(groupOrderRes);
        zaOrderServiceAdapter.queryChannelGroupEndorsement(groupPolicyNo);
    }
    @Test
    public void testCvtByGroupOrderNotify() {
        try{

            String orderId = "orderId";
            ZaGroupOrderRes groupOrderRes = getZaGroupOrderRes();


            Mockito.when(orderNoGenerator.getNextNo(EnumChannel.ZA.getCode())).thenReturn(orderId);
            SmCreateOrderSubmitRequest submitRequest = zaOrderServiceAdapter.cvtByGroupOrderNotify(groupOrderRes,getPlanMap(),Mockito.anyList(),"CNBJ0409");
            //验证数据
            Assert.assertEquals(submitRequest.getFhOrderId(), orderId);
            System.out.println(submitRequest.getChannel());
            System.out.println(submitRequest.getCustomerAdminJobCode());
        }catch (Exception e){

        }
    }
    @Test
    public void testCvtByGroupEndorsementNotify() {
    }
    @Test
    public void testCvtByGroupBatchIncrease() {
    }
    @Test
    public void testCvtListByGroupBatchDecrease() {
    }
    @Test
    public void testCvtByGroupBatchDecrease() {
    }
    private ZaGroupOrderRes getZaGroupOrderRes(){
        ZaGroupOrderRes groupOrderRes = JMockData.mock(ZaGroupOrderRes.class);
        ZaGroupOrderInfo zaGroupOrderInfo = JMockData.mock(ZaGroupOrderInfo.class);
        zaGroupOrderInfo.setHaGroupPolicyNo("ZA001001");
        zaGroupOrderInfo.setApplyTime("");
        zaGroupOrderInfo.setExpireTime("");
        zaGroupOrderInfo.setInsuredList(Mockito.anyList());
        zaGroupOrderInfo.setPolicyAmount(new BigDecimal(1000));

        ZaGroupHoldInfo policyHolder = new ZaGroupHoldInfo();
        zaGroupOrderInfo.setPolicyHolder(policyHolder);


        zaGroupOrderInfo.setPolicyPlanList(Mockito.anyList());
        zaGroupOrderInfo.setValidateTime("");
        groupOrderRes.setResult(zaGroupOrderInfo);
        return groupOrderRes;
    }
    private Map<String, SmPlanVO> getPlanMap(){
        Map<String, SmPlanVO> planMap = new HashMap<>();
        SmPlanVO smPlanVO = JMockData.mock(SmPlanVO.class);
        smPlanVO.setChannel(EnumChannel.ZA.getCode());
        smPlanVO.setProductId(126);
        planMap.put("ZATX001",smPlanVO);
        return planMap;
    }

    /*****begin s50 续保 ***/
    @Test
    public void genToRenewalUrl() throws Exception {
        String expectValue = "https://ihealth-test.zhongan.com/api/marigold/mobile/renewal/gateWay?channelCode=c20219741225001&notifyContent=CNBJ0409&renewPolicyNo=IH1100014160223912&sign=6c196344b65d1204bad660dd6f54be5d";
        ZaApiProperties zaApiProperties = new ZaApiProperties();
        zaApiProperties.setBindChannelCode("c20219741225001");
        zaApiProperties.setBindSignKey("GH7VM9xMSj");
        zaApiProperties.setRenewalH5Url("https://ihealth-test.zhongan.com/api/marigold/mobile/renewal/gateWay?");
        Mockito.when(apiService.getZaApiProperties()).thenReturn(zaApiProperties);
        String url = zaOrderServiceAdapter.genToRenewalUrl("IH1100014160223912","CNBJ0409",null);
        Assert.assertEquals(expectValue,url);
    }
    /*****end s50 续保 ***/
}
