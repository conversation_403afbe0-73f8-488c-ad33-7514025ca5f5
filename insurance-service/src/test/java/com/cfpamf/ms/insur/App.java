package com.cfpamf.ms.insur;

import com.cfpamf.cmis.common.utils.IdcardUtils;
import com.cfpamf.ms.insur.admin.external.whale.model.ChangeChannelReferrerContext;
import com.cfpamf.ms.insur.base.util.LocalDateUtil;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class App {
    public static void main(String[] args) {
//        catRecommendId("钟小福-CNJB001");
        ChangeChannelReferrerContext context = buildContext();
        Thread t1 = new Thread(()->{
            c1(context);
        });
        Thread t2 = new Thread(()->{
            c2(context);
        });
        Thread t3 = new Thread(()->{
            c3(context);
        });

        t1.start();
        t2.start();
        t3.start();
    }

    public static ChangeChannelReferrerContext buildContext(){
        return ChangeChannelReferrerContext.builder()
                .emit(new AtomicInteger(1))
                .build();

    }

    public static void c1(ChangeChannelReferrerContext context){

        for(int i = 0;i<10;i++){
            int a =  context.getEmit().addAndGet(1);
            System.out.println("C1累加值："+a);
        }
    }
    public static void c2(ChangeChannelReferrerContext context){

        for(int i = 0;i<10;i++){
            int a =  context.getEmit().addAndGet(1);
            System.out.println("C2累加值："+a);
        }
    }

    public static void c3(ChangeChannelReferrerContext context){

        for(int i = 0;i<10;i++){
            int a =  context.getEmit().addAndGet(1);
            System.out.println("C3累加值："+a);
        }
    }


    private static final Pattern p = Pattern.compile("(\\S+)-(\\S+)");
    private static void catRecommendId(String recommendId) {
        Matcher matcher = p.matcher(recommendId);
        while (matcher.find()){
            System.out.println(matcher.group(0));
            System.out.println(matcher.group(1));
            System.out.println(matcher.group(2));
        }
    }

    private static String getBirthday(String idNumber) {
        String birthday = IdcardUtils.getBirthByIdCard(idNumber);
        if (StringUtils.isNotBlank(birthday)) {
            Date birthdayCopy = LocalDateUtil.stringToUdate(birthday, "yyyyMMdd");
            return LocalDateUtil.format4Simple(birthdayCopy);
        }
        return null;
    }

    public static Class getClassName(Object a) {
        if (a == null) {
            return null;
        }
        return a.getClass();
    }
}
