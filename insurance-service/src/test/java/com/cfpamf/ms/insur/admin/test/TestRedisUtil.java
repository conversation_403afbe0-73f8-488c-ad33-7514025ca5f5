package com.cfpamf.ms.insur.admin.test;

import com.cfpamf.ms.insur.base.util.RedisUtil;
import org.junit.FixMethodOrder;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.MethodSorters;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Set;

/**
 * <AUTHOR>
 **/
/*@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("dev")
@FixMethodOrder(MethodSorters.NAME_ASCENDING)*/
public class TestRedisUtil {

    /*@Autowired
    private RedisUtil<String, String> redisUtil;

    @Test
    public void test() {
        redisUtil.set("pppp", "1");
        Set<String> hotPageKeys = redisUtil.keys("*");
        for (Object key : hotPageKeys) {
            redisUtil.remove(key.toString());
        }
    }

    @Test
    public void testHash() {
        redisUtil.hashSet("test", "test", "test");
    }


    @Test
    public void testExpire() {
        String key = "aaaaa";
//        redisUtil.set(key, key);
//        redisUtil.expire(key, 2L);
//        try {
//            Thread.sleep(5000);
//        } catch (Exception e) {
//
//        }
        long expire = redisUtil.getExpire(key);
        String value = redisUtil.get(key);
        System.out.println();
    }
*/
}
