package com.cfpamf.ms.insur.weixin.service.renewal;
import com.google.common.collect.Maps;
import java.math.BigDecimal;

import com.cfpamf.ms.insur.admin.pojo.vo.SmPlanVO;
import com.cfpamf.ms.insur.admin.service.BaseTest;
import com.google.common.collect.Lists;

import com.cfpamf.ms.insur.admin.renewal.dao.RenewalConfigMapper;
import com.cfpamf.ms.insur.admin.renewal.form.RenewalConfigForm;
import com.cfpamf.ms.insur.admin.renewal.form.RenewalConfigSearchForm;
import com.cfpamf.ms.insur.admin.renewal.service.impl.RenewalConfigServiceImpl;
import com.cfpamf.ms.insur.admin.renewal.vo.PlanRenewalConfigVo;
import com.cfpamf.ms.insur.admin.renewal.vo.ProductRenewalConfigVo;
import com.cfpamf.ms.insur.admin.service.SmProductService;
import com.cfpamf.ms.insur.base.util.HttpRequestUtil;
import com.github.pagehelper.PageInfo;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/11/5 9:37
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({HttpRequestUtil.class})
public class RenewalConfigServiceImplTest extends BaseTest {

    @Mock
    RenewalConfigMapper renewalConfigMapper;

    @Mock
    SmProductService smProductService;

    @InjectMocks
    RenewalConfigServiceImpl renewalConfigService;

    /**
     * 保存续保配置
     *
     * @param
     * @return
     */
    @Test
    public void saveTest() {
        RenewalConfigForm renewalConfigForm = new RenewalConfigForm();
        renewalConfigForm.setProductId(0);
        renewalConfigForm.setId(0);
        renewalConfigForm.setPlanId(1);
        renewalConfigForm.setBeforeExpirationDay(-3);
        renewalConfigForm.setAfterExpirationDay(10);
        renewalConfigForm.setRenewalProductIdList(Lists.newArrayList());
        renewalConfigForm.setRenewalPlanIdList(Lists.newArrayList());
        renewalConfigForm.setRenewalHint("");
        renewalConfigForm.setChangeProductIdList(Lists.newArrayList());
        renewalConfigForm.setChangePlanIdList(Lists.newArrayList());
        renewalConfigForm.setChangeHint("");
        renewalConfigForm.setBreakGuaranteeHint("");
        renewalConfigForm.setRecommendPlanId(0);

        SmPlanVO smPlanVO = new SmPlanVO();
        smPlanVO.setId(0);
        smPlanVO.setPlanId(1);
        smPlanVO.setProductStatus(0);
        smPlanVO.setChannel("");
        smPlanVO.setProductId(0);
        smPlanVO.setProductName("");
        smPlanVO.setPlanName("");
        smPlanVO.setCompanyName("");
        smPlanVO.setFhProductId("");
        smPlanVO.setPaymentProportion(new BigDecimal("0"));
        smPlanVO.setBuyLimit(0);
        smPlanVO.setPlanOrderOutType("");
        smPlanVO.setMinPrice(new BigDecimal("0"));
        smPlanVO.setMinPremium("");
        smPlanVO.setCompanyId("");
        smPlanVO.setPlanCode("");

        Mockito.when(smProductService.getActivePlanById(1)).thenReturn(smPlanVO);
        renewalConfigService.save(renewalConfigForm);

    }

    /**
     * 搜索续保配置列表
     *
     * @param
     * @return
     */
    @Test
    public void searchTest() {
        RenewalConfigSearchForm renewalConfigSearchForm = new RenewalConfigSearchForm();
        renewalConfigSearchForm.setProductName("");
        renewalConfigSearchForm.setProductStatus(0);
        renewalConfigSearchForm.setRenewalProductId("");
        renewalConfigSearchForm.setSortType("");
        renewalConfigSearchForm.setSortField("");
        renewalConfigSearchForm.setPageNo(1);
        renewalConfigSearchForm.setPageSize(10);
        renewalConfigService.search(renewalConfigSearchForm);
    }

    /**
     * 获取产品续保配置详情
     *
     * @param
     * @return
     */
    @Test
    public void getProductRenewalConfigByProductIdTest() {
        PlanRenewalConfigVo planRenewalConfigVo = new PlanRenewalConfigVo();
        planRenewalConfigVo.setId(0L);
        planRenewalConfigVo.setPlanId(1);
        planRenewalConfigVo.setPlanName("");
        planRenewalConfigVo.setProductId(0);
        planRenewalConfigVo.setProductName("");
        planRenewalConfigVo.setProductStatus("");
        planRenewalConfigVo.setBeforeExpirationDay(0);
        planRenewalConfigVo.setAfterExpirationDay(0);
        planRenewalConfigVo.setRenewalProductIdList("1,2");
        planRenewalConfigVo.setRenewalPlanIdList("1,2");
        planRenewalConfigVo.setRenewalPlanVoList(Lists.newArrayList());
        planRenewalConfigVo.setRenewalPlanVoMap(Maps.newHashMap());
        planRenewalConfigVo.setRenewalHint("");
        planRenewalConfigVo.setChangeProductIdList("1,2");
        planRenewalConfigVo.setChangePlanIdList("1,2");
        planRenewalConfigVo.setChangePlanVoList(Lists.newArrayList());
        planRenewalConfigVo.setChangePlanVoMap(Maps.newHashMap());
        planRenewalConfigVo.setChangeHint("");
        planRenewalConfigVo.setBreakGuaranteeHint("");
        planRenewalConfigVo.setRecommendPlanId(0);
        planRenewalConfigVo.setRecommendPlanVo(new SmPlanVO());

        Mockito.when(renewalConfigMapper.findPlanRenewalConfigVoByProductId(1)).thenReturn(Lists.newArrayList(planRenewalConfigVo));
        renewalConfigService.getProductRenewalConfigByProductId(1);

    }

    /**
     * 获取产品续保配置详情
     *
     * @param
     * @return
     */
    @Test
    public void getPlanRenewalConfigByPlanIdTest() {
        PlanRenewalConfigVo planRenewalConfigVo = new PlanRenewalConfigVo();
        planRenewalConfigVo.setId(0L);
        planRenewalConfigVo.setPlanId(1);
        planRenewalConfigVo.setPlanName("");
        planRenewalConfigVo.setProductId(0);
        planRenewalConfigVo.setProductName("");
        planRenewalConfigVo.setProductStatus("");
        planRenewalConfigVo.setBeforeExpirationDay(0);
        planRenewalConfigVo.setAfterExpirationDay(0);
        planRenewalConfigVo.setRenewalProductIdList("1,2");
        planRenewalConfigVo.setRenewalPlanIdList("1,2");
        planRenewalConfigVo.setRenewalPlanVoList(Lists.newArrayList());
        planRenewalConfigVo.setRenewalPlanVoMap(Maps.newHashMap());
        planRenewalConfigVo.setRenewalHint("");
        planRenewalConfigVo.setChangeProductIdList("1,2");
        planRenewalConfigVo.setChangePlanIdList("1,2");
        planRenewalConfigVo.setChangePlanVoList(Lists.newArrayList());
        planRenewalConfigVo.setChangePlanVoMap(Maps.newHashMap());
        planRenewalConfigVo.setChangeHint("");
        planRenewalConfigVo.setBreakGuaranteeHint("");
        planRenewalConfigVo.setRecommendPlanId(0);
        planRenewalConfigVo.setRecommendPlanVo(new SmPlanVO());

        Mockito.when(renewalConfigMapper.getPlanRenewalConfigByPlanId(1)).thenReturn(planRenewalConfigVo);

        renewalConfigService.getPlanRenewalConfigByPlanId(1);

    }

}
