package com.cfpamf.ms.insur.test;

import com.cfpamf.ms.insur.base.util.Base64Util;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;
import org.junit.Test;

import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR> 2020/6/8 10:36
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class TestBase64 {

    @Test
    public void testEncode(){
        System.err.println(Base64Util.encryptBASE64("http://weixin.yongtongbx.com/wx/#/userHome".getBytes(StandardCharsets.UTF_8)));
    }

    public void testPOi(){
//        JxlsHelper.getInstance().processTemplate(getTemplateFileInputStream(templateName), os,
//                new Context(xlsParams));
    }
}
