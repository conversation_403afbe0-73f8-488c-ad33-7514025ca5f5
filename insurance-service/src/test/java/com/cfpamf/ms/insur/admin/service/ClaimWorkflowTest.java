package com.cfpamf.ms.insur.admin.service;

import org.junit.Assert;
import org.junit.FixMethodOrder;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.MethodSorters;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

/*@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("dev")
@FixMethodOrder(MethodSorters.NAME_ASCENDING)*/
public class ClaimWorkflowTest {

   /* @Autowired
    private ClaimWorkflow workflow;

    @Test
    public void goToNextStep() {
        ClaimWorkflow.Step step = workflow.goToNextStep("stepAppear", "appeared");
        Assert.assertEquals(step.getSCode(), "stepReport");

        step = workflow.goToNextStep("stepReport", "reported2");
        Assert.assertEquals(step.getSCode(), "stepDataPrepare");

        step = workflow.goToNextStep("stepDataPrepare", "dataSubmitted");
        Assert.assertEquals(step.getSCode(), "stepDataCheckByPic");

        step = workflow.goToNextStep("stepDataPrepare", "claimCanceled");
        Assert.assertEquals(step.getSCode(), "stepFinish");

        step = workflow.goToNextStep("stepToPay", "payed");
        Assert.assertEquals(step.getSCode(), "stepFinish");
        step = workflow.goToNextStep("stepToPay", "payRejected");
        Assert.assertEquals(step.getSCode(), "stepFinish");

    }*/
}