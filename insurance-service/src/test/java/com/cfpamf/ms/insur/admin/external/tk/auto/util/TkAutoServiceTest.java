package com.cfpamf.ms.insur.admin.external.tk.auto.util;

import com.cfpamf.ms.insur.admin.external.tk.TkApiProperties;
import com.cfpamf.ms.insur.admin.external.tk.auto.TkAutoServiceAdapterImpl;
import com.cfpamf.ms.insur.admin.pojo.vo.SmProductDetailVO;
import com.cfpamf.ms.insur.admin.service.SmProductService;
import com.github.jsonzou.jmockdata.JMockData;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * <AUTHOR> 2021/4/6 16:25
 */
@RunWith(SpringJUnit4ClassRunner.class)
public class TkAutoServiceTest {
    @InjectMocks
    TkAutoServiceAdapterImpl autoService;


    @Mock
    TkApiProperties properties;

    @Mock
    SmProductService productService;

    @Before
    public void setUp() throws Exception {

        Mockito.when(properties.getAutoH5ChannelId())
                .thenReturn("71031");

        Mockito.when(properties.getAutoH5Key())
                .thenReturn("A9CAED282E471031");

        SmProductDetailVO mock = JMockData.mock(SmProductDetailVO.class);
        mock.setH5Url("http://wechat.test.car.tk.cn/#/index/home");
        Mockito.when(productService.getProductById(Mockito.anyInt()))
                .thenReturn(mock);

    }

    @Test
    public void getJumpAutoUri() {
//        System.err.println(autoService.getJumpAutoUri("ZHNX09760|wx|osUxv1opgsu4AWXfOaYs_VISY33Q", 1));

    }
}
