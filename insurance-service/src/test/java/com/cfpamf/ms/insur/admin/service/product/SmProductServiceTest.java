package com.cfpamf.ms.insur.admin.service.product;

import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.ms.insur.admin.constant.SmConstants;
import com.cfpamf.ms.insur.admin.convertedpremium.service.ConvertedPremiumConfigService;
import com.cfpamf.ms.insur.admin.dao.safes.*;
import com.cfpamf.ms.insur.admin.dao.safes.commission.SystemCommissionConfigDetailMapper;
import com.cfpamf.ms.insur.admin.dao.safes.product.*;
import com.cfpamf.ms.insur.admin.enums.EnumChannel;
import com.cfpamf.ms.insur.admin.enums.EnumProductApiType;
import com.cfpamf.ms.insur.admin.enums.product.EnumProductCreateType;
import com.cfpamf.ms.insur.admin.enums.product.EnumProductLabelType;
import com.cfpamf.ms.insur.admin.pojo.dto.SmProductCoverageDTO;
import com.cfpamf.ms.insur.admin.pojo.dto.SmProductNotificationDTO;
import com.cfpamf.ms.insur.admin.pojo.dto.SmProductQuoteLimitDTO;
import com.cfpamf.ms.insur.admin.pojo.dto.product.DutyFactorDTO;
import com.cfpamf.ms.insur.admin.pojo.dto.product.ProductLabelDTO;
import com.cfpamf.ms.insur.admin.pojo.dto.product.QuestionDTO;
import com.cfpamf.ms.insur.admin.pojo.po.product.SmProductLabel;
import com.cfpamf.ms.insur.admin.pojo.vo.SmPlanVO;
import com.cfpamf.ms.insur.admin.pojo.vo.SmProductDetailVO;
import com.cfpamf.ms.insur.admin.pojo.vo.SmProductQuoteLimitVO;
import com.cfpamf.ms.insur.admin.pojo.vo.product.BasicProductVo;
import com.cfpamf.ms.insur.admin.pojo.vo.product.PremiumFile;
import com.cfpamf.ms.insur.admin.pojo.vo.product.PremiumFlowFactorReqVo;
import com.cfpamf.ms.insur.admin.pojo.vo.product.SmProductCoverageDiscountWrap;
import com.cfpamf.ms.insur.admin.service.BaseTest;
import com.cfpamf.ms.insur.admin.service.DictionaryService;
import com.cfpamf.ms.insur.admin.service.SmProductHistoryService;
import com.cfpamf.ms.insur.admin.service.SmProductService;
import com.cfpamf.ms.insur.admin.service.aicheck.AkProductQuestionnaireService;
import com.cfpamf.ms.insur.base.exception.BizException;
import com.cfpamf.ms.insur.base.util.HttpRequestUtil;
import com.cfpamf.ms.insur.base.util.RedisUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.jsonzou.jmockdata.JMockData;
import com.github.jsonzou.jmockdata.TypeReference;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@RunWith(PowerMockRunner.class)
@PrepareForTest({HttpRequestUtil.class})
public class SmProductServiceTest extends BaseTest {

//    @Autowired
//    private SmProductService productService;

//    @Autowired
//    private SmProductHistoryService historyService;

    @InjectMocks
    private SmProductService productService;


    @Mock
    private SmProductDutyFactorMapper dutyFactorMapper;
    @Mock
    private SystemCommissionConfigDetailMapper sysCommissionConfigDetailMapper;
    @Mock
    private SmProductMapper mapper;

    @Mock
    private SmProductAttrMapper productAttrMapper;

    @Mock
    private SmProductAttrHistoryMapper productAttrHistoryMapper;

    @Mock
    SmProductVersionMapper productVersionMapper;


    @Mock
    private SmProductHistoryMapper historyMapper;

    @Mock
    private SmPlanSaleOrgMapper planSaleOrgMapper;

    /**
     * 字典service
     */
    @Mock
    private DictionaryService dictionaryService;

    /**
     * 职业mapper
     */
    @Mock
    private SmOccupationMapper occupationMapper;

    /**
     * 提成mapper
     */
    @Mock
    private SmCommissionMapper commissionMapper;

    /**
     * 客户告知书Mapper
     */
    @Mock
    private SmProductNotifyMapper notifyMapper;

    @Mock
    private AkProductQuestionnaireService akProductQuestionnaireService;

    /**
     * RedisUtil
     */
    @Mock
    private RedisUtil<String, String> redisUtil;

    @Mock
    private ObjectMapper jsonMapper;

    /**
     * 快照服务
     */
    @Mock
    private SmProductHistoryService historyService;

    @Mock
    private SmProductConfirmMapper confirmMapper;

    @Mock
    ConvertedPremiumConfigService convertedPremiumConfigService;

    @Mock
    SmLongInsurancePlanService smLongInsurancePlanService;

    @Mock
    SmProductHealthInformService smProductHealthInformService;

    @Mock
    SmProductRiskService smProductRiskService;

    @Mock
    private SmProductEndorMapper endorMapper;

    @Test
    public void saveProductBaseInfo() {
//        int r = productService.saveProductBaseInfo(mockProductBean());
//        Assert.assertTrue("产品保存失败",r>0);
    }

    @Test
    public void saveProductHealthNotification() throws Exception {
//        productService.saveProductHealthNotification(moceNotificationBean());
//        Assert.assertTrue("健康告知信息保存失败",true);
    }

    @Test
    public void getProductCoverageList() {
//        Integer productId = 288;
//        Integer version = null;
//        List<SmProductCoverageVO> data = historyService.getProductCoverageList(productId,version);
//        Assert.assertTrue("责任列表查询失败",data!=null);
    }

    @Test
    public void saveProductCoverage() {
//        Integer productId = 288;
//        productService.saveProductCoverage(productId,mockCoverageList(productId));
//        Assert.assertTrue("责任列表查询失败",true);
    }

    @Test
    public void getPlanDutyTable() {
//        Integer productId = 288;
//        List<DutyTableVO> data = historyService.getPlanDutyTable(productId,null);
//        Assert.assertTrue("责任列表查询失败",data!=null);
    }

    @Test
    public void getCoverageTable() {
//        Integer productId = 288;
//        List<CoverageTableVO> data = historyService.getCoverageTable(productId,null);
//        Assert.assertTrue("获取产品计划的费率表失败",data!=null);
    }

    @Test
    public void savePremiumFile() {
//        Integer productId = 288;
//        boolean rs= productService.savePremiumFile(productId,mockPremiumFile(productId));
//        Assert.assertTrue("获取产品计划的费率表失败",rs);
    }

    @Test
    public void listDuty() {
//        Integer productId = 288;
//        boolean rs= productService.savePremiumFile(productId,mockPremiumFile(productId));
//        Assert.assertTrue("获取产品计划的费率表失败",rs);
    }

    private List<PremiumFile> mockPremiumFile(Integer productId) {
        List<PremiumFile> pf = new ArrayList<>();
        PremiumFile p = new PremiumFile();
        p.setUrl("http://www.baidu.com");
        p.setName("A");
        pf.add(p);
        return pf;
    }

    private List<SmProductCoverageDTO> mockCoverageList(Integer productId) {
        List<SmProductCoverageDTO> dtos = new ArrayList<>();
        SmProductCoverageDTO vo = new SmProductCoverageDTO();
        vo.setSpcId(null);
        vo.setRiskCode("R001");
        vo.setProductId(productId);
        vo.setOperator("10086");
        vo.setCvgCode("D001");
        vo.setCvgItemName("人身意外险");
        vo.setCvgRespType("100");
        vo.setMandatory(1);
        vo.setPlanId(109);
        vo.setPlanName("a套餐");
        dtos.add(vo);
        return dtos;
    }

    private SmProductNotificationDTO moceNotificationBean() {
        SmProductNotificationDTO dto = new SmProductNotificationDTO();
        dto.setProductId(288);
        dto.setAiCheck(true);
        dto.setAiCheckWay(2);

        List<QuestionDTO> questions = new ArrayList<>();
        QuestionDTO q = new QuestionDTO();
        q.setCode("Q1");
        q.setProductId(288);
        q.setQuestion("你今年过的好吗");
        questions.add(q);
        dto.setQuestions(questions);
        dto.setModifyBy("SuperMan");
        return dto;
    }

    private BasicProductVo mockProductBean() {
        BasicProductVo vo = new BasicProductVo();
        vo.setApiType(EnumProductApiType.API.getType());
        vo.setChannel(EnumChannel.TK.getCode());
        vo.setProductCategoryId("218");
        vo.setProductAttrCode(SmConstants.PRODUCT_ATTR_GROUP);
        vo.setCompanyId("213");
        vo.setProductName("泰康新版团险");
        vo.setHeadImageUrl(null);
        vo.setProductTags("不信拉倒");
        vo.setProductFeature("没啥特色，骗点零花钱花花。。。");
        vo.setThumbnailImageUrl("https://safesfiles-test.oss-cn-beijing.aliyuncs.com/image/1627980283217/7a774b46d263455b84a08244a79bcef3/zsh.jpg");
        vo.setIntroduceImageUrl(null);
        vo.setEffectWaitingDayMin(1);
        vo.setEffectWaitingDayMax(30);
        vo.setBuyLimit(1);
        vo.setMiniPremium(new BigDecimal(100));
        vo.setValidPeriod("10年");
        vo.setGlOcpnGroup("1,2,5");
        vo.setGlUwaFrom("10周岁");
        vo.setGlUwaTo("60周岁");
        vo.setGlProductIntroduce(null);
        vo.setProductType("accident");
        vo.setProductCode("TX");
        vo.setCreateType(EnumProductCreateType.DEFAULT_TYPE);
        return vo;
    }


    @Test
    public void saveProductQuoteLimit() {
        Integer productId = JMockData.mock(Integer.class);
        SmProductQuoteLimitDTO dto = JMockData.mock(SmProductQuoteLimitDTO.class);
        productService.saveProductQuoteLimit(productId, dto);
        Assert.assertTrue(true);
    }


    @Test
    public void getProductQuoteLimit() {
        Integer productId = JMockData.mock(Integer.class);
        SmProductQuoteLimitVO productVo = productService.getProductQuoteLimit(productId);
        System.err.println(productVo);
        Assert.assertTrue(productVo != null);
    }

    @Test
    public void savePremiumFactor() {
        Integer productId = JMockData.mock(Integer.class);

        PremiumFlowFactorReqVo vo = mockPremiumFactor();
        productService.savePremiumFactor(productId, vo);
        Assert.assertTrue(Boolean.TRUE);
    }


    private PremiumFlowFactorReqVo mockPremiumFactor() {
        PremiumFlowFactorReqVo vo = new PremiumFlowFactorReqVo();
        vo.setCalType("2");
        vo.setFlowType("2");
        SmProductCoverageDiscountWrap<DutyFactorDTO> dutyFactor = new SmProductCoverageDiscountWrap<>();
        dutyFactor.setAccuracy("2");
        List<DutyFactorDTO> data = new ArrayList<>();
        DutyFactorDTO dto = new DutyFactorDTO();
        dto.setDutyCode("ZX109");
        dto.setFactorValue("2");
        dto.setProductId(454);
        dto.setDutyFactorCode("deductible");
        data.add(dto);
        dutyFactor.setItems(data);
        vo.setDutyFactor(dutyFactor);
        return vo;
    }

    @Mock
    private SmProductLabelMapper smProductLabelMapper;

    @Test
    public void saveProductLabel() {
        ProductLabelDTO dto = mockProductLabelDTO();
        Mockito.when(smProductLabelMapper.batchInsertProductLabel(Mockito.anyList())).thenReturn(1);
        productService.saveProductLabel(dto);
    }

    private ProductLabelDTO mockProductLabelDTO() {
        ProductLabelDTO dto = new ProductLabelDTO();
        dto.setProductId(533);
        ProductLabelDTO.LabelDTO labelDTO = new ProductLabelDTO.LabelDTO();
        labelDTO.setLabelType(EnumProductLabelType.JOIN_PERFORMANCE.getCode());
        labelDTO.setLabelValue("N");
        List<ProductLabelDTO.LabelDTO> list = new ArrayList<>();
        list.add(labelDTO);
        dto.setLabelList(list);
        return dto;
    }

    @Test
    public void getProductLabels() {
        Integer productId = JMockData.mock(Integer.class);


        List<SmProductLabel> labelList = mockProductLabelList();
        Mockito.when(smProductLabelMapper.listByProductId(Mockito.anyInt())).thenReturn(labelList);
        ProductLabelDTO labelDTO = productService.getProductLabels(productId);
        Assert.assertEquals(1, labelDTO.getLabelList().size());
    }

    @Test
    public void listProductIdByLabelTypeValue() {
        String labelType = JMockData.mock(String.class);
        String value = JMockData.mock(String.class);

        List<SmProductLabel> labelList = mockProductLabelList();
        Mockito.when(smProductLabelMapper.listByLabelType(Mockito.anyString(), Mockito.anyString())).thenReturn(labelList);
        List<Integer> productIdList = productService.listProductIdByLabelTypeValue(labelType, value);
        Assert.assertEquals(1, productIdList.size());
    }

    private List<SmProductLabel> mockProductLabelList() {
        List<SmProductLabel> list = new ArrayList<>();
        SmProductLabel label = new SmProductLabel();
        label.setProductId(533);
        label.setLabelType(EnumProductLabelType.JOIN_PERFORMANCE.getCode());
        label.setLabelTypeDesc(EnumProductLabelType.JOIN_PERFORMANCE.getDesc());
        label.setLabelValue("Y");
        list.add(label);
        return list;
    }

    @Test
    public void validProduct() {
        try {
            productService.validProduct(454);
        } catch (MSBizNormalException | BizException e) {
            e.printStackTrace();
        } catch (Exception e) {
            e.printStackTrace();
            Assert.assertTrue(false);
        }
    }

    @Test
    public void validConvertedPremiumConfig() {
        try {
            List<SmPlanVO> plans = new ArrayList<>();
            SmPlanVO planVO = JMockData.mock(SmPlanVO.class);
            plans.add(planVO);
            productService.validConvertedPremium(plans);
        } catch (MSBizNormalException | BizException e) {
            e.printStackTrace();
        } catch (Exception e) {
            e.printStackTrace();
            Assert.assertTrue(false);
        }
    }

    @Test
    public void validSysCommission() {
        List<SmPlanVO> smPlanList = new ArrayList<>();
        SmPlanVO plan = JMockData.mock(SmPlanVO.class);
        smPlanList.add(plan);
        try {
            productService.validSysCommission(smPlanList);
        } catch (BizException e) {
            e.printStackTrace();
        } catch (MSBizNormalException e) {
            e.printStackTrace();
        }
    }

    @Test
    public void checkLongInsuranceConfig() {
        int id = 454;
        SmProductDetailVO productDetail = JMockData.mock(SmProductDetailVO.class);
        TypeReference<List<SmPlanVO>> tr = new TypeReference<List<SmPlanVO>>() {
        };
        List<SmPlanVO> plans = JMockData.mock(tr);

        Integer apiType = 1;

        try {
            productService.checkLongInsuranceConfig(id, productDetail, plans, apiType);
        } catch (MSBizNormalException | BizException e) {
            e.printStackTrace();
        } catch (Exception e) {
            e.printStackTrace();
            Assert.assertTrue(false);
        }
    }
}
