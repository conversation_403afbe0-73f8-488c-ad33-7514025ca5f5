package com.cfpamf.ms.insur.admin.pojo.convertor;

import com.cfpamf.ms.insur.admin.pojo.po.product.SmPlanRisk;
import com.cfpamf.ms.insur.admin.pojo.po.product.SmPlanRiskHistory;
import org.junit.Assert;
import org.junit.Test;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2021/9/7 14:57
 */
public class SmPlanRiskTest {

    @Test
    public void testGetAndSet() {
        LocalDateTime now = LocalDateTime.now();
        SmPlanRisk smPlanRisk = new SmPlanRisk();
        smPlanRisk.setPlanId(0);
        smPlanRisk.setProductId(0);
        smPlanRisk.setRiskKey("");
        smPlanRisk.setRiskVersion(0);
        smPlanRisk.setIncludedTotalInsuredAmount(0);
        smPlanRisk.setSortField(0);
        smPlanRisk.setCreateTime(now);
        smPlanRisk.setUpdateTime(now);
        smPlanRisk.setCreateBy("");
        smPlanRisk.setUpdateBy("");
        smPlanRisk.setId(0);
        smPlanRisk.setEnabledFlag(0);

        Assert.assertEquals(Integer.valueOf(0), smPlanRisk.getPlanId());
        Assert.assertEquals(Integer.valueOf(0), smPlanRisk.getProductId());
        Assert.assertEquals("", smPlanRisk.getRiskKey());
        Assert.assertEquals("", smPlanRisk.getCreateBy());
        Assert.assertEquals("", smPlanRisk.getUpdateBy());
        Assert.assertEquals(Integer.valueOf(0), smPlanRisk.getRiskVersion());
        Assert.assertEquals(Integer.valueOf(0), smPlanRisk.getIncludedTotalInsuredAmount());
        Assert.assertEquals(Integer.valueOf(0), smPlanRisk.getSortField());
        Assert.assertEquals(now, smPlanRisk.getCreateTime());
        Assert.assertEquals(now, smPlanRisk.getUpdateTime());
        Assert.assertEquals(Integer.valueOf(0), smPlanRisk.getId());
        Assert.assertEquals(Integer.valueOf(0), smPlanRisk.getEnabledFlag());

        SmPlanRiskHistory smPlanRiskHistory = new SmPlanRiskHistory();
        smPlanRiskHistory.setPlanId(0);
        smPlanRiskHistory.setProductId(0);
        smPlanRiskHistory.setPlanRiskId(0);
        smPlanRiskHistory.setRiskKey("");
        smPlanRiskHistory.setRiskVersion(0);
        smPlanRiskHistory.setIncludedTotalInsuredAmount(0);
        smPlanRiskHistory.setVersion(0);
        smPlanRiskHistory.setSortField(0);
        smPlanRiskHistory.setCreateTime(now);
        smPlanRiskHistory.setUpdateTime(now);
        smPlanRiskHistory.setCreateBy("");
        smPlanRiskHistory.setUpdateBy("");
        smPlanRiskHistory.setId(0);
        smPlanRiskHistory.setEnabledFlag(0);


        Assert.assertEquals(Integer.valueOf(0), smPlanRiskHistory.getPlanId());
        Assert.assertEquals(Integer.valueOf(0), smPlanRiskHistory.getProductId());
        Assert.assertEquals("", smPlanRiskHistory.getRiskKey());
        Assert.assertEquals("", smPlanRiskHistory.getCreateBy());
        Assert.assertEquals("", smPlanRiskHistory.getUpdateBy());
        Assert.assertEquals(Integer.valueOf(0), smPlanRiskHistory.getRiskVersion());
        Assert.assertEquals(Integer.valueOf(0), smPlanRiskHistory.getIncludedTotalInsuredAmount());
        Assert.assertEquals(Integer.valueOf(0), smPlanRiskHistory.getSortField());
        Assert.assertEquals(now, smPlanRiskHistory.getCreateTime());
        Assert.assertEquals(now, smPlanRiskHistory.getUpdateTime());
        Assert.assertEquals(Integer.valueOf(0), smPlanRiskHistory.getId());
        Assert.assertEquals(Integer.valueOf(0), smPlanRiskHistory.getEnabledFlag());

    }
}
