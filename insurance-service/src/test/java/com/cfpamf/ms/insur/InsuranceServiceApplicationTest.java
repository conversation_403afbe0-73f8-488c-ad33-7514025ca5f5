package com.cfpamf.ms.insur;

import com.github.pagehelper.autoconfigure.PageHelperAutoConfiguration;
import lombok.extern.slf4j.Slf4j;
import org.activiti.spring.boot.JpaProcessEngineAutoConfiguration;
import org.junit.runner.RunWith;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.actuate.autoconfigure.security.servlet.ManagementWebSecurityAutoConfiguration;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.*;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.stereotype.Controller;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.client.RestTemplate;

/**
 * <AUTHOR> F
 * @date 2024/2/20 11:45
 */
@SpringBootApplication(exclude = {
        PageHelperAutoConfiguration.class,
        org.activiti.spring.boot.SecurityAutoConfiguration.class,
        SecurityAutoConfiguration.class,
        ManagementWebSecurityAutoConfiguration.class,
        JpaProcessEngineAutoConfiguration.class})
@EnableFeignClients("com.cfpamf.ms")
@RunWith(SpringJUnit4ClassRunner.class)
@ComponentScans(
        @ComponentScan(
                basePackages = {"com.cfpamf.ms", "com.cfpamf.cmis", "com.cfpamf.ms.insur"},
                excludeFilters = {
                        @ComponentScan.Filter(type = FilterType.ANNOTATION, classes = {Controller.class, ControllerAdvice.class})
                })
)
@PropertySource(value = {"classpath:application-dev.yml"})
@ImportResource(locations = {"classpath:kaptcha.xml"})
@MapperScan(basePackages = "com.cfpamf.**.*.dao")
@Slf4j
public class InsuranceServiceApplicationTest {

    @Bean
    public RestTemplate restTemplate() {
        SimpleClientHttpRequestFactory requestFactory = new SimpleClientHttpRequestFactory();
        requestFactory.setBufferRequestBody(false);
        return new RestTemplate(requestFactory);
    }
    public static void main(String[] args) {
        SpringApplication springApplication = new SpringApplication(InsuranceServiceApplicationTest.class);
        springApplication.addListeners((ApplicationReadyEvent event) -> {
            ConfigurableApplicationContext application = event.getApplicationContext();
            String[] beanDefinitionNames = application.getBeanDefinitionNames();
            for (String beanDefinitionName : beanDefinitionNames) {
                log.info("当前beanDefinitionName --> {}", beanDefinitionName);
            }
        });
        springApplication.run(args);
    }

}



