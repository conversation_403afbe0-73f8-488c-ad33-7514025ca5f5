package com.cfpamf.ms.insur.admin.service.reconciliation;
import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.ms.insur.admin.dao.safes.reconciliation.SmReconciliationPolicyCacheMapper;
import com.cfpamf.ms.insur.admin.enums.reconciliation.EnumReconciliationStatusType;
import com.cfpamf.ms.insur.admin.pojo.dto.reconciliation.ReconciledPolicyDTO;
import com.cfpamf.ms.insur.admin.pojo.form.reconciliation.*;
import com.cfpamf.ms.insur.admin.pojo.po.reconciliation.SmReconciliation;
import com.cfpamf.ms.insur.admin.service.SmOrderCoreService;
import com.cfpamf.ms.insur.base.util.SpringFactoryUtil;
import com.google.common.collect.Lists;

import com.alibaba.fastjson.JSON;
import com.cfpamf.ms.insur.admin.dao.safes.order.ReconciliationOrderMapper;
import com.cfpamf.ms.insur.admin.dao.safes.reconciliation.SmReconciliationDifferenceMapper;
import com.cfpamf.ms.insur.admin.dao.safes.reconciliation.SmReconciliationMapper;
import com.cfpamf.ms.insur.admin.dao.safes.reconciliation.SmReconciliationPolicyMapper;
import com.cfpamf.ms.insur.admin.pojo.dto.reconciliation.ReconciliationExcelDTO;
import com.cfpamf.ms.insur.admin.pojo.dto.reconciliation.ReconciliationPolicyDTO;
import com.cfpamf.ms.insur.admin.pojo.po.reconciliation.SmReconciliationDifference;
import com.cfpamf.ms.insur.admin.pojo.vo.reconciliation.BalanceAccountResultVo;
import com.cfpamf.ms.insur.admin.pojo.vo.reconciliation.ReconciliationDifferenceExcelVo;
import com.cfpamf.ms.insur.admin.pojo.vo.reconciliation.ReconciliationResultVo;
import com.cfpamf.ms.insur.admin.service.BaseTest;
import com.cfpamf.ms.insur.admin.service.reconciliation.impl.ReconciliationServiceImpl;
import com.cfpamf.ms.insur.base.util.HttpRequestUtil;
import org.apache.commons.lang3.reflect.FieldUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/10/20 13:35
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({HttpRequestUtil.class})
public class ReconciliationServiceImplTest extends BaseTest {

    @Mock
    ReconciliationOrderMapper reconciliationOrderMapper;
    @Mock
    SmReconciliationMapper smReconciliationMapper;
    @Mock
    SmReconciliationPolicyMapper smReconciliationPolicyMapper;
    @Mock
    SmReconciliationDifferenceMapper smReconciliationDifferenceMapper;
    @Mock
    SmOrderCoreService smOrderCoreService;
    @Mock
    Float allowErrorAmount;
    @InjectMocks
    ReconciliationServiceImpl reconciliationService;
    @Mock
    SmReconciliationPolicyCacheMapper smReconciliationPolicyCacheMapper;

    @Before
    public void setAllowErrorAmount() throws IllegalAccessException {
        FieldUtils.writeField(FieldUtils.getDeclaredField(ReconciliationServiceImpl.class, "allowErrorAmount", true), reconciliationService, 1.5f);
    }


    @Test
    public void reconciliationDifferenceTest()  {
            ReconciliationExcelDTO reconciliationExcelDTO = new ReconciliationExcelDTO();
            reconciliationExcelDTO.setPolicyNo("policy");
            reconciliationExcelDTO.setApplicantName("name");
            reconciliationExcelDTO.setPolicyAmount(new BigDecimal("20"));
            reconciliationExcelDTO.setAppStatusDesc("承保成功");
            ReconciliationImportForm reconciliationImportForm = new ReconciliationImportForm();
            reconciliationImportForm.setReconciliationStartTime(LocalDateTime.now());
            reconciliationImportForm.setReconciliationEndTime(LocalDateTime.now());
            reconciliationImportForm.setChannel("");
            reconciliationImportForm.setChannels(Lists.newArrayList(""));
            reconciliationImportForm.setProductId(0);
            reconciliationImportForm.setReconciliationFileUrl("");
            ReconciliationResultVo reconciliation = reconciliationService.reconciliation(reconciliationImportForm, Lists.newArrayList(reconciliationExcelDTO));

    }

    @Test
    public void reconciliationTest()  {
        ReconciliationExcelDTO reconciliationExcelDTO = new ReconciliationExcelDTO();
        reconciliationExcelDTO.setPolicyNo("policy");
        reconciliationExcelDTO.setApplicantName("name");
        reconciliationExcelDTO.setPolicyAmount(new BigDecimal("20"));
        reconciliationExcelDTO.setAppStatusDesc("承保成功");
        ReconciliationImportForm reconciliationImportForm = new ReconciliationImportForm();
        reconciliationImportForm.setReconciliationStartTime(LocalDateTime.now());
        reconciliationImportForm.setReconciliationEndTime(LocalDateTime.now());
        reconciliationImportForm.setChannel("");
        reconciliationImportForm.setChannels(Lists.newArrayList(""));
        reconciliationImportForm.setProductId(0);
        reconciliationImportForm.setReconciliationFileUrl("");

        ReconciliationPolicyDTO reconciliationPolicyDTO = new ReconciliationPolicyDTO();
        reconciliationPolicyDTO.setNoLinePolicyNo("policy");
        reconciliationPolicyDTO.setPolicyNo("policy");
        reconciliationPolicyDTO.setApplicantName("name");
        reconciliationPolicyDTO.setPolicyAmount(new BigDecimal("20"));
        reconciliationPolicyDTO.setAppStatus("1");
        Mockito.when(reconciliationOrderMapper.getReconciliationPolicyList(reconciliationImportForm))
                .thenReturn(Lists.newArrayList(reconciliationPolicyDTO));

        ReconciliationResultVo reconciliation = reconciliationService.reconciliation(reconciliationImportForm, Lists.newArrayList(reconciliationExcelDTO));
        String s = JSON.toJSONString(reconciliation);
        System.out.println(s);
        Assert.assertEquals(Integer.valueOf(1),reconciliation.getTotalNumber());
        Assert.assertEquals(Integer.valueOf(0),reconciliation.getDifferenceNumber());
    }

    @Test
    public void testGetSmReconciliationDifferenceByReconciliationId(){
        List<ReconciliationDifferenceExcelVo> smReconciliationDifferenceByReconciliationId = reconciliationService.getSmReconciliationDifferenceByReconciliationId(2);
    }

    @Test
    public void testSearchSmReconciliationDifference(){
         reconciliationService.searchSmReconciliationDifference(Mockito.mock(ReconciliationDifferenceSearchForm.class));

    }

    @Test
    public void testBatchBalanceAccount(){
        SmReconciliationDifference smReconciliationDifference = new SmReconciliationDifference();
        smReconciliationDifference.setReconciliationId(0);
        smReconciliationDifference.setSerialNumber(1);
        smReconciliationDifference.setCompanyFlag(0);
        smReconciliationDifference.setPolicyNo("");
        smReconciliationDifference.setPremium(new BigDecimal("0"));
        smReconciliationDifference.setApplicantName("");
        smReconciliationDifference.setAppStatus("");
        smReconciliationDifference.setBalanceAccountFlag(0);
        smReconciliationDifference.setBalanceAccountReason("");
        smReconciliationDifference.setSystemCheckReason("");
        smReconciliationDifference.setReconciliationStatus(2);
        smReconciliationDifference.setCreateTime(LocalDateTime.now());
        smReconciliationDifference.setUpdateTime(LocalDateTime.now());
        smReconciliationDifference.setCreateBy("");
        smReconciliationDifference.setUpdateBy("");
        smReconciliationDifference.setId(0);
        smReconciliationDifference.setEnabledFlag(0);

        SmReconciliationDifference smReconciliationDifference2 = new SmReconciliationDifference();
        smReconciliationDifference2.setReconciliationId(0);
        smReconciliationDifference2.setSerialNumber(1);
        smReconciliationDifference2.setCompanyFlag(0);
        smReconciliationDifference2.setPolicyNo("");
        smReconciliationDifference2.setPremium(new BigDecimal("0"));
        smReconciliationDifference2.setApplicantName("");
        smReconciliationDifference2.setAppStatus("");
        smReconciliationDifference2.setBalanceAccountFlag(0);
        smReconciliationDifference2.setBalanceAccountReason("");
        smReconciliationDifference2.setSystemCheckReason("");
        smReconciliationDifference2.setReconciliationStatus(2);
        smReconciliationDifference2.setCreateTime(LocalDateTime.now());
        smReconciliationDifference2.setUpdateTime(LocalDateTime.now());
        smReconciliationDifference2.setCreateBy("");
        smReconciliationDifference2.setUpdateBy("");
        smReconciliationDifference2.setId(0);
        smReconciliationDifference2.setEnabledFlag(0);
        BatchBalanceAccountForm batchBalanceAccountForm = new BatchBalanceAccountForm();
        batchBalanceAccountForm.setBalanceAccountReason("");
        batchBalanceAccountForm.setReconciliationId(0);
        ArrayList<Integer> integers = Lists.newArrayList(1);
        batchBalanceAccountForm.setSerialNumberList(integers);

        Mockito.when(smReconciliationDifferenceMapper.getByReconciliationIdAndSerialNumber(0,integers)).thenReturn(Lists.newArrayList(smReconciliationDifference,smReconciliationDifference2));
        BalanceAccountResultVo balanceAccountResultVo = reconciliationService.batchBalanceAccount(batchBalanceAccountForm);
        System.out.println(balanceAccountResultVo);

    }

    @Test
    public void testBalanceAccount(){
        SmReconciliationDifference smReconciliationDifference = new SmReconciliationDifference();
        smReconciliationDifference.setReconciliationId(0);
        smReconciliationDifference.setSerialNumber(1);
        smReconciliationDifference.setCompanyFlag(0);
        smReconciliationDifference.setPolicyNo("");
        smReconciliationDifference.setPremium(new BigDecimal("0"));
        smReconciliationDifference.setApplicantName("");
        smReconciliationDifference.setAppStatus("");
        smReconciliationDifference.setBalanceAccountFlag(0);
        smReconciliationDifference.setBalanceAccountReason("");
        smReconciliationDifference.setSystemCheckReason("");
        smReconciliationDifference.setReconciliationStatus(2);
        smReconciliationDifference.setCreateTime(LocalDateTime.now());
        smReconciliationDifference.setUpdateTime(LocalDateTime.now());
        smReconciliationDifference.setCreateBy("");
        smReconciliationDifference.setUpdateBy("");
        smReconciliationDifference.setId(0);
        smReconciliationDifference.setEnabledFlag(0);

        SmReconciliationDifference smReconciliationDifference2 = new SmReconciliationDifference();
        smReconciliationDifference2.setReconciliationId(0);
        smReconciliationDifference2.setSerialNumber(1);
        smReconciliationDifference2.setCompanyFlag(0);
        smReconciliationDifference2.setPolicyNo("");
        smReconciliationDifference2.setPremium(new BigDecimal("0"));
        smReconciliationDifference2.setApplicantName("");
        smReconciliationDifference2.setAppStatus("");
        smReconciliationDifference2.setBalanceAccountFlag(0);
        smReconciliationDifference2.setBalanceAccountReason("");
        smReconciliationDifference2.setSystemCheckReason("");
        smReconciliationDifference2.setReconciliationStatus(2);
        smReconciliationDifference2.setCreateTime(LocalDateTime.now());
        smReconciliationDifference2.setUpdateTime(LocalDateTime.now());
        smReconciliationDifference2.setCreateBy("");
        smReconciliationDifference2.setUpdateBy("");
        smReconciliationDifference2.setId(0);
        smReconciliationDifference2.setEnabledFlag(0);
        BalanceAccountForm batchBalanceAccountForm = new BalanceAccountForm();
        batchBalanceAccountForm.setBalanceAccountReason("");
        batchBalanceAccountForm.setReconciliationId(0);
        batchBalanceAccountForm.setSerialNumber(1);

        Mockito.when(smReconciliationDifferenceMapper.getByReconciliationIdAndSerialNumber(0,Lists.newArrayList(1))).thenReturn(Lists.newArrayList(smReconciliationDifference,smReconciliationDifference2));
        try {
            reconciliationService.balanceAccount(batchBalanceAccountForm);
        } catch (MSBizNormalException e) {
            Assert.assertEquals(e.getMessage(),"平账失败，系统找不到该保单");
        }


    }

    @Test
    public void testSearchSmReconciliationVo(){
        ReconciliationSearchForm mock = Mockito.mock(ReconciliationSearchForm.class);
        reconciliationService.searchSmReconciliationVo(mock);
    }

    @Test
    public void testBatchUpdatePolicyState(){
        ReconciliationBatchForm mock = Mockito.mock(ReconciliationBatchForm.class);
        reconciliationService.batchUpdatePolicyState(mock);
    }

    @Test
    public void testSystemCheckReason(){
        Integer reconciliationId= 2;

        SmReconciliation smReconciliation = new SmReconciliation();
        smReconciliation.setChannel("");
        smReconciliation.setProductId(0);
        smReconciliation.setFileUrl("");
        smReconciliation.setOperator("");
        smReconciliation.setStartTime(LocalDateTime.now());
        smReconciliation.setEndTime(LocalDateTime.now());
        smReconciliation.setImportTime(LocalDateTime.now());
        smReconciliation.setChannelAmount(new BigDecimal("0"));
        smReconciliation.setOurCompanyAmount(new BigDecimal("0"));
        smReconciliation.setTotalNumber(0);
        smReconciliation.setCorrectNumber(0);
        smReconciliation.setDifferenceDetail("");
        smReconciliation.setCreateTime(LocalDateTime.now());
        smReconciliation.setUpdateTime(LocalDateTime.now());
        smReconciliation.setCreateBy("");
        smReconciliation.setUpdateBy("");
        smReconciliation.setId(reconciliationId);
        smReconciliation.setEnabledFlag(0);
        Mockito.when(smReconciliationMapper.selectByPrimaryKey(reconciliationId)).thenReturn(smReconciliation);
        Mockito.when(smReconciliationDifferenceMapper.getByReconciliationId(reconciliationId)).thenReturn(getSmReconciliationDifferences(reconciliationId));
        ReconciliationPolicyDTO reconciliationPolicyDTO = getReconciliationPolicyDTO();
        ReconciledPolicyDTO reconciledPolicyDTO = getReconciledPolicyDTO();
        Mockito.when(reconciliationOrderMapper.getReconciliationPolicy(Mockito.any(LocalDateTime.class), Mockito.any(LocalDateTime.class), Mockito.anyString(), Mockito.anyListOf(String.class))).thenReturn(Lists.newArrayList(reconciliationPolicyDTO));
        Mockito.when(smReconciliationPolicyMapper.getSmReconciliationPolicy(Mockito.any(LocalDateTime.class), Mockito.any(LocalDateTime.class), Mockito.anyString(), Mockito.anyListOf(String.class))).thenReturn(Lists.newArrayList(reconciledPolicyDTO));
        reconciliationService.systemCheckReason(2);
    }

    private ReconciledPolicyDTO getReconciledPolicyDTO() {
        ReconciledPolicyDTO reconciledPolicyDTO = new ReconciledPolicyDTO();
        reconciledPolicyDTO.setPolicyNo("policyNo2");
        reconciledPolicyDTO.setAppStatus("1");
        reconciledPolicyDTO.setReconciliationTime(LocalDateTime.now());
        reconciledPolicyDTO.setReconciliationStatus(3);
        reconciledPolicyDTO.setBalanceAccountFlag(1);
        return reconciledPolicyDTO;
    }

    private ReconciliationPolicyDTO getReconciliationPolicyDTO() {
        ReconciliationPolicyDTO reconciliationPolicyDTO = new ReconciliationPolicyDTO();
        reconciliationPolicyDTO.setNoLinePolicyNo("policyNo1");
        reconciliationPolicyDTO.setPolicyNo("policyNo1");
        reconciliationPolicyDTO.setApplicantName("");
        reconciliationPolicyDTO.setPolicyAmount(new BigDecimal("0"));
        reconciliationPolicyDTO.setAppStatus("1");
        reconciliationPolicyDTO.setReconciled(0);
        reconciliationPolicyDTO.setAccountTime(LocalDateTime.now());
        return reconciliationPolicyDTO;
    }

    private List<SmReconciliationDifference> getSmReconciliationDifferences(Integer reconciliationId) {
        SmReconciliationDifference smReconciliationDifference = new SmReconciliationDifference();
        smReconciliationDifference.setReconciliationId(reconciliationId);
        smReconciliationDifference.setSerialNumber(0);
        smReconciliationDifference.setCompanyFlag(0);
        smReconciliationDifference.setPolicyNo("policyNo1");
        smReconciliationDifference.setPremium(new BigDecimal("0"));
        smReconciliationDifference.setApplicantName("");
        smReconciliationDifference.setAppStatus("1");
        smReconciliationDifference.setBalanceAccountFlag(0);
        smReconciliationDifference.setBalanceAccountReason("");
        smReconciliationDifference.setSystemCheckReason("");
        smReconciliationDifference.setReconciliationStatus(EnumReconciliationStatusType.ShortAccount.getCode());
        smReconciliationDifference.setCreateTime(LocalDateTime.now());
        smReconciliationDifference.setUpdateTime(LocalDateTime.now());
        smReconciliationDifference.setCreateBy("");
        smReconciliationDifference.setUpdateBy("");
        smReconciliationDifference.setId(0);
        smReconciliationDifference.setEnabledFlag(0);

        SmReconciliationDifference smReconciliationDifference1 = new SmReconciliationDifference();
        smReconciliationDifference1.setReconciliationId(reconciliationId);
        smReconciliationDifference1.setSerialNumber(0);
        smReconciliationDifference1.setCompanyFlag(1);
        smReconciliationDifference1.setPolicyNo("policyNo2");
        smReconciliationDifference1.setPremium(new BigDecimal("0"));
        smReconciliationDifference1.setApplicantName("");
        smReconciliationDifference1.setAppStatus("1");
        smReconciliationDifference1.setBalanceAccountFlag(0);
        smReconciliationDifference1.setBalanceAccountReason("");
        smReconciliationDifference1.setSystemCheckReason("");
        smReconciliationDifference1.setReconciliationStatus(EnumReconciliationStatusType.MoreAccount.getCode());
        smReconciliationDifference1.setCreateTime(LocalDateTime.now());
        smReconciliationDifference1.setUpdateTime(LocalDateTime.now());
        smReconciliationDifference1.setCreateBy("");
        smReconciliationDifference1.setUpdateBy("");
        smReconciliationDifference1.setId(0);
        smReconciliationDifference1.setEnabledFlag(0);
        return Lists.newArrayList(smReconciliationDifference, smReconciliationDifference1);
    }
}
