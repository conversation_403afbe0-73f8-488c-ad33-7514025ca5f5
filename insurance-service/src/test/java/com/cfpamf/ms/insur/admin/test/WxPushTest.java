package com.cfpamf.ms.insur.admin.test;

import com.cfpamf.ms.insur.admin.event.WxClaimNotifyEvent;
import com.cfpamf.ms.insur.base.event.EventBusEngine;
import com.cfpamf.ms.insur.base.util.SpringFactoryUtil;
import org.junit.FixMethodOrder;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.MethodSorters;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 **/
/*@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("dev")
@FixMethodOrder(MethodSorters.NAME_ASCENDING)*/
public class WxPushTest {

    /*@Test
    public void testPushWxClaimNotify() {
        SpringFactoryUtil.getBean(EventBusEngine.class).publish(new WxClaimNotifyEvent(2120, "stepDataCheckBySafesCenter", "", ""));
//        SpringFactoryUtil.getBean(EventBusEngine.class).publish(new WxClaimNotifyEvent(649));
        try {
            Thread.sleep(100000);
        } catch (Exception e) {

        }
    }*/
}
