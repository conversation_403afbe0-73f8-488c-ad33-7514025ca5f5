package com.cfpamf.ms.insur.weixin.service;

import com.alibaba.fastjson.JSON;
import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.common.ms.result.Result;
import com.cfpamf.ms.customer.facade.api.ManageFacade;
import com.cfpamf.ms.customer.facade.vo.CustInfoVo;
import com.cfpamf.ms.insur.admin.dao.safes.AuthUserMapper;
import com.cfpamf.ms.insur.admin.dao.safes.CustomerMapper;
import com.cfpamf.ms.insur.admin.dao.safes.UserPostMapper;
import com.cfpamf.ms.insur.admin.pojo.vo.CustPropertyVO;
import com.cfpamf.ms.insur.admin.service.BaseTest;
import com.cfpamf.ms.insur.base.bean.SmyPageInfo;
import com.cfpamf.ms.insur.base.exception.BizException;
import com.cfpamf.ms.insur.base.exception.ExcptEnum;
import com.cfpamf.ms.insur.base.util.HttpRequestUtil;
import com.cfpamf.ms.insur.base.util.RedisUtil;
import com.cfpamf.ms.insur.weixin.dao.safes.WxOrderMapper;
import com.cfpamf.ms.insur.weixin.pojo.dto.customer.WxCustomerDTO;
import com.cfpamf.ms.insur.weixin.pojo.query.WxCustOrderQuery;
import com.cfpamf.ms.insur.weixin.pojo.query.WxCustomerQuery;
import com.cfpamf.ms.insur.weixin.pojo.vo.*;
import com.github.jsonzou.jmockdata.JMockData;
import com.github.pagehelper.PageInfo;
import org.apache.xmlbeans.impl.jam.JMember;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;

@RunWith(PowerMockRunner.class)
@PrepareForTest({HttpRequestUtil.class})
public class WxCcCustomerServiceTest extends BaseTest {

    @InjectMocks
    private WxCcCustomerService customerService;

    @Mock
    RedisUtil<String, String> redisUtil;

    @Mock
    private CustomerMapper customerMapper;

    @Mock
    private WxOrderMapper orderMapper;

    @Mock
    private ManageFacade manageFacade;

    @Mock
    private AuthUserMapper authUserMapper;

    @Mock
    private UserPostMapper userPostMapper;

    @Test
    public void getCustomerById() {
        String openId = JMockData.mock(String.class);
        String authorization = JMockData.mock(String.class);
        String id = JMockData.mock(String.class);
        String customerType = JMockData.mock(String.class);

        try {
            WxCustBaseVO baseVO = customerService.getCustomerById(openId, authorization, id, customerType);
            System.err.println(baseVO);
        }catch (Exception e){
            if(e instanceof BizException){
                BizException be =(BizException)e;
                be.printStackTrace();
            }else{
                throw e;
            }
        }
    }

    @Test
    public void getWxCustomerShipById() {
        try {
            Mockito.when(customerService.checkAuthorityEpAg()).thenReturn(JMockData.mock(WxSessionVO.class));
            Mockito.when(customerService.checkAuthority(Mockito.anyString(), Mockito.anyString())).thenReturn(JMockData.mock(WxSessionVO.class));
            String openId = JMockData.mock(String.class);
            String authorization = JMockData.mock(String.class);
            String id = JMockData.mock(String.class);
            String type = "insur";
            List<WxCustShipVO> data = customerService.getWxCustomerShipById(openId, authorization, id, type);
            System.err.println(data);
        }catch (Exception e){
            if(e instanceof BizException){
                BizException be =(BizException)e;
                be.printStackTrace();
            }else{
                throw e;
            }
        }
        Assert.assertTrue(true);
    }

    @Test
    public void getWxCustomerPropertyById() {
        try {
//            Mockito.when(customerService.checkAuthorityEpAg()).thenReturn(JMockData.mock(WxSessionVO.class));
//            Mockito.when(customerService.checkAuthority(Mockito.anyString(), Mockito.anyString())).thenReturn(JMockData.mock(WxSessionVO.class));
            String openId = JMockData.mock(String.class);
            String authorization = JMockData.mock(String.class);
            String id = JMockData.mock(String.class);
            String type = "insur";
            WxCustPropertyVO data = customerService.getWxCustomerPropertyById(openId, authorization, id, type);
            System.err.println(data);
        }catch (Exception e){
            if(e instanceof BizException){
                BizException be =(BizException)e;
                be.printStackTrace();
            }else{
                throw e;
            }
        }
        Assert.assertTrue(true);
    }

    @Test
    public  void getWxCustomerPolicys() {
        try{

            Mockito.when(customerService.checkAuthorityEpAg()).thenReturn(JMockData.mock(WxSessionVO.class));


            WxCustOrderQuery query = JMockData.mock(WxCustOrderQuery.class);
            SmyPageInfo<WxPolicyListVo, WxCmsSmyVo> data =customerService.getWxCustomerPolicys(query);
            System.err.println(data);
            Assert.assertTrue(true);
        }catch (Exception e){
        if(e instanceof BizException){
            BizException be =(BizException)e;
            be.printStackTrace();
        }else{
            throw e;
        }
    }
    }

    @Test
    public void testModify() {
        String customerId = JMockData.mock(String.class);
        WxCustBaseVO vo = customerMapper.getCustomerById(customerId);
        System.err.println(JSON.toJSONString(vo));

        List<CustPropertyVO> list=customerMapper.listWxCustomerPropertyById(customerId);
        System.err.println(JSON.toJSONString(list));

        Set<String> idNumbers = new HashSet<>();
        idNumbers.add(vo.getIdNumber());
        List<WxCustomerDTO> clist= customerMapper.queryCustomerByIdCard(idNumbers);
        System.err.println(JSON.toJSONString(clist));

        Assert.assertTrue(true);
    }
}
