package com.cfpamf.ms.insur.admin.service;

import com.cfpamf.ms.insur.admin.dao.safes.CompanyMapper;
import com.cfpamf.ms.insur.admin.dao.safes.SmCmpySettingMapper;
import com.cfpamf.ms.insur.admin.dao.safes.SmProductMapper;
import com.cfpamf.ms.insur.admin.pojo.query.CompanyListQuery;
import com.cfpamf.ms.insur.admin.pojo.vo.CompanyVO;
import com.cfpamf.ms.insur.base.bean.Pageable;
import com.cfpamf.ms.insur.base.util.HttpRequestUtil;
import com.github.jsonzou.jmockdata.JMockData;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * <AUTHOR>
 * @Date 2021/10/20 15:42
 * @Version 1.0
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({HttpRequestUtil.class})
public class CompanyServiceTest extends BaseTest {
    @InjectMocks
    CompanyService companyService;
    @Mock
    CompanyMapper companyMapper;
    @Mock
    private SmProductMapper productMapper;
    @Mock
    private SmCmpySettingMapper settingMapper;

    @Test
    public void testGetCompanysByPageByKeword() {
        companyService.getCompanysByPage(JMockData.mock(String.class), new Pageable());
        companyService.getCompanysByPage(JMockData.mock(String.class), null);
    }

    @Test
    public void testGetCompanysByPage() {
        CompanyListQuery query = JMockData.mock(CompanyListQuery.class);
        companyService.getCompanysByPage(query);
    }


    @Override
    @Before
    public void setUp() throws Exception {
        super.setUp();
        Mockito.when(companyMapper.listPageCompanys(Mockito.any()))
                .thenReturn(
                        IntStream.range(0, 3)
                                .mapToObj(obj -> JMockData.mock(CompanyVO.class))
                                .collect(Collectors.toList())
                );
    }
}
