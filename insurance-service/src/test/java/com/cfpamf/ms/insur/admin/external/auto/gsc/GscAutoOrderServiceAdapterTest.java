package com.cfpamf.ms.insur.admin.external.auto.gsc;

import com.cfpamf.ms.insur.admin.external.gsc.model.GscApiProperties;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import static org.junit.Assert.*;

/**
 * <AUTHOR> 2021/6/1 14:50
 */
@RunWith(SpringJUnit4ClassRunner.class)
public class GscAutoOrderServiceAdapterTest {

    @InjectMocks
    GscAutoOrderServiceAdapter gscAutoOrderServiceAdapter;

    @Mock
    GscApiProperties apiProperties;

    @Before
    public void setUp() throws Exception {
        Mockito.when(apiProperties.getAutoKey())
                .thenReturn("e0c065a19b2d48f0");
    }

    @Test
    public void getJumpUrl() {
        String zhnx09760 = gscAutoOrderServiceAdapter.getJumpUrl("ZHNX09760", "wx-sss", "https://hub.chinalife-p.com.cn/mescifp/prod/index.html?userCode=QdEwhdrR8%2FXIDGzM%2B%2BbR7tM5nBs37gj%2FUgzepGMfJZM%3D&bid=15000000&systemSource=JS0&userinfo={userInfo}&extparams={extparams}");
        Assert.assertEquals("https://hub.chinalife-p.com.cn/mescifp/prod/index.html?userCode=QdEwhdrR8%2FXIDGzM%2B%2BbR7tM5nBs37gj%2FUgzepGMfJZM%3D&bid=15000000&systemSource=JS0&userinfo=Iik%2FYur%2BxBi8Fs8tS1ZiaJMNFBuDz1%2F71u7Jsrosqr4X3YnWqmKfeQX73VhpIDZH&extparams=wx-sss",
                zhnx09760);
    }
}
