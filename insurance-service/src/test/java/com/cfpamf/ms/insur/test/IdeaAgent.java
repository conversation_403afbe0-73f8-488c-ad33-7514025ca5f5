package com.cfpamf.ms.insur.test;

import com.cfpamf.ms.bms.facade.util.JwtHelper;
import com.cfpamf.ms.bms.facade.vo.JwtUserInfo;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.util.regex.Pattern;

/**
 * <AUTHOR> 2020/10/21 15:48
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class IdeaAgent {

    public static void main(String[] args) {
        JwtUserInfo userFromToken = new JwtHelper().getUserFromToken("eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiItMSIsImVtcGxveWVlSWQiOiIiLCJoclVzZXJJZCI6IiIsImFjY291bnQiOiIxMzgxODU1NzQyNiIsInVzZXJOYW1lIjoi5pyx6JOT6JW-Iiwiam9iTnVtYmVyIjoiMTAwMDEzIiwibWFzdGVySm9iTnVtYmVyIjoiIiwib3JnSWQiOiIiLCJock9yZ0lkIjoiIiwiaHJPcmdDb2RlIjoiIiwiaHJPcmdOYW1lIjoiIiwiaHJPcmdUcmVlUGF0aCI6IiIsInVzZXJUeXBlIjoiMjAiLCJyYW5kb21Db2RlIjoiQjVFRUFGREMtNzcyQS00QTdFLTgxQUQtOTA2Mzc0REEwOEQzIiwidXNlU3NvIjoiIiwic3lzdGVtSWQiOiIiLCJjcmVhdGVUaW1lIjoiMTYyODg2NTU4MjE5NyIsImdyYXkiOjB9.PAomkdZbIYnsKXF99wxKb3blCITJ_neJtTwn-fq2dTYpl1Nc7qGYFSbVQ9Ipkgv69STW9n8_ePGrSIcSony34w");
        System.err.println(userFromToken);
        Pattern.matches("/back/company/\\d+/\\w+/import", "/back/company/232/occuplCategory/import");

        JwtUserInfo userFromToken1 = new JwtHelper().getUserFromToken("eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiIxMDA0NiIsImVtcGxveWVlSWQiOiIxMDY2NyIsImhyVXNlcklkIjoiMTM1MzQ4MDkwIiwiYWNjb3VudCI6IjE4MjI5Njk1Nzk0IiwidXNlck5hbWUiOiLokovkupHpvpkiLCJqb2JOdW1iZXIiOiJaSE5YMTMyMjMiLCJtYXN0ZXJKb2JOdW1iZXIiOiJaSE5YMTMyMjMiLCJvcmdJZCI6Ijk5MSIsImhyT3JnSWQiOiI5MDAxMDUyNDkiLCJock9yZ0NvZGUiOiJaSElMRVoiLCJock9yZ05hbWUiOiLotKjph4_kuoznu4QiLCJock9yZ1RyZWVQYXRoIjoiOTAwMTA1MTUzLzI4MjcxNy8zNjAzMTcvNDQ3OTQxLzQ0Nzk0Ni85MDAxMDUyNDkiLCJ1c2VyVHlwZSI6IjIiLCJyYW5kb21Db2RlIjoiNTFEOUM5MTUtMzA5QS00MzgwLUFFMDEtODYyMEEzMTVBMDMxIiwidXNlU3NvIjoidHJ1ZSIsInN5c3RlbUlkIjoiMyIsImNyZWF0ZVRpbWUiOiIxNjMwMTI5NDc4MjczIiwiZ3JheSI6MX0.tG4nwO9zc2acZPXm2eJWJO1YA-5hUbIlA186mrg5ySc6yAGrsyJ1UwAPpGXyf36yfaPJnf7IThAeV_HASNqS7g");

        System.err.println(userFromToken1);

        userFromToken1 = new JwtHelper().getUserFromToken("yJhbGciOiJIUzUxMiJ9.eyJzdWIiOiI4NTc1IiwiZW1wbG95ZWVJZCI6Ijg5NDYiLCJoclVzZXJJZCI6IjEyODAwNzQ0MiIsImFjY291bnQiOiIxMzE4ODY3NzAwMyIsInVzZXJOYW1lIjoi5ZGo546l5b2kIiwiam9iTnVtYmVyIjoiWkhOWDExNTE3IiwibWFzdGVySm9iTnVtYmVyIjoiWkhOWDExNTE3Iiwib3JnSWQiOiIyMDciLCJock9yZ0lkIjoiMzA5MjgxIiwiaHJPcmdDb2RlIjoiTE5YRiIsImhyT3JnTmFtZSI6Iuilv-S4sCIsImhyT3JnVHJlZVBhdGgiOiI5MDAxMDUxNTMvMjgyNzMyLzI4NDUxNS85MDAxMDUxODQvMzA5MjgxIiwidXNlclR5cGUiOiIyIiwicmFuZG9tQ29kZSI6IjJENzVERjI4LTU1ODgtNDMwOC05OUZCLUFGMkQzQThBRUVDOSIsInVzZVNzbyI6InRydWUiLCJzeXN0ZW1JZCI6IjMiLCJjcmVhdGVUaW1lIjoiMTYzMDEzMjI3ODkxMSIsImdyYXkiOjB9.7rryz3ldyTFUUWsYCX40tJMUuB0kmNRuj9MwYCBSHWdYaxwRtJaAhykIlju7YwdYj1vKLwcmJ31nER_8LyJzZw");

        System.err.println(userFromToken1);
    }

}
