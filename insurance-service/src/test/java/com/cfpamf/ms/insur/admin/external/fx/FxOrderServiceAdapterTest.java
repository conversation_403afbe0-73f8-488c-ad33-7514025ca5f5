package com.cfpamf.ms.insur.admin.external.fx;

import com.cfpamf.common.ms.util.DateUtil;
import com.cfpamf.ms.insur.admin.enums.order.OrderSourceEnum;
import com.cfpamf.ms.insur.admin.external.OrderQueryRequest;
import com.cfpamf.ms.insur.admin.external.OrderQueryResponse;
import junit.framework.TestCase;
import org.junit.FixMethodOrder;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.MethodSorters;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

/*@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("dev")
@FixMethodOrder(MethodSorters.NAME_ASCENDING)*/
public class FxOrderServiceAdapterTest {
   /* @Autowired
    FxOrderServiceAdapter service;

    @Test
    public void genAgentUrl() {
        try {
            String url = service.genAgentUrl(OrderSourceEnum.ZHNX.getCode(),"cnbj0409","","191203026");
            System.out.println(url);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }*/
}