package com.cfpamf.ms.insur.admin.service.commission;

import com.cfpamf.ms.insur.admin.dao.safes.commission.SmOrderCommissionDetailMapper;
import com.cfpamf.ms.insur.admin.dao.safes.commission.SmOrderCommissionItemMapper;
import com.cfpamf.ms.insur.admin.pojo.dto.commission.*;
import com.cfpamf.ms.insur.admin.pojo.dto.order.OrderCommissionInsuredInfoDTO;
import com.cfpamf.ms.insur.admin.pojo.dto.order.OrderRiskCorrectDTO;
import com.cfpamf.ms.insur.admin.pojo.dto.renewal.RenewalTermDTO;
import com.cfpamf.ms.insur.admin.pojo.po.commission.SmCommissionDetailItemPO;
import com.cfpamf.ms.insur.admin.pojo.po.commission.SmCommissionDetailPO;
import com.cfpamf.ms.insur.admin.pojo.po.commission.SystemCommissionConfig;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class CommissionCalcProcessServiceTest {

    @Mock
    private CommissionExternalService mockCommissionExternalService;
    @Mock
    private CommissionConfigQueryService mockCommissionConfigQueryService;
    @Mock
    private SmOrderCommissionDetailMapper mockSmOrderCommissionDetailMapper;
    @Mock
    private SmOrderCommissionItemMapper mockSmOrderCommissionItemMapper;
    @Mock
    private CommissionCalcService mockCommissionCalcService;

    @InjectMocks
    private CommissionCalcProcessService commissionCalcProcessServiceUnderTest;

    @Test
    public void testCalcRenewalCommission() throws Exception {
        // Setup
        final CommissionCalcProcessDTO processDTO = new CommissionCalcProcessDTO();
        processDTO.setOrderId("orderId");
        processDTO.setProductId(0);
        processDTO.setProductType("productType");
        processDTO.setMatchingTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        processDTO.setTermNum(0);
        processDTO.setCommissionConfigInfo(new HashMap<>());
        final CommissionRateDTO commissionRateDTO = new CommissionRateDTO();
        processDTO.setOldCommissionConfigInfo(Arrays.asList(commissionRateDTO));
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO.setCommissionId(0);
        commissionCalcOrderInfoDTO.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO.setAppStatus("appStatus");
        processDTO.setOrderInfo(Arrays.asList(commissionCalcOrderInfoDTO));
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO1 = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO1.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO1.setCommissionId(0);
        commissionCalcOrderInfoDTO1.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO1.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO1.setAppStatus("appStatus");
        processDTO.setRefundOrderInfo(Arrays.asList(commissionCalcOrderInfoDTO1));
        final OrderRiskCorrectDTO orderRiskCorrectDTO = new OrderRiskCorrectDTO();
        processDTO.setRiskCorrectDTOS(Arrays.asList(orderRiskCorrectDTO));
        processDTO.setItemMap(new HashMap<>());
        final CalcCommissionItemResultDTO calcCommissionItemResultDTO = new CalcCommissionItemResultDTO();
        processDTO.setItemResults(Arrays.asList(calcCommissionItemResultDTO));
        processDTO.setCommissionUserId("commissionUserId");
        processDTO.setBusinessType("businessType");

        final OrderCommissionInsuredInfoDTO orderCommissionInsuredInfoDTO = new OrderCommissionInsuredInfoDTO();
        orderCommissionInsuredInfoDTO.setFhOrderId("fhOrderId");
        orderCommissionInsuredInfoDTO.setInsuredId(0);
        orderCommissionInsuredInfoDTO.setCompanyId(0);
        orderCommissionInsuredInfoDTO.setProductId(0);
        orderCommissionInsuredInfoDTO.setCommissionId(0);
        final List<OrderCommissionInsuredInfoDTO> insuredInfoDTOList = Arrays.asList(orderCommissionInsuredInfoDTO);
        final RenewalTermDTO renewalTermDTO = new RenewalTermDTO();
        renewalTermDTO.setOrderId("orderId");
        renewalTermDTO.setPolicyNo("policyNo");
        renewalTermDTO.setOrderAmount(new BigDecimal("0.00"));
        renewalTermDTO.setTermNum(0);
        renewalTermDTO.setChannel("channel");
        final List<RenewalTermDTO> renewalTermDTOList = Arrays.asList(renewalTermDTO);

        // Configure CommissionConfigQueryService.listConfigByProductId(...).
        final SystemCommissionConfig systemCommissionConfig = new SystemCommissionConfig();
        systemCommissionConfig.setType(0);
        systemCommissionConfig.setCompanyId(0);
        systemCommissionConfig.setProductId(0);
        systemCommissionConfig.setStartTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        systemCommissionConfig.setEndTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SystemCommissionConfig> systemCommissionConfigs = Arrays.asList(systemCommissionConfig);
        mockCommissionConfigQueryService.listConfigByProductId(0);

        // Configure CommissionConfigQueryService.listCalcCommissionDetailByConfigId(...).
        final CommissionCalcConfigDetailDTO commissionCalcConfigDetailDTO = new CommissionCalcConfigDetailDTO();
        commissionCalcConfigDetailDTO.setId(0);
        commissionCalcConfigDetailDTO.setConfigId(0);
        commissionCalcConfigDetailDTO.setPeriodNum(0);
        commissionCalcConfigDetailDTO.setPlanId(0);
        commissionCalcConfigDetailDTO.setRiskId(0);
        final List<CommissionCalcConfigDetailDTO> commissionCalcConfigDetailDTOS = Arrays.asList(
                commissionCalcConfigDetailDTO);
        mockCommissionConfigQueryService.listCalcCommissionDetailByConfigId(0);

        // Configure CommissionCalcService.calc(...).
        final CalcCommissionItemResultDTO calcCommissionItemResultDTO1 = new CalcCommissionItemResultDTO();
        calcCommissionItemResultDTO1.setOrderId("orderId");
        calcCommissionItemResultDTO1.setPolicyNo("policyNo");
        calcCommissionItemResultDTO1.setInsuredIdNumber("insuredIdNumber");
        calcCommissionItemResultDTO1.setPolicyStatus("policyStatus");
        calcCommissionItemResultDTO1.setRiskStatus("riskStatus");
        final List<CalcCommissionItemResultDTO> calcCommissionItemResultDTOS = Arrays.asList(
                calcCommissionItemResultDTO1);
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO2 = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO2.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO2.setCommissionId(0);
        commissionCalcOrderInfoDTO2.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO2.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO2.setAppStatus("appStatus");
        final List<CommissionCalcOrderInfoDTO> orderInfoDTOList = Arrays.asList(commissionCalcOrderInfoDTO2);
        final CommissionCalcConfigInfoDTO configInfoDTO = new CommissionCalcConfigInfoDTO();
        configInfoDTO.setId(0);
        configInfoDTO.setType(0);
        configInfoDTO.setCompanyId(0);
        final CommissionCalcConfigDetailDTO commissionCalcConfigDetailDTO1 = new CommissionCalcConfigDetailDTO();
        commissionCalcConfigDetailDTO1.setPeriodNum(0);
        configInfoDTO.setConfigDetailDTOList(Arrays.asList(commissionCalcConfigDetailDTO1));
        mockCommissionCalcService.calc(0, 0, orderInfoDTOList, configInfoDTO, new CalcErrorDTO("errorMsg","errorMsgNoAlarm"),
                "commissionUserId", "businessType");

        // Configure CommissionCalcService.calcCorrectCommission(...).
        final CalcCommissionItemResultDTO calcCommissionItemResultDTO2 = new CalcCommissionItemResultDTO();
        calcCommissionItemResultDTO2.setOrderId("orderId");
        calcCommissionItemResultDTO2.setPolicyNo("policyNo");
        calcCommissionItemResultDTO2.setInsuredIdNumber("insuredIdNumber");
        calcCommissionItemResultDTO2.setPolicyStatus("policyStatus");
        calcCommissionItemResultDTO2.setRiskStatus("riskStatus");
        final List<CalcCommissionItemResultDTO> calcCommissionItemResultDTOS1 = Arrays.asList(
                calcCommissionItemResultDTO2);
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO3 = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO3.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO3.setCommissionId(0);
        commissionCalcOrderInfoDTO3.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO3.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO3.setAppStatus("appStatus");
        final List<CommissionCalcOrderInfoDTO> orderInfoDTOList1 = Arrays.asList(commissionCalcOrderInfoDTO3);
        final OrderRiskCorrectDTO orderRiskCorrectDTO1 = new OrderRiskCorrectDTO();
        orderRiskCorrectDTO1.setOrderId("orderId");
        orderRiskCorrectDTO1.setPolicyNo("policyNo");
        orderRiskCorrectDTO1.setCorrectId("correctId");
        orderRiskCorrectDTO1.setCorrectType("correctType");
        orderRiskCorrectDTO1.setRiskId(0);
        final List<OrderRiskCorrectDTO> correctDTOList = Arrays.asList(orderRiskCorrectDTO1);
        final SmCommissionDetailItemPO smCommissionDetailItemPO = new SmCommissionDetailItemPO();
        smCommissionDetailItemPO.setOrderId("orderId");
        smCommissionDetailItemPO.setPolicyNo("policyNo");
        smCommissionDetailItemPO.setInsuredIdNumber("insuredIdNumber");
        smCommissionDetailItemPO.setPolicyStatus("policyStatus");
        smCommissionDetailItemPO.setRiskStatus("riskStatus");
        smCommissionDetailItemPO.setPlanId(0);
        smCommissionDetailItemPO.setRiskId(0);
        smCommissionDetailItemPO.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smCommissionDetailItemPO.setPlanAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setOriginalAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setTermNum(0);
        smCommissionDetailItemPO.setCommissionType(0);
        smCommissionDetailItemPO.setCommissionRate(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setCommissionAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setCommissionUserId("commissionUserId");
        smCommissionDetailItemPO.setBusinessType("businessType");
        smCommissionDetailItemPO.setBusinessId("businessId");
        smCommissionDetailItemPO.setImportFlag(0);
        final List<SmCommissionDetailItemPO> itemPOs = Arrays.asList(smCommissionDetailItemPO);
        mockCommissionCalcService.calcCorrectCommission(0, orderInfoDTOList1, correctDTOList, itemPOs,
                new CalcErrorDTO("errorMsg","errorMsgNoAlarm"));

        // Configure CommissionCalcService.calcNewRefundDetailItem(...).
        final CalcCommissionItemResultDTO calcCommissionItemResultDTO3 = new CalcCommissionItemResultDTO();
        calcCommissionItemResultDTO3.setOrderId("orderId");
        calcCommissionItemResultDTO3.setPolicyNo("policyNo");
        calcCommissionItemResultDTO3.setInsuredIdNumber("insuredIdNumber");
        calcCommissionItemResultDTO3.setPolicyStatus("policyStatus");
        calcCommissionItemResultDTO3.setRiskStatus("riskStatus");
        final List<CalcCommissionItemResultDTO> calcCommissionItemResultDTOS2 = Arrays.asList(
                calcCommissionItemResultDTO3);
        final CommissionCalcOrderInfoDTO orderInfo = new CommissionCalcOrderInfoDTO();
        orderInfo.setProductAttrCode("productAttrCode");
        orderInfo.setCommissionId(0);
        orderInfo.setOriginalFhOrderId("originalFhOrderId");
        orderInfo.setOriginalCommissionId(0);
        orderInfo.setAppStatus("appStatus");
        mockCommissionCalcService.calcNewRefundDetailItem(orderInfo, new HashMap<>());

        // Run the test
        commissionCalcProcessServiceUnderTest.calcRenewalCommission(processDTO, insuredInfoDTOList, renewalTermDTOList);

        // Verify the results
        // Confirm CommissionExternalService.getRenewalCommissionCalcOrderParam(...).
        final CommissionCalcProcessDTO processDTO1 = new CommissionCalcProcessDTO();
        processDTO1.setOrderId("orderId");
        processDTO1.setProductId(0);
        processDTO1.setProductType("productType");
        processDTO1.setMatchingTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        processDTO1.setTermNum(0);
        processDTO1.setCommissionConfigInfo(new HashMap<>());
        final CommissionRateDTO commissionRateDTO1 = new CommissionRateDTO();
        processDTO1.setOldCommissionConfigInfo(Arrays.asList(commissionRateDTO1));
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO4 = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO4.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO4.setCommissionId(0);
        commissionCalcOrderInfoDTO4.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO4.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO4.setAppStatus("appStatus");
        processDTO1.setOrderInfo(Arrays.asList(commissionCalcOrderInfoDTO4));
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO5 = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO5.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO5.setCommissionId(0);
        commissionCalcOrderInfoDTO5.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO5.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO5.setAppStatus("appStatus");
        processDTO1.setRefundOrderInfo(Arrays.asList(commissionCalcOrderInfoDTO5));
        final OrderRiskCorrectDTO orderRiskCorrectDTO2 = new OrderRiskCorrectDTO();
        processDTO1.setRiskCorrectDTOS(Arrays.asList(orderRiskCorrectDTO2));
        processDTO1.setItemMap(new HashMap<>());
        final CalcCommissionItemResultDTO calcCommissionItemResultDTO4 = new CalcCommissionItemResultDTO();
        processDTO1.setItemResults(Arrays.asList(calcCommissionItemResultDTO4));
        processDTO1.setCommissionUserId("commissionUserId");
        processDTO1.setBusinessType("businessType");
        final OrderCommissionInsuredInfoDTO orderCommissionInsuredInfoDTO1 = new OrderCommissionInsuredInfoDTO();
        orderCommissionInsuredInfoDTO1.setFhOrderId("fhOrderId");
        orderCommissionInsuredInfoDTO1.setInsuredId(0);
        orderCommissionInsuredInfoDTO1.setCompanyId(0);
        orderCommissionInsuredInfoDTO1.setProductId(0);
        orderCommissionInsuredInfoDTO1.setCommissionId(0);
        final List<OrderCommissionInsuredInfoDTO> insuredInfoDTOList1 = Arrays.asList(orderCommissionInsuredInfoDTO1);
        final RenewalTermDTO renewalTermDTO1 = new RenewalTermDTO();
        renewalTermDTO1.setOrderId("orderId");
        renewalTermDTO1.setPolicyNo("policyNo");
        renewalTermDTO1.setOrderAmount(new BigDecimal("0.00"));
        renewalTermDTO1.setTermNum(0);
        renewalTermDTO1.setChannel("channel");
        final List<RenewalTermDTO> renewalTermDTOList1 = Arrays.asList(renewalTermDTO1);
        mockCommissionExternalService.getRenewalCommissionCalcOrderParam(processDTO1, insuredInfoDTOList1,
                renewalTermDTOList1);

        // Confirm SmOrderCommissionItemMapper.batchInsertCommissionItem(...).
        final SmCommissionDetailItemPO smCommissionDetailItemPO1 = new SmCommissionDetailItemPO();
        smCommissionDetailItemPO1.setOrderId("orderId");
        smCommissionDetailItemPO1.setPolicyNo("policyNo");
        smCommissionDetailItemPO1.setInsuredIdNumber("insuredIdNumber");
        smCommissionDetailItemPO1.setPolicyStatus("policyStatus");
        smCommissionDetailItemPO1.setRiskStatus("riskStatus");
        smCommissionDetailItemPO1.setPlanId(0);
        smCommissionDetailItemPO1.setRiskId(0);
        smCommissionDetailItemPO1.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smCommissionDetailItemPO1.setPlanAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO1.setAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO1.setOriginalAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO1.setTermNum(0);
        smCommissionDetailItemPO1.setCommissionType(0);
        smCommissionDetailItemPO1.setCommissionRate(new BigDecimal("0.00"));
        smCommissionDetailItemPO1.setCommissionAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO1.setCommissionUserId("commissionUserId");
        smCommissionDetailItemPO1.setBusinessType("businessType");
        smCommissionDetailItemPO1.setBusinessId("businessId");
        smCommissionDetailItemPO1.setImportFlag(0);
        final List<SmCommissionDetailItemPO> commissionConfigItemList = Arrays.asList(smCommissionDetailItemPO1);
        mockSmOrderCommissionItemMapper.batchInsertCommissionItem(commissionConfigItemList);

        // Confirm SmOrderCommissionItemMapper.batchInsertCommissionItemExceptImport(...).
        final SmCommissionDetailItemPO smCommissionDetailItemPO2 = new SmCommissionDetailItemPO();
        smCommissionDetailItemPO2.setOrderId("orderId");
        smCommissionDetailItemPO2.setPolicyNo("policyNo");
        smCommissionDetailItemPO2.setInsuredIdNumber("insuredIdNumber");
        smCommissionDetailItemPO2.setPolicyStatus("policyStatus");
        smCommissionDetailItemPO2.setRiskStatus("riskStatus");
        smCommissionDetailItemPO2.setPlanId(0);
        smCommissionDetailItemPO2.setRiskId(0);
        smCommissionDetailItemPO2.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smCommissionDetailItemPO2.setPlanAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO2.setAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO2.setOriginalAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO2.setTermNum(0);
        smCommissionDetailItemPO2.setCommissionType(0);
        smCommissionDetailItemPO2.setCommissionRate(new BigDecimal("0.00"));
        smCommissionDetailItemPO2.setCommissionAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO2.setCommissionUserId("commissionUserId");
        smCommissionDetailItemPO2.setBusinessType("businessType");
        smCommissionDetailItemPO2.setBusinessId("businessId");
        smCommissionDetailItemPO2.setImportFlag(0);
        final List<SmCommissionDetailItemPO> commissionConfigItemList1 = Arrays.asList(smCommissionDetailItemPO2);
        mockSmOrderCommissionItemMapper.batchInsertCommissionItemExceptImport(commissionConfigItemList1);

        // Confirm SmOrderCommissionDetailMapper.batchReCalcCommissionDetail(...).
        final SmCommissionDetailPO smCommissionDetailPO = new SmCommissionDetailPO();
        smCommissionDetailPO.setOrderId("orderId");
        smCommissionDetailPO.setPolicyNo("policyNo");
        smCommissionDetailPO.setInsuredIdNumber("insuredIdNumber");
        smCommissionDetailPO.setPolicyStatus("policyStatus");
        smCommissionDetailPO.setPlanId(0);
        smCommissionDetailPO.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smCommissionDetailPO.setAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setOriginalAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setPaymentRate(new BigDecimal("0.00"));
        smCommissionDetailPO.setPaymentAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setSettlementRate(new BigDecimal("0.00"));
        smCommissionDetailPO.setSettlementAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setConversionRate(new BigDecimal("0.00"));
        smCommissionDetailPO.setConversionAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setTermNum(0);
        smCommissionDetailPO.setPaymentRiskJson("paymentRiskJson");
        smCommissionDetailPO.setSettlementRiskJson("settlementRiskJson");
        smCommissionDetailPO.setConversionRiskJson("conversionRiskJson");
        smCommissionDetailPO.setPAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setSAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setCAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setCommissionUserId("commissionUserId");
        smCommissionDetailPO.setBusinessType("businessType");
        smCommissionDetailPO.setBusinessId("businessId");
        smCommissionDetailPO.setImportFlag(0);
        final List<SmCommissionDetailPO> commissionConfigDetailList = Arrays.asList(smCommissionDetailPO);
        mockSmOrderCommissionDetailMapper.batchReCalcCommissionDetail(commissionConfigDetailList);

        // Confirm SmOrderCommissionDetailMapper.batchInsertCommissionDetail(...).
        final SmCommissionDetailPO smCommissionDetailPO1 = new SmCommissionDetailPO();
        smCommissionDetailPO1.setOrderId("orderId");
        smCommissionDetailPO1.setPolicyNo("policyNo");
        smCommissionDetailPO1.setInsuredIdNumber("insuredIdNumber");
        smCommissionDetailPO1.setPolicyStatus("policyStatus");
        smCommissionDetailPO1.setPlanId(0);
        smCommissionDetailPO1.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smCommissionDetailPO1.setAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setOriginalAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setPaymentRate(new BigDecimal("0.00"));
        smCommissionDetailPO1.setPaymentAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setSettlementRate(new BigDecimal("0.00"));
        smCommissionDetailPO1.setSettlementAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setConversionRate(new BigDecimal("0.00"));
        smCommissionDetailPO1.setConversionAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setTermNum(0);
        smCommissionDetailPO1.setPaymentRiskJson("paymentRiskJson");
        smCommissionDetailPO1.setSettlementRiskJson("settlementRiskJson");
        smCommissionDetailPO1.setConversionRiskJson("conversionRiskJson");
        smCommissionDetailPO1.setPAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setSAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setCAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setCommissionUserId("commissionUserId");
        smCommissionDetailPO1.setBusinessType("businessType");
        smCommissionDetailPO1.setBusinessId("businessId");
        smCommissionDetailPO1.setImportFlag(0);
        final List<SmCommissionDetailPO> commissionConfigDetailList1 = Arrays.asList(smCommissionDetailPO1);
        mockSmOrderCommissionDetailMapper.batchInsertCommissionDetail(commissionConfigDetailList1);
    }

    @Test
    public void testCalcRenewalCommission_CommissionConfigQueryServiceListConfigByProductIdReturnsNoItems() throws Exception {
        // Setup
        final CommissionCalcProcessDTO processDTO = new CommissionCalcProcessDTO();
        processDTO.setOrderId("orderId");
        processDTO.setProductId(0);
        processDTO.setProductType("productType");
        processDTO.setMatchingTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        processDTO.setTermNum(0);
        processDTO.setCommissionConfigInfo(new HashMap<>());
        final CommissionRateDTO commissionRateDTO = new CommissionRateDTO();
        processDTO.setOldCommissionConfigInfo(Arrays.asList(commissionRateDTO));
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO.setCommissionId(0);
        commissionCalcOrderInfoDTO.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO.setAppStatus("appStatus");
        processDTO.setOrderInfo(Arrays.asList(commissionCalcOrderInfoDTO));
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO1 = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO1.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO1.setCommissionId(0);
        commissionCalcOrderInfoDTO1.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO1.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO1.setAppStatus("appStatus");
        processDTO.setRefundOrderInfo(Arrays.asList(commissionCalcOrderInfoDTO1));
        final OrderRiskCorrectDTO orderRiskCorrectDTO = new OrderRiskCorrectDTO();
        processDTO.setRiskCorrectDTOS(Arrays.asList(orderRiskCorrectDTO));
        processDTO.setItemMap(new HashMap<>());
        final CalcCommissionItemResultDTO calcCommissionItemResultDTO = new CalcCommissionItemResultDTO();
        processDTO.setItemResults(Arrays.asList(calcCommissionItemResultDTO));
        processDTO.setCommissionUserId("commissionUserId");
        processDTO.setBusinessType("businessType");

        final OrderCommissionInsuredInfoDTO orderCommissionInsuredInfoDTO = new OrderCommissionInsuredInfoDTO();
        orderCommissionInsuredInfoDTO.setFhOrderId("fhOrderId");
        orderCommissionInsuredInfoDTO.setInsuredId(0);
        orderCommissionInsuredInfoDTO.setCompanyId(0);
        orderCommissionInsuredInfoDTO.setProductId(0);
        orderCommissionInsuredInfoDTO.setCommissionId(0);
        final List<OrderCommissionInsuredInfoDTO> insuredInfoDTOList = Arrays.asList(orderCommissionInsuredInfoDTO);
        final RenewalTermDTO renewalTermDTO = new RenewalTermDTO();
        renewalTermDTO.setOrderId("orderId");
        renewalTermDTO.setPolicyNo("policyNo");
        renewalTermDTO.setOrderAmount(new BigDecimal("0.00"));
        renewalTermDTO.setTermNum(0);
        renewalTermDTO.setChannel("channel");
        final List<RenewalTermDTO> renewalTermDTOList = Arrays.asList(renewalTermDTO);
        mockCommissionConfigQueryService.listConfigByProductId(0);

        // Configure CommissionConfigQueryService.listCalcCommissionDetailByConfigId(...).
        final CommissionCalcConfigDetailDTO commissionCalcConfigDetailDTO = new CommissionCalcConfigDetailDTO();
        commissionCalcConfigDetailDTO.setId(0);
        commissionCalcConfigDetailDTO.setConfigId(0);
        commissionCalcConfigDetailDTO.setPeriodNum(0);
        commissionCalcConfigDetailDTO.setPlanId(0);
        commissionCalcConfigDetailDTO.setRiskId(0);
        final List<CommissionCalcConfigDetailDTO> commissionCalcConfigDetailDTOS = Arrays.asList(
                commissionCalcConfigDetailDTO);
        mockCommissionConfigQueryService.listCalcCommissionDetailByConfigId(0);

        // Configure CommissionCalcService.calc(...).
        final CalcCommissionItemResultDTO calcCommissionItemResultDTO1 = new CalcCommissionItemResultDTO();
        calcCommissionItemResultDTO1.setOrderId("orderId");
        calcCommissionItemResultDTO1.setPolicyNo("policyNo");
        calcCommissionItemResultDTO1.setInsuredIdNumber("insuredIdNumber");
        calcCommissionItemResultDTO1.setPolicyStatus("policyStatus");
        calcCommissionItemResultDTO1.setRiskStatus("riskStatus");
        final List<CalcCommissionItemResultDTO> calcCommissionItemResultDTOS = Arrays.asList(
                calcCommissionItemResultDTO1);
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO2 = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO2.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO2.setCommissionId(0);
        commissionCalcOrderInfoDTO2.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO2.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO2.setAppStatus("appStatus");
        final List<CommissionCalcOrderInfoDTO> orderInfoDTOList = Arrays.asList(commissionCalcOrderInfoDTO2);
        final CommissionCalcConfigInfoDTO configInfoDTO = new CommissionCalcConfigInfoDTO();
        configInfoDTO.setId(0);
        configInfoDTO.setType(0);
        configInfoDTO.setCompanyId(0);
        final CommissionCalcConfigDetailDTO commissionCalcConfigDetailDTO1 = new CommissionCalcConfigDetailDTO();
        commissionCalcConfigDetailDTO1.setPeriodNum(0);
        configInfoDTO.setConfigDetailDTOList(Arrays.asList(commissionCalcConfigDetailDTO1));
        mockCommissionCalcService.calc(0, 0, orderInfoDTOList, configInfoDTO, new CalcErrorDTO("errorMsg","errorMsgNoAlarm"),
                "commissionUserId", "businessType");

        // Configure CommissionCalcService.calcCorrectCommission(...).
        final CalcCommissionItemResultDTO calcCommissionItemResultDTO2 = new CalcCommissionItemResultDTO();
        calcCommissionItemResultDTO2.setOrderId("orderId");
        calcCommissionItemResultDTO2.setPolicyNo("policyNo");
        calcCommissionItemResultDTO2.setInsuredIdNumber("insuredIdNumber");
        calcCommissionItemResultDTO2.setPolicyStatus("policyStatus");
        calcCommissionItemResultDTO2.setRiskStatus("riskStatus");
        final List<CalcCommissionItemResultDTO> calcCommissionItemResultDTOS1 = Arrays.asList(
                calcCommissionItemResultDTO2);
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO3 = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO3.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO3.setCommissionId(0);
        commissionCalcOrderInfoDTO3.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO3.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO3.setAppStatus("appStatus");
        final List<CommissionCalcOrderInfoDTO> orderInfoDTOList1 = Arrays.asList(commissionCalcOrderInfoDTO3);
        final OrderRiskCorrectDTO orderRiskCorrectDTO1 = new OrderRiskCorrectDTO();
        orderRiskCorrectDTO1.setOrderId("orderId");
        orderRiskCorrectDTO1.setPolicyNo("policyNo");
        orderRiskCorrectDTO1.setCorrectId("correctId");
        orderRiskCorrectDTO1.setCorrectType("correctType");
        orderRiskCorrectDTO1.setRiskId(0);
        final List<OrderRiskCorrectDTO> correctDTOList = Arrays.asList(orderRiskCorrectDTO1);
        final SmCommissionDetailItemPO smCommissionDetailItemPO = new SmCommissionDetailItemPO();
        smCommissionDetailItemPO.setOrderId("orderId");
        smCommissionDetailItemPO.setPolicyNo("policyNo");
        smCommissionDetailItemPO.setInsuredIdNumber("insuredIdNumber");
        smCommissionDetailItemPO.setPolicyStatus("policyStatus");
        smCommissionDetailItemPO.setRiskStatus("riskStatus");
        smCommissionDetailItemPO.setPlanId(0);
        smCommissionDetailItemPO.setRiskId(0);
        smCommissionDetailItemPO.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smCommissionDetailItemPO.setPlanAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setOriginalAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setTermNum(0);
        smCommissionDetailItemPO.setCommissionType(0);
        smCommissionDetailItemPO.setCommissionRate(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setCommissionAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setCommissionUserId("commissionUserId");
        smCommissionDetailItemPO.setBusinessType("businessType");
        smCommissionDetailItemPO.setBusinessId("businessId");
        smCommissionDetailItemPO.setImportFlag(0);
        final List<SmCommissionDetailItemPO> itemPOs = Arrays.asList(smCommissionDetailItemPO);
        mockCommissionCalcService.calcCorrectCommission(0, orderInfoDTOList1, correctDTOList, itemPOs,
                new CalcErrorDTO("errorMsg","errorMsgNoAlarm"));

        // Configure CommissionCalcService.calcNewRefundDetailItem(...).
        final CalcCommissionItemResultDTO calcCommissionItemResultDTO3 = new CalcCommissionItemResultDTO();
        calcCommissionItemResultDTO3.setOrderId("orderId");
        calcCommissionItemResultDTO3.setPolicyNo("policyNo");
        calcCommissionItemResultDTO3.setInsuredIdNumber("insuredIdNumber");
        calcCommissionItemResultDTO3.setPolicyStatus("policyStatus");
        calcCommissionItemResultDTO3.setRiskStatus("riskStatus");
        final List<CalcCommissionItemResultDTO> calcCommissionItemResultDTOS2 = Arrays.asList(
                calcCommissionItemResultDTO3);
        final CommissionCalcOrderInfoDTO orderInfo = new CommissionCalcOrderInfoDTO();
        orderInfo.setProductAttrCode("productAttrCode");
        orderInfo.setCommissionId(0);
        orderInfo.setOriginalFhOrderId("originalFhOrderId");
        orderInfo.setOriginalCommissionId(0);
        orderInfo.setAppStatus("appStatus");
        mockCommissionCalcService.calcNewRefundDetailItem(orderInfo, new HashMap<>());

        // Run the test
        commissionCalcProcessServiceUnderTest.calcRenewalCommission(processDTO, insuredInfoDTOList, renewalTermDTOList);

        // Verify the results
        // Confirm CommissionExternalService.getRenewalCommissionCalcOrderParam(...).
        final CommissionCalcProcessDTO processDTO1 = new CommissionCalcProcessDTO();
        processDTO1.setOrderId("orderId");
        processDTO1.setProductId(0);
        processDTO1.setProductType("productType");
        processDTO1.setMatchingTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        processDTO1.setTermNum(0);
        processDTO1.setCommissionConfigInfo(new HashMap<>());
        final CommissionRateDTO commissionRateDTO1 = new CommissionRateDTO();
        processDTO1.setOldCommissionConfigInfo(Arrays.asList(commissionRateDTO1));
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO4 = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO4.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO4.setCommissionId(0);
        commissionCalcOrderInfoDTO4.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO4.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO4.setAppStatus("appStatus");
        processDTO1.setOrderInfo(Arrays.asList(commissionCalcOrderInfoDTO4));
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO5 = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO5.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO5.setCommissionId(0);
        commissionCalcOrderInfoDTO5.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO5.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO5.setAppStatus("appStatus");
        processDTO1.setRefundOrderInfo(Arrays.asList(commissionCalcOrderInfoDTO5));
        final OrderRiskCorrectDTO orderRiskCorrectDTO2 = new OrderRiskCorrectDTO();
        processDTO1.setRiskCorrectDTOS(Arrays.asList(orderRiskCorrectDTO2));
        processDTO1.setItemMap(new HashMap<>());
        final CalcCommissionItemResultDTO calcCommissionItemResultDTO4 = new CalcCommissionItemResultDTO();
        processDTO1.setItemResults(Arrays.asList(calcCommissionItemResultDTO4));
        processDTO1.setCommissionUserId("commissionUserId");
        processDTO1.setBusinessType("businessType");
        final OrderCommissionInsuredInfoDTO orderCommissionInsuredInfoDTO1 = new OrderCommissionInsuredInfoDTO();
        orderCommissionInsuredInfoDTO1.setFhOrderId("fhOrderId");
        orderCommissionInsuredInfoDTO1.setInsuredId(0);
        orderCommissionInsuredInfoDTO1.setCompanyId(0);
        orderCommissionInsuredInfoDTO1.setProductId(0);
        orderCommissionInsuredInfoDTO1.setCommissionId(0);
        final List<OrderCommissionInsuredInfoDTO> insuredInfoDTOList1 = Arrays.asList(orderCommissionInsuredInfoDTO1);
        final RenewalTermDTO renewalTermDTO1 = new RenewalTermDTO();
        renewalTermDTO1.setOrderId("orderId");
        renewalTermDTO1.setPolicyNo("policyNo");
        renewalTermDTO1.setOrderAmount(new BigDecimal("0.00"));
        renewalTermDTO1.setTermNum(0);
        renewalTermDTO1.setChannel("channel");
        final List<RenewalTermDTO> renewalTermDTOList1 = Arrays.asList(renewalTermDTO1);
        mockCommissionExternalService.getRenewalCommissionCalcOrderParam(processDTO1, insuredInfoDTOList1,
                renewalTermDTOList1);

        // Confirm SmOrderCommissionItemMapper.batchInsertCommissionItem(...).
        final SmCommissionDetailItemPO smCommissionDetailItemPO1 = new SmCommissionDetailItemPO();
        smCommissionDetailItemPO1.setOrderId("orderId");
        smCommissionDetailItemPO1.setPolicyNo("policyNo");
        smCommissionDetailItemPO1.setInsuredIdNumber("insuredIdNumber");
        smCommissionDetailItemPO1.setPolicyStatus("policyStatus");
        smCommissionDetailItemPO1.setRiskStatus("riskStatus");
        smCommissionDetailItemPO1.setPlanId(0);
        smCommissionDetailItemPO1.setRiskId(0);
        smCommissionDetailItemPO1.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smCommissionDetailItemPO1.setPlanAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO1.setAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO1.setOriginalAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO1.setTermNum(0);
        smCommissionDetailItemPO1.setCommissionType(0);
        smCommissionDetailItemPO1.setCommissionRate(new BigDecimal("0.00"));
        smCommissionDetailItemPO1.setCommissionAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO1.setCommissionUserId("commissionUserId");
        smCommissionDetailItemPO1.setBusinessType("businessType");
        smCommissionDetailItemPO1.setBusinessId("businessId");
        smCommissionDetailItemPO1.setImportFlag(0);
        final List<SmCommissionDetailItemPO> commissionConfigItemList = Arrays.asList(smCommissionDetailItemPO1);
        mockSmOrderCommissionItemMapper.batchInsertCommissionItem(commissionConfigItemList);

        // Confirm SmOrderCommissionItemMapper.batchInsertCommissionItemExceptImport(...).
        final SmCommissionDetailItemPO smCommissionDetailItemPO2 = new SmCommissionDetailItemPO();
        smCommissionDetailItemPO2.setOrderId("orderId");
        smCommissionDetailItemPO2.setPolicyNo("policyNo");
        smCommissionDetailItemPO2.setInsuredIdNumber("insuredIdNumber");
        smCommissionDetailItemPO2.setPolicyStatus("policyStatus");
        smCommissionDetailItemPO2.setRiskStatus("riskStatus");
        smCommissionDetailItemPO2.setPlanId(0);
        smCommissionDetailItemPO2.setRiskId(0);
        smCommissionDetailItemPO2.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smCommissionDetailItemPO2.setPlanAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO2.setAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO2.setOriginalAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO2.setTermNum(0);
        smCommissionDetailItemPO2.setCommissionType(0);
        smCommissionDetailItemPO2.setCommissionRate(new BigDecimal("0.00"));
        smCommissionDetailItemPO2.setCommissionAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO2.setCommissionUserId("commissionUserId");
        smCommissionDetailItemPO2.setBusinessType("businessType");
        smCommissionDetailItemPO2.setBusinessId("businessId");
        smCommissionDetailItemPO2.setImportFlag(0);
        final List<SmCommissionDetailItemPO> commissionConfigItemList1 = Arrays.asList(smCommissionDetailItemPO2);
        mockSmOrderCommissionItemMapper.batchInsertCommissionItemExceptImport(commissionConfigItemList1);

        // Confirm SmOrderCommissionDetailMapper.batchReCalcCommissionDetail(...).
        final SmCommissionDetailPO smCommissionDetailPO = new SmCommissionDetailPO();
        smCommissionDetailPO.setOrderId("orderId");
        smCommissionDetailPO.setPolicyNo("policyNo");
        smCommissionDetailPO.setInsuredIdNumber("insuredIdNumber");
        smCommissionDetailPO.setPolicyStatus("policyStatus");
        smCommissionDetailPO.setPlanId(0);
        smCommissionDetailPO.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smCommissionDetailPO.setAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setOriginalAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setPaymentRate(new BigDecimal("0.00"));
        smCommissionDetailPO.setPaymentAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setSettlementRate(new BigDecimal("0.00"));
        smCommissionDetailPO.setSettlementAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setConversionRate(new BigDecimal("0.00"));
        smCommissionDetailPO.setConversionAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setTermNum(0);
        smCommissionDetailPO.setPaymentRiskJson("paymentRiskJson");
        smCommissionDetailPO.setSettlementRiskJson("settlementRiskJson");
        smCommissionDetailPO.setConversionRiskJson("conversionRiskJson");
        smCommissionDetailPO.setPAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setSAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setCAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setCommissionUserId("commissionUserId");
        smCommissionDetailPO.setBusinessType("businessType");
        smCommissionDetailPO.setBusinessId("businessId");
        smCommissionDetailPO.setImportFlag(0);
        final List<SmCommissionDetailPO> commissionConfigDetailList = Arrays.asList(smCommissionDetailPO);
        mockSmOrderCommissionDetailMapper.batchReCalcCommissionDetail(commissionConfigDetailList);

        // Confirm SmOrderCommissionDetailMapper.batchInsertCommissionDetail(...).
        final SmCommissionDetailPO smCommissionDetailPO1 = new SmCommissionDetailPO();
        smCommissionDetailPO1.setOrderId("orderId");
        smCommissionDetailPO1.setPolicyNo("policyNo");
        smCommissionDetailPO1.setInsuredIdNumber("insuredIdNumber");
        smCommissionDetailPO1.setPolicyStatus("policyStatus");
        smCommissionDetailPO1.setPlanId(0);
        smCommissionDetailPO1.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smCommissionDetailPO1.setAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setOriginalAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setPaymentRate(new BigDecimal("0.00"));
        smCommissionDetailPO1.setPaymentAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setSettlementRate(new BigDecimal("0.00"));
        smCommissionDetailPO1.setSettlementAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setConversionRate(new BigDecimal("0.00"));
        smCommissionDetailPO1.setConversionAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setTermNum(0);
        smCommissionDetailPO1.setPaymentRiskJson("paymentRiskJson");
        smCommissionDetailPO1.setSettlementRiskJson("settlementRiskJson");
        smCommissionDetailPO1.setConversionRiskJson("conversionRiskJson");
        smCommissionDetailPO1.setPAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setSAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setCAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setCommissionUserId("commissionUserId");
        smCommissionDetailPO1.setBusinessType("businessType");
        smCommissionDetailPO1.setBusinessId("businessId");
        smCommissionDetailPO1.setImportFlag(0);
        final List<SmCommissionDetailPO> commissionConfigDetailList1 = Arrays.asList(smCommissionDetailPO1);
        mockSmOrderCommissionDetailMapper.batchInsertCommissionDetail(commissionConfigDetailList1);
    }

    @Test
    public void testCalcRenewalCommission_CommissionConfigQueryServiceListCalcCommissionDetailByConfigIdReturnsNoItems() throws Exception {
        // Setup
        final CommissionCalcProcessDTO processDTO = new CommissionCalcProcessDTO();
        processDTO.setOrderId("orderId");
        processDTO.setProductId(0);
        processDTO.setProductType("productType");
        processDTO.setMatchingTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        processDTO.setTermNum(0);
        processDTO.setCommissionConfigInfo(new HashMap<>());
        final CommissionRateDTO commissionRateDTO = new CommissionRateDTO();
        processDTO.setOldCommissionConfigInfo(Arrays.asList(commissionRateDTO));
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO.setCommissionId(0);
        commissionCalcOrderInfoDTO.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO.setAppStatus("appStatus");
        processDTO.setOrderInfo(Arrays.asList(commissionCalcOrderInfoDTO));
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO1 = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO1.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO1.setCommissionId(0);
        commissionCalcOrderInfoDTO1.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO1.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO1.setAppStatus("appStatus");
        processDTO.setRefundOrderInfo(Arrays.asList(commissionCalcOrderInfoDTO1));
        final OrderRiskCorrectDTO orderRiskCorrectDTO = new OrderRiskCorrectDTO();
        processDTO.setRiskCorrectDTOS(Arrays.asList(orderRiskCorrectDTO));
        processDTO.setItemMap(new HashMap<>());
        final CalcCommissionItemResultDTO calcCommissionItemResultDTO = new CalcCommissionItemResultDTO();
        processDTO.setItemResults(Arrays.asList(calcCommissionItemResultDTO));
        processDTO.setCommissionUserId("commissionUserId");
        processDTO.setBusinessType("businessType");

        final OrderCommissionInsuredInfoDTO orderCommissionInsuredInfoDTO = new OrderCommissionInsuredInfoDTO();
        orderCommissionInsuredInfoDTO.setFhOrderId("fhOrderId");
        orderCommissionInsuredInfoDTO.setInsuredId(0);
        orderCommissionInsuredInfoDTO.setCompanyId(0);
        orderCommissionInsuredInfoDTO.setProductId(0);
        orderCommissionInsuredInfoDTO.setCommissionId(0);
        final List<OrderCommissionInsuredInfoDTO> insuredInfoDTOList = Arrays.asList(orderCommissionInsuredInfoDTO);
        final RenewalTermDTO renewalTermDTO = new RenewalTermDTO();
        renewalTermDTO.setOrderId("orderId");
        renewalTermDTO.setPolicyNo("policyNo");
        renewalTermDTO.setOrderAmount(new BigDecimal("0.00"));
        renewalTermDTO.setTermNum(0);
        renewalTermDTO.setChannel("channel");
        final List<RenewalTermDTO> renewalTermDTOList = Arrays.asList(renewalTermDTO);

        // Configure CommissionConfigQueryService.listConfigByProductId(...).
        final SystemCommissionConfig systemCommissionConfig = new SystemCommissionConfig();
        systemCommissionConfig.setType(0);
        systemCommissionConfig.setCompanyId(0);
        systemCommissionConfig.setProductId(0);
        systemCommissionConfig.setStartTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        systemCommissionConfig.setEndTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SystemCommissionConfig> systemCommissionConfigs = Arrays.asList(systemCommissionConfig);
        mockCommissionConfigQueryService.listConfigByProductId(0);

        mockCommissionConfigQueryService.listCalcCommissionDetailByConfigId(0);

        // Configure CommissionCalcService.calc(...).
        final CalcCommissionItemResultDTO calcCommissionItemResultDTO1 = new CalcCommissionItemResultDTO();
        calcCommissionItemResultDTO1.setOrderId("orderId");
        calcCommissionItemResultDTO1.setPolicyNo("policyNo");
        calcCommissionItemResultDTO1.setInsuredIdNumber("insuredIdNumber");
        calcCommissionItemResultDTO1.setPolicyStatus("policyStatus");
        calcCommissionItemResultDTO1.setRiskStatus("riskStatus");
        final List<CalcCommissionItemResultDTO> calcCommissionItemResultDTOS = Arrays.asList(
                calcCommissionItemResultDTO1);
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO2 = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO2.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO2.setCommissionId(0);
        commissionCalcOrderInfoDTO2.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO2.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO2.setAppStatus("appStatus");
        final List<CommissionCalcOrderInfoDTO> orderInfoDTOList = Arrays.asList(commissionCalcOrderInfoDTO2);
        final CommissionCalcConfigInfoDTO configInfoDTO = new CommissionCalcConfigInfoDTO();
        configInfoDTO.setId(0);
        configInfoDTO.setType(0);
        configInfoDTO.setCompanyId(0);
        final CommissionCalcConfigDetailDTO commissionCalcConfigDetailDTO = new CommissionCalcConfigDetailDTO();
        commissionCalcConfigDetailDTO.setPeriodNum(0);
        configInfoDTO.setConfigDetailDTOList(Arrays.asList(commissionCalcConfigDetailDTO));
        mockCommissionCalcService.calc(0, 0, orderInfoDTOList, configInfoDTO, new CalcErrorDTO("errorMsg","errorMsgNoAlarm"),
                "commissionUserId", "businessType");

        // Configure CommissionCalcService.calcCorrectCommission(...).
        final CalcCommissionItemResultDTO calcCommissionItemResultDTO2 = new CalcCommissionItemResultDTO();
        calcCommissionItemResultDTO2.setOrderId("orderId");
        calcCommissionItemResultDTO2.setPolicyNo("policyNo");
        calcCommissionItemResultDTO2.setInsuredIdNumber("insuredIdNumber");
        calcCommissionItemResultDTO2.setPolicyStatus("policyStatus");
        calcCommissionItemResultDTO2.setRiskStatus("riskStatus");
        final List<CalcCommissionItemResultDTO> calcCommissionItemResultDTOS1 = Arrays.asList(
                calcCommissionItemResultDTO2);
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO3 = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO3.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO3.setCommissionId(0);
        commissionCalcOrderInfoDTO3.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO3.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO3.setAppStatus("appStatus");
        final List<CommissionCalcOrderInfoDTO> orderInfoDTOList1 = Arrays.asList(commissionCalcOrderInfoDTO3);
        final OrderRiskCorrectDTO orderRiskCorrectDTO1 = new OrderRiskCorrectDTO();
        orderRiskCorrectDTO1.setOrderId("orderId");
        orderRiskCorrectDTO1.setPolicyNo("policyNo");
        orderRiskCorrectDTO1.setCorrectId("correctId");
        orderRiskCorrectDTO1.setCorrectType("correctType");
        orderRiskCorrectDTO1.setRiskId(0);
        final List<OrderRiskCorrectDTO> correctDTOList = Arrays.asList(orderRiskCorrectDTO1);
        final SmCommissionDetailItemPO smCommissionDetailItemPO = new SmCommissionDetailItemPO();
        smCommissionDetailItemPO.setOrderId("orderId");
        smCommissionDetailItemPO.setPolicyNo("policyNo");
        smCommissionDetailItemPO.setInsuredIdNumber("insuredIdNumber");
        smCommissionDetailItemPO.setPolicyStatus("policyStatus");
        smCommissionDetailItemPO.setRiskStatus("riskStatus");
        smCommissionDetailItemPO.setPlanId(0);
        smCommissionDetailItemPO.setRiskId(0);
        smCommissionDetailItemPO.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smCommissionDetailItemPO.setPlanAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setOriginalAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setTermNum(0);
        smCommissionDetailItemPO.setCommissionType(0);
        smCommissionDetailItemPO.setCommissionRate(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setCommissionAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setCommissionUserId("commissionUserId");
        smCommissionDetailItemPO.setBusinessType("businessType");
        smCommissionDetailItemPO.setBusinessId("businessId");
        smCommissionDetailItemPO.setImportFlag(0);
        final List<SmCommissionDetailItemPO> itemPOs = Arrays.asList(smCommissionDetailItemPO);
        mockCommissionCalcService.calcCorrectCommission(0, orderInfoDTOList1, correctDTOList, itemPOs,
                new CalcErrorDTO("errorMsg","errorMsgNoAlarm"));

        // Configure CommissionCalcService.calcNewRefundDetailItem(...).
        final CalcCommissionItemResultDTO calcCommissionItemResultDTO3 = new CalcCommissionItemResultDTO();
        calcCommissionItemResultDTO3.setOrderId("orderId");
        calcCommissionItemResultDTO3.setPolicyNo("policyNo");
        calcCommissionItemResultDTO3.setInsuredIdNumber("insuredIdNumber");
        calcCommissionItemResultDTO3.setPolicyStatus("policyStatus");
        calcCommissionItemResultDTO3.setRiskStatus("riskStatus");
        final List<CalcCommissionItemResultDTO> calcCommissionItemResultDTOS2 = Arrays.asList(
                calcCommissionItemResultDTO3);
        final CommissionCalcOrderInfoDTO orderInfo = new CommissionCalcOrderInfoDTO();
        orderInfo.setProductAttrCode("productAttrCode");
        orderInfo.setCommissionId(0);
        orderInfo.setOriginalFhOrderId("originalFhOrderId");
        orderInfo.setOriginalCommissionId(0);
        orderInfo.setAppStatus("appStatus");
        mockCommissionCalcService.calcNewRefundDetailItem(orderInfo, new HashMap<>());

        // Run the test
        commissionCalcProcessServiceUnderTest.calcRenewalCommission(processDTO, insuredInfoDTOList, renewalTermDTOList);

        // Verify the results
        // Confirm CommissionExternalService.getRenewalCommissionCalcOrderParam(...).
        final CommissionCalcProcessDTO processDTO1 = new CommissionCalcProcessDTO();
        processDTO1.setOrderId("orderId");
        processDTO1.setProductId(0);
        processDTO1.setProductType("productType");
        processDTO1.setMatchingTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        processDTO1.setTermNum(0);
        processDTO1.setCommissionConfigInfo(new HashMap<>());
        final CommissionRateDTO commissionRateDTO1 = new CommissionRateDTO();
        processDTO1.setOldCommissionConfigInfo(Arrays.asList(commissionRateDTO1));
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO4 = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO4.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO4.setCommissionId(0);
        commissionCalcOrderInfoDTO4.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO4.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO4.setAppStatus("appStatus");
        processDTO1.setOrderInfo(Arrays.asList(commissionCalcOrderInfoDTO4));
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO5 = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO5.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO5.setCommissionId(0);
        commissionCalcOrderInfoDTO5.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO5.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO5.setAppStatus("appStatus");
        processDTO1.setRefundOrderInfo(Arrays.asList(commissionCalcOrderInfoDTO5));
        final OrderRiskCorrectDTO orderRiskCorrectDTO2 = new OrderRiskCorrectDTO();
        processDTO1.setRiskCorrectDTOS(Arrays.asList(orderRiskCorrectDTO2));
        processDTO1.setItemMap(new HashMap<>());
        final CalcCommissionItemResultDTO calcCommissionItemResultDTO4 = new CalcCommissionItemResultDTO();
        processDTO1.setItemResults(Arrays.asList(calcCommissionItemResultDTO4));
        processDTO1.setCommissionUserId("commissionUserId");
        processDTO1.setBusinessType("businessType");
        final OrderCommissionInsuredInfoDTO orderCommissionInsuredInfoDTO1 = new OrderCommissionInsuredInfoDTO();
        orderCommissionInsuredInfoDTO1.setFhOrderId("fhOrderId");
        orderCommissionInsuredInfoDTO1.setInsuredId(0);
        orderCommissionInsuredInfoDTO1.setCompanyId(0);
        orderCommissionInsuredInfoDTO1.setProductId(0);
        orderCommissionInsuredInfoDTO1.setCommissionId(0);
        final List<OrderCommissionInsuredInfoDTO> insuredInfoDTOList1 = Arrays.asList(orderCommissionInsuredInfoDTO1);
        final RenewalTermDTO renewalTermDTO1 = new RenewalTermDTO();
        renewalTermDTO1.setOrderId("orderId");
        renewalTermDTO1.setPolicyNo("policyNo");
        renewalTermDTO1.setOrderAmount(new BigDecimal("0.00"));
        renewalTermDTO1.setTermNum(0);
        renewalTermDTO1.setChannel("channel");
        final List<RenewalTermDTO> renewalTermDTOList1 = Arrays.asList(renewalTermDTO1);
        mockCommissionExternalService.getRenewalCommissionCalcOrderParam(processDTO1, insuredInfoDTOList1,
                renewalTermDTOList1);

        // Confirm SmOrderCommissionItemMapper.batchInsertCommissionItem(...).
        final SmCommissionDetailItemPO smCommissionDetailItemPO1 = new SmCommissionDetailItemPO();
        smCommissionDetailItemPO1.setOrderId("orderId");
        smCommissionDetailItemPO1.setPolicyNo("policyNo");
        smCommissionDetailItemPO1.setInsuredIdNumber("insuredIdNumber");
        smCommissionDetailItemPO1.setPolicyStatus("policyStatus");
        smCommissionDetailItemPO1.setRiskStatus("riskStatus");
        smCommissionDetailItemPO1.setPlanId(0);
        smCommissionDetailItemPO1.setRiskId(0);
        smCommissionDetailItemPO1.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smCommissionDetailItemPO1.setPlanAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO1.setAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO1.setOriginalAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO1.setTermNum(0);
        smCommissionDetailItemPO1.setCommissionType(0);
        smCommissionDetailItemPO1.setCommissionRate(new BigDecimal("0.00"));
        smCommissionDetailItemPO1.setCommissionAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO1.setCommissionUserId("commissionUserId");
        smCommissionDetailItemPO1.setBusinessType("businessType");
        smCommissionDetailItemPO1.setBusinessId("businessId");
        smCommissionDetailItemPO1.setImportFlag(0);
        final List<SmCommissionDetailItemPO> commissionConfigItemList = Arrays.asList(smCommissionDetailItemPO1);
        mockSmOrderCommissionItemMapper.batchInsertCommissionItem(commissionConfigItemList);

        // Confirm SmOrderCommissionItemMapper.batchInsertCommissionItemExceptImport(...).
        final SmCommissionDetailItemPO smCommissionDetailItemPO2 = new SmCommissionDetailItemPO();
        smCommissionDetailItemPO2.setOrderId("orderId");
        smCommissionDetailItemPO2.setPolicyNo("policyNo");
        smCommissionDetailItemPO2.setInsuredIdNumber("insuredIdNumber");
        smCommissionDetailItemPO2.setPolicyStatus("policyStatus");
        smCommissionDetailItemPO2.setRiskStatus("riskStatus");
        smCommissionDetailItemPO2.setPlanId(0);
        smCommissionDetailItemPO2.setRiskId(0);
        smCommissionDetailItemPO2.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smCommissionDetailItemPO2.setPlanAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO2.setAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO2.setOriginalAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO2.setTermNum(0);
        smCommissionDetailItemPO2.setCommissionType(0);
        smCommissionDetailItemPO2.setCommissionRate(new BigDecimal("0.00"));
        smCommissionDetailItemPO2.setCommissionAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO2.setCommissionUserId("commissionUserId");
        smCommissionDetailItemPO2.setBusinessType("businessType");
        smCommissionDetailItemPO2.setBusinessId("businessId");
        smCommissionDetailItemPO2.setImportFlag(0);
        final List<SmCommissionDetailItemPO> commissionConfigItemList1 = Arrays.asList(smCommissionDetailItemPO2);
        mockSmOrderCommissionItemMapper.batchInsertCommissionItemExceptImport(commissionConfigItemList1);

        // Confirm SmOrderCommissionDetailMapper.batchReCalcCommissionDetail(...).
        final SmCommissionDetailPO smCommissionDetailPO = new SmCommissionDetailPO();
        smCommissionDetailPO.setOrderId("orderId");
        smCommissionDetailPO.setPolicyNo("policyNo");
        smCommissionDetailPO.setInsuredIdNumber("insuredIdNumber");
        smCommissionDetailPO.setPolicyStatus("policyStatus");
        smCommissionDetailPO.setPlanId(0);
        smCommissionDetailPO.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smCommissionDetailPO.setAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setOriginalAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setPaymentRate(new BigDecimal("0.00"));
        smCommissionDetailPO.setPaymentAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setSettlementRate(new BigDecimal("0.00"));
        smCommissionDetailPO.setSettlementAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setConversionRate(new BigDecimal("0.00"));
        smCommissionDetailPO.setConversionAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setTermNum(0);
        smCommissionDetailPO.setPaymentRiskJson("paymentRiskJson");
        smCommissionDetailPO.setSettlementRiskJson("settlementRiskJson");
        smCommissionDetailPO.setConversionRiskJson("conversionRiskJson");
        smCommissionDetailPO.setPAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setSAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setCAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setCommissionUserId("commissionUserId");
        smCommissionDetailPO.setBusinessType("businessType");
        smCommissionDetailPO.setBusinessId("businessId");
        smCommissionDetailPO.setImportFlag(0);
        final List<SmCommissionDetailPO> commissionConfigDetailList = Arrays.asList(smCommissionDetailPO);
        mockSmOrderCommissionDetailMapper.batchReCalcCommissionDetail(commissionConfigDetailList);

        // Confirm SmOrderCommissionDetailMapper.batchInsertCommissionDetail(...).
        final SmCommissionDetailPO smCommissionDetailPO1 = new SmCommissionDetailPO();
        smCommissionDetailPO1.setOrderId("orderId");
        smCommissionDetailPO1.setPolicyNo("policyNo");
        smCommissionDetailPO1.setInsuredIdNumber("insuredIdNumber");
        smCommissionDetailPO1.setPolicyStatus("policyStatus");
        smCommissionDetailPO1.setPlanId(0);
        smCommissionDetailPO1.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smCommissionDetailPO1.setAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setOriginalAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setPaymentRate(new BigDecimal("0.00"));
        smCommissionDetailPO1.setPaymentAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setSettlementRate(new BigDecimal("0.00"));
        smCommissionDetailPO1.setSettlementAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setConversionRate(new BigDecimal("0.00"));
        smCommissionDetailPO1.setConversionAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setTermNum(0);
        smCommissionDetailPO1.setPaymentRiskJson("paymentRiskJson");
        smCommissionDetailPO1.setSettlementRiskJson("settlementRiskJson");
        smCommissionDetailPO1.setConversionRiskJson("conversionRiskJson");
        smCommissionDetailPO1.setPAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setSAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setCAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setCommissionUserId("commissionUserId");
        smCommissionDetailPO1.setBusinessType("businessType");
        smCommissionDetailPO1.setBusinessId("businessId");
        smCommissionDetailPO1.setImportFlag(0);
        final List<SmCommissionDetailPO> commissionConfigDetailList1 = Arrays.asList(smCommissionDetailPO1);
        mockSmOrderCommissionDetailMapper.batchInsertCommissionDetail(commissionConfigDetailList1);
    }

    @Test
    public void testCalcRenewalCommission_CommissionCalcServiceCalcReturnsNoItems() throws Exception {
        // Setup
        final CommissionCalcProcessDTO processDTO = new CommissionCalcProcessDTO();
        processDTO.setOrderId("orderId");
        processDTO.setProductId(0);
        processDTO.setProductType("productType");
        processDTO.setMatchingTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        processDTO.setTermNum(0);
        processDTO.setCommissionConfigInfo(new HashMap<>());
        final CommissionRateDTO commissionRateDTO = new CommissionRateDTO();
        processDTO.setOldCommissionConfigInfo(Arrays.asList(commissionRateDTO));
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO.setCommissionId(0);
        commissionCalcOrderInfoDTO.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO.setAppStatus("appStatus");
        processDTO.setOrderInfo(Arrays.asList(commissionCalcOrderInfoDTO));
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO1 = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO1.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO1.setCommissionId(0);
        commissionCalcOrderInfoDTO1.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO1.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO1.setAppStatus("appStatus");
        processDTO.setRefundOrderInfo(Arrays.asList(commissionCalcOrderInfoDTO1));
        final OrderRiskCorrectDTO orderRiskCorrectDTO = new OrderRiskCorrectDTO();
        processDTO.setRiskCorrectDTOS(Arrays.asList(orderRiskCorrectDTO));
        processDTO.setItemMap(new HashMap<>());
        final CalcCommissionItemResultDTO calcCommissionItemResultDTO = new CalcCommissionItemResultDTO();
        processDTO.setItemResults(Arrays.asList(calcCommissionItemResultDTO));
        processDTO.setCommissionUserId("commissionUserId");
        processDTO.setBusinessType("businessType");

        final OrderCommissionInsuredInfoDTO orderCommissionInsuredInfoDTO = new OrderCommissionInsuredInfoDTO();
        orderCommissionInsuredInfoDTO.setFhOrderId("fhOrderId");
        orderCommissionInsuredInfoDTO.setInsuredId(0);
        orderCommissionInsuredInfoDTO.setCompanyId(0);
        orderCommissionInsuredInfoDTO.setProductId(0);
        orderCommissionInsuredInfoDTO.setCommissionId(0);
        final List<OrderCommissionInsuredInfoDTO> insuredInfoDTOList = Arrays.asList(orderCommissionInsuredInfoDTO);
        final RenewalTermDTO renewalTermDTO = new RenewalTermDTO();
        renewalTermDTO.setOrderId("orderId");
        renewalTermDTO.setPolicyNo("policyNo");
        renewalTermDTO.setOrderAmount(new BigDecimal("0.00"));
        renewalTermDTO.setTermNum(0);
        renewalTermDTO.setChannel("channel");
        final List<RenewalTermDTO> renewalTermDTOList = Arrays.asList(renewalTermDTO);

        // Configure CommissionConfigQueryService.listConfigByProductId(...).
        final SystemCommissionConfig systemCommissionConfig = new SystemCommissionConfig();
        systemCommissionConfig.setType(0);
        systemCommissionConfig.setCompanyId(0);
        systemCommissionConfig.setProductId(0);
        systemCommissionConfig.setStartTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        systemCommissionConfig.setEndTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SystemCommissionConfig> systemCommissionConfigs = Arrays.asList(systemCommissionConfig);
        mockCommissionConfigQueryService.listConfigByProductId(0);

        // Configure CommissionConfigQueryService.listCalcCommissionDetailByConfigId(...).
        final CommissionCalcConfigDetailDTO commissionCalcConfigDetailDTO = new CommissionCalcConfigDetailDTO();
        commissionCalcConfigDetailDTO.setId(0);
        commissionCalcConfigDetailDTO.setConfigId(0);
        commissionCalcConfigDetailDTO.setPeriodNum(0);
        commissionCalcConfigDetailDTO.setPlanId(0);
        commissionCalcConfigDetailDTO.setRiskId(0);
        final List<CommissionCalcConfigDetailDTO> commissionCalcConfigDetailDTOS = Arrays.asList(
                commissionCalcConfigDetailDTO);
        mockCommissionConfigQueryService.listCalcCommissionDetailByConfigId(0);

        // Configure CommissionCalcService.calc(...).
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO2 = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO2.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO2.setCommissionId(0);
        commissionCalcOrderInfoDTO2.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO2.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO2.setAppStatus("appStatus");
        final List<CommissionCalcOrderInfoDTO> orderInfoDTOList = Arrays.asList(commissionCalcOrderInfoDTO2);
        final CommissionCalcConfigInfoDTO configInfoDTO = new CommissionCalcConfigInfoDTO();
        configInfoDTO.setId(0);
        configInfoDTO.setType(0);
        configInfoDTO.setCompanyId(0);
        final CommissionCalcConfigDetailDTO commissionCalcConfigDetailDTO1 = new CommissionCalcConfigDetailDTO();
        commissionCalcConfigDetailDTO1.setPeriodNum(0);
        configInfoDTO.setConfigDetailDTOList(Arrays.asList(commissionCalcConfigDetailDTO1));
        mockCommissionCalcService.calc(0, 0, orderInfoDTOList, configInfoDTO, new CalcErrorDTO("errorMsg","errorMsgNoAlarm"),
                "commissionUserId", "businessType");

        // Configure CommissionCalcService.calcCorrectCommission(...).
        final CalcCommissionItemResultDTO calcCommissionItemResultDTO1 = new CalcCommissionItemResultDTO();
        calcCommissionItemResultDTO1.setOrderId("orderId");
        calcCommissionItemResultDTO1.setPolicyNo("policyNo");
        calcCommissionItemResultDTO1.setInsuredIdNumber("insuredIdNumber");
        calcCommissionItemResultDTO1.setPolicyStatus("policyStatus");
        calcCommissionItemResultDTO1.setRiskStatus("riskStatus");
        final List<CalcCommissionItemResultDTO> calcCommissionItemResultDTOS = Arrays.asList(
                calcCommissionItemResultDTO1);
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO3 = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO3.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO3.setCommissionId(0);
        commissionCalcOrderInfoDTO3.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO3.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO3.setAppStatus("appStatus");
        final List<CommissionCalcOrderInfoDTO> orderInfoDTOList1 = Arrays.asList(commissionCalcOrderInfoDTO3);
        final OrderRiskCorrectDTO orderRiskCorrectDTO1 = new OrderRiskCorrectDTO();
        orderRiskCorrectDTO1.setOrderId("orderId");
        orderRiskCorrectDTO1.setPolicyNo("policyNo");
        orderRiskCorrectDTO1.setCorrectId("correctId");
        orderRiskCorrectDTO1.setCorrectType("correctType");
        orderRiskCorrectDTO1.setRiskId(0);
        final List<OrderRiskCorrectDTO> correctDTOList = Arrays.asList(orderRiskCorrectDTO1);
        final SmCommissionDetailItemPO smCommissionDetailItemPO = new SmCommissionDetailItemPO();
        smCommissionDetailItemPO.setOrderId("orderId");
        smCommissionDetailItemPO.setPolicyNo("policyNo");
        smCommissionDetailItemPO.setInsuredIdNumber("insuredIdNumber");
        smCommissionDetailItemPO.setPolicyStatus("policyStatus");
        smCommissionDetailItemPO.setRiskStatus("riskStatus");
        smCommissionDetailItemPO.setPlanId(0);
        smCommissionDetailItemPO.setRiskId(0);
        smCommissionDetailItemPO.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smCommissionDetailItemPO.setPlanAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setOriginalAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setTermNum(0);
        smCommissionDetailItemPO.setCommissionType(0);
        smCommissionDetailItemPO.setCommissionRate(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setCommissionAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setCommissionUserId("commissionUserId");
        smCommissionDetailItemPO.setBusinessType("businessType");
        smCommissionDetailItemPO.setBusinessId("businessId");
        smCommissionDetailItemPO.setImportFlag(0);
        final List<SmCommissionDetailItemPO> itemPOs = Arrays.asList(smCommissionDetailItemPO);
        mockCommissionCalcService.calcCorrectCommission(0, orderInfoDTOList1, correctDTOList, itemPOs,
                new CalcErrorDTO("errorMsg","errorMsgNoAlarm"));

        // Configure CommissionCalcService.calcNewRefundDetailItem(...).
        final CalcCommissionItemResultDTO calcCommissionItemResultDTO2 = new CalcCommissionItemResultDTO();
        calcCommissionItemResultDTO2.setOrderId("orderId");
        calcCommissionItemResultDTO2.setPolicyNo("policyNo");
        calcCommissionItemResultDTO2.setInsuredIdNumber("insuredIdNumber");
        calcCommissionItemResultDTO2.setPolicyStatus("policyStatus");
        calcCommissionItemResultDTO2.setRiskStatus("riskStatus");
        final List<CalcCommissionItemResultDTO> calcCommissionItemResultDTOS1 = Arrays.asList(
                calcCommissionItemResultDTO2);
        final CommissionCalcOrderInfoDTO orderInfo = new CommissionCalcOrderInfoDTO();
        orderInfo.setProductAttrCode("productAttrCode");
        orderInfo.setCommissionId(0);
        orderInfo.setOriginalFhOrderId("originalFhOrderId");
        orderInfo.setOriginalCommissionId(0);
        orderInfo.setAppStatus("appStatus");
        mockCommissionCalcService.calcNewRefundDetailItem(orderInfo, new HashMap<>());

        // Run the test
        commissionCalcProcessServiceUnderTest.calcRenewalCommission(processDTO, insuredInfoDTOList, renewalTermDTOList);

        // Verify the results
        // Confirm CommissionExternalService.getRenewalCommissionCalcOrderParam(...).
        final CommissionCalcProcessDTO processDTO1 = new CommissionCalcProcessDTO();
        processDTO1.setOrderId("orderId");
        processDTO1.setProductId(0);
        processDTO1.setProductType("productType");
        processDTO1.setMatchingTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        processDTO1.setTermNum(0);
        processDTO1.setCommissionConfigInfo(new HashMap<>());
        final CommissionRateDTO commissionRateDTO1 = new CommissionRateDTO();
        processDTO1.setOldCommissionConfigInfo(Arrays.asList(commissionRateDTO1));
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO4 = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO4.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO4.setCommissionId(0);
        commissionCalcOrderInfoDTO4.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO4.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO4.setAppStatus("appStatus");
        processDTO1.setOrderInfo(Arrays.asList(commissionCalcOrderInfoDTO4));
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO5 = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO5.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO5.setCommissionId(0);
        commissionCalcOrderInfoDTO5.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO5.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO5.setAppStatus("appStatus");
        processDTO1.setRefundOrderInfo(Arrays.asList(commissionCalcOrderInfoDTO5));
        final OrderRiskCorrectDTO orderRiskCorrectDTO2 = new OrderRiskCorrectDTO();
        processDTO1.setRiskCorrectDTOS(Arrays.asList(orderRiskCorrectDTO2));
        processDTO1.setItemMap(new HashMap<>());
        final CalcCommissionItemResultDTO calcCommissionItemResultDTO3 = new CalcCommissionItemResultDTO();
        processDTO1.setItemResults(Arrays.asList(calcCommissionItemResultDTO3));
        processDTO1.setCommissionUserId("commissionUserId");
        processDTO1.setBusinessType("businessType");
        final OrderCommissionInsuredInfoDTO orderCommissionInsuredInfoDTO1 = new OrderCommissionInsuredInfoDTO();
        orderCommissionInsuredInfoDTO1.setFhOrderId("fhOrderId");
        orderCommissionInsuredInfoDTO1.setInsuredId(0);
        orderCommissionInsuredInfoDTO1.setCompanyId(0);
        orderCommissionInsuredInfoDTO1.setProductId(0);
        orderCommissionInsuredInfoDTO1.setCommissionId(0);
        final List<OrderCommissionInsuredInfoDTO> insuredInfoDTOList1 = Arrays.asList(orderCommissionInsuredInfoDTO1);
        final RenewalTermDTO renewalTermDTO1 = new RenewalTermDTO();
        renewalTermDTO1.setOrderId("orderId");
        renewalTermDTO1.setPolicyNo("policyNo");
        renewalTermDTO1.setOrderAmount(new BigDecimal("0.00"));
        renewalTermDTO1.setTermNum(0);
        renewalTermDTO1.setChannel("channel");
        final List<RenewalTermDTO> renewalTermDTOList1 = Arrays.asList(renewalTermDTO1);
        mockCommissionExternalService.getRenewalCommissionCalcOrderParam(processDTO1, insuredInfoDTOList1,
                renewalTermDTOList1);

        // Confirm SmOrderCommissionItemMapper.batchInsertCommissionItem(...).
        final SmCommissionDetailItemPO smCommissionDetailItemPO1 = new SmCommissionDetailItemPO();
        smCommissionDetailItemPO1.setOrderId("orderId");
        smCommissionDetailItemPO1.setPolicyNo("policyNo");
        smCommissionDetailItemPO1.setInsuredIdNumber("insuredIdNumber");
        smCommissionDetailItemPO1.setPolicyStatus("policyStatus");
        smCommissionDetailItemPO1.setRiskStatus("riskStatus");
        smCommissionDetailItemPO1.setPlanId(0);
        smCommissionDetailItemPO1.setRiskId(0);
        smCommissionDetailItemPO1.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smCommissionDetailItemPO1.setPlanAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO1.setAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO1.setOriginalAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO1.setTermNum(0);
        smCommissionDetailItemPO1.setCommissionType(0);
        smCommissionDetailItemPO1.setCommissionRate(new BigDecimal("0.00"));
        smCommissionDetailItemPO1.setCommissionAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO1.setCommissionUserId("commissionUserId");
        smCommissionDetailItemPO1.setBusinessType("businessType");
        smCommissionDetailItemPO1.setBusinessId("businessId");
        smCommissionDetailItemPO1.setImportFlag(0);
        final List<SmCommissionDetailItemPO> commissionConfigItemList = Arrays.asList(smCommissionDetailItemPO1);
        mockSmOrderCommissionItemMapper.batchInsertCommissionItem(commissionConfigItemList);

        // Confirm SmOrderCommissionItemMapper.batchInsertCommissionItemExceptImport(...).
        final SmCommissionDetailItemPO smCommissionDetailItemPO2 = new SmCommissionDetailItemPO();
        smCommissionDetailItemPO2.setOrderId("orderId");
        smCommissionDetailItemPO2.setPolicyNo("policyNo");
        smCommissionDetailItemPO2.setInsuredIdNumber("insuredIdNumber");
        smCommissionDetailItemPO2.setPolicyStatus("policyStatus");
        smCommissionDetailItemPO2.setRiskStatus("riskStatus");
        smCommissionDetailItemPO2.setPlanId(0);
        smCommissionDetailItemPO2.setRiskId(0);
        smCommissionDetailItemPO2.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smCommissionDetailItemPO2.setPlanAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO2.setAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO2.setOriginalAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO2.setTermNum(0);
        smCommissionDetailItemPO2.setCommissionType(0);
        smCommissionDetailItemPO2.setCommissionRate(new BigDecimal("0.00"));
        smCommissionDetailItemPO2.setCommissionAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO2.setCommissionUserId("commissionUserId");
        smCommissionDetailItemPO2.setBusinessType("businessType");
        smCommissionDetailItemPO2.setBusinessId("businessId");
        smCommissionDetailItemPO2.setImportFlag(0);
        final List<SmCommissionDetailItemPO> commissionConfigItemList1 = Arrays.asList(smCommissionDetailItemPO2);
        mockSmOrderCommissionItemMapper.batchInsertCommissionItemExceptImport(commissionConfigItemList1);

        // Confirm SmOrderCommissionDetailMapper.batchReCalcCommissionDetail(...).
        final SmCommissionDetailPO smCommissionDetailPO = new SmCommissionDetailPO();
        smCommissionDetailPO.setOrderId("orderId");
        smCommissionDetailPO.setPolicyNo("policyNo");
        smCommissionDetailPO.setInsuredIdNumber("insuredIdNumber");
        smCommissionDetailPO.setPolicyStatus("policyStatus");
        smCommissionDetailPO.setPlanId(0);
        smCommissionDetailPO.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smCommissionDetailPO.setAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setOriginalAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setPaymentRate(new BigDecimal("0.00"));
        smCommissionDetailPO.setPaymentAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setSettlementRate(new BigDecimal("0.00"));
        smCommissionDetailPO.setSettlementAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setConversionRate(new BigDecimal("0.00"));
        smCommissionDetailPO.setConversionAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setTermNum(0);
        smCommissionDetailPO.setPaymentRiskJson("paymentRiskJson");
        smCommissionDetailPO.setSettlementRiskJson("settlementRiskJson");
        smCommissionDetailPO.setConversionRiskJson("conversionRiskJson");
        smCommissionDetailPO.setPAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setSAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setCAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setCommissionUserId("commissionUserId");
        smCommissionDetailPO.setBusinessType("businessType");
        smCommissionDetailPO.setBusinessId("businessId");
        smCommissionDetailPO.setImportFlag(0);
        final List<SmCommissionDetailPO> commissionConfigDetailList = Arrays.asList(smCommissionDetailPO);
        mockSmOrderCommissionDetailMapper.batchReCalcCommissionDetail(commissionConfigDetailList);

        // Confirm SmOrderCommissionDetailMapper.batchInsertCommissionDetail(...).
        final SmCommissionDetailPO smCommissionDetailPO1 = new SmCommissionDetailPO();
        smCommissionDetailPO1.setOrderId("orderId");
        smCommissionDetailPO1.setPolicyNo("policyNo");
        smCommissionDetailPO1.setInsuredIdNumber("insuredIdNumber");
        smCommissionDetailPO1.setPolicyStatus("policyStatus");
        smCommissionDetailPO1.setPlanId(0);
        smCommissionDetailPO1.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smCommissionDetailPO1.setAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setOriginalAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setPaymentRate(new BigDecimal("0.00"));
        smCommissionDetailPO1.setPaymentAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setSettlementRate(new BigDecimal("0.00"));
        smCommissionDetailPO1.setSettlementAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setConversionRate(new BigDecimal("0.00"));
        smCommissionDetailPO1.setConversionAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setTermNum(0);
        smCommissionDetailPO1.setPaymentRiskJson("paymentRiskJson");
        smCommissionDetailPO1.setSettlementRiskJson("settlementRiskJson");
        smCommissionDetailPO1.setConversionRiskJson("conversionRiskJson");
        smCommissionDetailPO1.setPAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setSAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setCAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setCommissionUserId("commissionUserId");
        smCommissionDetailPO1.setBusinessType("businessType");
        smCommissionDetailPO1.setBusinessId("businessId");
        smCommissionDetailPO1.setImportFlag(0);
        final List<SmCommissionDetailPO> commissionConfigDetailList1 = Arrays.asList(smCommissionDetailPO1);
        mockSmOrderCommissionDetailMapper.batchInsertCommissionDetail(commissionConfigDetailList1);
    }

    @Test
    public void testCalcRenewalCommission_CommissionCalcServiceCalcCorrectCommissionReturnsNoItems() throws Exception {
        // Setup
        final CommissionCalcProcessDTO processDTO = new CommissionCalcProcessDTO();
        processDTO.setOrderId("orderId");
        processDTO.setProductId(0);
        processDTO.setProductType("productType");
        processDTO.setMatchingTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        processDTO.setTermNum(0);
        processDTO.setCommissionConfigInfo(new HashMap<>());
        final CommissionRateDTO commissionRateDTO = new CommissionRateDTO();
        processDTO.setOldCommissionConfigInfo(Arrays.asList(commissionRateDTO));
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO.setCommissionId(0);
        commissionCalcOrderInfoDTO.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO.setAppStatus("appStatus");
        processDTO.setOrderInfo(Arrays.asList(commissionCalcOrderInfoDTO));
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO1 = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO1.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO1.setCommissionId(0);
        commissionCalcOrderInfoDTO1.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO1.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO1.setAppStatus("appStatus");
        processDTO.setRefundOrderInfo(Arrays.asList(commissionCalcOrderInfoDTO1));
        final OrderRiskCorrectDTO orderRiskCorrectDTO = new OrderRiskCorrectDTO();
        processDTO.setRiskCorrectDTOS(Arrays.asList(orderRiskCorrectDTO));
        processDTO.setItemMap(new HashMap<>());
        final CalcCommissionItemResultDTO calcCommissionItemResultDTO = new CalcCommissionItemResultDTO();
        processDTO.setItemResults(Arrays.asList(calcCommissionItemResultDTO));
        processDTO.setCommissionUserId("commissionUserId");
        processDTO.setBusinessType("businessType");

        final OrderCommissionInsuredInfoDTO orderCommissionInsuredInfoDTO = new OrderCommissionInsuredInfoDTO();
        orderCommissionInsuredInfoDTO.setFhOrderId("fhOrderId");
        orderCommissionInsuredInfoDTO.setInsuredId(0);
        orderCommissionInsuredInfoDTO.setCompanyId(0);
        orderCommissionInsuredInfoDTO.setProductId(0);
        orderCommissionInsuredInfoDTO.setCommissionId(0);
        final List<OrderCommissionInsuredInfoDTO> insuredInfoDTOList = Arrays.asList(orderCommissionInsuredInfoDTO);
        final RenewalTermDTO renewalTermDTO = new RenewalTermDTO();
        renewalTermDTO.setOrderId("orderId");
        renewalTermDTO.setPolicyNo("policyNo");
        renewalTermDTO.setOrderAmount(new BigDecimal("0.00"));
        renewalTermDTO.setTermNum(0);
        renewalTermDTO.setChannel("channel");
        final List<RenewalTermDTO> renewalTermDTOList = Arrays.asList(renewalTermDTO);

        // Configure CommissionConfigQueryService.listConfigByProductId(...).
        final SystemCommissionConfig systemCommissionConfig = new SystemCommissionConfig();
        systemCommissionConfig.setType(0);
        systemCommissionConfig.setCompanyId(0);
        systemCommissionConfig.setProductId(0);
        systemCommissionConfig.setStartTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        systemCommissionConfig.setEndTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SystemCommissionConfig> systemCommissionConfigs = Arrays.asList(systemCommissionConfig);
        mockCommissionConfigQueryService.listConfigByProductId(0);

        // Configure CommissionConfigQueryService.listCalcCommissionDetailByConfigId(...).
        final CommissionCalcConfigDetailDTO commissionCalcConfigDetailDTO = new CommissionCalcConfigDetailDTO();
        commissionCalcConfigDetailDTO.setId(0);
        commissionCalcConfigDetailDTO.setConfigId(0);
        commissionCalcConfigDetailDTO.setPeriodNum(0);
        commissionCalcConfigDetailDTO.setPlanId(0);
        commissionCalcConfigDetailDTO.setRiskId(0);
        final List<CommissionCalcConfigDetailDTO> commissionCalcConfigDetailDTOS = Arrays.asList(
                commissionCalcConfigDetailDTO);
        mockCommissionConfigQueryService.listCalcCommissionDetailByConfigId(0);

        // Configure CommissionCalcService.calc(...).
        final CalcCommissionItemResultDTO calcCommissionItemResultDTO1 = new CalcCommissionItemResultDTO();
        calcCommissionItemResultDTO1.setOrderId("orderId");
        calcCommissionItemResultDTO1.setPolicyNo("policyNo");
        calcCommissionItemResultDTO1.setInsuredIdNumber("insuredIdNumber");
        calcCommissionItemResultDTO1.setPolicyStatus("policyStatus");
        calcCommissionItemResultDTO1.setRiskStatus("riskStatus");
        final List<CalcCommissionItemResultDTO> calcCommissionItemResultDTOS = Arrays.asList(
                calcCommissionItemResultDTO1);
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO2 = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO2.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO2.setCommissionId(0);
        commissionCalcOrderInfoDTO2.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO2.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO2.setAppStatus("appStatus");
        final List<CommissionCalcOrderInfoDTO> orderInfoDTOList = Arrays.asList(commissionCalcOrderInfoDTO2);
        final CommissionCalcConfigInfoDTO configInfoDTO = new CommissionCalcConfigInfoDTO();
        configInfoDTO.setId(0);
        configInfoDTO.setType(0);
        configInfoDTO.setCompanyId(0);
        final CommissionCalcConfigDetailDTO commissionCalcConfigDetailDTO1 = new CommissionCalcConfigDetailDTO();
        commissionCalcConfigDetailDTO1.setPeriodNum(0);
        configInfoDTO.setConfigDetailDTOList(Arrays.asList(commissionCalcConfigDetailDTO1));
        mockCommissionCalcService.calc(0, 0, orderInfoDTOList, configInfoDTO, new CalcErrorDTO("errorMsg","errorMsgNoAlarm"),
                "commissionUserId", "businessType");

        // Configure CommissionCalcService.calcCorrectCommission(...).
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO3 = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO3.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO3.setCommissionId(0);
        commissionCalcOrderInfoDTO3.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO3.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO3.setAppStatus("appStatus");
        final List<CommissionCalcOrderInfoDTO> orderInfoDTOList1 = Arrays.asList(commissionCalcOrderInfoDTO3);
        final OrderRiskCorrectDTO orderRiskCorrectDTO1 = new OrderRiskCorrectDTO();
        orderRiskCorrectDTO1.setOrderId("orderId");
        orderRiskCorrectDTO1.setPolicyNo("policyNo");
        orderRiskCorrectDTO1.setCorrectId("correctId");
        orderRiskCorrectDTO1.setCorrectType("correctType");
        orderRiskCorrectDTO1.setRiskId(0);
        final List<OrderRiskCorrectDTO> correctDTOList = Arrays.asList(orderRiskCorrectDTO1);
        final SmCommissionDetailItemPO smCommissionDetailItemPO = new SmCommissionDetailItemPO();
        smCommissionDetailItemPO.setOrderId("orderId");
        smCommissionDetailItemPO.setPolicyNo("policyNo");
        smCommissionDetailItemPO.setInsuredIdNumber("insuredIdNumber");
        smCommissionDetailItemPO.setPolicyStatus("policyStatus");
        smCommissionDetailItemPO.setRiskStatus("riskStatus");
        smCommissionDetailItemPO.setPlanId(0);
        smCommissionDetailItemPO.setRiskId(0);
        smCommissionDetailItemPO.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smCommissionDetailItemPO.setPlanAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setOriginalAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setTermNum(0);
        smCommissionDetailItemPO.setCommissionType(0);
        smCommissionDetailItemPO.setCommissionRate(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setCommissionAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setCommissionUserId("commissionUserId");
        smCommissionDetailItemPO.setBusinessType("businessType");
        smCommissionDetailItemPO.setBusinessId("businessId");
        smCommissionDetailItemPO.setImportFlag(0);
        final List<SmCommissionDetailItemPO> itemPOs = Arrays.asList(smCommissionDetailItemPO);
        mockCommissionCalcService.calcCorrectCommission(0, orderInfoDTOList1, correctDTOList, itemPOs,
                new CalcErrorDTO("errorMsg","errorMsgNoAlarm"));

        // Configure CommissionCalcService.calcNewRefundDetailItem(...).
        final CalcCommissionItemResultDTO calcCommissionItemResultDTO2 = new CalcCommissionItemResultDTO();
        calcCommissionItemResultDTO2.setOrderId("orderId");
        calcCommissionItemResultDTO2.setPolicyNo("policyNo");
        calcCommissionItemResultDTO2.setInsuredIdNumber("insuredIdNumber");
        calcCommissionItemResultDTO2.setPolicyStatus("policyStatus");
        calcCommissionItemResultDTO2.setRiskStatus("riskStatus");
        final List<CalcCommissionItemResultDTO> calcCommissionItemResultDTOS1 = Arrays.asList(
                calcCommissionItemResultDTO2);
        final CommissionCalcOrderInfoDTO orderInfo = new CommissionCalcOrderInfoDTO();
        orderInfo.setProductAttrCode("productAttrCode");
        orderInfo.setCommissionId(0);
        orderInfo.setOriginalFhOrderId("originalFhOrderId");
        orderInfo.setOriginalCommissionId(0);
        orderInfo.setAppStatus("appStatus");
        mockCommissionCalcService.calcNewRefundDetailItem(orderInfo, new HashMap<>());

        // Run the test
        commissionCalcProcessServiceUnderTest.calcRenewalCommission(processDTO, insuredInfoDTOList, renewalTermDTOList);

        // Verify the results
        // Confirm CommissionExternalService.getRenewalCommissionCalcOrderParam(...).
        final CommissionCalcProcessDTO processDTO1 = new CommissionCalcProcessDTO();
        processDTO1.setOrderId("orderId");
        processDTO1.setProductId(0);
        processDTO1.setProductType("productType");
        processDTO1.setMatchingTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        processDTO1.setTermNum(0);
        processDTO1.setCommissionConfigInfo(new HashMap<>());
        final CommissionRateDTO commissionRateDTO1 = new CommissionRateDTO();
        processDTO1.setOldCommissionConfigInfo(Arrays.asList(commissionRateDTO1));
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO4 = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO4.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO4.setCommissionId(0);
        commissionCalcOrderInfoDTO4.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO4.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO4.setAppStatus("appStatus");
        processDTO1.setOrderInfo(Arrays.asList(commissionCalcOrderInfoDTO4));
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO5 = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO5.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO5.setCommissionId(0);
        commissionCalcOrderInfoDTO5.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO5.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO5.setAppStatus("appStatus");
        processDTO1.setRefundOrderInfo(Arrays.asList(commissionCalcOrderInfoDTO5));
        final OrderRiskCorrectDTO orderRiskCorrectDTO2 = new OrderRiskCorrectDTO();
        processDTO1.setRiskCorrectDTOS(Arrays.asList(orderRiskCorrectDTO2));
        processDTO1.setItemMap(new HashMap<>());
        final CalcCommissionItemResultDTO calcCommissionItemResultDTO3 = new CalcCommissionItemResultDTO();
        processDTO1.setItemResults(Arrays.asList(calcCommissionItemResultDTO3));
        processDTO1.setCommissionUserId("commissionUserId");
        processDTO1.setBusinessType("businessType");
        final OrderCommissionInsuredInfoDTO orderCommissionInsuredInfoDTO1 = new OrderCommissionInsuredInfoDTO();
        orderCommissionInsuredInfoDTO1.setFhOrderId("fhOrderId");
        orderCommissionInsuredInfoDTO1.setInsuredId(0);
        orderCommissionInsuredInfoDTO1.setCompanyId(0);
        orderCommissionInsuredInfoDTO1.setProductId(0);
        orderCommissionInsuredInfoDTO1.setCommissionId(0);
        final List<OrderCommissionInsuredInfoDTO> insuredInfoDTOList1 = Arrays.asList(orderCommissionInsuredInfoDTO1);
        final RenewalTermDTO renewalTermDTO1 = new RenewalTermDTO();
        renewalTermDTO1.setOrderId("orderId");
        renewalTermDTO1.setPolicyNo("policyNo");
        renewalTermDTO1.setOrderAmount(new BigDecimal("0.00"));
        renewalTermDTO1.setTermNum(0);
        renewalTermDTO1.setChannel("channel");
        final List<RenewalTermDTO> renewalTermDTOList1 = Arrays.asList(renewalTermDTO1);
        mockCommissionExternalService.getRenewalCommissionCalcOrderParam(processDTO1, insuredInfoDTOList1,
                renewalTermDTOList1);

        // Confirm SmOrderCommissionItemMapper.batchInsertCommissionItem(...).
        final SmCommissionDetailItemPO smCommissionDetailItemPO1 = new SmCommissionDetailItemPO();
        smCommissionDetailItemPO1.setOrderId("orderId");
        smCommissionDetailItemPO1.setPolicyNo("policyNo");
        smCommissionDetailItemPO1.setInsuredIdNumber("insuredIdNumber");
        smCommissionDetailItemPO1.setPolicyStatus("policyStatus");
        smCommissionDetailItemPO1.setRiskStatus("riskStatus");
        smCommissionDetailItemPO1.setPlanId(0);
        smCommissionDetailItemPO1.setRiskId(0);
        smCommissionDetailItemPO1.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smCommissionDetailItemPO1.setPlanAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO1.setAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO1.setOriginalAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO1.setTermNum(0);
        smCommissionDetailItemPO1.setCommissionType(0);
        smCommissionDetailItemPO1.setCommissionRate(new BigDecimal("0.00"));
        smCommissionDetailItemPO1.setCommissionAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO1.setCommissionUserId("commissionUserId");
        smCommissionDetailItemPO1.setBusinessType("businessType");
        smCommissionDetailItemPO1.setBusinessId("businessId");
        smCommissionDetailItemPO1.setImportFlag(0);
        final List<SmCommissionDetailItemPO> commissionConfigItemList = Arrays.asList(smCommissionDetailItemPO1);
        mockSmOrderCommissionItemMapper.batchInsertCommissionItem(commissionConfigItemList);

        // Confirm SmOrderCommissionItemMapper.batchInsertCommissionItemExceptImport(...).
        final SmCommissionDetailItemPO smCommissionDetailItemPO2 = new SmCommissionDetailItemPO();
        smCommissionDetailItemPO2.setOrderId("orderId");
        smCommissionDetailItemPO2.setPolicyNo("policyNo");
        smCommissionDetailItemPO2.setInsuredIdNumber("insuredIdNumber");
        smCommissionDetailItemPO2.setPolicyStatus("policyStatus");
        smCommissionDetailItemPO2.setRiskStatus("riskStatus");
        smCommissionDetailItemPO2.setPlanId(0);
        smCommissionDetailItemPO2.setRiskId(0);
        smCommissionDetailItemPO2.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smCommissionDetailItemPO2.setPlanAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO2.setAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO2.setOriginalAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO2.setTermNum(0);
        smCommissionDetailItemPO2.setCommissionType(0);
        smCommissionDetailItemPO2.setCommissionRate(new BigDecimal("0.00"));
        smCommissionDetailItemPO2.setCommissionAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO2.setCommissionUserId("commissionUserId");
        smCommissionDetailItemPO2.setBusinessType("businessType");
        smCommissionDetailItemPO2.setBusinessId("businessId");
        smCommissionDetailItemPO2.setImportFlag(0);
        final List<SmCommissionDetailItemPO> commissionConfigItemList1 = Arrays.asList(smCommissionDetailItemPO2);
        mockSmOrderCommissionItemMapper.batchInsertCommissionItemExceptImport(commissionConfigItemList1);

        // Confirm SmOrderCommissionDetailMapper.batchReCalcCommissionDetail(...).
        final SmCommissionDetailPO smCommissionDetailPO = new SmCommissionDetailPO();
        smCommissionDetailPO.setOrderId("orderId");
        smCommissionDetailPO.setPolicyNo("policyNo");
        smCommissionDetailPO.setInsuredIdNumber("insuredIdNumber");
        smCommissionDetailPO.setPolicyStatus("policyStatus");
        smCommissionDetailPO.setPlanId(0);
        smCommissionDetailPO.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smCommissionDetailPO.setAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setOriginalAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setPaymentRate(new BigDecimal("0.00"));
        smCommissionDetailPO.setPaymentAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setSettlementRate(new BigDecimal("0.00"));
        smCommissionDetailPO.setSettlementAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setConversionRate(new BigDecimal("0.00"));
        smCommissionDetailPO.setConversionAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setTermNum(0);
        smCommissionDetailPO.setPaymentRiskJson("paymentRiskJson");
        smCommissionDetailPO.setSettlementRiskJson("settlementRiskJson");
        smCommissionDetailPO.setConversionRiskJson("conversionRiskJson");
        smCommissionDetailPO.setPAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setSAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setCAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setCommissionUserId("commissionUserId");
        smCommissionDetailPO.setBusinessType("businessType");
        smCommissionDetailPO.setBusinessId("businessId");
        smCommissionDetailPO.setImportFlag(0);
        final List<SmCommissionDetailPO> commissionConfigDetailList = Arrays.asList(smCommissionDetailPO);
        mockSmOrderCommissionDetailMapper.batchReCalcCommissionDetail(commissionConfigDetailList);

        // Confirm SmOrderCommissionDetailMapper.batchInsertCommissionDetail(...).
        final SmCommissionDetailPO smCommissionDetailPO1 = new SmCommissionDetailPO();
        smCommissionDetailPO1.setOrderId("orderId");
        smCommissionDetailPO1.setPolicyNo("policyNo");
        smCommissionDetailPO1.setInsuredIdNumber("insuredIdNumber");
        smCommissionDetailPO1.setPolicyStatus("policyStatus");
        smCommissionDetailPO1.setPlanId(0);
        smCommissionDetailPO1.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smCommissionDetailPO1.setAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setOriginalAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setPaymentRate(new BigDecimal("0.00"));
        smCommissionDetailPO1.setPaymentAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setSettlementRate(new BigDecimal("0.00"));
        smCommissionDetailPO1.setSettlementAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setConversionRate(new BigDecimal("0.00"));
        smCommissionDetailPO1.setConversionAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setTermNum(0);
        smCommissionDetailPO1.setPaymentRiskJson("paymentRiskJson");
        smCommissionDetailPO1.setSettlementRiskJson("settlementRiskJson");
        smCommissionDetailPO1.setConversionRiskJson("conversionRiskJson");
        smCommissionDetailPO1.setPAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setSAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setCAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setCommissionUserId("commissionUserId");
        smCommissionDetailPO1.setBusinessType("businessType");
        smCommissionDetailPO1.setBusinessId("businessId");
        smCommissionDetailPO1.setImportFlag(0);
        final List<SmCommissionDetailPO> commissionConfigDetailList1 = Arrays.asList(smCommissionDetailPO1);
        mockSmOrderCommissionDetailMapper.batchInsertCommissionDetail(commissionConfigDetailList1);
    }

    @Test
    public void testCalcRenewalCommission_CommissionCalcServiceCalcNewRefundDetailItemReturnsNoItems() throws Exception {
        // Setup
        final CommissionCalcProcessDTO processDTO = new CommissionCalcProcessDTO();
        processDTO.setOrderId("orderId");
        processDTO.setProductId(0);
        processDTO.setProductType("productType");
        processDTO.setMatchingTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        processDTO.setTermNum(0);
        processDTO.setCommissionConfigInfo(new HashMap<>());
        final CommissionRateDTO commissionRateDTO = new CommissionRateDTO();
        processDTO.setOldCommissionConfigInfo(Arrays.asList(commissionRateDTO));
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO.setCommissionId(0);
        commissionCalcOrderInfoDTO.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO.setAppStatus("appStatus");
        processDTO.setOrderInfo(Arrays.asList(commissionCalcOrderInfoDTO));
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO1 = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO1.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO1.setCommissionId(0);
        commissionCalcOrderInfoDTO1.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO1.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO1.setAppStatus("appStatus");
        processDTO.setRefundOrderInfo(Arrays.asList(commissionCalcOrderInfoDTO1));
        final OrderRiskCorrectDTO orderRiskCorrectDTO = new OrderRiskCorrectDTO();
        processDTO.setRiskCorrectDTOS(Arrays.asList(orderRiskCorrectDTO));
        processDTO.setItemMap(new HashMap<>());
        final CalcCommissionItemResultDTO calcCommissionItemResultDTO = new CalcCommissionItemResultDTO();
        processDTO.setItemResults(Arrays.asList(calcCommissionItemResultDTO));
        processDTO.setCommissionUserId("commissionUserId");
        processDTO.setBusinessType("businessType");

        final OrderCommissionInsuredInfoDTO orderCommissionInsuredInfoDTO = new OrderCommissionInsuredInfoDTO();
        orderCommissionInsuredInfoDTO.setFhOrderId("fhOrderId");
        orderCommissionInsuredInfoDTO.setInsuredId(0);
        orderCommissionInsuredInfoDTO.setCompanyId(0);
        orderCommissionInsuredInfoDTO.setProductId(0);
        orderCommissionInsuredInfoDTO.setCommissionId(0);
        final List<OrderCommissionInsuredInfoDTO> insuredInfoDTOList = Arrays.asList(orderCommissionInsuredInfoDTO);
        final RenewalTermDTO renewalTermDTO = new RenewalTermDTO();
        renewalTermDTO.setOrderId("orderId");
        renewalTermDTO.setPolicyNo("policyNo");
        renewalTermDTO.setOrderAmount(new BigDecimal("0.00"));
        renewalTermDTO.setTermNum(0);
        renewalTermDTO.setChannel("channel");
        final List<RenewalTermDTO> renewalTermDTOList = Arrays.asList(renewalTermDTO);

        // Configure CommissionConfigQueryService.listConfigByProductId(...).
        final SystemCommissionConfig systemCommissionConfig = new SystemCommissionConfig();
        systemCommissionConfig.setType(0);
        systemCommissionConfig.setCompanyId(0);
        systemCommissionConfig.setProductId(0);
        systemCommissionConfig.setStartTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        systemCommissionConfig.setEndTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SystemCommissionConfig> systemCommissionConfigs = Arrays.asList(systemCommissionConfig);
        mockCommissionConfigQueryService.listConfigByProductId(0);

        // Configure CommissionConfigQueryService.listCalcCommissionDetailByConfigId(...).
        final CommissionCalcConfigDetailDTO commissionCalcConfigDetailDTO = new CommissionCalcConfigDetailDTO();
        commissionCalcConfigDetailDTO.setId(0);
        commissionCalcConfigDetailDTO.setConfigId(0);
        commissionCalcConfigDetailDTO.setPeriodNum(0);
        commissionCalcConfigDetailDTO.setPlanId(0);
        commissionCalcConfigDetailDTO.setRiskId(0);
        final List<CommissionCalcConfigDetailDTO> commissionCalcConfigDetailDTOS = Arrays.asList(
                commissionCalcConfigDetailDTO);
        mockCommissionConfigQueryService.listCalcCommissionDetailByConfigId(0);

        // Configure CommissionCalcService.calc(...).
        final CalcCommissionItemResultDTO calcCommissionItemResultDTO1 = new CalcCommissionItemResultDTO();
        calcCommissionItemResultDTO1.setOrderId("orderId");
        calcCommissionItemResultDTO1.setPolicyNo("policyNo");
        calcCommissionItemResultDTO1.setInsuredIdNumber("insuredIdNumber");
        calcCommissionItemResultDTO1.setPolicyStatus("policyStatus");
        calcCommissionItemResultDTO1.setRiskStatus("riskStatus");
        final List<CalcCommissionItemResultDTO> calcCommissionItemResultDTOS = Arrays.asList(
                calcCommissionItemResultDTO1);
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO2 = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO2.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO2.setCommissionId(0);
        commissionCalcOrderInfoDTO2.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO2.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO2.setAppStatus("appStatus");
        final List<CommissionCalcOrderInfoDTO> orderInfoDTOList = Arrays.asList(commissionCalcOrderInfoDTO2);
        final CommissionCalcConfigInfoDTO configInfoDTO = new CommissionCalcConfigInfoDTO();
        configInfoDTO.setId(0);
        configInfoDTO.setType(0);
        configInfoDTO.setCompanyId(0);
        final CommissionCalcConfigDetailDTO commissionCalcConfigDetailDTO1 = new CommissionCalcConfigDetailDTO();
        commissionCalcConfigDetailDTO1.setPeriodNum(0);
        configInfoDTO.setConfigDetailDTOList(Arrays.asList(commissionCalcConfigDetailDTO1));
        mockCommissionCalcService.calc(0, 0, orderInfoDTOList, configInfoDTO, new CalcErrorDTO("errorMsg","errorMsgNoAlarm"),
                "commissionUserId", "businessType");

        // Configure CommissionCalcService.calcCorrectCommission(...).
        final CalcCommissionItemResultDTO calcCommissionItemResultDTO2 = new CalcCommissionItemResultDTO();
        calcCommissionItemResultDTO2.setOrderId("orderId");
        calcCommissionItemResultDTO2.setPolicyNo("policyNo");
        calcCommissionItemResultDTO2.setInsuredIdNumber("insuredIdNumber");
        calcCommissionItemResultDTO2.setPolicyStatus("policyStatus");
        calcCommissionItemResultDTO2.setRiskStatus("riskStatus");
        final List<CalcCommissionItemResultDTO> calcCommissionItemResultDTOS1 = Arrays.asList(
                calcCommissionItemResultDTO2);
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO3 = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO3.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO3.setCommissionId(0);
        commissionCalcOrderInfoDTO3.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO3.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO3.setAppStatus("appStatus");
        final List<CommissionCalcOrderInfoDTO> orderInfoDTOList1 = Arrays.asList(commissionCalcOrderInfoDTO3);
        final OrderRiskCorrectDTO orderRiskCorrectDTO1 = new OrderRiskCorrectDTO();
        orderRiskCorrectDTO1.setOrderId("orderId");
        orderRiskCorrectDTO1.setPolicyNo("policyNo");
        orderRiskCorrectDTO1.setCorrectId("correctId");
        orderRiskCorrectDTO1.setCorrectType("correctType");
        orderRiskCorrectDTO1.setRiskId(0);
        final List<OrderRiskCorrectDTO> correctDTOList = Arrays.asList(orderRiskCorrectDTO1);
        final SmCommissionDetailItemPO smCommissionDetailItemPO = new SmCommissionDetailItemPO();
        smCommissionDetailItemPO.setOrderId("orderId");
        smCommissionDetailItemPO.setPolicyNo("policyNo");
        smCommissionDetailItemPO.setInsuredIdNumber("insuredIdNumber");
        smCommissionDetailItemPO.setPolicyStatus("policyStatus");
        smCommissionDetailItemPO.setRiskStatus("riskStatus");
        smCommissionDetailItemPO.setPlanId(0);
        smCommissionDetailItemPO.setRiskId(0);
        smCommissionDetailItemPO.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smCommissionDetailItemPO.setPlanAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setOriginalAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setTermNum(0);
        smCommissionDetailItemPO.setCommissionType(0);
        smCommissionDetailItemPO.setCommissionRate(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setCommissionAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setCommissionUserId("commissionUserId");
        smCommissionDetailItemPO.setBusinessType("businessType");
        smCommissionDetailItemPO.setBusinessId("businessId");
        smCommissionDetailItemPO.setImportFlag(0);
        final List<SmCommissionDetailItemPO> itemPOs = Arrays.asList(smCommissionDetailItemPO);
        mockCommissionCalcService.calcCorrectCommission(0, orderInfoDTOList1, correctDTOList, itemPOs,
                new CalcErrorDTO("errorMsg","errorMsgNoAlarm"));

        // Configure CommissionCalcService.calcNewRefundDetailItem(...).
        final CommissionCalcOrderInfoDTO orderInfo = new CommissionCalcOrderInfoDTO();
        orderInfo.setProductAttrCode("productAttrCode");
        orderInfo.setCommissionId(0);
        orderInfo.setOriginalFhOrderId("originalFhOrderId");
        orderInfo.setOriginalCommissionId(0);
        orderInfo.setAppStatus("appStatus");
        mockCommissionCalcService.calcNewRefundDetailItem(orderInfo, new HashMap<>());

        // Configure CommissionCalcService.calcOldRefundDetailItem(...).
        final CalcCommissionItemResultDTO calcCommissionItemResultDTO3 = new CalcCommissionItemResultDTO();
        calcCommissionItemResultDTO3.setOrderId("orderId");
        calcCommissionItemResultDTO3.setPolicyNo("policyNo");
        calcCommissionItemResultDTO3.setInsuredIdNumber("insuredIdNumber");
        calcCommissionItemResultDTO3.setPolicyStatus("policyStatus");
        calcCommissionItemResultDTO3.setRiskStatus("riskStatus");
        final List<CalcCommissionItemResultDTO> calcCommissionItemResultDTOS2 = Arrays.asList(
                calcCommissionItemResultDTO3);
        final CommissionCalcOrderInfoDTO orderInfo1 = new CommissionCalcOrderInfoDTO();
        orderInfo1.setProductAttrCode("productAttrCode");
        orderInfo1.setCommissionId(0);
        orderInfo1.setOriginalFhOrderId("originalFhOrderId");
        orderInfo1.setOriginalCommissionId(0);
        orderInfo1.setAppStatus("appStatus");
        final CommissionRateDTO commissionRateDTO1 = new CommissionRateDTO();
        commissionRateDTO1.setPlanId(0);
        commissionRateDTO1.setRiskId(0);
        commissionRateDTO1.setCommissionType(0);
        commissionRateDTO1.setCommissionId(0);
        commissionRateDTO1.setCommissionRate(new BigDecimal("0.00"));
        final List<CommissionRateDTO> oldCommissionRates = Arrays.asList(commissionRateDTO1);
        mockCommissionCalcService.calcOldRefundDetailItem(orderInfo1, oldCommissionRates,
                "commissionUserId");

        // Run the test
        commissionCalcProcessServiceUnderTest.calcRenewalCommission(processDTO, insuredInfoDTOList, renewalTermDTOList);

        // Verify the results
        // Confirm CommissionExternalService.getRenewalCommissionCalcOrderParam(...).
        final CommissionCalcProcessDTO processDTO1 = new CommissionCalcProcessDTO();
        processDTO1.setOrderId("orderId");
        processDTO1.setProductId(0);
        processDTO1.setProductType("productType");
        processDTO1.setMatchingTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        processDTO1.setTermNum(0);
        processDTO1.setCommissionConfigInfo(new HashMap<>());
        final CommissionRateDTO commissionRateDTO2 = new CommissionRateDTO();
        processDTO1.setOldCommissionConfigInfo(Arrays.asList(commissionRateDTO2));
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO4 = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO4.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO4.setCommissionId(0);
        commissionCalcOrderInfoDTO4.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO4.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO4.setAppStatus("appStatus");
        processDTO1.setOrderInfo(Arrays.asList(commissionCalcOrderInfoDTO4));
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO5 = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO5.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO5.setCommissionId(0);
        commissionCalcOrderInfoDTO5.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO5.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO5.setAppStatus("appStatus");
        processDTO1.setRefundOrderInfo(Arrays.asList(commissionCalcOrderInfoDTO5));
        final OrderRiskCorrectDTO orderRiskCorrectDTO2 = new OrderRiskCorrectDTO();
        processDTO1.setRiskCorrectDTOS(Arrays.asList(orderRiskCorrectDTO2));
        processDTO1.setItemMap(new HashMap<>());
        final CalcCommissionItemResultDTO calcCommissionItemResultDTO4 = new CalcCommissionItemResultDTO();
        processDTO1.setItemResults(Arrays.asList(calcCommissionItemResultDTO4));
        processDTO1.setCommissionUserId("commissionUserId");
        processDTO1.setBusinessType("businessType");
        final OrderCommissionInsuredInfoDTO orderCommissionInsuredInfoDTO1 = new OrderCommissionInsuredInfoDTO();
        orderCommissionInsuredInfoDTO1.setFhOrderId("fhOrderId");
        orderCommissionInsuredInfoDTO1.setInsuredId(0);
        orderCommissionInsuredInfoDTO1.setCompanyId(0);
        orderCommissionInsuredInfoDTO1.setProductId(0);
        orderCommissionInsuredInfoDTO1.setCommissionId(0);
        final List<OrderCommissionInsuredInfoDTO> insuredInfoDTOList1 = Arrays.asList(orderCommissionInsuredInfoDTO1);
        final RenewalTermDTO renewalTermDTO1 = new RenewalTermDTO();
        renewalTermDTO1.setOrderId("orderId");
        renewalTermDTO1.setPolicyNo("policyNo");
        renewalTermDTO1.setOrderAmount(new BigDecimal("0.00"));
        renewalTermDTO1.setTermNum(0);
        renewalTermDTO1.setChannel("channel");
        final List<RenewalTermDTO> renewalTermDTOList1 = Arrays.asList(renewalTermDTO1);
        mockCommissionExternalService.getRenewalCommissionCalcOrderParam(processDTO1, insuredInfoDTOList1,
                renewalTermDTOList1);

        // Confirm SmOrderCommissionItemMapper.batchInsertCommissionItem(...).
        final SmCommissionDetailItemPO smCommissionDetailItemPO1 = new SmCommissionDetailItemPO();
        smCommissionDetailItemPO1.setOrderId("orderId");
        smCommissionDetailItemPO1.setPolicyNo("policyNo");
        smCommissionDetailItemPO1.setInsuredIdNumber("insuredIdNumber");
        smCommissionDetailItemPO1.setPolicyStatus("policyStatus");
        smCommissionDetailItemPO1.setRiskStatus("riskStatus");
        smCommissionDetailItemPO1.setPlanId(0);
        smCommissionDetailItemPO1.setRiskId(0);
        smCommissionDetailItemPO1.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smCommissionDetailItemPO1.setPlanAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO1.setAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO1.setOriginalAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO1.setTermNum(0);
        smCommissionDetailItemPO1.setCommissionType(0);
        smCommissionDetailItemPO1.setCommissionRate(new BigDecimal("0.00"));
        smCommissionDetailItemPO1.setCommissionAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO1.setCommissionUserId("commissionUserId");
        smCommissionDetailItemPO1.setBusinessType("businessType");
        smCommissionDetailItemPO1.setBusinessId("businessId");
        smCommissionDetailItemPO1.setImportFlag(0);
        final List<SmCommissionDetailItemPO> commissionConfigItemList = Arrays.asList(smCommissionDetailItemPO1);
        mockSmOrderCommissionItemMapper.batchInsertCommissionItem(commissionConfigItemList);

        // Confirm SmOrderCommissionItemMapper.batchInsertCommissionItemExceptImport(...).
        final SmCommissionDetailItemPO smCommissionDetailItemPO2 = new SmCommissionDetailItemPO();
        smCommissionDetailItemPO2.setOrderId("orderId");
        smCommissionDetailItemPO2.setPolicyNo("policyNo");
        smCommissionDetailItemPO2.setInsuredIdNumber("insuredIdNumber");
        smCommissionDetailItemPO2.setPolicyStatus("policyStatus");
        smCommissionDetailItemPO2.setRiskStatus("riskStatus");
        smCommissionDetailItemPO2.setPlanId(0);
        smCommissionDetailItemPO2.setRiskId(0);
        smCommissionDetailItemPO2.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smCommissionDetailItemPO2.setPlanAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO2.setAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO2.setOriginalAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO2.setTermNum(0);
        smCommissionDetailItemPO2.setCommissionType(0);
        smCommissionDetailItemPO2.setCommissionRate(new BigDecimal("0.00"));
        smCommissionDetailItemPO2.setCommissionAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO2.setCommissionUserId("commissionUserId");
        smCommissionDetailItemPO2.setBusinessType("businessType");
        smCommissionDetailItemPO2.setBusinessId("businessId");
        smCommissionDetailItemPO2.setImportFlag(0);
        final List<SmCommissionDetailItemPO> commissionConfigItemList1 = Arrays.asList(smCommissionDetailItemPO2);
        mockSmOrderCommissionItemMapper.batchInsertCommissionItemExceptImport(commissionConfigItemList1);

        // Confirm SmOrderCommissionDetailMapper.batchReCalcCommissionDetail(...).
        final SmCommissionDetailPO smCommissionDetailPO = new SmCommissionDetailPO();
        smCommissionDetailPO.setOrderId("orderId");
        smCommissionDetailPO.setPolicyNo("policyNo");
        smCommissionDetailPO.setInsuredIdNumber("insuredIdNumber");
        smCommissionDetailPO.setPolicyStatus("policyStatus");
        smCommissionDetailPO.setPlanId(0);
        smCommissionDetailPO.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smCommissionDetailPO.setAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setOriginalAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setPaymentRate(new BigDecimal("0.00"));
        smCommissionDetailPO.setPaymentAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setSettlementRate(new BigDecimal("0.00"));
        smCommissionDetailPO.setSettlementAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setConversionRate(new BigDecimal("0.00"));
        smCommissionDetailPO.setConversionAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setTermNum(0);
        smCommissionDetailPO.setPaymentRiskJson("paymentRiskJson");
        smCommissionDetailPO.setSettlementRiskJson("settlementRiskJson");
        smCommissionDetailPO.setConversionRiskJson("conversionRiskJson");
        smCommissionDetailPO.setPAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setSAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setCAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setCommissionUserId("commissionUserId");
        smCommissionDetailPO.setBusinessType("businessType");
        smCommissionDetailPO.setBusinessId("businessId");
        smCommissionDetailPO.setImportFlag(0);
        final List<SmCommissionDetailPO> commissionConfigDetailList = Arrays.asList(smCommissionDetailPO);
        mockSmOrderCommissionDetailMapper.batchReCalcCommissionDetail(commissionConfigDetailList);

        // Confirm SmOrderCommissionDetailMapper.batchInsertCommissionDetail(...).
        final SmCommissionDetailPO smCommissionDetailPO1 = new SmCommissionDetailPO();
        smCommissionDetailPO1.setOrderId("orderId");
        smCommissionDetailPO1.setPolicyNo("policyNo");
        smCommissionDetailPO1.setInsuredIdNumber("insuredIdNumber");
        smCommissionDetailPO1.setPolicyStatus("policyStatus");
        smCommissionDetailPO1.setPlanId(0);
        smCommissionDetailPO1.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smCommissionDetailPO1.setAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setOriginalAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setPaymentRate(new BigDecimal("0.00"));
        smCommissionDetailPO1.setPaymentAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setSettlementRate(new BigDecimal("0.00"));
        smCommissionDetailPO1.setSettlementAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setConversionRate(new BigDecimal("0.00"));
        smCommissionDetailPO1.setConversionAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setTermNum(0);
        smCommissionDetailPO1.setPaymentRiskJson("paymentRiskJson");
        smCommissionDetailPO1.setSettlementRiskJson("settlementRiskJson");
        smCommissionDetailPO1.setConversionRiskJson("conversionRiskJson");
        smCommissionDetailPO1.setPAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setSAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setCAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setCommissionUserId("commissionUserId");
        smCommissionDetailPO1.setBusinessType("businessType");
        smCommissionDetailPO1.setBusinessId("businessId");
        smCommissionDetailPO1.setImportFlag(0);
        final List<SmCommissionDetailPO> commissionConfigDetailList1 = Arrays.asList(smCommissionDetailPO1);
        mockSmOrderCommissionDetailMapper.batchInsertCommissionDetail(commissionConfigDetailList1);
    }

    @Test
    public void testCalcRenewalCommission_CommissionCalcServiceCalcOldRefundDetailItemReturnsNoItems() throws Exception {
        // Setup
        final CommissionCalcProcessDTO processDTO = new CommissionCalcProcessDTO();
        processDTO.setOrderId("orderId");
        processDTO.setProductId(0);
        processDTO.setProductType("productType");
        processDTO.setMatchingTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        processDTO.setTermNum(0);
        processDTO.setCommissionConfigInfo(new HashMap<>());
        final CommissionRateDTO commissionRateDTO = new CommissionRateDTO();
        processDTO.setOldCommissionConfigInfo(Arrays.asList(commissionRateDTO));
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO.setCommissionId(0);
        commissionCalcOrderInfoDTO.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO.setAppStatus("appStatus");
        processDTO.setOrderInfo(Arrays.asList(commissionCalcOrderInfoDTO));
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO1 = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO1.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO1.setCommissionId(0);
        commissionCalcOrderInfoDTO1.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO1.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO1.setAppStatus("appStatus");
        processDTO.setRefundOrderInfo(Arrays.asList(commissionCalcOrderInfoDTO1));
        final OrderRiskCorrectDTO orderRiskCorrectDTO = new OrderRiskCorrectDTO();
        processDTO.setRiskCorrectDTOS(Arrays.asList(orderRiskCorrectDTO));
        processDTO.setItemMap(new HashMap<>());
        final CalcCommissionItemResultDTO calcCommissionItemResultDTO = new CalcCommissionItemResultDTO();
        processDTO.setItemResults(Arrays.asList(calcCommissionItemResultDTO));
        processDTO.setCommissionUserId("commissionUserId");
        processDTO.setBusinessType("businessType");

        final OrderCommissionInsuredInfoDTO orderCommissionInsuredInfoDTO = new OrderCommissionInsuredInfoDTO();
        orderCommissionInsuredInfoDTO.setFhOrderId("fhOrderId");
        orderCommissionInsuredInfoDTO.setInsuredId(0);
        orderCommissionInsuredInfoDTO.setCompanyId(0);
        orderCommissionInsuredInfoDTO.setProductId(0);
        orderCommissionInsuredInfoDTO.setCommissionId(0);
        final List<OrderCommissionInsuredInfoDTO> insuredInfoDTOList = Arrays.asList(orderCommissionInsuredInfoDTO);
        final RenewalTermDTO renewalTermDTO = new RenewalTermDTO();
        renewalTermDTO.setOrderId("orderId");
        renewalTermDTO.setPolicyNo("policyNo");
        renewalTermDTO.setOrderAmount(new BigDecimal("0.00"));
        renewalTermDTO.setTermNum(0);
        renewalTermDTO.setChannel("channel");
        final List<RenewalTermDTO> renewalTermDTOList = Arrays.asList(renewalTermDTO);

        // Configure CommissionConfigQueryService.listConfigByProductId(...).
        final SystemCommissionConfig systemCommissionConfig = new SystemCommissionConfig();
        systemCommissionConfig.setType(0);
        systemCommissionConfig.setCompanyId(0);
        systemCommissionConfig.setProductId(0);
        systemCommissionConfig.setStartTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        systemCommissionConfig.setEndTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SystemCommissionConfig> systemCommissionConfigs = Arrays.asList(systemCommissionConfig);
        mockCommissionConfigQueryService.listConfigByProductId(0);

        // Configure CommissionConfigQueryService.listCalcCommissionDetailByConfigId(...).
        final CommissionCalcConfigDetailDTO commissionCalcConfigDetailDTO = new CommissionCalcConfigDetailDTO();
        commissionCalcConfigDetailDTO.setId(0);
        commissionCalcConfigDetailDTO.setConfigId(0);
        commissionCalcConfigDetailDTO.setPeriodNum(0);
        commissionCalcConfigDetailDTO.setPlanId(0);
        commissionCalcConfigDetailDTO.setRiskId(0);
        final List<CommissionCalcConfigDetailDTO> commissionCalcConfigDetailDTOS = Arrays.asList(
                commissionCalcConfigDetailDTO);
        mockCommissionConfigQueryService.listCalcCommissionDetailByConfigId(0);

        // Configure CommissionCalcService.calc(...).
        final CalcCommissionItemResultDTO calcCommissionItemResultDTO1 = new CalcCommissionItemResultDTO();
        calcCommissionItemResultDTO1.setOrderId("orderId");
        calcCommissionItemResultDTO1.setPolicyNo("policyNo");
        calcCommissionItemResultDTO1.setInsuredIdNumber("insuredIdNumber");
        calcCommissionItemResultDTO1.setPolicyStatus("policyStatus");
        calcCommissionItemResultDTO1.setRiskStatus("riskStatus");
        final List<CalcCommissionItemResultDTO> calcCommissionItemResultDTOS = Arrays.asList(
                calcCommissionItemResultDTO1);
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO2 = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO2.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO2.setCommissionId(0);
        commissionCalcOrderInfoDTO2.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO2.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO2.setAppStatus("appStatus");
        final List<CommissionCalcOrderInfoDTO> orderInfoDTOList = Arrays.asList(commissionCalcOrderInfoDTO2);
        final CommissionCalcConfigInfoDTO configInfoDTO = new CommissionCalcConfigInfoDTO();
        configInfoDTO.setId(0);
        configInfoDTO.setType(0);
        configInfoDTO.setCompanyId(0);
        final CommissionCalcConfigDetailDTO commissionCalcConfigDetailDTO1 = new CommissionCalcConfigDetailDTO();
        commissionCalcConfigDetailDTO1.setPeriodNum(0);
        configInfoDTO.setConfigDetailDTOList(Arrays.asList(commissionCalcConfigDetailDTO1));
        mockCommissionCalcService.calc(0, 0, orderInfoDTOList, configInfoDTO, new CalcErrorDTO("errorMsg","errorMsgNoAlarm"),
                "commissionUserId", "businessType");

        // Configure CommissionCalcService.calcCorrectCommission(...).
        final CalcCommissionItemResultDTO calcCommissionItemResultDTO2 = new CalcCommissionItemResultDTO();
        calcCommissionItemResultDTO2.setOrderId("orderId");
        calcCommissionItemResultDTO2.setPolicyNo("policyNo");
        calcCommissionItemResultDTO2.setInsuredIdNumber("insuredIdNumber");
        calcCommissionItemResultDTO2.setPolicyStatus("policyStatus");
        calcCommissionItemResultDTO2.setRiskStatus("riskStatus");
        final List<CalcCommissionItemResultDTO> calcCommissionItemResultDTOS1 = Arrays.asList(
                calcCommissionItemResultDTO2);
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO3 = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO3.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO3.setCommissionId(0);
        commissionCalcOrderInfoDTO3.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO3.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO3.setAppStatus("appStatus");
        final List<CommissionCalcOrderInfoDTO> orderInfoDTOList1 = Arrays.asList(commissionCalcOrderInfoDTO3);
        final OrderRiskCorrectDTO orderRiskCorrectDTO1 = new OrderRiskCorrectDTO();
        orderRiskCorrectDTO1.setOrderId("orderId");
        orderRiskCorrectDTO1.setPolicyNo("policyNo");
        orderRiskCorrectDTO1.setCorrectId("correctId");
        orderRiskCorrectDTO1.setCorrectType("correctType");
        orderRiskCorrectDTO1.setRiskId(0);
        final List<OrderRiskCorrectDTO> correctDTOList = Arrays.asList(orderRiskCorrectDTO1);
        final SmCommissionDetailItemPO smCommissionDetailItemPO = new SmCommissionDetailItemPO();
        smCommissionDetailItemPO.setOrderId("orderId");
        smCommissionDetailItemPO.setPolicyNo("policyNo");
        smCommissionDetailItemPO.setInsuredIdNumber("insuredIdNumber");
        smCommissionDetailItemPO.setPolicyStatus("policyStatus");
        smCommissionDetailItemPO.setRiskStatus("riskStatus");
        smCommissionDetailItemPO.setPlanId(0);
        smCommissionDetailItemPO.setRiskId(0);
        smCommissionDetailItemPO.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smCommissionDetailItemPO.setPlanAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setOriginalAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setTermNum(0);
        smCommissionDetailItemPO.setCommissionType(0);
        smCommissionDetailItemPO.setCommissionRate(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setCommissionAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setCommissionUserId("commissionUserId");
        smCommissionDetailItemPO.setBusinessType("businessType");
        smCommissionDetailItemPO.setBusinessId("businessId");
        smCommissionDetailItemPO.setImportFlag(0);
        final List<SmCommissionDetailItemPO> itemPOs = Arrays.asList(smCommissionDetailItemPO);
        mockCommissionCalcService.calcCorrectCommission(0, orderInfoDTOList1, correctDTOList, itemPOs,
                new CalcErrorDTO("errorMsg","errorMsgNoAlarm"));

        // Configure CommissionCalcService.calcNewRefundDetailItem(...).
        final CommissionCalcOrderInfoDTO orderInfo = new CommissionCalcOrderInfoDTO();
        orderInfo.setProductAttrCode("productAttrCode");
        orderInfo.setCommissionId(0);
        orderInfo.setOriginalFhOrderId("originalFhOrderId");
        orderInfo.setOriginalCommissionId(0);
        orderInfo.setAppStatus("appStatus");
        mockCommissionCalcService.calcNewRefundDetailItem(orderInfo, new HashMap<>());

        // Configure CommissionCalcService.calcOldRefundDetailItem(...).
        final CommissionCalcOrderInfoDTO orderInfo1 = new CommissionCalcOrderInfoDTO();
        orderInfo1.setProductAttrCode("productAttrCode");
        orderInfo1.setCommissionId(0);
        orderInfo1.setOriginalFhOrderId("originalFhOrderId");
        orderInfo1.setOriginalCommissionId(0);
        orderInfo1.setAppStatus("appStatus");
        final CommissionRateDTO commissionRateDTO1 = new CommissionRateDTO();
        commissionRateDTO1.setPlanId(0);
        commissionRateDTO1.setRiskId(0);
        commissionRateDTO1.setCommissionType(0);
        commissionRateDTO1.setCommissionId(0);
        commissionRateDTO1.setCommissionRate(new BigDecimal("0.00"));
        final List<CommissionRateDTO> oldCommissionRates = Arrays.asList(commissionRateDTO1);
        mockCommissionCalcService.calcOldRefundDetailItem(orderInfo1, oldCommissionRates,
                "commissionUserId");

        // Run the test
        commissionCalcProcessServiceUnderTest.calcRenewalCommission(processDTO, insuredInfoDTOList, renewalTermDTOList);

        // Verify the results
        // Confirm CommissionExternalService.getRenewalCommissionCalcOrderParam(...).
        final CommissionCalcProcessDTO processDTO1 = new CommissionCalcProcessDTO();
        processDTO1.setOrderId("orderId");
        processDTO1.setProductId(0);
        processDTO1.setProductType("productType");
        processDTO1.setMatchingTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        processDTO1.setTermNum(0);
        processDTO1.setCommissionConfigInfo(new HashMap<>());
        final CommissionRateDTO commissionRateDTO2 = new CommissionRateDTO();
        processDTO1.setOldCommissionConfigInfo(Arrays.asList(commissionRateDTO2));
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO4 = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO4.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO4.setCommissionId(0);
        commissionCalcOrderInfoDTO4.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO4.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO4.setAppStatus("appStatus");
        processDTO1.setOrderInfo(Arrays.asList(commissionCalcOrderInfoDTO4));
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO5 = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO5.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO5.setCommissionId(0);
        commissionCalcOrderInfoDTO5.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO5.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO5.setAppStatus("appStatus");
        processDTO1.setRefundOrderInfo(Arrays.asList(commissionCalcOrderInfoDTO5));
        final OrderRiskCorrectDTO orderRiskCorrectDTO2 = new OrderRiskCorrectDTO();
        processDTO1.setRiskCorrectDTOS(Arrays.asList(orderRiskCorrectDTO2));
        processDTO1.setItemMap(new HashMap<>());
        final CalcCommissionItemResultDTO calcCommissionItemResultDTO3 = new CalcCommissionItemResultDTO();
        processDTO1.setItemResults(Arrays.asList(calcCommissionItemResultDTO3));
        processDTO1.setCommissionUserId("commissionUserId");
        processDTO1.setBusinessType("businessType");
        final OrderCommissionInsuredInfoDTO orderCommissionInsuredInfoDTO1 = new OrderCommissionInsuredInfoDTO();
        orderCommissionInsuredInfoDTO1.setFhOrderId("fhOrderId");
        orderCommissionInsuredInfoDTO1.setInsuredId(0);
        orderCommissionInsuredInfoDTO1.setCompanyId(0);
        orderCommissionInsuredInfoDTO1.setProductId(0);
        orderCommissionInsuredInfoDTO1.setCommissionId(0);
        final List<OrderCommissionInsuredInfoDTO> insuredInfoDTOList1 = Arrays.asList(orderCommissionInsuredInfoDTO1);
        final RenewalTermDTO renewalTermDTO1 = new RenewalTermDTO();
        renewalTermDTO1.setOrderId("orderId");
        renewalTermDTO1.setPolicyNo("policyNo");
        renewalTermDTO1.setOrderAmount(new BigDecimal("0.00"));
        renewalTermDTO1.setTermNum(0);
        renewalTermDTO1.setChannel("channel");
        final List<RenewalTermDTO> renewalTermDTOList1 = Arrays.asList(renewalTermDTO1);
        mockCommissionExternalService.getRenewalCommissionCalcOrderParam(processDTO1, insuredInfoDTOList1,
                renewalTermDTOList1);

        // Confirm SmOrderCommissionItemMapper.batchInsertCommissionItem(...).
        final SmCommissionDetailItemPO smCommissionDetailItemPO1 = new SmCommissionDetailItemPO();
        smCommissionDetailItemPO1.setOrderId("orderId");
        smCommissionDetailItemPO1.setPolicyNo("policyNo");
        smCommissionDetailItemPO1.setInsuredIdNumber("insuredIdNumber");
        smCommissionDetailItemPO1.setPolicyStatus("policyStatus");
        smCommissionDetailItemPO1.setRiskStatus("riskStatus");
        smCommissionDetailItemPO1.setPlanId(0);
        smCommissionDetailItemPO1.setRiskId(0);
        smCommissionDetailItemPO1.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smCommissionDetailItemPO1.setPlanAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO1.setAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO1.setOriginalAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO1.setTermNum(0);
        smCommissionDetailItemPO1.setCommissionType(0);
        smCommissionDetailItemPO1.setCommissionRate(new BigDecimal("0.00"));
        smCommissionDetailItemPO1.setCommissionAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO1.setCommissionUserId("commissionUserId");
        smCommissionDetailItemPO1.setBusinessType("businessType");
        smCommissionDetailItemPO1.setBusinessId("businessId");
        smCommissionDetailItemPO1.setImportFlag(0);
        final List<SmCommissionDetailItemPO> commissionConfigItemList = Arrays.asList(smCommissionDetailItemPO1);
        mockSmOrderCommissionItemMapper.batchInsertCommissionItem(commissionConfigItemList);

        // Confirm SmOrderCommissionItemMapper.batchInsertCommissionItemExceptImport(...).
        final SmCommissionDetailItemPO smCommissionDetailItemPO2 = new SmCommissionDetailItemPO();
        smCommissionDetailItemPO2.setOrderId("orderId");
        smCommissionDetailItemPO2.setPolicyNo("policyNo");
        smCommissionDetailItemPO2.setInsuredIdNumber("insuredIdNumber");
        smCommissionDetailItemPO2.setPolicyStatus("policyStatus");
        smCommissionDetailItemPO2.setRiskStatus("riskStatus");
        smCommissionDetailItemPO2.setPlanId(0);
        smCommissionDetailItemPO2.setRiskId(0);
        smCommissionDetailItemPO2.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smCommissionDetailItemPO2.setPlanAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO2.setAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO2.setOriginalAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO2.setTermNum(0);
        smCommissionDetailItemPO2.setCommissionType(0);
        smCommissionDetailItemPO2.setCommissionRate(new BigDecimal("0.00"));
        smCommissionDetailItemPO2.setCommissionAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO2.setCommissionUserId("commissionUserId");
        smCommissionDetailItemPO2.setBusinessType("businessType");
        smCommissionDetailItemPO2.setBusinessId("businessId");
        smCommissionDetailItemPO2.setImportFlag(0);
        final List<SmCommissionDetailItemPO> commissionConfigItemList1 = Arrays.asList(smCommissionDetailItemPO2);
        mockSmOrderCommissionItemMapper.batchInsertCommissionItemExceptImport(commissionConfigItemList1);

        // Confirm SmOrderCommissionDetailMapper.batchReCalcCommissionDetail(...).
        final SmCommissionDetailPO smCommissionDetailPO = new SmCommissionDetailPO();
        smCommissionDetailPO.setOrderId("orderId");
        smCommissionDetailPO.setPolicyNo("policyNo");
        smCommissionDetailPO.setInsuredIdNumber("insuredIdNumber");
        smCommissionDetailPO.setPolicyStatus("policyStatus");
        smCommissionDetailPO.setPlanId(0);
        smCommissionDetailPO.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smCommissionDetailPO.setAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setOriginalAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setPaymentRate(new BigDecimal("0.00"));
        smCommissionDetailPO.setPaymentAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setSettlementRate(new BigDecimal("0.00"));
        smCommissionDetailPO.setSettlementAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setConversionRate(new BigDecimal("0.00"));
        smCommissionDetailPO.setConversionAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setTermNum(0);
        smCommissionDetailPO.setPaymentRiskJson("paymentRiskJson");
        smCommissionDetailPO.setSettlementRiskJson("settlementRiskJson");
        smCommissionDetailPO.setConversionRiskJson("conversionRiskJson");
        smCommissionDetailPO.setPAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setSAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setCAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setCommissionUserId("commissionUserId");
        smCommissionDetailPO.setBusinessType("businessType");
        smCommissionDetailPO.setBusinessId("businessId");
        smCommissionDetailPO.setImportFlag(0);
        final List<SmCommissionDetailPO> commissionConfigDetailList = Arrays.asList(smCommissionDetailPO);
        mockSmOrderCommissionDetailMapper.batchReCalcCommissionDetail(commissionConfigDetailList);

        // Confirm SmOrderCommissionDetailMapper.batchInsertCommissionDetail(...).
        final SmCommissionDetailPO smCommissionDetailPO1 = new SmCommissionDetailPO();
        smCommissionDetailPO1.setOrderId("orderId");
        smCommissionDetailPO1.setPolicyNo("policyNo");
        smCommissionDetailPO1.setInsuredIdNumber("insuredIdNumber");
        smCommissionDetailPO1.setPolicyStatus("policyStatus");
        smCommissionDetailPO1.setPlanId(0);
        smCommissionDetailPO1.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smCommissionDetailPO1.setAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setOriginalAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setPaymentRate(new BigDecimal("0.00"));
        smCommissionDetailPO1.setPaymentAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setSettlementRate(new BigDecimal("0.00"));
        smCommissionDetailPO1.setSettlementAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setConversionRate(new BigDecimal("0.00"));
        smCommissionDetailPO1.setConversionAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setTermNum(0);
        smCommissionDetailPO1.setPaymentRiskJson("paymentRiskJson");
        smCommissionDetailPO1.setSettlementRiskJson("settlementRiskJson");
        smCommissionDetailPO1.setConversionRiskJson("conversionRiskJson");
        smCommissionDetailPO1.setPAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setSAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setCAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setCommissionUserId("commissionUserId");
        smCommissionDetailPO1.setBusinessType("businessType");
        smCommissionDetailPO1.setBusinessId("businessId");
        smCommissionDetailPO1.setImportFlag(0);
        final List<SmCommissionDetailPO> commissionConfigDetailList1 = Arrays.asList(smCommissionDetailPO1);
        mockSmOrderCommissionDetailMapper.batchInsertCommissionDetail(commissionConfigDetailList1);
    }

    @Test
    public void testCalcInsureCommission() throws Exception {
        // Setup
        final CommissionCalcProcessDTO processDTO = new CommissionCalcProcessDTO();
        processDTO.setOrderId("orderId");
        processDTO.setProductId(0);
        processDTO.setProductType("productType");
        processDTO.setMatchingTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        processDTO.setTermNum(0);
        processDTO.setCommissionConfigInfo(new HashMap<>());
        final CommissionRateDTO commissionRateDTO = new CommissionRateDTO();
        processDTO.setOldCommissionConfigInfo(Arrays.asList(commissionRateDTO));
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO.setCommissionId(0);
        commissionCalcOrderInfoDTO.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO.setAppStatus("appStatus");
        processDTO.setOrderInfo(Arrays.asList(commissionCalcOrderInfoDTO));
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO1 = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO1.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO1.setCommissionId(0);
        commissionCalcOrderInfoDTO1.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO1.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO1.setAppStatus("appStatus");
        processDTO.setRefundOrderInfo(Arrays.asList(commissionCalcOrderInfoDTO1));
        final OrderRiskCorrectDTO orderRiskCorrectDTO = new OrderRiskCorrectDTO();
        processDTO.setRiskCorrectDTOS(Arrays.asList(orderRiskCorrectDTO));
        processDTO.setItemMap(new HashMap<>());
        final CalcCommissionItemResultDTO calcCommissionItemResultDTO = new CalcCommissionItemResultDTO();
        processDTO.setItemResults(Arrays.asList(calcCommissionItemResultDTO));
        processDTO.setCommissionUserId("commissionUserId");
        processDTO.setBusinessType("businessType");

        final OrderCommissionInsuredInfoDTO orderCommissionInsuredInfoDTO = new OrderCommissionInsuredInfoDTO();
        orderCommissionInsuredInfoDTO.setFhOrderId("fhOrderId");
        orderCommissionInsuredInfoDTO.setInsuredId(0);
        orderCommissionInsuredInfoDTO.setCompanyId(0);
        orderCommissionInsuredInfoDTO.setProductId(0);
        orderCommissionInsuredInfoDTO.setCommissionId(0);
        final List<OrderCommissionInsuredInfoDTO> insuredInfoDTOList = Arrays.asList(orderCommissionInsuredInfoDTO);

        // Configure CommissionConfigQueryService.listConfigByProductId(...).
        final SystemCommissionConfig systemCommissionConfig = new SystemCommissionConfig();
        systemCommissionConfig.setType(0);
        systemCommissionConfig.setCompanyId(0);
        systemCommissionConfig.setProductId(0);
        systemCommissionConfig.setStartTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        systemCommissionConfig.setEndTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SystemCommissionConfig> systemCommissionConfigs = Arrays.asList(systemCommissionConfig);
        mockCommissionConfigQueryService.listConfigByProductId(0);

        // Configure CommissionConfigQueryService.listCalcCommissionDetailByConfigId(...).
        final CommissionCalcConfigDetailDTO commissionCalcConfigDetailDTO = new CommissionCalcConfigDetailDTO();
        commissionCalcConfigDetailDTO.setId(0);
        commissionCalcConfigDetailDTO.setConfigId(0);
        commissionCalcConfigDetailDTO.setPeriodNum(0);
        commissionCalcConfigDetailDTO.setPlanId(0);
        commissionCalcConfigDetailDTO.setRiskId(0);
        final List<CommissionCalcConfigDetailDTO> commissionCalcConfigDetailDTOS = Arrays.asList(
                commissionCalcConfigDetailDTO);
        mockCommissionConfigQueryService.listCalcCommissionDetailByConfigId(0);

        mockCommissionExternalService.isDecreaseGroupOrder("productAttrCode", "appStatus");

        // Configure SmOrderCommissionItemMapper.listCommissionItemByOrderId(...).
        final SmCommissionDetailItemPO smCommissionDetailItemPO = new SmCommissionDetailItemPO();
        smCommissionDetailItemPO.setOrderId("orderId");
        smCommissionDetailItemPO.setPolicyNo("policyNo");
        smCommissionDetailItemPO.setInsuredIdNumber("insuredIdNumber");
        smCommissionDetailItemPO.setPolicyStatus("policyStatus");
        smCommissionDetailItemPO.setRiskStatus("riskStatus");
        smCommissionDetailItemPO.setPlanId(0);
        smCommissionDetailItemPO.setRiskId(0);
        smCommissionDetailItemPO.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smCommissionDetailItemPO.setPlanAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setOriginalAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setTermNum(0);
        smCommissionDetailItemPO.setCommissionType(0);
        smCommissionDetailItemPO.setCommissionRate(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setCommissionAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setCommissionUserId("commissionUserId");
        smCommissionDetailItemPO.setBusinessType("businessType");
        smCommissionDetailItemPO.setBusinessId("businessId");
        smCommissionDetailItemPO.setImportFlag(0);
        final List<SmCommissionDetailItemPO> smCommissionDetailItemPOS = Arrays.asList(smCommissionDetailItemPO);
        mockSmOrderCommissionItemMapper.listCommissionItemByOrderId("originalFhOrderId", 0);

        // Configure CommissionExternalService.getOldConvertedConfig(...).
        final CommissionRateDTO commissionRateDTO1 = new CommissionRateDTO();
        commissionRateDTO1.setPlanId(0);
        commissionRateDTO1.setRiskId(0);
        commissionRateDTO1.setCommissionType(0);
        commissionRateDTO1.setCommissionId(0);
        commissionRateDTO1.setCommissionRate(new BigDecimal("0.00"));
        final List<CommissionRateDTO> commissionRateDTOS = Arrays.asList(commissionRateDTO1);
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO2 = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO2.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO2.setCommissionId(0);
        commissionCalcOrderInfoDTO2.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO2.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO2.setAppStatus("appStatus");
        final List<CommissionCalcOrderInfoDTO> orderInfoList = Arrays.asList(commissionCalcOrderInfoDTO2);
        mockCommissionExternalService.getOldConvertedConfig(orderInfoList);

        // Configure CommissionConfigQueryService.getOldCommissionSetting(...).
        final CommissionRateDTO commissionRateDTO2 = new CommissionRateDTO();
        commissionRateDTO2.setPlanId(0);
        commissionRateDTO2.setRiskId(0);
        commissionRateDTO2.setCommissionType(0);
        commissionRateDTO2.setCommissionId(0);
        commissionRateDTO2.setCommissionRate(new BigDecimal("0.00"));
        final List<CommissionRateDTO> commissionRateDTOS1 = Arrays.asList(commissionRateDTO2);
        mockCommissionConfigQueryService.getOldCommissionSetting(Arrays.asList(0));

        // Configure CommissionCalcService.calc(...).
        final CalcCommissionItemResultDTO calcCommissionItemResultDTO1 = new CalcCommissionItemResultDTO();
        calcCommissionItemResultDTO1.setOrderId("orderId");
        calcCommissionItemResultDTO1.setPolicyNo("policyNo");
        calcCommissionItemResultDTO1.setInsuredIdNumber("insuredIdNumber");
        calcCommissionItemResultDTO1.setPolicyStatus("policyStatus");
        calcCommissionItemResultDTO1.setRiskStatus("riskStatus");
        final List<CalcCommissionItemResultDTO> calcCommissionItemResultDTOS = Arrays.asList(
                calcCommissionItemResultDTO1);
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO3 = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO3.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO3.setCommissionId(0);
        commissionCalcOrderInfoDTO3.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO3.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO3.setAppStatus("appStatus");
        final List<CommissionCalcOrderInfoDTO> orderInfoDTOList = Arrays.asList(commissionCalcOrderInfoDTO3);
        final CommissionCalcConfigInfoDTO configInfoDTO = new CommissionCalcConfigInfoDTO();
        configInfoDTO.setId(0);
        configInfoDTO.setType(0);
        configInfoDTO.setCompanyId(0);
        final CommissionCalcConfigDetailDTO commissionCalcConfigDetailDTO1 = new CommissionCalcConfigDetailDTO();
        commissionCalcConfigDetailDTO1.setPeriodNum(0);
        configInfoDTO.setConfigDetailDTOList(Arrays.asList(commissionCalcConfigDetailDTO1));
        mockCommissionCalcService.calc(0, 0, orderInfoDTOList, configInfoDTO, new CalcErrorDTO("errorMsg","errorMsgNoAlarm"),
                "commissionUserId", "businessType");

        // Configure CommissionCalcService.calcCorrectCommission(...).
        final CalcCommissionItemResultDTO calcCommissionItemResultDTO2 = new CalcCommissionItemResultDTO();
        calcCommissionItemResultDTO2.setOrderId("orderId");
        calcCommissionItemResultDTO2.setPolicyNo("policyNo");
        calcCommissionItemResultDTO2.setInsuredIdNumber("insuredIdNumber");
        calcCommissionItemResultDTO2.setPolicyStatus("policyStatus");
        calcCommissionItemResultDTO2.setRiskStatus("riskStatus");
        final List<CalcCommissionItemResultDTO> calcCommissionItemResultDTOS1 = Arrays.asList(
                calcCommissionItemResultDTO2);
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO4 = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO4.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO4.setCommissionId(0);
        commissionCalcOrderInfoDTO4.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO4.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO4.setAppStatus("appStatus");
        final List<CommissionCalcOrderInfoDTO> orderInfoDTOList1 = Arrays.asList(commissionCalcOrderInfoDTO4);
        final OrderRiskCorrectDTO orderRiskCorrectDTO1 = new OrderRiskCorrectDTO();
        orderRiskCorrectDTO1.setOrderId("orderId");
        orderRiskCorrectDTO1.setPolicyNo("policyNo");
        orderRiskCorrectDTO1.setCorrectId("correctId");
        orderRiskCorrectDTO1.setCorrectType("correctType");
        orderRiskCorrectDTO1.setRiskId(0);
        final List<OrderRiskCorrectDTO> correctDTOList = Arrays.asList(orderRiskCorrectDTO1);
        final SmCommissionDetailItemPO smCommissionDetailItemPO1 = new SmCommissionDetailItemPO();
        smCommissionDetailItemPO1.setOrderId("orderId");
        smCommissionDetailItemPO1.setPolicyNo("policyNo");
        smCommissionDetailItemPO1.setInsuredIdNumber("insuredIdNumber");
        smCommissionDetailItemPO1.setPolicyStatus("policyStatus");
        smCommissionDetailItemPO1.setRiskStatus("riskStatus");
        smCommissionDetailItemPO1.setPlanId(0);
        smCommissionDetailItemPO1.setRiskId(0);
        smCommissionDetailItemPO1.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smCommissionDetailItemPO1.setPlanAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO1.setAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO1.setOriginalAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO1.setTermNum(0);
        smCommissionDetailItemPO1.setCommissionType(0);
        smCommissionDetailItemPO1.setCommissionRate(new BigDecimal("0.00"));
        smCommissionDetailItemPO1.setCommissionAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO1.setCommissionUserId("commissionUserId");
        smCommissionDetailItemPO1.setBusinessType("businessType");
        smCommissionDetailItemPO1.setBusinessId("businessId");
        smCommissionDetailItemPO1.setImportFlag(0);
        final List<SmCommissionDetailItemPO> itemPOs = Arrays.asList(smCommissionDetailItemPO1);
        mockCommissionCalcService.calcCorrectCommission(0, orderInfoDTOList1, correctDTOList, itemPOs,
                new CalcErrorDTO("errorMsg","errorMsgNoAlarm"));

        // Configure CommissionCalcService.calcNewRefundDetailItem(...).
        final CalcCommissionItemResultDTO calcCommissionItemResultDTO3 = new CalcCommissionItemResultDTO();
        calcCommissionItemResultDTO3.setOrderId("orderId");
        calcCommissionItemResultDTO3.setPolicyNo("policyNo");
        calcCommissionItemResultDTO3.setInsuredIdNumber("insuredIdNumber");
        calcCommissionItemResultDTO3.setPolicyStatus("policyStatus");
        calcCommissionItemResultDTO3.setRiskStatus("riskStatus");
        final List<CalcCommissionItemResultDTO> calcCommissionItemResultDTOS2 = Arrays.asList(
                calcCommissionItemResultDTO3);
        final CommissionCalcOrderInfoDTO orderInfo = new CommissionCalcOrderInfoDTO();
        orderInfo.setProductAttrCode("productAttrCode");
        orderInfo.setCommissionId(0);
        orderInfo.setOriginalFhOrderId("originalFhOrderId");
        orderInfo.setOriginalCommissionId(0);
        orderInfo.setAppStatus("appStatus");
        mockCommissionCalcService.calcNewRefundDetailItem(orderInfo, new HashMap<>());

        // Run the test
        commissionCalcProcessServiceUnderTest.calcInsureCommission(processDTO, insuredInfoDTOList, false);

        // Verify the results
        // Confirm CommissionExternalService.getCommissionCalcOrderParam(...).
        final CommissionCalcProcessDTO processDTO1 = new CommissionCalcProcessDTO();
        processDTO1.setOrderId("orderId");
        processDTO1.setProductId(0);
        processDTO1.setProductType("productType");
        processDTO1.setMatchingTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        processDTO1.setTermNum(0);
        processDTO1.setCommissionConfigInfo(new HashMap<>());
        final CommissionRateDTO commissionRateDTO3 = new CommissionRateDTO();
        processDTO1.setOldCommissionConfigInfo(Arrays.asList(commissionRateDTO3));
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO5 = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO5.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO5.setCommissionId(0);
        commissionCalcOrderInfoDTO5.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO5.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO5.setAppStatus("appStatus");
        processDTO1.setOrderInfo(Arrays.asList(commissionCalcOrderInfoDTO5));
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO6 = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO6.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO6.setCommissionId(0);
        commissionCalcOrderInfoDTO6.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO6.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO6.setAppStatus("appStatus");
        processDTO1.setRefundOrderInfo(Arrays.asList(commissionCalcOrderInfoDTO6));
        final OrderRiskCorrectDTO orderRiskCorrectDTO2 = new OrderRiskCorrectDTO();
        processDTO1.setRiskCorrectDTOS(Arrays.asList(orderRiskCorrectDTO2));
        processDTO1.setItemMap(new HashMap<>());
        final CalcCommissionItemResultDTO calcCommissionItemResultDTO4 = new CalcCommissionItemResultDTO();
        processDTO1.setItemResults(Arrays.asList(calcCommissionItemResultDTO4));
        processDTO1.setCommissionUserId("commissionUserId");
        processDTO1.setBusinessType("businessType");
        final OrderCommissionInsuredInfoDTO orderCommissionInsuredInfoDTO1 = new OrderCommissionInsuredInfoDTO();
        orderCommissionInsuredInfoDTO1.setFhOrderId("fhOrderId");
        orderCommissionInsuredInfoDTO1.setInsuredId(0);
        orderCommissionInsuredInfoDTO1.setCompanyId(0);
        orderCommissionInsuredInfoDTO1.setProductId(0);
        orderCommissionInsuredInfoDTO1.setCommissionId(0);
        final List<OrderCommissionInsuredInfoDTO> insuredInfoDTOList1 = Arrays.asList(orderCommissionInsuredInfoDTO1);
        mockCommissionExternalService.getCommissionCalcOrderParam(processDTO1, insuredInfoDTOList1);

        // Confirm SmOrderCommissionItemMapper.batchInsertCommissionItem(...).
        final SmCommissionDetailItemPO smCommissionDetailItemPO2 = new SmCommissionDetailItemPO();
        smCommissionDetailItemPO2.setOrderId("orderId");
        smCommissionDetailItemPO2.setPolicyNo("policyNo");
        smCommissionDetailItemPO2.setInsuredIdNumber("insuredIdNumber");
        smCommissionDetailItemPO2.setPolicyStatus("policyStatus");
        smCommissionDetailItemPO2.setRiskStatus("riskStatus");
        smCommissionDetailItemPO2.setPlanId(0);
        smCommissionDetailItemPO2.setRiskId(0);
        smCommissionDetailItemPO2.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smCommissionDetailItemPO2.setPlanAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO2.setAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO2.setOriginalAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO2.setTermNum(0);
        smCommissionDetailItemPO2.setCommissionType(0);
        smCommissionDetailItemPO2.setCommissionRate(new BigDecimal("0.00"));
        smCommissionDetailItemPO2.setCommissionAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO2.setCommissionUserId("commissionUserId");
        smCommissionDetailItemPO2.setBusinessType("businessType");
        smCommissionDetailItemPO2.setBusinessId("businessId");
        smCommissionDetailItemPO2.setImportFlag(0);
        final List<SmCommissionDetailItemPO> commissionConfigItemList = Arrays.asList(smCommissionDetailItemPO2);
        mockSmOrderCommissionItemMapper.batchInsertCommissionItem(commissionConfigItemList);

        // Confirm SmOrderCommissionItemMapper.batchInsertCommissionItemExceptImport(...).
        final SmCommissionDetailItemPO smCommissionDetailItemPO3 = new SmCommissionDetailItemPO();
        smCommissionDetailItemPO3.setOrderId("orderId");
        smCommissionDetailItemPO3.setPolicyNo("policyNo");
        smCommissionDetailItemPO3.setInsuredIdNumber("insuredIdNumber");
        smCommissionDetailItemPO3.setPolicyStatus("policyStatus");
        smCommissionDetailItemPO3.setRiskStatus("riskStatus");
        smCommissionDetailItemPO3.setPlanId(0);
        smCommissionDetailItemPO3.setRiskId(0);
        smCommissionDetailItemPO3.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smCommissionDetailItemPO3.setPlanAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO3.setAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO3.setOriginalAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO3.setTermNum(0);
        smCommissionDetailItemPO3.setCommissionType(0);
        smCommissionDetailItemPO3.setCommissionRate(new BigDecimal("0.00"));
        smCommissionDetailItemPO3.setCommissionAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO3.setCommissionUserId("commissionUserId");
        smCommissionDetailItemPO3.setBusinessType("businessType");
        smCommissionDetailItemPO3.setBusinessId("businessId");
        smCommissionDetailItemPO3.setImportFlag(0);
        final List<SmCommissionDetailItemPO> commissionConfigItemList1 = Arrays.asList(smCommissionDetailItemPO3);
        mockSmOrderCommissionItemMapper.batchInsertCommissionItemExceptImport(commissionConfigItemList1);

        // Confirm SmOrderCommissionDetailMapper.batchReCalcCommissionDetail(...).
        final SmCommissionDetailPO smCommissionDetailPO = new SmCommissionDetailPO();
        smCommissionDetailPO.setOrderId("orderId");
        smCommissionDetailPO.setPolicyNo("policyNo");
        smCommissionDetailPO.setInsuredIdNumber("insuredIdNumber");
        smCommissionDetailPO.setPolicyStatus("policyStatus");
        smCommissionDetailPO.setPlanId(0);
        smCommissionDetailPO.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smCommissionDetailPO.setAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setOriginalAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setPaymentRate(new BigDecimal("0.00"));
        smCommissionDetailPO.setPaymentAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setSettlementRate(new BigDecimal("0.00"));
        smCommissionDetailPO.setSettlementAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setConversionRate(new BigDecimal("0.00"));
        smCommissionDetailPO.setConversionAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setTermNum(0);
        smCommissionDetailPO.setPaymentRiskJson("paymentRiskJson");
        smCommissionDetailPO.setSettlementRiskJson("settlementRiskJson");
        smCommissionDetailPO.setConversionRiskJson("conversionRiskJson");
        smCommissionDetailPO.setPAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setSAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setCAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setCommissionUserId("commissionUserId");
        smCommissionDetailPO.setBusinessType("businessType");
        smCommissionDetailPO.setBusinessId("businessId");
        smCommissionDetailPO.setImportFlag(0);
        final List<SmCommissionDetailPO> commissionConfigDetailList = Arrays.asList(smCommissionDetailPO);
        mockSmOrderCommissionDetailMapper.batchReCalcCommissionDetail(commissionConfigDetailList);

        // Confirm SmOrderCommissionDetailMapper.batchInsertCommissionDetail(...).
        final SmCommissionDetailPO smCommissionDetailPO1 = new SmCommissionDetailPO();
        smCommissionDetailPO1.setOrderId("orderId");
        smCommissionDetailPO1.setPolicyNo("policyNo");
        smCommissionDetailPO1.setInsuredIdNumber("insuredIdNumber");
        smCommissionDetailPO1.setPolicyStatus("policyStatus");
        smCommissionDetailPO1.setPlanId(0);
        smCommissionDetailPO1.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smCommissionDetailPO1.setAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setOriginalAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setPaymentRate(new BigDecimal("0.00"));
        smCommissionDetailPO1.setPaymentAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setSettlementRate(new BigDecimal("0.00"));
        smCommissionDetailPO1.setSettlementAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setConversionRate(new BigDecimal("0.00"));
        smCommissionDetailPO1.setConversionAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setTermNum(0);
        smCommissionDetailPO1.setPaymentRiskJson("paymentRiskJson");
        smCommissionDetailPO1.setSettlementRiskJson("settlementRiskJson");
        smCommissionDetailPO1.setConversionRiskJson("conversionRiskJson");
        smCommissionDetailPO1.setPAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setSAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setCAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setCommissionUserId("commissionUserId");
        smCommissionDetailPO1.setBusinessType("businessType");
        smCommissionDetailPO1.setBusinessId("businessId");
        smCommissionDetailPO1.setImportFlag(0);
        final List<SmCommissionDetailPO> commissionConfigDetailList1 = Arrays.asList(smCommissionDetailPO1);
        mockSmOrderCommissionDetailMapper.batchInsertCommissionDetail(commissionConfigDetailList1);
    }

    @Test
    public void testCalcInsureCommission_CommissionConfigQueryServiceListConfigByProductIdReturnsNoItems() throws Exception {
        // Setup
        final CommissionCalcProcessDTO processDTO = new CommissionCalcProcessDTO();
        processDTO.setOrderId("orderId");
        processDTO.setProductId(0);
        processDTO.setProductType("productType");
        processDTO.setMatchingTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        processDTO.setTermNum(0);
        processDTO.setCommissionConfigInfo(new HashMap<>());
        final CommissionRateDTO commissionRateDTO = new CommissionRateDTO();
        processDTO.setOldCommissionConfigInfo(Arrays.asList(commissionRateDTO));
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO.setCommissionId(0);
        commissionCalcOrderInfoDTO.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO.setAppStatus("appStatus");
        processDTO.setOrderInfo(Arrays.asList(commissionCalcOrderInfoDTO));
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO1 = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO1.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO1.setCommissionId(0);
        commissionCalcOrderInfoDTO1.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO1.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO1.setAppStatus("appStatus");
        processDTO.setRefundOrderInfo(Arrays.asList(commissionCalcOrderInfoDTO1));
        final OrderRiskCorrectDTO orderRiskCorrectDTO = new OrderRiskCorrectDTO();
        processDTO.setRiskCorrectDTOS(Arrays.asList(orderRiskCorrectDTO));
        processDTO.setItemMap(new HashMap<>());
        final CalcCommissionItemResultDTO calcCommissionItemResultDTO = new CalcCommissionItemResultDTO();
        processDTO.setItemResults(Arrays.asList(calcCommissionItemResultDTO));
        processDTO.setCommissionUserId("commissionUserId");
        processDTO.setBusinessType("businessType");

        final OrderCommissionInsuredInfoDTO orderCommissionInsuredInfoDTO = new OrderCommissionInsuredInfoDTO();
        orderCommissionInsuredInfoDTO.setFhOrderId("fhOrderId");
        orderCommissionInsuredInfoDTO.setInsuredId(0);
        orderCommissionInsuredInfoDTO.setCompanyId(0);
        orderCommissionInsuredInfoDTO.setProductId(0);
        orderCommissionInsuredInfoDTO.setCommissionId(0);
        final List<OrderCommissionInsuredInfoDTO> insuredInfoDTOList = Arrays.asList(orderCommissionInsuredInfoDTO);
        mockCommissionConfigQueryService.listConfigByProductId(0);

        // Configure CommissionConfigQueryService.listCalcCommissionDetailByConfigId(...).
        final CommissionCalcConfigDetailDTO commissionCalcConfigDetailDTO = new CommissionCalcConfigDetailDTO();
        commissionCalcConfigDetailDTO.setId(0);
        commissionCalcConfigDetailDTO.setConfigId(0);
        commissionCalcConfigDetailDTO.setPeriodNum(0);
        commissionCalcConfigDetailDTO.setPlanId(0);
        commissionCalcConfigDetailDTO.setRiskId(0);
        final List<CommissionCalcConfigDetailDTO> commissionCalcConfigDetailDTOS = Arrays.asList(
                commissionCalcConfigDetailDTO);
        mockCommissionConfigQueryService.listCalcCommissionDetailByConfigId(0);

        mockCommissionExternalService.isDecreaseGroupOrder("productAttrCode", "appStatus");

        // Configure SmOrderCommissionItemMapper.listCommissionItemByOrderId(...).
        final SmCommissionDetailItemPO smCommissionDetailItemPO = new SmCommissionDetailItemPO();
        smCommissionDetailItemPO.setOrderId("orderId");
        smCommissionDetailItemPO.setPolicyNo("policyNo");
        smCommissionDetailItemPO.setInsuredIdNumber("insuredIdNumber");
        smCommissionDetailItemPO.setPolicyStatus("policyStatus");
        smCommissionDetailItemPO.setRiskStatus("riskStatus");
        smCommissionDetailItemPO.setPlanId(0);
        smCommissionDetailItemPO.setRiskId(0);
        smCommissionDetailItemPO.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smCommissionDetailItemPO.setPlanAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setOriginalAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setTermNum(0);
        smCommissionDetailItemPO.setCommissionType(0);
        smCommissionDetailItemPO.setCommissionRate(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setCommissionAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setCommissionUserId("commissionUserId");
        smCommissionDetailItemPO.setBusinessType("businessType");
        smCommissionDetailItemPO.setBusinessId("businessId");
        smCommissionDetailItemPO.setImportFlag(0);
        final List<SmCommissionDetailItemPO> smCommissionDetailItemPOS = Arrays.asList(smCommissionDetailItemPO);
        mockSmOrderCommissionItemMapper.listCommissionItemByOrderId("originalFhOrderId", 0);

        // Configure CommissionExternalService.getOldConvertedConfig(...).
        final CommissionRateDTO commissionRateDTO1 = new CommissionRateDTO();
        commissionRateDTO1.setPlanId(0);
        commissionRateDTO1.setRiskId(0);
        commissionRateDTO1.setCommissionType(0);
        commissionRateDTO1.setCommissionId(0);
        commissionRateDTO1.setCommissionRate(new BigDecimal("0.00"));
        final List<CommissionRateDTO> commissionRateDTOS = Arrays.asList(commissionRateDTO1);
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO2 = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO2.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO2.setCommissionId(0);
        commissionCalcOrderInfoDTO2.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO2.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO2.setAppStatus("appStatus");
        final List<CommissionCalcOrderInfoDTO> orderInfoList = Arrays.asList(commissionCalcOrderInfoDTO2);
        mockCommissionExternalService.getOldConvertedConfig(orderInfoList);

        // Configure CommissionConfigQueryService.getOldCommissionSetting(...).
        final CommissionRateDTO commissionRateDTO2 = new CommissionRateDTO();
        commissionRateDTO2.setPlanId(0);
        commissionRateDTO2.setRiskId(0);
        commissionRateDTO2.setCommissionType(0);
        commissionRateDTO2.setCommissionId(0);
        commissionRateDTO2.setCommissionRate(new BigDecimal("0.00"));
        final List<CommissionRateDTO> commissionRateDTOS1 = Arrays.asList(commissionRateDTO2);
        mockCommissionConfigQueryService.getOldCommissionSetting(Arrays.asList(0));

        // Configure CommissionCalcService.calc(...).
        final CalcCommissionItemResultDTO calcCommissionItemResultDTO1 = new CalcCommissionItemResultDTO();
        calcCommissionItemResultDTO1.setOrderId("orderId");
        calcCommissionItemResultDTO1.setPolicyNo("policyNo");
        calcCommissionItemResultDTO1.setInsuredIdNumber("insuredIdNumber");
        calcCommissionItemResultDTO1.setPolicyStatus("policyStatus");
        calcCommissionItemResultDTO1.setRiskStatus("riskStatus");
        final List<CalcCommissionItemResultDTO> calcCommissionItemResultDTOS = Arrays.asList(
                calcCommissionItemResultDTO1);
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO3 = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO3.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO3.setCommissionId(0);
        commissionCalcOrderInfoDTO3.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO3.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO3.setAppStatus("appStatus");
        final List<CommissionCalcOrderInfoDTO> orderInfoDTOList = Arrays.asList(commissionCalcOrderInfoDTO3);
        final CommissionCalcConfigInfoDTO configInfoDTO = new CommissionCalcConfigInfoDTO();
        configInfoDTO.setId(0);
        configInfoDTO.setType(0);
        configInfoDTO.setCompanyId(0);
        final CommissionCalcConfigDetailDTO commissionCalcConfigDetailDTO1 = new CommissionCalcConfigDetailDTO();
        commissionCalcConfigDetailDTO1.setPeriodNum(0);
        configInfoDTO.setConfigDetailDTOList(Arrays.asList(commissionCalcConfigDetailDTO1));
        mockCommissionCalcService.calc(0, 0, orderInfoDTOList, configInfoDTO, new CalcErrorDTO("errorMsg","errorMsgNoAlarm"),
                "commissionUserId", "businessType");

        // Configure CommissionCalcService.calcCorrectCommission(...).
        final CalcCommissionItemResultDTO calcCommissionItemResultDTO2 = new CalcCommissionItemResultDTO();
        calcCommissionItemResultDTO2.setOrderId("orderId");
        calcCommissionItemResultDTO2.setPolicyNo("policyNo");
        calcCommissionItemResultDTO2.setInsuredIdNumber("insuredIdNumber");
        calcCommissionItemResultDTO2.setPolicyStatus("policyStatus");
        calcCommissionItemResultDTO2.setRiskStatus("riskStatus");
        final List<CalcCommissionItemResultDTO> calcCommissionItemResultDTOS1 = Arrays.asList(
                calcCommissionItemResultDTO2);
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO4 = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO4.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO4.setCommissionId(0);
        commissionCalcOrderInfoDTO4.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO4.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO4.setAppStatus("appStatus");
        final List<CommissionCalcOrderInfoDTO> orderInfoDTOList1 = Arrays.asList(commissionCalcOrderInfoDTO4);
        final OrderRiskCorrectDTO orderRiskCorrectDTO1 = new OrderRiskCorrectDTO();
        orderRiskCorrectDTO1.setOrderId("orderId");
        orderRiskCorrectDTO1.setPolicyNo("policyNo");
        orderRiskCorrectDTO1.setCorrectId("correctId");
        orderRiskCorrectDTO1.setCorrectType("correctType");
        orderRiskCorrectDTO1.setRiskId(0);
        final List<OrderRiskCorrectDTO> correctDTOList = Arrays.asList(orderRiskCorrectDTO1);
        final SmCommissionDetailItemPO smCommissionDetailItemPO1 = new SmCommissionDetailItemPO();
        smCommissionDetailItemPO1.setOrderId("orderId");
        smCommissionDetailItemPO1.setPolicyNo("policyNo");
        smCommissionDetailItemPO1.setInsuredIdNumber("insuredIdNumber");
        smCommissionDetailItemPO1.setPolicyStatus("policyStatus");
        smCommissionDetailItemPO1.setRiskStatus("riskStatus");
        smCommissionDetailItemPO1.setPlanId(0);
        smCommissionDetailItemPO1.setRiskId(0);
        smCommissionDetailItemPO1.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smCommissionDetailItemPO1.setPlanAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO1.setAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO1.setOriginalAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO1.setTermNum(0);
        smCommissionDetailItemPO1.setCommissionType(0);
        smCommissionDetailItemPO1.setCommissionRate(new BigDecimal("0.00"));
        smCommissionDetailItemPO1.setCommissionAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO1.setCommissionUserId("commissionUserId");
        smCommissionDetailItemPO1.setBusinessType("businessType");
        smCommissionDetailItemPO1.setBusinessId("businessId");
        smCommissionDetailItemPO1.setImportFlag(0);
        final List<SmCommissionDetailItemPO> itemPOs = Arrays.asList(smCommissionDetailItemPO1);
        mockCommissionCalcService.calcCorrectCommission(0, orderInfoDTOList1, correctDTOList, itemPOs,
                new CalcErrorDTO("errorMsg","errorMsgNoAlarm"));

        // Configure CommissionCalcService.calcNewRefundDetailItem(...).
        final CalcCommissionItemResultDTO calcCommissionItemResultDTO3 = new CalcCommissionItemResultDTO();
        calcCommissionItemResultDTO3.setOrderId("orderId");
        calcCommissionItemResultDTO3.setPolicyNo("policyNo");
        calcCommissionItemResultDTO3.setInsuredIdNumber("insuredIdNumber");
        calcCommissionItemResultDTO3.setPolicyStatus("policyStatus");
        calcCommissionItemResultDTO3.setRiskStatus("riskStatus");
        final List<CalcCommissionItemResultDTO> calcCommissionItemResultDTOS2 = Arrays.asList(
                calcCommissionItemResultDTO3);
        final CommissionCalcOrderInfoDTO orderInfo = new CommissionCalcOrderInfoDTO();
        orderInfo.setProductAttrCode("productAttrCode");
        orderInfo.setCommissionId(0);
        orderInfo.setOriginalFhOrderId("originalFhOrderId");
        orderInfo.setOriginalCommissionId(0);
        orderInfo.setAppStatus("appStatus");
        mockCommissionCalcService.calcNewRefundDetailItem(orderInfo, new HashMap<>());

        // Run the test
        commissionCalcProcessServiceUnderTest.calcInsureCommission(processDTO, insuredInfoDTOList, false);

        // Verify the results
        // Confirm CommissionExternalService.getCommissionCalcOrderParam(...).
        final CommissionCalcProcessDTO processDTO1 = new CommissionCalcProcessDTO();
        processDTO1.setOrderId("orderId");
        processDTO1.setProductId(0);
        processDTO1.setProductType("productType");
        processDTO1.setMatchingTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        processDTO1.setTermNum(0);
        processDTO1.setCommissionConfigInfo(new HashMap<>());
        final CommissionRateDTO commissionRateDTO3 = new CommissionRateDTO();
        processDTO1.setOldCommissionConfigInfo(Arrays.asList(commissionRateDTO3));
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO5 = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO5.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO5.setCommissionId(0);
        commissionCalcOrderInfoDTO5.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO5.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO5.setAppStatus("appStatus");
        processDTO1.setOrderInfo(Arrays.asList(commissionCalcOrderInfoDTO5));
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO6 = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO6.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO6.setCommissionId(0);
        commissionCalcOrderInfoDTO6.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO6.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO6.setAppStatus("appStatus");
        processDTO1.setRefundOrderInfo(Arrays.asList(commissionCalcOrderInfoDTO6));
        final OrderRiskCorrectDTO orderRiskCorrectDTO2 = new OrderRiskCorrectDTO();
        processDTO1.setRiskCorrectDTOS(Arrays.asList(orderRiskCorrectDTO2));
        processDTO1.setItemMap(new HashMap<>());
        final CalcCommissionItemResultDTO calcCommissionItemResultDTO4 = new CalcCommissionItemResultDTO();
        processDTO1.setItemResults(Arrays.asList(calcCommissionItemResultDTO4));
        processDTO1.setCommissionUserId("commissionUserId");
        processDTO1.setBusinessType("businessType");
        final OrderCommissionInsuredInfoDTO orderCommissionInsuredInfoDTO1 = new OrderCommissionInsuredInfoDTO();
        orderCommissionInsuredInfoDTO1.setFhOrderId("fhOrderId");
        orderCommissionInsuredInfoDTO1.setInsuredId(0);
        orderCommissionInsuredInfoDTO1.setCompanyId(0);
        orderCommissionInsuredInfoDTO1.setProductId(0);
        orderCommissionInsuredInfoDTO1.setCommissionId(0);
        final List<OrderCommissionInsuredInfoDTO> insuredInfoDTOList1 = Arrays.asList(orderCommissionInsuredInfoDTO1);
        mockCommissionExternalService.getCommissionCalcOrderParam(processDTO1, insuredInfoDTOList1);

        // Confirm SmOrderCommissionItemMapper.batchInsertCommissionItem(...).
        final SmCommissionDetailItemPO smCommissionDetailItemPO2 = new SmCommissionDetailItemPO();
        smCommissionDetailItemPO2.setOrderId("orderId");
        smCommissionDetailItemPO2.setPolicyNo("policyNo");
        smCommissionDetailItemPO2.setInsuredIdNumber("insuredIdNumber");
        smCommissionDetailItemPO2.setPolicyStatus("policyStatus");
        smCommissionDetailItemPO2.setRiskStatus("riskStatus");
        smCommissionDetailItemPO2.setPlanId(0);
        smCommissionDetailItemPO2.setRiskId(0);
        smCommissionDetailItemPO2.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smCommissionDetailItemPO2.setPlanAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO2.setAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO2.setOriginalAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO2.setTermNum(0);
        smCommissionDetailItemPO2.setCommissionType(0);
        smCommissionDetailItemPO2.setCommissionRate(new BigDecimal("0.00"));
        smCommissionDetailItemPO2.setCommissionAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO2.setCommissionUserId("commissionUserId");
        smCommissionDetailItemPO2.setBusinessType("businessType");
        smCommissionDetailItemPO2.setBusinessId("businessId");
        smCommissionDetailItemPO2.setImportFlag(0);
        final List<SmCommissionDetailItemPO> commissionConfigItemList = Arrays.asList(smCommissionDetailItemPO2);
        mockSmOrderCommissionItemMapper.batchInsertCommissionItem(commissionConfigItemList);

        // Confirm SmOrderCommissionItemMapper.batchInsertCommissionItemExceptImport(...).
        final SmCommissionDetailItemPO smCommissionDetailItemPO3 = new SmCommissionDetailItemPO();
        smCommissionDetailItemPO3.setOrderId("orderId");
        smCommissionDetailItemPO3.setPolicyNo("policyNo");
        smCommissionDetailItemPO3.setInsuredIdNumber("insuredIdNumber");
        smCommissionDetailItemPO3.setPolicyStatus("policyStatus");
        smCommissionDetailItemPO3.setRiskStatus("riskStatus");
        smCommissionDetailItemPO3.setPlanId(0);
        smCommissionDetailItemPO3.setRiskId(0);
        smCommissionDetailItemPO3.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smCommissionDetailItemPO3.setPlanAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO3.setAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO3.setOriginalAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO3.setTermNum(0);
        smCommissionDetailItemPO3.setCommissionType(0);
        smCommissionDetailItemPO3.setCommissionRate(new BigDecimal("0.00"));
        smCommissionDetailItemPO3.setCommissionAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO3.setCommissionUserId("commissionUserId");
        smCommissionDetailItemPO3.setBusinessType("businessType");
        smCommissionDetailItemPO3.setBusinessId("businessId");
        smCommissionDetailItemPO3.setImportFlag(0);
        final List<SmCommissionDetailItemPO> commissionConfigItemList1 = Arrays.asList(smCommissionDetailItemPO3);
        mockSmOrderCommissionItemMapper.batchInsertCommissionItemExceptImport(commissionConfigItemList1);

        // Confirm SmOrderCommissionDetailMapper.batchReCalcCommissionDetail(...).
        final SmCommissionDetailPO smCommissionDetailPO = new SmCommissionDetailPO();
        smCommissionDetailPO.setOrderId("orderId");
        smCommissionDetailPO.setPolicyNo("policyNo");
        smCommissionDetailPO.setInsuredIdNumber("insuredIdNumber");
        smCommissionDetailPO.setPolicyStatus("policyStatus");
        smCommissionDetailPO.setPlanId(0);
        smCommissionDetailPO.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smCommissionDetailPO.setAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setOriginalAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setPaymentRate(new BigDecimal("0.00"));
        smCommissionDetailPO.setPaymentAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setSettlementRate(new BigDecimal("0.00"));
        smCommissionDetailPO.setSettlementAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setConversionRate(new BigDecimal("0.00"));
        smCommissionDetailPO.setConversionAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setTermNum(0);
        smCommissionDetailPO.setPaymentRiskJson("paymentRiskJson");
        smCommissionDetailPO.setSettlementRiskJson("settlementRiskJson");
        smCommissionDetailPO.setConversionRiskJson("conversionRiskJson");
        smCommissionDetailPO.setPAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setSAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setCAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setCommissionUserId("commissionUserId");
        smCommissionDetailPO.setBusinessType("businessType");
        smCommissionDetailPO.setBusinessId("businessId");
        smCommissionDetailPO.setImportFlag(0);
        final List<SmCommissionDetailPO> commissionConfigDetailList = Arrays.asList(smCommissionDetailPO);
        mockSmOrderCommissionDetailMapper.batchReCalcCommissionDetail(commissionConfigDetailList);

        // Confirm SmOrderCommissionDetailMapper.batchInsertCommissionDetail(...).
        final SmCommissionDetailPO smCommissionDetailPO1 = new SmCommissionDetailPO();
        smCommissionDetailPO1.setOrderId("orderId");
        smCommissionDetailPO1.setPolicyNo("policyNo");
        smCommissionDetailPO1.setInsuredIdNumber("insuredIdNumber");
        smCommissionDetailPO1.setPolicyStatus("policyStatus");
        smCommissionDetailPO1.setPlanId(0);
        smCommissionDetailPO1.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smCommissionDetailPO1.setAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setOriginalAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setPaymentRate(new BigDecimal("0.00"));
        smCommissionDetailPO1.setPaymentAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setSettlementRate(new BigDecimal("0.00"));
        smCommissionDetailPO1.setSettlementAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setConversionRate(new BigDecimal("0.00"));
        smCommissionDetailPO1.setConversionAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setTermNum(0);
        smCommissionDetailPO1.setPaymentRiskJson("paymentRiskJson");
        smCommissionDetailPO1.setSettlementRiskJson("settlementRiskJson");
        smCommissionDetailPO1.setConversionRiskJson("conversionRiskJson");
        smCommissionDetailPO1.setPAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setSAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setCAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setCommissionUserId("commissionUserId");
        smCommissionDetailPO1.setBusinessType("businessType");
        smCommissionDetailPO1.setBusinessId("businessId");
        smCommissionDetailPO1.setImportFlag(0);
        final List<SmCommissionDetailPO> commissionConfigDetailList1 = Arrays.asList(smCommissionDetailPO1);
        mockSmOrderCommissionDetailMapper.batchInsertCommissionDetail(commissionConfigDetailList1);
    }

    @Test
    public void testCalcInsureCommission_CommissionConfigQueryServiceListCalcCommissionDetailByConfigIdReturnsNoItems() throws Exception {
        // Setup
        final CommissionCalcProcessDTO processDTO = new CommissionCalcProcessDTO();
        processDTO.setOrderId("orderId");
        processDTO.setProductId(0);
        processDTO.setProductType("productType");
        processDTO.setMatchingTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        processDTO.setTermNum(0);
        processDTO.setCommissionConfigInfo(new HashMap<>());
        final CommissionRateDTO commissionRateDTO = new CommissionRateDTO();
        processDTO.setOldCommissionConfigInfo(Arrays.asList(commissionRateDTO));
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO.setCommissionId(0);
        commissionCalcOrderInfoDTO.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO.setAppStatus("appStatus");
        processDTO.setOrderInfo(Arrays.asList(commissionCalcOrderInfoDTO));
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO1 = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO1.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO1.setCommissionId(0);
        commissionCalcOrderInfoDTO1.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO1.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO1.setAppStatus("appStatus");
        processDTO.setRefundOrderInfo(Arrays.asList(commissionCalcOrderInfoDTO1));
        final OrderRiskCorrectDTO orderRiskCorrectDTO = new OrderRiskCorrectDTO();
        processDTO.setRiskCorrectDTOS(Arrays.asList(orderRiskCorrectDTO));
        processDTO.setItemMap(new HashMap<>());
        final CalcCommissionItemResultDTO calcCommissionItemResultDTO = new CalcCommissionItemResultDTO();
        processDTO.setItemResults(Arrays.asList(calcCommissionItemResultDTO));
        processDTO.setCommissionUserId("commissionUserId");
        processDTO.setBusinessType("businessType");

        final OrderCommissionInsuredInfoDTO orderCommissionInsuredInfoDTO = new OrderCommissionInsuredInfoDTO();
        orderCommissionInsuredInfoDTO.setFhOrderId("fhOrderId");
        orderCommissionInsuredInfoDTO.setInsuredId(0);
        orderCommissionInsuredInfoDTO.setCompanyId(0);
        orderCommissionInsuredInfoDTO.setProductId(0);
        orderCommissionInsuredInfoDTO.setCommissionId(0);
        final List<OrderCommissionInsuredInfoDTO> insuredInfoDTOList = Arrays.asList(orderCommissionInsuredInfoDTO);

        // Configure CommissionConfigQueryService.listConfigByProductId(...).
        final SystemCommissionConfig systemCommissionConfig = new SystemCommissionConfig();
        systemCommissionConfig.setType(0);
        systemCommissionConfig.setCompanyId(0);
        systemCommissionConfig.setProductId(0);
        systemCommissionConfig.setStartTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        systemCommissionConfig.setEndTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SystemCommissionConfig> systemCommissionConfigs = Arrays.asList(systemCommissionConfig);
        mockCommissionConfigQueryService.listConfigByProductId(0);

        mockCommissionConfigQueryService.listCalcCommissionDetailByConfigId(0);
        mockCommissionExternalService.isDecreaseGroupOrder("productAttrCode", "appStatus");

        // Configure SmOrderCommissionItemMapper.listCommissionItemByOrderId(...).
        final SmCommissionDetailItemPO smCommissionDetailItemPO = new SmCommissionDetailItemPO();
        smCommissionDetailItemPO.setOrderId("orderId");
        smCommissionDetailItemPO.setPolicyNo("policyNo");
        smCommissionDetailItemPO.setInsuredIdNumber("insuredIdNumber");
        smCommissionDetailItemPO.setPolicyStatus("policyStatus");
        smCommissionDetailItemPO.setRiskStatus("riskStatus");
        smCommissionDetailItemPO.setPlanId(0);
        smCommissionDetailItemPO.setRiskId(0);
        smCommissionDetailItemPO.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smCommissionDetailItemPO.setPlanAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setOriginalAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setTermNum(0);
        smCommissionDetailItemPO.setCommissionType(0);
        smCommissionDetailItemPO.setCommissionRate(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setCommissionAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setCommissionUserId("commissionUserId");
        smCommissionDetailItemPO.setBusinessType("businessType");
        smCommissionDetailItemPO.setBusinessId("businessId");
        smCommissionDetailItemPO.setImportFlag(0);
        final List<SmCommissionDetailItemPO> smCommissionDetailItemPOS = Arrays.asList(smCommissionDetailItemPO);
        mockSmOrderCommissionItemMapper.listCommissionItemByOrderId("originalFhOrderId", 0);

        // Configure CommissionExternalService.getOldConvertedConfig(...).
        final CommissionRateDTO commissionRateDTO1 = new CommissionRateDTO();
        commissionRateDTO1.setPlanId(0);
        commissionRateDTO1.setRiskId(0);
        commissionRateDTO1.setCommissionType(0);
        commissionRateDTO1.setCommissionId(0);
        commissionRateDTO1.setCommissionRate(new BigDecimal("0.00"));
        final List<CommissionRateDTO> commissionRateDTOS = Arrays.asList(commissionRateDTO1);
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO2 = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO2.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO2.setCommissionId(0);
        commissionCalcOrderInfoDTO2.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO2.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO2.setAppStatus("appStatus");
        final List<CommissionCalcOrderInfoDTO> orderInfoList = Arrays.asList(commissionCalcOrderInfoDTO2);
        mockCommissionExternalService.getOldConvertedConfig(orderInfoList);

        // Configure CommissionConfigQueryService.getOldCommissionSetting(...).
        final CommissionRateDTO commissionRateDTO2 = new CommissionRateDTO();
        commissionRateDTO2.setPlanId(0);
        commissionRateDTO2.setRiskId(0);
        commissionRateDTO2.setCommissionType(0);
        commissionRateDTO2.setCommissionId(0);
        commissionRateDTO2.setCommissionRate(new BigDecimal("0.00"));
        final List<CommissionRateDTO> commissionRateDTOS1 = Arrays.asList(commissionRateDTO2);
        mockCommissionConfigQueryService.getOldCommissionSetting(Arrays.asList(0));

        // Configure CommissionCalcService.calc(...).
        final CalcCommissionItemResultDTO calcCommissionItemResultDTO1 = new CalcCommissionItemResultDTO();
        calcCommissionItemResultDTO1.setOrderId("orderId");
        calcCommissionItemResultDTO1.setPolicyNo("policyNo");
        calcCommissionItemResultDTO1.setInsuredIdNumber("insuredIdNumber");
        calcCommissionItemResultDTO1.setPolicyStatus("policyStatus");
        calcCommissionItemResultDTO1.setRiskStatus("riskStatus");
        final List<CalcCommissionItemResultDTO> calcCommissionItemResultDTOS = Arrays.asList(
                calcCommissionItemResultDTO1);
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO3 = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO3.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO3.setCommissionId(0);
        commissionCalcOrderInfoDTO3.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO3.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO3.setAppStatus("appStatus");
        final List<CommissionCalcOrderInfoDTO> orderInfoDTOList = Arrays.asList(commissionCalcOrderInfoDTO3);
        final CommissionCalcConfigInfoDTO configInfoDTO = new CommissionCalcConfigInfoDTO();
        configInfoDTO.setId(0);
        configInfoDTO.setType(0);
        configInfoDTO.setCompanyId(0);
        final CommissionCalcConfigDetailDTO commissionCalcConfigDetailDTO = new CommissionCalcConfigDetailDTO();
        commissionCalcConfigDetailDTO.setPeriodNum(0);
        configInfoDTO.setConfigDetailDTOList(Arrays.asList(commissionCalcConfigDetailDTO));
        mockCommissionCalcService.calc(0, 0, orderInfoDTOList, configInfoDTO, new CalcErrorDTO("errorMsg","errorMsgNoAlarm"),
                "commissionUserId", "businessType");

        // Configure CommissionCalcService.calcCorrectCommission(...).
        final CalcCommissionItemResultDTO calcCommissionItemResultDTO2 = new CalcCommissionItemResultDTO();
        calcCommissionItemResultDTO2.setOrderId("orderId");
        calcCommissionItemResultDTO2.setPolicyNo("policyNo");
        calcCommissionItemResultDTO2.setInsuredIdNumber("insuredIdNumber");
        calcCommissionItemResultDTO2.setPolicyStatus("policyStatus");
        calcCommissionItemResultDTO2.setRiskStatus("riskStatus");
        final List<CalcCommissionItemResultDTO> calcCommissionItemResultDTOS1 = Arrays.asList(
                calcCommissionItemResultDTO2);
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO4 = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO4.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO4.setCommissionId(0);
        commissionCalcOrderInfoDTO4.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO4.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO4.setAppStatus("appStatus");
        final List<CommissionCalcOrderInfoDTO> orderInfoDTOList1 = Arrays.asList(commissionCalcOrderInfoDTO4);
        final OrderRiskCorrectDTO orderRiskCorrectDTO1 = new OrderRiskCorrectDTO();
        orderRiskCorrectDTO1.setOrderId("orderId");
        orderRiskCorrectDTO1.setPolicyNo("policyNo");
        orderRiskCorrectDTO1.setCorrectId("correctId");
        orderRiskCorrectDTO1.setCorrectType("correctType");
        orderRiskCorrectDTO1.setRiskId(0);
        final List<OrderRiskCorrectDTO> correctDTOList = Arrays.asList(orderRiskCorrectDTO1);
        final SmCommissionDetailItemPO smCommissionDetailItemPO1 = new SmCommissionDetailItemPO();
        smCommissionDetailItemPO1.setOrderId("orderId");
        smCommissionDetailItemPO1.setPolicyNo("policyNo");
        smCommissionDetailItemPO1.setInsuredIdNumber("insuredIdNumber");
        smCommissionDetailItemPO1.setPolicyStatus("policyStatus");
        smCommissionDetailItemPO1.setRiskStatus("riskStatus");
        smCommissionDetailItemPO1.setPlanId(0);
        smCommissionDetailItemPO1.setRiskId(0);
        smCommissionDetailItemPO1.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smCommissionDetailItemPO1.setPlanAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO1.setAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO1.setOriginalAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO1.setTermNum(0);
        smCommissionDetailItemPO1.setCommissionType(0);
        smCommissionDetailItemPO1.setCommissionRate(new BigDecimal("0.00"));
        smCommissionDetailItemPO1.setCommissionAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO1.setCommissionUserId("commissionUserId");
        smCommissionDetailItemPO1.setBusinessType("businessType");
        smCommissionDetailItemPO1.setBusinessId("businessId");
        smCommissionDetailItemPO1.setImportFlag(0);
        final List<SmCommissionDetailItemPO> itemPOs = Arrays.asList(smCommissionDetailItemPO1);
        mockCommissionCalcService.calcCorrectCommission(0, orderInfoDTOList1, correctDTOList, itemPOs,
                new CalcErrorDTO("errorMsg","errorMsgNoAlarm"));

        // Configure CommissionCalcService.calcNewRefundDetailItem(...).
        final CalcCommissionItemResultDTO calcCommissionItemResultDTO3 = new CalcCommissionItemResultDTO();
        calcCommissionItemResultDTO3.setOrderId("orderId");
        calcCommissionItemResultDTO3.setPolicyNo("policyNo");
        calcCommissionItemResultDTO3.setInsuredIdNumber("insuredIdNumber");
        calcCommissionItemResultDTO3.setPolicyStatus("policyStatus");
        calcCommissionItemResultDTO3.setRiskStatus("riskStatus");
        final List<CalcCommissionItemResultDTO> calcCommissionItemResultDTOS2 = Arrays.asList(
                calcCommissionItemResultDTO3);
        final CommissionCalcOrderInfoDTO orderInfo = new CommissionCalcOrderInfoDTO();
        orderInfo.setProductAttrCode("productAttrCode");
        orderInfo.setCommissionId(0);
        orderInfo.setOriginalFhOrderId("originalFhOrderId");
        orderInfo.setOriginalCommissionId(0);
        orderInfo.setAppStatus("appStatus");
        mockCommissionCalcService.calcNewRefundDetailItem(orderInfo, new HashMap<>());

        // Run the test
        commissionCalcProcessServiceUnderTest.calcInsureCommission(processDTO, insuredInfoDTOList, false);

        // Verify the results
        // Confirm CommissionExternalService.getCommissionCalcOrderParam(...).
        final CommissionCalcProcessDTO processDTO1 = new CommissionCalcProcessDTO();
        processDTO1.setOrderId("orderId");
        processDTO1.setProductId(0);
        processDTO1.setProductType("productType");
        processDTO1.setMatchingTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        processDTO1.setTermNum(0);
        processDTO1.setCommissionConfigInfo(new HashMap<>());
        final CommissionRateDTO commissionRateDTO3 = new CommissionRateDTO();
        processDTO1.setOldCommissionConfigInfo(Arrays.asList(commissionRateDTO3));
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO5 = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO5.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO5.setCommissionId(0);
        commissionCalcOrderInfoDTO5.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO5.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO5.setAppStatus("appStatus");
        processDTO1.setOrderInfo(Arrays.asList(commissionCalcOrderInfoDTO5));
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO6 = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO6.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO6.setCommissionId(0);
        commissionCalcOrderInfoDTO6.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO6.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO6.setAppStatus("appStatus");
        processDTO1.setRefundOrderInfo(Arrays.asList(commissionCalcOrderInfoDTO6));
        final OrderRiskCorrectDTO orderRiskCorrectDTO2 = new OrderRiskCorrectDTO();
        processDTO1.setRiskCorrectDTOS(Arrays.asList(orderRiskCorrectDTO2));
        processDTO1.setItemMap(new HashMap<>());
        final CalcCommissionItemResultDTO calcCommissionItemResultDTO4 = new CalcCommissionItemResultDTO();
        processDTO1.setItemResults(Arrays.asList(calcCommissionItemResultDTO4));
        processDTO1.setCommissionUserId("commissionUserId");
        processDTO1.setBusinessType("businessType");
        final OrderCommissionInsuredInfoDTO orderCommissionInsuredInfoDTO1 = new OrderCommissionInsuredInfoDTO();
        orderCommissionInsuredInfoDTO1.setFhOrderId("fhOrderId");
        orderCommissionInsuredInfoDTO1.setInsuredId(0);
        orderCommissionInsuredInfoDTO1.setCompanyId(0);
        orderCommissionInsuredInfoDTO1.setProductId(0);
        orderCommissionInsuredInfoDTO1.setCommissionId(0);
        final List<OrderCommissionInsuredInfoDTO> insuredInfoDTOList1 = Arrays.asList(orderCommissionInsuredInfoDTO1);
        mockCommissionExternalService.getCommissionCalcOrderParam(processDTO1, insuredInfoDTOList1);

        // Confirm SmOrderCommissionItemMapper.batchInsertCommissionItem(...).
        final SmCommissionDetailItemPO smCommissionDetailItemPO2 = new SmCommissionDetailItemPO();
        smCommissionDetailItemPO2.setOrderId("orderId");
        smCommissionDetailItemPO2.setPolicyNo("policyNo");
        smCommissionDetailItemPO2.setInsuredIdNumber("insuredIdNumber");
        smCommissionDetailItemPO2.setPolicyStatus("policyStatus");
        smCommissionDetailItemPO2.setRiskStatus("riskStatus");
        smCommissionDetailItemPO2.setPlanId(0);
        smCommissionDetailItemPO2.setRiskId(0);
        smCommissionDetailItemPO2.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smCommissionDetailItemPO2.setPlanAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO2.setAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO2.setOriginalAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO2.setTermNum(0);
        smCommissionDetailItemPO2.setCommissionType(0);
        smCommissionDetailItemPO2.setCommissionRate(new BigDecimal("0.00"));
        smCommissionDetailItemPO2.setCommissionAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO2.setCommissionUserId("commissionUserId");
        smCommissionDetailItemPO2.setBusinessType("businessType");
        smCommissionDetailItemPO2.setBusinessId("businessId");
        smCommissionDetailItemPO2.setImportFlag(0);
        final List<SmCommissionDetailItemPO> commissionConfigItemList = Arrays.asList(smCommissionDetailItemPO2);
        mockSmOrderCommissionItemMapper.batchInsertCommissionItem(commissionConfigItemList);

        // Confirm SmOrderCommissionItemMapper.batchInsertCommissionItemExceptImport(...).
        final SmCommissionDetailItemPO smCommissionDetailItemPO3 = new SmCommissionDetailItemPO();
        smCommissionDetailItemPO3.setOrderId("orderId");
        smCommissionDetailItemPO3.setPolicyNo("policyNo");
        smCommissionDetailItemPO3.setInsuredIdNumber("insuredIdNumber");
        smCommissionDetailItemPO3.setPolicyStatus("policyStatus");
        smCommissionDetailItemPO3.setRiskStatus("riskStatus");
        smCommissionDetailItemPO3.setPlanId(0);
        smCommissionDetailItemPO3.setRiskId(0);
        smCommissionDetailItemPO3.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smCommissionDetailItemPO3.setPlanAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO3.setAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO3.setOriginalAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO3.setTermNum(0);
        smCommissionDetailItemPO3.setCommissionType(0);
        smCommissionDetailItemPO3.setCommissionRate(new BigDecimal("0.00"));
        smCommissionDetailItemPO3.setCommissionAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO3.setCommissionUserId("commissionUserId");
        smCommissionDetailItemPO3.setBusinessType("businessType");
        smCommissionDetailItemPO3.setBusinessId("businessId");
        smCommissionDetailItemPO3.setImportFlag(0);
        final List<SmCommissionDetailItemPO> commissionConfigItemList1 = Arrays.asList(smCommissionDetailItemPO3);
        mockSmOrderCommissionItemMapper.batchInsertCommissionItemExceptImport(commissionConfigItemList1);

        // Confirm SmOrderCommissionDetailMapper.batchReCalcCommissionDetail(...).
        final SmCommissionDetailPO smCommissionDetailPO = new SmCommissionDetailPO();
        smCommissionDetailPO.setOrderId("orderId");
        smCommissionDetailPO.setPolicyNo("policyNo");
        smCommissionDetailPO.setInsuredIdNumber("insuredIdNumber");
        smCommissionDetailPO.setPolicyStatus("policyStatus");
        smCommissionDetailPO.setPlanId(0);
        smCommissionDetailPO.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smCommissionDetailPO.setAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setOriginalAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setPaymentRate(new BigDecimal("0.00"));
        smCommissionDetailPO.setPaymentAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setSettlementRate(new BigDecimal("0.00"));
        smCommissionDetailPO.setSettlementAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setConversionRate(new BigDecimal("0.00"));
        smCommissionDetailPO.setConversionAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setTermNum(0);
        smCommissionDetailPO.setPaymentRiskJson("paymentRiskJson");
        smCommissionDetailPO.setSettlementRiskJson("settlementRiskJson");
        smCommissionDetailPO.setConversionRiskJson("conversionRiskJson");
        smCommissionDetailPO.setPAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setSAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setCAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setCommissionUserId("commissionUserId");
        smCommissionDetailPO.setBusinessType("businessType");
        smCommissionDetailPO.setBusinessId("businessId");
        smCommissionDetailPO.setImportFlag(0);
        final List<SmCommissionDetailPO> commissionConfigDetailList = Arrays.asList(smCommissionDetailPO);
        mockSmOrderCommissionDetailMapper.batchReCalcCommissionDetail(commissionConfigDetailList);

        // Confirm SmOrderCommissionDetailMapper.batchInsertCommissionDetail(...).
        final SmCommissionDetailPO smCommissionDetailPO1 = new SmCommissionDetailPO();
        smCommissionDetailPO1.setOrderId("orderId");
        smCommissionDetailPO1.setPolicyNo("policyNo");
        smCommissionDetailPO1.setInsuredIdNumber("insuredIdNumber");
        smCommissionDetailPO1.setPolicyStatus("policyStatus");
        smCommissionDetailPO1.setPlanId(0);
        smCommissionDetailPO1.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smCommissionDetailPO1.setAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setOriginalAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setPaymentRate(new BigDecimal("0.00"));
        smCommissionDetailPO1.setPaymentAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setSettlementRate(new BigDecimal("0.00"));
        smCommissionDetailPO1.setSettlementAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setConversionRate(new BigDecimal("0.00"));
        smCommissionDetailPO1.setConversionAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setTermNum(0);
        smCommissionDetailPO1.setPaymentRiskJson("paymentRiskJson");
        smCommissionDetailPO1.setSettlementRiskJson("settlementRiskJson");
        smCommissionDetailPO1.setConversionRiskJson("conversionRiskJson");
        smCommissionDetailPO1.setPAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setSAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setCAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setCommissionUserId("commissionUserId");
        smCommissionDetailPO1.setBusinessType("businessType");
        smCommissionDetailPO1.setBusinessId("businessId");
        smCommissionDetailPO1.setImportFlag(0);
        final List<SmCommissionDetailPO> commissionConfigDetailList1 = Arrays.asList(smCommissionDetailPO1);
        mockSmOrderCommissionDetailMapper.batchInsertCommissionDetail(commissionConfigDetailList1);
    }

    @Test
    public void testCalcInsureCommission_SmOrderCommissionItemMapperListCommissionItemByOrderIdReturnsNoItems() throws Exception {
        // Setup
        final CommissionCalcProcessDTO processDTO = new CommissionCalcProcessDTO();
        processDTO.setOrderId("orderId");
        processDTO.setProductId(0);
        processDTO.setProductType("productType");
        processDTO.setMatchingTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        processDTO.setTermNum(0);
        processDTO.setCommissionConfigInfo(new HashMap<>());
        final CommissionRateDTO commissionRateDTO = new CommissionRateDTO();
        processDTO.setOldCommissionConfigInfo(Arrays.asList(commissionRateDTO));
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO.setCommissionId(0);
        commissionCalcOrderInfoDTO.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO.setAppStatus("appStatus");
        processDTO.setOrderInfo(Arrays.asList(commissionCalcOrderInfoDTO));
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO1 = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO1.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO1.setCommissionId(0);
        commissionCalcOrderInfoDTO1.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO1.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO1.setAppStatus("appStatus");
        processDTO.setRefundOrderInfo(Arrays.asList(commissionCalcOrderInfoDTO1));
        final OrderRiskCorrectDTO orderRiskCorrectDTO = new OrderRiskCorrectDTO();
        processDTO.setRiskCorrectDTOS(Arrays.asList(orderRiskCorrectDTO));
        processDTO.setItemMap(new HashMap<>());
        final CalcCommissionItemResultDTO calcCommissionItemResultDTO = new CalcCommissionItemResultDTO();
        processDTO.setItemResults(Arrays.asList(calcCommissionItemResultDTO));
        processDTO.setCommissionUserId("commissionUserId");
        processDTO.setBusinessType("businessType");

        final OrderCommissionInsuredInfoDTO orderCommissionInsuredInfoDTO = new OrderCommissionInsuredInfoDTO();
        orderCommissionInsuredInfoDTO.setFhOrderId("fhOrderId");
        orderCommissionInsuredInfoDTO.setInsuredId(0);
        orderCommissionInsuredInfoDTO.setCompanyId(0);
        orderCommissionInsuredInfoDTO.setProductId(0);
        orderCommissionInsuredInfoDTO.setCommissionId(0);
        final List<OrderCommissionInsuredInfoDTO> insuredInfoDTOList = Arrays.asList(orderCommissionInsuredInfoDTO);

        // Configure CommissionConfigQueryService.listConfigByProductId(...).
        final SystemCommissionConfig systemCommissionConfig = new SystemCommissionConfig();
        systemCommissionConfig.setType(0);
        systemCommissionConfig.setCompanyId(0);
        systemCommissionConfig.setProductId(0);
        systemCommissionConfig.setStartTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        systemCommissionConfig.setEndTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SystemCommissionConfig> systemCommissionConfigs = Arrays.asList(systemCommissionConfig);
        mockCommissionConfigQueryService.listConfigByProductId(0);

        // Configure CommissionConfigQueryService.listCalcCommissionDetailByConfigId(...).
        final CommissionCalcConfigDetailDTO commissionCalcConfigDetailDTO = new CommissionCalcConfigDetailDTO();
        commissionCalcConfigDetailDTO.setId(0);
        commissionCalcConfigDetailDTO.setConfigId(0);
        commissionCalcConfigDetailDTO.setPeriodNum(0);
        commissionCalcConfigDetailDTO.setPlanId(0);
        commissionCalcConfigDetailDTO.setRiskId(0);
        final List<CommissionCalcConfigDetailDTO> commissionCalcConfigDetailDTOS = Arrays.asList(
                commissionCalcConfigDetailDTO);
        mockCommissionConfigQueryService.listCalcCommissionDetailByConfigId(0);

        mockCommissionExternalService.isDecreaseGroupOrder("productAttrCode", "appStatus");
        mockSmOrderCommissionItemMapper.listCommissionItemByOrderId("originalFhOrderId", 0);

        // Configure CommissionExternalService.getOldConvertedConfig(...).
        final CommissionRateDTO commissionRateDTO1 = new CommissionRateDTO();
        commissionRateDTO1.setPlanId(0);
        commissionRateDTO1.setRiskId(0);
        commissionRateDTO1.setCommissionType(0);
        commissionRateDTO1.setCommissionId(0);
        commissionRateDTO1.setCommissionRate(new BigDecimal("0.00"));
        final List<CommissionRateDTO> commissionRateDTOS = Arrays.asList(commissionRateDTO1);
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO2 = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO2.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO2.setCommissionId(0);
        commissionCalcOrderInfoDTO2.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO2.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO2.setAppStatus("appStatus");
        final List<CommissionCalcOrderInfoDTO> orderInfoList = Arrays.asList(commissionCalcOrderInfoDTO2);
        mockCommissionExternalService.getOldConvertedConfig(orderInfoList);

        // Configure CommissionConfigQueryService.getOldCommissionSetting(...).
        final CommissionRateDTO commissionRateDTO2 = new CommissionRateDTO();
        commissionRateDTO2.setPlanId(0);
        commissionRateDTO2.setRiskId(0);
        commissionRateDTO2.setCommissionType(0);
        commissionRateDTO2.setCommissionId(0);
        commissionRateDTO2.setCommissionRate(new BigDecimal("0.00"));
        final List<CommissionRateDTO> commissionRateDTOS1 = Arrays.asList(commissionRateDTO2);
        mockCommissionConfigQueryService.getOldCommissionSetting(Arrays.asList(0));

        // Configure CommissionCalcService.calc(...).
        final CalcCommissionItemResultDTO calcCommissionItemResultDTO1 = new CalcCommissionItemResultDTO();
        calcCommissionItemResultDTO1.setOrderId("orderId");
        calcCommissionItemResultDTO1.setPolicyNo("policyNo");
        calcCommissionItemResultDTO1.setInsuredIdNumber("insuredIdNumber");
        calcCommissionItemResultDTO1.setPolicyStatus("policyStatus");
        calcCommissionItemResultDTO1.setRiskStatus("riskStatus");
        final List<CalcCommissionItemResultDTO> calcCommissionItemResultDTOS = Arrays.asList(
                calcCommissionItemResultDTO1);
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO3 = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO3.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO3.setCommissionId(0);
        commissionCalcOrderInfoDTO3.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO3.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO3.setAppStatus("appStatus");
        final List<CommissionCalcOrderInfoDTO> orderInfoDTOList = Arrays.asList(commissionCalcOrderInfoDTO3);
        final CommissionCalcConfigInfoDTO configInfoDTO = new CommissionCalcConfigInfoDTO();
        configInfoDTO.setId(0);
        configInfoDTO.setType(0);
        configInfoDTO.setCompanyId(0);
        final CommissionCalcConfigDetailDTO commissionCalcConfigDetailDTO1 = new CommissionCalcConfigDetailDTO();
        commissionCalcConfigDetailDTO1.setPeriodNum(0);
        configInfoDTO.setConfigDetailDTOList(Arrays.asList(commissionCalcConfigDetailDTO1));
        mockCommissionCalcService.calc(0, 0, orderInfoDTOList, configInfoDTO, new CalcErrorDTO("errorMsg","errorMsgNoAlarm"),
                "commissionUserId", "businessType");

        // Configure CommissionCalcService.calcCorrectCommission(...).
        final CalcCommissionItemResultDTO calcCommissionItemResultDTO2 = new CalcCommissionItemResultDTO();
        calcCommissionItemResultDTO2.setOrderId("orderId");
        calcCommissionItemResultDTO2.setPolicyNo("policyNo");
        calcCommissionItemResultDTO2.setInsuredIdNumber("insuredIdNumber");
        calcCommissionItemResultDTO2.setPolicyStatus("policyStatus");
        calcCommissionItemResultDTO2.setRiskStatus("riskStatus");
        final List<CalcCommissionItemResultDTO> calcCommissionItemResultDTOS1 = Arrays.asList(
                calcCommissionItemResultDTO2);
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO4 = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO4.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO4.setCommissionId(0);
        commissionCalcOrderInfoDTO4.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO4.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO4.setAppStatus("appStatus");
        final List<CommissionCalcOrderInfoDTO> orderInfoDTOList1 = Arrays.asList(commissionCalcOrderInfoDTO4);
        final OrderRiskCorrectDTO orderRiskCorrectDTO1 = new OrderRiskCorrectDTO();
        orderRiskCorrectDTO1.setOrderId("orderId");
        orderRiskCorrectDTO1.setPolicyNo("policyNo");
        orderRiskCorrectDTO1.setCorrectId("correctId");
        orderRiskCorrectDTO1.setCorrectType("correctType");
        orderRiskCorrectDTO1.setRiskId(0);
        final List<OrderRiskCorrectDTO> correctDTOList = Arrays.asList(orderRiskCorrectDTO1);
        final SmCommissionDetailItemPO smCommissionDetailItemPO = new SmCommissionDetailItemPO();
        smCommissionDetailItemPO.setOrderId("orderId");
        smCommissionDetailItemPO.setPolicyNo("policyNo");
        smCommissionDetailItemPO.setInsuredIdNumber("insuredIdNumber");
        smCommissionDetailItemPO.setPolicyStatus("policyStatus");
        smCommissionDetailItemPO.setRiskStatus("riskStatus");
        smCommissionDetailItemPO.setPlanId(0);
        smCommissionDetailItemPO.setRiskId(0);
        smCommissionDetailItemPO.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smCommissionDetailItemPO.setPlanAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setOriginalAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setTermNum(0);
        smCommissionDetailItemPO.setCommissionType(0);
        smCommissionDetailItemPO.setCommissionRate(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setCommissionAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setCommissionUserId("commissionUserId");
        smCommissionDetailItemPO.setBusinessType("businessType");
        smCommissionDetailItemPO.setBusinessId("businessId");
        smCommissionDetailItemPO.setImportFlag(0);
        final List<SmCommissionDetailItemPO> itemPOs = Arrays.asList(smCommissionDetailItemPO);
        mockCommissionCalcService.calcCorrectCommission(0, orderInfoDTOList1, correctDTOList, itemPOs,
                new CalcErrorDTO("errorMsg","errorMsgNoAlarm"));

        // Configure CommissionCalcService.calcNewRefundDetailItem(...).
        final CalcCommissionItemResultDTO calcCommissionItemResultDTO3 = new CalcCommissionItemResultDTO();
        calcCommissionItemResultDTO3.setOrderId("orderId");
        calcCommissionItemResultDTO3.setPolicyNo("policyNo");
        calcCommissionItemResultDTO3.setInsuredIdNumber("insuredIdNumber");
        calcCommissionItemResultDTO3.setPolicyStatus("policyStatus");
        calcCommissionItemResultDTO3.setRiskStatus("riskStatus");
        final List<CalcCommissionItemResultDTO> calcCommissionItemResultDTOS2 = Arrays.asList(
                calcCommissionItemResultDTO3);
        final CommissionCalcOrderInfoDTO orderInfo = new CommissionCalcOrderInfoDTO();
        orderInfo.setProductAttrCode("productAttrCode");
        orderInfo.setCommissionId(0);
        orderInfo.setOriginalFhOrderId("originalFhOrderId");
        orderInfo.setOriginalCommissionId(0);
        orderInfo.setAppStatus("appStatus");
        mockCommissionCalcService.calcNewRefundDetailItem(orderInfo, new HashMap<>());

        // Run the test
        commissionCalcProcessServiceUnderTest.calcInsureCommission(processDTO, insuredInfoDTOList, false);

        // Verify the results
        // Confirm CommissionExternalService.getCommissionCalcOrderParam(...).
        final CommissionCalcProcessDTO processDTO1 = new CommissionCalcProcessDTO();
        processDTO1.setOrderId("orderId");
        processDTO1.setProductId(0);
        processDTO1.setProductType("productType");
        processDTO1.setMatchingTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        processDTO1.setTermNum(0);
        processDTO1.setCommissionConfigInfo(new HashMap<>());
        final CommissionRateDTO commissionRateDTO3 = new CommissionRateDTO();
        processDTO1.setOldCommissionConfigInfo(Arrays.asList(commissionRateDTO3));
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO5 = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO5.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO5.setCommissionId(0);
        commissionCalcOrderInfoDTO5.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO5.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO5.setAppStatus("appStatus");
        processDTO1.setOrderInfo(Arrays.asList(commissionCalcOrderInfoDTO5));
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO6 = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO6.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO6.setCommissionId(0);
        commissionCalcOrderInfoDTO6.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO6.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO6.setAppStatus("appStatus");
        processDTO1.setRefundOrderInfo(Arrays.asList(commissionCalcOrderInfoDTO6));
        final OrderRiskCorrectDTO orderRiskCorrectDTO2 = new OrderRiskCorrectDTO();
        processDTO1.setRiskCorrectDTOS(Arrays.asList(orderRiskCorrectDTO2));
        processDTO1.setItemMap(new HashMap<>());
        final CalcCommissionItemResultDTO calcCommissionItemResultDTO4 = new CalcCommissionItemResultDTO();
        processDTO1.setItemResults(Arrays.asList(calcCommissionItemResultDTO4));
        processDTO1.setCommissionUserId("commissionUserId");
        processDTO1.setBusinessType("businessType");
        final OrderCommissionInsuredInfoDTO orderCommissionInsuredInfoDTO1 = new OrderCommissionInsuredInfoDTO();
        orderCommissionInsuredInfoDTO1.setFhOrderId("fhOrderId");
        orderCommissionInsuredInfoDTO1.setInsuredId(0);
        orderCommissionInsuredInfoDTO1.setCompanyId(0);
        orderCommissionInsuredInfoDTO1.setProductId(0);
        orderCommissionInsuredInfoDTO1.setCommissionId(0);
        final List<OrderCommissionInsuredInfoDTO> insuredInfoDTOList1 = Arrays.asList(orderCommissionInsuredInfoDTO1);
        mockCommissionExternalService.getCommissionCalcOrderParam(processDTO1, insuredInfoDTOList1);

        // Confirm SmOrderCommissionItemMapper.batchInsertCommissionItem(...).
        final SmCommissionDetailItemPO smCommissionDetailItemPO1 = new SmCommissionDetailItemPO();
        smCommissionDetailItemPO1.setOrderId("orderId");
        smCommissionDetailItemPO1.setPolicyNo("policyNo");
        smCommissionDetailItemPO1.setInsuredIdNumber("insuredIdNumber");
        smCommissionDetailItemPO1.setPolicyStatus("policyStatus");
        smCommissionDetailItemPO1.setRiskStatus("riskStatus");
        smCommissionDetailItemPO1.setPlanId(0);
        smCommissionDetailItemPO1.setRiskId(0);
        smCommissionDetailItemPO1.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smCommissionDetailItemPO1.setPlanAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO1.setAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO1.setOriginalAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO1.setTermNum(0);
        smCommissionDetailItemPO1.setCommissionType(0);
        smCommissionDetailItemPO1.setCommissionRate(new BigDecimal("0.00"));
        smCommissionDetailItemPO1.setCommissionAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO1.setCommissionUserId("commissionUserId");
        smCommissionDetailItemPO1.setBusinessType("businessType");
        smCommissionDetailItemPO1.setBusinessId("businessId");
        smCommissionDetailItemPO1.setImportFlag(0);
        final List<SmCommissionDetailItemPO> commissionConfigItemList = Arrays.asList(smCommissionDetailItemPO1);
        mockSmOrderCommissionItemMapper.batchInsertCommissionItem(commissionConfigItemList);

        // Confirm SmOrderCommissionItemMapper.batchInsertCommissionItemExceptImport(...).
        final SmCommissionDetailItemPO smCommissionDetailItemPO2 = new SmCommissionDetailItemPO();
        smCommissionDetailItemPO2.setOrderId("orderId");
        smCommissionDetailItemPO2.setPolicyNo("policyNo");
        smCommissionDetailItemPO2.setInsuredIdNumber("insuredIdNumber");
        smCommissionDetailItemPO2.setPolicyStatus("policyStatus");
        smCommissionDetailItemPO2.setRiskStatus("riskStatus");
        smCommissionDetailItemPO2.setPlanId(0);
        smCommissionDetailItemPO2.setRiskId(0);
        smCommissionDetailItemPO2.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smCommissionDetailItemPO2.setPlanAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO2.setAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO2.setOriginalAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO2.setTermNum(0);
        smCommissionDetailItemPO2.setCommissionType(0);
        smCommissionDetailItemPO2.setCommissionRate(new BigDecimal("0.00"));
        smCommissionDetailItemPO2.setCommissionAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO2.setCommissionUserId("commissionUserId");
        smCommissionDetailItemPO2.setBusinessType("businessType");
        smCommissionDetailItemPO2.setBusinessId("businessId");
        smCommissionDetailItemPO2.setImportFlag(0);
        final List<SmCommissionDetailItemPO> commissionConfigItemList1 = Arrays.asList(smCommissionDetailItemPO2);
        mockSmOrderCommissionItemMapper.batchInsertCommissionItemExceptImport(commissionConfigItemList1);

        // Confirm SmOrderCommissionDetailMapper.batchReCalcCommissionDetail(...).
        final SmCommissionDetailPO smCommissionDetailPO = new SmCommissionDetailPO();
        smCommissionDetailPO.setOrderId("orderId");
        smCommissionDetailPO.setPolicyNo("policyNo");
        smCommissionDetailPO.setInsuredIdNumber("insuredIdNumber");
        smCommissionDetailPO.setPolicyStatus("policyStatus");
        smCommissionDetailPO.setPlanId(0);
        smCommissionDetailPO.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smCommissionDetailPO.setAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setOriginalAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setPaymentRate(new BigDecimal("0.00"));
        smCommissionDetailPO.setPaymentAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setSettlementRate(new BigDecimal("0.00"));
        smCommissionDetailPO.setSettlementAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setConversionRate(new BigDecimal("0.00"));
        smCommissionDetailPO.setConversionAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setTermNum(0);
        smCommissionDetailPO.setPaymentRiskJson("paymentRiskJson");
        smCommissionDetailPO.setSettlementRiskJson("settlementRiskJson");
        smCommissionDetailPO.setConversionRiskJson("conversionRiskJson");
        smCommissionDetailPO.setPAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setSAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setCAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setCommissionUserId("commissionUserId");
        smCommissionDetailPO.setBusinessType("businessType");
        smCommissionDetailPO.setBusinessId("businessId");
        smCommissionDetailPO.setImportFlag(0);
        final List<SmCommissionDetailPO> commissionConfigDetailList = Arrays.asList(smCommissionDetailPO);
        mockSmOrderCommissionDetailMapper.batchReCalcCommissionDetail(commissionConfigDetailList);

        // Confirm SmOrderCommissionDetailMapper.batchInsertCommissionDetail(...).
        final SmCommissionDetailPO smCommissionDetailPO1 = new SmCommissionDetailPO();
        smCommissionDetailPO1.setOrderId("orderId");
        smCommissionDetailPO1.setPolicyNo("policyNo");
        smCommissionDetailPO1.setInsuredIdNumber("insuredIdNumber");
        smCommissionDetailPO1.setPolicyStatus("policyStatus");
        smCommissionDetailPO1.setPlanId(0);
        smCommissionDetailPO1.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smCommissionDetailPO1.setAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setOriginalAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setPaymentRate(new BigDecimal("0.00"));
        smCommissionDetailPO1.setPaymentAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setSettlementRate(new BigDecimal("0.00"));
        smCommissionDetailPO1.setSettlementAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setConversionRate(new BigDecimal("0.00"));
        smCommissionDetailPO1.setConversionAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setTermNum(0);
        smCommissionDetailPO1.setPaymentRiskJson("paymentRiskJson");
        smCommissionDetailPO1.setSettlementRiskJson("settlementRiskJson");
        smCommissionDetailPO1.setConversionRiskJson("conversionRiskJson");
        smCommissionDetailPO1.setPAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setSAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setCAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setCommissionUserId("commissionUserId");
        smCommissionDetailPO1.setBusinessType("businessType");
        smCommissionDetailPO1.setBusinessId("businessId");
        smCommissionDetailPO1.setImportFlag(0);
        final List<SmCommissionDetailPO> commissionConfigDetailList1 = Arrays.asList(smCommissionDetailPO1);
        mockSmOrderCommissionDetailMapper.batchInsertCommissionDetail(commissionConfigDetailList1);
    }

    @Test
    public void testCalcInsureCommission_CommissionExternalServiceGetOldConvertedConfigReturnsNoItems() throws Exception {
        // Setup
        final CommissionCalcProcessDTO processDTO = new CommissionCalcProcessDTO();
        processDTO.setOrderId("orderId");
        processDTO.setProductId(0);
        processDTO.setProductType("productType");
        processDTO.setMatchingTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        processDTO.setTermNum(0);
        processDTO.setCommissionConfigInfo(new HashMap<>());
        final CommissionRateDTO commissionRateDTO = new CommissionRateDTO();
        processDTO.setOldCommissionConfigInfo(Arrays.asList(commissionRateDTO));
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO.setCommissionId(0);
        commissionCalcOrderInfoDTO.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO.setAppStatus("appStatus");
        processDTO.setOrderInfo(Arrays.asList(commissionCalcOrderInfoDTO));
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO1 = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO1.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO1.setCommissionId(0);
        commissionCalcOrderInfoDTO1.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO1.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO1.setAppStatus("appStatus");
        processDTO.setRefundOrderInfo(Arrays.asList(commissionCalcOrderInfoDTO1));
        final OrderRiskCorrectDTO orderRiskCorrectDTO = new OrderRiskCorrectDTO();
        processDTO.setRiskCorrectDTOS(Arrays.asList(orderRiskCorrectDTO));
        processDTO.setItemMap(new HashMap<>());
        final CalcCommissionItemResultDTO calcCommissionItemResultDTO = new CalcCommissionItemResultDTO();
        processDTO.setItemResults(Arrays.asList(calcCommissionItemResultDTO));
        processDTO.setCommissionUserId("commissionUserId");
        processDTO.setBusinessType("businessType");

        final OrderCommissionInsuredInfoDTO orderCommissionInsuredInfoDTO = new OrderCommissionInsuredInfoDTO();
        orderCommissionInsuredInfoDTO.setFhOrderId("fhOrderId");
        orderCommissionInsuredInfoDTO.setInsuredId(0);
        orderCommissionInsuredInfoDTO.setCompanyId(0);
        orderCommissionInsuredInfoDTO.setProductId(0);
        orderCommissionInsuredInfoDTO.setCommissionId(0);
        final List<OrderCommissionInsuredInfoDTO> insuredInfoDTOList = Arrays.asList(orderCommissionInsuredInfoDTO);

        // Configure CommissionConfigQueryService.listConfigByProductId(...).
        final SystemCommissionConfig systemCommissionConfig = new SystemCommissionConfig();
        systemCommissionConfig.setType(0);
        systemCommissionConfig.setCompanyId(0);
        systemCommissionConfig.setProductId(0);
        systemCommissionConfig.setStartTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        systemCommissionConfig.setEndTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SystemCommissionConfig> systemCommissionConfigs = Arrays.asList(systemCommissionConfig);
        mockCommissionConfigQueryService.listConfigByProductId(0);

        // Configure CommissionConfigQueryService.listCalcCommissionDetailByConfigId(...).
        final CommissionCalcConfigDetailDTO commissionCalcConfigDetailDTO = new CommissionCalcConfigDetailDTO();
        commissionCalcConfigDetailDTO.setId(0);
        commissionCalcConfigDetailDTO.setConfigId(0);
        commissionCalcConfigDetailDTO.setPeriodNum(0);
        commissionCalcConfigDetailDTO.setPlanId(0);
        commissionCalcConfigDetailDTO.setRiskId(0);
        final List<CommissionCalcConfigDetailDTO> commissionCalcConfigDetailDTOS = Arrays.asList(
                commissionCalcConfigDetailDTO);
        mockCommissionConfigQueryService.listCalcCommissionDetailByConfigId(0);

        mockCommissionExternalService.isDecreaseGroupOrder("productAttrCode", "appStatus");

        // Configure SmOrderCommissionItemMapper.listCommissionItemByOrderId(...).
        final SmCommissionDetailItemPO smCommissionDetailItemPO = new SmCommissionDetailItemPO();
        smCommissionDetailItemPO.setOrderId("orderId");
        smCommissionDetailItemPO.setPolicyNo("policyNo");
        smCommissionDetailItemPO.setInsuredIdNumber("insuredIdNumber");
        smCommissionDetailItemPO.setPolicyStatus("policyStatus");
        smCommissionDetailItemPO.setRiskStatus("riskStatus");
        smCommissionDetailItemPO.setPlanId(0);
        smCommissionDetailItemPO.setRiskId(0);
        smCommissionDetailItemPO.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smCommissionDetailItemPO.setPlanAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setOriginalAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setTermNum(0);
        smCommissionDetailItemPO.setCommissionType(0);
        smCommissionDetailItemPO.setCommissionRate(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setCommissionAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setCommissionUserId("commissionUserId");
        smCommissionDetailItemPO.setBusinessType("businessType");
        smCommissionDetailItemPO.setBusinessId("businessId");
        smCommissionDetailItemPO.setImportFlag(0);
        final List<SmCommissionDetailItemPO> smCommissionDetailItemPOS = Arrays.asList(smCommissionDetailItemPO);
        mockSmOrderCommissionItemMapper.listCommissionItemByOrderId("originalFhOrderId", 0);

        // Configure CommissionExternalService.getOldConvertedConfig(...).
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO2 = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO2.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO2.setCommissionId(0);
        commissionCalcOrderInfoDTO2.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO2.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO2.setAppStatus("appStatus");
        final List<CommissionCalcOrderInfoDTO> orderInfoList = Arrays.asList(commissionCalcOrderInfoDTO2);
        mockCommissionExternalService.getOldConvertedConfig(orderInfoList);

        // Configure CommissionConfigQueryService.getOldCommissionSetting(...).
        final CommissionRateDTO commissionRateDTO1 = new CommissionRateDTO();
        commissionRateDTO1.setPlanId(0);
        commissionRateDTO1.setRiskId(0);
        commissionRateDTO1.setCommissionType(0);
        commissionRateDTO1.setCommissionId(0);
        commissionRateDTO1.setCommissionRate(new BigDecimal("0.00"));
        final List<CommissionRateDTO> commissionRateDTOS = Arrays.asList(commissionRateDTO1);
        mockCommissionConfigQueryService.getOldCommissionSetting(Arrays.asList(0));

        // Configure CommissionCalcService.calc(...).
        final CalcCommissionItemResultDTO calcCommissionItemResultDTO1 = new CalcCommissionItemResultDTO();
        calcCommissionItemResultDTO1.setOrderId("orderId");
        calcCommissionItemResultDTO1.setPolicyNo("policyNo");
        calcCommissionItemResultDTO1.setInsuredIdNumber("insuredIdNumber");
        calcCommissionItemResultDTO1.setPolicyStatus("policyStatus");
        calcCommissionItemResultDTO1.setRiskStatus("riskStatus");
        final List<CalcCommissionItemResultDTO> calcCommissionItemResultDTOS = Arrays.asList(
                calcCommissionItemResultDTO1);
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO3 = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO3.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO3.setCommissionId(0);
        commissionCalcOrderInfoDTO3.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO3.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO3.setAppStatus("appStatus");
        final List<CommissionCalcOrderInfoDTO> orderInfoDTOList = Arrays.asList(commissionCalcOrderInfoDTO3);
        final CommissionCalcConfigInfoDTO configInfoDTO = new CommissionCalcConfigInfoDTO();
        configInfoDTO.setId(0);
        configInfoDTO.setType(0);
        configInfoDTO.setCompanyId(0);
        final CommissionCalcConfigDetailDTO commissionCalcConfigDetailDTO1 = new CommissionCalcConfigDetailDTO();
        commissionCalcConfigDetailDTO1.setPeriodNum(0);
        configInfoDTO.setConfigDetailDTOList(Arrays.asList(commissionCalcConfigDetailDTO1));
        mockCommissionCalcService.calc(0, 0, orderInfoDTOList, configInfoDTO, new CalcErrorDTO("errorMsg","errorMsgNoAlarm"),
                "commissionUserId", "businessType");

        // Configure CommissionCalcService.calcCorrectCommission(...).
        final CalcCommissionItemResultDTO calcCommissionItemResultDTO2 = new CalcCommissionItemResultDTO();
        calcCommissionItemResultDTO2.setOrderId("orderId");
        calcCommissionItemResultDTO2.setPolicyNo("policyNo");
        calcCommissionItemResultDTO2.setInsuredIdNumber("insuredIdNumber");
        calcCommissionItemResultDTO2.setPolicyStatus("policyStatus");
        calcCommissionItemResultDTO2.setRiskStatus("riskStatus");
        final List<CalcCommissionItemResultDTO> calcCommissionItemResultDTOS1 = Arrays.asList(
                calcCommissionItemResultDTO2);
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO4 = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO4.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO4.setCommissionId(0);
        commissionCalcOrderInfoDTO4.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO4.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO4.setAppStatus("appStatus");
        final List<CommissionCalcOrderInfoDTO> orderInfoDTOList1 = Arrays.asList(commissionCalcOrderInfoDTO4);
        final OrderRiskCorrectDTO orderRiskCorrectDTO1 = new OrderRiskCorrectDTO();
        orderRiskCorrectDTO1.setOrderId("orderId");
        orderRiskCorrectDTO1.setPolicyNo("policyNo");
        orderRiskCorrectDTO1.setCorrectId("correctId");
        orderRiskCorrectDTO1.setCorrectType("correctType");
        orderRiskCorrectDTO1.setRiskId(0);
        final List<OrderRiskCorrectDTO> correctDTOList = Arrays.asList(orderRiskCorrectDTO1);
        final SmCommissionDetailItemPO smCommissionDetailItemPO1 = new SmCommissionDetailItemPO();
        smCommissionDetailItemPO1.setOrderId("orderId");
        smCommissionDetailItemPO1.setPolicyNo("policyNo");
        smCommissionDetailItemPO1.setInsuredIdNumber("insuredIdNumber");
        smCommissionDetailItemPO1.setPolicyStatus("policyStatus");
        smCommissionDetailItemPO1.setRiskStatus("riskStatus");
        smCommissionDetailItemPO1.setPlanId(0);
        smCommissionDetailItemPO1.setRiskId(0);
        smCommissionDetailItemPO1.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smCommissionDetailItemPO1.setPlanAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO1.setAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO1.setOriginalAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO1.setTermNum(0);
        smCommissionDetailItemPO1.setCommissionType(0);
        smCommissionDetailItemPO1.setCommissionRate(new BigDecimal("0.00"));
        smCommissionDetailItemPO1.setCommissionAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO1.setCommissionUserId("commissionUserId");
        smCommissionDetailItemPO1.setBusinessType("businessType");
        smCommissionDetailItemPO1.setBusinessId("businessId");
        smCommissionDetailItemPO1.setImportFlag(0);
        final List<SmCommissionDetailItemPO> itemPOs = Arrays.asList(smCommissionDetailItemPO1);
        mockCommissionCalcService.calcCorrectCommission(0, orderInfoDTOList1, correctDTOList, itemPOs,
                new CalcErrorDTO("errorMsg","errorMsgNoAlarm"));

        // Configure CommissionCalcService.calcNewRefundDetailItem(...).
        final CalcCommissionItemResultDTO calcCommissionItemResultDTO3 = new CalcCommissionItemResultDTO();
        calcCommissionItemResultDTO3.setOrderId("orderId");
        calcCommissionItemResultDTO3.setPolicyNo("policyNo");
        calcCommissionItemResultDTO3.setInsuredIdNumber("insuredIdNumber");
        calcCommissionItemResultDTO3.setPolicyStatus("policyStatus");
        calcCommissionItemResultDTO3.setRiskStatus("riskStatus");
        final List<CalcCommissionItemResultDTO> calcCommissionItemResultDTOS2 = Arrays.asList(
                calcCommissionItemResultDTO3);
        final CommissionCalcOrderInfoDTO orderInfo = new CommissionCalcOrderInfoDTO();
        orderInfo.setProductAttrCode("productAttrCode");
        orderInfo.setCommissionId(0);
        orderInfo.setOriginalFhOrderId("originalFhOrderId");
        orderInfo.setOriginalCommissionId(0);
        orderInfo.setAppStatus("appStatus");
        mockCommissionCalcService.calcNewRefundDetailItem(orderInfo, new HashMap<>());

        // Run the test
        commissionCalcProcessServiceUnderTest.calcInsureCommission(processDTO, insuredInfoDTOList, false);

        // Verify the results
        // Confirm CommissionExternalService.getCommissionCalcOrderParam(...).
        final CommissionCalcProcessDTO processDTO1 = new CommissionCalcProcessDTO();
        processDTO1.setOrderId("orderId");
        processDTO1.setProductId(0);
        processDTO1.setProductType("productType");
        processDTO1.setMatchingTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        processDTO1.setTermNum(0);
        processDTO1.setCommissionConfigInfo(new HashMap<>());
        final CommissionRateDTO commissionRateDTO2 = new CommissionRateDTO();
        processDTO1.setOldCommissionConfigInfo(Arrays.asList(commissionRateDTO2));
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO5 = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO5.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO5.setCommissionId(0);
        commissionCalcOrderInfoDTO5.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO5.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO5.setAppStatus("appStatus");
        processDTO1.setOrderInfo(Arrays.asList(commissionCalcOrderInfoDTO5));
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO6 = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO6.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO6.setCommissionId(0);
        commissionCalcOrderInfoDTO6.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO6.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO6.setAppStatus("appStatus");
        processDTO1.setRefundOrderInfo(Arrays.asList(commissionCalcOrderInfoDTO6));
        final OrderRiskCorrectDTO orderRiskCorrectDTO2 = new OrderRiskCorrectDTO();
        processDTO1.setRiskCorrectDTOS(Arrays.asList(orderRiskCorrectDTO2));
        processDTO1.setItemMap(new HashMap<>());
        final CalcCommissionItemResultDTO calcCommissionItemResultDTO4 = new CalcCommissionItemResultDTO();
        processDTO1.setItemResults(Arrays.asList(calcCommissionItemResultDTO4));
        processDTO1.setCommissionUserId("commissionUserId");
        processDTO1.setBusinessType("businessType");
        final OrderCommissionInsuredInfoDTO orderCommissionInsuredInfoDTO1 = new OrderCommissionInsuredInfoDTO();
        orderCommissionInsuredInfoDTO1.setFhOrderId("fhOrderId");
        orderCommissionInsuredInfoDTO1.setInsuredId(0);
        orderCommissionInsuredInfoDTO1.setCompanyId(0);
        orderCommissionInsuredInfoDTO1.setProductId(0);
        orderCommissionInsuredInfoDTO1.setCommissionId(0);
        final List<OrderCommissionInsuredInfoDTO> insuredInfoDTOList1 = Arrays.asList(orderCommissionInsuredInfoDTO1);
        mockCommissionExternalService.getCommissionCalcOrderParam(processDTO1, insuredInfoDTOList1);

        // Confirm SmOrderCommissionItemMapper.batchInsertCommissionItem(...).
        final SmCommissionDetailItemPO smCommissionDetailItemPO2 = new SmCommissionDetailItemPO();
        smCommissionDetailItemPO2.setOrderId("orderId");
        smCommissionDetailItemPO2.setPolicyNo("policyNo");
        smCommissionDetailItemPO2.setInsuredIdNumber("insuredIdNumber");
        smCommissionDetailItemPO2.setPolicyStatus("policyStatus");
        smCommissionDetailItemPO2.setRiskStatus("riskStatus");
        smCommissionDetailItemPO2.setPlanId(0);
        smCommissionDetailItemPO2.setRiskId(0);
        smCommissionDetailItemPO2.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smCommissionDetailItemPO2.setPlanAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO2.setAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO2.setOriginalAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO2.setTermNum(0);
        smCommissionDetailItemPO2.setCommissionType(0);
        smCommissionDetailItemPO2.setCommissionRate(new BigDecimal("0.00"));
        smCommissionDetailItemPO2.setCommissionAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO2.setCommissionUserId("commissionUserId");
        smCommissionDetailItemPO2.setBusinessType("businessType");
        smCommissionDetailItemPO2.setBusinessId("businessId");
        smCommissionDetailItemPO2.setImportFlag(0);
        final List<SmCommissionDetailItemPO> commissionConfigItemList = Arrays.asList(smCommissionDetailItemPO2);
        mockSmOrderCommissionItemMapper.batchInsertCommissionItem(commissionConfigItemList);

        // Confirm SmOrderCommissionItemMapper.batchInsertCommissionItemExceptImport(...).
        final SmCommissionDetailItemPO smCommissionDetailItemPO3 = new SmCommissionDetailItemPO();
        smCommissionDetailItemPO3.setOrderId("orderId");
        smCommissionDetailItemPO3.setPolicyNo("policyNo");
        smCommissionDetailItemPO3.setInsuredIdNumber("insuredIdNumber");
        smCommissionDetailItemPO3.setPolicyStatus("policyStatus");
        smCommissionDetailItemPO3.setRiskStatus("riskStatus");
        smCommissionDetailItemPO3.setPlanId(0);
        smCommissionDetailItemPO3.setRiskId(0);
        smCommissionDetailItemPO3.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smCommissionDetailItemPO3.setPlanAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO3.setAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO3.setOriginalAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO3.setTermNum(0);
        smCommissionDetailItemPO3.setCommissionType(0);
        smCommissionDetailItemPO3.setCommissionRate(new BigDecimal("0.00"));
        smCommissionDetailItemPO3.setCommissionAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO3.setCommissionUserId("commissionUserId");
        smCommissionDetailItemPO3.setBusinessType("businessType");
        smCommissionDetailItemPO3.setBusinessId("businessId");
        smCommissionDetailItemPO3.setImportFlag(0);
        final List<SmCommissionDetailItemPO> commissionConfigItemList1 = Arrays.asList(smCommissionDetailItemPO3);
        mockSmOrderCommissionItemMapper.batchInsertCommissionItemExceptImport(commissionConfigItemList1);

        // Confirm SmOrderCommissionDetailMapper.batchReCalcCommissionDetail(...).
        final SmCommissionDetailPO smCommissionDetailPO = new SmCommissionDetailPO();
        smCommissionDetailPO.setOrderId("orderId");
        smCommissionDetailPO.setPolicyNo("policyNo");
        smCommissionDetailPO.setInsuredIdNumber("insuredIdNumber");
        smCommissionDetailPO.setPolicyStatus("policyStatus");
        smCommissionDetailPO.setPlanId(0);
        smCommissionDetailPO.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smCommissionDetailPO.setAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setOriginalAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setPaymentRate(new BigDecimal("0.00"));
        smCommissionDetailPO.setPaymentAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setSettlementRate(new BigDecimal("0.00"));
        smCommissionDetailPO.setSettlementAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setConversionRate(new BigDecimal("0.00"));
        smCommissionDetailPO.setConversionAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setTermNum(0);
        smCommissionDetailPO.setPaymentRiskJson("paymentRiskJson");
        smCommissionDetailPO.setSettlementRiskJson("settlementRiskJson");
        smCommissionDetailPO.setConversionRiskJson("conversionRiskJson");
        smCommissionDetailPO.setPAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setSAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setCAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setCommissionUserId("commissionUserId");
        smCommissionDetailPO.setBusinessType("businessType");
        smCommissionDetailPO.setBusinessId("businessId");
        smCommissionDetailPO.setImportFlag(0);
        final List<SmCommissionDetailPO> commissionConfigDetailList = Arrays.asList(smCommissionDetailPO);
        mockSmOrderCommissionDetailMapper.batchReCalcCommissionDetail(commissionConfigDetailList);

        // Confirm SmOrderCommissionDetailMapper.batchInsertCommissionDetail(...).
        final SmCommissionDetailPO smCommissionDetailPO1 = new SmCommissionDetailPO();
        smCommissionDetailPO1.setOrderId("orderId");
        smCommissionDetailPO1.setPolicyNo("policyNo");
        smCommissionDetailPO1.setInsuredIdNumber("insuredIdNumber");
        smCommissionDetailPO1.setPolicyStatus("policyStatus");
        smCommissionDetailPO1.setPlanId(0);
        smCommissionDetailPO1.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smCommissionDetailPO1.setAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setOriginalAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setPaymentRate(new BigDecimal("0.00"));
        smCommissionDetailPO1.setPaymentAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setSettlementRate(new BigDecimal("0.00"));
        smCommissionDetailPO1.setSettlementAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setConversionRate(new BigDecimal("0.00"));
        smCommissionDetailPO1.setConversionAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setTermNum(0);
        smCommissionDetailPO1.setPaymentRiskJson("paymentRiskJson");
        smCommissionDetailPO1.setSettlementRiskJson("settlementRiskJson");
        smCommissionDetailPO1.setConversionRiskJson("conversionRiskJson");
        smCommissionDetailPO1.setPAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setSAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setCAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setCommissionUserId("commissionUserId");
        smCommissionDetailPO1.setBusinessType("businessType");
        smCommissionDetailPO1.setBusinessId("businessId");
        smCommissionDetailPO1.setImportFlag(0);
        final List<SmCommissionDetailPO> commissionConfigDetailList1 = Arrays.asList(smCommissionDetailPO1);
        mockSmOrderCommissionDetailMapper.batchInsertCommissionDetail(commissionConfigDetailList1);
    }

    @Test
    public void testCalcInsureCommission_CommissionConfigQueryServiceGetOldCommissionSettingReturnsNoItems() throws Exception {
        // Setup
        final CommissionCalcProcessDTO processDTO = new CommissionCalcProcessDTO();
        processDTO.setOrderId("orderId");
        processDTO.setProductId(0);
        processDTO.setProductType("productType");
        processDTO.setMatchingTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        processDTO.setTermNum(0);
        processDTO.setCommissionConfigInfo(new HashMap<>());
        final CommissionRateDTO commissionRateDTO = new CommissionRateDTO();
        processDTO.setOldCommissionConfigInfo(Arrays.asList(commissionRateDTO));
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO.setCommissionId(0);
        commissionCalcOrderInfoDTO.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO.setAppStatus("appStatus");
        processDTO.setOrderInfo(Arrays.asList(commissionCalcOrderInfoDTO));
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO1 = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO1.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO1.setCommissionId(0);
        commissionCalcOrderInfoDTO1.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO1.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO1.setAppStatus("appStatus");
        processDTO.setRefundOrderInfo(Arrays.asList(commissionCalcOrderInfoDTO1));
        final OrderRiskCorrectDTO orderRiskCorrectDTO = new OrderRiskCorrectDTO();
        processDTO.setRiskCorrectDTOS(Arrays.asList(orderRiskCorrectDTO));
        processDTO.setItemMap(new HashMap<>());
        final CalcCommissionItemResultDTO calcCommissionItemResultDTO = new CalcCommissionItemResultDTO();
        processDTO.setItemResults(Arrays.asList(calcCommissionItemResultDTO));
        processDTO.setCommissionUserId("commissionUserId");
        processDTO.setBusinessType("businessType");

        final OrderCommissionInsuredInfoDTO orderCommissionInsuredInfoDTO = new OrderCommissionInsuredInfoDTO();
        orderCommissionInsuredInfoDTO.setFhOrderId("fhOrderId");
        orderCommissionInsuredInfoDTO.setInsuredId(0);
        orderCommissionInsuredInfoDTO.setCompanyId(0);
        orderCommissionInsuredInfoDTO.setProductId(0);
        orderCommissionInsuredInfoDTO.setCommissionId(0);
        final List<OrderCommissionInsuredInfoDTO> insuredInfoDTOList = Arrays.asList(orderCommissionInsuredInfoDTO);

        // Configure CommissionConfigQueryService.listConfigByProductId(...).
        final SystemCommissionConfig systemCommissionConfig = new SystemCommissionConfig();
        systemCommissionConfig.setType(0);
        systemCommissionConfig.setCompanyId(0);
        systemCommissionConfig.setProductId(0);
        systemCommissionConfig.setStartTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        systemCommissionConfig.setEndTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SystemCommissionConfig> systemCommissionConfigs = Arrays.asList(systemCommissionConfig);
        mockCommissionConfigQueryService.listConfigByProductId(0);

        // Configure CommissionConfigQueryService.listCalcCommissionDetailByConfigId(...).
        final CommissionCalcConfigDetailDTO commissionCalcConfigDetailDTO = new CommissionCalcConfigDetailDTO();
        commissionCalcConfigDetailDTO.setId(0);
        commissionCalcConfigDetailDTO.setConfigId(0);
        commissionCalcConfigDetailDTO.setPeriodNum(0);
        commissionCalcConfigDetailDTO.setPlanId(0);
        commissionCalcConfigDetailDTO.setRiskId(0);
        final List<CommissionCalcConfigDetailDTO> commissionCalcConfigDetailDTOS = Arrays.asList(
                commissionCalcConfigDetailDTO);
        mockCommissionConfigQueryService.listCalcCommissionDetailByConfigId(0);
        mockCommissionExternalService.isDecreaseGroupOrder("productAttrCode", "appStatus");

        // Configure SmOrderCommissionItemMapper.listCommissionItemByOrderId(...).
        final SmCommissionDetailItemPO smCommissionDetailItemPO = new SmCommissionDetailItemPO();
        smCommissionDetailItemPO.setOrderId("orderId");
        smCommissionDetailItemPO.setPolicyNo("policyNo");
        smCommissionDetailItemPO.setInsuredIdNumber("insuredIdNumber");
        smCommissionDetailItemPO.setPolicyStatus("policyStatus");
        smCommissionDetailItemPO.setRiskStatus("riskStatus");
        smCommissionDetailItemPO.setPlanId(0);
        smCommissionDetailItemPO.setRiskId(0);
        smCommissionDetailItemPO.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smCommissionDetailItemPO.setPlanAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setOriginalAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setTermNum(0);
        smCommissionDetailItemPO.setCommissionType(0);
        smCommissionDetailItemPO.setCommissionRate(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setCommissionAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setCommissionUserId("commissionUserId");
        smCommissionDetailItemPO.setBusinessType("businessType");
        smCommissionDetailItemPO.setBusinessId("businessId");
        smCommissionDetailItemPO.setImportFlag(0);
        final List<SmCommissionDetailItemPO> smCommissionDetailItemPOS = Arrays.asList(smCommissionDetailItemPO);
        mockSmOrderCommissionItemMapper.listCommissionItemByOrderId("originalFhOrderId", 0);

        // Configure CommissionExternalService.getOldConvertedConfig(...).
        final CommissionRateDTO commissionRateDTO1 = new CommissionRateDTO();
        commissionRateDTO1.setPlanId(0);
        commissionRateDTO1.setRiskId(0);
        commissionRateDTO1.setCommissionType(0);
        commissionRateDTO1.setCommissionId(0);
        commissionRateDTO1.setCommissionRate(new BigDecimal("0.00"));
        final List<CommissionRateDTO> commissionRateDTOS = Arrays.asList(commissionRateDTO1);
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO2 = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO2.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO2.setCommissionId(0);
        commissionCalcOrderInfoDTO2.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO2.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO2.setAppStatus("appStatus");
        final List<CommissionCalcOrderInfoDTO> orderInfoList = Arrays.asList(commissionCalcOrderInfoDTO2);
        mockCommissionExternalService.getOldConvertedConfig(orderInfoList);

        mockCommissionConfigQueryService.getOldCommissionSetting(Arrays.asList(0));

        // Configure CommissionCalcService.calc(...).
        final CalcCommissionItemResultDTO calcCommissionItemResultDTO1 = new CalcCommissionItemResultDTO();
        calcCommissionItemResultDTO1.setOrderId("orderId");
        calcCommissionItemResultDTO1.setPolicyNo("policyNo");
        calcCommissionItemResultDTO1.setInsuredIdNumber("insuredIdNumber");
        calcCommissionItemResultDTO1.setPolicyStatus("policyStatus");
        calcCommissionItemResultDTO1.setRiskStatus("riskStatus");
        final List<CalcCommissionItemResultDTO> calcCommissionItemResultDTOS = Arrays.asList(
                calcCommissionItemResultDTO1);
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO3 = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO3.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO3.setCommissionId(0);
        commissionCalcOrderInfoDTO3.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO3.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO3.setAppStatus("appStatus");
        final List<CommissionCalcOrderInfoDTO> orderInfoDTOList = Arrays.asList(commissionCalcOrderInfoDTO3);
        final CommissionCalcConfigInfoDTO configInfoDTO = new CommissionCalcConfigInfoDTO();
        configInfoDTO.setId(0);
        configInfoDTO.setType(0);
        configInfoDTO.setCompanyId(0);
        final CommissionCalcConfigDetailDTO commissionCalcConfigDetailDTO1 = new CommissionCalcConfigDetailDTO();
        commissionCalcConfigDetailDTO1.setPeriodNum(0);
        configInfoDTO.setConfigDetailDTOList(Arrays.asList(commissionCalcConfigDetailDTO1));
        mockCommissionCalcService.calc(0, 0, orderInfoDTOList, configInfoDTO, new CalcErrorDTO("errorMsg","errorMsgNoAlarm"),
                "commissionUserId", "businessType");

        // Configure CommissionCalcService.calcCorrectCommission(...).
        final CalcCommissionItemResultDTO calcCommissionItemResultDTO2 = new CalcCommissionItemResultDTO();
        calcCommissionItemResultDTO2.setOrderId("orderId");
        calcCommissionItemResultDTO2.setPolicyNo("policyNo");
        calcCommissionItemResultDTO2.setInsuredIdNumber("insuredIdNumber");
        calcCommissionItemResultDTO2.setPolicyStatus("policyStatus");
        calcCommissionItemResultDTO2.setRiskStatus("riskStatus");
        final List<CalcCommissionItemResultDTO> calcCommissionItemResultDTOS1 = Arrays.asList(
                calcCommissionItemResultDTO2);
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO4 = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO4.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO4.setCommissionId(0);
        commissionCalcOrderInfoDTO4.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO4.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO4.setAppStatus("appStatus");
        final List<CommissionCalcOrderInfoDTO> orderInfoDTOList1 = Arrays.asList(commissionCalcOrderInfoDTO4);
        final OrderRiskCorrectDTO orderRiskCorrectDTO1 = new OrderRiskCorrectDTO();
        orderRiskCorrectDTO1.setOrderId("orderId");
        orderRiskCorrectDTO1.setPolicyNo("policyNo");
        orderRiskCorrectDTO1.setCorrectId("correctId");
        orderRiskCorrectDTO1.setCorrectType("correctType");
        orderRiskCorrectDTO1.setRiskId(0);
        final List<OrderRiskCorrectDTO> correctDTOList = Arrays.asList(orderRiskCorrectDTO1);
        final SmCommissionDetailItemPO smCommissionDetailItemPO1 = new SmCommissionDetailItemPO();
        smCommissionDetailItemPO1.setOrderId("orderId");
        smCommissionDetailItemPO1.setPolicyNo("policyNo");
        smCommissionDetailItemPO1.setInsuredIdNumber("insuredIdNumber");
        smCommissionDetailItemPO1.setPolicyStatus("policyStatus");
        smCommissionDetailItemPO1.setRiskStatus("riskStatus");
        smCommissionDetailItemPO1.setPlanId(0);
        smCommissionDetailItemPO1.setRiskId(0);
        smCommissionDetailItemPO1.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smCommissionDetailItemPO1.setPlanAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO1.setAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO1.setOriginalAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO1.setTermNum(0);
        smCommissionDetailItemPO1.setCommissionType(0);
        smCommissionDetailItemPO1.setCommissionRate(new BigDecimal("0.00"));
        smCommissionDetailItemPO1.setCommissionAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO1.setCommissionUserId("commissionUserId");
        smCommissionDetailItemPO1.setBusinessType("businessType");
        smCommissionDetailItemPO1.setBusinessId("businessId");
        smCommissionDetailItemPO1.setImportFlag(0);
        final List<SmCommissionDetailItemPO> itemPOs = Arrays.asList(smCommissionDetailItemPO1);
        mockCommissionCalcService.calcCorrectCommission(0, orderInfoDTOList1, correctDTOList, itemPOs,
                new CalcErrorDTO("errorMsg","errorMsgNoAlarm"));

        // Configure CommissionCalcService.calcNewRefundDetailItem(...).
        final CalcCommissionItemResultDTO calcCommissionItemResultDTO3 = new CalcCommissionItemResultDTO();
        calcCommissionItemResultDTO3.setOrderId("orderId");
        calcCommissionItemResultDTO3.setPolicyNo("policyNo");
        calcCommissionItemResultDTO3.setInsuredIdNumber("insuredIdNumber");
        calcCommissionItemResultDTO3.setPolicyStatus("policyStatus");
        calcCommissionItemResultDTO3.setRiskStatus("riskStatus");
        final List<CalcCommissionItemResultDTO> calcCommissionItemResultDTOS2 = Arrays.asList(
                calcCommissionItemResultDTO3);
        final CommissionCalcOrderInfoDTO orderInfo = new CommissionCalcOrderInfoDTO();
        orderInfo.setProductAttrCode("productAttrCode");
        orderInfo.setCommissionId(0);
        orderInfo.setOriginalFhOrderId("originalFhOrderId");
        orderInfo.setOriginalCommissionId(0);
        orderInfo.setAppStatus("appStatus");
        mockCommissionCalcService.calcNewRefundDetailItem(orderInfo, new HashMap<>());

        // Run the test
        commissionCalcProcessServiceUnderTest.calcInsureCommission(processDTO, insuredInfoDTOList, false);

        // Verify the results
        // Confirm CommissionExternalService.getCommissionCalcOrderParam(...).
        final CommissionCalcProcessDTO processDTO1 = new CommissionCalcProcessDTO();
        processDTO1.setOrderId("orderId");
        processDTO1.setProductId(0);
        processDTO1.setProductType("productType");
        processDTO1.setMatchingTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        processDTO1.setTermNum(0);
        processDTO1.setCommissionConfigInfo(new HashMap<>());
        final CommissionRateDTO commissionRateDTO2 = new CommissionRateDTO();
        processDTO1.setOldCommissionConfigInfo(Arrays.asList(commissionRateDTO2));
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO5 = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO5.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO5.setCommissionId(0);
        commissionCalcOrderInfoDTO5.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO5.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO5.setAppStatus("appStatus");
        processDTO1.setOrderInfo(Arrays.asList(commissionCalcOrderInfoDTO5));
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO6 = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO6.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO6.setCommissionId(0);
        commissionCalcOrderInfoDTO6.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO6.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO6.setAppStatus("appStatus");
        processDTO1.setRefundOrderInfo(Arrays.asList(commissionCalcOrderInfoDTO6));
        final OrderRiskCorrectDTO orderRiskCorrectDTO2 = new OrderRiskCorrectDTO();
        processDTO1.setRiskCorrectDTOS(Arrays.asList(orderRiskCorrectDTO2));
        processDTO1.setItemMap(new HashMap<>());
        final CalcCommissionItemResultDTO calcCommissionItemResultDTO4 = new CalcCommissionItemResultDTO();
        processDTO1.setItemResults(Arrays.asList(calcCommissionItemResultDTO4));
        processDTO1.setCommissionUserId("commissionUserId");
        processDTO1.setBusinessType("businessType");
        final OrderCommissionInsuredInfoDTO orderCommissionInsuredInfoDTO1 = new OrderCommissionInsuredInfoDTO();
        orderCommissionInsuredInfoDTO1.setFhOrderId("fhOrderId");
        orderCommissionInsuredInfoDTO1.setInsuredId(0);
        orderCommissionInsuredInfoDTO1.setCompanyId(0);
        orderCommissionInsuredInfoDTO1.setProductId(0);
        orderCommissionInsuredInfoDTO1.setCommissionId(0);
        final List<OrderCommissionInsuredInfoDTO> insuredInfoDTOList1 = Arrays.asList(orderCommissionInsuredInfoDTO1);
        mockCommissionExternalService.getCommissionCalcOrderParam(processDTO1, insuredInfoDTOList1);

        // Confirm SmOrderCommissionItemMapper.batchInsertCommissionItem(...).
        final SmCommissionDetailItemPO smCommissionDetailItemPO2 = new SmCommissionDetailItemPO();
        smCommissionDetailItemPO2.setOrderId("orderId");
        smCommissionDetailItemPO2.setPolicyNo("policyNo");
        smCommissionDetailItemPO2.setInsuredIdNumber("insuredIdNumber");
        smCommissionDetailItemPO2.setPolicyStatus("policyStatus");
        smCommissionDetailItemPO2.setRiskStatus("riskStatus");
        smCommissionDetailItemPO2.setPlanId(0);
        smCommissionDetailItemPO2.setRiskId(0);
        smCommissionDetailItemPO2.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smCommissionDetailItemPO2.setPlanAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO2.setAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO2.setOriginalAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO2.setTermNum(0);
        smCommissionDetailItemPO2.setCommissionType(0);
        smCommissionDetailItemPO2.setCommissionRate(new BigDecimal("0.00"));
        smCommissionDetailItemPO2.setCommissionAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO2.setCommissionUserId("commissionUserId");
        smCommissionDetailItemPO2.setBusinessType("businessType");
        smCommissionDetailItemPO2.setBusinessId("businessId");
        smCommissionDetailItemPO2.setImportFlag(0);
        final List<SmCommissionDetailItemPO> commissionConfigItemList = Arrays.asList(smCommissionDetailItemPO2);
        mockSmOrderCommissionItemMapper.batchInsertCommissionItem(commissionConfigItemList);

        // Confirm SmOrderCommissionItemMapper.batchInsertCommissionItemExceptImport(...).
        final SmCommissionDetailItemPO smCommissionDetailItemPO3 = new SmCommissionDetailItemPO();
        smCommissionDetailItemPO3.setOrderId("orderId");
        smCommissionDetailItemPO3.setPolicyNo("policyNo");
        smCommissionDetailItemPO3.setInsuredIdNumber("insuredIdNumber");
        smCommissionDetailItemPO3.setPolicyStatus("policyStatus");
        smCommissionDetailItemPO3.setRiskStatus("riskStatus");
        smCommissionDetailItemPO3.setPlanId(0);
        smCommissionDetailItemPO3.setRiskId(0);
        smCommissionDetailItemPO3.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smCommissionDetailItemPO3.setPlanAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO3.setAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO3.setOriginalAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO3.setTermNum(0);
        smCommissionDetailItemPO3.setCommissionType(0);
        smCommissionDetailItemPO3.setCommissionRate(new BigDecimal("0.00"));
        smCommissionDetailItemPO3.setCommissionAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO3.setCommissionUserId("commissionUserId");
        smCommissionDetailItemPO3.setBusinessType("businessType");
        smCommissionDetailItemPO3.setBusinessId("businessId");
        smCommissionDetailItemPO3.setImportFlag(0);
        final List<SmCommissionDetailItemPO> commissionConfigItemList1 = Arrays.asList(smCommissionDetailItemPO3);
        mockSmOrderCommissionItemMapper.batchInsertCommissionItemExceptImport(commissionConfigItemList1);

        // Confirm SmOrderCommissionDetailMapper.batchReCalcCommissionDetail(...).
        final SmCommissionDetailPO smCommissionDetailPO = new SmCommissionDetailPO();
        smCommissionDetailPO.setOrderId("orderId");
        smCommissionDetailPO.setPolicyNo("policyNo");
        smCommissionDetailPO.setInsuredIdNumber("insuredIdNumber");
        smCommissionDetailPO.setPolicyStatus("policyStatus");
        smCommissionDetailPO.setPlanId(0);
        smCommissionDetailPO.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smCommissionDetailPO.setAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setOriginalAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setPaymentRate(new BigDecimal("0.00"));
        smCommissionDetailPO.setPaymentAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setSettlementRate(new BigDecimal("0.00"));
        smCommissionDetailPO.setSettlementAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setConversionRate(new BigDecimal("0.00"));
        smCommissionDetailPO.setConversionAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setTermNum(0);
        smCommissionDetailPO.setPaymentRiskJson("paymentRiskJson");
        smCommissionDetailPO.setSettlementRiskJson("settlementRiskJson");
        smCommissionDetailPO.setConversionRiskJson("conversionRiskJson");
        smCommissionDetailPO.setPAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setSAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setCAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setCommissionUserId("commissionUserId");
        smCommissionDetailPO.setBusinessType("businessType");
        smCommissionDetailPO.setBusinessId("businessId");
        smCommissionDetailPO.setImportFlag(0);
        final List<SmCommissionDetailPO> commissionConfigDetailList = Arrays.asList(smCommissionDetailPO);
        mockSmOrderCommissionDetailMapper.batchReCalcCommissionDetail(commissionConfigDetailList);

        // Confirm SmOrderCommissionDetailMapper.batchInsertCommissionDetail(...).
        final SmCommissionDetailPO smCommissionDetailPO1 = new SmCommissionDetailPO();
        smCommissionDetailPO1.setOrderId("orderId");
        smCommissionDetailPO1.setPolicyNo("policyNo");
        smCommissionDetailPO1.setInsuredIdNumber("insuredIdNumber");
        smCommissionDetailPO1.setPolicyStatus("policyStatus");
        smCommissionDetailPO1.setPlanId(0);
        smCommissionDetailPO1.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smCommissionDetailPO1.setAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setOriginalAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setPaymentRate(new BigDecimal("0.00"));
        smCommissionDetailPO1.setPaymentAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setSettlementRate(new BigDecimal("0.00"));
        smCommissionDetailPO1.setSettlementAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setConversionRate(new BigDecimal("0.00"));
        smCommissionDetailPO1.setConversionAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setTermNum(0);
        smCommissionDetailPO1.setPaymentRiskJson("paymentRiskJson");
        smCommissionDetailPO1.setSettlementRiskJson("settlementRiskJson");
        smCommissionDetailPO1.setConversionRiskJson("conversionRiskJson");
        smCommissionDetailPO1.setPAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setSAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setCAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setCommissionUserId("commissionUserId");
        smCommissionDetailPO1.setBusinessType("businessType");
        smCommissionDetailPO1.setBusinessId("businessId");
        smCommissionDetailPO1.setImportFlag(0);
        final List<SmCommissionDetailPO> commissionConfigDetailList1 = Arrays.asList(smCommissionDetailPO1);
        mockSmOrderCommissionDetailMapper.batchInsertCommissionDetail(commissionConfigDetailList1);
    }

    @Test
    public void testCalcInsureCommission_CommissionCalcServiceCalcReturnsNoItems() throws Exception {
        // Setup
        final CommissionCalcProcessDTO processDTO = new CommissionCalcProcessDTO();
        processDTO.setOrderId("orderId");
        processDTO.setProductId(0);
        processDTO.setProductType("productType");
        processDTO.setMatchingTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        processDTO.setTermNum(0);
        processDTO.setCommissionConfigInfo(new HashMap<>());
        final CommissionRateDTO commissionRateDTO = new CommissionRateDTO();
        processDTO.setOldCommissionConfigInfo(Arrays.asList(commissionRateDTO));
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO.setCommissionId(0);
        commissionCalcOrderInfoDTO.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO.setAppStatus("appStatus");
        processDTO.setOrderInfo(Arrays.asList(commissionCalcOrderInfoDTO));
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO1 = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO1.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO1.setCommissionId(0);
        commissionCalcOrderInfoDTO1.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO1.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO1.setAppStatus("appStatus");
        processDTO.setRefundOrderInfo(Arrays.asList(commissionCalcOrderInfoDTO1));
        final OrderRiskCorrectDTO orderRiskCorrectDTO = new OrderRiskCorrectDTO();
        processDTO.setRiskCorrectDTOS(Arrays.asList(orderRiskCorrectDTO));
        processDTO.setItemMap(new HashMap<>());
        final CalcCommissionItemResultDTO calcCommissionItemResultDTO = new CalcCommissionItemResultDTO();
        processDTO.setItemResults(Arrays.asList(calcCommissionItemResultDTO));
        processDTO.setCommissionUserId("commissionUserId");
        processDTO.setBusinessType("businessType");

        final OrderCommissionInsuredInfoDTO orderCommissionInsuredInfoDTO = new OrderCommissionInsuredInfoDTO();
        orderCommissionInsuredInfoDTO.setFhOrderId("fhOrderId");
        orderCommissionInsuredInfoDTO.setInsuredId(0);
        orderCommissionInsuredInfoDTO.setCompanyId(0);
        orderCommissionInsuredInfoDTO.setProductId(0);
        orderCommissionInsuredInfoDTO.setCommissionId(0);
        final List<OrderCommissionInsuredInfoDTO> insuredInfoDTOList = Arrays.asList(orderCommissionInsuredInfoDTO);

        // Configure CommissionConfigQueryService.listConfigByProductId(...).
        final SystemCommissionConfig systemCommissionConfig = new SystemCommissionConfig();
        systemCommissionConfig.setType(0);
        systemCommissionConfig.setCompanyId(0);
        systemCommissionConfig.setProductId(0);
        systemCommissionConfig.setStartTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        systemCommissionConfig.setEndTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SystemCommissionConfig> systemCommissionConfigs = Arrays.asList(systemCommissionConfig);
        mockCommissionConfigQueryService.listConfigByProductId(0);

        // Configure CommissionConfigQueryService.listCalcCommissionDetailByConfigId(...).
        final CommissionCalcConfigDetailDTO commissionCalcConfigDetailDTO = new CommissionCalcConfigDetailDTO();
        commissionCalcConfigDetailDTO.setId(0);
        commissionCalcConfigDetailDTO.setConfigId(0);
        commissionCalcConfigDetailDTO.setPeriodNum(0);
        commissionCalcConfigDetailDTO.setPlanId(0);
        commissionCalcConfigDetailDTO.setRiskId(0);
        final List<CommissionCalcConfigDetailDTO> commissionCalcConfigDetailDTOS = Arrays.asList(
                commissionCalcConfigDetailDTO);
        mockCommissionConfigQueryService.listCalcCommissionDetailByConfigId(0);

        mockCommissionExternalService.isDecreaseGroupOrder("productAttrCode", "appStatus");

        // Configure SmOrderCommissionItemMapper.listCommissionItemByOrderId(...).
        final SmCommissionDetailItemPO smCommissionDetailItemPO = new SmCommissionDetailItemPO();
        smCommissionDetailItemPO.setOrderId("orderId");
        smCommissionDetailItemPO.setPolicyNo("policyNo");
        smCommissionDetailItemPO.setInsuredIdNumber("insuredIdNumber");
        smCommissionDetailItemPO.setPolicyStatus("policyStatus");
        smCommissionDetailItemPO.setRiskStatus("riskStatus");
        smCommissionDetailItemPO.setPlanId(0);
        smCommissionDetailItemPO.setRiskId(0);
        smCommissionDetailItemPO.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smCommissionDetailItemPO.setPlanAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setOriginalAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setTermNum(0);
        smCommissionDetailItemPO.setCommissionType(0);
        smCommissionDetailItemPO.setCommissionRate(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setCommissionAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setCommissionUserId("commissionUserId");
        smCommissionDetailItemPO.setBusinessType("businessType");
        smCommissionDetailItemPO.setBusinessId("businessId");
        smCommissionDetailItemPO.setImportFlag(0);
        final List<SmCommissionDetailItemPO> smCommissionDetailItemPOS = Arrays.asList(smCommissionDetailItemPO);
        mockSmOrderCommissionItemMapper.listCommissionItemByOrderId("originalFhOrderId", 0);

        // Configure CommissionExternalService.getOldConvertedConfig(...).
        final CommissionRateDTO commissionRateDTO1 = new CommissionRateDTO();
        commissionRateDTO1.setPlanId(0);
        commissionRateDTO1.setRiskId(0);
        commissionRateDTO1.setCommissionType(0);
        commissionRateDTO1.setCommissionId(0);
        commissionRateDTO1.setCommissionRate(new BigDecimal("0.00"));
        final List<CommissionRateDTO> commissionRateDTOS = Arrays.asList(commissionRateDTO1);
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO2 = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO2.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO2.setCommissionId(0);
        commissionCalcOrderInfoDTO2.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO2.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO2.setAppStatus("appStatus");
        final List<CommissionCalcOrderInfoDTO> orderInfoList = Arrays.asList(commissionCalcOrderInfoDTO2);
        mockCommissionExternalService.getOldConvertedConfig(orderInfoList);

        // Configure CommissionConfigQueryService.getOldCommissionSetting(...).
        final CommissionRateDTO commissionRateDTO2 = new CommissionRateDTO();
        commissionRateDTO2.setPlanId(0);
        commissionRateDTO2.setRiskId(0);
        commissionRateDTO2.setCommissionType(0);
        commissionRateDTO2.setCommissionId(0);
        commissionRateDTO2.setCommissionRate(new BigDecimal("0.00"));
        final List<CommissionRateDTO> commissionRateDTOS1 = Arrays.asList(commissionRateDTO2);
        mockCommissionConfigQueryService.getOldCommissionSetting(Arrays.asList(0));

        // Configure CommissionCalcService.calc(...).
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO3 = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO3.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO3.setCommissionId(0);
        commissionCalcOrderInfoDTO3.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO3.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO3.setAppStatus("appStatus");
        final List<CommissionCalcOrderInfoDTO> orderInfoDTOList = Arrays.asList(commissionCalcOrderInfoDTO3);
        final CommissionCalcConfigInfoDTO configInfoDTO = new CommissionCalcConfigInfoDTO();
        configInfoDTO.setId(0);
        configInfoDTO.setType(0);
        configInfoDTO.setCompanyId(0);
        final CommissionCalcConfigDetailDTO commissionCalcConfigDetailDTO1 = new CommissionCalcConfigDetailDTO();
        commissionCalcConfigDetailDTO1.setPeriodNum(0);
        configInfoDTO.setConfigDetailDTOList(Arrays.asList(commissionCalcConfigDetailDTO1));
        mockCommissionCalcService.calc(0, 0, orderInfoDTOList, configInfoDTO, new CalcErrorDTO("errorMsg","errorMsgNoAlarm"),
                "commissionUserId", "businessType");

        // Configure CommissionCalcService.calcCorrectCommission(...).
        final CalcCommissionItemResultDTO calcCommissionItemResultDTO1 = new CalcCommissionItemResultDTO();
        calcCommissionItemResultDTO1.setOrderId("orderId");
        calcCommissionItemResultDTO1.setPolicyNo("policyNo");
        calcCommissionItemResultDTO1.setInsuredIdNumber("insuredIdNumber");
        calcCommissionItemResultDTO1.setPolicyStatus("policyStatus");
        calcCommissionItemResultDTO1.setRiskStatus("riskStatus");
        final List<CalcCommissionItemResultDTO> calcCommissionItemResultDTOS = Arrays.asList(
                calcCommissionItemResultDTO1);
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO4 = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO4.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO4.setCommissionId(0);
        commissionCalcOrderInfoDTO4.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO4.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO4.setAppStatus("appStatus");
        final List<CommissionCalcOrderInfoDTO> orderInfoDTOList1 = Arrays.asList(commissionCalcOrderInfoDTO4);
        final OrderRiskCorrectDTO orderRiskCorrectDTO1 = new OrderRiskCorrectDTO();
        orderRiskCorrectDTO1.setOrderId("orderId");
        orderRiskCorrectDTO1.setPolicyNo("policyNo");
        orderRiskCorrectDTO1.setCorrectId("correctId");
        orderRiskCorrectDTO1.setCorrectType("correctType");
        orderRiskCorrectDTO1.setRiskId(0);
        final List<OrderRiskCorrectDTO> correctDTOList = Arrays.asList(orderRiskCorrectDTO1);
        final SmCommissionDetailItemPO smCommissionDetailItemPO1 = new SmCommissionDetailItemPO();
        smCommissionDetailItemPO1.setOrderId("orderId");
        smCommissionDetailItemPO1.setPolicyNo("policyNo");
        smCommissionDetailItemPO1.setInsuredIdNumber("insuredIdNumber");
        smCommissionDetailItemPO1.setPolicyStatus("policyStatus");
        smCommissionDetailItemPO1.setRiskStatus("riskStatus");
        smCommissionDetailItemPO1.setPlanId(0);
        smCommissionDetailItemPO1.setRiskId(0);
        smCommissionDetailItemPO1.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smCommissionDetailItemPO1.setPlanAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO1.setAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO1.setOriginalAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO1.setTermNum(0);
        smCommissionDetailItemPO1.setCommissionType(0);
        smCommissionDetailItemPO1.setCommissionRate(new BigDecimal("0.00"));
        smCommissionDetailItemPO1.setCommissionAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO1.setCommissionUserId("commissionUserId");
        smCommissionDetailItemPO1.setBusinessType("businessType");
        smCommissionDetailItemPO1.setBusinessId("businessId");
        smCommissionDetailItemPO1.setImportFlag(0);
        final List<SmCommissionDetailItemPO> itemPOs = Arrays.asList(smCommissionDetailItemPO1);
        mockCommissionCalcService.calcCorrectCommission(0, orderInfoDTOList1, correctDTOList, itemPOs,
                new CalcErrorDTO("errorMsg","errorMsgNoAlarm"));

        // Configure CommissionCalcService.calcNewRefundDetailItem(...).
        final CalcCommissionItemResultDTO calcCommissionItemResultDTO2 = new CalcCommissionItemResultDTO();
        calcCommissionItemResultDTO2.setOrderId("orderId");
        calcCommissionItemResultDTO2.setPolicyNo("policyNo");
        calcCommissionItemResultDTO2.setInsuredIdNumber("insuredIdNumber");
        calcCommissionItemResultDTO2.setPolicyStatus("policyStatus");
        calcCommissionItemResultDTO2.setRiskStatus("riskStatus");
        final List<CalcCommissionItemResultDTO> calcCommissionItemResultDTOS1 = Arrays.asList(
                calcCommissionItemResultDTO2);
        final CommissionCalcOrderInfoDTO orderInfo = new CommissionCalcOrderInfoDTO();
        orderInfo.setProductAttrCode("productAttrCode");
        orderInfo.setCommissionId(0);
        orderInfo.setOriginalFhOrderId("originalFhOrderId");
        orderInfo.setOriginalCommissionId(0);
        orderInfo.setAppStatus("appStatus");
        mockCommissionCalcService.calcNewRefundDetailItem(orderInfo, new HashMap<>());

        // Run the test
        commissionCalcProcessServiceUnderTest.calcInsureCommission(processDTO, insuredInfoDTOList, false);

        // Verify the results
        // Confirm CommissionExternalService.getCommissionCalcOrderParam(...).
        final CommissionCalcProcessDTO processDTO1 = new CommissionCalcProcessDTO();
        processDTO1.setOrderId("orderId");
        processDTO1.setProductId(0);
        processDTO1.setProductType("productType");
        processDTO1.setMatchingTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        processDTO1.setTermNum(0);
        processDTO1.setCommissionConfigInfo(new HashMap<>());
        final CommissionRateDTO commissionRateDTO3 = new CommissionRateDTO();
        processDTO1.setOldCommissionConfigInfo(Arrays.asList(commissionRateDTO3));
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO5 = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO5.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO5.setCommissionId(0);
        commissionCalcOrderInfoDTO5.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO5.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO5.setAppStatus("appStatus");
        processDTO1.setOrderInfo(Arrays.asList(commissionCalcOrderInfoDTO5));
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO6 = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO6.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO6.setCommissionId(0);
        commissionCalcOrderInfoDTO6.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO6.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO6.setAppStatus("appStatus");
        processDTO1.setRefundOrderInfo(Arrays.asList(commissionCalcOrderInfoDTO6));
        final OrderRiskCorrectDTO orderRiskCorrectDTO2 = new OrderRiskCorrectDTO();
        processDTO1.setRiskCorrectDTOS(Arrays.asList(orderRiskCorrectDTO2));
        processDTO1.setItemMap(new HashMap<>());
        final CalcCommissionItemResultDTO calcCommissionItemResultDTO3 = new CalcCommissionItemResultDTO();
        processDTO1.setItemResults(Arrays.asList(calcCommissionItemResultDTO3));
        processDTO1.setCommissionUserId("commissionUserId");
        processDTO1.setBusinessType("businessType");
        final OrderCommissionInsuredInfoDTO orderCommissionInsuredInfoDTO1 = new OrderCommissionInsuredInfoDTO();
        orderCommissionInsuredInfoDTO1.setFhOrderId("fhOrderId");
        orderCommissionInsuredInfoDTO1.setInsuredId(0);
        orderCommissionInsuredInfoDTO1.setCompanyId(0);
        orderCommissionInsuredInfoDTO1.setProductId(0);
        orderCommissionInsuredInfoDTO1.setCommissionId(0);
        final List<OrderCommissionInsuredInfoDTO> insuredInfoDTOList1 = Arrays.asList(orderCommissionInsuredInfoDTO1);
        mockCommissionExternalService.getCommissionCalcOrderParam(processDTO1, insuredInfoDTOList1);

        // Confirm SmOrderCommissionItemMapper.batchInsertCommissionItem(...).
        final SmCommissionDetailItemPO smCommissionDetailItemPO2 = new SmCommissionDetailItemPO();
        smCommissionDetailItemPO2.setOrderId("orderId");
        smCommissionDetailItemPO2.setPolicyNo("policyNo");
        smCommissionDetailItemPO2.setInsuredIdNumber("insuredIdNumber");
        smCommissionDetailItemPO2.setPolicyStatus("policyStatus");
        smCommissionDetailItemPO2.setRiskStatus("riskStatus");
        smCommissionDetailItemPO2.setPlanId(0);
        smCommissionDetailItemPO2.setRiskId(0);
        smCommissionDetailItemPO2.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smCommissionDetailItemPO2.setPlanAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO2.setAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO2.setOriginalAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO2.setTermNum(0);
        smCommissionDetailItemPO2.setCommissionType(0);
        smCommissionDetailItemPO2.setCommissionRate(new BigDecimal("0.00"));
        smCommissionDetailItemPO2.setCommissionAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO2.setCommissionUserId("commissionUserId");
        smCommissionDetailItemPO2.setBusinessType("businessType");
        smCommissionDetailItemPO2.setBusinessId("businessId");
        smCommissionDetailItemPO2.setImportFlag(0);
        final List<SmCommissionDetailItemPO> commissionConfigItemList = Arrays.asList(smCommissionDetailItemPO2);
        mockSmOrderCommissionItemMapper.batchInsertCommissionItem(commissionConfigItemList);

        // Confirm SmOrderCommissionItemMapper.batchInsertCommissionItemExceptImport(...).
        final SmCommissionDetailItemPO smCommissionDetailItemPO3 = new SmCommissionDetailItemPO();
        smCommissionDetailItemPO3.setOrderId("orderId");
        smCommissionDetailItemPO3.setPolicyNo("policyNo");
        smCommissionDetailItemPO3.setInsuredIdNumber("insuredIdNumber");
        smCommissionDetailItemPO3.setPolicyStatus("policyStatus");
        smCommissionDetailItemPO3.setRiskStatus("riskStatus");
        smCommissionDetailItemPO3.setPlanId(0);
        smCommissionDetailItemPO3.setRiskId(0);
        smCommissionDetailItemPO3.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smCommissionDetailItemPO3.setPlanAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO3.setAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO3.setOriginalAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO3.setTermNum(0);
        smCommissionDetailItemPO3.setCommissionType(0);
        smCommissionDetailItemPO3.setCommissionRate(new BigDecimal("0.00"));
        smCommissionDetailItemPO3.setCommissionAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO3.setCommissionUserId("commissionUserId");
        smCommissionDetailItemPO3.setBusinessType("businessType");
        smCommissionDetailItemPO3.setBusinessId("businessId");
        smCommissionDetailItemPO3.setImportFlag(0);
        final List<SmCommissionDetailItemPO> commissionConfigItemList1 = Arrays.asList(smCommissionDetailItemPO3);
        mockSmOrderCommissionItemMapper.batchInsertCommissionItemExceptImport(commissionConfigItemList1);

        // Confirm SmOrderCommissionDetailMapper.batchReCalcCommissionDetail(...).
        final SmCommissionDetailPO smCommissionDetailPO = new SmCommissionDetailPO();
        smCommissionDetailPO.setOrderId("orderId");
        smCommissionDetailPO.setPolicyNo("policyNo");
        smCommissionDetailPO.setInsuredIdNumber("insuredIdNumber");
        smCommissionDetailPO.setPolicyStatus("policyStatus");
        smCommissionDetailPO.setPlanId(0);
        smCommissionDetailPO.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smCommissionDetailPO.setAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setOriginalAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setPaymentRate(new BigDecimal("0.00"));
        smCommissionDetailPO.setPaymentAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setSettlementRate(new BigDecimal("0.00"));
        smCommissionDetailPO.setSettlementAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setConversionRate(new BigDecimal("0.00"));
        smCommissionDetailPO.setConversionAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setTermNum(0);
        smCommissionDetailPO.setPaymentRiskJson("paymentRiskJson");
        smCommissionDetailPO.setSettlementRiskJson("settlementRiskJson");
        smCommissionDetailPO.setConversionRiskJson("conversionRiskJson");
        smCommissionDetailPO.setPAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setSAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setCAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setCommissionUserId("commissionUserId");
        smCommissionDetailPO.setBusinessType("businessType");
        smCommissionDetailPO.setBusinessId("businessId");
        smCommissionDetailPO.setImportFlag(0);
        final List<SmCommissionDetailPO> commissionConfigDetailList = Arrays.asList(smCommissionDetailPO);
        mockSmOrderCommissionDetailMapper.batchReCalcCommissionDetail(commissionConfigDetailList);

        // Confirm SmOrderCommissionDetailMapper.batchInsertCommissionDetail(...).
        final SmCommissionDetailPO smCommissionDetailPO1 = new SmCommissionDetailPO();
        smCommissionDetailPO1.setOrderId("orderId");
        smCommissionDetailPO1.setPolicyNo("policyNo");
        smCommissionDetailPO1.setInsuredIdNumber("insuredIdNumber");
        smCommissionDetailPO1.setPolicyStatus("policyStatus");
        smCommissionDetailPO1.setPlanId(0);
        smCommissionDetailPO1.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smCommissionDetailPO1.setAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setOriginalAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setPaymentRate(new BigDecimal("0.00"));
        smCommissionDetailPO1.setPaymentAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setSettlementRate(new BigDecimal("0.00"));
        smCommissionDetailPO1.setSettlementAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setConversionRate(new BigDecimal("0.00"));
        smCommissionDetailPO1.setConversionAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setTermNum(0);
        smCommissionDetailPO1.setPaymentRiskJson("paymentRiskJson");
        smCommissionDetailPO1.setSettlementRiskJson("settlementRiskJson");
        smCommissionDetailPO1.setConversionRiskJson("conversionRiskJson");
        smCommissionDetailPO1.setPAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setSAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setCAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setCommissionUserId("commissionUserId");
        smCommissionDetailPO1.setBusinessType("businessType");
        smCommissionDetailPO1.setBusinessId("businessId");
        smCommissionDetailPO1.setImportFlag(0);
        final List<SmCommissionDetailPO> commissionConfigDetailList1 = Arrays.asList(smCommissionDetailPO1);
        mockSmOrderCommissionDetailMapper.batchInsertCommissionDetail(commissionConfigDetailList1);
    }

    @Test
    public void testCalcInsureCommission_CommissionCalcServiceCalcCorrectCommissionReturnsNoItems() throws Exception {
        // Setup
        final CommissionCalcProcessDTO processDTO = new CommissionCalcProcessDTO();
        processDTO.setOrderId("orderId");
        processDTO.setProductId(0);
        processDTO.setProductType("productType");
        processDTO.setMatchingTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        processDTO.setTermNum(0);
        processDTO.setCommissionConfigInfo(new HashMap<>());
        final CommissionRateDTO commissionRateDTO = new CommissionRateDTO();
        processDTO.setOldCommissionConfigInfo(Arrays.asList(commissionRateDTO));
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO.setCommissionId(0);
        commissionCalcOrderInfoDTO.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO.setAppStatus("appStatus");
        processDTO.setOrderInfo(Arrays.asList(commissionCalcOrderInfoDTO));
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO1 = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO1.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO1.setCommissionId(0);
        commissionCalcOrderInfoDTO1.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO1.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO1.setAppStatus("appStatus");
        processDTO.setRefundOrderInfo(Arrays.asList(commissionCalcOrderInfoDTO1));
        final OrderRiskCorrectDTO orderRiskCorrectDTO = new OrderRiskCorrectDTO();
        processDTO.setRiskCorrectDTOS(Arrays.asList(orderRiskCorrectDTO));
        processDTO.setItemMap(new HashMap<>());
        final CalcCommissionItemResultDTO calcCommissionItemResultDTO = new CalcCommissionItemResultDTO();
        processDTO.setItemResults(Arrays.asList(calcCommissionItemResultDTO));
        processDTO.setCommissionUserId("commissionUserId");
        processDTO.setBusinessType("businessType");

        final OrderCommissionInsuredInfoDTO orderCommissionInsuredInfoDTO = new OrderCommissionInsuredInfoDTO();
        orderCommissionInsuredInfoDTO.setFhOrderId("fhOrderId");
        orderCommissionInsuredInfoDTO.setInsuredId(0);
        orderCommissionInsuredInfoDTO.setCompanyId(0);
        orderCommissionInsuredInfoDTO.setProductId(0);
        orderCommissionInsuredInfoDTO.setCommissionId(0);
        final List<OrderCommissionInsuredInfoDTO> insuredInfoDTOList = Arrays.asList(orderCommissionInsuredInfoDTO);

        // Configure CommissionConfigQueryService.listConfigByProductId(...).
        final SystemCommissionConfig systemCommissionConfig = new SystemCommissionConfig();
        systemCommissionConfig.setType(0);
        systemCommissionConfig.setCompanyId(0);
        systemCommissionConfig.setProductId(0);
        systemCommissionConfig.setStartTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        systemCommissionConfig.setEndTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SystemCommissionConfig> systemCommissionConfigs = Arrays.asList(systemCommissionConfig);
        mockCommissionConfigQueryService.listConfigByProductId(0);

        // Configure CommissionConfigQueryService.listCalcCommissionDetailByConfigId(...).
        final CommissionCalcConfigDetailDTO commissionCalcConfigDetailDTO = new CommissionCalcConfigDetailDTO();
        commissionCalcConfigDetailDTO.setId(0);
        commissionCalcConfigDetailDTO.setConfigId(0);
        commissionCalcConfigDetailDTO.setPeriodNum(0);
        commissionCalcConfigDetailDTO.setPlanId(0);
        commissionCalcConfigDetailDTO.setRiskId(0);
        final List<CommissionCalcConfigDetailDTO> commissionCalcConfigDetailDTOS = Arrays.asList(
                commissionCalcConfigDetailDTO);
        mockCommissionConfigQueryService.listCalcCommissionDetailByConfigId(0);

        mockCommissionExternalService.isDecreaseGroupOrder("productAttrCode", "appStatus");

        // Configure SmOrderCommissionItemMapper.listCommissionItemByOrderId(...).
        final SmCommissionDetailItemPO smCommissionDetailItemPO = new SmCommissionDetailItemPO();
        smCommissionDetailItemPO.setOrderId("orderId");
        smCommissionDetailItemPO.setPolicyNo("policyNo");
        smCommissionDetailItemPO.setInsuredIdNumber("insuredIdNumber");
        smCommissionDetailItemPO.setPolicyStatus("policyStatus");
        smCommissionDetailItemPO.setRiskStatus("riskStatus");
        smCommissionDetailItemPO.setPlanId(0);
        smCommissionDetailItemPO.setRiskId(0);
        smCommissionDetailItemPO.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smCommissionDetailItemPO.setPlanAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setOriginalAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setTermNum(0);
        smCommissionDetailItemPO.setCommissionType(0);
        smCommissionDetailItemPO.setCommissionRate(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setCommissionAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setCommissionUserId("commissionUserId");
        smCommissionDetailItemPO.setBusinessType("businessType");
        smCommissionDetailItemPO.setBusinessId("businessId");
        smCommissionDetailItemPO.setImportFlag(0);
        final List<SmCommissionDetailItemPO> smCommissionDetailItemPOS = Arrays.asList(smCommissionDetailItemPO);
        mockSmOrderCommissionItemMapper.listCommissionItemByOrderId("originalFhOrderId", 0);

        // Configure CommissionExternalService.getOldConvertedConfig(...).
        final CommissionRateDTO commissionRateDTO1 = new CommissionRateDTO();
        commissionRateDTO1.setPlanId(0);
        commissionRateDTO1.setRiskId(0);
        commissionRateDTO1.setCommissionType(0);
        commissionRateDTO1.setCommissionId(0);
        commissionRateDTO1.setCommissionRate(new BigDecimal("0.00"));
        final List<CommissionRateDTO> commissionRateDTOS = Arrays.asList(commissionRateDTO1);
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO2 = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO2.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO2.setCommissionId(0);
        commissionCalcOrderInfoDTO2.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO2.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO2.setAppStatus("appStatus");
        final List<CommissionCalcOrderInfoDTO> orderInfoList = Arrays.asList(commissionCalcOrderInfoDTO2);
        mockCommissionExternalService.getOldConvertedConfig(orderInfoList);

        // Configure CommissionConfigQueryService.getOldCommissionSetting(...).
        final CommissionRateDTO commissionRateDTO2 = new CommissionRateDTO();
        commissionRateDTO2.setPlanId(0);
        commissionRateDTO2.setRiskId(0);
        commissionRateDTO2.setCommissionType(0);
        commissionRateDTO2.setCommissionId(0);
        commissionRateDTO2.setCommissionRate(new BigDecimal("0.00"));
        final List<CommissionRateDTO> commissionRateDTOS1 = Arrays.asList(commissionRateDTO2);
        mockCommissionConfigQueryService.getOldCommissionSetting(Arrays.asList(0));

        // Configure CommissionCalcService.calc(...).
        final CalcCommissionItemResultDTO calcCommissionItemResultDTO1 = new CalcCommissionItemResultDTO();
        calcCommissionItemResultDTO1.setOrderId("orderId");
        calcCommissionItemResultDTO1.setPolicyNo("policyNo");
        calcCommissionItemResultDTO1.setInsuredIdNumber("insuredIdNumber");
        calcCommissionItemResultDTO1.setPolicyStatus("policyStatus");
        calcCommissionItemResultDTO1.setRiskStatus("riskStatus");
        final List<CalcCommissionItemResultDTO> calcCommissionItemResultDTOS = Arrays.asList(
                calcCommissionItemResultDTO1);
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO3 = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO3.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO3.setCommissionId(0);
        commissionCalcOrderInfoDTO3.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO3.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO3.setAppStatus("appStatus");
        final List<CommissionCalcOrderInfoDTO> orderInfoDTOList = Arrays.asList(commissionCalcOrderInfoDTO3);
        final CommissionCalcConfigInfoDTO configInfoDTO = new CommissionCalcConfigInfoDTO();
        configInfoDTO.setId(0);
        configInfoDTO.setType(0);
        configInfoDTO.setCompanyId(0);
        final CommissionCalcConfigDetailDTO commissionCalcConfigDetailDTO1 = new CommissionCalcConfigDetailDTO();
        commissionCalcConfigDetailDTO1.setPeriodNum(0);
        configInfoDTO.setConfigDetailDTOList(Arrays.asList(commissionCalcConfigDetailDTO1));
        mockCommissionCalcService.calc(0, 0, orderInfoDTOList, configInfoDTO, new CalcErrorDTO("errorMsg","errorMsgNoAlarm"),
                "commissionUserId", "businessType");

        // Configure CommissionCalcService.calcCorrectCommission(...).
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO4 = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO4.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO4.setCommissionId(0);
        commissionCalcOrderInfoDTO4.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO4.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO4.setAppStatus("appStatus");
        final List<CommissionCalcOrderInfoDTO> orderInfoDTOList1 = Arrays.asList(commissionCalcOrderInfoDTO4);
        final OrderRiskCorrectDTO orderRiskCorrectDTO1 = new OrderRiskCorrectDTO();
        orderRiskCorrectDTO1.setOrderId("orderId");
        orderRiskCorrectDTO1.setPolicyNo("policyNo");
        orderRiskCorrectDTO1.setCorrectId("correctId");
        orderRiskCorrectDTO1.setCorrectType("correctType");
        orderRiskCorrectDTO1.setRiskId(0);
        final List<OrderRiskCorrectDTO> correctDTOList = Arrays.asList(orderRiskCorrectDTO1);
        final SmCommissionDetailItemPO smCommissionDetailItemPO1 = new SmCommissionDetailItemPO();
        smCommissionDetailItemPO1.setOrderId("orderId");
        smCommissionDetailItemPO1.setPolicyNo("policyNo");
        smCommissionDetailItemPO1.setInsuredIdNumber("insuredIdNumber");
        smCommissionDetailItemPO1.setPolicyStatus("policyStatus");
        smCommissionDetailItemPO1.setRiskStatus("riskStatus");
        smCommissionDetailItemPO1.setPlanId(0);
        smCommissionDetailItemPO1.setRiskId(0);
        smCommissionDetailItemPO1.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smCommissionDetailItemPO1.setPlanAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO1.setAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO1.setOriginalAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO1.setTermNum(0);
        smCommissionDetailItemPO1.setCommissionType(0);
        smCommissionDetailItemPO1.setCommissionRate(new BigDecimal("0.00"));
        smCommissionDetailItemPO1.setCommissionAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO1.setCommissionUserId("commissionUserId");
        smCommissionDetailItemPO1.setBusinessType("businessType");
        smCommissionDetailItemPO1.setBusinessId("businessId");
        smCommissionDetailItemPO1.setImportFlag(0);
        final List<SmCommissionDetailItemPO> itemPOs = Arrays.asList(smCommissionDetailItemPO1);
        mockCommissionCalcService.calcCorrectCommission(0, orderInfoDTOList1, correctDTOList, itemPOs,
                new CalcErrorDTO("errorMsg","errorMsgNoAlarm"));

        // Configure CommissionCalcService.calcNewRefundDetailItem(...).
        final CalcCommissionItemResultDTO calcCommissionItemResultDTO2 = new CalcCommissionItemResultDTO();
        calcCommissionItemResultDTO2.setOrderId("orderId");
        calcCommissionItemResultDTO2.setPolicyNo("policyNo");
        calcCommissionItemResultDTO2.setInsuredIdNumber("insuredIdNumber");
        calcCommissionItemResultDTO2.setPolicyStatus("policyStatus");
        calcCommissionItemResultDTO2.setRiskStatus("riskStatus");
        final List<CalcCommissionItemResultDTO> calcCommissionItemResultDTOS1 = Arrays.asList(
                calcCommissionItemResultDTO2);
        final CommissionCalcOrderInfoDTO orderInfo = new CommissionCalcOrderInfoDTO();
        orderInfo.setProductAttrCode("productAttrCode");
        orderInfo.setCommissionId(0);
        orderInfo.setOriginalFhOrderId("originalFhOrderId");
        orderInfo.setOriginalCommissionId(0);
        orderInfo.setAppStatus("appStatus");
        mockCommissionCalcService.calcNewRefundDetailItem(orderInfo, new HashMap<>());

        // Run the test
        commissionCalcProcessServiceUnderTest.calcInsureCommission(processDTO, insuredInfoDTOList, false);

        // Verify the results
        // Confirm CommissionExternalService.getCommissionCalcOrderParam(...).
        final CommissionCalcProcessDTO processDTO1 = new CommissionCalcProcessDTO();
        processDTO1.setOrderId("orderId");
        processDTO1.setProductId(0);
        processDTO1.setProductType("productType");
        processDTO1.setMatchingTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        processDTO1.setTermNum(0);
        processDTO1.setCommissionConfigInfo(new HashMap<>());
        final CommissionRateDTO commissionRateDTO3 = new CommissionRateDTO();
        processDTO1.setOldCommissionConfigInfo(Arrays.asList(commissionRateDTO3));
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO5 = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO5.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO5.setCommissionId(0);
        commissionCalcOrderInfoDTO5.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO5.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO5.setAppStatus("appStatus");
        processDTO1.setOrderInfo(Arrays.asList(commissionCalcOrderInfoDTO5));
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO6 = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO6.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO6.setCommissionId(0);
        commissionCalcOrderInfoDTO6.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO6.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO6.setAppStatus("appStatus");
        processDTO1.setRefundOrderInfo(Arrays.asList(commissionCalcOrderInfoDTO6));
        final OrderRiskCorrectDTO orderRiskCorrectDTO2 = new OrderRiskCorrectDTO();
        processDTO1.setRiskCorrectDTOS(Arrays.asList(orderRiskCorrectDTO2));
        processDTO1.setItemMap(new HashMap<>());
        final CalcCommissionItemResultDTO calcCommissionItemResultDTO3 = new CalcCommissionItemResultDTO();
        processDTO1.setItemResults(Arrays.asList(calcCommissionItemResultDTO3));
        processDTO1.setCommissionUserId("commissionUserId");
        processDTO1.setBusinessType("businessType");
        final OrderCommissionInsuredInfoDTO orderCommissionInsuredInfoDTO1 = new OrderCommissionInsuredInfoDTO();
        orderCommissionInsuredInfoDTO1.setFhOrderId("fhOrderId");
        orderCommissionInsuredInfoDTO1.setInsuredId(0);
        orderCommissionInsuredInfoDTO1.setCompanyId(0);
        orderCommissionInsuredInfoDTO1.setProductId(0);
        orderCommissionInsuredInfoDTO1.setCommissionId(0);
        final List<OrderCommissionInsuredInfoDTO> insuredInfoDTOList1 = Arrays.asList(orderCommissionInsuredInfoDTO1);
        mockCommissionExternalService.getCommissionCalcOrderParam(processDTO1, insuredInfoDTOList1);

        // Confirm SmOrderCommissionItemMapper.batchInsertCommissionItem(...).
        final SmCommissionDetailItemPO smCommissionDetailItemPO2 = new SmCommissionDetailItemPO();
        smCommissionDetailItemPO2.setOrderId("orderId");
        smCommissionDetailItemPO2.setPolicyNo("policyNo");
        smCommissionDetailItemPO2.setInsuredIdNumber("insuredIdNumber");
        smCommissionDetailItemPO2.setPolicyStatus("policyStatus");
        smCommissionDetailItemPO2.setRiskStatus("riskStatus");
        smCommissionDetailItemPO2.setPlanId(0);
        smCommissionDetailItemPO2.setRiskId(0);
        smCommissionDetailItemPO2.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smCommissionDetailItemPO2.setPlanAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO2.setAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO2.setOriginalAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO2.setTermNum(0);
        smCommissionDetailItemPO2.setCommissionType(0);
        smCommissionDetailItemPO2.setCommissionRate(new BigDecimal("0.00"));
        smCommissionDetailItemPO2.setCommissionAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO2.setCommissionUserId("commissionUserId");
        smCommissionDetailItemPO2.setBusinessType("businessType");
        smCommissionDetailItemPO2.setBusinessId("businessId");
        smCommissionDetailItemPO2.setImportFlag(0);
        final List<SmCommissionDetailItemPO> commissionConfigItemList = Arrays.asList(smCommissionDetailItemPO2);
        mockSmOrderCommissionItemMapper.batchInsertCommissionItem(commissionConfigItemList);

        // Confirm SmOrderCommissionItemMapper.batchInsertCommissionItemExceptImport(...).
        final SmCommissionDetailItemPO smCommissionDetailItemPO3 = new SmCommissionDetailItemPO();
        smCommissionDetailItemPO3.setOrderId("orderId");
        smCommissionDetailItemPO3.setPolicyNo("policyNo");
        smCommissionDetailItemPO3.setInsuredIdNumber("insuredIdNumber");
        smCommissionDetailItemPO3.setPolicyStatus("policyStatus");
        smCommissionDetailItemPO3.setRiskStatus("riskStatus");
        smCommissionDetailItemPO3.setPlanId(0);
        smCommissionDetailItemPO3.setRiskId(0);
        smCommissionDetailItemPO3.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smCommissionDetailItemPO3.setPlanAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO3.setAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO3.setOriginalAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO3.setTermNum(0);
        smCommissionDetailItemPO3.setCommissionType(0);
        smCommissionDetailItemPO3.setCommissionRate(new BigDecimal("0.00"));
        smCommissionDetailItemPO3.setCommissionAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO3.setCommissionUserId("commissionUserId");
        smCommissionDetailItemPO3.setBusinessType("businessType");
        smCommissionDetailItemPO3.setBusinessId("businessId");
        smCommissionDetailItemPO3.setImportFlag(0);
        final List<SmCommissionDetailItemPO> commissionConfigItemList1 = Arrays.asList(smCommissionDetailItemPO3);
        mockSmOrderCommissionItemMapper.batchInsertCommissionItemExceptImport(commissionConfigItemList1);

        // Confirm SmOrderCommissionDetailMapper.batchReCalcCommissionDetail(...).
        final SmCommissionDetailPO smCommissionDetailPO = new SmCommissionDetailPO();
        smCommissionDetailPO.setOrderId("orderId");
        smCommissionDetailPO.setPolicyNo("policyNo");
        smCommissionDetailPO.setInsuredIdNumber("insuredIdNumber");
        smCommissionDetailPO.setPolicyStatus("policyStatus");
        smCommissionDetailPO.setPlanId(0);
        smCommissionDetailPO.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smCommissionDetailPO.setAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setOriginalAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setPaymentRate(new BigDecimal("0.00"));
        smCommissionDetailPO.setPaymentAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setSettlementRate(new BigDecimal("0.00"));
        smCommissionDetailPO.setSettlementAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setConversionRate(new BigDecimal("0.00"));
        smCommissionDetailPO.setConversionAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setTermNum(0);
        smCommissionDetailPO.setPaymentRiskJson("paymentRiskJson");
        smCommissionDetailPO.setSettlementRiskJson("settlementRiskJson");
        smCommissionDetailPO.setConversionRiskJson("conversionRiskJson");
        smCommissionDetailPO.setPAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setSAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setCAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setCommissionUserId("commissionUserId");
        smCommissionDetailPO.setBusinessType("businessType");
        smCommissionDetailPO.setBusinessId("businessId");
        smCommissionDetailPO.setImportFlag(0);
        final List<SmCommissionDetailPO> commissionConfigDetailList = Arrays.asList(smCommissionDetailPO);
        mockSmOrderCommissionDetailMapper.batchReCalcCommissionDetail(commissionConfigDetailList);

        // Confirm SmOrderCommissionDetailMapper.batchInsertCommissionDetail(...).
        final SmCommissionDetailPO smCommissionDetailPO1 = new SmCommissionDetailPO();
        smCommissionDetailPO1.setOrderId("orderId");
        smCommissionDetailPO1.setPolicyNo("policyNo");
        smCommissionDetailPO1.setInsuredIdNumber("insuredIdNumber");
        smCommissionDetailPO1.setPolicyStatus("policyStatus");
        smCommissionDetailPO1.setPlanId(0);
        smCommissionDetailPO1.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smCommissionDetailPO1.setAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setOriginalAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setPaymentRate(new BigDecimal("0.00"));
        smCommissionDetailPO1.setPaymentAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setSettlementRate(new BigDecimal("0.00"));
        smCommissionDetailPO1.setSettlementAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setConversionRate(new BigDecimal("0.00"));
        smCommissionDetailPO1.setConversionAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setTermNum(0);
        smCommissionDetailPO1.setPaymentRiskJson("paymentRiskJson");
        smCommissionDetailPO1.setSettlementRiskJson("settlementRiskJson");
        smCommissionDetailPO1.setConversionRiskJson("conversionRiskJson");
        smCommissionDetailPO1.setPAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setSAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setCAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setCommissionUserId("commissionUserId");
        smCommissionDetailPO1.setBusinessType("businessType");
        smCommissionDetailPO1.setBusinessId("businessId");
        smCommissionDetailPO1.setImportFlag(0);
        final List<SmCommissionDetailPO> commissionConfigDetailList1 = Arrays.asList(smCommissionDetailPO1);
        mockSmOrderCommissionDetailMapper.batchInsertCommissionDetail(commissionConfigDetailList1);
    }

    @Test
    public void testCalcInsureCommission_CommissionCalcServiceCalcNewRefundDetailItemReturnsNoItems() throws Exception {
        // Setup
        final CommissionCalcProcessDTO processDTO = new CommissionCalcProcessDTO();
        processDTO.setOrderId("orderId");
        processDTO.setProductId(0);
        processDTO.setProductType("productType");
        processDTO.setMatchingTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        processDTO.setTermNum(0);
        processDTO.setCommissionConfigInfo(new HashMap<>());
        final CommissionRateDTO commissionRateDTO = new CommissionRateDTO();
        processDTO.setOldCommissionConfigInfo(Arrays.asList(commissionRateDTO));
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO.setCommissionId(0);
        commissionCalcOrderInfoDTO.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO.setAppStatus("appStatus");
        processDTO.setOrderInfo(Arrays.asList(commissionCalcOrderInfoDTO));
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO1 = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO1.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO1.setCommissionId(0);
        commissionCalcOrderInfoDTO1.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO1.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO1.setAppStatus("appStatus");
        processDTO.setRefundOrderInfo(Arrays.asList(commissionCalcOrderInfoDTO1));
        final OrderRiskCorrectDTO orderRiskCorrectDTO = new OrderRiskCorrectDTO();
        processDTO.setRiskCorrectDTOS(Arrays.asList(orderRiskCorrectDTO));
        processDTO.setItemMap(new HashMap<>());
        final CalcCommissionItemResultDTO calcCommissionItemResultDTO = new CalcCommissionItemResultDTO();
        processDTO.setItemResults(Arrays.asList(calcCommissionItemResultDTO));
        processDTO.setCommissionUserId("commissionUserId");
        processDTO.setBusinessType("businessType");

        final OrderCommissionInsuredInfoDTO orderCommissionInsuredInfoDTO = new OrderCommissionInsuredInfoDTO();
        orderCommissionInsuredInfoDTO.setFhOrderId("fhOrderId");
        orderCommissionInsuredInfoDTO.setInsuredId(0);
        orderCommissionInsuredInfoDTO.setCompanyId(0);
        orderCommissionInsuredInfoDTO.setProductId(0);
        orderCommissionInsuredInfoDTO.setCommissionId(0);
        final List<OrderCommissionInsuredInfoDTO> insuredInfoDTOList = Arrays.asList(orderCommissionInsuredInfoDTO);

        // Configure CommissionConfigQueryService.listConfigByProductId(...).
        final SystemCommissionConfig systemCommissionConfig = new SystemCommissionConfig();
        systemCommissionConfig.setType(0);
        systemCommissionConfig.setCompanyId(0);
        systemCommissionConfig.setProductId(0);
        systemCommissionConfig.setStartTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        systemCommissionConfig.setEndTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SystemCommissionConfig> systemCommissionConfigs = Arrays.asList(systemCommissionConfig);
        mockCommissionConfigQueryService.listConfigByProductId(0);

        // Configure CommissionConfigQueryService.listCalcCommissionDetailByConfigId(...).
        final CommissionCalcConfigDetailDTO commissionCalcConfigDetailDTO = new CommissionCalcConfigDetailDTO();
        commissionCalcConfigDetailDTO.setId(0);
        commissionCalcConfigDetailDTO.setConfigId(0);
        commissionCalcConfigDetailDTO.setPeriodNum(0);
        commissionCalcConfigDetailDTO.setPlanId(0);
        commissionCalcConfigDetailDTO.setRiskId(0);
        final List<CommissionCalcConfigDetailDTO> commissionCalcConfigDetailDTOS = Arrays.asList(
                commissionCalcConfigDetailDTO);
        mockCommissionConfigQueryService.listCalcCommissionDetailByConfigId(0);

        mockCommissionExternalService.isDecreaseGroupOrder("productAttrCode", "appStatus");

        // Configure SmOrderCommissionItemMapper.listCommissionItemByOrderId(...).
        final SmCommissionDetailItemPO smCommissionDetailItemPO = new SmCommissionDetailItemPO();
        smCommissionDetailItemPO.setOrderId("orderId");
        smCommissionDetailItemPO.setPolicyNo("policyNo");
        smCommissionDetailItemPO.setInsuredIdNumber("insuredIdNumber");
        smCommissionDetailItemPO.setPolicyStatus("policyStatus");
        smCommissionDetailItemPO.setRiskStatus("riskStatus");
        smCommissionDetailItemPO.setPlanId(0);
        smCommissionDetailItemPO.setRiskId(0);
        smCommissionDetailItemPO.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smCommissionDetailItemPO.setPlanAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setOriginalAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setTermNum(0);
        smCommissionDetailItemPO.setCommissionType(0);
        smCommissionDetailItemPO.setCommissionRate(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setCommissionAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setCommissionUserId("commissionUserId");
        smCommissionDetailItemPO.setBusinessType("businessType");
        smCommissionDetailItemPO.setBusinessId("businessId");
        smCommissionDetailItemPO.setImportFlag(0);
        final List<SmCommissionDetailItemPO> smCommissionDetailItemPOS = Arrays.asList(smCommissionDetailItemPO);
        mockSmOrderCommissionItemMapper.listCommissionItemByOrderId("originalFhOrderId", 0);

        // Configure CommissionExternalService.getOldConvertedConfig(...).
        final CommissionRateDTO commissionRateDTO1 = new CommissionRateDTO();
        commissionRateDTO1.setPlanId(0);
        commissionRateDTO1.setRiskId(0);
        commissionRateDTO1.setCommissionType(0);
        commissionRateDTO1.setCommissionId(0);
        commissionRateDTO1.setCommissionRate(new BigDecimal("0.00"));
        final List<CommissionRateDTO> commissionRateDTOS = Arrays.asList(commissionRateDTO1);
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO2 = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO2.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO2.setCommissionId(0);
        commissionCalcOrderInfoDTO2.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO2.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO2.setAppStatus("appStatus");
        final List<CommissionCalcOrderInfoDTO> orderInfoList = Arrays.asList(commissionCalcOrderInfoDTO2);
        mockCommissionExternalService.getOldConvertedConfig(orderInfoList);

        // Configure CommissionConfigQueryService.getOldCommissionSetting(...).
        final CommissionRateDTO commissionRateDTO2 = new CommissionRateDTO();
        commissionRateDTO2.setPlanId(0);
        commissionRateDTO2.setRiskId(0);
        commissionRateDTO2.setCommissionType(0);
        commissionRateDTO2.setCommissionId(0);
        commissionRateDTO2.setCommissionRate(new BigDecimal("0.00"));
        final List<CommissionRateDTO> commissionRateDTOS1 = Arrays.asList(commissionRateDTO2);
        mockCommissionConfigQueryService.getOldCommissionSetting(Arrays.asList(0));

        // Configure CommissionCalcService.calc(...).
        final CalcCommissionItemResultDTO calcCommissionItemResultDTO1 = new CalcCommissionItemResultDTO();
        calcCommissionItemResultDTO1.setOrderId("orderId");
        calcCommissionItemResultDTO1.setPolicyNo("policyNo");
        calcCommissionItemResultDTO1.setInsuredIdNumber("insuredIdNumber");
        calcCommissionItemResultDTO1.setPolicyStatus("policyStatus");
        calcCommissionItemResultDTO1.setRiskStatus("riskStatus");
        final List<CalcCommissionItemResultDTO> calcCommissionItemResultDTOS = Arrays.asList(
                calcCommissionItemResultDTO1);
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO3 = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO3.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO3.setCommissionId(0);
        commissionCalcOrderInfoDTO3.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO3.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO3.setAppStatus("appStatus");
        final List<CommissionCalcOrderInfoDTO> orderInfoDTOList = Arrays.asList(commissionCalcOrderInfoDTO3);
        final CommissionCalcConfigInfoDTO configInfoDTO = new CommissionCalcConfigInfoDTO();
        configInfoDTO.setId(0);
        configInfoDTO.setType(0);
        configInfoDTO.setCompanyId(0);
        final CommissionCalcConfigDetailDTO commissionCalcConfigDetailDTO1 = new CommissionCalcConfigDetailDTO();
        commissionCalcConfigDetailDTO1.setPeriodNum(0);
        configInfoDTO.setConfigDetailDTOList(Arrays.asList(commissionCalcConfigDetailDTO1));
        mockCommissionCalcService.calc(0, 0, orderInfoDTOList, configInfoDTO, new CalcErrorDTO("errorMsg","errorMsgNoAlarm"),
                "commissionUserId", "businessType");

        // Configure CommissionCalcService.calcCorrectCommission(...).
        final CalcCommissionItemResultDTO calcCommissionItemResultDTO2 = new CalcCommissionItemResultDTO();
        calcCommissionItemResultDTO2.setOrderId("orderId");
        calcCommissionItemResultDTO2.setPolicyNo("policyNo");
        calcCommissionItemResultDTO2.setInsuredIdNumber("insuredIdNumber");
        calcCommissionItemResultDTO2.setPolicyStatus("policyStatus");
        calcCommissionItemResultDTO2.setRiskStatus("riskStatus");
        final List<CalcCommissionItemResultDTO> calcCommissionItemResultDTOS1 = Arrays.asList(
                calcCommissionItemResultDTO2);
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO4 = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO4.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO4.setCommissionId(0);
        commissionCalcOrderInfoDTO4.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO4.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO4.setAppStatus("appStatus");
        final List<CommissionCalcOrderInfoDTO> orderInfoDTOList1 = Arrays.asList(commissionCalcOrderInfoDTO4);
        final OrderRiskCorrectDTO orderRiskCorrectDTO1 = new OrderRiskCorrectDTO();
        orderRiskCorrectDTO1.setOrderId("orderId");
        orderRiskCorrectDTO1.setPolicyNo("policyNo");
        orderRiskCorrectDTO1.setCorrectId("correctId");
        orderRiskCorrectDTO1.setCorrectType("correctType");
        orderRiskCorrectDTO1.setRiskId(0);
        final List<OrderRiskCorrectDTO> correctDTOList = Arrays.asList(orderRiskCorrectDTO1);
        final SmCommissionDetailItemPO smCommissionDetailItemPO1 = new SmCommissionDetailItemPO();
        smCommissionDetailItemPO1.setOrderId("orderId");
        smCommissionDetailItemPO1.setPolicyNo("policyNo");
        smCommissionDetailItemPO1.setInsuredIdNumber("insuredIdNumber");
        smCommissionDetailItemPO1.setPolicyStatus("policyStatus");
        smCommissionDetailItemPO1.setRiskStatus("riskStatus");
        smCommissionDetailItemPO1.setPlanId(0);
        smCommissionDetailItemPO1.setRiskId(0);
        smCommissionDetailItemPO1.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smCommissionDetailItemPO1.setPlanAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO1.setAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO1.setOriginalAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO1.setTermNum(0);
        smCommissionDetailItemPO1.setCommissionType(0);
        smCommissionDetailItemPO1.setCommissionRate(new BigDecimal("0.00"));
        smCommissionDetailItemPO1.setCommissionAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO1.setCommissionUserId("commissionUserId");
        smCommissionDetailItemPO1.setBusinessType("businessType");
        smCommissionDetailItemPO1.setBusinessId("businessId");
        smCommissionDetailItemPO1.setImportFlag(0);
        final List<SmCommissionDetailItemPO> itemPOs = Arrays.asList(smCommissionDetailItemPO1);
        mockCommissionCalcService.calcCorrectCommission(0, orderInfoDTOList1, correctDTOList, itemPOs,
                new CalcErrorDTO("errorMsg","errorMsgNoAlarm"));

        // Configure CommissionCalcService.calcNewRefundDetailItem(...).
        final CommissionCalcOrderInfoDTO orderInfo = new CommissionCalcOrderInfoDTO();
        orderInfo.setProductAttrCode("productAttrCode");
        orderInfo.setCommissionId(0);
        orderInfo.setOriginalFhOrderId("originalFhOrderId");
        orderInfo.setOriginalCommissionId(0);
        orderInfo.setAppStatus("appStatus");
        mockCommissionCalcService.calcNewRefundDetailItem(orderInfo, new HashMap<>());

        // Configure CommissionCalcService.calcOldRefundDetailItem(...).
        final CalcCommissionItemResultDTO calcCommissionItemResultDTO3 = new CalcCommissionItemResultDTO();
        calcCommissionItemResultDTO3.setOrderId("orderId");
        calcCommissionItemResultDTO3.setPolicyNo("policyNo");
        calcCommissionItemResultDTO3.setInsuredIdNumber("insuredIdNumber");
        calcCommissionItemResultDTO3.setPolicyStatus("policyStatus");
        calcCommissionItemResultDTO3.setRiskStatus("riskStatus");
        final List<CalcCommissionItemResultDTO> calcCommissionItemResultDTOS2 = Arrays.asList(
                calcCommissionItemResultDTO3);
        final CommissionCalcOrderInfoDTO orderInfo1 = new CommissionCalcOrderInfoDTO();
        orderInfo1.setProductAttrCode("productAttrCode");
        orderInfo1.setCommissionId(0);
        orderInfo1.setOriginalFhOrderId("originalFhOrderId");
        orderInfo1.setOriginalCommissionId(0);
        orderInfo1.setAppStatus("appStatus");
        final CommissionRateDTO commissionRateDTO3 = new CommissionRateDTO();
        commissionRateDTO3.setPlanId(0);
        commissionRateDTO3.setRiskId(0);
        commissionRateDTO3.setCommissionType(0);
        commissionRateDTO3.setCommissionId(0);
        commissionRateDTO3.setCommissionRate(new BigDecimal("0.00"));
        final List<CommissionRateDTO> oldCommissionRates = Arrays.asList(commissionRateDTO3);
        mockCommissionCalcService.calcOldRefundDetailItem(orderInfo1, oldCommissionRates,
                "commissionUserId");

        // Run the test
        commissionCalcProcessServiceUnderTest.calcInsureCommission(processDTO, insuredInfoDTOList, false);

        // Verify the results
        // Confirm CommissionExternalService.getCommissionCalcOrderParam(...).
        final CommissionCalcProcessDTO processDTO1 = new CommissionCalcProcessDTO();
        processDTO1.setOrderId("orderId");
        processDTO1.setProductId(0);
        processDTO1.setProductType("productType");
        processDTO1.setMatchingTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        processDTO1.setTermNum(0);
        processDTO1.setCommissionConfigInfo(new HashMap<>());
        final CommissionRateDTO commissionRateDTO4 = new CommissionRateDTO();
        processDTO1.setOldCommissionConfigInfo(Arrays.asList(commissionRateDTO4));
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO5 = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO5.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO5.setCommissionId(0);
        commissionCalcOrderInfoDTO5.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO5.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO5.setAppStatus("appStatus");
        processDTO1.setOrderInfo(Arrays.asList(commissionCalcOrderInfoDTO5));
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO6 = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO6.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO6.setCommissionId(0);
        commissionCalcOrderInfoDTO6.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO6.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO6.setAppStatus("appStatus");
        processDTO1.setRefundOrderInfo(Arrays.asList(commissionCalcOrderInfoDTO6));
        final OrderRiskCorrectDTO orderRiskCorrectDTO2 = new OrderRiskCorrectDTO();
        processDTO1.setRiskCorrectDTOS(Arrays.asList(orderRiskCorrectDTO2));
        processDTO1.setItemMap(new HashMap<>());
        final CalcCommissionItemResultDTO calcCommissionItemResultDTO4 = new CalcCommissionItemResultDTO();
        processDTO1.setItemResults(Arrays.asList(calcCommissionItemResultDTO4));
        processDTO1.setCommissionUserId("commissionUserId");
        processDTO1.setBusinessType("businessType");
        final OrderCommissionInsuredInfoDTO orderCommissionInsuredInfoDTO1 = new OrderCommissionInsuredInfoDTO();
        orderCommissionInsuredInfoDTO1.setFhOrderId("fhOrderId");
        orderCommissionInsuredInfoDTO1.setInsuredId(0);
        orderCommissionInsuredInfoDTO1.setCompanyId(0);
        orderCommissionInsuredInfoDTO1.setProductId(0);
        orderCommissionInsuredInfoDTO1.setCommissionId(0);
        final List<OrderCommissionInsuredInfoDTO> insuredInfoDTOList1 = Arrays.asList(orderCommissionInsuredInfoDTO1);
        mockCommissionExternalService.getCommissionCalcOrderParam(processDTO1, insuredInfoDTOList1);

        // Confirm SmOrderCommissionItemMapper.batchInsertCommissionItem(...).
        final SmCommissionDetailItemPO smCommissionDetailItemPO2 = new SmCommissionDetailItemPO();
        smCommissionDetailItemPO2.setOrderId("orderId");
        smCommissionDetailItemPO2.setPolicyNo("policyNo");
        smCommissionDetailItemPO2.setInsuredIdNumber("insuredIdNumber");
        smCommissionDetailItemPO2.setPolicyStatus("policyStatus");
        smCommissionDetailItemPO2.setRiskStatus("riskStatus");
        smCommissionDetailItemPO2.setPlanId(0);
        smCommissionDetailItemPO2.setRiskId(0);
        smCommissionDetailItemPO2.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smCommissionDetailItemPO2.setPlanAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO2.setAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO2.setOriginalAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO2.setTermNum(0);
        smCommissionDetailItemPO2.setCommissionType(0);
        smCommissionDetailItemPO2.setCommissionRate(new BigDecimal("0.00"));
        smCommissionDetailItemPO2.setCommissionAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO2.setCommissionUserId("commissionUserId");
        smCommissionDetailItemPO2.setBusinessType("businessType");
        smCommissionDetailItemPO2.setBusinessId("businessId");
        smCommissionDetailItemPO2.setImportFlag(0);
        final List<SmCommissionDetailItemPO> commissionConfigItemList = Arrays.asList(smCommissionDetailItemPO2);
        mockSmOrderCommissionItemMapper.batchInsertCommissionItem(commissionConfigItemList);

        // Confirm SmOrderCommissionItemMapper.batchInsertCommissionItemExceptImport(...).
        final SmCommissionDetailItemPO smCommissionDetailItemPO3 = new SmCommissionDetailItemPO();
        smCommissionDetailItemPO3.setOrderId("orderId");
        smCommissionDetailItemPO3.setPolicyNo("policyNo");
        smCommissionDetailItemPO3.setInsuredIdNumber("insuredIdNumber");
        smCommissionDetailItemPO3.setPolicyStatus("policyStatus");
        smCommissionDetailItemPO3.setRiskStatus("riskStatus");
        smCommissionDetailItemPO3.setPlanId(0);
        smCommissionDetailItemPO3.setRiskId(0);
        smCommissionDetailItemPO3.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smCommissionDetailItemPO3.setPlanAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO3.setAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO3.setOriginalAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO3.setTermNum(0);
        smCommissionDetailItemPO3.setCommissionType(0);
        smCommissionDetailItemPO3.setCommissionRate(new BigDecimal("0.00"));
        smCommissionDetailItemPO3.setCommissionAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO3.setCommissionUserId("commissionUserId");
        smCommissionDetailItemPO3.setBusinessType("businessType");
        smCommissionDetailItemPO3.setBusinessId("businessId");
        smCommissionDetailItemPO3.setImportFlag(0);
        final List<SmCommissionDetailItemPO> commissionConfigItemList1 = Arrays.asList(smCommissionDetailItemPO3);
        mockSmOrderCommissionItemMapper.batchInsertCommissionItemExceptImport(commissionConfigItemList1);

        // Confirm SmOrderCommissionDetailMapper.batchReCalcCommissionDetail(...).
        final SmCommissionDetailPO smCommissionDetailPO = new SmCommissionDetailPO();
        smCommissionDetailPO.setOrderId("orderId");
        smCommissionDetailPO.setPolicyNo("policyNo");
        smCommissionDetailPO.setInsuredIdNumber("insuredIdNumber");
        smCommissionDetailPO.setPolicyStatus("policyStatus");
        smCommissionDetailPO.setPlanId(0);
        smCommissionDetailPO.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smCommissionDetailPO.setAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setOriginalAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setPaymentRate(new BigDecimal("0.00"));
        smCommissionDetailPO.setPaymentAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setSettlementRate(new BigDecimal("0.00"));
        smCommissionDetailPO.setSettlementAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setConversionRate(new BigDecimal("0.00"));
        smCommissionDetailPO.setConversionAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setTermNum(0);
        smCommissionDetailPO.setPaymentRiskJson("paymentRiskJson");
        smCommissionDetailPO.setSettlementRiskJson("settlementRiskJson");
        smCommissionDetailPO.setConversionRiskJson("conversionRiskJson");
        smCommissionDetailPO.setPAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setSAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setCAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setCommissionUserId("commissionUserId");
        smCommissionDetailPO.setBusinessType("businessType");
        smCommissionDetailPO.setBusinessId("businessId");
        smCommissionDetailPO.setImportFlag(0);
        final List<SmCommissionDetailPO> commissionConfigDetailList = Arrays.asList(smCommissionDetailPO);
        mockSmOrderCommissionDetailMapper.batchReCalcCommissionDetail(commissionConfigDetailList);

        // Confirm SmOrderCommissionDetailMapper.batchInsertCommissionDetail(...).
        final SmCommissionDetailPO smCommissionDetailPO1 = new SmCommissionDetailPO();
        smCommissionDetailPO1.setOrderId("orderId");
        smCommissionDetailPO1.setPolicyNo("policyNo");
        smCommissionDetailPO1.setInsuredIdNumber("insuredIdNumber");
        smCommissionDetailPO1.setPolicyStatus("policyStatus");
        smCommissionDetailPO1.setPlanId(0);
        smCommissionDetailPO1.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smCommissionDetailPO1.setAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setOriginalAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setPaymentRate(new BigDecimal("0.00"));
        smCommissionDetailPO1.setPaymentAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setSettlementRate(new BigDecimal("0.00"));
        smCommissionDetailPO1.setSettlementAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setConversionRate(new BigDecimal("0.00"));
        smCommissionDetailPO1.setConversionAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setTermNum(0);
        smCommissionDetailPO1.setPaymentRiskJson("paymentRiskJson");
        smCommissionDetailPO1.setSettlementRiskJson("settlementRiskJson");
        smCommissionDetailPO1.setConversionRiskJson("conversionRiskJson");
        smCommissionDetailPO1.setPAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setSAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setCAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setCommissionUserId("commissionUserId");
        smCommissionDetailPO1.setBusinessType("businessType");
        smCommissionDetailPO1.setBusinessId("businessId");
        smCommissionDetailPO1.setImportFlag(0);
        final List<SmCommissionDetailPO> commissionConfigDetailList1 = Arrays.asList(smCommissionDetailPO1);
        mockSmOrderCommissionDetailMapper.batchInsertCommissionDetail(commissionConfigDetailList1);
    }

    @Test
    public void testCalcInsureCommission_CommissionCalcServiceCalcOldRefundDetailItemReturnsNoItems() throws Exception {
        // Setup
        final CommissionCalcProcessDTO processDTO = new CommissionCalcProcessDTO();
        processDTO.setOrderId("orderId");
        processDTO.setProductId(0);
        processDTO.setProductType("productType");
        processDTO.setMatchingTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        processDTO.setTermNum(0);
        processDTO.setCommissionConfigInfo(new HashMap<>());
        final CommissionRateDTO commissionRateDTO = new CommissionRateDTO();
        processDTO.setOldCommissionConfigInfo(Arrays.asList(commissionRateDTO));
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO.setCommissionId(0);
        commissionCalcOrderInfoDTO.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO.setAppStatus("appStatus");
        processDTO.setOrderInfo(Arrays.asList(commissionCalcOrderInfoDTO));
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO1 = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO1.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO1.setCommissionId(0);
        commissionCalcOrderInfoDTO1.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO1.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO1.setAppStatus("appStatus");
        processDTO.setRefundOrderInfo(Arrays.asList(commissionCalcOrderInfoDTO1));
        final OrderRiskCorrectDTO orderRiskCorrectDTO = new OrderRiskCorrectDTO();
        processDTO.setRiskCorrectDTOS(Arrays.asList(orderRiskCorrectDTO));
        processDTO.setItemMap(new HashMap<>());
        final CalcCommissionItemResultDTO calcCommissionItemResultDTO = new CalcCommissionItemResultDTO();
        processDTO.setItemResults(Arrays.asList(calcCommissionItemResultDTO));
        processDTO.setCommissionUserId("commissionUserId");
        processDTO.setBusinessType("businessType");

        final OrderCommissionInsuredInfoDTO orderCommissionInsuredInfoDTO = new OrderCommissionInsuredInfoDTO();
        orderCommissionInsuredInfoDTO.setFhOrderId("fhOrderId");
        orderCommissionInsuredInfoDTO.setInsuredId(0);
        orderCommissionInsuredInfoDTO.setCompanyId(0);
        orderCommissionInsuredInfoDTO.setProductId(0);
        orderCommissionInsuredInfoDTO.setCommissionId(0);
        final List<OrderCommissionInsuredInfoDTO> insuredInfoDTOList = Arrays.asList(orderCommissionInsuredInfoDTO);

        // Configure CommissionConfigQueryService.listConfigByProductId(...).
        final SystemCommissionConfig systemCommissionConfig = new SystemCommissionConfig();
        systemCommissionConfig.setType(0);
        systemCommissionConfig.setCompanyId(0);
        systemCommissionConfig.setProductId(0);
        systemCommissionConfig.setStartTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        systemCommissionConfig.setEndTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SystemCommissionConfig> systemCommissionConfigs = Arrays.asList(systemCommissionConfig);
        mockCommissionConfigQueryService.listConfigByProductId(0);

        // Configure CommissionConfigQueryService.listCalcCommissionDetailByConfigId(...).
        final CommissionCalcConfigDetailDTO commissionCalcConfigDetailDTO = new CommissionCalcConfigDetailDTO();
        commissionCalcConfigDetailDTO.setId(0);
        commissionCalcConfigDetailDTO.setConfigId(0);
        commissionCalcConfigDetailDTO.setPeriodNum(0);
        commissionCalcConfigDetailDTO.setPlanId(0);
        commissionCalcConfigDetailDTO.setRiskId(0);
        final List<CommissionCalcConfigDetailDTO> commissionCalcConfigDetailDTOS = Arrays.asList(
                commissionCalcConfigDetailDTO);
        mockCommissionConfigQueryService.listCalcCommissionDetailByConfigId(0);

        mockCommissionExternalService.isDecreaseGroupOrder("productAttrCode", "appStatus");

        // Configure SmOrderCommissionItemMapper.listCommissionItemByOrderId(...).
        final SmCommissionDetailItemPO smCommissionDetailItemPO = new SmCommissionDetailItemPO();
        smCommissionDetailItemPO.setOrderId("orderId");
        smCommissionDetailItemPO.setPolicyNo("policyNo");
        smCommissionDetailItemPO.setInsuredIdNumber("insuredIdNumber");
        smCommissionDetailItemPO.setPolicyStatus("policyStatus");
        smCommissionDetailItemPO.setRiskStatus("riskStatus");
        smCommissionDetailItemPO.setPlanId(0);
        smCommissionDetailItemPO.setRiskId(0);
        smCommissionDetailItemPO.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smCommissionDetailItemPO.setPlanAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setOriginalAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setTermNum(0);
        smCommissionDetailItemPO.setCommissionType(0);
        smCommissionDetailItemPO.setCommissionRate(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setCommissionAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setCommissionUserId("commissionUserId");
        smCommissionDetailItemPO.setBusinessType("businessType");
        smCommissionDetailItemPO.setBusinessId("businessId");
        smCommissionDetailItemPO.setImportFlag(0);
        final List<SmCommissionDetailItemPO> smCommissionDetailItemPOS = Arrays.asList(smCommissionDetailItemPO);
        mockSmOrderCommissionItemMapper.listCommissionItemByOrderId("originalFhOrderId", 0);

        // Configure CommissionExternalService.getOldConvertedConfig(...).
        final CommissionRateDTO commissionRateDTO1 = new CommissionRateDTO();
        commissionRateDTO1.setPlanId(0);
        commissionRateDTO1.setRiskId(0);
        commissionRateDTO1.setCommissionType(0);
        commissionRateDTO1.setCommissionId(0);
        commissionRateDTO1.setCommissionRate(new BigDecimal("0.00"));
        final List<CommissionRateDTO> commissionRateDTOS = Arrays.asList(commissionRateDTO1);
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO2 = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO2.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO2.setCommissionId(0);
        commissionCalcOrderInfoDTO2.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO2.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO2.setAppStatus("appStatus");
        final List<CommissionCalcOrderInfoDTO> orderInfoList = Arrays.asList(commissionCalcOrderInfoDTO2);
        mockCommissionExternalService.getOldConvertedConfig(orderInfoList);

        // Configure CommissionConfigQueryService.getOldCommissionSetting(...).
        final CommissionRateDTO commissionRateDTO2 = new CommissionRateDTO();
        commissionRateDTO2.setPlanId(0);
        commissionRateDTO2.setRiskId(0);
        commissionRateDTO2.setCommissionType(0);
        commissionRateDTO2.setCommissionId(0);
        commissionRateDTO2.setCommissionRate(new BigDecimal("0.00"));
        final List<CommissionRateDTO> commissionRateDTOS1 = Arrays.asList(commissionRateDTO2);
        mockCommissionConfigQueryService.getOldCommissionSetting(Arrays.asList(0));

        // Configure CommissionCalcService.calc(...).
        final CalcCommissionItemResultDTO calcCommissionItemResultDTO1 = new CalcCommissionItemResultDTO();
        calcCommissionItemResultDTO1.setOrderId("orderId");
        calcCommissionItemResultDTO1.setPolicyNo("policyNo");
        calcCommissionItemResultDTO1.setInsuredIdNumber("insuredIdNumber");
        calcCommissionItemResultDTO1.setPolicyStatus("policyStatus");
        calcCommissionItemResultDTO1.setRiskStatus("riskStatus");
        final List<CalcCommissionItemResultDTO> calcCommissionItemResultDTOS = Arrays.asList(
                calcCommissionItemResultDTO1);
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO3 = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO3.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO3.setCommissionId(0);
        commissionCalcOrderInfoDTO3.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO3.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO3.setAppStatus("appStatus");
        final List<CommissionCalcOrderInfoDTO> orderInfoDTOList = Arrays.asList(commissionCalcOrderInfoDTO3);
        final CommissionCalcConfigInfoDTO configInfoDTO = new CommissionCalcConfigInfoDTO();
        configInfoDTO.setId(0);
        configInfoDTO.setType(0);
        configInfoDTO.setCompanyId(0);
        final CommissionCalcConfigDetailDTO commissionCalcConfigDetailDTO1 = new CommissionCalcConfigDetailDTO();
        commissionCalcConfigDetailDTO1.setPeriodNum(0);
        configInfoDTO.setConfigDetailDTOList(Arrays.asList(commissionCalcConfigDetailDTO1));
        mockCommissionCalcService.calc(0, 0, orderInfoDTOList, configInfoDTO, new CalcErrorDTO("errorMsg","errorMsgNoAlarm"),
                "commissionUserId", "businessType");

        // Configure CommissionCalcService.calcCorrectCommission(...).
        final CalcCommissionItemResultDTO calcCommissionItemResultDTO2 = new CalcCommissionItemResultDTO();
        calcCommissionItemResultDTO2.setOrderId("orderId");
        calcCommissionItemResultDTO2.setPolicyNo("policyNo");
        calcCommissionItemResultDTO2.setInsuredIdNumber("insuredIdNumber");
        calcCommissionItemResultDTO2.setPolicyStatus("policyStatus");
        calcCommissionItemResultDTO2.setRiskStatus("riskStatus");
        final List<CalcCommissionItemResultDTO> calcCommissionItemResultDTOS1 = Arrays.asList(
                calcCommissionItemResultDTO2);
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO4 = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO4.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO4.setCommissionId(0);
        commissionCalcOrderInfoDTO4.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO4.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO4.setAppStatus("appStatus");
        final List<CommissionCalcOrderInfoDTO> orderInfoDTOList1 = Arrays.asList(commissionCalcOrderInfoDTO4);
        final OrderRiskCorrectDTO orderRiskCorrectDTO1 = new OrderRiskCorrectDTO();
        orderRiskCorrectDTO1.setOrderId("orderId");
        orderRiskCorrectDTO1.setPolicyNo("policyNo");
        orderRiskCorrectDTO1.setCorrectId("correctId");
        orderRiskCorrectDTO1.setCorrectType("correctType");
        orderRiskCorrectDTO1.setRiskId(0);
        final List<OrderRiskCorrectDTO> correctDTOList = Arrays.asList(orderRiskCorrectDTO1);
        final SmCommissionDetailItemPO smCommissionDetailItemPO1 = new SmCommissionDetailItemPO();
        smCommissionDetailItemPO1.setOrderId("orderId");
        smCommissionDetailItemPO1.setPolicyNo("policyNo");
        smCommissionDetailItemPO1.setInsuredIdNumber("insuredIdNumber");
        smCommissionDetailItemPO1.setPolicyStatus("policyStatus");
        smCommissionDetailItemPO1.setRiskStatus("riskStatus");
        smCommissionDetailItemPO1.setPlanId(0);
        smCommissionDetailItemPO1.setRiskId(0);
        smCommissionDetailItemPO1.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smCommissionDetailItemPO1.setPlanAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO1.setAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO1.setOriginalAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO1.setTermNum(0);
        smCommissionDetailItemPO1.setCommissionType(0);
        smCommissionDetailItemPO1.setCommissionRate(new BigDecimal("0.00"));
        smCommissionDetailItemPO1.setCommissionAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO1.setCommissionUserId("commissionUserId");
        smCommissionDetailItemPO1.setBusinessType("businessType");
        smCommissionDetailItemPO1.setBusinessId("businessId");
        smCommissionDetailItemPO1.setImportFlag(0);
        final List<SmCommissionDetailItemPO> itemPOs = Arrays.asList(smCommissionDetailItemPO1);
        mockCommissionCalcService.calcCorrectCommission(0, orderInfoDTOList1, correctDTOList, itemPOs,
                new CalcErrorDTO("errorMsg","errorMsgNoAlarm"));

        // Configure CommissionCalcService.calcNewRefundDetailItem(...).
        final CommissionCalcOrderInfoDTO orderInfo = new CommissionCalcOrderInfoDTO();
        orderInfo.setProductAttrCode("productAttrCode");
        orderInfo.setCommissionId(0);
        orderInfo.setOriginalFhOrderId("originalFhOrderId");
        orderInfo.setOriginalCommissionId(0);
        orderInfo.setAppStatus("appStatus");
        mockCommissionCalcService.calcNewRefundDetailItem(orderInfo, new HashMap<>());

        // Configure CommissionCalcService.calcOldRefundDetailItem(...).
        final CommissionCalcOrderInfoDTO orderInfo1 = new CommissionCalcOrderInfoDTO();
        orderInfo1.setProductAttrCode("productAttrCode");
        orderInfo1.setCommissionId(0);
        orderInfo1.setOriginalFhOrderId("originalFhOrderId");
        orderInfo1.setOriginalCommissionId(0);
        orderInfo1.setAppStatus("appStatus");
        final CommissionRateDTO commissionRateDTO3 = new CommissionRateDTO();
        commissionRateDTO3.setPlanId(0);
        commissionRateDTO3.setRiskId(0);
        commissionRateDTO3.setCommissionType(0);
        commissionRateDTO3.setCommissionId(0);
        commissionRateDTO3.setCommissionRate(new BigDecimal("0.00"));
        final List<CommissionRateDTO> oldCommissionRates = Arrays.asList(commissionRateDTO3);
        mockCommissionCalcService.calcOldRefundDetailItem(orderInfo1, oldCommissionRates,
                "commissionUserId");

        // Run the test
        commissionCalcProcessServiceUnderTest.calcInsureCommission(processDTO, insuredInfoDTOList, false);

        // Verify the results
        // Confirm CommissionExternalService.getCommissionCalcOrderParam(...).
        final CommissionCalcProcessDTO processDTO1 = new CommissionCalcProcessDTO();
        processDTO1.setOrderId("orderId");
        processDTO1.setProductId(0);
        processDTO1.setProductType("productType");
        processDTO1.setMatchingTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        processDTO1.setTermNum(0);
        processDTO1.setCommissionConfigInfo(new HashMap<>());
        final CommissionRateDTO commissionRateDTO4 = new CommissionRateDTO();
        processDTO1.setOldCommissionConfigInfo(Arrays.asList(commissionRateDTO4));
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO5 = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO5.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO5.setCommissionId(0);
        commissionCalcOrderInfoDTO5.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO5.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO5.setAppStatus("appStatus");
        processDTO1.setOrderInfo(Arrays.asList(commissionCalcOrderInfoDTO5));
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO6 = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO6.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO6.setCommissionId(0);
        commissionCalcOrderInfoDTO6.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO6.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO6.setAppStatus("appStatus");
        processDTO1.setRefundOrderInfo(Arrays.asList(commissionCalcOrderInfoDTO6));
        final OrderRiskCorrectDTO orderRiskCorrectDTO2 = new OrderRiskCorrectDTO();
        processDTO1.setRiskCorrectDTOS(Arrays.asList(orderRiskCorrectDTO2));
        processDTO1.setItemMap(new HashMap<>());
        final CalcCommissionItemResultDTO calcCommissionItemResultDTO3 = new CalcCommissionItemResultDTO();
        processDTO1.setItemResults(Arrays.asList(calcCommissionItemResultDTO3));
        processDTO1.setCommissionUserId("commissionUserId");
        processDTO1.setBusinessType("businessType");
        final OrderCommissionInsuredInfoDTO orderCommissionInsuredInfoDTO1 = new OrderCommissionInsuredInfoDTO();
        orderCommissionInsuredInfoDTO1.setFhOrderId("fhOrderId");
        orderCommissionInsuredInfoDTO1.setInsuredId(0);
        orderCommissionInsuredInfoDTO1.setCompanyId(0);
        orderCommissionInsuredInfoDTO1.setProductId(0);
        orderCommissionInsuredInfoDTO1.setCommissionId(0);
        final List<OrderCommissionInsuredInfoDTO> insuredInfoDTOList1 = Arrays.asList(orderCommissionInsuredInfoDTO1);
        mockCommissionExternalService.getCommissionCalcOrderParam(processDTO1, insuredInfoDTOList1);

        // Confirm SmOrderCommissionItemMapper.batchInsertCommissionItem(...).
        final SmCommissionDetailItemPO smCommissionDetailItemPO2 = new SmCommissionDetailItemPO();
        smCommissionDetailItemPO2.setOrderId("orderId");
        smCommissionDetailItemPO2.setPolicyNo("policyNo");
        smCommissionDetailItemPO2.setInsuredIdNumber("insuredIdNumber");
        smCommissionDetailItemPO2.setPolicyStatus("policyStatus");
        smCommissionDetailItemPO2.setRiskStatus("riskStatus");
        smCommissionDetailItemPO2.setPlanId(0);
        smCommissionDetailItemPO2.setRiskId(0);
        smCommissionDetailItemPO2.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smCommissionDetailItemPO2.setPlanAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO2.setAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO2.setOriginalAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO2.setTermNum(0);
        smCommissionDetailItemPO2.setCommissionType(0);
        smCommissionDetailItemPO2.setCommissionRate(new BigDecimal("0.00"));
        smCommissionDetailItemPO2.setCommissionAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO2.setCommissionUserId("commissionUserId");
        smCommissionDetailItemPO2.setBusinessType("businessType");
        smCommissionDetailItemPO2.setBusinessId("businessId");
        smCommissionDetailItemPO2.setImportFlag(0);
        final List<SmCommissionDetailItemPO> commissionConfigItemList = Arrays.asList(smCommissionDetailItemPO2);
        mockSmOrderCommissionItemMapper.batchInsertCommissionItem(commissionConfigItemList);

        // Confirm SmOrderCommissionItemMapper.batchInsertCommissionItemExceptImport(...).
        final SmCommissionDetailItemPO smCommissionDetailItemPO3 = new SmCommissionDetailItemPO();
        smCommissionDetailItemPO3.setOrderId("orderId");
        smCommissionDetailItemPO3.setPolicyNo("policyNo");
        smCommissionDetailItemPO3.setInsuredIdNumber("insuredIdNumber");
        smCommissionDetailItemPO3.setPolicyStatus("policyStatus");
        smCommissionDetailItemPO3.setRiskStatus("riskStatus");
        smCommissionDetailItemPO3.setPlanId(0);
        smCommissionDetailItemPO3.setRiskId(0);
        smCommissionDetailItemPO3.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smCommissionDetailItemPO3.setPlanAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO3.setAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO3.setOriginalAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO3.setTermNum(0);
        smCommissionDetailItemPO3.setCommissionType(0);
        smCommissionDetailItemPO3.setCommissionRate(new BigDecimal("0.00"));
        smCommissionDetailItemPO3.setCommissionAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO3.setCommissionUserId("commissionUserId");
        smCommissionDetailItemPO3.setBusinessType("businessType");
        smCommissionDetailItemPO3.setBusinessId("businessId");
        smCommissionDetailItemPO3.setImportFlag(0);
        final List<SmCommissionDetailItemPO> commissionConfigItemList1 = Arrays.asList(smCommissionDetailItemPO3);
        mockSmOrderCommissionItemMapper.batchInsertCommissionItemExceptImport(commissionConfigItemList1);

        // Confirm SmOrderCommissionDetailMapper.batchReCalcCommissionDetail(...).
        final SmCommissionDetailPO smCommissionDetailPO = new SmCommissionDetailPO();
        smCommissionDetailPO.setOrderId("orderId");
        smCommissionDetailPO.setPolicyNo("policyNo");
        smCommissionDetailPO.setInsuredIdNumber("insuredIdNumber");
        smCommissionDetailPO.setPolicyStatus("policyStatus");
        smCommissionDetailPO.setPlanId(0);
        smCommissionDetailPO.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smCommissionDetailPO.setAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setOriginalAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setPaymentRate(new BigDecimal("0.00"));
        smCommissionDetailPO.setPaymentAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setSettlementRate(new BigDecimal("0.00"));
        smCommissionDetailPO.setSettlementAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setConversionRate(new BigDecimal("0.00"));
        smCommissionDetailPO.setConversionAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setTermNum(0);
        smCommissionDetailPO.setPaymentRiskJson("paymentRiskJson");
        smCommissionDetailPO.setSettlementRiskJson("settlementRiskJson");
        smCommissionDetailPO.setConversionRiskJson("conversionRiskJson");
        smCommissionDetailPO.setPAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setSAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setCAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setCommissionUserId("commissionUserId");
        smCommissionDetailPO.setBusinessType("businessType");
        smCommissionDetailPO.setBusinessId("businessId");
        smCommissionDetailPO.setImportFlag(0);
        final List<SmCommissionDetailPO> commissionConfigDetailList = Arrays.asList(smCommissionDetailPO);
        mockSmOrderCommissionDetailMapper.batchReCalcCommissionDetail(commissionConfigDetailList);

        // Confirm SmOrderCommissionDetailMapper.batchInsertCommissionDetail(...).
        final SmCommissionDetailPO smCommissionDetailPO1 = new SmCommissionDetailPO();
        smCommissionDetailPO1.setOrderId("orderId");
        smCommissionDetailPO1.setPolicyNo("policyNo");
        smCommissionDetailPO1.setInsuredIdNumber("insuredIdNumber");
        smCommissionDetailPO1.setPolicyStatus("policyStatus");
        smCommissionDetailPO1.setPlanId(0);
        smCommissionDetailPO1.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smCommissionDetailPO1.setAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setOriginalAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setPaymentRate(new BigDecimal("0.00"));
        smCommissionDetailPO1.setPaymentAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setSettlementRate(new BigDecimal("0.00"));
        smCommissionDetailPO1.setSettlementAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setConversionRate(new BigDecimal("0.00"));
        smCommissionDetailPO1.setConversionAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setTermNum(0);
        smCommissionDetailPO1.setPaymentRiskJson("paymentRiskJson");
        smCommissionDetailPO1.setSettlementRiskJson("settlementRiskJson");
        smCommissionDetailPO1.setConversionRiskJson("conversionRiskJson");
        smCommissionDetailPO1.setPAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setSAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setCAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setCommissionUserId("commissionUserId");
        smCommissionDetailPO1.setBusinessType("businessType");
        smCommissionDetailPO1.setBusinessId("businessId");
        smCommissionDetailPO1.setImportFlag(0);
        final List<SmCommissionDetailPO> commissionConfigDetailList1 = Arrays.asList(smCommissionDetailPO1);
        mockSmOrderCommissionDetailMapper.batchInsertCommissionDetail(commissionConfigDetailList1);
    }

    @Test
    public void testQueryCommissionDetailItem() throws Exception {
        // Setup
        final CommissionCalcProcessDTO processDTO = new CommissionCalcProcessDTO();
        processDTO.setOrderId("orderId");
        processDTO.setProductId(0);
        processDTO.setProductType("productType");
        processDTO.setMatchingTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        processDTO.setTermNum(0);
        processDTO.setCommissionConfigInfo(new HashMap<>());
        final CommissionRateDTO commissionRateDTO = new CommissionRateDTO();
        processDTO.setOldCommissionConfigInfo(Arrays.asList(commissionRateDTO));
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO.setCommissionId(0);
        commissionCalcOrderInfoDTO.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO.setAppStatus("appStatus");
        processDTO.setOrderInfo(Arrays.asList(commissionCalcOrderInfoDTO));
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO1 = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO1.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO1.setCommissionId(0);
        commissionCalcOrderInfoDTO1.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO1.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO1.setAppStatus("appStatus");
        processDTO.setRefundOrderInfo(Arrays.asList(commissionCalcOrderInfoDTO1));
        final OrderRiskCorrectDTO orderRiskCorrectDTO = new OrderRiskCorrectDTO();
        processDTO.setRiskCorrectDTOS(Arrays.asList(orderRiskCorrectDTO));
        processDTO.setItemMap(new HashMap<>());
        final CalcCommissionItemResultDTO calcCommissionItemResultDTO = new CalcCommissionItemResultDTO();
        processDTO.setItemResults(Arrays.asList(calcCommissionItemResultDTO));
        processDTO.setCommissionUserId("commissionUserId");
        processDTO.setBusinessType("businessType");

        mockCommissionExternalService.isDecreaseGroupOrder("productAttrCode", "appStatus");

        // Configure SmOrderCommissionItemMapper.listCommissionItemByOrderId(...).
        final SmCommissionDetailItemPO smCommissionDetailItemPO = new SmCommissionDetailItemPO();
        smCommissionDetailItemPO.setOrderId("orderId");
        smCommissionDetailItemPO.setPolicyNo("policyNo");
        smCommissionDetailItemPO.setInsuredIdNumber("insuredIdNumber");
        smCommissionDetailItemPO.setPolicyStatus("policyStatus");
        smCommissionDetailItemPO.setRiskStatus("riskStatus");
        smCommissionDetailItemPO.setPlanId(0);
        smCommissionDetailItemPO.setRiskId(0);
        smCommissionDetailItemPO.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smCommissionDetailItemPO.setPlanAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setOriginalAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setTermNum(0);
        smCommissionDetailItemPO.setCommissionType(0);
        smCommissionDetailItemPO.setCommissionRate(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setCommissionAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setCommissionUserId("commissionUserId");
        smCommissionDetailItemPO.setBusinessType("businessType");
        smCommissionDetailItemPO.setBusinessId("businessId");
        smCommissionDetailItemPO.setImportFlag(0);
        final List<SmCommissionDetailItemPO> smCommissionDetailItemPOS = Arrays.asList(smCommissionDetailItemPO);
        mockSmOrderCommissionItemMapper.listCommissionItemByOrderId("originalFhOrderId", 0);

        // Run the test
        commissionCalcProcessServiceUnderTest.queryCommissionDetailItem(processDTO);

        // Verify the results
    }

    @Test
    public void testQueryCommissionDetailItem_SmOrderCommissionItemMapperReturnsNoItems() throws Exception {
        // Setup
        final CommissionCalcProcessDTO processDTO = new CommissionCalcProcessDTO();
        processDTO.setOrderId("orderId");
        processDTO.setProductId(0);
        processDTO.setProductType("productType");
        processDTO.setMatchingTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        processDTO.setTermNum(0);
        processDTO.setCommissionConfigInfo(new HashMap<>());
        final CommissionRateDTO commissionRateDTO = new CommissionRateDTO();
        processDTO.setOldCommissionConfigInfo(Arrays.asList(commissionRateDTO));
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO.setCommissionId(0);
        commissionCalcOrderInfoDTO.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO.setAppStatus("appStatus");
        processDTO.setOrderInfo(Arrays.asList(commissionCalcOrderInfoDTO));
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO1 = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO1.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO1.setCommissionId(0);
        commissionCalcOrderInfoDTO1.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO1.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO1.setAppStatus("appStatus");
        processDTO.setRefundOrderInfo(Arrays.asList(commissionCalcOrderInfoDTO1));
        final OrderRiskCorrectDTO orderRiskCorrectDTO = new OrderRiskCorrectDTO();
        processDTO.setRiskCorrectDTOS(Arrays.asList(orderRiskCorrectDTO));
        processDTO.setItemMap(new HashMap<>());
        final CalcCommissionItemResultDTO calcCommissionItemResultDTO = new CalcCommissionItemResultDTO();
        processDTO.setItemResults(Arrays.asList(calcCommissionItemResultDTO));
        processDTO.setCommissionUserId("commissionUserId");
        processDTO.setBusinessType("businessType");

        mockCommissionExternalService.isDecreaseGroupOrder("productAttrCode", "appStatus");
        mockSmOrderCommissionItemMapper.listCommissionItemByOrderId("originalFhOrderId", 0);

        // Run the test
        commissionCalcProcessServiceUnderTest.queryCommissionDetailItem(processDTO);

        // Verify the results
    }

    @Test
    public void testCalc() throws Exception {
        // Setup
        final CommissionCalcProcessDTO processDTO = new CommissionCalcProcessDTO();
        processDTO.setOrderId("orderId");
        processDTO.setProductId(0);
        processDTO.setProductType("productType");
        processDTO.setMatchingTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        processDTO.setTermNum(0);
        processDTO.setCommissionConfigInfo(new HashMap<>());
        final CommissionRateDTO commissionRateDTO = new CommissionRateDTO();
        processDTO.setOldCommissionConfigInfo(Arrays.asList(commissionRateDTO));
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO.setCommissionId(0);
        commissionCalcOrderInfoDTO.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO.setAppStatus("appStatus");
        processDTO.setOrderInfo(Arrays.asList(commissionCalcOrderInfoDTO));
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO1 = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO1.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO1.setCommissionId(0);
        commissionCalcOrderInfoDTO1.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO1.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO1.setAppStatus("appStatus");
        processDTO.setRefundOrderInfo(Arrays.asList(commissionCalcOrderInfoDTO1));
        final OrderRiskCorrectDTO orderRiskCorrectDTO = new OrderRiskCorrectDTO();
        processDTO.setRiskCorrectDTOS(Arrays.asList(orderRiskCorrectDTO));
        processDTO.setItemMap(new HashMap<>());
        final CalcCommissionItemResultDTO calcCommissionItemResultDTO = new CalcCommissionItemResultDTO();
        processDTO.setItemResults(Arrays.asList(calcCommissionItemResultDTO));
        processDTO.setCommissionUserId("commissionUserId");
        processDTO.setBusinessType("businessType");

        // Configure CommissionCalcService.calc(...).
        final CalcCommissionItemResultDTO calcCommissionItemResultDTO1 = new CalcCommissionItemResultDTO();
        calcCommissionItemResultDTO1.setOrderId("orderId");
        calcCommissionItemResultDTO1.setPolicyNo("policyNo");
        calcCommissionItemResultDTO1.setInsuredIdNumber("insuredIdNumber");
        calcCommissionItemResultDTO1.setPolicyStatus("policyStatus");
        calcCommissionItemResultDTO1.setRiskStatus("riskStatus");
        final List<CalcCommissionItemResultDTO> calcCommissionItemResultDTOS = Arrays.asList(
                calcCommissionItemResultDTO1);
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO2 = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO2.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO2.setCommissionId(0);
        commissionCalcOrderInfoDTO2.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO2.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO2.setAppStatus("appStatus");
        final List<CommissionCalcOrderInfoDTO> orderInfoDTOList = Arrays.asList(commissionCalcOrderInfoDTO2);
        final CommissionCalcConfigInfoDTO configInfoDTO = new CommissionCalcConfigInfoDTO();
        configInfoDTO.setId(0);
        configInfoDTO.setType(0);
        configInfoDTO.setCompanyId(0);
        final CommissionCalcConfigDetailDTO commissionCalcConfigDetailDTO = new CommissionCalcConfigDetailDTO();
        commissionCalcConfigDetailDTO.setPeriodNum(0);
        configInfoDTO.setConfigDetailDTOList(Arrays.asList(commissionCalcConfigDetailDTO));
        mockCommissionCalcService.calc(0, 0, orderInfoDTOList, configInfoDTO, new CalcErrorDTO("errorMsg","errorMsgNoAlarm"),
                "commissionUserId", "businessType");

        // Configure CommissionCalcService.calcCorrectCommission(...).
        final CalcCommissionItemResultDTO calcCommissionItemResultDTO2 = new CalcCommissionItemResultDTO();
        calcCommissionItemResultDTO2.setOrderId("orderId");
        calcCommissionItemResultDTO2.setPolicyNo("policyNo");
        calcCommissionItemResultDTO2.setInsuredIdNumber("insuredIdNumber");
        calcCommissionItemResultDTO2.setPolicyStatus("policyStatus");
        calcCommissionItemResultDTO2.setRiskStatus("riskStatus");
        final List<CalcCommissionItemResultDTO> calcCommissionItemResultDTOS1 = Arrays.asList(
                calcCommissionItemResultDTO2);
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO3 = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO3.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO3.setCommissionId(0);
        commissionCalcOrderInfoDTO3.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO3.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO3.setAppStatus("appStatus");
        final List<CommissionCalcOrderInfoDTO> orderInfoDTOList1 = Arrays.asList(commissionCalcOrderInfoDTO3);
        final OrderRiskCorrectDTO orderRiskCorrectDTO1 = new OrderRiskCorrectDTO();
        orderRiskCorrectDTO1.setOrderId("orderId");
        orderRiskCorrectDTO1.setPolicyNo("policyNo");
        orderRiskCorrectDTO1.setCorrectId("correctId");
        orderRiskCorrectDTO1.setCorrectType("correctType");
        orderRiskCorrectDTO1.setRiskId(0);
        final List<OrderRiskCorrectDTO> correctDTOList = Arrays.asList(orderRiskCorrectDTO1);
        final SmCommissionDetailItemPO smCommissionDetailItemPO = new SmCommissionDetailItemPO();
        smCommissionDetailItemPO.setOrderId("orderId");
        smCommissionDetailItemPO.setPolicyNo("policyNo");
        smCommissionDetailItemPO.setInsuredIdNumber("insuredIdNumber");
        smCommissionDetailItemPO.setPolicyStatus("policyStatus");
        smCommissionDetailItemPO.setRiskStatus("riskStatus");
        smCommissionDetailItemPO.setPlanId(0);
        smCommissionDetailItemPO.setRiskId(0);
        smCommissionDetailItemPO.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smCommissionDetailItemPO.setPlanAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setOriginalAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setTermNum(0);
        smCommissionDetailItemPO.setCommissionType(0);
        smCommissionDetailItemPO.setCommissionRate(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setCommissionAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setCommissionUserId("commissionUserId");
        smCommissionDetailItemPO.setBusinessType("businessType");
        smCommissionDetailItemPO.setBusinessId("businessId");
        smCommissionDetailItemPO.setImportFlag(0);
        final List<SmCommissionDetailItemPO> itemPOs = Arrays.asList(smCommissionDetailItemPO);
        mockCommissionCalcService.calcCorrectCommission(0, orderInfoDTOList1, correctDTOList, itemPOs,
                new CalcErrorDTO("errorMsg","errorMsgNoAlarm"));

        // Configure CommissionCalcService.calcNewRefundDetailItem(...).
        final CalcCommissionItemResultDTO calcCommissionItemResultDTO3 = new CalcCommissionItemResultDTO();
        calcCommissionItemResultDTO3.setOrderId("orderId");
        calcCommissionItemResultDTO3.setPolicyNo("policyNo");
        calcCommissionItemResultDTO3.setInsuredIdNumber("insuredIdNumber");
        calcCommissionItemResultDTO3.setPolicyStatus("policyStatus");
        calcCommissionItemResultDTO3.setRiskStatus("riskStatus");
        final List<CalcCommissionItemResultDTO> calcCommissionItemResultDTOS2 = Arrays.asList(
                calcCommissionItemResultDTO3);
        final CommissionCalcOrderInfoDTO orderInfo = new CommissionCalcOrderInfoDTO();
        orderInfo.setProductAttrCode("productAttrCode");
        orderInfo.setCommissionId(0);
        orderInfo.setOriginalFhOrderId("originalFhOrderId");
        orderInfo.setOriginalCommissionId(0);
        orderInfo.setAppStatus("appStatus");
        mockCommissionCalcService.calcNewRefundDetailItem(orderInfo, new HashMap<>());

        // Run the test
        commissionCalcProcessServiceUnderTest.calc(processDTO);

        // Verify the results
    }

    @Test
    public void testCalc_CommissionCalcServiceCalcReturnsNoItems() throws Exception {
        // Setup
        final CommissionCalcProcessDTO processDTO = new CommissionCalcProcessDTO();
        processDTO.setOrderId("orderId");
        processDTO.setProductId(0);
        processDTO.setProductType("productType");
        processDTO.setMatchingTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        processDTO.setTermNum(0);
        processDTO.setCommissionConfigInfo(new HashMap<>());
        final CommissionRateDTO commissionRateDTO = new CommissionRateDTO();
        processDTO.setOldCommissionConfigInfo(Arrays.asList(commissionRateDTO));
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO.setCommissionId(0);
        commissionCalcOrderInfoDTO.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO.setAppStatus("appStatus");
        processDTO.setOrderInfo(Arrays.asList(commissionCalcOrderInfoDTO));
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO1 = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO1.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO1.setCommissionId(0);
        commissionCalcOrderInfoDTO1.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO1.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO1.setAppStatus("appStatus");
        processDTO.setRefundOrderInfo(Arrays.asList(commissionCalcOrderInfoDTO1));
        final OrderRiskCorrectDTO orderRiskCorrectDTO = new OrderRiskCorrectDTO();
        processDTO.setRiskCorrectDTOS(Arrays.asList(orderRiskCorrectDTO));
        processDTO.setItemMap(new HashMap<>());
        final CalcCommissionItemResultDTO calcCommissionItemResultDTO = new CalcCommissionItemResultDTO();
        processDTO.setItemResults(Arrays.asList(calcCommissionItemResultDTO));
        processDTO.setCommissionUserId("commissionUserId");
        processDTO.setBusinessType("businessType");

        // Configure CommissionCalcService.calc(...).
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO2 = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO2.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO2.setCommissionId(0);
        commissionCalcOrderInfoDTO2.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO2.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO2.setAppStatus("appStatus");
        final List<CommissionCalcOrderInfoDTO> orderInfoDTOList = Arrays.asList(commissionCalcOrderInfoDTO2);
        final CommissionCalcConfigInfoDTO configInfoDTO = new CommissionCalcConfigInfoDTO();
        configInfoDTO.setId(0);
        configInfoDTO.setType(0);
        configInfoDTO.setCompanyId(0);
        final CommissionCalcConfigDetailDTO commissionCalcConfigDetailDTO = new CommissionCalcConfigDetailDTO();
        commissionCalcConfigDetailDTO.setPeriodNum(0);
        configInfoDTO.setConfigDetailDTOList(Arrays.asList(commissionCalcConfigDetailDTO));
        mockCommissionCalcService.calc(0, 0, orderInfoDTOList, configInfoDTO, new CalcErrorDTO("errorMsg","errorMsgNoAlarm"),
                "commissionUserId", "businessType");

        // Configure CommissionCalcService.calcCorrectCommission(...).
        final CalcCommissionItemResultDTO calcCommissionItemResultDTO1 = new CalcCommissionItemResultDTO();
        calcCommissionItemResultDTO1.setOrderId("orderId");
        calcCommissionItemResultDTO1.setPolicyNo("policyNo");
        calcCommissionItemResultDTO1.setInsuredIdNumber("insuredIdNumber");
        calcCommissionItemResultDTO1.setPolicyStatus("policyStatus");
        calcCommissionItemResultDTO1.setRiskStatus("riskStatus");
        final List<CalcCommissionItemResultDTO> calcCommissionItemResultDTOS = Arrays.asList(
                calcCommissionItemResultDTO1);
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO3 = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO3.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO3.setCommissionId(0);
        commissionCalcOrderInfoDTO3.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO3.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO3.setAppStatus("appStatus");
        final List<CommissionCalcOrderInfoDTO> orderInfoDTOList1 = Arrays.asList(commissionCalcOrderInfoDTO3);
        final OrderRiskCorrectDTO orderRiskCorrectDTO1 = new OrderRiskCorrectDTO();
        orderRiskCorrectDTO1.setOrderId("orderId");
        orderRiskCorrectDTO1.setPolicyNo("policyNo");
        orderRiskCorrectDTO1.setCorrectId("correctId");
        orderRiskCorrectDTO1.setCorrectType("correctType");
        orderRiskCorrectDTO1.setRiskId(0);
        final List<OrderRiskCorrectDTO> correctDTOList = Arrays.asList(orderRiskCorrectDTO1);
        final SmCommissionDetailItemPO smCommissionDetailItemPO = new SmCommissionDetailItemPO();
        smCommissionDetailItemPO.setOrderId("orderId");
        smCommissionDetailItemPO.setPolicyNo("policyNo");
        smCommissionDetailItemPO.setInsuredIdNumber("insuredIdNumber");
        smCommissionDetailItemPO.setPolicyStatus("policyStatus");
        smCommissionDetailItemPO.setRiskStatus("riskStatus");
        smCommissionDetailItemPO.setPlanId(0);
        smCommissionDetailItemPO.setRiskId(0);
        smCommissionDetailItemPO.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smCommissionDetailItemPO.setPlanAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setOriginalAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setTermNum(0);
        smCommissionDetailItemPO.setCommissionType(0);
        smCommissionDetailItemPO.setCommissionRate(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setCommissionAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setCommissionUserId("commissionUserId");
        smCommissionDetailItemPO.setBusinessType("businessType");
        smCommissionDetailItemPO.setBusinessId("businessId");
        smCommissionDetailItemPO.setImportFlag(0);
        final List<SmCommissionDetailItemPO> itemPOs = Arrays.asList(smCommissionDetailItemPO);
        mockCommissionCalcService.calcCorrectCommission(0, orderInfoDTOList1, correctDTOList, itemPOs,
                new CalcErrorDTO("errorMsg","errorMsgNoAlarm"));

        // Configure CommissionCalcService.calcNewRefundDetailItem(...).
        final CalcCommissionItemResultDTO calcCommissionItemResultDTO2 = new CalcCommissionItemResultDTO();
        calcCommissionItemResultDTO2.setOrderId("orderId");
        calcCommissionItemResultDTO2.setPolicyNo("policyNo");
        calcCommissionItemResultDTO2.setInsuredIdNumber("insuredIdNumber");
        calcCommissionItemResultDTO2.setPolicyStatus("policyStatus");
        calcCommissionItemResultDTO2.setRiskStatus("riskStatus");
        final List<CalcCommissionItemResultDTO> calcCommissionItemResultDTOS1 = Arrays.asList(
                calcCommissionItemResultDTO2);
        final CommissionCalcOrderInfoDTO orderInfo = new CommissionCalcOrderInfoDTO();
        orderInfo.setProductAttrCode("productAttrCode");
        orderInfo.setCommissionId(0);
        orderInfo.setOriginalFhOrderId("originalFhOrderId");
        orderInfo.setOriginalCommissionId(0);
        orderInfo.setAppStatus("appStatus");
        mockCommissionCalcService.calcNewRefundDetailItem(orderInfo, new HashMap<>());

        // Run the test
        commissionCalcProcessServiceUnderTest.calc(processDTO);

        // Verify the results
    }

    @Test
    public void testCalc_CommissionCalcServiceCalcCorrectCommissionReturnsNoItems() throws Exception {
        // Setup
        final CommissionCalcProcessDTO processDTO = new CommissionCalcProcessDTO();
        processDTO.setOrderId("orderId");
        processDTO.setProductId(0);
        processDTO.setProductType("productType");
        processDTO.setMatchingTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        processDTO.setTermNum(0);
        processDTO.setCommissionConfigInfo(new HashMap<>());
        final CommissionRateDTO commissionRateDTO = new CommissionRateDTO();
        processDTO.setOldCommissionConfigInfo(Arrays.asList(commissionRateDTO));
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO.setCommissionId(0);
        commissionCalcOrderInfoDTO.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO.setAppStatus("appStatus");
        processDTO.setOrderInfo(Arrays.asList(commissionCalcOrderInfoDTO));
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO1 = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO1.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO1.setCommissionId(0);
        commissionCalcOrderInfoDTO1.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO1.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO1.setAppStatus("appStatus");
        processDTO.setRefundOrderInfo(Arrays.asList(commissionCalcOrderInfoDTO1));
        final OrderRiskCorrectDTO orderRiskCorrectDTO = new OrderRiskCorrectDTO();
        processDTO.setRiskCorrectDTOS(Arrays.asList(orderRiskCorrectDTO));
        processDTO.setItemMap(new HashMap<>());
        final CalcCommissionItemResultDTO calcCommissionItemResultDTO = new CalcCommissionItemResultDTO();
        processDTO.setItemResults(Arrays.asList(calcCommissionItemResultDTO));
        processDTO.setCommissionUserId("commissionUserId");
        processDTO.setBusinessType("businessType");

        // Configure CommissionCalcService.calc(...).
        final CalcCommissionItemResultDTO calcCommissionItemResultDTO1 = new CalcCommissionItemResultDTO();
        calcCommissionItemResultDTO1.setOrderId("orderId");
        calcCommissionItemResultDTO1.setPolicyNo("policyNo");
        calcCommissionItemResultDTO1.setInsuredIdNumber("insuredIdNumber");
        calcCommissionItemResultDTO1.setPolicyStatus("policyStatus");
        calcCommissionItemResultDTO1.setRiskStatus("riskStatus");
        final List<CalcCommissionItemResultDTO> calcCommissionItemResultDTOS = Arrays.asList(
                calcCommissionItemResultDTO1);
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO2 = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO2.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO2.setCommissionId(0);
        commissionCalcOrderInfoDTO2.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO2.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO2.setAppStatus("appStatus");
        final List<CommissionCalcOrderInfoDTO> orderInfoDTOList = Arrays.asList(commissionCalcOrderInfoDTO2);
        final CommissionCalcConfigInfoDTO configInfoDTO = new CommissionCalcConfigInfoDTO();
        configInfoDTO.setId(0);
        configInfoDTO.setType(0);
        configInfoDTO.setCompanyId(0);
        final CommissionCalcConfigDetailDTO commissionCalcConfigDetailDTO = new CommissionCalcConfigDetailDTO();
        commissionCalcConfigDetailDTO.setPeriodNum(0);
        configInfoDTO.setConfigDetailDTOList(Arrays.asList(commissionCalcConfigDetailDTO));
        mockCommissionCalcService.calc(0, 0, orderInfoDTOList, configInfoDTO, new CalcErrorDTO("errorMsg","errorMsgNoAlarm"),
                "commissionUserId", "businessType");

        // Configure CommissionCalcService.calcCorrectCommission(...).
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO3 = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO3.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO3.setCommissionId(0);
        commissionCalcOrderInfoDTO3.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO3.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO3.setAppStatus("appStatus");
        final List<CommissionCalcOrderInfoDTO> orderInfoDTOList1 = Arrays.asList(commissionCalcOrderInfoDTO3);
        final OrderRiskCorrectDTO orderRiskCorrectDTO1 = new OrderRiskCorrectDTO();
        orderRiskCorrectDTO1.setOrderId("orderId");
        orderRiskCorrectDTO1.setPolicyNo("policyNo");
        orderRiskCorrectDTO1.setCorrectId("correctId");
        orderRiskCorrectDTO1.setCorrectType("correctType");
        orderRiskCorrectDTO1.setRiskId(0);
        final List<OrderRiskCorrectDTO> correctDTOList = Arrays.asList(orderRiskCorrectDTO1);
        final SmCommissionDetailItemPO smCommissionDetailItemPO = new SmCommissionDetailItemPO();
        smCommissionDetailItemPO.setOrderId("orderId");
        smCommissionDetailItemPO.setPolicyNo("policyNo");
        smCommissionDetailItemPO.setInsuredIdNumber("insuredIdNumber");
        smCommissionDetailItemPO.setPolicyStatus("policyStatus");
        smCommissionDetailItemPO.setRiskStatus("riskStatus");
        smCommissionDetailItemPO.setPlanId(0);
        smCommissionDetailItemPO.setRiskId(0);
        smCommissionDetailItemPO.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smCommissionDetailItemPO.setPlanAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setOriginalAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setTermNum(0);
        smCommissionDetailItemPO.setCommissionType(0);
        smCommissionDetailItemPO.setCommissionRate(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setCommissionAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setCommissionUserId("commissionUserId");
        smCommissionDetailItemPO.setBusinessType("businessType");
        smCommissionDetailItemPO.setBusinessId("businessId");
        smCommissionDetailItemPO.setImportFlag(0);
        final List<SmCommissionDetailItemPO> itemPOs = Arrays.asList(smCommissionDetailItemPO);
        mockCommissionCalcService.calcCorrectCommission(0, orderInfoDTOList1, correctDTOList, itemPOs,
                new CalcErrorDTO("errorMsg","errorMsgNoAlarm"));

        // Configure CommissionCalcService.calcNewRefundDetailItem(...).
        final CalcCommissionItemResultDTO calcCommissionItemResultDTO2 = new CalcCommissionItemResultDTO();
        calcCommissionItemResultDTO2.setOrderId("orderId");
        calcCommissionItemResultDTO2.setPolicyNo("policyNo");
        calcCommissionItemResultDTO2.setInsuredIdNumber("insuredIdNumber");
        calcCommissionItemResultDTO2.setPolicyStatus("policyStatus");
        calcCommissionItemResultDTO2.setRiskStatus("riskStatus");
        final List<CalcCommissionItemResultDTO> calcCommissionItemResultDTOS1 = Arrays.asList(
                calcCommissionItemResultDTO2);
        final CommissionCalcOrderInfoDTO orderInfo = new CommissionCalcOrderInfoDTO();
        orderInfo.setProductAttrCode("productAttrCode");
        orderInfo.setCommissionId(0);
        orderInfo.setOriginalFhOrderId("originalFhOrderId");
        orderInfo.setOriginalCommissionId(0);
        orderInfo.setAppStatus("appStatus");
        mockCommissionCalcService.calcNewRefundDetailItem(orderInfo, new HashMap<>());

        // Run the test
        commissionCalcProcessServiceUnderTest.calc(processDTO);

        // Verify the results
    }

    @Test
    public void testCalc_CommissionCalcServiceCalcNewRefundDetailItemReturnsNoItems() throws Exception {
        // Setup
        final CommissionCalcProcessDTO processDTO = new CommissionCalcProcessDTO();
        processDTO.setOrderId("orderId");
        processDTO.setProductId(0);
        processDTO.setProductType("productType");
        processDTO.setMatchingTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        processDTO.setTermNum(0);
        processDTO.setCommissionConfigInfo(new HashMap<>());
        final CommissionRateDTO commissionRateDTO = new CommissionRateDTO();
        processDTO.setOldCommissionConfigInfo(Arrays.asList(commissionRateDTO));
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO.setCommissionId(0);
        commissionCalcOrderInfoDTO.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO.setAppStatus("appStatus");
        processDTO.setOrderInfo(Arrays.asList(commissionCalcOrderInfoDTO));
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO1 = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO1.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO1.setCommissionId(0);
        commissionCalcOrderInfoDTO1.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO1.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO1.setAppStatus("appStatus");
        processDTO.setRefundOrderInfo(Arrays.asList(commissionCalcOrderInfoDTO1));
        final OrderRiskCorrectDTO orderRiskCorrectDTO = new OrderRiskCorrectDTO();
        processDTO.setRiskCorrectDTOS(Arrays.asList(orderRiskCorrectDTO));
        processDTO.setItemMap(new HashMap<>());
        final CalcCommissionItemResultDTO calcCommissionItemResultDTO = new CalcCommissionItemResultDTO();
        processDTO.setItemResults(Arrays.asList(calcCommissionItemResultDTO));
        processDTO.setCommissionUserId("commissionUserId");
        processDTO.setBusinessType("businessType");

        // Configure CommissionCalcService.calc(...).
        final CalcCommissionItemResultDTO calcCommissionItemResultDTO1 = new CalcCommissionItemResultDTO();
        calcCommissionItemResultDTO1.setOrderId("orderId");
        calcCommissionItemResultDTO1.setPolicyNo("policyNo");
        calcCommissionItemResultDTO1.setInsuredIdNumber("insuredIdNumber");
        calcCommissionItemResultDTO1.setPolicyStatus("policyStatus");
        calcCommissionItemResultDTO1.setRiskStatus("riskStatus");
        final List<CalcCommissionItemResultDTO> calcCommissionItemResultDTOS = Arrays.asList(
                calcCommissionItemResultDTO1);
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO2 = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO2.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO2.setCommissionId(0);
        commissionCalcOrderInfoDTO2.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO2.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO2.setAppStatus("appStatus");
        final List<CommissionCalcOrderInfoDTO> orderInfoDTOList = Arrays.asList(commissionCalcOrderInfoDTO2);
        final CommissionCalcConfigInfoDTO configInfoDTO = new CommissionCalcConfigInfoDTO();
        configInfoDTO.setId(0);
        configInfoDTO.setType(0);
        configInfoDTO.setCompanyId(0);
        final CommissionCalcConfigDetailDTO commissionCalcConfigDetailDTO = new CommissionCalcConfigDetailDTO();
        commissionCalcConfigDetailDTO.setPeriodNum(0);
        configInfoDTO.setConfigDetailDTOList(Arrays.asList(commissionCalcConfigDetailDTO));
        mockCommissionCalcService.calc(0, 0, orderInfoDTOList, configInfoDTO, new CalcErrorDTO("errorMsg","errorMsgNoAlarm"),
                "commissionUserId", "businessType");

        // Configure CommissionCalcService.calcCorrectCommission(...).
        final CalcCommissionItemResultDTO calcCommissionItemResultDTO2 = new CalcCommissionItemResultDTO();
        calcCommissionItemResultDTO2.setOrderId("orderId");
        calcCommissionItemResultDTO2.setPolicyNo("policyNo");
        calcCommissionItemResultDTO2.setInsuredIdNumber("insuredIdNumber");
        calcCommissionItemResultDTO2.setPolicyStatus("policyStatus");
        calcCommissionItemResultDTO2.setRiskStatus("riskStatus");
        final List<CalcCommissionItemResultDTO> calcCommissionItemResultDTOS1 = Arrays.asList(
                calcCommissionItemResultDTO2);
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO3 = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO3.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO3.setCommissionId(0);
        commissionCalcOrderInfoDTO3.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO3.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO3.setAppStatus("appStatus");
        final List<CommissionCalcOrderInfoDTO> orderInfoDTOList1 = Arrays.asList(commissionCalcOrderInfoDTO3);
        final OrderRiskCorrectDTO orderRiskCorrectDTO1 = new OrderRiskCorrectDTO();
        orderRiskCorrectDTO1.setOrderId("orderId");
        orderRiskCorrectDTO1.setPolicyNo("policyNo");
        orderRiskCorrectDTO1.setCorrectId("correctId");
        orderRiskCorrectDTO1.setCorrectType("correctType");
        orderRiskCorrectDTO1.setRiskId(0);
        final List<OrderRiskCorrectDTO> correctDTOList = Arrays.asList(orderRiskCorrectDTO1);
        final SmCommissionDetailItemPO smCommissionDetailItemPO = new SmCommissionDetailItemPO();
        smCommissionDetailItemPO.setOrderId("orderId");
        smCommissionDetailItemPO.setPolicyNo("policyNo");
        smCommissionDetailItemPO.setInsuredIdNumber("insuredIdNumber");
        smCommissionDetailItemPO.setPolicyStatus("policyStatus");
        smCommissionDetailItemPO.setRiskStatus("riskStatus");
        smCommissionDetailItemPO.setPlanId(0);
        smCommissionDetailItemPO.setRiskId(0);
        smCommissionDetailItemPO.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smCommissionDetailItemPO.setPlanAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setOriginalAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setTermNum(0);
        smCommissionDetailItemPO.setCommissionType(0);
        smCommissionDetailItemPO.setCommissionRate(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setCommissionAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setCommissionUserId("commissionUserId");
        smCommissionDetailItemPO.setBusinessType("businessType");
        smCommissionDetailItemPO.setBusinessId("businessId");
        smCommissionDetailItemPO.setImportFlag(0);
        final List<SmCommissionDetailItemPO> itemPOs = Arrays.asList(smCommissionDetailItemPO);
        mockCommissionCalcService.calcCorrectCommission(0, orderInfoDTOList1, correctDTOList, itemPOs,
                new CalcErrorDTO("errorMsg","errorMsgNoAlarm"));

        // Configure CommissionCalcService.calcNewRefundDetailItem(...).
        final CommissionCalcOrderInfoDTO orderInfo = new CommissionCalcOrderInfoDTO();
        orderInfo.setProductAttrCode("productAttrCode");
        orderInfo.setCommissionId(0);
        orderInfo.setOriginalFhOrderId("originalFhOrderId");
        orderInfo.setOriginalCommissionId(0);
        orderInfo.setAppStatus("appStatus");
        mockCommissionCalcService.calcNewRefundDetailItem(orderInfo, new HashMap<>());

        // Configure CommissionCalcService.calcOldRefundDetailItem(...).
        final CalcCommissionItemResultDTO calcCommissionItemResultDTO3 = new CalcCommissionItemResultDTO();
        calcCommissionItemResultDTO3.setOrderId("orderId");
        calcCommissionItemResultDTO3.setPolicyNo("policyNo");
        calcCommissionItemResultDTO3.setInsuredIdNumber("insuredIdNumber");
        calcCommissionItemResultDTO3.setPolicyStatus("policyStatus");
        calcCommissionItemResultDTO3.setRiskStatus("riskStatus");
        final List<CalcCommissionItemResultDTO> calcCommissionItemResultDTOS2 = Arrays.asList(
                calcCommissionItemResultDTO3);
        final CommissionCalcOrderInfoDTO orderInfo1 = new CommissionCalcOrderInfoDTO();
        orderInfo1.setProductAttrCode("productAttrCode");
        orderInfo1.setCommissionId(0);
        orderInfo1.setOriginalFhOrderId("originalFhOrderId");
        orderInfo1.setOriginalCommissionId(0);
        orderInfo1.setAppStatus("appStatus");
        final CommissionRateDTO commissionRateDTO1 = new CommissionRateDTO();
        commissionRateDTO1.setPlanId(0);
        commissionRateDTO1.setRiskId(0);
        commissionRateDTO1.setCommissionType(0);
        commissionRateDTO1.setCommissionId(0);
        commissionRateDTO1.setCommissionRate(new BigDecimal("0.00"));
        final List<CommissionRateDTO> oldCommissionRates = Arrays.asList(commissionRateDTO1);
        mockCommissionCalcService.calcOldRefundDetailItem(orderInfo1, oldCommissionRates,
                "commissionUserId");

        // Run the test
        commissionCalcProcessServiceUnderTest.calc(processDTO);

        // Verify the results
    }

    @Test
    public void testCalc_CommissionCalcServiceCalcOldRefundDetailItemReturnsNoItems() throws Exception {
        // Setup
        final CommissionCalcProcessDTO processDTO = new CommissionCalcProcessDTO();
        processDTO.setOrderId("orderId");
        processDTO.setProductId(0);
        processDTO.setProductType("productType");
        processDTO.setMatchingTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        processDTO.setTermNum(0);
        processDTO.setCommissionConfigInfo(new HashMap<>());
        final CommissionRateDTO commissionRateDTO = new CommissionRateDTO();
        processDTO.setOldCommissionConfigInfo(Arrays.asList(commissionRateDTO));
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO.setCommissionId(0);
        commissionCalcOrderInfoDTO.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO.setAppStatus("appStatus");
        processDTO.setOrderInfo(Arrays.asList(commissionCalcOrderInfoDTO));
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO1 = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO1.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO1.setCommissionId(0);
        commissionCalcOrderInfoDTO1.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO1.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO1.setAppStatus("appStatus");
        processDTO.setRefundOrderInfo(Arrays.asList(commissionCalcOrderInfoDTO1));
        final OrderRiskCorrectDTO orderRiskCorrectDTO = new OrderRiskCorrectDTO();
        processDTO.setRiskCorrectDTOS(Arrays.asList(orderRiskCorrectDTO));
        processDTO.setItemMap(new HashMap<>());
        final CalcCommissionItemResultDTO calcCommissionItemResultDTO = new CalcCommissionItemResultDTO();
        processDTO.setItemResults(Arrays.asList(calcCommissionItemResultDTO));
        processDTO.setCommissionUserId("commissionUserId");
        processDTO.setBusinessType("businessType");

        // Configure CommissionCalcService.calc(...).
        final CalcCommissionItemResultDTO calcCommissionItemResultDTO1 = new CalcCommissionItemResultDTO();
        calcCommissionItemResultDTO1.setOrderId("orderId");
        calcCommissionItemResultDTO1.setPolicyNo("policyNo");
        calcCommissionItemResultDTO1.setInsuredIdNumber("insuredIdNumber");
        calcCommissionItemResultDTO1.setPolicyStatus("policyStatus");
        calcCommissionItemResultDTO1.setRiskStatus("riskStatus");
        final List<CalcCommissionItemResultDTO> calcCommissionItemResultDTOS = Arrays.asList(
                calcCommissionItemResultDTO1);
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO2 = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO2.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO2.setCommissionId(0);
        commissionCalcOrderInfoDTO2.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO2.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO2.setAppStatus("appStatus");
        final List<CommissionCalcOrderInfoDTO> orderInfoDTOList = Arrays.asList(commissionCalcOrderInfoDTO2);
        final CommissionCalcConfigInfoDTO configInfoDTO = new CommissionCalcConfigInfoDTO();
        configInfoDTO.setId(0);
        configInfoDTO.setType(0);
        configInfoDTO.setCompanyId(0);
        final CommissionCalcConfigDetailDTO commissionCalcConfigDetailDTO = new CommissionCalcConfigDetailDTO();
        commissionCalcConfigDetailDTO.setPeriodNum(0);
        configInfoDTO.setConfigDetailDTOList(Arrays.asList(commissionCalcConfigDetailDTO));
        mockCommissionCalcService.calc(0, 0, orderInfoDTOList, configInfoDTO, new CalcErrorDTO("errorMsg","errorMsgNoAlarm"),
                "commissionUserId", "businessType");

        // Configure CommissionCalcService.calcCorrectCommission(...).
        final CalcCommissionItemResultDTO calcCommissionItemResultDTO2 = new CalcCommissionItemResultDTO();
        calcCommissionItemResultDTO2.setOrderId("orderId");
        calcCommissionItemResultDTO2.setPolicyNo("policyNo");
        calcCommissionItemResultDTO2.setInsuredIdNumber("insuredIdNumber");
        calcCommissionItemResultDTO2.setPolicyStatus("policyStatus");
        calcCommissionItemResultDTO2.setRiskStatus("riskStatus");
        final List<CalcCommissionItemResultDTO> calcCommissionItemResultDTOS1 = Arrays.asList(
                calcCommissionItemResultDTO2);
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO3 = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO3.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO3.setCommissionId(0);
        commissionCalcOrderInfoDTO3.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO3.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO3.setAppStatus("appStatus");
        final List<CommissionCalcOrderInfoDTO> orderInfoDTOList1 = Arrays.asList(commissionCalcOrderInfoDTO3);
        final OrderRiskCorrectDTO orderRiskCorrectDTO1 = new OrderRiskCorrectDTO();
        orderRiskCorrectDTO1.setOrderId("orderId");
        orderRiskCorrectDTO1.setPolicyNo("policyNo");
        orderRiskCorrectDTO1.setCorrectId("correctId");
        orderRiskCorrectDTO1.setCorrectType("correctType");
        orderRiskCorrectDTO1.setRiskId(0);
        final List<OrderRiskCorrectDTO> correctDTOList = Arrays.asList(orderRiskCorrectDTO1);
        final SmCommissionDetailItemPO smCommissionDetailItemPO = new SmCommissionDetailItemPO();
        smCommissionDetailItemPO.setOrderId("orderId");
        smCommissionDetailItemPO.setPolicyNo("policyNo");
        smCommissionDetailItemPO.setInsuredIdNumber("insuredIdNumber");
        smCommissionDetailItemPO.setPolicyStatus("policyStatus");
        smCommissionDetailItemPO.setRiskStatus("riskStatus");
        smCommissionDetailItemPO.setPlanId(0);
        smCommissionDetailItemPO.setRiskId(0);
        smCommissionDetailItemPO.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smCommissionDetailItemPO.setPlanAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setOriginalAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setTermNum(0);
        smCommissionDetailItemPO.setCommissionType(0);
        smCommissionDetailItemPO.setCommissionRate(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setCommissionAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setCommissionUserId("commissionUserId");
        smCommissionDetailItemPO.setBusinessType("businessType");
        smCommissionDetailItemPO.setBusinessId("businessId");
        smCommissionDetailItemPO.setImportFlag(0);
        final List<SmCommissionDetailItemPO> itemPOs = Arrays.asList(smCommissionDetailItemPO);
        mockCommissionCalcService.calcCorrectCommission(0, orderInfoDTOList1, correctDTOList, itemPOs,
                new CalcErrorDTO("errorMsg","errorMsgNoAlarm"));

        // Configure CommissionCalcService.calcNewRefundDetailItem(...).
        final CommissionCalcOrderInfoDTO orderInfo = new CommissionCalcOrderInfoDTO();
        orderInfo.setProductAttrCode("productAttrCode");
        orderInfo.setCommissionId(0);
        orderInfo.setOriginalFhOrderId("originalFhOrderId");
        orderInfo.setOriginalCommissionId(0);
        orderInfo.setAppStatus("appStatus");
        mockCommissionCalcService.calcNewRefundDetailItem(orderInfo, new HashMap<>());

        // Configure CommissionCalcService.calcOldRefundDetailItem(...).
        final CommissionCalcOrderInfoDTO orderInfo1 = new CommissionCalcOrderInfoDTO();
        orderInfo1.setProductAttrCode("productAttrCode");
        orderInfo1.setCommissionId(0);
        orderInfo1.setOriginalFhOrderId("originalFhOrderId");
        orderInfo1.setOriginalCommissionId(0);
        orderInfo1.setAppStatus("appStatus");
        final CommissionRateDTO commissionRateDTO1 = new CommissionRateDTO();
        commissionRateDTO1.setPlanId(0);
        commissionRateDTO1.setRiskId(0);
        commissionRateDTO1.setCommissionType(0);
        commissionRateDTO1.setCommissionId(0);
        commissionRateDTO1.setCommissionRate(new BigDecimal("0.00"));
        final List<CommissionRateDTO> oldCommissionRates = Arrays.asList(commissionRateDTO1);
        mockCommissionCalcService.calcOldRefundDetailItem(orderInfo1, oldCommissionRates,
                "commissionUserId");

        // Run the test
        commissionCalcProcessServiceUnderTest.calc(processDTO);

        // Verify the results
    }

    @Test
    public void testQueryOrderCommissionConfig() throws Exception {
        // Setup
        final CommissionCalcProcessDTO processDTO = new CommissionCalcProcessDTO();
        processDTO.setOrderId("orderId");
        processDTO.setProductId(0);
        processDTO.setProductType("productType");
        processDTO.setMatchingTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        processDTO.setTermNum(0);
        processDTO.setCommissionConfigInfo(new HashMap<>());
        final CommissionRateDTO commissionRateDTO = new CommissionRateDTO();
        processDTO.setOldCommissionConfigInfo(Arrays.asList(commissionRateDTO));
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO.setCommissionId(0);
        commissionCalcOrderInfoDTO.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO.setAppStatus("appStatus");
        processDTO.setOrderInfo(Arrays.asList(commissionCalcOrderInfoDTO));
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO1 = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO1.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO1.setCommissionId(0);
        commissionCalcOrderInfoDTO1.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO1.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO1.setAppStatus("appStatus");
        processDTO.setRefundOrderInfo(Arrays.asList(commissionCalcOrderInfoDTO1));
        final OrderRiskCorrectDTO orderRiskCorrectDTO = new OrderRiskCorrectDTO();
        processDTO.setRiskCorrectDTOS(Arrays.asList(orderRiskCorrectDTO));
        processDTO.setItemMap(new HashMap<>());
        final CalcCommissionItemResultDTO calcCommissionItemResultDTO = new CalcCommissionItemResultDTO();
        processDTO.setItemResults(Arrays.asList(calcCommissionItemResultDTO));
        processDTO.setCommissionUserId("commissionUserId");
        processDTO.setBusinessType("businessType");

        // Configure CommissionConfigQueryService.listConfigByProductId(...).
        final SystemCommissionConfig systemCommissionConfig = new SystemCommissionConfig();
        systemCommissionConfig.setType(0);
        systemCommissionConfig.setCompanyId(0);
        systemCommissionConfig.setProductId(0);
        systemCommissionConfig.setStartTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        systemCommissionConfig.setEndTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SystemCommissionConfig> systemCommissionConfigs = Arrays.asList(systemCommissionConfig);
        mockCommissionConfigQueryService.listConfigByProductId(0);

        // Configure CommissionConfigQueryService.listCalcCommissionDetailByConfigId(...).
        final CommissionCalcConfigDetailDTO commissionCalcConfigDetailDTO = new CommissionCalcConfigDetailDTO();
        commissionCalcConfigDetailDTO.setId(0);
        commissionCalcConfigDetailDTO.setConfigId(0);
        commissionCalcConfigDetailDTO.setPeriodNum(0);
        commissionCalcConfigDetailDTO.setPlanId(0);
        commissionCalcConfigDetailDTO.setRiskId(0);
        final List<CommissionCalcConfigDetailDTO> commissionCalcConfigDetailDTOS = Arrays.asList(
                commissionCalcConfigDetailDTO);
        mockCommissionConfigQueryService.listCalcCommissionDetailByConfigId(0);

        // Run the test
        commissionCalcProcessServiceUnderTest.queryOrderCommissionConfig(processDTO);

        // Verify the results
    }

    @Test
    public void testQueryOrderCommissionConfig_CommissionConfigQueryServiceListConfigByProductIdReturnsNoItems() throws Exception {
        // Setup
        final CommissionCalcProcessDTO processDTO = new CommissionCalcProcessDTO();
        processDTO.setOrderId("orderId");
        processDTO.setProductId(0);
        processDTO.setProductType("productType");
        processDTO.setMatchingTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        processDTO.setTermNum(0);
        processDTO.setCommissionConfigInfo(new HashMap<>());
        final CommissionRateDTO commissionRateDTO = new CommissionRateDTO();
        processDTO.setOldCommissionConfigInfo(Arrays.asList(commissionRateDTO));
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO.setCommissionId(0);
        commissionCalcOrderInfoDTO.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO.setAppStatus("appStatus");
        processDTO.setOrderInfo(Arrays.asList(commissionCalcOrderInfoDTO));
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO1 = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO1.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO1.setCommissionId(0);
        commissionCalcOrderInfoDTO1.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO1.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO1.setAppStatus("appStatus");
        processDTO.setRefundOrderInfo(Arrays.asList(commissionCalcOrderInfoDTO1));
        final OrderRiskCorrectDTO orderRiskCorrectDTO = new OrderRiskCorrectDTO();
        processDTO.setRiskCorrectDTOS(Arrays.asList(orderRiskCorrectDTO));
        processDTO.setItemMap(new HashMap<>());
        final CalcCommissionItemResultDTO calcCommissionItemResultDTO = new CalcCommissionItemResultDTO();
        processDTO.setItemResults(Arrays.asList(calcCommissionItemResultDTO));
        processDTO.setCommissionUserId("commissionUserId");
        processDTO.setBusinessType("businessType");

        mockCommissionConfigQueryService.listConfigByProductId(0);

        // Configure CommissionConfigQueryService.listCalcCommissionDetailByConfigId(...).
        final CommissionCalcConfigDetailDTO commissionCalcConfigDetailDTO = new CommissionCalcConfigDetailDTO();
        commissionCalcConfigDetailDTO.setId(0);
        commissionCalcConfigDetailDTO.setConfigId(0);
        commissionCalcConfigDetailDTO.setPeriodNum(0);
        commissionCalcConfigDetailDTO.setPlanId(0);
        commissionCalcConfigDetailDTO.setRiskId(0);
        final List<CommissionCalcConfigDetailDTO> commissionCalcConfigDetailDTOS = Arrays.asList(
                commissionCalcConfigDetailDTO);
        mockCommissionConfigQueryService.listCalcCommissionDetailByConfigId(0);

        // Run the test
        commissionCalcProcessServiceUnderTest.queryOrderCommissionConfig(processDTO);

        // Verify the results
    }

    @Test
    public void testQueryOrderCommissionConfig_CommissionConfigQueryServiceListCalcCommissionDetailByConfigIdReturnsNoItems() throws Exception {
        // Setup
        final CommissionCalcProcessDTO processDTO = new CommissionCalcProcessDTO();
        processDTO.setOrderId("orderId");
        processDTO.setProductId(0);
        processDTO.setProductType("productType");
        processDTO.setMatchingTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        processDTO.setTermNum(0);
        processDTO.setCommissionConfigInfo(new HashMap<>());
        final CommissionRateDTO commissionRateDTO = new CommissionRateDTO();
        processDTO.setOldCommissionConfigInfo(Arrays.asList(commissionRateDTO));
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO.setCommissionId(0);
        commissionCalcOrderInfoDTO.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO.setAppStatus("appStatus");
        processDTO.setOrderInfo(Arrays.asList(commissionCalcOrderInfoDTO));
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO1 = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO1.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO1.setCommissionId(0);
        commissionCalcOrderInfoDTO1.setOriginalFhOrderId("originalFhOrderId");
        commissionCalcOrderInfoDTO1.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO1.setAppStatus("appStatus");
        processDTO.setRefundOrderInfo(Arrays.asList(commissionCalcOrderInfoDTO1));
        final OrderRiskCorrectDTO orderRiskCorrectDTO = new OrderRiskCorrectDTO();
        processDTO.setRiskCorrectDTOS(Arrays.asList(orderRiskCorrectDTO));
        processDTO.setItemMap(new HashMap<>());
        final CalcCommissionItemResultDTO calcCommissionItemResultDTO = new CalcCommissionItemResultDTO();
        processDTO.setItemResults(Arrays.asList(calcCommissionItemResultDTO));
        processDTO.setCommissionUserId("commissionUserId");
        processDTO.setBusinessType("businessType");

        // Configure CommissionConfigQueryService.listConfigByProductId(...).
        final SystemCommissionConfig systemCommissionConfig = new SystemCommissionConfig();
        systemCommissionConfig.setType(0);
        systemCommissionConfig.setCompanyId(0);
        systemCommissionConfig.setProductId(0);
        systemCommissionConfig.setStartTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        systemCommissionConfig.setEndTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SystemCommissionConfig> systemCommissionConfigs = Arrays.asList(systemCommissionConfig);
        mockCommissionConfigQueryService.listConfigByProductId(0);

        mockCommissionConfigQueryService.listCalcCommissionDetailByConfigId(0);

        // Run the test
        commissionCalcProcessServiceUnderTest.queryOrderCommissionConfig(processDTO);

        // Verify the results
    }

    @Test
    public void testAddSmCommissionItem() throws Exception {
        // Setup
        final CalcCommissionItemResultDTO calcCommissionItemResultDTO = new CalcCommissionItemResultDTO();
        calcCommissionItemResultDTO.setOrderId("orderId");
        calcCommissionItemResultDTO.setPolicyNo("policyNo");
        calcCommissionItemResultDTO.setInsuredIdNumber("insuredIdNumber");
        calcCommissionItemResultDTO.setPolicyStatus("policyStatus");
        calcCommissionItemResultDTO.setRiskStatus("riskStatus");
        final List<CalcCommissionItemResultDTO> itemResults = Arrays.asList(calcCommissionItemResultDTO);
        final SmCommissionDetailItemPO smCommissionDetailItemPO = new SmCommissionDetailItemPO();
        smCommissionDetailItemPO.setOrderId("orderId");
        smCommissionDetailItemPO.setPolicyNo("policyNo");
        smCommissionDetailItemPO.setInsuredIdNumber("insuredIdNumber");
        smCommissionDetailItemPO.setPolicyStatus("policyStatus");
        smCommissionDetailItemPO.setRiskStatus("riskStatus");
        smCommissionDetailItemPO.setPlanId(0);
        smCommissionDetailItemPO.setRiskId(0);
        smCommissionDetailItemPO.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smCommissionDetailItemPO.setPlanAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setOriginalAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setTermNum(0);
        smCommissionDetailItemPO.setCommissionType(0);
        smCommissionDetailItemPO.setCommissionRate(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setCommissionAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setCommissionUserId("commissionUserId");
        smCommissionDetailItemPO.setBusinessType("businessType");
        smCommissionDetailItemPO.setBusinessId("businessId");
        smCommissionDetailItemPO.setImportFlag(0);
        final List<SmCommissionDetailItemPO> expectedResult = Arrays.asList(smCommissionDetailItemPO);

        // Run the test
        commissionCalcProcessServiceUnderTest.addSmCommissionItem(
                itemResults, false);

        // Confirm SmOrderCommissionItemMapper.batchInsertCommissionItem(...).
        final SmCommissionDetailItemPO smCommissionDetailItemPO1 = new SmCommissionDetailItemPO();
        smCommissionDetailItemPO1.setOrderId("orderId");
        smCommissionDetailItemPO1.setPolicyNo("policyNo");
        smCommissionDetailItemPO1.setInsuredIdNumber("insuredIdNumber");
        smCommissionDetailItemPO1.setPolicyStatus("policyStatus");
        smCommissionDetailItemPO1.setRiskStatus("riskStatus");
        smCommissionDetailItemPO1.setPlanId(0);
        smCommissionDetailItemPO1.setRiskId(0);
        smCommissionDetailItemPO1.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smCommissionDetailItemPO1.setPlanAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO1.setAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO1.setOriginalAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO1.setTermNum(0);
        smCommissionDetailItemPO1.setCommissionType(0);
        smCommissionDetailItemPO1.setCommissionRate(new BigDecimal("0.00"));
        smCommissionDetailItemPO1.setCommissionAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO1.setCommissionUserId("commissionUserId");
        smCommissionDetailItemPO1.setBusinessType("businessType");
        smCommissionDetailItemPO1.setBusinessId("businessId");
        smCommissionDetailItemPO1.setImportFlag(0);
        final List<SmCommissionDetailItemPO> commissionConfigItemList = Arrays.asList(smCommissionDetailItemPO1);
        mockSmOrderCommissionItemMapper.batchInsertCommissionItem(commissionConfigItemList);

        // Confirm SmOrderCommissionItemMapper.batchInsertCommissionItemExceptImport(...).
        final SmCommissionDetailItemPO smCommissionDetailItemPO2 = new SmCommissionDetailItemPO();
        smCommissionDetailItemPO2.setOrderId("orderId");
        smCommissionDetailItemPO2.setPolicyNo("policyNo");
        smCommissionDetailItemPO2.setInsuredIdNumber("insuredIdNumber");
        smCommissionDetailItemPO2.setPolicyStatus("policyStatus");
        smCommissionDetailItemPO2.setRiskStatus("riskStatus");
        smCommissionDetailItemPO2.setPlanId(0);
        smCommissionDetailItemPO2.setRiskId(0);
        smCommissionDetailItemPO2.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smCommissionDetailItemPO2.setPlanAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO2.setAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO2.setOriginalAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO2.setTermNum(0);
        smCommissionDetailItemPO2.setCommissionType(0);
        smCommissionDetailItemPO2.setCommissionRate(new BigDecimal("0.00"));
        smCommissionDetailItemPO2.setCommissionAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO2.setCommissionUserId("commissionUserId");
        smCommissionDetailItemPO2.setBusinessType("businessType");
        smCommissionDetailItemPO2.setBusinessId("businessId");
        smCommissionDetailItemPO2.setImportFlag(0);
        final List<SmCommissionDetailItemPO> commissionConfigItemList1 = Arrays.asList(smCommissionDetailItemPO2);
        mockSmOrderCommissionItemMapper.batchInsertCommissionItemExceptImport(commissionConfigItemList1);
    }

    @Test
    public void testAddSmCommissionDetail() throws Exception {
        // Setup
        final SmCommissionDetailItemPO smCommissionDetailItemPO = new SmCommissionDetailItemPO();
        smCommissionDetailItemPO.setOrderId("orderId");
        smCommissionDetailItemPO.setPolicyNo("policyNo");
        smCommissionDetailItemPO.setInsuredIdNumber("insuredIdNumber");
        smCommissionDetailItemPO.setPolicyStatus("policyStatus");
        smCommissionDetailItemPO.setRiskStatus("riskStatus");
        smCommissionDetailItemPO.setPlanId(0);
        smCommissionDetailItemPO.setRiskId(0);
        smCommissionDetailItemPO.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smCommissionDetailItemPO.setPlanAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setOriginalAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setTermNum(0);
        smCommissionDetailItemPO.setCommissionType(0);
        smCommissionDetailItemPO.setCommissionRate(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setCommissionAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setCommissionUserId("commissionUserId");
        smCommissionDetailItemPO.setBusinessType("businessType");
        smCommissionDetailItemPO.setBusinessId("businessId");
        smCommissionDetailItemPO.setImportFlag(0);
        final List<SmCommissionDetailItemPO> itemPOList = Arrays.asList(smCommissionDetailItemPO);
        final CalcErrorDTO errorDTO = new CalcErrorDTO("errorMsg","errorMsgNoAlarm");

        // Run the test
        commissionCalcProcessServiceUnderTest.addSmCommissionDetail(itemPOList, errorDTO, false);

        // Verify the results
        // Confirm SmOrderCommissionDetailMapper.batchReCalcCommissionDetail(...).
        final SmCommissionDetailPO smCommissionDetailPO = new SmCommissionDetailPO();
        smCommissionDetailPO.setOrderId("orderId");
        smCommissionDetailPO.setPolicyNo("policyNo");
        smCommissionDetailPO.setInsuredIdNumber("insuredIdNumber");
        smCommissionDetailPO.setPolicyStatus("policyStatus");
        smCommissionDetailPO.setPlanId(0);
        smCommissionDetailPO.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smCommissionDetailPO.setAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setOriginalAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setPaymentRate(new BigDecimal("0.00"));
        smCommissionDetailPO.setPaymentAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setSettlementRate(new BigDecimal("0.00"));
        smCommissionDetailPO.setSettlementAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setConversionRate(new BigDecimal("0.00"));
        smCommissionDetailPO.setConversionAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setTermNum(0);
        smCommissionDetailPO.setPaymentRiskJson("paymentRiskJson");
        smCommissionDetailPO.setSettlementRiskJson("settlementRiskJson");
        smCommissionDetailPO.setConversionRiskJson("conversionRiskJson");
        smCommissionDetailPO.setPAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setSAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setCAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setCommissionUserId("commissionUserId");
        smCommissionDetailPO.setBusinessType("businessType");
        smCommissionDetailPO.setBusinessId("businessId");
        smCommissionDetailPO.setImportFlag(0);
        final List<SmCommissionDetailPO> commissionConfigDetailList = Arrays.asList(smCommissionDetailPO);
        mockSmOrderCommissionDetailMapper.batchReCalcCommissionDetail(commissionConfigDetailList);

        // Confirm SmOrderCommissionDetailMapper.batchInsertCommissionDetail(...).
        final SmCommissionDetailPO smCommissionDetailPO1 = new SmCommissionDetailPO();
        smCommissionDetailPO1.setOrderId("orderId");
        smCommissionDetailPO1.setPolicyNo("policyNo");
        smCommissionDetailPO1.setInsuredIdNumber("insuredIdNumber");
        smCommissionDetailPO1.setPolicyStatus("policyStatus");
        smCommissionDetailPO1.setPlanId(0);
        smCommissionDetailPO1.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smCommissionDetailPO1.setAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setOriginalAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setPaymentRate(new BigDecimal("0.00"));
        smCommissionDetailPO1.setPaymentAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setSettlementRate(new BigDecimal("0.00"));
        smCommissionDetailPO1.setSettlementAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setConversionRate(new BigDecimal("0.00"));
        smCommissionDetailPO1.setConversionAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setTermNum(0);
        smCommissionDetailPO1.setPaymentRiskJson("paymentRiskJson");
        smCommissionDetailPO1.setSettlementRiskJson("settlementRiskJson");
        smCommissionDetailPO1.setConversionRiskJson("conversionRiskJson");
        smCommissionDetailPO1.setPAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setSAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setCAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setCommissionUserId("commissionUserId");
        smCommissionDetailPO1.setBusinessType("businessType");
        smCommissionDetailPO1.setBusinessId("businessId");
        smCommissionDetailPO1.setImportFlag(0);
        final List<SmCommissionDetailPO> commissionConfigDetailList1 = Arrays.asList(smCommissionDetailPO1);
        mockSmOrderCommissionDetailMapper.batchInsertCommissionDetail(commissionConfigDetailList1);
    }

    @Test
    public void testUpdateCommissionJson() throws Exception {
        // Setup
        // Configure SmOrderCommissionItemMapper.listCommissionItemByOrderId(...).
        final SmCommissionDetailItemPO smCommissionDetailItemPO = new SmCommissionDetailItemPO();
        smCommissionDetailItemPO.setOrderId("orderId");
        smCommissionDetailItemPO.setPolicyNo("policyNo");
        smCommissionDetailItemPO.setInsuredIdNumber("insuredIdNumber");
        smCommissionDetailItemPO.setPolicyStatus("policyStatus");
        smCommissionDetailItemPO.setRiskStatus("riskStatus");
        smCommissionDetailItemPO.setPlanId(0);
        smCommissionDetailItemPO.setRiskId(0);
        smCommissionDetailItemPO.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smCommissionDetailItemPO.setPlanAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setOriginalAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setTermNum(0);
        smCommissionDetailItemPO.setCommissionType(0);
        smCommissionDetailItemPO.setCommissionRate(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setCommissionAmount(new BigDecimal("0.00"));
        smCommissionDetailItemPO.setCommissionUserId("commissionUserId");
        smCommissionDetailItemPO.setBusinessType("businessType");
        smCommissionDetailItemPO.setBusinessId("businessId");
        smCommissionDetailItemPO.setImportFlag(0);
        final List<SmCommissionDetailItemPO> smCommissionDetailItemPOS = Arrays.asList(smCommissionDetailItemPO);
        mockSmOrderCommissionItemMapper.listCommissionItemByOrderId("fhOrderId", 0);

        // Run the test
        commissionCalcProcessServiceUnderTest.updateCommissionJson("fhOrderId", 0);

        // Verify the results
        // Confirm SmOrderCommissionDetailMapper.batchReCalcCommissionDetail(...).
        final SmCommissionDetailPO smCommissionDetailPO = new SmCommissionDetailPO();
        smCommissionDetailPO.setOrderId("orderId");
        smCommissionDetailPO.setPolicyNo("policyNo");
        smCommissionDetailPO.setInsuredIdNumber("insuredIdNumber");
        smCommissionDetailPO.setPolicyStatus("policyStatus");
        smCommissionDetailPO.setPlanId(0);
        smCommissionDetailPO.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smCommissionDetailPO.setAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setOriginalAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setPaymentRate(new BigDecimal("0.00"));
        smCommissionDetailPO.setPaymentAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setSettlementRate(new BigDecimal("0.00"));
        smCommissionDetailPO.setSettlementAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setConversionRate(new BigDecimal("0.00"));
        smCommissionDetailPO.setConversionAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setTermNum(0);
        smCommissionDetailPO.setPaymentRiskJson("paymentRiskJson");
        smCommissionDetailPO.setSettlementRiskJson("settlementRiskJson");
        smCommissionDetailPO.setConversionRiskJson("conversionRiskJson");
        smCommissionDetailPO.setPAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setSAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setCAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setCommissionUserId("commissionUserId");
        smCommissionDetailPO.setBusinessType("businessType");
        smCommissionDetailPO.setBusinessId("businessId");
        smCommissionDetailPO.setImportFlag(0);
        final List<SmCommissionDetailPO> commissionConfigDetailList = Arrays.asList(smCommissionDetailPO);
        mockSmOrderCommissionDetailMapper.batchReCalcCommissionDetail(commissionConfigDetailList);

        // Confirm SmOrderCommissionDetailMapper.batchInsertCommissionDetail(...).
        final SmCommissionDetailPO smCommissionDetailPO1 = new SmCommissionDetailPO();
        smCommissionDetailPO1.setOrderId("orderId");
        smCommissionDetailPO1.setPolicyNo("policyNo");
        smCommissionDetailPO1.setInsuredIdNumber("insuredIdNumber");
        smCommissionDetailPO1.setPolicyStatus("policyStatus");
        smCommissionDetailPO1.setPlanId(0);
        smCommissionDetailPO1.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smCommissionDetailPO1.setAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setOriginalAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setPaymentRate(new BigDecimal("0.00"));
        smCommissionDetailPO1.setPaymentAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setSettlementRate(new BigDecimal("0.00"));
        smCommissionDetailPO1.setSettlementAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setConversionRate(new BigDecimal("0.00"));
        smCommissionDetailPO1.setConversionAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setTermNum(0);
        smCommissionDetailPO1.setPaymentRiskJson("paymentRiskJson");
        smCommissionDetailPO1.setSettlementRiskJson("settlementRiskJson");
        smCommissionDetailPO1.setConversionRiskJson("conversionRiskJson");
        smCommissionDetailPO1.setPAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setSAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setCAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setCommissionUserId("commissionUserId");
        smCommissionDetailPO1.setBusinessType("businessType");
        smCommissionDetailPO1.setBusinessId("businessId");
        smCommissionDetailPO1.setImportFlag(0);
        final List<SmCommissionDetailPO> commissionConfigDetailList1 = Arrays.asList(smCommissionDetailPO1);
        mockSmOrderCommissionDetailMapper.batchInsertCommissionDetail(commissionConfigDetailList1);
    }

    @Test
    public void testUpdateCommissionJson_SmOrderCommissionItemMapperReturnsNoItems() throws Exception {
        // Setup
        mockSmOrderCommissionItemMapper.listCommissionItemByOrderId("fhOrderId", 0);

        // Run the test
        commissionCalcProcessServiceUnderTest.updateCommissionJson("fhOrderId", 0);

        // Verify the results
        // Confirm SmOrderCommissionDetailMapper.batchReCalcCommissionDetail(...).
        final SmCommissionDetailPO smCommissionDetailPO = new SmCommissionDetailPO();
        smCommissionDetailPO.setOrderId("orderId");
        smCommissionDetailPO.setPolicyNo("policyNo");
        smCommissionDetailPO.setInsuredIdNumber("insuredIdNumber");
        smCommissionDetailPO.setPolicyStatus("policyStatus");
        smCommissionDetailPO.setPlanId(0);
        smCommissionDetailPO.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smCommissionDetailPO.setAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setOriginalAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setPaymentRate(new BigDecimal("0.00"));
        smCommissionDetailPO.setPaymentAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setSettlementRate(new BigDecimal("0.00"));
        smCommissionDetailPO.setSettlementAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setConversionRate(new BigDecimal("0.00"));
        smCommissionDetailPO.setConversionAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setTermNum(0);
        smCommissionDetailPO.setPaymentRiskJson("paymentRiskJson");
        smCommissionDetailPO.setSettlementRiskJson("settlementRiskJson");
        smCommissionDetailPO.setConversionRiskJson("conversionRiskJson");
        smCommissionDetailPO.setPAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setSAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setCAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setCommissionUserId("commissionUserId");
        smCommissionDetailPO.setBusinessType("businessType");
        smCommissionDetailPO.setBusinessId("businessId");
        smCommissionDetailPO.setImportFlag(0);
        final List<SmCommissionDetailPO> commissionConfigDetailList = Arrays.asList(smCommissionDetailPO);
        mockSmOrderCommissionDetailMapper.batchReCalcCommissionDetail(commissionConfigDetailList);

        // Confirm SmOrderCommissionDetailMapper.batchInsertCommissionDetail(...).
        final SmCommissionDetailPO smCommissionDetailPO1 = new SmCommissionDetailPO();
        smCommissionDetailPO1.setOrderId("orderId");
        smCommissionDetailPO1.setPolicyNo("policyNo");
        smCommissionDetailPO1.setInsuredIdNumber("insuredIdNumber");
        smCommissionDetailPO1.setPolicyStatus("policyStatus");
        smCommissionDetailPO1.setPlanId(0);
        smCommissionDetailPO1.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smCommissionDetailPO1.setAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setOriginalAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setPaymentRate(new BigDecimal("0.00"));
        smCommissionDetailPO1.setPaymentAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setSettlementRate(new BigDecimal("0.00"));
        smCommissionDetailPO1.setSettlementAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setConversionRate(new BigDecimal("0.00"));
        smCommissionDetailPO1.setConversionAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setTermNum(0);
        smCommissionDetailPO1.setPaymentRiskJson("paymentRiskJson");
        smCommissionDetailPO1.setSettlementRiskJson("settlementRiskJson");
        smCommissionDetailPO1.setConversionRiskJson("conversionRiskJson");
        smCommissionDetailPO1.setPAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setSAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setCAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setCommissionUserId("commissionUserId");
        smCommissionDetailPO1.setBusinessType("businessType");
        smCommissionDetailPO1.setBusinessId("businessId");
        smCommissionDetailPO1.setImportFlag(0);
        final List<SmCommissionDetailPO> commissionConfigDetailList1 = Arrays.asList(smCommissionDetailPO1);
        mockSmOrderCommissionDetailMapper.batchInsertCommissionDetail(commissionConfigDetailList1);
    }

    @Test
    public void testBatchCopyOldCommissionToNew() throws Exception {
        // Setup
        final SmCommissionDetailPO smCommissionDetailPO = new SmCommissionDetailPO();
        smCommissionDetailPO.setOrderId("orderId");
        smCommissionDetailPO.setPolicyNo("policyNo");
        smCommissionDetailPO.setInsuredIdNumber("insuredIdNumber");
        smCommissionDetailPO.setPolicyStatus("policyStatus");
        smCommissionDetailPO.setPlanId(0);
        smCommissionDetailPO.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smCommissionDetailPO.setAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setOriginalAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setPaymentRate(new BigDecimal("0.00"));
        smCommissionDetailPO.setPaymentAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setSettlementRate(new BigDecimal("0.00"));
        smCommissionDetailPO.setSettlementAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setConversionRate(new BigDecimal("0.00"));
        smCommissionDetailPO.setConversionAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setTermNum(0);
        smCommissionDetailPO.setPaymentRiskJson("paymentRiskJson");
        smCommissionDetailPO.setSettlementRiskJson("settlementRiskJson");
        smCommissionDetailPO.setConversionRiskJson("conversionRiskJson");
        smCommissionDetailPO.setPAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setSAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setCAmount(new BigDecimal("0.00"));
        smCommissionDetailPO.setCommissionUserId("commissionUserId");
        smCommissionDetailPO.setBusinessType("businessType");
        smCommissionDetailPO.setBusinessId("businessId");
        smCommissionDetailPO.setImportFlag(0);
        final List<SmCommissionDetailPO> detailPOS = Arrays.asList(smCommissionDetailPO);

        // Run the test
        commissionCalcProcessServiceUnderTest.batchCopyOldCommissionToNew(detailPOS);

        // Verify the results
        // Confirm SmOrderCommissionDetailMapper.batchReCalcCommissionDetail(...).
        final SmCommissionDetailPO smCommissionDetailPO1 = new SmCommissionDetailPO();
        smCommissionDetailPO1.setOrderId("orderId");
        smCommissionDetailPO1.setPolicyNo("policyNo");
        smCommissionDetailPO1.setInsuredIdNumber("insuredIdNumber");
        smCommissionDetailPO1.setPolicyStatus("policyStatus");
        smCommissionDetailPO1.setPlanId(0);
        smCommissionDetailPO1.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smCommissionDetailPO1.setAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setOriginalAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setPaymentRate(new BigDecimal("0.00"));
        smCommissionDetailPO1.setPaymentAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setSettlementRate(new BigDecimal("0.00"));
        smCommissionDetailPO1.setSettlementAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setConversionRate(new BigDecimal("0.00"));
        smCommissionDetailPO1.setConversionAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setTermNum(0);
        smCommissionDetailPO1.setPaymentRiskJson("paymentRiskJson");
        smCommissionDetailPO1.setSettlementRiskJson("settlementRiskJson");
        smCommissionDetailPO1.setConversionRiskJson("conversionRiskJson");
        smCommissionDetailPO1.setPAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setSAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setCAmount(new BigDecimal("0.00"));
        smCommissionDetailPO1.setCommissionUserId("commissionUserId");
        smCommissionDetailPO1.setBusinessType("businessType");
        smCommissionDetailPO1.setBusinessId("businessId");
        smCommissionDetailPO1.setImportFlag(0);
        final List<SmCommissionDetailPO> commissionConfigDetailList = Arrays.asList(smCommissionDetailPO1);
        mockSmOrderCommissionDetailMapper.batchReCalcCommissionDetail(commissionConfigDetailList);
    }
}
