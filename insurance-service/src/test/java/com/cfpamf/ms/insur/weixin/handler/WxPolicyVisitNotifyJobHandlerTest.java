package com.cfpamf.ms.insur.weixin.handler;

import java.util.Date;

import com.cfpamf.ms.insur.admin.dao.safes.order.SmOrderPolicyMapper;
import com.cfpamf.ms.insur.base.config.WechatConfig;
import com.cfpamf.ms.insur.base.service.BmsService;
import com.cfpamf.ms.insur.base.util.HttpRequestUtil;
import com.cfpamf.ms.insur.weixin.job.WxPolicyVisitNotifyJobHandler;
import com.cfpamf.ms.insur.weixin.pojo.dto.policy.PolicyVisitNotifyDTO;
import com.cfpamf.ms.insur.weixin.service.WxMpServiceProxy;
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @date 2021/11/16 11:52
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({HttpRequestUtil.class})
public class WxPolicyVisitNotifyJobHandlerTest {


    /**
     * 微信模板Id
     */
    @Mock
    private String templateId;

    @Mock
    SmOrderPolicyMapper smOrderPolicyMapper;

    @Mock
    WechatConfig wechatConfig;

    /**
     * 微信mpService
     */
    @Mock
    protected WxMpServiceProxy wxMpServiceProxy;

    @Mock
    BmsService bmsService;

    @InjectMocks
    WxPolicyVisitNotifyJobHandler wxPolicyVisitNotifyJobHandler;



    @Test
    public void test() {
        PolicyVisitNotifyDTO policyVisitNotifyDTO = new PolicyVisitNotifyDTO();
        policyVisitNotifyDTO.setApplicantName("123");
        policyVisitNotifyDTO.setUndoVisitDay(123);
        policyVisitNotifyDTO.setWxOpenId("11");
        policyVisitNotifyDTO.setProductName("222");
        policyVisitNotifyDTO.setPaymentTime(new Date());

        Mockito.when(smOrderPolicyMapper.getUndoVisitPolicyList(Lists.newArrayList())).thenReturn(Lists.newArrayList(policyVisitNotifyDTO));
        wxPolicyVisitNotifyJobHandler.execute();
    }
}
