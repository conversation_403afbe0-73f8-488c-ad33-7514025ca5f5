//package com.cfpamf.ms.insur.admin.external.cic.api;
//
//import com.cfpamf.ms.insur.admin.external.cic.bean.InsureQ;
//import com.cfpamf.ms.insur.admin.external.cic.bean.InsureRet;
//import com.fasterxml.jackson.annotation.JsonInclude;
//import com.fasterxml.jackson.dataformat.xml.XmlMapper;
//import org.junit.Before;
//import org.junit.FixMethodOrder;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.junit.runners.MethodSorters;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.test.context.ActiveProfiles;
//import org.springframework.test.context.junit4.SpringRunner;
//
//@RunWith(SpringRunner.class)
//@SpringBootTest
//@ActiveProfiles("dev")
//@FixMethodOrder(MethodSorters.NAME_ASCENDING)
//public class CicApiServiceTest {
//
//    @Autowired
//    private CicApiService cicOrderService;
//
//    @Before
//    public void setUp() {
//    }
//
//    @Test
//    public void submitOrder() throws Exception {
//        String xml = "<?xml version=\"1.0\" encoding=\"GBK\"?>  " +
//                "<INSUREQ>  " +
//                "  <HEAD>  " +
//                "    <TRANSRNO>CIC001</TRANSRNO>  " +
//                "    <PARTNERCODE>life_test</PARTNERCODE>  " +
//                "    <PARTNERSUBCODE>life_test</PARTNERSUBCODE>  " +
//                "    <ASYNC>1</ASYNC>  " +
//                "  </HEAD>  " +
//                "  <MAIN>  " +
//                "    <USERCODE>2015A32147</USERCODE>  " +
//                "    <OTHERPAYID></OTHERPAYID>  " +
//                "    <PRODUCTCODE>100001</PRODUCTCODE>  " +
//                "    <SERIALNUMBER>lj20150123000002</SERIALNUMBER>  " +
//                "    <TRANSRDATE>2015-01-23 10:00:00</TRANSRDATE>  " +
//                "    <PRODUCTUNIT>2</PRODUCTUNIT>  " +
//                "    <EFFDATE>2015-01-24 00:00:00</EFFDATE>  " +
//                "    <TERMDATE>2015-01-28 24:00:00</TERMDATE>  " +
//                "    <CATALOGPREMIUM>11.44</CATALOGPREMIUM>  " +
//                "    <RCV_AMT>1144</RCV_AMT>  " +
//                "    <BBRUNIT>1</BBRUNIT>  " +
//                "    <PRNNO>117640000099</PRNNO>  " +
//                "    <POLICYFLAG>1</POLICYFLAG>  " +
//                "    <OLDPOLICYNO>01201843010900194900000010</OLDPOLICYNO>  " +
//                "  </MAIN>  " +
//                "  <HEALPOLICYINFO>  " +
//                "    <TBR>  " +
//                "      <TBRNAME>张三</TBRNAME>  " +
//                "      <TBRSEX>1</TBRSEX>  " +
//                "      <TBRIDTYPE>01</TBRIDTYPE>  " +
//                "      <TBRNATURE>4</TBRNATURE>  " +
//                "      <TBRIDNO>130406198801160641</TBRIDNO>  " +
//                "      <TBRBIRTH>1980-01-01</TBRBIRTH>  " +
//                "      <TBRADDR>石家庄市</TBRADDR>  " +
//                "      <TBRPOSTCODE>050000</TBRPOSTCODE>  " +
//                "      <TBRTEL>03178713265</TBRTEL>  " +
//                "      <TBRMOBILE>13001121112</TBRMOBILE>  " +
//                "      <TBREMAIL><EMAIL></TBREMAIL>  " +
//                "      <TBRBBRRELATION>01</TBRBBRRELATION>  " +
//                "      <PROVINCE>130000</PROVINCE>  " +
//                "      <PREFECTURE></PREFECTURE>  " +
//                "      <COUNTY></COUNTY>  " +
//                "      <IDENTIFYPERIODFLAG></IDENTIFYPERIODFLAG>  " +
//                "      <IDENTIFYPERIODSTART></IDENTIFYPERIODSTART>  " +
//                "      <IDENTIFYPERIODEND></IDENTIFYPERIODEND>  " +
//                "    </TBR>  " +
//                "    <BBRS>  " +
//                "      <BBR>  " +
//                "        <BBRNAME>张三</BBRNAME>  " +
//                "        <BBRSEX>1</BBRSEX>  " +
//                "        <BBRBIRTH>1988-01-16</BBRBIRTH>  " +
//                "        <BBRIDTYPE>01</BBRIDTYPE>  " +
//                "        <BBRIDNO>130406198801160641</BBRIDNO>  " +
//                "        <BBRADDR>石家庄市</BBRADDR>  " +
//                "        <BBRPOSTCODE>050000</BBRPOSTCODE>  " +
//                "        <BBRTEL>03178713265</BBRTEL>  " +
//                "        <BBRMOBILE>13001121112</BBRMOBILE>  " +
//                "        <BBREMAIL><EMAIL></BBREMAIL>  " +
//                "        <BBRWORKCODE>110127</BBRWORKCODE>  " +
//                "        <BBRCATEGORY></BBRCATEGORY>  " +
//                "        <BENIFITMARK>Y</BENIFITMARK>  " +
//                "        <SCHOOLNAME></SCHOOLNAME>  " +
//                "        <CLASS></CLASS>  " +
//                "        <STUDENTID></STUDENTID>  " +
//                "        <SYRS>  " +
//                "          <SYR>  " +
//                "            <SYRNAME>李四</SYRNAME>  " +
//                "            <SYRSEX>1061</SYRSEX>  " +
//                "            <SYRBIRTH>1980-01-01</SYRBIRTH>  " +
//                "            <SYRIDTYPE>身份证</SYRIDTYPE>  " +
//                "            <SYRIDNO>130406198801160641</SYRIDNO>  " +
//                "            <RELATIONTOINSURED></RELATIONTOINSURED>  " +
//                "            <BENIFITPROPORTION></BENIFITPROPORTION>  " +
//                "          </SYR>  " +
//                "        </SYRS>  " +
//                "      </BBR>  " +
//                "    </BBRS>  " +
//                "    <CARINFO>  " +
//                "      <LICENSENO>1234</LICENSENO>  " +
//                "      <FRAMENO>1423</FRAMENO>  " +
//                "      <SEATCOUNT>1</SEATCOUNT>  " +
//                "      <USENATURE>002</USENATURE>  " +
//                "      <CARTYPE></CARTYPE>  " +
//                "      <VEHICLENATURE></VEHICLENATURE>  " +
//                "    </CARINFO>  " +
//                "    <ANESTHTYPE>C1</ANESTHTYPE>  " +
//                "    <LOANINFO>  " +
//                "      <LOANDATE></LOANDATE>  " +
//                "      <REPAIDDATE></REPAIDDATE>  " +
//                "      <REPAIDTYPE></REPAIDTYPE>  " +
//                "      <LOANWAY></LOANWAY>  " +
//                "      <CURRENCY></CURRENCY>  " +
//                "      <BENIFITPRINTFLAG></BENIFITPRINTFLAG>  " +
//                "      <LOANBANKSITENAME></LOANBANKSITENAME>  " +
//                "      <BANKLINKERNAME></BANKLINKERNAME>  " +
//                "      <BANKLINKERPHONE></BANKLINKERPHONE>  " +
//                "    </LOANINFO>  " +
//                "    <OTHERINFO>  " +
//                "      <SMWEBHOSP>直接结算网络医院</SMWEBHOSP>  " +
//                "    </OTHERINFO>  " +
//                "    <STUINFO>  " +
//                "      <SCHOOLNATURE>01</SCHOOLNATURE>  " +
//                "      <STUDENTTYPE>07</STUDENTTYPE>  " +
//                "      <SCHOOLTYPE>01</SCHOOLTYPE>  " +
//                "    </STUINFO>  " +
//                "    <TRAIN>  " +
//                "      <TRAINNUMBER></TRAINNUMBER>  " +
//                "      <SEATNUMBER></SEATNUMBER>  " +
//                "      <DEPARTUREDATE>2017-07-01</DEPARTUREDATE>  " +
//                "      <DEPARTUREHOUR>11:47:05</DEPARTUREHOUR>  " +
//                "      <ARRIVALTIME>2017-07-01 11:52:03</ARRIVALTIME>  " +
//                "      <TICKETNUMBER></TICKETNUMBER>  " +
//                "      <VEHICLE></VEHICLE>  " +
//                "      <TICKETNO></TICKETNO>  " +
//                "      <DEPARTURESTATION></DEPARTURESTATION>  " +
//                "      <ARRIVALSTATION></ARRIVALSTATION>  " +
//                "      <RATIONPERIOD></RATIONPERIOD>  " +
//                "    </TRAIN>  " +
//                "    <AIR>  " +
//                "      <FLIGHTNUMBER></FLIGHTNUMBER>  " +
//                "      <RETURNFLIGHTNUMBER></RETURNFLIGHTNUMBER>  " +
//                "      <FLIGHTDATE></FLIGHTDATE>  " +
//                "      <FLIGHTTIME></FLIGHTTIME>  " +
//                "      <RETURNFLIGHTDATE></RETURNFLIGHTDATE>  " +
//                "      <BUYTIME></BUYTIME>  " +
//                "      <ORIGINAIRPORT></ORIGINAIRPORT>  " +
//                "      <DESTAIRPORT></DESTAIRPORT>  " +
//                "      <AIRLINENO></AIRLINENO>  " +
//                "      <IATANO></IATANO>  " +
//                "      <ETICKETNO></ETICKETNO>  " +
//                "      <AIRLINEORDERNO></AIRLINEORDERNO>  " +
//                "      <OPERATOR></OPERATOR>  " +
//                "      <OPERATETIME></OPERATETIME>  " +
//                "      <INSURENO></INSURENO>  " +
//                "    </AIR>  " +
//                "    <TRAVEL>  " +
//                "      <GROUPNO>1</GROUPNO>  " +
//                "      <COUNTRYCODE>1</COUNTRYCODE>  " +
//                "      <COUNTRYNAME>1</COUNTRYNAME>  " +
//                "      <LINE>1</LINE>  " +
//                "    </TRAVEL>  " +
//                "  </HEALPOLICYINFO>  " +
//                "</INSUREQ>";
//        XmlMapper xmlMapper = new XmlMapper();
//        xmlMapper.setSerializationInclusion(JsonInclude.Include.NON_EMPTY);
//        InsureQ request = xmlMapper.readValue(String.join("", xml), InsureQ.class);
//        InsureRet insureQRet = cicOrderService.submitOrder(request);
//        System.out.println();
//    }
//
//    @Test
//    public void queryOrder() {
//
//    }
//}