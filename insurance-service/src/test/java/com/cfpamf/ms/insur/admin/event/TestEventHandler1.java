package com.cfpamf.ms.insur.admin.event;

import com.cfpamf.ms.insur.base.event.BaseEventHandler;
import com.google.common.eventbus.Subscribe;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 **/
@Component
public class TestEventHandler1 implements BaseEventHandler {

    @Subscribe
    public boolean process1(TestEvent testEvent) {
//        System.out.println("TestEventHandler1 process " + testEvent.getValue());
        return true;
    }

    @Subscribe
    public boolean process2(TestEvent testEvent) {
//        System.out.println("TestEventHandler2 process " + testEvent.getValue());
        return true;
    }
}
