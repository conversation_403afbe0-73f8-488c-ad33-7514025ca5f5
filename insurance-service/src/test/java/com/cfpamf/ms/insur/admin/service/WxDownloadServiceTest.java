//package com.cfpamf.ms.insur.admin.service;
//
//import com.cfpamf.ms.insur.weixin.service.WxDownloadService;
//import org.junit.FixMethodOrder;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.junit.runners.MethodSorters;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.mock.web.MockHttpServletResponse;
//import org.springframework.test.context.ActiveProfiles;
//import org.springframework.test.context.junit4.SpringRunner;
//
//import java.util.concurrent.CountDownLatch;
//
//@RunWith(SpringRunner.class)
//@SpringBootTest
//@ActiveProfiles("dev")
//@FixMethodOrder(MethodSorters.NAME_ASCENDING)
//public class WxDownloadServiceTest {
//
//    @Autowired
//    private WxDownloadService service;
//
//    @Test
//    public void downloadPolicy() throws InterruptedException {
//        CountDownLatch latch = new CountDownLatch(19);
//        for (int i = 0; i < 20; i++) {
//            final int x = i;
//            new Thread(() -> {
//                try {
//                    service.downloadPolicy("https://www.baoxian.com/console/Shop/Electronic/down.jsp?id=601021107201821648091144669", "601021107201821648091144669", new MockHttpServletResponse());
////                    System.out.println("处理完后" + x);
//                    latch.countDown();
//                } catch (Exception e) {
//                }
//            }).start();
//        }
//        latch.await();
//    }
//}