package com.cfpamf.ms.insur.base.util;

/**
 * 双向脱敏匹配测试程序
 * 测试当输入数据和数据库数据都可能脱敏时的匹配效果
 * 
 * <AUTHOR>
 */
public class BidirectionalMaskMatchTest {
    
    public static void main(String[] args) {
        System.out.println("=== 双向脱敏匹配测试 ===\n");
        
        // 测试场景1：输入脱敏，数据库完整
        testInputMaskedDbComplete();
        
        // 测试场景2：输入完整，数据库脱敏
        testInputCompleteDbMasked();
        
        // 测试场景3：双方都脱敏
        testBothMasked();
        
        // 测试场景4：双方都完整
        testBothComplete();
        
        // 测试场景5：复杂脱敏场景
        testComplexMaskedScenarios();
    }
    
    private static void testInputMaskedDbComplete() {
        System.out.println("场景1：输入脱敏，数据库完整");
        System.out.println("----------------------------------------");
        
        String[][] tests = {
            {"*三", "*A**OW", "张三", "甘A88OW", "姓名车牌号都脱敏 vs 完整"},
            {"张*", "甘*88OW", "张三", "甘A88OW", "部分脱敏 vs 完整"},
            {"***", "******", "张阿三", "甘A88OW", "完全脱敏 vs 完整"}
        };
        
        for (String[] test : tests) {
            boolean result = MaskMatchUtil.matchMaskedPersonInfo(test[0], test[1], test[2], test[3]);
            System.out.printf("输入:(%s,%s) vs 数据库:(%s,%s) => %s (%s)\n", 
                test[0], test[1], test[2], test[3], result ? "匹配" : "不匹配", test[4]);
        }
        System.out.println();
    }
    
    private static void testInputCompleteDbMasked() {
        System.out.println("场景2：输入完整，数据库脱敏");
        System.out.println("----------------------------------------");
        
        String[][] tests = {
            {"张三", "甘A88OW", "*三", "*A**OW", "完整 vs 姓名车牌号都脱敏"},
            {"张三", "甘A88OW", "张*", "甘*88OW", "完整 vs 部分脱敏"},
            {"张阿三", "甘A88OW", "***", "******", "完整 vs 完全脱敏"}
        };
        
        for (String[] test : tests) {
            boolean result = MaskMatchUtil.matchMaskedPersonInfo(test[0], test[1], test[2], test[3]);
            System.out.printf("输入:(%s,%s) vs 数据库:(%s,%s) => %s (%s)\n", 
                test[0], test[1], test[2], test[3], result ? "匹配" : "不匹配", test[4]);
        }
        System.out.println();
    }
    
    private static void testBothMasked() {
        System.out.println("场景3：双方都脱敏");
        System.out.println("----------------------------------------");
        
        String[][] tests = {
            {"*三", "*A**OW", "张*", "甘*88OW", "不同脱敏规则"},
            {"*阿*", "*A*8*W", "*阿三", "甘*88*W", "多位脱敏"},
            {"**三", "***8OW", "*阿*", "甘**8*W", "复杂脱敏"},
            {"*三", "*A**OW", "*四", "*B**OW", "脱敏但不匹配"}
        };
        
        for (String[] test : tests) {
            boolean result = MaskMatchUtil.matchMaskedPersonInfo(test[0], test[1], test[2], test[3]);
            System.out.printf("输入:(%s,%s) vs 数据库:(%s,%s) => %s (%s)\n", 
                test[0], test[1], test[2], test[3], result ? "匹配" : "不匹配", test[4]);
        }
        System.out.println();
    }
    
    private static void testBothComplete() {
        System.out.println("场景4：双方都完整");
        System.out.println("----------------------------------------");
        
        String[][] tests = {
            {"张三", "甘A88OW", "张三", "甘A88OW", "完全相同"},
            {"张三", "甘A88OW", "张三", "甘A99OW", "姓名相同车牌号不同"},
            {"张三", "甘A88OW", "李四", "甘A88OW", "姓名不同车牌号相同"},
            {"张三", "甘A88OW", "李四", "京B99OW", "完全不同"}
        };
        
        for (String[] test : tests) {
            boolean result = MaskMatchUtil.matchMaskedPersonInfo(test[0], test[1], test[2], test[3]);
            System.out.printf("输入:(%s,%s) vs 数据库:(%s,%s) => %s (%s)\n", 
                test[0], test[1], test[2], test[3], result ? "匹配" : "不匹配", test[4]);
        }
        System.out.println();
    }
    
    private static void testComplexMaskedScenarios() {
        System.out.println("场景5：复杂脱敏场景");
        System.out.println("----------------------------------------");
        
        String[][] tests = {
            // 新能源车牌测试
            {"张三", "甘A*88OW", "张三", "甘AD88OW", "新能源车牌脱敏"},
            {"*三", "*AD**OW", "张三", "甘AD88OW", "新能源车牌双向脱敏"},
            
            // 长度不同的脱敏
            {"张*", "甘A*8OW", "张阿三", "甘A88OW", "长度不同的脱敏"},
            {"张", "*A88OW", "张阿三", "甘A88OW", "极端长度差异"},
            
            // 边界情况
            {"", "", "张三", "甘A88OW", "空字符串"},
            {"张三", "", "张三", "甘A88OW", "车牌号为空"},
            {"", "甘A88OW", "张三", "甘A88OW", "姓名为空"},
            
            // 特殊字符
            {"张三", "甘A88OW", "张三", "甘A88OW", "完全匹配基准"},
            {"*", "*", "张", "甘", "单字符脱敏"}
        };
        
        for (String[] test : tests) {
            boolean result = MaskMatchUtil.matchMaskedPersonInfo(test[0], test[1], test[2], test[3]);
            System.out.printf("输入:(%s,%s) vs 数据库:(%s,%s) => %s (%s)\n", 
                test[0], test[1], test[2], test[3], result ? "匹配" : "不匹配", test[4]);
        }
        System.out.println();
    }
}
