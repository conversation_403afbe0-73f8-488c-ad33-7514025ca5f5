package com.cfpamf.ms.insur.admin.riskmanager;

import com.cfpamf.common.ms.util.DateUtil;
import com.cfpamf.ms.bms.facade.vo.JwtUserInfo;
import com.cfpamf.ms.insur.admin.external.tk.TkApiProperties;
import com.cfpamf.ms.insur.admin.pojo.open.TKBizContent;
import com.cfpamf.ms.insur.admin.pojo.open.TKRequest;
import com.cfpamf.ms.insur.admin.service.BaseTest;
import com.cfpamf.ms.insur.admin.service.riskmanager.EnumModelType;
import com.cfpamf.ms.insur.admin.service.riskmanager.RiskManagerService;
import com.cfpamf.ms.insur.base.exception.BizException;
import com.cfpamf.ms.insur.base.exception.ExcptEnum;
import com.cfpamf.ms.insur.base.util.HttpRequestUtil;
import com.cfpamf.ms.insur.base.util.RedisUtil;
import com.cfpamf.ms.insur.base.util.SpringFactoryUtil;
import com.cfpamf.ms.insur.weixin.common.IdGenerator;
import com.github.jsonzou.jmockdata.DataConfig;
import com.github.jsonzou.jmockdata.JMockData;
import com.github.jsonzou.jmockdata.MockConfig;
import com.github.jsonzou.jmockdata.Mocker;
import org.apache.commons.lang3.StringUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.context.ApplicationContext;
import org.springframework.data.redis.core.RedisTemplate;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Random;

@RunWith(PowerMockRunner.class)
@PrepareForTest({HttpRequestUtil.class})
public class RiskManagerServiceTest   extends BaseTest{

    @InjectMocks
    private RiskManagerService riskManagerService;

    @Mock
    private RedisUtil redisUtil;

    @Mock
    private RedisTemplate<String, Object> redisTemplate;

    @Mock
    private TkApiProperties properties;

    private String bizSecret="1001";

    private String appSecret="1002";

    @Before
    public void setUp() throws Exception {
        super.setUp();
        Mockito.when(properties.getRiskManagerAppSecret())
                .thenReturn(appSecret);

        Mockito.when(properties.getRiskManagerBizSecret())
                .thenReturn(bizSecret);
    }


    @Test
    public void calScore(){
        String modelType="healthScore3434";
        TKBizContent content = new TKBizContent();
        content.setModelType(modelType);
        content.setOrderId("123");
        content.setCidNumber("321");

        Integer score=riskManagerService.calScore(modelType,content.getCidNumber());
        System.err.println(score);
        Assert.assertTrue(score!=null);
    }

    private String getModeType(){
        EnumModelType[] typeArray = EnumModelType.values();
        int size = typeArray.length;
        int newSize = new Random().nextInt(size);
        StringBuilder sb = new StringBuilder();
        for(int i=0;i<newSize;i++){
            sb.append(typeArray[i].name());
            sb.append(",");
        }
        return sb.toString();
    }

    @Test
    public void run(){
        String modelType="healthScore";
        EnumModelType model = EnumModelType.getType(modelType);
        for(int i=0;i<200;i++){
            Integer score = model.metic.run();
            System.err.println(score);
        }
    }

}
