//package com.cfpamf.ms.insur.admin.external.picc.api;
//
//import com.cfpamf.ms.insur.admin.external.picc.dto.*;
//import org.junit.FixMethodOrder;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.junit.runners.MethodSorters;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.test.context.ActiveProfiles;
//import org.springframework.test.context.junit4.SpringRunner;
//
//import java.util.Arrays;
//
//@RunWith(SpringRunner.class)
//@SpringBootTest
//@ActiveProfiles("dev")
//@FixMethodOrder(MethodSorters.NAME_ASCENDING)
//public class PiccApiServiceTest {
//
//    @Autowired
//    private PiccApiService client;
//
//    @Test
//    public void submitOrder() {
//
//        ApplyInfo applyInfo = ApplyInfo.builder().generalInfo(
//                GeneralInfo
//                        .builder()
//                        .uuid("hnvrvyyulduhhvIbvvod")
//                        .plateformCode("CPI000414")
//                        .md5Value("0e18824ec7a08590257a0954e36317b3").build())
//                .policyInfos(Arrays.asList(
//                        EndorPolicyInfo
//                                .builder()
//                                .serialNo("1")
//                                .riskCode("EAA")
//                                .payWay("P")
//                                .operateTimes("2018-06-19 14:41:40")
//                                .startDate("2018-06-25")
//                                .endDate("2019-06-26")
//                                .startHour("0")
//                                .endHour("24")
//                                .sumAmount("30000")
//                                .sumPremium("100")
//                                .arguSolution("1")
//                                .quantity("1")
//                                .insuredPlan(InsuredPlan.builder().rationType("EAA3400009").build())
//                                .applicant(
//                                        Applicant.builder()
//                                                .appliName("本山")
//                                                .appliIdType("01")
//                                                .appliIdNo("120222199901019874")
//                                                .appliIdMobile("17600220157")
//                                                .appliSex("1")
//                                                .sendSMS("Y")
//                                                .build()
//                                )
//                                .insureds(Arrays.asList(Insured.builder()
//                                        .insuredSeqNo("1")
//                                        .insuredName("本山")
//                                        .insuredIdType("01")
//                                        .insuredIdNo("120222199901019874")
//                                        .insuredBirthday("2017-02-05")
//                                        .insuredIdMobile("***********")
//                                        .insuredSex("1")
//                                        .creditlevel("1")
//                                        .occupationcode("000103")
//                                        .build()))
//                                .specials(Arrays.asList(
//                                        Special.builder()
//                                                .businessDepartmentCode("34010206")
//                                                .build()
//                                ))
////                                .extendInfos(Arrays.asList(ExtendInfo
////                                        .builder()
////                                        .callbackPage("www.baidu.com")
////                                        .build()))
////                                .guardians(Arrays.asList(
////                                        Guardian.builder()
////                                                .guardianName("李四")
////                                                .guardianSex("01")
////                                                .guardianBirthday("1989-03-12")
////                                                .build()
////                                ))
//                                .build()
//                ))
//                .build();
//
//        ReturnInfo returnInfo = client.submitOrder(applyInfo);
//        System.out.println();
//    }
//}