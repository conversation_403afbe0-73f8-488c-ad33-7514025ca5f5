package com.cfpamf.ms.insur.admin.service.product;

import com.cfpamf.ms.insur.admin.dao.safes.SmProductHistoryMapper;
import com.cfpamf.ms.insur.admin.dao.safes.SmProductMapper;
import com.cfpamf.ms.insur.admin.dao.safes.SmProductVersionMapper;
import com.cfpamf.ms.insur.admin.dao.safes.product.*;
import com.cfpamf.ms.insur.admin.dao.safes.product.risk.SysRiskAmountMapper;
import com.cfpamf.ms.insur.admin.dao.safes.product.risk.SysRiskDutyAmountMapper;
import com.cfpamf.ms.insur.admin.dao.safes.product.risk.SysRiskDutyMapper;
import com.cfpamf.ms.insur.admin.dao.safes.product.risk.SysRiskFactorFileMapper;
import com.cfpamf.ms.insur.admin.pojo.form.product.SmPlanForm;
import com.cfpamf.ms.insur.admin.pojo.form.product.SmPlanRiskForm;
import com.cfpamf.ms.insur.admin.pojo.vo.SmProductDetailVO;
import com.cfpamf.ms.insur.admin.service.BaseTest;
import com.cfpamf.ms.insur.admin.service.product.impl.SmLongInsurancePlanServiceImpl;
import com.cfpamf.ms.insur.base.exception.BizException;
import com.cfpamf.ms.insur.base.util.HttpRequestUtil;
import com.github.jsonzou.jmockdata.JMockData;
import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

/**
 * <AUTHOR>
 * @date 2021/9/7 15:56
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({HttpRequestUtil.class})
public class SmLongInsurancePlanServiceTest extends BaseTest {

    @Mock
    SmProductMapper smProductMapper;
    @Mock
    SmProductHistoryMapper smProductHistoryMapper;
    @Mock
    SmProductRiskService smProductRiskService;
    @Mock
    PlanMapper planMapper;
    @Mock
    SmPlanHistoryMapper smPlanHistoryMapper;
    @Mock
    SysRiskDutyMapper sysRiskDutyMapper;
    @Mock
    SmProductVersionMapper productVersionMapper;
    @Mock
    SmPlanRiskHistoryMapper smPlanRiskHistoryMapper;
    @Mock
    SmPlanRiskMapper smPlanRiskMapper;
    @Mock
    SmPlanRiskDutyHistoryMapper smPlanRiskDutyHistoryMapper;
    @Mock
    SmPlanRiskDutyMapper smPlanRiskDutyMapper;
    @Mock
    SysRiskDutyAmountMapper sysRiskDutyAmountMapper;
    @Mock
    SysRiskAmountMapper sysRiskAmountMapper;
    @Mock
    SysRiskFactorFileMapper sysRiskFactorFileMapper;

    @InjectMocks
    SmLongInsurancePlanServiceImpl smLongInsurancePlanService;

//    @Before
//    public void before() {
//        smLongInsurancePlanService = new SmLongInsurancePlanServiceImpl(smProductMapper, smProductHistoryMapper, smProductRiskService, planMapper, smPlanHistoryMapper, sysRiskDutyMapper, productVersionMapper, smPlanRiskHistoryMapper, smPlanRiskMapper, smPlanRiskDutyHistoryMapper, smPlanRiskDutyMapper, sysRiskDutyAmountMapper, sysRiskAmountMapper, sysRiskFactorFileMapper);
//    }

    @Test
    public void testSaveRiskAndDuty() {
        SmPlanRiskForm riskForm = JMockData.mock(SmPlanRiskForm.class);
        Mockito.when(smPlanRiskMapper.insertUseGeneratedKeys(Mockito.any())).thenReturn(1);
        Mockito.when(smPlanHistoryMapper.insert(Mockito.any())).thenReturn(1);
        Integer id = smLongInsurancePlanService.saveRiskAndDuty(1, Lists.newArrayList(riskForm), "test", 1, 1);
        Assert.assertEquals(id, Integer.valueOf(1));
    }

    @Test
    public void testSave() {
        SmPlanForm smPlanForm = JMockData.mock(SmPlanForm.class);
        SmProductDetailVO productDetailVO = JMockData.mock(SmProductDetailVO.class);
        int productId = 1;
        Mockito.when(smProductMapper.getProductById(productId)).thenReturn(productDetailVO);
        try {
            smLongInsurancePlanService.save(productId, smPlanForm);
        } catch (BizException e) {
            Assert.assertEquals(e.getCode(), "801042");
        }

    }

    @Test
    public void testUpdate() {
        SmPlanForm smPlanForm = JMockData.mock(SmPlanForm.class);
        SmProductDetailVO productDetailVO = JMockData.mock(SmProductDetailVO.class);
        int productId = 1;
        Mockito.when(smProductMapper.getProductById(productId)).thenReturn(productDetailVO);
        try {
            smLongInsurancePlanService.update(productId, smPlanForm);
        } catch (BizException e) {
            Assert.assertEquals(e.getCode(), "801042");
        }
    }

}
