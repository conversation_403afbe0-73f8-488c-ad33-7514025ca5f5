package com.cfpamf.ms.insur.base.util;

import org.junit.Test;
import static org.junit.Assert.*;

/**
 * 脱敏匹配工具类测试
 * 
 * <AUTHOR>
 */
public class MaskMatchUtilTest {

    @Test
    public void testMatchMaskedName() {
        // 测试完全相同的情况
        assertTrue(MaskMatchUtil.matchMaskedName("张三", "张三"));
        
        // 测试脱敏匹配的情况
        assertTrue(MaskMatchUtil.matchMaskedName("*三", "张三"));
        assertTrue(MaskMatchUtil.matchMaskedName("张*", "张三"));
        assertTrue(MaskMatchUtil.matchMaskedName("*三", "*三"));
        
        // 测试不匹配的情况
        assertFalse(MaskMatchUtil.matchMaskedName("张三", "李四"));
        assertFalse(MaskMatchUtil.matchMaskedName("*三", "李*"));
        
        // 测试空值情况
        assertFalse(MaskMatchUtil.matchMaskedName(null, "张三"));
        assertFalse(MaskMatchUtil.matchMaskedName("张三", null));
        assertFalse(MaskMatchUtil.matchMaskedName("", "张三"));
        
        // 测试复杂脱敏情况
        assertTrue(MaskMatchUtil.matchMaskedName("*阿三", "张阿三"));
        assertTrue(MaskMatchUtil.matchMaskedName("张*三", "张阿三"));
    }

    @Test
    public void testMatchMaskedIdNumber() {
        // 测试完全相同的情况
        assertTrue(MaskMatchUtil.matchMaskedIdNumber("123456789012345678", "123456789012345678"));
        
        // 测试脱敏情况（由于证件号可能是虚拟的，不进行匹配）
        assertFalse(MaskMatchUtil.matchMaskedIdNumber("1*********78", "123456789012345678"));
        
        // 测试空值情况
        assertFalse(MaskMatchUtil.matchMaskedIdNumber(null, "123456789012345678"));
        assertFalse(MaskMatchUtil.matchMaskedIdNumber("123456789012345678", null));
    }

    @Test
    public void testMatchMaskedPersonInfo() {
        // 测试姓名匹配的情况
        assertTrue(MaskMatchUtil.matchMaskedPersonInfo("*三", "1*********78", "张三", "123456789012345678"));
        
        // 测试证件号匹配的情况
        assertTrue(MaskMatchUtil.matchMaskedPersonInfo("李四", "123456789012345678", "张三", "123456789012345678"));
        
        // 测试都不匹配的情况
        assertFalse(MaskMatchUtil.matchMaskedPersonInfo("李四", "1*********99", "张三", "123456789012345678"));
        
        // 测试姓名匹配但证件号不匹配的情况
        assertTrue(MaskMatchUtil.matchMaskedPersonInfo("*三", "1*********99", "张三", "123456789012345678"));
    }

    @Test
    public void testRealWorldScenarios() {
        // 模拟真实场景：第一年和第二年在同一家公司都脱敏，完全一致
        assertTrue(MaskMatchUtil.matchMaskedPersonInfo("*阿三", "*A**OW", "张阿三", "甘A88OW"));
        
        // 模拟真实场景：换了公司，脱敏规则不一样
        assertTrue(MaskMatchUtil.matchMaskedPersonInfo("*阿三", "*A**OW", "张*三", "甘*OW"));
        
        // 模拟真实场景：脱敏失败的情况
        assertFalse(MaskMatchUtil.matchMaskedPersonInfo("*阿三", "*A**OW", "李*四", "京*OW"));
    }
}
