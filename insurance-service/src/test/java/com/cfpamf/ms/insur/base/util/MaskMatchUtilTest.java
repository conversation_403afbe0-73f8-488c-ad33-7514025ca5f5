package com.cfpamf.ms.insur.base.util;

import org.junit.Test;
import static org.junit.Assert.*;

/**
 * 脱敏匹配工具类测试
 * 测试新的相似度算法和正则表达式匹配
 *
 * <AUTHOR>
 */
public class MaskMatchUtilTest {

    @Test
    public void testMatchMaskedName() {
        // 测试完全相同的情况
        assertTrue(MaskMatchUtil.matchMaskedName("张三", "张三"));

        // 测试正则表达式匹配的情况
        assertTrue(MaskMatchUtil.matchMaskedName("*三", "张三"));
        assertTrue(MaskMatchUtil.matchMaskedName("张*", "张三"));
        assertTrue(MaskMatchUtil.matchMaskedName("*阿三", "张阿三"));
        assertTrue(MaskMatchUtil.matchMaskedName("张*三", "张阿三"));

        // 测试多个脱敏字符的情况
        assertTrue(MaskMatchUtil.matchMaskedName("**三", "张阿三"));
        assertTrue(MaskMatchUtil.matchMaskedName("*阿*", "张阿三"));

        // 测试不匹配的情况
        assertFalse(MaskMatchUtil.matchMaskedName("张三", "李四"));
        assertFalse(MaskMatchUtil.matchMaskedName("*四", "张三"));

        // 测试空值情况
        assertFalse(MaskMatchUtil.matchMaskedName(null, "张三"));
        assertFalse(MaskMatchUtil.matchMaskedName("张三", null));
        assertFalse(MaskMatchUtil.matchMaskedName("", "张三"));
    }

    @Test
    public void testMatchMaskedPlateNumber() {
        // 测试完全相同的情况
        assertTrue(MaskMatchUtil.matchMaskedPlateNumber("甘A88OW", "甘A88OW"));

        // 测试脱敏匹配的情况
        assertTrue(MaskMatchUtil.matchMaskedPlateNumber("*A**OW", "甘A88OW"));
        assertTrue(MaskMatchUtil.matchMaskedPlateNumber("甘*8OW", "甘A88OW"));
        assertTrue(MaskMatchUtil.matchMaskedPlateNumber("甘A*8*W", "甘A88OW"));

        // 测试不匹配的情况
        assertFalse(MaskMatchUtil.matchMaskedPlateNumber("*B**OW", "甘A88OW"));
        assertFalse(MaskMatchUtil.matchMaskedPlateNumber("京*8OW", "甘A88OW"));

        // 测试空值情况
        assertFalse(MaskMatchUtil.matchMaskedPlateNumber(null, "甘A88OW"));
        assertFalse(MaskMatchUtil.matchMaskedPlateNumber("甘A88OW", null));
    }

    @Test
    public void testMatchMaskedPersonInfo() {
        // 测试姓名和车牌号都匹配的情况
        assertTrue(MaskMatchUtil.matchMaskedPersonInfo("*三", "*A**OW", "张三", "甘A88OW"));

        // 测试姓名匹配但车牌号不完全匹配的情况（综合相似度仍然较高）
        assertTrue(MaskMatchUtil.matchMaskedPersonInfo("*三", "*A**OW", "张三", "甘A99OW"));

        // 测试都不匹配的情况
        assertFalse(MaskMatchUtil.matchMaskedPersonInfo("李四", "*B**OW", "张三", "甘A88OW"));
    }

    @Test
    public void testRealWorldScenarios() {
        // 模拟真实场景：投保人姓名和车牌号脱敏匹配
        assertTrue(MaskMatchUtil.matchMaskedPersonInfo("*阿三", "*A**OW", "张阿三", "甘A88OW"));

        // 模拟真实场景：不同脱敏规则
        assertTrue(MaskMatchUtil.matchMaskedPersonInfo("*阿三", "*A**OW", "张*三", "甘*8OW"));

        // 模拟真实场景：完全不匹配
        assertFalse(MaskMatchUtil.matchMaskedPersonInfo("*阿三", "*A**OW", "李*四", "京*9OW"));

        // 模拟真实场景：长度不同的脱敏
        assertTrue(MaskMatchUtil.matchMaskedPersonInfo("张*", "*A88OW", "张阿三", "甘A88OW"));

        // 模拟真实场景：车牌号完全脱敏
        assertTrue(MaskMatchUtil.matchMaskedPersonInfo("张三", "******", "张三", "甘A88OW"));

        // 模拟真实场景：姓名完全脱敏但车牌号匹配
        assertTrue(MaskMatchUtil.matchMaskedPersonInfo("***", "甘A88OW", "张阿三", "甘A88OW"));
    }

    @Test
    public void testEdgeCases() {
        // 测试全部脱敏的情况
        assertTrue(MaskMatchUtil.matchMaskedPersonInfo("***", "******", "张阿三", "甘A88OW"));

        // 测试空字符串
        assertFalse(MaskMatchUtil.matchMaskedPersonInfo("", "", "张三", "123456"));

        // 测试单字符匹配
        assertTrue(MaskMatchUtil.matchMaskedPersonInfo("*", "*", "张", "1"));

        // 测试长度差异很大的情况
        assertFalse(MaskMatchUtil.matchMaskedPersonInfo("张", "123456789012345678", "张阿三李四王五", "1"));
    }
}
