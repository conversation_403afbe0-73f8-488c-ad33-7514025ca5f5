package com.cfpamf.ms.insur.base.util;

import org.junit.Test;
import static org.junit.Assert.*;

/**
 * 脱敏匹配工具类测试
 * 测试新的相似度算法和正则表达式匹配
 *
 * <AUTHOR>
 */
public class MaskMatchUtilTest {

    @Test
    public void testMatchMaskedName() {
        // 测试完全相同的情况
        assertTrue(MaskMatchUtil.matchMaskedName("张三", "张三"));

        // 测试正则表达式匹配的情况
        assertTrue(MaskMatchUtil.matchMaskedName("*三", "张三"));
        assertTrue(MaskMatchUtil.matchMaskedName("张*", "张三"));
        assertTrue(MaskMatchUtil.matchMaskedName("*阿三", "张阿三"));
        assertTrue(MaskMatchUtil.matchMaskedName("张*三", "张阿三"));

        // 测试多个脱敏字符的情况
        assertTrue(MaskMatchUtil.matchMaskedName("**三", "张阿三"));
        assertTrue(MaskMatchUtil.matchMaskedName("*阿*", "张阿三"));

        // 测试不匹配的情况
        assertFalse(MaskMatchUtil.matchMaskedName("张三", "李四"));
        assertFalse(MaskMatchUtil.matchMaskedName("*四", "张三"));

        // 测试空值情况
        assertFalse(MaskMatchUtil.matchMaskedName(null, "张三"));
        assertFalse(MaskMatchUtil.matchMaskedName("张三", null));
        assertFalse(MaskMatchUtil.matchMaskedName("", "张三"));
    }

    @Test
    public void testMatchMaskedIdNumber() {
        // 测试完全相同的情况
        assertTrue(MaskMatchUtil.matchMaskedIdNumber("123456789012345678", "123456789012345678"));

        // 测试脱敏匹配的情况
        assertTrue(MaskMatchUtil.matchMaskedIdNumber("1*********78", "123456789012345678"));
        assertTrue(MaskMatchUtil.matchMaskedIdNumber("12345*****5678", "123456789012345678"));

        // 测试不匹配的情况
        assertFalse(MaskMatchUtil.matchMaskedIdNumber("1*********99", "123456789012345678"));

        // 测试空值情况
        assertFalse(MaskMatchUtil.matchMaskedIdNumber(null, "123456789012345678"));
        assertFalse(MaskMatchUtil.matchMaskedIdNumber("123456789012345678", null));
    }

    @Test
    public void testMatchMaskedPersonInfo() {
        // 测试姓名和证件号都匹配的情况
        assertTrue(MaskMatchUtil.matchMaskedPersonInfo("*三", "1*********78", "张三", "123456789012345678"));

        // 测试姓名匹配但证件号不完全匹配的情况（综合相似度仍然较高）
        assertTrue(MaskMatchUtil.matchMaskedPersonInfo("*三", "1*********99", "张三", "123456789012345678"));

        // 测试都不匹配的情况
        assertFalse(MaskMatchUtil.matchMaskedPersonInfo("李四", "9*********99", "张三", "123456789012345678"));
    }

    @Test
    public void testRealWorldScenarios() {
        // 模拟真实场景：车牌号脱敏匹配
        assertTrue(MaskMatchUtil.matchMaskedPersonInfo("*阿三", "*A**OW", "张阿三", "甘A88OW"));

        // 模拟真实场景：不同脱敏规则
        assertTrue(MaskMatchUtil.matchMaskedPersonInfo("*阿三", "*A**OW", "张*三", "甘*8OW"));

        // 模拟真实场景：完全不匹配
        assertFalse(MaskMatchUtil.matchMaskedPersonInfo("*阿三", "*A**OW", "李*四", "京*9OW"));

        // 模拟真实场景：长度不同的脱敏
        assertTrue(MaskMatchUtil.matchMaskedPersonInfo("张*", "*A88OW", "张阿三", "甘A88OW"));
    }

    @Test
    public void testEdgeCases() {
        // 测试全部脱敏的情况
        assertTrue(MaskMatchUtil.matchMaskedPersonInfo("***", "******", "张阿三", "甘A88OW"));

        // 测试空字符串
        assertFalse(MaskMatchUtil.matchMaskedPersonInfo("", "", "张三", "123456"));

        // 测试单字符匹配
        assertTrue(MaskMatchUtil.matchMaskedPersonInfo("*", "*", "张", "1"));

        // 测试长度差异很大的情况
        assertFalse(MaskMatchUtil.matchMaskedPersonInfo("张", "123456789012345678", "张阿三李四王五", "1"));
    }
}
