//package com.cfpamf.ms.insur.admin.job;
//
//import org.junit.FixMethodOrder;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.junit.runners.MethodSorters;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.jdbc.core.JdbcTemplate;
//import org.springframework.test.context.ActiveProfiles;
//import org.springframework.test.context.junit4.SpringRunner;
//
//
///*@RunWith(SpringRunner.class)
//@SpringBootTest
//@ActiveProfiles("dev")
//@FixMethodOrder(MethodSorters.NAME_ASCENDING)*/
//public class SmCustomerExtractHandlerTest extends AbstractJobTest {
//
//    /*@Autowired
//    private SmCustomerExtractHandler handler;
//
//    @Autowired
//    private JdbcTemplate template;
//
//    @Test
//    public void execute() {
//        template.execute("UPDATE sm_order SET extractFlag=NULL WHERE fhOrderId = '18982079EubPTT06'");
//        testJob(() -> handler.execute("18982079EubPTT06"));
//    }*/
//}