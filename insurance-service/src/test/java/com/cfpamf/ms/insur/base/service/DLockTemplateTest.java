package com.cfpamf.ms.insur.base.service;

import org.junit.FixMethodOrder;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.MethodSorters;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.concurrent.*;

/*@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("dev")
@FixMethodOrder(MethodSorters.NAME_ASCENDING)*/
public class DLockTemplateTest {

    /*String lockKey = "testLockKey";
    @Autowired
    private DLockTemplate lockTemplate;

    @Test
    public void tryLock() throws BrokenBarrierException, InterruptedException {
//        System.out.println(lockTemplate.tryLock(lockKey, 60));
//        lockTemplate.unLock(lockKey);
        System.out.println(lockTemplate.tryLock(lock<PERSON><PERSON>, 60));
        lockTemplate.unLock(lockKey);

        CyclicBarrier cyclicBarrier = new CyclicBarrier(10);
        CountDownLatch downLatch = new CountDownLatch(10);
        ExecutorService executorService = Executors.newFixedThreadPool(10);
        for (int i = 0; i < 10; i++) {
            final int index = i + 1;
            executorService.submit(() -> {
                try {
                    downLatch.countDown();
                    boolean success = lockTemplate.tryLock(lockKey, 60);
                    Thread.sleep(10);
                    if (success) {
                        System.out.println("线程" + index + "获得锁");
                    } else {
                        System.out.println("线程" + index + "未获得锁");
                    }
                } catch (InterruptedException e) {
                    e.printStackTrace();
                } finally {
                    System.out.println("线程" + index + "释放锁");
                    lockTemplate.unLock(lockKey);
                }
                System.out.println("线程" + index + "tryLock complete ");
                try {
                    cyclicBarrier.await();
                } catch (InterruptedException e) {
                    e.printStackTrace();
                } catch (BrokenBarrierException e) {
                    e.printStackTrace();
                }
            });
        }
        cyclicBarrier.await();
        System.out.println("test tryLock complete ");
    }

    @Test
    public void lock() throws BrokenBarrierException, InterruptedException {
        System.out.println(lockTemplate.tryLock(lockKey, 60));
        lockTemplate.unLock(lockKey);

        CyclicBarrier cyclicBarrier = new CyclicBarrier(11);
        CountDownLatch downLatch = new CountDownLatch(10);
        ExecutorService executorService = Executors.newFixedThreadPool(10);
        for (int i = 0; i < 10; i++) {
            final int index = i + 1;
            executorService.submit(() -> {
                try {
                    downLatch.countDown();
                    boolean success = lockTemplate.lock(lockKey, 60);
                    Thread.sleep(100);
                    if (success) {
                        System.out.println("线程" + index + "获得锁");
                    } else {
                        System.out.println("线程" + index + "未获得锁");
                    }
                } catch (InterruptedException e) {
                    e.printStackTrace();
                } finally {
                    System.out.println("线程" + index + "释放锁");
                    lockTemplate.unLock(lockKey);
                }
                try {
                    cyclicBarrier.await();
                } catch (InterruptedException e) {
                    e.printStackTrace();
                } catch (BrokenBarrierException e) {
                    e.printStackTrace();
                }
            });
        }
        cyclicBarrier.await();
        System.out.println("test lock complete ");
    }

    @Test
    public void unLock() {
    }*/
}