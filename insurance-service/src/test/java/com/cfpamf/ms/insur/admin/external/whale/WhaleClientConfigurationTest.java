package com.cfpamf.ms.insur.admin.external.whale;

import com.github.jsonzou.jmockdata.JMockData;
import org.junit.Test;

import static org.junit.Assert.*;

/**
 * <AUTHOR> 2022/3/21 14:11
 */
public class WhaleClientConfigurationTest {

    WhaleClientConfiguration w = new WhaleClientConfiguration();

    @Test
    public void whaleOrderClient() {
        WhaleApiProperties mock = JMockData.mock(WhaleApiProperties.class);
        mock.setDomain("http://www.baidu.com");
        w.whaleOrderClient(mock);
    }
}
