package com.cfpamf.ms.insur.base.activiti;

import com.cfpamf.ms.insur.admin.service.BaseTest;
import com.cfpamf.ms.insur.base.util.HttpRequestUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

/**
 * <AUTHOR> 2020/3/11 10:03
 */
@Slf4j
@RunWith(PowerMockRunner.class)
@PrepareForTest({HttpRequestUtil.class})
public class ActivitiCommonServiceTest extends BaseTest {
    @InjectMocks
    ActivitiCommonService activitiCommonService;
    @Mock
    org.activiti.engine.ProcessEngine processEngine;
    @Mock
    org.activiti.engine.RuntimeService runtimeService;
    @Mock
    org.activiti.engine.TaskService taskService;
    @Mock
    org.activiti.engine.RepositoryService repositoryService;
    @Mock
    org.activiti.engine.FormService formService;
    @Mock
    org.activiti.engine.IdentityService identityService;
    @Mock
    org.activiti.engine.HistoryService historyService;
    @Mock
    com.cfpamf.ms.insur.base.mapper.ActivitiMapper activitiMapper;

    @Test
    public void getLastEndUserTask() {
        activitiCommonService.getLastEndUserTask(",SgucS,");
    }
//
//    @Test
//    public void findProcessPath() {
//        activitiCommonService.findProcessPath(",wuwiM,", JMockData.mock(boolean.class));
//    }
//
//    @Test
//    public void executeJob() {
//        activitiCommonService.executeJob(",GiRff,");
//    }
//
//    @Test
//    public void isSeqOrGateWay() {
//        activitiCommonService.isSeqOrGateWay(",HfYjo,");
//    }
//
//    @Test
//    public void findDefaultPath() {
//        activitiCommonService.findDefaultPath(",DtOut,");
//    }
//
//    @Test
//    public void listTaskEndTime() {
//        activitiCommonService.listTaskEndTime(Arrays.asList("!,", "2", "#", "5"));
//    }
//
//    @Test
//    public void deployProcess() {
//        activitiCommonService.deployProcess(",cQLiw,");
//    }
//
//    @Test
//    public void startProcessInstanceByKey() {
//        activitiCommonService.startProcessInstanceByKey(",LhMkT,", ",gZRER,", JMockData.mock(java.util.Map.class));
//    }
//
//    @Test
//    public void completeTask() {
//        activitiCommonService.completeTask(",nVNuP,", ",ZPELh,", ",sSmtU,", ",lyPEC,", JMockData.mock(java.util.Map.class));
//    }
//
//    @Test
//    public void completeTask1() {
//        activitiCommonService.completeTask(JMockData.mock(org.activiti.engine.task.Task.class), ",SpVbW,", ",ouSeu,", JMockData.mock(java.util.Map.class), JMockData.mock(boolean.class));
//    }
//
//    @Test
//    public void completeTask2() {
//        activitiCommonService.completeTask(",oLhvp,", ",WgedJ,", ",zeXHn,", JMockData.mock(java.util.Map.class));
//    }
//
//    @Test
//    public void completeTask3() {
//        activitiCommonService.completeTask(",RqPfW,", ",rkFfe,", ",YKRbN,", JMockData.mock(java.util.Map.class), JMockData.mock(boolean.class));
//    }
//
//    @Test
//    public void completeTask4() {
//        activitiCommonService.completeTask(",uCBwT,", ",VXtMu,", ",kLDBN,", JMockData.mock(java.util.Map.class), JMockData.mock(java.util.function.Consumer.class), JMockData.mock(boolean.class));
//    }
//
//    @Test
//    public void setCandidateUser() {
//        activitiCommonService.setCandidateUser(",OUuqb,", ",qSXkL,");
//    }
//
//    @Test
//    public void jump() {
//        activitiCommonService.jump(",eieIB,", ",TBfSW,", ",EfkCa,", ",eMUQE,");
//    }
//
//    @Test
//    public void deleteProcessInstance() {
//        activitiCommonService.deleteProcessInstance(",LKgWn,", ",qrgwm,");
//    }
//
//    @Test
//    public void getVariable() {
//        activitiCommonService.getVariable(",PacoL,", JMockData.mock(java.lang.Class.class));
//    }
////
//    @Test
//    public void getDeleteReason() {
//        activitiCommonService.getDeleteReason(",vvTLb,");
//    }
}
