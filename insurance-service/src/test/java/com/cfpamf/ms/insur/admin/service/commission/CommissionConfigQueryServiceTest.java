package com.cfpamf.ms.insur.admin.service.commission;

import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.ms.insur.admin.dao.safes.SmCommissionMapper;
import com.cfpamf.ms.insur.admin.dao.safes.SmProductMapper;
import com.cfpamf.ms.insur.admin.dao.safes.SmProductVersionMapper;
import com.cfpamf.ms.insur.admin.dao.safes.commission.SystemCommissionConfigDetailMapper;
import com.cfpamf.ms.insur.admin.dao.safes.commission.SystemCommissionConfigMapper;
import com.cfpamf.ms.insur.admin.dao.safes.product.PlanMapper;
import com.cfpamf.ms.insur.admin.dao.safes.product.SmPlanHistoryMapper;
import com.cfpamf.ms.insur.admin.dao.safes.product.SmPlanRiskHistoryMapper;
import com.cfpamf.ms.insur.admin.pojo.dto.SmCommissionSettingDTO;
import com.cfpamf.ms.insur.admin.pojo.dto.commission.CommissionCalcConfigDetailDTO;
import com.cfpamf.ms.insur.admin.pojo.dto.commission.CommissionConfigDetailDTO;
import com.cfpamf.ms.insur.admin.pojo.dto.commission.CommissionConfigItemDTO;
import com.cfpamf.ms.insur.admin.pojo.dto.commission.CommissionRateDTO;
import com.cfpamf.ms.insur.admin.pojo.dto.product.PlanRiskDTO;
import com.cfpamf.ms.insur.admin.pojo.po.commission.SystemCommissionConfig;
import com.cfpamf.ms.insur.admin.pojo.po.commission.SystemCommissionConfigDetail;
import com.cfpamf.ms.insur.admin.pojo.po.product.SmPlanHistory;
import com.cfpamf.ms.insur.admin.pojo.query.commission.SystemCommissionConfigQuery;
import com.cfpamf.ms.insur.admin.pojo.vo.SmProductDetailVO;
import com.cfpamf.ms.insur.admin.pojo.vo.commission.CommissionConfigDetailVO;
import com.cfpamf.ms.insur.admin.pojo.vo.commission.CommissionConfigVO;
import com.cfpamf.ms.insur.admin.pojo.vo.product.CommissionConfigPlanVO;
import com.cfpamf.ms.insur.admin.pojo.vo.product.CommissionConfigProductVO;
import com.cfpamf.ms.insur.admin.pojo.vo.product.PlanRiskInfoVO;
import com.cfpamf.ms.insur.admin.service.SmProductService;
import com.cfpamf.ms.insur.admin.service.product.SysRiskService;
import com.github.pagehelper.PageInfo;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class CommissionConfigQueryServiceTest {

    @Mock
    private SystemCommissionConfigMapper mockSystemCommissionConfigMapper;
    @Mock
    private SystemCommissionConfigDetailMapper mockDetailMapper;
    @Mock
    private SysRiskService mockSysRiskService;
    @Mock
    private SmProductService mockSmProductService;
    @Mock
    private SmProductVersionMapper mockProductVersionMapper;
    @Mock
    private SmProductMapper mockSmProductMapper;
    @Mock
    private PlanMapper mockPlanMapper;
    @Mock
    private SmPlanRiskHistoryMapper mockSmPlanRiskHistoryMapper;
    @Mock
    private SmPlanHistoryMapper mockSmPlanHistoryMapper;
    @Mock
    private SmCommissionMapper mockOldSmCommissionMapper;

    @Mock
    private CommissionConfigQueryService commissionConfigQueryServiceUnderTest;

    @Test
    public void testListCommissionConfig() {
        // Setup
        final SystemCommissionConfigQuery query = new SystemCommissionConfigQuery();
        query.setPage(0);
        query.setSize(0);
        query.setAll(false);
        query.setCompanyId(0);
        query.setProductId(0);

        // Configure SystemCommissionConfigMapper.listCommissionConfig(...).
        final CommissionConfigVO commissionConfigVO = new CommissionConfigVO();
        commissionConfigVO.setId(0);
        commissionConfigVO.setProductId(0);
        commissionConfigVO.setProductName("productName");
        commissionConfigVO.setCompanyId(0);
        commissionConfigVO.setCompanyName("companyName");
        final List<CommissionConfigVO> commissionConfigVOS = Arrays.asList(commissionConfigVO);
        final SystemCommissionConfigQuery query1 = new SystemCommissionConfigQuery();
        query1.setPage(0);
        query1.setSize(0);
        query1.setAll(false);
        query1.setCompanyId(0);
        query1.setProductId(0);
        mockSystemCommissionConfigMapper.listCommissionConfig(query1);

        // Run the test
        commissionConfigQueryServiceUnderTest.listCommissionConfig(query);
    }

    @Test
    public void testListCommissionConfig_SystemCommissionConfigMapperReturnsNoItems() {
        // Setup
        final SystemCommissionConfigQuery query = new SystemCommissionConfigQuery();
        query.setPage(0);
        query.setSize(0);
        query.setAll(false);
        query.setCompanyId(0);
        query.setProductId(0);

        // Configure SystemCommissionConfigMapper.listCommissionConfig(...).
        final SystemCommissionConfigQuery query1 = new SystemCommissionConfigQuery();
        query1.setPage(0);
        query1.setSize(0);
        query1.setAll(false);
        query1.setCompanyId(0);
        query1.setProductId(0);
        mockSystemCommissionConfigMapper.listCommissionConfig(query1);

        // Run the test
        commissionConfigQueryServiceUnderTest.listCommissionConfig(query);
    }

    @Test
    public void testDetailConfig() {
        // Setup
        final CommissionConfigDetailVO expectedResult = new CommissionConfigDetailVO();
        expectedResult.setCommissionFactorLst(Arrays.asList("value"));
        expectedResult.setSendPeriod(0);
        final CommissionConfigItemDTO itemDTO = new CommissionConfigItemDTO();
        itemDTO.setItemId(0);
        itemDTO.setPeriodNum(0);
        itemDTO.setPlanId(0);
        itemDTO.setRiskId(0);
        itemDTO.setPayWayList(Arrays.asList(0));
        itemDTO.setCoveredYearsList(Arrays.asList("value"));
        itemDTO.setValidPeriodList(Arrays.asList("value"));
        itemDTO.setContinuationRate(new BigDecimal("0.00"));
        itemDTO.setCommissionRate(new BigDecimal("0.00"));
        expectedResult.setFirstPeriodList(Arrays.asList(itemDTO));
        final CommissionConfigItemDTO itemDTO1 = new CommissionConfigItemDTO();
        itemDTO1.setItemId(0);
        itemDTO1.setPeriodNum(0);
        itemDTO1.setPlanId(0);
        itemDTO1.setRiskId(0);
        itemDTO1.setPayWayList(Arrays.asList(0));
        itemDTO1.setCoveredYearsList(Arrays.asList("value"));
        itemDTO1.setValidPeriodList(Arrays.asList("value"));
        itemDTO1.setContinuationRate(new BigDecimal("0.00"));
        itemDTO1.setCommissionRate(new BigDecimal("0.00"));
        expectedResult.setOtherPeriodList(Arrays.asList(itemDTO1));

        // Configure SystemCommissionConfigMapper.selectByPrimaryKey(...).
        final SystemCommissionConfig systemCommissionConfig = new SystemCommissionConfig();
        systemCommissionConfig.setId(0);
        systemCommissionConfig.setEnabledFlag(0);
        systemCommissionConfig.setType(0);
        systemCommissionConfig.setProductId(0);
        systemCommissionConfig.setRelatedCommissionFactor("relatedCommissionFactor");
        systemCommissionConfig.setReleaseFlag(0);
        mockSystemCommissionConfigMapper.selectByPrimaryKey(0);

        // Configure SystemCommissionConfigDetailMapper.listDetailByConfigId(...).
        final SystemCommissionConfigDetail systemCommissionConfigDetail = new SystemCommissionConfigDetail();
        systemCommissionConfigDetail.setId(0);
        systemCommissionConfigDetail.setEnabledFlag(0);
        systemCommissionConfigDetail.setPeriodNum(0);
        systemCommissionConfigDetail.setPlanId(0);
        systemCommissionConfigDetail.setRiskId(0);
        systemCommissionConfigDetail.setPayWay("payWay");
        systemCommissionConfigDetail.setCoveredYears("coveredYears");
        systemCommissionConfigDetail.setValidPeriod("validPeriod");
        systemCommissionConfigDetail.setContinuationRate(new BigDecimal("0.00"));
        systemCommissionConfigDetail.setCommissionRate(new BigDecimal("0.00"));
        final List<SystemCommissionConfigDetail> systemCommissionConfigDetails = Arrays.asList(
                systemCommissionConfigDetail);
        mockDetailMapper.listDetailByConfigId(0);

        // Run the test
        commissionConfigQueryServiceUnderTest.detailConfig(0);
    }

    @Test
    public void testDetailConfig_SystemCommissionConfigDetailMapperReturnsNoItems() {
        // Setup
        final CommissionConfigDetailVO expectedResult = new CommissionConfigDetailVO();
        expectedResult.setCommissionFactorLst(Arrays.asList("value"));
        expectedResult.setSendPeriod(0);
        final CommissionConfigItemDTO itemDTO = new CommissionConfigItemDTO();
        itemDTO.setItemId(0);
        itemDTO.setPeriodNum(0);
        itemDTO.setPlanId(0);
        itemDTO.setRiskId(0);
        itemDTO.setPayWayList(Arrays.asList(0));
        itemDTO.setCoveredYearsList(Arrays.asList("value"));
        itemDTO.setValidPeriodList(Arrays.asList("value"));
        itemDTO.setContinuationRate(new BigDecimal("0.00"));
        itemDTO.setCommissionRate(new BigDecimal("0.00"));
        expectedResult.setFirstPeriodList(Arrays.asList(itemDTO));
        final CommissionConfigItemDTO itemDTO1 = new CommissionConfigItemDTO();
        itemDTO1.setItemId(0);
        itemDTO1.setPeriodNum(0);
        itemDTO1.setPlanId(0);
        itemDTO1.setRiskId(0);
        itemDTO1.setPayWayList(Arrays.asList(0));
        itemDTO1.setCoveredYearsList(Arrays.asList("value"));
        itemDTO1.setValidPeriodList(Arrays.asList("value"));
        itemDTO1.setContinuationRate(new BigDecimal("0.00"));
        itemDTO1.setCommissionRate(new BigDecimal("0.00"));
        expectedResult.setOtherPeriodList(Arrays.asList(itemDTO1));

        // Configure SystemCommissionConfigMapper.selectByPrimaryKey(...).
        final SystemCommissionConfig systemCommissionConfig = new SystemCommissionConfig();
        systemCommissionConfig.setId(0);
        systemCommissionConfig.setEnabledFlag(0);
        systemCommissionConfig.setType(0);
        systemCommissionConfig.setProductId(0);
        systemCommissionConfig.setRelatedCommissionFactor("relatedCommissionFactor");
        systemCommissionConfig.setReleaseFlag(0);
        mockSystemCommissionConfigMapper.selectByPrimaryKey(0);

        mockDetailMapper.listDetailByConfigId(0);

        // Run the test
        commissionConfigQueryServiceUnderTest.detailConfig(0);
    }

    @Test
    public void testDetailConfigItem() {
        // Setup
        final CommissionConfigDetailVO detailVO = new CommissionConfigDetailVO();
        detailVO.setCommissionFactorLst(Arrays.asList("value"));
        detailVO.setSendPeriod(0);
        final CommissionConfigItemDTO itemDTO = new CommissionConfigItemDTO();
        itemDTO.setItemId(0);
        itemDTO.setPeriodNum(0);
        itemDTO.setPlanId(0);
        itemDTO.setRiskId(0);
        itemDTO.setPayWayList(Arrays.asList(0));
        itemDTO.setCoveredYearsList(Arrays.asList("value"));
        itemDTO.setValidPeriodList(Arrays.asList("value"));
        itemDTO.setContinuationRate(new BigDecimal("0.00"));
        itemDTO.setCommissionRate(new BigDecimal("0.00"));
        detailVO.setFirstPeriodList(Arrays.asList(itemDTO));
        final CommissionConfigItemDTO itemDTO1 = new CommissionConfigItemDTO();
        itemDTO1.setItemId(0);
        itemDTO1.setPeriodNum(0);
        itemDTO1.setPlanId(0);
        itemDTO1.setRiskId(0);
        itemDTO1.setPayWayList(Arrays.asList(0));
        itemDTO1.setCoveredYearsList(Arrays.asList("value"));
        itemDTO1.setValidPeriodList(Arrays.asList("value"));
        itemDTO1.setContinuationRate(new BigDecimal("0.00"));
        itemDTO1.setCommissionRate(new BigDecimal("0.00"));
        detailVO.setOtherPeriodList(Arrays.asList(itemDTO1));

        // Configure SystemCommissionConfigDetailMapper.listDetailByConfigId(...).
        final SystemCommissionConfigDetail systemCommissionConfigDetail = new SystemCommissionConfigDetail();
        systemCommissionConfigDetail.setId(0);
        systemCommissionConfigDetail.setEnabledFlag(0);
        systemCommissionConfigDetail.setPeriodNum(0);
        systemCommissionConfigDetail.setPlanId(0);
        systemCommissionConfigDetail.setRiskId(0);
        systemCommissionConfigDetail.setPayWay("payWay");
        systemCommissionConfigDetail.setCoveredYears("coveredYears");
        systemCommissionConfigDetail.setValidPeriod("validPeriod");
        systemCommissionConfigDetail.setContinuationRate(new BigDecimal("0.00"));
        systemCommissionConfigDetail.setCommissionRate(new BigDecimal("0.00"));
        final List<SystemCommissionConfigDetail> systemCommissionConfigDetails = Arrays.asList(
                systemCommissionConfigDetail);
        mockDetailMapper.listDetailByConfigId(0);

        // Run the test
        commissionConfigQueryServiceUnderTest.detailConfigItem(0, detailVO);
    }

    @Test
    public void testDetailConfigItem_SystemCommissionConfigDetailMapperReturnsNoItems() {
        // Setup
        final CommissionConfigDetailVO detailVO = new CommissionConfigDetailVO();
        detailVO.setCommissionFactorLst(Arrays.asList("value"));
        detailVO.setSendPeriod(0);
        final CommissionConfigItemDTO itemDTO = new CommissionConfigItemDTO();
        itemDTO.setItemId(0);
        itemDTO.setPeriodNum(0);
        itemDTO.setPlanId(0);
        itemDTO.setRiskId(0);
        itemDTO.setPayWayList(Arrays.asList(0));
        itemDTO.setCoveredYearsList(Arrays.asList("value"));
        itemDTO.setValidPeriodList(Arrays.asList("value"));
        itemDTO.setContinuationRate(new BigDecimal("0.00"));
        itemDTO.setCommissionRate(new BigDecimal("0.00"));
        detailVO.setFirstPeriodList(Arrays.asList(itemDTO));
        final CommissionConfigItemDTO itemDTO1 = new CommissionConfigItemDTO();
        itemDTO1.setItemId(0);
        itemDTO1.setPeriodNum(0);
        itemDTO1.setPlanId(0);
        itemDTO1.setRiskId(0);
        itemDTO1.setPayWayList(Arrays.asList(0));
        itemDTO1.setCoveredYearsList(Arrays.asList("value"));
        itemDTO1.setValidPeriodList(Arrays.asList("value"));
        itemDTO1.setContinuationRate(new BigDecimal("0.00"));
        itemDTO1.setCommissionRate(new BigDecimal("0.00"));
        detailVO.setOtherPeriodList(Arrays.asList(itemDTO1));

        mockDetailMapper.listDetailByConfigId(0);

        // Run the test
        commissionConfigQueryServiceUnderTest.detailConfigItem(0, detailVO);
    }

    @Test
    public void testGetPlanRiskByPlanId() {
        // Setup
        final PlanRiskDTO planRiskDTO = new PlanRiskDTO();
        planRiskDTO.setMainRisk(false);
        planRiskDTO.setRiskId(0);
        planRiskDTO.setRiskKey("riskKey");
        planRiskDTO.setRiskVersion(0);
        planRiskDTO.setRiskName("riskName");
        final List<PlanRiskDTO> expectedResult = Arrays.asList(planRiskDTO);

        // Configure SysRiskService.queryPlanRisk(...).
        final PlanRiskDTO planRiskDTO1 = new PlanRiskDTO();
        planRiskDTO1.setMainRisk(false);
        planRiskDTO1.setRiskId(0);
        planRiskDTO1.setRiskKey("riskKey");
        planRiskDTO1.setRiskVersion(0);
        planRiskDTO1.setRiskName("riskName");
        final List<PlanRiskDTO> planRiskDTOS = Arrays.asList(planRiskDTO1);
        mockSysRiskService.queryPlanRisk(0);

        // Run the test
        commissionConfigQueryServiceUnderTest.getPlanRiskByPlanId(0);
    }

    @Test
    public void testGetPlanRiskByPlanId_SysRiskServiceReturnsNoItems() {
        // Setup
        mockSysRiskService.queryPlanRisk(0);

        // Run the test
        commissionConfigQueryServiceUnderTest.getPlanRiskByPlanId(0);
    }

    @Test
    public void testGetRiskByProductId() {
        // Setup
        final CommissionConfigProductVO expectedResult = new CommissionConfigProductVO();
        expectedResult.setCompanyId(0);
        expectedResult.setCompanyName("companyName");
        expectedResult.setProductId(0);
        expectedResult.setProductName("productName");
        expectedResult.setChannel("channel");
        final CommissionConfigPlanVO commissionConfigPlanVO = new CommissionConfigPlanVO();
        final PlanRiskInfoVO planRiskInfoVO = new PlanRiskInfoVO();
        planRiskInfoVO.setPlanId(0);
        commissionConfigPlanVO.setPlanRiskList(Arrays.asList(planRiskInfoVO));
        expectedResult.setPlanVOList(Arrays.asList(commissionConfigPlanVO));

        // Configure SmProductMapper.getProductById(...).
        final SmProductDetailVO smProductDetailVO = new SmProductDetailVO();
        smProductDetailVO.setId(0);
        smProductDetailVO.setChannel("channel");
        smProductDetailVO.setCompanyId(0);
        smProductDetailVO.setCompanyName("companyName");
        smProductDetailVO.setProductName("productName");
        mockSmProductMapper.getProductById(0);

        mockProductVersionMapper.getMaxVersion(0);

        // Configure SmPlanHistoryMapper.selectByProductAndVersion(...).
        final SmPlanHistory smPlanHistory = new SmPlanHistory();
        smPlanHistory.setId(0);
        smPlanHistory.setEnabledFlag(0);
        smPlanHistory.setPlanId(0);
        smPlanHistory.setVersion(0);
        smPlanHistory.setProductId(0);
        final List<SmPlanHistory> smPlanHistories = Arrays.asList(smPlanHistory);
        mockSmPlanHistoryMapper.selectByProductAndVersion(0, 0);

        // Configure SmPlanRiskHistoryMapper.listPlanRiskInfo(...).
        final PlanRiskInfoVO planRiskInfoVO1 = new PlanRiskInfoVO();
        planRiskInfoVO1.setPlanId(0);
        planRiskInfoVO1.setPlanRiskId(0);
        planRiskInfoVO1.setRiskId(0);
        planRiskInfoVO1.setVersion(0);
        planRiskInfoVO1.setRiskName("riskName");
        final List<PlanRiskInfoVO> planRiskInfoVOS = Arrays.asList(planRiskInfoVO1);
        mockSmPlanRiskHistoryMapper.listPlanRiskInfo(Arrays.asList(0), 0);

        // Run the test
        commissionConfigQueryServiceUnderTest.getRiskByProductId(0);
    }

    @Test
    public void testGetRiskByProductId_SmPlanHistoryMapperReturnsNoItems() {
        // Setup
        // Configure SmProductMapper.getProductById(...).
        final SmProductDetailVO smProductDetailVO = new SmProductDetailVO();
        smProductDetailVO.setId(0);
        smProductDetailVO.setChannel("channel");
        smProductDetailVO.setCompanyId(0);
        smProductDetailVO.setCompanyName("companyName");
        smProductDetailVO.setProductName("productName");
        mockSmProductMapper.getProductById(0);

        mockProductVersionMapper.getMaxVersion(0);
        mockSmPlanHistoryMapper.selectByProductAndVersion(0, 0);

        // Run the test
        commissionConfigQueryServiceUnderTest.getRiskByProductId(0);
    }

    @Test
    public void testGetRiskByProductId_SmPlanRiskHistoryMapperReturnsNoItems() {
        // Setup
        final CommissionConfigProductVO expectedResult = new CommissionConfigProductVO();
        expectedResult.setCompanyId(0);
        expectedResult.setCompanyName("companyName");
        expectedResult.setProductId(0);
        expectedResult.setProductName("productName");
        expectedResult.setChannel("channel");
        final CommissionConfigPlanVO commissionConfigPlanVO = new CommissionConfigPlanVO();
        final PlanRiskInfoVO planRiskInfoVO = new PlanRiskInfoVO();
        planRiskInfoVO.setPlanId(0);
        commissionConfigPlanVO.setPlanRiskList(Arrays.asList(planRiskInfoVO));
        expectedResult.setPlanVOList(Arrays.asList(commissionConfigPlanVO));

        // Configure SmProductMapper.getProductById(...).
        final SmProductDetailVO smProductDetailVO = new SmProductDetailVO();
        smProductDetailVO.setId(0);
        smProductDetailVO.setChannel("channel");
        smProductDetailVO.setCompanyId(0);
        smProductDetailVO.setCompanyName("companyName");
        smProductDetailVO.setProductName("productName");
        mockSmProductMapper.getProductById(0);

        mockProductVersionMapper.getMaxVersion(0);

        // Configure SmPlanHistoryMapper.selectByProductAndVersion(...).
        final SmPlanHistory smPlanHistory = new SmPlanHistory();
        smPlanHistory.setId(0);
        smPlanHistory.setEnabledFlag(0);
        smPlanHistory.setPlanId(0);
        smPlanHistory.setVersion(0);
        smPlanHistory.setProductId(0);
        final List<SmPlanHistory> smPlanHistories = Arrays.asList(smPlanHistory);
        mockSmPlanHistoryMapper.selectByProductAndVersion(0, 0);

        mockSmPlanRiskHistoryMapper.listPlanRiskInfo(Arrays.asList(0), 0);

        // Run the test
        commissionConfigQueryServiceUnderTest.getRiskByProductId(0);
    }

    @Test
    public void testListPlanRiskInfo() {
        // Setup
        final PlanRiskInfoVO planRiskInfoVO = new PlanRiskInfoVO();
        planRiskInfoVO.setPlanId(0);
        planRiskInfoVO.setPlanRiskId(0);
        planRiskInfoVO.setRiskId(0);
        planRiskInfoVO.setVersion(0);
        planRiskInfoVO.setRiskName("riskName");
        final List<PlanRiskInfoVO> expectedResult = Arrays.asList(planRiskInfoVO);

        // Configure SmPlanRiskHistoryMapper.listPlanRiskInfo(...).
        final PlanRiskInfoVO planRiskInfoVO1 = new PlanRiskInfoVO();
        planRiskInfoVO1.setPlanId(0);
        planRiskInfoVO1.setPlanRiskId(0);
        planRiskInfoVO1.setRiskId(0);
        planRiskInfoVO1.setVersion(0);
        planRiskInfoVO1.setRiskName("riskName");
        final List<PlanRiskInfoVO> planRiskInfoVOS = Arrays.asList(planRiskInfoVO1);
        mockSmPlanRiskHistoryMapper.listPlanRiskInfo(Arrays.asList(0), 0);

        // Run the test
        commissionConfigQueryServiceUnderTest.listPlanRiskInfo(0, Arrays.asList(0));
    }

    @Test
    public void testListPlanRiskInfo_SmPlanRiskHistoryMapperReturnsNoItems() {
        // Setup
        mockSmPlanRiskHistoryMapper.listPlanRiskInfo(Arrays.asList(0), 0);

        // Run the test
        commissionConfigQueryServiceUnderTest.listPlanRiskInfo(0, Arrays.asList(0));
    }

    @Test
    public void testListConfigByProductId() {
        // Setup
        final SystemCommissionConfig systemCommissionConfig = new SystemCommissionConfig();
        systemCommissionConfig.setId(0);
        systemCommissionConfig.setEnabledFlag(0);
        systemCommissionConfig.setType(0);
        systemCommissionConfig.setProductId(0);
        systemCommissionConfig.setRelatedCommissionFactor("relatedCommissionFactor");
        systemCommissionConfig.setReleaseFlag(0);
        final List<SystemCommissionConfig> expectedResult = Arrays.asList(systemCommissionConfig);

        // Configure SystemCommissionConfigMapper.select(...).
        final SystemCommissionConfig systemCommissionConfig1 = new SystemCommissionConfig();
        systemCommissionConfig1.setId(0);
        systemCommissionConfig1.setEnabledFlag(0);
        systemCommissionConfig1.setType(0);
        systemCommissionConfig1.setProductId(0);
        systemCommissionConfig1.setRelatedCommissionFactor("relatedCommissionFactor");
        systemCommissionConfig1.setReleaseFlag(0);
        final List<SystemCommissionConfig> systemCommissionConfigs = Arrays.asList(systemCommissionConfig1);
        final SystemCommissionConfig t = new SystemCommissionConfig();
        t.setId(0);
        t.setEnabledFlag(0);
        t.setType(0);
        t.setProductId(0);
        t.setRelatedCommissionFactor("relatedCommissionFactor");
        t.setReleaseFlag(0);
        mockSystemCommissionConfigMapper.select(t);

        // Run the test
        commissionConfigQueryServiceUnderTest.listConfigByProductId(0);
    }

    @Test
    public void testListConfigByProductId_SystemCommissionConfigMapperReturnsNoItems() {
        // Setup
        // Configure SystemCommissionConfigMapper.select(...).
        final SystemCommissionConfig t = new SystemCommissionConfig();
        t.setId(0);
        t.setEnabledFlag(0);
        t.setType(0);
        t.setProductId(0);
        t.setRelatedCommissionFactor("relatedCommissionFactor");
        t.setReleaseFlag(0);
        mockSystemCommissionConfigMapper.select(t);

        // Run the test
        commissionConfigQueryServiceUnderTest.listConfigByProductId(0);
    }

    @Test
    public void testListDetailByConfigId() {
        // Setup
        final CommissionConfigDetailDTO commissionConfigDetailDTO = new CommissionConfigDetailDTO();
        commissionConfigDetailDTO.setId(0);
        commissionConfigDetailDTO.setEnabledFlag(0);
        commissionConfigDetailDTO.setPeriodNum(0);
        commissionConfigDetailDTO.setPlanId(0);
        commissionConfigDetailDTO.setRiskId(0);
        commissionConfigDetailDTO.setPayWay("payWay");
        commissionConfigDetailDTO.setCoveredYears("coveredYears");
        commissionConfigDetailDTO.setValidPeriod("validPeriod");
        commissionConfigDetailDTO.setContinuationRate(new BigDecimal("0.00"));
        commissionConfigDetailDTO.setCommissionRate(new BigDecimal("0.00"));
        commissionConfigDetailDTO.setPayWayList(Arrays.asList(0));
        commissionConfigDetailDTO.setCoveredYearsList(Arrays.asList("value"));
        commissionConfigDetailDTO.setValidPeriodList(Arrays.asList("value"));
        final List<CommissionConfigDetailDTO> expectedResult = Arrays.asList(commissionConfigDetailDTO);

        // Configure SystemCommissionConfigDetailMapper.listDetailByConfigId(...).
        final SystemCommissionConfigDetail systemCommissionConfigDetail = new SystemCommissionConfigDetail();
        systemCommissionConfigDetail.setId(0);
        systemCommissionConfigDetail.setEnabledFlag(0);
        systemCommissionConfigDetail.setPeriodNum(0);
        systemCommissionConfigDetail.setPlanId(0);
        systemCommissionConfigDetail.setRiskId(0);
        systemCommissionConfigDetail.setPayWay("payWay");
        systemCommissionConfigDetail.setCoveredYears("coveredYears");
        systemCommissionConfigDetail.setValidPeriod("validPeriod");
        systemCommissionConfigDetail.setContinuationRate(new BigDecimal("0.00"));
        systemCommissionConfigDetail.setCommissionRate(new BigDecimal("0.00"));
        final List<SystemCommissionConfigDetail> systemCommissionConfigDetails = Arrays.asList(
                systemCommissionConfigDetail);
        mockDetailMapper.listDetailByConfigId(0);

        // Run the test
        commissionConfigQueryServiceUnderTest.listDetailByConfigId(0);
    }

    @Test
    public void testListDetailByConfigId_SystemCommissionConfigDetailMapperReturnsNoItems() {
        // Setup
        mockDetailMapper.listDetailByConfigId(0);

        // Run the test
        commissionConfigQueryServiceUnderTest.listDetailByConfigId(0);
    }

    @Test
    public void testListCalcCommissionDetailByConfigId() {
        // Setup
        final CommissionCalcConfigDetailDTO commissionCalcConfigDetailDTO = new CommissionCalcConfigDetailDTO();
        commissionCalcConfigDetailDTO.setId(0);
        commissionCalcConfigDetailDTO.setConfigId(0);
        commissionCalcConfigDetailDTO.setPayWayList(Arrays.asList(0));
        commissionCalcConfigDetailDTO.setCoveredYearsList(Arrays.asList("value"));
        commissionCalcConfigDetailDTO.setValidPeriodList(Arrays.asList("value"));
        final List<CommissionCalcConfigDetailDTO> expectedResult = Arrays.asList(commissionCalcConfigDetailDTO);

        // Configure SystemCommissionConfigDetailMapper.listDetailByConfigId(...).
        final SystemCommissionConfigDetail systemCommissionConfigDetail = new SystemCommissionConfigDetail();
        systemCommissionConfigDetail.setId(0);
        systemCommissionConfigDetail.setEnabledFlag(0);
        systemCommissionConfigDetail.setPeriodNum(0);
        systemCommissionConfigDetail.setPlanId(0);
        systemCommissionConfigDetail.setRiskId(0);
        systemCommissionConfigDetail.setPayWay("payWay");
        systemCommissionConfigDetail.setCoveredYears("coveredYears");
        systemCommissionConfigDetail.setValidPeriod("validPeriod");
        systemCommissionConfigDetail.setContinuationRate(new BigDecimal("0.00"));
        systemCommissionConfigDetail.setCommissionRate(new BigDecimal("0.00"));
        final List<SystemCommissionConfigDetail> systemCommissionConfigDetails = Arrays.asList(
                systemCommissionConfigDetail);
        mockDetailMapper.listDetailByConfigId(0);

        // Run the test
        commissionConfigQueryServiceUnderTest.listCalcCommissionDetailByConfigId(
                0);
    }

    @Test
    public void testListCalcCommissionDetailByConfigId_SystemCommissionConfigDetailMapperReturnsNoItems() {
        // Setup
        mockDetailMapper.listDetailByConfigId(0);

        // Run the test
        commissionConfigQueryServiceUnderTest.listCalcCommissionDetailByConfigId(
                0);
    }

    @Test
    public void testGetOldCommissionSetting() {
        // Setup
        final CommissionRateDTO commissionRateDTO = new CommissionRateDTO();
        commissionRateDTO.setPlanId(0);
        commissionRateDTO.setRiskId(0);
        commissionRateDTO.setCommissionType(0);
        commissionRateDTO.setCommissionId(0);
        commissionRateDTO.setCommissionRate(new BigDecimal("0.00"));
        final List<CommissionRateDTO> expectedResult = Arrays.asList(commissionRateDTO);

        // Configure SmCommissionMapper.selectByIds(...).
        final SmCommissionSettingDTO smCommissionSettingDTO = new SmCommissionSettingDTO();
        smCommissionSettingDTO.setId(0);
        smCommissionSettingDTO.setEnabledFlag(0);
        smCommissionSettingDTO.setPlanId(0);
        smCommissionSettingDTO.setPaymentProportion(new BigDecimal("0.00"));
        smCommissionSettingDTO.setSettlementProportion(new BigDecimal("0.00"));
        final List<SmCommissionSettingDTO> smCommissionSettingDTOS = Arrays.asList(smCommissionSettingDTO);
        mockOldSmCommissionMapper.selectByIds(Arrays.asList(0));

        // Run the test
        commissionConfigQueryServiceUnderTest.getOldCommissionSetting(
                Arrays.asList(0));

    }

    @Test
    public void testGetOldCommissionSetting_SmCommissionMapperReturnsNoItems() {
        // Setup
        mockOldSmCommissionMapper.selectByIds(Arrays.asList(0));

        // Run the test
        commissionConfigQueryServiceUnderTest.getOldCommissionSetting(
                Arrays.asList(0));
    }

    @Test
    public void testMain() {
        // Setup
        // Run the test
        CommissionConfigQueryService.main(new String[]{"args"});

        // Verify the results
    }

    @Test
    public void testQueryCommissionProportionByProductId() {
        // Setup
        final SystemCommissionConfigDetail systemCommissionConfigDetail = new SystemCommissionConfigDetail();
        systemCommissionConfigDetail.setId(0);
        systemCommissionConfigDetail.setEnabledFlag(0);
        systemCommissionConfigDetail.setPeriodNum(0);
        systemCommissionConfigDetail.setPlanId(0);
        systemCommissionConfigDetail.setRiskId(0);
        systemCommissionConfigDetail.setPayWay("payWay");
        systemCommissionConfigDetail.setCoveredYears("coveredYears");
        systemCommissionConfigDetail.setValidPeriod("validPeriod");
        systemCommissionConfigDetail.setContinuationRate(new BigDecimal("0.00"));
        systemCommissionConfigDetail.setCommissionRate(new BigDecimal("0.00"));
        final List<SystemCommissionConfigDetail> expectedResult = Arrays.asList(systemCommissionConfigDetail);

        // Configure SystemCommissionConfigDetailMapper.listCommissionConfigDetailByProductIdAndMatchingTime(...).
        final SystemCommissionConfigDetail systemCommissionConfigDetail1 = new SystemCommissionConfigDetail();
        systemCommissionConfigDetail1.setId(0);
        systemCommissionConfigDetail1.setEnabledFlag(0);
        systemCommissionConfigDetail1.setPeriodNum(0);
        systemCommissionConfigDetail1.setPlanId(0);
        systemCommissionConfigDetail1.setRiskId(0);
        systemCommissionConfigDetail1.setPayWay("payWay");
        systemCommissionConfigDetail1.setCoveredYears("coveredYears");
        systemCommissionConfigDetail1.setValidPeriod("validPeriod");
        systemCommissionConfigDetail1.setContinuationRate(new BigDecimal("0.00"));
        systemCommissionConfigDetail1.setCommissionRate(new BigDecimal("0.00"));
        final List<SystemCommissionConfigDetail> systemCommissionConfigDetails = Arrays.asList(
                systemCommissionConfigDetail1);
        mockDetailMapper.listCommissionConfigDetailByProductIdAndMatchingTime(0, 0,
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        // Run the test
        commissionConfigQueryServiceUnderTest.queryCommissionProportionByProductId(
                0);
    }

    @Test
    public void testQueryCommissionProportionByProductId_SystemCommissionConfigDetailMapperReturnsNoItems() {
        // Setup
        mockDetailMapper.listCommissionConfigDetailByProductIdAndMatchingTime(0, 0,
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        commissionConfigQueryServiceUnderTest.queryCommissionProportionByProductId(
                0);
    }
}
