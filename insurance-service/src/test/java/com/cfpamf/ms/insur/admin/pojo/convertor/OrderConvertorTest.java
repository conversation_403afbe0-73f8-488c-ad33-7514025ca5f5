package com.cfpamf.ms.insur.admin.pojo.convertor;

import com.cfpamf.ms.insur.admin.pojo.dto.order.SmOrderCarInfoDTO;
import com.cfpamf.ms.insur.admin.pojo.vo.SmCompanySettingVO;
import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.junit.Test;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> 2021/3/1 11:53
 */
public class OrderConvertorTest {


    @Test
    public void testDict() {

        Map<String, List<SmCompanySettingVO>> maps = new HashMap<>();

        SmCompanySettingVO vo = new SmCompanySettingVO();
        vo.setFieldCode("carUseNature");
        vo.setOptionCode("1");
        vo.setOptionName("????");

        SmCompanySettingVO vo1 = new SmCompanySettingVO();
        vo1.setFieldCode("carUseNature");
        vo1.setOptionName("names");
        vo1.setOptionCode("002");
        maps.put("carUseNature",Lists.newArrayList(vo, vo1));

        SmCompanySettingVO vo2 = new SmCompanySettingVO();
        vo2.setFieldCode("carType");
        vo2.setOptionCode("002");
        vo2.setOptionName("????");

        SmCompanySettingVO vo3 = new SmCompanySettingVO();
        vo3.setFieldCode("carType");
        vo3.setOptionName("carType111");
        vo3.setOptionCode("01");
        maps.put("carType", Lists.newArrayList(vo2, vo3));


        SmOrderCarInfoDTO smOrderCarInfoDTO = new SmOrderCarInfoDTO();
        smOrderCarInfoDTO.setCarType("01");
        smOrderCarInfoDTO.setCarUseNature("1");
        OrderConvertor.dict(maps, smOrderCarInfoDTO);

        Assert.assertEquals("????", smOrderCarInfoDTO.getCarUseNatureName());
    }


}
