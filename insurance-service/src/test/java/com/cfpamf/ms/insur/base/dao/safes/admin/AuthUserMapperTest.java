package com.cfpamf.ms.insur.base.dao.safes.admin;

import com.cfpamf.ms.insur.admin.dao.safes.SmAgentMapper;
import org.junit.FixMethodOrder;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.MethodSorters;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

/*@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("dev")
@FixMethodOrder(MethodSorters.NAME_ASCENDING)*/
public class AuthUserMapperTest {

    /*@Autowired
    private SmAgentMapper mapper;

    @Test
    public void updateAgentPathInfo() {
        mapper.updateAgentPathInfo(1005286, "1000000/1005286");
    }*/
}