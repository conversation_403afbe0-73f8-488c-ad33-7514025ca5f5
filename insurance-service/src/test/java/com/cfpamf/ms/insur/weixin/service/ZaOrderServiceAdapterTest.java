package com.cfpamf.ms.insur.weixin.service;

import com.cfpamf.ms.insur.admin.dao.safes.SmOrderMapper;
import com.cfpamf.ms.insur.admin.enums.order.GroupNotify;
import com.cfpamf.ms.insur.admin.external.zhongan.ZaApiProperties;
import com.cfpamf.ms.insur.admin.external.zhongan.ZaOrderServiceAdapter;
import com.cfpamf.ms.insur.admin.external.zhongan.api.ZaApiService;
import com.cfpamf.ms.insur.admin.pojo.po.order.SmOrderGroupNotify;
import com.cfpamf.ms.insur.admin.service.OrderNoGenerator;
import com.cfpamf.ms.insur.base.controller.ApplicationTest;
import com.cfpamf.ms.insur.base.util.HttpRequestUtil;
import com.cfpamf.ms.insur.weixin.dao.safes.EndorMapper;
import com.cfpamf.ms.insur.weixin.pojo.dto.policy.Endor;
import com.cfpamf.ms.insur.weixin.pojo.vo.apply.common.GroupEndorsement;
import com.cfpamf.ms.insur.weixin.pojo.vo.apply.zhongan.request.*;
import com.cfpamf.ms.insur.weixin.zaGroup.ZaMain;
import com.github.jsonzou.jmockdata.JMockData;
import com.zhongan.scorpoin.common.ZhongAnApiClient;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

@RunWith(PowerMockRunner.class)
@PrepareForTest({HttpRequestUtil.class})
public class ZaOrderServiceAdapterTest extends ApplicationTest {

    @Mock
    private SmOrderMapper orderMapper;

    @InjectMocks
    private ZaOrderServiceAdapter adapter;

    @Mock
    private ZaApiService apiService;

    @Mock
    protected ZaApiProperties zaApiProperties;

    @Mock
    protected OrderNoGenerator orderNoGenerator;

    @Mock
    protected ZhongAnApiClient apiClient;

//    @Autowired
//    SmProductExtendZaCacheMapper cacheMapper;

    @Mock
    protected ZhongAnApiClient aiCheckClient;

    @Mock
    protected EndorMapper endorMapper;

    @Test
    public void endorCalPremium(){
        GroupEndorsement req = JMockData.mock(GroupEndorsement.class);
//        GroupEndorResponse resp = adapter.endorCalPremium(req);
//        System.err.println(resp);
    }

    @Test
    public void endorEffect(){
        ZaGroupEndorEffective effective = new ZaGroupEndorEffective();
        effective.setTradeNo("");
        effective.setGroupPolicyNo("HA1100001479002569");
        effective.setChannelCode("XIAOJING");
        effective.setEndorsementNo("CS0200002516069868");
        try {
            String res = ZaMain.endorEffective(effective);
            System.err.println(res);
        }catch (Exception e){
            e.printStackTrace();
        }
        Assert.assertTrue(true);
    }

    public void underwriter(){

        Assert.assertTrue(true);
    }

    private void afterEndorEffect(Endor endor){
        Endor param = new Endor();
        param.setId(endor.getId());
        param.setStatus(2);

        SmOrderGroupNotify update = new SmOrderGroupNotify();
        update.setStatus(GroupNotify.StatusEnum.UNDO.getCode());
        update.setEndorsementNo(endor.getEndorsementNo());
        update.setGroupPolicyNo(endor.getPolicyNo());
        update.setChannel(endor.getChannel());

    }
}
