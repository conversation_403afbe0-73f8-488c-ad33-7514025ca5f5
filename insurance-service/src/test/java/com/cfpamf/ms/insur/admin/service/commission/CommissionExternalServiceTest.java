package com.cfpamf.ms.insur.admin.service.commission;

import com.cfpamf.ms.insur.admin.convertedpremium.entity.OrderConvertedPremium;
import com.cfpamf.ms.insur.admin.convertedpremium.service.ConvertedPremiumService;
import com.cfpamf.ms.insur.admin.dao.safes.SmOrderMapper;
import com.cfpamf.ms.insur.admin.pojo.dto.commission.*;
import com.cfpamf.ms.insur.admin.pojo.dto.order.OrderCommissionInsuredInfoDTO;
import com.cfpamf.ms.insur.admin.pojo.dto.order.OrderRiskCorrectDTO;
import com.cfpamf.ms.insur.admin.pojo.dto.order.OrderRiskPremiumDTO;
import com.cfpamf.ms.insur.admin.pojo.dto.renewal.RenewalTermDTO;
import com.cfpamf.ms.insur.admin.pojo.po.order.SmOrderPolicy;
import com.cfpamf.ms.insur.admin.pojo.vo.SmBaseOrderInsuredVO;
import com.cfpamf.ms.insur.admin.pojo.vo.SmPolicyInsured;
import com.cfpamf.ms.insur.admin.service.order.SmOrderPolicyService;
import com.cfpamf.ms.insur.admin.service.order.SmOrderRiskService;
import com.cfpamf.ms.insur.admin.service.renewalterm.SmOrderRenewalTermService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class CommissionExternalServiceTest {

    @Mock
    private SmOrderRiskService mockSmOrderRiskService;
    @Mock
    private SmOrderPolicyService mockSmOrderPolicyService;
    @Mock
    private ConvertedPremiumService mockConvertedPremiumService;
    @Mock
    private SmOrderMapper mockSmOrderMapper;
    @Mock
    private SmOrderRenewalTermService mockRenewalTermService;

    @Mock
    private CommissionExternalService commissionExternalServiceUnderTest;

    @Test
    public void testGetCommissionCalcOrderParam() {
        // Setup
        final CommissionCalcProcessDTO processDTO = new CommissionCalcProcessDTO();
        processDTO.setOrderId("orderId");
        processDTO.setProductAttrCode("productAttrCode");
        processDTO.setTermNum(0);
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO.setFhOrderId("fhOrderId");
        commissionCalcOrderInfoDTO.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO.setCommissionId(0);
        commissionCalcOrderInfoDTO.setOriginalPolicyNo("originalPolicyNo");
        commissionCalcOrderInfoDTO.setOriginalFhOrderId("fhOrderId");
        commissionCalcOrderInfoDTO.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO.setPolicyNo("policyNo");
        commissionCalcOrderInfoDTO.setAppStatus("appStatus");
        commissionCalcOrderInfoDTO.setInsuredIdNumber("insuredIdNumber");
        commissionCalcOrderInfoDTO.setPlanId(0);
        commissionCalcOrderInfoDTO.setTotalAmount(new BigDecimal("0.00"));
        commissionCalcOrderInfoDTO.setOriginalAmount(new BigDecimal("0.00"));
        commissionCalcOrderInfoDTO.setSurrenderTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        commissionCalcOrderInfoDTO.setRefundAmount(new BigDecimal("0.00"));
        commissionCalcOrderInfoDTO.setPaymentTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        commissionCalcOrderInfoDTO.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        commissionCalcOrderInfoDTO.setVisitStatus("visitStatus");
        commissionCalcOrderInfoDTO.setSurrenderType("surrenderType");
        commissionCalcOrderInfoDTO.setRenewalAmount(new BigDecimal("0.00"));
        commissionCalcOrderInfoDTO.setRenewalTermTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        commissionCalcOrderInfoDTO.setPayWay("payWay");
        commissionCalcOrderInfoDTO.setCoveredYears("coveredYears");
        commissionCalcOrderInfoDTO.setValidPeriod("validPeriod");
        final CommissionCalcRiskInfoDTO commissionCalcRiskInfoDTO = new CommissionCalcRiskInfoDTO();
        commissionCalcRiskInfoDTO.setRiskId(0);
        commissionCalcRiskInfoDTO.setOneYearRisk(2020);
        commissionCalcRiskInfoDTO.setAddTermNum(0);
        commissionCalcRiskInfoDTO.setPayWay("payWay");
        commissionCalcRiskInfoDTO.setCoveredYears("coveredYears");
        commissionCalcRiskInfoDTO.setValidPeriod("validPeriod");
        commissionCalcRiskInfoDTO.setPremium(new BigDecimal("0.00"));
        commissionCalcRiskInfoDTO.setRefundAmount(new BigDecimal("0.00"));
        commissionCalcRiskInfoDTO.setRenewalAmount(new BigDecimal("0.00"));
        commissionCalcOrderInfoDTO.setRiskParamList(Arrays.asList(commissionCalcRiskInfoDTO));
        commissionCalcOrderInfoDTO.setLongInsurance(0);
        processDTO.setOrderInfo(Arrays.asList(commissionCalcOrderInfoDTO));
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO1 = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO1.setFhOrderId("fhOrderId");
        commissionCalcOrderInfoDTO1.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO1.setCommissionId(0);
        commissionCalcOrderInfoDTO1.setOriginalPolicyNo("originalPolicyNo");
        commissionCalcOrderInfoDTO1.setOriginalFhOrderId("fhOrderId");
        commissionCalcOrderInfoDTO1.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO1.setPolicyNo("policyNo");
        commissionCalcOrderInfoDTO1.setAppStatus("appStatus");
        commissionCalcOrderInfoDTO1.setInsuredIdNumber("insuredIdNumber");
        commissionCalcOrderInfoDTO1.setPlanId(0);
        commissionCalcOrderInfoDTO1.setTotalAmount(new BigDecimal("0.00"));
        commissionCalcOrderInfoDTO1.setOriginalAmount(new BigDecimal("0.00"));
        commissionCalcOrderInfoDTO1.setSurrenderTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        commissionCalcOrderInfoDTO1.setRefundAmount(new BigDecimal("0.00"));
        commissionCalcOrderInfoDTO1.setPaymentTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        commissionCalcOrderInfoDTO1.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        commissionCalcOrderInfoDTO1.setVisitStatus("visitStatus");
        commissionCalcOrderInfoDTO1.setSurrenderType("surrenderType");
        commissionCalcOrderInfoDTO1.setRenewalAmount(new BigDecimal("0.00"));
        commissionCalcOrderInfoDTO1.setRenewalTermTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        commissionCalcOrderInfoDTO1.setPayWay("payWay");
        commissionCalcOrderInfoDTO1.setCoveredYears("coveredYears");
        commissionCalcOrderInfoDTO1.setValidPeriod("validPeriod");
        final CommissionCalcRiskInfoDTO commissionCalcRiskInfoDTO1 = new CommissionCalcRiskInfoDTO();
        commissionCalcRiskInfoDTO1.setRiskId(0);
        commissionCalcRiskInfoDTO1.setOneYearRisk(2020);
        commissionCalcRiskInfoDTO1.setAddTermNum(0);
        commissionCalcRiskInfoDTO1.setPayWay("payWay");
        commissionCalcRiskInfoDTO1.setCoveredYears("coveredYears");
        commissionCalcRiskInfoDTO1.setValidPeriod("validPeriod");
        commissionCalcRiskInfoDTO1.setPremium(new BigDecimal("0.00"));
        commissionCalcOrderInfoDTO1.setRiskParamList(Arrays.asList(commissionCalcRiskInfoDTO1));
        processDTO.setRefundOrderInfo(Arrays.asList(commissionCalcOrderInfoDTO1));

        final OrderCommissionInsuredInfoDTO orderCommissionInsuredInfoDTO = new OrderCommissionInsuredInfoDTO();
        orderCommissionInsuredInfoDTO.setTotalAmount(new BigDecimal("0.00"));
        orderCommissionInsuredInfoDTO.setPolicyNo("policyNo");
        orderCommissionInsuredInfoDTO.setAppStatus("appStatus");
        orderCommissionInsuredInfoDTO.setInsuredIdNumber("insuredIdNumber");
        orderCommissionInsuredInfoDTO.setSurrenderTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<OrderCommissionInsuredInfoDTO> insuredInfoDTOList = Arrays.asList(orderCommissionInsuredInfoDTO);

        // Configure SmOrderPolicyService.listOrderPolicyByFhOrderId(...).
        final SmOrderPolicy smOrderPolicy = new SmOrderPolicy();
        smOrderPolicy.setId(0);
        smOrderPolicy.setChannel("channel");
        smOrderPolicy.setPolicyNo("policyNo");
        smOrderPolicy.setPayType("payType");
        smOrderPolicy.setPayPeriod("payPeriod");
        smOrderPolicy.setPayUnit("payUnit");
        smOrderPolicy.setValidPeriod("validPeriod");
        smOrderPolicy.setValidUnit("validUnit");
        smOrderPolicy.setVisitStatus("visitStatus");
        smOrderPolicy.setSurrenderTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        smOrderPolicy.setCancelAmount(new BigDecimal("0.00"));
        smOrderPolicy.setSurrenderType("surrenderType");
        final List<SmOrderPolicy> smOrderPolicies = Arrays.asList(smOrderPolicy);
        mockSmOrderPolicyService.listOrderPolicyByFhOrderId("orderId");

        // Configure SmOrderRiskService.listRiskPremiumByFhOrderId(...).
        final OrderRiskPremiumDTO orderRiskPremiumDTO = new OrderRiskPremiumDTO();
        orderRiskPremiumDTO.setFhOrderId("fhOrderId");
        orderRiskPremiumDTO.setInsuredIdNumber("insuredIdNumber");
        orderRiskPremiumDTO.setRiskId(0L);
        orderRiskPremiumDTO.setRiskName("riskName");
        orderRiskPremiumDTO.setRiskSurrenderType("riskSurrenderType");
        final List<OrderRiskPremiumDTO> orderRiskPremiumDTOS = Arrays.asList(orderRiskPremiumDTO);
        mockSmOrderRiskService.listRiskPremiumByFhOrderId("orderId");

        // Configure SmOrderRiskService.listRiskCorrectByOrderId(...).
        final OrderRiskCorrectDTO orderRiskCorrectDTO = new OrderRiskCorrectDTO();
        orderRiskCorrectDTO.setOrderId("orderId");
        orderRiskCorrectDTO.setPolicyNo("policyNo");
        orderRiskCorrectDTO.setCorrectId("correctId");
        orderRiskCorrectDTO.setCorrectType("correctType");
        orderRiskCorrectDTO.setRiskId(0);
        final List<OrderRiskCorrectDTO> orderRiskCorrectDTOS = Arrays.asList(orderRiskCorrectDTO);
        mockSmOrderRiskService.listRiskCorrectByOrderId("orderId");

        // Configure SmOrderMapper.listOrderInsuredByPolicyNo(...).
        final SmPolicyInsured smPolicyInsured = new SmPolicyInsured();
        smPolicyInsured.setId(0);
        smPolicyInsured.setPolicyNo("policyNo");
        smPolicyInsured.setIdNumber("idNumber");
        smPolicyInsured.setPersonName("personName");
        smPolicyInsured.setFhOrderId("fhOrderId");
        final List<SmPolicyInsured> insureds = Arrays.asList(smPolicyInsured);
        mockSmOrderMapper.listOrderInsuredByPolicyNo("originalPolicyNo");

        // Configure SmOrderMapper.listOriginalOrderByOrderIdAndIdNumber(...).
        final SmBaseOrderInsuredVO smBaseOrderInsuredVO = new SmBaseOrderInsuredVO();
        smBaseOrderInsuredVO.setChannel("channel");
        smBaseOrderInsuredVO.setFhOrderId("fhOrderId");
        smBaseOrderInsuredVO.setProductId(0);
        smBaseOrderInsuredVO.setInsuredIdNumber("insuredIdNumber");
        smBaseOrderInsuredVO.setCommissionId(0);
        final List<SmBaseOrderInsuredVO> smBaseOrderInsuredVOS = Arrays.asList(smBaseOrderInsuredVO);
        mockSmOrderMapper.listOriginalOrderByOrderIdAndIdNumber("fhOrderId", Arrays.asList("value"));

        // Run the test
        commissionExternalServiceUnderTest.getCommissionCalcOrderParam(processDTO, insuredInfoDTOList);

        // Verify the results
    }

    @Test
    public void testGetCommissionCalcOrderParam_SmOrderPolicyServiceReturnsNoItems() {
        // Setup
        final CommissionCalcProcessDTO processDTO = new CommissionCalcProcessDTO();
        processDTO.setOrderId("orderId");
        processDTO.setProductAttrCode("productAttrCode");
        processDTO.setTermNum(0);
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO.setFhOrderId("fhOrderId");
        commissionCalcOrderInfoDTO.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO.setCommissionId(0);
        commissionCalcOrderInfoDTO.setOriginalPolicyNo("originalPolicyNo");
        commissionCalcOrderInfoDTO.setOriginalFhOrderId("fhOrderId");
        commissionCalcOrderInfoDTO.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO.setPolicyNo("policyNo");
        commissionCalcOrderInfoDTO.setAppStatus("appStatus");
        commissionCalcOrderInfoDTO.setInsuredIdNumber("insuredIdNumber");
        commissionCalcOrderInfoDTO.setPlanId(0);
        commissionCalcOrderInfoDTO.setTotalAmount(new BigDecimal("0.00"));
        commissionCalcOrderInfoDTO.setOriginalAmount(new BigDecimal("0.00"));
        commissionCalcOrderInfoDTO.setSurrenderTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        commissionCalcOrderInfoDTO.setRefundAmount(new BigDecimal("0.00"));
        commissionCalcOrderInfoDTO.setPaymentTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        commissionCalcOrderInfoDTO.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        commissionCalcOrderInfoDTO.setVisitStatus("visitStatus");
        commissionCalcOrderInfoDTO.setSurrenderType("surrenderType");
        commissionCalcOrderInfoDTO.setRenewalAmount(new BigDecimal("0.00"));
        commissionCalcOrderInfoDTO.setRenewalTermTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        commissionCalcOrderInfoDTO.setPayWay("payWay");
        commissionCalcOrderInfoDTO.setCoveredYears("coveredYears");
        commissionCalcOrderInfoDTO.setValidPeriod("validPeriod");
        final CommissionCalcRiskInfoDTO commissionCalcRiskInfoDTO = new CommissionCalcRiskInfoDTO();
        commissionCalcRiskInfoDTO.setRiskId(0);
        commissionCalcRiskInfoDTO.setOneYearRisk(2020);
        commissionCalcRiskInfoDTO.setAddTermNum(0);
        commissionCalcRiskInfoDTO.setPayWay("payWay");
        commissionCalcRiskInfoDTO.setCoveredYears("coveredYears");
        commissionCalcRiskInfoDTO.setValidPeriod("validPeriod");
        commissionCalcRiskInfoDTO.setPremium(new BigDecimal("0.00"));
        commissionCalcRiskInfoDTO.setRefundAmount(new BigDecimal("0.00"));
        commissionCalcRiskInfoDTO.setRenewalAmount(new BigDecimal("0.00"));
        commissionCalcOrderInfoDTO.setRiskParamList(Arrays.asList(commissionCalcRiskInfoDTO));
        commissionCalcOrderInfoDTO.setLongInsurance(0);
        processDTO.setOrderInfo(Arrays.asList(commissionCalcOrderInfoDTO));
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO1 = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO1.setFhOrderId("fhOrderId");
        commissionCalcOrderInfoDTO1.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO1.setCommissionId(0);
        commissionCalcOrderInfoDTO1.setOriginalPolicyNo("originalPolicyNo");
        commissionCalcOrderInfoDTO1.setOriginalFhOrderId("fhOrderId");
        commissionCalcOrderInfoDTO1.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO1.setPolicyNo("policyNo");
        commissionCalcOrderInfoDTO1.setAppStatus("appStatus");
        commissionCalcOrderInfoDTO1.setInsuredIdNumber("insuredIdNumber");
        commissionCalcOrderInfoDTO1.setPlanId(0);
        commissionCalcOrderInfoDTO1.setTotalAmount(new BigDecimal("0.00"));
        commissionCalcOrderInfoDTO1.setOriginalAmount(new BigDecimal("0.00"));
        commissionCalcOrderInfoDTO1.setSurrenderTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        commissionCalcOrderInfoDTO1.setRefundAmount(new BigDecimal("0.00"));
        commissionCalcOrderInfoDTO1.setPaymentTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        commissionCalcOrderInfoDTO1.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        commissionCalcOrderInfoDTO1.setVisitStatus("visitStatus");
        commissionCalcOrderInfoDTO1.setSurrenderType("surrenderType");
        commissionCalcOrderInfoDTO1.setRenewalAmount(new BigDecimal("0.00"));
        commissionCalcOrderInfoDTO1.setRenewalTermTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        commissionCalcOrderInfoDTO1.setPayWay("payWay");
        commissionCalcOrderInfoDTO1.setCoveredYears("coveredYears");
        commissionCalcOrderInfoDTO1.setValidPeriod("validPeriod");
        final CommissionCalcRiskInfoDTO commissionCalcRiskInfoDTO1 = new CommissionCalcRiskInfoDTO();
        commissionCalcRiskInfoDTO1.setRiskId(0);
        commissionCalcRiskInfoDTO1.setOneYearRisk(2020);
        commissionCalcRiskInfoDTO1.setAddTermNum(0);
        commissionCalcRiskInfoDTO1.setPayWay("payWay");
        commissionCalcRiskInfoDTO1.setCoveredYears("coveredYears");
        commissionCalcRiskInfoDTO1.setValidPeriod("validPeriod");
        commissionCalcRiskInfoDTO1.setPremium(new BigDecimal("0.00"));
        commissionCalcOrderInfoDTO1.setRiskParamList(Arrays.asList(commissionCalcRiskInfoDTO1));
        processDTO.setRefundOrderInfo(Arrays.asList(commissionCalcOrderInfoDTO1));

        final OrderCommissionInsuredInfoDTO orderCommissionInsuredInfoDTO = new OrderCommissionInsuredInfoDTO();
        orderCommissionInsuredInfoDTO.setTotalAmount(new BigDecimal("0.00"));
        orderCommissionInsuredInfoDTO.setPolicyNo("policyNo");
        orderCommissionInsuredInfoDTO.setAppStatus("appStatus");
        orderCommissionInsuredInfoDTO.setInsuredIdNumber("insuredIdNumber");
        orderCommissionInsuredInfoDTO.setSurrenderTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<OrderCommissionInsuredInfoDTO> insuredInfoDTOList = Arrays.asList(orderCommissionInsuredInfoDTO);
        mockSmOrderPolicyService.listOrderPolicyByFhOrderId("orderId");

        // Configure SmOrderRiskService.listRiskPremiumByFhOrderId(...).
        final OrderRiskPremiumDTO orderRiskPremiumDTO = new OrderRiskPremiumDTO();
        orderRiskPremiumDTO.setFhOrderId("fhOrderId");
        orderRiskPremiumDTO.setInsuredIdNumber("insuredIdNumber");
        orderRiskPremiumDTO.setRiskId(0L);
        orderRiskPremiumDTO.setRiskName("riskName");
        orderRiskPremiumDTO.setRiskSurrenderType("riskSurrenderType");
        final List<OrderRiskPremiumDTO> orderRiskPremiumDTOS = Arrays.asList(orderRiskPremiumDTO);
        mockSmOrderRiskService.listRiskPremiumByFhOrderId("orderId");

        // Configure SmOrderRiskService.listRiskCorrectByOrderId(...).
        final OrderRiskCorrectDTO orderRiskCorrectDTO = new OrderRiskCorrectDTO();
        orderRiskCorrectDTO.setOrderId("orderId");
        orderRiskCorrectDTO.setPolicyNo("policyNo");
        orderRiskCorrectDTO.setCorrectId("correctId");
        orderRiskCorrectDTO.setCorrectType("correctType");
        orderRiskCorrectDTO.setRiskId(0);
        final List<OrderRiskCorrectDTO> orderRiskCorrectDTOS = Arrays.asList(orderRiskCorrectDTO);
        mockSmOrderRiskService.listRiskCorrectByOrderId("orderId");

        // Configure SmOrderMapper.listOrderInsuredByPolicyNo(...).
        final SmPolicyInsured smPolicyInsured = new SmPolicyInsured();
        smPolicyInsured.setId(0);
        smPolicyInsured.setPolicyNo("policyNo");
        smPolicyInsured.setIdNumber("idNumber");
        smPolicyInsured.setPersonName("personName");
        smPolicyInsured.setFhOrderId("fhOrderId");
        final List<SmPolicyInsured> insureds = Arrays.asList(smPolicyInsured);
        mockSmOrderMapper.listOrderInsuredByPolicyNo("originalPolicyNo");

        // Configure SmOrderMapper.listOriginalOrderByOrderIdAndIdNumber(...).
        final SmBaseOrderInsuredVO smBaseOrderInsuredVO = new SmBaseOrderInsuredVO();
        smBaseOrderInsuredVO.setChannel("channel");
        smBaseOrderInsuredVO.setFhOrderId("fhOrderId");
        smBaseOrderInsuredVO.setProductId(0);
        smBaseOrderInsuredVO.setInsuredIdNumber("insuredIdNumber");
        smBaseOrderInsuredVO.setCommissionId(0);
        final List<SmBaseOrderInsuredVO> smBaseOrderInsuredVOS = Arrays.asList(smBaseOrderInsuredVO);
        mockSmOrderMapper.listOriginalOrderByOrderIdAndIdNumber("fhOrderId", Arrays.asList("value"));

        // Run the test
        commissionExternalServiceUnderTest.getCommissionCalcOrderParam(processDTO, insuredInfoDTOList);

        // Verify the results
    }

    @Test
    public void testGetCommissionCalcOrderParam_SmOrderRiskServiceListRiskPremiumByFhOrderIdReturnsNoItems() {
        // Setup
        final CommissionCalcProcessDTO processDTO = new CommissionCalcProcessDTO();
        processDTO.setOrderId("orderId");
        processDTO.setProductAttrCode("productAttrCode");
        processDTO.setTermNum(0);
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO.setFhOrderId("fhOrderId");
        commissionCalcOrderInfoDTO.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO.setCommissionId(0);
        commissionCalcOrderInfoDTO.setOriginalPolicyNo("originalPolicyNo");
        commissionCalcOrderInfoDTO.setOriginalFhOrderId("fhOrderId");
        commissionCalcOrderInfoDTO.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO.setPolicyNo("policyNo");
        commissionCalcOrderInfoDTO.setAppStatus("appStatus");
        commissionCalcOrderInfoDTO.setInsuredIdNumber("insuredIdNumber");
        commissionCalcOrderInfoDTO.setPlanId(0);
        commissionCalcOrderInfoDTO.setTotalAmount(new BigDecimal("0.00"));
        commissionCalcOrderInfoDTO.setOriginalAmount(new BigDecimal("0.00"));
        commissionCalcOrderInfoDTO.setSurrenderTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        commissionCalcOrderInfoDTO.setRefundAmount(new BigDecimal("0.00"));
        commissionCalcOrderInfoDTO.setPaymentTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        commissionCalcOrderInfoDTO.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        commissionCalcOrderInfoDTO.setVisitStatus("visitStatus");
        commissionCalcOrderInfoDTO.setSurrenderType("surrenderType");
        commissionCalcOrderInfoDTO.setRenewalAmount(new BigDecimal("0.00"));
        commissionCalcOrderInfoDTO.setRenewalTermTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        commissionCalcOrderInfoDTO.setPayWay("payWay");
        commissionCalcOrderInfoDTO.setCoveredYears("coveredYears");
        commissionCalcOrderInfoDTO.setValidPeriod("validPeriod");
        final CommissionCalcRiskInfoDTO commissionCalcRiskInfoDTO = new CommissionCalcRiskInfoDTO();
        commissionCalcRiskInfoDTO.setRiskId(0);
        commissionCalcRiskInfoDTO.setOneYearRisk(2020);
        commissionCalcRiskInfoDTO.setAddTermNum(0);
        commissionCalcRiskInfoDTO.setPayWay("payWay");
        commissionCalcRiskInfoDTO.setCoveredYears("coveredYears");
        commissionCalcRiskInfoDTO.setValidPeriod("validPeriod");
        commissionCalcRiskInfoDTO.setPremium(new BigDecimal("0.00"));
        commissionCalcRiskInfoDTO.setRefundAmount(new BigDecimal("0.00"));
        commissionCalcRiskInfoDTO.setRenewalAmount(new BigDecimal("0.00"));
        commissionCalcOrderInfoDTO.setRiskParamList(Arrays.asList(commissionCalcRiskInfoDTO));
        commissionCalcOrderInfoDTO.setLongInsurance(0);
        processDTO.setOrderInfo(Arrays.asList(commissionCalcOrderInfoDTO));
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO1 = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO1.setFhOrderId("fhOrderId");
        commissionCalcOrderInfoDTO1.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO1.setCommissionId(0);
        commissionCalcOrderInfoDTO1.setOriginalPolicyNo("originalPolicyNo");
        commissionCalcOrderInfoDTO1.setOriginalFhOrderId("fhOrderId");
        commissionCalcOrderInfoDTO1.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO1.setPolicyNo("policyNo");
        commissionCalcOrderInfoDTO1.setAppStatus("appStatus");
        commissionCalcOrderInfoDTO1.setInsuredIdNumber("insuredIdNumber");
        commissionCalcOrderInfoDTO1.setPlanId(0);
        commissionCalcOrderInfoDTO1.setTotalAmount(new BigDecimal("0.00"));
        commissionCalcOrderInfoDTO1.setOriginalAmount(new BigDecimal("0.00"));
        commissionCalcOrderInfoDTO1.setSurrenderTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        commissionCalcOrderInfoDTO1.setRefundAmount(new BigDecimal("0.00"));
        commissionCalcOrderInfoDTO1.setPaymentTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        commissionCalcOrderInfoDTO1.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        commissionCalcOrderInfoDTO1.setVisitStatus("visitStatus");
        commissionCalcOrderInfoDTO1.setSurrenderType("surrenderType");
        commissionCalcOrderInfoDTO1.setRenewalAmount(new BigDecimal("0.00"));
        commissionCalcOrderInfoDTO1.setRenewalTermTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        commissionCalcOrderInfoDTO1.setPayWay("payWay");
        commissionCalcOrderInfoDTO1.setCoveredYears("coveredYears");
        commissionCalcOrderInfoDTO1.setValidPeriod("validPeriod");
        final CommissionCalcRiskInfoDTO commissionCalcRiskInfoDTO1 = new CommissionCalcRiskInfoDTO();
        commissionCalcRiskInfoDTO1.setRiskId(0);
        commissionCalcRiskInfoDTO1.setOneYearRisk(2020);
        commissionCalcRiskInfoDTO1.setAddTermNum(0);
        commissionCalcRiskInfoDTO1.setPayWay("payWay");
        commissionCalcRiskInfoDTO1.setCoveredYears("coveredYears");
        commissionCalcRiskInfoDTO1.setValidPeriod("validPeriod");
        commissionCalcRiskInfoDTO1.setPremium(new BigDecimal("0.00"));
        commissionCalcOrderInfoDTO1.setRiskParamList(Arrays.asList(commissionCalcRiskInfoDTO1));
        processDTO.setRefundOrderInfo(Arrays.asList(commissionCalcOrderInfoDTO1));

        final OrderCommissionInsuredInfoDTO orderCommissionInsuredInfoDTO = new OrderCommissionInsuredInfoDTO();
        orderCommissionInsuredInfoDTO.setTotalAmount(new BigDecimal("0.00"));
        orderCommissionInsuredInfoDTO.setPolicyNo("policyNo");
        orderCommissionInsuredInfoDTO.setAppStatus("appStatus");
        orderCommissionInsuredInfoDTO.setInsuredIdNumber("insuredIdNumber");
        orderCommissionInsuredInfoDTO.setSurrenderTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<OrderCommissionInsuredInfoDTO> insuredInfoDTOList = Arrays.asList(orderCommissionInsuredInfoDTO);

        // Configure SmOrderPolicyService.listOrderPolicyByFhOrderId(...).
        final SmOrderPolicy smOrderPolicy = new SmOrderPolicy();
        smOrderPolicy.setId(0);
        smOrderPolicy.setChannel("channel");
        smOrderPolicy.setPolicyNo("policyNo");
        smOrderPolicy.setPayType("payType");
        smOrderPolicy.setPayPeriod("payPeriod");
        smOrderPolicy.setPayUnit("payUnit");
        smOrderPolicy.setValidPeriod("validPeriod");
        smOrderPolicy.setValidUnit("validUnit");
        smOrderPolicy.setVisitStatus("visitStatus");
        smOrderPolicy.setSurrenderTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        smOrderPolicy.setCancelAmount(new BigDecimal("0.00"));
        smOrderPolicy.setSurrenderType("surrenderType");
        final List<SmOrderPolicy> smOrderPolicies = Arrays.asList(smOrderPolicy);
        mockSmOrderPolicyService.listOrderPolicyByFhOrderId("orderId");

        mockSmOrderRiskService.listRiskPremiumByFhOrderId("orderId");

        // Configure SmOrderRiskService.listRiskCorrectByOrderId(...).
        final OrderRiskCorrectDTO orderRiskCorrectDTO = new OrderRiskCorrectDTO();
        orderRiskCorrectDTO.setOrderId("orderId");
        orderRiskCorrectDTO.setPolicyNo("policyNo");
        orderRiskCorrectDTO.setCorrectId("correctId");
        orderRiskCorrectDTO.setCorrectType("correctType");
        orderRiskCorrectDTO.setRiskId(0);
        final List<OrderRiskCorrectDTO> orderRiskCorrectDTOS = Arrays.asList(orderRiskCorrectDTO);
        mockSmOrderRiskService.listRiskCorrectByOrderId("orderId");

        // Configure SmOrderMapper.listOrderInsuredByPolicyNo(...).
        final SmPolicyInsured smPolicyInsured = new SmPolicyInsured();
        smPolicyInsured.setId(0);
        smPolicyInsured.setPolicyNo("policyNo");
        smPolicyInsured.setIdNumber("idNumber");
        smPolicyInsured.setPersonName("personName");
        smPolicyInsured.setFhOrderId("fhOrderId");
        final List<SmPolicyInsured> insureds = Arrays.asList(smPolicyInsured);
        mockSmOrderMapper.listOrderInsuredByPolicyNo("originalPolicyNo");

        // Configure SmOrderMapper.listOriginalOrderByOrderIdAndIdNumber(...).
        final SmBaseOrderInsuredVO smBaseOrderInsuredVO = new SmBaseOrderInsuredVO();
        smBaseOrderInsuredVO.setChannel("channel");
        smBaseOrderInsuredVO.setFhOrderId("fhOrderId");
        smBaseOrderInsuredVO.setProductId(0);
        smBaseOrderInsuredVO.setInsuredIdNumber("insuredIdNumber");
        smBaseOrderInsuredVO.setCommissionId(0);
        final List<SmBaseOrderInsuredVO> smBaseOrderInsuredVOS = Arrays.asList(smBaseOrderInsuredVO);
        mockSmOrderMapper.listOriginalOrderByOrderIdAndIdNumber("fhOrderId", Arrays.asList("value"));

        // Run the test
        commissionExternalServiceUnderTest.getCommissionCalcOrderParam(processDTO, insuredInfoDTOList);

        // Verify the results
    }

    @Test
    public void testGetCommissionCalcOrderParam_SmOrderRiskServiceListRiskCorrectByOrderIdReturnsNoItems() {
        // Setup
        final CommissionCalcProcessDTO processDTO = new CommissionCalcProcessDTO();
        processDTO.setOrderId("orderId");
        processDTO.setProductAttrCode("productAttrCode");
        processDTO.setTermNum(0);
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO.setFhOrderId("fhOrderId");
        commissionCalcOrderInfoDTO.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO.setCommissionId(0);
        commissionCalcOrderInfoDTO.setOriginalPolicyNo("originalPolicyNo");
        commissionCalcOrderInfoDTO.setOriginalFhOrderId("fhOrderId");
        commissionCalcOrderInfoDTO.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO.setPolicyNo("policyNo");
        commissionCalcOrderInfoDTO.setAppStatus("appStatus");
        commissionCalcOrderInfoDTO.setInsuredIdNumber("insuredIdNumber");
        commissionCalcOrderInfoDTO.setPlanId(0);
        commissionCalcOrderInfoDTO.setTotalAmount(new BigDecimal("0.00"));
        commissionCalcOrderInfoDTO.setOriginalAmount(new BigDecimal("0.00"));
        commissionCalcOrderInfoDTO.setSurrenderTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        commissionCalcOrderInfoDTO.setRefundAmount(new BigDecimal("0.00"));
        commissionCalcOrderInfoDTO.setPaymentTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        commissionCalcOrderInfoDTO.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        commissionCalcOrderInfoDTO.setVisitStatus("visitStatus");
        commissionCalcOrderInfoDTO.setSurrenderType("surrenderType");
        commissionCalcOrderInfoDTO.setRenewalAmount(new BigDecimal("0.00"));
        commissionCalcOrderInfoDTO.setRenewalTermTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        commissionCalcOrderInfoDTO.setPayWay("payWay");
        commissionCalcOrderInfoDTO.setCoveredYears("coveredYears");
        commissionCalcOrderInfoDTO.setValidPeriod("validPeriod");
        final CommissionCalcRiskInfoDTO commissionCalcRiskInfoDTO = new CommissionCalcRiskInfoDTO();
        commissionCalcRiskInfoDTO.setRiskId(0);
        commissionCalcRiskInfoDTO.setOneYearRisk(2020);
        commissionCalcRiskInfoDTO.setAddTermNum(0);
        commissionCalcRiskInfoDTO.setPayWay("payWay");
        commissionCalcRiskInfoDTO.setCoveredYears("coveredYears");
        commissionCalcRiskInfoDTO.setValidPeriod("validPeriod");
        commissionCalcRiskInfoDTO.setPremium(new BigDecimal("0.00"));
        commissionCalcRiskInfoDTO.setRefundAmount(new BigDecimal("0.00"));
        commissionCalcRiskInfoDTO.setRenewalAmount(new BigDecimal("0.00"));
        commissionCalcOrderInfoDTO.setRiskParamList(Arrays.asList(commissionCalcRiskInfoDTO));
        commissionCalcOrderInfoDTO.setLongInsurance(0);
        processDTO.setOrderInfo(Arrays.asList(commissionCalcOrderInfoDTO));
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO1 = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO1.setFhOrderId("fhOrderId");
        commissionCalcOrderInfoDTO1.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO1.setCommissionId(0);
        commissionCalcOrderInfoDTO1.setOriginalPolicyNo("originalPolicyNo");
        commissionCalcOrderInfoDTO1.setOriginalFhOrderId("fhOrderId");
        commissionCalcOrderInfoDTO1.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO1.setPolicyNo("policyNo");
        commissionCalcOrderInfoDTO1.setAppStatus("appStatus");
        commissionCalcOrderInfoDTO1.setInsuredIdNumber("insuredIdNumber");
        commissionCalcOrderInfoDTO1.setPlanId(0);
        commissionCalcOrderInfoDTO1.setTotalAmount(new BigDecimal("0.00"));
        commissionCalcOrderInfoDTO1.setOriginalAmount(new BigDecimal("0.00"));
        commissionCalcOrderInfoDTO1.setSurrenderTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        commissionCalcOrderInfoDTO1.setRefundAmount(new BigDecimal("0.00"));
        commissionCalcOrderInfoDTO1.setPaymentTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        commissionCalcOrderInfoDTO1.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        commissionCalcOrderInfoDTO1.setVisitStatus("visitStatus");
        commissionCalcOrderInfoDTO1.setSurrenderType("surrenderType");
        commissionCalcOrderInfoDTO1.setRenewalAmount(new BigDecimal("0.00"));
        commissionCalcOrderInfoDTO1.setRenewalTermTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        commissionCalcOrderInfoDTO1.setPayWay("payWay");
        commissionCalcOrderInfoDTO1.setCoveredYears("coveredYears");
        commissionCalcOrderInfoDTO1.setValidPeriod("validPeriod");
        final CommissionCalcRiskInfoDTO commissionCalcRiskInfoDTO1 = new CommissionCalcRiskInfoDTO();
        commissionCalcRiskInfoDTO1.setRiskId(0);
        commissionCalcRiskInfoDTO1.setOneYearRisk(2020);
        commissionCalcRiskInfoDTO1.setAddTermNum(0);
        commissionCalcRiskInfoDTO1.setPayWay("payWay");
        commissionCalcRiskInfoDTO1.setCoveredYears("coveredYears");
        commissionCalcRiskInfoDTO1.setValidPeriod("validPeriod");
        commissionCalcRiskInfoDTO1.setPremium(new BigDecimal("0.00"));
        commissionCalcOrderInfoDTO1.setRiskParamList(Arrays.asList(commissionCalcRiskInfoDTO1));
        processDTO.setRefundOrderInfo(Arrays.asList(commissionCalcOrderInfoDTO1));

        final OrderCommissionInsuredInfoDTO orderCommissionInsuredInfoDTO = new OrderCommissionInsuredInfoDTO();
        orderCommissionInsuredInfoDTO.setTotalAmount(new BigDecimal("0.00"));
        orderCommissionInsuredInfoDTO.setPolicyNo("policyNo");
        orderCommissionInsuredInfoDTO.setAppStatus("appStatus");
        orderCommissionInsuredInfoDTO.setInsuredIdNumber("insuredIdNumber");
        orderCommissionInsuredInfoDTO.setSurrenderTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<OrderCommissionInsuredInfoDTO> insuredInfoDTOList = Arrays.asList(orderCommissionInsuredInfoDTO);

        // Configure SmOrderPolicyService.listOrderPolicyByFhOrderId(...).
        final SmOrderPolicy smOrderPolicy = new SmOrderPolicy();
        smOrderPolicy.setId(0);
        smOrderPolicy.setChannel("channel");
        smOrderPolicy.setPolicyNo("policyNo");
        smOrderPolicy.setPayType("payType");
        smOrderPolicy.setPayPeriod("payPeriod");
        smOrderPolicy.setPayUnit("payUnit");
        smOrderPolicy.setValidPeriod("validPeriod");
        smOrderPolicy.setValidUnit("validUnit");
        smOrderPolicy.setVisitStatus("visitStatus");
        smOrderPolicy.setSurrenderTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        smOrderPolicy.setCancelAmount(new BigDecimal("0.00"));
        smOrderPolicy.setSurrenderType("surrenderType");
        final List<SmOrderPolicy> smOrderPolicies = Arrays.asList(smOrderPolicy);
        mockSmOrderPolicyService.listOrderPolicyByFhOrderId("orderId");

        // Configure SmOrderRiskService.listRiskPremiumByFhOrderId(...).
        final OrderRiskPremiumDTO orderRiskPremiumDTO = new OrderRiskPremiumDTO();
        orderRiskPremiumDTO.setFhOrderId("fhOrderId");
        orderRiskPremiumDTO.setInsuredIdNumber("insuredIdNumber");
        orderRiskPremiumDTO.setRiskId(0L);
        orderRiskPremiumDTO.setRiskName("riskName");
        orderRiskPremiumDTO.setRiskSurrenderType("riskSurrenderType");
        final List<OrderRiskPremiumDTO> orderRiskPremiumDTOS = Arrays.asList(orderRiskPremiumDTO);
        mockSmOrderRiskService.listRiskPremiumByFhOrderId("orderId");

        mockSmOrderRiskService.listRiskCorrectByOrderId("orderId");

        // Configure SmOrderMapper.listOrderInsuredByPolicyNo(...).
        final SmPolicyInsured smPolicyInsured = new SmPolicyInsured();
        smPolicyInsured.setId(0);
        smPolicyInsured.setPolicyNo("policyNo");
        smPolicyInsured.setIdNumber("idNumber");
        smPolicyInsured.setPersonName("personName");
        smPolicyInsured.setFhOrderId("fhOrderId");
        final List<SmPolicyInsured> insureds = Arrays.asList(smPolicyInsured);
        mockSmOrderMapper.listOrderInsuredByPolicyNo("originalPolicyNo");

        // Configure SmOrderMapper.listOriginalOrderByOrderIdAndIdNumber(...).
        final SmBaseOrderInsuredVO smBaseOrderInsuredVO = new SmBaseOrderInsuredVO();
        smBaseOrderInsuredVO.setChannel("channel");
        smBaseOrderInsuredVO.setFhOrderId("fhOrderId");
        smBaseOrderInsuredVO.setProductId(0);
        smBaseOrderInsuredVO.setInsuredIdNumber("insuredIdNumber");
        smBaseOrderInsuredVO.setCommissionId(0);
        final List<SmBaseOrderInsuredVO> smBaseOrderInsuredVOS = Arrays.asList(smBaseOrderInsuredVO);
        mockSmOrderMapper.listOriginalOrderByOrderIdAndIdNumber("fhOrderId", Arrays.asList("value"));

        // Run the test
        commissionExternalServiceUnderTest.getCommissionCalcOrderParam(processDTO, insuredInfoDTOList);

        // Verify the results
    }

    @Test
    public void testGetCommissionCalcOrderParam_SmOrderMapperListOrderInsuredByPolicyNoReturnsNoItems() {
        // Setup
        final CommissionCalcProcessDTO processDTO = new CommissionCalcProcessDTO();
        processDTO.setOrderId("orderId");
        processDTO.setProductAttrCode("productAttrCode");
        processDTO.setTermNum(0);
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO.setFhOrderId("fhOrderId");
        commissionCalcOrderInfoDTO.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO.setCommissionId(0);
        commissionCalcOrderInfoDTO.setOriginalPolicyNo("originalPolicyNo");
        commissionCalcOrderInfoDTO.setOriginalFhOrderId("fhOrderId");
        commissionCalcOrderInfoDTO.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO.setPolicyNo("policyNo");
        commissionCalcOrderInfoDTO.setAppStatus("appStatus");
        commissionCalcOrderInfoDTO.setInsuredIdNumber("insuredIdNumber");
        commissionCalcOrderInfoDTO.setPlanId(0);
        commissionCalcOrderInfoDTO.setTotalAmount(new BigDecimal("0.00"));
        commissionCalcOrderInfoDTO.setOriginalAmount(new BigDecimal("0.00"));
        commissionCalcOrderInfoDTO.setSurrenderTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        commissionCalcOrderInfoDTO.setRefundAmount(new BigDecimal("0.00"));
        commissionCalcOrderInfoDTO.setPaymentTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        commissionCalcOrderInfoDTO.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        commissionCalcOrderInfoDTO.setVisitStatus("visitStatus");
        commissionCalcOrderInfoDTO.setSurrenderType("surrenderType");
        commissionCalcOrderInfoDTO.setRenewalAmount(new BigDecimal("0.00"));
        commissionCalcOrderInfoDTO.setRenewalTermTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        commissionCalcOrderInfoDTO.setPayWay("payWay");
        commissionCalcOrderInfoDTO.setCoveredYears("coveredYears");
        commissionCalcOrderInfoDTO.setValidPeriod("validPeriod");
        final CommissionCalcRiskInfoDTO commissionCalcRiskInfoDTO = new CommissionCalcRiskInfoDTO();
        commissionCalcRiskInfoDTO.setRiskId(0);
        commissionCalcRiskInfoDTO.setOneYearRisk(2020);
        commissionCalcRiskInfoDTO.setAddTermNum(0);
        commissionCalcRiskInfoDTO.setPayWay("payWay");
        commissionCalcRiskInfoDTO.setCoveredYears("coveredYears");
        commissionCalcRiskInfoDTO.setValidPeriod("validPeriod");
        commissionCalcRiskInfoDTO.setPremium(new BigDecimal("0.00"));
        commissionCalcRiskInfoDTO.setRefundAmount(new BigDecimal("0.00"));
        commissionCalcRiskInfoDTO.setRenewalAmount(new BigDecimal("0.00"));
        commissionCalcOrderInfoDTO.setRiskParamList(Arrays.asList(commissionCalcRiskInfoDTO));
        commissionCalcOrderInfoDTO.setLongInsurance(0);
        processDTO.setOrderInfo(Arrays.asList(commissionCalcOrderInfoDTO));
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO1 = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO1.setFhOrderId("fhOrderId");
        commissionCalcOrderInfoDTO1.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO1.setCommissionId(0);
        commissionCalcOrderInfoDTO1.setOriginalPolicyNo("originalPolicyNo");
        commissionCalcOrderInfoDTO1.setOriginalFhOrderId("fhOrderId");
        commissionCalcOrderInfoDTO1.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO1.setPolicyNo("policyNo");
        commissionCalcOrderInfoDTO1.setAppStatus("appStatus");
        commissionCalcOrderInfoDTO1.setInsuredIdNumber("insuredIdNumber");
        commissionCalcOrderInfoDTO1.setPlanId(0);
        commissionCalcOrderInfoDTO1.setTotalAmount(new BigDecimal("0.00"));
        commissionCalcOrderInfoDTO1.setOriginalAmount(new BigDecimal("0.00"));
        commissionCalcOrderInfoDTO1.setSurrenderTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        commissionCalcOrderInfoDTO1.setRefundAmount(new BigDecimal("0.00"));
        commissionCalcOrderInfoDTO1.setPaymentTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        commissionCalcOrderInfoDTO1.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        commissionCalcOrderInfoDTO1.setVisitStatus("visitStatus");
        commissionCalcOrderInfoDTO1.setSurrenderType("surrenderType");
        commissionCalcOrderInfoDTO1.setRenewalAmount(new BigDecimal("0.00"));
        commissionCalcOrderInfoDTO1.setRenewalTermTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        commissionCalcOrderInfoDTO1.setPayWay("payWay");
        commissionCalcOrderInfoDTO1.setCoveredYears("coveredYears");
        commissionCalcOrderInfoDTO1.setValidPeriod("validPeriod");
        final CommissionCalcRiskInfoDTO commissionCalcRiskInfoDTO1 = new CommissionCalcRiskInfoDTO();
        commissionCalcRiskInfoDTO1.setRiskId(0);
        commissionCalcRiskInfoDTO1.setOneYearRisk(2020);
        commissionCalcRiskInfoDTO1.setAddTermNum(0);
        commissionCalcRiskInfoDTO1.setPayWay("payWay");
        commissionCalcRiskInfoDTO1.setCoveredYears("coveredYears");
        commissionCalcRiskInfoDTO1.setValidPeriod("validPeriod");
        commissionCalcRiskInfoDTO1.setPremium(new BigDecimal("0.00"));
        commissionCalcOrderInfoDTO1.setRiskParamList(Arrays.asList(commissionCalcRiskInfoDTO1));
        processDTO.setRefundOrderInfo(Arrays.asList(commissionCalcOrderInfoDTO1));

        final OrderCommissionInsuredInfoDTO orderCommissionInsuredInfoDTO = new OrderCommissionInsuredInfoDTO();
        orderCommissionInsuredInfoDTO.setTotalAmount(new BigDecimal("0.00"));
        orderCommissionInsuredInfoDTO.setPolicyNo("policyNo");
        orderCommissionInsuredInfoDTO.setAppStatus("appStatus");
        orderCommissionInsuredInfoDTO.setInsuredIdNumber("insuredIdNumber");
        orderCommissionInsuredInfoDTO.setSurrenderTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<OrderCommissionInsuredInfoDTO> insuredInfoDTOList = Arrays.asList(orderCommissionInsuredInfoDTO);

        // Configure SmOrderPolicyService.listOrderPolicyByFhOrderId(...).
        final SmOrderPolicy smOrderPolicy = new SmOrderPolicy();
        smOrderPolicy.setId(0);
        smOrderPolicy.setChannel("channel");
        smOrderPolicy.setPolicyNo("policyNo");
        smOrderPolicy.setPayType("payType");
        smOrderPolicy.setPayPeriod("payPeriod");
        smOrderPolicy.setPayUnit("payUnit");
        smOrderPolicy.setValidPeriod("validPeriod");
        smOrderPolicy.setValidUnit("validUnit");
        smOrderPolicy.setVisitStatus("visitStatus");
        smOrderPolicy.setSurrenderTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        smOrderPolicy.setCancelAmount(new BigDecimal("0.00"));
        smOrderPolicy.setSurrenderType("surrenderType");
        final List<SmOrderPolicy> smOrderPolicies = Arrays.asList(smOrderPolicy);
        mockSmOrderPolicyService.listOrderPolicyByFhOrderId("orderId");

        // Configure SmOrderRiskService.listRiskPremiumByFhOrderId(...).
        final OrderRiskPremiumDTO orderRiskPremiumDTO = new OrderRiskPremiumDTO();
        orderRiskPremiumDTO.setFhOrderId("fhOrderId");
        orderRiskPremiumDTO.setInsuredIdNumber("insuredIdNumber");
        orderRiskPremiumDTO.setRiskId(0L);
        orderRiskPremiumDTO.setRiskName("riskName");
        orderRiskPremiumDTO.setRiskSurrenderType("riskSurrenderType");
        final List<OrderRiskPremiumDTO> orderRiskPremiumDTOS = Arrays.asList(orderRiskPremiumDTO);
        mockSmOrderRiskService.listRiskPremiumByFhOrderId("orderId");

        // Configure SmOrderRiskService.listRiskCorrectByOrderId(...).
        final OrderRiskCorrectDTO orderRiskCorrectDTO = new OrderRiskCorrectDTO();
        orderRiskCorrectDTO.setOrderId("orderId");
        orderRiskCorrectDTO.setPolicyNo("policyNo");
        orderRiskCorrectDTO.setCorrectId("correctId");
        orderRiskCorrectDTO.setCorrectType("correctType");
        orderRiskCorrectDTO.setRiskId(0);
        final List<OrderRiskCorrectDTO> orderRiskCorrectDTOS = Arrays.asList(orderRiskCorrectDTO);
        mockSmOrderRiskService.listRiskCorrectByOrderId("orderId");

        mockSmOrderMapper.listOrderInsuredByPolicyNo("originalPolicyNo");

        // Run the test
        commissionExternalServiceUnderTest.getCommissionCalcOrderParam(processDTO, insuredInfoDTOList);

        // Verify the results
    }

    @Test
    public void testGetCommissionCalcOrderParam_SmOrderMapperListOriginalOrderByOrderIdAndIdNumberReturnsNoItems() {
        // Setup
        final CommissionCalcProcessDTO processDTO = new CommissionCalcProcessDTO();
        processDTO.setOrderId("orderId");
        processDTO.setProductAttrCode("productAttrCode");
        processDTO.setTermNum(0);
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO.setFhOrderId("fhOrderId");
        commissionCalcOrderInfoDTO.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO.setCommissionId(0);
        commissionCalcOrderInfoDTO.setOriginalPolicyNo("originalPolicyNo");
        commissionCalcOrderInfoDTO.setOriginalFhOrderId("fhOrderId");
        commissionCalcOrderInfoDTO.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO.setPolicyNo("policyNo");
        commissionCalcOrderInfoDTO.setAppStatus("appStatus");
        commissionCalcOrderInfoDTO.setInsuredIdNumber("insuredIdNumber");
        commissionCalcOrderInfoDTO.setPlanId(0);
        commissionCalcOrderInfoDTO.setTotalAmount(new BigDecimal("0.00"));
        commissionCalcOrderInfoDTO.setOriginalAmount(new BigDecimal("0.00"));
        commissionCalcOrderInfoDTO.setSurrenderTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        commissionCalcOrderInfoDTO.setRefundAmount(new BigDecimal("0.00"));
        commissionCalcOrderInfoDTO.setPaymentTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        commissionCalcOrderInfoDTO.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        commissionCalcOrderInfoDTO.setVisitStatus("visitStatus");
        commissionCalcOrderInfoDTO.setSurrenderType("surrenderType");
        commissionCalcOrderInfoDTO.setRenewalAmount(new BigDecimal("0.00"));
        commissionCalcOrderInfoDTO.setRenewalTermTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        commissionCalcOrderInfoDTO.setPayWay("payWay");
        commissionCalcOrderInfoDTO.setCoveredYears("coveredYears");
        commissionCalcOrderInfoDTO.setValidPeriod("validPeriod");
        final CommissionCalcRiskInfoDTO commissionCalcRiskInfoDTO = new CommissionCalcRiskInfoDTO();
        commissionCalcRiskInfoDTO.setRiskId(0);
        commissionCalcRiskInfoDTO.setOneYearRisk(2020);
        commissionCalcRiskInfoDTO.setAddTermNum(0);
        commissionCalcRiskInfoDTO.setPayWay("payWay");
        commissionCalcRiskInfoDTO.setCoveredYears("coveredYears");
        commissionCalcRiskInfoDTO.setValidPeriod("validPeriod");
        commissionCalcRiskInfoDTO.setPremium(new BigDecimal("0.00"));
        commissionCalcRiskInfoDTO.setRefundAmount(new BigDecimal("0.00"));
        commissionCalcRiskInfoDTO.setRenewalAmount(new BigDecimal("0.00"));
        commissionCalcOrderInfoDTO.setRiskParamList(Arrays.asList(commissionCalcRiskInfoDTO));
        commissionCalcOrderInfoDTO.setLongInsurance(0);
        processDTO.setOrderInfo(Arrays.asList(commissionCalcOrderInfoDTO));
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO1 = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO1.setFhOrderId("fhOrderId");
        commissionCalcOrderInfoDTO1.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO1.setCommissionId(0);
        commissionCalcOrderInfoDTO1.setOriginalPolicyNo("originalPolicyNo");
        commissionCalcOrderInfoDTO1.setOriginalFhOrderId("fhOrderId");
        commissionCalcOrderInfoDTO1.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO1.setPolicyNo("policyNo");
        commissionCalcOrderInfoDTO1.setAppStatus("appStatus");
        commissionCalcOrderInfoDTO1.setInsuredIdNumber("insuredIdNumber");
        commissionCalcOrderInfoDTO1.setPlanId(0);
        commissionCalcOrderInfoDTO1.setTotalAmount(new BigDecimal("0.00"));
        commissionCalcOrderInfoDTO1.setOriginalAmount(new BigDecimal("0.00"));
        commissionCalcOrderInfoDTO1.setSurrenderTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        commissionCalcOrderInfoDTO1.setRefundAmount(new BigDecimal("0.00"));
        commissionCalcOrderInfoDTO1.setPaymentTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        commissionCalcOrderInfoDTO1.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        commissionCalcOrderInfoDTO1.setVisitStatus("visitStatus");
        commissionCalcOrderInfoDTO1.setSurrenderType("surrenderType");
        commissionCalcOrderInfoDTO1.setRenewalAmount(new BigDecimal("0.00"));
        commissionCalcOrderInfoDTO1.setRenewalTermTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        commissionCalcOrderInfoDTO1.setPayWay("payWay");
        commissionCalcOrderInfoDTO1.setCoveredYears("coveredYears");
        commissionCalcOrderInfoDTO1.setValidPeriod("validPeriod");
        final CommissionCalcRiskInfoDTO commissionCalcRiskInfoDTO1 = new CommissionCalcRiskInfoDTO();
        commissionCalcRiskInfoDTO1.setRiskId(0);
        commissionCalcRiskInfoDTO1.setOneYearRisk(2020);
        commissionCalcRiskInfoDTO1.setAddTermNum(0);
        commissionCalcRiskInfoDTO1.setPayWay("payWay");
        commissionCalcRiskInfoDTO1.setCoveredYears("coveredYears");
        commissionCalcRiskInfoDTO1.setValidPeriod("validPeriod");
        commissionCalcRiskInfoDTO1.setPremium(new BigDecimal("0.00"));
        commissionCalcOrderInfoDTO1.setRiskParamList(Arrays.asList(commissionCalcRiskInfoDTO1));
        processDTO.setRefundOrderInfo(Arrays.asList(commissionCalcOrderInfoDTO1));

        final OrderCommissionInsuredInfoDTO orderCommissionInsuredInfoDTO = new OrderCommissionInsuredInfoDTO();
        orderCommissionInsuredInfoDTO.setTotalAmount(new BigDecimal("0.00"));
        orderCommissionInsuredInfoDTO.setPolicyNo("policyNo");
        orderCommissionInsuredInfoDTO.setAppStatus("appStatus");
        orderCommissionInsuredInfoDTO.setInsuredIdNumber("insuredIdNumber");
        orderCommissionInsuredInfoDTO.setSurrenderTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<OrderCommissionInsuredInfoDTO> insuredInfoDTOList = Arrays.asList(orderCommissionInsuredInfoDTO);

        // Configure SmOrderPolicyService.listOrderPolicyByFhOrderId(...).
        final SmOrderPolicy smOrderPolicy = new SmOrderPolicy();
        smOrderPolicy.setId(0);
        smOrderPolicy.setChannel("channel");
        smOrderPolicy.setPolicyNo("policyNo");
        smOrderPolicy.setPayType("payType");
        smOrderPolicy.setPayPeriod("payPeriod");
        smOrderPolicy.setPayUnit("payUnit");
        smOrderPolicy.setValidPeriod("validPeriod");
        smOrderPolicy.setValidUnit("validUnit");
        smOrderPolicy.setVisitStatus("visitStatus");
        smOrderPolicy.setSurrenderTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        smOrderPolicy.setCancelAmount(new BigDecimal("0.00"));
        smOrderPolicy.setSurrenderType("surrenderType");
        final List<SmOrderPolicy> smOrderPolicies = Arrays.asList(smOrderPolicy);
        mockSmOrderPolicyService.listOrderPolicyByFhOrderId("orderId");

        // Configure SmOrderRiskService.listRiskPremiumByFhOrderId(...).
        final OrderRiskPremiumDTO orderRiskPremiumDTO = new OrderRiskPremiumDTO();
        orderRiskPremiumDTO.setFhOrderId("fhOrderId");
        orderRiskPremiumDTO.setInsuredIdNumber("insuredIdNumber");
        orderRiskPremiumDTO.setRiskId(0L);
        orderRiskPremiumDTO.setRiskName("riskName");
        orderRiskPremiumDTO.setRiskSurrenderType("riskSurrenderType");
        final List<OrderRiskPremiumDTO> orderRiskPremiumDTOS = Arrays.asList(orderRiskPremiumDTO);
        mockSmOrderRiskService.listRiskPremiumByFhOrderId("orderId");

        // Configure SmOrderRiskService.listRiskCorrectByOrderId(...).
        final OrderRiskCorrectDTO orderRiskCorrectDTO = new OrderRiskCorrectDTO();
        orderRiskCorrectDTO.setOrderId("orderId");
        orderRiskCorrectDTO.setPolicyNo("policyNo");
        orderRiskCorrectDTO.setCorrectId("correctId");
        orderRiskCorrectDTO.setCorrectType("correctType");
        orderRiskCorrectDTO.setRiskId(0);
        final List<OrderRiskCorrectDTO> orderRiskCorrectDTOS = Arrays.asList(orderRiskCorrectDTO);
        mockSmOrderRiskService.listRiskCorrectByOrderId("orderId");

        // Configure SmOrderMapper.listOrderInsuredByPolicyNo(...).
        final SmPolicyInsured smPolicyInsured = new SmPolicyInsured();
        smPolicyInsured.setId(0);
        smPolicyInsured.setPolicyNo("policyNo");
        smPolicyInsured.setIdNumber("idNumber");
        smPolicyInsured.setPersonName("personName");
        smPolicyInsured.setFhOrderId("fhOrderId");
        final List<SmPolicyInsured> insureds = Arrays.asList(smPolicyInsured);
        mockSmOrderMapper.listOrderInsuredByPolicyNo("originalPolicyNo");

        mockSmOrderMapper.listOriginalOrderByOrderIdAndIdNumber("fhOrderId", Arrays.asList("value"));

        // Run the test
        commissionExternalServiceUnderTest.getCommissionCalcOrderParam(processDTO, insuredInfoDTOList);

        // Verify the results
    }

    @Test
    public void testCheckVisit() {
        // Setup
        final SmOrderPolicy policy = new SmOrderPolicy();
        policy.setId(0);
        policy.setChannel("channel");
        policy.setPolicyNo("policyNo");
        policy.setPayType("payType");
        policy.setPayPeriod("payPeriod");
        policy.setPayUnit("payUnit");
        policy.setValidPeriod("validPeriod");
        policy.setValidUnit("validUnit");
        policy.setVisitStatus("visitStatus");
        policy.setSurrenderTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        policy.setCancelAmount(new BigDecimal("0.00"));
        policy.setSurrenderType("surrenderType");

        // Run the test
        commissionExternalServiceUnderTest.checkVisit(policy);
    }

    @Test
    public void testCheckDeal() {
        // Setup
        final SmOrderPolicy policy = new SmOrderPolicy();
        policy.setId(0);
        policy.setChannel("channel");
        policy.setPolicyNo("policyNo");
        policy.setPayType("payType");
        policy.setPayPeriod("payPeriod");
        policy.setPayUnit("payUnit");
        policy.setValidPeriod("validPeriod");
        policy.setValidUnit("validUnit");
        policy.setVisitStatus("visitStatus");
        policy.setSurrenderTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        policy.setCancelAmount(new BigDecimal("0.00"));
        policy.setSurrenderType("surrenderType");

        final CommissionCalcOrderInfoDTO dto = new CommissionCalcOrderInfoDTO();
        dto.setFhOrderId("fhOrderId");
        dto.setProductAttrCode("productAttrCode");
        dto.setCommissionId(0);
        dto.setOriginalPolicyNo("originalPolicyNo");
        dto.setOriginalFhOrderId("fhOrderId");
        dto.setOriginalCommissionId(0);
        dto.setPolicyNo("policyNo");
        dto.setAppStatus("appStatus");
        dto.setInsuredIdNumber("insuredIdNumber");
        dto.setPlanId(0);
        dto.setTotalAmount(new BigDecimal("0.00"));
        dto.setOriginalAmount(new BigDecimal("0.00"));
        dto.setSurrenderTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        dto.setRefundAmount(new BigDecimal("0.00"));
        dto.setPaymentTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        dto.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dto.setVisitStatus("visitStatus");
        dto.setSurrenderType("surrenderType");
        dto.setRenewalAmount(new BigDecimal("0.00"));
        dto.setRenewalTermTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        dto.setPayWay("payWay");
        dto.setCoveredYears("coveredYears");
        dto.setValidPeriod("validPeriod");
        final CommissionCalcRiskInfoDTO commissionCalcRiskInfoDTO = new CommissionCalcRiskInfoDTO();
        commissionCalcRiskInfoDTO.setRiskId(0);
        commissionCalcRiskInfoDTO.setOneYearRisk(2020);
        commissionCalcRiskInfoDTO.setAddTermNum(0);
        commissionCalcRiskInfoDTO.setPayWay("payWay");
        commissionCalcRiskInfoDTO.setCoveredYears("coveredYears");
        commissionCalcRiskInfoDTO.setValidPeriod("validPeriod");
        commissionCalcRiskInfoDTO.setPremium(new BigDecimal("0.00"));
        commissionCalcRiskInfoDTO.setRefundAmount(new BigDecimal("0.00"));
        commissionCalcRiskInfoDTO.setRenewalAmount(new BigDecimal("0.00"));
        dto.setRiskParamList(Arrays.asList(commissionCalcRiskInfoDTO));
        dto.setLongInsurance(0);

        // Run the test
        commissionExternalServiceUnderTest.checkDeal(policy, dto);
    }

    @Test
    public void testIsNotHesitationPeriod() {
        commissionExternalServiceUnderTest.isNotHesitationPeriod("visitStatus", "surrenderType");
    }

    @Test
    public void testIsDecreaseGroupOrder() {
        commissionExternalServiceUnderTest.isDecreaseGroupOrder("productAttrCode", "appStatus");
    }

    @Test
    public void testGetOldConvertedConfig() {
        // Setup
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO.setFhOrderId("fhOrderId");
        commissionCalcOrderInfoDTO.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO.setCommissionId(0);
        commissionCalcOrderInfoDTO.setOriginalPolicyNo("originalPolicyNo");
        commissionCalcOrderInfoDTO.setOriginalFhOrderId("fhOrderId");
        commissionCalcOrderInfoDTO.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO.setPolicyNo("policyNo");
        commissionCalcOrderInfoDTO.setAppStatus("appStatus");
        commissionCalcOrderInfoDTO.setInsuredIdNumber("insuredIdNumber");
        commissionCalcOrderInfoDTO.setPlanId(0);
        commissionCalcOrderInfoDTO.setTotalAmount(new BigDecimal("0.00"));
        commissionCalcOrderInfoDTO.setOriginalAmount(new BigDecimal("0.00"));
        commissionCalcOrderInfoDTO.setSurrenderTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        commissionCalcOrderInfoDTO.setRefundAmount(new BigDecimal("0.00"));
        commissionCalcOrderInfoDTO.setPaymentTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        commissionCalcOrderInfoDTO.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        commissionCalcOrderInfoDTO.setVisitStatus("visitStatus");
        commissionCalcOrderInfoDTO.setSurrenderType("surrenderType");
        commissionCalcOrderInfoDTO.setRenewalAmount(new BigDecimal("0.00"));
        commissionCalcOrderInfoDTO.setRenewalTermTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        commissionCalcOrderInfoDTO.setPayWay("payWay");
        commissionCalcOrderInfoDTO.setCoveredYears("coveredYears");
        commissionCalcOrderInfoDTO.setValidPeriod("validPeriod");
        final CommissionCalcRiskInfoDTO commissionCalcRiskInfoDTO = new CommissionCalcRiskInfoDTO();
        commissionCalcRiskInfoDTO.setRiskId(0);
        commissionCalcRiskInfoDTO.setOneYearRisk(2020);
        commissionCalcRiskInfoDTO.setAddTermNum(0);
        commissionCalcRiskInfoDTO.setPayWay("payWay");
        commissionCalcRiskInfoDTO.setCoveredYears("coveredYears");
        commissionCalcRiskInfoDTO.setValidPeriod("validPeriod");
        commissionCalcRiskInfoDTO.setPremium(new BigDecimal("0.00"));
        commissionCalcRiskInfoDTO.setRefundAmount(new BigDecimal("0.00"));
        commissionCalcRiskInfoDTO.setRenewalAmount(new BigDecimal("0.00"));
        commissionCalcOrderInfoDTO.setRiskParamList(Arrays.asList(commissionCalcRiskInfoDTO));
        commissionCalcOrderInfoDTO.setLongInsurance(0);
        final List<CommissionCalcOrderInfoDTO> orderInfoList = Arrays.asList(commissionCalcOrderInfoDTO);
        final CommissionRateDTO commissionRateDTO = new CommissionRateDTO();
        commissionRateDTO.setPlanId(0);
        commissionRateDTO.setRiskId(0);
        commissionRateDTO.setCommissionType(0);
        commissionRateDTO.setCommissionId(0);
        commissionRateDTO.setCommissionRate(new BigDecimal("0.00"));
        final List<CommissionRateDTO> expectedResult = Arrays.asList(commissionRateDTO);

        // Configure ConvertedPremiumService.listOrderConvertedPremium(...).
        final OrderConvertedPremium orderConvertedPremium = new OrderConvertedPremium();
        orderConvertedPremium.setId(0);
        orderConvertedPremium.setFhOrderId("fhOrderId");
        orderConvertedPremium.setPolicyNo("policyNo");
        orderConvertedPremium.setInsIdNumber("insIdNumber");
        orderConvertedPremium.setProportion(0);
        final List<OrderConvertedPremium> orderConvertedPremiums = Arrays.asList(orderConvertedPremium);
        mockConvertedPremiumService.listOrderConvertedPremium("fhOrderId", "1",
                Arrays.asList("value"));

        // Run the test
        commissionExternalServiceUnderTest.getOldConvertedConfig(orderInfoList);
    }

    @Test
    public void testGetOldConvertedConfig_ConvertedPremiumServiceReturnsNoItems() {
        // Setup
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO.setFhOrderId("fhOrderId");
        commissionCalcOrderInfoDTO.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO.setCommissionId(0);
        commissionCalcOrderInfoDTO.setOriginalPolicyNo("originalPolicyNo");
        commissionCalcOrderInfoDTO.setOriginalFhOrderId("fhOrderId");
        commissionCalcOrderInfoDTO.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO.setPolicyNo("policyNo");
        commissionCalcOrderInfoDTO.setAppStatus("appStatus");
        commissionCalcOrderInfoDTO.setInsuredIdNumber("insuredIdNumber");
        commissionCalcOrderInfoDTO.setPlanId(0);
        commissionCalcOrderInfoDTO.setTotalAmount(new BigDecimal("0.00"));
        commissionCalcOrderInfoDTO.setOriginalAmount(new BigDecimal("0.00"));
        commissionCalcOrderInfoDTO.setSurrenderTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        commissionCalcOrderInfoDTO.setRefundAmount(new BigDecimal("0.00"));
        commissionCalcOrderInfoDTO.setPaymentTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        commissionCalcOrderInfoDTO.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        commissionCalcOrderInfoDTO.setVisitStatus("visitStatus");
        commissionCalcOrderInfoDTO.setSurrenderType("surrenderType");
        commissionCalcOrderInfoDTO.setRenewalAmount(new BigDecimal("0.00"));
        commissionCalcOrderInfoDTO.setRenewalTermTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        commissionCalcOrderInfoDTO.setPayWay("payWay");
        commissionCalcOrderInfoDTO.setCoveredYears("coveredYears");
        commissionCalcOrderInfoDTO.setValidPeriod("validPeriod");
        final CommissionCalcRiskInfoDTO commissionCalcRiskInfoDTO = new CommissionCalcRiskInfoDTO();
        commissionCalcRiskInfoDTO.setRiskId(0);
        commissionCalcRiskInfoDTO.setOneYearRisk(2020);
        commissionCalcRiskInfoDTO.setAddTermNum(0);
        commissionCalcRiskInfoDTO.setPayWay("payWay");
        commissionCalcRiskInfoDTO.setCoveredYears("coveredYears");
        commissionCalcRiskInfoDTO.setValidPeriod("validPeriod");
        commissionCalcRiskInfoDTO.setPremium(new BigDecimal("0.00"));
        commissionCalcRiskInfoDTO.setRefundAmount(new BigDecimal("0.00"));
        commissionCalcRiskInfoDTO.setRenewalAmount(new BigDecimal("0.00"));
        commissionCalcOrderInfoDTO.setRiskParamList(Arrays.asList(commissionCalcRiskInfoDTO));
        commissionCalcOrderInfoDTO.setLongInsurance(0);
        final List<CommissionCalcOrderInfoDTO> orderInfoList = Arrays.asList(commissionCalcOrderInfoDTO);
        mockConvertedPremiumService.listOrderConvertedPremium("fhOrderId", "1",
                Arrays.asList("value"));

        // Run the test
        commissionExternalServiceUnderTest.getOldConvertedConfig(orderInfoList);
    }

    @Test
    public void testGetGroupInsureIncreaseOrderOriginal() {
        // Setup
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO.setFhOrderId("fhOrderId");
        commissionCalcOrderInfoDTO.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO.setCommissionId(0);
        commissionCalcOrderInfoDTO.setOriginalPolicyNo("originalPolicyNo");
        commissionCalcOrderInfoDTO.setOriginalFhOrderId("fhOrderId");
        commissionCalcOrderInfoDTO.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO.setPolicyNo("policyNo");
        commissionCalcOrderInfoDTO.setAppStatus("appStatus");
        commissionCalcOrderInfoDTO.setInsuredIdNumber("insuredIdNumber");
        commissionCalcOrderInfoDTO.setPlanId(0);
        commissionCalcOrderInfoDTO.setTotalAmount(new BigDecimal("0.00"));
        commissionCalcOrderInfoDTO.setOriginalAmount(new BigDecimal("0.00"));
        commissionCalcOrderInfoDTO.setSurrenderTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        commissionCalcOrderInfoDTO.setRefundAmount(new BigDecimal("0.00"));
        commissionCalcOrderInfoDTO.setPaymentTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        commissionCalcOrderInfoDTO.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        commissionCalcOrderInfoDTO.setVisitStatus("visitStatus");
        commissionCalcOrderInfoDTO.setSurrenderType("surrenderType");
        commissionCalcOrderInfoDTO.setRenewalAmount(new BigDecimal("0.00"));
        commissionCalcOrderInfoDTO.setRenewalTermTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        commissionCalcOrderInfoDTO.setPayWay("payWay");
        commissionCalcOrderInfoDTO.setCoveredYears("coveredYears");
        commissionCalcOrderInfoDTO.setValidPeriod("validPeriod");
        final CommissionCalcRiskInfoDTO commissionCalcRiskInfoDTO = new CommissionCalcRiskInfoDTO();
        commissionCalcRiskInfoDTO.setRiskId(0);
        commissionCalcRiskInfoDTO.setOneYearRisk(2020);
        commissionCalcRiskInfoDTO.setAddTermNum(0);
        commissionCalcRiskInfoDTO.setPayWay("payWay");
        commissionCalcRiskInfoDTO.setCoveredYears("coveredYears");
        commissionCalcRiskInfoDTO.setValidPeriod("validPeriod");
        commissionCalcRiskInfoDTO.setPremium(new BigDecimal("0.00"));
        commissionCalcRiskInfoDTO.setRefundAmount(new BigDecimal("0.00"));
        commissionCalcRiskInfoDTO.setRenewalAmount(new BigDecimal("0.00"));
        commissionCalcOrderInfoDTO.setRiskParamList(Arrays.asList(commissionCalcRiskInfoDTO));
        commissionCalcOrderInfoDTO.setLongInsurance(0);
        final List<CommissionCalcOrderInfoDTO> orderInfoList = Arrays.asList(commissionCalcOrderInfoDTO);

        // Configure SmOrderMapper.listOrderInsuredByPolicyNo(...).
        final SmPolicyInsured smPolicyInsured = new SmPolicyInsured();
        smPolicyInsured.setId(0);
        smPolicyInsured.setPolicyNo("policyNo");
        smPolicyInsured.setIdNumber("idNumber");
        smPolicyInsured.setPersonName("personName");
        smPolicyInsured.setFhOrderId("fhOrderId");
        final List<SmPolicyInsured> insureds = Arrays.asList(smPolicyInsured);
        mockSmOrderMapper.listOrderInsuredByPolicyNo("originalPolicyNo");

        // Configure SmOrderMapper.listOriginalOrderByOrderIdAndIdNumber(...).
        final SmBaseOrderInsuredVO smBaseOrderInsuredVO = new SmBaseOrderInsuredVO();
        smBaseOrderInsuredVO.setChannel("channel");
        smBaseOrderInsuredVO.setFhOrderId("fhOrderId");
        smBaseOrderInsuredVO.setProductId(0);
        smBaseOrderInsuredVO.setInsuredIdNumber("insuredIdNumber");
        smBaseOrderInsuredVO.setCommissionId(0);
        final List<SmBaseOrderInsuredVO> smBaseOrderInsuredVOS = Arrays.asList(smBaseOrderInsuredVO);
        mockSmOrderMapper.listOriginalOrderByOrderIdAndIdNumber("fhOrderId", Arrays.asList("value"));

        // Run the test
        commissionExternalServiceUnderTest.getGroupInsureIncreaseOrderOriginal(orderInfoList);

        // Verify the results
    }

    @Test
    public void testGetGroupInsureIncreaseOrderOriginal_SmOrderMapperListOrderInsuredByPolicyNoReturnsNoItems() {
        // Setup
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO.setFhOrderId("fhOrderId");
        commissionCalcOrderInfoDTO.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO.setCommissionId(0);
        commissionCalcOrderInfoDTO.setOriginalPolicyNo("originalPolicyNo");
        commissionCalcOrderInfoDTO.setOriginalFhOrderId("fhOrderId");
        commissionCalcOrderInfoDTO.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO.setPolicyNo("policyNo");
        commissionCalcOrderInfoDTO.setAppStatus("appStatus");
        commissionCalcOrderInfoDTO.setInsuredIdNumber("insuredIdNumber");
        commissionCalcOrderInfoDTO.setPlanId(0);
        commissionCalcOrderInfoDTO.setTotalAmount(new BigDecimal("0.00"));
        commissionCalcOrderInfoDTO.setOriginalAmount(new BigDecimal("0.00"));
        commissionCalcOrderInfoDTO.setSurrenderTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        commissionCalcOrderInfoDTO.setRefundAmount(new BigDecimal("0.00"));
        commissionCalcOrderInfoDTO.setPaymentTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        commissionCalcOrderInfoDTO.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        commissionCalcOrderInfoDTO.setVisitStatus("visitStatus");
        commissionCalcOrderInfoDTO.setSurrenderType("surrenderType");
        commissionCalcOrderInfoDTO.setRenewalAmount(new BigDecimal("0.00"));
        commissionCalcOrderInfoDTO.setRenewalTermTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        commissionCalcOrderInfoDTO.setPayWay("payWay");
        commissionCalcOrderInfoDTO.setCoveredYears("coveredYears");
        commissionCalcOrderInfoDTO.setValidPeriod("validPeriod");
        final CommissionCalcRiskInfoDTO commissionCalcRiskInfoDTO = new CommissionCalcRiskInfoDTO();
        commissionCalcRiskInfoDTO.setRiskId(0);
        commissionCalcRiskInfoDTO.setOneYearRisk(2020);
        commissionCalcRiskInfoDTO.setAddTermNum(0);
        commissionCalcRiskInfoDTO.setPayWay("payWay");
        commissionCalcRiskInfoDTO.setCoveredYears("coveredYears");
        commissionCalcRiskInfoDTO.setValidPeriod("validPeriod");
        commissionCalcRiskInfoDTO.setPremium(new BigDecimal("0.00"));
        commissionCalcRiskInfoDTO.setRefundAmount(new BigDecimal("0.00"));
        commissionCalcRiskInfoDTO.setRenewalAmount(new BigDecimal("0.00"));
        commissionCalcOrderInfoDTO.setRiskParamList(Arrays.asList(commissionCalcRiskInfoDTO));
        commissionCalcOrderInfoDTO.setLongInsurance(0);
        final List<CommissionCalcOrderInfoDTO> orderInfoList = Arrays.asList(commissionCalcOrderInfoDTO);
        mockSmOrderMapper.listOrderInsuredByPolicyNo("originalPolicyNo");

        // Run the test
        commissionExternalServiceUnderTest.getGroupInsureIncreaseOrderOriginal(orderInfoList);

        // Verify the results
    }

    @Test
    public void testGetGroupInsureIncreaseOrderOriginal_SmOrderMapperListOriginalOrderByOrderIdAndIdNumberReturnsNoItems() {
        // Setup
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO.setFhOrderId("fhOrderId");
        commissionCalcOrderInfoDTO.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO.setCommissionId(0);
        commissionCalcOrderInfoDTO.setOriginalPolicyNo("originalPolicyNo");
        commissionCalcOrderInfoDTO.setOriginalFhOrderId("fhOrderId");
        commissionCalcOrderInfoDTO.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO.setPolicyNo("policyNo");
        commissionCalcOrderInfoDTO.setAppStatus("appStatus");
        commissionCalcOrderInfoDTO.setInsuredIdNumber("insuredIdNumber");
        commissionCalcOrderInfoDTO.setPlanId(0);
        commissionCalcOrderInfoDTO.setTotalAmount(new BigDecimal("0.00"));
        commissionCalcOrderInfoDTO.setOriginalAmount(new BigDecimal("0.00"));
        commissionCalcOrderInfoDTO.setSurrenderTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        commissionCalcOrderInfoDTO.setRefundAmount(new BigDecimal("0.00"));
        commissionCalcOrderInfoDTO.setPaymentTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        commissionCalcOrderInfoDTO.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        commissionCalcOrderInfoDTO.setVisitStatus("visitStatus");
        commissionCalcOrderInfoDTO.setSurrenderType("surrenderType");
        commissionCalcOrderInfoDTO.setRenewalAmount(new BigDecimal("0.00"));
        commissionCalcOrderInfoDTO.setRenewalTermTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        commissionCalcOrderInfoDTO.setPayWay("payWay");
        commissionCalcOrderInfoDTO.setCoveredYears("coveredYears");
        commissionCalcOrderInfoDTO.setValidPeriod("validPeriod");
        final CommissionCalcRiskInfoDTO commissionCalcRiskInfoDTO = new CommissionCalcRiskInfoDTO();
        commissionCalcRiskInfoDTO.setRiskId(0);
        commissionCalcRiskInfoDTO.setOneYearRisk(2020);
        commissionCalcRiskInfoDTO.setAddTermNum(0);
        commissionCalcRiskInfoDTO.setPayWay("payWay");
        commissionCalcRiskInfoDTO.setCoveredYears("coveredYears");
        commissionCalcRiskInfoDTO.setValidPeriod("validPeriod");
        commissionCalcRiskInfoDTO.setPremium(new BigDecimal("0.00"));
        commissionCalcRiskInfoDTO.setRefundAmount(new BigDecimal("0.00"));
        commissionCalcRiskInfoDTO.setRenewalAmount(new BigDecimal("0.00"));
        commissionCalcOrderInfoDTO.setRiskParamList(Arrays.asList(commissionCalcRiskInfoDTO));
        commissionCalcOrderInfoDTO.setLongInsurance(0);
        final List<CommissionCalcOrderInfoDTO> orderInfoList = Arrays.asList(commissionCalcOrderInfoDTO);

        // Configure SmOrderMapper.listOrderInsuredByPolicyNo(...).
        final SmPolicyInsured smPolicyInsured = new SmPolicyInsured();
        smPolicyInsured.setId(0);
        smPolicyInsured.setPolicyNo("policyNo");
        smPolicyInsured.setIdNumber("idNumber");
        smPolicyInsured.setPersonName("personName");
        smPolicyInsured.setFhOrderId("fhOrderId");
        final List<SmPolicyInsured> insureds = Arrays.asList(smPolicyInsured);
        mockSmOrderMapper.listOrderInsuredByPolicyNo("originalPolicyNo");

        mockSmOrderMapper.listOriginalOrderByOrderIdAndIdNumber("fhOrderId", Arrays.asList("value"));

        // Run the test
        commissionExternalServiceUnderTest.getGroupInsureIncreaseOrderOriginal(orderInfoList);

        // Verify the results
    }

    @Test
    public void testListSmOrderRenewalTerm() {
        // Setup
        final RenewalTermDTO renewalTermDTO = new RenewalTermDTO();
        renewalTermDTO.setPolicyNo("policyNo");
        renewalTermDTO.setRenewalAmount(new BigDecimal("0.00"));
        renewalTermDTO.setRenewalStatus(0);
        renewalTermDTO.setRenewalSuccessSyncDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        renewalTermDTO.setRenewalItemFlag(false);
        final List<RenewalTermDTO> expectedResult = Arrays.asList(renewalTermDTO);

        // Configure SmOrderRenewalTermService.listSmOrderRenewalTermByOrderIdAndTermNum(...).
        final RenewalTermDTO renewalTermDTO1 = new RenewalTermDTO();
        renewalTermDTO1.setPolicyNo("policyNo");
        renewalTermDTO1.setRenewalAmount(new BigDecimal("0.00"));
        renewalTermDTO1.setRenewalStatus(0);
        renewalTermDTO1.setRenewalSuccessSyncDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        renewalTermDTO1.setRenewalItemFlag(false);
        final List<RenewalTermDTO> renewalTermDTOS = Arrays.asList(renewalTermDTO1);
        mockRenewalTermService.listSmOrderRenewalTermByOrderIdAndTermNum("fhOrderId", 0);

        // Run the test
        commissionExternalServiceUnderTest.listSmOrderRenewalTerm("fhOrderId", 0);
    }

    @Test
    public void testListSmOrderRenewalTerm_SmOrderRenewalTermServiceReturnsNoItems() {
        // Setup
        mockRenewalTermService.listSmOrderRenewalTermByOrderIdAndTermNum("fhOrderId", 0);

        // Run the test
        commissionExternalServiceUnderTest.listSmOrderRenewalTerm("fhOrderId", 0);
    }

    @Test
    public void testGetRenewalCommissionCalcOrderParam() {
        // Setup
        final CommissionCalcProcessDTO processDTO = new CommissionCalcProcessDTO();
        processDTO.setOrderId("orderId");
        processDTO.setProductAttrCode("productAttrCode");
        processDTO.setTermNum(0);
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO.setFhOrderId("fhOrderId");
        commissionCalcOrderInfoDTO.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO.setCommissionId(0);
        commissionCalcOrderInfoDTO.setOriginalPolicyNo("originalPolicyNo");
        commissionCalcOrderInfoDTO.setOriginalFhOrderId("fhOrderId");
        commissionCalcOrderInfoDTO.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO.setPolicyNo("policyNo");
        commissionCalcOrderInfoDTO.setAppStatus("appStatus");
        commissionCalcOrderInfoDTO.setInsuredIdNumber("insuredIdNumber");
        commissionCalcOrderInfoDTO.setPlanId(0);
        commissionCalcOrderInfoDTO.setTotalAmount(new BigDecimal("0.00"));
        commissionCalcOrderInfoDTO.setOriginalAmount(new BigDecimal("0.00"));
        commissionCalcOrderInfoDTO.setSurrenderTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        commissionCalcOrderInfoDTO.setRefundAmount(new BigDecimal("0.00"));
        commissionCalcOrderInfoDTO.setPaymentTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        commissionCalcOrderInfoDTO.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        commissionCalcOrderInfoDTO.setVisitStatus("visitStatus");
        commissionCalcOrderInfoDTO.setSurrenderType("surrenderType");
        commissionCalcOrderInfoDTO.setRenewalAmount(new BigDecimal("0.00"));
        commissionCalcOrderInfoDTO.setRenewalTermTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        commissionCalcOrderInfoDTO.setPayWay("payWay");
        commissionCalcOrderInfoDTO.setCoveredYears("coveredYears");
        commissionCalcOrderInfoDTO.setValidPeriod("validPeriod");
        final CommissionCalcRiskInfoDTO commissionCalcRiskInfoDTO = new CommissionCalcRiskInfoDTO();
        commissionCalcRiskInfoDTO.setRiskId(0);
        commissionCalcRiskInfoDTO.setOneYearRisk(2020);
        commissionCalcRiskInfoDTO.setAddTermNum(0);
        commissionCalcRiskInfoDTO.setPayWay("payWay");
        commissionCalcRiskInfoDTO.setCoveredYears("coveredYears");
        commissionCalcRiskInfoDTO.setValidPeriod("validPeriod");
        commissionCalcRiskInfoDTO.setPremium(new BigDecimal("0.00"));
        commissionCalcRiskInfoDTO.setRefundAmount(new BigDecimal("0.00"));
        commissionCalcRiskInfoDTO.setRenewalAmount(new BigDecimal("0.00"));
        commissionCalcOrderInfoDTO.setRiskParamList(Arrays.asList(commissionCalcRiskInfoDTO));
        commissionCalcOrderInfoDTO.setLongInsurance(0);
        processDTO.setOrderInfo(Arrays.asList(commissionCalcOrderInfoDTO));
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO1 = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO1.setFhOrderId("fhOrderId");
        commissionCalcOrderInfoDTO1.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO1.setCommissionId(0);
        commissionCalcOrderInfoDTO1.setOriginalPolicyNo("originalPolicyNo");
        commissionCalcOrderInfoDTO1.setOriginalFhOrderId("fhOrderId");
        commissionCalcOrderInfoDTO1.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO1.setPolicyNo("policyNo");
        commissionCalcOrderInfoDTO1.setAppStatus("appStatus");
        commissionCalcOrderInfoDTO1.setInsuredIdNumber("insuredIdNumber");
        commissionCalcOrderInfoDTO1.setPlanId(0);
        commissionCalcOrderInfoDTO1.setTotalAmount(new BigDecimal("0.00"));
        commissionCalcOrderInfoDTO1.setOriginalAmount(new BigDecimal("0.00"));
        commissionCalcOrderInfoDTO1.setSurrenderTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        commissionCalcOrderInfoDTO1.setRefundAmount(new BigDecimal("0.00"));
        commissionCalcOrderInfoDTO1.setPaymentTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        commissionCalcOrderInfoDTO1.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        commissionCalcOrderInfoDTO1.setVisitStatus("visitStatus");
        commissionCalcOrderInfoDTO1.setSurrenderType("surrenderType");
        commissionCalcOrderInfoDTO1.setRenewalAmount(new BigDecimal("0.00"));
        commissionCalcOrderInfoDTO1.setRenewalTermTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        commissionCalcOrderInfoDTO1.setPayWay("payWay");
        commissionCalcOrderInfoDTO1.setCoveredYears("coveredYears");
        commissionCalcOrderInfoDTO1.setValidPeriod("validPeriod");
        final CommissionCalcRiskInfoDTO commissionCalcRiskInfoDTO1 = new CommissionCalcRiskInfoDTO();
        commissionCalcRiskInfoDTO1.setRiskId(0);
        commissionCalcRiskInfoDTO1.setOneYearRisk(2020);
        commissionCalcRiskInfoDTO1.setAddTermNum(0);
        commissionCalcRiskInfoDTO1.setPayWay("payWay");
        commissionCalcRiskInfoDTO1.setCoveredYears("coveredYears");
        commissionCalcRiskInfoDTO1.setValidPeriod("validPeriod");
        commissionCalcRiskInfoDTO1.setPremium(new BigDecimal("0.00"));
        commissionCalcOrderInfoDTO1.setRiskParamList(Arrays.asList(commissionCalcRiskInfoDTO1));
        processDTO.setRefundOrderInfo(Arrays.asList(commissionCalcOrderInfoDTO1));

        final OrderCommissionInsuredInfoDTO orderCommissionInsuredInfoDTO = new OrderCommissionInsuredInfoDTO();
        orderCommissionInsuredInfoDTO.setTotalAmount(new BigDecimal("0.00"));
        orderCommissionInsuredInfoDTO.setPolicyNo("policyNo");
        orderCommissionInsuredInfoDTO.setAppStatus("appStatus");
        orderCommissionInsuredInfoDTO.setInsuredIdNumber("insuredIdNumber");
        orderCommissionInsuredInfoDTO.setSurrenderTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<OrderCommissionInsuredInfoDTO> insuredInfoDTOList = Arrays.asList(orderCommissionInsuredInfoDTO);
        final RenewalTermDTO renewalTermDTO = new RenewalTermDTO();
        renewalTermDTO.setPolicyNo("policyNo");
        renewalTermDTO.setRenewalAmount(new BigDecimal("0.00"));
        renewalTermDTO.setRenewalStatus(0);
        renewalTermDTO.setRenewalSuccessSyncDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        renewalTermDTO.setRenewalItemFlag(false);
        final List<RenewalTermDTO> renewalTermDTOList = Arrays.asList(renewalTermDTO);

        // Configure SmOrderPolicyService.listOrderPolicyByFhOrderId(...).
        final SmOrderPolicy smOrderPolicy = new SmOrderPolicy();
        smOrderPolicy.setId(0);
        smOrderPolicy.setChannel("channel");
        smOrderPolicy.setPolicyNo("policyNo");
        smOrderPolicy.setPayType("payType");
        smOrderPolicy.setPayPeriod("payPeriod");
        smOrderPolicy.setPayUnit("payUnit");
        smOrderPolicy.setValidPeriod("validPeriod");
        smOrderPolicy.setValidUnit("validUnit");
        smOrderPolicy.setVisitStatus("visitStatus");
        smOrderPolicy.setSurrenderTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        smOrderPolicy.setCancelAmount(new BigDecimal("0.00"));
        smOrderPolicy.setSurrenderType("surrenderType");
        final List<SmOrderPolicy> smOrderPolicies = Arrays.asList(smOrderPolicy);
        mockSmOrderPolicyService.listOrderPolicyByFhOrderId("orderId");

        // Configure SmOrderRiskService.listRiskPremiumByFhOrderIdAndTermNum(...).
        final OrderRiskPremiumDTO orderRiskPremiumDTO = new OrderRiskPremiumDTO();
        orderRiskPremiumDTO.setFhOrderId("fhOrderId");
        orderRiskPremiumDTO.setInsuredIdNumber("insuredIdNumber");
        orderRiskPremiumDTO.setRiskId(0L);
        orderRiskPremiumDTO.setRiskName("riskName");
        orderRiskPremiumDTO.setRiskSurrenderType("riskSurrenderType");
        final List<OrderRiskPremiumDTO> orderRiskPremiumDTOS = Arrays.asList(orderRiskPremiumDTO);
        mockSmOrderRiskService.listRiskPremiumByFhOrderIdAndTermNum("orderId", 0);

        // Run the test
        commissionExternalServiceUnderTest.getRenewalCommissionCalcOrderParam(processDTO, insuredInfoDTOList,
                renewalTermDTOList);

    }

    @Test
    public void testGetRenewalCommissionCalcOrderParam_SmOrderPolicyServiceReturnsNoItems() {
        // Setup
        final CommissionCalcProcessDTO processDTO = new CommissionCalcProcessDTO();
        processDTO.setOrderId("orderId");
        processDTO.setProductAttrCode("productAttrCode");
        processDTO.setTermNum(0);
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO.setFhOrderId("fhOrderId");
        commissionCalcOrderInfoDTO.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO.setCommissionId(0);
        commissionCalcOrderInfoDTO.setOriginalPolicyNo("originalPolicyNo");
        commissionCalcOrderInfoDTO.setOriginalFhOrderId("fhOrderId");
        commissionCalcOrderInfoDTO.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO.setPolicyNo("policyNo");
        commissionCalcOrderInfoDTO.setAppStatus("appStatus");
        commissionCalcOrderInfoDTO.setInsuredIdNumber("insuredIdNumber");
        commissionCalcOrderInfoDTO.setPlanId(0);
        commissionCalcOrderInfoDTO.setTotalAmount(new BigDecimal("0.00"));
        commissionCalcOrderInfoDTO.setOriginalAmount(new BigDecimal("0.00"));
        commissionCalcOrderInfoDTO.setSurrenderTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        commissionCalcOrderInfoDTO.setRefundAmount(new BigDecimal("0.00"));
        commissionCalcOrderInfoDTO.setPaymentTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        commissionCalcOrderInfoDTO.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        commissionCalcOrderInfoDTO.setVisitStatus("visitStatus");
        commissionCalcOrderInfoDTO.setSurrenderType("surrenderType");
        commissionCalcOrderInfoDTO.setRenewalAmount(new BigDecimal("0.00"));
        commissionCalcOrderInfoDTO.setRenewalTermTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        commissionCalcOrderInfoDTO.setPayWay("payWay");
        commissionCalcOrderInfoDTO.setCoveredYears("coveredYears");
        commissionCalcOrderInfoDTO.setValidPeriod("validPeriod");
        final CommissionCalcRiskInfoDTO commissionCalcRiskInfoDTO = new CommissionCalcRiskInfoDTO();
        commissionCalcRiskInfoDTO.setRiskId(0);
        commissionCalcRiskInfoDTO.setOneYearRisk(2020);
        commissionCalcRiskInfoDTO.setAddTermNum(0);
        commissionCalcRiskInfoDTO.setPayWay("payWay");
        commissionCalcRiskInfoDTO.setCoveredYears("coveredYears");
        commissionCalcRiskInfoDTO.setValidPeriod("validPeriod");
        commissionCalcRiskInfoDTO.setPremium(new BigDecimal("0.00"));
        commissionCalcRiskInfoDTO.setRefundAmount(new BigDecimal("0.00"));
        commissionCalcRiskInfoDTO.setRenewalAmount(new BigDecimal("0.00"));
        commissionCalcOrderInfoDTO.setRiskParamList(Arrays.asList(commissionCalcRiskInfoDTO));
        commissionCalcOrderInfoDTO.setLongInsurance(0);
        processDTO.setOrderInfo(Arrays.asList(commissionCalcOrderInfoDTO));
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO1 = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO1.setFhOrderId("fhOrderId");
        commissionCalcOrderInfoDTO1.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO1.setCommissionId(0);
        commissionCalcOrderInfoDTO1.setOriginalPolicyNo("originalPolicyNo");
        commissionCalcOrderInfoDTO1.setOriginalFhOrderId("fhOrderId");
        commissionCalcOrderInfoDTO1.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO1.setPolicyNo("policyNo");
        commissionCalcOrderInfoDTO1.setAppStatus("appStatus");
        commissionCalcOrderInfoDTO1.setInsuredIdNumber("insuredIdNumber");
        commissionCalcOrderInfoDTO1.setPlanId(0);
        commissionCalcOrderInfoDTO1.setTotalAmount(new BigDecimal("0.00"));
        commissionCalcOrderInfoDTO1.setOriginalAmount(new BigDecimal("0.00"));
        commissionCalcOrderInfoDTO1.setSurrenderTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        commissionCalcOrderInfoDTO1.setRefundAmount(new BigDecimal("0.00"));
        commissionCalcOrderInfoDTO1.setPaymentTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        commissionCalcOrderInfoDTO1.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        commissionCalcOrderInfoDTO1.setVisitStatus("visitStatus");
        commissionCalcOrderInfoDTO1.setSurrenderType("surrenderType");
        commissionCalcOrderInfoDTO1.setRenewalAmount(new BigDecimal("0.00"));
        commissionCalcOrderInfoDTO1.setRenewalTermTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        commissionCalcOrderInfoDTO1.setPayWay("payWay");
        commissionCalcOrderInfoDTO1.setCoveredYears("coveredYears");
        commissionCalcOrderInfoDTO1.setValidPeriod("validPeriod");
        final CommissionCalcRiskInfoDTO commissionCalcRiskInfoDTO1 = new CommissionCalcRiskInfoDTO();
        commissionCalcRiskInfoDTO1.setRiskId(0);
        commissionCalcRiskInfoDTO1.setOneYearRisk(2020);
        commissionCalcRiskInfoDTO1.setAddTermNum(0);
        commissionCalcRiskInfoDTO1.setPayWay("payWay");
        commissionCalcRiskInfoDTO1.setCoveredYears("coveredYears");
        commissionCalcRiskInfoDTO1.setValidPeriod("validPeriod");
        commissionCalcRiskInfoDTO1.setPremium(new BigDecimal("0.00"));
        commissionCalcOrderInfoDTO1.setRiskParamList(Arrays.asList(commissionCalcRiskInfoDTO1));
        processDTO.setRefundOrderInfo(Arrays.asList(commissionCalcOrderInfoDTO1));

        final OrderCommissionInsuredInfoDTO orderCommissionInsuredInfoDTO = new OrderCommissionInsuredInfoDTO();
        orderCommissionInsuredInfoDTO.setTotalAmount(new BigDecimal("0.00"));
        orderCommissionInsuredInfoDTO.setPolicyNo("policyNo");
        orderCommissionInsuredInfoDTO.setAppStatus("appStatus");
        orderCommissionInsuredInfoDTO.setInsuredIdNumber("insuredIdNumber");
        orderCommissionInsuredInfoDTO.setSurrenderTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<OrderCommissionInsuredInfoDTO> insuredInfoDTOList = Arrays.asList(orderCommissionInsuredInfoDTO);
        final RenewalTermDTO renewalTermDTO = new RenewalTermDTO();
        renewalTermDTO.setPolicyNo("policyNo");
        renewalTermDTO.setRenewalAmount(new BigDecimal("0.00"));
        renewalTermDTO.setRenewalStatus(0);
        renewalTermDTO.setRenewalSuccessSyncDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        renewalTermDTO.setRenewalItemFlag(false);
        final List<RenewalTermDTO> renewalTermDTOList = Arrays.asList(renewalTermDTO);
        mockSmOrderPolicyService.listOrderPolicyByFhOrderId("orderId");

        // Configure SmOrderRiskService.listRiskPremiumByFhOrderIdAndTermNum(...).
        final OrderRiskPremiumDTO orderRiskPremiumDTO = new OrderRiskPremiumDTO();
        orderRiskPremiumDTO.setFhOrderId("fhOrderId");
        orderRiskPremiumDTO.setInsuredIdNumber("insuredIdNumber");
        orderRiskPremiumDTO.setRiskId(0L);
        orderRiskPremiumDTO.setRiskName("riskName");
        orderRiskPremiumDTO.setRiskSurrenderType("riskSurrenderType");
        final List<OrderRiskPremiumDTO> orderRiskPremiumDTOS = Arrays.asList(orderRiskPremiumDTO);
        mockSmOrderRiskService.listRiskPremiumByFhOrderIdAndTermNum("orderId", 0);

        // Run the test
        commissionExternalServiceUnderTest.getRenewalCommissionCalcOrderParam(processDTO, insuredInfoDTOList,
                renewalTermDTOList);

        // Verify the results
    }

    @Test
    public void testGetRenewalCommissionCalcOrderParam_SmOrderRiskServiceReturnsNoItems() {
        // Setup
        final CommissionCalcProcessDTO processDTO = new CommissionCalcProcessDTO();
        processDTO.setOrderId("orderId");
        processDTO.setProductAttrCode("productAttrCode");
        processDTO.setTermNum(0);
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO.setFhOrderId("fhOrderId");
        commissionCalcOrderInfoDTO.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO.setCommissionId(0);
        commissionCalcOrderInfoDTO.setOriginalPolicyNo("originalPolicyNo");
        commissionCalcOrderInfoDTO.setOriginalFhOrderId("fhOrderId");
        commissionCalcOrderInfoDTO.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO.setPolicyNo("policyNo");
        commissionCalcOrderInfoDTO.setAppStatus("appStatus");
        commissionCalcOrderInfoDTO.setInsuredIdNumber("insuredIdNumber");
        commissionCalcOrderInfoDTO.setPlanId(0);
        commissionCalcOrderInfoDTO.setTotalAmount(new BigDecimal("0.00"));
        commissionCalcOrderInfoDTO.setOriginalAmount(new BigDecimal("0.00"));
        commissionCalcOrderInfoDTO.setSurrenderTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        commissionCalcOrderInfoDTO.setRefundAmount(new BigDecimal("0.00"));
        commissionCalcOrderInfoDTO.setPaymentTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        commissionCalcOrderInfoDTO.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        commissionCalcOrderInfoDTO.setVisitStatus("visitStatus");
        commissionCalcOrderInfoDTO.setSurrenderType("surrenderType");
        commissionCalcOrderInfoDTO.setRenewalAmount(new BigDecimal("0.00"));
        commissionCalcOrderInfoDTO.setRenewalTermTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        commissionCalcOrderInfoDTO.setPayWay("payWay");
        commissionCalcOrderInfoDTO.setCoveredYears("coveredYears");
        commissionCalcOrderInfoDTO.setValidPeriod("validPeriod");
        final CommissionCalcRiskInfoDTO commissionCalcRiskInfoDTO = new CommissionCalcRiskInfoDTO();
        commissionCalcRiskInfoDTO.setRiskId(0);
        commissionCalcRiskInfoDTO.setOneYearRisk(2020);
        commissionCalcRiskInfoDTO.setAddTermNum(0);
        commissionCalcRiskInfoDTO.setPayWay("payWay");
        commissionCalcRiskInfoDTO.setCoveredYears("coveredYears");
        commissionCalcRiskInfoDTO.setValidPeriod("validPeriod");
        commissionCalcRiskInfoDTO.setPremium(new BigDecimal("0.00"));
        commissionCalcRiskInfoDTO.setRefundAmount(new BigDecimal("0.00"));
        commissionCalcRiskInfoDTO.setRenewalAmount(new BigDecimal("0.00"));
        commissionCalcOrderInfoDTO.setRiskParamList(Arrays.asList(commissionCalcRiskInfoDTO));
        commissionCalcOrderInfoDTO.setLongInsurance(0);
        processDTO.setOrderInfo(Arrays.asList(commissionCalcOrderInfoDTO));
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO1 = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO1.setFhOrderId("fhOrderId");
        commissionCalcOrderInfoDTO1.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO1.setCommissionId(0);
        commissionCalcOrderInfoDTO1.setOriginalPolicyNo("originalPolicyNo");
        commissionCalcOrderInfoDTO1.setOriginalFhOrderId("fhOrderId");
        commissionCalcOrderInfoDTO1.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO1.setPolicyNo("policyNo");
        commissionCalcOrderInfoDTO1.setAppStatus("appStatus");
        commissionCalcOrderInfoDTO1.setInsuredIdNumber("insuredIdNumber");
        commissionCalcOrderInfoDTO1.setPlanId(0);
        commissionCalcOrderInfoDTO1.setTotalAmount(new BigDecimal("0.00"));
        commissionCalcOrderInfoDTO1.setOriginalAmount(new BigDecimal("0.00"));
        commissionCalcOrderInfoDTO1.setSurrenderTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        commissionCalcOrderInfoDTO1.setRefundAmount(new BigDecimal("0.00"));
        commissionCalcOrderInfoDTO1.setPaymentTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        commissionCalcOrderInfoDTO1.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        commissionCalcOrderInfoDTO1.setVisitStatus("visitStatus");
        commissionCalcOrderInfoDTO1.setSurrenderType("surrenderType");
        commissionCalcOrderInfoDTO1.setRenewalAmount(new BigDecimal("0.00"));
        commissionCalcOrderInfoDTO1.setRenewalTermTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        commissionCalcOrderInfoDTO1.setPayWay("payWay");
        commissionCalcOrderInfoDTO1.setCoveredYears("coveredYears");
        commissionCalcOrderInfoDTO1.setValidPeriod("validPeriod");
        final CommissionCalcRiskInfoDTO commissionCalcRiskInfoDTO1 = new CommissionCalcRiskInfoDTO();
        commissionCalcRiskInfoDTO1.setRiskId(0);
        commissionCalcRiskInfoDTO1.setOneYearRisk(2020);
        commissionCalcRiskInfoDTO1.setAddTermNum(0);
        commissionCalcRiskInfoDTO1.setPayWay("payWay");
        commissionCalcRiskInfoDTO1.setCoveredYears("coveredYears");
        commissionCalcRiskInfoDTO1.setValidPeriod("validPeriod");
        commissionCalcRiskInfoDTO1.setPremium(new BigDecimal("0.00"));
        commissionCalcOrderInfoDTO1.setRiskParamList(Arrays.asList(commissionCalcRiskInfoDTO1));
        processDTO.setRefundOrderInfo(Arrays.asList(commissionCalcOrderInfoDTO1));

        final OrderCommissionInsuredInfoDTO orderCommissionInsuredInfoDTO = new OrderCommissionInsuredInfoDTO();
        orderCommissionInsuredInfoDTO.setTotalAmount(new BigDecimal("0.00"));
        orderCommissionInsuredInfoDTO.setPolicyNo("policyNo");
        orderCommissionInsuredInfoDTO.setAppStatus("appStatus");
        orderCommissionInsuredInfoDTO.setInsuredIdNumber("insuredIdNumber");
        orderCommissionInsuredInfoDTO.setSurrenderTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<OrderCommissionInsuredInfoDTO> insuredInfoDTOList = Arrays.asList(orderCommissionInsuredInfoDTO);
        final RenewalTermDTO renewalTermDTO = new RenewalTermDTO();
        renewalTermDTO.setPolicyNo("policyNo");
        renewalTermDTO.setRenewalAmount(new BigDecimal("0.00"));
        renewalTermDTO.setRenewalStatus(0);
        renewalTermDTO.setRenewalSuccessSyncDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        renewalTermDTO.setRenewalItemFlag(false);
        final List<RenewalTermDTO> renewalTermDTOList = Arrays.asList(renewalTermDTO);

        // Configure SmOrderPolicyService.listOrderPolicyByFhOrderId(...).
        final SmOrderPolicy smOrderPolicy = new SmOrderPolicy();
        smOrderPolicy.setId(0);
        smOrderPolicy.setChannel("channel");
        smOrderPolicy.setPolicyNo("policyNo");
        smOrderPolicy.setPayType("payType");
        smOrderPolicy.setPayPeriod("payPeriod");
        smOrderPolicy.setPayUnit("payUnit");
        smOrderPolicy.setValidPeriod("validPeriod");
        smOrderPolicy.setValidUnit("validUnit");
        smOrderPolicy.setVisitStatus("visitStatus");
        smOrderPolicy.setSurrenderTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        smOrderPolicy.setCancelAmount(new BigDecimal("0.00"));
        smOrderPolicy.setSurrenderType("surrenderType");
        final List<SmOrderPolicy> smOrderPolicies = Arrays.asList(smOrderPolicy);
        mockSmOrderPolicyService.listOrderPolicyByFhOrderId("orderId");

        mockSmOrderRiskService.listRiskPremiumByFhOrderIdAndTermNum("orderId", 0);

        // Run the test
        commissionExternalServiceUnderTest.getRenewalCommissionCalcOrderParam(processDTO, insuredInfoDTOList,
                renewalTermDTOList);

        // Verify the results
    }

    @Test
    public void testDoRenewalInfo() {
        // Setup
        final CommissionCalcOrderInfoDTO commissionCalcOrderInfoDTO = new CommissionCalcOrderInfoDTO();
        commissionCalcOrderInfoDTO.setFhOrderId("fhOrderId");
        commissionCalcOrderInfoDTO.setProductAttrCode("productAttrCode");
        commissionCalcOrderInfoDTO.setCommissionId(0);
        commissionCalcOrderInfoDTO.setOriginalPolicyNo("originalPolicyNo");
        commissionCalcOrderInfoDTO.setOriginalFhOrderId("fhOrderId");
        commissionCalcOrderInfoDTO.setOriginalCommissionId(0);
        commissionCalcOrderInfoDTO.setPolicyNo("policyNo");
        commissionCalcOrderInfoDTO.setAppStatus("appStatus");
        commissionCalcOrderInfoDTO.setInsuredIdNumber("insuredIdNumber");
        commissionCalcOrderInfoDTO.setPlanId(0);
        commissionCalcOrderInfoDTO.setTotalAmount(new BigDecimal("0.00"));
        commissionCalcOrderInfoDTO.setOriginalAmount(new BigDecimal("0.00"));
        commissionCalcOrderInfoDTO.setSurrenderTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        commissionCalcOrderInfoDTO.setRefundAmount(new BigDecimal("0.00"));
        commissionCalcOrderInfoDTO.setPaymentTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        commissionCalcOrderInfoDTO.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        commissionCalcOrderInfoDTO.setVisitStatus("visitStatus");
        commissionCalcOrderInfoDTO.setSurrenderType("surrenderType");
        commissionCalcOrderInfoDTO.setRenewalAmount(new BigDecimal("0.00"));
        commissionCalcOrderInfoDTO.setRenewalTermTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        commissionCalcOrderInfoDTO.setPayWay("payWay");
        commissionCalcOrderInfoDTO.setCoveredYears("coveredYears");
        commissionCalcOrderInfoDTO.setValidPeriod("validPeriod");
        final CommissionCalcRiskInfoDTO commissionCalcRiskInfoDTO = new CommissionCalcRiskInfoDTO();
        commissionCalcRiskInfoDTO.setRiskId(0);
        commissionCalcRiskInfoDTO.setOneYearRisk(2020);
        commissionCalcRiskInfoDTO.setAddTermNum(0);
        commissionCalcRiskInfoDTO.setPayWay("payWay");
        commissionCalcRiskInfoDTO.setCoveredYears("coveredYears");
        commissionCalcRiskInfoDTO.setValidPeriod("validPeriod");
        commissionCalcRiskInfoDTO.setPremium(new BigDecimal("0.00"));
        commissionCalcRiskInfoDTO.setRefundAmount(new BigDecimal("0.00"));
        commissionCalcRiskInfoDTO.setRenewalAmount(new BigDecimal("0.00"));
        commissionCalcOrderInfoDTO.setRiskParamList(Arrays.asList(commissionCalcRiskInfoDTO));
        commissionCalcOrderInfoDTO.setLongInsurance(0);
        final List<CommissionCalcOrderInfoDTO> orderInfos = Arrays.asList(commissionCalcOrderInfoDTO);
        final RenewalTermDTO renewalTermDTO = new RenewalTermDTO();
        renewalTermDTO.setPolicyNo("policyNo");
        renewalTermDTO.setRenewalAmount(new BigDecimal("0.00"));
        renewalTermDTO.setRenewalStatus(0);
        renewalTermDTO.setRenewalSuccessSyncDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        renewalTermDTO.setRenewalItemFlag(false);
        final List<RenewalTermDTO> renewalTermDTOList = Arrays.asList(renewalTermDTO);

        // Run the test
        commissionExternalServiceUnderTest.doRenewalInfo(orderInfos, renewalTermDTOList);

        // Verify the results
    }

    @Test
    public void testListOldOrderCommission() {
        // Setup
        final ManualQueryOldCommissionDTO dto = new ManualQueryOldCommissionDTO();
        dto.setUserId("userId");
        dto.setPassword("password");
        dto.setAccountStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        dto.setAccountEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        dto.setProductIds(Arrays.asList(0));

        final SmOrderCommissionDTO smOrderCommissionDTO = new SmOrderCommissionDTO();
        smOrderCommissionDTO.setId(0);
        smOrderCommissionDTO.setChannel("channel");
        smOrderCommissionDTO.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smOrderCommissionDTO.setOrderCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smOrderCommissionDTO.setPaymentTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SmOrderCommissionDTO> expectedResult = Arrays.asList(smOrderCommissionDTO);

        // Configure SmOrderMapper.listSmOrderCommission(...).
        final SmOrderCommissionDTO smOrderCommissionDTO1 = new SmOrderCommissionDTO();
        smOrderCommissionDTO1.setId(0);
        smOrderCommissionDTO1.setChannel("channel");
        smOrderCommissionDTO1.setAccountTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smOrderCommissionDTO1.setOrderCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        smOrderCommissionDTO1.setPaymentTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SmOrderCommissionDTO> smOrderCommissionDTOS = Arrays.asList(smOrderCommissionDTO1);
        final ManualQueryOldCommissionDTO query = new ManualQueryOldCommissionDTO();
        query.setUserId("userId");
        query.setPassword("password");
        query.setAccountStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setAccountEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setProductIds(Arrays.asList(0));
        mockSmOrderMapper.listSmOrderCommission(query);

        // Run the test
        commissionExternalServiceUnderTest.listOldOrderCommission(dto);
    }

    @Test
    public void testListOldOrderCommission_SmOrderMapperReturnsNoItems() {
        // Setup
        final ManualQueryOldCommissionDTO dto = new ManualQueryOldCommissionDTO();
        dto.setUserId("userId");
        dto.setPassword("password");
        dto.setAccountStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        dto.setAccountEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        dto.setProductIds(Arrays.asList(0));

        // Configure SmOrderMapper.listSmOrderCommission(...).
        final ManualQueryOldCommissionDTO query = new ManualQueryOldCommissionDTO();
        query.setUserId("userId");
        query.setPassword("password");
        query.setAccountStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setAccountEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setProductIds(Arrays.asList(0));
        mockSmOrderMapper.listSmOrderCommission(query);

        // Run the test
        commissionExternalServiceUnderTest.listOldOrderCommission(dto);
    }
}
