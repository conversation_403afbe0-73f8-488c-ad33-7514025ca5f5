package com.cfpamf.ms.insur.admin.service.product.transform;

import com.cfpamf.ms.insur.admin.pojo.form.product.TestPremiumDutyForm;
import com.cfpamf.ms.insur.admin.pojo.form.product.TestPremiumForm;
import com.github.jsonzou.jmockdata.JMockData;
import org.junit.Test;

/**
 * <AUTHOR> 2021/8/13 14:44
 */
public class AmountTransformTest {

    @Test
    public void apply() {
        final TestPremiumDutyForm mock = JMockData.mock(TestPremiumDutyForm.class);
        mock.getFactorConfig().setParams("{\"calcType\":\"1\",\"baseAmount\":15000}");
        new AmountTransform().apply("amount", JMockData.mock(TestPremiumForm.class),
                mock , null);
    }
}
