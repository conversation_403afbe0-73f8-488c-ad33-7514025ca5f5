package com.cfpamf.ms.insur.admin.service;

import com.cfpamf.ms.insur.admin.pojo.po.SmCommonSetting;
import com.cfpamf.ms.insur.base.util.HttpRequestUtil;
import com.github.jsonzou.jmockdata.JMockData;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import tk.mybatis.mapper.entity.Config;
import tk.mybatis.mapper.mapperhelper.EntityHelper;

@RunWith(PowerMockRunner.class)
@PrepareForTest({HttpRequestUtil.class})
public class SmCommonSettingServiceTest extends BaseTest {
    @InjectMocks
    SmCommonSettingService smCommonSettingService;
    @Mock
    com.cfpamf.ms.insur.admin.dao.safes.SmCommonSettingMapper mapper;

    @Override
    @Before
    public void setUp() throws Exception {
        EntityHelper.initEntityNameMap(SmCommonSetting.class, new Config());
    }

//    @Test
//    public void add() {
//        smCommonSettingService.add(JMockData.mock(com.cfpamf.ms.insur.admin.pojo.po.SmCommonSetting.class));
//    }

//    @Test
//    public void update() {
//        smCommonSettingService.update(JMockData.mock(com.cfpamf.ms.insur.admin.pojo.po.SmCommonSetting.class));
//    }

//    @Test
//    public void delete() {
//        smCommonSettingService.delete(8438);
//    }

    @Test
    public void getCompanySettingsByPage() {
        smCommonSettingService.getCompanySettingsByPage(",SyiYu,", JMockData.mock(com.cfpamf.ms.insur.base.bean.Pageable.class));
    }

    @Test
    public void queryByFieldAndCode() {
        smCommonSettingService.queryByFieldAndCode(",SNgfr,", ",TEAme,");
    }

    @Test
    public void queryByFieldCode() {
        smCommonSettingService.queryByFieldCode(",ZqpcL,");
    }
}
