package com.cfpamf.ms.insur.admin.service.order;

import com.cfpamf.ms.insur.admin.dao.safes.SmOrderMapper;
import com.cfpamf.ms.insur.admin.external.OrderSubmitResponse;
import com.cfpamf.ms.insur.admin.external.zhongan.model.bank.ZaSyncBindInfo;
import com.cfpamf.ms.insur.admin.external.zhongan.model.renewal.ZaRenewalNotify;
import com.cfpamf.ms.insur.admin.external.zhongan.model.renewal.ZaRenewalPolicyInfo;
import com.cfpamf.ms.insur.admin.pojo.dto.BankCardBindingReqDTO;
import com.cfpamf.ms.insur.admin.pojo.dto.SmCreateOrderSubmitRequest;
import com.cfpamf.ms.insur.admin.pojo.dto.order.OrderWaitRenewalDTO;
import com.cfpamf.ms.insur.admin.pojo.po.SmOrderApplicant;
import com.cfpamf.ms.insur.admin.pojo.po.order.ChOrderPersonalNotify;
import com.cfpamf.ms.insur.admin.pojo.po.order.ChOrderPersonalNotifyMsg;
import com.cfpamf.ms.insur.admin.pojo.vo.SmPlanVO;
import com.cfpamf.ms.insur.admin.pojo.vo.SmPolicyInsured;
import com.cfpamf.ms.insur.admin.service.*;
import com.cfpamf.ms.insur.base.service.BusinessTokenService;
import com.cfpamf.ms.insur.base.util.HttpRequestUtil;
import com.github.jsonzou.jmockdata.JMockData;
import org.apache.commons.io.IOUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;

import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> 2021/3/26 10:49
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({HttpRequestUtil.class,IOUtils.class})
public class ZaOrderServiceTest extends BaseTest {
    @InjectMocks
    ZaOrderService zaOrderService;
    @org.mockito.Mock
    com.cfpamf.ms.insur.admin.external.zhongan.ZaOrderServiceAdapter adapter;
    @org.mockito.Mock
    com.cfpamf.ms.insur.admin.external.zhongan.ZaApiProperties zaApiProperties;
    @org.mockito.Mock
    com.cfpamf.ms.insur.admin.dao.safes.order.SmOrderExtendZaMapper zaMapper;
    @org.mockito.Mock
    com.cfpamf.ms.insur.admin.dao.safes.order.SmOrderCancelExtendZaMapper cancelExtendZaMapper;
    @org.mockito.Mock
    com.fasterxml.jackson.databind.ObjectMapper jsonMapper;
    @org.mockito.Mock
    com.cfpamf.ms.insur.admin.dao.safes.order.SmOrderExtendZanMapper zanMapper;
    @org.mockito.Mock
    com.cfpamf.ms.insur.admin.service.SmCancelRefundService cancelRefundService;
    @org.mockito.Mock
    com.cfpamf.ms.insur.admin.dao.safes.product.SmPlanMapperPlusMapper planMapperPlusMapper;
    @Mock
    ChOrderPersonalNotifyService chOrderPersonalNotifyService;
    @Mock
    SmOrderMapper orderMapper;
    @Mock
    RenewalManagerService renewalManagerService;
    @Mock
    BusinessTokenService tokenService;
    @Mock
    OrderCoreService orderCoreService;
    @Mock
    SmProductService productService;
    @Mock
    SmOrderRenewBindService smOrderRenewBindService;

    @Test
    public void support() {
        try {
            zaOrderService.support("za");
        } catch (Exception e) {

        }
    }

    @Test
    public void aiCheckQuery() {
        try {
            zaOrderService.aiCheckQuery(",xoQHw,", ",ERKnt,");
        } catch (Exception e) {

        }
    }

    @Test
    public void handAICheckAccept() throws IOException {
        try {
            zaOrderService.handAICheckAccept(",DBsHV,", ",IKUeS,", new MockHttpServletRequest(), new MockHttpServletResponse());
        } catch (Exception e) {

        }
    }

    @Test
    public void handAICheckAcceptFail() throws IOException {
        try {
            zaOrderService.handAICheckAcceptFail(",Dlmbv,", ",bdcGX,", new MockHttpServletRequest(), new MockHttpServletResponse());
        } catch (Exception e) {

        }
    }

    @Test
    public void handAICheckAcceptTimeout() throws IOException {
        try {
            zaOrderService.handAICheckAcceptTimeout(",geIcT,", ",MZVzj,", new MockHttpServletRequest(), new MockHttpServletResponse());
        } catch (Exception e) {

        }
    }

    @Test
    public void handAcceptSuccess() throws IOException {
        try {
            zaOrderService.handAcceptSuccess(new MockHttpServletRequest(), new MockHttpServletResponse());
        } catch (Exception e) {

        }
    }

    @Test
    public void saveOrderInfoFree() {
        try {
            zaOrderService.saveOrderInfoFree(JMockData.mock(com.cfpamf.ms.insur.admin.external.OrderSubmitRequest.class), JMockData.mock(com.cfpamf.ms.insur.admin.external.OrderSubmitResponse.class), JMockData.mock(com.cfpamf.ms.insur.admin.pojo.vo.SmPlanVO.class));
        } catch (Exception e) {

        }
    }

    @Test
    public void handCancelCallBack() throws IOException {
        try {
            zaOrderService.handCancelCallBack(null,new MockHttpServletRequest(), new MockHttpServletResponse());
        } catch (Exception e) {

        }
    }

    @Test
    public void handRenewalCallback() throws IOException {
        try {
            String msg = "1111";
            PowerMockito.mockStatic(IOUtils.class);
            PowerMockito.when(IOUtils.toString(Mockito.any(InputStream.class),Mockito.anyString())).thenReturn(msg);
            zaOrderService.handRenewalCallback(new MockHttpServletRequest(), new MockHttpServletResponse());
        } catch (Exception e) {

        }
    }

    @Test
    public void getRenewalUrl() throws IOException {
        try {

            Mockito.when(adapter.genToRenewalUrl(Mockito.anyString(),Mockito.anyString(),Mockito.anyString())).thenReturn("");
            zaOrderService.getRenewalUrl("IH1100014169056316","CNBJ0409");
        } catch (Exception e) {

        }
    }

    @Test
    public void handWaitRenewalNotify() throws IOException {
        try {
            String msg = "{\"bizType\":\"renewalPolicy\",\"content\":{\"expiryDate\":\"2021-10-08 23:59:59\",\"insured\":{\"property\":{\"discountFactor\":{\"hasClaim\":\"N\"}}},\"packageCode\":\"AV25\",\"errorCode\":\"\",\"policyNo\":\"IH1100014169056316\",\"packageName\":\"尊享e生2021版（年缴版A）\",\"renewalEndDate\":\"2021-11-07 23:59:59\",\"renewalBeginDate\":\"2021-08-09 00:00:00\",\"message\":\"\",\"groupNo\":\"\",\"effectiveDate\":\"2020-10-09 00:00:00\",\"status\":\"1\"},\"sign\":\"10F46AEA02030863F312C3D87AA4CA66\"}";
            ZaRenewalNotify renewalNotify = new ZaRenewalNotify();
            renewalNotify.setPolicyNo("IH1100014169056316");
            renewalNotify.setStatus("1");
            PowerMockito.mockStatic(IOUtils.class);
            PowerMockito.when(IOUtils.toString(Mockito.any(InputStream.class),Mockito.anyString())).thenReturn(msg);
            PowerMockito.when(jsonMapper.readValue(Mockito.anyString(), Mockito.eq(ZaRenewalNotify.class))).thenReturn(renewalNotify);
            PowerMockito.when(adapter.checkMd5Sign(Mockito.anyString(), Mockito.anyString())).thenReturn(true);
            PowerMockito.when(chOrderPersonalNotifyService.insertOrderPersonalNotifyContent(Mockito.any(ChOrderPersonalNotify.class),Mockito.anyString())).thenReturn(1);
            PowerMockito.when(chOrderPersonalNotifyService.updateOrderGroupStatus(Mockito.anyInt(),Mockito.anyString(),Mockito.anyInt(),Mockito.anyInt())).thenReturn(1);
            PowerMockito.when(orderMapper.listOrderInsuredByPolicyNo(Mockito.anyString())).thenReturn(getInsuredList());
            PowerMockito.when(orderMapper.selectOrderApplicantByOrderId(Mockito.anyString())).thenReturn(getApplicant());
            PowerMockito.when(renewalManagerService.addSmOrderWaitRenewalInfo(Mockito.any(OrderWaitRenewalDTO.class))).thenReturn(1);
            PowerMockito.when(tokenService.lockBusinessToken(Mockito.anyString(),Mockito.anyLong())).thenReturn(true);
            PowerMockito.when(chOrderPersonalNotifyService.getWaitRenewalNotifyByPolicyNo(Mockito.anyString())).thenReturn(null);
            PowerMockito.doNothing().when(tokenService).unlockBusinessToken(Mockito.anyString());
        zaOrderService.handWaitRenewalNotify(new MockHttpServletRequest(), new MockHttpServletResponse());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private List<SmPolicyInsured> getInsuredList(){
        List<SmPolicyInsured> list = new ArrayList<>();
        SmPolicyInsured insured = new SmPolicyInsured();
        insured.setFhOrderId("ZA21092702641484P");
        insured.setCustomerAdminId("ZHNX08073");
        insured.setPolicyNo("IH1100014169056316");
        insured.setPlanId(311);
        list.add(insured);
        return list;
    }
    private SmOrderApplicant getApplicant(){
        SmOrderApplicant applicant = new SmOrderApplicant();
        applicant.setCellPhone("***********");
        applicant.setPersonName("张健");
        return applicant;
    }
    @Test
    public void manualHandRenewPolicy(){
        String msg = "{\"bizType\":\"renewalPolicyInfo\",\"content\":{\"channelInfo\":{\"campaignId\":\"63100079010\",\"channelId\":\"1813\"},\"insured\":{\"birthDay\":\"1980-05-10\",\"occupationType\":\"\",\"gender\":\"F\",\"mail\":\"\",\"phone\":\"\",\"name\":\"孙晓玲\",\"certiNo\":\"130528198005104845\",\"relationToHolder\":\"1\",\"certiType\":\"I\",\"hasSocial\":\"Y\"},\"isAutoPay\":\"Y\",\"issueTime\":\"*************\",\"payTime\":\"*************\",\"notifyContent\":\"ZHNX07749\",\"renewalTimes\":\"1\",\"planName\":\"尊享e生2021版（年缴版A）\",\"policyNo\":\"******************\",\"channelOrderNo\":\"R210046655530\",\"bindCardInfo\":{\"accountId\":\"*********\",\"bizType\":\"RENEWAL\",\"payAccountNo\":\"6228481256042827367\",\"accountNo\":\"6228481256042827367\",\"cardType\":\"BANK\",\"name\":\"孙晓玲\",\"mobile\":\"***********\",\"certiNo\":\"130528198005104845\",\"certiType\":\"SHENFENZHENG\",\"nextInstallmentNo\":\"\"},\"isUpdateCamp\":\"N\",\"expiryDate\":\"2022-11-24 23:59:59\",\"orderPolicySize\":\"4\",\"premium\":\"564.000000\",\"orderTime\":\"*************\",\"uwTime\":\"*************\",\"channelUserId\":\"\",\"listProduct\":[{\"amount\":\"6000000.00\",\"productCode\":\"7G2\",\"premium\":\"475.000000\",\"masterProductCode\":\"\",\"listLiability\":[{\"amount\":\"3000000.00\",\"liabilityCode\":\"ZXG027\",\"liabilityName\":\"一般医疗保险金\"},{\"amount\":\"6000000.00\",\"liabilityCode\":\"ZXG129\",\"liabilityName\":\"重大疾病医疗保险金\"}],\"productName\":\"个人住院医疗保险2021版（B款）\"},{\"amount\":\"0.00\",\"productCode\":\"7G9\",\"premium\":\"28.000000\",\"masterProductCode\":\"7P3\",\"listLiability\":[{\"amount\":\"0.00\",\"liabilityCode\":\"FXG071\",\"liabilityName\":\"家庭共享免赔额\"}],\"productName\":\"附加免赔额豁免保险2021版（A款）\"},{\"amount\":\"18000.00\",\"productCode\":\"7G6\",\"premium\":\"20.000000\",\"masterProductCode\":\"7P3\",\"listLiability\":[{\"amount\":\"18000.00\",\"liabilityCode\":\"FXG022\",\"liabilityName\":\"重大疾病住院津贴保险金\"}],\"productName\":\"附加重大疾病住院津贴保险2021版（A款）\"},{\"amount\":\"6000000.00\",\"productCode\":\"7G4\",\"premium\":\"23.000000\",\"masterProductCode\":\"7P3\",\"listLiability\":[{\"amount\":\"6000000.00\",\"liabilityCode\":\"FXG054\",\"liabilityName\":\"恶性肿瘤院外特定药品费用医疗保险金\"}],\"productName\":\"附加恶性肿瘤院外特定药品费用医疗保险2021版（B款）\"},{\"amount\":\"6000000.00\",\"productCode\":\"7G3\",\"premium\":\"18.000000\",\"masterProductCode\":\"7P3\",\"listLiability\":[{\"amount\":\"6000000.00\",\"liabilityCode\":\"FXG055\",\"liabilityName\":\"恶性肿瘤质子重离子医疗保险金\"}],\"productName\":\"附加恶性肿瘤质子重离子医疗保险2021版（B款）\"}],\"elePolicyUrl\":\"https://t.zhongan.com/open/common/download/docdownload/eyJkb2NUeXBlIjoiMiIsInNpZ24iOiI3Y2ZmMzhiNzk1NTBlODE0YWNjOTU3Y2NlMmZiZTEwNyIsImRvY05vIjoiSUgxMTAwNDI2NjY2NzM4NTIxIn0=\",\"shareFamilySize\":\"4\",\"planId\":\"********\",\"updateCampReason\":\"\",\"giftPolicyNo\":\"\",\"groupNo\":\"R210046655530\",\"firstMonthPremium\":\"\",\"amount\":\"6018000\",\"renewalPolicyNo\":\"******************\",\"coverageTime\":\"oneYear\",\"autoPayInfo\":{\"autoPayType\":\"1\",\"accountName\":\"孙晓玲\",\"accountNo\":\"6228481256042827367\"},\"holder\":{\"birthDay\":\"1980-05-10\",\"gender\":\"F\",\"mail\":\"<EMAIL>\",\"phone\":\"***********\",\"name\":\"孙晓玲\",\"certiNo\":\"130528198005104845\",\"certiType\":\"I\"},\"isFamily\":\"Y\",\"planCode\":\"AV25\",\"originalPolicyNo\":\"******************\",\"applyNo\":\"******************\",\"isShare\":\"Y\",\"tagPolicyNo\":\"\",\"effectiveDate\":\"2021-11-25 00:00:00\",\"extraInfo\":{},\"virtualPlanCode\":\"AV25$**********\"},\"sign\":\"096A93537200F3E267A059854013E0D9\"}";
        //PowerMockito.mockStatic(IOUtils.class);
        //PowerMockito.when(IOUtils.toString(Mockito.any(InputStream.class),Mockito.anyString())).thenReturn(msg);
        //PowerMockito.when(jsonMapper.readValue(Mockito.anyString(), Mockito.eq(ZaRenewalNotify.class))).thenReturn(renewalNotify);
        String policyNo = "******************";
        try {
            PowerMockito.when(chOrderPersonalNotifyService.getRenewalNotifyByPolicyNo(Mockito.anyString(),Mockito.anyString())).thenReturn(null);
            zaOrderService.manualHandRenewPolicy(policyNo);

            ChOrderPersonalNotify notify = new ChOrderPersonalNotify();
            notify.setStatus(2);
            PowerMockito.when(chOrderPersonalNotifyService.getRenewalNotifyByPolicyNo(Mockito.anyString(),Mockito.anyString())).thenReturn(notify);
            zaOrderService.manualHandRenewPolicy(policyNo);


            notify.setStatus(1);
            PowerMockito.when(chOrderPersonalNotifyService.getRenewalNotifyByPolicyNo(Mockito.anyString(),Mockito.anyString())).thenReturn(notify);
            ChOrderPersonalNotifyMsg notifyMsg = new ChOrderPersonalNotifyMsg();
            notifyMsg.setNotifyContent(msg);
            PowerMockito.when(chOrderPersonalNotifyService.getRenewalNotifyMsgByNotifyId(Mockito.anyInt())).thenReturn(notifyMsg);
            ZaRenewalPolicyInfo policyInfo = new ZaRenewalPolicyInfo();
            policyInfo.setPlanCode("$");
            PowerMockito.when(jsonMapper.readValue(Mockito.anyString(), Mockito.eq(ZaRenewalPolicyInfo.class))).thenReturn(policyInfo);
            PowerMockito.when(orderCoreService.isExistPolicyNo(Mockito.anyString())).thenReturn(false);
            PowerMockito.when(productService.getPlanByFhProductId(Mockito.anyString())).thenReturn(new SmPlanVO());
            PowerMockito.when(adapter.cvtRenewalOrder(Mockito.any(ZaRenewalPolicyInfo.class), Mockito.any(SmPlanVO.class))).thenReturn(new SmCreateOrderSubmitRequest());
            PowerMockito.doNothing().when(orderCoreService).saveRenewalOrderInfo(Mockito.any(SmCreateOrderSubmitRequest.class),Mockito.any(OrderSubmitResponse.class),Mockito.any(SmPlanVO.class));
            PowerMockito.doNothing().when(orderCoreService).pushSupplementSuccess(Mockito.anyString());
            zaOrderService.manualHandRenewPolicy(policyNo);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    @Test
    public void manualHandBindCard(){
        String msg ="{\"bizType\":\"bindCardInfo\",\"content\":{\"bankCode\":\"ABC\",\"bizType\":\"RENEWAL\",\"opeType\":\"add\",\"bindType\":\"1\",\"opeTime\":\"2021-11-03 14:32:36\",\"policyNo\":\"******************\",\"bankName\":\"农业银行\",\"cardNo\":\"7367\"},\"sign\":\"911923A0AF586DE8D19055C59CD2E612\"}";
        String policyNo = "******************";
        PowerMockito.when(chOrderPersonalNotifyService.getBindCardNotifyByPolicyNo(Mockito.anyString(),Mockito.anyString())).thenReturn(null);
        try {
            zaOrderService.manualHandBindCard(policyNo);
            List<ChOrderPersonalNotify> lst = new ArrayList<>();
            ChOrderPersonalNotify notify = new ChOrderPersonalNotify();
            notify.setStatus(2);
            lst.add(notify);
            PowerMockito.when(chOrderPersonalNotifyService.getBindCardNotifyByPolicyNo(Mockito.anyString(),Mockito.anyString())).thenReturn(lst );
            zaOrderService.manualHandBindCard(policyNo);

            lst.get(0).setStatus(1);
            PowerMockito.when(chOrderPersonalNotifyService.getBindCardNotifyByPolicyNo(Mockito.anyString(),Mockito.anyString())).thenReturn(lst);
            ChOrderPersonalNotifyMsg notifyMsg = new ChOrderPersonalNotifyMsg();
            notifyMsg.setNotifyContent(msg);
            PowerMockito.when(chOrderPersonalNotifyService.getRenewalNotifyMsgByNotifyId(Mockito.anyInt())).thenReturn(notifyMsg);
            ZaSyncBindInfo policyInfo = new ZaSyncBindInfo();

            PowerMockito.when(jsonMapper.readValue(Mockito.anyString(), Mockito.eq(ZaSyncBindInfo.class))).thenReturn(policyInfo);
            PowerMockito.doNothing().when(smOrderRenewBindService).syncCallback(Mockito.any(BankCardBindingReqDTO.class));
            zaOrderService.manualHandBindCard(policyNo);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
