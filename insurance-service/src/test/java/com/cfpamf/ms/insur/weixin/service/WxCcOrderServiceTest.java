package com.cfpamf.ms.insur.weixin.service;

import com.cfpamf.common.ms.util.DateUtil;
import com.cfpamf.ms.insur.admin.receiver.service.OrderReceiverInfoService;
import com.cfpamf.ms.insur.admin.renewal.service.RenewalConfigService;
import com.cfpamf.ms.insur.admin.service.*;
import com.cfpamf.ms.insur.admin.service.order.SmOrderServiceWrapper;
import com.cfpamf.ms.insur.base.service.BusinessTokenService;
import com.cfpamf.ms.insur.base.util.HttpRequestUtil;
import com.cfpamf.ms.insur.weixin.dao.safes.WxOrderMapper;
import com.cfpamf.ms.insur.weixin.dao.safes.WxUserSettingMapper;
import com.cfpamf.ms.insur.weixin.pojo.query.order.OrderDetailQuery;
import com.cfpamf.ms.insur.weixin.pojo.vo.WxOrderQueryDetailVO;
import com.github.jsonzou.jmockdata.JMockData;
import com.google.code.kaptcha.Producer;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;

@RunWith(PowerMockRunner.class)
@PrepareForTest({HttpRequestUtil.class, DateUtil.class})
public class WxCcOrderServiceTest extends BaseTest {

    @InjectMocks
    private WxCcOrderService wxCcOrderService;

    @Mock
    SmProductService productService;

    @Mock
    ProductRenewRuleService productRenewRuleService;

    @Mock
    private BusinessTokenService tokenService;

    @Mock
    private SmOrderCoreService orderService;

    @Mock
    private OrderReceiverInfoService orderReceiverInfoService;

    @Mock
    private SmOrderRenewBindService smOrderRenewBindService;

    @Mock
    private RenewalConfigService renewalConfigService;

    @Mock
    private WxOrderMapper orderMapper;

    @Mock
    private WxUserSettingMapper wusMapper;

    @Mock
    private Producer captchaProducer;

    @Mock
    SmOrderServiceWrapper orderServiceWrapper;

    @Test
    public void queryOrderDetail(){

//        OrderDetailQuery query = JMockData.mock(OrderDetailQuery.class);
//
//        WxOrderQueryDetailVO detail = wxCcOrderService.queryOrderDetail(query);
//        System.out.println(detail);
//        Assert.assertTrue(detail!=null);
    }


}
