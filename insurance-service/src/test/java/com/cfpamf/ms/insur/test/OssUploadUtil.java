package com.cfpamf.ms.insur.test;

import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.model.CannedAccessControlList;
import com.aliyun.oss.model.ObjectMetadata;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.io.File;
import java.io.FileInputStream;

/**
 * <AUTHOR> 2020/7/2 17:25
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class OssUploadUtil {


    public static void main(String[] args) throws Exception {
//        String file = "/Users/<USER>/Downloads/pco_day_form.xlsx";
        String file = "/Users/<USER>/Downloads/Frame 53.png";
//        String file = "/Users/<USER>/gitwork/zhnx/insurance-image/app-test.js";
        uploadOss(new File(file), "safes/ZHNX09760/", "Frame-53.png");
    }
//
//    public static void batchUpload(String path, String baseKey) throws Exception {
//        if (StringUtils.isBlank(path)) {
//            return;
//        }
//        File file = new File(path);
//        if (!file.exists()) {
//            return;
//        }
//        upload(file, baseKey);
//    }
//
//    public static void upload(File file, String baseKey,String ossKet) throws Exception {
//        if (!file.exists()) {
//            return;
//        }
//        if (file.isFile()) {
//            uploadOss(file, baseKey);
//        }
//        //文件夹递归
//        if (file.isDirectory()) {
//            File[] files = file.listFiles();
//            for (File childFile : files) {
//                upload(childFile, baseKey + file.getName() + "/");
//            }
//        }
//    }

    public static void uploadOss(File file, String baseKey, String osskey) throws Exception {
        ObjectMetadata metadata = new ObjectMetadata();
        FileInputStream fileInputStream = new FileInputStream(file);
        metadata.setContentLength(fileInputStream.available());
        metadata.setCacheControl("no-store");
        metadata.setHeader("Pragma", "no-cache");
        metadata.setContentEncoding("utf-8");
        metadata.setObjectAcl(CannedAccessControlList.PublicRead);
//#生产的新key：
//#key：LTAI4FeQAMY7LFxtNFEPZQax
//#secret：******************************
//#bucket：safesfiles

//  # 非生产的新key：
//  #key：LTAI4FkaWm37HxWhJWVPU4wv
//  #secret：******************************
//  #bucket：safesfiles-test
        OSS oss = new OSSClientBuilder().build("https://oss-cn-beijing.aliyuncs.com/", "LTAI4FeQAMY7LFxtNFEPZQax", "******************************");

        //https://oss-cn-beijing.aliyuncs.com/template/order/import/v1.xlsx
        String key = baseKey + osskey;
        oss.putObject("safesfiles", key, fileInputStream, metadata);

        System.out.println("https://safesfiles.oss-cn-beijing.aliyuncs.com/" + key);
    }
}
