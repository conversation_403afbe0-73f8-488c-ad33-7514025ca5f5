package com.cfpamf.ms.insur.admin.service.order;

import com.cfpamf.ms.insur.admin.config.ProductProperties;
import com.cfpamf.ms.insur.admin.config.ProductSpecialRuleProperties;
import com.cfpamf.ms.insur.admin.dao.safes.*;
import com.cfpamf.ms.insur.admin.dao.safes.order.*;
import com.cfpamf.ms.insur.admin.external.ChannelOrderService;
import com.cfpamf.ms.insur.admin.external.OrderSubmitRequest;
import com.cfpamf.ms.insur.admin.external.OrderSubmitResponse;
import com.cfpamf.ms.insur.admin.external.fh.dto.*;
import com.cfpamf.ms.insur.admin.external.xm.XmApiProperties;
import com.cfpamf.ms.insur.admin.external.xm.XmOrderServiceAdapterImpl;
import com.cfpamf.ms.insur.admin.external.xm.client.XmPolicyClient;
import com.cfpamf.ms.insur.admin.external.xm.model.XmInsuredInfo;
import com.cfpamf.ms.insur.admin.external.xm.model.XmNotifyReq;
import com.cfpamf.ms.insur.admin.external.xm.model.XmProductInfo;
import com.cfpamf.ms.insur.admin.pojo.dto.SmCreateOrderSubmitRequest;
import com.cfpamf.ms.insur.admin.pojo.dto.order.SmOrderCarInfoDTO;
import com.cfpamf.ms.insur.admin.pojo.dto.order.SmOrderHouseDTO;
import com.cfpamf.ms.insur.admin.pojo.form.product.TestPremiumDutyForm;
import com.cfpamf.ms.insur.admin.pojo.form.product.TestPremiumProductForm;
import com.cfpamf.ms.insur.admin.pojo.po.auto.AmPolicyTk;
import com.cfpamf.ms.insur.admin.pojo.po.order.SmOrderPolicy;
import com.cfpamf.ms.insur.admin.pojo.po.order.extend.SmOrderExtendXm;
import com.cfpamf.ms.insur.admin.pojo.query.xinmei.Signature;
import com.cfpamf.ms.insur.admin.pojo.query.xinmei.XmQuery;
import com.cfpamf.ms.insur.admin.pojo.query.xinmei.XmResponse;
import com.cfpamf.ms.insur.admin.pojo.query.xinmei.backvisit.BackVisitHeader;
import com.cfpamf.ms.insur.admin.pojo.query.xinmei.backvisit.BackVisitQueryBody;
import com.cfpamf.ms.insur.admin.pojo.query.xinmei.backvisit.BackVisitResBody;
import com.cfpamf.ms.insur.admin.pojo.query.xinmei.backvisit.BackVisitResHeader;
import com.cfpamf.ms.insur.admin.pojo.vo.SmPlanVO;
import com.cfpamf.ms.insur.admin.renewal.dao.SmOrderRenewalTermMapper;
import com.cfpamf.ms.insur.admin.renewal.service.OrderRenewalRecordService;
import com.cfpamf.ms.insur.admin.service.*;
import com.cfpamf.ms.insur.base.event.EventBusEngine;
import com.cfpamf.ms.insur.base.infeface.CustomerCenterService;
import com.cfpamf.ms.insur.base.service.BusinessTokenService;
import com.cfpamf.ms.insur.base.util.HttpRequestUtil;
import com.cfpamf.ms.insur.common.service.monitor.BusinessMonitorService;
import com.cfpamf.ms.insur.weixin.dao.safes.EndorMapper;
import com.cfpamf.ms.pay.facade.config.PayFacadeConfigProperties;
import com.fasterxml.jackson.core.JsonParseException;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.slf4j.Logger;
import org.springframework.aop.framework.AopContext;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;
import tk.mybatis.mapper.entity.Config;
import tk.mybatis.mapper.mapperhelper.EntityHelper;

import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static org.junit.Assert.assertFalse;
import static org.mockito.Mockito.when;

@RunWith(PowerMockRunner.class)
@PrepareForTest({HttpRequestUtil.class, AopContext.class})
public class XmOrderServiceTest extends BaseTest {

    @Mock private XmOrderServiceAdapterImpl mockAdapter;
    @Mock private ObjectMapper mockMapper;
    @Mock private SmOrderExtendXmMapper mockXmMapper;
    @Mock private SmOrderDDDMapper mockOrderDDDMapper;
    @Mock private SmOrderPolicyMapper mockSmOrderPolicyMapper;
    @Mock private SmOrderRenewalTermMapper mockSmOrderRenewalTermMapper;
    @Mock private XmPolicyClient mockXmPolicyClient;
    @Mock private XmApiProperties mockXmApiProperties;
    private int YEAR_NODE;
    private DateTimeFormatter FMT_PARSE;
    private DateTimeFormatter FMT_SUB;
    @Mock private BusinessMonitorService monitorService;
    @Mock private SmProductService productService;
    @Mock private BusinessTokenService tokenService;
    @Mock private CustomerCenterService ccService;
    @Mock private SmCommissionMapper cmsMapper;
    @Mock private SmOrderMapper orderMapper;
    @Mock private SmOrderItemMapper orderItemMapper;
    @Mock private SmProductFormBuyLimitMapper formBuyLimitMapper;
    @Mock private AuthUserMapper userMapper;
    @Mock private OrderRenewalRecordService orderRenewalRecordService;
    @Mock private OrderCoreService orderCoreService;
    @Mock private SmChannelCallbackMapper channelCallbackMapper;
    @Mock private EventBusEngine busEngine;
    @Mock private SmOrderCarMapper carMapper;
    @Mock private SmOrderHouseMapper houseMapper;
    @Mock private SmOrderGroupNotifyService smOrderGroupNotifyService;
    @Mock private SmCommonSettingService commonSettingService;
    @Mock private SmOrderItemMapper smOrderItemMapper;
    @Mock private SmOrderRenewBindService smOrderRenewBindService;
    @Mock private SmXjxhService smXjxhService;
    @Mock private ChOrderPersonalNotifyService chOrderPersonalNotifyService;
    @Mock private SmOrderRiskDutyMapper riskDutyMapper;
    @Mock private SmProductMapper productMapper;
    @Mock private SmOrderPolicyService smOrderPolicyService;
    @Mock private RenewalManagerService renewalManagerService;
    @Mock private SmOrderInsuredMapper insuredMapper;
    private long tokenLockTime;
    private int phoneLimit;
    @Mock private ProductProperties properties;
    @Mock private ProductSpecialRuleProperties productSpecialRuleProperties;
    @Mock private EndorMapper paymentMapper;
    @Mock private PayFacadeConfigProperties payFacadeConfigProperties;
    @Mock private TempOrderNewProductMapper tempOrderNewProductMapper;
    @Mock private Logger log;

    @InjectMocks private XmOrderService xmOrderServiceUnderTest;

    @Before
    public void setUp() throws Exception {
        super.setUp();
        EntityHelper.initEntityNameMap(AmPolicyTk.class, new Config());
        PowerMockito.mockStatic(AopContext.class);
        when(AopContext.currentProxy()).thenReturn(xmOrderServiceUnderTest);
        Mockito.when(mock.getBean(Mockito.anyString(), (Class<Object>) Mockito.any()))
                .thenAnswer(a -> Mockito.mock(a.getArgument(0)));
    }


    @Test
    public void testChannel() throws Exception {
    }

    @Test
    public void testOrderService() throws Exception {
        // Setup
        // Run the test
        final ChannelOrderService result = xmOrderServiceUnderTest.orderService();

        // Verify the results
    }

    @Test
    public void testSupport() throws Exception {
        assertFalse(xmOrderServiceUnderTest.support("channel"));
    }

    @Test
    public void testUpdateOrderPolicyInfo() throws Exception {
        // Setup
        final Map<String, String> expectedResult = new HashMap<>();

        // Configure SmOrderPolicyMapper.getOrderPolicyByFhOrderId(...).
        final SmOrderPolicy smOrderPolicy = new SmOrderPolicy();
        smOrderPolicy.setId(0);
        smOrderPolicy.setFhOrderId("orderId");
        smOrderPolicy.setChannel("code");
        smOrderPolicy.setPolicyNo("oldPolicyNo");
        smOrderPolicy.setPremium(new BigDecimal("0.00"));
        smOrderPolicy.setAmount(new BigDecimal("0.00"));
        smOrderPolicy.setPayType("payingType");
        smOrderPolicy.setPayPeriod("payingYears");
        smOrderPolicy.setPayUnit("Y");
        smOrderPolicy.setValidPeriod("coveredType");
        smOrderPolicy.setValidUnit("coveredType");
        smOrderPolicy.setVisitTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        smOrderPolicy.setVisitStatus("returnVisitStatus");
        smOrderPolicy.setSurrenderTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        smOrderPolicy.setCancelAmount(new BigDecimal("0.00"));
        smOrderPolicy.setSurrenderType("surrenderType");
        smOrderPolicy.setExtend("extend");
        when(mockSmOrderPolicyMapper.getOrderPolicyByFhOrderId("orderId")).thenReturn(smOrderPolicy);

        when(mockXmApiProperties.getPrivateKey()).thenReturn("privateKey");
        when(mockXmApiProperties.getChannelCode()).thenReturn("channelCode");

        // Configure XmPolicyClient.buildBackVisitQuery(...).
        final XmQuery<BackVisitHeader, BackVisitQueryBody> backVisitHeaderBackVisitQueryBodyXmQuery = new XmQuery<>();
        backVisitHeaderBackVisitQueryBodyXmQuery.setReqHead(null);
        backVisitHeaderBackVisitQueryBodyXmQuery.setReqBody(null);
        final Signature signature = new Signature();
        signature.setSign("sign");
        backVisitHeaderBackVisitQueryBodyXmQuery.setSignature(signature);
        when(mockXmPolicyClient.buildBackVisitQuery("privateKey", "channelCode", Arrays.asList("value"))).thenReturn(backVisitHeaderBackVisitQueryBodyXmQuery);

        // Configure XmPolicyClient.queryBackVisit(...).
        final XmResponse<List<BackVisitResBody>, BackVisitResHeader> listBackVisitResHeaderXmResponse = new XmResponse<>();
        listBackVisitResHeaderXmResponse.setResBody(null);
        listBackVisitResHeaderXmResponse.setResHeader(null);
        final Signature signature1 = new Signature();
        signature1.setSign("sign");
        listBackVisitResHeaderXmResponse.setSignature(signature1);
        final XmQuery<BackVisitHeader, BackVisitQueryBody> data = new XmQuery<>();
        data.setReqHead(null);
        data.setReqBody(null);
        final Signature signature2 = new Signature();
        signature2.setSign("sign");
        data.setSignature(signature2);
        when(mockXmPolicyClient.queryBackVisit(data)).thenReturn(listBackVisitResHeaderXmResponse);

        // Run the test
        final Map<String, String> result = xmOrderServiceUnderTest.updateOrderPolicyInfo("orderId");

        // Verify the results
    }

    @Test
    public void testUpdateOrderPolicyInfo_SmOrderPolicyMapperReturnsNull() {
        // Setup
        final Map<String, String> expectedResult = new HashMap<>();
        when(mockSmOrderPolicyMapper.getOrderPolicyByFhOrderId("orderId")).thenReturn(null);

        // Run the test
        final Map<String, String> result = xmOrderServiceUnderTest.updateOrderPolicyInfo("orderId");

        // Verify the results
    }

    @Test
    public void testUpdateOrderPolicyInfo_XmPolicyClientBuildBackVisitQueryThrowsException() throws Exception {
        // Setup
        final Map<String, String> expectedResult = new HashMap<>();

        // Configure SmOrderPolicyMapper.getOrderPolicyByFhOrderId(...).
        final SmOrderPolicy smOrderPolicy = new SmOrderPolicy();
        smOrderPolicy.setId(0);
        smOrderPolicy.setFhOrderId("orderId");
        smOrderPolicy.setChannel("code");
        smOrderPolicy.setPolicyNo("oldPolicyNo");
        smOrderPolicy.setPremium(new BigDecimal("0.00"));
        smOrderPolicy.setAmount(new BigDecimal("0.00"));
        smOrderPolicy.setPayType("payingType");
        smOrderPolicy.setPayPeriod("payingYears");
        smOrderPolicy.setPayUnit("Y");
        smOrderPolicy.setValidPeriod("coveredType");
        smOrderPolicy.setValidUnit("coveredType");
        smOrderPolicy.setVisitTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        smOrderPolicy.setVisitStatus("returnVisitStatus");
        smOrderPolicy.setSurrenderTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        smOrderPolicy.setCancelAmount(new BigDecimal("0.00"));
        smOrderPolicy.setSurrenderType("surrenderType");
        smOrderPolicy.setExtend("extend");
        when(mockSmOrderPolicyMapper.getOrderPolicyByFhOrderId("orderId")).thenReturn(smOrderPolicy);

        when(mockXmApiProperties.getPrivateKey()).thenReturn("privateKey");
        when(mockXmApiProperties.getChannelCode()).thenReturn("channelCode");
        when(mockXmPolicyClient.buildBackVisitQuery("privateKey", "channelCode", Arrays.asList("value"))).thenThrow(Exception.class);

        // Run the test
        final Map<String, String> result = xmOrderServiceUnderTest.updateOrderPolicyInfo("orderId");

        // Verify the results
    }

    @Test
    public void testNotifyBackVisit() throws Exception {
        // Setup
        // Configure SmOrderPolicyMapper.getOrderPolicyByFhOrderId(...).
        final SmOrderPolicy smOrderPolicy = new SmOrderPolicy();
        smOrderPolicy.setId(0);
        smOrderPolicy.setFhOrderId("orderId");
        smOrderPolicy.setChannel("code");
        smOrderPolicy.setPolicyNo("oldPolicyNo");
        smOrderPolicy.setPremium(new BigDecimal("0.00"));
        smOrderPolicy.setAmount(new BigDecimal("0.00"));
        smOrderPolicy.setPayType("payingType");
        smOrderPolicy.setPayPeriod("payingYears");
        smOrderPolicy.setPayUnit("Y");
        smOrderPolicy.setValidPeriod("coveredType");
        smOrderPolicy.setValidUnit("coveredType");
        smOrderPolicy.setVisitTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        smOrderPolicy.setVisitStatus("returnVisitStatus");
        smOrderPolicy.setSurrenderTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        smOrderPolicy.setCancelAmount(new BigDecimal("0.00"));
        smOrderPolicy.setSurrenderType("surrenderType");
        smOrderPolicy.setExtend("extend");
        when(mockSmOrderPolicyMapper.getOrderPolicyByFhOrderId("orderId")).thenReturn(smOrderPolicy);

        when(mockXmApiProperties.getPrivateKey()).thenReturn("privateKey");
        when(mockXmApiProperties.getChannelCode()).thenReturn("channelCode");

        // Configure XmPolicyClient.buildBackVisitQuery(...).
        final XmQuery<BackVisitHeader, BackVisitQueryBody> backVisitHeaderBackVisitQueryBodyXmQuery = new XmQuery<>();
        backVisitHeaderBackVisitQueryBodyXmQuery.setReqHead(null);
        backVisitHeaderBackVisitQueryBodyXmQuery.setReqBody(null);
        final Signature signature = new Signature();
        signature.setSign("sign");
        backVisitHeaderBackVisitQueryBodyXmQuery.setSignature(signature);
        when(mockXmPolicyClient.buildBackVisitQuery("privateKey", "channelCode", Arrays.asList("value"))).thenReturn(backVisitHeaderBackVisitQueryBodyXmQuery);

        // Configure XmPolicyClient.queryBackVisit(...).
        final XmResponse<List<BackVisitResBody>, BackVisitResHeader> listBackVisitResHeaderXmResponse = new XmResponse<>();
        listBackVisitResHeaderXmResponse.setResBody(null);
        listBackVisitResHeaderXmResponse.setResHeader(null);
        final Signature signature1 = new Signature();
        signature1.setSign("sign");
        listBackVisitResHeaderXmResponse.setSignature(signature1);
        final XmQuery<BackVisitHeader, BackVisitQueryBody> data = new XmQuery<>();
        data.setReqHead(null);
        data.setReqBody(null);
        final Signature signature2 = new Signature();
        signature2.setSign("sign");
        data.setSignature(signature2);
        when(mockXmPolicyClient.queryBackVisit(data)).thenReturn(listBackVisitResHeaderXmResponse);

        // Run the test
        xmOrderServiceUnderTest.notifyBackVisit("orderId");

        // Verify the results
    }

    @Test
    public void testNotifyBackVisit_SmOrderPolicyMapperReturnsNull() {
        // Setup
        when(mockSmOrderPolicyMapper.getOrderPolicyByFhOrderId("orderId")).thenReturn(null);

        // Run the test
        xmOrderServiceUnderTest.notifyBackVisit("orderId");

        // Verify the results
    }

    @Test
    public void testNotifyBackVisit_XmPolicyClientBuildBackVisitQueryThrowsException() throws Exception {
        // Setup
        // Configure SmOrderPolicyMapper.getOrderPolicyByFhOrderId(...).
        final SmOrderPolicy smOrderPolicy = new SmOrderPolicy();
        smOrderPolicy.setId(0);
        smOrderPolicy.setFhOrderId("orderId");
        smOrderPolicy.setChannel("code");
        smOrderPolicy.setPolicyNo("oldPolicyNo");
        smOrderPolicy.setPremium(new BigDecimal("0.00"));
        smOrderPolicy.setAmount(new BigDecimal("0.00"));
        smOrderPolicy.setPayType("payingType");
        smOrderPolicy.setPayPeriod("payingYears");
        smOrderPolicy.setPayUnit("Y");
        smOrderPolicy.setValidPeriod("coveredType");
        smOrderPolicy.setValidUnit("coveredType");
        smOrderPolicy.setVisitTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        smOrderPolicy.setVisitStatus("returnVisitStatus");
        smOrderPolicy.setSurrenderTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        smOrderPolicy.setCancelAmount(new BigDecimal("0.00"));
        smOrderPolicy.setSurrenderType("surrenderType");
        smOrderPolicy.setExtend("extend");
        when(mockSmOrderPolicyMapper.getOrderPolicyByFhOrderId("orderId")).thenReturn(smOrderPolicy);

        when(mockXmApiProperties.getPrivateKey()).thenReturn("privateKey");
        when(mockXmApiProperties.getChannelCode()).thenReturn("channelCode");
        when(mockXmPolicyClient.buildBackVisitQuery("privateKey", "channelCode", Arrays.asList("value"))).thenThrow(Exception.class);

        // Run the test
        xmOrderServiceUnderTest.notifyBackVisit("orderId");

        // Verify the results
    }

    @Test
    public void testHandleBackVisit() {
        // Setup
        final BackVisitResBody body = new BackVisitResBody();
        body.setBackVisitStatus("backVisitStatus");
        body.setBackVisitSuccessWay("backVisitSuccessWay");
        body.setBackVisitSucsessTime("backVisitSucsessTime");
        body.setBeyondHesitatPeriod("beyondHesitatPeriod");
        body.setCertifyCode("certifyCode");

        // Run the test
        xmOrderServiceUnderTest.handleBackVisit(body);

        // Verify the results
    }

    @Test
    public void testNeedUpdate() {
        assertFalse(xmOrderServiceUnderTest.needUpdate("whaleCode"));
    }

    @Test
    public void testHandNotify() {
        xmOrderServiceUnderTest.handNotify(new XmNotifyReq());
    }

    @Test
    public void testSaveXmOrderInfo() {
        // Setup
        // Run the test
        xmOrderServiceUnderTest.saveXmOrderInfo();

        // Verify the results
    }

    @Test(expected = UnsupportedOperationException.class)
    public void testSubmitSuccessAfter() throws Exception {
        xmOrderServiceUnderTest.submitSuccessAfter("userUniqueId", new SmPlanVO(), new OrderSubmitRequest(), new OrderSubmitRequest(), new OrderSubmitResponse());
    }

    @Test(expected = UnsupportedOperationException.class)
    public void testSubmitOrder() throws Exception {
        xmOrderServiceUnderTest.submitOrder("userUniqueId", new OrderSubmitRequest());
    }



    @Test
    public void testJump2Xm() throws Exception {
        // Setup
        final MockHttpServletResponse response = new MockHttpServletResponse();
        when(mockAdapter.genAgentUrl("h5Url", "userId")).thenReturn("result");

        // Run the test
        xmOrderServiceUnderTest.jump2Xm("userId", 0, response);

        // Verify the results
    }

    @Test
    public void testSaveOrderInfo() throws Exception {
        // Setup
        final OrderSubmitRequest dto = new OrderSubmitRequest();
        final FhProduct productInfo = new FhProduct();
        productInfo.setProductId("fhProductId");
        productInfo.setRecommendId("recommendId");
        productInfo.setRecommendMainJobNumber("recommendMainJobNumber");
        productInfo.setRecommendOrgCode("recommendOrgCode");
        dto.setProductInfo(productInfo);
        final FhOrderInfo orderInfo = new FhOrderInfo();
        orderInfo.setSubmitTime("submitTime");
        orderInfo.setStartTime("startTime");
        orderInfo.setEndTime("endTime");
        dto.setOrderInfo(orderInfo);
        final FhProposer proposerInfo = new FhProposer();
        proposerInfo.setPersonName("insuredName");
        proposerInfo.setPersonGender("insuredPersonGender");
        proposerInfo.setIdType("idType");
        proposerInfo.setIdNumber("idNumber");
        proposerInfo.setBirthday("birthday");
        proposerInfo.setCellPhone("cellPhone");
        proposerInfo.setEmail("email");
        proposerInfo.setAddress("address");
        proposerInfo.setAppStatus("-2");
        proposerInfo.setPolicyNo("insIdNumber");
        proposerInfo.setDownloadURL("downloadURL");
        dto.setProposerInfo(proposerInfo);
        final FhInsuredPerson fhInsuredPerson = new FhInsuredPerson();
        fhInsuredPerson.setPersonName("insuredName");
        fhInsuredPerson.setPersonGender("insuredPersonGender");
        fhInsuredPerson.setIdType("idType");
        fhInsuredPerson.setIdNumber("idNumber");
        fhInsuredPerson.setBirthday("birthday");
        fhInsuredPerson.setCellPhone("cellPhone");
        fhInsuredPerson.setAppStatus("-2");
        fhInsuredPerson.setRelationship("insuredAppntShipCode");
        fhInsuredPerson.setFlightNo("flightNo");
        fhInsuredPerson.setFlightTime("flightTime");
        fhInsuredPerson.setOccupationCode("");
        fhInsuredPerson.setOccupationGroup("occupationGroup");
        fhInsuredPerson.setDestinationCountryText("destinationCountryText");
        fhInsuredPerson.setAnnualIncome("annualIncome");
        fhInsuredPerson.setStudentType("studentType");
        fhInsuredPerson.setSchoolType("schoolType");
        fhInsuredPerson.setSchoolNature("schoolNature");
        fhInsuredPerson.setSchoolName("schoolName");
        fhInsuredPerson.setSchoolClass("schoolClass");
        fhInsuredPerson.setStudentId("studentId");
        fhInsuredPerson.setIdPeriodStart("idPeriodStart");
        fhInsuredPerson.setIdPeriodEnd("idPeriodEnd");
        fhInsuredPerson.setSmoke("smoke");
        fhInsuredPerson.setOldPolicyNo("oldPolicyNo");
        final TestPremiumDutyForm testPremiumDutyForm = new TestPremiumDutyForm();
        fhInsuredPerson.setDuties(Arrays.asList(testPremiumDutyForm));
        final TestPremiumProductForm product = new TestPremiumProductForm();
        product.setProductId(0);
        product.setPlanId(0);
        product.setPlanCode("planCode");
        product.setPremium(new BigDecimal("0.00"));
        product.setQty(0);
        final TestPremiumDutyForm testPremiumDutyForm1 = new TestPremiumDutyForm();
        product.setPremiumDutyForms(Arrays.asList(testPremiumDutyForm1));
        fhInsuredPerson.setProduct(product);
        dto.setInsuredPerson(Arrays.asList(fhInsuredPerson));
        final FhProperty propertyInfo = new FhProperty();
        propertyInfo.setPropertyInfoIsExist("0");
        dto.setPropertyInfo(propertyInfo);
        final SmOrderHouseDTO house = new SmOrderHouseDTO();
        dto.setHouse(house);
        final SmOrderCarInfoDTO carInfo = new SmOrderCarInfoDTO();
        dto.setCarInfo(carInfo);
        dto.setProductType("code");
        dto.setBizCode("bizCode");
        dto.setOrderOutType("orderOutType");
        dto.setToken("token");
        dto.setPlanId(0);
        dto.setQty(0);
        dto.setPreOrderId("preOrderId");
        dto.setRenew(false);
        dto.setRealRenewFlag(false);
        dto.setAppNo("fhOrderId");
        dto.setAgentId(0);
        dto.setJobCode("jobCode");

        final OrderSubmitResponse smOrderResp = new OrderSubmitResponse();
        smOrderResp.setNoticeCode("-1");
        smOrderResp.setNoticeMsg("noticeMsg");
        smOrderResp.setOrderId("orderId");
        smOrderResp.setAppNo("fhOrderId");
        final OrderSubmitResponse.ReturnMap returnMap = new OrderSubmitResponse.ReturnMap();
        returnMap.setMsg("msg");
        returnMap.setRenewOrderSn("orderId");
        smOrderResp.setReturnMap(returnMap);

        final SmPlanVO planVo = new SmPlanVO();
        planVo.setId(0);
        planVo.setPlanId(0);
        planVo.setChannel("channel");
        planVo.setProductId(0);
        planVo.setFhProductId("fhProductId");
        planVo.setBuyLimit(0);
        planVo.setPlanCode("fhProductId");

        // Run the test
        xmOrderServiceUnderTest.saveOrderInfo(dto, smOrderResp, planVo);

        // Verify the results
    }

    @Test
    public void testHandOrderPolicy() throws Exception {
        // Setup
        // Configure SmOrderExtendXmMapper.listUnDoRecord(...).
        final SmOrderExtendXm smOrderExtendXm = new SmOrderExtendXm();
        smOrderExtendXm.setId(0);
        smOrderExtendXm.setFhOrderId("orderId");
        smOrderExtendXm.setXmOrderNo("fhOrderId");
        smOrderExtendXm.setCoveredType("coveredType");
        smOrderExtendXm.setCoveredYears(0);
        smOrderExtendXm.setPayingType("payingType");
        smOrderExtendXm.setPayingYears("payingYears");
        smOrderExtendXm.setPayFrequency("payFrequency");
        smOrderExtendXm.setNotifyContent("jsonStr");
        final List<SmOrderExtendXm> smOrderExtendXms = Arrays.asList(smOrderExtendXm);
        when(mockXmMapper.listUnDoRecord()).thenReturn(smOrderExtendXms);

        // Configure ObjectMapper.readValue(...).
        final XmNotifyReq xmNotifyReq = new XmNotifyReq();
        xmNotifyReq.setOrderNo("fhOrderId");
        xmNotifyReq.setPolicyCode("oldPolicyNo");
        xmNotifyReq.setOrderPrem("orderPrem");
        xmNotifyReq.setBizTag("bizExtendsContext");
        xmNotifyReq.setBizExtendsContext("bizExtendsContext");
        final XmInsuredInfo xmInsuredInfo = new XmInsuredInfo();
        final XmProductInfo xmProductInfo = new XmProductInfo();
        xmProductInfo.setCoveredType("coveredType");
        xmProductInfo.setCoveredYears(0);
        xmProductInfo.setPayFrequency("payFrequency");
        xmProductInfo.setPayingType("payingType");
        xmProductInfo.setPayingYears(0);
        xmProductInfo.setAmount(new BigDecimal("0.00"));
        xmInsuredInfo.setProductInfo(Arrays.asList(xmProductInfo));
        xmNotifyReq.setInsuredInfo(Arrays.asList(xmInsuredInfo));
        when(mockMapper.readValue("jsonStr", XmNotifyReq.class)).thenReturn(xmNotifyReq);

        // Run the test
        xmOrderServiceUnderTest.handOrderPolicy();

        // Verify the results
    }

    @Test
    public void testHandOrderPolicy_SmOrderExtendXmMapperListUnDoRecordReturnsNoItems() {
        // Setup
        when(mockXmMapper.listUnDoRecord()).thenReturn(Collections.emptyList());

        // Run the test
        xmOrderServiceUnderTest.handOrderPolicy();

        // Verify the results
    }

    @Test
    public void testHandOrderPolicy_ObjectMapperThrowsIOException() throws Exception {
        // Setup
        // Configure SmOrderExtendXmMapper.listUnDoRecord(...).
        final SmOrderExtendXm smOrderExtendXm = new SmOrderExtendXm();
        smOrderExtendXm.setId(0);
        smOrderExtendXm.setFhOrderId("orderId");
        smOrderExtendXm.setXmOrderNo("fhOrderId");
        smOrderExtendXm.setCoveredType("coveredType");
        smOrderExtendXm.setCoveredYears(0);
        smOrderExtendXm.setPayingType("payingType");
        smOrderExtendXm.setPayingYears("payingYears");
        smOrderExtendXm.setPayFrequency("payFrequency");
        smOrderExtendXm.setNotifyContent("jsonStr");
        final List<SmOrderExtendXm> smOrderExtendXms = Arrays.asList(smOrderExtendXm);
        when(mockXmMapper.listUnDoRecord()).thenReturn(smOrderExtendXms);

        when(mockMapper.readValue("jsonStr", XmNotifyReq.class)).thenThrow(IOException.class);

        // Run the test
        xmOrderServiceUnderTest.handOrderPolicy();

        // Verify the results
    }

    @Test
    public void testHandOrderPolicy_ObjectMapperThrowsJsonParseException() throws Exception {
        // Setup
        // Configure SmOrderExtendXmMapper.listUnDoRecord(...).
        final SmOrderExtendXm smOrderExtendXm = new SmOrderExtendXm();
        smOrderExtendXm.setId(0);
        smOrderExtendXm.setFhOrderId("orderId");
        smOrderExtendXm.setXmOrderNo("fhOrderId");
        smOrderExtendXm.setCoveredType("coveredType");
        smOrderExtendXm.setCoveredYears(0);
        smOrderExtendXm.setPayingType("payingType");
        smOrderExtendXm.setPayingYears("payingYears");
        smOrderExtendXm.setPayFrequency("payFrequency");
        smOrderExtendXm.setNotifyContent("jsonStr");
        final List<SmOrderExtendXm> smOrderExtendXms = Arrays.asList(smOrderExtendXm);
        when(mockXmMapper.listUnDoRecord()).thenReturn(smOrderExtendXms);

        when(mockMapper.readValue("jsonStr", XmNotifyReq.class)).thenThrow(JsonParseException.class);

        // Run the test
        xmOrderServiceUnderTest.handOrderPolicy();

        // Verify the results
    }

    @Test
    public void testHandOrderPolicy_ObjectMapperThrowsJsonMappingException() throws Exception {
        // Setup
        // Configure SmOrderExtendXmMapper.listUnDoRecord(...).
        final SmOrderExtendXm smOrderExtendXm = new SmOrderExtendXm();
        smOrderExtendXm.setId(0);
        smOrderExtendXm.setFhOrderId("orderId");
        smOrderExtendXm.setXmOrderNo("fhOrderId");
        smOrderExtendXm.setCoveredType("coveredType");
        smOrderExtendXm.setCoveredYears(0);
        smOrderExtendXm.setPayingType("payingType");
        smOrderExtendXm.setPayingYears("payingYears");
        smOrderExtendXm.setPayFrequency("payFrequency");
        smOrderExtendXm.setNotifyContent("jsonStr");
        final List<SmOrderExtendXm> smOrderExtendXms = Arrays.asList(smOrderExtendXm);
        when(mockXmMapper.listUnDoRecord()).thenReturn(smOrderExtendXms);

        when(mockMapper.readValue("jsonStr", XmNotifyReq.class)).thenThrow(JsonMappingException.class);

        // Run the test
        xmOrderServiceUnderTest.handOrderPolicy();

        // Verify the results
    }

    @Test
    public void testPushRenewalTerm() {
        // Setup
        // Run the test
        xmOrderServiceUnderTest.pushRenewalTerm(1, 0);

        // Verify the results
    }
}
