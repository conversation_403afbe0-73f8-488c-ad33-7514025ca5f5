package com.cfpamf.ms.insur.weixin.service;

import com.alibaba.fastjson.JSON;
import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.ms.insur.admin.external.zhongan.ZaApiProperties;
import com.cfpamf.ms.insur.admin.external.zhongan.api.PaymentIntentUtil;
import com.cfpamf.ms.insur.admin.pojo.dto.SmProductQuoteLimitDTO;
import com.cfpamf.ms.insur.admin.pojo.dto.order.za.PaymentDTO;
import com.cfpamf.ms.insur.admin.pojo.vo.SmProductCoverageAmountVO;
import com.cfpamf.ms.insur.admin.pojo.vo.SmProductQuoteLimitItemVO;
import com.cfpamf.ms.insur.admin.pojo.vo.SmProductQuoteLimitVO;
import com.cfpamf.ms.insur.base.exception.BizException;
import com.cfpamf.ms.insur.base.exception.ExcptEnum;
import com.cfpamf.ms.insur.base.util.HttpRequestUtil;
import com.cfpamf.ms.insur.weixin.dao.safes.PolicyMapper;
import com.cfpamf.ms.insur.weixin.pojo.dto.WxGlProductQuoteDTO;
import com.cfpamf.ms.insur.weixin.pojo.dto.policy.Endor;
import com.cfpamf.ms.insur.weixin.pojo.dto.product.QuoteLimitDTO;
import com.cfpamf.ms.insur.weixin.pojo.dto.product.TimeFactorItem;
import com.cfpamf.ms.insur.weixin.pojo.query.policy.CoverageQuery;
import com.cfpamf.ms.insur.weixin.pojo.vo.apply.common.GroupCheckReq;
import com.cfpamf.ms.insur.weixin.pojo.vo.apply.common.GroupInsured;
import com.cfpamf.ms.insur.weixin.pojo.vo.apply.zhongan.pay.Commodity;
import com.cfpamf.ms.insur.weixin.pojo.vo.apply.zhongan.pay.PaymentReq;
import com.cfpamf.ms.insur.weixin.pojo.vo.product.EndorConfig;
import com.cfpamf.ms.insur.weixin.service.underwriting.GroupApplyHandler;
import com.github.jsonzou.jmockdata.JMockData;
import com.github.jsonzou.jmockdata.TypeReference;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.beans.factory.annotation.Autowired;

import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Type;
import java.math.BigDecimal;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

@RunWith(PowerMockRunner.class)
@PrepareForTest({HttpRequestUtil.class})
public class GroupApplyHandlerTest {

    @Mock
    private PolicyMapper policyMapper;

    @InjectMocks
    GroupApplyHandler applyHandler;

    @Test
    public void applyCheck(){
        GroupCheckReq req = JMockData.mock(GroupCheckReq.class);
        boolean rtn = applyHandler.applyCheck(req);
        Assert.assertTrue(rtn);
    }

    @Test
    public void applyCheck2(){
        Integer productId = JMockData.mock(Integer.class);
        TypeReference<Map<String,Integer>> tm = new TypeReference<Map<String,Integer>>() {
        };
        Map<String,Integer> ocpMap=JMockData.mock(tm);
        TypeReference<List<CoverageQuery>> tr = new TypeReference<List<CoverageQuery>>() {
        };
        List<CoverageQuery> coverageList = JMockData.mock(tr);

        TimeFactorItem timeFactor = JMockData.mock(TimeFactorItem.class);
        applyHandler.applyCheck(productId,ocpMap,coverageList,timeFactor);
    }

    @Test
    public void checkPersonNumber(){
        Integer qty = 100;
        TypeReference<Map<String,Integer>> tm = new TypeReference<Map<String,Integer>>() {
        };
        Map<String,Integer> ocpMap=JMockData.mock(tm);
        TypeReference<Map<String, CoverageQuery>> tr = new TypeReference<Map<String, CoverageQuery>>() {
        };
        Map<String, CoverageQuery> coverageMap = JMockData.mock(tr);
        QuoteLimitDTO entry = JMockData.mock(QuoteLimitDTO.class);
        entry.setLimitType(SmProductQuoteLimitItemVO.LIMIT_TYPE_PER);
        entry.setMinPerQty(20);
        applyHandler.checkPersonNumber(qty,entry);
    }

    @Test
    public void checkOcpnNumber(){
        Integer qty = JMockData.mock(Integer.class);
        TypeReference<Map<String,Integer>> tm = new TypeReference<Map<String,Integer>>() {
        };
        Map<String,Integer> ocpMap=JMockData.mock(tm);
        TypeReference<Map<String, CoverageQuery>> tr = new TypeReference<Map<String, CoverageQuery>>() {
        };
        Map<String, CoverageQuery> coverageMap = JMockData.mock(tr);
        QuoteLimitDTO entry = JMockData.mock(QuoteLimitDTO.class);
        entry.setLimitType(SmProductQuoteLimitItemVO.LIMIT_TYPE_OCPN_PER);
        applyHandler.checkOcpnNumber(qty,ocpMap,entry);
    }

    @Test
    public void checkCvgRelation(){
        Integer qty = JMockData.mock(Integer.class);
        TypeReference<Map<String,Integer>> tm = new TypeReference<Map<String,Integer>>() {
        };
        Map<String,Integer> ocpMap=JMockData.mock(tm);
        TypeReference<Map<String, CoverageQuery>> tr = new TypeReference<Map<String, CoverageQuery>>() {
        };
        Map<String, CoverageQuery> coverageMap = JMockData.mock(tr);
        QuoteLimitDTO entry = JMockData.mock(QuoteLimitDTO.class);
        entry.setLimitType(SmProductQuoteLimitItemVO.LIMIT_TYPE_CVG_RELY);
        applyHandler.checkCvgRelation(coverageMap,entry);
    }

    @Test
    public void checkCvgAmountLimit(){
        Integer qty = JMockData.mock(Integer.class);
        TypeReference<Map<String,Integer>> tm = new TypeReference<Map<String,Integer>>() {
        };
        Map<String,Integer> ocpMap=JMockData.mock(tm);
        TypeReference<Map<String, CoverageQuery>> tr = new TypeReference<Map<String, CoverageQuery>>() {
        };
        Map<String, CoverageQuery> coverageMap = JMockData.mock(tr);
        QuoteLimitDTO entry = JMockData.mock(QuoteLimitDTO.class);
        entry.setLimitType(SmProductQuoteLimitItemVO.LIMIT_TYPE_CVG_RELY);
        applyHandler.checkCvgAmountLimit(ocpMap,coverageMap,entry);
    }

    @Test
    public void payHtml(){
        String orderId = "za00001";
        BigDecimal amount = new BigDecimal(100);
        PaymentReq req = new PaymentReq();
        req.setOutTradeNo(orderId);
        req.setOrderAmt(String.valueOf(amount));
        req.setPayChannel(new String[]{"wxpay"});
        req.setSignType("MD5");
        req.setRequestCharset("UTF-8");
        req.setTradeType("H5");
        req.setSrcType("H5");
        req.setOrderType("INS");
        req.setShowUrl("www.zhongan.com");
        req.setNotifyUrl("www.baidu.com");
        req.setMerchantCode("10001");
        req.setReturnUrl("https://bms-test.cdfinance.com.cn/api/insurance/micro/back/public/order/fx/payRedirect");

        Commodity commodity = new Commodity();
        commodity.setSubject("众安支付测试");
        commodity.setPrice(amount+"元");
        req.setCommodity(commodity);
        String json=JSON.toJSONString(req);
        String content = PaymentIntentUtil.buildFormV2("http://opengw.daily.zhongan.com/Gateway.do",
                JSON.parseObject(json,Map.class), "test");
        System.out.print(content);
    }

    @Test
    public void endorCheck(){
        String orderId = "";
        EndorConfig config = JMockData.mock(EndorConfig.class);
        TypeReference<List<GroupInsured>> ins = new TypeReference<List<GroupInsured>>() {
        };
        List<GroupInsured> insureds=JMockData.mock(ins);
        for(GroupInsured gi:insureds){
            gi.setOccupationGroup("1");
        }
        if(insureds==null){
            throw new BizException(ExcptEnum.PARAMS_ERROR.getCode(),"被保人列表不能为空");
        }
        /**
         * 验证职业类别
         */
        Integer addJobClass = 0;
        if(addJobClass==0){
            TypeReference<List<String>> ocpListTr = new TypeReference<List<String>>() {
            };
            List<String> ocps = JMockData.mock(ocpListTr);
//            ocps.add("1");
            if(ocps!=null){
                try {
                    Set<String> ocpSet = new HashSet<>(ocps);
                    for (GroupInsured entry : insureds) {
                        if (!ocpSet.contains(entry.getOccupationGroup())) {
                            throw new BizException(ExcptEnum.PARAMS_ERROR.getCode(), "该产品不支持批增新职业类别");
                        }
                    }
                }catch (BizException e){
                    Assert.assertTrue("未知异常",e.getCode().equalsIgnoreCase(ExcptEnum.PARAMS_ERROR.getCode()));
                }catch (Exception ex){
                    Assert.assertTrue("未知异常:"+ex.getMessage(),false);
                }
            }
        }
    }

    @Test
    public void checkOcp4LiabAmountLimit(){
        TypeReference<Map<String,Integer>> tr1 = new TypeReference<Map<String,Integer>>() {
        };
        Map<String,Integer> ocpMap = JMockData.mock(tr1);

        TypeReference<Map<String, CoverageQuery>> tr2 = new TypeReference<Map<String, CoverageQuery>>() {
        };
        Map<String, CoverageQuery> coverageMap= JMockData.mock(tr2);

        QuoteLimitDTO entry = JMockData.mock(QuoteLimitDTO.class);
        applyHandler.checkOcp4LiabAmountLimit(ocpMap,coverageMap,entry);
    }


}
