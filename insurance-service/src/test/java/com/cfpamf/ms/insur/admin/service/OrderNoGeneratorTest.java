package com.cfpamf.ms.insur.admin.service;

import com.cfpamf.ms.insur.admin.dao.safes.SmOrderMapper;
import com.cfpamf.ms.insur.base.util.RedisUtil;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * <AUTHOR> 2021/10/13 10:49
 */
@RunWith(SpringJUnit4ClassRunner.class)
public class OrderNoGeneratorTest {
    @InjectMocks
    OrderNoGenerator generator;

    @Mock
    SmOrderMapper orderMapper;

    @Mock
    RedisUtil<String, Integer> redisUtil;

    @Before
    public void setUp() throws Exception {
    }

    @Test
    public void timeBatchNo() {
        OrderNoGenerator.timeBatchNo();
    }

    @Test
    public void getNextNo() {
        Mockito.when(redisUtil.get(Mockito.any())).thenReturn(123);
        generator.getNextNo();
    }

    @Test
    public void testGetBatchNo() {
    }

    @Test
    public void testGetNextNo() {
    }

    @Test
    public void getDbMaxNo() {
    }
}
