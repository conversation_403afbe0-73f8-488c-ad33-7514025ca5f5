package com.cfpamf.ms.insur.admin.test;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import org.apache.http.HttpResponse;
import org.apache.http.util.EntityUtils;

import java.util.HashMap;
import java.util.Map;


/**
 * 使用APPCODE进行云市场ocr服务接口调用
 */

public class AliyunIdCardTest {

    /*
     * 获取参数的json对象
     */
    public static JSONObject getParam(int type, String dataValue) {
        JSONObject obj = new JSONObject();
        try {
            obj.put("dataType", type);
            obj.put("dataValue", dataValue);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return obj;
    }

    public static void main(String[] args) {

        String host = "http://dm-51.data.aliyun.com";
        String path = "/rest/160601/ocr/ocr_idcard.json";
        String appcode = "68aba32145ca4bc1aa6d60272d62fc85";
        String imgFile = "C:\\Users\\<USER>\\Desktop\\a.jpg";
        Boolean is_old_format = false;//如果文档的输入中含有inputs字段，设置为True， 否则设置为False
        //请根据线上文档修改configure字段
        JSONObject configObj = new JSONObject();
        configObj.put("side", "face");
        String config_str = configObj.toString();
        //            configObj.put("min_size", 5);
        //            String config_str = "";

        String method = "POST";
        Map<String, String> headers = new HashMap<String, String>();
        //最后在header中的格式(中间是英文空格)为Authorization:APPCODE 83359fd73fe94948385f570e3c139105
        headers.put("Authorization", "APPCODE " + appcode);

        Map<String, String> querys = new HashMap<String, String>();

        // 对图像进行base64编码
        String imgBase64 = "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";
//        try {
//            File file = new File(imgFile);
//            byte[] content = new byte[(int) file.length()];
//            FileInputStream finputstream = new FileInputStream(file);
//            finputstream.read(content);
//            finputstream.close();
//            imgBase64 = new String(encodeBase64(content));
//        } catch (IOException e) {
//            e.printStackTrace();
//            return;
//        }
        // 拼装请求body的json字符串
        JSONObject requestObj = new JSONObject();
        try {
            if (is_old_format) {
                JSONObject obj = new JSONObject();
                obj.put("image", getParam(50, imgBase64));
                if (config_str.length() > 0) {
                    obj.put("configure", getParam(50, config_str));
                }
                JSONArray inputArray = new JSONArray();
                inputArray.add(obj);
                requestObj.put("inputs", inputArray);
            } else {
                requestObj.put("image", imgBase64);
                if (config_str.length() > 0) {
                    requestObj.put("configure", config_str);
                }
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
        String bodys = requestObj.toString();

        try {
            /**
             * 重要提示如下:
             * HttpUtils请从
             * https://github.com/aliyun/api-gateway-demo-sign-java/blob/master/src/main/java/com/aliyun/api/gateway/demo/util/HttpUtils.java
             * 下载
             *
             * 相应的依赖请参照
             * https://github.com/aliyun/api-gateway-demo-sign-java/blob/master/pom.xml
             */
            HttpResponse response = HttpUtils.doPost(host, path, method, headers, querys, bodys);
            int stat = response.getStatusLine().getStatusCode();
            if (stat != 200) {
                System.out.println("Http code: " + stat);
                System.out.println("http header error msg: " + response.getFirstHeader("X-Ca-Error-Message"));
                System.out.println("Http body error msg:" + EntityUtils.toString(response.getEntity()));
                return;
            }

            String res = EntityUtils.toString(response.getEntity());
            JSONObject res_obj = JSON.parseObject(res);
            if (is_old_format) {
                JSONArray outputArray = res_obj.getJSONArray("outputs");
                String output = outputArray.getJSONObject(0).getJSONObject("outputValue").getString("dataValue");
                JSONObject out = JSON.parseObject(output);
//                System.out.println(out.toJSONString());
            } else {
//                System.out.println(res_obj.toJSONString());
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}