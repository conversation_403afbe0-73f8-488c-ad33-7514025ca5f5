package com.cfpamf.ms.insur.weixin.pojo.query;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.constraints.NotBlank;
import org.hibernate.validator.constraints.NotEmpty;

/**
 * 微信客户订单query
 *
 * <AUTHOR>
 **/
@Data
public class WxCustOrderQuery {

    /**
     * openid
     */
    private String openId;

    /**
     * authorization
     */
    private String authorization;

    /**
     * 身份证为敏感信息，不建议通过此字段信息查询用户
     * S52迭代改为通过客户Id查询详情信息
     * @zenghuaguang
     */
    @Deprecated
    @ApiModelProperty(hidden = true)
    private String idNumber;

    /**
     * 客户类别
     */
    @ApiModelProperty("客户类别")
    private String customerType;

    /**
     * 客户id
     */
    @ApiModelProperty("客户Id")
    @NotEmpty(message = "客户Id不能为空")
    private String customerId;

    /**
     * type
     */
    @ApiModelProperty("查询类型：0=投保人， 1=被保人")
    private Integer type;

    /**
     * 第几页
     */
    private Integer page;

    /**
     * 页大小
     */
    private Integer size;
}
