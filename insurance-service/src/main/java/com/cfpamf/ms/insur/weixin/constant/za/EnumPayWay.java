package com.cfpamf.ms.insur.weixin.constant.za;

import java.util.Objects;

public enum EnumPayWay {
    alipay("1"),
    wxpay("4"),
    anthb("3"),
    unionpay("2"),
    unionpayB2B("2"),
    lakala("3"),
    alipay_app("3"),
    wxpay_app("3"),
    alipay_lit("3"),
    wxpay_lit("3"),
    wxpay_sign("3"),
    alipay_sign("3"),
    ali_sign("3"),
    wx_sign("3"),
    offline("100");

    String code;

    EnumPayWay(String code) {
        this.code = code;
    }

    public static String getCode(String name) {
        EnumPayWay[] enums = EnumPayWay.values();
        for (EnumPayWay w : enums) {
            if (w.name().equalsIgnoreCase(name)) {
                return w.code;
            }
        }
        return "3";
    }

    public static EnumPayWay convertEnum(String name) {
        EnumPayWay[] enums = EnumPayWay.values();
        for (EnumPayWay w : enums) {
            if (w.name().equalsIgnoreCase(name)) {
                return w;
            }
        }
        return null;
    }

    public static String showPayWay(String payway) {
        if (Objects.equals("offline", payway)) {
            return "线下支付";
        }
        if (Objects.equals("online", payway)) {
            return "线上支付";
        }
        return payway;
    }

    public String getCode() {
        return this.code;
    }
}
