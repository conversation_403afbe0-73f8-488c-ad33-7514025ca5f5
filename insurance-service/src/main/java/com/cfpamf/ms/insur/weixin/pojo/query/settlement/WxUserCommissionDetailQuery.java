package com.cfpamf.ms.insur.weixin.pojo.query.settlement;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
@ApiModel("微信端24版佣金明细查询")
public class WxUserCommissionDetailQuery implements Serializable {
    @ApiModelProperty(value = "查询月份", example = "2022-01")
    @NotNull(message = "查询月份不能为空")
    String commissionMonth;
    @ApiModelProperty(value = "查询月份",hidden = true )
    String userId;
}
