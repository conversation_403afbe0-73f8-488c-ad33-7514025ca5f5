package com.cfpamf.ms.insur.weixin.pojo.query.renewal;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021/11/9 16:19
 */
@Data
@ApiModel
@ToString(callSuper = true)
public class RenewalBasicQuery {
    @ApiModelProperty(value = "推荐人Id", hidden = true)
    private String userId;

    @ApiModelProperty(value = "代理人Id", hidden = true)
    private Integer agentId;
    @ApiModelProperty(value = "微信openId")
    private String openId;
    /**
     * 授权token
     */
    @ApiModelProperty("授权token")
    private String authorization;

    @ApiModelProperty(value = "订单创建开始时间")
    private Date startTime;

    @ApiModelProperty(value = "订单创建结束时间")
    private Date endTime;



}
