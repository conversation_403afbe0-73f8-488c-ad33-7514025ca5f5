package com.cfpamf.ms.insur.weixin.service;

import com.alibaba.fastjson.JSON;
import com.cfpamf.ms.insur.admin.pojo.vo.SystemPushSettingVO;
import com.cfpamf.ms.insur.base.util.CommonUtil;
import com.cfpamf.ms.insur.weixin.dao.dc.WxReportMapper;
import com.cfpamf.ms.insur.weixin.dao.safes.WxPushSettingMapper;
import com.cfpamf.ms.insur.weixin.pojo.query.WxTseQuery;
import com.cfpamf.ms.insur.weixin.pojo.vo.WxActivityTopSalesEmployeeVO;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Objects;

import static com.cfpamf.ms.insur.base.constant.CacheKeyConstants.WX_PUSH_SETTING;

/**
 * 微信活动接口service
 *
 * <AUTHOR>
 **/
@Slf4j
@Component
public class WxHomePushService extends WxAbstractService {

    /**
     * 微信排行版缓存key
     */
    private final static String WX_TOP_SALES_CACHE_KEY = "activityTopSalesEmployee";
    /**
     * 微信排行版缓存正在查询value
     */
    private final static String IS_DB_QUERY_PROCESS = "isDbQueryProcess";
    /**
     * 数据中心保险报表mapper
     */
    @Autowired
    private WxReportMapper wxReportMapper;
    /**
     * 微信推送mapper
     */
    @Autowired
    private WxPushSettingMapper wxPushSettingMapper;

    @Autowired
    @Qualifier("cache-Executor")
    private AsyncTaskExecutor taskExecutor;

    /**
     * 查询微信推广精英排行版
     *
     * @param query
     * @return
     */
    public PageInfo getActivityTopSalesEmployee(WxTseQuery query, boolean isForceRefresh) {
        String cacheKey = new StringBuilder().append(WX_TOP_SALES_CACHE_KEY).append(":").append(query).toString();
        String cacheDataStr = redisUtil.get(cacheKey);
        if (isForceRefresh && cacheDataStr != null){
            redisUtil.remove(cacheKey);
            cacheDataStr = redisUtil.get(cacheKey);
        }
        // 缓存无数据
        if (cacheDataStr == null) {
            // 设置DB正在查询标记
            redisUtil.set(cacheKey, IS_DB_QUERY_PROCESS, CommonUtil.getTodayNextSeconds() + 3600);
            taskExecutor.submit(() -> {
                PageHelper.startPage(query.getPage(), query.getSize());
                PageInfo<WxActivityTopSalesEmployeeVO> dbPageInfo = new PageInfo<>(wxReportMapper.listWxTopSalesEmployee(query));
                redisUtil.set(cacheKey, JSON.toJSONString(dbPageInfo), CommonUtil.getTodayNextSeconds() + 3600);
            });
            return new PageInfo<>();
        }
        // 缓存数据DB正在查询
        if (Objects.equals(cacheDataStr, IS_DB_QUERY_PROCESS)) {
            return new PageInfo<>();
        }
        return JSON.parseObject(cacheDataStr, PageInfo.class);
    }

    /**
     * 查询系统推送设置
     *
     * @return
     */
    @Cacheable(cacheNames = WX_PUSH_SETTING, key = "#pushType + # userType + #date.toString()")
    public List<SystemPushSettingVO> getSystemPushSetting(String pushType, String userType, Date date) {
        return wxPushSettingMapper.getSystemPushSetting(pushType, userType, date);
    }
}
