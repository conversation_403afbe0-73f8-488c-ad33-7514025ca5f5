package com.cfpamf.ms.insur.weixin.pojo.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 微信绑定DTO
 *
 * <AUTHOR>
 */
@Data
public class WxBindDTO {

    /**
     * 用户种类
     */
    @ApiModelProperty(value = "用户种类")
    private String loginType;

    /**
     * 员工号
     */
    @ApiModelProperty(value = "员工号")
    private String loginID;

    /**
     * 密码
     */
    @ApiModelProperty(value = "密码")
    private String loginPwd;

    /**
     * 微信openId
     */
    @ApiModelProperty(value = "微信openId")
    private String wxOpenId;

    /**
     * 微信token
     */
    @ApiModelProperty(value = "微信token")
    private String authorization;

    /**
     * 微信名称
     */
    @ApiModelProperty(hidden = true)
    private String wxNickName;

    /**
     * 微信头像Url
     */
    @ApiModelProperty(hidden = true)
    private String wxImgUrl;

    @ApiModelProperty(value = "客户端Ip地址",hidden = true)
    private String ip;
}