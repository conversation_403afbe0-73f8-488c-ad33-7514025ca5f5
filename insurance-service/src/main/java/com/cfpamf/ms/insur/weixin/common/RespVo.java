package com.cfpamf.ms.insur.weixin.common;

import lombok.Data;

/**
 * @zenghuaguang
 * @param <T>
 */
@Data
public class RespVo<T>{
    private String code="0";
    private String msg;
    private T Data;

    public static RespVo ok(Object data){
        RespVo vo = new RespVo();
        vo.setCode("0");
        vo.setMsg("请求成功");
        vo.setData(data);
        return vo;
    }

    public static RespVo error(String code,String msg){
        RespVo vo = new RespVo();
        vo.setCode(code);
        vo.setMsg(msg);
        return vo;
    }

}
