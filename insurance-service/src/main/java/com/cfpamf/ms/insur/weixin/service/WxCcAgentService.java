package com.cfpamf.ms.insur.weixin.service;

import com.alibaba.fastjson.JSON;
import com.cfpamf.ms.insur.admin.constant.SmConstants;
import com.cfpamf.ms.insur.admin.dao.safes.AuthUserMapper;
import com.cfpamf.ms.insur.admin.dao.safes.SmAgentMapper;
import com.cfpamf.ms.insur.admin.event.AgentRegisterEvent;
import com.cfpamf.ms.insur.admin.pojo.dto.SmAgentRealVerifyDTO;
import com.cfpamf.ms.insur.admin.pojo.dto.SmAgentRegisterDTO;
import com.cfpamf.ms.insur.admin.pojo.query.AgentMonthAllowanceQuery;
import com.cfpamf.ms.insur.admin.pojo.query.SmAgentTeamCmsQuery;
import com.cfpamf.ms.insur.admin.pojo.vo.*;
import com.cfpamf.ms.insur.admin.service.SystemFileService;
import com.cfpamf.ms.insur.base.bean.SmyPageInfo;
import com.cfpamf.ms.insur.base.event.EventBusEngine;
import com.cfpamf.ms.insur.base.exception.BizException;
import com.cfpamf.ms.insur.base.exception.ExcptEnum;
import com.cfpamf.ms.insur.base.infeface.CustomerCenterService;
import com.cfpamf.ms.insur.base.util.CommonUtil;
import com.cfpamf.ms.insur.base.util.SpringFactoryUtil;
import com.cfpamf.ms.insur.weixin.pojo.dto.*;
import com.cfpamf.ms.insur.weixin.pojo.query.WxAuthPageQuery;
import com.cfpamf.ms.insur.weixin.pojo.query.WxTeamCmsQuery;
import com.cfpamf.ms.insur.weixin.pojo.query.WxTeamQuery;
import com.cfpamf.ms.insur.weixin.pojo.vo.*;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

/**
 * 微信团队代理service
 *
 * <AUTHOR>
 **/
@Slf4j
@Component
public class WxCcAgentService extends WxAbstractService {

    /**
     * 客户中心service
     */
    @Autowired
    private CustomerCenterService ccService;

    /**
     * 微信用户中心service
     */
    @Autowired
    private WxCcUserService wxcService;

    /**
     * EventBusEngine
     */
    @Lazy
    @Autowired
    private EventBusEngine eventBusEngine;

    /**
     * 用户mapper
     */
    @Autowired
    private AuthUserMapper auMapper;

    /**
     * 代理人mapper
     */
    @Autowired
    private SmAgentMapper agentMapper;

    /**
     * 发送代理人注册验证码
     *
     * @param mobile
     * @return
     */
    public void sendWxRegisterCode(String openId, String authorization, String mobile) {
        checkAuthority(openId, authorization);
        sendWxRegisterCode(mobile);
    }

    /**
     * 发送注册验证码
     * @param mobile
     */
    public void sendWxRegisterCode(String mobile) {
        boolean isRegister = ccService.checkRegisterMobile(mobile);
        if (isRegister) {
            throw new BizException(ExcptEnum.AGENT_MOBILE_REGISTER_801034);
        }
        if (redisUtil.get(mobile) != null) {
            throw new BizException(ExcptEnum.VERIFY_CODE_TOO_MANY_801038);
        }
        redisUtil.set(mobile, mobile, 50L);
        ccService.sendRegisterCode(mobile);
    }

    /**
     * 代理人注册上传照片
     *
     * @param file
     * @return
     */
    public String uploadWxRegisterPicture(String openId, String authorization, MultipartFile file) {
        checkAuthority(openId, authorization);
        return SpringFactoryUtil.getBean(SystemFileService.class).uploadFile(file);
    }

    /**
     * 代理人注册
     *
     * @param dto
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Integer wxRegister(WxAgentRegisterDTO dto) {
        WxSessionVO session = checkAuthority(dto.getOpenId(), dto.getAuthorization());
        // 账号已经注册
        AuthUserVO authUser = auMapper.getAuthUserByUserMobile(dto.getAgentMobile());
        if (authUser != null) {
            throw new BizException(ExcptEnum.AGENT_MOBILE_REGISTER_801034);
        }
        // 代理人邀请码错误
        SmUserAgentVO parentUserAgent = auMapper.getUserAgentByAgentMobile(dto.getParentAgentMobile());
        if (parentUserAgent == null) {
            throw new BizException(ExcptEnum.AGENT_INVITE_CODE_ERROR_801036);
        }
        SmAgentVO parentAgent = agentMapper.getAgentByAgentMobile(dto.getParentAgentMobile());
        if (parentAgent == null) {
            throw new BizException(ExcptEnum.AGENT_INVITE_CODE_ERROR_801036);
        }
        if (parentAgent.getAgentType() == null || parentAgent.getAgentType() == 0) {
            throw new BizException(ExcptEnum.AGENT_LEVEL_ERROR_801136);
        }
        // 外部人员开通代理账户  不开通团队
        if (session.isBindWeixin()) {
            checkWxRegister(dto);
            // 客户中心注册
            String userNo = ccService.register(dto);
            SmAgentRegisterDTO saDTO = buildWeixinAgentDTO(dto, userNo);
            agentMapper.insertAgent(saDTO);
            agentMapper.updateAgentPathInfo(saDTO.getAgentId(), saDTO.getAgentPath() + "/" + saDTO.getAgentId());
            auMapper.updateWeixinAgentRegisterInfo(saDTO);
            // 重新加载session
            wxcService.reloadWxSession(dto.getOpenId(), dto.getAuthorization(), null, session.getChannel());
            return saDTO.getAgentId();
        } else if (session.isBindEmployee()) {
            throw new BizException(ExcptEnum.AGENT_ACCOUNT_ERROR_EMPLOYEE_801039);
        } else {
            throw new BizException(ExcptEnum.AGENT_ACCOUNT_ERROR_AGENT_801039);
        }
    }


    /**
     * 微信实名认证
     *
     * @param dto
     */
    @Transactional(rollbackFor = Exception.class)
    public void realVerify(SmAgentRealVerifyDTO dto) {
        WxSessionVO session = checkAuthority(dto.getOpenId(), dto.getAuthorization());
        if (session.isBindEmployee()) {
            throw new BizException(ExcptEnum.INNER_USER_NOT_ALLOW_FAIL_801137);
        }
        AuthUserVO authUserVO = auMapper.getAuthUserByUserIdCard(dto.getAgentIdCard());
        if (authUserVO != null) {
            throw new BizException(ExcptEnum.AGENT_IDCARD_ERROR_801135);
        }
        SmAgentVO agentVO = agentMapper.getAgentByAgentId(dto.getAgentId());
        if (agentVO == null) {
            throw new BizException(ExcptEnum.AGENT_MOBILE_NOT_REGISTER_801035);
        }
        String custNo = ccService.realCheck(dto.getAgentName(), dto.getAgentIdCard(), agentVO.getCcUserNo());
        dto.setCcCustNo(custNo);
        agentMapper.updateAgentRealVerifyInfo(dto);
        // 更新用户代理人信息
        auMapper.updateWeixinAgentRealVerifyInfo(dto);
        // 重新加载session
        wxcService.reloadWxSession(dto.getOpenId(), dto.getAuthorization(), null, session.getChannel());
        // 发布代理人注册事件
        eventBusEngine.post(new AgentRegisterEvent(dto.getAgentId()));
    }

    /**
     * 发送代理人忘记密码验证码
     *
     * @param mobile
     * @return
     */
    public void sendWxForgetCode(String openId, String authorization, String mobile) {
        checkAuthority(openId, authorization);
        boolean isRegister = ccService.checkRegisterMobile(mobile);
        if (!isRegister) {
            throw new BizException(ExcptEnum.AGENT_MOBILE_NOT_REGISTER_801035);
        }
        if (redisUtil.get(mobile) != null) {
            throw new BizException(ExcptEnum.VERIFY_CODE_TOO_MANY_801038);
        }
        redisUtil.set(mobile, mobile, 50L);
        ccService.sendForgetCode(mobile);
    }

    /**
     * 代理人修改密码
     *
     * @param dto
     * @return
     */
    public void wxModifyPassword(WxAgentModifyPwdDTO dto) {
        checkAuthority(dto.getOpenId(), dto.getAuthorization());
        ccService.modifyPassword(dto.getAgentMobile(), dto.getVerifyCode(), dto.getPassword());
    }

    /**
     * 微信团队管理首页
     *
     * @return
     */
    public WxTeamHomeVO getWxTeamHome(String openId, String authorization) {
        WxSessionVO session = checkAuthorityEpAg(openId, authorization);
        return agentMapper.getWxAgentTeamHome(session.getAgentId(), session.getTeamId());
    }

    /**
     * 微信我的团队
     *
     * @return
     */
    public PageInfo<WxAgentVO> getWxTeamAgents(WxTeamQuery query) {
        WxSessionVO session = checkAuthorityEpAg(query.getOpenId(), query.getAuthorization());
        PageHelper.startPage(query.getPage(), query.getSize());
        query.setTeamId(session.getTeamId());
        return new PageInfo<>(agentMapper.listWxTeamAgents(query));
    }

    /**
     * 微信我的团队上级
     *
     * @return
     */
    public List<WxAgentVO> getTeamParentAgents(WxTeamQuery query) {
        checkAuthorityEpAg(query.getOpenId(), query.getAuthorization());
        return agentMapper.listTeamParentAgents(query.getTeamId());
    }

    /**
     * 微信我的团队代理人详情
     *
     * @return
     */
    public SmAgentVO getWxAgentByAgentId(int agentId, String openId, String authorization) {
        checkAuthorityEpAg(openId, authorization);
        return agentMapper.getAgentByAgentId(agentId);
    }

    /**
     * 修改微信我的团队名称
     *
     * @return
     */
    public void updateAgentTeamName(WxAgentModifyTeamDTO dto) {
        WxSessionVO session = checkAuthorityEpAg(dto.getOpenId(), dto.getAuthorization());
        if (agentMapper.countTeamByTeamName(dto.getTeamName(), session.getTeamId()) > 0) {
            throw new BizException(ExcptEnum.AGENT_TEAM_NAME_ERROR_80119);
        }
        agentMapper.updateAgentTeamName(session.getTeamId(), dto.getTeamName());
        // 重新加载session
        wxcService.reloadWxSession(dto.getOpenId(), dto.getAuthorization(), null, session.getChannel());
    }

    /**
     * 修改微信代理人种类
     *
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateAgentType(WxAgentModifyTypeDTO dto) {
        if (dto.getAgentType() != SmAgentVO.AGENT_TYPE_NORMAL
                && dto.getAgentType() != SmAgentVO.AGENT_TYPE_RELATIVE) {
            throw new IllegalArgumentException("不允许变更成客户经理代理人");
        }
        WxSessionVO session = checkAuthorityEpAg(dto.getOpenId(), dto.getAuthorization());
        SmAgentVO oldAgentVO = agentMapper.getAgentByAgentId(dto.getAgentId());
        // 只能公司内部员工操作更新同一团队的人
        if (session.isBindWeixin() || !Objects.equals(oldAgentVO.getTeamId(), session.getTeamId())) {
            throw new BizException(ExcptEnum.API_AUTH_ERROR);
        }
        // 更新代理人类别
        agentMapper.updateAgentType(dto.getAgentId(), dto.getAgentType());

        int oldAgentType = oldAgentVO.getAgentType();
        int newAgentType = dto.getAgentType();
        // 亲属代理人变更成普通代理人逻辑
        if (oldAgentType == SmAgentVO.AGENT_TYPE_RELATIVE && newAgentType == SmAgentVO.AGENT_TYPE_NORMAL) {
            agentMapper.updateAgentParentAgentId(null, dto.getAgentId(), session.getAgentId());
        }
        // 普通代理人变更成亲属代理人逻辑
        else if (oldAgentType == SmAgentVO.AGENT_TYPE_NORMAL && newAgentType == SmAgentVO.AGENT_TYPE_RELATIVE) {
            agentMapper.updateAgentParentAgentId(dto.getAgentId(), null, session.getAgentId());
        }
    }

    /**
     * 修改微信代理人父级代理人
     *
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateAgentParentAgentId(WxAgentModifyParentAgentIdDTO dto) {
        WxSessionVO session = checkAuthorityEpAg(dto.getOpenId(), dto.getAuthorization());
        SmAgentVO oldAgentVO = agentMapper.getAgentByAgentId(dto.getAgentId());
        // 只能公司内部员工操作更新同一团队的人
        if (session.isBindWeixin() || !Objects.equals(oldAgentVO.getTeamId(), session.getTeamId())) {
            throw new BizException(ExcptEnum.API_AUTH_ERROR);
        }
        agentMapper.updateAgentParentAgentId(dto.getAgentId(), null, dto.getParentAgentId());
    }

    /**
     * 查询微信团队业绩
     *
     * @return
     */
    public SmyPageInfo<SmAgentTeamOrderDetailVO, SmAgentTeamOrderSmyVO> getWxTeamOrder(WxTeamCmsQuery query) {
        WxSessionVO session = checkAuthorityEpAg(query.getOpenId(), query.getAuthorization());
        query.setStartDate(CommonUtil.getStartTimeOfDay(query.getStartDate()));
        query.setEndDate(CommonUtil.getEndTimeOfDay(query.getEndDate()));
        SmAgentVO agentVO = agentMapper.getAgentByAgentId(session.getAgentId());
        SmAgentTeamCmsQuery cmsQuery = new SmAgentTeamCmsQuery();
        cmsQuery.setStartDate(query.getStartDate());
        cmsQuery.setEndDate(query.getEndDate());
        cmsQuery.setTeamId(agentVO.getTeamId());
        cmsQuery.setAgentName(query.getAgentName());
        PageHelper.startPage(query.getPage(), query.getSize());
        List<SmAgentTeamOrderDetailVO> atods = agentMapper.listSingleAgentTeamOrder(cmsQuery);
        cmsQuery.setAgentName(null);
        return new SmyPageInfo<>(atods, agentMapper.getSingleAgentTeamOrderSmy(cmsQuery));
    }

    /**
     * 微信管理津贴
     *
     * @return
     */
    public PageInfo<AgentMonthAllowanceVO> getAgentMonthAllowances(WxAuthPageQuery query) {
        WxSessionVO session = checkAuthorityEpAg(query.getOpenId(), query.getAuthorization());
        AgentMonthAllowanceQuery allowanceQuery = new AgentMonthAllowanceQuery();
        allowanceQuery.setAgentId(session.getAgentId());
        PageHelper.startPage(query.getPage(), query.getSize());
        return new PageInfo<>(agentMapper.listAgentMonthAllowance(allowanceQuery));
    }

    /**
     * 查询微信团队佣金
     *
     * @return
     */
    public PageInfo<WxAgentTeamCmsSmyVO> getWxTeamCms(WxTeamCmsQuery query) {
        WxSessionVO session = checkAuthorityEpAg(query.getOpenId(), query.getAuthorization());
        query.setStartDate(CommonUtil.getStartTimeOfDay(query.getStartDate()));
        query.setEndDate(CommonUtil.getEndTimeOfDay(query.getEndDate()));
        query.setTeamId(session.getTeamId());
        PageHelper.startPage(query.getPage(), query.getSize());
        return new PageInfo<>(agentMapper.listWxTeamCmsSmy(query));
    }

    /**
     * 查询微信团队佣金明细
     *
     * @return
     */
    public SmyPageInfo<WxAgentTeamCmsDetailVO, WxAgentTeamCmsSmyVO> getWxTeamAgentCms(WxTeamCmsQuery query) {
        WxSessionVO session = checkAuthorityEpAg(query.getOpenId(), query.getAuthorization());
        query.setTeamId(session.getTeamId());
        query.setParentAgentId(session.getAgentId());
        query.setStartDate(CommonUtil.getStartTimeOfDay(query.getStartDate()));
        query.setEndDate(CommonUtil.getEndTimeOfDay(query.getEndDate()));
        WxTeamCmsQuery query2 = JSON.parseObject(JSON.toJSONString(query), WxTeamCmsQuery.class);
        query2.setAgentName(null);
        List<WxAgentTeamCmsSmyVO> cmsSmyList = agentMapper.listWxTeamCmsSmy(query2);
        WxAgentTeamCmsSmyVO smy = null;
        if (!cmsSmyList.isEmpty()) {
            smy = cmsSmyList.get(0);
        } else {
            SmAgentVO agentVO = agentMapper.getAgentByAgentId(query.getAgentId());
            smy = new WxAgentTeamCmsSmyVO();
            if (agentVO != null) {
                smy.setAgentName(agentVO.getAgentName());
            }
            smy.setFstLevelCms(BigDecimal.ZERO);
            smy.setSedLevelCms(BigDecimal.ZERO);
            smy.setTotalCms(BigDecimal.ZERO);
        }
        PageHelper.startPage(query.getPage(), query.getSize());

        return new SmyPageInfo<>(agentMapper.listWxAgentCmsSmy(query), smy);
    }

    /**
     * buildWeixinAgentDTO
     *
     * @param userNo
     * @return
     */
    private SmAgentRegisterDTO buildWeixinAgentDTO(WxAgentRegisterDTO wxDto, String userNo) {
        SmUserAgentVO parentUserAgent = auMapper.getUserAgentByAgentMobile(wxDto.getParentAgentMobile());
        SmAgentVO parentAgent = agentMapper.getAgentByAgentMobile(wxDto.getParentAgentMobile());
        SmAgentRegisterDTO rstDto = new SmAgentRegisterDTO();
        rstDto.setCcUserNo(userNo);
        rstDto.setParentAgentId(parentAgent.getAgentId());
        rstDto.setAgentTopUserId(parentAgent.getAgentTopUserId());
        rstDto.setAgentPath(parentAgent.getAgentPath());
        rstDto.setAgentMobile(wxDto.getAgentMobile());
        rstDto.setAgentJobNumber("");
        rstDto.setAgentStatus(SmConstants.AGENT_STATUS_NOT_REAL);
        rstDto.setHrOrgId(parentUserAgent.getHrOrgId());
        rstDto.setRegionCode(parentUserAgent.getRegionCode());
        rstDto.setRegionName(parentUserAgent.getRegionName());
        rstDto.setOrgCode(parentUserAgent.getOrgCode());
        rstDto.setOrganizationName(parentUserAgent.getOrganizationName());
        rstDto.setOrganizationFullName(parentUserAgent.getOrganizationFullName());
        rstDto.setPostCode(SmConstants.USER_AGENT_POST_CODE);
        rstDto.setPostName(SmConstants.USER_AGENT_POST_NAME);
        rstDto.setTeamId(parentAgent.getTeamId());
        rstDto.setTeamPath(parentAgent.getTeamPath());
        rstDto.setUserType(SmConstants.USER_TYPE_AGENT);
        // TODO
        rstDto.setLevelId(1);
        rstDto.setWxOpenId(wxDto.getOpenId());
        return rstDto;
    }

    /**
     * buildWeixinAgentDTO
     *
     * @param userNo
     * @return
     */
    private SmAgentRegisterDTO buildEmployeeAgentDTO(WxAgentRegisterDTO wxDto, String userNo) {
        SmUserAgentVO userAgent = auMapper.getUserAgentByAgentMobile(wxDto.getAgentMobile());
        if (userAgent == null) {
            throw new BizException(ExcptEnum.AGENT_INVITE_CODE_ERROR_801036);
        }
        SmAgentRegisterDTO rstDto = new SmAgentRegisterDTO();
        rstDto.setCcUserNo(userNo);
        rstDto.setAgentTopUserId(userAgent.getUserId());
        rstDto.setAgentBindUserId(userAgent.getUserId());
        rstDto.setAgentMobile(wxDto.getAgentMobile());
        rstDto.setAgentJobNumber("");
        rstDto.setAgentStatus(SmConstants.AGENT_STATUS_NOT_REAL);
        rstDto.setLevelId(1);
        rstDto.setWxOpenId(wxDto.getOpenId());
        return rstDto;
    }

    /**
     * 注册检验
     *
     * @param dto
     */
    private void checkWxRegister(WxAgentRegisterDTO dto) {
        List<SmUserAgentVO> agents = auMapper.getAgentByMobileOrOpenId(dto.getAgentMobile(), dto.getOpenId());
        if (agents.stream().anyMatch(g -> !Objects.equals(g.getUserType(), SmConstants.USER_TYPE_WEIXIN))) {
            throw new BizException(ExcptEnum.AGENT_MOBILE_REGISTER_801034);
        }
    }
}
