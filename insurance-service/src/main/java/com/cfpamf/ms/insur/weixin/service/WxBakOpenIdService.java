package com.cfpamf.ms.insur.weixin.service;

import com.cfpamf.ms.insur.admin.dao.safes.BakWxOpenidMapperMapper;
import com.cfpamf.ms.insur.admin.pojo.po.BakWxOpenidMapper;
import com.cfpamf.ms.insur.base.config.WechatConfig;
import com.cfpamf.ms.insur.weixin.pojo.dto.WxChangeOpenIdDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.ListUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR> 2020/8/7 10:06
 */
@Slf4j
@Service
public class WxBakOpenIdService {

    @Autowired
    WechatConfig wechatConfig;
    @Autowired
    WxMpServiceProxy wxMpServiceProxy;
    /**
     * mapper
     */
    @Autowired
    BakWxOpenidMapperMapper bakWxOpenidMapperMapper;


    @Transactional(rollbackFor = Exception.class)
    public void resetData() {
        int i = bakWxOpenidMapperMapper.deleteAll();
        log.info("清楚历史数据:{}", i);
        bakWxOpenidMapperMapper.insertByAuthUser(wechatConfig.getOldAppId(), wechatConfig.getNewAppId());
        log.info("从authUser表初始化:{}", i);
        List<BakWxOpenidMapper> bakWxOpenidMappers = bakWxOpenidMapperMapper.selectAll();

        List<List<BakWxOpenidMapper>> partition = ListUtils.partition(bakWxOpenidMappers, 100);
        partition.forEach(mappers -> {
            try {
                List<String> oldOpenIds = mappers.stream().map(BakWxOpenidMapper::getOldOpenId).collect(Collectors.toList());
                WxChangeOpenIdDTO changeOpenIdDTO = wxMpServiceProxy.changeOpenid(oldOpenIds);

                if (!changeOpenIdDTO.isSuccess()) {
                    mappers.forEach(mapper -> {
                        mapper.setRemark("微信返回报错:" + changeOpenIdDTO.getErrcode());
                        mapper.setState(11);
                    });
                } else {
                    List<WxChangeOpenIdDTO.OpenidMapper> resultList = changeOpenIdDTO.getResultList();

                    mappers.forEach(mapper -> {
                        String oldOpenId = mapper.getOldOpenId();
                        Optional<WxChangeOpenIdDTO.OpenidMapper> first = resultList.stream().filter(res -> Objects.equals(res.getOriOpenid(), oldOpenId))
                                .findFirst();
                        if (first.isPresent()) {
                            WxChangeOpenIdDTO.OpenidMapper openidMapper = first.get();
                            mapper.setNewOpenId(openidMapper.getNewOpenid());
                            mapper.setRemark(openidMapper.getErrMsg());
                            mapper.setState(20);
                        } else {
                            mapper.setState(14);
                            mapper.setRemark("没有获取到微信结果");
                        }

                    });
                }
            } catch (Exception e) {

                mappers.forEach(mapper -> {
                    mapper.setRemark("调用微信接口失败:" + e.getMessage());
                    mapper.setState(12);
                });
                log.info("获取微信openId失败");
            }
        });

        bakWxOpenidMapperMapper.replaceList(bakWxOpenidMappers);

    }

}
