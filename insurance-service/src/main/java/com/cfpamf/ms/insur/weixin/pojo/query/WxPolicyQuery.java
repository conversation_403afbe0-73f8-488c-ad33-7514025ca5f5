package com.cfpamf.ms.insur.weixin.pojo.query;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 保单自助查询接口query
 *
 * <AUTHOR>
 **/
@Data
@ApiModel
public class WxPolicyQuery {

    /**
     * 保单号
     */
    @ApiModelProperty("保单号")
    private String policyNo;

    /**
     * 被保人证件号
     */
    @ApiModelProperty("被保人证件号")
    private String idNumber;

    /**
     * 验证码
     */
    @ApiModelProperty("验证码")
    private String verifyCode;

    @ApiModelProperty("会话标记")
    private String sessionId;
}
