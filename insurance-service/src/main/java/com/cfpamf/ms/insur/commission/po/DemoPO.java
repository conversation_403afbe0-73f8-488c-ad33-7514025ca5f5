package com.cfpamf.ms.insur.commission.po;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * Demo实体对象
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Table(name = "t_demo")
public class DemoPO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer id;

    private String apiUrl;

    private Integer isEnabled;

    private String type;

    private String env;

    private String routeId;

    private String creater;

    private Date createTime;

    private String updater;

    private Date updateTime;

}
