package com.cfpamf.ms.insur.weixin.pojo.dto;

import com.cfpamf.ms.insur.weixin.pojo.dto.product.TimeFactorItem;
import com.cfpamf.ms.insur.weixin.pojo.query.policy.PlanQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.util.List;

/**
 * 微信产品报价DTO
 *
 * <AUTHOR>
 **/
@Data
@ApiModel
public class WxGlProductQuoteDTO {

    @ApiModelProperty("产品Id")
    private Integer productId;

    @Deprecated
    @ApiModelProperty("保障项目金额Id列表")
    private List<Integer> spcaIds;

    /**
     * 迭代版本：S51
     */
    @ApiModelProperty("产品计划")
    private PlanQuery plan;

    @ApiModelProperty("保障项目Ids")
    private List<OccupationPerson> ocpnPersons;

    @ApiModelProperty("短期浮动因子")
    private TimeFactorItem timeFactor;

    @ApiModelProperty("微信openId")
    private String openId;

    @ApiModelProperty("授权token")
    private String authorization;

    @ApiModelProperty("待续保订单号")
    private String oldOrderId;

    @Data
    @ApiModel("报价职业种类和人数")
    public static class OccupationPerson {

        /**
         * 职业编码
         */
        @ApiModelProperty("职业编码")
        private String occupationCode;

        /**
         * 职业分类
         */
        @ApiModelProperty("职业分类")
        private String occupationGroup;

        /**
         * 职业人数
         */
        @ApiModelProperty("职业人数")
        private Integer perQty;
    }
}
