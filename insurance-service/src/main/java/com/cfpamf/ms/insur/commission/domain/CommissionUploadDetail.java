package com.cfpamf.ms.insur.commission.domain;

import com.cfpamf.ms.insur.admin.pojo.po.BasePO;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.time.YearMonth;
import java.util.Map;

/**
 * <AUTHOR> 2022/12/16 15:38
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class CommissionUploadDetail extends BasePO {

    Long uploadId;

    YearMonth commissionMonth;

    String regionName;

    String orgName;

    String jobNumber;

    String userName;

    String commissionDataType;

    Map<String, Object> commissionItem;

}
