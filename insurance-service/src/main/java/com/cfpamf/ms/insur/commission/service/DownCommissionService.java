package com.cfpamf.ms.insur.commission.service;

import com.alibaba.excel.EasyExcel;

import com.alibaba.fastjson.JSON;
import com.cfpamf.ms.insur.base.exception.BizException;
import com.cfpamf.ms.insur.commission.common.constant.SystemConstants;
import com.cfpamf.ms.insur.commission.domain.CommissionUploadDetail;
import com.cfpamf.ms.insur.commission.domain.CommissionUploadFile;
import com.cfpamf.ms.insur.commission.domain.CommissionUploadMonth;
import com.cfpamf.ms.insur.commission.dto.query.CommissionMonthQuery;
import com.cfpamf.ms.insur.commission.dto.query.CommissionUploadQuery;
import com.cfpamf.ms.insur.commission.mapper.CommissionUploadDetailMapper;
import com.cfpamf.ms.insur.commission.mapper.CommissionUploadFileMapper;
import com.cfpamf.ms.insur.commission.mapper.CommissionUploadMonthMapper;
import com.cfpamf.ms.insur.commission.po.CommissionUploadDetailPO;
import com.cfpamf.ms.insur.commission.po.CommissionUploadFilePO;
import com.cfpamf.ms.insur.commission.po.CommissionUploadMonthPO;
import com.cfpamf.ms.insur.commission.utils.DownloadUtil;
import com.cfpamf.ms.insur.commission.utils.excel.CommissionUploadDetailDataListener;
import com.google.common.collect.Lists;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import tk.mybatis.mapper.entity.Example;

import javax.validation.Valid;
import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;

import static com.cfpamf.ms.insur.commission.converter.CommissionUploadConverter.INS;

/**
 * <AUTHOR> 2022/12/16 11:56
 */
@Service
@Slf4j
@AllArgsConstructor
@Validated
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class DownCommissionService {

    static long MAX_QUERY_MONTH = 120;

    CommissionUploadDetailMapper detailMapper;

    CommissionUploadFileMapper fileMapper;

    CommissionUploadMonthMapper monthMapper;

    /**
     * 上传文件
     *
     * @param upload
     * @throws IOException
     */
    @Transactional(rollbackFor = Exception.class)
    public void upload(@Valid CommissionUploadFile upload) throws IOException {

        CommissionUploadFilePO file = INS.domainFile2Po(upload);
        file.setUploadTime(LocalDateTime.now());
        CommissionUploadMonthPO month = new CommissionUploadMonthPO();
        month.setUpdaterName(upload.getCreatorName());
        month.setCommissionMonth(upload.getCommissionMonth());
        month.setUpdaterJobNumber(upload.getCreatorJobNumber());
        month.setCount(1);
        month.setState(1);
        month.setLastUploadTime(LocalDateTime.now());

        fileMapper.insert(file);
        monthMapper.insertListOrCount(month);
        // excel 读取
        read(DownloadUtil.downloadByUrl(upload.getFileUrl()), file);

    }


    /**
     * 查询上传明细
     *
     * @return
     */
    public List<CommissionUploadFile> selectUploads(@Valid CommissionUploadQuery query) {
        Example example = new Example(CommissionUploadFilePO.class);
        example.createCriteria()
                .andEqualTo("commissionMonth", query.getCommissionMonth())
                .andEqualTo("enabledFlag", 0);
        return INS.poFile2domain(fileMapper.selectByExample(example));

    }

    /**
     * 查询月份数据
     *
     * @param monthQuery
     * @return
     */
    public List<CommissionUploadMonth> selectMonths(@Valid CommissionMonthQuery monthQuery) {

        long between = ChronoUnit.MONTHS.between(monthQuery.getStart(), monthQuery.getEnd()) + 1;
        if (between > MAX_QUERY_MONTH) {
            throw new BizException(SystemConstants.BAD_ERROR_CODE, "单次最多查询十年的数据");
        }
        Example example = new Example(CommissionUploadMonthPO.class);
        example.orderBy("commissionMonth").orderBy("id");
        example.createCriteria().andLessThan("commissionMonth", monthQuery.getEnd())
                .andGreaterThan("commissionMonth", monthQuery.getStart());
        List<CommissionUploadMonth> months = INS.poMonth2Domain(monthMapper.selectByExample(example));
//        monthMapper.selectList(new QueryWrapper<CommissionUploadMonthPO>()
//                .lambda().le(CommissionUploadMonthPO::getCommissionMonth, monthQuery.getEnd())
//                .ge(CommissionUploadMonthPO::getCommissionMonth, monthQuery.getStart()).orderByDesc(CommissionUploadMonthPO::getCommissionMonth)
//                .orderByDesc(CommissionUploadMonthPO::getId))
        if (months.size() == between) {
            return months;
        }
        ArrayList<CommissionUploadMonth> list = Lists.newArrayListWithCapacity((int) between);
        YearMonth cur = monthQuery.getEnd();
        while (monthQuery.getStart().isBefore(cur)
                || monthQuery.getStart().compareTo(cur) == 0) {
            final YearMonth tmp = cur;
            Optional<CommissionUploadMonth> first = months.stream().filter(y -> y.getCommissionMonth().compareTo(tmp) == 0)
                    .findFirst();
            if (first.isPresent()) {
                list.add(first.get());
            } else {
                CommissionUploadMonth commissionUploadMonth = new CommissionUploadMonth();
                commissionUploadMonth.setCommissionMonth(tmp);
                commissionUploadMonth.setState(0);
                list.add(commissionUploadMonth);
            }
            cur = cur.minusMonths(1L);
        }
        return list;
    }

    private void read(InputStream is, CommissionUploadFilePO file) {
        log.info("佣金明细文件信息:{}", JSON.toJSONString(file));
        EasyExcel.read(is,new CommissionUploadDetailDataListener(detailMapper, file))
                .sheet().doRead();
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteUpload(Integer fileId, String jobNumber) {
        log.info("删除上传数据:{}  {}", fileId, jobNumber);

        CommissionUploadFilePO file = fileMapper.selectByPrimaryKey(fileId);
        fileMapper.updateFlagById(fileId, 1);
        detailMapper.updateFlagByUploadId(fileId, 1);
        Example example = new Example(CommissionUploadFilePO.class);
        example.createCriteria().andEqualTo("commissionMonth", file.getCommissionMonth())
                .andEqualTo("enabledFlag",0);
        Integer count = fileMapper.selectCountByExample(example);
        if (Objects.equals(count, 0)) {
            Example updateExample = new Example(CommissionUploadMonthPO.class);
            updateExample.createCriteria().andEqualTo("commissionMonth", file.getCommissionMonth());
            CommissionUploadMonthPO uploadMonthPO = new CommissionUploadMonthPO();
            uploadMonthPO.setState(0);
            monthMapper.updateByExampleSelective(uploadMonthPO,updateExample);

        }


    }

    /**
     * 查询某个人的明细
     *
     * @param query
     * @param jobNumber
     * @return
     */
    public CommissionUploadDetail selectDetail(@Valid CommissionUploadQuery query,
                                               String jobNumber, String userName) {
        CommissionUploadDetailPO op = new CommissionUploadDetailPO();
        op.setCommissionMonth(query.getCommissionMonth().format(DateTimeFormatter.ofPattern("yyyy-MM")));
        op.setJobNumber(jobNumber);
        op.setUserName(userName);
        op.setEnabledFlag(0);
        op.setCommissionDataType(query.getCommissionDataType());
        return INS.po2Domain(detailMapper.selectOne(op));
    }

}
