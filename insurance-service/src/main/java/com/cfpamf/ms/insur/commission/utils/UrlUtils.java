package com.cfpamf.ms.insur.commission.utils;

import java.util.regex.Pattern;

/**
 * <AUTHOR>
 */
public class UrlUtils {
    private static final Pattern ABSOLUTE_URL = Pattern.compile("\\A[a-z0-9.+-]+://.*", 2);
    public static boolean isAbsoluteUrl(String url) {
        if (url == null) {
            return false;
        } else {
            return ABSOLUTE_URL.matcher(url).matches();
        }
    }
}
