package com.cfpamf.ms.insur.weixin.common;

import com.cfpamf.ms.insur.admin.dao.safes.SmOrderMapper;
import com.cfpamf.ms.insur.admin.service.OrderNoGenerator;
import com.cfpamf.ms.insur.base.util.RedisUtil;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.time.format.DateTimeFormatter;

/**
 *  容器启动后处理一些初始化的业务逻辑
 * <AUTHOR>
 */
@Component
public class AppListense implements InitializingBean {

//    @Autowired
//    SmOrderMapper orderMapper;
//
//    @Autowired
//    RedisUtil<String, Integer> redisUtil;
//
//    static DateTimeFormatter FMT = DateTimeFormatter.ofPattern("yyMMddHHmmss");
//
//    @Value("${spring.redis.database:119}")
//    int redisDatabase;

    @Autowired
    private OrderNoGenerator orderNoGenerator;

    @Override
    public void afterPropertiesSet() {
        /**
         * 初始化Id生成器
         */
        initIdGenerator();
    }
    private void initIdGenerator(){
        IdGenerator.init(orderNoGenerator);
    }
}
