package com.cfpamf.ms.insur.commission.po;

import com.cfpamf.ms.insur.admin.pojo.po.BasePO;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import javax.persistence.Table;

/**
 * <AUTHOR> 2022/12/16 15:38
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@Table(name = "commission_upload_detail")
public class CommissionUploadDetailPO extends BasePO {
    Long uploadId;

    String commissionMonth;

    String regionName;

    String orgName;

    String jobNumber;

    String userName;

    String commissionItem;

    String commissionDataType;

}
