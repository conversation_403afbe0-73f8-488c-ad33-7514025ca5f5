package com.cfpamf.ms.insur.weixin.service.config;

import com.cfpamf.ms.insur.weixin.pojo.vo.apply.common.BankVo;

import java.util.ArrayList;
import java.util.List;

public class ApplyConfig {
    private static List<BankVo> banks = new ArrayList<>();
    static{
        banks.add(new BankVo("SDCU","广东顺德农村商业银行"));
        banks.add(new BankVo("CRCB","重庆农村商业银行股份有限公司"));
        banks.add(new BankVo("KSCU","昆山农村商业银行"));
        banks.add(new BankVo("X50016","东莞农村商业银行股份有限公司"));
        banks.add(new BankVo("HBCCB","河北银行股份有限公司"));
        banks.add(new BankVo("YTCCB","烟台银行股份有限公司"));
        banks.add(new BankVo("TRC","天津农村商业银行股份有限公司"));
        banks.add(new BankVo("GZYH","贵州银行股份有限公司"));
        banks.add(new BankVo("Webank","深圳前海微众银行"));
        banks.add(new BankVo("ZYB","中原银行"));
        banks.add(new BankVo("HZURCB","杭州联合农村商业银行股份有限公司"));
        banks.add(new BankVo("SHRB","上海华瑞银行股份有限公司"));
        banks.add(new BankVo("NDHB","宁波东海银行"));
        banks.add(new BankVo("NCBANK","宁波通商银行股份有限公司"));

        banks.add(new BankVo("ICBC","中国工商银行股份有限公司"));
        banks.add(new BankVo("ABC","中国农业银行股份有限公司"));
        banks.add(new BankVo("BOC","中国银行股份有限公司"));
        banks.add(new BankVo("CCB","中国建设银行股份有限公司"));
        banks.add(new BankVo("BOCOM","交通银行"));
        banks.add(new BankVo("CITIC","中信银行"));

        banks.add(new BankVo("CEB","中国光大银行"));
        banks.add(new BankVo("HXB","华夏银行"));
        banks.add(new BankVo("CMBC","中国民生银行"));
        banks.add(new BankVo("GDB","广发银行股份有限公司"));
        banks.add(new BankVo("CMB","招商银行"));

        banks.add(new BankVo("CIB","兴业银行"));

        banks.add(new BankVo("CSCB","长沙银行股份有限公司"));
        banks.add(new BankVo("HXBC","华融湘江银行"));
    }

    public static List<BankVo> getBanks(){
        return banks;
    }
}
