package com.cfpamf.ms.insur.weixin.service.claim;

import com.cfpamf.ms.insur.admin.pojo.po.claim.SmClaimCancelReport;
import com.cfpamf.ms.insur.base.service.BaseWorkflow;
import com.cfpamf.ms.insur.weixin.pojo.form.claim.ClaimCancelReportExamineForm;
import com.cfpamf.ms.insur.weixin.pojo.form.claim.ClaimCancelReportForm;
import com.cfpamf.ms.insur.weixin.pojo.query.WxClaimQuery;
import com.cfpamf.ms.insur.weixin.pojo.vo.WxClaimListVo;
import com.cfpamf.ms.insur.weixin.pojo.vo.WxSessionVO;
import com.cfpamf.ms.insur.weixin.pojo.vo.claim.WxClaimCancelReportListVo;
import com.cfpamf.ms.insur.weixin.pojo.vo.claim.WxClaimCancelReportVo;
import com.github.pagehelper.PageInfo;

/**
 * <AUTHOR>
 * @date 2022/1/17 14:25
 */
public interface WxClaimCancelReportService {
    /**
     * 申请注销理赔报案
     *
     * @param claimNo
     * @param claimCancelReportForm
     * @return
     */
    WxClaimCancelReportVo apply(String claimNo, ClaimCancelReportForm claimCancelReportForm);

    /**
     * 申请注销理赔报案
     *
     * @param claimNo
     * @param claimCancelReportForm
     * @return
     */
    WxClaimCancelReportVo applyNoWxAuthority(String claimNo, ClaimCancelReportForm claimCancelReportForm);

    /**
     * 查看详情
     *
     * @param claimCancelReportId
     * @return
     */
    WxClaimCancelReportVo detail(Integer claimCancelReportId, String requestSource);


    /**
     * 更新注销理赔报案
     *
     * @param reportExamineForm
     * @param claimCancelReportId
     * @return
     */
    WxClaimCancelReportVo updateClaimCancelReport(Integer claimCancelReportId, ClaimCancelReportExamineForm reportExamineForm);

    /**
     * 搜索理赔注销流程集合
     *
     * @param query
     * @return
     */
    public PageInfo<WxClaimCancelReportListVo> getWxClaimCancelReportListByPage(WxClaimQuery query);

    /**
     * 校验权限
     *
     * @param wxOpenId
     * @param authorization
     * @return
     */
    public WxSessionVO checkAuthority(String wxOpenId, String authorization);

    /**
     * 处理选项操作流程  1 更新注销理赔申请 2 保存注销理赔申请流程
     * other： 撤回申请  注销报案   修改理赔流程状态
     *
     * @param reportExamineForm
     * @param wxClaimCancelReportVo
     */
    public void handlerOperationOption(ClaimCancelReportExamineForm reportExamineForm, WxClaimCancelReportVo wxClaimCancelReportVo);

    /**
     * 24个月理赔自动注销
     * @param wxClaimListV0
     * @param claimCancelReportForm
     */
    public void handlerFinishOption(WxClaimListVo wxClaimListV0, ClaimCancelReportForm claimCancelReportForm);
}
