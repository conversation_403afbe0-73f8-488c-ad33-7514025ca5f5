package com.cfpamf.ms.insur.weixin.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.ms.insur.admin.external.fx.model.renewal.FxFailRenewalTermDTO;
import com.cfpamf.ms.insur.admin.external.fx.model.renewal.FxSuccessRenewalTermDTO;
import com.cfpamf.ms.insur.admin.external.fx.model.renewal.FxWaitRenewalTermDTO;
import com.cfpamf.ms.insur.admin.external.xm.model.XmRenewalTermDTO;
import com.cfpamf.ms.insur.admin.external.zhongan.util.FileUtils;
import com.cfpamf.ms.insur.admin.pojo.dto.renewal.RenewalTermDTO;
import com.cfpamf.ms.insur.base.exception.BizException;
import com.cfpamf.ms.insur.base.util.excel.ExcelReadUtils;
import com.cfpamf.ms.insur.weixin.common.IdGenerator;
import com.google.common.collect.Lists;
import com.jcraft.jsch.*;
import com.jcraft.jsch.ChannelSftp.LsEntry;
import com.monitorjbl.xlsx.StreamingReader;
import lombok.extern.slf4j.Slf4j;
import net.lingala.zip4j.ZipFile;
import net.lingala.zip4j.io.inputstream.ZipInputStream;
import net.lingala.zip4j.model.FileHeader;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.IOUtils;
import org.apache.poi.ss.usermodel.Workbook;

import java.io.*;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * SFTP(Secure File Transfer Protocol)，安全文件传送协议。
 *
 * <AUTHOR>
 * @version 1.0 2014/12/18
 */
@Slf4j
public class Sftp {


    /**
     * Session
     */
    private Session session = null;
    /**
     * Channel
     */
    private ChannelSftp channel = null;
    /**
     * SFTP服务器IP地址
     */
    private String host;
    /**
     * SFTP服务器端口
     */
    private int port;
    /**
     * 连接超时时间，单位毫秒
     */
    private int timeout;

    /**
     * 用户名
     */
    private String username;
    /**
     * 密码
     */
    private String password;

    /**
     * SFTP 安全文件传送协议
     *
     * @param host     SFTP服务器IP地址
     * @param port     SFTP服务器端口
     * @param timeout  连接超时时间，单位毫秒
     * @param username 用户名
     * @param password 密码
     */
    public Sftp(String host, int port, int timeout, String username, String password) {
        this.host = host;
        this.port = port;
        this.timeout = timeout;
        this.username = username;
        this.password = password;
    }



    private static void xm() throws IOException {
        LocalDate renewalReturnDate = LocalDate.now().plusDays(-1);
        String renewalReturnDateString = renewalReturnDate.format(DateTimeFormatter.ofPattern("yyyyMMdd"));

        Sftp sftp = null;
//        Sftp sftp = new Sftp("************", 15022, 10000, "xiaojing", "xiaojing");
//        Sftp sftp = new Sftp("***************", 5023, 10000, "XJXH01_test", "dCmJ8mTP");
        try {
//            sftp = new Sftp("***************", 5023, 10000, "XJXH01_test", "dCmJ8mTP");
//            sftp = new Sftp("***********", 8822, 10000, "xiaojing", "pbkre7DvMh8x");
            sftp = new Sftp("************", 15022, 10000, "xiaojing", "xiaojing");
            sftp.login();
//        String currentDir = sftp.currentDir();
//        System.err.println("currentDir:" + currentDir);
//            sftp.changeDir("/renewal/20220328");
            sftp.changeDir("/customerService/renewal/channel/410");

//        sftp.uploadFile("/customerService/renewal/channel/410/20220330","renewal_3_410_20220330.data.zip",new FileInputStream(new File("C:\\Users\\<USER>\\ZHNX\\work\\tmpFile\\xm\\20220330.zip")));
//        sftp.uploadFile("/customerService/renewal/channel/410/20220407","renewal_3_410_20220407.data.zip",new FileInputStream(new File("C:\\Users\\<USER>\\ZHNX\\work\\tmpFile\\xm\\20220407.zip")));
//        sftp.uploadFile("/customerService/renewal/channel/410/20220410","renewal_3_410_20220410.data.zip",new FileInputStream(new File("C:\\Users\\<USER>\\ZHNX\\work\\tmpFile\\xm\\20220410.zip")));
//        sftp.changeDir(s);
            String[] list = new String[]{"20220314","20220318","20220310"};
            System.err.println("file list:" + JSON.toJSONString(list));
            for (String s : list) {
                InputStream okJsonInputStream = sftp.downloadFile("/customerService/renewal/channel/410/" + s, "renewal_3_410_" + s + ".ok.json");
                BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(okJsonInputStream));
                StringBuilder stringBuilder = new StringBuilder();
                String str;
                while ((str = bufferedReader.readLine()) != null) {
                    stringBuilder.append(str);
                }
                JSONObject jsonObject = JSON.parseObject(stringBuilder.toString());
                String countString = (String) jsonObject.get("count");
                long count = Long.parseLong(countString);
                //如果当日回传数量为小于1 则返回空
                if (count >= 1){
                    InputStream inputStream = sftp.downloadFile("/customerService/renewal/channel/410/" + s, "renewal_3_410_" + s + ".data.zip");
                    byte[] bytes = IOUtils.toByteArray(inputStream);
                    File file = FileUtils.saveFile(bytes, "C:\\Users\\<USER>\\ZHNX\\work\\tmpFile\\xm\\" + s + ".zip");
                    System.out.println(file.getPath());
                    ZipFile zipFile = new ZipFile(file);
                    //获取zip每个文件中的数据
                    List<FileHeader> fileHeaders = zipFile.getFileHeaders();
                    for (FileHeader fileHeader : fileHeaders) {
                        ZipInputStream csvFileInputStream = zipFile.getInputStream(fileHeader);
                        List<XmRenewalTermDTO> csvData = CsvUtil.getCsvData(csvFileInputStream, XmRenewalTermDTO.class);
                        if (CollectionUtils.isNotEmpty(csvData)) {
                            csvData.forEach(xmRenewalTermDTO -> {
                                System.out.println("保单号: " + xmRenewalTermDTO.getPolicyCode());
                                System.out.println("续期状态: " + xmRenewalTermDTO.getOfferStatus());
                                System.out.println("实缴日期: " + xmRenewalTermDTO.getArrivalTime());
                            });
                        }
                    }
                }


            }
//            InputStream inputStream = sftp.downloadFile("/customerService/renewal/channel/410/20220331", "renewal_3_410_20220331.data.zip");
//            byte[] bytes = IOUtils.toByteArray(inputStream);
//            File file = FileUtils.saveFile(bytes, IdGenerator.getUuid(), ".zip");
//            System.out.println(file.getPath());

        } finally {
            if (Objects.nonNull(sftp)) {
                sftp.logout();
            }

        }
    }
    public static void main(String[] args) throws Exception {

//        fx();
        xm();
    }
    private static void fx() throws IOException {
        List<RenewalTermDTO> result = Lists.newArrayList();
        List<FxSuccessRenewalTermDTO> successRenewalTermDTOS = Lists.newArrayList();
        LocalDate renewalReturnDate = LocalDate.now().plusDays(-1);
        Sftp sftp = null;
//        Sftp sftp = new Sftp("************", 15022, 10000, "xiaojing", "xiaojing");
//        Sftp sftp = new Sftp("***************", 5023, 10000, "XJXH01_test", "dCmJ8mTP");
        try {
//            sftp = new Sftp("***************", 5023, 10000, "XJXH01_test", "dCmJ8mTP");
            sftp = new Sftp("**************", 5023, 10000, "XJXH01_prd", "Wh71RtwP");
            sftp.login();
//        String currentDir = sftp.currentDir();
//        System.err.println("currentDir:" + currentDir);
            sftp.changeDir("/renewal");
//            sftp.changeDir("/customerService/renewal/channel/410");

//        sftp.uploadFile("/customerService/renewal/channel/410/20220331","renewal_3_410_20220331.data.zip",new FileInputStream(new File("C:\\Users\\<USER>\\ZHNX\\work\\tmpFile\\7f3e51fb2305485da256c89da3afab663789493708012464976\\renewal_3_410_20220314_0001.data.csv.zip")));
//        sftp.uploadFile("/customerService/renewal/channel/410/20220318","renewal_3_410_20220318.ok.json",new FileInputStream(new File("C:\\Users\\<USER>\\ZHNX\\work\\tmpFile\\test.json")));
//        sftp.changeDir(s);
            String[] list = sftp.list(Filter.ALL);
            System.err.println("file list:" + JSON.toJSONString(list));
            int renewalGraceDays = 70;
            for (String s : list) {
                boolean exist = sftp.exist("/renewal/" + s, "XJXH01_renewal_" + s + ".xls");
                if(!exist){
                    continue;
                }
                InputStream inputStream = sftp.downloadFile("/renewal/" + s, "XJXH01_renewal_" + s + ".xls");
                byte[] bytes = IOUtils.toByteArray(inputStream);
                File file = FileUtils.saveFile(bytes, "C:\\Users\\<USER>\\ZHNX\\work\\tmpFile\\fx\\" + s + ".xls");
                System.out.println(file.getPath());


                InputStream inputStream2 = sftp.downloadFile("/renewal/" + s, "XJXH01_renewal_" + s + ".xls");
                try (Workbook workbook = StreamingReader.builder()
                        .rowCacheSize(100)
                        //缓存到内存中的行数，默认是10
                        .bufferSize(1024 * 8)
                        .open(inputStream2)) {
                    List<FxWaitRenewalTermDTO> fxWaitRenewalTermDTOS = ExcelReadUtils.getSheetData(FxWaitRenewalTermDTO.class, 0, 0, false, workbook);
                    List<FxFailRenewalTermDTO> fxFailRenewalTermDTOList = ExcelReadUtils.getSheetData(FxFailRenewalTermDTO.class, 1, 0, false, workbook);
                    List<FxSuccessRenewalTermDTO> fxSuccessRenewalTermDTOList = ExcelReadUtils.getSheetData(FxSuccessRenewalTermDTO.class, 2, 0, false, workbook);
                    fxFailRenewalTermDTOList.forEach(fxFailRenewalTermDTO -> result.add(fxFailRenewalTermDTO.convert(renewalGraceDays)));
                    fxWaitRenewalTermDTOS.forEach(fxWaitRenewalTermDTO -> result.add(fxWaitRenewalTermDTO.convert(renewalGraceDays)));
                    fxSuccessRenewalTermDTOList.forEach(fxSuccessRenewalTermDTO -> result.add(fxSuccessRenewalTermDTO.convert(renewalGraceDays)));

                    successRenewalTermDTOS.addAll(fxSuccessRenewalTermDTOList);
                }


            }
//            InputStream inputStream = sftp.downloadFile("/customerService/renewal/channel/410/20220331", "renewal_3_410_20220331.data.zip");
//            byte[] bytes = IOUtils.toByteArray(inputStream);
//            File file = FileUtils.saveFile(bytes, IdGenerator.getUuid(), ".zip");
//            System.out.println(file.getPath());

        } finally {
            if (Objects.nonNull(sftp)) {
                sftp.logout();
            }

        }
    }

    /**
     * 登陆SFTP服务器
     *
     * @return boolean
     */
    public boolean login() {
        try {
            JSch jsch = new JSch();
            session = jsch.getSession(username, host, port);
            if (password != null) {
                session.setPassword(password);
            }
            Properties config = new Properties();
            config.put("StrictHostKeyChecking", "no");
            session.setConfig(config);
            session.setTimeout(timeout);
            session.connect();
            log.debug("sftp session connected");

            log.debug("opening channel");
            channel = (ChannelSftp) session.openChannel("sftp");
            channel.connect();

            log.debug("connected successfully");
            return true;
        } catch (JSchException e) {
            log.warn("sftp login failed", e);
            return false;
        }
    }

    /**
     * 上传文件
     * <p>
     * 使用示例，SFTP服务器上的目录结构如下：/testA/testA_B/
     * <p>
     * 当前目录方法参数：绝对路径/相对路径上传后
     * uploadFile("testA","upload.txt",new FileInputStream(new File("up.txt")))相对路径/testA/upload.txt
     * uploadFile("testA/testA_B","upload.txt",new FileInputStream(new File("up.txt")))相对路径/testA/testA_B/upload.txt
     * uploadFile("/testA/testA_B","upload.txt",new FileInputStream(new File("up.txt")))绝对路径/testA/testA_B/upload.txt
     *
     * </p>
     *
     * @param pathName SFTP服务器目录
     * @param fileName 服务器上保存的文件名
     * @param input    输入文件流
     * @return boolean
     */
    public boolean uploadFile(String pathName, String fileName, InputStream input) {

        String currentDir = currentDir();
        if (!changeDir(pathName)) {
            return false;
        }

        try {
            channel.put(input, fileName, ChannelSftp.OVERWRITE);
            if (!existFile(fileName)) {
                log.debug("upload failed");
                return false;
            }
            log.debug("upload successful");
            return true;
        } catch (SftpException e) {
            log.warn("upload failed", e);
            return false;
        } finally {
            changeDir(currentDir);
        }
    }

    /**
     * 下载文件
     * <p>
     * 使用示例，SFTP服务器上的目录结构如下：/testA/testA_B/
     * <p>
     * 当前目录方法参数：绝对路径/相对路径下载后
     * downloadFile("testA","down.txt","D:\\downDir")相对路径D:\\downDir\\down.txt
     * downloadFile("testA/testA_B","down.txt","D:\\downDir")相对路径D:\\downDir\\down.txt
     * downloadFile("/testA/testA_B","down.txt","D:\\downDir")绝对路径D:\\downDir\\down.txt
     *
     * </p>
     *
     * @param remotePath SFTP服务器目录
     * @param fileName   服务器上需要下载的文件名
     * @param localPath  本地保存路径
     * @return boolean
     */
    public boolean downloadFile(String remotePath, String fileName, String localPath) {

        String currentDir = currentDir();
        if (!changeDir(remotePath)) {
            return false;
        }

        try {
            String localFilePath = localPath + File.separator + fileName;
            channel.get(fileName, localFilePath);
            File localFile = new File(localFilePath);
            if (!localFile.exists()) {
                log.debug("download file failed");
                return false;
            }
            log.debug("download successful");
            return true;
        } catch (SftpException e) {
            log.warn("download file failed", e);
            return false;
        } finally {
            changeDir(currentDir);
        }
    }

    /**
     * 下载文件
     * <p>
     * 使用示例，SFTP服务器上的目录结构如下：/testA/testA_B/
     * <p>
     * 当前目录方法参数：绝对路径/相对路径下载后
     * downloadFile("testA","down.txt","D:\\downDir")相对路径D:\\downDir\\down.txt
     * downloadFile("testA/testA_B","down.txt","D:\\downDir")相对路径D:\\downDir\\down.txt
     * downloadFile("/testA/testA_B","down.txt","D:\\downDir")绝对路径D:\\downDir\\down.txt
     *
     * </p>
     *
     * @param remotePath SFTP服务器目录
     * @param fileName   服务器上需要下载的文件名
     * @return boolean
     */
    public InputStream downloadFile(String remotePath, String fileName) {
        String currentDir = currentDir();
        if (!changeDir(remotePath)) {
            log.warn("download file failed remotePath:{} , fileName:{}", remotePath, fileName);
            throw new BizException("", "SFTP下载文件失败");
        }
        try {
            log.debug("download successful");
            return channel.get(fileName);
        } catch (SftpException e) {
            log.warn("download file failed remotePath:{} , fileName:{}", remotePath, fileName, e);
            throw new BizException("", "SFTP下载文件失败");
        } finally {
            changeDir(currentDir);
        }
    }

    /**
     * 切换工作目录
     * <p>
     * 使用示例，SFTP服务器上的目录结构如下：/testA/testA_B/
     * <p>
     * 当前目录方法参数(绝对路径/相对路径)切换后的目录
     * changeDir("testA")相对路径/testA/
     * changeDir("testA/testA_B")相对路径/testA/testA_B/
     * changeDir("/testA")绝对路径/testA/
     * /testA/testA_B/changeDir("/testA")绝对路径/testA/
     *
     * </p>
     *
     * @param pathName 路径
     * @return boolean
     */
    public boolean changeDir(String pathName) {
        if (pathName == null || "".equals(pathName.trim())) {
            log.debug("invalid pathName");
            return false;
        }

        try {
            channel.cd(pathName.replaceAll("\\\\", "/"));
            log.debug("directory successfully changed,current dir=" + channel.pwd());
            return true;
        } catch (SftpException e) {
            log.warn("failed to change directory", e);
            return false;
        }
    }

    /**
     * 切换到上一级目录
     * <p>
     * 使用示例，SFTP服务器上的目录结构如下：/testA/testA_B/
     * <p>
     * 当前目录方法切换后的目录
     * /testA/changeToParentDir()/
     * /testA/testA_B/changeToParentDir()/testA/
     *
     * </p>
     *
     * @return boolean
     */
    public boolean changeToParentDir() {
        return changeDir("..");
    }

    /**
     * 切换到根目录
     *
     * @return boolean
     */
    public boolean changeToHomeDir() {
        String homeDir = null;
        try {
            homeDir = channel.getHome();
        } catch (SftpException e) {
            log.warn("can not get home directory", e);
            return false;
        }
        return changeDir(homeDir);
    }

    /**
     * 创建目录
     * <p>
     * 使用示例，SFTP服务器上的目录结构如下：/testA/testA_B/
     * <p>
     * 当前目录方法参数(绝对路径/相对路径)创建成功后的目录
     * /testA/testA_B/makeDir("testA_B_C")相对路径/testA/testA_B/testA_B_C/
     * makeDir("/testA/testA_B/testA_B_D")绝对路径/testA/testA_B/testA_B_D/
     * <p>
     * <br/>
     * <b>注意</b>，当<b>中间目录不存在</b>的情况下，不能够使用绝对路径的方式期望创建中间目录及目标目录。
     * 例如makeDir("/testNOEXIST1/testNOEXIST2/testNOEXIST3")，这是错误的。
     * </p>
     *
     * @param dirName 目录
     * @return boolean
     */
    public boolean makeDir(String dirName) {
        try {
            channel.mkdir(dirName);
            log.debug("directory successfully created,dir=" + dirName);
            return true;
        } catch (SftpException e) {
            log.warn("failed to create directory", e);
            return false;
        }
    }

    /**
     * 删除文件夹
     *
     * @param dirName
     * @return boolean
     */
    @SuppressWarnings("unchecked")
    public boolean delDir(String dirName) {
        if (!changeDir(dirName)) {
            return false;
        }

        Vector<LsEntry> list = null;
        try {
            list = channel.ls(channel.pwd());
        } catch (SftpException e) {
            log.warn("can not list directory", e);
            return false;
        }

        for (LsEntry entry : list) {
            String fileName = entry.getFilename();
            //if (!fileName.equals(".") && !fileName.equals("..")) {
            if(!Objects.equals(fileName,".") && !Objects.equals(fileName,"..")){
                if (entry.getAttrs().isDir()) {
                    delDir(fileName);
                } else {
                    delFile(fileName);
                }
            }
        }

        if (!changeToParentDir()) {
            return false;
        }

        try {
            channel.rmdir(dirName);
            log.debug("directory " + dirName + " successfully deleted");
            return true;
        } catch (SftpException e) {
            log.warn("failed to delete directory " + dirName, e);
            return false;
        }
    }

    /**
     * 删除文件
     *
     * @param fileName 文件名
     * @return boolean
     */
    public boolean delFile(String fileName) {
        if (fileName == null || fileName.trim().equals("")) {
            log.debug("invalid filename");
            return false;
        }

        try {
            channel.rm(fileName);
            log.debug("file " + fileName + " successfully deleted");
            return true;
        } catch (SftpException e) {
            log.warn("failed to delete file " + fileName, e);
            return false;
        }
    }

    /**
     * 当前目录下文件及文件夹名称列表
     *
     * @return String[]
     */
    public String[] ls() {
        return list(Filter.ALL);
    }

    /**
     * 指定目录下文件及文件夹名称列表
     *
     * @return String[]
     */
    public String[] ls(String pathName) {
        String currentDir = currentDir();
        if (!changeDir(pathName)) {
            return new String[0];
        }

        String[] result = list(Filter.ALL);
        if (!changeDir(currentDir)) {
            return new String[0];
        }
        return result;
    }

    /**
     * 当前目录下文件名称列表
     *
     * @return String[]
     */
    public String[] getFileList() {
        return list(Filter.FILE);
    }

    /**
     * 指定目录下文件名称列表
     *
     * @return String[]
     */
    public String[] getFileList(String pathName) {
        String currentDir = currentDir();
        if (!changeDir(pathName)) {
            return new String[0];
        }
        ;
        String[] result = list(Filter.FILE);
        if (!changeDir(currentDir)) {
            return new String[0];
        }
        return result;
    }

    /**
     * 当前目录下文件夹名称列表
     *
     * @return String[]
     */
    public String[] lsDirs() {
        return list(Filter.DIR);
    }

    /**
     * 指定目录下文件夹名称列表
     *
     * @return String[]
     */
    public String[] lsDirs(String pathName) {
        String currentDir = currentDir();
        if (!changeDir(pathName)) {
            return new String[0];
        }
        ;
        String[] result = list(Filter.DIR);
        if (!changeDir(currentDir)) {
            return new String[0];
        }
        return result;
    }

    /**
     * 当前目录是否存在文件或文件夹
     *
     * @param name 名称
     * @return boolean
     */
    public boolean exist(String name) {
        return exist(ls(), name);
    }

    /**
     * 指定目录下，是否存在文件或文件夹
     *
     * @param path 目录
     * @param name 名称
     * @return boolean
     */
    public boolean exist(String path, String name) {
        return exist(ls(path), name);
    }

    /**
     * 当前目录是否存在文件
     *
     * @param name 文件名
     * @return boolean
     */
    public boolean existFile(String name) {
        return exist(getFileList(), name);
    }

    /**
     * 指定目录下，是否存在文件
     *
     * @param path 目录
     * @param name 文件名
     * @return boolean
     */
    public boolean existFile(String path, String name) {
        return exist(getFileList(path), name);
    }

    /**
     * 当前目录是否存在文件夹
     *
     * @param name 文件夹名称
     * @return boolean
     */
    public boolean existDir(String name) {
        return exist(lsDirs(), name);
    }

    /**
     * 指定目录下，是否存在文件夹
     *
     * @param path 目录
     * @param name 文家夹名称
     * @return boolean
     */
    public boolean existDir(String path, String name) {
        return exist(lsDirs(path), name);
    }

    /**
     * 当前工作目录
     *
     * @return String
     */
    public String currentDir() {
        try {
            return channel.pwd();
        } catch (SftpException e) {
            log.warn("failed to get current dir", e);
            return homeDir();
        }
    }

    /**
     * 登出
     */
    public void logout() {
        if (channel != null) {
            channel.quit();
            channel.disconnect();
        }
        if (session != null) {
            session.disconnect();
        }
        log.debug("logout successfully");
    }


    //------private method ------

    /**
     * 枚举，用于过滤文件和文件夹
     */
    private enum Filter {
        /**
         * 文件及文件夹
         */
        ALL,
        /**
         * 文件
         */
        FILE,
        /**
         * 文件夹
         */
        DIR
    }

    ;

    /**
     * 列出当前目录下的文件及文件夹
     *
     * @param filter 过滤参数
     * @return String[]
     */
    @SuppressWarnings("unchecked")
    private String[] list(Filter filter) {
        Vector<LsEntry> list = null;
        try {
            //ls方法会返回两个特殊的目录，当前目录(.)和父目录(..)
            list = channel.ls(channel.pwd());
        } catch (SftpException e) {
            log.warn("can not list directory", e);
            return new String[0];
        }

        List<String> resultList = new ArrayList<String>();
        for (LsEntry entry : list) {
            if (filter(entry, filter)) {
                resultList.add(entry.getFilename());
            }
        }
        return resultList.toArray(new String[0]);
    }

    /**
     * 判断是否是否过滤条件
     *
     * @param entry LsEntry
     * @param f     过滤参数
     * @return boolean
     */
    private boolean filter(LsEntry entry, Filter f) {
        if (f.equals(Filter.ALL)) {
            return !entry.getFilename().equals(".") && !entry.getFilename().equals("..");
        } else if (f.equals(Filter.FILE)) {
            return !entry.getFilename().equals(".") && !entry.getFilename().equals("..") && !entry.getAttrs().isDir();
        } else if (f.equals(Filter.DIR)) {
            return !entry.getFilename().equals(".") && !entry.getFilename().equals("..") && entry.getAttrs().isDir();
        }
        return false;
    }

    /**
     * 根目录
     *
     * @return String
     */
    private String homeDir() {
        try {
            return channel.getHome();
        } catch (SftpException e) {
            return "/";
        }
    }

    /**
     * 判断字符串是否存在于数组中
     *
     * @param strArr 字符串数组
     * @param str    字符串
     * @return boolean
     */
    private boolean exist(String[] strArr, String str) {
        if (strArr == null || strArr.length == 0) {
            return false;
        }
        if (str == null || str.trim().equals("")) {
            return false;
        }
        for (String s : strArr) {
            if (s.equalsIgnoreCase(str)) {
                return true;
            }
        }
        return false;
    }
}