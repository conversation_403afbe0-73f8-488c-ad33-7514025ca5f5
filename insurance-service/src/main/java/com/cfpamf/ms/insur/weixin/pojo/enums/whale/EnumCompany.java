package com.cfpamf.ms.insur.weixin.pojo.enums.whale;

import lombok.Getter;

/**
 * 小鲸(产品中心)定义的渠道名
 */
@Getter
public enum EnumCompany {
    ZhongHuaCai("ZhongHuaCai","中华财机构编码"),
    <PERSON><PERSON><PERSON>("<PERSON>_Kang","泰康保险机构编码"),
    ZhongAn("ZhongAn","众安机构编码");


    /**
     * 机构编码
     */
    private String code;

    /**
     * 描述
     */
    private String desc;

    EnumCompany(String code,String desc){
        this.code = code;
        this.desc = desc;
    }
}
