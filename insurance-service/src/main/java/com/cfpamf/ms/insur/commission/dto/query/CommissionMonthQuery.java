package com.cfpamf.ms.insur.commission.dto.query;

import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import javax.validation.constraints.NotNull;
import java.time.YearMonth;

/**
 * <AUTHOR> 2022/12/23 13:04
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class CommissionMonthQuery {

    @NotNull(message = "查询开始时间不能为空")
    @ApiModelProperty(value = "开始月份", example = "2022-12")
    YearMonth start;

    @NotNull(message = "查询结束时间不能为空")
    @ApiModelProperty(value = "结束月份", example = "2022-12")
    YearMonth end;


}
