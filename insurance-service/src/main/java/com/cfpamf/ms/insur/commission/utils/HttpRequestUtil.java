package com.cfpamf.ms.insur.commission.utils;

import org.apache.commons.lang3.StringUtils;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.Objects;

/**
 * 获取http request 参数信息
 *
 * <AUTHOR>
 */
public class HttpRequestUtil {
    private final static String API_AUTH_NAME = "authorization";
    private static JwtHelper jwtHelper = new JwtHelper();

    public HttpRequestUtil() {
    }

    /**
     * 获取token
     *
     * @return
     */
    public static String getToken() {
        HttpServletRequest request = getRequest();
        if (request == null) {
            return null;
        }
        String token = request.getHeader(API_AUTH_NAME);
        if (StringUtils.isEmpty(token)) {
            token = request.getParameter(API_AUTH_NAME);
        }
        return token;
    }


    /**
     * 获取requestUrl
     *
     * @return
     */
    public static String getRequestUri() {
        HttpServletRequest request = getRequest();
        if (request != null) {
            return getRequest().getRequestURI();
        }
        return "";
    }


    /**
     * 获取当前请求主体
     *
     * @return
     */
    public static HttpServletRequest getRequest() {
        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        if (requestAttributes != null) {
            return ((ServletRequestAttributes) requestAttributes).getRequest();
        }
        return null;
    }

    /**
     * 获取用户Id
     *
     * @return
     */
    @SuppressWarnings({"unchecked", "rawtypes"})
    public static String getUserId() {
        String token = getToken();
        if (token == null) {
            return null;
        }
        JwtUserInfo jwtUserInfo = jwtHelper.getUserFromToken(token);
        return jwtUserInfo.getJobNumber();
    }

    public static JwtUserInfo getJwtUserInfo() {
        String token = getToken();
        if (token == null) {
            return null;
        }
        return jwtHelper.getUserFromToken(token);
    }

    public static JwtUserInfo getJwtUserInfoNonNull() {
        return Objects.requireNonNull(getJwtUserInfo(), "token不能为空");
    }
}
