package com.cfpamf.ms.insur.weixin.service.policy;

import com.cfpamf.ms.insur.admin.external.whale.api.WhaleApiService;
import com.cfpamf.ms.insur.admin.external.whale.client.WhalePolicyLostClient;
import com.cfpamf.ms.insur.admin.external.whale.model.PolicyLostRegisterDataInput;
import com.cfpamf.ms.insur.admin.external.whale.model.PolicyLostRegisterList;
import com.cfpamf.ms.insur.admin.external.whale.model.PolicyLostRegisterQuery;
import com.cfpamf.ms.insur.admin.external.whale.model.WhaleResp;
import com.cfpamf.ms.insur.admin.external.whale.utils.PageUtils;
import com.cfpamf.ms.insur.weixin.pojo.vo.WxSessionVO;
import com.cfpamf.ms.insur.weixin.service.WxAbstractService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;



/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class PolicyLostRegisterService extends WxAbstractService {

    @Autowired
    private WhaleApiService whaleApiService;

    public PageUtils<PolicyLostRegisterList> queryPage(PolicyLostRegisterQuery query, WxSessionVO wxSessionVO) {
        query.setRegisterUserId(wxSessionVO.getUserId());
        WhaleResp<PageUtils<PolicyLostRegisterList>> whaleResp = whaleApiService.list(query);
        return whaleResp.getData();
    }

    public PolicyLostRegisterList queryDetail(Integer id) {
        WhaleResp<PolicyLostRegisterList> whaleResp = whaleApiService.detail(id);
        return whaleResp.getData();
    }

    /**
     * 插入丢单补偿记录
     * @param input
     */
    public void insertData(PolicyLostRegisterDataInput input,WxSessionVO wxSessionVO){
        input.setRegisterUserId(wxSessionVO.getUserId());
        input.setRegisterUserName(wxSessionVO.getUserName());
        input.setRegisterUserRegionName(wxSessionVO.getRegionName());
        whaleApiService.save(input);
    }

    /**
     * 插入丢单补偿记录
     * @param input
     */
    public void updatePolicyNo(PolicyLostRegisterDataInput input){
        whaleApiService.updatePolicyNo(input);
    }

}
