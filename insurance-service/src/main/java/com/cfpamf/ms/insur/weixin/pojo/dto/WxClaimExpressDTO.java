package com.cfpamf.ms.insur.weixin.pojo.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 理赔快递DTO
 *
 * <AUTHOR>
 **/
@Data
@ApiModel
public class WxClaimExpressDTO extends WxAuthorizationDTO {

    /**
     * 理赔文件Id
     */
    @ApiModelProperty("理赔文件Id")
    private Integer ceId;

    /**
     * 理赔Id
     */
    @ApiModelProperty("理赔Id")
    private Integer claimId;

    /**
     * 邮件快递时间
     */
    @ApiModelProperty("邮件快递时间")
    private Date expressDate;

    /**
     * 快递公司
     */
    @ApiModelProperty("快递公司")
    private String expressCompanyName;

    /**
     * 快递单号
     */
    @ApiModelProperty("快递单号")
    private String expressNo;

    /**
     * 文件url join
     */
    @JsonIgnore
    @ApiModelProperty(value = "文件url join", hidden = true)
    private String expressUrlJoin;

    /**
     * 文件url列表
     */
    @ApiModelProperty("文件url列表")
    private List<String> expressUrls;

    /**
     * 操作人
     */
    @ApiModelProperty("操作人")
    private String userName;
}
