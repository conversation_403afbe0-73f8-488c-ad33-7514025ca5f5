package com.cfpamf.ms.insur.weixin.pojo.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * 微信代理人修改密码DTO
 *
 * <AUTHOR>
 **/
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class WxAgentModifyPwdDTO extends WxAuthorizationDTO {

    /**
     * 手机号
     */
    private String agentMobile;

    /**
     * 验证码
     */
    private String verifyCode;

    /**
     * 密码
     */
    private String password;
}
