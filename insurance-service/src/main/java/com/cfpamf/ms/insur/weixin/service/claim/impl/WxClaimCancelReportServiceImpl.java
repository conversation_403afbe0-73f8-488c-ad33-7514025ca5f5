package com.cfpamf.ms.insur.weixin.service.claim.impl;

import com.alibaba.fastjson.JSON;
import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.ms.bms.facade.vo.UserDetailVO;
import com.cfpamf.ms.bms.facade.vo.UserRoleVO;
import com.cfpamf.ms.insur.admin.dao.safes.SmClaimMapper;
import com.cfpamf.ms.insur.admin.dao.safes.claim.SmClaimCancelReportMapper;
import com.cfpamf.ms.insur.admin.dao.safes.claim.SmClaimCancelReportProgressMapper;
import com.cfpamf.ms.insur.admin.enums.EnumRequestSource;
import com.cfpamf.ms.insur.admin.pojo.dto.ProgressDTO;
import com.cfpamf.ms.insur.admin.pojo.po.claim.SmClaimCancelReport;
import com.cfpamf.ms.insur.admin.pojo.po.claim.SmClaimCancelReportProgress;
import com.cfpamf.ms.insur.admin.pojo.vo.AuthUserVO;
import com.cfpamf.ms.insur.admin.pojo.vo.ProgressVO;
import com.cfpamf.ms.insur.admin.pojo.vo.SmClaimProgressVO;
import com.cfpamf.ms.insur.admin.service.ClaimWorkflow;
import com.cfpamf.ms.insur.admin.service.SmClaimServiceImpl;
import com.cfpamf.ms.insur.admin.service.UserService;
import com.cfpamf.ms.insur.admin.service.claim.ClaimCancelReportWorkflow;
import com.cfpamf.ms.insur.base.exception.BizException;
import com.cfpamf.ms.insur.base.exception.ExcptEnum;
import com.cfpamf.ms.insur.base.service.BaseWorkflow;
import com.cfpamf.ms.insur.base.util.*;
import com.cfpamf.ms.insur.weixin.pojo.form.claim.ClaimCancelReportExamineForm;
import com.cfpamf.ms.insur.weixin.pojo.form.claim.ClaimCancelReportForm;
import com.cfpamf.ms.insur.weixin.pojo.query.WxClaimQuery;
import com.cfpamf.ms.insur.weixin.pojo.vo.WxClaimDetailVO;
import com.cfpamf.ms.insur.weixin.pojo.vo.WxClaimListVo;
import com.cfpamf.ms.insur.weixin.pojo.vo.WxSessionVO;
import com.cfpamf.ms.insur.weixin.pojo.vo.claim.WxClaimCancelReportListVo;
import com.cfpamf.ms.insur.weixin.pojo.vo.claim.WxClaimCancelReportVo;
import com.cfpamf.ms.insur.weixin.service.WxAbstractService;
import com.cfpamf.ms.insur.weixin.service.WxCcClaimService;
import com.cfpamf.ms.insur.weixin.service.claim.WxClaimCancelReportService;
import com.cfpamf.ms.insur.weixin.service.claim.helper.ClaimCancelReportWxNotifyHelper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/1/17 15:05
 */
@Service
public class WxClaimCancelReportServiceImpl extends WxAbstractService implements WxClaimCancelReportService {
    @Autowired
    SmClaimMapper claimMapper;
    @Autowired
    SmClaimCancelReportMapper claimCancelReportMapper;
    @Autowired
    SmClaimCancelReportProgressMapper claimCancelReportProgressMapper;
    @Autowired
    private UserService userService;
    @Autowired
    ClaimCancelReportWorkflow claimCancelReportWorkflow;
    @Autowired
    PermissionUtil permissionUtil;
    @Autowired
    WxCcClaimService wxCcClaimService;
    /**
     * 理赔service
     */
    @Autowired
    private SmClaimServiceImpl claimService;
    @Autowired
    ClaimCancelReportWxNotifyHelper claimCancelReportWxNotifyHelper;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public WxClaimCancelReportVo apply(String claimNo, ClaimCancelReportForm claimCancelReportForm) {
        //微信菜单鉴权
        checkAuthority();
        return applyNoWxAuthority(claimNo, claimCancelReportForm);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public WxClaimCancelReportVo applyNoWxAuthority(String claimNo, ClaimCancelReportForm claimCancelReportForm) {
        UserDetailVO currentUser = ThreadUserUtil.USER_DETAIL_TL.get();
        String loginUserId = currentUser.getJobNumber();
        //校验表单
        WxClaimListVo claimOrder = claimMapper.getClaimOrder(claimNo);
        checkClaimCancelReportForm(claimOrder, loginUserId, currentUser);

        AuthUserVO currUser = userService.getAuthUserByUserId(loginUserId);
        claimCancelReportForm.setClaimNo(claimNo);
        //当前登录用户不存在
        if (Objects.isNull(currUser)) {
            throw new BizException(ExcptEnum.DATA_ERROR_801304);
        }

        //保存理赔注销申请
        BaseWorkflow.Step cancelReportStep = claimCancelReportWorkflow.getStepByCode(ClaimCancelReportWorkflow.STEP_CANCEL_REPORT_APPLY);
        BaseWorkflow.Option cancelReportStepOption = getCancelReportStepOption(cancelReportStep, currUser, currentUser);
        BaseWorkflow.Step nextStep = claimCancelReportWorkflow.getStepByCode(cancelReportStepOption.getNextStep());
        SmClaimCancelReport smClaimCancelReport = saveSmClaimCancelReport(claimCancelReportForm, loginUserId, nextStep);
        WxClaimCancelReportVo wxClaimCancelReportVo = claimCancelReportMapper.getByClaimCancelReportId(smClaimCancelReport.getId());
        //保存理赔注销申请流程
        SmClaimCancelReportProgress smClaimCancelReportProgress = saveSmClaimCancelReportProgress(loginUserId, smClaimCancelReport.getId(), cancelReportStep, cancelReportStepOption);
        if (Objects.equals(cancelReportStepOption.getNextStep(), claimCancelReportWorkflow.getFinishStepCode())) {
            //理赔专员注销报案的话一开始注销就结案
            //插入结案记录
            saveSmClaimCancelReportProgress(loginUserId, smClaimCancelReport.getId(), nextStep, cancelReportStepOption);
            updateSmClaimCancelReport(null, wxClaimCancelReportVo, nextStep, cancelReportStepOption);
            //更新理赔流程状态
            updateClaimProgress(wxClaimCancelReportVo, cancelReportStepOption.getOCode());
            return wxClaimCancelReportVo;
        }
        //修改理赔流程去申请报案状态
        claimService.updateClaimToCancelReport(claimOrder.getId(), currUser, smClaimCancelReportProgress);
        //发送微信通知
        sendWxNotify(nextStep, smClaimCancelReport.getId());
        return wxClaimCancelReportVo;
    }

    /**
     * 发送微信提醒
     *
     * @param nextStep
     * @param claimCancelReportId
     */
    private void sendWxNotify(BaseWorkflow.Step nextStep, Integer claimCancelReportId) {
        String sCode = nextStep.getSCode();
        if (Objects.equals(sCode, ClaimCancelReportWorkflow.STEP_DATA_CHECK_BY_PIC)) {
            claimCancelReportWxNotifyHelper.sendNotifyMessageToPic(claimCancelReportId);
        }
        if (Objects.equals(sCode, ClaimCancelReportWorkflow.STEP_DATA_CHECK_BY_SAFES_CENTER)) {
            claimCancelReportWxNotifyHelper.sendNotifyMessageToClaimAdmin(claimCancelReportId);
        }
    }


    @Override
    public WxClaimCancelReportVo detail(Integer claimCancelReportId, String requestSource) {
        WxClaimCancelReportVo wxClaimCancelReportVo = claimCancelReportMapper.getByClaimCancelReportId(claimCancelReportId);
        if (Objects.isNull(wxClaimCancelReportVo)) {
            throw new MSBizNormalException("", "当前注销理赔报案申请不存在。");
        }
        String currentState = wxClaimCancelReportVo.getCurrentState();
        //设置注销理赔报案流程信息
        SmClaimProgressVO claimProgressVO = new SmClaimProgressVO();
        List<ProgressVO> historyProgressList = claimCancelReportProgressMapper.getProgressList(claimCancelReportId);
        claimProgressVO.setProgressList(historyProgressList);
        claimProgressVO.setAllSteps(claimCancelReportWorkflow.getCurrentWorkflowAllStep(currentState, historyProgressList));
        BaseWorkflow.Step nextProgress = getNextProgress(currentState, wxClaimCancelReportVo.getCreateBy(), requestSource);
        claimProgressVO.setNextProgress(nextProgress);
        wxClaimCancelReportVo.setProgressVO(claimProgressVO);

        //设置理赔信息
        WxClaimDetailVO wxClaimDetailVO = wxCcClaimService.getWxClaimDetailVONoProgressInfo(wxClaimCancelReportVo.getClaimId());
        wxClaimCancelReportVo.setWxClaimDetailVO(wxClaimDetailVO);
        return wxClaimCancelReportVo;
    }

    /**
     * 获取下一步流程
     *
     * @param currentState
     * @param currentState
     * @return
     */
    private BaseWorkflow.Step getNextProgress(String currentState, String createBy, String requestSource) {
        BaseWorkflow.Step nextProgress = claimCancelReportWorkflow.getStepByCode(currentState);
        if (Objects.isNull(nextProgress)) {
            return null;
        }
        List<BaseWorkflow.Option> options = nextProgress.getOptions();
        if (CollectionUtils.isEmpty(options)) {
            return nextProgress;
        }
        //过滤掉系统自动撤回选项
        List<BaseWorkflow.Option> optionList = options.stream().filter(option -> !Objects.equals(option.getOCode(), ClaimCancelReportWorkflow.OPTION_SYSTEM_WITHDRAW_APPLY))
                .collect(Collectors.toList());
        //如果创建人和当前用户一样就可以显示撤回申请选项
        if (!Objects.equals(createBy, HttpRequestUtil.getUserId()) || Objects.equals(requestSource, EnumRequestSource.back.name())) {
            //过滤掉撤回申请选项
            optionList = optionList.stream().filter(option -> !Objects.equals(option.getOCode(), ClaimCancelReportWorkflow.OPTION_WITHDRAW_APPLY))
                    .collect(Collectors.toList());
        }
        nextProgress.setOptions(optionList);

        return nextProgress;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public WxClaimCancelReportVo updateClaimCancelReport(Integer claimCancelReportId, ClaimCancelReportExamineForm reportExamineForm) {
        WxClaimCancelReportVo wxClaimCancelReportVo = claimCancelReportMapper.getByClaimCancelReportId(claimCancelReportId);
        if (Objects.isNull(wxClaimCancelReportVo)) {
            throw new MSBizNormalException("", "当前注销理赔报案申请不存在。");
        }
        //校验权限
        checkUpdateAuthority(wxClaimCancelReportVo, reportExamineForm.getOptionCode());
        //更新注销理赔报案申请
        handlerOperationOption(reportExamineForm, wxClaimCancelReportVo);

        return claimCancelReportMapper.getByClaimCancelReportId(claimCancelReportId);
    }

    @Override
    public PageInfo<WxClaimCancelReportListVo> getWxClaimCancelReportListByPage(WxClaimQuery query) {
        WxSessionVO session = checkAuthorityEpAg(query.getOpenId(), query.getAuthorization());
        wxCcClaimService.initQuery(query, session);
        if (!query.isAll()) {
            PageHelper.startPage(query.getPage(), query.getSize());
        }
        List<WxClaimCancelReportListVo> claimCancelReportListVoList = claimCancelReportMapper.searchWxClaimCancelReport(query);
        return new PageInfo<>(claimCancelReportListVoList);
    }

    /**
     * 处理选项操作流程  1 更新注销理赔申请 2 保存注销理赔申请流程
     * other： 撤回申请  注销报案   修改理赔流程状态
     *
     * @param reportExamineForm
     * @param wxClaimCancelReportVo
     */
    @Override
    public void handlerOperationOption(ClaimCancelReportExamineForm reportExamineForm, WxClaimCancelReportVo wxClaimCancelReportVo) {
        String currentState = wxClaimCancelReportVo.getCurrentState();
        String optionCode = reportExamineForm.getOptionCode();
        String loginUserId = HttpRequestUtil.getUserId();
        //获取步骤选项
        BaseWorkflow.Step currentStep = claimCancelReportWorkflow.getStepByCode(currentState);
        BaseWorkflow.Option updateOption = currentStep.getOptions()
                .stream()
                .filter(option -> Objects.equals(option.getOCode(), optionCode))
                .findFirst()
                .orElseThrow(() -> new MSBizNormalException("", "步骤选项不存在"));
        updateOption.setOValue(reportExamineForm.getOptionValue());
        //获取下一步骤的步骤code
        String nextStepCode = updateOption.getNextStep();
        BaseWorkflow.Step nextStep = claimCancelReportWorkflow.getStepByCode(nextStepCode);
        Integer claimCancelReportId = wxClaimCancelReportVo.getId();

        //更新注销理赔申请
        updateSmClaimCancelReport(reportExamineForm.getClaimCancelReportForm(), wxClaimCancelReportVo, nextStep, updateOption);

        //保存操作流程
        saveSmClaimCancelReportProgress(loginUserId, claimCancelReportId, currentStep, updateOption);
        if (Objects.equals(nextStepCode, claimCancelReportWorkflow.getFinishStepCode())) {
            //如果下一步是结案的话 自动插入结案流程 结案选项是来源于操作选项 所以直接穿操作选项
            saveSmClaimCancelReportProgress(loginUserId, claimCancelReportId, nextStep, updateOption);

            //更新理赔流程状态
            updateClaimProgress(wxClaimCancelReportVo, optionCode);

        }
        //微信通知  注销申请被驳回-提醒管护客户经理
        if (Objects.equals(optionCode, ClaimCancelReportWorkflow.OPTION_CODE_REJECT_BY_SAFES_CENTER) || Objects.equals(optionCode, ClaimCancelReportWorkflow.OPTION_CODE_REJECT_BY_PIC)) {
            //驳回微信通知
            claimCancelReportWxNotifyHelper.sendRejectMessage(claimCancelReportId, reportExamineForm.getOptionValue());
        }
        sendWxNotify(nextStep, claimCancelReportId);
    }

    @Override
    public void handlerFinishOption(WxClaimListVo wxClaimListV0, ClaimCancelReportForm claimCancelReportForm) {
        String loginUserId = wxClaimListV0.getCustomerAdminId();
        //保存理赔注销申请
        BaseWorkflow.Step cancelReportStep = claimCancelReportWorkflow.getStepByCode(ClaimCancelReportWorkflow.STEP_CANCEL_REPORT_APPLY);
        BaseWorkflow.Option cancelReportStepOption = claimCancelReportWorkflow.getStepOption(cancelReportStep, ClaimCancelReportWorkflow.OPTION_CODE_SETTLEMENT_CANCEL_REPORT);
        BaseWorkflow.Step nextStep = claimCancelReportWorkflow.getStepByCode(cancelReportStepOption.getNextStep());
        SmClaimCancelReport smClaimCancelReport = saveSmClaimCancelReport(claimCancelReportForm, loginUserId, nextStep);
        WxClaimCancelReportVo wxClaimCancelReportVo = claimCancelReportMapper.getByClaimCancelReportId(smClaimCancelReport.getId());
        //保存理赔注销申请流程
        SmClaimCancelReportProgress smClaimCancelReportProgress = saveSmClaimCancelReportProgress(loginUserId, smClaimCancelReport.getId(), cancelReportStep, cancelReportStepOption);
        //理赔专员注销报案的话一开始注销就结案
        //插入结案记录
        saveSmClaimCancelReportProgress(loginUserId, smClaimCancelReport.getId(), nextStep, cancelReportStepOption);
        updateSmClaimCancelReport(null, wxClaimCancelReportVo, nextStep, cancelReportStepOption);
        //更新理赔流程状态
        updateClaimFinishProgress(wxClaimCancelReportVo);
    }

    /**
     * 更新理赔流程状态
     *
     * @param wxClaimCancelReportVo
     */
    private void updateClaimFinishProgress(WxClaimCancelReportVo wxClaimCancelReportVo) {
        Integer claimId = wxClaimCancelReportVo.getClaimId();

        ProgressDTO progressDTO = new ProgressDTO();
        progressDTO.setClaimId(claimId);
        progressDTO.setSCode(ClaimWorkflow.STEP_CLAIM_CANCEL_REPORT_CODE);
        progressDTO.setSName(ClaimWorkflow.STEP_CLAIM_CANCEL_REPORT_NAME);
        //如果选项理赔专员注销报案
        WxClaimDetailVO claimDetailVO = claimService.getSmClaimById(claimId);
        progressDTO.setSCode(claimDetailVO.getClaimState());
        progressDTO.setSName(claimDetailVO.getClaimResult());
        progressDTO.setOCode(ClaimWorkflow.OPTION_CODE_CLAIM_CANCEL);
        progressDTO.setOName(ClaimWorkflow.OPTION_NAME_CLAIM_CANCEL);
        progressDTO.setOValue("案件已过理赔时效");
        progressDTO.setOTime(Date.from(wxClaimCancelReportVo.getCreateTime().atZone(ZoneId.systemDefault()).toInstant()));
        progressDTO.setCreateBy(HttpRequestUtil.getUserName());
        claimService.saveFinishProgress(progressDTO);
    }

    /**
     * 更新理赔流程状态
     *
     * @param wxClaimCancelReportVo
     * @param optionCode
     */
    private void updateClaimProgress(WxClaimCancelReportVo wxClaimCancelReportVo, String optionCode) {
        Integer claimId = wxClaimCancelReportVo.getClaimId();

        ProgressDTO progressDTO = new ProgressDTO();
        progressDTO.setClaimId(claimId);
        progressDTO.setSCode(ClaimWorkflow.STEP_CLAIM_CANCEL_REPORT_CODE);
        progressDTO.setSName(ClaimWorkflow.STEP_CLAIM_CANCEL_REPORT_NAME);
        if (Objects.equals(optionCode, ClaimCancelReportWorkflow.OPTION_CLAIM_CANCELED)) {
            //如果选项是注销报案
            progressDTO.setOCode(ClaimWorkflow.OPTION_CODE_CLAIM_CANCEL);
            progressDTO.setOName(ClaimWorkflow.OPTION_NAME_CLAIM_CANCEL);
            progressDTO.setOValue(wxClaimCancelReportVo.getCancelReportCause());
            progressDTO.setOTime(Date.from(wxClaimCancelReportVo.getCreateTime().atZone(ZoneId.systemDefault()).toInstant()));
            progressDTO.setCreateBy(HttpRequestUtil.getUserName());
        }
        if (Objects.equals(optionCode, ClaimCancelReportWorkflow.OPTION_WITHDRAW_APPLY) || Objects.equals(optionCode, ClaimCancelReportWorkflow.OPTION_SYSTEM_WITHDRAW_APPLY)) {
            //如果选项是撤回申请
            progressDTO.setOCode(ClaimWorkflow.OPTION_CODE_WITHDRAW_APPLY);
            progressDTO.setOName(ClaimWorkflow.OPTION_NAME_WITHDRAW_APPLY);
            if (Objects.equals(optionCode, ClaimCancelReportWorkflow.OPTION_SYSTEM_WITHDRAW_APPLY)) {
                progressDTO.setOValue("逾期未处理，系统自动撤回申请注销报案");
            }
            if (Objects.equals(optionCode, ClaimCancelReportWorkflow.OPTION_WITHDRAW_APPLY)) {
                progressDTO.setOValue("客户经理撤回申请注销报案");
            }
            progressDTO.setOTime(new Date());
            progressDTO.setCreateBy(HttpRequestUtil.getUserName());
        }
        if (Objects.equals(optionCode, ClaimCancelReportWorkflow.OPTION_CODE_SETTLEMENT_CANCEL_REPORT)) {
            //如果选项理赔专员注销报案
            WxClaimDetailVO claimDetailVO = claimService.getSmClaimById(claimId);
            progressDTO.setSCode(claimDetailVO.getClaimState());
            progressDTO.setSName(claimDetailVO.getClaimResult());
            progressDTO.setOCode(ClaimWorkflow.OPTION_CODE_CLAIM_CANCEL);
            progressDTO.setOName(ClaimWorkflow.OPTION_NAME_CLAIM_CANCEL);
            progressDTO.setOValue("理赔专员注销报案");
            progressDTO.setOTime(Date.from(wxClaimCancelReportVo.getCreateTime().atZone(ZoneId.systemDefault()).toInstant()));
            progressDTO.setCreateBy(HttpRequestUtil.getUserName());
        }
        claimService.saveProgress(progressDTO);
        //如果是注销申请通过 发送微信通知
        String oCode = progressDTO.getOCode();
        if (Objects.equals(oCode, ClaimWorkflow.OPTION_CODE_CLAIM_CANCEL)) {
            claimCancelReportWxNotifyHelper.sendSuccessMessage(wxClaimCancelReportVo.getId());
        }
    }

    /**
     * 校验权限
     * 撤回申请选项除了结案 发起人都有权限
     * 1.未结案
     * 2.机构审核 -  机构渠道PCO
     * 3.总部审核 -  理赔专员
     * 4.其他步骤 - 发起人
     *
     * @param wxClaimCancelReportVo
     * @param optionCode
     */
    public void checkUpdateAuthority(WxClaimCancelReportVo wxClaimCancelReportVo, String optionCode) {
        String currentState = wxClaimCancelReportVo.getCurrentState();
        String createUserId = wxClaimCancelReportVo.getCreateBy();
        AuthUserVO createUser = userService.getAuthUserByUserId(createUserId);
        String currentUserId = HttpRequestUtil.getUserId();
        if (Objects.equals(currentState, claimCancelReportWorkflow.getFinishStepCode())) {
            throw new MSBizNormalException("", "当前注销理赔报案申请已结案。");
        }
        if (Objects.equals(optionCode, ClaimCancelReportWorkflow.OPTION_WITHDRAW_APPLY)) {
            if (!Objects.equals(createUserId, currentUserId)) {
                throw new MSBizNormalException("", "当前流程节点,用户暂无权限。");
            }
            return;
        }
        if (Objects.equals(currentState, ClaimCancelReportWorkflow.STEP_DATA_CHECK_BY_PIC)) {
            //机构审核阶段
            List<UserRoleVO> orgChannelPcoUserRoleList = userService.getChannelPCOUserRoleList(createUser.getHrOrgId());
            if (CollectionUtils.isEmpty(orgChannelPcoUserRoleList)) {
                throw new MSBizNormalException("", "当前机构暂无渠道PCO,暂无权限,请联系理赔专员处理。");
            }
            boolean isOrgChannelPco = orgChannelPcoUserRoleList.stream()
                    .anyMatch(userRoleVO -> Objects.equals(userRoleVO.getJobNumber(), currentUserId));
            if (!isOrgChannelPco) {
                throw new MSBizNormalException("", "当前流程节点,用户暂无权限。");
            }
            return;
        }

        if (Objects.equals(currentState, ClaimCancelReportWorkflow.STEP_DATA_CHECK_BY_SAFES_CENTER)) {
            //总部审核阶段
            UserDetailVO currentUser = ThreadUserUtil.USER_DETAIL_TL.get();
            if (!permissionUtil.isClaimAdmin(currentUser)) {
                throw new MSBizNormalException("", "当前流程节点,用户暂无权限。");
            }
            return;
        }
        if (!Objects.equals(createUserId, currentUserId)) {
            throw new MSBizNormalException("", "当前流程节点,用户暂无权限。");
        }
    }

    /**
     * 获取注销报案申请选项
     *
     * @param cancelReportStep
     * @param currUser
     * @param currentUser
     * @return
     */
    private BaseWorkflow.Option getCancelReportStepOption(BaseWorkflow.Step cancelReportStep, AuthUserVO currUser, UserDetailVO currentUser) {
        //如果是理赔专员  理赔专员注销选项
        if (permissionUtil.isClaimAdmin(currentUser)) {
            return claimCancelReportWorkflow.getStepOption(cancelReportStep, ClaimCancelReportWorkflow.OPTION_CODE_SETTLEMENT_CANCEL_REPORT);
        }
        //如果是总部角色 提交资料至总部审核选项
        if (permissionUtil.isHeadRole(currentUser)) {
            return claimCancelReportWorkflow.getStepOption(cancelReportStep, ClaimCancelReportWorkflow.OPTION_CODE_SUBMIT_DATA_TO_SAFES_CENTER);
        }
        //如果分支角色 存在渠道PCO 提交资料至机构审核选项
        AuthUserVO channelPCO = userService.getChannelPCOByHrOrgId(currUser.getHrOrgId());
        if (Objects.nonNull(channelPCO)) {
            return claimCancelReportWorkflow.getStepOption(cancelReportStep, ClaimCancelReportWorkflow.OPTION_CODE_SUBMIT_DATA_TO_PIC);
        }
        //如果分支角色 不存在渠道PCO 提交资料至总部审核选项
        return claimCancelReportWorkflow.getStepOption(cancelReportStep, ClaimCancelReportWorkflow.OPTION_CODE_SUBMIT_DATA_TO_SAFES_CENTER);
    }

    private WxClaimCancelReportVo convertToClaimCancelReportVo(SmClaimCancelReport smClaimCancelReport) {
        WxClaimCancelReportVo wxClaimCancelReportVo = new WxClaimCancelReportVo();
        BeanUtils.copyProperties(smClaimCancelReport, wxClaimCancelReportVo);
        return wxClaimCancelReportVo;
    }

    /**
     * 保存注销理赔报案流程
     *
     * @param loginUserId
     * @param claimCancelReportId
     * @param currentStep
     * @param option
     */
    private SmClaimCancelReportProgress saveSmClaimCancelReportProgress(String loginUserId, Integer claimCancelReportId, BaseWorkflow.Step currentStep, BaseWorkflow.Option option) {
        SmClaimCancelReportProgress smClaimCancelReportProgress = new SmClaimCancelReportProgress();
        smClaimCancelReportProgress.setClaimCancelReportId(claimCancelReportId);
        smClaimCancelReportProgress.setStepCode(currentStep.getSCode());
        smClaimCancelReportProgress.setStepName(currentStep.getSName());
        smClaimCancelReportProgress.setOptionCode(option.getOCode());
        smClaimCancelReportProgress.setOptionName(option.getOName());
        smClaimCancelReportProgress.setOptionType(option.getOType());
        smClaimCancelReportProgress.setOptionValue(option.getOValue());
        smClaimCancelReportProgress.setCreateTime(LocalDateTime.now());
        smClaimCancelReportProgress.setUpdateTime(LocalDateTime.now());
        smClaimCancelReportProgress.setCreateBy(loginUserId);
        smClaimCancelReportProgress.setUpdateBy(loginUserId);
        claimCancelReportProgressMapper.insertUseGeneratedKeys(smClaimCancelReportProgress);
        return smClaimCancelReportProgress;
    }

    /**
     * 保存注销理赔报案申请
     *
     * @param claimCancelReportForm
     * @param loginUserId
     * @param nextStep
     * @return
     */
    private SmClaimCancelReport saveSmClaimCancelReport(ClaimCancelReportForm claimCancelReportForm, String loginUserId, BaseWorkflow.Step nextStep) {
        SmClaimCancelReport smClaimCancelReport = new SmClaimCancelReport();
        BeanUtils.copyProperties(claimCancelReportForm, smClaimCancelReport);
        smClaimCancelReport.setCreateTime(LocalDateTime.now());
        List<String> formVouchers = claimCancelReportForm.getVouchers();
        if (CollectionUtils.isNotEmpty(formVouchers)) {
            List<String> vouchers = formVouchers
                    .stream()
                    .map(OssUrlUtil::convertUrl)
                    .collect(Collectors.toList());
            smClaimCancelReport.setVouchers(JSON.toJSONString(vouchers));
        }
        smClaimCancelReport.setUpdateTime(LocalDateTime.now());
        smClaimCancelReport.setCreateBy(loginUserId);
        smClaimCancelReport.setUpdateBy(loginUserId);
        smClaimCancelReport.setCurrentState(nextStep.getSCode());
        smClaimCancelReport.setCurrentResult(nextStep.getSName());
        claimCancelReportMapper.insertUseGeneratedKeys(smClaimCancelReport);
        return smClaimCancelReport;
    }

    /**
     * 更新注销理赔报案申请
     * 补充提交资料至机构审核  补充提交资料至总部审核 额外更新资料信息
     *
     * @param claimCancelReportForm
     * @param nextStep
     * @param updateOption
     * @return
     */
    private SmClaimCancelReport updateSmClaimCancelReport(ClaimCancelReportForm claimCancelReportForm, WxClaimCancelReportVo wxClaimCancelReportVo, BaseWorkflow.Step nextStep, BaseWorkflow.Option updateOption) {
        String currentState = wxClaimCancelReportVo.getCurrentState();
        if (!Objects.equals(currentState, ClaimCancelReportWorkflow.STEP_MORE_DATA_TO_PIC) && !Objects.equals(currentState, ClaimCancelReportWorkflow.STEP_MORE_DATA_TO_SAFES_CENTER)) {
            //如果不是补充更多资料阶段 设置申请表单的资料为null
            claimCancelReportForm = null;
        } else {
            //如果是补充更多资料阶段 申请表单的资料不能为null
            if (!Objects.equals(updateOption.getOCode(), ClaimCancelReportWorkflow.OPTION_SYSTEM_WITHDRAW_APPLY) && Objects.isNull(claimCancelReportForm)) {
                throw new MSBizNormalException("", "补充资料不能为空。");
            }
        }
        String sCode = nextStep.getSCode();
        SmClaimCancelReport smClaimCancelReport = new SmClaimCancelReport();
        BeanUtils.copyProperties(wxClaimCancelReportVo, smClaimCancelReport);
        smClaimCancelReport.setVouchers(wxClaimCancelReportVo.getVouchersString());
        if (Objects.nonNull(claimCancelReportForm)) {
            BeanUtils.copyProperties(claimCancelReportForm, smClaimCancelReport);
            if (Objects.isNull(claimCancelReportForm.getVouchers())) {
                smClaimCancelReport.setVouchers(null);
            } else {
                smClaimCancelReport.setVouchers(JSON.toJSONString(claimCancelReportForm.getVouchers()));
            }
            smClaimCancelReport.setClaimNo(wxClaimCancelReportVo.getClaimNo());
        }
        smClaimCancelReport.setUpdateTime(LocalDateTime.now());
        smClaimCancelReport.setUpdateBy(HttpRequestUtil.getUserId());
        smClaimCancelReport.setCurrentState(sCode);
        smClaimCancelReport.setCurrentResult(nextStep.getSName());
        if (Objects.equals(sCode, claimCancelReportWorkflow.getFinishStepCode())) {
            //如果是结案
            smClaimCancelReport.setFinishResult(updateOption.getOName());
            smClaimCancelReport.setFinishState(updateOption.getOCode());
            smClaimCancelReport.setFinishTime(new Date());
            smClaimCancelReport.setNote(updateOption.getOValue());

        }
        claimCancelReportMapper.updateByPrimaryKey(smClaimCancelReport);
        return smClaimCancelReport;
    }

    /**
     * 校验理赔注销报案申请表单
     * 1.发起人和订单客户经理是否一致 或者为理赔专员
     * 2.判断当前理赔编号是否已有理赔注销报案申请
     * 3.判断理赔状态是否支持发起赔注销报案申请  待提交资料、待提交补充资料、机构审核、总部审核
     *
     * @param claimOrder
     * @param loginUserId
     * @param currentUser
     */
    private void checkClaimCancelReportForm(WxClaimListVo claimOrder, String loginUserId, UserDetailVO currentUser) {
        //发起人和客户经理是否一致
        String claimNo = claimOrder.getClaimNo();
        if (!Objects.equals(claimOrder.getCustomerAdminId(), loginUserId) && !permissionUtil.isClaimAdmin(currentUser)) {
            throw new MSBizNormalException("", "暂无当前理赔流程权限。");
        }

        //断当前理赔编号是否已有理赔注销报案申请
        List<SmClaimCancelReport> noFinishClaimCancelReportList = claimCancelReportMapper.getByClaimNo(claimNo)
                .stream()
                .filter(claimCancelReport -> !Objects.equals(claimCancelReport.getCurrentState(), ClaimCancelReportWorkflow.STEP_FINISH))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(noFinishClaimCancelReportList)) {
            throw new MSBizNormalException("", "当前理赔流程有未完成的注销理赔报案申请。");
        }
        //判断理赔状态是否支持发起赔注销报案申请
        String claimState = claimOrder.getClaimState();
        List<String> supportClaimCancelReportStateList = getSupportClaimCancelReportStateList();
        if (!supportClaimCancelReportStateList.contains(claimState) && !permissionUtil.isClaimAdmin(currentUser)) {
            throw new MSBizNormalException("", "当前理赔流程状态已不支持理赔注销报案申请");
        }
    }

    /**
     * 获取支持进行理赔注销报案申请的理赔状态集合
     *
     * @return
     */
    private List<String> getSupportClaimCancelReportStateList() {
        return Lists.newArrayList(ClaimWorkflow.STEP_DATA_PREPARE,
                ClaimWorkflow.STEP_DATA_PREPARE2,
                ClaimWorkflow.STEP_DATA_CHECK_BY_PIC,
                ClaimWorkflow.STEP_DATA_CHECK_BY_SAFES_CENTER);
    }
}
