package com.cfpamf.ms.insur.weixin.util;

import com.alibaba.fastjson.JSONArray;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @date: 2020/4/30 16:53
 * @description:
 */
@Slf4j
public class HttpPostUtilTest {
    static String url = "https://safes.xiangzhu.org.cn/api/v1/order/callback";

    static List<String[]> list = new ArrayList<>();

    static {

        String jsonStr = "[\"20672080J9RO1225\",null]\n" +
                "[\"20069175D3KG1225\",null]\n" +
                "[\"20897089YJDX1225\",null]\n" +
                "[\"20776620VROV1225\",null]\n" +
                "[\"20069175D3KG1225\",null]\n" +
                "[\"20672080J9RO1225\",null]\n" +
                "[\"20621546IRAB1225\",null]\n" +
                "[\"20956559UKQB1225\",null]\n" +
                "[\"20066693T4KD1225\",null]\n" +
                "[\"20939262C2VP1225\",null]\n" +
                "[\"20929558SFKY1225\",null]\n" +
                "[\"20036850MKHM1225\",null]\n" +
                "[\"201506489BAD1225\",null]\n" +
                "[\"202274013HNL1225\",null]\n" +
                "[\"20235682LNZH1224\",null]";
        String[] split = jsonStr.split("\n");

        for (int i = 0; i < split.length; i++) {
            JSONArray parse = JSONArray.parseArray(split[i]);

            list.add(parse.toArray(new String[]{}));
        }

    }

    static class TestObject {
        private String id;
        private String desc;

        public String getId() {
            return id;
        }

        public String getDesc() {
            return desc;
        }

        public void setId(String id) {
            this.id = id;
        }

        public void setDesc(String desc) {
            this.desc = desc;
        }

        @Override
        public String toString() {
            return "TestObject{" +
                    "id='" + id + '\'' +
                    ", desc='" + desc + '\'' +
                    '}';
        }

    }

    //    public static void main(String[] args) throws Exception {
//        System.out.println("executing request ");
//        for (int i = 0; i < list.size(); i++) {
//            String rem = list.get(i)[1];
//            if (StringUtils.isBlank(rem)) {
//                rem = "";
//            }
//            httpPost(url, list.get(i)[0], rem);
//            Thread.sleep(100);
//        }
//    }
    public static void main(String[] args) {
        String str =
                "200049256SUV1228,HBWY0043\n" +
                        "20032106AF5S1224,ZHNX12499\n" +
                        "20054025MVQP1226,ZHNX14406\n" +
                        "20189180WULJ1228,HBWY0043\n" +
                        "20216440UHYS1228,ZHNX31465\n" +
                        "20219025HMER1228,ZHNX31465\n" +
                        "20243002ZVIO1228,ZHNX31465\n" +
                        "20257576CNIW1225,ZHNX10481\n" +
                        "2026076982B51228,HBWY0043\n" +
                        "20287337E7IA1229,ZHNX14090\n" +
                        "20340464AMKP1228,HBWY0043\n" +
                        "203513098MRW1228,ZHNX31465\n" +
                        "20374290XT0N1228,ZHNX31465\n" +
                        "20383500KUYL1228,HBWY0043\n" +
                        "20396304FZUD1228,HBWY0043\n" +
                        "20408761OR5Z1228,ZHNX31465\n" +
                        "204726033RWX1228,HBWY0043\n" +
                        "20504493AZCZ1228,ZHNX31465\n" +
                        "20528466PYSQ1228,HBWY0043\n" +
                        "20547771RQHW1228,ZHNX31465\n" +
                        "20562639VQSF1228,HBWY0043\n" +
                        "20577388EA0P1228,HBWY0043\n" +
                        "206336954VJB1228,HBWY0043\n" +
                        "20659355GKNQ1229,GSJT0010\n" +
                        "206692193CMA1229,ZHNX14090\n" +
                        "20721615X2QD1228,ZHNX31465\n" +
                        "20726924WBAC1228,ZHNX31465\n" +
                        "20737377XFKN1228,HBWY0043\n" +
                        "20743330W7FY1229,ZHNX14090\n" +
                        "20747403TJGA1229,ZHNX14090\n" +
                        "20785581IBKA1228,ZHNX31465\n" +
                        "20851815BPSD1225,ZHNX10481\n" +
                        "20884309IJ8B1228,ZHNX31465\n" +
                        "20953262ZEYI1230,ZHNX10695\n" +
                        "20967322OVGV1228,HBWY0043\n" +
                        "20971415WCHK1230,ZHNX11758\n" +
                        "20976077B5AT1228,ZHNX31465\n" +
                        "20997913AEHS1229,ZHNX14090\n" +
                        "20000773LIV51230,ZHNX30346\n" +
                        "20001659TNKB1229,ZHNX10124\n" +
                        "20002241QDWJ1229,ZHNX10681\n" +
                        "20005653LT9K1229,HBCA0004\n" +
                        "20005849FVXT1229,ZHNX14090\n" +
                        "20006002ICGU1226,HBBX0013\n" +
                        "20008852CA0F1229,ZHNX07631\n" +
                        "20009389H8SX1229,ZHNX10681\n" +
                        "20012147UBC21229,ZHNX10734\n" +
                        "20013324FFTC1230,ZHNX08450\n" +
                        "200142545EYC1227,GSYD0015\n" +
                        "20014432DB2G1230,HZLQ0008\n" +
                        "20014910ZLCS1228,ZHNX12552\n" +
                        "20015639GCLM1228,ZHNX31465\n" +
                        "20016745IJ5N1229,ZHNX10734\n" +
                        "200173864MQN1224,HBJZ0023\n" +
                        "200180563VJA1229,ZHNX10734\n" +
                        "20019110I1NV1228,ZHNX12943\n" +
                        "20019609JZA01230,ZHNX10695\n" +
                        "20021082ZTI21230,LNBP0017\n" +
                        "20021239IWQA1227,GSYD0009\n" +
                        "20021856J8FZ1229,ZHNX10734\n" +
                        "20023235KM5L1223,SXLS0007\n" +
                        "20024059XW101227,NMNC0002\n" +
                        "20024281NHB91228,NMAH0017\n" +
                        "20025264BBAK1226,ZHNX11173\n" +
                        "20025378GCLO1230,ZHNX09662\n" +
                        "20027597L6IO1228,GSYD0011\n" +
                        "20027703KZDJ1229,ZHNX10734\n" +
                        "20029614NOVB1228,GSAD0039\n" +
                        "200298330AYZ1229,ZHNX06748\n" +
                        "20031020WC6Y1230,HZLQ0008\n" +
                        "20032195WQDQ1230,LNBP0018\n" +
                        "20032639QNKB1228,ZHNX31465\n" +
                        "20035548XCGS1229,ZHNX10734\n" +
                        "200356110ZZO1228,GSAD0039\n" +
                        "20036379YZQC1229,ZHNX10124\n" +
                        "20036850MKHM1225,ZHNX32095\n" +
                        "200380177XC71229,SXYS0016\n" +
                        "20039150HN811228,GSYD0011\n" +
                        "20040030QDRI1228,HBWY0043\n" +
                        "200401924EQP1230,ZHNX11758\n" +
                        "20041050JDGK1229,\n" +
                        "20041699FPX51224,ZHNX32095\n" +
                        "20041883DOFQ1228,ZHNX14059\n" +
                        "2004256453MT1230,HBCA0015\n" +
                        "200431930BBH1229,SXYS0016\n" +
                        "20043224CNJF1229,ZHNX10681\n" +
                        "200434110YNT1228,ZHNX14059\n" +
                        "20044745BGF11230,HBCA0015\n" +
                        "200479644KT81229,ZHNX10734\n" +
                        "20050164Y9DU1231,\n" +
                        "20051066KOYE1229,SXYS0006\n" +
                        "20051960O51H1229,ZHNX10681\n" +
                        "20054462JFEL1230,LNBP0017\n" +
                        "200569229ICD1229,ZHNX10681\n" +
                        "20060394CECW1228,SDLQ0011\n" +
                        "20061302DHQQ1230,ZHNX13726\n" +
                        "20061988GVTR1226,ZHNX14406\n" +
                        "20064690UFFL1228,HBWY0043\n" +
                        "20065760CEEL1229,NMWN0013\n" +
                        "20066381E3C21228,ZHNX12545\n" +
                        "20069831JPAD1230,ZHNX11758\n" +
                        "20070379RYDO1229,ZHNX03941\n" +
                        "20070802MFYW1229,ZHNX10124\n" +
                        "20071418UJIS1228,ZHNX11105\n" +
                        "20072089PSRA1229,ZHNX09634\n" +
                        "20073047QTAQ1226,ZHNX13216\n" +
                        "20073243CX401229,ZHNX14090\n" +
                        "20076085UKK41230,ZHNX30346\n" +
                        "20076169SODM1230,LNBP0031\n" +
                        "20078064QGD31222,ZHNX14090\n" +
                        "20078463VDVE1228,GSYD0011\n" +
                        "20081861MWAW1230,ZHNX13726\n" +
                        "20082661FIJ41228,GSYD0011\n" +
                        "2008269302YO1223,HBJZ0023\n" +
                        "20084487W8XT1230,LNBP0031\n" +
                        "20084924FH5Y1230,ZHNX30181\n" +
                        "200852503YWU1229,GSYD0006\n" +
                        "20085319HVFF1229,ZHNX07343\n" +
                        "20086294ESVD1229,GSYD0006\n" +
                        "200882260MAZ1228,GSAD0039\n" +
                        "20089576PUQN1230,HBCA0004\n" +
                        "20089898HYNO1228,ZHNX31465\n" +
                        "20091644XOUD1228,ZHNX14059\n" +
                        "20092086ARJD1230,ZHNX13386\n" +
                        "20096147AAZN1230,HBCA0015\n" +
                        "200978110BEV1229,ZHNX14090\n" +
                        "20100217YYLA1230,HZLQ0008\n" +
                        "20104375ZH631228,ZHNX12545\n" +
                        "2010439331TN1229,ZHNX09541\n" +
                        "20106439KPF61228,GSAD0039\n" +
                        "201066937SVI1228,ZHNX11105\n" +
                        "20106726V3IH1230,HZLQ0008\n" +
                        "20109306SDNS1229,ZHNX31438\n" +
                        "20110182ATR31229,ZHNX10734\n" +
                        "20110973J9TI1229,ZHNX10734\n" +
                        "20112463INMN1227,NMNC0002\n" +
                        "20117643PCLB1230,ZHNX07894\n" +
                        "20122271OIG01228,GSAD0039\n" +
                        "20123665KOOC1229,ZHNX14090\n" +
                        "20127108UAHC1230,HBMC0009\n" +
                        "201308912JJG1228,GSAD0039\n" +
                        "201317039YQ61230,ZHNX08453\n" +
                        "20132641B4OK1229,ZHNX14090\n" +
                        "20135264HDQB1229,ZHNX14090\n" +
                        "2013855891UF1229,ZHNX14090\n" +
                        "20138580P79L1230,HBCA0015\n" +
                        "20139704LLJO1230,HBCA0015\n" +
                        "201463097TCA1227,NMNC0002\n" +
                        "20147665RBFJ1229,ZHNX13560\n" +
                        "20148137U41U1226,ZHNX13216\n" +
                        "2014976401YU1223,ZHNX09414\n" +
                        "20150548QLFW1224,ZHNX09792\n" +
                        "20151068AS8E1228,HBGP0002\n" +
                        "20151367UI1B1230,NMAH0005\n" +
                        "20155411TW6J1228,ZHNX13423\n" +
                        "20156119S7DB1225,SXLS0017\n" +
                        "20157775HUHY1230,ZHNX10695\n" +
                        "201626564N0I1222,HBST0015\n" +
                        "201626835EHZ1230,ZHNX10695\n" +
                        "201645247TEB1228,ZHNX12545\n" +
                        "20165595JIHA1230,ZHNX13726\n" +
                        "20166202WF3U1229,ZHNX14090\n" +
                        "20168657O2TC1228,GSYD0011\n" +
                        "20169095RSSI1229,HBCA0004\n" +
                        "201711915SI91229,GSJT0010\n" +
                        "20176793HAZF1228,HBCA0007\n" +
                        "20179388NTUQ1228,ZHNX12545\n" +
                        "201836906GUA1229,ZHNX10734\n" +
                        "20183782PAG21229,ZHNX10124\n" +
                        "20184990VIBZ1228,ZHNX06761\n" +
                        "20185679SCFS1229,ZHNX10734\n" +
                        "20186854PUWR1229,ZHNX10124\n" +
                        "20191377FVLR1228,GSAD0039\n" +
                        "20191478PEA61227,ZHNX12652\n" +
                        "20194294KFOO1226,ZHNX13801\n" +
                        "20195063LOSW1230,ZHNX12123\n" +
                        "20196455OQBQ1230,ZHNX10695\n" +
                        "20197031KQMQ1229,GSYD0006\n" +
                        "20197898RRC11230,ZHNX07894\n" +
                        "20199409DLZA1230,ZHNX30181\n" +
                        "20199970BDNB1230,ZHNX07617\n" +
                        "20202501BGH51223,ZHNX09064\n" +
                        "20203243RI1H1223,SXLS0007\n" +
                        "20203531R0161230,ZHNX30346\n" +
                        "20204099WAGQ1230,ZHNX13726\n" +
                        "20204177WHD41228,GSAD0039\n" +
                        "20205105ST9Z1229,ZHNX10734\n" +
                        "20206411EMWP1228,ZHNX14059\n" +
                        "20207455UC721226,ZHNX11173\n" +
                        "20208745IHTJ1228,ZHNX12943\n" +
                        "20210362OIDA1230,LNBP0031\n" +
                        "20211508OKMD1228,ZHNX31438\n" +
                        "20212188K9JZ1228,NMAH0012\n" +
                        "20212587ZNTX1227,GSYD0015\n" +
                        "20212825KBA41227,HBBY0014\n" +
                        "20215787LPDT1230,ZHNX10695\n" +
                        "20216010S5KQ1230,ZHNX13167\n" +
                        "20216510FVMM1229,GSYD0013\n" +
                        "20218995MFCU1228,ZHNX11105\n" +
                        "202210428CBW1230,LNBP0031\n" +
                        "20222182N7PQ1229,ZHNX07343\n" +
                        "20227180I1NJ1229,ZHNX14090\n" +
                        "20227222C1TH1229,ZHNX10734\n" +
                        "20227759LDFI1222,ZHNX13201\n" +
                        "20228914RFNE1229,ZHNX14090\n" +
                        "20231164IEH31230,ZHNX08450\n" +
                        "20238767KS671228,GSYD0011\n" +
                        "20239122CAZ51228,ZHNX11105\n" +
                        "2023951605OG1226,ZHNX11173\n" +
                        "20240362I22C1223,ZHNX09414\n" +
                        "202458368XLB1229,NMWN0003\n" +
                        "20246947BQ9L1229,ZHNX10734\n" +
                        "20248576JOYB1228,GSYD0015\n" +
                        "20249232ZV661230,HBMC0009\n" +
                        "20255975HMEK1230,HBCA0004\n" +
                        "2025717533QX1228,SDLQ0011\n" +
                        "20258379MUJY1230,HZLQ0008\n" +
                        "20261240PLSR1229,ZHNX10734\n" +
                        "20261583TBGF1226,HBBX0013\n" +
                        "20265018H1BA1228,HBCA0007\n" +
                        "20265200BTFH1228,ZHNX11105\n" +
                        "20265520GHCX1229,SXYS0006\n" +
                        "202659397LSW1223,SXLS0007\n" +
                        "20266300HILO1223,SXLS0007\n" +
                        "202688029RVN1229,SXYS0016\n" +
                        "20271034WHYN1229,SXYS0016\n" +
                        "20271143UBZL1230,ZHNX08450\n" +
                        "20271708JHXZ1226,HBXD0005\n" +
                        "20272297FGCB1229,GSYD0006\n" +
                        "20272810Y1QN1230,LNSZ0006\n" +
                        "20273475JLVF1230,ZHNX10695\n" +
                        "20274635CMVO1229,ZHNX31438\n" +
                        "20278316NLIZ1229,ZHNX10124\n" +
                        "202805470PSX1229,ZHNX31639\n" +
                        "202810899KFN1226,ZHNX14406\n" +
                        "20281138U5LO1230,HZLQ0008\n" +
                        "20282457CSBT1230,ZHNX07894\n" +
                        "20285560BNLV1224,ZHNX07894\n" +
                        "20285948O67H1229,ZHNX10734\n" +
                        "20286749JUJE1230,LNBP0017\n" +
                        "20287020XDUG1230,GSTZ0004\n" +
                        "202889598LYG1228,ZHNX31689\n" +
                        "20290560KOQ01230,HBCA0015\n" +
                        "20290750NO7A1228,ZHNX09066\n" +
                        "20292192RHYS1230,GSTZ0004\n" +
                        "20293271W48C1230,GSTZ0004\n" +
                        "20293325S8CK1230,HZLQ0008\n" +
                        "202944353U5O1230,ZHNX30346\n" +
                        "20295039S4ON1230,ZHNX30346\n" +
                        "20296767WFIW1229,ZHNX09541\n" +
                        "20296935PIPN1229,ZHNX08588\n" +
                        "202993428PHX1230,HZLQ0008\n" +
                        "203027769C041230,ZHNX30596\n" +
                        "20302887AMLJ1230,HZLQ0008\n" +
                        "203050764TVR1230,ZHNX13726\n" +
                        "20306230LVYK1229,ZHNX14090\n" +
                        "20306428ZHDG1228,ZHNX31438\n" +
                        "20307271V9DY1229,ZHNX08588\n" +
                        "20311831NDXG1230,ZHNX10695\n" +
                        "20311990GW0W1227,GSWY0011\n" +
                        "20316058M0OM1229,ZHNX14090\n" +
                        "203163026UWT1225,ZHNX08417\n" +
                        "20317370CJYR1230,\n" +
                        "20321143FXCN1230,GSTZ0004\n" +
                        "20322167CBUN1229,ZHNX08588\n" +
                        "20323370VGTK1230,LNBP0031\n" +
                        "20323471SWBM1227,NMNC0002\n" +
                        "20323780GUCJ1228,ZHNX11105\n" +
                        "20323886HQBB1229,ZHNX09634\n" +
                        "20324457J2NN1227,NMNC0002\n" +
                        "20328132N84K1230,HBMC0009\n" +
                        "20328738LI8H1229,GSYD0006\n" +
                        "20332466Z3AC1230,ZHNX08453\n" +
                        "20333630SRNT1223,\n" +
                        "20335611NHXT1228,ZHNX31465\n" +
                        "20337059XSCN1229,ZHNX10734\n" +
                        "20338785FPKN1230,ZHNX13386\n" +
                        "20339382NH6J1230,ZHNX13167\n" +
                        "20340623WTTN1228,ZHNX14285\n" +
                        "20340888UH1I1228,ZHNX12545\n" +
                        "20341739ATOJ1228,HBCA0007\n" +
                        "20343206G4X41229,ZHNX10734\n" +
                        "20351020QOTN1228,ZHNX12943\n" +
                        "20351281SRUJ1228,HBWY0045\n" +
                        "20351469IPDX1224,SXLS0016\n" +
                        "20358295GLJ01224,SXJX0016\n" +
                        "20358749R5RK1229,ZHNX14090\n" +
                        "203615381URM1225,ZHNX08417\n" +
                        "20362258H9S61229,ZHNX14090\n" +
                        "203633642WR61230,ZHNX09414\n" +
                        "2036381217PI1228,ZHNX14059\n" +
                        "20364281D5ID1230,ZHNX13167\n" +
                        "20367382H8EF1225,ZHNX08417\n" +
                        "203698405CXL1226,ZHNX08240\n" +
                        "20369935YOLR1230,ZHNX10695\n" +
                        "20370922DDGF1229,GSYD0013\n" +
                        "20371139LJZO1230,ZHNX10695\n" +
                        "20372321APFU1227,SXLS0017\n" +
                        "20374271UWB01228,ZHNX14059\n" +
                        "20376713RWWX1228,ZHNX11105\n" +
                        "20378663Y2NC1229,ZHNX07343\n" +
                        "20387415PDOP1230,ZHNX13167\n" +
                        "20388203J8IQ1230,ZHNX30346\n" +
                        "20391235VCDF1225,ZHNX08417\n" +
                        "20393047V5CU1228,SDLQ0011\n" +
                        "20396931H0Q31230,LNBP0017\n" +
                        "20400121UAGK1228,ZHNX31465\n" +
                        "20400970WBS41226,ZHNX13146\n" +
                        "20401127NZWX1230,HBCA0015\n" +
                        "204020887HLY1228,ZHNX11105\n" +
                        "20402181NZ781230,HBCA0015\n" +
                        "20403274VB1P1229,ZHNX10734\n" +
                        "20403293GAR11228,ZHNX31438\n" +
                        "20403453FEQ31230,ZHNX30346\n" +
                        "20403647GMNB1229,ZHNX10734\n" +
                        "20403972KVKJ1230,LNBP0031\n" +
                        "20404522PDJH1227,ZHNX08240\n" +
                        "204046217WAV1229,ZHNX10734\n" +
                        "20404866LRA91230,HNSP0023\n" +
                        "20407977TARA1229,SXYS0016\n" +
                        "20408127HEAH1230,ZHNX30346\n" +
                        "20409002FFPC1229,GSJT0010\n" +
                        "20409098P0PJ1230,HBCA0004\n" +
                        "20413577HCDT1230,ZHNX12123\n" +
                        "20413585MRIP1229,ZHNX10734\n" +
                        "20413764MDVI1229,ZHNX08588\n" +
                        "20416862IWVU1224,ZHNX31447\n" +
                        "20418194LJ5F1230,FJFA0019\n" +
                        "20419854N7GZ1230,LNBP0031\n" +
                        "20420046UMS61227,ZHNX31381\n" +
                        "20420954PMOE1229,ZHNX13560\n" +
                        "20421207XJ8U1230,ZHNX30346\n" +
                        "20421468IYEG1230,ZHNX08453\n" +
                        "20422288LXY41230,HBMC0009\n" +
                        "204224118UY51228,GSAD0039\n" +
                        "20422461EVCI1229,ZHNX31438\n" +
                        "20424026RUUY1229,ZHNX31639\n" +
                        "20425015EGLX1230,ZHNX08453\n" +
                        "20425182D9U91229,ZHNX14090\n" +
                        "20427420FMPB1230,ZHNX10695\n" +
                        "20427890GLOJ1229,SXYS0006\n" +
                        "20430444SPJE1230,ZHNX08453\n" +
                        "20430956IEVP1230,LNSZ0006\n" +
                        "20431801RXRL1229,ZHNX10734\n" +
                        "20432325SZRX1229,ZHNX10734\n" +
                        "20435077WNYG1230,NMAH0012\n" +
                        "204356769QHE1230,HZLQ0008\n" +
                        "204362782GW81229,HBCA0004\n" +
                        "20436657JCGM1229,ZHNX10734\n" +
                        "20438547C0BS1228,ZHNX11105\n" +
                        "20438976A7NL1229,ZHNX10734\n" +
                        "20443171KQ9Y1226,ZHNX10887\n" +
                        "20443500UMPV1229,ZHNX03941\n" +
                        "20443549LYLN1229,GSYD0006\n" +
                        "20446934JWGO1231,\n" +
                        "20447234LIMC1230,ZHNX03939\n" +
                        "20448501ESAZ1229,NMWN0013\n" +
                        "204519934HQG1228,HBWY0043\n" +
                        "20456473MCFL1229,ZHNX10734\n" +
                        "204565368CBE1223,SXLS0007\n" +
                        "204567993CXN1229,ZHNX14090\n" +
                        "20458694JLGJ1229,ZHNX10734\n" +
                        "20459404M3KH1224,HBJZ0023\n" +
                        "20459610DWNQ1229,ZHNX10681\n" +
                        "20465002VTIM1223,ZHNX32139\n" +
                        "20465782XZA11230,ZHNX30346\n" +
                        "20466516UELM1225,HBCA0022\n" +
                        "20467130SRAF1230,ZHNX07894\n" +
                        "20467479RJIP1229,ZHNX10681\n" +
                        "20467715GOVY1229,GSJT0010\n" +
                        "20468029DLDY1228,HBWY0046\n" +
                        "20469723BUU51230,HBCA0015\n" +
                        "20472509FWBZ1225,SXTG0021\n" +
                        "20472925VUNT1228,ZHNX14091\n" +
                        "20473134WXOZ1230,ZHNX10695\n" +
                        "20473316AX2A1230,ZHNX12123\n" +
                        "20475025WGXC1229,GSYD0006\n" +
                        "20477598F4UT1230,ZHNX09414\n" +
                        "204795556DZX1229,GSYD0006\n" +
                        "20480306VKWJ1230,ZHNX30346\n" +
                        "20482889NXN61229,ZHNX10124\n" +
                        "20483487805J1230,HBCA0004\n" +
                        "20485262VZNL1228,ZHNX31689\n" +
                        "2048702523CM1227,GSWY0011\n" +
                        "20488694YCD01229,ZHNX10124\n" +
                        "20489479VEBS1228,GSAD0039\n" +
                        "20489910LW5E1230,ZHNX03942\n" +
                        "204910951XYT1230,ZHNX08453\n" +
                        "20492308GGQT1229,ZHNX10734\n" +
                        "20492367QWXS1227,GSWY0011\n" +
                        "204923990EUU1230,HBCA0015\n" +
                        "20492701VTHJ1229,GSYD0006\n" +
                        "20494747ZKMC1228,ZHNX31465\n" +
                        "20495741UCCQ1229,ZHNX31639\n" +
                        "20495794RX3I1230,LNBP0017\n" +
                        "20499114NR8N1229,ZHNX13560\n" +
                        "20499282HUYP1225,HBCA0022\n" +
                        "20499335QHA61230,ZHNX30346\n" +
                        "20500321WDND1230,GSTZ0022\n" +
                        "205019426NHN1230,ZHNX07344\n" +
                        "20503617MVGY1228,ZHNX12545\n" +
                        "20505871Y6DO1230,GSTZ0013\n" +
                        "20506716STSJ1228,GSAD0039\n" +
                        "20507664FXIV1224,ZHNX10481\n" +
                        "20508097VMGO1229,ZHNX13560\n" +
                        "20510110U7001230,ZHNX08123\n" +
                        "20510192USMR1229,ZHNX14090\n" +
                        "205123430JE01225,ZHNX09085\n" +
                        "20512584OPIG1228,ZHNX11105\n" +
                        "20512913SFSE1228,GSAD0039\n" +
                        "20512991QHVJ1230,ZHNX10695\n" +
                        "205134169CUE1227,ZHNX08417\n" +
                        "20513531ARSE1228,ZHNX31689\n" +
                        "20516113SDK71230,HBCA0004\n" +
                        "20517669H8Y61228,HBWY0043\n" +
                        "20517972QZRO1228,ZHNX08703\n" +
                        "20518106P4V01228,GSYD0011\n" +
                        "205188132OXH1228,ZHNX11105\n" +
                        "20520951YFQT1228,GSAD0039\n" +
                        "205232994EFO1230,ZHNX10695\n" +
                        "20524479W1PQ1230,HZLQ0008\n" +
                        "20524502GN9Q1229,ZHNX10681\n" +
                        "20525683AHT01230,HZLQ0008\n" +
                        "20529669Y0ZN1228,ZHNX11105\n" +
                        "20533562QETR1229,ZHNX10734\n" +
                        "20539003AIA71229,ZHNX10681\n" +
                        "20540366LFVB1229,GSYD0006\n" +
                        "2054048147X71229,ZHNX13560\n" +
                        "205433475UW11230,ZHNX08453\n" +
                        "20546501MOW61228,GSYD0011\n" +
                        "20548181YEW71228,ZHNX31465\n" +
                        "205522667HZ41228,GSYD0006\n" +
                        "205533698CX51230,HZLQ0008\n" +
                        "20554168COBC1227,ZHNX12545\n" +
                        "20555740DPZB1230,LNBP0031\n" +
                        "20557622LA6M1230,ZHNX13167\n" +
                        "205578699GTD1229,NMAH0018\n" +
                        "20558165SX1Q1230,GSTZ0004\n" +
                        "20559474OAUD1230,HZLQ0008\n" +
                        "20560688A7QO1230,ZHNX11963\n" +
                        "20561132PZIJ1229,ZHNX10734\n" +
                        "205617654BUT1228,GSYD0006\n" +
                        "20561954BRJQ1227,NMNC0002\n" +
                        "20564527YHQ41228,ZHNX11105\n" +
                        "20565514NENH1229,ZHNX10124\n" +
                        "205657852OPA1229,SXYS0016\n" +
                        "20570136XPJD1226,HBBX0013\n" +
                        "20571375UPBC1230,ZHNX13167\n" +
                        "20573146S0MB1229,SXYS0016\n" +
                        "20573147BO4J1230,ZHNX07344\n" +
                        "20573264GXWT1230,LNBP0031\n" +
                        "20573832KVIZ1228,GSAD0039\n" +
                        "20579743SLKR1229,ZHNX31438\n" +
                        "205798470CU81230,LNSZ0006\n" +
                        "20580444R1VP1230,LNBP0031\n" +
                        "20581419ZIJY1230,NMAH0005\n" +
                        "20583447DZ0N1230,HBCA0015\n" +
                        "205846865FOJ1230,ZHNX10695\n" +
                        "20584795NKXQ1227,GSYD0015\n" +
                        "20585556H2801228,ZHNX31689\n" +
                        "205860994Z351230,ZHNX13726\n" +
                        "20586683EKJ61229,GSYD0006\n" +
                        "20588169YWJG1223,SXLS0007\n" +
                        "205929622R3I1228,GSYD0011\n" +
                        "20593298J4O91230,ZHNX10695\n" +
                        "20593449KGZ01228,ZHNX31412\n" +
                        "20594740GEUY1229,ZHNX14090\n" +
                        "205952177DN11230,LNBP0031\n" +
                        "20596112ZZNC1230,NMAH0012\n" +
                        "20596330MA7W1230,NMAH0012\n" +
                        "20599492WQUA1230,ZHNX13167\n" +
                        "20599923EUCL1227,NMNC0002\n" +
                        "20603256NQ2F1228,GSYD0011\n" +
                        "20603479RUKA1229,ZHNX10734\n" +
                        "20603604MJ6F1225,ZHNX08417\n" +
                        "20606502YGXG1230,HZLQ0008\n" +
                        "20606969R7IJ1229,ZHNX31438\n" +
                        "20607351FMYV1229,ZHNX10734\n" +
                        "20608222EO3H1229,ZHNX31438\n" +
                        "206084443VEW1228,GSYD0011\n" +
                        "206089053HFE1227,GSYD0015\n" +
                        "20609836VO6V1229,ZHNX10124\n" +
                        "206110222GJY1229,GSTZ0001\n" +
                        "20613588I9571229,ZHNX10681\n" +
                        "20614324XFXQ1229,ZHNX10734\n" +
                        "20614467K21E1229,ZHNX13560\n" +
                        "20614634ZMZU1230,LNSZ0006\n" +
                        "20614635M0BA1230,ZHNX10695\n" +
                        "20615538IIDW1230,ZHNX30346\n" +
                        "20615712EOIB1230,ZHNX08453\n" +
                        "206185136MQP1227,NMNC0002\n" +
                        "20618708WMZ01230,HNSP0023\n" +
                        "20619506IT4N1228,GSAD0039\n" +
                        "20623471YOJE1229,ZHNX14090\n" +
                        "206245654GZQ1223,ZHNX07894\n" +
                        "20629072LUJJ1230,ZHNX10695\n" +
                        "20629207UOGJ1229,ZHNX10734\n" +
                        "206295391Z2U1230,ZHNX31669\n" +
                        "20630567TBCK1225,LNBP0018\n" +
                        "206309297CPR1225,SXLS0017\n" +
                        "20633838MSG51228,ZHNX11105\n" +
                        "20634171EIUS1228,ZHNX11105\n" +
                        "2063476434MA1227,GSWY0017\n" +
                        "20636700QEXV1229,ZHNX09541\n" +
                        "20636885TU9L1229,SXYS0016\n" +
                        "20637053LFNX1230,ZHNX13167\n" +
                        "20638885SE2A1230,HZLQ0008\n" +
                        "20641300Q3AI1227,ZHNX08417\n" +
                        "20641795JNY31229,ZHNX14090\n" +
                        "20642874BDZE1230,FJFA0019\n" +
                        "20643039VWZD1230,ZHNX08450\n" +
                        "206433787Q2X1230,LNBP0031\n" +
                        "2064349092ES1224,HBJZ0023\n" +
                        "20645205CLRM1229,ZHNX07343\n" +
                        "20645250I3VE1228,GSYD0011\n" +
                        "20645950IWOM1230,ZHNX08453\n" +
                        "20646146FGDT1225,SXLS0017\n" +
                        "20646794CO7M1229,ZHNX10734\n" +
                        "20647281ZFAK1226,ZHNX11695\n" +
                        "20648473JO2I1230,HBCA0015\n" +
                        "20648852AQK01230,ZHNX10695\n" +
                        "206501513XMZ1223,SXLS0007\n" +
                        "20652167RYAZ1230,ZHNX08453\n" +
                        "20652576P43K1229,ZHNX10734\n" +
                        "20654606TZCF1230,HZLQ0008\n" +
                        "20657817TSW71228,ZHNX12943\n" +
                        "206581105JXF1230,HZLQ0008\n" +
                        "206588336JGQ1226,ZHNX31859\n" +
                        "20659455XPLC1227,ZHNX11538\n" +
                        "20659925SQWJ1228,GSAD0039\n" +
                        "20666981LOUT1228,ZHNX09085\n" +
                        "20668704CISD1230,NMAH0005\n" +
                        "20679874DQ5L1230,ZHNX08453\n" +
                        "20679902OBZN1230,ZHNX07894\n" +
                        "20680513BAPK1229,SXYS0016\n" +
                        "20681867ANRI1230,LNBP0031\n" +
                        "206831345KI01231,\n" +
                        "20685867GIWC1228,GSAD0039\n" +
                        "20688972SJ4M1230,ZHNX08450\n" +
                        "20689196BCGD1227,HBBY0008\n" +
                        "20690533RAEN1230,ZHNX07344\n" +
                        "20691158VHEU1229,SXYS0016\n" +
                        "20692677BWOJ1230,HBCA0004\n" +
                        "20695443QY341230,ZHNX30346\n" +
                        "20695702ZUHA1230,ZHNX08453\n" +
                        "20701410KOZV1230,ZHNX07344\n" +
                        "20701737IQIL1230,ZHNX10695\n" +
                        "20705057M1SH1222,ZHNX09403\n" +
                        "20706978LFHC1229,ZHNX13560\n" +
                        "207106316DO41229,ZHNX31689\n" +
                        "20710701HXBK1228,HBWY0043\n" +
                        "20714359RFHI1227,GSWY0011\n" +
                        "20714360OTXL1230,HBCA0015\n" +
                        "20715555MZHD1223,ZHNX09064\n" +
                        "20716763IGSC1228,GSAD0039\n" +
                        "20716880A4U01230,ZHNX13167\n" +
                        "20717214H68H1229,ZHNX14090\n" +
                        "20720746776E1230,NMAH0012\n" +
                        "20722446UKYJ1228,ZHNX11105\n" +
                        "20726593LZTF1226,ZHNX11173\n" +
                        "20727506PHLG1226,ZHNX11173\n" +
                        "20729535JO5G1228,HBWY0024\n" +
                        "20732345ME1L1230,ZHNX30181\n" +
                        "207328062EFD1224,HBXD0005\n" +
                        "207340953HLL1228,ZHNX11105\n" +
                        "20737611BXJQ1229,ZHNX14090\n" +
                        "207390899ZAJ1230,ZHNX09860\n" +
                        "2074064338QY1230,HBMC0009\n" +
                        "20741836BKXQ1226,ZHNX30175\n" +
                        "20742311QQVE1229,GSYD0006\n" +
                        "20743388ZKZJ1230,ZHNX08453\n" +
                        "20744875IA8I1230,HZLQ0008\n" +
                        "20745121MPLW1227,ZHNX13854\n" +
                        "20745962OQM71227,GSYD0015\n" +
                        "20748568EU5G1229,ZHNX07343\n" +
                        "20749507Y1Y31230,HBMC0009\n" +
                        "20752363JRBD1229,GSYD0013\n" +
                        "20754433YOT41223,SXLS0007\n" +
                        "207564450EHQ1223,HBLR0008\n" +
                        "20757286PCFA1230,LNBP0031\n" +
                        "20758106WL1H1227,NMNC0002\n" +
                        "20759835LGEE1229,GSYD0006\n" +
                        "207619959KGC1229,ZHNX10734\n" +
                        "207648464DOO1223,SXLS0007\n" +
                        "20765597ULLX1229,ZHNX10734\n" +
                        "20767817N1BL1229,ZHNX31438\n" +
                        "20767864QQ4F1229,ZHNX08588\n" +
                        "20778723CNEG1222,HBST0015\n" +
                        "20781677G28Y1227,ZHNX08240\n" +
                        "20783418DJZB1230,HBCA0015\n" +
                        "207859221KHN1226,ZHNX07847\n" +
                        "207891943LXR1230,LNSZ0006\n" +
                        "20792289NWXO1230,ZHNX11758\n" +
                        "20794221IQZV1230,ZHNX13726\n" +
                        "20794443WJVO1225,ZHNX10481\n" +
                        "207978294K8D1228,ZHNX31465\n" +
                        "20798358KH7G1230,HBCA0004\n" +
                        "207988752PYP1224,ZHNX07894\n" +
                        "20798893PXYZ1229,GSYD0006\n" +
                        "20800749NVOM1230,ZHNX09414\n" +
                        "208019588N4V1229,ZHNX10681\n" +
                        "20812247GY4P1229,ZHNX09541\n" +
                        "20813180Y2FI1230,ZHNX30346\n" +
                        "20814890ZFUL1228,GSAD0039\n" +
                        "20816407DN081224,HBXD0005\n" +
                        "20817575SIYJ1230,HBCX0010\n" +
                        "20818323QVEA1229,ZHNX14090\n" +
                        "20818869ADFS1230,LNSZ0006\n" +
                        "20819403QFOS1224,ZHNX07894\n" +
                        "208221195CCP1229,ZHNX13560\n" +
                        "20822135Z3LN1225,HBXX0012\n" +
                        "20822822YPDB1230,HZLQ0008\n" +
                        "20824936IZJ31230,HZLQ0008\n" +
                        "20825113ENWG1229,ZHNX13560\n" +
                        "20829398OKCD1227,NMNC0002\n" +
                        "20831239UXAJ1228,GSAD0039\n" +
                        "20833876JLWO1228,GSAD0039\n" +
                        "20834458EWZO1225,HBMC0010\n" +
                        "20835359HYPA1230,LNBP0031\n" +
                        "20836171O73V1229,ZHNX14090\n" +
                        "20840867NTSD1229,ZHNX31438\n" +
                        "20844062LTY41229,ZHNX09634\n" +
                        "20845689TUMD1228,GSAD0039\n" +
                        "20845753XLUG1229,ZHNX10681\n" +
                        "20846021WNYD1230,LNBP0031\n" +
                        "208473108MRH1230,NMAH0005\n" +
                        "208475240QTF1229,ZHNX10734\n" +
                        "20848439NVVY1230,ZHNX10695\n" +
                        "20848877ICLL1226,ZHNX07847\n" +
                        "208502959NQY1230,ZHNX08453\n" +
                        "20850312AVVM1229,ZHNX10734\n" +
                        "20850799WE291230,ZHNX07894\n" +
                        "20852478UIMV1230,HBCA0004\n" +
                        "208531532XAC1229,NMWN0013\n" +
                        "20853587VWJ81229,ZHNX10734\n" +
                        "20853704TACE1227,NMNC0002\n" +
                        "20854088TNYD1228,GSAD0039\n" +
                        "20854153ZH6W1230,ZHNX08946\n" +
                        "20856808JTD21229,ZHNX08588\n" +
                        "20857248IO7S1229,NMWN0003\n" +
                        "20858295NAKB1228,ZHNX31465\n" +
                        "20858546MMJP1227,NMNC0002\n" +
                        "20859201OLHB1224,ZHNX07894\n" +
                        "208676811ZJJ1229,ZHNX08588\n" +
                        "20867824FIIY1222,CNBJ0561\n" +
                        "20870979VLYM1228,ZHNX31465\n" +
                        "20871237GDSU1230,HBMC0009\n" +
                        "20873846ZGZD1229,ZHNX10681\n" +
                        "20874737KR3N1229,GSYD0006\n" +
                        "20876335UDW81230,ZHNX08453\n" +
                        "20876430CVVG1225,HBXX0012\n" +
                        "20877918IR7K1228,ZHNX11105\n" +
                        "20878418JTNF1229,GSJT0028\n" +
                        "20878697OVEH1230,GSTZ0004\n" +
                        "20880736HGFU1228,GSAD0039\n" +
                        "20882755P3NP1230,ZHNX30346\n" +
                        "208837758T521230,ZHNX12123\n" +
                        "20884897DS961230,HZLQ0008\n" +
                        "208868151QKL1223,SXLS0007\n" +
                        "2088704569N71229,ZHNX14090\n" +
                        "20887569B03E1227,GSWY0011\n" +
                        "20888196XKBM1227,ZHNX08240\n" +
                        "20888754QAGE1230,HBCA0015\n" +
                        "20894373GKBR1230,ZHNX08453\n" +
                        "20895039EUQ01224,HBJZ0023\n" +
                        "20895867ZECF1230,ZHNX30346\n" +
                        "20896741B5PU1230,HBMC0009\n" +
                        "20897448VZSL1230,HBCA0015\n" +
                        "20899452WSIC1222,ZHNX14090\n" +
                        "20900006CIPZ1229,ZHNX09066\n" +
                        "20902194TYRK1230,LNSZ0006\n" +
                        "20902204IQGO1230,HBMC0009\n" +
                        "20904043CLRX1227,ZHNX08487\n" +
                        "20904574JW1Y1229,ZHNX08588\n" +
                        "209065291QXZ1228,GSAD0039\n" +
                        "209079172PQY1230,GSTZ0004\n" +
                        "20909218YWUP1230,GSTZ0004\n" +
                        "20911282KFXG1228,GSAD0039\n" +
                        "209123244I7J1226,ZHNX11173\n" +
                        "20914540UD951230,ZHNX09414\n" +
                        "20915500DRWA1230,ZHNX10695\n" +
                        "20920247BDKT1229,ZHNX10734\n" +
                        "20920302XY8V1229,ZHNX10681\n" +
                        "20921898JNRK1228,GSAD0039\n" +
                        "20922353C4NU1229,SXYS0016\n" +
                        "20922372GKSN1229,ZHNX10681\n" +
                        "20922609MJOC1228,ZHNX11105\n" +
                        "20924585FBE61228,ZHNX08936\n" +
                        "20924644TPDC1228,ZHNX11105\n" +
                        "20927803VTIZ1230,ZHNX09414\n" +
                        "20928258DCW91228,GSYD0011\n" +
                        "209287581OYH1230,ZHNX10695\n" +
                        "2092955025WC1228,ZHNX14059\n" +
                        "20930407DKZU1229,ZHNX10734\n" +
                        "20930477EA7H1229,ZHNX14090\n" +
                        "20932526RORC1228,ZHNX31465\n" +
                        "20935846UIEF1228,ZHNX14059\n" +
                        "20936507FETQ1227,ZHNX07609\n" +
                        "20937406SOOK1229,ZHNX10734\n" +
                        "209387034OYG1230,HZLQ0008\n" +
                        "20939251RRUB1230,HBCA0004\n" +
                        "20939596ZXJS1228,ZHNX12545\n" +
                        "209407164WK91230,ZHNX09662\n" +
                        "20941246QLI41229,ZHNX14090\n" +
                        "20943345TZQQ1228,ZHNX12545\n" +
                        "20944088G72O1230,ZHNX30346\n" +
                        "20945271HMVI1228,ZHNX11105\n" +
                        "20945287OULM1228,ZHNX14059\n" +
                        "209455515KEF1229,ZHNX14090\n" +
                        "209473122MMM1229,ZHNX31639\n" +
                        "20947377YPUD1228,ZHNX14059\n" +
                        "20947417NF271230,ZHNX13167\n" +
                        "20950410TAGA1229,GSYD0006\n" +
                        "20955325CWYU1230,HBCA0004\n" +
                        "20956490QXZZ1228,ZHNX09085\n" +
                        "20957500RMYD1227,NMNC0002\n" +
                        "209582250XHK1230,ZHNX13167\n" +
                        "20958672Z1Q61226,ZHNX07847\n" +
                        "20961091AOPZ1229,ZHNX14090\n" +
                        "20961540ZS3E1229,ZHNX14090\n" +
                        "20961591TBJV1229,ZHNX10734\n" +
                        "20962127ZBTK1230,ZHNX13167\n" +
                        "20963152U9B21226,ZHNX08240\n" +
                        "20964618DLTF1230,HZLQ0008\n" +
                        "20965797FY3L1229,ZHNX08588\n" +
                        "20965829DBM01229,ZHNX08588\n" +
                        "20966928ERE31228,ZHNX11105\n" +
                        "20970428SXHJ1228,ZHNX04056\n" +
                        "20972400JCOS1229,ZHNX14090\n" +
                        "2097290567LS1230,NMAH0012\n" +
                        "20976014HIYO1229,ZHNX10681\n" +
                        "20977172RWBE1228,ZHNX31412\n" +
                        "20977509HM6S1228,NMAH0017\n" +
                        "20978276Z7UI1228,GSAD0039\n" +
                        "20979429GO5R1229,ZHNX14090\n" +
                        "20983030SANE1230,HBCA0004\n" +
                        "20983693HP2W1228,NMAH0012\n" +
                        "20984638I5HV1229,ZHNX14090\n" +
                        "20985612AF4X1229,GSYD0006\n" +
                        "2098607710U01229,SXYS0016\n" +
                        "20986207CIGH1230,ZHNX08450\n" +
                        "20986218EA5B1230,ZHNX09414\n" +
                        "20987106I15O1230,ZHNX09662\n" +
                        "20990181CDUF1230,ZHNX07894\n" +
                        "2099087624RU1228,GSYD0011\n" +
                        "20991806WRSQ1227,NMNC0002\n" +
                        "20993331YXK71228,ZHNX14059\n" +
                        "20995522Q4KA1229,ZHNX09541\n" +
                        "20996724T7DN1228,ZHNX11105\n" +
                        "20997465RHSI1227,NMWN0004\n" +
                        "20998847WDNP1227,ZHNX12545";

        Arrays.stream(str.split("\n"))
                .parallel()
                .forEach(ss -> {
            try {
                String[] split = ss.split(",");
                if (split.length != 2) {
                    System.err.println("????," + ss);
                    return;
                }
                httpPost(url, split[0], split[1]);
            } catch (Exception e) {
                System.err.println("orderNd:" + ss + "error");
                e.printStackTrace();
            }
        });
    }

    public static void modfiyDesc(TestObject o) {
        o.setDesc("3_desc");
    }


    public static void httpPost(String url, String orderSn, String recommendId)
            throws Exception {
        // 创建默认的httpClient实例.
        CloseableHttpClient httpclient = HttpClients.createDefault();
        // 创建httppost
        String newUrl = url.concat("?orderSn=").concat(orderSn).concat("&recommendId=").concat(recommendId);
        HttpPost httppost = new HttpPost(newUrl);
        try {
            /*if (null != formparams) {
                UrlEncodedFormEntity uefEntity = new UrlEncodedFormEntity(
                        formparams, "UTF-8");
                httppost.setEntity(uefEntity);
            }*/
            System.out.println("executing request " + httppost.getURI());
            CloseableHttpResponse response = httpclient.execute(httppost);
            String respData = null;
            try {
                HttpEntity entity = response.getEntity();
                String s = EntityUtils.toString(entity);
                log.info("{} resp:{}", orderSn, s);
                if (!StringUtils.contains(s, "success")) {
                    System.err.println("补单失败" + orderSn + recommendId);
                }
            } finally {
                response.close();
            }
        } finally {
            // 关闭连接,释放资源
            httpclient.close();
        }
    }
}
