package com.cfpamf.ms.insur.weixin.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.cfpamf.ms.insur.admin.constant.ClaimRiskType;
import com.cfpamf.ms.insur.admin.dao.safes.claim.SmClaimTemplateInfoMapper;
import com.cfpamf.ms.insur.admin.pojo.po.claim.SmClaimTemplateInfo;
import com.cfpamf.ms.insur.admin.pojo.vo.CompanyVO;
import com.cfpamf.ms.insur.admin.pojo.vo.DictionaryVO;
import com.cfpamf.ms.insur.admin.pojo.vo.SmProductDetailVO;
import com.cfpamf.ms.insur.admin.pojo.vo.claim.SmClaimTemplateFileDto;
import com.cfpamf.ms.insur.admin.service.CompanyService;
import com.cfpamf.ms.insur.admin.service.DictionaryService;
import com.cfpamf.ms.insur.admin.service.SmProductService;
import com.cfpamf.ms.insur.base.exception.BizException;
import com.cfpamf.ms.insur.base.exception.ExcptEnum;
import com.cfpamf.ms.insur.base.util.AliYunOssUtil;
import com.cfpamf.ms.insur.weixin.pojo.vo.claim.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.cxf.security.claims.authorization.Claim;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.web.util.UrlUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @Date 2024/5/6 14:56
 * @Version 1.0
 */
@Service
public class WxCcClaimTemplateService extends WxAbstractService {

    @Autowired
    private SmClaimTemplateInfoMapper claimTemplateInfoMapper;

    @Autowired
    private CompanyService companyService;

    @Autowired
    private DictionaryService dictionaryService;

    @Autowired
    private SmProductService productService;

    public List<WxClaimTemplateGuideVo> guideList(WxClaimTemplateGuideQueryDto templateGuideQueryDto) {

        List<WxClaimTemplateGuideVo> result = claimTemplateInfoMapper.pageGuide(templateGuideQueryDto);

        if (CollectionUtils.isNotEmpty(result)) {
            result.forEach(
                    x -> {
                        templateGuideQueryDto.setCompanyId(x.getCompanyId());
                        x.setList(listGuideChild(templateGuideQueryDto, 0));
                    }
            );
        }

        return result;

    }


    public List<WxClaimGuideChildrenParamsVo> listGuideChild(WxClaimTemplateGuideQueryDto templateGuideQueryDto, Integer start) {
        return claimTemplateInfoMapper.listGuideChild(templateGuideQueryDto, start);
    }

    public int guideListCount(WxClaimTemplateGuideQueryDto templateGuideQueryDto) {

        return claimTemplateInfoMapper.count(templateGuideQueryDto);

    }

    public List<DictionaryVO> listGuideDetailRiskType(WxClaimGuideChildrenParamsVo guideChildrenParamsVo) {

        List<DictionaryVO> dicList = claimTemplateInfoMapper.detailWxRiskTypeByGroup(guideChildrenParamsVo);

        List<DictionaryVO> parentDicList = null;
        if (
                Objects.nonNull(guideChildrenParamsVo.getProductId())
                        && Objects.equals(guideChildrenParamsVo.getSelectProduct(), 1)
                        && StrUtil.isNotEmpty(guideChildrenParamsVo.getProductType())
        ) {
            WxClaimGuideChildrenParamsVo newCondition = new WxClaimGuideChildrenParamsVo();
            newCondition.setProductType(guideChildrenParamsVo.getProductType());
            newCondition.setSelectProduct(guideChildrenParamsVo.getSelectProduct());
            newCondition.setCompanyId(guideChildrenParamsVo.getCompanyId());
            parentDicList = claimTemplateInfoMapper.detailWxRiskTypeByGroup(newCondition);
        }

        return Stream.concat(
                        Optional.ofNullable(dicList).orElse(Collections.emptyList()).stream(),
                        Optional.ofNullable(parentDicList).orElse(Collections.emptyList()).stream()
                ).peek(
                        x -> {
                            ClaimRiskType riskType = ClaimRiskType.getClaimRiskTypeByCode(x.getCode());
                            x.setSorting(Integer.MAX_VALUE);
                            if (Objects.nonNull(riskType)) {
                                x.setName(riskType.getMame());
                                x.setSorting(riskType.getSort());
                            }
                        }
                )
                .distinct().sorted(Comparator.comparingInt(DictionaryVO::getSorting))
                .collect(Collectors.toList());


    }


    public WxSmClaimTemplateVo guideDetail(WxClaimGuideChildrenParamsVo guideChildrenParamsVo) {

        List<SmClaimTemplateInfo> claimSendFileList = claimTemplateInfoMapper.sendFileList(
                guideChildrenParamsVo.getCompanyId()
                , guideChildrenParamsVo.getProductId()
                , CollectionUtil.toList(guideChildrenParamsVo.getRiskType())
                , guideChildrenParamsVo.getSelectProduct()
                , guideChildrenParamsVo.getProductType()
        );

        if (CollectionUtils.isEmpty(claimSendFileList)) {
            claimSendFileList = claimTemplateInfoMapper.sendFileList(
                    guideChildrenParamsVo.getCompanyId()
                    , null
                    , CollectionUtil.toList(guideChildrenParamsVo.getRiskType())
                    , guideChildrenParamsVo.getSelectProduct()
                    , guideChildrenParamsVo.getProductType()
            );
        }

        if (CollectionUtils.isEmpty(claimSendFileList)) {
            throw new BizException(ExcptEnum.PARAMS_ERROR.getCode(), "未找到文件模板信息不存在");
        }

        CompanyVO companyVO = companyService.getCompanyById(guideChildrenParamsVo.getCompanyId());
        if (Objects.isNull(companyVO)) {
            throw new BizException("", "保司信息不存在");
        }

        Map<String, String> fileTypeMap = dictionaryService.getMapByType("claim-template-file-type");

        SmProductDetailVO productDetailVO;
        if (Objects.nonNull(guideChildrenParamsVo.getProductId())) {
            productDetailVO = productService.getProductById(guideChildrenParamsVo.getProductId());
        } else {
            productDetailVO = null;
        }

        Map<WxSmClaimTemplateVo, List<WxSmClaimTemplateFileVo>> resultMap = claimSendFileList.stream()
                .collect(Collectors.toMap(
                        SmClaimTemplateInfo::getFileTypeCode,
                        Function.identity(),
                        (t1, t2) -> t1
                ))
                .values().stream().collect(
                        Collectors.groupingBy(
                                x -> {
                                    WxSmClaimTemplateVo result = new WxSmClaimTemplateVo();
                                    result.setCompanyId(x.getCompanyId());
                                    result.setCompanyName(companyVO.getCompanyName());
                                    result.setCompanyLogoImageUrl(companyVO.getCompanyLogoImageUrl());
                                    result.setSelectProduct(x.getSelectProduct());
                                    result.setProductId(x.getProductId());
                                    result.setRiskType(guideChildrenParamsVo.getRiskType());
                                    result.setRiskTypeName(ClaimRiskType.getNameByCode(guideChildrenParamsVo.getRiskType()));
                                    result.setProductName(Objects.isNull(productDetailVO) ? null : productDetailVO.getProductName());
                                    result.setProductType(x.getProductType());
                                    result.setReportPhone(companyVO.getReportPhoneNo());
                                    return result;
                                },
                                Collectors.mapping(
                                        x -> {
                                            WxSmClaimTemplateFileVo claimTemplateFileVo = new WxSmClaimTemplateFileVo();
                                            claimTemplateFileVo.setFileTypeCode(x.getFileTypeCode());
                                            claimTemplateFileVo.setRemark(x.getRemark());
                                            claimTemplateFileVo.setFileTypeName(fileTypeMap.get(claimTemplateFileVo.getFileTypeCode()));

                                            String fileTemplateUrl = x.getFileTemplateUrl();
                                            if (StringUtils.isNotEmpty(fileTemplateUrl) && !UrlUtils.isAbsoluteUrl(fileTemplateUrl)) {
                                                fileTemplateUrl = AliYunOssUtil.generatePresignedUri5M(fileTemplateUrl);
                                            }

                                            String fileExampleUrl = x.getFileExampleUrl();
                                            if (StringUtils.isNotEmpty(fileExampleUrl) && !UrlUtils.isAbsoluteUrl(fileExampleUrl)) {
                                                fileExampleUrl = AliYunOssUtil.generatePresignedUri5M(fileExampleUrl);
                                            }

                                            claimTemplateFileVo.setFileTemplateUrl(fileTemplateUrl);
                                            claimTemplateFileVo.setFileExampleUrl(fileExampleUrl);
                                            claimTemplateFileVo.setIndex(x.getSortNum());
                                            claimTemplateFileVo.setTipInfo(x.getTipInfo());
                                            claimTemplateFileVo.setTemplateSource(x.getTemplateSource());
                                            claimTemplateFileVo.setNeedUpload(x.getNeedUpload());
                                            return claimTemplateFileVo;
                                        },
                                        Collectors.toList()
                                )
                        )
                );


        resultMap.forEach(
                WxSmClaimTemplateVo::setTemplateFileVoList
        );
        WxSmClaimTemplateVo claimTemplateVo = resultMap.keySet().stream().findFirst()
                .orElseThrow(() -> new BizException(ExcptEnum.PARAMS_ERROR.getCode(), "未找到文件模板信息不存在"));

        claimTemplateVo.setTemplateFileVoList(
                claimTemplateVo.getTemplateFileVoList().stream().sorted(Comparator.comparingInt(SmClaimTemplateFileDto::getIndex)).collect(Collectors.toList())
        );
        if (CollectionUtils.isNotEmpty(claimTemplateVo.getTemplateFileVoList())) {
            claimTemplateVo.setTipInfo(claimTemplateVo.getTemplateFileVoList().get(0).getTipInfo());
        }
        return claimTemplateVo;

    }

}
