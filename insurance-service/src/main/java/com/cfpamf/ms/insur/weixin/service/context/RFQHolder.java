package com.cfpamf.ms.insur.weixin.service.context;

import com.cfpamf.ms.insur.weixin.pojo.dto.product.ProductAttrDTO;
import com.cfpamf.ms.insur.weixin.pojo.dto.product.TimeFactorItem;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 试算保费-上下文信息
 */
@Data
public class RFQHolder {

    private Integer productId;

    private Integer planId;

    /**
     * 计算每个因子后保留的精度信息
     */
    private List<ProductAttrDTO> attrs;

    /**
     * 企业风险系数因子
     */
    private RateFacor enterpriserRisk;

    /**
     * 时间因子
     */
    private RateFacor timeFactor;

    /**
     * 最终的保费折扣
     */
    private BigDecimal discount;
}
