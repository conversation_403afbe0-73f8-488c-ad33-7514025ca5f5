package com.cfpamf.ms.insur.weixin.service;

import com.alibaba.fastjson.JSONArray;
import com.cfpamf.ms.insur.weixin.dao.safes.WxActivityMapper;
import com.cfpamf.ms.insur.weixin.dao.safes.WxProductMapper;
import com.cfpamf.ms.insur.weixin.pojo.query.WxActivityQuery;
import com.cfpamf.ms.insur.weixin.pojo.vo.WxActivityDetailVO;
import com.cfpamf.ms.insur.weixin.pojo.vo.WxActivityListVO;
import com.cfpamf.ms.insur.weixin.pojo.vo.WxProductListVO;
import com.cfpamf.ms.insur.weixin.pojo.vo.WxSessionVO;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 微信活动service
 *
 * <AUTHOR>
 **/
@Slf4j
@Component
public class WxHomeActivityService extends WxAbstractService {

    /**
     * 活动设置mapper
     *
     * <AUTHOR>
     **/
    @Autowired
    private WxActivityMapper mapper;

    /**
     * 微信产品查询mapper
     */
    @Autowired
    private WxProductMapper productMapper;

    /**
     * 查询微信活动列表
     *
     * @param query
     */
    public PageInfo<WxActivityListVO> getWxActivitys(WxActivityQuery query) {
        WxSessionVO session = checkAuthority(query.getOpenId(), query.getAuthorization());
        String orgFullName = session.getOrganizationFullName();
        //modify by zhangjian 2020-11-11去掉办公室判断
        //if (orgFullName != null ) {
        if(super.isAreaOffice(session.getHrOrgId())){
            //2020-08-10 modify by zhangjian
            //query.setRegionName(orgFullName.substring(0, orgFullName.length() - 3));
            query.setRegionName(super.getBranchOrgName(session.getOrgCode()));
        } else {
            query.setRegionName(session.getRegionName());
        }
        PageHelper.startPage(query.getPage(), query.getSize());
        return new PageInfo<>(mapper.listWxActivitys(query));
    }

    /**
     * 查询微信活动列表
     *
     * @param saId
     */
    public WxActivityDetailVO getWxActivityById(int saId, String openId, String authorization) {
        WxSessionVO session = checkAuthority(openId, authorization);
        WxActivityDetailVO detailVO = new WxActivityDetailVO();
        BeanUtils.copyProperties(mapper.getSystemActivityById(saId), detailVO);
        JSONArray jsonArray = detailVO.getProducts();
        if (jsonArray != null && !jsonArray.isEmpty()) {
            List<Integer> activityProductIds = new ArrayList<>();
            for (int i = 0, len = jsonArray.size(); i < len; i++) {
                activityProductIds.add(Integer.valueOf(jsonArray.get(i).toString()));
            }
            String orgFullName = session.getOrganizationFullName();
            String regionName = null;
            //modify by zhangjian 2020-11-11 去掉以前以中文判断区域办公室
            //if (orgFullName != null && orgFullName.endsWith("办公室")) {
            if(super.isAreaOffice(session.getHrOrgId())){
                //regionName = orgFullName.substring(0, orgFullName.length() - 3);
                regionName = super.getBranchOrgName(session.getOrgCode());
            } else {
                regionName = session.getRegionName();
            }
            List<Integer> salesProductIds = productMapper.listProductSalesOrgsByOrgPath(regionName);
            List<Integer> filterProductIds = activityProductIds.stream().filter(salesProductIds::contains).collect(Collectors.toList());
            if (!filterProductIds.isEmpty()) {
                List<WxProductListVO> productList = productMapper.listWxProductsWithGroup(filterProductIds);
                detailVO.setProductList(productList);
            }
        }
        return detailVO;
    }
}
