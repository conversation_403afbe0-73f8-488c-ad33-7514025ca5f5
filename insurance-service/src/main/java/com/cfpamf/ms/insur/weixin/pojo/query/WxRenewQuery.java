package com.cfpamf.ms.insur.weixin.pojo.query;

import com.cfpamf.ms.insur.base.bean.Pageable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.Date;

/**
 * 微信续保查询Query
 *
 * <AUTHOR>
 **/
@Data
@ApiModel
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class WxRenewQuery extends Pageable {

    /**
     * 微信openId
     */
    @ApiModelProperty(value = "微信openId")
    private String openId;

    /**
     * 微信token
     */
    @ApiModelProperty(value = "微信token")
    private String authorization;

    /**
     * 保单到期开始时间
     */
    @ApiModelProperty(value = "保单到期开始时间")
    private Date startTime;

    /**
     * 保单到期结束时间
     */
    @ApiModelProperty(value = "保单到期结束时间")
    private Date endTime;

    /**
     * 查询关键字
     */
    @ApiModelProperty(value = "查询关键字")
    private String keyword;

    /**
     * 负责人Id
     */
    @ApiModelProperty(value = "负责人Id", hidden = true)
    private String customerAdminId;

    /**
     * 代理人Id
     */
    @ApiModelProperty(value = "代理人Id", hidden = true)
    private Integer agentId;

    /**
     * 渠道
     */
    @ApiModelProperty(value = "渠道")
    private String channel;
}
