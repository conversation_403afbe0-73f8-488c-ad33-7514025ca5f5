package com.cfpamf.ms.insur.weixin.pojo.query;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.Date;

/**
 * 微信我的团队query
 *
 * <AUTHOR>
 **/
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class WxTeamCmsQuery extends WxAuthPageQuery {

    /**
     * 父级代理人姓名或者手机号
     */
    private Integer parentAgentId;

    /**
     * 代理人姓名或者手机号
     */
    private Integer agentId;

    /**
     * 代理人姓名或者手机号
     */
    private String agentName;

    /**
     * 团队Id
     */
    private Integer teamId;

    /**
     * 开始时间
     */
    private Date startDate;

    /**
     * 结束时间
     */
    private Date endDate;
}
