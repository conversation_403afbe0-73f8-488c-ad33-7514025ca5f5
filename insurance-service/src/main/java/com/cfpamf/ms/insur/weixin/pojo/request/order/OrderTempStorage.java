package com.cfpamf.ms.insur.weixin.pojo.request.order;

import com.cfpamf.ms.insur.admin.external.fh.dto.FhInsuredPerson;
import com.cfpamf.ms.insur.weixin.pojo.dto.product.AiQuestion;
import com.cfpamf.ms.insur.weixin.pojo.vo.order.ApplicantVo;
import com.cfpamf.ms.insur.weixin.pojo.vo.order.OrderProductVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 订单暂存
 */
@Data
public class OrderTempStorage {
    @ApiModelProperty("订单Id")
    private String orderId;

    private OrderBasicVo orderInfo;

    private OrderProductVo product;

    private ApplicantVo applicant;

    private List<FhInsuredPerson> insuredPerson;

    @ApiModelProperty(value = "核保问题")
    private List<AiQuestion> question;


}
