package com.cfpamf.ms.insur.weixin.pojo.query.aicheck;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @date: 2020/11/30 11:30
 * @description:
 */
@Data
public class WxAkQuestionaireNodeQuery extends WxAkBaseQuery{





    @ApiModelProperty(value = "问卷编号")
    private String questionnaireCode;

    @ApiModelProperty(value = "当前核保对象姓名")
    private String personName;

    @ApiModelProperty(value = "当前核保对象身份证")
    private String idNumber;
    @ApiModelProperty(value = "疾病编码")
    private String diseaseCode;

    @ApiModelProperty(value = "节点编码")
    private String nodeCode;

    @ApiModelProperty(value = "节点类型")
    private String nodeType;
    /**
     * 问题编码路径
     */
    @ApiModelProperty(value = "问题编码路径")
    private String nodeCodePath;


}
