package com.cfpamf.ms.insur.weixin.service.settlement.impl;

import cn.hutool.core.date.DateField;
import cn.hutool.core.util.StrUtil;
import com.cfpamf.common.ms.util.DateUtil;
import com.cfpamf.ms.bms.facade.vo.JwtUserInfo;
import com.cfpamf.ms.insur.admin.settlement.enums.DistributionFlagEnum;
import com.cfpamf.ms.insur.admin.settlement.enums.LongShortFlagEnum;
import com.cfpamf.ms.insur.admin.settlement.enums.RuralProxyFlagEnum;
import com.cfpamf.ms.insur.admin.settlement.enums.SettlementEventTypeEnum;
import com.cfpamf.ms.insur.admin.settlement.service.WhaleManagerInnerService;
import com.cfpamf.ms.insur.admin.settlement.util.CommonUtil;
import com.cfpamf.ms.insur.base.bean.SmyPageInfo;
import com.cfpamf.ms.insur.base.util.HttpRequestUtil;
import com.cfpamf.ms.insur.facade.dto.whale.SettlementCostSummary;
import com.cfpamf.ms.insur.facade.dto.whale.SettlementPolicyCostInfo;
import com.cfpamf.ms.insur.facade.dto.whale.WxUserSettlementDetail;
import com.cfpamf.ms.insur.facade.query.whale.SettlementCostQuery;
import com.cfpamf.ms.insur.facade.query.whale.WxUserSettlementQuery;
import com.cfpamf.ms.insur.weixin.pojo.query.settlement.WxCostQuery;
import com.cfpamf.ms.insur.weixin.pojo.query.settlement.WxUserCommissionDetailQuery;
import com.cfpamf.ms.insur.weixin.pojo.vo.WxCmsSmyVo;
import com.cfpamf.ms.insur.weixin.pojo.vo.WxSessionVO;
import com.cfpamf.ms.insur.weixin.pojo.vo.settlement.WxSettlementCostTotalVO;
import com.cfpamf.ms.insur.weixin.pojo.vo.settlement.WxUserCommissionDetailVo;
import com.cfpamf.ms.insur.weixin.pojo.vo.settlement.WxUserCostListVo;
import com.cfpamf.ms.insur.weixin.service.WxAbstractService;
import com.cfpamf.ms.insur.weixin.service.enums.GrantWayEnum;
import com.cfpamf.ms.insur.weixin.service.settlement.WxSettlementCostService;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Maps;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Slf4j
@Service("wxSettlementCostService")
public class WxSettlementCostServiceImpl extends WxAbstractService implements WxSettlementCostService {
    @Autowired
    private WhaleManagerInnerService whaleManagerInnerService;

    private static String KEY_GRANT_WAY = "发放方式";

    private static String KEY_TOTAL = "合计";

    @Override
    public SmyPageInfo<WxUserCostListVo, WxCmsSmyVo> getWxUserCostListAndSummary(WxCostQuery query) {
        validWxParam(query);
        List<SettlementPolicyCostInfo> policyCostInfos =  whaleManagerInnerService.listZhSettlementPolicyCost(costWxCostQueryToSettlementCostQuery(query));
        List<WxUserCostListVo> costListVoList = toWxUserCostListVo(policyCostInfos);
        WxCmsSmyVo smyVo =  getWxUserCostSummary(query);
        return new SmyPageInfo<>(costListVoList,smyVo);
    }

    @Override
    public SmyPageInfo<WxUserCostListVo, WxCmsSmyVo> pageWxUserCostListAndSummary(WxCostQuery query) {
        validWxParam(query);
        PageInfo<SettlementPolicyCostInfo> policyCostInfos =  whaleManagerInnerService.pageZhSettlementPolicyCost(costWxCostQueryToSettlementCostQuery(query));
        List<WxUserCostListVo> costListVoList = toWxUserCostListVo(policyCostInfos.getList());
        WxCmsSmyVo smyVo =  getWxUserCostSummary(query);
        SmyPageInfo smyPageInfo = new SmyPageInfo<>(costListVoList,smyVo);
        smyPageInfo.setPageNum(policyCostInfos.getPageNum());
        smyPageInfo.setSize(policyCostInfos.getSize());
        smyPageInfo.setTotal(policyCostInfos.getTotal());
        smyPageInfo.setHasNextPage(policyCostInfos.isHasNextPage());

        return smyPageInfo;
    }

    @Override
    public PageInfo<WxUserCostListVo> getWxUserCostList(WxCostQuery query) {
        validWxParam(query);
        List<SettlementPolicyCostInfo> policyCostInfos =  whaleManagerInnerService.listZhSettlementPolicyCost(costWxCostQueryToSettlementCostQuery(query));
        return new PageInfo<>(toWxUserCostListVo(policyCostInfos));
    }

    @Override
    public PageInfo<WxUserCostListVo> pageWxUserCostList(WxCostQuery query) {
        validWxParam(query);
        PageInfo<SettlementPolicyCostInfo> policyCostInfos =  whaleManagerInnerService.pageZhSettlementPolicyCost(costWxCostQueryToSettlementCostQuery(query));
        List<WxUserCostListVo> costListVoList = toWxUserCostListVo(policyCostInfos.getList());
        PageInfo pageInfo = new PageInfo<>(costListVoList);
        pageInfo.setPageNum(policyCostInfos.getPageNum());
        pageInfo.setSize(policyCostInfos.getSize());
        pageInfo.setTotal(policyCostInfos.getTotal());
        pageInfo.setHasNextPage(policyCostInfos.isHasNextPage());
        return pageInfo;
    }

    private List<WxUserCostListVo> toWxUserCostListVo(List<SettlementPolicyCostInfo> policyCostInfoList){
        if(CollectionUtils.isEmpty(policyCostInfoList)){
            return Collections.EMPTY_LIST;
        }
        return policyCostInfoList.stream().map(o->{
            WxUserCostListVo costListVo = new WxUserCostListVo();
            //BeanUtils.copyProperties(o,costListVo);
            costListVo.setLongShortFlag(LongShortFlagEnum.getDesc(o.getLongShortFlag()));
            costListVo.setApplicantName(o.getApplicantName());
            costListVo.setInsuredName(o.getInsuredName());
            costListVo.setGrantAmount(o.getGrantAmount());
            costListVo.setContractCode(o.getContractCode());
            costListVo.setDistributionFlag(DistributionFlagEnum.getDesc(o.getDistributionFlag()));
            costListVo.setPolicyNo(o.getPolicyNo());
            costListVo.setPolicyPremium(o.getPolicyPremium());
            costListVo.setSettlementEventCode(o.getSettlementEventCode());
            costListVo.setSettlementEventDesc(SettlementEventTypeEnum.dict(o.getSettlementEventCode()));
            costListVo.setInitialEventCode(SettlementEventTypeEnum.dict(o.getInitialEventCode()));
            costListVo.setBusinessAccountTime(o.getBusinessAccountTime());
            costListVo.setProductCode(o.getMainProductCode());
            if(StringUtils.isNotBlank(o.getCommodityName())){
                costListVo.setProductName(o.getCommodityName());
            }else{
                costListVo.setProductName(o.getMainProductName());
            }

            costListVo.setRenewalPeriod(o.getRenewalPeriod());
            costListVo.setRuralProxyFlag(RuralProxyFlagEnum.getDesc(o.getRuralProxyFlag()));

            return costListVo;
        }).collect(Collectors.toList());
    }

    public WxCmsSmyVo getWxUserCostSummary(WxCostQuery query){
        //验证参数
        validWxParam(query);

        SettlementCostSummary summary = whaleManagerInnerService.sumSettlementCostSmy(costWxCostQueryToSettlementCostQuery(query));
        if(Objects.isNull(summary)){
            return new WxCmsSmyVo();
        }

        WxCmsSmyVo smyVo = new WxCmsSmyVo();
        smyVo.setCmsAmount(summary.getCmsAmount());
        smyVo.setOrderAmount(summary.getTotalPremium());
        smyVo.setOrderQty(summary.getPolicyQty());
        smyVo.setAddCommissionAmount(null);
        return smyVo;
    }

    public WxSettlementCostTotalVO get24WxUserHomePageCostSummary(WxCostQuery query){
        //验证参数
        if(Objects.isNull(query.getUserId())){
            JwtUserInfo userInfo = HttpRequestUtil.getUserOrThrowExp();
            query.setUserId(userInfo.getJobNumber());
        }
        WxSettlementCostTotalVO totalVO = new WxSettlementCostTotalVO();
        //当日数据
        Date now = new Date();
        query.setStartTime(DateUtil.getBeginOfDay(now));
        query.setEndTime(DateUtil.getEndOfDay(now));
        SettlementCostSummary todaySummary = whaleManagerInnerService.sumSettlementCostSmy(costWxCostQueryToSettlementCostQuery(query));
        totalVO.setTodayAmount(Objects.isNull(todaySummary)? BigDecimal.ZERO:todaySummary.getSettlementAmount());
        if(totalVO.getTodayAmount() == null){
            totalVO.setTodayAmount(BigDecimal.ZERO);
        }
        //当月数据处理
        query.setStartTime(DateUtil.getBeginOfMonth(now));
        SettlementCostSummary monthSummary = whaleManagerInnerService.sumSettlementCostSmy(costWxCostQueryToSettlementCostQuery(query));
        totalVO.setCurrentMonthAmount(Objects.isNull(monthSummary)? BigDecimal.ZERO:monthSummary.getSettlementAmount());
        if(totalVO.getCurrentMonthAmount() == null){
            totalVO.setCurrentMonthAmount(BigDecimal.ZERO);
        }


        return totalVO;
    }

    /**
     * 验证并处理参数
     * @param query
     */
    private void validWxParam(WxCostQuery query){
        //WxSessionVO session = checkAuthorityEmployee(query.getOpenId(), query.getAuthorization());
        //JwtUserInfo userInfo = HttpRequestUtil.getUserOrThrowExp();
        //设置登录客户经理工号
        //query.setUserId(userInfo.getJobNumber());
        if(Objects.isNull(query.getUserId())){
            JwtUserInfo userInfo = HttpRequestUtil.getUserOrThrowExp();
            query.setUserId(userInfo.getJobNumber());
        }
        if(query.getStartTime()!=null){
            query.setStartTime(DateUtil.getBeginOfDay(query.getStartTime()));
        }
        if(query.getEndTime()!=null){
            query.setEndTime(DateUtil.getEndOfDay(query.getEndTime()));
        }
    }


    private SettlementCostQuery costWxCostQueryToSettlementCostQuery(WxCostQuery query){
        SettlementCostQuery costQuery = new SettlementCostQuery();
        BeanUtils.copyProperties(query,costQuery);
        //设置佣金归属人查询条件
        costQuery.setOwnerThirdCode(query.getUserId());

        if(query.getStartTime()!=null){
            costQuery.setBusinessAccountStartTime(query.getStartTime());
        }
        if(query.getEndTime()!=null){
            costQuery.setBusinessAccountEndTime(query.getEndTime());
        }
        //处理 投保人查询条件
        if (StringUtils.isNotBlank(costQuery.getApplicantName())) {
            //手机号码
            if (CommonUtil.isPhone(costQuery.getApplicantName())) {
                costQuery.setApplicantMobile(costQuery.getApplicantName());
                costQuery.setApplicantName(null);
            }else if (Pattern.matches("[\\w\\-]+", costQuery.getApplicantName())) {
                costQuery.setApplicantIdCard(costQuery.getApplicantName());
                costQuery.setApplicantName(null);
            }
        }
        //处理被保人查询条件
        if (StringUtils.isNotBlank(costQuery.getInsuredName())) {
            //手机号码
            if (CommonUtil.isPhone(costQuery.getInsuredName())) {
                costQuery.setInsuredMobile(costQuery.getInsuredName());
                costQuery.setInsuredName(null);
            }else if (Pattern.matches("[\\w\\-]+", costQuery.getInsuredName())) {
                costQuery.setInsuredIdCard(costQuery.getInsuredName());
                costQuery.setInsuredName(null);
            }
        }
        //设置查询页面参数
        costQuery.setPage(query.getPage());
        costQuery.setLimit(query.getSize());
        //不查为合伙人的佣金记录
        costQuery.setIsLifeServicePartner(0);
        return costQuery;
    }

    @Override
    public WxUserCommissionDetailVo getWxUserCommissionDetail(WxUserCommissionDetailQuery query) {
        JwtUserInfo userInfo = HttpRequestUtil.getUserOrThrowExp();
        query.setUserId(userInfo.getJobNumber());

        WxUserSettlementDetail detail = whaleManagerInnerService.getWxUserSettlementDetail(commissionDetailQueryToSettlementQuery(query));
        WxUserCommissionDetailVo detailVo = new WxUserCommissionDetailVo();
        Map<String,Object> commissionItem = Maps.newHashMap();
        if(Objects.equals(detail.getGrantWay(), GrantWayEnum.WITHDRAW.getCode())) {
            detailVo.setGrantWay(GrantWayEnum.WITHDRAW.getName() );
        }else if(Objects.equals(detail.getGrantWay(), GrantWayEnum.BANK_CARD.getCode())) {
            detailVo.setGrantWay(StrUtil.format("{}\n{}",detail.getBankName(),detail.getBankCardNo()));
        }else{
            detailVo.setGrantWay("");
        }

        detailVo.setTotal(detail.getTotal());

        if(!CollectionUtils.isEmpty(detail.getSubjectDetailList())){
            detail.getSubjectDetailList().stream().forEach(s->{
                commissionItem.put(s.getSendSubjectName(),s.getGrantAmount());
            });

        }
        detailVo.setCommissionItem(commissionItem);

        return detailVo;
    }
    private WxUserSettlementQuery commissionDetailQueryToSettlementQuery(WxUserCommissionDetailQuery query){
        WxUserSettlementQuery settlementQuery = new WxUserSettlementQuery();
        settlementQuery.setSendObjectCode(query.getUserId());
        if(StringUtil.isNotBlank(query.getCommissionMonth())){
            settlementQuery.setCostSettlementCycle(StrUtil.replace(query.getCommissionMonth(),"-",""));
        }
        return settlementQuery;
    }




}
