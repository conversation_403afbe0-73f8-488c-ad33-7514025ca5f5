package com.cfpamf.ms.insur.weixin.pojo.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * 微信代理人团队名称DTO
 *
 * <AUTHOR>
 **/
@Data
@ApiModel
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class WxAgentModifyTeamDTO extends WxAuthorizationDTO {

    /**
     * 团队名称
     */
    @ApiModelProperty("团队名称")
    private String teamName;
}
