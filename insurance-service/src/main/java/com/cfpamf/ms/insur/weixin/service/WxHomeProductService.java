package com.cfpamf.ms.insur.weixin.service;

import org.apache.commons.lang3.StringUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.cfpamf.cmis.common.utils.Base64Utils;
import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.common.ms.util.DateUtil;
import com.cfpamf.ms.insur.admin.constant.SmConstants;
import com.cfpamf.ms.insur.admin.dao.safes.SmCommissionMapper;
import com.cfpamf.ms.insur.admin.dao.safes.SmOccupationMapper;
import com.cfpamf.ms.insur.admin.dao.safes.SmProductMapper;
import com.cfpamf.ms.insur.admin.dao.safes.SmProductNotifyMapper;
import com.cfpamf.ms.insur.admin.dao.safes.product.PlanMapper;
import com.cfpamf.ms.insur.admin.dao.safes.product.SmProductConfirmMapper;
import com.cfpamf.ms.insur.admin.enums.EnumChannel;
import com.cfpamf.ms.insur.admin.enums.EnumProductApiType;
import com.cfpamf.ms.insur.admin.enums.product.EnumProductCreateType;
import com.cfpamf.ms.insur.admin.pojo.dto.*;
import com.cfpamf.ms.insur.admin.pojo.po.SmProductNotify;
import com.cfpamf.ms.insur.admin.pojo.po.commission.SystemCommissionConfigDetail;
import com.cfpamf.ms.insur.admin.pojo.query.CmpySettingQuery;
import com.cfpamf.ms.insur.admin.pojo.query.PosterGenQuery;
import com.cfpamf.ms.insur.admin.pojo.query.SmPlanFactorQuery;
import com.cfpamf.ms.insur.admin.pojo.vo.SmProductCoveragePremiumDTO;
import com.cfpamf.ms.insur.admin.pojo.vo.*;
import com.cfpamf.ms.insur.admin.service.*;
import com.cfpamf.ms.insur.admin.service.commission.CommissionConfigQueryService;
import com.cfpamf.ms.insur.admin.service.sys.SystemShareKnowledgeService;
import com.cfpamf.ms.insur.base.bean.Pageable;
import com.cfpamf.ms.insur.base.exception.BizException;
import com.cfpamf.ms.insur.base.exception.ExcptEnum;
import com.cfpamf.ms.insur.base.service.DLockTemplate;
import com.cfpamf.ms.insur.base.util.*;
import com.cfpamf.ms.insur.weixin.constant.WxConstant;
import com.cfpamf.ms.insur.weixin.dao.safes.*;
import com.cfpamf.ms.insur.weixin.pojo.dto.WxGlProductQuoteDTO;
import com.cfpamf.ms.insur.weixin.pojo.dto.WxGlProductQuotePlanDTO;
import com.cfpamf.ms.insur.weixin.pojo.dto.homepage.SalesProductDTO;
import com.cfpamf.ms.insur.weixin.pojo.dto.product.GroupProductListDTO;
import com.cfpamf.ms.insur.weixin.pojo.dto.product.TimeFactorItem;
import com.cfpamf.ms.insur.weixin.pojo.po.product.CoverageAmountPo;
import com.cfpamf.ms.insur.weixin.pojo.query.WxGlProductQuoteQuery;
import com.cfpamf.ms.insur.weixin.pojo.query.WxProductQuery;
import com.cfpamf.ms.insur.weixin.pojo.query.policy.CoverageQuery;
import com.cfpamf.ms.insur.weixin.pojo.query.policy.PlanQuery;
import com.cfpamf.ms.insur.weixin.pojo.vo.*;
import com.cfpamf.ms.insur.weixin.pojo.vo.apply.common.GroupInquiry;
import com.cfpamf.ms.insur.weixin.pojo.vo.product.CoverageAmountVo;
import com.cfpamf.ms.insur.weixin.pojo.vo.product.ProductSelectionVO;
import com.cfpamf.ms.insur.weixin.service.context.RFQHolder;
import com.cfpamf.ms.insur.weixin.service.context.RateFacor;
import com.cfpamf.ms.insur.weixin.service.policy.InquiryService;
import com.cfpamf.ms.insur.weixin.service.underwriting.GroupApplyHandler;
import com.cfpamf.ms.insur.weixin.util.ContailerTools;
import com.cfpamf.ms.insur.weixin.util.UnitConvertor;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.security.core.parameters.P;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.cfpamf.ms.insur.base.constant.CacheKeyConstants.*;

/**
 * 微信产品查询service
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class WxHomeProductService extends WxAbstractService {

    /**
     * 微信产品轮播图URL前缀
     */
    private final static String LBT_PRODUCT_URL_PREFIX = "/proDetail/";

    /**
     * 投保人与被保人关系排序map
     */
    private final static Map<String, Integer> RS_SORT_MAP = new HashMap<>();

    static {
        RS_SORT_MAP.put("本人", 100);
        RS_SORT_MAP.put("配偶", 99);
        RS_SORT_MAP.put("丈夫", 98);
        RS_SORT_MAP.put("妻子", 97);

        RS_SORT_MAP.put("子女", 89);
        RS_SORT_MAP.put("儿子", 88);
        RS_SORT_MAP.put("女儿", 87);
        RS_SORT_MAP.put("父子", 86);
        RS_SORT_MAP.put("父女", 85);
        RS_SORT_MAP.put("母子", 84);
        RS_SORT_MAP.put("母女", 83);

        RS_SORT_MAP.put("父母", 79);
        RS_SORT_MAP.put("父亲", 78);
        RS_SORT_MAP.put("母亲", 77);

        RS_SORT_MAP.put("兄弟", 69);
        RS_SORT_MAP.put("姐弟", 68);
        RS_SORT_MAP.put("祖孙", 67);
        RS_SORT_MAP.put("雇佣", 66);
        RS_SORT_MAP.put("其他", 65);
    }

    /**
     * 产品service
     */
    @Autowired
    private SmProductService productService;

    /**
     * 产品配置service
     */
    @Autowired
    private SmCmpySettingService cmpySettingService;

    /**
     * 小额保险产品海报Service
     */
    @Autowired
    private SmProductPosterService posterService;

    /**
     * 保险公司职业service
     */
    @Autowired
    private OccupationService occupationService;

    /**
     * 系统设置service
     */
    @Autowired
    private SystemSettingService systemSettingService;

    /**
     * 职业mapper
     */
    @Autowired
    private SmOccupationMapper occupationMapper;

    /**
     * 产品mapper
     */
    @Autowired
    private WxProductMapper wxProductMapper;

    @Autowired
    private SmProductConfirmMapper confirmMapper;

    /**
     * 产品mapper
     */
    @Autowired
    private SmProductMapper smProductMapper;

    /**
     * 产品mapper
     */
    @Autowired
    private SmProductNotifyMapper productNotifyMapper;

    /**
     * 提成mapper
     */
    @Autowired
    private SmCommissionMapper commissionMapper;

    /**
     * 海报mapper
     */
    @Autowired
    private WxProductPosterMapper wxProductPosterMapper;

    /**
     * 微信用户设置mapper
     */
    @Autowired
    private WxUserSettingMapper wxUserSettingMapper;

    /**
     * 微信团险报价Mapper
     */
    @Autowired
    private WxGlProductQuoteMapper productQuoteMapper;
    /**
     * 产品计划mapper
     */
    @Autowired
    private PlanMapper planMapper;

    @Autowired
    private SystemShareKnowledgeService shareKnowledgeService;

    @Autowired
    private GroupApplyHandler applyHandler;

    @Autowired
    private CommissionConfigQueryService commissionConfigQueryService;

    /**
     * 重新设置最小金额
     *
     * @param orgName
     * @param vos
     */
    public void resetMinAmt(String orgName, List<WxProductListVO> vos) {
        if (!CollectionUtils.isEmpty(vos)) {
            List<Integer> voIds = vos.stream().map(WxProductListVO::getId).collect(Collectors.toList());
            List<ProductMinAmtDTO> wxProductMinAmtDTOS = smProductMapper.listProductMinAmtByOrgName(voIds, orgName)
                    .stream().filter(Objects::nonNull).collect(Collectors.toList());
            ;
            Map<Integer, List<ProductMinAmtDTO>> minAmtDTOMap = LambdaUtils.groupBy(wxProductMinAmtDTOS, ProductMinAmtDTO::getProductId);
            vos.forEach(pro -> {
                List<ProductMinAmtDTO> amts = minAmtDTOMap.get(pro.getId());
                if (!CollectionUtils.isEmpty(amts)) {
                    pro.setMinAmount(amts.stream().map(ProductMinAmtDTO::getAmount)
                            .min(BigDecimal::compareTo).orElse(BigDecimal.ZERO));
                }
            });
        }
    }

    /**
     * 查询微信产品列表
     *
     * @param query
     * @return
     */
    @Cacheable(cacheNames = PRODUCT_LIST,
            key = "'system'+#query.system +'channel'+#query.channel + 'orgName'+#query.orgName " +
                    "+ 'categoryId'+#query.categoryId + 'companyId'+#query.companyId +'page' + #query.page +'keyword' + #query.keyword  + 'size'+#query.size + 'id' + #query.productId")
    public PageInfo<WxProductListVO> getWxProductsByPage(WxProductQuery query) {
        if (Objects.equals(query.getCategoryId(), WxConstant.PRODUCT_CATEGORY_ALL)) {
            query.setCategoryId(null);
        }
        List<Integer> salesProductIds = null;
        // 中和小保不加销售区域限制
        if (!Objects.equals(query.getSystem(), SmConstants.CHANNEL_CIC)) {
            salesProductIds = getOrgSalesProductIds(query.getOrgName());
        }
        if (salesProductIds != null && salesProductIds.isEmpty()) {
            return new PageInfo<>(Collections.emptyList());
        }
        String keyword = query.getKeyword();
        keyword = filterSQLChar(keyword);
        try {
            log.info("查询:{}", query);
            PageHelper.startPage(query.getPage(), query.getSize());
            List<WxProductListVO> productListVos = wxProductMapper.listWxProducts(query.getCategoryId(), query.getChannel(), keyword, query.getCompanyId()
                    , salesProductIds, query.getProductId());
            resetMinAmt(query.getOrgName(), productListVos);
            initAttr(productListVos);
            return new PageInfo<>(productListVos);
        } catch (Exception e) {
            log.warn("数据查询失败", e);
            return new PageInfo<>(Collections.emptyList());
        }
    }

    private String filterSQLChar(String sql) {
        if (sql == null) {
            return sql;
        }
        try {
            String errorChar = "'|\"|&|@|%|\\*|\\(|\\)|-|>|<|`|~|-";
            Pattern pattern = Pattern.compile(errorChar);
            Matcher m = pattern.matcher(sql);
            if (m.find()) {
                return m.replaceAll("").trim();
            }
            return sql.trim();
        } catch (Exception e) {
            log.warn("SQL过滤失败：{}", sql);
            return sql;
        }
    }

    private String getFhH5Url(String url) {
        if (StringUtils.isEmpty(url)) {
            return url;
        }
        final String userID = checkAuthority().getUserId();
        if (url.contains("?")) {//  如果有queryString
            return url + "&userId=" + userID;
        }
        return (url + "?userId=" + userID);
    }

    private String getZaH5Url(String url) {
        if (StringUtils.isEmpty(url)) {
            return url;
        }
        final String bizCode = checkAuthority().getBizCode();
        if (url.contains("?")) {//  如果有queryString
            return url + "&ext=" + bizCode;
        }
        return (url + "?ext=" + bizCode);
    }

    /**
     * 初始化产品扩展属性
     *
     * @param productListVOS
     */
    public void initAttr(List<WxProductListVO> productListVOS) {

        if (CollectionUtils.isEmpty(productListVOS)) {
            return;
        }
        List<Integer> collect = productListVOS.stream().map(WxProductListVO::getId).collect(Collectors.toList());
        Map<Integer, Map<String, String>> attrMap = productService.listProductAttr(collect);
        productListVOS.forEach(pro -> pro.setAttrs(attrMap.getOrDefault(pro.getId(), Collections.emptyMap())));
    }

    /**
     * 凭借泛华H5的连接地址
     */
    public void resetH5Url(List<WxProductListVO> productListVOS) {
        if (CollectionUtils.isEmpty(productListVOS)) {
            return;
        }
        // 泛华H5处理
        productListVOS.stream()
                .filter(pro -> EnumChannel.FH.getCode().equals(pro.getChannel()))
                .filter(pro -> Objects.equals(EnumProductApiType.H5.getType(), pro.getApiType()))
                .filter(pro -> org.apache.commons.lang3.StringUtils.isNotBlank(pro.getH5Url()))
                .forEach(pro -> pro.setH5Url(getFhH5Url(pro.getH5Url())));

        //众安H5处理
        productListVOS.stream()
                .filter(pro -> EnumChannel.ZA.getCode().equals(pro.getChannel()))
                .filter(pro -> Objects.equals(EnumProductApiType.H5.getType(), pro.getApiType()))
                .filter(pro -> org.apache.commons.lang3.StringUtils.isNotBlank(pro.getH5Url()))
                .forEach(pro -> pro.setH5Url(getZaH5Url(pro.getH5Url())));

    }

    /**
     * 查询微信团险产品列表
     *
     * @param query
     * @return
     */
    public PageInfo<WxGlProductListVO> getWxGroupProductsByPage(WxProductQuery query) {
        checkAuthority(query.getOpenId(), query.getAuthorization());
        /**
         * 修订时间:2021-09-16
         * <AUTHOR>
         * 团险的最低保费：从区域表中关联出可选的计划，从计划中选一个最低保费作为当前产品的最低保费
         */
        List<SalesProductDTO> salesProducts = wxProductMapper.listProductByOrg(query.getOrgName());
        log.info("查询可售产品信息:{}",salesProducts);
        if (CollectionUtils.isEmpty(salesProducts)) {
            return new PageInfo<>(Collections.emptyList());
        }

        List<Integer> productIdList = new ArrayList<>(salesProducts.size());
        Map<Integer, BigDecimal> premiumMap = new HashMap<>();
        for (SalesProductDTO product : salesProducts) {
            productIdList.add(product.getProductId());
            premiumMap.put(product.getProductId(), product.getMinPremium());
        }

        if(CollectionUtils.isEmpty(productIdList)) {
            return new PageInfo<>(Collections.emptyList());
        }
        PageInfo<GroupProductListDTO> page = PageHelper.startPage(query.getPage(), query.getSize())
                .doSelectPageInfo(() -> wxProductMapper.queryGroupProductList(productIdList));
        PageInfo<WxGlProductListVO> page2 = new PageInfo<>();
        BeanUtils.copyProperties(page, page2);

        List<WxGlProductListVO> rtn = new ArrayList<>();
        for (GroupProductListDTO entry : page.getList()) {
            WxGlProductListVO vo = WxGlProductListVO.build(entry);
            vo.setMinAmount(premiumMap.get(entry.getProductId()));
            rtn.add(vo);
        }
        page2.setList(rtn);
        return page2;
    }


    /**
     * 查询微信团险产品详情
     *
     * @param openId
     * @param authorization
     * @param productId
     * @return
     */
    public WxGlProductDetailVO getWxGroupProductsDetail(String openId, String authorization, int productId) {
        checkAuthority(openId, authorization);
        WxGlProductDetailVO detail = wxProductMapper.getGlProductById(productId);
        detail.setClauses(wxProductMapper.listProductClausesByProductId(productId));
        detail.setCvgNameDetails(wxProductMapper.listProductCoverages(productId).stream().map(SmProductCoverageDTO::getCvgNameDetail).collect(Collectors.toList()));
        List<WxProductQuoteLimitItemVO> limitItems = wxProductMapper.listProductQuoteLimit(productId, SmProductQuoteLimitItemVO.LIMIT_TYPE_PER);
        if (!limitItems.isEmpty()) {
            detail.setGlPerStartQty(limitItems.get(0).getMinPerQty());
        }

        detail.setShareKnowledge(shareKnowledgeService.getProductShareKnowledgeConfig(productId));

        return detail;
    }

    /**
     * 查询微信热门产品列表
     *
     * @param query
     * @return
     */
    @SuppressWarnings("unchecked")
    public PageInfo<WxProductListVO> getWxHotProductsByPage(WxProductQuery query) {
        // 微信热销产品只显示五个 + 设置的推荐
        int maxTotal = 5;
        query.setPage(1);
        query.setSize(maxTotal);
        String redisKey = "product_list:system" + query.getSystem() + "channel" + query.getChannel() + "orgName" + query.getOrgName() + "categoryId hot";
        String dataStr = redisUtil.get(redisKey);
        if (dataStr != null) {
            return JSON.parseObject(dataStr,
                    new TypeReference<PageInfo<WxProductListVO>>() {
                    });
        }
        List<Integer> salesProductIds = null;
        // 中和小保不加销售区域限制
        if (!Objects.equals(query.getSystem(), SmConstants.CHANNEL_CIC)) {
            salesProductIds = getOrgSalesProductIds(query.getOrgName());
        }
        if (salesProductIds != null && salesProductIds.isEmpty()) {
            return new PageInfo<>(Collections.emptyList());
        }
        //推荐标记 产品
        List<WxProductListVO> activeProducts = wxProductMapper.listWxActiveProducts(query.getChannel(), salesProductIds);
        List<WxProductListVO> hotProducts = wxProductMapper.listWxHotProducts(query.getChannel(), salesProductIds);
        hotProducts.forEach(m -> {
            if (activeProducts.stream().noneMatch(x -> x.getId().equals(m.getId()))) {
                activeProducts.add(m);
            }
        });

        initAttr(activeProducts);
        //重设最小金额
        resetMinAmt(query.getOrgName(), activeProducts);
        PageInfo<WxProductListVO> pageList = new PageInfo<>(activeProducts);
        pageList.setPageNum(query.getPage());
        pageList.setSize(activeProducts.size());
        pageList.setTotal(activeProducts.size());
        pageList.setHasNextPage(false);
        redisUtil.set(redisKey, JSON.toJSONString(pageList), CommonUtil.getTodayNextSeconds());
        return pageList;
    }

    /**
     * 获取区域销售产品Ids
     *
     * @param orgName
     * @return
     */
    public List<Integer> getOrgSalesProductIds(String orgName) {
        return wxProductMapper.listProductSalesOrgsByOrgPath(orgName);
    }


    @Cacheable(cacheNames = PRODUCT_DETAIL, key = "#regionName+':'+#productId+':'+#planIds")
    public WxProductDetailVO getWxProductDetailByIdAndRegionName(int productId, String regionName, String planIds) {
        WxProductDetailVO detailVo = wxProductMapper.getWxProductDetailById(productId);
        if (detailVo == null) {
            throw new BizException(ExcptEnum.PRODUCT_NOT_ONLINE_201001);
        }
        //如果是长期险产品
        String createType = detailVo.getCreateType();
        if (Objects.equals(createType, EnumProductCreateType.PERSON_LONG_INSURANCE.name())) {
            initLongInsuranceProduct(productId, regionName, planIds, detailVo);
            return detailVo;
        }
        List<Integer> salePlanIds =
                getWxProductPlans(productId, regionName)
                        .stream().map(SmPlanVO::getId).collect(Collectors.toList());
        //过滤销售计划id
        salePlanIds = filterSalePlanId(planIds, salePlanIds);
        if (CollectionUtils.isEmpty(salePlanIds)) {
            throw new BizException(ExcptEnum.NO_DATA_PERMISSION_801010.getCode(), "很抱歉，该产品不在本区域销售");
        }
        List<ProductMinAmtDTO> wxProductMinAmtDTOS = smProductMapper.listProductMinAmtByOrgName(Collections.singletonList(productId), regionName);
        if (!CollectionUtils.isEmpty(wxProductMinAmtDTOS) && wxProductMinAmtDTOS.get(0) != null) {
            final List<Integer> tmpPlanIds = salePlanIds;
            BigDecimal minAmount = wxProductMinAmtDTOS.stream().filter(pr -> tmpPlanIds.contains(pr.getPlanId())).map(ProductMinAmtDTO::getAmount)
                    .min(BigDecimal::compareTo).orElse(BigDecimal.ZERO);
            detailVo.setMinAmount(minAmount);
        }
        initWxProductInfo(productId, detailVo, salePlanIds);
        return detailVo;
    }

    /**
     * @param productId
     * @param regionName
     * @param planIds
     * @return
     */
    @Cacheable(cacheNames = PRODUCT_DETAIL, key = "#regionName+':'+#productId+':'+#planIds")
    public WxProductDetailVO getWxLongInsuranceProductDetailByIdAndRegionName(int productId, String regionName, String planIds) {
        WxProductDetailVO detailVo = wxProductMapper.getWxProductDetailById(productId);
        if (detailVo == null) {
            throw new BizException(ExcptEnum.PRODUCT_NOT_ONLINE_201001);
        }

        initLongInsuranceProduct(productId, regionName, planIds, detailVo);
        return detailVo;
    }


    /**
     * 初始化长期险产品
     *
     * @param productId
     * @param regionName
     * @param planIds
     * @param detailVo
     */
    private void initLongInsuranceProduct(int productId, String regionName, String planIds, WxProductDetailVO detailVo) {
        //获取销售区域的计划id集合
        List<Integer> salePlanIds =
                productService.getProductPlansByOrgLimit(productId, regionName)
                        .stream().map(SmPlanVO::getId).collect(Collectors.toList());
        //过滤销售计划id
        salePlanIds = filterSalePlanId(planIds, salePlanIds);
        //判断产品是否在销售区域
        if (CollectionUtils.isEmpty(salePlanIds)) {
            throw new BizException(ExcptEnum.NO_DATA_PERMISSION_801010.getCode(), "很抱歉，该产品不在本区域销售");
        }
        detailVo.setMinAmount(planMapper.selectMinAmountByPlanIdList(salePlanIds));
        initWxProductInfo(productId, detailVo, salePlanIds);
    }

    /**
     * 销售计划id和参数计划id的集合
     *
     * @param planIds
     * @param salePlanIds
     * @return
     */
    private List<Integer> filterSalePlanId(String planIds, List<Integer> salePlanIds) {
        if (org.apache.commons.lang3.StringUtils.isNotBlank(planIds)) {
            salePlanIds = salePlanIds.stream()
                    .filter(id -> Arrays.asList(planIds.trim().split(",")).contains(id + ""))
                    .collect(Collectors.toList());
        }
        return salePlanIds;
    }

    /**
     * 初始化微信产品信息
     * <p>
     * 1.保险条款
     * 2.海报详情
     * 3.产品试点区域
     * 4.条款内容
     * 5.自主确认条款
     * 6.客户告知书
     * 7.保障计划
     * 8.分享知识点
     * <p>
     * 8.自动绑卡
     *
     * @param productId
     * @param detailVo
     * @param salePlanIds
     */
    private void initWxProductInfo(int productId, WxProductDetailVO detailVo, List<Integer> salePlanIds) {
        detailVo.setClauses(wxProductMapper.listProductClausesByProductId(productId));
        detailVo.setHasPoster(wxProductPosterMapper.getProductPosterByProductId(productId) != null);
        detailVo.setSalesOrgs(wxProductMapper.listProductSalePlanByProductId(productId));
        detailVo.setClauseContent(wxProductMapper.getProductClauseContent(productId));
        detailVo.setConfirms(confirmMapper.selectByProductId(productId));
        SmProductNotify smProductNotify = productNotifyMapper.selectByProductId(productId);
        if (Objects.nonNull(smProductNotify) && Objects.equals(smProductNotify.getCustNotify(), 1)) {
            detailVo.setCustNotifyDetail(smProductNotify.getCustNotifyContent());
        }

        //s48 绑卡/自动续保 add by zhangjian
        detailVo.setSupportBindFlag(smOrderRenewBindService.validateProductCanRenewBind(productId + ""));
        //分享知识点
        detailVo.setShareKnowledge(shareKnowledgeService.getProductShareKnowledgeConfig(productId));

        detailVo.setCoveragePlan(buildWxCoveragePlanVO(productId, salePlanIds));
        detailVo.setAttrs(productService.getProductAttr(productId));

    }

    /**
     * 查询微信商品保障计划详情
     *
     * @param planId
     * @return
     */
    public List<WxPlanCoverageVO> getWxProductPlanCoverages(int planId) {
        return wxProductMapper.listWxProductPlanCoverages(planId);
    }

    /**
     * 微信查询产品所有计划
     *
     * @param productId
     * @return
     */
    @Cacheable(cacheNames = PRODUCT_PLAN, key = "#productId+':'+#regionName")
    public List<SmPlanVO> getWxProductPlans(int productId, String regionName) {

        SmProductDetailVO detail = productService.getProductById(productId);
        List<SmPlanVO> plans = productService.getProductPlansByOrgLimit(productId, regionName);

        if (CollectionUtils.isEmpty(plans)) {
            return Collections.emptyList();
        }

        List<SystemCommissionConfigDetail> commissionConfigDetailList = commissionConfigQueryService.queryCommissionProportionByProductId(productId);
        Map<Integer, List<SystemCommissionConfigDetail>> planCommissionMap = Optional.ofNullable(commissionConfigDetailList)
                .filter(org.apache.commons.collections.CollectionUtils::isNotEmpty)
                .orElseGet(ArrayList::new)
                .stream().collect(Collectors.groupingBy(SystemCommissionConfigDetail::getPlanId));

        plans.forEach(
                smPlanVO -> {
                    List<SystemCommissionConfigDetail> commissionConfigDetails = planCommissionMap.get(smPlanVO.getId());
                    if (CollectionUtils.isEmpty(commissionConfigDetails)) {
                        return;
                    }
                    BigDecimal minProportion = commissionConfigDetails.stream().map(SystemCommissionConfigDetail::getCommissionRate)
                            .filter(Objects::nonNull).min(BigDecimal::compareTo).orElse(null);
                    smPlanVO.setMultiProportion(commissionConfigDetails.size() > 1);
                    smPlanVO.setPaymentProportion(minProportion);
                }
        );

        if (detail.isLongType()) {
            return plans;
        }

        SmPlanFactorQuery query = new SmPlanFactorQuery();
        query.setProductId(productId + "");
        query.setAvailable(Boolean.TRUE);
        List<SmPlanFactorPriceDT> planPrices = smProductMapper.getPlanSpecPrice(query);

        Map<Integer, List<SmPlanFactorPriceDT>> priceMap = LambdaUtils.groupBy(planPrices, SmPlanFactorPriceDT::getPlanId);
        return plans.stream().filter(p -> p.getId() != null
                        && priceMap.containsKey(p.getId()))
                //找到最低金额
                .peek(p -> p.setMinPrice(priceMap.get(p.getId()).stream().map(SmPlanFactorPriceDT::getPrice)
                        .map(BigDecimal::new).min(BigDecimal::compareTo)
                        .orElse(BigDecimal.ZERO))).collect(Collectors.toList());
    }

    public SmPlanCmsVO getWxProductCms(int productId, String openId, String authorization) {
        WxSessionVO session = checkAuthority(openId, authorization);
        List<SmPlanVO> plans = getWxProductPlans(productId, session.getRegionName());
        SmPlanCmsVO planCmsVO = new SmPlanCmsVO();
        planCmsVO.setPlans(plans);
        // 客户经理才能显示产品详情的推广费比例
        if (session.isBindEmployee()) {
            WxUserSettingVO settingVO = wxUserSettingMapper.getUserShowCmsSetting(session.getUserId(), SmConstants.WX_USER_SETTING_CODE_CMS);
            if (settingVO != null) {
                planCmsVO.setShowCmsRatio(Boolean.valueOf(settingVO.getStValue()));
            } else {
                planCmsVO.setShowCmsRatio(Boolean.FALSE);
            }
        } else {
            planCmsVO.setShowCmsRatio(Boolean.FALSE);
        }
        if (!planCmsVO.getShowCmsRatio()) {
            planCmsVO.getPlans().forEach(p -> p.setPaymentProportion(null));
        }
        return planCmsVO;
    }

    /**
     * 微信查询产品所有计划
     *
     * @param productId
     * @return
     */
    public SmPlanCmsVO getWxProductCms(int productId, String openId, String authorization, String planIds) {
        SmPlanCmsVO wxProductCms = getWxProductCms(productId, openId, authorization);
        if (org.apache.commons.lang3.StringUtils.isNotBlank(planIds)) {
            //如果需要过滤计划
            List<String> ids = Arrays.asList(planIds.trim().split(","));
            List<SmPlanVO> plans = wxProductCms.getPlans();
            List<SmPlanVO> collect = plans.stream()
                    .filter(p -> ids.contains(p.getId() + "")).collect(Collectors.toList());
            wxProductCms.setPlans(collect);
        }
        return wxProductCms;
    }

    /**
     * 微信查询产品所有计划选项
     *
     * @param planId
     * @return
     */
    @Cacheable(cacheNames = PRODUCT_PLAN_FACTOR, key = "#planId")
    public SmPriceFactorOptionalsVO getWxFactorOptionalsVo(int planId) {
        return productService.getPlanPriceFactorOptions(planId);
    }

    /**
     * 查询微信产品因素价格
     *
     * @param query
     * @return
     */
    @Cacheable(cacheNames = PRODUCT_PLAN_PRICE, key = "#query.keyIds")
    public SmPlanFactorPriceDT getPlanFactorPrice(SmPlanFactorQuery query) {
        List<SmPlanFactorPriceDT> planSpecs = smProductMapper.getPlanSpecPrice(query);
        if (!planSpecs.isEmpty()) {
            return planSpecs.get(0);
        }
        throw new BizException(ExcptEnum.PRODUCT_PLAN_PRICE_ERROR_201004);
    }

    /**
     * 微信查询保险某个计划选项所有价格
     *
     * @param id
     * @return
     */
    @Cacheable(cacheNames = PRODUCT_PLAN_PRICE, key = "#id")
    public List<SmPlanFactorPriceDT> getAllPlanFactorPrices(int id) {
        SmPlanFactorQuery query = new SmPlanFactorQuery();
        query.setPlanId(id + "");
        return smProductMapper.getPlanSpecPrice(query);
    }

    /**
     * 查询产品详情基本信息
     *
     * @param productId
     * @return
     */
    public int getProductCompanyId(int productId) {
        return productService.getProductById(productId).getCompanyId();
    }

    /**
     * 查询计划详情信息
     *
     * @param planId
     * @return
     */
    public SmPlanVO getPlanById(int planId) {
        return productService.getPlanById(planId);
    }

    /**
     * 查询保险页面基本的保险公司配置参数
     *
     * @param planId
     * @return
     */
    @Cacheable(cacheNames = PRODUCT_FORM_OPTIONAL, key = "'planId'+#planId")
    public WxFormFieldsVO getWxFormFieldSettings(int planId) {
        WxFormFieldsVO wffVo = new WxFormFieldsVO();
        wffVo.setPtyRelationship(getCompanySettings(planId, SmConstants.PRODUCT_FORM_FIELD_TYPE_PTY_RELATIONSHIP));
        wffVo.setApiRelationship(sortApiRelationship(getCompanySettings(planId, SmConstants.PRODUCT_FORM_FIELD_TYPE_API_RELATIONSHIP)));
        wffVo.setIdTypes(getCompanySettings(planId, SmConstants.PRODUCT_FORM_FIELD_TYPE_IDTYPE));
        wffVo.setSexes(getCompanySettings(planId, SmConstants.PRODUCT_FORM_FIELD_TYPE_SEX));
        wffVo.setHouseTypes(getCompanySettings(planId, SmConstants.PRODUCT_FORM_FIELD_TYPE_HOUSE_TYPE));
        wffVo.setProvinces(getCompanySettings(planId, SmConstants.PRODUCT_FORM_FIELD_TYPE_PROVINCE_ABBR));
        wffVo.setCarNatures(getCompanySettings(planId, SmConstants.PRODUCT_FORM_FIELD_TYPE_CAR_NATURE));
        wffVo.setStudentTypes(getCompanySettings(planId, SmConstants.PRODUCT_FORM_FIELD_TYPE_STUDENT_TYPE));
        wffVo.setSchoolTypes(getCompanySettings(planId, SmConstants.PRODUCT_FORM_FIELD_TYPE_SCHOOL_TYPE));
        wffVo.setSchoolNatures(getCompanySettings(planId, SmConstants.PRODUCT_FORM_FIELD_TYPE_SCHOOL_NATURE));
        wffVo.setHhrTypes(getCompanySettings(planId, SmConstants.PRODUCT_FORM_FIELD_HHR_TYPE));
        wffVo.setIsSecurity(getCompanySettings(planId, SmConstants.SOCIAL_SECURITY));
        wffVo.setAllSetting(cmpySettingService.getCompanySettingMap(planId));
        return wffVo;
    }

    /**
     * 查询保险公司配置
     *
     * @param planId
     * @param fieldCode
     * @return
     */
    public List<WxFormFieldOptionVO> getCompanySettings(int planId, String fieldCode) {
        SmPlanVO planVo = getPlanById(planId);
        if (planVo == null) {
            throw new BizException(ExcptEnum.PRODUCT_ERROR_201011);
        }
        CmpySettingQuery query = new CmpySettingQuery();
        query.setFhProductId("" + planVo.getFhProductId());
        query.setCompanyId("" + getProductCompanyId(planVo.getProductId()));
        query.setChannel(planVo.getChannel());
        query.setFieldCode(fieldCode);
        List<WxFormFieldOptionVO> settingVos = cmpySettingService.getWxCompanySettingsList(query);
        if (!settingVos.isEmpty()) {
            return settingVos;
        }
        query.setFhProductId(null);
        return cmpySettingService.getWxCompanySettingsList(query);
    }

    /**
     * 查询产品投保信息录入列表
     *
     * @param productId
     * @return
     */
    @Cacheable(cacheNames = PRODUCT_FORM_FIELD, key = "#productId")
    public List<SmProductFormFieldCombDTO> getProductFormFields(int productId) {
        return productService.getProductFormFields(productId);
    }

    /**
     * 查询产品职业列表top排行
     * 热门职业功能需要关闭，因为职业表已经更新了多个版本，历史保单的职业信息可能跟当前版本的职业不匹配
     * @param productId
     * @param size
     * @return
     */
    public List<WxTreeVO> getOccupationHotList(int productId, int size) {
        return Collections.emptyList();
//        return occupationService.getOccupationHotListInCache(productId, size);
    }

    /**
     * 查询微信产品海报详情
     *
     * @param query
     * @return
     */
    public WxProductPosterBase64VO getWxProductPoster(PosterGenQuery query) {
        WxSessionVO session = checkAuthority(query.getOpenId(), query.getAuthorization());
        if (session.isBindWeixin()) {
            throw new BizException(ExcptEnum.POSTER_ACCESS_DENY);
        }
        WxProductPosterVO posterVO = wxProductPosterMapper.getProductPosterByProductId(query.getProductId());
        if (posterVO == null) {
            throw new BizException(ExcptEnum.POSTER_SETTING_ERROR);
        }
        PosterGenDTO dto = new PosterGenDTO();
        dto.setPosterUrl(posterVO.getPosterUrl());
        dto.setUserMobile(session.getUserMobile());
        dto.setUserName(session.getUserName());
        try {
            dto.setWxImageBytes(CommonUtil.getBytesFromUrl(session.getWxImgUrl()));
        } catch (Exception e) {
            throw new BizException(ExcptEnum.POSTER_GEN_ERROR, e);
        }
        dto.setQrBytes(getQrcodeBase64(query));
        String imageBase64 = posterService.genPoster(dto);
        return new WxProductPosterBase64VO(imageBase64, posterVO.getWords());
    }

    /**
     * 查询产品可续保产品
     *
     * @param productId
     * @return
     */
    public List<WxProductRenewVO> getWxProductRenews(int productId, String openId, String authorization) {
        WxSessionVO session = checkAuthorityEpAg(openId, authorization);
        String orgFullName = session.getOrganizationFullName();
        String queryOrgName = null;
        //modify by zhangjian 2020-11-11去掉办公室判断
        //if (orgFullName != null && orgFullName.endsWith("办公室")) {
        if (super.isAreaOffice(session.getHrOrgId())) {
            //queryOrgName = orgFullName.substring(0, orgFullName.length() - 3);
            queryOrgName = super.getBranchOrgName(session.getOrgCode());
        } else {
            queryOrgName = session.getRegionName();
        }
        List<Integer> canSalesProductIds = smProductMapper.listProductSalesOrgsByOrgPath(queryOrgName);
        return productService.getWxRenewProductsByProductId(productId)
                .stream()
                .filter(r -> canSalesProductIds.stream().anyMatch(cs ->
                        Objects.equals(r.getRenewProductId(), cs)
                ))
                .peek(pro -> {
                    //修改泛华的地址
                    if (Objects.equals(pro.getChannel(), EnumChannel.FH.getCode())
                            && Objects.equals(pro.getApiType(), EnumProductApiType.H5.getType())) {
                        pro.setH5Url(getFhH5Url(pro.getH5Url()));
                    }
                })
                .collect(Collectors.toList());
    }

    /**
     * 查询微信首页轮播图
     *
     * @param orgName
     * @return
     */
    @Cacheable(cacheNames = WX_LBT_IMAGE, key = "'orgName'+ #orgName")
    public List<SystemSettingVO> getWxHomeLbtImage(String orgName) {
        List<SystemSettingVO> lbtImages = systemSettingService.getSystemSettings(WxConstant.WX_HOME_PAGE_IMAGE, 1);
        List<Integer> canSalesProductIds = wxProductMapper.listProductSalesOrgsByOrgPath(orgName);
        return lbtImages.stream().filter(lbt -> {
            String urlPath = lbt.getValue();
            if (urlPath != null) {
                urlPath = urlPath.trim();
            }
            if (StringUtils.isEmpty(urlPath)) {
                return true;
            } else if (urlPath.startsWith(LBT_PRODUCT_URL_PREFIX)) {
                String urlProductId = urlPath.substring(LBT_PRODUCT_URL_PREFIX.length());
                return canSalesProductIds.stream().anyMatch(sp -> Objects.equals(sp.toString(), urlProductId));
            } else {
                return true;
            }
        }).collect(Collectors.toList());
    }

    /**
     * 查询微信团险产品保障项目金额列表
     *
     * @param openId
     * @param authorization
     * @param productId
     * @return
     */
    public List<SmProductCoverageAmountItemVO> getWxGlProductCoverages(String openId, String authorization, int productId) {
        checkAuthority(openId, authorization);
        return productService.getProductCoverageAmountList(productId);
    }

    /**
     * 二维码生成
     *
     * @param dto
     * @return
     */
    @Cacheable(cacheNames = "wxUserQrcode", key = "#dto.height+''+#dto.width+''+#dto.margin+''+#dto.url")
    public String getQrcodeBase64(QrcodeDTO dto) {
        if (dto.getMargin() == null) {
            dto.setMargin(2);
        }
        return QrcodeUtil.getQrcodeBase64(dto, false);
    }

    /**
     * 获取分页二维码bytes
     *
     * @param query
     * @return
     */
    private byte[] getQrcodeBase64(PosterGenQuery query) {
        QrcodeDTO qrcodeDTO = new QrcodeDTO();
        qrcodeDTO.setHeight(360);
        qrcodeDTO.setWidth(360);
        qrcodeDTO.setMargin(2);
        qrcodeDTO.setUrl(query.getProductUrl());
        try {
            return Base64Utils.decode(getQrcodeBase64(qrcodeDTO));
        } catch (Exception e) {
            throw new BizException(ExcptEnum.POSTER_GEN_ERROR, e);
        }
    }

    /**
     * 微信团险立即报价验证接口
     *
     * @param dto
     * @return
     */
    public String checkWxGlProductQuote(WxGlProductQuoteDTO dto) {
        applyHandler.applyCheck(dto);
        return getWxGlProductQuote(dto).getResultId();
    }

    @Autowired
    private InquiryService inquiryService;

    @Autowired
    private SmProductMapper productMapper;

    /**
     * 微信团险具体报价结果
     * 生成报价书
     *
     * @param dto
     * @return
     */
    public WxGlProductQuoteResultVO getWxGlProductQuote(WxGlProductQuoteDTO dto) {
        log.info("[{}]-开始试算保费......", dto.getProductId());
        WxSessionVO sessionVO = checkAuthority(dto.getOpenId(), dto.getAuthorization());
        Integer productId = dto.getProductId();
        String oldOrderId = dto.getOldOrderId();

        PlanQuery plan = dto.getPlan();

        if (plan == null) {
            throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(), "请选择产品套餐");
        }

        SmProductDetailVO pd = productMapper.getProductById(productId);
        if (pd == null) {
            throw new MSBizNormalException(ExcptEnum.DATA_NOT_FOUNT.getCode(), ExcptEnum.DATA_NOT_FOUNT.getMsg());
        }

        int companyId = pd.getCompanyId();

        Integer planId = plan.getId();

        WxGlProductQuoteResultVO result = new WxGlProductQuoteResultVO();
        result.setProductId(productId);
        result.setProductName(pd.getProductName());
        result.setPlanId(planId);
        result.setThumbnailImageUrl(pd.getThumbnailImageUrl());

        List<String> jobClassList = Arrays.asList(pd.getGlOcpnGroup().split(","));
        jobClassList.sort((a, b) -> a.compareTo(b));
        result.setOccupationGroups(jobClassList);
        result.getOccupationGroups().sort(String::compareTo);
        result.initFiled();

        Integer perQty = dto.getOcpnPersons().stream().map(WxGlProductQuoteDTO.OccupationPerson::getPerQty).reduce(0, Integer::sum);
        result.setTotalPerQty(perQty);

        result.setCoveragePeriod(pd.getValidPeriod());
        /**
         * ★修正承保期限
         */
        TimeFactorItem timeFactor = dto.getTimeFactor();
        if (timeFactor != null && timeFactor.selfCheck()) {
            timeFactor.fixFactorName();
            result.setCoveragePeriod(timeFactor.getFactorName());
        }

        /**
         * 计算每一个节点的保费：某类职业的单人保费
         */
        List<SmProductCoveragePremiumDTO> coveragePremiums = queryPremiumTable(productId, planId);
        RFQHolder holder = inquiryService.buildRFQHolder(productId, planId);
        if (timeFactor != null) {
            RateFacor rf = new RateFacor(timeFactor.getFactorValue(), timeFactor.getFactorName(), timeFactor.getFlow());
            holder.setTimeFactor(rf);
        }

        List<CoverageQuery> coverages = plan.getCoverages();
        result.setPremiums(buildPermiumTable(holder, coverages, jobClassList, coveragePremiums));

        Map<String, BigDecimal> quoteMap = tryPremiumByOne(dto, holder, jobClassList, coveragePremiums);

        List<WxGlProductQuoteResultVO.OcpnQuote> productQuote = buildQuoteV2(companyId, productId, dto, quoteMap);
        result.setQuotes(productQuote);

        SmPlanVO planVO = smProductMapper.getPlanById(planId);
        if (planVO != null) {
            result.setPlanName(plan.getPlanName());
        }
        /**
         * 汇总责任保费
         */
        summaryCoveragePremium(pd.getValidPeriod(), timeFactor, quoteMap, result);
        /**
         * 计算当前计划书的总保费
         */
        summaryPremium(result);
        /**
         * 保存计划书
         */
        SmGlProductQuoteDTO quoteSaveDTO = buildSmGlProductQuoteDTO(sessionVO, result);
        quoteSaveDTO.setQuoteType(1);
        quoteSaveDTO.setOldOrderId(oldOrderId);
        smProductMapper.insertGlProductQuotePlan(quoteSaveDTO);
        result.setResultId(String.valueOf(quoteSaveDTO.getSpqpId()));
        return result;
    }

    /**
     * 按产品职业类别计算单人保费
     *
     * @return
     */
    public Map<String, BigDecimal> tryPremiumByOne(WxGlProductQuoteDTO data,
                                                   RFQHolder holder,
                                                   List<String> jobClassList,
                                                   List<SmProductCoveragePremiumDTO> premiumTable) {
        PlanQuery plan = data.getPlan();
        TimeFactorItem timeFactor = data.getTimeFactor();

        if (CollectionUtils.isEmpty(premiumTable)) {
            throw new MSBizNormalException(ExcptEnum.DATA_NOT_FOUNT.getCode(), "产品未配置费率表，无法计算保费");
        }
        Map<String, List<SmProductCoveragePremiumDTO>> premiumJobMap = LambdaUtils.groupBy(premiumTable, SmProductCoveragePremiumDTO::getOccupationGroup);

        Map<String, BigDecimal> quoteMap = new HashMap<>();
        for (String jobclass : jobClassList) {
            GroupInquiry req = new GroupInquiry();
            req.setPlan(plan);
            req.setTimeFactor(timeFactor);
            req.setProductId(data.getProductId());

            List<SmProductCoveragePremiumDTO> premiums = premiumJobMap.get(jobclass);
            if (CollectionUtils.isEmpty(premiums)) {
                continue;
            }
            Map<Integer, BigDecimal> premiumMap = LambdaUtils.safeToMap(premiums, SmProductCoveragePremiumDTO::getSpcaId, SmProductCoveragePremiumDTO::getPremium);
            /**
             * 开始试算单人保费...
             */
            BigDecimal premium = inquiryService.tryPermiumByOneV2(req, premiumMap, holder);
            quoteMap.put(jobclass, premium);
        }
        return quoteMap;
    }

    private WxGlProductQuoteResultVO.ProductPremium initCoveragePremiumSummary(String coveragePeriod) {
        WxGlProductQuoteResultVO.ProductPremium premiumSummary = new WxGlProductQuoteResultVO.ProductPremium();
        String yearPeriod = UnitConvertor.month2Year(coveragePeriod);
        String title = buildTitle(yearPeriod);

        premiumSummary.setCoveragePeriod(yearPeriod);
        premiumSummary.setCvgItemName(title);
        premiumSummary.initPremium();
        return premiumSummary;
    }

    private WxGlProductQuoteResultVO.ProductPremium initShortTermPremiumSummary(TimeFactorItem timeFactor) {
        if (timeFactor == null || (!timeFactor.selfCheck())) {
            return null;
        }
        WxGlProductQuoteResultVO.ProductPremium premiumSummary = new WxGlProductQuoteResultVO.ProductPremium();

        String title = buildTitle(timeFactor.getFactorName());
        premiumSummary.setCoveragePeriod(timeFactor.getFactorName());
        premiumSummary.setCvgItemName(title);

        premiumSummary.initPremium();
        return premiumSummary;
    }

    /**
     * 按责任&职业汇总保费
     *
     * @param result
     */
    private void summaryCoveragePremium(String coveragePeriod, TimeFactorItem timeFactor, Map<String, BigDecimal> quoteMap, WxGlProductQuoteResultVO result) {
        WxGlProductQuoteResultVO.ProductPremium premiumSummary = initCoveragePremiumSummary(coveragePeriod);

        WxGlProductQuoteResultVO.ProductPremium shortTermSummary = initShortTermPremiumSummary(timeFactor);

        List<String> occupationGroups = result.getOccupationGroups();

        for (int i = 0, len = occupationGroups.size(); i < len; i++) {

            List<WxGlProductQuoteResultVO.ProductPremium> premiums = result.getPremiums();
            String occup = occupationGroups.get(i);

            BigDecimal sum = summaryByCoverage(i, occup, premiums);
            /**
             * 此处对应的其实就是之前计算出来的某类职业下的单人报价保费
             */
            if (shortTermSummary != null) {
                BigDecimal quotePremium = quoteMap.get(occup);
                shortTermSummary.addPremium(occup, quotePremium);
            }
            premiumSummary.addPremium(occup, sum.setScale(0, RoundingMode.UP));
        }
        result.setSummary(premiumSummary);
        result.setShortTermSummary(shortTermSummary);
    }

    /**
     * 汇总保费
     *
     * @param occup
     * @param premiums
     * @return
     */
    private BigDecimal summaryByCoverage(int index, String occup, List<WxGlProductQuoteResultVO.ProductPremium> premiums) {
        BigDecimal sum = BigDecimal.ZERO;
        for (WxGlProductQuoteResultVO.ProductPremium premium : premiums) {
            if (premium == null) {
                return null;
            }
            List<WxGlProductQuoteResultVO.PremiumMap> ops = premium.getPremiumMap();
            if (ops != null) {
                Map<String, WxGlProductQuoteResultVO.PremiumMap> opm = LambdaUtils.safeToMap(ops, WxGlProductQuoteResultVO.PremiumMap::getOccupationGroup);
                WxGlProductQuoteResultVO.PremiumMap entry = opm.get(occup);
                if (entry != null && entry.getPremium() != null) {
                    sum = sum.add(entry.getPremium());
                }
            } else {
                BigDecimal p = ContailerTools.safeGet(premium.getPremiums(), index);
                if (p == null) {
                    return null;
                }
                sum = sum.add(p);
            }
        }
        return sum;
    }

    /**
     * TODO 构建每一个责任下不同职业的保费列表
     * 修改时间：21-12-22，责任保费列表需要这算费率
     * 改动点：要将责任调整因子、企业风险调整系数计算进去;
     * 责任保费=责任费率*责任调整因子*（1+企业风险调整系数）
     *
     * @param holder
     * @param coverages
     * @param coveragePremiums
     */
    public List<WxGlProductQuoteResultVO.ProductPremium> buildPermiumTable(RFQHolder holder,
                                                                           List<CoverageQuery> coverages,
                                                                           List<String> jobclassList,
                                                                           List<SmProductCoveragePremiumDTO> coveragePremiums) {
        Map<Integer, List<SmProductCoveragePremiumDTO>> premiumMap = LambdaUtils.groupBy(coveragePremiums, SmProductCoveragePremiumDTO::getSpcaId);
        List<WxGlProductQuoteResultVO.ProductPremium> data = new ArrayList<>();

        for (CoverageQuery cp : coverages) {
            WxGlProductQuoteResultVO.ProductPremium pp = new WxGlProductQuoteResultVO.ProductPremium();
            pp.initPremium();

            CoverageAmountVo amount = cp.getAmount();
            if (amount != null) {
                pp.setSpcId(amount.getSpcId());
                pp.setSpcaId(amount.getSpcaId());
                Integer amountId = amount.getSpcaId();
                List<SmProductCoveragePremiumDTO> premiums = premiumMap.get(amountId);
                if (!CollectionUtils.isEmpty(premiums)) {
                    Map<String, SmProductCoveragePremiumDTO> premium4JobClassMap = LambdaUtils.safeToMap(premiums, SmProductCoveragePremiumDTO::getOccupationGroup);
                    pp.setCvgItemName(premiums.get(0).getCvgItemName());
                    pp.setCvgNotice(premiums.get(0).getCvgNotice());

                    jobclassList.stream().forEach(p -> {
                        SmProductCoveragePremiumDTO vo = premium4JobClassMap.get(p);
                        if (vo != null) {
                            BigDecimal d = vo.getPremium();
                            BigDecimal a = BigDecimal.ZERO;
                            if (d != null) {
                                a = inquiryService.tryPermium4Coverage(d, holder, cp);
                            }
                            pp.addPremium(p, a);
                        }
                    });
                }
            }
            pp.setDutyFactorList(cp.getDutyFactor());
            data.add(pp);
        }
        return data;
    }


    /**
     * 查询费率表
     *
     * @param productId
     * @param planId
     * @return
     */
    public List<SmProductCoveragePremiumDTO> queryPremiumTable(Integer productId, Integer planId) {
        List<SmProductCoveragePremiumDTO> coveragePremiums = smProductMapper.queryPremiumByPlan(productId, planId);
        if (CollectionUtils.isEmpty(coveragePremiums)) {
            coveragePremiums = smProductMapper.listProductCoveragePremiums(productId);
        }
        return coveragePremiums;
    }

    @Autowired
    private PolicyMapper policyMapper;

    /**
     * 查询团险报价记录
     *
     * @param query
     * @return
     */
    public PageInfo<WxGlProductQuoteListVO> getWxGlProductQuotePlanList(WxGlProductQuoteQuery query) {
        WxSessionVO sessionVO = checkAuthorityEpAg(query.getOpenId(), query.getAuthorization());
        if (sessionVO.isBindEmployee()) {
            query.setUserId(sessionVO.getUserId());
        } else if (sessionVO.isBindAgent()) {
            query.setAgentId(sessionVO.getAgentId());
        }
        PageHelper.startPage(query.getPage(), query.getSize());
        List<WxGlProductQuoteListVO> wxGlProductQuoteListVOList = productQuoteMapper.listWxGlProductQuotePlans(query);
        if (!CollectionUtils.isEmpty(wxGlProductQuoteListVOList)) {
            wxGlProductQuoteListVOList.stream().forEach(x -> {
                //有待续保订单的计划书则打上续保标识
                if (x.getOldOrderId() != null) {
                    x.setRealRenewFlag(Boolean.TRUE);
                } else {
                    x.setRealRenewFlag(Boolean.FALSE);
                }
            });
        }
        return new PageInfo<>(wxGlProductQuoteListVOList);
    }

    /**
     * S51-询价
     * 按职业类别计算标准保费
     *
     * @return
     */
    public List<WxGlProductQuoteResultVO.OcpnQuote> buildQuote(Integer companyId, WxGlProductQuoteDTO data, Map<String, BigDecimal> quoteMap) {
        List<WxGlProductQuoteDTO.OccupationPerson> insuredList = data.getOcpnPersons();
        List<WxGlProductQuoteResultVO.OcpnQuote> rtn = new ArrayList<>();
        for (WxGlProductQuoteDTO.OccupationPerson person : insuredList) {
            BigDecimal premium = quoteMap.get(person.getOccupationGroup());
            rtn.add(buildQuote(companyId, premium, person));
        }
        return rtn;
    }

    /**
     * S51-询价
     * 按职业类别计算标准保费
     *
     * @return
     */
    public List<WxGlProductQuoteResultVO.OcpnQuote> buildQuoteV2(Integer companyId, Integer productId, WxGlProductQuoteDTO data, Map<String, BigDecimal> quoteMap) {
        List<WxGlProductQuoteDTO.OccupationPerson> insuredList = data.getOcpnPersons();
        if (CollectionUtils.isEmpty(insuredList)) {
            log.warn("被保人职业信息不能为空:{}", data);
            return Collections.emptyList();
        }
        List<WxGlProductQuoteResultVO.OcpnQuote> rtn = new ArrayList<>(16);

        List<WxTreeVO> occupationTable = occupationService.getOccupationList(companyId, productId);
        Map<String, WxTreeVO> occupationMap = new HashMap<>(16);
        for (WxTreeVO vo : occupationTable) {
            String value = vo.getValue();
            occupationMap.put(value, vo);
        }
        for (WxGlProductQuoteDTO.OccupationPerson person : insuredList) {
            BigDecimal premium = quoteMap.get(person.getOccupationGroup());
            String occupationCode = person.getOccupationCode();
            WxTreeVO tree = occupationMap.get(occupationCode);
            if (tree == null) {
                log.warn("该职业未配置:{}", occupationCode);
                continue;
            }

            WxGlProductQuoteResultVO.OcpnQuote quote = new WxGlProductQuoteResultVO.OcpnQuote();
            quote.setOccupationName(tree.getName());
            quote.setOccupationCode(tree.getValue());
            quote.setOccupationGroup(tree.getType());
            quote.setPerQty(person.getPerQty());
            quote.setStdPemium(premium);
            rtn.add(quote);
        }
        return rtn;
    }


    /**
     * 微信保存团险计划书
     *
     * @param dto
     * @return
     */
    public int saveWxGlProductQuotePlan(WxGlProductQuotePlanDTO dto) {
        WxSessionVO session = checkAuthority(dto.getOpenId(), dto.getAuthorization());
        String resultStr = productQuoteMapper.getProductQuoteDetail(Integer.valueOf(dto.getResultId()));
        if (resultStr == null) {
            throw new BizException(ExcptEnum.OPERATION_TIMEOUT_504005);
        }
        SmGlProductQuoteDTO quoteSaveDTO = new SmGlProductQuoteDTO();
        quoteSaveDTO.setCustomerName(dto.getCustomerName());
        quoteSaveDTO.setQuoteType(2);
        quoteSaveDTO.setSpqpId(Integer.valueOf(dto.getResultId()));
        smProductMapper.updateGlProductQuotePlan(quoteSaveDTO);
        return quoteSaveDTO.getSpqpId();
    }

    /**
     * 获取下一个理赔编号
     *
     * @return
     */
    private String getNextQuoteNo() {
        RedisUtil<String, Integer> localRedisUtil = SpringFactoryUtil.getBean(RedisUtil.class);
        String tds = DateUtil.format(new Date(), "yyMMdd");
        String redisClaimKey = SmConstants.REDIS_KEY_QUOTE_NO + tds;
        Integer oldNo = localRedisUtil.get(redisClaimKey);
        if (oldNo == null) {
            DLockTemplate lockTemplate = SpringFactoryUtil.getBean(DLockTemplate.class);
            String dLockKey = "claim_no" + tds;
            try {
                lockTemplate.lock(dLockKey, 2);
                String maxClaimNo = productQuoteMapper.getMaxProductQuoteNo();
                if (maxClaimNo == null || !Objects.equals(maxClaimNo.substring(2, 8), tds)) {
                    localRedisUtil.setnx(redisClaimKey, 0);
                } else {
                    oldNo = Integer.valueOf(maxClaimNo.substring(8));
                    localRedisUtil.setnx(redisClaimKey, oldNo);
                }
                redisUtil.expire(redisClaimKey, CommonUtil.getTodayNextSeconds());
            } finally {
                lockTemplate.unLock(dLockKey);
            }
        }
        long newNo = localRedisUtil.increment(redisClaimKey, 1);
        return String.format(SmConstants.QUOTE_NO_PREFIX + tds + "%04d", newNo);
    }

    @Deprecated
    private WxGlProductQuoteResultVO.ProductPremium buildPermium(Integer spcaId, List<SmProductCoveragePremiumDTO> coveragePremiums) {
        WxGlProductQuoteResultVO.ProductPremium pp = new WxGlProductQuoteResultVO.ProductPremium();
        pp.setPremiums(new ArrayList<>());
        coveragePremiums.stream().filter(cp -> Objects.equals(cp.getSpcaId(), spcaId))
                .sorted(Comparator.comparing(SmProductCoveragePremiumDTO::getOccupationGroup))
                .forEach(cp -> {
                    pp.setCvgItemName(cp.getCvgItemName());
                    pp.setCvgNotice(cp.getCvgNotice());
                    pp.getPremiums().add(cp.getPremium());
                });
        return pp;
    }

    /**
     * 统计汇总
     *
     * @param spcaIds
     * @param perQty
     * @param occupationVO
     * @param coveragePremiums
     */
    @Deprecated
    private WxGlProductQuoteResultVO.OcpnQuote buildQuote(List<Integer> spcaIds, int perQty, SmOccupationVO occupationVO, List<SmProductCoveragePremiumDTO> coveragePremiums) {
        WxGlProductQuoteResultVO.OcpnQuote quote = new WxGlProductQuoteResultVO.OcpnQuote();
        BigDecimal stdPremium = coveragePremiums.stream()
                .filter(cp -> spcaIds.stream().anyMatch(spi -> Objects.equals(spi, cp.getSpcaId())) && Objects.equals(cp.getOccupationGroup(), occupationVO.getOccupationGroup()))
                .map(SmProductCoveragePremiumDTO::getPremium).filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        quote.setOccupationName(occupationVO.getOccupationName());
        quote.setOccupationCode(occupationVO.getOccupationCode());
        quote.setOccupationGroup(occupationVO.getOccupationGroup());
        quote.setPerQty(perQty);
        quote.setStdPemium(stdPremium);
        return quote;
    }

    /**
     * 构造[单人保费]报价单
     *
     * @param companyId
     * @param premium
     * @param premium
     */
    @Deprecated
    public WxGlProductQuoteResultVO.OcpnQuote buildQuote(Integer companyId,
                                                         BigDecimal premium,
                                                         WxGlProductQuoteDTO.OccupationPerson person) {

        SmOccupationVO job = occupationMapper.getOccupationByCompanyIdAndCode(companyId, person.getOccupationCode());

        WxGlProductQuoteResultVO.OcpnQuote quote = new WxGlProductQuoteResultVO.OcpnQuote();
        quote.setOccupationName(job.getOccupationName());
        quote.setOccupationCode(job.getOccupationCode());
        quote.setOccupationGroup(job.getOccupationGroup());
        quote.setPerQty(person.getPerQty());
        quote.setStdPemium(premium);
        return quote;
    }


    /**
     * 计算此次报价的总保费
     *
     * @param result
     */
    private void summaryPremium(WxGlProductQuoteResultVO result) {
//        BigDecimal totalAmount = result.getQuotes().stream().map(t -> t.getStdPemium().multiply(new BigDecimal(t.getPerQty()))).reduce(BigDecimal.ZERO, BigDecimal::add);


        int totalQty = result.getTotalPerQty();

        /**
         * 计算人数折扣保费
         */
        BigDecimal totalAmount = BigDecimal.ZERO;
        BigDecimal actualAmount = BigDecimal.ZERO;
        BigDecimal discount = inquiryService.matchDiscount(result.getProductId(), totalQty);
        List<WxGlProductQuoteResultVO.OcpnQuote> quotes = result.getQuotes();
        for (WxGlProductQuoteResultVO.OcpnQuote entry : quotes) {
            BigDecimal stdPremium = entry.getStdPemium();
            BigDecimal qty = new BigDecimal(entry.getPerQty());
            totalAmount = totalAmount.add(stdPremium.multiply(qty));

            BigDecimal premium = inquiryService.tryPermiumByDiscount(stdPremium, discount);
            /**
             * 单人保费-向上取整
             */
            premium = premium.setScale(0, RoundingMode.UP);
            actualAmount = actualAmount.add(premium.multiply(qty));

        }
        result.setTotalAmount(totalAmount);
        result.setDiscount(discount);
        result.setActualAmount(actualAmount);
    }


    private String buildTitle(String coveragePeriod) {
        String title = "保费合计(年)";
        if (UnitConvertor.matchPeriod(coveragePeriod)) {
            String unit = coveragePeriod.substring(coveragePeriod.length() - 1);
            title = String.format("保费合计(%s)", unit);
        }
        return title;
    }

    /**
     * 构建SmGlProductQuoteDTO
     *
     * @param session
     * @param result
     * @return
     */
    private SmGlProductQuoteDTO buildSmGlProductQuoteDTO(WxSessionVO session, WxGlProductQuoteResultVO result) {
        SmGlProductQuoteDTO dto = new SmGlProductQuoteDTO();
        BeanUtils.copyProperties(result, dto);
        dto.setQuoteNo(getNextQuoteNo());
        dto.setAgentId(session.getAgentId());
        dto.setCustomerAdminId(session.getUserId());
        dto.setCustomerAdminMobile(session.getUserMobile());
        dto.setCustomerAdminName(session.getUserName());
        dto.setCustomerAdminImgUrl(session.getWxImgUrl());
        dto.setCustomerAdminType(session.getUserType());
        dto.setQuoteDetail(JSON.toJSONString(result));
        return dto;
    }

    /**
     * 查询微信团险计划书详情
     *
     * @param spqpId
     * @param openId
     * @param authorization
     * @return
     */
    public WxGlProductQuoteVO getWxGlProductQuotePlan(int spqpId, String openId, String authorization) {
        checkAuthority(openId, authorization);
        WxGlProductQuoteVO vo = wxProductMapper.getGlProductQuotePlan(spqpId);
        List<CoverageAmountPo> caps = wxProductMapper.getCoverageAmountList(vo.getProductId());
        if (StringUtils.isEmpty(vo.getOldOrderId())) {
            vo.setRealRenewFlag(Boolean.FALSE);
        } else {
            vo.setRealRenewFlag(Boolean.TRUE);
        }
        vo.setAmounts(caps);
        return vo;
    }

    /**
     * 删除微信团险计划书详情
     *
     * @param spqpId
     * @param openId
     * @param authorization
     * @return
     */
    public void deleteWxGlProductQuotePlan(int spqpId, String openId, String authorization) {
        checkAuthority(openId, authorization);
        wxProductMapper.deleteWxGlProductQuotePlan(spqpId);
    }


    /**
     * 创建微信产品详情保障项目计划价格表
     *
     * @param productId
     * @return
     */
    private WxCoveragePlanVO buildWxCoveragePlanVO(int productId, List<Integer> planIds) {
        List<SmProductCoverageAmountVO> coverageAmounts = smProductMapper.listProductCoverageAmounts(productId);

        //筛选区域需要卖的计划
        List<SmPlanVO> plans = smProductMapper.listProductPlansById(String.valueOf(productId), true)
                .stream().filter(ps -> CollectionUtils.isEmpty(planIds)
                        || planIds.stream().anyMatch(p -> Objects.equals(p, ps.getId())))
                .collect(Collectors.toList());


        List<SmProductCoverageVO> coverages = smProductMapper.listProductCoverages(productId);

        WxCoveragePlanVO coveragePlan = new WxCoveragePlanVO();
        List<WxCoveragePlanVO.CoverageAmount> planCoverageAmounts = new ArrayList<>();
        coveragePlan.setPlanNames(plans.stream()
                //过滤掉不需要用的计划
                .filter(pl -> CollectionUtils.isEmpty(planIds)
                        || planIds.stream().anyMatch(p -> Objects.equals(p, pl.getPlanId())))
                .map(p -> {
                    WxCoveragePlanVO.Plan plan = new WxCoveragePlanVO.Plan();
                    plan.setPlanId(p.getPlanId());
                    plan.setPlanName(p.getPlanName());
                    plan.setCoverages(wxProductMapper.listWxProductPlanCoverages(p.getPlanId()));
                    return plan;
                }).collect(Collectors.toList()));
        coverages.forEach(cvg -> {
            WxCoveragePlanVO.CoverageAmount planCoverageAmount = new WxCoveragePlanVO.CoverageAmount();
            planCoverageAmount.setCoverageName(cvg.getCvgItemName());
            planCoverageAmount.setCoverageCode(cvg.getCvgCode());
            planCoverageAmount.setCoverageRespCode(cvg.getCvgRespType());
            List<String> cvgNotices = new ArrayList<>();
            plans.forEach(plan -> {
                Optional<SmProductCoverageAmountVO> optional = coverageAmounts.stream().filter(ca -> Objects.equals(ca.getPlanId(), plan.getId())
                        && Objects.equals(cvg.getSpcId(), ca.getSpcId())).findFirst();
                if (optional.isPresent()) {
                    cvgNotices.add(optional.get().getCvgNotice());
                } else {
                    cvgNotices.add("");
                }
            });
            planCoverageAmount.setCvgNotices(cvgNotices);
            planCoverageAmounts.add(planCoverageAmount);
        });
        coveragePlan.setCoverageAmounts(planCoverageAmounts);

        List<SmPlanFactorPriceDT> planPrices = smProductMapper.listPlanFactorPrices(productId, planIds);
        List<BigDecimal> planMinPrices = new ArrayList<>();
        plans.forEach(plan -> {
            BigDecimal minPrice = planPrices.stream()
                    .filter(pp -> Objects.equals(pp.getPlanId(), plan.getId()) && !StringUtils.isEmpty(pp.getPrice()))
                    .map(pp -> new BigDecimal(pp.getPrice()))
                    .filter(p -> p.compareTo(BigDecimal.ZERO) > 0)
                    .min(BigDecimal::compareTo)
                    .orElse(BigDecimal.ZERO);
            planMinPrices.add(minPrice);
        });
        coveragePlan.setPlanMinPrices(planMinPrices);
        return coveragePlan;
    }

    /**
     * 投保人与被保人关系排序
     *
     * @return
     */
    private List<WxFormFieldOptionVO> sortApiRelationship(List<WxFormFieldOptionVO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        list.sort((t1, t2) -> RS_SORT_MAP.getOrDefault(t2.getOptionName(), -1) - RS_SORT_MAP.getOrDefault(t1.getOptionName(), -1));
        return list;
    }

    /**
     * 微信产品选择数据
     */
    public PageInfo<ProductSelectionVO> productSelectionPage(Pageable page) {
        PageHelper.startPage(page.getPage(), page.getSize());
        return new PageInfo<>(wxProductMapper.getProductSelectionList(page));
    }
}
