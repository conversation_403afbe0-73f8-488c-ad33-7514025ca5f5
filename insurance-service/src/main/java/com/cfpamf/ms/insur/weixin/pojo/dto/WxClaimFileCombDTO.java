package com.cfpamf.ms.insur.weixin.pojo.dto;

import com.cfpamf.ms.insur.admin.pojo.dto.SmClaimFileCombDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import java.util.List;

/**
 * 理赔文件VO
 *
 * <AUTHOR>
 **/
@Data
@ApiModel
public class WxClaimFileCombDTO extends WxAuthorizationDTO {

    /**
     * 理赔文件列表
     */
    @ApiModelProperty("理赔文件列表")
    @Valid
    private List<SmClaimFileCombDTO> fileCombs;

    /**
     * 操作人
     */
    @ApiModelProperty("操作人")
    private String userName;
}
