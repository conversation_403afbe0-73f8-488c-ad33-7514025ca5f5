package com.cfpamf.ms.insur.weixin.pojo.query.policy;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 询价Vo
 */
@Data
public class PlanQuery {
    @ApiModelProperty("计划Id")
    private Integer id;

    @ApiModelProperty("计划名")
    private String planName;

    @ApiModelProperty("计划编码")
    private String planCode;

    @ApiModelProperty("计划类型：DEFAULT_TYPE-缺省类型,QUOTA-定额计划,NON_QUOTA-非定额计划")
    private String planType;

    @ApiModelProperty("保障责任信息")
    private List<CoverageQuery> coverages;

}
