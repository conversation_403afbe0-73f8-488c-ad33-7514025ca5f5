package com.cfpamf.ms.insur.commission.dto;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * DemoDTO对象 web层使用<p>
 * <p>
 * 代码描述<p>
 * <p>
 * Copyright: Copyright (C) 2021 CD Finance Management Co., Ltd. All rights reserved. <p>
 * <p>
 * Company: 中和农信项目管理有限公司<p>
 *
 * <AUTHOR>
 * @since 2021/9/13 5:56 PM
 */
@Data
@ApiModel(value="DemoDTO对象", description="demo数据传输对象")
public class DemoDTO {

	@ApiModelProperty(value = "id")
	private Integer id;

	@ApiModelProperty(value = "白名单URL")
	private String apiUrl;

	@ApiModelProperty(value = "是否可用 1-是 0-否")
	private Integer isEnabled;

	@ApiModelProperty(value = "网关类型", example = "BEFORE_LOGIN")
	private String type;

	@ApiModelProperty(value = "环境", example = "ALL")
	private String env;

	@ApiModelProperty(value = "所属路由Id")
	private String routeId;
}
