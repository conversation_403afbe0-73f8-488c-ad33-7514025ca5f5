package com.cfpamf.ms.insur.commission.utils;

import lombok.AccessLevel;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.util.Date;

/**
 * <AUTHOR> 2022/12/22 12:41
 */
@Data
@Builder
@FieldDefaults(level = AccessLevel.PRIVATE)
public class JwtUserInfo {

    static final long serialVersionUID = 3646927206154766695L;
    Integer userId;
    Integer gray;
    Integer employeeId;
    Integer hrUserId;
    String account;
    String userName;
    String jobNumber;

    String mainJobNumber;
    Integer orgId;
    Integer hrOrgId;
    String hrOrgCode;
    String hrOrgName;
    String hrOrgTreePath;
    Integer userType;
    String randomCode;

    String masterJobNumber;

    Boolean useSso;

    Integer systemId;

    Date createTime;
}
