package com.cfpamf.ms.insur.weixin.pojo.request.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;

/**
 * 订单基本信息
 */
@Data
public class OrderBasicVo {

    @Column(name = "wxOpenId")
    @ApiModelProperty("openId")
    private String openId;

    @ApiModelProperty("产品Id")
    private Integer productId;

    @ApiModelProperty(value = "起保时间")
    private String startTime;

    @ApiModelProperty(value = "终保时间")
    private String endTime;

    @ApiModelProperty(value = "购买产品数量")
    private Integer qty;

    @ApiModelProperty(value = "订单状态", hidden = true)
    private String orderState;

    @ApiModelProperty(value = "订单支付状态", hidden = true)
    private String payStatus;

    @ApiModelProperty(value = "保险期限")
    private String validPeriod;

    @ApiModelProperty("待续保订单号")
    private String oldOrderId;

    /**
     * 真实续保标志，是否续保， true续保 false新保
     */
    @ApiModelProperty(value = "是否续保", hidden = true)
    private Boolean realRenewFlag;

}
