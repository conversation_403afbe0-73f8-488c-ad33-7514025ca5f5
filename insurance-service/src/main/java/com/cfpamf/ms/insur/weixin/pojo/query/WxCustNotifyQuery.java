package com.cfpamf.ms.insur.weixin.pojo.query;

import com.cfpamf.ms.insur.admin.pojo.query.BaseQuery;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.util.List;

/**
 * <AUTHOR> 2020/1/20 16:41
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class WxCustNotifyQuery extends BaseQuery {


    @ApiModelProperty(value = "客户告知书待办主键", hidden = true)
    Integer custNotifyId;

    @ApiModelProperty("是否签收")
    Boolean isSign;

    @ApiModelProperty("0-未查阅 1-已查阅 ")
    Integer state;

    @ApiModelProperty("签收方式 0-未签收 1 在线电签、2 线下签收、3退保系统自动取消")
    Integer confirmWay;

    @ApiModelProperty("搜索")
    String keyword;

    @ApiModelProperty(value = "投保人证件号码", hidden = true)
    String applicantIdNumber;

    @ApiModelProperty(value = "主键查询",hidden = true)
    List<Integer> ids;
}
