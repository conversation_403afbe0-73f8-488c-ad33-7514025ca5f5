package com.cfpamf.ms.insur.assistant.service;

import com.cfpamf.ms.insur.admin.pojo.query.BaseQuery;
import com.cfpamf.ms.insur.assistant.pojo.vo.OrganizationTreeVo;
import com.cfpamf.ms.insur.weixin.pojo.vo.LoginUserInfoVo;

import java.util.List;

/**
 * 保险业务助手的通用实现
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024年03月05日
 */
public interface AssistantCommonService {
    /**
     * 获取用户允许访问的组织树信息
     *
     * @param authorization BMS token
     * @return 组织树
     */
    List<OrganizationTreeVo> getAllowOrgTreeInfo(BaseQuery baseQuery, String authorization);

    /**
     * 在保险助手中获取用户信息
     *
     * @param authorization BMS token
     * @return 用户信息
     */
    LoginUserInfoVo getUserInfo(BaseQuery baseQuery, String authorization);
}
