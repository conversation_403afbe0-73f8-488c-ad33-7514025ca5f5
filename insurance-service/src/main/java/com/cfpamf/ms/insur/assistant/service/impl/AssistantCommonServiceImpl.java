package com.cfpamf.ms.insur.assistant.service.impl;

import com.alibaba.fastjson.JSON;
import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.common.ms.exception.MSException;
import com.cfpamf.common.ms.result.Result;
import com.cfpamf.ms.bms.facade.api.BmsOrganizationFacade;
import com.cfpamf.ms.bms.facade.vo.OrganizationBaseVO;
import com.cfpamf.ms.bms.facade.vo.UserDetailVO;
import com.cfpamf.ms.insur.admin.annotation.AdminAutoAuthQuery;
import com.cfpamf.ms.insur.admin.dao.safes.AuthUserMapper;
import com.cfpamf.ms.insur.admin.pojo.query.BaseQuery;
import com.cfpamf.ms.insur.admin.pojo.vo.AuthUserVO;
import com.cfpamf.ms.insur.admin.service.UserService;
import com.cfpamf.ms.insur.assistant.pojo.vo.OrganizationTreeVo;
import com.cfpamf.ms.insur.assistant.service.AssistantCommonService;
import com.cfpamf.ms.insur.base.exception.ExcptEnum;
import com.cfpamf.ms.insur.base.util.ThreadUserUtil;
import com.cfpamf.ms.insur.weixin.feign.BmsExtendFacade;
import com.cfpamf.ms.insur.weixin.pojo.vo.LoginUserInfoVo;
import com.cfpamf.ms.insur.weixin.pojo.vo.OrganizationParentVo;
import com.cfpamf.ms.insur.weixin.pojo.vo.WxUserVO;
import com.cfpamf.ms.insur.weixin.service.WxCcUserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.Period;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 保险业务助手的通用实现
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024年03月05日
 */
@Service
@Slf4j
public class AssistantCommonServiceImpl implements AssistantCommonService {

    @Resource
    private BmsExtendFacade bmsExtendFacade;

    @Resource
    private BmsOrganizationFacade bmsOrganizationFacade;

    @Resource
    private AuthUserMapper authUserMapper;

    @Resource
    private WxCcUserService wxCcUserService;

    /**
     * 获取用户允许访问的组织树信息
     *
     * @param authorization BMS token
     * @return 组织树
     */
    @AdminAutoAuthQuery
    @Override
    public List<OrganizationTreeVo> getAllowOrgTreeInfo(BaseQuery baseQuery, String authorization) {
        log.info("authorization:{},baseQuery:{}", authorization, JSON.toJSONString(baseQuery));
        String areaCode = null;
        String districtCode = null;
        String branchCode = null;
        if (baseQuery != null) {
            String areaName = baseQuery.getRegionName();
            String branchName = baseQuery.getOrgName();
            if (StringUtils.isNotBlank(branchName)) {
                OrganizationBaseVO branch = getOrganizationByName(authorization, branchName);
                if (branch != null) {
                    branchCode = branch.getOrgCode();
                    List<OrganizationParentVo> parentOrganizations = getParentOrganizationsByOrgCodes(branch.getOrgCode());
                    if (parentOrganizations == null) {
                        parentOrganizations = new ArrayList<>();
                    }
                    for (OrganizationParentVo parentOrganization : parentOrganizations) {
                        List<OrganizationParentVo> superOrganizations = parentOrganization.getSuperOrganizations();
                        if (CollectionUtils.isEmpty(superOrganizations)) {
                            continue;
                        }
                        for (OrganizationParentVo organizationBaseVo : superOrganizations) {
                            Integer orgCategory = organizationBaseVo.getOrgCategory();
                            if (orgCategory == null) {
                                continue;
                            }
                            switch (orgCategory) {
                                case 4: {
                                    areaCode = organizationBaseVo.getOrgCode();
                                    break;
                                }
                                case 6: {
                                    districtCode = organizationBaseVo.getOrgCode();
                                    break;
                                }
                                default:
                                    break;
                            }
                        }
                    }
                }
            } else if (StringUtils.isNotBlank(areaName)) {
                OrganizationBaseVO area = getOrganizationByName(authorization, areaName);
                if (area != null) {
                    areaCode = area.getOrgCode();
                }
            } else if (StringUtils.isNotBlank(baseQuery.getUserId())) {
                return new ArrayList<>();
            }
        }
        final String finalDistrictCode = districtCode;
        final String finalBranchCode = branchCode;
        if (areaCode == null) {
            // 需要查询全国的组织树
            List<OrganizationTreeVo> organizationTreeVos = getOrganizationTreeVos(authorization);
            if (CollectionUtils.isEmpty(organizationTreeVos)) {
                return new ArrayList<>();
            }
            organizationTreeVos = organizationTreeVos.stream()
                    .filter(organizationTreeVo -> Objects.equals(organizationTreeVo.getOrgCode(), "2") || Objects.equals(organizationTreeVo.getOrgName(), "分支机构"))
                    .findAny()
                    .orElseGet(() -> {
                        OrganizationTreeVo organizationTreeVo = new OrganizationTreeVo();
                        organizationTreeVo.setChildren(new ArrayList<>());
                        return organizationTreeVo;
                    })
                    .getChildren();
            for (OrganizationTreeVo area : organizationTreeVos) {
                List<OrganizationTreeVo> districts = area.getChildren();
                if (CollectionUtils.isEmpty(districts)) {
                    continue;
                }
                for (OrganizationTreeVo district : districts) {
                    district.setParentOrgCode(area.getOrgCode());
                    List<OrganizationTreeVo> branches = district.getChildren();
                    if (CollectionUtils.isEmpty(branches)) {
                        continue;
                    }
                    for (OrganizationTreeVo branch : branches) {
                        branch.setParentOrgCode(district.getOrgCode());
                    }
                }
            }
            return organizationTreeVos;
        } else {
            // 查询特定的组织树并过滤
            List<OrganizationTreeVo> organizationTreeVos = getOrganizationTreeVosByOrgCode(areaCode);
            if (CollectionUtils.isEmpty(organizationTreeVos)) {
                return new ArrayList<>();
            }
            for (OrganizationTreeVo area : organizationTreeVos) {
                List<OrganizationTreeVo> districts = area.getChildren();
                if (CollectionUtils.isEmpty(districts)) {
                    continue;
                }
                Iterator<OrganizationTreeVo> districtIterator = districts.iterator();
                while (districtIterator.hasNext()) {
                    OrganizationTreeVo district = districtIterator.next();
                    district.setParentOrgCode(area.getOrgCode());
                    if (district.getOrgCategory() == null) {
                        districtIterator.remove();
                    } else if (district.getOrgCategory() == 6) {
                        // 这里是片区的逻辑，如果存在片区编码且编码和当前片区编码不一致，则需要过滤掉
                        if (finalDistrictCode != null && !Objects.equals(finalDistrictCode, district.getOrgCode())) {
                            districtIterator.remove();
                            continue;
                        }
                        List<OrganizationTreeVo> branches = district.getChildren();
                        if (CollectionUtils.isEmpty(branches) && finalDistrictCode == null) {
                            if (finalBranchCode != null) {
                                districtIterator.remove();
                            }
                            continue;
                        }
                        branches = branches.stream()
                                .peek(branch -> branch.setParentOrgCode(district.getOrgCode()))
                                .filter(branch -> {
                                    if (finalBranchCode == null) {
                                        return true;
                                    }
                                    return branch.getOrgCategory() != null && branch.getOrgCategory() == 7 && Objects.equals(branch.getOrgCode(), finalBranchCode);
                                })
                                .collect(Collectors.toList());
                        if (CollectionUtils.isEmpty(branches)) {
                            districtIterator.remove();
                            continue;
                        }
                        district.setChildren(branches);
                    } else if (district.getOrgCategory() == 7) {
                        if (finalBranchCode != null) {
                            if (!Objects.equals(finalBranchCode, district.getOrgCode())) {
                                districtIterator.remove();
                            }
                        }
                    } else {
                        districtIterator.remove();
                        continue;
                    }
                }
            }
            return organizationTreeVos;
        }
    }

    /**
     * 获取传入组织机构编码的下级组织机构树
     *
     * @param orgCode 组织机构编码
     * @return 下级组织机构树
     */
    private List<OrganizationTreeVo> getOrganizationTreeVosByOrgCode(String orgCode) {
        Result<List<OrganizationTreeVo>> organizationTreeResult = bmsExtendFacade.listRootBranchBuAreaBranchBySuperOrgCode(orgCode);
        if (!organizationTreeResult.isSuccess()) {
            MSException msException = new MSException(ExcptEnum.HTTP_METHOD_ERROR_000098);
            msException.setErrorMsg("HTTP调用BMS的服务获取某区域的下级片区、分支树对方返回失败，返回的错误信息：" + organizationTreeResult.getErrorMsg());
            throw msException;
        }
        return organizationTreeResult.getData();
    }

    /**
     * 获取全国的组织机构树
     *
     * @param authorization BMS token
     * @return 全国的组织机构树
     */
    private List<OrganizationTreeVo> getOrganizationTreeVos(String authorization) {
        Result<List<OrganizationTreeVo>> organizationTreeResult = bmsExtendFacade.getOrganizationTree(authorization);
        if (!organizationTreeResult.isSuccess()) {
            MSException msException = new MSException(ExcptEnum.HTTP_METHOD_ERROR_000098);
            msException.setErrorMsg("HTTP调用BMS的服务获取某区域的下级片区、分支树对方返回失败，返回的错误信息：" + organizationTreeResult.getErrorMsg());
            throw msException;
        }
        return organizationTreeResult.getData();
    }

    @AdminAutoAuthQuery
    @Override
    public LoginUserInfoVo getUserInfo(BaseQuery baseQuery, String authorization) {
        try {
            ThreadUserUtil.USER_DETAIL_TL.remove();
            UserDetailVO userDetailVO = ThreadUserUtil.USER_DETAIL_TL.get();
            log.info("authorization:{},userDetailVO:{}", authorization, JSON.toJSONString(userDetailVO));
            WxUserVO wxUserVO = authUserMapper.getUserByUserId(userDetailVO.getJobNumber());
            if (wxUserVO == null) {
                throw new MSBizNormalException(ExcptEnum.DATA_NOT_EXISTS.getCode(), "当前用户在保险系统内不存在！");
            }
            LoginUserInfoVo loginUserInfoVo = new LoginUserInfoVo();
            if (baseQuery != null) {
                String areaName = baseQuery.getRegionName();
                String branchName = baseQuery.getOrgName();
                String districtCode = baseQuery.getZoneCode();
                if (StringUtils.isNotBlank(branchName)) {
                    OrganizationBaseVO branch = getOrganizationByName(authorization, branchName);
                    if (branch != null) {
                        loginUserInfoVo.setBranchName(branch.getOrgName());
                        loginUserInfoVo.setBranchCode(branch.getOrgCode());
                        List<OrganizationParentVo> parentOrganizations = getParentOrganizationsByOrgCodes(branch.getOrgCode());
                        if (parentOrganizations == null) {
                            parentOrganizations = new ArrayList<>();
                        }
                        for (OrganizationParentVo parentOrganization : parentOrganizations) {
                            List<OrganizationParentVo> superOrganizations = parentOrganization.getSuperOrganizations();
                            if (CollectionUtils.isEmpty(superOrganizations)) {
                                continue;
                            }
                            for (OrganizationParentVo organizationBaseVo : superOrganizations) {
                                Integer orgCategory = organizationBaseVo.getOrgCategory();
                                if (orgCategory == null) {
                                    continue;
                                }
                                switch (orgCategory) {
                                    case 4: {
                                        loginUserInfoVo.setAreaCode(organizationBaseVo.getOrgCode());
                                        loginUserInfoVo.setAreaName(organizationBaseVo.getOrgName());
                                        break;
                                    }
                                    case 6: {
                                        loginUserInfoVo.setDistrictCode(organizationBaseVo.getOrgCode());
                                        loginUserInfoVo.setDistrictName(organizationBaseVo.getOrgName());
                                        break;
                                    }
                                    default:
                                        break;
                                }
                            }
                        }
                    }
                } else if (StringUtils.isNotBlank(districtCode)){
                    //片区长角色进来设置对应的片区和区域
                    loginUserInfoVo.setAreaCode(baseQuery.getRegionCode());
                    loginUserInfoVo.setAreaName(baseQuery.getRegionName());
                    loginUserInfoVo.setDistrictCode(baseQuery.getZoneCode());
                    loginUserInfoVo.setDistrictName(baseQuery.getZoneName());
                } else if (StringUtils.isNotBlank(areaName)) {
                    OrganizationBaseVO area = getOrganizationByName(authorization, areaName);
                    if (area != null) {
                        loginUserInfoVo.setAreaName(area.getOrgName());
                        loginUserInfoVo.setAreaCode(area.getOrgCode());
                    }
                }
                if (StringUtils.isBlank(baseQuery.getUserId())) {
                    loginUserInfoVo.setManagerHelperAvailable(true);
                }
            }
            loginUserInfoVo.setUserId(userDetailVO.getJobNumber());
            loginUserInfoVo.setUserName(userDetailVO.getEmployeeName());
            loginUserInfoVo.setOrgCode(userDetailVO.getOrgCode());
            loginUserInfoVo.setOrgName(userDetailVO.getOrgName());
            loginUserInfoVo.setUserType(AuthUserVO.USER_TYPE_EMPLOYEE);
            loginUserInfoVo.setUserIdCard(userDetailVO.getIdCard());
            loginUserInfoVo.setUserMobile(userDetailVO.getMobile());
            loginUserInfoVo.setUserEmail(userDetailVO.getUserEmail());
            loginUserInfoVo.setRealVerify(wxUserVO.getRealVerify() != null && wxUserVO.getRealVerify());
            loginUserInfoVo.setMainJobNumber(wxUserVO.getMainJobNumber());
            loginUserInfoVo.setBizCode(wxUserVO.getBizCode());
            loginUserInfoVo.setHrOrgId(userDetailVO.getHrOrgId());
            loginUserInfoVo.setPostId(userDetailVO.getPostId());
            Integer workMonths = null;
            Date entryDate = userDetailVO.getEntryDate();
            if (entryDate != null) {
                workMonths = ((int) Period.between(entryDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate(), LocalDate.now()).toTotalMonths());
                if (workMonths == 0) {
                    workMonths = 1;
                }
            }
            loginUserInfoVo.setWorkMonths(workMonths);
            loginUserInfoVo.setWxSession(wxCcUserService.reloadWxSession(authorization, userDetailVO));
            return loginUserInfoVo;
        } finally {
            ThreadUserUtil.USER_DETAIL_TL.remove();
        }
    }

    /**
     * 根据组织机构编码获取上级组织机构信息
     *
     * @param orgCodes 组织机构编码
     * @return 上级组织机构信息
     */
    private List<OrganizationParentVo> getParentOrganizationsByOrgCodes(String... orgCodes) {
        Result<List<OrganizationParentVo>> parentOrganizationsResult = bmsExtendFacade.getParentOrganizationsByOrgCodes(Arrays.asList(orgCodes));
        if (!parentOrganizationsResult.isSuccess()) {
            MSException msException = new MSException(ExcptEnum.HTTP_METHOD_ERROR_000098);
            msException.setErrorMsg("HTTP调用BMS的服务获取用户的所述组织的上级组织信息对方返回失败，返回的错误信息：" + parentOrganizationsResult.getErrorMsg());
            throw msException;
        }
        return parentOrganizationsResult.getData();
    }

    /**
     * 组织信息
     *
     * @param authorization BMS token
     * @param orgName       组织名称
     * @return 组织信息
     */
    private OrganizationBaseVO getOrganizationByName(String authorization, String orgName) {
        Result<OrganizationBaseVO> organizationResult = bmsOrganizationFacade.getOrganizationByName(authorization, orgName);
        if (!organizationResult.isSuccess()) {
            MSException msException = new MSException(ExcptEnum.HTTP_METHOD_ERROR_000098);
            msException.setErrorMsg("HTTP调用BMS的服务获取分支信息对方返回失败，返回的错误信息：" + organizationResult.getErrorMsg());
            throw msException;
        }
        return organizationResult.getData();
    }
}
