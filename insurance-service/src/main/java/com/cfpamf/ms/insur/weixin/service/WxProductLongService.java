package com.cfpamf.ms.insur.weixin.service;

import com.cfpamf.ms.insur.admin.pojo.vo.SmPlanVO;
import com.cfpamf.ms.insur.admin.pojo.vo.product.LongProductPriceInfo;
import com.cfpamf.ms.insur.admin.pojo.vo.product.SmLongInsurancePlanVo;
import com.cfpamf.ms.insur.admin.service.SmProductService;
import com.cfpamf.ms.insur.admin.service.product.SmLongInsuranceProductService;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR> 2021/12/30 17:44
 */
@FieldDefaults(level = AccessLevel.PRIVATE)
@Service
public class WxProductLongService extends WxAbstractService {

    @Autowired
    SmLongInsuranceProductService service;

    @Autowired
    SmProductService productService;

    public LongProductPriceInfo price(int productId) {
        List<Integer> salePlanIds =
                productService.getProductPlansByOrgLimit(productId, checkAuthority().getRegionName())
                        .stream().map(SmPlanVO::getId).collect(Collectors.toList());
        LongProductPriceInfo longProductPriceInfo = service.priceFactor(productId);

        //过滤掉没有权限的数据
        List<SmLongInsurancePlanVo> fs = longProductPriceInfo.getPlans()
                .stream()
                .filter(p -> salePlanIds.stream().anyMatch(pt ->
                        Objects.equals(p.getPlanId().intValue(), pt)))
                .collect(Collectors.toList());
        longProductPriceInfo.setPlans(fs);
        return longProductPriceInfo;
    }

    /**
     * 过滤计划
     * @param productId
     * @return
     */
    public List<SmLongInsurancePlanVo> planDuty(int productId) {
        List<Integer> salePlanIds =
                productService.getProductPlansByOrgLimit(productId, checkAuthority().getRegionName())
                        .stream().map(SmPlanVO::getId).collect(Collectors.toList());
        return service.getPlanDuty(productId).stream()
                .filter(p -> salePlanIds.stream().anyMatch(pt ->
                        Objects.equals(p.getPlanId().intValue(), pt)))
                .collect(Collectors.toList());
    }
}
