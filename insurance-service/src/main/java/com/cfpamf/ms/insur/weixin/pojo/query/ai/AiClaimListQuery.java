package com.cfpamf.ms.insur.weixin.pojo.query.ai;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2024/12/5 19:53
 * @Version 1.0
 */
@Data
public class AiClaimListQuery {

    @ApiModelProperty("被保人姓名或身份证")
    private String insuredName;

    @ApiModelProperty("保单号")
    private String policyNo;

    @ApiModelProperty("请求头信息")
    private String authorization;

    private Integer insuredType;

    private String payStatus;

    private String customerAdminId;

}
