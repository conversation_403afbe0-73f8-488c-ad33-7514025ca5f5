package com.cfpamf.ms.insur.weixin.pojo.query;

import com.cfpamf.ms.insur.base.bean.Pageable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 团险报价查询query
 *
 * <AUTHOR>
 */
@Data
@ApiModel
public class SmGlProductQuoteQuery extends Pageable {

    /**
     * 创建时间开始
     */
    @ApiModelProperty("创建时间开始")
    private Date startDate;

    /**
     * 创建时间结束
     */
    @ApiModelProperty("创建时间结束")
    private Date endDate;

    /**
     * 报价单号
     */
    @ApiModelProperty("报价单号")
    private String quoteNo;

    /**
     * 客户姓名
     */
    @ApiModelProperty("客户姓名")
    private String customerName;

    /**
     * 区域
     */
    @ApiModelProperty("区域")
    private String regionName;

    /**
     * 机构
     */
    @ApiModelProperty("机构")
    private String organizationName;

    /**
     * 客户经理
     */
    @ApiModelProperty("客户经理")
    private String userName;

    /**
     * 代理人
     */
    @ApiModelProperty("代理人")
    private String agentName;
}
