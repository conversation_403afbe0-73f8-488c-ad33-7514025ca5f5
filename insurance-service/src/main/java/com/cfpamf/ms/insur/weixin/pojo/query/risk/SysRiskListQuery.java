package com.cfpamf.ms.insur.weixin.pojo.query.risk;

import com.cfpamf.ms.insur.weixin.pojo.dto.risk.SysRiskIdentificationDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

import java.util.List;

/**
 * 险种集合查询
 *
 * <AUTHOR>
 * @date 2021/11/5 15:56
 */
@Data
@ApiModel("险种集合查询")
public class SysRiskListQuery {
    /**
     * 微信openId
     */
    @ApiModelProperty("微信openId")
    private String openId;

    /**
     * 授权token
     */
    @ApiModelProperty("授权token")
    private String authorization;

    /**
     * 险种标识集合
     */
    @ApiModelProperty("险种标识集合")
    @NotEmpty(message = "险种标识集合不能为空")
    private List<SysRiskIdentificationDTO> sysRiskIdentificationDTOList;
}
