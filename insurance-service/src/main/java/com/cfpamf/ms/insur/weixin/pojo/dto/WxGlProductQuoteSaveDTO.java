package com.cfpamf.ms.insur.weixin.pojo.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 微信团险报价保存DTO
 *
 * <AUTHOR>
 **/
@Data
@Builder
@ApiModel
public class WxGlProductQuoteSaveDTO {

    /**
     * 报价Id
     */
    @ApiModelProperty("报价Id")
    private Integer pqId;

    /**
     * 询价单号
     */
    @ApiModelProperty("询价单号")
    private String quoteNo;

    /**
     * 询价种类
     */
    @ApiModelProperty("询价种类")
    private Integer quoteType;

    /**
     * 产品Id
     */
    @ApiModelProperty("产品Id")
    private Integer productId;

    /**
     * 客户手机号
     */
    @ApiModelProperty("客户手机号")
    private String customerMobile;

    /**
     * 客户姓名
     */
    @ApiModelProperty("客户姓名")
    private String customerName;

    /**
     * 总人数
     */
    @ApiModelProperty("总人数")
    private Integer totalPerQty;

    /**
     * 总保费
     */
    @ApiModelProperty("总保费")
    private BigDecimal totalAmount;

    /**
     * 客户经理Id
     */
    @ApiModelProperty("客户经理Id")
    private String customerAdminId;

    /**
     * 代理人Id
     */
    @ApiModelProperty("代理人Id")
    private Integer agentId;

    /**
     * 报价结果json
     */
    @ApiModelProperty("报价结果json")
    private String quoteResultJson;
}
