package com.cfpamf.ms.insur.weixin.pojo.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * 身份证识别DTO
 *
 * <AUTHOR>
 **/
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class WxOrcIdCardDTO extends WxAuthorizationDTO {

    /**
     * 图像base64字符串
     */
    @ApiModelProperty("图像base64字符串")
    private String base64;
}
