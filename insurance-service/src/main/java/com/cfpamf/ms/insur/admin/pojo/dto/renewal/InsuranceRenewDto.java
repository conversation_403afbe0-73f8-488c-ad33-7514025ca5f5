package com.cfpamf.ms.insur.admin.pojo.dto.renewal;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class InsuranceRenewDto {

    Integer id;

    Integer enabledFlag = 0;

    @ApiModelProperty(name="原订单Id")
    String oldOrderId;

    @ApiModelProperty(name="原保单Id")
    String oldPolicyNo;
    @ApiModelProperty(name="续保订单Id")
    String newOrderId;

    @ApiModelProperty(name="续保保单Id")
    String newPolicyNo;

    @ApiModelProperty(name="投保人姓名")
    String applicantPersonName;

    @ApiModelProperty(name="投保人证件号")
    String applicantIdNumber;

    @ApiModelProperty(name="被保人姓名")
    String insuredPersonName;

    @ApiModelProperty(name="被保人证件号")
    String insuredIdNumber;

    @ApiModelProperty(name="状态 waited 待续保 renewed 续保  cancel 已断保")
    String insStatus;

    @ApiModelProperty(name="宽限天数")
    Integer graceDay;

    @ApiModelProperty(name="过期天数")
    Integer surplusDay;

    @ApiModelProperty(name="到期前天数")
    Integer beforeExpirationDay;

    @ApiModelProperty(name="到期后天数")
    Integer afterExpirationDay;

    @ApiModelProperty(name="订单总保费")
    BigDecimal totalAmount;

    @ApiModelProperty(name="产品ID")
    Integer productId;

    @ApiModelProperty(name="计划ID")
    Integer planId;

    @ApiModelProperty(name="产品属性：团险，个险")
    String productAttrCode;

    @ApiModelProperty("产品名称")
    String productName;

    @ApiModelProperty("渠道")
    String channel;

    @ApiModelProperty(name="失效时间")
    LocalDateTime invalidTime;

    @ApiModelProperty(name="生效时间")
    LocalDateTime startTime;

    @ApiModelProperty("断保原因")
    String overReason;

    @ApiModelProperty("管护经理")
    String customerAdminId;

    @ApiModelProperty("微信openId")

    String wxOpenId;


    Integer agentId;

    Integer todoState;


    String riskCategory2;

    String policyProductType;

    @ApiModelProperty("车牌号")
    String carPlateNo;


}
