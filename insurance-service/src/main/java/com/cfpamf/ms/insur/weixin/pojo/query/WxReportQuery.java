package com.cfpamf.ms.insur.weixin.pojo.query;

import com.cfpamf.ms.insur.base.pojo.quwey.ReportQuery;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * 微信报表查询VO
 *
 * <AUTHOR>
 **/
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class WxReportQuery extends ReportQuery {
    /**
     * 微信openId
     */
    @ApiModelProperty("微信openId")
    private String openId;

    /**
     * 授权token
     */
    @ApiModelProperty("授权token")
    private String authorization;

    /**
     * 渠道
     */
    @ApiModelProperty(value = "渠道")
    private String channel;

    /**
     * 员工姓名/员工工号
     */
    @ApiModelProperty(value = "员工姓名/员工工号")
    private String employee;
    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    private String sort;
}
