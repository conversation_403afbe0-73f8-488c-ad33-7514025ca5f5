package com.cfpamf.ms.insur.weixin.service;

import com.cfpamf.ms.insur.admin.pojo.vo.UserCollectionListVO;
import com.cfpamf.ms.insur.base.exception.BizException;
import com.cfpamf.ms.insur.base.exception.ExcptEnum;
import com.cfpamf.ms.insur.weixin.dao.safes.WxCollectionMapper;
import com.cfpamf.ms.insur.weixin.pojo.dto.WxCollectionDTO;
import com.cfpamf.ms.insur.weixin.pojo.query.WxAuthPageQuery;
import com.cfpamf.ms.insur.weixin.pojo.vo.WxSessionVO;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 微信客户我的收藏Service
 **/
@Slf4j
@Service
public class WxCcCollectionService extends WxAbstractService {

    /**
     * 微信用户收藏
     */
    @Autowired
    private WxCollectionMapper collectionMapper;

    /**
     * 查询用户产品收藏列表
     *
     * @param query
     * @return
     */
    public PageInfo<UserCollectionListVO> getWxCollectionListByPage(WxAuthPageQuery query) {
        WxSessionVO session = checkAuthorityEpAg(query.getOpenId(), query.getAuthorization());
        query.setChannel(session.getChannel());
        PageHelper.startPage(query.getPage(), query.getSize());
        List<UserCollectionListVO> collectionList = collectionMapper.getUserCollectionList(session.getRoleUniqueId(), query.getChannel());
        return new PageInfo<>(collectionList);
    }

    /**
     * 添加用户产品收藏
     *
     * @param dto
     * @return
     */
    public int saveWxUserCollectionList(WxCollectionDTO dto) {
        WxSessionVO session = checkAuthorityEpAg(dto.getOpenId(), dto.getAuthorization());
        if (collectionMapper.countUserCollection(dto.getProductId(), session.getContextUser()) > 0) {
            throw new BizException(ExcptEnum.COLLECTION_ERROR_801006);
        }
        dto.setUserId(session.getRoleUniqueId());
        collectionMapper.insertUserCollection(dto);
        return dto.getId();
    }

    /**
     * 删除用户产品收藏
     *
     * @param dto
     * @return
     */
    public void deleteWxCollection(WxCollectionDTO dto) {
        WxSessionVO session = checkAuthorityEpAg(dto.getOpenId(), dto.getAuthorization());
        dto.setUserId(session.getRoleUniqueId());
        collectionMapper.deleteUserCollection(dto.getProductId(), dto.getUserId());
    }

    /**
     * 删除用户产品收藏
     *
     * @param productId
     * @param openId
     * @param authorization
     * @return
     */
    public boolean getUserProductCollectionStatus(Integer productId, String openId, String authorization) {
        WxSessionVO session = checkAuthority(openId, authorization);
        String roleUniqueId = session.getRoleUniqueId();
        return collectionMapper.countUserCollection(productId, roleUniqueId) > 0;
    }
}
