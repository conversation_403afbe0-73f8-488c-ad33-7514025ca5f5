package com.cfpamf.ms.insur.weixin.service;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.excel.util.CollectionUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.oss.OSS;
import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.common.ms.util.DateUtil;
import com.cfpamf.ms.bms.facade.vo.UserDetailVO;
import com.cfpamf.ms.insur.admin.constant.ClaimRiskType;
import com.cfpamf.ms.insur.admin.constant.SmConstants;
import com.cfpamf.ms.insur.admin.dao.safes.SmClaimMapper;
import com.cfpamf.ms.insur.admin.dao.safes.order.SmOrderInsuredMapper;
import com.cfpamf.ms.insur.admin.dao.safes.product.SmProductLabelMapper;
import com.cfpamf.ms.insur.admin.enums.claim.EnumClaimProcessType;
import com.cfpamf.ms.insur.admin.enums.product.EnumProductLabelType;
import com.cfpamf.ms.insur.admin.event.ClaimProcessNodeChangeEvent;
import com.cfpamf.ms.insur.admin.event.WxClaimNotifyEvent;
import com.cfpamf.ms.insur.admin.external.OrderQueryResponse;
import com.cfpamf.ms.insur.admin.pojo.convertor.OrderConvertor;
import com.cfpamf.ms.insur.admin.pojo.dto.SmClaimFollowUpDTO;
import com.cfpamf.ms.insur.admin.pojo.dto.claim.NewFlagFileForm;
import com.cfpamf.ms.insur.admin.pojo.po.SmOrderInsured;
import com.cfpamf.ms.insur.admin.pojo.po.claim.SmClaim;
import com.cfpamf.ms.insur.admin.pojo.po.claim.evaluation.SmClaimEvaluationPO;
import com.cfpamf.ms.insur.admin.pojo.po.claim.evaluation.SmClaimQuestionnairePO;
import com.cfpamf.ms.insur.admin.pojo.po.product.SmProductLabel;
import com.cfpamf.ms.insur.admin.pojo.query.BaseQuery;
import com.cfpamf.ms.insur.admin.pojo.vo.*;
import com.cfpamf.ms.insur.admin.pojo.vo.claim.SmClaimFollowUpListVo;
import com.cfpamf.ms.insur.admin.pojo.vo.claim.SmClaimFollowupQueryVo;
import com.cfpamf.ms.insur.admin.pojo.vo.claim.SmClaimPosterApplyDetailVO;
import com.cfpamf.ms.insur.admin.service.*;
import com.cfpamf.ms.insur.admin.service.ClaimServiceConfig;
import com.cfpamf.ms.insur.admin.service.ClaimWorkflow;
import com.cfpamf.ms.insur.admin.service.SmClaimServiceImpl;
import com.cfpamf.ms.insur.admin.service.UserService;
import com.cfpamf.ms.insur.admin.service.claim.ClaimRiskReasonService;
import com.cfpamf.ms.insur.admin.service.claim.SmClaimPosterApplyService;
import com.cfpamf.ms.insur.admin.service.claim.evaluation.SmClaimEvaluationService;
import com.cfpamf.ms.insur.admin.service.claim.evaluation.SmClaimQuestionnaireService;
import com.cfpamf.ms.insur.admin.service.claim.impl.ClaimDeduceContextParams;
import com.cfpamf.ms.insur.admin.service.claim.impl.ClaimProcessDistinctServiceImpl;
import com.cfpamf.ms.insur.admin.service.claim.impl.hasten.ClaimHastenProcessor;
import com.cfpamf.ms.insur.base.config.ClaimConfig;
import com.cfpamf.ms.insur.base.config.tx.TxServiceManager;
import com.cfpamf.ms.insur.base.constant.BaseConstants;
import com.cfpamf.ms.insur.base.event.EventBusEngine;
import com.cfpamf.ms.insur.base.exception.BizException;
import com.cfpamf.ms.insur.base.exception.ExcptEnum;
import com.cfpamf.ms.insur.base.service.BaseWorkflow;
import com.cfpamf.ms.insur.base.service.BusinessTokenService;
import com.cfpamf.ms.insur.base.util.*;
import com.cfpamf.ms.insur.facade.dto.DataAuth;
import com.cfpamf.ms.insur.weixin.dao.safes.WxOrderMapper;
import com.cfpamf.ms.insur.weixin.pojo.dto.*;
import com.cfpamf.ms.insur.weixin.pojo.dto.claim.za.SmSettlementClaimQuery;
import com.cfpamf.ms.insur.weixin.pojo.query.WxClaimQuery;
import com.cfpamf.ms.insur.weixin.pojo.request.order.OrderBasicVo;
import com.cfpamf.ms.insur.weixin.pojo.vo.*;
import com.cfpamf.ms.insur.weixin.pojo.vo.claim.WxClaimCancelReportListVo;
import com.cfpamf.ms.insur.weixin.pojo.vo.claim.evaluation.ClaimEvaluationVo;
import com.cfpamf.ms.insur.weixin.pojo.vo.claim.evaluation.ClaimQuestionnaireOptionVo;
import com.cfpamf.ms.insur.weixin.pojo.vo.claim.evaluation.ClaimQuestionnaireVo;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.fileupload.FileItem;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.cfpamf.ms.insur.admin.enums.claim.EnumClaimProcessType.*;
import static com.cfpamf.ms.insur.admin.service.ClaimWorkflow.*;
import static com.cfpamf.ms.insur.base.constant.BaseConstants.ORG_BRANCH_HQ;
import static com.cfpamf.ms.insur.base.constant.BaseConstants.ORG_REGION_HQ;
import static com.cfpamf.ms.insur.base.exception.ExcptEnum.CLAIM_DUPLICATE_CASE;

/**
 * 理赔服务Service
 *
 * <AUTHOR>
 **/
@Slf4j
@Service
public class WxCcClaimService extends WxAbstractService {

    /**
     * 事件工作流
     */
    @Lazy
    @Autowired
    protected EventBusEngine busEngine;
    /**
     * 总部审批人工号
     */
    @Value("${claim.headquarters.approver.userId}")
    private String headquartersApprover;

    /**
     * 用户service
     */
    @Autowired
    private UserService userService;

    /**
     * 理赔service
     */
    @Autowired
    @Lazy
    private SmClaimServiceImpl claimService;

    /**
     * 业务token service
     */
    @Autowired
    private BusinessTokenService tokenService;

    @Autowired
    private PermissionUtil permissionUtil;

    /**
     * 订单mapper
     */
    @Autowired
    private WxOrderMapper orderMapper;

    /**
     * 微信代理service
     */
    @Lazy
    @Autowired
    private WxMpServiceProxy serviceProxy;

    @Autowired
    private ClaimServiceConfig claimServiceConfig;

    @Autowired
    private SmOrderInsuredMapper insuredMapper;

    @Resource
    SmClaimPosterApplyService smClaimPosterApplyService;

    @Autowired
    private ClaimHastenProcessor claimHastenProcessor;

    @Autowired
    private SmProductLabelMapper productLabelMapper;

    @Autowired
    private SmClaimMapper claimMapper;

    @Autowired
    private SmClaimEvaluationService claimEvaluationService;
    @Autowired
    private SmClaimQuestionnaireService  claimQuestionnaireService;
    @Autowired
    private TxServiceManager txServiceManager;
    @Autowired
    private SystemFileService fileService;
    @Autowired
    private DataAuthService dataAuthService;

    @Autowired
    private ClaimRiskReasonService claimRiskReasonService;

    /**
     * 查询用户微信可以理赔订单列表
     *
     * @param query
     * @return
     */
    public WxClaimHomeVO getWxClaimHome(WxClaimQuery query) {
        WxSessionVO session = checkAuthorityEpAg(query.getOpenId(), query.getAuthorization());
        UserDetailVO userDetailVO = ThreadUserUtil.USER_DETAIL_TL.get();
        WxClaimHomeVO claimHomeVO = new WxClaimHomeVO();
        WxClaimQuery proQuery = JSON.parseObject(JSON.toJSONString(query), WxClaimQuery.class);
        claimHomeVO.setTodoQty(claimService.countWxClaimTodoList(buildWxClaimTodoQuery(proQuery)));

        // 分支创新业务对接人
        long approvalQty = 0;
        long followUpQty = 0;

        query.setClaimState(STEP_DATA_CHECK_BY_PIC);
        log.info("获取理赔页-Tips|{}", userDetailVO);
        if (permissionUtil.isChannelPCO(userDetailVO)) {
            query.setRegionName(session.getRegionName());
            query.setOrgName(session.getOrganizationName());
            log.info("获取理赔页-渠道PCO|参数-{}", query);
            approvalQty = claimService.countWxClaimList(query);
            query.setClaimState("unFinish");
            followUpQty = claimService.countWxFollowUpClaimList(query);
        }
        // 总部创新业务对接人
        else if (Objects.equals(session.getUserId(), headquartersApprover)) {
            log.info("获取理赔页-总部|参数-{}", query);
            query.setRegionName(ORG_REGION_HQ);
            query.setOrgName(ORG_BRANCH_HQ);
            approvalQty = claimService.countWxClaimList(query);
            query.setClaimState("unFinish");
            followUpQty = claimService.countWxFollowUpClaimList(query);
        }
        claimHomeVO.setApprovalQty(approvalQty);
        claimHomeVO.setFollowUpQty(followUpQty);
        return claimHomeVO;
    }

    /**
     * 查询用户微信可以理赔订单列表
     *
     * @param query
     * @return
     */
    public PageInfo<WxClaimCancelReportListVo> getWxClaimByPage(WxClaimQuery query) {
        WxSessionVO session = checkAuthorityEpAg(query.getOpenId(), query.getAuthorization());
        initQuery(query, session);
        return claimService.getWxClaimList(query);
    }

    /**
     * 初始化查询
     *
     * @param query
     * @param session
     */
    public void initQuery(WxClaimQuery query, WxSessionVO session) {
        if (session.isBindEmployee()) {
            query.setUserId(session.getUserId());
        } else if (session.isBindAgent()) {
            query.setAgentId(session.getAgentId());
        }
        query.setChannel(session.getChannel());
    }

    /**
     * 查询用户微信理赔代办事项列表
     *
     * @param query
     * @return
     */
    public PageInfo<WxClaimListVo> getWxClaimTodoListByPage(WxClaimQuery query) {
        return claimService.getWxClaimTodoListByPage(buildWxClaimTodoQuery(query));
    }

    /**
     * 查询用户微信理赔资料审批列表
     *
     * @param query
     * @return
     */
    public PageInfo<WxClaimCancelReportListVo> getWxClaimPicApprovalListByPage(WxClaimQuery query) {
        WxSessionVO session = checkAuthorityEpAg(query.getOpenId(), query.getAuthorization());
        UserDetailVO userDetailVO = ThreadUserUtil.USER_DETAIL_TL.get();
        if (permissionUtil.isChannelPCO(userDetailVO)) {
            query.setRegionName(session.getRegionName());
            query.setOrgName(session.getOrganizationName());
        }
        // 总部创新业务对接人
        else if (Objects.equals(session.getUserId(), headquartersApprover)) {
            query.setRegionName(ORG_REGION_HQ);
            query.setOrgName(ORG_BRANCH_HQ);
        } else {
            return new PageInfo<>(Collections.emptyList());
        }
        return claimService.getWxClaimList(query);
    }

    /**
     * 查询用户微信理赔资料审批列表
     *
     * @param query
     * @return
     */
    public PageInfo<WxClaimListVo> getWxClaimPicFollowUpListByPage(WxClaimQuery query) {
        WxSessionVO session = checkAuthorityEpAg(query.getOpenId(), query.getAuthorization());
        query.setChannel(session.getChannel());
        UserDetailVO userDetailVO = ThreadUserUtil.USER_DETAIL_TL.get();
        if (permissionUtil.isChannelPCO(userDetailVO)) {
            query.setRegionName(session.getRegionName());
            query.setOrgName(session.getOrganizationName());
        }
        // 总部创新业务对接人
        else if (Objects.equals(session.getUserId(), headquartersApprover)) {
            query.setRegionName(ORG_REGION_HQ);
            query.setOrgName(ORG_BRANCH_HQ);
        } else {
            return new PageInfo<>(Collections.emptyList());
        }
        return claimService.getWxFollowUpClaimList(query);
    }

    /**
     * 查询用户微信理赔案件更进列表
     *
     * @param openId
     * @param authorization
     * @param claimId
     * @return
     */
    public List<SmClaimFollowUpVO> getClaimFollowUpListById(String openId, String authorization, Integer claimId) {
        checkAuthority(openId, authorization);
        return claimService.getClaimFollowUpListById(claimId);
    }

    /**
     * 保存用户微信理赔案件更进
     *
     * @param dto
     * @return
     */
    public void saveClaimFollowUp(SmClaimFollowUpDTO dto) {
        WxSessionVO sessionVO = checkAuthority(dto.getOpenId(), dto.getAuthorization());
        dto.setCreateBy(sessionVO.getUserName());
        dto.setCreateByCode(sessionVO.getUserId());
        claimService.saveClaimFollowUp(dto);
    }

    public void checkHasten(int claimId) {
        claimService.checkCurrentDayHastenFollowUp(claimId);
    }

    /**
     * 查看用户微信理赔详情
     *
     * @param id
     * @return
     */
    public WxClaimDetailVO getWxClaimById(int id, String wxOpenId, String authorization) {
        //checkAuthorityEpAg(wxOpenId, authorization);
        WxClaimDetailVO wxClaimDetailVO = getWxClaimDetailVONoProgressInfo(id);
        if (Objects.nonNull(wxClaimDetailVO)) {
            SmClaimProgressVO claimProgressVO = claimService.getClaimProgress(id);
            if (Objects.nonNull(claimProgressVO.getNextProgress())) {
                List<BaseWorkflow.Option> options = Optional.ofNullable(claimProgressVO.getNextProgress()).map(BaseWorkflow.Step::getOptions)
                        .orElse(Collections.emptyList())
                        .stream()
                        .filter(x -> !org.apache.commons.lang3.StringUtils.equals(x.getOType(), "-6"))
                        .collect(Collectors.toList());
                claimProgressVO.getNextProgress().setOptions(options);
            }
            wxClaimDetailVO.setProgress(claimProgressVO);

        }
        return wxClaimDetailVO;
    }

    /**
     * 查看用户微信理赔详情 无流程信息
     *
     * @param id
     * @return
     */
    public WxClaimDetailVO getWxClaimDetailVONoProgressInfo(int id) {
        WxClaimDetailVO detailVO = claimService.getSmClaimById(id);
        List<SmClaimFollowUpVO> followUpList = claimService.getClaimFollowUpListById(id);
        if (!CollectionUtils.isEmpty(followUpList)) {
            detailVO.setNewestFollowUp(followUpList.get(0).getFollowUpDetail());
            detailVO.setNewestFollowUp(followUpList.get(0).getFollowUpDetail());
            detailVO.setNewestClaimFollowUpVO(followUpList.get(0));
        }
        if (Objects.nonNull(detailVO)) {
            String channel = detailVO.getChannel();
            detailVO.setCompanyId(claimService.getCompanyIdByClaimId(id));
            SmOrderInsuredVO insuredVo = orderMapper.getOrderInsuredByInsId(detailVO.getInsuredId());
            OrderQueryResponse fhOrderDTO = OrderConvertor.mapperOrderQuery4Local(insuredVo.getFhOrderId());
            BeanUtils.copyProperties(fhOrderDTO, detailVO);
            detailVO.getOrderInfo().setRecommendId(Optional.ofNullable(fhOrderDTO.getOrderInfo()).map(OrderQueryResponse.OrderInfo::getCustomerAdminId).orElse(null));
            detailVO.setId(id);
            detailVO.getOrderInfo().setCompanyFullName(insuredVo.getCompanyName());
            if (detailVO.getInsuredInfos() != null && detailVO.getInsuredInfos().size() > 1) {
                detailVO.setInsuredInfos(detailVO.getInsuredInfos().stream().filter(i -> Objects.equals(i.getInsuredIdNo(), insuredVo.getIdNumber())).collect(Collectors.toList()));
            }
            if (detailVO.getPolicyInfos() != null && detailVO.getPolicyInfos().size() > 1) {
                detailVO.setPolicyInfos(detailVO.getPolicyInfos().stream().filter(i -> Objects.equals(i.getPolicyNo(), insuredVo.getPolicyNo())).collect(Collectors.toList()));
            }
            detailVO.setChannel(channel);
            detailVO.setCompanyLogoThumbUrl(insuredVo.getCompanyLogoThumbUrl());
            detailVO.setReportPhone(insuredVo.getReportPhone());

            OrderQueryResponse.OrderInfo orderInfo = fhOrderDTO.getOrderInfo();
            if (Objects.nonNull(orderInfo) && StringUtils.isNotEmpty(orderInfo.getCustomerAdminId())) {
                AuthUserVO authUserVO = userService.getAuthUserByUserId(orderInfo.getCustomerAdminId());
                if (Objects.nonNull(authUserVO)) {
                    detailVO.setCustomerAdminName(authUserVO.getUserName());
                    detailVO.setCustomerAdminId(orderInfo.getCustomerAdminId());
                    detailVO.setOrgName(authUserVO.getOrganizationName());
                    List<AuthUserVO> pcoList = userService.getUserChannelPCO(orderInfo.getCustomerAdminId());
                    if (CollectionUtil.isNotEmpty(pcoList)) {
                        detailVO.setPcoName(pcoList.iterator().next().getUserName());
                    }

                }
            }

        }
        detailVO.setClaimRiskReasonStr(claimRiskReasonService.getRiskReasonByCode(detailVO.getClaimRiskReason()));
        claimHastenProcessor.hastenDetail(detailVO.getClaimState(), detailVO.getId());
        return detailVO;
    }

    /**
     * 查看用户微信理赔文件
     *
     * @param claimId
     * @return
     */
    public List<SmClaimFileCombVO> getWxClaimFileByClaimId(int claimId, String openId, String authorization) {
        checkAuthority(openId, authorization);
        // 获取理赔渠道
        SmClaim claim = claimService.getClaimById(claimId);
        return claimServiceConfig.findClaimByChannelCode(claim.getProcessType()).getSmClaimFileCombByClaimId(claimId);
    }

    /**
     * 补材接口
     * @param claimId
     * @param authorization
     * @return
     */
    public List<SmClaimFileCombVO> getWxClaimSupplementFileByClaimId(int claimId, String authorization) {
        checkAuthority(null, authorization);

        // 获取理赔渠道
        SmClaim claim = claimService.getClaimById(claimId);
        return claimServiceConfig.findClaimByChannelCode(claim.getProcessType()).getSupplementFileByClaimId(claimId);
    }


    /**
     * 查看用户微信是否已经提交理赔文件
     *
     * @param claimId
     * @return
     */
    public boolean getWxClaimFileStatusByClaimId(int claimId, String openId, String authorization) {
        checkAuthority(openId, authorization);
        WxClaimDetailVO detailVO = claimService.getSmClaimById(claimId);
        return detailVO.getClaimState() != null && detailVO.getClaimState().startsWith(STEP_DATA_PREPARE);
    }

    /**
     * 查询用户微信理赔事故类型
     *
     * @param claimId
     * @param openId
     * @param authorization
     * @return
     */
    public WxClaimAccidentVO getWxClaimAccidentTypes(int claimId, String openId, String authorization) {
        checkAuthorityEpAg(openId, authorization);
        return claimService.getSmClaimAccidentTypes(claimId);
    }

    /**
     * 保存用户微信理赔事故类型
     *
     * @param claimId
     * @param dto
     * @return
     */
    public void saveWxClaimAccidentTypes(int claimId, WxClaimAccidentDTO dto) {
        checkAuthority(dto.getOpenId(), dto.getAuthorization());
        String accidentTypeJoins = "";
        if (dto.getAccidentTypeCodes() != null) {
            accidentTypeJoins = CommonUtil.strJoin(",", dto.getAccidentTypeCodes());
        }
        claimService.updateClaimAccidentTypes(claimId, accidentTypeJoins, dto.getMedicalInsur(), dto.getOtherInsur(), dto.getVisitingHospital(), dto.getVisitingHospitalName(), dto.getVisitingDate(), dto.getVisitingOccupation());
    }

    /**
     * 保存用户微信理赔文件（文件是url）
     *
     * @param claimId
     * @param dto
     * @return
     */
    public void saveWxClaimFiles(int claimId, WxClaimFileCombDTO dto) {
        WxSessionVO session = checkAuthority(dto.getOpenId(), dto.getAuthorization());
        dto.setUserName(session.getUserName());
        claimServiceConfig.saveWxClaimFileByClaimId(claimId, dto, session);
    }

    /**
     * 保存用户微信理赔文件（文件是微信mediaId，需重新下载）
     *
     * @param claimId
     * @param dto
     * @return
     */
    public void saveWxClaimMediaIdFiles(int claimId, WxClaimFileCombDTO dto) {
        WxSessionVO session = checkAuthority(dto.getOpenId(), dto.getAuthorization());
        dto.setUserName(session.getUserName());
        dto.getFileCombs().forEach(fc -> {
            List<NewFlagFileForm> newFlagFileList = fc.getNewFlagFileList();
            for (int i = 0; i < newFlagFileList.size(); i++) {
                NewFlagFileForm newFlagFileForm = newFlagFileList.get(i);
                String mediaId = newFlagFileForm.getFileUrl();
                File file = serviceProxy.mediaDownload(mediaId);
                if (file == null) {
                    throw new BizException(ExcptEnum.WX_MEDIAID_DOWNLOAD_FILE_ERROR_801304);
                }
                FileItem fileItem = AliYunOssUtil.createFileItem(file, "blob");
                MultipartFile mfile = new CommonsMultipartFile(fileItem);
                OSS client = AliYunOssUtil.getOSSClient();
                // 文件位置  image/时间戳/uuid/文件名
                String relativePath = AliYunOssUtil.SUB_BUCKET_IMAGE + BaseConstants.STR_LABEL_SLASH + System.currentTimeMillis() + BaseConstants.STR_LABEL_SLASH +
                        UUID.randomUUID().toString().replaceAll("-", "") +
                        BaseConstants.STR_LABEL_SLASH + mfile.getOriginalFilename();
                String aliETagName = AliYunOssUtil.uploadObject2OSS(client, mfile, relativePath, false);
                if (!StringUtils.isEmpty(aliETagName)) {
                    String url = AliYunOssUtil.generatePresignedUri5M(client, relativePath);
                    if (StringUtils.isEmpty(url)) {
                        throw new BizException(ExcptEnum.FILE_UPLOAD_FAIL_501007);
                    }
                    newFlagFileForm.setFileUrl(url);
                } else {
                    throw new BizException(ExcptEnum.FILE_UPLOAD_FAIL_501007);
                }
            }
        });

        claimServiceConfig.saveWxClaimFileByClaimId(claimId, dto, session);
    }

    @Autowired
    private ClaimProcessDistinctServiceImpl processDistinctService;

    /**
     * 用户微信申请理赔
     *
     * @param dto
     * @return
     */
    public WxClaimDTO addUserClaim(WxClaimDTO dto) {
        // 鉴权
        WxSessionVO session = checkAuthorityEpAg(dto.getOpenId(), dto.getAuthorization());

        ClaimDeduceContextParams p = claimMapper.deduceByInsuredId(dto.getInsuredId());

        dto.setProcessType(processDistinctService.deduceByByInsuredId(p, dto.getRiskType()));

        if (Objects.equals(dto.getProcessType(), EnumClaimProcessType.GUO_TAI_API.getCode())) {
            if (ClaimRiskType.gtccExclude().contains(dto.getRiskType())) {
                throw new BizException("", "该类型暂不支持公众号提交理赔材料，请出险后48小时内电话保险公司报案。");
            }
        }

        checkOfflineClaim(dto, p);

        checkKpRiskType(dto);

        checkRiskTime(dto);

        checkDuplicateCase(dto);

        boolean validToken = tokenService.validateBusinessToken(session.getUserId(), SmConstants.SM_CLAIM_BUSINESS_CODE, dto.getToken());
        if (!validToken) {
            throw new BizException(ExcptEnum.INVALID_TOKEN_501009);
        }

        // 删除业务token
        tokenService.deleteBusinessToken(session.getUserId(), SmConstants.SM_CLAIM_BUSINESS_CODE, dto.getToken());
        // 设置审计信息
        dto.setCreateBy(session.getUserName());
        dto.setCreateRole(session.getUserType());
//        // 校验当前保单是否已有理赔
//        List<WxClaimDetailVO> claims = claimService.getSmClaimByInsuredId(dto.getInsuredId());
//        if (claims.stream().anyMatch(c -> !Objects.equals(c.getClaimState(), ClaimWorkflow.STEP_FINISH))) {
//            throw new BizException(ExcptEnum.CLAIM_APPLY_ERROR);
//        }
        WxClaimDTO result =  processDistinctService.appearAndReport(dto);

        //添加案件流程节点事件触发器 add by zhangjian 2024-10-29
        busEngine.publish(new ClaimProcessNodeChangeEvent(dto.getId()));

        return result;
    }

    private void checkOfflineClaim(WxClaimDTO dto, ClaimDeduceContextParams p) {

        SmProductLabel productLabel = productLabelMapper.getByProductIdAndType(p.getProductId(), EnumProductLabelType.OFFLINE_CLAIM.getCode());
        if (Objects.nonNull(productLabel) && Objects.equals(productLabel.getLabelValue(), "N")) {
            throw new BizException(ExcptEnum.CLAIM_OFFLINE_GUIDE.getCode(), productLabel.getLabelDesc());
        }

    }

    private void checkKpRiskType(WxClaimDTO dto) {

        if (!EnumClaimProcessType.kp().contains(dto.getProcessType())) {
            return;
        }

        if (
                Sets.newHashSet(
                        ZA_KAI_PING_API.getCode()
                        , ZA_KAI_PING_THIRD_REPORT_API.getCode()
                ).contains(dto.getProcessType()) &&
                        !ClaimRiskType.kpContains().contains(dto.getRiskType())
        ) {
            throw new BizException("", "该类型暂不支持公众号提交理赔材料，请出险后48小时内电话保险公司报案。");
        }

        if (
                Sets.newHashSet(
                        ZA_KAI_PING_THIRD_REPORT_FAMILY_API.getCode()
                ).contains(dto.getProcessType()) &&
                        !ClaimRiskType.kpFamilyContains().contains(dto.getRiskType())
        ) {
            throw new BizException("", "出险类型为“家财”，不支持在本系统申请理赔，请务必联系保司报案。");
        }
    }

    private void checkDuplicateCase(WxClaimDTO dto) {

        Integer insuredId = dto.getInsuredId();
        SmOrderInsured insured = insuredMapper.selectByInsuredId(insuredId);
        if (Objects.isNull(insured)) {
            throw new BizException("", "被保人信息不存在");
        }

        SmClaim smClaim = claimService.queryUnClosedCaseByInsuredIdAndRiskType(insuredId, dto.getRiskType());

        if (Objects.nonNull(smClaim)) {
            throw new BizException(CLAIM_DUPLICATE_CASE);
        }

    }

    public Integer queryDuplicateCase(Integer insuredId, String riskType) {
        SmClaim claim = claimService.queryUnClosedCaseByInsuredIdAndRiskType(insuredId, riskType);
        if (Objects.isNull(claim)) {
            throw new BizException("", "无理赔案件信息");
        }
        return claim.getId();
    }

    private void checkRiskTime(WxClaimDTO dto) {

        if (Objects.isNull(dto.getInsuredId())) {
            throw new BizException(ExcptEnum.PARAMS_ERROR.getCode(), "被保人信息参数缺失");
        }

        if (org.apache.commons.lang.StringUtils.isEmpty(dto.getRiskTime())) {
            throw new BizException(ExcptEnum.PARAMS_ERROR.getCode(), "出险时间信息缺失");
        }

        Date riskTime = DateUtil.parseDate(dto.getRiskTime(), "yyyy-MM-dd HH:mm");
        SmOrderInsured orderInsured = insuredMapper.selectByInsuredId(dto.getInsuredId());

        if (Objects.isNull(orderInsured)) {
            throw new BizException(ExcptEnum.PARAMS_ERROR.getCode(), "被保人不存在");
        }
        OrderBasicVo orderBasicVo = orderMapper.queryFastOrder(orderInsured.getFhOrderId());

        if (Objects.isNull(orderBasicVo)) {
            throw new BizException(ExcptEnum.PARAMS_ERROR.getCode(), "订单信息不存在");
        }

        if (StringUtils.isEmpty(orderBasicVo.getStartTime()) || StringUtils.isEmpty(orderBasicVo.getEndTime())) {
            throw new BizException(ExcptEnum.PARAMS_ERROR.getCode(), "起、终保时间异常");
        }

        Date startTime = DateUtil.parseDate(orderBasicVo.getStartTime());
        Date endTime = DateUtil.parseDate(orderBasicVo.getEndTime());

        if (riskTime.before(startTime) || riskTime.after(endTime)) {
            throw new BizException(
                    ExcptEnum.PARAMS_ERROR.getCode(),
                    "出险时间与保单有效保障时间冲突，请核实！"
            );
        }

    }

    /**
     * 用户微信理赔代办query
     *
     * @param query
     * @return
     */
    private WxClaimQuery buildWxClaimTodoQuery(WxClaimQuery query) {
        WxSessionVO session = checkAuthorityEpAg(query.getOpenId(), query.getAuthorization());
        query.setChannel(session.getChannel());
        String searchUserId = session.getUserId();
        if (session.isBindEmployee()) {
            query.setUserId(session.getUserId());
        } else if (session.isBindAgent()) {
            query.setAgentId(session.getAgentId());
            searchUserId = session.getAgentTopUserId();
        }

        // 渠道PCO
        if (permissionUtil.isChannelPCO(ThreadUserUtil.USER_DETAIL_TL.get())) {
            query.setPicRole(Boolean.TRUE);
            query.setRegionName(session.getRegionName());
            query.setOrgName(session.getOrganizationName());
        }
        // 总部创新业务对接人
        else if (Objects.equals(session.getUserId(), headquartersApprover)) {
            query.setPicRole(Boolean.TRUE);
            query.setRegionName(ORG_REGION_HQ);
            query.setOrgName(ORG_BRANCH_HQ);
        }
        return query;
    }

    /**
     * 查看用户微信理赔快递信息
     *
     * @param claimId
     * @param openId
     * @param authorization
     * @return
     */
    public List<SmClaimExpressVO> getWxClaimExpressByClaimId(int claimId, String openId, String authorization) {
        checkAuthorityEpAg(openId, authorization);
        return claimService.getClaimExpressByClaimId(claimId);
    }

    /**
     * 保存用户微信理赔快递信息
     *
     * @param claimId
     * @param dto
     */
    public void saveWxClaimExpress(int claimId, WxClaimExpressDTO dto) {
        WxSessionVO session = checkAuthorityEpAg(dto.getOpenId(), dto.getAuthorization());
        dto.setClaimId(claimId);
        dto.setUserName(session.getUserName());
        claimServiceConfig.saveWxClaimExpress(dto);
    }

    /**
     * 微信操作理赔流程
     *
     * @param dto
     */
    public void saveWxClaimProgress(WxProgressDTO dto) {
        WxSessionVO session = checkAuthorityEpAg(dto.getOpenId(), dto.getAuthorization());
        ThreadUserUtil.USER_DETAIL_TL.remove();
        UserDetailVO userDetailVO = ThreadUserUtil.USER_DETAIL_TL.get();
        if (!permissionUtil.isHeadRole(userDetailVO) && !permissionUtil.isChannelPCO(userDetailVO)) {
            throw new MSBizNormalException("", "当前流程，暂无权限");
        }
        // 禁止非总部角色审核总部节点 （目前没有节点权限配置的地方）
        if (!permissionUtil.isHeadRole(userDetailVO) && Objects.equals(dto.getSCode(), ClaimWorkflow.STEP_DATA_CHECK_BY_SAFES_CENTER)) {
            throw new BizException(ExcptEnum.INVALID_USER_AUTH_844448.getCode(), "案件已移交到总部审核");
        }

        // 禁止操作保司核赔节点
        if (Objects.equals(dto.getSCode(), STEP_TO_PAY)) {
            throw new BizException(ExcptEnum.INVALID_USER_AUTH_844448.getCode(), "案件已移交到保司核赔");
        }

        // 总部人员（包含区域岗）负责的理赔案件，提交理赔资料后，流程自动到总部审核阶段，跳过机构审核阶段
        if ((Objects.equals(dto.getSCode(), STEP_DATA_PREPARE) || Objects.equals(dto.getSCode(), STEP_DATA_PREPARE2)) && Objects.equals(
                dto.getOCode(),
                ClaimWorkflow.OPTION_CODE_DATA_SUBMITTED
        )) {
            AuthUserVO currUser = userService.getAuthUserByUserId(session.getUserId());
            // 当前登录用户不存在
            if (Objects.isNull(currUser)) {
                throw new BizException(ExcptEnum.DATA_ERROR_801304);
            }
            AuthUserVO channelPCO = userService.getChannelPCOBydOrg(currUser.getRegionName(), currUser.getOrganizationName());
            // 查询机构对接人
            if (Objects.isNull(channelPCO)) {
                dto.setOCode(ClaimWorkflow.JUMP_TO_DATA_CHECK_BY_SAFES_CENTER);
                dto.setOName("跳转进度至总部审核阶段");
            }
        }
        // 设置操作人名称
        dto.setCreateBy(session.getUserName());
        SmClaim claim = claimService.getClaimById(dto.getClaimId());
        claimServiceConfig.findClaimByChannelCode(claim.getProcessType()).saveProgress(dto);
    }

    /**
     * 更新用户理赔信息
     *
     * @param dto
     * @return
     */
    public void updateUserClaim(WxClaimDTO dto) {
        checkAuthorityEpAg(dto.getOpenId(), dto.getAuthorization());
        claimUpdateCheck(dto);
        SmClaim smClaim = claimService.queryUnClosedCaseByInsuredIdAndRiskType(dto.getInsuredId(), dto.getRiskType());

        if (Objects.nonNull(smClaim) && !Objects.equals(smClaim.getId(), dto.getId())) {
            throw new BizException(CLAIM_DUPLICATE_CASE);
        }

        claimService.updateClaim(dto);
    }

    public void claimUpdateCheck(WxClaimDTO dto) {

        WxClaimDetailVO wxClaimDetailVO = claimService.getSmClaimById(dto.getId());
        if (Objects.isNull(wxClaimDetailVO)) {
            throw new BizException(ExcptEnum.PARAMS_ERROR.getCode(), "理赔信息不存在");
        }
        if (DateUtil.parseDate(dto.getRiskTime(), "yyyy-MM-dd HH:mm").after(DateUtil.parseDate(wxClaimDetailVO.getReportTime(), DateUtil.CN_LONG_FORMAT))) {
            throw new BizException(ExcptEnum.PARAMS_ERROR.getCode(), "出险时间不能晚于报案时间");
        }


        if (!Objects.equals(dto.getRiskType(), wxClaimDetailVO.getRiskType()) && EnumClaimProcessType.kp().contains(dto.getProcessType())) {
            throw new BizException("", "众安开平不支持修改出险类型");
        }

        // 校验修改理赔信息用户权限
        checkUpdateUserClaimRole(wxClaimDetailVO);
    }

    /**
     * 校验修改理赔信息用户权限
     * 阶段  ：权限
     * 待指导报案	 - 客户经理
     * 待提交资料	 - 客户经理
     * 补充待提交资料	 - 客户经理
     * 机构审核	- 渠道PCO
     *
     * @param wxClaimDetailVO
     */
    public void checkUpdateUserClaimRole(WxClaimDetailVO wxClaimDetailVO) {
        String claimState = wxClaimDetailVO.getClaimState();
        UserDetailVO userDetailVO = ThreadUserUtil.USER_DETAIL_TL.get();
        String jobNumber = userDetailVO.getJobNumber();
        SmClaimVO smClaimVO = claimService.getSmClaimByClaimId(wxClaimDetailVO.getId());

        log.info(JSONObject.toJSONString(userDetailVO));
        log.info("当前节点步骤-{}", claimState);
        log.info("当前节点操作用户-{}", jobNumber);
        log.info("理赔单推荐人-{}", smClaimVO.getRecommendUserId());
        // 如果当前用户是理赔订单的管护客户经理
        if ((Objects.equals(claimState, STEP_GUIDE) || Objects.equals(claimState, STEP_DATA_PREPARE) || Objects.equals(claimState, STEP_DATA_PREPARE2)) && Objects.equals(
                jobNumber,
                smClaimVO.getRecommendUserId()
        )) {
            return;
        }

        if (Objects.equals(claimState, STEP_DATA_CHECK_BY_PIC) && Objects.equals(
                userDetailVO.getOrgName(),
                smClaimVO.getRecommendOrganizationName()
        ) && permissionUtil.isChannelPCO(userDetailVO)) {
            return;
        }

        throw new MSBizNormalException("", "当前节点或用户暂无操作权限");
    }

    public PageInfo<SmClaimVO> pageWxSettlement(SmSettlementClaimQuery query) {

        UserDetailVO userDetailVO = ThreadUserUtil.USER_DETAIL_TL.get();

        List<ClaimConfig.User> claimUserList = userService.getSettlements();
        if (Objects.isNull(userDetailVO) || claimUserList.stream().map(ClaimConfig.User::getUserName).noneMatch(x -> Objects.equals(x, userDetailVO.getEmployeeName()))) {
            log.warn("登录人信息-{}", userDetailVO.getEmployeeName());
            throw new MSBizNormalException("", "当前用户暂无操作权限");
        }

        query.setSettlement(userDetailVO.getEmployeeName());

        return claimService.pageWxSettlement(query);

    }

    public PageInfo<SmClaimGuideHandledVO> pageWxSettlementHandled(SmSettlementClaimQuery query) {

        UserDetailVO userDetailVO = ThreadUserUtil.USER_DETAIL_TL.get();


        List<ClaimConfig.User> claimUserList = userService.getSettlements();
        if (Objects.isNull(userDetailVO) || claimUserList.stream().map(ClaimConfig.User::getUserName).noneMatch(x -> Objects.equals(x, userDetailVO.getEmployeeName()))) {
            log.warn("登录人信息-{}", userDetailVO.getEmployeeName());
            throw new MSBizNormalException("", "当前用户暂无操作权限");
        }

        query.setSettlement(userDetailVO.getEmployeeName());

        return claimService.pageWxSettlementHandled(query);

    }

    public SmClaimPosterApplyDetailVO saveApply(SmClaimPosterApplyDetailVO vo) {
        WxSessionVO session = checkAuthorityEpAg(vo.getOpenId(), vo.getAuthorization());
        boolean validToken = tokenService.validateBusinessToken(vo.getOpenId(), SmConstants.SM_CLAIM_BUSINESS_CODE, vo.getToken());
        if (!validToken) {
            throw new BizException(ExcptEnum.INVALID_TOKEN_501019);
        }

        // 删除业务token
        tokenService.deleteBusinessToken(vo.getOpenId(), SmConstants.SM_CLAIM_BUSINESS_CODE, vo.getToken());
        // 设置审计信息
        vo.setCreateBy(session.getUserName());
        vo.setUpdateBy(session.getUserName());
        vo.setApplyUserId(session.getUserId());
        vo.setApplyUserName(session.getUserName());
        // 执行操作
        return smClaimPosterApplyService.saveApply(vo);
    }

    public SmClaimPosterApplyDetailVO qryApplyDetail(String smClaimId, String openId, String authorization) {
        checkAuthorityEpAg(openId, authorization);
        return smClaimPosterApplyService.qryApplyDetail(smClaimId);
    }

    /**
     * 理赔评价
     * @param vo 评价内容
     * @return 理赔评价
     */
    public Integer claimEvaluation(ClaimEvaluationVo vo) {
        checkEvaluation(vo);
        Integer claimId = vo.getClaimId();
        SmClaim claim = claimService.getClaimById(claimId);
        if(claim == null){
            throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(),"理赔信息不存在");
        }
        Integer evalStatus = claim.getEvaluationStatus();
        if(!Objects.equals(evalStatus,0)){
            throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(),"当前理赔案件信息不可评价");
        }
        SmClaimEvaluationPO po = convertEntity(vo);
        String score = vo.getEvaluationScore();
        Integer evaluationId = txServiceManager.excute(()->{
            int id = claimEvaluationService.save(po);
            claimService.finishEvaluation(claimId,score);
            return id;
        });
        return evaluationId;
    }

    private void checkEvaluation(ClaimEvaluationVo vo){
        ValidatorUtils.validateParam(vo);
        String score = vo.getEvaluationScore();
        BigDecimal val = new BigDecimal(score);
        if(val.compareTo(BigDecimal.ONE)>=0 && val.compareTo(new BigDecimal("3"))<=0){
            if(CollectionUtil.isEmpty(vo.getEvaluationLabel()) && StringUtils.isBlank(vo.getEvaluationContent())){
                throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(),"请填写评价内容");
            }
        }
    }

    private SmClaimEvaluationPO convertEntity(ClaimEvaluationVo vo){
        UserDetailVO user = ThreadUserUtil.USER_DETAIL_TL.get();

        Date now = new Date();
        SmClaimEvaluationPO po = new SmClaimEvaluationPO();
        po.setClaimId(vo.getClaimId());
        po.setEvaluationContent(vo.getEvaluationContent());
        po.setEvaluationLabel(JSON.toJSONString(vo.getEvaluationLabel()));
        po.setEvaluationScore(vo.getEvaluationScore());
        po.setEvaluationSharePic(JSON.toJSONString(vo.getEvaluationSharePic()));
        po.setQuestionnaire(JSON.toJSONString(vo.getQuestionnaireList()));
        if(user!=null) {
            po.setOperatorId(user.getJobNumber());
            po.setOperatorName(user.getEmployeeName());
            po.setOperatorPostId(user.getPostId());
            po.setOperatorPostName(user.getPostName());
            po.setOperatorOrganizationCode(user.getOrgCode());
            po.setOperatorOrganizationName(user.getOrgName());
            po.setCreateUser(user.getEmployeeName());
            po.setUpdateUser(user.getEmployeeName());

            AuthUserVO authUserVO = userService.getAuthUserByUserId(user.getJobNumber());
            if(authUserVO!=null){
                po.setOperatorRegionCode(authUserVO.getRegionCode());
                po.setOperatorRegionName(authUserVO.getRegionName());
            }
        }
        po.setCreateTime(now);
        po.setUpdateTime(now);
        po.setEnabledFlag(0);
        return po;
    }

    private ClaimEvaluationVo convertVo(SmClaimEvaluationPO po){
        ClaimEvaluationVo vo = new ClaimEvaluationVo();
        vo.setClaimId(po.getClaimId());
        vo.setEvaluationContent(po.getEvaluationContent());
        vo.setEvaluationLabel(JSON.parseArray(po.getEvaluationLabel(),String.class));
        vo.setEvaluationScore(po.getEvaluationScore());
        String sharePic = po.getEvaluationSharePic();
        if(StringUtils.isNotBlank(sharePic)){
            List<String> list = JSON.parseArray(sharePic, String.class);
            List<String> sharePicList = fileService.reGenerateUrl4Expired(list);
            vo.setEvaluationSharePic(sharePicList);
        }
        vo.setQuestionnaireList(JSON.parseArray(po.getQuestionnaire(), ClaimQuestionnaireVo.class));
        return vo;
    }

    public ClaimEvaluationVo queryEvaluation(Integer claimId) {
        SmClaimEvaluationPO po = claimEvaluationService.queryByClaimId(claimId);
        if(po==null){
            return null;
        }
        return convertVo(po);
    }

    public List<ClaimQuestionnaireVo> questionnaireList() {
        List<SmClaimQuestionnairePO> questionnaireList = claimQuestionnaireService.questionnaireList();
        if(CollectionUtil.isEmpty(questionnaireList)){
            return Collections.emptyList();
        }
        return questionnaireList.stream().map(entry->{
            ClaimQuestionnaireVo vo = new ClaimQuestionnaireVo();
            vo.setQuestionnaireId(entry.getId());
            vo.setType(entry.getType());
            vo.setTitle(entry.getTitle());
            vo.setOptionList(JSON.parseArray(entry.getOptionList(), ClaimQuestionnaireOptionVo.class));
            return vo;
        }).collect(Collectors.toList());
    }
}
