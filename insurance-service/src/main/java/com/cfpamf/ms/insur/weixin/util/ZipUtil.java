package com.cfpamf.ms.insur.weixin.util;

import com.beust.jcommander.internal.Lists;
import com.cfpamf.ms.insur.admin.external.xm.model.XmRenewalTermDTO;
import lombok.extern.slf4j.Slf4j;
import net.lingala.zip4j.ZipFile;
import net.lingala.zip4j.io.inputstream.ZipInputStream;
import net.lingala.zip4j.model.FileHeader;

import java.io.File;
import java.util.List;


/**
 * zip工具栏
 *
 * <AUTHOR>
 * @date 2022/3/9 10:28
 */
@Slf4j
public class ZipUtil {

    public static void main(String[] args) throws Exception {
        List<XmRenewalTermDTO> renewalReturnDTOList = Lists.newArrayList();
        File tempFile = new File("C:\\Users\\<USER>\\ZHNX\\work\\tmpFile\\tmpFile.zip");
        ZipFile zipFile = new ZipFile(tempFile);
        //获取zip每个文件中的数据
        List<FileHeader> fileHeaders = zipFile.getFileHeaders();
        for (FileHeader fileHeader : fileHeaders) {
            ZipInputStream csvFileInputStream = zipFile.getInputStream(fileHeader);
            renewalReturnDTOList.addAll(CsvUtil.getCsvData(csvFileInputStream, XmRenewalTermDTO.class));
        }
        for (XmRenewalTermDTO xmRenewalTermDTO : renewalReturnDTOList) {

        }
    }


}
