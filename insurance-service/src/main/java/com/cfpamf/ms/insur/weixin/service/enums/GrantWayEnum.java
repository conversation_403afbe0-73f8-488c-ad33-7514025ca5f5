package com.cfpamf.ms.insur.weixin.service.enums;

import cn.hutool.core.util.StrUtil;
import lombok.Getter;

import java.util.Arrays;

public enum GrantWayEnum {

    WITHDRAW("withdraw", "乡助提现"),

    BANK_CARD("bank_card", "银行卡"),

    ;

    @Getter
    private final String code;

    @Getter
    private final String name;


    GrantWayEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    /**
     * 获取结算类型枚举
     *
     * @param code 类型编码
     * <AUTHOR>
     * @since 2021/11/02
     */
    public static GrantWayEnum deCode(String code) {
        return Arrays.stream(GrantWayEnum.values()).filter(x -> x.code.equals(code)).findFirst().orElse(null);
    }

    public static GrantWayEnum deName(String name) {
        return Arrays.stream(GrantWayEnum.values()).filter(x -> x.name.equals(name)).findFirst()
                .orElse(null);
    }


}
