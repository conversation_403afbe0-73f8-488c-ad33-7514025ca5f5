package com.cfpamf.ms.insur.weixin.service.underwriting;

import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.common.ms.util.DateUtil;
import com.cfpamf.ms.insur.admin.dao.safes.SmProductMapper;
import com.cfpamf.ms.insur.admin.pojo.dto.SmProductCoverageDTO;
import com.cfpamf.ms.insur.admin.pojo.dto.SmProductQuoteLimitDTO;
import com.cfpamf.ms.insur.admin.pojo.vo.*;
import com.cfpamf.ms.insur.admin.service.SmOrderManageService;
import com.cfpamf.ms.insur.admin.service.SmProductService;
import com.cfpamf.ms.insur.base.exception.BizException;
import com.cfpamf.ms.insur.base.exception.ExcptEnum;
import com.cfpamf.ms.insur.base.util.LambdaUtils;
import com.cfpamf.ms.insur.base.util.LocalDateUtil;
import com.cfpamf.ms.insur.base.util.NumCompareUtil;
import com.cfpamf.ms.insur.weixin.dao.safes.PolicyMapper;
import com.cfpamf.ms.insur.weixin.pojo.dto.WxGlProductQuoteDTO;
import com.cfpamf.ms.insur.weixin.pojo.dto.product.PremiumDTO;
import com.cfpamf.ms.insur.weixin.pojo.dto.product.QuoteLimitDTO;
import com.cfpamf.ms.insur.weixin.pojo.dto.product.TimeFactorItem;
import com.cfpamf.ms.insur.weixin.pojo.query.policy.CoverageQuery;
import com.cfpamf.ms.insur.weixin.pojo.query.policy.PlanQuery;
import com.cfpamf.ms.insur.weixin.pojo.vo.apply.common.GroupCheckReq;
import com.cfpamf.ms.insur.weixin.pojo.vo.apply.common.GroupEndorsement;
import com.cfpamf.ms.insur.weixin.pojo.vo.apply.common.GroupInsured;
import com.cfpamf.ms.insur.weixin.pojo.vo.product.CoverageVo;
import com.cfpamf.ms.insur.weixin.pojo.vo.product.EndorConfig;
import com.cfpamf.ms.insur.weixin.util.ExpressTools;
import com.cfpamf.ms.insur.weixin.util.UnitConvertor;
import org.apache.commons.lang.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.text.MessageFormat;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.cfpamf.ms.insur.weixin.service.underwriting.ErrorMessage.AMOUNT_LIMIT_ERROR;
import static com.cfpamf.ms.insur.weixin.service.underwriting.ErrorMessage.LIAB_AMOUNT_RELATION_LIMIT_ERROR;

/**
 * 团险核保
 */
@Component
public class GroupApplyHandler {

    @Autowired
    private PolicyMapper policyMapper;

    @Autowired
    private SmProductMapper smProductMapper;

    @Autowired
    private SmProductService productService;


    /**
     * 老的校验逻辑
     * 报价方案验证
     *
     * @param dto
     */
    public void applyCheck(WxGlProductQuoteDTO dto) {
        SmProductDetailVO productDetail = productService.getProductById(dto.getProductId());
        SmProductQuoteLimitVO quoteLimitSettings = productService.getProductQuoteLimit(dto.getProductId());
        /**
         * 1.判断职业类别限制
         */
        String[] ocpnGroupLimits = new String[0];
        String ocpnGroupLimit = productDetail.getGlOcpnGroup();
        if (ocpnGroupLimit != null) {
            ocpnGroupLimits = ocpnGroupLimit.split(",");
        }
        List<SmProductCoveragePremiumDTO> coveragePremiums = smProductMapper.listProductCoveragePremiums(dto.getProductId());
        for (WxGlProductQuoteDTO.OccupationPerson op : dto.getOcpnPersons()) {
            /**
             * 保障职业限制
             */
            if (Stream.of(ocpnGroupLimits).noneMatch(ogl -> Objects.equals(ogl, op.getOccupationGroup()))) {
                throw new MSBizNormalException(ExcptEnum.QUOTE_ERROR_801040.getCode(), "报价失败，" + op.getOccupationGroup() + "类职业不能投保");
            }
            // 保障项目保额判断
            for (Integer spcaId : dto.getSpcaIds()) {
                if (spcaId == null) {
                    throw new MSBizNormalException(ExcptEnum.QUOTE_ERROR_801040.getCode(), "报价失败，未选择责任信息");
                }
                Optional<SmProductCoveragePremiumDTO> optional = coveragePremiums.stream()
                        .filter(cp -> Objects.equals(cp.getSpcaId(), spcaId) && Objects.equals(cp.getOccupationGroup(), op.getOccupationGroup()))
                        .findFirst();
                boolean checkSuccess = true;
                if (!optional.isPresent()) {
                    checkSuccess = false;
                } else {
                    SmProductCoveragePremiumDTO premiumDTO = optional.get();
                    if (premiumDTO.getPremium() == null) {
                        checkSuccess = false;
                    }
                }
                if (!checkSuccess) {

                    List<SmProductCoverageAmountVO> coverageAmounts = smProductMapper.listProductCoverageAmountBySpcaIds(Collections.singletonList(spcaId));
                    if (coverageAmounts.size() > 0) {
                        SmProductCoverageAmountVO coverageAmount = coverageAmounts.get(0);
                        if (coverageAmount != null) {
                            throw new MSBizNormalException(ExcptEnum.QUOTE_ERROR_801040.getCode(), "报价失败，" + op.getOccupationGroup() + "类职业不能投保" + coverageAmount.getCvgItemName() + coverageAmount.getCvgNotice());
                        }
                    }
                }
            }
        }

        /*
         * 总起保人数判断校验
         */
        int quoteAllPerQty = dto.getOcpnPersons().stream().map(WxGlProductQuoteDTO.OccupationPerson::getPerQty).reduce(0, Integer::sum);
        if (quoteLimitSettings.getGlPerStartQty() != null && quoteAllPerQty < quoteLimitSettings.getGlPerStartQty()) {
            throw new MSBizNormalException(ExcptEnum.QUOTE_ERROR_801040.getCode(), "报价失败，" + "需" + quoteLimitSettings.getGlPerStartQty() + "人起保");
        }

        /*
         * 职业起保人数判断校验
         */
        List<SmProductQuoteLimitDTO.OcpnPerLimit> ocpnPerLimits = quoteLimitSettings.getOcpnPerLimit();
        // 报价单职业人数
        List<WxGlProductQuoteDTO.OccupationPerson> quoteOcpnPersons = dto.getOcpnPersons();
        if (ocpnPerLimits != null && !ocpnPerLimits.isEmpty()) {
            for (SmProductQuoteLimitDTO.OcpnPerLimit limit : ocpnPerLimits) {
                // 包含职业类别
                String[] occupationGroups = limit.getOccupationGroup().split(",");
                // 职业最小人数
                Integer minPerQty = limit.getMinPerQty();
                // 职业最小占比
                BigDecimal minPerRatio = limit.getMinPerRatio();
                // 过滤满条限制条件的职业人数
                List<WxGlProductQuoteDTO.OccupationPerson> matchOcpnPersons = quoteOcpnPersons.stream()
                        .filter(op -> Stream.of(occupationGroups)
                                .anyMatch(og -> Objects.equals(og, op.getOccupationGroup())))
                        .collect(Collectors.toList());
                // 在限制职业之内
                if (!matchOcpnPersons.isEmpty()) {
                    if (minPerQty != null) {
                        if (quoteAllPerQty < minPerQty) {
                            throw new MSBizNormalException(ExcptEnum.QUOTE_ERROR_801040.getCode()
                                    , new StringBuilder("报价失败，含").append(limit.getOccupationGroup())
                                    .append("类职业人员，需").append(limit.getMinPerQty())
                                    .append("人起保").toString());
                        }
                    }
                    if (minPerRatio != null) {
                        int matchOcpnQty = matchOcpnPersons.stream().map(WxGlProductQuoteDTO.OccupationPerson::getPerQty).reduce(0, Integer::sum);
                        if (minPerRatio.multiply(new BigDecimal(quoteAllPerQty)).divide(BigDecimal.valueOf(100.00), BigDecimal.ROUND_DOWN).intValue() < matchOcpnQty) {
                            throw new MSBizNormalException(ExcptEnum.QUOTE_ERROR_801040.getCode()
                                    , new StringBuilder("报价失败，").append(limit.getOccupationGroup())
                                    .append("类职业人员，不能超过总投保人数的").append(minPerRatio)
                                    .append("%").toString());
                        }
                    }
                }
            }
        }
        /**
         * 保障项目依赖关系判断校验
         */
        List<SmProductQuoteLimitDTO.CvgRelationLimit> cvgRelationLimits = quoteLimitSettings.getCvgRelationLimits();
        Map<Integer, Integer> spcaMap = smProductMapper.listProductCoverageAmounts(dto.getProductId())
                .stream()
                .collect(Collectors.toMap(SmProductCoverageAmountVO::getSpcaId, SmProductCoverageAmountVO::getSpcId));
        if (cvgRelationLimits != null && !cvgRelationLimits.isEmpty()) {
            // 报价单所有项目
            List<Integer> quoteSpcaIds = dto.getSpcaIds().stream().map(spcaMap::get).collect(Collectors.toList());
            cvgRelationLimits.forEach(crLimit -> {
                String sourceSpcId = crLimit.getSourceSpcId();
                // 报价选择了限制的保障项目
                if (sourceSpcId != null && quoteSpcaIds.contains(Integer.valueOf(sourceSpcId))) {
                    // 遍历所有依赖保障项目
                    String[] relyOnSpcIds = crLimit.getRelyOnSpcId().split(",");
                    String operation = crLimit.getOperation();

                    // 验证通过
                    boolean checkSuccess = false;
                    //依賴保障項目其一
                    if (Objects.equals(operation, "one")) {
                        for (String relyOnSpcId : relyOnSpcIds) {
                            if (quoteSpcaIds.contains(Integer.valueOf(relyOnSpcId))) {
                                checkSuccess = true;
                                break;
                            }
                        }
                    }
                    // 依賴保障項目所有
                    if (Objects.equals(operation, "all")) {
                        checkSuccess = true;
                        for (String relyOnSpcId : relyOnSpcIds) {
                            // 没有找到限制项目依赖的项目
                            if (!quoteSpcaIds.contains(Integer.valueOf(relyOnSpcId))) {
                                checkSuccess = false;
                            }
                        }
                    }

                    if (!checkSuccess) {
                        // 获取保障项目名称方法
                        StringBuilder cvgNameSb = new StringBuilder();
                        Map<Integer, String> coveragesMap = smProductMapper.listProductCoverages(dto.getProductId()).stream()
                                .collect(Collectors.toMap(SmProductCoverageDTO::getSpcId, SmProductCoverageDTO::getCvgItemName));
                        for (String spcId : relyOnSpcIds) {
                            cvgNameSb.append(coveragesMap.get(Integer.valueOf(spcId))).append('和');
                        }
                        String cvgName = cvgNameSb.toString();
                        cvgName = cvgName.substring(0, cvgName.length() - 1);
                        throw new MSBizNormalException(ExcptEnum.QUOTE_ERROR_801040.getCode(),
                                "报价失败，" + coveragesMap.get(Integer.valueOf(sourceSpcId)) + ",必须投保" + cvgName
                                        + (Objects.equals(operation, "all") ? "全部" : "其一"));
                    }
                }
            });
        }

        List<Integer> spcaIds = dto.getSpcaIds();
        List<SmProductCoverageAmountVO> coverageAmounts = smProductMapper.listProductCoverageAmountBySpcaIds(spcaIds);

        if (coverageAmounts != null) {
            /**
             * 约束某些职业下，多个责任之间的保额关系~
             */
            checkCvgAmountLimit(dto, quoteLimitSettings, coverageAmounts);

            /**
             * 约束某些职业类别下某一个责任的保额大小~
             */
            checkOcp4LiabAmountLimit(dto, quoteLimitSettings, coverageAmounts);
        }
    }

    /**
     * 约束某些职业下，多个责任之间的保额关系~
     *
     * @param dto
     * @param limitSetting
     * @param coverageAmounts
     */
    public void checkCvgAmountLimit(WxGlProductQuoteDTO dto, SmProductQuoteLimitVO limitSetting, List<SmProductCoverageAmountVO> coverageAmounts) {
        List<SmProductQuoteLimitDTO.CvgAmountLimit> limits = limitSetting.getCvgAmountLimits();
        if (limits != null && !limits.isEmpty()) {
            Map<Integer, String> cvgNameMap = new HashMap<>();
            Map<Integer, BigDecimal> cvgAmountMap = new HashMap<>();
            coverageAmounts.forEach(entry -> {
                cvgAmountMap.put(entry.getSpcId(), entry.getCvgAmount());
                cvgNameMap.put(entry.getSpcId(), entry.getCvgItemName());
            });
            List<WxGlProductQuoteDTO.OccupationPerson> quoteOcpnPersons = dto.getOcpnPersons();

            limits.forEach(caLimit -> {
                String[] occupationGroups = caLimit.getOccupationGroup().split(",");
                Integer sourceSpcId = Integer.valueOf(caLimit.getSourceSpcId());
                Integer targetSpcId = Integer.valueOf(caLimit.getTargetSpcId());
                String operation = caLimit.getOperation();
                BigDecimal amountRatio = caLimit.getAmountRatio();
                // 客户选择的职业人数
                for (WxGlProductQuoteDTO.OccupationPerson sop : quoteOcpnPersons) {
                    // 产品限制职业
                    for (String limitOcpn : occupationGroups) {
                        // 客户选择了该职业
                        if (Objects.equals(limitOcpn, sop.getOccupationGroup())) {
                            BigDecimal sourceAmount = cvgAmountMap.get(sourceSpcId);
                            BigDecimal targetAmount = cvgAmountMap.get(targetSpcId);
                            if (sourceAmount == null || targetAmount == null) {
                                continue;
                            }
                            BigDecimal targetRatioAmount = targetAmount.multiply(amountRatio).divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_UP);
                            boolean isExpect = NumCompareUtil.compareAndExpect(sourceAmount, targetRatioAmount, operation);
                            // A保障项目保额 不符合B保障项目保额设置比例
                            if (!isExpect) {
                                String sourceCvgName = cvgNameMap.get(sourceSpcId);
                                String targetCvgName = cvgNameMap.get(targetSpcId);
                                String errorMsg = MessageFormat.format(LIAB_AMOUNT_RELATION_LIMIT_ERROR, limitOcpn, sourceCvgName, operation, targetCvgName, amountRatio);

                                throw new BizException(ExcptEnum.QUOTE_ERROR_801040.getCode(), errorMsg);
                            }
                        }
                    }
                }
            });
        }
    }

    /**
     * 约束某些职业类别下某一个责任的保额大小
     *
     * @param dto
     * @param limits
     * @param coverageAmounts
     */
    private void checkOcp4LiabAmountLimit(WxGlProductQuoteDTO dto, SmProductQuoteLimitVO limits, List<SmProductCoverageAmountVO> coverageAmounts) {
        List<SmProductQuoteLimitDTO.Ocp4LiabAmountLimit> ocp4LiabAmountLimits = limits.getOcp4LiabAmountLimits();

        if (ocp4LiabAmountLimits != null && !ocp4LiabAmountLimits.isEmpty()) {
            Map<Integer, BigDecimal> cvgAmountMap = new HashMap<>();
            Map<Integer, String> cvgNameMap = new HashMap<>();
            coverageAmounts.forEach(entry -> {
                cvgAmountMap.put(entry.getSpcId(), entry.getCvgAmount());
                cvgNameMap.put(entry.getSpcId(), entry.getCvgItemName());
            });
            List<WxGlProductQuoteDTO.OccupationPerson> quoteOcpnPersons = dto.getOcpnPersons();
            ocp4LiabAmountLimits.forEach(caLimit -> {
                String[] occupationGroups = caLimit.getOccupationGroup().split(",");

                Integer sourceSpcId = Integer.valueOf(caLimit.getSourceSpcId());
                String operation = caLimit.getOperation();
                for (WxGlProductQuoteDTO.OccupationPerson sop : quoteOcpnPersons) {
                    for (String limitOcpn : occupationGroups) {
                        if (Objects.equals(limitOcpn, sop.getOccupationGroup())) {
                            BigDecimal sourceAmount = cvgAmountMap.get(sourceSpcId);
                            if (sourceAmount == null) {
                                continue;
                            }
                            BigDecimal amount = cvgAmountMap.get(sourceSpcId);
                            BigDecimal limitAmount = caLimit.getLimitAmount();
                            Object rtn = ExpressTools.eval(amount.toString(), limitAmount.toString(), operation);
                            if (ObjectUtils.equals(rtn, Boolean.FALSE)) {
                                String cvgItemName = cvgNameMap.get(sourceSpcId);
                                String jobClass = caLimit.getOccupationGroup();
                                String limitAmoutNotice = caLimit.getLimitAmountNotice();
                                throw new BizException("-1", MessageFormat.format(AMOUNT_LIMIT_ERROR, jobClass, cvgItemName, operation, limitAmoutNotice));
                            }
                        }
                    }
                }
            });
        }
    }


    /**
     * 投保校验
     *
     * @param req
     * @return
     */
    public boolean applyCheck(GroupCheckReq req) {

        Integer productId = req.getProductId();
        PlanQuery plan = req.getPlan();

        List<CoverageQuery> coverageList = plan.getCoverages();
        Map<String, Integer> ocpMap = new HashMap<>();
        for (GroupCheckReq.OccupationItem i : req.getOccupationItemList()) {
            Integer qty = ocpMap.get(i.getOccupationGroup());
            if (qty != null) {
                qty = qty + i.getQty();
            } else {
                qty = i.getQty();
            }
            ocpMap.put(i.getOccupationGroup(), qty);
        }
        TimeFactorItem timeFactor = req.getTimeFactor();
        /**
         * 验证数据完整性
         */
//        checkPremium(productId,plan.getId(),coverageList,ocpMap.keySet());
        /**
         * 核保
         */
        applyCheck(productId, ocpMap, coverageList, timeFactor);
        return true;
    }

    /**
     * 验证产品数据的完整性
     */
    private void checkPremium(Integer productId, Integer planId, List<CoverageQuery> coverageList, Set<String> jobSet) {
        List<PremiumDTO> premiums = policyMapper.queryPremiumByPlan(productId, planId);
        if (premiums != null) {
            Map<Integer, List<PremiumDTO>> premiumMap = LambdaUtils.groupBy(premiums, PremiumDTO::getSpcaId);
            for (CoverageQuery cq : coverageList) {
                List<PremiumDTO> cvgPremiums = premiumMap.get(cq.getAmount().getSpcaId());
                if (cvgPremiums != null) {
                    Map<String, PremiumDTO> ocpPremiumMap = LambdaUtils.safeToMap(cvgPremiums, PremiumDTO::getOccupationGroup);
                    for (String ocp : jobSet) {
                        PremiumDTO dto = ocpPremiumMap.get(ocp);
                        if (dto == null || dto.getPremium() == null) {
                            throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(), String.format("[%s]未配置%s类职业的费率", cq.getCvgItemName(), ocp));
                        }
                    }
                }
            }
        }
    }

    @Autowired
    private SmOrderManageService orderManageService;

    /**
     * 投保校验
     *
     * @param req
     * @return
     */
    public boolean endorCheck(GroupEndorsement req) {
        String orderId = req.getOrderId();
        EndorConfig config = policyMapper.queryEndorConfigByOrder(orderId);
        if (config == null) {
            return true;
        }
        List<GroupInsured> insureds = req.getInsuredList();
        if (insureds == null) {
            throw new BizException(ExcptEnum.PARAMS_ERROR.getCode(), "被保人列表不能为空");
        }
        /**
         * 验证职业类别
         */
        Integer addJobClass = config.getAdditionJobClass();
        if (addJobClass == 0) {
            List<String> ocps = policyMapper.queryJobList(orderId, null);
            if (ocps != null) {
                Set<String> ocpSet = new HashSet<>(ocps);
                for (GroupInsured entry : insureds) {
                    if (!ocpSet.contains(entry.getOccupationGroup())) {
                        throw new BizException(ExcptEnum.PARAMS_ERROR.getCode(), "该产品不支持批增新职业类别");
                    }
                }
            }
        }
        /**
         * 投保份数限制
         */
        Date effTime = LocalDateUtil.parseTime(req.getAdditionEffectiveTime());
        List<GroupInsured> insuredList = req.getInsuredList();
        List<String> idNumbers = insuredList.stream().filter(entry -> entry.getOpType() == 1).map(GroupInsured::getIdNumber).collect(Collectors.toList());
        orderManageService.checkBuyLimit(req.getProductId(), 1, effTime, idNumbers);
        return true;
    }

    public void quoteLimitCheck(int qty, Map<String, Integer> ocpMap, Map<String, CoverageQuery> coverageMap, QuoteLimitDTO entry) {
        switch (entry.getLimitType()) {
            case SmProductQuoteLimitItemVO.LIMIT_TYPE_PER:
                checkPersonNumber(qty, entry);
                return;
            case SmProductQuoteLimitItemVO.LIMIT_TYPE_OCPN_PER:
                checkOcpnNumber(qty, ocpMap, entry);
                return;
            case SmProductQuoteLimitItemVO.LIMIT_TYPE_CVG_RELY:
                checkCvgRelation(coverageMap, entry);
                return;
            case SmProductQuoteLimitItemVO.LIMIT_TYPE_OCVG_AMOUNT:
                checkCvgAmountLimit(ocpMap, coverageMap, entry);
                return;
            case SmProductQuoteLimitItemVO.LIMIT_TYPE_OCVG_AMOUNT_2:
                checkOcp4LiabAmountLimit(ocpMap, coverageMap, entry);
                return;
            /**
             * [保障期限]不做校验(已跟产品沟通确认)
             */
            case SmProductQuoteLimitItemVO.LIMIT_TYPE_TIME:
                return;
            default:
                break;
        }
    }

    /**
     * 报价数据校验
     *
     * @param
     * @return
     */
    public void applyCheck(Integer productId, Map<String, Integer> ocpMap, List<CoverageQuery> coverageList, TimeFactorItem timeFactor) {

        List<QuoteLimitDTO> limits = policyMapper.queryQuoteLimit(productId);
        if (limits != null) {
            Map<String, CoverageQuery> coverageMap = new HashMap<>();
            coverageList.forEach(c -> {
                coverageMap.put(String.valueOf(c.getSpcId()), c);
            });
            Integer qty = ocpMap.values().stream().reduce(Integer::sum).get();
            for (QuoteLimitDTO entry : limits) {
                switch (entry.getLimitType()) {
                    case SmProductQuoteLimitItemVO.LIMIT_TYPE_PER:
                        checkPersonNumber(qty, entry);
                        break;
                    case SmProductQuoteLimitItemVO.LIMIT_TYPE_OCPN_PER:
                        checkOcpnNumber(qty, ocpMap, entry);
                        break;
                    case SmProductQuoteLimitItemVO.LIMIT_TYPE_CVG_RELY:
                        checkCvgRelation(coverageMap, entry);
                        break;
                    case SmProductQuoteLimitItemVO.LIMIT_TYPE_OCVG_AMOUNT:
                        checkCvgAmountLimit(ocpMap, coverageMap, entry);
                        break;
                    case SmProductQuoteLimitItemVO.LIMIT_TYPE_OCVG_AMOUNT_2:
                        checkOcp4LiabAmountLimit(ocpMap, coverageMap, entry);
                        break;
                    case SmProductQuoteLimitItemVO.LIMIT_TYPE_TIME:
                        checkShortTermLimit(ocpMap, entry, timeFactor);
                        break;
                    default:
                        break;
                }
            }
        }
    }

    /**
     * 短期险-限制条件
     *
     * @param ocpMap
     * @param entry
     */
    public void checkShortTermLimit(Map<String, Integer> ocpMap, QuoteLimitDTO entry, TimeFactorItem timeFactor) {
        Set<String> jobList = new HashSet<>(ocpMap.keySet());

        String occpGroup = entry.getOccupationGroup();
        if (StringUtils.isNotBlank(occpGroup)) {
            List<String> occpGroupList = Arrays.asList(occpGroup.split(","));
            jobList.retainAll(occpGroupList);
            if (jobList.size() > 0) {
                String factorValue = timeFactor.getFactorValue();
                String timeUnit = timeFactor.getTimeUnit();
                String settingUnit = entry.getUnit();
                String supportTime = entry.getSupportTime();
                /**
                 * 暂不支持单位换算
                 */
                String tips = "{0}类职业，保障期限需{1}{2}个{3}";
                String chinaUnit = UnitConvertor.convertTimeUnit(timeUnit);
                tips = MessageFormat.format(tips, occpGroup, entry.getOperation(), supportTime, chinaUnit);
                if (Objects.equals(timeUnit, settingUnit)) {
                    Object rtn = ExpressTools.eval(factorValue, supportTime, entry.getOperation());
                    if (ObjectUtils.equals(rtn, Boolean.FALSE)) {
                        throw new BizException("-1", tips);
                    }
                }
            }
        }
    }

    /**
     * TODO 责任保额限制条件
     *
     * @param ocpMap      当前保障计划选择的职业类别
     * @param coverageMap
     * @param entry
     */
    public void checkOcp4LiabAmountLimit(Map<String, Integer> ocpMap, Map<String, CoverageQuery> coverageMap, QuoteLimitDTO entry) {
        String ocp = entry.getOccupationGroup();
        List<String> ocpLimits = Arrays.asList(ocp.split(","));

        Set<String> ocpList = new HashSet<>(ocpMap.keySet());

        ocpList.retainAll(ocpLimits);
        if (ocpList.size() > 0) {
            String sourceId = entry.getSourceSpcId();
            CoverageQuery scq = coverageMap.get(sourceId);
            if (scq != null) {
                BigDecimal amount = scq.getAmount().getCvgAmount();
                BigDecimal limitAmount = entry.getLimitAmount();

                Object rtn = ExpressTools.eval(amount.toString(), limitAmount.toString(), entry.getOperation());
                if (ObjectUtils.equals(rtn, Boolean.FALSE)) {
                    throw new BizException("-1", MessageFormat.format(AMOUNT_LIMIT_ERROR, ocp, scq.getCvgItemName(), entry.getOperation(), entry.getLimitAmountNotice()));
                }
            }
        }
    }

    /**
     * 多个责任的保额关系限制条件
     * 条件中的职业类别只需要满足一个则需要判断约束
     *
     * @param ocpMap
     * @param coverageMap
     * @param entry
     */
    public void checkCvgAmountLimit(Map<String, Integer> ocpMap, Map<String, CoverageQuery> coverageMap, QuoteLimitDTO entry) {
        String ocp = entry.getOccupationGroup();
        List<String> ocpLimits = Arrays.asList(ocp.split(","));
        Set<String> ocpList = new HashSet<>(ocpMap.keySet());
        /**
         * 判断只需要存在交集则需要判断约束关系
         */
        ocpList.retainAll(ocpLimits);
        if (ocpList.size() > 0) {
            String source = entry.getSourceSpcId();
            String rely = entry.getTargetSpcId();
            CoverageQuery scq = coverageMap.get(source);
            CoverageQuery rcq = coverageMap.get(rely);
            if (scq != null && rcq != null) {
                BigDecimal amount1 = scq.getAmount().getCvgAmount();
                BigDecimal amount2 = rcq.getAmount().getCvgAmount();
                amount2 = amount2.multiply(entry.getAmountRatio()).divide(new BigDecimal(100));
                Object rtn = ExpressTools.eval(amount1.toString(), amount2.toString(), entry.getOperation());
                if (ObjectUtils.equals(rtn, Boolean.FALSE)) {
                    String jobClass = entry.getOccupationGroup();
                    String sourceItemName = scq.getCvgItemName();
                    String targetItemName = rcq.getCvgItemName();
                    String operator = entry.getOperation();
                    BigDecimal amoutRatio = entry.getAmountRatio();
                    String errorMsg = MessageFormat.format(LIAB_AMOUNT_RELATION_LIMIT_ERROR, jobClass, sourceItemName, operator, targetItemName, amoutRatio);
                    throw new MSBizNormalException("-1", errorMsg);
                }
            }
        }
    }

    /**
     * 险种责任关系限制
     *
     * @param cvgMap
     * @param entry
     */
    public void checkCvgRelation(Map<String, CoverageQuery> cvgMap, QuoteLimitDTO entry) {
        String sourceSpc = entry.getSourceSpcId();
        String relySpc = entry.getRelyOnSpcId();
        List<String> relySpcList = Arrays.asList(relySpc.split(","));
        Set<String> cvgList = cvgMap.keySet();
        String lossCvgId = null;
        if (cvgList.contains(sourceSpc)) {
            switch (entry.getOperation()) {
                case "one":
                    for (String o : relySpcList) {
                        if (cvgList.contains(o)) {
                            return;
                        }
                    }
                    lossCvgId = relySpcList.get(0);
                    break;
                case "all":
                    if (cvgList.containsAll(relySpcList)) {
                        return;
                    }
                    lossCvgId = relySpcList.get(0);
                    break;
                default:
                    break;
            }
        }
        if (lossCvgId != null) {
            CoverageVo cvgVo = policyMapper.queryCoverageById(lossCvgId);
            if (cvgVo != null) {
                throw new BizException("-1", String.format("请选择[%s]", cvgVo.getCvgItemName()));
            }
        }
    }

    /**
     * 最少投保人数限制
     *
     * @param qty
     * @param entry
     */
    public void checkPersonNumber(int qty, QuoteLimitDTO entry) {
        if (entry.getMinPerQty() > qty) {
            throw new MSBizNormalException("-1", "最少投保人数" + entry.getMinPerQty());
        }
    }

    /**
     * 职业类别
     *
     * @param ocpMap
     * @param entry
     */
    public void checkOcpnNumber(Integer qty, Map<String, Integer> ocpMap, QuoteLimitDTO entry) {
        String ocp = entry.getOccupationGroup();
        String[] ocps = ocp.split(",");
        Set<String> ocpSet = new HashSet<>(Arrays.asList(ocps));
        int ocpPers = 0;
        boolean retFlag = false;
        for (String o : ocpSet) {
            if (!ocpMap.containsKey(o)) {
                retFlag = true;
                break;
            }
            ocpPers += ocpMap.get(o);
        }
        if (retFlag) {
            return;
        }
        if (entry.getMinPerQty() != null && entry.getMinPerQty() > ocpPers) {
            throw new MSBizNormalException("-1", "投保人数不满足要求");
        }
        if (entry.getMinPerRatio() != null) {
            BigDecimal ocpDecimal = new BigDecimal(ocpPers);
            BigDecimal insDecimal = new BigDecimal(qty);
            ocpDecimal = ocpDecimal.divide(insDecimal);
            if (ocpDecimal.compareTo(entry.getMinPerRatio()) < 0) {
                throw new MSBizNormalException("-1", "投保人数不满足要求");
            }
        }
    }


}
