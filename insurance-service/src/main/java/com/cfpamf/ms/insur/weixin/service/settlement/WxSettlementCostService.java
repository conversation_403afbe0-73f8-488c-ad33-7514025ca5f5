package com.cfpamf.ms.insur.weixin.service.settlement;

import com.cfpamf.ms.insur.base.bean.SmyPageInfo;
import com.cfpamf.ms.insur.weixin.pojo.query.settlement.WxCostQuery;
import com.cfpamf.ms.insur.weixin.pojo.query.settlement.WxUserCommissionDetailQuery;
import com.cfpamf.ms.insur.weixin.pojo.vo.WxCmsSmyVo;
import com.cfpamf.ms.insur.weixin.pojo.vo.settlement.WxSettlementCostTotalVO;
import com.cfpamf.ms.insur.weixin.pojo.vo.settlement.WxUserCommissionDetailVo;
import com.cfpamf.ms.insur.weixin.pojo.vo.settlement.WxUserCostListVo;
import com.github.pagehelper.PageInfo;

public interface WxSettlementCostService {

    /**
     * 微信24版我的推广费(分页列表+汇总)
     * @param query
     * @return
     */
    SmyPageInfo<WxUserCostListVo,WxCmsSmyVo> getWxUserCostListAndSummary(WxCostQuery query);

    /**
     * 微信端我的推广费列表
     * @param query
     * @return
     */
    PageInfo<WxUserCostListVo> getWxUserCostList(WxCostQuery query);
    /**
     * 微信端我的推广费汇总信息
     * @param query
     * @return
     */
    WxCmsSmyVo getWxUserCostSummary(WxCostQuery query);

    /**
     *
     * @param query
     * @return
     */
    SmyPageInfo<WxUserCostListVo, WxCmsSmyVo> pageWxUserCostListAndSummary(WxCostQuery query);

    /**
     *
     * @param query
     * @return
     */
    PageInfo<WxUserCostListVo> pageWxUserCostList(WxCostQuery query);

    /**
     *
     * @param query
     * @return
     */
    WxSettlementCostTotalVO get24WxUserHomePageCostSummary(WxCostQuery query);

    WxUserCommissionDetailVo getWxUserCommissionDetail(WxUserCommissionDetailQuery query);
}
