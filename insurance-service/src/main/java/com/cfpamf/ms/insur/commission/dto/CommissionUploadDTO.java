package com.cfpamf.ms.insur.commission.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.time.YearMonth;

/**
 * <AUTHOR> 2022/12/16 16:56
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@ApiModel("推广费上传明细")
public class CommissionUploadDTO {

    @ApiModelProperty("月份")
    YearMonth yearMonth;

    @ApiModelProperty("文件名称")
    String fileName;

    @ApiModelProperty("文件地址")
    String fileUrl;

    @ApiModelProperty("操作人姓名")
    String creatorName;

    @ApiModelProperty("操作人工号")
    String creatorJobNumber;

}
