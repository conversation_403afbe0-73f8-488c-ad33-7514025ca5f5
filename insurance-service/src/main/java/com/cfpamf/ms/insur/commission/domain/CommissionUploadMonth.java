package com.cfpamf.ms.insur.commission.domain;

import com.cfpamf.ms.insur.admin.pojo.po.BasePO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.time.LocalDateTime;
import java.time.YearMonth;

/**
 * <AUTHOR> 2022/12/16 15:38
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class CommissionUploadMonth extends BasePO {

    YearMonth commissionMonth;

    @ApiModelProperty("1-已导入 0-未导入")
    Integer state;

    @ApiModelProperty("导入次数")
    Integer count;

    @ApiModelProperty("最后操作时间")
    LocalDateTime lastUploadTime;

    @ApiModelProperty("创建人")
    String updaterName;

    @ApiModelProperty("创建人工号")
    String updaterJobNumber;

}
