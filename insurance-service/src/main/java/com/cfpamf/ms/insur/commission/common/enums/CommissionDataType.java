package com.cfpamf.ms.insur.commission.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.stream.Stream;
@Getter
@AllArgsConstructor
public enum CommissionDataType {
    INSURANCE("insurance", "保险"),
    LIFE_SERVICES("life_services", "生服");

    private String code;

    private String desc;


    public static String dict(String taskKey) {
        return Stream.of(values())
                .filter(em -> em.getCode().equals(taskKey))
                .findFirst().map(CommissionDataType::getDesc)
                .orElse("未知");
    }
}
