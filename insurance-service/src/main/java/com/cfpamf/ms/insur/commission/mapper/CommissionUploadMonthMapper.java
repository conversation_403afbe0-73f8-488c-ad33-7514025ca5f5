package com.cfpamf.ms.insur.commission.mapper;

import com.cfpamf.ms.insur.base.dao.MyMappler;
import com.cfpamf.ms.insur.commission.po.CommissionUploadMonthPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @date 2022/12/22 11:12
 */
@Mapper
public interface CommissionUploadMonthMapper extends MyMappler<CommissionUploadMonthPO> {

    /**
     * 插入 并统计
     *
     * @param month 月份数据
     * @return
     */
    int insertListOrCount(@Param("item") CommissionUploadMonthPO month);
}
