package com.cfpamf.ms.insur.weixin.pojo.query;

import com.cfpamf.ms.insur.base.bean.Pageable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.Date;

/**
 * @description: 微信提成查询Query
 * @author: zhang<PERSON>i
 * @create: 2018-07-23 15:44
 **/
@Data
@ApiModel
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class WxCmsQuery extends Pageable {

    @ApiModelProperty(value = "开始时间")
    private Date startTime;

    @ApiModelProperty(value = "结束时间")
    private Date endTime;

    @ApiModelProperty(value = "推荐人工号", hidden = true)
    private String userId;

    @ApiModelProperty(value = "微信openId")
    private String openId;

    @ApiModelProperty(value = "授权token")
    private String authorization;

    @ApiModelProperty(value = "身份证号码")
    private String idNumber;

    @ApiModelProperty(value = "身份证号码类型")
    private Integer idNumberType;

    @ApiModelProperty(value = "承保状态")
    private String appStatus;

    @ApiModelProperty(value = "客户负责人Id")
    private String customerAdminId;

    @ApiModelProperty(value = "客户代理人Id")
    private Integer agentId;

    /**
     * 渠道
     */
    @ApiModelProperty(value = "渠道")
    private String channel;

    @ApiModelProperty(value = "订单类型 0-短险_普通订单 1-短险_分销订单 2-短险_宣讲会推广订单 3-长险单")
    private Integer orderType;
}
