package com.cfpamf.ms.insur.commission.po;

import com.cfpamf.ms.insur.admin.pojo.po.BasePO;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import javax.persistence.Table;
import javax.validation.constraints.NotBlank;
import java.time.LocalDateTime;
import java.time.YearMonth;

/**
 * <AUTHOR> 2022/12/16 15:38
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@Table(name = "commission_upload_file")
public class CommissionUploadFilePO extends BasePO {

    YearMonth commissionMonth;

    String fileName;

    String fileUrl;

    String commissionDataType;

    LocalDateTime uploadTime;

    String creatorName;

    String creatorJobNumber;
}
