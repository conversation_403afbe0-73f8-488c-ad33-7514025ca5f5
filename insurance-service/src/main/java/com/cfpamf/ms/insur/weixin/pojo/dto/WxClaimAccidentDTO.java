package com.cfpamf.ms.insur.weixin.pojo.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 小额保险理赔DTO
 *
 * <AUTHOR>
 */
@Data
@ApiModel
public class WxClaimAccidentDTO extends WxAuthorizationDTO {

    /**
     * 事故类型
     */
    @ApiModelProperty("事故类型")
    private List<String> accidentTypeCodes;

    /*--add by z<PERSON>jian 2020-06-11 --*/
    /**
     * 是否存在医疗保险
     */
    @ApiModelProperty(value = "是否存在医疗保险")
    private String medicalInsur;

    /**
     * 是否存在其他保险
     */
    @ApiModelProperty(value = "是否存在其他保险")
    private String otherInsur;


    @ApiModelProperty("就诊医院")
    private String visitingHospital;

    @ApiModelProperty("就诊医院名称")
    private String visitingHospitalName;

    @ApiModelProperty("就诊日期，yyyy-MM-dd")
    private String visitingDate;

    @ApiModelProperty("就诊时职业")
    private String visitingOccupation;

}
