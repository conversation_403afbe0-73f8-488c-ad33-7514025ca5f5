package com.cfpamf.ms.insur.weixin.util;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class ContailerTools {
    /**
     * 从容器中安全获取数据
     *
     * @param container
     * @param size
     * @param <T>
     * @return
     */
    public static <T> T safeGet(List<T> container, int size) {
        if (container != null && container.size() > size) {
            return container.get(size);
        }
        return null;
    }

    public static <T, V> void safePut(T key, V value, Map<T, List<V>> map) {
        List<V> list = map.get(key);
        if (list == null) {
            list = new ArrayList<>();
        }
        list.add(value);
        map.put(key, list);
    }
}
