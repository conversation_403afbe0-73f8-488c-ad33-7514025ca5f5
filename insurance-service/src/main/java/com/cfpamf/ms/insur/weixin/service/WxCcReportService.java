package com.cfpamf.ms.insur.weixin.service;

import com.cfpamf.common.ms.util.DateUtil;
import com.cfpamf.ms.bms.facade.dto.ElementTreeNodeDTO;
import com.cfpamf.ms.bms.facade.vo.ModuleVO;
import com.cfpamf.ms.bms.facade.vo.UserDetailVO;
import com.cfpamf.ms.insur.base.exception.BizException;
import com.cfpamf.ms.insur.base.exception.ExcptEnum;
import com.cfpamf.ms.insur.base.service.BmsService;
import com.cfpamf.ms.insur.base.util.*;
import com.cfpamf.ms.insur.weixin.dao.dc.WxReportMapper;
import com.cfpamf.ms.insur.weixin.pojo.query.WxReportQuery;
import com.cfpamf.ms.insur.weixin.pojo.vo.*;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.function.Function;

/**
 * 微信报表service
 *
 * <AUTHOR>
 **/
@Slf4j
@Service
public class WxCcReportService extends WxAbstractService {

    /**
     * BmsService
     */
    @Autowired
    private BmsService bmsService;

    /**
     * 微信报表mapper
     */
    @Autowired
    private WxReportMapper wxReportMapper;

    /**
     * 获取登录用户的按钮权限列表
     *
     * @param openId
     * @param authorization
     * @return
     */
    public List<ModuleVO> getAuthPermissions(String openId, String authorization) {
        WxSessionVO session = checkAuthorityEmployee(openId, authorization);
        return bmsService.getContextUserPermissions(session.getAuthorization());
    }

    /**
     * 获取当前登录人区域分支树
     *
     * @param openId
     * @param authorization
     * @return
     */
    public List<ElementTreeNodeDTO> getVisibleAreaBranchTree(String openId, String authorization) {
        WxSessionVO session = checkAuthorityEmployee(openId, authorization);
        //log.info("session={}",session);
        return bmsService.getVisibleAreaBranchTree(session.getAuthorization());
    }

    /**
     * 查询微信个人业绩报表2019
     *
     * @param query
     * @return
     */
    public PageInfo<WxReportEmployeeVO> getWxReportEmployeeByPage(WxReportQuery query) {
        return commonQuery(wxReportMapper::listWxReportEmployeeAchmt, query);
    }

    /**
     * 查询区域业绩报表2019
     *
     * @param query
     * @return
     */
    public PageInfo<WxReportRegionVO> getWxReportRegionByPage(WxReportQuery query) {
        return commonQuery(wxReportMapper::listWxReportRegionAchmt, query);
    }

    /**
     * 查询微信机构业绩报表2019
     *
     * @param query
     * @return
     */
    public PageInfo<WxReportOrgVO> getWxReportOrgByPage(WxReportQuery query) {
        return commonQuery(wxReportMapper::listWxReportOrgAchmt, query);
    }

    /**
     * 查询微信个人业绩报表 2020
     *
     * @param query
     * @return
     */
    public PageInfo<WxReportBusDetailsPersonalVO> getWxReportPersonalBusinessDetailsByPage(WxReportQuery query) {

        setQueryType(query);
        CommonUtil.fillQueryMonthEndDayList(query);


        PageInfo<WxReportBusDetailsPersonalVO> ret =  commonQuery(wxReportMapper::listWxReportPersonalBusDetailsAchmt, query);

        return ret;
    }

    /**
     * 查询微信机构业绩报表 2020
     *
     * @param query
     * @return
     */
    public PageInfo<WxReportBusDetailsOrgVO> getWxReportOrgBusinessDetailsByPage(WxReportQuery query) {
        setQueryType(query);
        CommonUtil.fillQueryMonthEndDayList(query);
        return commonQuery(wxReportMapper::listWxReportOrgBusDetailsAchmt, query);
    }

    /**
     * 查询区域业绩报表 2020
     *
     * @param query
     * @return
     */
    public PageInfo<WxReportBusDetailsRegionVO> getWxReportRegionBusinessDetailsByPage(WxReportQuery query) {
        setQueryType(query);
        CommonUtil.fillQueryMonthEndDayList(query);
        return commonQuery(wxReportMapper::listWxReportRegionBusDetailsAchmt, query);
    }

    /**
     * 查询事业部业绩报表 2020
     *
     * @param query
     * @return
     */
    public PageInfo<WxReportBusDetailsBranchBuVO> getWxReportBranchBuBusinessDetailsByPage(WxReportQuery query) {
        setQueryType(query);
        CommonUtil.fillQueryMonthEndDayList(query);
        return commonQuery(wxReportMapper::listWxReportBranchBuBusDetailsAchmt, query);
    }

    private void setQueryType(WxReportQuery query){
        //统计类型（1月度 2年度）业务报表默认为1
        query.setType("1");
    }

    /**
     * 填充查询条件  monthEndDayList月底最后一天列表
     *
     * @param query
     */
    private void fillQueryMonthEndDayList(WxReportQuery query) {
        try {
            query.setStartDate(LocalDateUtil.stringToUdate(query.getStartMonth() + "-01", DateUtil.CN_YEAR_MONTH_DAY_FORMAT));
            query.setEndDate(DateUtil.getEndOfMonth(LocalDateUtil.stringToUdate(query.getEndMonth() + "-01", DateUtil.CN_YEAR_MONTH_DAY_FORMAT)));
        } catch (Exception e) {
            throw new BizException(ExcptEnum.HTTP_PARAM_ERROR_000097);
        }
        if (query.getEndDate().before(query.getStartDate())) {
            throw new BizException(ExcptEnum.HTTP_PARAM_ERROR_000097);
        }
        //当前月一样能取到数据，取当前日期减1
        Date nowDate = new Date();
        LocalDate localNowDate = LocalDateUtil.dateToLocalDate(nowDate);
        if (nowDate.before(query.getEndDate())) {
            query.setEndDate(DateUtil.addDay(nowDate, -1));
        }
        if (query.getEndDate().before(query.getStartDate())) {
            throw new BizException(ExcptEnum.HTTP_PARAM_ERROR_000097.getCode(), "数据还未生成");
        }
        LocalDate startDate = LocalDateUtil.dateToLocalDate(DateUtil.getBeginOfMonth(query.getStartDate()));
        LocalDate endDate = LocalDateUtil.dateToLocalDate(DateUtil.getBeginOfMonth(query.getEndDate()));
        List<String> monthEndDayList = new ArrayList<>(1);
        LocalDate monthEndDay;
        for (; !endDate.isBefore(startDate); startDate = startDate.plusMonths(1)) {
            monthEndDay = startDate.with(TemporalAdjusters.lastDayOfMonth());
            if (!localNowDate.isAfter(monthEndDay)) {
                monthEndDayList.add(localNowDate.plusDays(-1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                break;
            }
            monthEndDayList.add(monthEndDay.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        }
        query.setMonthEndDayList(monthEndDayList);
    }
//    /**
//     * 查询微信个人留存率报表 2020
//     *
//     * @param query
//     * @return
//     */
//    public PageInfo<WxReportCustStayPersonalVO> getWxReportPersonalCustStayByPage(WxReportQuery query) {
//        CommonUtil.fillQueryMonthEndDayList(query);
//        return commonQuery(wxReportMapper::listWxReportPersonalCustStay, query);
//    }
//
//    /**
//     * 查询微信机构留存率报表 2020
//     *
//     * @param query
//     * @return
//     */
//    public PageInfo<WxReportCustStayOrgVO> getWxReportOrgCustStayByPage(WxReportQuery query) {
//        CommonUtil.fillQueryMonthEndDayList(query);
//        return commonQuery(wxReportMapper::listWxReportOrgCustStay, query);
//    }
//
//    /**
//     * 查询微信区域留存率报表 2020
//     *
//     * @param query
//     * @return
//     */
//    public PageInfo<WxReportCustStayRegionVO> getWxReportRegionCustStayByPage(WxReportQuery query) {
//        CommonUtil.fillQueryMonthEndDayList(query);
//        return commonQuery(wxReportMapper::listWxReportRegionCustStay, query);
//    }
//
//    /**
//     * 查询微信事业部留存率报表 2020
//     *
//     * @param query
//     * @return
//     */
//    public PageInfo<WxReportCustStayBranchBuVO> getWxReportBranchBuCustStayByPage(WxReportQuery query) {
//        CommonUtil.fillQueryMonthEndDayList(query);
//        return commonQuery(wxReportMapper::listWxReportBranchBuCustStay, query);
//    }

    /**
     * 查询缓存公共类
     *
     * @param queryFun
     * @param query
     * @param <T>
     * @return
     */
    @SuppressWarnings("all")
    public <T> PageInfo<T> commonQuery(Function<WxReportQuery, List<T>> queryFun, WxReportQuery query) {
        buildDataPermissionPageQuery(query);
        PageHelper.startPage(query.getPage(), query.getSize());
        PageInfo<T> pageInfo = new PageInfo<>(queryFun.apply(query));
        return pageInfo;
    }

    /**
     * 构造数据权限查询条件
     *
     * @param query
     */
    private void buildDataPermissionPageQuery(WxReportQuery query) {
        WxSessionVO session = checkAuthorityEmployee(query.getOpenId(), query.getAuthorization());
        if (query.getStartDate() != null) {
            query.setStartDate(DateUtil.getBeginOfDay(query.getStartDate()));
        }
        if (query.getEndDate() != null) {
            query.setEndDate(DateUtil.getEndOfDay(query.getEndDate()));
        }
        // 为了复用ContextUserUtil 必须默认后端用户session到threadLocal
        UserDetailVO userDetail = SpringFactoryUtil.getBean(BmsService.class).getUserDetailByToken(session.getAuthorization());
        ThreadUserUtil.USER_DETAIL_TL.set(userDetail);
        Date s = new Date();
        //log.info("buildDataPermissionPageQuery开始时间：{}",s);
        SpringFactoryUtil.getBean(PermissionUtil.class).buildDataPermissionPageQuery(query);
        //log.info("查询参数：{}",query);
        //log.info("buildDataPermissionPageQuery花费时间:{}",new Date().getTime() - s.getTime());
        ThreadUserUtil.USER_DETAIL_TL.remove();
    }
}
