package com.cfpamf.ms.insur.weixin.pojo.dto;

import com.cfpamf.ms.insur.admin.pojo.vo.AuthUserVO;
import com.cfpamf.ms.insur.base.util.Base64Util;
import com.cfpamf.ms.insur.weixin.pojo.vo.WxUserVO;
import lombok.Data;
import me.chanjar.weixin.mp.bean.result.WxMpUser;

import java.nio.charset.StandardCharsets;

/**
 * 微信用户DTO
 *
 * <AUTHOR>
 */
@Data
public class WxUserDTO {

    /**
     * ID
     */
    private Integer id;

    /**
     * userType
     */
    private String userType;

    /**
     * 微信openId
     */
    private String wxOpenId;

    /**
     * 微信名称
     */
    private String wxNickName;

    /**
     * 微信头像Url
     */
    private String wxImgUrl;

    public static WxUserDTO newWxUserDto(WxMpUser mpUser) {
        WxUserDTO dto = new WxUserDTO();
        dto.wxOpenId = mpUser.getOpenId();
        dto.wxImgUrl = mpUser.getHeadImgUrl();
        dto.wxNickName = Base64Util.encryptBASE64(mpUser.getNickname().getBytes(StandardCharsets.UTF_8));
        dto.userType = AuthUserVO.USER_TYPE_WEIXIN;
        return dto;
    }

    public static WxUserDTO newWxUserDto(WxUserVO vo) {
        WxUserDTO dto = new WxUserDTO();
        dto.id = vo.getId();
        dto.wxOpenId = vo.getWxOpenId();
        dto.wxImgUrl = vo.getWxImgUrl();
        dto.wxNickName = vo.getWxNickName();
        dto.userType = vo.getUserType();
        return dto;
    }
}