package com.cfpamf.ms.insur.commission.dto.query;

import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import javax.validation.constraints.NotNull;
import java.time.YearMonth;

/**
 * <AUTHOR> 2022/12/23 13:04
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class CommissionUploadQuery {

    @ApiModelProperty(value = "查询月份", example = "2022-01")
    @NotNull(message = "查询月份不能为空")
    YearMonth commissionMonth;

    @ApiModelProperty(value = "佣金类型 保险insurance 生服life_services", example = "insurance",required = false)
    String commissionDataType;
}
