package com.cfpamf.ms.insur.weixin.pojo.dto;

import com.alibaba.fastjson.JSON;
import com.cfpamf.ms.insur.base.util.CommonUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 小额保险理赔DTO
 *
 * <AUTHOR>
 */
@Data
public class WxClaimDTO {

    /**
     * id
     */
    @ApiModelProperty(value = "id")
    private Integer id;

    /**
     * id
     */
    @ApiModelProperty(value = "claimId")
    private Integer claimId;

    /**
     * 微信openId
     */
    @ApiModelProperty(value = "微信openId")
    private String openId;

    /**
     * token
     */
    @ApiModelProperty(value = "token")
    private String token;

    /**
     * 微信token
     */
    @ApiModelProperty(value = "微信token")
    private String authorization;

    /**
     * 保单Id
     */
    @ApiModelProperty(value = "保单Id")
    private Integer insuredId;

    /**
     * 出险类型（1意外住院 2一般疾病 3重大疾病 4身故 5残疾 6意外门诊 7家财）
     */
    @ApiModelProperty(value = "出险类型（1意外住院 2一般疾病 3重大疾病 4意外身故 5残疾 6意外门诊 7家财 8疾病身故）")
    private String riskType;

    /**
     * 出险时间
     */
    @ApiModelProperty(value = "出险时间")
    private String riskTime;

    /**
     * 出险经过
     */
    @ApiModelProperty(value = "出险经过")
    private String riskDesc;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String note;

    /**
     * 理赔专员
     */
    @ApiModelProperty(value = "理赔专员")
    private String settlement;

    /**
     * 预估金额
     */
    @ApiModelProperty(value = "预估金额")
    private BigDecimal estimatedAmount;

    /**
     * 是否报销医保
     */
    @ApiModelProperty(value = "是否报销医保")
    private String medicalInsur;

    /**
     * 是否报销其他保险
     */
    @ApiModelProperty(value = "是否报销其他保险")
    private String otherInsur;

    /**
     * 创建角色
     */
    @ApiModelProperty(hidden = true)
    private String createRole;


    /**
     * 创建人
     */
    @ApiModelProperty(hidden = true)
    private String createBy;

    /**
     * 理赔状态
     */
    @ApiModelProperty(hidden = true)
    private String claimState;


    /**
     * 理赔状态
     */
    @ApiModelProperty(hidden = true)
    private String claimResult;

    /**
     * 理赔编号
     */
    @ApiModelProperty(hidden = true)
    private String claimNo;

    /**
     * 理赔原因
     */
    @ApiModelProperty("出险原因")
    private String claimRiskReason;

    /**
     * 事故类型：1单方事故 2:双方事故
     */
    @ApiModelProperty("当大类选择交通工具事故，需要选择事故类型：1单方事故 2:双方事故")
    private Integer riskReasonAccidentType;

    @ApiModelProperty("渠道")
    private String channel;

    @ApiModelProperty("恶性肿瘤")
    private String malignantTumour;


    @ApiModelProperty("流程类型")
    private String processType;

    @ApiModelProperty("导入创建时间")
    private String importCreateTime;

    @ApiModelProperty("就诊医院")
    private String visitingHospital;

    @ApiModelProperty("就诊医院名称")
    private String visitingHospitalName;

    @ApiModelProperty("就诊日期，yyyy-MM-dd")
    private String visitingDate;

    @ApiModelProperty("就诊时职业")
    private String visitingOccupation;
    /**
     * 事故类型
     */
    @ApiModelProperty("事故类型")
    private List<String> accidentTypeCodes;

    private String accidentTypeJoins;

    private Integer expectWaitDay;

    @ApiModelProperty("出险人")
    private String accidentName;

    @ApiModelProperty("出险人与被保人关系")
    private String accidentRelationToInsured;


    public String getAccidentTypeJoins() {
        String accidentTypeJoins = "";
        if (getAccidentTypeCodes() != null) {
            accidentTypeJoins = CommonUtil.strJoin(",", getAccidentTypeCodes());
        }
        return accidentTypeJoins;
    }



    public static void main(String[] args) {
        WxClaimDTO wxClaimDTO = new WxClaimDTO();
        wxClaimDTO.setId(14711);
        wxClaimDTO.setOpenId("osUxv1ifzPU_3FYgJGRDM4q55c1I");
        wxClaimDTO.setToken("");
        wxClaimDTO.setAuthorization("eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiIxMzY1MCIsImVtcGxveWVlSWQiOiIxNzIzOCIsImhyVXNlcklkIjoiMTAwMDAwNTMxMSIsImFjY291bnQiOiIxMzI3MjA0NTYyMyIsInVzZXJOYW1lIjoi5YiY5aWH6ZqGIiwiam9iTnVtYmVyIjoiWkhOWDMyOTIzIiwibWFzdGVySm9iTnVtYmVyIjoiWkhOWDMyOTIzIiwib3JnSWQiOiI5NjEiLCJock9yZ0lkIjoiOTAwMTA1MjIyIiwiaHJPcmdDb2RlIjoiQlhEU1oiLCJock9yZ05hbWUiOiLkv53pmankuJrliqHlvIDlj5Hnu4QiLCJock9yZ1RyZWVQYXRoIjoiOTAwMTA1MTUzLzI4MjcxNy8zNjAzMTcvNDQ3OTQxLzgwNzM1LzkwMDEwNTIyMiIsInVzZXJUeXBlIjoiMyIsInJhbmRvbUNvZGUiOiIxRjcyMjc0Ni01OTdDLTQ4RUMtQTNGQi1CNjFCMThDREI2RkYiLCJ1c2VTc28iOiJmYWxzZSIsInN5c3RlbUlkIjoiMyIsImNyZWF0ZVRpbWUiOiIxNjM2NDQyNDgyNjk4IiwiZ3JheSI6MH0.sQNoANAczGsymQyHU-fS91bdxy81EJxXXn-SSmlzrMhpZMsn2ld1g4u7l8iAnB3jGAq5n1NYoAn4faTilnJ4jg");
        wxClaimDTO.setRiskType("2");
        wxClaimDTO.setRiskTime("2021-10-29 18:05:00");
        wxClaimDTO.setRiskDesc("ceshiupdate");

        wxClaimDTO.setSettlement("");
        wxClaimDTO.setEstimatedAmount(new BigDecimal("1251.0"));
        wxClaimDTO.setMedicalInsur("0");
        wxClaimDTO.setOtherInsur("0");

        wxClaimDTO.setClaimState("stepDataPrepare");
        wxClaimDTO.setClaimRiskReason("123123");
        wxClaimDTO.setRiskReasonAccidentType(1);

        System.out.println(JSON.toJSONString(wxClaimDTO));
    }
}
