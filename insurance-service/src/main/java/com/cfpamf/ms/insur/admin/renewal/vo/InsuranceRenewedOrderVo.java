package com.cfpamf.ms.insur.admin.renewal.vo;

import com.cfpamf.ms.insur.base.annotation.ExportField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * 已续保订单视图对象
 *
 * <AUTHOR>
 * @date 2021/5/13 16:13
 */
@Getter
@Setter
public class InsuranceRenewedOrderVo extends BaseOrderVo {

    @ApiModelProperty("续购产品名称")
    private String renewalProductName;
    @ApiModelProperty("续购计划名称")
    private String renewalPlanName;

    @ApiModelProperty("续购保单")
    private String renewalPolicyNo;

    @ApiModelProperty("续购类型 renewal/续保  change/转保")
    private String renewalType;

    @ApiModelProperty("起保时间")
    private LocalDateTime  renewalStartTime;

    @ApiModelProperty("续保平台")
    private String renewalPlatform;

    @ApiModelProperty(value = "是否自保件 0 否 1 是")
    private String selfInsured;

    @ApiModelProperty(value = "自保件")
    private String selfInsuredName;

    @ApiModelProperty(value = "是否异业客户")
    private String judgeCustomerLoan;


    @ApiModelProperty("投保人证件号")
    private String applicantIdNumber;

    @ApiModelProperty("续保可能性，高、中、低")
    private String renewalIntentionEval;
    @ApiModelProperty("主营、非主营")
    private String loanFlag;
}
