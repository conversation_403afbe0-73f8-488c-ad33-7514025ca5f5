package com.cfpamf.ms.insur.weixin.util.xjgsc;

/**
 * <AUTHOR>
 * @Date 2023/2/10 10:43
 * @Version 1.0
 */

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.cfpamf.ms.insur.base.exception.BizException;
import jodd.util.Base64;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import sun.misc.BASE64Decoder;
import sun.misc.BASE64Encoder;

import javax.crypto.Cipher;
import java.io.ByteArrayOutputStream;
import java.security.*;
import java.security.interfaces.RSAPrivateKey;
import java.security.interfaces.RSAPublicKey;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.HashMap;
import java.util.Map;
import java.util.TreeMap;

@Slf4j
public class WhaleGscRsaUtils {

    public static final String KEY_ALGORITHM = "RSA";
    private static final String PUBLIC_KEY = "RSAPublicKey";
    private static final String PRIVATE_KEY = "RSAPrivateKey";
    public static final String SIGNATURE_ALGORITHM = "MD5withRSA";
    /**
     * RSA最大加密明文大小
     */
    private static final int MAX_ENCRYPT_BLOCK = 117;

    /**
     * RSA最大解密密文大小
     */
    private static final int MAX_DECRYPT_BLOCK = 128;

    //获得公钥字符串
    public static String getPublicKeyStr(Map<String, Object> keyMap) throws Exception {
        //获得map中的公钥对象 转为key对象
        Key key = (Key) keyMap.get(PUBLIC_KEY);
        //编码返回字符串
        return encryptBASE64(key.getEncoded());
    }


    //获得私钥字符串
    public static String getPrivateKeyStr(Map<String, Object> keyMap) throws Exception {
        //获得map中的私钥对象 转为key对象
        Key key = (Key) keyMap.get(PRIVATE_KEY);
        //编码返回字符串
        return encryptBASE64(key.getEncoded());
    }

    //获取公钥
    public static PublicKey getPublicKey(String key) throws Exception {
        byte[] keyBytes;
        keyBytes = (new BASE64Decoder()).decodeBuffer(key);
        X509EncodedKeySpec keySpec = new X509EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance(KEY_ALGORITHM);
        PublicKey publicKey = keyFactory.generatePublic(keySpec);
        return publicKey;
    }

    //获取私钥
    public static PrivateKey getPrivateKey(String key) throws Exception {
        byte[] keyBytes;
        keyBytes = (new BASE64Decoder()).decodeBuffer(key);
        PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance(KEY_ALGORITHM);
        PrivateKey privateKey = keyFactory.generatePrivate(keySpec);
        return privateKey;
    }

    //解码返回byte
    public static byte[] decryptBASE64(String key) throws Exception {
        return (new BASE64Decoder()).decodeBuffer(key);
    }


    //编码返回字符串
    public static String encryptBASE64(byte[] key) throws Exception {
        return (new BASE64Encoder()).encodeBuffer(key);
    }

    public static Map<String, Object> initKey() throws Exception {
        //获得对象 KeyPairGenerator 参数 RSA 1024个字节
        KeyPairGenerator keyPairGen = KeyPairGenerator.getInstance(KEY_ALGORITHM);
        keyPairGen.initialize(1024);
        //通过对象 KeyPairGenerator 获取对象KeyPair
        KeyPair keyPair = keyPairGen.generateKeyPair();
        //通过对象 KeyPair 获取RSA公私钥对象RSAPublicKey RSAPrivateKey
        RSAPublicKey publicKey = (RSAPublicKey) keyPair.getPublic();
        RSAPrivateKey privateKey = (RSAPrivateKey) keyPair.getPrivate();
        //公私钥对象存入map中`
        Map<String, Object> keyMap = new HashMap<String, Object>(2);
        keyMap.put(PUBLIC_KEY, publicKey);
        keyMap.put(PRIVATE_KEY, privateKey);
        return keyMap;
    }

    //***************************签名和验证*******************************
    public static byte[] sign(String data, String privateKeyStr) throws Exception {
        PrivateKey priK = getPrivateKey(privateKeyStr);
        Signature sig = Signature.getInstance(SIGNATURE_ALGORITHM);
        sig.initSign(priK);
        sig.update(decryptBASE64(data));
        return sig.sign();
    }

    //***************************验证*******************************
    public static boolean verify(String data, String sign, String publicKeyStr) throws Exception {
        PublicKey pubK = getPublicKey(publicKeyStr);
        Signature sig = Signature.getInstance(SIGNATURE_ALGORITHM);
        sig.initVerify(pubK);
        sig.update(decryptBASE64(data));
        return sig.verify(decryptBASE64(sign));
    }

    //************************加密**************************
    public static byte[] encrypt(String plainText, String publicKeyStr) throws Exception {
        PublicKey publicKey = getPublicKey(publicKeyStr);
        Cipher cipher = Cipher.getInstance(KEY_ALGORITHM);
        cipher.init(Cipher.ENCRYPT_MODE, publicKey);
        byte[] bytes = decryptBASE64(plainText);
        int inputLen = bytes.length;
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        int offSet = 0;
        int i = 0;
        byte[] cache;
        while (inputLen - offSet > 0) {
            if (inputLen - offSet > MAX_ENCRYPT_BLOCK) {
                cache = cipher.doFinal(bytes, offSet, MAX_ENCRYPT_BLOCK);
            } else {
                cache = cipher.doFinal(bytes, offSet, inputLen - offSet);
            }
            out.write(cache, 0, cache.length);
            i++;
            offSet = i * MAX_ENCRYPT_BLOCK;
        }
        byte[] encryptText = out.toByteArray();
        out.close();
        return encryptText;
    }

    //************************解密**************************
    public static byte[] decrypt(String encryptText, String privateKeyStr) throws Exception {
        PrivateKey privateKey = getPrivateKey(privateKeyStr);
        Cipher cipher = Cipher.getInstance(KEY_ALGORITHM);
        cipher.init(Cipher.DECRYPT_MODE, privateKey);
        byte[] bytes = decryptBASE64(encryptText);
        int inputLen = bytes.length;
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        int offSet = 0;
        byte[] cache;
        int i = 0;
        // 对数据分段解密
        while (inputLen - offSet > 0) {
            if (inputLen - offSet > MAX_DECRYPT_BLOCK) {
                cache = cipher.doFinal(bytes, offSet, MAX_DECRYPT_BLOCK);
            } else {
                cache = cipher.doFinal(bytes, offSet, inputLen - offSet);
            }
            out.write(cache, 0, cache.length);
            i++;
            offSet = i * MAX_DECRYPT_BLOCK;
        }
        byte[] plainText = out.toByteArray();
        out.close();
        return plainText;
    }

    /**
     * 加密和签名处理方法,
     * @param request
     * @param publicKey
     * @param privateKey
     * @return
     * @throws Exception
     */
    public static String enCodeAndSign(String request,String publicKey,String privateKey) throws Exception {
        JSONObject requestJson = JSON.parseObject(request);
        byte[] sign = sign(encryptBASE64(getSortedByte(request)),privateKey);
        String signStr = encryptBASE64(sign);
        //把签名信息写入到原始报文中
        requestJson.put("sign",signStr);
        byte[] encode = encrypt(encryptBASE64(requestJson.toJSONString().getBytes()), publicKey);
        //加密包含签名的报文
        String encodeStr = encryptBASE64(encode);
        return encodeStr;
    }

    public static void main(String[] args) {
        try{
            String request = "{\"policyNo\":\"6687012023320197000001\",\"times\":\"#times#\",\"idCard\":\"110101198001016332\"}";
            request = request.replaceAll("#times#",System.currentTimeMillis()+"");
            System.out.println("原始报文 \t"+request);
            String publicKey = Base64.decodeToString("TUlHZk1BMEdDU3FHU0liM0RRRUJBUVVBQTRHTkFEQ0JpUUtCZ1FDZkVVUG4ra00rRFIwWm9ZQ1ZSWHBJbmMxaWtSbE01cjNBUnEzbQ0KRXNMVHh1cFVGMzVYUSs5eUgwRTF4N3R4dUJYODFJeDFyVVZaZjlsYXpHeWRhRjh5VFYxN3ZpMldGOFRyRUVLWG1MUUx4cUYyZy9Xdg0KNzZadUw2ZGx5bVp6Rlp1TnBMQWtCSURTYjlvdmZZUkhmSXJoOHh1WmdTZWhsUzF0OTMvcStlVFdvUUlEQVFBQg0K");
            String privateKey = Base64.decodeToString("TUlJQ2VBSUJBREFOQmdrcWhraUc5dzBCQVFFRkFBU0NBbUl3Z2dKZUFnRUFBb0dCQUo4UlErZjZRejROSFJtaGdKVkZla2lkeldLUg0KR1V6bXZjQkdyZVlTd3RQRzZsUVhmbGRENzNJZlFUWEh1M0c0RmZ6VWpIV3RSVmwvMlZyTWJKMW9YekpOWFh1K0xaWVh4T3NRUXBlWQ0KdEF2R29YYUQ5YS92cG00dnAyWEtabk1WbTQya3NDUUVnTkp2Mmk5OWhFZDhpdUh6RzVtQko2R1ZMVzMzZityNTVOYWhBZ01CQUFFQw0KZ1lFQWtMWCtWejdnaWNVWVlrY0JXY3U1Y2ZMTjlRd1lQbFAwOE1YY1V1RmlpWlAvZnIxWTNUOGF5WElSdTBHWUQ5N3ZjZkpVVG9KeQ0KQXZoQzMwMzJ5a1UzeEhHM2EwaFlBRmd2b3A5MGFWQzRLWnd6SENXNWdNM1Rwb1Z4Vnh2SVA5U1BNcnlrdVJTNFdXTzN0eGdwTm1VMg0KMUUrU014ejZJZFgrZisxQ29oaWREUmtDUVFEc2t5cEFRS1p1ajJrKzdLQS9SMjJSWm5abExqQnUyd1I1Vi9uMjRPOFVsc3FJMnI3eQ0KWHpvVDFJK1FDOXpBc0FFTXFnMWc5WUlBM0V2ai9nL1pUZ2tQQWtFQXJDRGlTMUttd0xybW5NSlQ1bHJibHVBR0VWZHBvQTBITE54QQ0KdERTMzE4VE1JRTlZbk9PeVN4RE5WSFZKNlNwbWMwSVN0aWxSVjg3MDMzdHZjYkZGVHdKQkFLRjgwNXFJaHhOeFVhWmlZQURTQVRqLw0KZUZJWTlYYTJpcGRLTnBjUGRDUFp5dWNnYXJlSHlWMC8rNytZV1ZndXpJaVlqQWxqalJxSlJrU2JhTFl0VG9FQ1FHWldGclNacXpLaQ0KN0VEKytFclljZjI5cTEzY0RweHRvbnAxYUxLN05TSS9pdTBhdjZCbmI3VlpnL3BXMFY2aktzNFQxblFmRkpHVUpkcEkxUFZQWTlrQw0KUVFEQm1aMHFiRkFBS1dSSHkxY1pwd09Dc0NGRWRiT1IyOW1DZGxpd0R3OVY1dnVnYi9LRFB4d002Yldybk1mYlVxM2d6SmlSN3diRg0KMktwZjdub3dRMEgvDQo=");
            String encodeRequest = enCodeAndSign(request, publicKey, privateKey);
            System.out.println("encodeRequest= \t"+encodeRequest);
            System.out.println("解密后的数据:\t"+new String(decrypt(encodeRequest, privateKey)));
//            System.out.println("url= \t"+JdAlHttpsUtils.comonInterface("http://localhost:8087/feign/gsc/getDownloadUrl", encodeRequest));
        }catch(Exception e){
            e.printStackTrace();
        }

    }

//    public static void main(String[] args){
//        try{
//            String test = "{\"policyNo\":\"6687012022320197000257\",\"times\":\"6687012023320197000002\",\"fhProductId\":\"PROG1489|PROG1489-DUIJIE-001|********\",\"idCard\":\"******************\"}";
//            JSONObject testjs = JSON.parseObject(test);
//            System.out.println("原始报文 \t"+test);
//            String publicKey = Base64.decodeToString("TUlHZk1BMEdDU3FHU0liM0RRRUJBUVVBQTRHTkFEQ0JpUUtCZ1FDZkVVUG4ra00rRFIwWm9ZQ1ZSWHBJbmMxaWtSbE01cjNBUnEzbQ0KRXNMVHh1cFVGMzVYUSs5eUgwRTF4N3R4dUJYODFJeDFyVVZaZjlsYXpHeWRhRjh5VFYxN3ZpMldGOFRyRUVLWG1MUUx4cUYyZy9Xdg0KNzZadUw2ZGx5bVp6Rlp1TnBMQWtCSURTYjlvdmZZUkhmSXJoOHh1WmdTZWhsUzF0OTMvcStlVFdvUUlEQVFBQg0K");
//            String privateKey = Base64.decodeToString("TUlJQ2VBSUJBREFOQmdrcWhraUc5dzBCQVFFRkFBU0NBbUl3Z2dKZUFnRUFBb0dCQUo4UlErZjZRejROSFJtaGdKVkZla2lkeldLUg0KR1V6bXZjQkdyZVlTd3RQRzZsUVhmbGRENzNJZlFUWEh1M0c0RmZ6VWpIV3RSVmwvMlZyTWJKMW9YekpOWFh1K0xaWVh4T3NRUXBlWQ0KdEF2R29YYUQ5YS92cG00dnAyWEtabk1WbTQya3NDUUVnTkp2Mmk5OWhFZDhpdUh6RzVtQko2R1ZMVzMzZityNTVOYWhBZ01CQUFFQw0KZ1lFQWtMWCtWejdnaWNVWVlrY0JXY3U1Y2ZMTjlRd1lQbFAwOE1YY1V1RmlpWlAvZnIxWTNUOGF5WElSdTBHWUQ5N3ZjZkpVVG9KeQ0KQXZoQzMwMzJ5a1UzeEhHM2EwaFlBRmd2b3A5MGFWQzRLWnd6SENXNWdNM1Rwb1Z4Vnh2SVA5U1BNcnlrdVJTNFdXTzN0eGdwTm1VMg0KMUUrU014ejZJZFgrZisxQ29oaWREUmtDUVFEc2t5cEFRS1p1ajJrKzdLQS9SMjJSWm5abExqQnUyd1I1Vi9uMjRPOFVsc3FJMnI3eQ0KWHpvVDFJK1FDOXpBc0FFTXFnMWc5WUlBM0V2ai9nL1pUZ2tQQWtFQXJDRGlTMUttd0xybW5NSlQ1bHJibHVBR0VWZHBvQTBITE54QQ0KdERTMzE4VE1JRTlZbk9PeVN4RE5WSFZKNlNwbWMwSVN0aWxSVjg3MDMzdHZjYkZGVHdKQkFLRjgwNXFJaHhOeFVhWmlZQURTQVRqLw0KZUZJWTlYYTJpcGRLTnBjUGRDUFp5dWNnYXJlSHlWMC8rNytZV1ZndXpJaVlqQWxqalJxSlJrU2JhTFl0VG9FQ1FHWldGclNacXpLaQ0KN0VEKytFclljZjI5cTEzY0RweHRvbnAxYUxLN05TSS9pdTBhdjZCbmI3VlpnL3BXMFY2aktzNFQxblFmRkpHVUpkcEkxUFZQWTlrQw0KUVFEQm1aMHFiRkFBS1dSSHkxY1pwd09Dc0NGRWRiT1IyOW1DZGxpd0R3OVY1dnVnYi9LRFB4d002Yldybk1mYlVxM2d6SmlSN3diRg0KMktwZjdub3dRMEgvDQo=");
//            //先签名原始报文
//            byte[] sign = sign(encryptBASE64(test.getBytes()),privateKey);
//            String signStr = encryptBASE64(sign);
//            //把签名信息写入到原始报文中
//            testjs.put("sign",signStr);
//            byte[] miwen = encrypt(encryptBASE64(testjs.toJSONString().getBytes()), publicKey);
//            //加密包含签名的报文
//            String miwenStr = encryptBASE64(miwen);
//            byte[] mingwen = decrypt(miwenStr,privateKey);
//            System.out.println("解密后的报文 \t"+new String(mingwen));
//            System.out.println("signStr\t" + signStr);
//            System.out.println("验签结果 \t"+verify(encryptBASE64(test.getBytes()), signStr, publicKey));
//            System.out.println(System.currentTimeMillis());
//        JdAlHttpsUtils.comonInterface("http://localhost:8087/feign/gsc/getDownloadUrl",miwenStr);
//        }catch(Exception e){
//            e.printStackTrace();
//        }
//
//    }

    /**
     * 对属性字典排序输出key:value形式的json
     *
     * @param obj 要排序的对象
     * @return json串
     * @throws Exception e
     */
    private static byte[] getSortedByte(Object obj) throws Exception {
        Map<String, Object> map = null;
        if(obj.getClass() == String.class){
            map = JSON.parseObject((String)obj, new TypeReference<Map<String, Object>>() {
            });
        }else{
            map = JSON.parseObject(JSON.toJSONString(obj), new TypeReference<Map<String, Object>>() {
            });
        }
        TreeMap<String, Object> treeMap = new TreeMap<>(map);
        return JSON.toJSONString(treeMap).getBytes();
    }

    /**
     *
     * @param response
     * @param publicKey
     * @param privateKey
     * @return
     * @throws Exception
     */
    public static String decryptAndCheckSign(String response,String publicKey,String privateKey) throws Exception {
        String decode = new String(decrypt(response,privateKey));

        if(StringUtils.isBlank(decode)){
            log.warn("保险中台RSA解密后数据为空 decode= {}",decode);
            throw new BizException("-1","保险中台RSA解密后数据为空");
        }
        JSONObject json = JSON.parseObject(decode);
        String sign = json.getString("sign");
        if(StringUtils.isBlank(sign)){
            log.warn("保险中台RSA签名信息不存在 sign= {}",sign);
            throw new BizException("-1","保险中台RSA签名信息不存在");
        }
        json.remove("sign");
        //原始报文
        String body = json.toJSONString();
        if(verify(encryptBASE64(getSortedByte(body)), sign, publicKey)){
            return body;
        }
        throw new BizException("-1","保险中台RSA验签失败");
    }

}
