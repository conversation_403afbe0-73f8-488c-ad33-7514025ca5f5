package com.cfpamf.ms.insur.weixin.pojo.query;

import com.cfpamf.ms.insur.base.bean.Pageable;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

/**
 * 微信用户查询Query
 *
 * <AUTHOR>
 **/
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class WxCustomerQuery extends Pageable {

    /**
     * 用户Id
     */
    @ApiModelProperty(value = "用户Id", hidden = true)
    private String userId;

    /**
     * 微信openId
     */
    @ApiModelProperty(value = "微信openId")
    private String openId;

    /**
     * 微信token
     */
    @ApiModelProperty(value = "微信token")
    private String authorization;

    /**
     * 被保人/保单号/投保人姓名
     */
    @ApiModelProperty(value = "被保人/保单号/投保人姓名")
    private String keyword;

    /**
     * 出单状态
     */
    @ApiModelProperty(value = "出单状态")
    private String state;

    /**
     * 用户种类
     */
    @ApiModelProperty(value = "用户种类")
    private String userType;

    /**
     * 身份证列表
     */
    @ApiModelProperty(hidden = true)
    private List<String> idNos;
}
