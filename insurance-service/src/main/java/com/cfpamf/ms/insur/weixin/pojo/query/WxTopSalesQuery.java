package com.cfpamf.ms.insur.weixin.pojo.query;

import com.cfpamf.ms.insur.base.bean.Pageable;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.Date;

/**
 * 微信推广精英排行版Query
 **/
@Data
@ApiModel
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class WxTopSalesQuery extends Pageable {

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;
}
