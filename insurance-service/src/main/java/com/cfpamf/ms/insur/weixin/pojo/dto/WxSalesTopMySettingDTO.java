package com.cfpamf.ms.insur.weixin.pojo.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * @description: 微信用户配置DTO
 * @author: z<PERSON><PERSON><PERSON>
 * @create: 2018-07-24 10:30
 **/
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class WxSalesTopMySettingDTO extends WxAuthorizationDTO {

    /**
     * 显示（true）不显示（false）
     */
    @ApiModelProperty("显示（true）不显示（false）")
    private Boolean showHomeSalesTop;
}
