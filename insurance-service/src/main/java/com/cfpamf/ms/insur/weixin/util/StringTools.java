package com.cfpamf.ms.insur.weixin.util;

import cn.hutool.core.util.StrUtil;
import com.cfpamf.ms.insur.admin.util.LogUtil;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class StringTools {

    /**
     * 正则：身份证号码 15 位
     */
    public static final String REGEX_ID_CARD15 = "^[1-9]\\d{7}((0\\d)|(1[0-2]))(([0|1|2]\\d)|3[0-1])\\d{3}$";
    /**
     * 正则：身份证号码 18 位
     */
    public static final String REGEX_ID_CARD18 = "^[1-9]\\d{5}[1-9]\\d{3}((0\\d)|(1[0-2]))(([0|1|2]\\d)|3[0-1])\\d{3}([0-9Xx])$";

    private static SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");

    private static String NUMBER_REG = "^\\d+(.\\d*)?$";


    private static String MOBILE_REG = "^((11[0-9])|(12[0-9])|(13[0-9])|(14[0-9])|(15[0-9])|(16[0-9])|(17[0-9])|(18[0-9])|(19[0-9]))\\d{8}$";

    private static Pattern pattern = Pattern.compile(MOBILE_REG);
    public static boolean isNumber(String param) {
        return param != null && param.matches(NUMBER_REG);
    }

    public static String parseGener(String idNumber) {
        if (StringUtils.isBlank(idNumber)) {
            return null;
        }
        String sexNumber = "0";
        if (idNumber.length() == 15) {
            sexNumber = idNumber.substring(14, 15);
        } else if (idNumber.length() == 18) {
            sexNumber = idNumber.substring(16, 17);
        }
        int sexNo = Integer.parseInt(sexNumber);
        return sexNo % 2 == 0 ? "女" : "男";
    }

    public static boolean validMobile(String mobile) {
        if (StringUtils.isBlank(mobile)) {
            return false;
        }
        return pattern.matcher(mobile).matches();
    }


    public static Integer parseAge(String IDCard) {
        Integer age = 0;
        LocalDate localDate = LocalDate.now();
        int year = localDate.getYear();
        int month = localDate.getMonthValue();
        int day = localDate.getDayOfMonth();

        String birthYear = "";
        String birthMonth = "";
        String birthDay = "";
        if (IDCard.length() == 15) {
            birthYear = "19" + IDCard.substring(6, 8);
            birthMonth = IDCard.substring(8, 10);
            birthDay = IDCard.substring(10, 12);

        } else if (IDCard.length() == 18) {
            birthYear = IDCard.substring(6).substring(0, 4);
            birthMonth = IDCard.substring(10).substring(0, 2);
            birthDay = IDCard.substring(12).substring(0, 2);
        }
        Integer birthY = Integer.parseInt(birthYear);
        Integer birthM = Integer.parseInt(birthMonth);
        Integer birthD = Integer.parseInt(birthDay);
        if (birthM < month) {
            age = year - birthY;
        }
        if (birthM > month) {
            age = year - birthY - 1;
        }
        if (birthM == month) {
            if (birthD > day) {
                age = year - birthY - 1;
            } else {
                age = year - birthY;
            }
        }
        return age;
    }

    public static void main(String[] args) {
        String tel = "2024-01";
        System.out.println(StrUtil.replace(tel,"-",""));
    }

    public static boolean validIdCard(String idNumber) {
        if (StringUtils.isBlank(idNumber)) {
            return false;
        }
        int length = idNumber.length();
        if (length == 15) {
            if (!idNumber.matches(REGEX_ID_CARD15)) {
                return false;
            }
        }

        if (length == 18) {
            if (!idNumber.matches(REGEX_ID_CARD18)) {
                return false;
            }
        }

        if (birthday(idNumber) == null) {
            return false;
        }
        return true;
    }


    public static String birthday(String idCardNum) {
        String birthday = null;

        if (idCardNum.length() == 15 && isIDCard15(idCardNum)) {
            birthday = "19" + idCardNum.substring(6, 8) + idCardNum.substring(8, 10) + idCardNum.substring(10, 12);
        } else if (idCardNum.length() == 18 && isIDCard18(idCardNum)) {
            birthday = idCardNum.substring(6, 10) + idCardNum.substring(10, 12) + idCardNum.substring(12, 14);
        } else {
            return null;
        }
        SimpleDateFormat sd = new SimpleDateFormat("yyyyMMdd");
        try {
            sd.parse(birthday);
        } catch (Exception e) {
            LogUtil.warn("身份证解析出生日期失败：{}", idCardNum);
            return null;
        }
        return birthday;
    }

    public static String catNativeId(String orderId) {
        String correctReg = "^(\\S+)_(\\d+)$";
        Pattern pattern = Pattern.compile(correctReg);
        Matcher matcher = pattern.matcher(orderId);
        if (matcher.find()) {
            return matcher.group(1);
        }
        return orderId;
    }

    public static Integer catBatch(String orderId) {
        String correctReg = "^(\\S+)_(\\d+)$";
        Pattern pattern = Pattern.compile(correctReg);
        Matcher matcher = pattern.matcher(orderId);
        String batchNo = null;
        if (matcher.find()) {
            batchNo = matcher.group(2);
            if (StringUtils.isNotBlank(batchNo)) {
                return Integer.parseInt(batchNo);
            }
        }
        return 0;
    }

    private static boolean isIDCard15(String idCardNum) {
        return idCardNum.length() == 15;
    }

    private static boolean isIDCard18(String idCardNum) {
        return idCardNum.length() == 18;
    }


    public static BigDecimal convertNumber(String number) {
        if (StringUtils.isBlank(number)) {
            return null;
        }
        String reg = "[0-9]+(.[0-9]+)?";
        if (number.matches(reg)) {
            return new BigDecimal(number);
        }
        return null;
    }


}
