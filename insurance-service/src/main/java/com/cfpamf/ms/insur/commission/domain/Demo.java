package com.cfpamf.ms.insur.commission.domain;

import lombok.Data;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 应用模块名称<p>
 * <p>
 * 代码描述<p>
 * <p>
 * Copyright: Copyright (C) 2021 CD Finance Management Co., Ltd. All rights reserved. <p>
 * <p>
 * Company: 中和农信项目管理有限公司<p>
 *
 * <AUTHOR>
 * @since 2021/9/9 10:41 AM
 */
@Data
@ToString
@Accessors(chain = true)
public class Demo {

	private Integer id;

	private String apiUrl;

	private Integer isEnabled;

	private String type;

	private String env;

	private String routeId;

}
