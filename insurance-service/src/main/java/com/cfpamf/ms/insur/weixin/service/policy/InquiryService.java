package com.cfpamf.ms.insur.weixin.service.policy;

import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.ms.insur.admin.constant.EnumProductAttr;
import com.cfpamf.ms.insur.admin.dao.safes.product.ProductMapper;
import com.cfpamf.ms.insur.admin.enums.product.EnumPremiumFlow;
import com.cfpamf.ms.insur.admin.pojo.dto.SmProductCoverageDiscountDTO;
import com.cfpamf.ms.insur.admin.pojo.dto.product.PremiumFlow;
import com.cfpamf.ms.insur.admin.pojo.vo.SmProductQuoteLimitItemVO;
import com.cfpamf.ms.insur.base.exception.ExcptEnum;
import com.cfpamf.ms.insur.base.util.LambdaUtils;
import com.cfpamf.ms.insur.weixin.dao.safes.PolicyMapper;
import com.cfpamf.ms.insur.weixin.pojo.dto.WxGlProductQuoteDTO;
import com.cfpamf.ms.insur.weixin.pojo.dto.product.PremiumDTO;
import com.cfpamf.ms.insur.weixin.pojo.dto.product.ProductAttrDTO;
import com.cfpamf.ms.insur.weixin.pojo.dto.product.QuoteLimitDTO;
import com.cfpamf.ms.insur.weixin.pojo.dto.product.TimeFactorItem;
import com.cfpamf.ms.insur.weixin.pojo.query.policy.CoverageQuery;
import com.cfpamf.ms.insur.weixin.pojo.query.policy.DutyFactorFlowQuery;
import com.cfpamf.ms.insur.weixin.pojo.query.policy.PlanQuery;
import com.cfpamf.ms.insur.weixin.pojo.vo.apply.common.GroupInquiry;
import com.cfpamf.ms.insur.weixin.pojo.vo.apply.common.GroupInquiryResponse;
import com.cfpamf.ms.insur.weixin.pojo.vo.product.CoverageAmountVo;
import com.cfpamf.ms.insur.weixin.service.context.RFQHolder;
import com.cfpamf.ms.insur.weixin.service.context.RateFacor;
import com.cfpamf.ms.insur.weixin.service.underwriting.GroupApplyHandler;
import com.cfpamf.ms.insur.weixin.util.StringTools;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

/**
 * 询价逻辑
 */
@Slf4j
@Service
public class InquiryService {

    @Autowired
    private PolicyMapper policyMapper;

    @Autowired
    private ProductMapper productMapper;

    @Autowired
    private GroupApplyHandler applyHandler;

    private final BigDecimal HUNDRED = new BigDecimal(100);

    public GroupInquiryResponse inquery(GroupInquiry req) {
        return tryPermium(req);
    }

    /**
     * 计算规则：
     * 1、保障项目的保费=（选择的计划的保额&职业类别对应的保费）*责任调整因子*（1+企业风险调整系数）*(短期费率因子);
     * 2、计划保费=∑保障项目的保费
     * 3、整单保费=计划保费*人数折扣
     *
     * @param req
     * @return
     */
    public GroupInquiryResponse tryPermium(GroupInquiry req) {

        PlanQuery plan = req.getPlan();
        if (plan == null) {
            throw new MSBizNormalException("-1", "产品计划信息不能为空");
        }
        Integer productId = req.getProductId();
        Integer planId = plan.getId();

        List<PremiumDTO> premiumTable = policyMapper.queryPremiumByPlan(productId, planId);
        if (premiumTable.size() == 0) {
            throw new MSBizNormalException("-1", "产品费率表为空");
        }

        List<CoverageQuery> coverages = plan.getCoverages();
        if (CollectionUtils.isEmpty(coverages)) {
            throw new MSBizNormalException("-1", "责任列表不能为空");
        }

        List<WxGlProductQuoteDTO.OccupationPerson> personList = req.getPersonList();
        Map<String, List<PremiumDTO>> premiumTableMap = LambdaUtils.groupBy(premiumTable, PremiumDTO::getOccupationGroup);
        List<QuoteLimitDTO> limits = policyMapper.queryQuoteLimit(productId);

        GroupInquiryResponse resp = new GroupInquiryResponse();
        List<GroupInquiryResponse.InquiryItem> inquiryItems = new ArrayList<>();
        BigDecimal totalPremium = BigDecimal.ZERO;

        /**
         * 构建报价上下文
         */
        RFQHolder holder = buildRFQHolder(productId, planId);
        TimeFactorItem timeFactor = req.getTimeFactor();
        if (timeFactor != null) {
            RateFacor facor = new RateFacor(timeFactor.getFactorValue(), timeFactor.getFactorName(), timeFactor.getFlow());
            holder.setTimeFactor(facor);
        }
        Integer totalPerson = personList.stream().map(WxGlProductQuoteDTO.OccupationPerson::getPerQty).reduce(0,(a,b)->{return a+b;}).intValue();


        /**
         * 最后折算人数折扣
         */
        BigDecimal discount = matchDiscount(productId, totalPerson);
//        totalPremium = tryPermiumByDiscount(totalPremium, discount);
        holder.setDiscount(discount);

        for (WxGlProductQuoteDTO.OccupationPerson entry : personList) {
            List<PremiumDTO> premiumDTOS = premiumTableMap.get(entry.getOccupationGroup());
            Map<Integer, BigDecimal> premiumMap = LambdaUtils.safeToMap(premiumDTOS, PremiumDTO::getSpcaId, PremiumDTO::getPremium);
            /**
             * 判断投保规则
             */
            if (!CollectionUtils.isEmpty(limits)) {
                applyCheck4Inquiry(limits, coverages, entry.getOccupationGroup(), entry.getPerQty());
            }
            /**
             * 试算单人保费
             */
            BigDecimal res = tryPermiumByOneV2(req, premiumMap, holder);
            /**
             * 单人保费-向上取整
             */
            res = res.setScale(0, RoundingMode.UP);
            /**
             * 计算当前节点的保费
             */
            GroupInquiryResponse.InquiryItem item = new GroupInquiryResponse.InquiryItem();
            item.setOccupationGroup(entry.getOccupationGroup());
            item.setPerQty(entry.getPerQty());
            item.setSinglePremium(res);

            BigDecimal itemPremium = res.multiply(new BigDecimal(entry.getPerQty()));
            totalPremium = totalPremium.add(itemPremium);
            item.setTotalPremium(itemPremium);
            inquiryItems.add(item);
        }
        resp.setDiscount(discount);
        resp.setInquiryItems(inquiryItems);
        resp.setTotalPremium(totalPremium);

        return resp;
    }

    /**
     * 按职业类别试算[单人]保费
     * ☆该折算方法不符合产品要求，已废弃不用！！！
     * @param req
     * @param premiumMap
     * @return
     */
//    @Deprecated
//    public BigDecimal tryPermiumByOne(GroupInquiry req,
//                                    Map<Integer,BigDecimal> premiumMap, RFQHolder holder){
//        List<CoverageQuery> coverages = req.getPlan().getCoverages();
//        List<ProductAttrDTO> attrs = holder.getAttrs();
//        Map<String,String> attrMap = LambdaUtils.safeToMap(attrs,ProductAttrDTO::getAttrCode,ProductAttrDTO::getAttrVal);
//        BigDecimal res = BigDecimal.ZERO;
//        /**
//         * Step1:折算责任因子&修正保费精度
//         */
//        for(CoverageQuery cq:coverages){
//            if(!cq.selfCheck()){
//                throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(),"请选择责任保额...");
//            }
//            CoverageAmountVo amounts = cq.getAmount();
//            Integer spcaId = amounts.getSpcaId();
//            BigDecimal premium = premiumMap.get(spcaId);
//            List<DutyFactorFlowQuery> flows = cq.getDutyFactor();
//
//            BigDecimal tempPremium = tryPremiumByDutyFactor(premium,flows);
//            res = res.add(tempPremium);
//        }
//        res = fixPremium(res,attrMap.get(EnumProductAttr.PREMIUM_DUTY_FACTOR_ACC.getCode()));
//
//        /**
//         * Step2:折算短期费率&修正保费精度
//         */
//        TimeFactorItem tf = req.getTimeFactor();
//        RateFacor facor = new RateFacor(tf.getFactorValue(),tf.getFactorName(),tf.getFlow());
//
//        res = tryPermiumByShortTerm(res,facor);
//        res = fixPremium(res,attrMap.get(EnumProductAttr.PREMIUM_SHORT_TERM_ACC.getCode()));
//
//        /**
//         * Step3:折算企业风险系数因子&修正保费精度
//         */
//        res = tryPermiumByEnterpriserRisk(res,holder.getEnterpriserRisk());
//        res = fixPremium(res,attrMap.get(EnumProductAttr.PREMIUM_ENTERPRISE_RISK_ACC.getCode()));
//
//        /**
//         * 可继续叠加其他因子的折算...
//         * 但最终计算出来的结果需向上取整
//         */
//        res=res.setScale(0,RoundingMode.UP);
//        return res;
//    }

    /**
     * 按职业类别试算[单人]保费
     * ★责任保费=责任费率*责任调整因子*（1+企业风险调整系数）*短期费率调整比例；
     * ★个人保费=∑责任保费 （向上取整）
     * ★整单保费 =∑ 个人保费*人数折扣比例
     * 需求文档：https://cfpamf.yuque.com/insur/zi6fu0/ehqo7d
     *
     * @param req
     * @param premiumMap
     * @return
     */
    public BigDecimal tryPermiumByOneV2(GroupInquiry req,
                                        Map<Integer, BigDecimal> premiumMap, RFQHolder holder) {
        List<CoverageQuery> coverages = req.getPlan().getCoverages();
        BigDecimal res = BigDecimal.ZERO;

        for (CoverageQuery cq : coverages) {
            if (!cq.selfCheck()) {
                throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(), "请选择责任保额");
            }
            CoverageAmountVo amounts = cq.getAmount();
            Integer spcaId = amounts.getSpcaId();
            BigDecimal premium = premiumMap.get(spcaId);

            log.info("开始计算责任保费：{},{},{}", cq.getRiskCode(), cq.getCvgCode(), premium);
            BigDecimal tempPremium = tryPermiumByOne4Coverage(premium, holder, cq);
            log.info("责任保费计算完成：{},{},{}", cq.getRiskCode(), cq.getCvgCode(), tempPremium);
            res = res.add(tempPremium);
        }
        return res;
    }

    /**
     * 按职业类别试算[单人]保费
     * ★责任保费=责任费率*责任调整因子*（1+企业风险调整系数）*短期费率调整比例；
     * ★个人保费=∑责任保费 （向上取整）
     * ★整单保费 =∑ 个人保费*人数折扣比例
     * 需求文档：https://cfpamf.yuque.com/insur/zi6fu0/ehqo7d
     *
     * @param req
     * @param premiumMap
     * @return
     */
    public BigDecimal tryPermiumByOneV3(GroupInquiry req,
                                        Map<Integer, BigDecimal> premiumMap, RFQHolder holder) {
        List<CoverageQuery> coverages = req.getPlan().getCoverages();
        BigDecimal res = BigDecimal.ZERO;

        for (CoverageQuery cq : coverages) {
            if (!cq.selfCheck()) {
                throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(), "请选择责任保额");
            }
            CoverageAmountVo amounts = cq.getAmount();
            Integer spcaId = amounts.getSpcaId();
            BigDecimal premium = premiumMap.get(spcaId);

            log.info("开始计算责任保费：{},{},{}", cq.getRiskCode(), cq.getCvgCode(), premium);
            BigDecimal tempPremium = tryPermiumByOne4Coverage(premium, holder, cq);
            log.info("责任保费计算完成：{},{},{}", cq.getRiskCode(), cq.getCvgCode(), tempPremium);
            res = res.add(tempPremium);
        }
        return res;
    }

    /**
     * 计算每一个责任的单人保费
     * ★责任保费=责任费率*责任调整因子*（1+企业风险调整系数）*短期费率调整比例；
     *
     * @return
     */
    private BigDecimal tryPermiumByOne4Coverage(BigDecimal premium, RFQHolder holder, CoverageQuery cq) {
        if (premium == null) {
            return BigDecimal.ZERO;
        }

        if (Objects.equals(premium, 0)) {
            return premium;
        }

        BigDecimal res = premium;
        if (!cq.selfCheck()) {
            throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(), "请选择责任保额...");
        }

        List<ProductAttrDTO> attrs = holder.getAttrs();
        Map<String, String> attrMap = LambdaUtils.safeToMap(attrs, ProductAttrDTO::getAttrCode, ProductAttrDTO::getAttrVal);

        /**
         * 保费-责任因子折算
         */
        List<DutyFactorFlowQuery> flows = cq.getDutyFactor();
        if (CollectionUtils.isNotEmpty(flows)) {
            res = tryPremiumByDutyFactor(premium, flows);
            res = fixPremium(res, attrMap.get(EnumProductAttr.PREMIUM_DUTY_FACTOR_ACC.getCode()));
        }
        log.info("责任因子保费试算结果：{},{},{}", cq.getRiskCode(), cq.getAmount(), res);
        /**
         * 企业风险系数因子折算
         */
        RateFacor enterpriserRisk = holder.getEnterpriserRisk();
        if (enterpriserRisk != null) {
            res = tryPermiumByEnterpriserRisk(res, enterpriserRisk);
            res = fixPremium(res, attrMap.get(EnumProductAttr.PREMIUM_ENTERPRISE_RISK_ACC.getCode()));
        }
        log.info("企业风险系数因子保费试算结果：{},{},{}", cq.getRiskCode(), cq.getAmount(), res);
        /**
         * 短期费率因子折算
         */
        RateFacor timeFactor = holder.getTimeFactor();
        if (timeFactor != null) {
            res = tryPermiumByShortTerm(res, timeFactor);
            res = fixPremium(res, attrMap.get(EnumProductAttr.PREMIUM_SHORT_TERM_ACC.getCode()));
        }
        log.info("短期费率因子保费试算结果：{},{}，{}", cq.getRiskCode(), cq.getAmount(), res);

        /**
         * 最后计算人数折扣
         */
        BigDecimal discount = holder.getDiscount();
        if (discount != null) {
            res = tryPermiumByDiscount(res, discount);
        }
        log.info("保费折扣计算结果：{},{},{}", cq.getRiskCode(), cq.getAmount(), res);
        return res;
    }

    /**
     * 询价书：按一年期计算责任保费
     * ★责任保费=责任费率*责任调整因子*(1+企业风险调整系数)
     *
     * @param premium
     * @param holder
     * @param cq
     * @return
     */
    public BigDecimal tryPermium4Coverage(BigDecimal premium, RFQHolder holder, CoverageQuery cq) {
        if (premium == null) {
            return BigDecimal.ZERO;
        }

        if (Objects.equals(premium, 0)) {
            return premium;
        }

        BigDecimal res = premium;
        if (!cq.selfCheck()) {
            throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(), "请选择责任保额");
        }
        List<ProductAttrDTO> attrs = holder.getAttrs();
        Map<String, String> attrMap = LambdaUtils.safeToMap(attrs, ProductAttrDTO::getAttrCode, ProductAttrDTO::getAttrVal);
        List<DutyFactorFlowQuery> flows = cq.getDutyFactor();

        /**
         * 保费-责任因子折算
         */
        if (CollectionUtils.isNotEmpty(flows)) {
            res = tryPremiumByDutyFactor(premium, flows);
            res = fixPremium(res, attrMap.get(EnumProductAttr.PREMIUM_DUTY_FACTOR_ACC.getCode()));
            log.info("责任因子保费计算结果：{},{}", premium, res);
        }

        /**
         * 企业风险系数因子折算
         */
        RateFacor rf = holder.getEnterpriserRisk();
        if (rf != null) {
            res = tryPermiumByEnterpriserRisk(res, rf);
            res = fixPremium(res, attrMap.get(EnumProductAttr.PREMIUM_ENTERPRISE_RISK_ACC.getCode()));
            log.info("企业风险系数保费计算结果：{},{}", premium, res);
        }
        return res;
    }

    /**
     * 按配置的小数位数修正保费精度,四舍五入
     *
     * @param res
     * @param attr
     * @return
     */
    private BigDecimal fixPremium(BigDecimal res, String attr) {
        if (!StringTools.isNumber(attr)) {
            return res;
        }
        Integer acc = Integer.valueOf(attr);
        return res.setScale(acc, RoundingMode.HALF_UP);
    }

    /**
     * 试算保费·折算短期费率折扣
     *
     * @param premium    原始保费
     * @param timeFactor 短期费率因子
     * @return
     */
    public BigDecimal tryPermiumByShortTerm(BigDecimal premium, RateFacor timeFactor) {
        if (timeFactor == null) {
            return premium;
        }
        String flow = timeFactor.getFlow();
        if (StringTools.isNumber(flow)) {
            BigDecimal factor = new BigDecimal(flow);
            premium = premium.multiply(factor);
            premium = premium.divide(HUNDRED);
        }
        return premium;
    }

    /**
     * 试算保费·折算企业风险系数
     *
     * @param rf
     * @param res
     * @return
     */
    public BigDecimal tryPermiumByEnterpriserRisk(BigDecimal res, RateFacor rf) {
        if (rf == null) {
            return res;
        }
        String flow1 = rf.getFlow();
        if (StringTools.isNumber(flow1)) {
            BigDecimal flow = new BigDecimal(flow1);

            flow = flow.divide(HUNDRED);
            flow = flow.add(new BigDecimal(1));
            res = res.multiply(flow);
        }
        return res;
    }

    /**
     * 构建上下文数据
     *
     * @param productId
     * @param planId
     * @return
     */
    public RFQHolder buildRFQHolder(Integer productId, Integer planId) {
        RFQHolder holder = new RFQHolder();
        holder.setProductId(productId);
        holder.setPlanId(planId);
        List<ProductAttrDTO> attrs = policyMapper.listProductAttr(productId);
        holder.setAttrs(attrs);

        List<PremiumFlow> premiumFlows = productMapper.queryPremiumFactor(productId, planId, EnumPremiumFlow.ENTERPRISE_RISK_FACTOR.name());
        if (CollectionUtils.isNotEmpty(premiumFlows)) {
            PremiumFlow flow = premiumFlows.get(0);
            RateFacor rf = new RateFacor();
            rf.setFactorName(flow.getFactorName());
            rf.setFactorValue(flow.getFactorValue());
            rf.setFlow(flow.getFlow());
            holder.setEnterpriserRisk(rf);
        }
        return holder;
    }

    /**
     * 折算责任因子
     *
     * @param rawPremium
     * @param flows
     * @return
     */
    public BigDecimal tryPremiumByDutyFactor(BigDecimal rawPremium, List<DutyFactorFlowQuery> flows) {
        BigDecimal premium = rawPremium != null ? rawPremium : BigDecimal.ZERO;
        if (premium != null && flows != null) {
            for (DutyFactorFlowQuery q : flows) {
                if (q.getFlow() != null) {
                    premium = premium.multiply(q.getFlow()).divide(HUNDRED);
                }
            }
        }
        return premium;
    }


    /**
     * Final:最后折算人数折扣因子
     * ★最终结果向上取整
     *
     * @param res
     * @param discount
     * @return
     */
    public BigDecimal tryPermiumByDiscount(BigDecimal res, BigDecimal discount) {
        if (discount != null) {
            res = res.multiply(discount).divide(HUNDRED);
        }
        return res.setScale(2, RoundingMode.HALF_UP);
    }

    /**
     * 报价校验规则
     * 因为试算保费需要用到职业对应的配置保费，所以此处只校验职业&保额的规则
     * 其他规则会在后续的流程校验，因为需求设计如此，不太好在试算阶段做全量的规则校验
     *
     * @param limits    限制条件
     * @param coverages 责任信息
     * @param jobClass  职业类别
     * @param qty       被保人数量
     */
    public void applyCheck4Inquiry(List<QuoteLimitDTO> limits,
                                   List<CoverageQuery> coverages,
                                   String jobClass,
                                   Integer qty) {
        Map<String, CoverageQuery> coverageMap = LambdaUtils.toMap(coverages, a -> String.valueOf(a.getSpcId()));

        Map<String, Integer> ocpMap = new HashMap<>(1);
        ocpMap.put(jobClass, qty);

        limits.stream().filter(e -> Objects.equals(e.getLimitType(), SmProductQuoteLimitItemVO.LIMIT_TYPE_OCVG_AMOUNT_2))
                .forEach(e -> applyHandler.checkOcp4LiabAmountLimit(ocpMap, coverageMap, e));
    }


    /**
     * 匹配人数折扣
     *
     * @param productId
     * @param qty
     * @return
     */
    public BigDecimal matchDiscount(Integer productId, int qty) {
        List<SmProductCoverageDiscountDTO> discounts = policyMapper.queryDiscount(productId);
        SmProductCoverageDiscountDTO discount = discounts.stream().filter(i -> qty >= i.getMinPerQty() && qty <= i.getPerQty()).findAny().orElse(null);
        if (discount != null) {
            return discount.getDiscount();
        }
        return null;
    }
}
