package com.cfpamf.ms.insur.commission.utils;

import com.cfpamf.ms.insur.base.exception.BizException;
import com.cfpamf.ms.insur.base.exception.ExcptEnum;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpStatus;
import org.apache.http.client.HttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.client.LaxRedirectStrategy;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpRequest;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.ClientHttpRequestExecution;
import org.springframework.http.client.ClientHttpRequestInterceptor;
import org.springframework.http.client.ClientHttpResponse;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

import java.io.IOException;
import java.io.InputStream;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.Objects;

/**
 * <AUTHOR> 2020/6/23 14:19
 */
@Slf4j
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class DownloadUtil {
    /**
     * https 调用RestTemplate 单例不能每次新建！！
     */
    private static RestTemplate restTemplate = getHttpsTemplate();

    /**
     * 构造https请求参数
     *
     * @return
     */
    private static RestTemplate getHttpsTemplate() {
        // RestTemplate 支持服务器内302重定向
        HttpComponentsClientHttpRequestFactory factory = new HttpComponentsClientHttpRequestFactory();
        HttpClient httpClient = HttpClientBuilder.create()
                .setRedirectStrategy(new LaxRedirectStrategy())
                .build();
        factory.setHttpClient(httpClient);
        factory.setReadTimeout(10_000);
        factory.setConnectTimeout(1_000);
        RestTemplate restTemplate = new RestTemplate(factory);
        restTemplate.getInterceptors()
                .add(new ClientHttpRequestInterceptor() {
                    @Override
                    public ClientHttpResponse intercept(HttpRequest request, byte[] body, ClientHttpRequestExecution execution) throws IOException {
                        HttpHeaders headers = request.getHeaders();
                        if (!headers.containsKey(HttpHeaders.USER_AGENT)) {
                            headers.add(HttpHeaders.USER_AGENT, "DownloadClient/1.1.2");
                        }
                        if (!headers.containsKey(HttpHeaders.ACCEPT)) {
                            headers.add(HttpHeaders.ACCEPT, "*/*");
                        }
                        if (!headers.containsKey(HttpHeaders.ACCEPT_ENCODING)) {
                            headers.add(HttpHeaders.ACCEPT_ENCODING, "gzip, deflate, br");
                        }
                        return execution.execute(request, body);
                    }
                });
        return restTemplate;
    }

    public static InputStream downloadByUrl(String url) throws IOException {
        if (UrlUtils.isAbsoluteUrl(url)) {

            ResponseEntity<Resource> responseEntity = restTemplate.getForEntity(URLDecoder.decode(url, StandardCharsets.UTF_8.name()), Resource.class);
            if (responseEntity.getStatusCodeValue() >= HttpStatus.SC_BAD_REQUEST) {
                throw new BizException(ExcptEnum.FILE_DOWNLOAD_FAIL_501006);
            }
            return Objects.requireNonNull(responseEntity.getBody()).getInputStream();
        }
        throw new UnsupportedOperationException("暂时仅支持绝对路径的文件下载");

    }
}


