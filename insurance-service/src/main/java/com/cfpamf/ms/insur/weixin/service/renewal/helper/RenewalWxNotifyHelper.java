package com.cfpamf.ms.insur.weixin.service.renewal.helper;

import com.cfpamf.ms.insur.admin.dao.safes.claim.SmClaimCancelReportMapper;
import com.cfpamf.ms.insur.admin.dao.safes.order.SmOrderInsuredMapper;
import com.cfpamf.ms.insur.admin.enums.renewal.EnumNonRenewalReason;
import com.cfpamf.ms.insur.admin.enums.renewal.EnumRenewalIntention;
import com.cfpamf.ms.insur.admin.enums.renewal.EnumServiceMode;
import com.cfpamf.ms.insur.admin.pojo.po.SmOrderInsured;
import com.cfpamf.ms.insur.admin.pojo.vo.AuthUserVO;
import com.cfpamf.ms.insur.admin.pojo.vo.UserVO;
import com.cfpamf.ms.insur.admin.service.UserService;
import com.cfpamf.ms.insur.base.config.WechatConfig;
import com.cfpamf.ms.insur.weixin.pojo.vo.WxClaimListVo;
import com.cfpamf.ms.insur.weixin.pojo.vo.claim.WxClaimCancelReportNotifyVo;
import com.cfpamf.ms.insur.weixin.pojo.vo.renewal.WxRenewalFollowListVo;
import com.cfpamf.ms.insur.weixin.service.WxMpServiceProxy;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.mp.bean.template.WxMpTemplateData;
import me.chanjar.weixin.mp.bean.template.WxMpTemplateMessage;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.MessageFormat;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2022/2/15 10:01
 */
@Component
@Slf4j
public class RenewalWxNotifyHelper {
    /**
     * 理赔结果通知微信模板Id
     */
    @Value("${wechat.message.renewalFollowTemplateId}")
    private String templateId;

    @Value("${system.api-domain}")
    String apiDomain;

    /**
     * 微信参数配置
     */
    @Autowired
    private WechatConfig wechatConfig;

    /**
     * 微信代理service
     */
    @Lazy
    @Autowired
    private WxMpServiceProxy serviceProxy;

    @Autowired
    private SmOrderInsuredMapper smOrderInsuredMapper;


    /**
     * 约定日期前一天通知-提醒跟进人
     *
     */
    public void sendCancelBeforeMessage(WxRenewalFollowListVo wxRenewalFollowListVo) {
        WxMpTemplateMessage wxMpTemplateMessage = renewalFollowBeforeMessage(wxRenewalFollowListVo);
        serviceProxy.sendTemplateMsg(wxMpTemplateMessage);
    }

    /**
     * 约定日期前一天通知-提醒跟进人
     * <p>
     * ————————————————————————————————————————————
     * 您与客户【客户姓名】约定2023年4月01号【】联系，请勿忘记，及时联系续保哦~
     * 保单号：xxxx
     * 产品名称：xxxxxx
     *
     */
    private WxMpTemplateMessage renewalFollowBeforeMessage(WxRenewalFollowListVo wxRenewalFollowListVo) {
        String timeFormat = "{0}月{1}日";
        String nextTime = MessageFormat.format(timeFormat,wxRenewalFollowListVo.getNextTime().getMonthValue()
                , wxRenewalFollowListVo.getNextTime().getDayOfMonth());

        List<SmOrderInsured> insureds = smOrderInsuredMapper.selectByPolicyNo(wxRenewalFollowListVo.getPolicyNo());
        Integer insuredId = null;

        if (CollectionUtils.isNotEmpty(insureds)) {
            insuredId = insureds.get(0).getId();
        }

        WxMpTemplateMessage wtm = new WxMpTemplateMessage();
        wtm.setTemplateId(templateId);
        wtm.setToUser(wxRenewalFollowListVo.getWxOpenId());
        wtm.setUrl(getRenewalDetailUrl(insuredId,"schedule", "cm"));
        wtm.addData(new WxMpTemplateData("first", String.format("您与客户 %s 约定 %s %s 联系，请勿忘记，及时联系续保哦~\r\n", wxRenewalFollowListVo.getPersonName(),nextTime,EnumServiceMode.dict(wxRenewalFollowListVo.getNextMethod())), "#030303"));
        wtm.addData(new WxMpTemplateData("keyword1", wxRenewalFollowListVo.getPersonName(), "#0000FF"));
        wtm.addData(new WxMpTemplateData("keyword2", "产品："+wxRenewalFollowListVo.getProductName(), "#0000FF"));
        wtm.addData(new WxMpTemplateData("keyword3", "约定"+nextTime+EnumServiceMode.dict(wxRenewalFollowListVo.getNextMethod())+"联系", "#0000FF"));
        wtm.addData(new WxMpTemplateData("remark", ""));
        return wtm;
    }


    /**
     * 获取微信理赔详情进度页URL
     *
     * @return
     */
    public String getRenewalDetailUrl(int insuredId,String type,String role) {
        try {
            return String.format(wechatConfig.getBackJumpUrl(), URLEncoder.encode(apiDomain+"/wx/index/renewDetail?orderId=" + insuredId + "&type=" + type + "&role=" + role, StandardCharsets.UTF_8.name()), "");
        } catch (Exception e) {
            log.error("", e);
        }
        return null;
    }
}
