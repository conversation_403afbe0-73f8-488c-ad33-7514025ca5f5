package com.cfpamf.ms.insur.admin.renewal.dao;

import com.cfpamf.ms.insur.admin.pojo.dto.renewal.InsuranceRenewDto;
import com.cfpamf.ms.insur.admin.pojo.dto.renewal.RenewCountDTO;
import com.cfpamf.ms.insur.admin.pojo.po.renewal.InsuranceRenewPo;
import com.cfpamf.ms.insur.admin.renewal.entity.OrderRenewal;
import com.cfpamf.ms.insur.admin.renewal.form.RenewalOrderSearchForm;
import com.cfpamf.ms.insur.admin.renewal.form.WxOrderSearchForm;
import com.cfpamf.ms.insur.admin.renewal.vo.*;
import com.cfpamf.ms.insur.base.dao.MyMappler;
import com.cfpamf.ms.insur.weixin.pojo.query.order.OrderDetailQuery;
import com.cfpamf.ms.insur.weixin.pojo.query.renewal.RenewalBasicQuery;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;


/**
 * <AUTHOR>
 */
@Mapper
public interface InsuranceRenewMapper extends MyMappler<InsuranceRenewPo> {
    /**
     * 初始化保单续保表
     *
     * @return
     */
    int insertInsuranceRenew();

    /**
     * 续保列表分页查询
     *
     * @param wxOrderSearchForm
     * @return
     */
    List<InsuranceRenewedOrderVo> searchWxRenewalOrderVo(WxOrderSearchForm wxOrderSearchForm);

    /**
     * 获取管护经理待续保保单条数
     * @param wxOrderSearchForm
     * @return
     */
    Integer searchWxWaitRenewalOrderVoCount(WxOrderSearchForm wxOrderSearchForm);

    /**
     * 查询续保订单详情
     * @param query
     * @return
     */
    OrderDetailVo queryOrderDetail(OrderDetailQuery query);

    /**
     * 查询被保人信息
     * @param policyNo
     * @param productAttrCode
     * @return
     */
    List<PersonVo> queryInsuredsByPolicyNo(@Param("policyNo")String policyNo,@Param("productAttrCode")String productAttrCode);

    /**
     * 查询批减被保人信息
     * @param policyNo
     * @return
     */
    List<RenewalOrderItemVO> queryInsuredsCancelByPolicyNo(@Param("policyNo")String policyNo);

    /**
     * 根据保单号查询已断保续保单
     * @param policyNo
     * @return
     */
    Integer updateByPolicyNo(@Param("policyNo")String policyNo,@Param("overRenewalReason") String overRenewalReason);
    /**
     * 根据保单号更新续保单状态
     * @param orderRenewal
     * @return
     */
    Integer updateInsStatusByPolicyNo(OrderRenewal orderRenewal);

    /**
     * 待续保订单数
     * @param query
     * @return
     */
    Integer waitRenewalStatistics(RenewalBasicQuery query);

    /**
     * 已续购订单统计
     * @param query
     * @return
     */
    Integer renewedStatistics(RenewalBasicQuery query);

    /**
     * 已断保订单统计
     * @param query
     * @return
     */
    Integer overRenewalStatistics(RenewalBasicQuery query);

    /**
     * 续保订单查询（后端）
     * @param renewalOrderSearchForm
     * @return
     */
    List<InsuranceRenewedOrderVo> searchRenewalOrderVo(RenewalOrderSearchForm renewalOrderSearchForm);

    /**
     * 根据老保单号获取续保保单信息
     * @param oldPolicy
     * @return
     */
    default List<InsuranceRenewPo> listRenewDataByOldPolicyNo(String oldPolicy){
        if (StringUtils.isEmpty(oldPolicy)) {
            return Collections.emptyList();
        }
        InsuranceRenewPo renewPo = new InsuranceRenewPo();
        renewPo.setOldPolicyNo(oldPolicy);
        renewPo.setEnabledFlag(0);
        return select(renewPo);
    }

    /**
     * 根据计划id查续保保单数查询
     * @param
     * @return
     *
     */
    List<RenewCountDTO> selectByPlanId();


    /**
     * 根据老订单号获取续保保单信息
     * @param oldOrderId
     * @return
     */
    default InsuranceRenewPo getRenewDataByOldOrderId(String oldOrderId){
        if (StringUtils.isEmpty(oldOrderId)) {
            return new InsuranceRenewPo();
        }
        InsuranceRenewPo renewPo = new InsuranceRenewPo();
        renewPo.setOldOrderId(oldOrderId);
        renewPo.setEnabledFlag(0);
        List<InsuranceRenewPo> result = select(renewPo);
        return CollectionUtils.isEmpty(result)?new InsuranceRenewPo():result.get(0);
    }

    /**
     * 获取个险转投保单
     * @param policyVo
     * @return
     */
    List<TransferPolicyVo> getPersonTransferPolicy(WaitedPolicyVo policyVo);

    /**
     * 自动查询个险转投保单
     * @param policyVo
     * @return
     */
    List<TransferPolicyVo> autoQueryPersonTransferPolicy(WaitedPolicyVo policyVo);

    /**
     * 自动查询团险转投保单
     * @param policyVo
     * @return
     */
    List<TransferPolicyVo> autoQueryGroupTransferPolicy(WaitedPolicyVo policyVo);

    /**
     * 获取团险转投保单
     * @param policyVo
     * @return
     */
    List<TransferPolicyVo> getGroupTransferPolicy(WaitedPolicyVo policyVo);


    /**
     * 获取首页弹开页面待续保数据
     * @param customerAdminId
     * @return
     */
    List<HomePageRenewedOrderVo> listHomePageRenewedOrder(@Param("customerAdminId")String customerAdminId);

    /**
     * 获取首页弹开页面待续保数据个数
     * @param customerAdminId
     * @return
     */
    Integer countHomePageRenewedOrder(@Param("customerAdminId")String customerAdminId);

    /**
     * 注意，该方法已注销
     * 待续列表数据由insurance_renewal表改成policy_renewal_base_info表
     * @param limitSize
     * @return
     */
    @Deprecated
    List<InsuranceRenewPo> listWaitInsuranceRenewal(@Param("limitSize")Integer limitSize);

    List<InsuranceRenewDto> listWaitInsuranceRenewalV2(@Param("limitSize")Integer limitSize);

    InsuranceRenewDto getNewRenewDataByOldOrderId(@Param("oldOrderId")String oldOrderId);

    @Update("update insurance_renewal set update_time = now() where id =  #{id}")
    Integer modifyUpdateTime(@Param("id")Integer id);

    @Update("update insurance_renewal set update_time = now() where old_policy_no =  #{oldPolicyNo}")
    Integer modifyUpdateTimeByOldPolicyNo(@Param("oldPolicyNo")String oldPolicyNo);
}
