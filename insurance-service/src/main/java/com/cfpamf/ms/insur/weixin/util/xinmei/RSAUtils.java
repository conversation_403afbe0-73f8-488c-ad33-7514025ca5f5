package com.cfpamf.ms.insur.weixin.util.xinmei;

import java.security.KeyFactory;
import java.security.KeyPair;
import java.security.KeyPairGenerator;
import java.security.NoSuchAlgorithmException;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.security.Signature;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.HashMap;
import java.util.Map;

import com.alibaba.fastjson.JSON;
import org.apache.commons.codec.binary.Base64;

public class RSAUtils {
    public static void main(String[] args) throws Exception {
        Map<String, String> map = createKey();
        System.out.println(JSON.toJSONString(map));

        String puk = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEApTVHIArq6wCsUiRdk1EVEowTHCF1tNI22+dLxlQ1eFcWsQ6w1uOkZvDOkM7qlTwqaKL2E2gJa8SOQDEZEld2clNuO7m6c4KWq+FQ5heXolLIwcIqrLul1b8H+yPSSU4u9VJoEEl8IKszgIjUb+LM89SJge5rNXDA/3XSpEaRtNitz+yRv7rvJ/TZMcNubunZ/bWVpwxKAVdF1XfGJ9R9AO/zzCaHF8+vYGl+U/hPGYLje2yFgoqTfZhS+Q+J4pN6lFcdhblRHASWa5xPSR00jJhchdF86ogj2eNyoQmZ7C2GRdHAoc0HUGTwBXOprhg5gUX4LtMOc4lBveZy4oX69wIDAQAB";

        String msg = "{\"resultMsg\":\"\",\"result\":\"1\",\"body\":[],\"policyInfos\":[]}";
        String sign = "lGxezfUMnHtffW50xSZrO74paZIAPQU9iuiPb2tyJKA9OKm+MRv5udqawCbmCmLep9MUD73OmXLqlYVZPK1e2bAOxMjVi7+xGjYM3kZfr2AQMO0moUNG8QZ/zJsw0sn3vptP8ovCqZvBg4KfHoWpW2DPU1y33tQoj7CJgvZxBVd/xcGBf1Wgws619jRnVadyAK/v1w7dDYEmpXB0jfoMULLR1MU7VnmeLk8+xxla8uPyGlWZysvm+jXM4Am/q75yuDMTPUaaEWvFI1vCwg4ZYCPpEsY0Vuj/NEQqLmuwvQ3e4k/6u58KhlBB1z4RRnmcEi8k4b7YO1hz2aYg8erhiA==";

//        //加签
//        String sign = sign(msg, pri);
//        System.out.println("sign"+sign);
//        //验签
        System.out.println(verify(msg,sign, puk));
    }

    /**
     * 生成RSA公钥私钥
     *
     * @return Map key pubKey公钥 priKey私钥
     */
    public static Map<String, String> createKey() throws NoSuchAlgorithmException {
        Map<String, String> map = new HashMap<String, String>();
        KeyPairGenerator keyPairGenerator = KeyPairGenerator.getInstance("RSA");
        keyPairGenerator.initialize(2048);
        KeyPair keyPair = keyPairGenerator.generateKeyPair();
        PublicKey pubKey = keyPair.getPublic();
        PrivateKey privateKey = keyPair.getPrivate();
        map.put("pubKey", new String(Base64.encodeBase64(pubKey.getEncoded(), false)));
        map.put("priKey",
                new String(Base64.encodeBase64(privateKey.getEncoded(), false)));
        return map;
    }

    /**
     * 对传入的数据验证签名，返回验证结果
     *
     * @param message  需要验证签名的数据字符串
     * @param chkValue 签名字符串(长度：1024-->128 2048-->256)
     * @param pubKey   RSA公钥
     * @return boolean true : 验证签名成功 false: 验证签名失败
     */
    public static boolean verify(String message, String chkValue, String pubKey) {
        boolean flag = true;
        try {
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            byte[] encodedKey = Base64.decodeBase64(pubKey.getBytes());
            PublicKey publicKey = keyFactory.generatePublic(new X509EncodedKeySpec(encodedKey));
            Signature signet = Signature.getInstance("SHA256withRSA");
            signet.initVerify(publicKey);
            signet.update(message.getBytes("utf-8"));
            flag = signet.verify(Base64.decodeBase64(chkValue.getBytes()));
            return flag;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 用私钥对信息生成数字签名
     */
    public static String sign(String data, String priKey) throws Exception {
        byte[] keyBytes = Base64.decodeBase64(priKey.getBytes());
        // 构造PKCS8EncodedKeySpec对象
        PKCS8EncodedKeySpec pkcs8EncodedKeySpec = new PKCS8EncodedKeySpec(keyBytes);
        // 指定加密算法
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        // 取私钥匙对象
        PrivateKey privateKey2 = keyFactory.generatePrivate(pkcs8EncodedKeySpec);
        // 用私钥对信息生成数字签名
        Signature signature = Signature.getInstance("SHA256withRSA");
        signature.initSign(privateKey2);
        signature.update(data.getBytes("utf-8"));
        return new String(Base64.encodeBase64(signature.sign()));
    }
}
