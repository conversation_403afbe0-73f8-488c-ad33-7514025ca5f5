package com.cfpamf.ms.insur.weixin.pojo.query.group;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.constraints.NotBlank;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description
 * @date 2022/8/31 4:26 下午
 * @Version 1.0
 */
@Data
public class InvoiceInfoQuery implements Serializable {
    @NotBlank(message = "参数不能为空")
    @ApiModelProperty("滑块验证-Key")
    private String key;

    @NotBlank(message = "参数不能为空")
    @ApiModelProperty("滑块验证-坐标值")
    private String x;

    @NotBlank(message = "参数不能为空")
    @ApiModelProperty("保单号")
    private String policyNo;

}
