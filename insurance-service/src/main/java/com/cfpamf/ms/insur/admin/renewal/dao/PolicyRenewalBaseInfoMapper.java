package com.cfpamf.ms.insur.admin.renewal.dao;

import com.cfpamf.ms.insur.admin.renewal.entity.PolicyRenewalBaseInfo;
import com.cfpamf.ms.insur.base.dao.MyMappler;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.math.BigDecimal;
import java.util.Date;

@Mapper
public interface PolicyRenewalBaseInfoMapper extends MyMappler<PolicyRenewalBaseInfo> {

    int insertRenewalBaseInfo();

//    int updateRenewedStatusByPolicyNo(@Param("oldPolicyNo")String oldPolicyNo,
//                                      @Param("renewalType")String renewalType,
//                                      @Param("newOrderTime") Date newOrderTime,
//                                      @Param("newPolicyNo")String newPolicyNo,
//                                      @Param("newPremium") BigDecimal newPremium,
//                                      @Param("newProductCode") String newProductCode,
//                                      @Param("newProductName") String newProductName);

    int updateRenewedStatusByPolicyNo(PolicyRenewalBaseInfo baseInfo);

    /**
     * 断保状态变更
     * @return
     */
    int updateRenewShortBaseCancel();

    @Update("update policy_renewal_base_info set update_time = now() where  old_policy_no = #{oldPolicyNo} ")
    Integer modifyUpdateTimeByOldPolicyNo(@Param("oldPolicyNo")String oldPolicyNo);
}
