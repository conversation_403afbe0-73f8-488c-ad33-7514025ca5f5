package com.cfpamf.ms.insur.commission.mapper;

import com.cfpamf.ms.insur.base.dao.MyMappler;
import com.cfpamf.ms.insur.commission.po.CommissionUploadDetailPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 网关session白名单配置表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-22
 */
@Mapper
public interface CommissionUploadDetailMapper extends MyMappler<CommissionUploadDetailPO> {

    /**
     * 保存数据
     *
     * @param domains
     * @return
     */
    int insertListOrUpdateItem(List<CommissionUploadDetailPO> domains);

    /**
     * 跟进上传记录修改数据
     *
     * @param fileId
     * @param flag
     * @return
     */
    int updateFlagByUploadId(@Param("fileId") Integer fileId, @Param("flag") int flag);
}
