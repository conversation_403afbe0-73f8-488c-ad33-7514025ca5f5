package com.cfpamf.ms.insur.weixin.pojo.dto;

import com.cfpamf.ms.insur.admin.pojo.po.SmCancelBankDeposit;
import com.cfpamf.ms.insur.admin.pojo.vo.cancel.SmCancelFileGroupVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.constraints.NotBlank;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR> 2020/2/11 15:39
 */
@Data
@ApiModel("退保申请")
public class WxCancelDTO {

    @ApiModelProperty("退保ID（补充资料填写）")
    private Integer cancelId;

    /**
     * token
     */
//    @NotBlank(message = "业务标记不能为空")
    @ApiModelProperty(value = "业务token 初次申请不能为空")
    private String token;

    /**
     * 保单Id
     */
    @NotNull(message = "保单Id不能为空")
    @ApiModelProperty(value = "保单Id")
    private Integer insuredId;

    @NotBlank(message = "保单号不能为空")
    @ApiModelProperty("保单号")
    private String policyNo;

    @NotBlank(message = "订单ID不能为空")
    @ApiModelProperty("订单ID")
    private String fhOrderId;

    @NotBlank(message = "退保原因类型不能为空")
    @ApiModelProperty("退保原因类型")
    private String cancelReasonType;

    @ApiModelProperty("退保原因")
    private String cancelReason;

    @Valid
    @NotEmpty(message = "退保资料不能为空")
    @ApiModelProperty("退保资料")
    private List<SmCancelFileGroupVO> fileGroupVOS;

    @Valid
    @NotNull(message = "开户行不能为空")
    @ApiModelProperty("开户行信息")
    private SmCancelBankDeposit bankDeposit;
    /**
     * 创建角色
     */
    @ApiModelProperty(hidden = true)
    private String createRole;


    /**
     * 创建人
     */
    @ApiModelProperty(hidden = true)
    private String createBy;

    /**
     * 理赔状态
     */
    @ApiModelProperty(hidden = true)
    private String claimState;


    /**
     * 理赔状态
     */
    @ApiModelProperty(hidden = true)
    private String claimResult;

    /**
     * 理赔编号
     */
    @ApiModelProperty(hidden = true)
    private String claimNo;
}
