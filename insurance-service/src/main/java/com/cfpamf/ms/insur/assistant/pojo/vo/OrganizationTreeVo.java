package com.cfpamf.ms.insur.assistant.pojo.vo;

import com.cfpamf.ms.bms.facade.vo.OrganizationBaseVO;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 组织架构树的返回对象
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024年03月05日
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class OrganizationTreeVo implements Serializable {
    @ApiModelProperty("hr系统中的组织Id")
    private Integer hrOrgId;

    @ApiModelProperty("组织类型，已知：4为区域，6为片区，7为分支")
    private Integer orgCategory;

    @ApiModelProperty("组织编码")
    private String orgCode;

    @ApiModelProperty("组织Id")
    private Integer orgId;

    @ApiModelProperty("组织名称")
    private String orgName;

    @ApiModelProperty("组织在组织树中的完整路径")
    private String path;

    @ApiModelProperty("组织机构的上级Id")
    private String parentOrgCode;

    @ApiModelProperty("下级组织")
    private List<OrganizationTreeVo> children;
}
