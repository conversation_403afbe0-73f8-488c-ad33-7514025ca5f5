package com.cfpamf.ms.insur.commission.utils;

import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jws;
import io.jsonwebtoken.Jwts;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;

/**
 * <AUTHOR> 2022/12/22 12:38
 */
@Slf4j
public class JwtHelper {

    public static final String JWT_KEY_MASTERJOBNUMBER = "masterJobNumber";
    private static final String JWT_KEY_AUTH = "authorization";

    private static final String WEB_KEY_REFRESH_TOKEN = "cfpamfRt";
    private static final int JWT_KEY_TOKEN_MIN_LENGTH = 100;

    private final String JWT_SECRET_KEY = "1E02941E-9E8D-4CFB-9BEA-3AA5FCC5863B";
    /**
     * jwt属性 employeeId
     */
    private static final String JWT_KEY_EMPLOYEEID = "employeeId";
    /**
     * jwt属性 hrUserId
     */
    private static final String JWT_KEY_HRUSERID = "hrUserId";
    /**
     * jwt属性 account
     */
    private static final String JWT_KEY_ACCOUNT = "account";
    /**
     * jwt属性 userName
     */
    private static final String JWT_KEY_USERNAME = "userName";
    /**
     * jwt属性 jobNumber
     */
    private static final String JWT_KEY_JOBNUMBER = "jobNumber";
    /**
     * jwt属性 orgId
     */
    private static final String JWT_KEY_ORGID = "orgId";
    /**
     * jwt属性 hrOrgId
     */
    private static final String JWT_KEY_HRORGID = "hrOrgId";
    /**
     * jwt属性 hrOrgCode
     */
    private static final String JWT_KEY_HRORGCODE = "hrOrgCode";
    /**
     * jwt属性 hrOrgName
     */
    public static final String JWT_KEY_HRORGNAME = "hrOrgName";
    /**
     * jwt属性 hrOrgTreePath
     */
    public static final String JWT_KEY_HRORGTREEPATH = "hrOrgTreePath";
    /**
     * jwt属性 userType
     */
    public static final String JWT_KEY_USERTYPE = "userType";
    /**
     * jwt属性 randomCode
     */
    public static final String JWT_KEY_RANDOMCODE = "randomCode";
    /**
     * jwt属性 useSso
     */
    public static final String JWT_KEY_USE_SSO = "useSso";
    /**
     * jwt属性 systemId
     */
    public static final String JWT_KEY_SYSTEM_ID = "systemId";
    /**
     * jwt属性 createTime
     */
    public static final String JWT_KEY_CREATE_TIME = "createTime";

    public JwtUserInfo getJwtUserInfo() {
        String token = getToken();
        return getUserFromToken(token);
    }

    public String getToken() {
        HttpServletRequest request = HttpRequestUtil.getRequest();
        String token = request.getHeader(JWT_KEY_AUTH);
        if (token == null) {
            token = request.getParameter(JWT_KEY_AUTH);
            if (token == null) {
                return "";
            }
        }

        if ((StringUtils.isEmpty(token)) || (token.length() < JWT_KEY_TOKEN_MIN_LENGTH)) {
            return "";
        }
        return token;
    }

    /**
     * 根据token字符串解析用户信息
     */
    public JwtUserInfo getUserFromToken(String token) {
        if ((StringUtils.isEmpty(token)) || (token.length() < JWT_KEY_TOKEN_MIN_LENGTH)) {
            return null;
        }
        try {
            Jws<Claims> claimsJws = Jwts.parser().setSigningKey(JWT_SECRET_KEY).parseClaimsJws(token);
            JwtUserInfo jwtUser = getUserFromClaims(claimsJws);
            return jwtUser;
        } catch (Exception ex) {
            log.warn("根据token字符串解析用户信息，token：" + token, ex);
        }
        return null;
    }

    /**
     * 构造用户信息
     */
    private JwtUserInfo getUserFromClaims(Jws<Claims> claimsJws) {
        Claims body = claimsJws.getBody();
        String userId = body.getSubject();
        String employeeId = getObjectValue(body.get(JWT_KEY_EMPLOYEEID));
        String hrUserId = getObjectValue(body.get(JWT_KEY_HRUSERID));
        String account = getObjectValue(body.get(JWT_KEY_ACCOUNT));
        String userName = getObjectValue(body.get(JWT_KEY_USERNAME));
        String jobNumber = getObjectValue(body.get(JWT_KEY_JOBNUMBER));
        String masterJobNumber = getObjectValue(body.get(JWT_KEY_MASTERJOBNUMBER));
        String orgId = getObjectValue(body.get(JWT_KEY_ORGID));
        String hrOrgId = getObjectValue(body.get(JWT_KEY_HRORGID));
        String hrOrgCode = getObjectValue(body.get(JWT_KEY_HRORGCODE));
        String hrOrgName = getObjectValue(body.get(JWT_KEY_HRORGNAME));
        String orgTreePath = getObjectValue(body.get(JWT_KEY_HRORGTREEPATH));
        String userType = getObjectValue(body.get(JWT_KEY_USERTYPE));
        String randomCode = getObjectValue(body.get(JWT_KEY_RANDOMCODE));
        String systemId = getObjectValue(body.get(JWT_KEY_SYSTEM_ID));
        String useSso = getObjectValue(body.get(JWT_KEY_USE_SSO));
        Integer gray = getIntegerValue(body.get("gray"));
        Date date;
        try {
            date = new Date(Long.parseLong(getObjectValue(body.get(JWT_KEY_CREATE_TIME))));
        } catch (Exception e) {
            date = new Date();
        }
        return JwtUserInfo.builder()
                .gray(gray == null ? 0 : gray)
                .userId(StringUtils.isEmpty(userId) ? 0 : Integer.parseInt(userId))
                .employeeId(StringUtils.isEmpty(employeeId) ? 0 : Integer.parseInt(employeeId))
                .hrUserId(StringUtils.isEmpty(hrUserId) ? 0 : Integer.parseInt(hrUserId))
                .account(StringUtils.isEmpty(account) ? "" : account)
                .userName(StringUtils.isEmpty(userName) ? "" : userName)
                .jobNumber(StringUtils.isEmpty(jobNumber) ? "" : jobNumber)
                .masterJobNumber(StringUtils.isEmpty(masterJobNumber) ? "" : masterJobNumber)
                .orgId(StringUtils.isEmpty(orgId) ? 0 : Integer.parseInt(orgId))
                .hrOrgId(StringUtils.isEmpty(hrOrgId) ? 0 : Integer.parseInt(hrOrgId))
                .hrOrgCode(StringUtils.isEmpty(hrOrgCode) ? "" : hrOrgCode)
                .hrOrgName(StringUtils.isEmpty(hrOrgName) ? "" : hrOrgName)
                .hrOrgTreePath(StringUtils.isEmpty(orgTreePath) ? "" : orgTreePath)
                .userType(StringUtils.isEmpty(userType) ? 2 : Integer.parseInt(userType))
                .randomCode(randomCode)
                .useSso(!StringUtils.isEmpty(useSso) && Boolean.parseBoolean(useSso))
                .systemId(StringUtils.isEmpty(systemId) ? 0 : Integer.parseInt(systemId))
                .createTime(date)
                .build();
    }


    /**
     * 获取object字符串
     *
     * @param obj
     * @return
     */
    protected String getObjectValue(Object obj) {
        return obj == null ? "" : obj.toString();
    }

    protected Integer getIntegerValue(Object obj) {
        return obj == null ? 0 : (Integer) obj;
    }

}
