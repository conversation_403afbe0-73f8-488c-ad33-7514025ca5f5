package com.cfpamf.ms.insur.weixin.service;

import com.cfpamf.ms.insur.base.util.HttpRequestUtil;
import com.cfpamf.ms.insur.weixin.dao.safes.WxBlackListLogMapper;
import com.cfpamf.ms.insur.weixin.dao.safes.WxBlackListMapper;
import com.cfpamf.ms.insur.weixin.pojo.po.WxBlacklist;
import com.cfpamf.ms.insur.weixin.pojo.po.WxBlacklistLog;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.Enumeration;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR> 2021/11/25 10:13
 */
@Service
@Slf4j
public class WxBlacklistService {

    @Autowired
    WxBlackListMapper blackListMapper;

    @Autowired
    WxBlackListLogMapper blackListLogMapper;

    /**
     * 是否黑民单用户
     *
     * @param openId
     * @return
     */
    public boolean isBlack(String openId) {

        WxBlacklist query = new WxBlacklist();
        query.setOpenId(openId);
        return blackListMapper.selectCount(query) > 0;
    }

    /**
     * 记录黑名单登录信息
     *
     * @param openId
     * @return
     */
    public boolean loginLog(String openId) {
        HttpServletRequest request = Objects.requireNonNull(HttpRequestUtil.getRequest());
        Enumeration<String> headerNames = request.getHeaderNames();

        Map<String, Object> map = Maps.newHashMap();
        while (headerNames.hasMoreElements()) {
            String headName = headerNames.nextElement();
            map.put(headName, request.getHeaders(headName));
        }
        log.info("black log {} {}", openId, map);
        String ipAddr = HttpRequestUtil.getIpAddr(request);
        WxBlacklistLog add = new WxBlacklistLog();
        add.setOpenId(openId);
        add.setWebAgent(HttpRequestUtil.getUserAgent());
        add.setIp(ipAddr);
        blackListLogMapper.insertUseGeneratedKeys(add);

        return true;
    }
}
