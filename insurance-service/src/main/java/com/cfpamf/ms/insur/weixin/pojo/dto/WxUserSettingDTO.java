package com.cfpamf.ms.insur.weixin.pojo.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * @description: 微信用户配置DTO
 * @author: zhang<PERSON>i
 * @create: 2018-07-24 10:30
 **/
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class WxUserSettingDTO extends WxAuthorizationDTO {

    /**
     * 员工id
     */
    @ApiModelProperty(value = "员工id", hidden = true)
    private String userId;

    /**
     * 配置code
     */
    @ApiModelProperty("配置code")
    private String stCode;

    /**
     * 配置值
     */
    @ApiModelProperty("配置值")
    private String stValue;
}
