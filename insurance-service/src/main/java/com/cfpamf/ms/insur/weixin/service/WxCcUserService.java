package com.cfpamf.ms.insur.weixin.service;

import com.cfpamf.common.ms.exception.MSException;
import com.cfpamf.common.ms.result.Result;
import com.cfpamf.ms.bms.facade.api.BmsUserFacade;
import com.cfpamf.ms.insur.weixin.feign.BmsExtendFacade;
import com.cfpamf.ms.insur.weixin.pojo.vo.*;
import org.apache.commons.lang3.StringUtils;
import com.alibaba.fastjson.JSON;
import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.common.ms.util.DateUtil;
import com.cfpamf.ms.bms.facade.dto.FdAccountAuthDTO;
import com.cfpamf.ms.bms.facade.dto.FdWechatV2DTO;
import com.cfpamf.ms.bms.facade.vo.*;
import com.cfpamf.ms.insur.admin.constant.SmConstants;
import com.cfpamf.ms.insur.admin.dao.safes.AuthUserMapper;
import com.cfpamf.ms.insur.admin.dao.safes.SmAgentMapper;
import com.cfpamf.ms.insur.admin.dao.safes.SmProductMapper;
import com.cfpamf.ms.insur.admin.pojo.dto.QrcodeDTO;
import com.cfpamf.ms.insur.admin.pojo.po.UserPost;
import com.cfpamf.ms.insur.admin.pojo.vo.AuthUserVO;
import com.cfpamf.ms.insur.admin.pojo.vo.SmAgentVO;
import com.cfpamf.ms.insur.admin.pojo.vo.SmProductSalesOrgVO;
import com.cfpamf.ms.insur.admin.service.UserService;
import com.cfpamf.ms.insur.admin.service.UserSyncService;
import com.cfpamf.ms.insur.base.config.BmsWxConfig;
import com.cfpamf.ms.insur.base.config.WechatConfig;
import com.cfpamf.ms.insur.base.exception.BizException;
import com.cfpamf.ms.insur.base.exception.ExcptEnum;
import com.cfpamf.ms.insur.base.infeface.CustomerCenterService;
import com.cfpamf.ms.insur.base.service.BmsService;
import com.cfpamf.ms.insur.base.util.*;
import com.cfpamf.ms.insur.facade.dto.InsUserInfo;
import com.cfpamf.ms.insur.weixin.constant.WxConstant;
import com.cfpamf.ms.insur.weixin.dao.safes.WxUserSettingMapper;
import com.cfpamf.ms.insur.weixin.pojo.dto.WxBindDTO;
import com.cfpamf.ms.insur.weixin.pojo.dto.WxUserEmailDTO;
import com.cfpamf.ms.insur.weixin.pojo.dto.WxUserSettingDTO;
import com.cfpamf.ms.insur.weixin.pojo.dto.user.WxUserUnbindDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.Period;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

import static com.cfpamf.ms.insur.base.constant.BaseConstants.ORG_REGION_HQ;
import static com.cfpamf.ms.insur.base.constant.BaseConstants.USER_TOKEN;

/**
 * 微信客户中心Service
 **/
@Slf4j
@Service
public class WxCcUserService extends WxAbstractService {

    /**
     * 总部审批人工号
     */
    @Value("${claim.headquarters.approver.userId}")
    private String headquartersApprover;

    /**
     * bms微信系统配置
     */
    @Autowired
    private BmsWxConfig bmsWxConfig;

    /**
     * 用户service
     */
    @Autowired
    private UserService userService;

    /**
     * 代理人mapper
     */
    @Autowired
    private SmAgentMapper agentMapper;

    /**
     * 微信公众号MpService
     */
    @Autowired
    private WxMpServiceProxy wxServiceProxy;

    /**
     * 下载service
     */
    @Autowired
    private WxDownloadService wxDownloadService;

    /**
     * 微信用户设置mapper
     */
    @Autowired
    private WxUserSettingMapper wusMapper;

    /**
     * BmsService
     */
    @Autowired
    private BmsService bmsService;

    /**
     * 产品mapper
     */
    @Autowired
    private SmProductMapper spMapper;

    @Autowired
    private UserSyncService userSyncService;

    @Lazy
    @Autowired
    private WechatConfig wechatConfig;

    @Autowired
    WxBlacklistService wxBlacklistService;

    @Autowired
    PermissionUtil permissionUtil;


    /**
     * 客户中心service
     */
    @Autowired
    private CustomerCenterService customerCenterService;

    @Resource
    private AuthUserMapper authUserMapper;

    @Resource
    private BmsUserFacade bmsUserFacade;

    @Resource
    private BmsExtendFacade bmsExtendFacade;

    @Resource
    private WxCcUserService wxCcUserService;


    /**
     * 通过工号获取信息
     *
     * @param userId
     * @return
     */
    public AuthUserVO getUserByUserId(String userId) {

        return userService.getAuthUserByUserId(userId);
    }

    public String getUserWxUrl(String userId) {

        AuthUserVO userInfo = getUserByUserId(userId);
        if (Objects.isNull(userInfo)) {
            throw new BizException(ExcptEnum.PARAMS_ERROR);
        }

        return getUserWxUrlByOpenId(userInfo.getWxOpenId());

    }

    public String getUserWxUrlByOpenId(String openId) {

        if (org.apache.commons.lang3.StringUtils.isBlank(openId)) {
            throw new BizException(ExcptEnum.PARAMS_ERROR.getCode(), "用户未绑定微信！");
        }

        WxSessionVO wxSession = getWxSession(openId);
        if (Objects.isNull(wxSession)) {
            wxSession = reloadWxSession(openId, null, "xiangzhu", null);
        }
        return String.format(wechatConfig.getFrontIndexUrl(), wxSession.getWxOpenId(), wxSession.getAuthorization());
    }

    /**
     * 微信回调获取微信用户信息
     *
     * @param wxOpenId
     * @param authorization
     * @return
     */
    public WxSessionVO getBindWxUser(String wxOpenId, String authorization) {
        WxSessionVO session = getWxSession(wxOpenId);
        // 授权失败 未绑定的员工
        // 未绑定的员工也需要获取分享人的信息
        if (session == null || !Objects.equals(session.getAuthorization(), authorization) /*|| session.isBindWeixin()*/) {
            return null;
        }
        session.setShowCicEntry(Boolean.FALSE);
        // 渠道为空即乡助微服务需要判断中华联合渠道入口
        if (StringUtils.isEmpty(session.getChannel())) {
            List<SmProductSalesOrgVO> salesOrgs = spMapper.selectCicPilotOrgs();
            // 中华联合产品无限制
            if (salesOrgs.stream().allMatch(s -> StringUtils.isEmpty(s.getOrgPath()))) {
                session.setShowCicEntry(Boolean.TRUE);
            }
            // 中华联合产品加了试点区域限制
            else {
                String sessionOrgPath = session.getOrgPath();
                if (salesOrgs.stream().anyMatch(p -> p == null || p.getOrgPath() == null
                        || (sessionOrgPath != null && sessionOrgPath.startsWith(p.getOrgPath())))) {
                    session.setShowCicEntry(Boolean.TRUE);
                }
                if (Objects.equals(session.getRegionName(), ORG_REGION_HQ)) {
                    String orgFullName = session.getOrganizationFullName();
                    //modify by zhangjian 2020-11-11去掉办公室中文判断
                    //if (orgFullName != null) {
                    if (super.isAreaOffice(session.getHrOrgId())) {
                        //2020-08-10 modify by zhangjian
                        //String regionName = orgFullName.substring(0, orgFullName.length() - 3);
                        String regionName = super.getBranchOrgName(session.getOrgCode());
                        if (salesOrgs.stream().anyMatch(s -> Objects.equals(s.getOrgName(), regionName))) {
                            session.setShowCicEntry(Boolean.TRUE);
                        }
                    }
                }
            }
        }
        return session;
    }

    /**
     * 微信用户和信贷系统用户绑定
     *
     * @param bindDto
     * @return
     */
    public WxSessionVO bindUserWxInfo(WxBindDTO bindDto) {
        if (StringUtils.isEmpty(bindDto.getWxOpenId())
                || StringUtils.isEmpty(bindDto.getLoginID())
                || StringUtils.isEmpty(bindDto.getLoginPwd())) {
            throw new BizException(ExcptEnum.WEIXIN_BIND_ERROR_601003);
        }
        if (Objects.equals(bindDto.getLoginType(), SmConstants.USER_TYPE_AGENT)) {
            userService.bindUserAgentWxInfo(bindDto);
        } else {
            userService.bindUserEmployeeWxInfo(bindDto);
        }
        WxSessionVO session = getWxSession(bindDto.getWxOpenId());
        return reloadWxSession(bindDto.getWxOpenId(), null, session.getChannel(), null);
    }

    /**
     * 二维码生成
     *
     * @param dto
     * @return
     */
    @Cacheable(cacheNames = "wxUserQrcode", key = "#dto.height+''+#dto.width+''+#dto.margin+''+#dto.url")
    public String getQrcodeBase64(QrcodeDTO dto) {
        if (dto.getMargin() == null) {
            dto.setMargin(2);
        }
        return QrcodeUtil.getQrcodeBase64(dto, false);
    }

    /**
     * 微信用户解绑
     *
     * @param wxOpenId
     * @param authorization
     * @return
     */
    public void unbindWxUser(String wxOpenId, String authorization) {
        WxSessionVO session = checkAuthority(wxOpenId, authorization);
        userService.unbindUserWx(session.getWxOpenId());
        reloadWxSession(wxOpenId, authorization, session.getChannel(), null);
    }

    /**
     * 微信用户解绑
     *
     * @param dto
     * @return
     */
    public void unbindWxUser(WxUserUnbindDTO dto) {
        String wxOpenId = dto.getWxOpenId();
        String authorization = dto.getAuthorization();
        WxSessionVO session = checkAuthority(wxOpenId, authorization);
        String loginID = dto.getLoginID();
        if (!Objects.equals(loginID, session.getUserId()) && !Objects.equals(loginID, session.getUserMobile())) {
            throw new MSBizNormalException("", "只能解绑当前登录用户！");
        }
        String userType = session.getUserType();
        if (Objects.equals(userType, SmConstants.USER_TYPE_AGENT)) {
            String customerNo = customerCenterService.login(loginID, dto.getLoginPwd());
            if (Objects.isNull(customerNo)) {
                throw new MSBizNormalException(ExcptEnum.WEIXIN_UNBIND_ERROR_601009.getCode(), ExcptEnum.WEIXIN_UNBIND_ERROR_601009.getMsg());
            }
        } else {
            // 信贷系统验证用户有效性
            try {
                FdAccountAuthDTO fdDTO = new FdAccountAuthDTO();
                fdDTO.setAccount(dto.getLoginID());
                fdDTO.setPassword(dto.getLoginPwd());
                bmsService.getAuthToken(fdDTO);
            } catch (BizException e) {
                log.warn("验证用户有效性失败", e);
                throw new MSBizNormalException(ExcptEnum.WEIXIN_UNBIND_ERROR_601009.getCode(), ExcptEnum.WEIXIN_UNBIND_ERROR_601009.getMsg());
            }

        }
        //解绑
        userService.unbindUserWx(session.getWxOpenId());
    }

    /**
     * 创建微信session
     *
     * @param code
     * @param channel
     * @return
     */
    public WxSessionVO createWxSession(String code, String channel) {
        String wxOpenId = wxServiceProxy.getWeixnUserInfo(code, channel);
        return reloadWxSession(wxOpenId, null, channel, null);
    }

    /**
     * 创建微信session
     *
     * @param code
     * @return
     */
    public WxSessionVO createWxSessionByBizCode(String code, String bizCode) {
        String wxOpenId = wxServiceProxy.getWeixnUserInfo(code, null);
        WxUserVO userByBizCode = userService.getUserByBizCode(bizCode);
        String sid = null;
        if (Objects.nonNull(userByBizCode)) {
            sid = userByBizCode.getId() + "";
        }
        return reloadWxSession(wxOpenId, null, null, sid);
    }

    /**
     * 创建微信session
     *
     * @param code
     * @param channel
     * @param sid
     * @return
     */
    public WxSessionVO createWxSession(String code, String channel, String sid) {
        String wxOpenId = wxServiceProxy.getWeixnUserInfo(code, channel);
        log.info("[微信授权]-获取OpenId:[{}],Code:[{}],Channel:[{}] ", wxOpenId, code, channel);
        return reloadWxSession(wxOpenId, null, channel, sid);
    }

    /**
     * 微信用户切换
     *
     * @param userId
     * @return
     */
    public WxSessionVO switchWxUser(String userId, String jobCode, String orgPath, String wxOpenId, String authorization) {
        WxSessionVO session = checkAuthorityEpAg(wxOpenId, authorization);
        //modify by zhangjian 2020-07-09 统一工号改造
        UserPost userPost = userService.getUserPostByJobCode(jobCode);
        log.info("jobCode = {}", jobCode);
        //log.info("wxOpenId = {}",wxOpenId);
        //log.info("session.getWxUsers() = {}",session.getWxUsers());
        if (userPost != null) {
            if (!Objects.equals(userPost.getOrgPath(), orgPath)) {
                throw new BizException(ExcptEnum.JOB_CODE_ORG_PATH_NOT_MATCH);
            }
            String jobNumber = session.getUserId();
            Integer hrOrgId = Integer.valueOf(orgPath.substring(orgPath.lastIndexOf("/") + 1));
            String token = bmsService.switchBranch(authorization, hrOrgId, 3).getToken();
            session.setAuthorization(token);
            if (StringUtils.isEmpty(session.getWxOpenId())) {
                session.setWxOpenId(wxOpenId);
            }
            Integer authUserId = null;
            if (jobNumber.equals(userPost.getJobNumber())) {
                authUserId = session.getId();
                convertUserPostToWxUserVO(userPost, session);

            } else {
                WxUserVO wxUser = session.getWxUsers().stream()
                        .filter(w -> Objects.equals(w.getUserId(), userId)).findFirst().get();
                authUserId = wxUser.getId();
                convertUserPostToWxUserVO(userPost, wxUser);
                BeanUtils.copyProperties(wxUser, session);
            }
            //获取区域办公室对应的区域名称 2020-11-13 add by zhangjian
            session.setBranchRegionName(getBranchRegionNameBySession(session));
            //更新 切换组织时间
            userService.updateSwitchTime(authUserId, jobCode, userPost.getPostCode(), userPost.getPostName());
            // 如果是渠道PCO设置session中显示理赔资料身子权限
            setShowClaimProcessApproval(session);
            redisUtil.set(wxOpenId, JSON.toJSONString(session));

            return session;
        } else {
            List<FdSimpleUserVO> users = getWxUserBindJobNumbers(wxOpenId, authorization);
            Optional<FdSimpleUserVO> optional2 = users.stream().filter(w -> Objects.equals(w.getJobNumber(), userId) && Objects.equals(w.getOrgPath(), orgPath)).findFirst();
            if (optional2.isPresent()) {
                FdSimpleUserVO wxUser = optional2.get();
                //2020-04-26 add by zhangjian 将新的token设置到authorization
                log.info("wxUser.getOrgPath()={}", wxUser.getOrgPath());
                Integer hrOrgId = Integer.valueOf(wxUser.getOrgPath().substring(wxUser.getOrgPath().lastIndexOf("/") + 1));
                UserLoginVO userLoginVO = bmsService.switchBranch(authorization, hrOrgId, 3);

                //add by zhangjian 2020-07-08 统一工号 保存未同步的岗位信息
                AuthUserVO vo = userSyncService.syncSingleLoginUser(userLoginVO, wxUser.getMobile(), jobCode);
                copyProperties(wxUser, session);
                session.setJobCode(jobCode);
                //更新 切换组织时间
                if (vo != null) {
                    session.setId(vo.getId());
                    userService.updateSwitchTime(vo.getId(), jobCode, wxUser.getPostCode(), wxUser.getPostName());
                }
                addWxUserList(session, wxUser);

                String token = userLoginVO.getToken();
                session.setAuthorization(token);

                //获取区域办公室对应的区域名称 2020-11-13 add by zhangjian
                session.setBranchRegionName(getBranchRegionNameBySession(session));

                // 如果是机构对接人设置session中显示理赔资料身子权限
                setShowClaimProcessApproval(session);
                redisUtil.set(wxOpenId, JSON.toJSONString(session));
                return session;
            }
        }
        /*Optional<WxUserVO> optional = session.getWxUsers().stream()
                .filter(w -> Objects.equals(w.getUserId(), userId) && Objects.equals(w.getOrgPath(), orgPath)).findFirst();
        log.info("session.getWxUsers()={}",JSON.toJSONString(session.getWxUsers()));
        log.info("229orgPath={}",orgPath);
        if (optional.isPresent()) {
            WxUserVO wxUser = optional.get();
            //add by zhangjian 2020-07-08 统一工号
            getCurrUserPostInfo(jobCode,wxUser);

            BeanUtils.copyProperties(wxUser, session);
            //2020-04-14 add by zhangjian 将新的token设置到authorization
            log.info("wxUser.getOrgPath()={}",wxUser.getOrgPath());
            Integer hrOrgId = Integer.valueOf(wxUser.getOrgPath().substring(wxUser.getOrgPath().lastIndexOf("/")+1));
            String token = bmsService.switchBranch(authorization,hrOrgId,3).getToken();
            session.setAuthorization(token);
            if (StringUtils.isEmpty(session.getWxOpenId())){
                session.setWxOpenId(wxOpenId);
            }
            // 如果是机构对接人设置session中显示理赔资料身子权限
            setShowClaimProcessApproval(session);
            redisUtil.set(wxOpenId, JSON.toJSONString(session));
            //更新 切换组织时间
            userService.updateSwitchTime(wxUser.getId(),jobCode);
            return session;
        } else {
            //log.info("245else");
            List<FdSimpleUserVO> users = getWxUserBindJobNumbers(wxOpenId, authorization);
            Optional<FdSimpleUserVO> optional2 = users.stream().filter(w -> Objects.equals(w.getJobNumber(), userId) && Objects.equals(w.getOrgPath(), orgPath)).findFirst();
            if (optional2.isPresent()) {
                FdSimpleUserVO wxUser = optional2.get();
                //2020-04-26 add by zhangjian 将新的token设置到authorization
                log.info("wxUser.getOrgPath()={}",wxUser.getOrgPath());
                Integer hrOrgId = Integer.valueOf(wxUser.getOrgPath().substring(wxUser.getOrgPath().lastIndexOf("/")+1));
                UserLoginVO userLoginVO = bmsService.switchBranch(authorization,hrOrgId,3);

                //add by zhangjian 2020-07-08 统一工号 保存未同步的岗位信息
                userSyncService.syncSingleLoginUser(userLoginVO,wxUser.getMobile(),jobCode);
                copyProperties(wxUser, session);
                session.setJobCode(jobCode);

                String token = userLoginVO.getToken();
                session.setAuthorization(token);
                // 如果是机构对接人设置session中显示理赔资料身子权限
                setShowClaimProcessApproval(session);
                redisUtil.set(wxOpenId, JSON.toJSONString(session));
                return session;
            }
        }*/
        throw new BizException(ExcptEnum.ACCOUNT_NOT_EXIST);
    }

    private void addWxUserList(WxSessionVO session, FdSimpleUserVO wxUser) {
        if (session.getWxUsers() == null) {
            session.setWxUsers(new ArrayList<>());
        }
        WxUserVO newVo = new WxUserVO();
        copyProperties(wxUser, newVo);
        newVo.setId(session.getId());
        newVo.setWxImgUrl(session.getWxImgUrl());
        newVo.setWxNickName(session.getWxNickName());
        newVo.setWxImgUrl(session.getWxImgUrl());
        session.getWxUsers().add(newVo);
    }

    /**
     * 获取微信用户所员工有账号
     *
     * @param openId
     * @param authorization
     * @return
     */
    public List<FdSimpleUserVO> getWxUserBindJobNumbers(String openId, String authorization) {
        WxSessionVO session = checkAuthorityEmployee(openId, authorization);
        return bmsService.getContextUserDetail(session.getUserMobile());
    }

    /**
     * 修改微信用户邮箱
     *
     * @param dto
     * @return
     */
    public void updateWxUserEmail(WxUserEmailDTO dto) {
        WxSessionVO session = checkAuthority(dto.getOpenId(), dto.getAuthorization());
        userService.updateUserEmail(session.getId(), dto.getUserEmail());
        reloadWxSession(dto.getOpenId(), dto.getAuthorization(), session.getChannel(), null);
    }

    /**
     * 修改微信用户配置信息
     *
     * @param dto
     * @return
     */
    public void updateWxUserSetting(WxUserSettingDTO dto) {
        WxSessionVO session = checkAuthorityEpAg(dto.getOpenId(), dto.getAuthorization());
        dto.setUserId(session.getUserId());
        wusMapper.deleteUserShowCmsSetting(dto.getUserId(), dto.getStCode());
        wusMapper.insertUserShowCmsSetting(dto);
        reloadWxSession(dto.getOpenId(), dto.getAuthorization(), session.getChannel(), null);
    }

    /**
     * 获取微信用户头像信息base64字符串
     *
     * @param wxOpenId
     * @return
     */
    public String getWxUserImageBase64(String wxOpenId) {
        WxSessionVO session = getWxSession(wxOpenId);
        if (session != null) {
            try {
                if (StringUtils.isEmpty(session.getWxImgUrl())) {
                    return null;
                }
                byte[] bytes = wxDownloadService.getBytesFromUrl(session.getWxImgUrl());
                return Base64Util.encryptBASE64(bytes);
            } catch (Exception e) {
                log.error("", e);
            }
        }
        return null;
    }

    /**
     * 重新加载微信session,用于保险助手
     *
     * @param authorization
     * @return
     */
    public WxSessionVO reloadWxSession(String authorization,UserDetailVO userDetailVO){

        log.info("authorization={}", authorization);
        WxUserVO wxUser = userService.getWxUsersByUserId(userDetailVO.getJobNumber());
        // 数据库没有微信openid 信息
        if (wxUser == null) {
            throw new BizException(ExcptEnum.INVALID_TOKEN_501019);
        }

        WxSessionVO session = new WxSessionVO();
        session.setBmsSystemId(bmsWxConfig.getSystemId());

        //todo 统一工号改造需要修改点 2020-07-08 add by zhangjian
        getCurrUserPostInfo(wxUser.getJobCode(), wxUser);
        BeanUtils.copyProperties(wxUser, session);
        //获取区域办公室对应的区域名称 2020-11-13 add by zhangjian
        session.setBranchRegionName(getBranchRegionNameBySession(session));
        session.setWxUsers(Arrays.asList(wxUser));

        // 是否显示微信推广费比例显示
        WxUserSettingVO settingVO = wusMapper.getUserShowCmsSetting(session.getUserId(), SmConstants.WX_USER_SETTING_CODE_CMS);
        if (settingVO != null) {
            session.setShowCmsRatio(Boolean.valueOf(settingVO.getStValue()));
        } else {
            session.setShowCmsRatio(Boolean.FALSE);
        }

        /*
         *  按角色处理逻辑
         */
        // 微信绑定内部员工
        if (session.isBindEmployee()) {
            // 内部员工默认已经实名认证
            session.setRealVerify(Boolean.TRUE);

            // 内部员工已经开通过代理人角色
            if (session.getAgentId() != null && session.getAgentId() > 0) {
                session.setAgentType(SmAgentVO.AGENT_TYPE_EMPLOYEE);
                SmAgentVO agentVO = agentMapper.getAgentByAgentId(session.getAgentId());
                if (agentVO != null) {
                    session.setTeamId(agentVO.getTeamId());
                    session.setTeamName(agentVO.getTeamName());
                }
            }

            // 内部员工离职
            if (Objects.equals(session.getStatus(), SmConstants.EMPLOYEE_STATUS_LEASE)) {
                // 离职内部员工角色默认为微信用户
                session.setUserId(null);
                if (session.getAgentId() != null && session.getAgentId() > 0) {
                    session.setUserType(AuthUserVO.USER_TYPE_AGENT);
                } else {
                    session.setUserType(AuthUserVO.USER_TYPE_WEIXIN);
                }
            }

            session.setBmsRoleCode(bmsWxConfig.getEmployeeCode());
        }
        // 授权码加载
        if (!StringUtils.isEmpty(authorization)) {
            session.setAuthorization(authorization);
        } else {
            session.setAuthorization(getAuthTokenByWechatV2(session));
        }
        if (session.isBindEmployee()) {
            setShowClaimProcessApproval(userDetailVO,session);
        }
        redisUtil.set(USER_TOKEN +authorization, JSON.toJSONString(session), Long.valueOf(2*60 * 60) );
        log.info("login user session:{}", JSON.toJSONString(session));
        return session;
    }
    /**
     * 重新加载微信session
     *
     * @param wxOpenId
     * @param authorization 微信token
     * @param channel
     * @param sid
     * @return
     */
    public WxSessionVO reloadWxSession(String wxOpenId, String authorization, String channel, String sid) {

        //判断是否黑名单用户
        if (wxBlacklistService.isBlack(wxOpenId)) {
            wxBlacklistService.loginLog(wxOpenId);
            throw new BizException(ExcptEnum.WX_BLACK.getCode(), "登录失败");
        }
        log.info("authorization={}", authorization);
        List<WxUserVO> wxUsers = userService.getWxUsersByWxOpenId(wxOpenId);
        // 数据库没有微信openid 信息
        if (wxUsers.isEmpty()) {
            throw new BizException(ExcptEnum.INVALID_TOKEN_501019);
        }

        // 如果是正式员工 只取员工身份信息（有userId）
        if (wxUsers.stream().anyMatch(w -> !StringUtils.isEmpty(w.getUserId()))) {
            wxUsers = wxUsers.stream().filter(w -> !StringUtils.isEmpty(w.getUserId())).collect(Collectors.toList());
        }

        WxSessionVO session = new WxSessionVO();
        session.setBmsSystemId(bmsWxConfig.getSystemId());

        WxUserVO vo = wxUsers.stream().sorted(Comparator.comparing(WxUserVO::getSwitchTime, Comparator.nullsFirst(Date::compareTo))
                .reversed().thenComparing(WxUserVO::getServiceType, Comparator.nullsLast(Integer::compareTo))).findFirst().get();
        //todo 统一工号改造需要修改点 2020-07-08 add by zhangjian
        getCurrUserPostInfo(vo.getJobCode(), vo);


        BeanUtils.copyProperties(vo, session);
        if (StringUtils.isEmpty(session.getWxOpenId())) {
            session.setWxOpenId(wxOpenId);
        }
        //获取区域办公室对应的区域名称 2020-11-13 add by zhangjian
        session.setBranchRegionName(getBranchRegionNameBySession(session));
        session.setWxUsers(wxUsers);

        // 乡助微服务绑定的是多个渠道来源产品， 需要用system渠道具体哪个公众号
        if (!StringUtils.isEmpty(channel)) {
            session.setChannel(channel);
            session.setSystem(channel);
        }
        // 是否显示微信推广费比例显示
        WxUserSettingVO settingVO = wusMapper.getUserShowCmsSetting(session.getUserId(), SmConstants.WX_USER_SETTING_CODE_CMS);
        if (settingVO != null) {
            session.setShowCmsRatio(Boolean.valueOf(settingVO.getStValue()));
        } else {
            session.setShowCmsRatio(Boolean.FALSE);
        }

        /*
         *  按角色处理逻辑
         */
        // 微信绑定内部员工
        if (session.isBindEmployee()) {
            // 内部员工默认已经实名认证
            session.setRealVerify(Boolean.TRUE);

            // 内部员工已经开通过代理人角色
            if (session.getAgentId() != null && session.getAgentId() > 0) {
                session.setAgentType(SmAgentVO.AGENT_TYPE_EMPLOYEE);
                SmAgentVO agentVO = agentMapper.getAgentByAgentId(session.getAgentId());
                if (agentVO != null) {
                    session.setTeamId(agentVO.getTeamId());
                    session.setTeamName(agentVO.getTeamName());
                }
            }

            // 内部员工离职
            if (Objects.equals(session.getStatus(), SmConstants.EMPLOYEE_STATUS_LEASE)) {
                // 离职内部员工角色默认为微信用户
                session.setUserId(null);
                if (session.getAgentId() != null && session.getAgentId() > 0) {
                    session.setUserType(AuthUserVO.USER_TYPE_AGENT);
                } else {
                    session.setUserType(AuthUserVO.USER_TYPE_WEIXIN);
                }
            }

            session.setBmsRoleCode(bmsWxConfig.getEmployeeCode());
        }
        // 微信绑定代理人
        else if (session.isBindAgent()) {
            session.setUserId(session.getAgentTopUserId());
            if (session.getRealVerify() == null) {
                session.setRealVerify(Boolean.FALSE);
            }
            // 代理人种类
            SmAgentVO agentVO = agentMapper.getAgentByAgentId(session.getAgentId());
            if (agentVO != null) {
                session.setAgentType(agentVO.getAgentType());
                session.setTeamId(agentVO.getTeamId());
                session.setTeamName(agentVO.getTeamName());
            }
            session.setBmsRoleCode(bmsWxConfig.getAgentCode());
        }
        // 微信普通用户
        else {
            // 通过分享链接过来需要保存session存分享人的信息
            if (sid != null) {
                AuthUserVO user = userService.getAuthUserById(sid);
                // 客户经理分享链接 或 者代理人分享链接
                if (user != null && !(Objects.equals(user.getUserType(), SmConstants.USER_TYPE_WEIXIN))) {
                    session.setUserId(user.getUserId());
                    session.setAgentId(user.getAgentId());
                    session.setRegionName(user.getRegionName());
                    session.setOrganizationName(user.getOrganizationName());
                    session.setBizCode(user.getBizCode());
                    // 如果是代理人推荐人取代理人上级工号
                    if (user.getAgentTopUserId() != null) {
                        session.setUserId(user.getAgentTopUserId());
                    }
                    // 分享来的员工离职
                    if (Objects.equals(user.getUserType(), SmConstants.USER_TYPE_EMPLOYEE)) {
                        if (Objects.equals(session.getStatus(), SmConstants.EMPLOYEE_STATUS_LEASE)) {
                            session.setUserId(null);
                        }
                    }
                }
            }
            session.setBmsRoleCode(bmsWxConfig.getNormalCode());
        }
        // 授权码加载
        if (!StringUtils.isEmpty(authorization)) {
            session.setAuthorization(authorization);
        } else {
            session.setAuthorization(getAuthTokenByWechatV2(session));
        }
        if (session.isBindEmployee()) {
            setShowClaimProcessApproval(session);
        }
        redisUtil.set(wxOpenId, JSON.toJSONString(session));
        log.info("login user session:{}", JSON.toJSONString(session));
        return session;
    }

    /**
     * 如果是机构主任 副主任 设置session中显示理赔资料身子权限
     *
     * @param session
     */
    private void setShowClaimProcessApproval(WxSessionVO session) {
        // 创新业务对接人
        //OrgPicExtraVO picExtra = userService.getOrgPicExtraByUserIdAndOrg(session.getUserId(),session.getOrganizationName());
//        OrgPicExtraVO picExtra = userService.getOrgPicExtraByUserIdAndOrg(session.getMainJobNumber(), session.getOrganizationName());
//        if (picExtra != null
//                && Objects.equals(session.getRegionName(), picExtra.getRegionName())
//                && Objects.equals(session.getOrganizationName(), picExtra.getOrgName())) {
//            session.setShowProcessApproval(Boolean.TRUE);
//        } else
        UserDetailVO userDetailVO = bmsService.getUserDetailByToken(session.getAuthorization());
        if (Objects.equals(session.getUserId(), headquartersApprover)) {
            session.setShowProcessApproval(Boolean.TRUE);
        } else if (permissionUtil.isChannelPCO(userDetailVO)) {
            //渠道PCO 添加权限
            session.setShowProcessApproval(Boolean.TRUE);
        } else {
            session.setShowProcessApproval(Boolean.FALSE);
        }
    }

    private void setShowClaimProcessApproval(UserDetailVO userDetailVO,WxSessionVO session) {
        if (Objects.equals(session.getUserId(), headquartersApprover)) {
            session.setShowProcessApproval(Boolean.TRUE);
        } else if (permissionUtil.isChannelPCO(userDetailVO)) {
            //渠道PCO 添加权限
            session.setShowProcessApproval(Boolean.TRUE);
        } else {
            session.setShowProcessApproval(Boolean.FALSE);
        }
    }

    /**
     * 更新微信session 推广精英排行版用户设置
     *
     * @param wxOpenId
     * @param showHomeSalesTop
     * @return
     */
    public void updateWxSessionTopSales(String wxOpenId, boolean showHomeSalesTop) {
        redisUtil.set(getSalesTopCahcheKey(wxOpenId), Boolean.valueOf(showHomeSalesTop).toString(), CommonUtil.getTodayNextSeconds());
    }

    /**
     * 查询微信session 推广精英排行版用户设置
     *
     * @param wxOpenId
     * @return
     */
    public Boolean getWxSessionTopSales(String wxOpenId) {
        String value = redisUtil.get(getSalesTopCahcheKey(wxOpenId));
        return value == null ? Boolean.TRUE : Boolean.valueOf(value);
    }


    /**
     * 获取推广精英排行版用户设置key
     *
     * @param wxOpenId
     * @return
     */
    private String getSalesTopCahcheKey(String wxOpenId) {
        return SmConstants.WX_SALES_TOP_EMPLOYEE_SETTING_CACHE_KEY + wxOpenId;
    }


    /**
     * 加载bms token到微信session中
     *
     * @param wxOpenId
     * @param authorization
     */
    public String loadBmsSession(String wxOpenId, String authorization) {
        try {
            WxSessionVO session = checkAuthorityEmployee(wxOpenId, authorization);
            //String bmsToken = SpringFactoryUtil.getBean(BmsService.class).getAuthTokenByWechat(session.getUserMobile(), session.getOrgPath());
            String bmsToken = null;
            String orgPath = session.getOrgPath();

            log.info("session.getOrgPath() = {}", session.getOrgPath());
            FdAuthVO authVO = SpringFactoryUtil.getBean(BmsService.class).getAuthTokenByWechatFromBms(session.getUserMobile());
            log.info("authVO.getBranchVO().getHrOrgTreePath() = {}", authVO.getBranchVO().getHrOrgTreePath());
            if (authVO.getBranchVO() != null && Objects.equals(authVO.getBranchVO().getHrOrgTreePath(), orgPath)) {
                bmsToken = authVO.getToken();
                session.setAuthorization(authVO.getToken());
            }
            if (bmsToken == null) {
                // 查询员工所有岗位
                List<FdSimpleUserVO> userJobs = bmsService.getContextUserDetail(session.getUserMobile());
                Optional<FdSimpleUserVO> optional = userJobs.stream().filter(uj -> Objects.equals(uj.getOrgPath(), orgPath)).findFirst();
                // 切换到当前岗位分支获取token
                if (optional.isPresent()) {
                    log.info("optional.get().getHrOrgId() = {}", optional.get().getHrOrgId());
                    //return switchBranch(authVO.getToken(), optional.get().getHrOrgId(), bmsConfig.getSystemId()).getToken();

                    UserLoginVO loginVO = bmsService.switchBranch(authVO.getToken(), optional.get().getHrOrgId(), 3);
                    String token = loginVO.getToken();
                    session.setAuthorization(token);
                    bmsToken = token;
                } else {
                    session.setAuthorization(authVO.getToken());
                    bmsToken = authVO.getToken();
                }
            }
            session.setBmsToken(bmsToken);
            redisUtil.set(wxOpenId, JSON.toJSONString(session));
            return bmsToken;
        } catch (Exception e) {
            throw new BizException(ExcptEnum.BMS_AUTH_ERROR_801001, e);
        }
    }

    /**
     * FdSimpleUserVO copy to WxUserVO
     *
     * @param fd
     * @param wx
     */
    private void copyProperties(FdSimpleUserVO fd, WxUserVO wx) {
        wx.setUserId(fd.getJobNumber());
        wx.setRegionName(fd.getRegionName());
        wx.setOrganizationFullName(fd.getOrgName());
        wx.setOrganizationName(fd.getOrgName());
        wx.setUserMobile(fd.getMobile());
        wx.setOrgPath(fd.getOrgPath());
        wx.setOrgCode(fd.getOrgCode());
        wx.setUserType(SmConstants.USER_TYPE_EMPLOYEE);
        wx.setMainJobNumber(fd.getMasterJobNumber());
        wx.setHrOrgId(String.valueOf(fd.getHrOrgId()));
    }

    /**
     * 生成微信authorization授权字符串
     *
     * @return
     */
    private String generateWxAuthorization(String wxOpenId) {
        String seed = wxOpenId + DateUtil.getTodayString();
        return UUID.nameUUIDFromBytes(seed.getBytes(StandardCharsets.UTF_8)).toString().replaceAll("-", "");
    }

    /**
     * 获取bms保险角色token
     *
     * @param session
     */
    public String getAuthTokenByWechatV2(WxSessionVO session) {
        FdWechatV2DTO dto = new FdWechatV2DTO();
        // 用bms 岗位表存session信息可以被其他保险系统调用
        InsUserInfo keepDataDTO = new InsUserInfo();
        BeanUtils.copyProperties(session, keepDataDTO);
        if (session.isBindEmployee()) {
            dto.setAccount(session.getUserMobile());
            dto.setUserType(WxConstant.BMS_USER_INS_WX_EMPLOYEE);
            dto.setUserName(session.getUserName());
            dto.setJobNumber(session.getUserId());
        } else if (session.isBindAgent()) {
            dto.setAccount(session.getUserMobile());
            dto.setUserType(WxConstant.BMS_USER_INS_WX_AGENT);
            dto.setUserName(session.getUserName());
            dto.setJobNumber(session.getAgentId().toString());
        } else if (session.isBindWeixin()) {
            dto.setUserType(WxConstant.BMS_USER_INS_WX_NORMAL);
            dto.setAccount(session.getWxOpenId());
            dto.setUserName(session.getWxNickName());
        }
        return bmsService.getAuthTokenByWechatV2(dto);
    }

    private void getCurrUserPostInfo(String jobCode, WxUserVO wxUserVO) {
        UserPost userPost = userService.getUserPostByJobCode(jobCode);
        convertUserPostToWxUserVO(userPost, wxUserVO);
    }

    /**
     * @param post
     * @param wxUserVO
     */
    public void convertUserPostToWxUserVO(UserPost post, WxUserVO wxUserVO) {
        if (post == null) {
            return;
        }
        wxUserVO.setOrgPath(post.getOrgPath());
        wxUserVO.setRegionName(post.getRegionName());
        wxUserVO.setPostCode(post.getPostCode());
        wxUserVO.setOrganizationFullName(post.getOrgName());
        wxUserVO.setServiceType(post.getServiceType());
        wxUserVO.setStatus(String.valueOf(post.getEmployeeStatus()));
        wxUserVO.setOrganizationName(post.getOrganizationName());
        wxUserVO.setOrgCode(post.getOrgCode());
        wxUserVO.setJobCode(post.getJobCode());
        wxUserVO.setMainJobNumber(post.getMainJobNumber());
        wxUserVO.setHrOrgId(post.getHrOrgId());
    }

    public String getBranchRegionName(String openId, String authorization) {
        WxSessionVO session = checkAuthorityEmployee(openId, authorization);
        return getBranchRegionNameBySession(session);
    }

    private String getBranchRegionNameBySession(WxSessionVO session) {
        /*String orgFullName = session.getOrganizationFullName();
        if(super.isAreaOffice(session.getHrOrgId())){
            return super.getBranchOrgName(session.getOrgCode());
        } else {*/
        return session.getRegionName();
        // }
    }

    /**
     * 创建微信session
     * @param userId
     * @return
     */
    public String createWxSession(String userId) {
        //String userMobile, String userName,
        log.info("小程序和公众号token互通 createWxSession userId= {}",userId);
        AuthUserVO authUserVO = userService.getAuthUserByUserId(userId);
        if(Objects.isNull(authUserVO)){
            log.warn("小程序和公众号token互通:查无用户 userId= {}", userId);
            throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(), "查无用户");
        }
        //人员状态 1.待入职 2.试用 3.正式 4.调出 5.待调入 6.退休 8.离职 12.非正式
        if(Objects.equals(8,authUserVO.getStatus())){
            log.warn("小程序和公众号token互通:员工已离职 authUserVO= {}", JSON.toJSONString(authUserVO));
            throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(), "员工已离职");
        }
        WxSessionVO session = new WxSessionVO();
        //小程序和公众号token互通,用户默认为员工
        session.setUserType(SmConstants.USER_TYPE_EMPLOYEE);
        session.setUserId(authUserVO.getUserId());
        session.setUserMobile(authUserVO.getUserMobile());
        session.setUserName(authUserVO.getUserName());
        //获取公众号token
        String token = getAuthTokenByWechatV2(session);
        //获取用户信息
        UserDetailVO userDetailVO = SpringFactoryUtil.getBean(BmsService.class).getUserDetailByToken(token);
        wxCcUserService.reloadWxSession(token, userDetailVO);
        return token;
    }
}
