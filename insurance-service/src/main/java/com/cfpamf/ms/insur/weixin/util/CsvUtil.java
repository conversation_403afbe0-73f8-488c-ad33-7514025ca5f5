package com.cfpamf.ms.insur.weixin.util;

import com.cfpamf.common.ms.exception.MSException;
import com.opencsv.bean.CsvToBean;
import com.opencsv.bean.CsvToBeanBuilder;
import com.opencsv.bean.HeaderColumnNameMappingStrategy;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;


import java.io.*;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/3/8 15:14
 */
@Slf4j
public class CsvUtil {

    /**
     * 解析csv文件并转成bean
     *
     * @param file  csv文件
     * @param clazz 类
     * @param <T>   泛型
     * @return 泛型bean集合
     */
    public static <T> List<T> getCsvData(File file, Class<T> clazz)  {
        FileReader fileReader;
        try {
            fileReader = new FileReader(file);
        } catch (Exception e) {
            log.warn("解析csv数据异常", e);
            throw new MSException("", "解析csv数据异常");
        }

        HeaderColumnNameMappingStrategy<T> strategy = new HeaderColumnNameMappingStrategy<>();
        strategy.setType(clazz);

        CsvToBean<T> csvToBean = new CsvToBeanBuilder<T>(fileReader)
                .withSeparator(',')
                .withQuoteChar('\'')
                .withMappingStrategy(strategy).build();
        return csvToBean.parse();
    }

    /**
     * 解析csv文件并转成bean
     *
     * @param inputStream  字节流
     * @param clazz 类
     * @param <T>   泛型
     * @return 泛型bean集合
     */
    public static <T> List<T> getCsvData(InputStream inputStream, Class<T> clazz)  {
        HeaderColumnNameMappingStrategy<T> strategy = new HeaderColumnNameMappingStrategy<>();
        strategy.setType(clazz);
        CsvToBean<T> csvToBean = new CsvToBeanBuilder<T>(new InputStreamReader(inputStream))
                .withSeparator(',')
                .withQuoteChar('\'')
                .withFilter(line -> !(line.length == 1 && StringUtils.isBlank(line[0])))
                .withMappingStrategy(strategy).build();
        return csvToBean.parse();
    }


}
