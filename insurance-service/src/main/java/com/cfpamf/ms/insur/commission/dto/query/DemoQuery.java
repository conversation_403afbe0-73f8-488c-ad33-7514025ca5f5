package com.cfpamf.ms.insur.commission.dto.query;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 应用模块名称<p>
 * <p>
 * 代码描述<p>
 * <p>
 * Copyright: Copyright (C) 2021 CD Finance Management Co., Ltd. All rights reserved. <p>
 * <p>
 * Company: 中和农信项目管理有限公司<p>
 *
 * <AUTHOR>
 * @since 2021/9/13 3:11 PM
 */
@Data
@ApiModel(value="路由查询对象", description="ms-api网关路由配置表")
public class DemoQuery {
	@ApiModelProperty(value = "白名单URL")
	private String apiUrl;

	@ApiModelProperty(value = "是否可用 1-是 0-否", example = "1")
	private Integer isEnabled = 1;

	@ApiModelProperty(value = "网关类型")
	private String type;

	@ApiModelProperty(value = "环境", example = "ALL")
	private String env;

	@ApiModelProperty(value = "所属路由Id")
	private String routeId;

}
