package com.cfpamf.ms.insur.weixin.pojo.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * 微信代理人注册DTO
 *
 * <AUTHOR>
 **/
@Data
@ApiModel
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class WxAgentRegisterDTO extends WxAuthorizationDTO {

    /**
     * 手机号
     */
    @ApiModelProperty("手机号")
    private String agentMobile;

    /**
     * 验证码
     */
    @ApiModelProperty("验证码")
    private String verifyCode;

    /**
     * 密码
     */
    @ApiModelProperty("密码")
    private String password;

    /**
     * 邀请码
     */
    @ApiModelProperty("邀请码")
    private String parentAgentMobile;

    /**
     * 客户中心注册的ccUserNo
     */
    @ApiModelProperty(hidden = true)
    private String ccUserNo;
}
