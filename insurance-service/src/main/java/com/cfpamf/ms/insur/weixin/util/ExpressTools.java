package com.cfpamf.ms.insur.weixin.util;


import groovy.util.Eval;

import java.util.Objects;

/**
 * Groovy 表达式计算
 * @param <T>
 */
public class ExpressTools<T> {

    public static Object eval(String left,String right,String ope){
        String fixOpe = fixExpress(ope);
        String express = left+fixOpe+right;
        return Eval.me(express);
    }

    /**
     * 修正符号
     * @param operator
     * @return
     */
    private static String fixExpress(String operator){
        if(Objects.equals(operator,"=")){
            return "==";
        }
        return operator;
    }

    public static <T> T eval(String express,Class<T> t){
        Object data = Eval.me(express);
        if(t.isInstance(data)){
            return t.cast(data);
        }
        return null;
    }

}
