package com.cfpamf.ms.insur.weixin.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.cfpamf.ms.insur.admin.constant.SmConstants;
import com.cfpamf.ms.insur.admin.enums.EnumAiCard;
import com.cfpamf.ms.insur.admin.enums.aicheck.AiProcessType;
import com.cfpamf.ms.insur.admin.pojo.vo.AiCardRes;
import com.cfpamf.ms.insur.admin.pojo.vo.AiMyPolicy;
import com.cfpamf.ms.insur.base.util.HttpRequestUtil;
import com.cfpamf.ms.insur.weixin.constant.EnumOrderQueryKeyType;
import com.cfpamf.ms.insur.weixin.dao.safes.WxOrderMapper;
import com.cfpamf.ms.insur.weixin.pojo.query.ai.AiClaimListQuery;
import com.cfpamf.ms.insur.weixin.pojo.vo.WxSessionVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @Date 2024/12/5
 * @Version 1.0
 */
@Service
@Slf4j
public class AiCardService extends WxAbstractService {

    @Autowired
    private WxOrderMapper orderMapper;

    public AiCardRes<?> queryContent(AiClaimListQuery query) {

        try{
            String auth = "";
            if (StrUtil.isNotEmpty(HttpRequestUtil.getAuthToken())) {
                auth = HttpRequestUtil.getAuthToken();
            }

            if (StrUtil.isEmpty(auth)) {
                auth = query.getAuthorization();
            }

            WxSessionVO session = checkAuthority(null, auth);
            if (org.apache.commons.lang3.StringUtils.isNotBlank(query.getInsuredName())) {
                if (Pattern.matches("[\\w\\-]+", query.getInsuredName())) {
                    query.setInsuredType(EnumOrderQueryKeyType.INSURED_ID_CARD.getCode());
                } else {
                    query.setInsuredType(EnumOrderQueryKeyType.INSURED_NAME.getCode());
                }
            }
            query.setPayStatus(SmConstants.ORDER_STATUS_PAYED);

            query.setCustomerAdminId(session.getUserId());

            List<AiMyPolicy> wxPolicyList = orderMapper.listAiMyPolicyList(query);


            if (CollectionUtil.isEmpty(wxPolicyList)) {
                AiCardRes<String> result = EnumAiCard.constructTextInfo("未查询到相关保单的理赔记录，请确认输入的查询信息是否准确");
                result.setTarget("user");
                result.setType(AiProcessType.TEXT.getCode());
                return result;
            } else {
                AiCardRes<AiCardRes.AiCardResData> result = EnumAiCard.construct(EnumAiCard.MY_CLAIM);
                result.setTarget("user");
                result.getData().setData(StrUtil.format("帮你查到总共有{}份保险保单, 你想查看哪份保单呢？请点击下方卡片选择", wxPolicyList.size()));
                result.getData().getContent().setList(wxPolicyList);
                return result;
            }

        } catch(Exception e) {
            log.warn("AI查询保单理赔记录异常", e);
            AiCardRes<String> exceptionResult = EnumAiCard.constructTextInfo("未查询到相关保单的理赔记录");
            exceptionResult.setTarget("user");
            exceptionResult.setType(AiProcessType.TEXT.getCode());
            return exceptionResult;
        }
    }


}
