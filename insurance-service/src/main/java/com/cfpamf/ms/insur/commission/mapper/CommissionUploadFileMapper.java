package com.cfpamf.ms.insur.commission.mapper;

import com.cfpamf.ms.insur.base.dao.MyMappler;
import com.cfpamf.ms.insur.commission.po.CommissionUploadFilePO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @date 2022/12/22 11:12
 */
@Mapper
public interface CommissionUploadFileMapper extends MyMappler<CommissionUploadFilePO> {

    /**
     * 修改状态
     *
     * @param id
     * @param flag
     * @return
     */
    int updateFlagById(@Param("id") Integer id, @Param("flag") Integer flag);
}
