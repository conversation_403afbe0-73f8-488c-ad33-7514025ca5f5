package com.cfpamf.ms.insur.weixin.constant;

import lombok.Getter;

/**
 * 团险批改类型
 * [sm_order_endor]表的opType
 */
@Getter
public enum EnumGroupCorrectType {
    CORRECT_BASIC_MESSAGE(0,"基础信息更改"),
    INCREASE_MEMBER(1,"批增人员"),
    DECREASE_MEMBER(2,"批减人员"),
    COMBINE_CORRECT_MEMBER(3,"批增&批减人员"),
    REPLACE_MEMBER(4,"替换人员"),
    ;
    int code;
    String message;

    EnumGroupCorrectType(int code,String message){
        this.code=code;
        this.message=message;
    }


}
