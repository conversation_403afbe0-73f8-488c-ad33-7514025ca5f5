package com.cfpamf.ms.insur.weixin.common;

import com.cfpamf.ms.insur.admin.service.OrderNoGenerator;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 通过Redi序号发射器生成分布式Id
 * <AUTHOR>
public class IdGenerator {

    static OrderNoGenerator idGenerator;

    private static AtomicBoolean initFlag = new AtomicBoolean(Boolean.FALSE);

    public static synchronized void init(OrderNoGenerator generator) {
        if (!initFlag.get()) {
            idGenerator = generator;
            initFlag.set(Boolean.TRUE);
        }
    }

    /**
     * 根据渠道批量生成
     * @param channel
     * @param count
     * @return
     */
    public List<String> getBatchNo(@NotNull String channel, int count) {
        return idGenerator.getBatchNo(channel, count);
    }

    /**
     * 根据渠道获取单号
     *
     * @param channel
     * @return
     */
    public static String getNextNo(@NotNull String channel) {
        return idGenerator.getNextNo(channel);
    }

    /**
     * 获取UUID
     *
     * @return
     */
    public static String getUuid() {
        return UUID.randomUUID().toString().toLowerCase().replace("-", "");
    }

    @Data
    public static class ChannelOrderNo {
        private String orderNo;

        private String channel;
    }

}
