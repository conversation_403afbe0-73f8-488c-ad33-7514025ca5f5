package com.cfpamf.ms.insur.weixin.pojo.query;

import com.cfpamf.ms.insur.base.bean.Pageable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * @description: 四级分销推送成功列表查询参数
 * @author: lww
 **/
@Data
@ApiModel
public class DistributionCommissionQuery{

    /**
     * 批次号
     */
    @ApiModelProperty(value = "月结算批次号")
    private String monthBatchNo;

    /**
     * 第几页
     */
    @ApiModelProperty(value = "第几页")
    private Integer pageSize;

    /**
     * 每页记录数
     */
    @ApiModelProperty(value = "每页记录数")
    private Integer size;

    public Integer getOffset() {
        if (!Objects.isNull(pageSize) && !Objects.isNull(size)) {
            return size*(pageSize-1);
        }
        return 0;
    }

    /**
     * 偏移量
     */
    @ApiModelProperty(value = "偏移量")
    private Integer offset;
}
