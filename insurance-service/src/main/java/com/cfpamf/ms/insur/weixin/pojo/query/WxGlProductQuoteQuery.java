package com.cfpamf.ms.insur.weixin.pojo.query;

import com.cfpamf.ms.insur.base.bean.Pageable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 微信团险报价query
 *
 * <AUTHOR>
 */
@Data
@ApiModel
public class WxGlProductQuoteQuery extends Pageable {

    /**
     * 微信openId
     */
    @ApiModelProperty(value = "微信openId")
    private String openId;

    /**
     * 微信token
     */
    @ApiModelProperty(value = "微信token")
    private String authorization;

    /**
     * 用户Id
     */
    @ApiModelProperty(value = "用户Id", hidden = true)
    private String userId;

    /**
     * 代理人Id
     */
    @ApiModelProperty(value = "代理人Id", hidden = true)
    private Integer agentId;

    /**
     * 查询条件
     */
    @ApiModelProperty(value = "查询条件")
    private String keyword;

    /**
     * 产品Id
     */
    @ApiModelProperty(value = "产品Id", hidden = true)
    private Integer productId;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间", hidden = true)
    private Date startDate;

    /**
     * 限制条数
     */
    @ApiModelProperty(value = "限制条数", hidden = true)
    private Integer limitItem;
}
