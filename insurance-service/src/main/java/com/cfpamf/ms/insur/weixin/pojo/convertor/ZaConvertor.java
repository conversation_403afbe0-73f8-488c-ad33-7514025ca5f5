package com.cfpamf.ms.insur.weixin.pojo.convertor;

import com.alibaba.fastjson.JSON;
import com.cfpamf.cmis.common.utils.IdcardUtils;
import com.cfpamf.common.ms.util.DateUtil;
import com.cfpamf.ms.insur.admin.enums.EnumChannel;
import com.cfpamf.ms.insur.admin.external.zhongan.ZaApiProperties;
import com.cfpamf.ms.insur.admin.external.zhongan.util.FileUtils;
import com.cfpamf.ms.insur.admin.pojo.dto.product.DutyFactorDTO;
import com.cfpamf.ms.insur.admin.pojo.dto.product.SysDutyConfig;
import com.cfpamf.ms.insur.admin.pojo.vo.SmCommissionSettingVO;
import com.cfpamf.ms.insur.admin.service.SystemFileService;
import com.cfpamf.ms.insur.admin.util.LogUtil;
import com.cfpamf.ms.insur.base.util.LambdaUtils;
import com.cfpamf.ms.insur.base.util.LocalDateUtil;
import com.cfpamf.ms.insur.base.util.SpringFactoryUtil;
import com.cfpamf.ms.insur.weixin.common.IdGenerator;
import com.cfpamf.ms.insur.weixin.constant.za.EnumPayWay;
import com.cfpamf.ms.insur.weixin.pojo.dto.order.OfflinePayDTO;
import com.cfpamf.ms.insur.weixin.pojo.dto.order.OrderCoverageDTO;
import com.cfpamf.ms.insur.weixin.pojo.dto.policy.PolicyInvoice;
import com.cfpamf.ms.insur.weixin.pojo.vo.apply.common.*;
import com.cfpamf.ms.insur.weixin.pojo.vo.apply.zhongan.item.EndorInsured;
import com.cfpamf.ms.insur.weixin.pojo.vo.apply.zhongan.order.*;
import com.cfpamf.ms.insur.weixin.pojo.vo.apply.zhongan.request.*;
import com.cfpamf.ms.pay.facade.constant.PayTypeEnum;
import com.cfpamf.ms.pay.facade.vo.QueryOrderVO;
import com.cfpamf.ms.insur.weixin.pojo.vo.group.InvoiceVo;
import com.cfpamf.ms.insur.weixin.pojo.vo.group.PolicyItem;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cglib.beans.BeanCopier;
import org.springframework.web.bind.annotation.RequestBody;

import java.io.File;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Vo转换类
 */
@Slf4j
public class ZaConvertor {

    /**
     * 生成众安报价报文
     *
     * @param prop
     * @param dutys
     * @param req
     * @return
     */
    public static ZaGroupUnderwritingReq convertQuoteBean(ZaApiProperties prop,
                                                          List<SysDutyConfig> dutys,
                                                          GroupUnderwriting req) {
        ZaGroupUnderwritingReq data = new ZaGroupUnderwritingReq();

        String orderId =
                StringUtils.isBlank(req.getOrderId()) ? IdGenerator.getNextNo(EnumChannel.ZA.getCode()) : req.getOrderId();

        data.setChannelOrderNo(orderId);
        data.setChannelCode(prop.getGroupChannelCode());
        data.setProductCateCode(prop.getProductCateCode());
        data.setEffectiveDate(req.getStartTime());
        data.setExpireDate(req.getEndTime());
        data.setTotalPremium(req.getTotalAmount());

        data.setCoveragePeriodType(mapPeriodType(req.getValidPeriod()));
        data.setCoveragePeriod(mapPeriod(req.getValidPeriod()));
        data.setOrgHolder(convertOrgBean(req.getApplicant()));

        fullInsured(orderId, data, req);
        List<ProductReq> products = genVirtualProduct(orderId, req, dutys);
        data.setProductList(products);
        return data;
    }

    /**
     * 生成众安报价报文
     *
     * @param prop
     * @param dutys
     * @param req
     * @return
     */
    public static ZaGroupUnderwritingReq convertApplyBean(ZaApiProperties prop,
                                                          List<SysDutyConfig> dutys,
                                                          GroupUnderwriting req) {
        ZaGroupUnderwritingReq data = convertQuoteBean(prop, dutys, req);
        VatInvoice invoiceInfo = req.getInvoiceInfo();
        if (invoiceInfo != null) {
            data.setInvoiceInfo(invoiceInfo.create());
        }
        data.setHealthDeclarationList(data.getHealthDeclarationList());
        return data;
    }

    /**
     * 生成虚拟计划：虚拟计划编码为[订单Id+职业类别]
     *
     * @param orderId
     * @param req
     * @param dutys
     */
    private static List<ProductReq> genVirtualProduct(String orderId,
                                                      GroupUnderwriting req,
                                                      List<SysDutyConfig> dutys) {
        SmCommissionSettingVO commission = req.getCommission();
        BigDecimal commissionRate = commission != null ? commission.getSettlementProportion() : null;
        List<ProductReq> data = new ArrayList<>();
        ProductReq product = req.getProduct();
        List<Clause> cls = product.getClauseList();
        if (cls != null) {
            Map<String, String> dutyMap = LambdaUtils.safeToMap(dutys, SysDutyConfig::getCode, SysDutyConfig::getMainCode);
            for (Clause c : cls) {
                c.setCommissionRate(commissionRate == null ? null : commissionRate.toString());
                String baseClauseCode = dutyMap.get(c.getClauseCode());
                if (StringUtils.isNotBlank(baseClauseCode)) {
                    c.setBaseClauseCode(baseClauseCode);
                }
            }
        }
        List<GroupInsured> insureds = req.getInsuredList();
        Set<String> jobClass = insureds.stream()
                .map(GroupInsured::getOccupationGroup)
                .collect(Collectors.toSet());
        String suffix = "计划%s";
        for (String entry : jobClass) {
            ProductReq vr = product.clone();
            vr.setProductCode(orderId + entry);
            vr.setProductName(vr.getProductName() + String.format(suffix, entry));
            vr.setProfessionType(entry);
            vr.setHasSocialInsurance("N");
            data.add(vr);

        }
        return data;
    }

    /**
     * Vo转换
     *
     * @param prop
     * @param req
     * @return
     */
    public static ZaGroupUnderwritingReq convertGroupApplyBean(ZaApiProperties prop, Map<String, String> cvgRiskMap,
                                                               GroupUnderwriting req) {
        ZaGroupUnderwritingReq data = new ZaGroupUnderwritingReq();

        return data;
    }

    /**
     * 值 含义 1 无 2 年 3 月 4 日 5 保终身 6 保到某确定年龄
     *
     * @param validPeriod
     * @return
     */
    private static Long mapPeriod(String validPeriod) {
        if (StringUtils.isBlank(validPeriod)) {
            return null;
        }
        String s = validPeriod.substring(0, validPeriod.length() - 1);
        String numberReg = "^\\d+(\\.\\d*)?$";
        if (s.matches(numberReg)) {
            return Long.parseLong(s);
        }
        return null;
    }

    /**
     * 值 含义 1 无 2 年 3 月 4 日 5 保终身 6 保到某确定年龄
     *
     * @param validPeriod
     * @return
     */
    private static Integer mapPeriodType(String validPeriod) {
        if (StringUtils.isBlank(validPeriod)) {
            return null;
        }
        String type = validPeriod.substring(validPeriod.length() - 1);
        switch (type) {
            case "年":
                return Integer.valueOf(2);
            case "月":
                return Integer.valueOf(3);
            case "日":
                return Integer.valueOf(4);
            default:
                return 1;
        }
    }

    /**
     * 企业联系人
     *
     * @param applicant
     * @return
     */
    public static OrgHolder convertOrgBean(GroupApplicant applicant) {
        OrgHolder holder = new OrgHolder();
        holder.setOrgName(applicant.getPersonName());
        holder.setOrgCertType(applicant.getIdType());
        holder.setOrgCertNo(applicant.getIdNumber());
        holder.setContactPerson(applicant.getLinker());
        if (StringUtils.isBlank(applicant.getLinker())) {
            holder.setContactPerson(applicant.getCellPhone());
        }
        holder.setOrgProvinceCode(applicant.getProviceCode());
        holder.setOrgCityCode(applicant.getCityCode());
        holder.setOrgCountryCode(applicant.getCountryCode());
        holder.setOrgAddress(applicant.getAddress());
        holder.setContactMobile(applicant.getCellPhone());
        holder.setContactEmail(applicant.getEmail());
        return holder;

    }

    /**
     * 被保人信息 ★被保人的计划编码为[订单号+职业类别]
     *
     * @param req
     * @return
     */
    private static void fullInsured(String orderId,
                                    ZaGroupUnderwritingReq data,
                                    GroupUnderwriting req) {
        List<GroupInsured> insuredList = req.getInsuredList();
        Insured[] insureds = new Insured[insuredList.size()];
        Insured entry;
        for (int i = 0; i < insuredList.size(); i++) {
            entry = new Insured();
            GroupInsured ge = insuredList.get(i);
            entry.setProductCode(orderId + ge.getOccupationGroup());
            entry.setName(ge.getPersonName());
            entry.setCertType(ge.getIdType());
            entry.setCertNo(ge.getIdNumber());
            entry.setHasSocialInsurance("N");
            entry.setProfession(ge.getOccupationCode());
            entry.setRelationToMaster("1");
            insureds[i] = entry;
        }
        data.setInsuredList(insureds);
    }

    /**
     * 被保人信息
     *
     * @param insuredList
     * @return
     */
    private static String findHiProfession(GroupInsured[] insuredList) {
        String prof = "0";
        for (GroupInsured i : insuredList) {
            if (i.getOccupationGroup() != null && prof.compareTo(i.getOccupationGroup()) < 0) {
                prof = i.getOccupationGroup();
            }
        }
        return prof;
    }

    /**
     * 众安批改：身份证带字母的必须转为大写!!!
     *
     * @param productName
     * @param prop
     * @param req
     * @return
     */
    public static ZaGroupEndorReq convertGroupEndor(String productName, ZaApiProperties prop, GroupEndorsement req) {
        ZaGroupEndorReq endor = new ZaGroupEndorReq();
        endor.setApplyTime(DateUtil.format(new Date(), DateUtil.CN_LONG_FORMAT));
        endor.setEffectiveDate(req.chooseEffectiveTime());
        endor.setChannelCode(prop.getGroupChannelCode());
        endor.setGroupPolicyNo(req.getPolicyNo());
        endor.setPayAmount(req.getTotalPremium());
        List<EndorInsured> insuredPersons = new ArrayList<>();
        List<GroupInsured> ins = req.getInsuredList();

        for (GroupInsured entry : ins) {
            EndorInsured item = new EndorInsured();
            item.setPlanName(genVirtualPlanNameV2(req.getPolicyNo(),productName, entry.getOccupationGroup()));
            item.setName(entry.getPersonName());
            item.setGender(entry.getPersonGender());
            item.setCertType(entry.getIdType());
            item.setCertNo(entry.getIdNumber().toUpperCase());
            item.setBirthday(getBirthday(entry.getIdNumber()));
            item.setOpType(entry.getOpType());
            item.setMainInsuredRelation("1");
            item.setJobCode(entry.getOccupationCode());
            item.setEffectiveDate(
                    entry.getOpType() == 1 ? req.getAdditionEffectiveTime() : req.getReductionEffectiveTime());
            insuredPersons.add(item);
        }
        endor.setInsuredPersons(insuredPersons);
        return endor;
    }

    private static String getBirthday(String idNumber) {
        String birthday = IdcardUtils.getBirthByIdCard(idNumber);
        if (StringUtils.isNotBlank(birthday)) {
            Date birthday1 = LocalDateUtil.stringToUdate(birthday, "yyyyMMdd");
            return LocalDateUtil.format4Simple(birthday1);
        }
        return null;
    }

    /**
     * 众安团险的虚拟计划名
     *
     * @param productName
     * @param jobCode
     * @return
     */
    @Deprecated
    public static String genVirtualPlanNameV1(String productName, String jobCode) {
        String virtualPlanNameExp = productName + "计划%s";
        return String.format(virtualPlanNameExp, jobCode);
    }

    /**
     * 众安团险的虚拟计划名
     *
     * @param productName
     * @param jobCode
     * @return
     */
    public static String genVirtualPlanNameV2(String policyNo,String productName, String jobCode) {
        ZaApiProperties prop = SpringFactoryUtil.getBean(ZaApiProperties.class);
        String virtualPlanNameExp = "%s计划%s";
        if(prop!=null) {
            String v1ProductList = prop.getV1PolicyList();
            String v1ProductName = prop.getV1ProductName();
            if (StringUtils.isNotBlank(v1ProductList)) {
                String[] arr = v1ProductList.split(",");
                for (String a : arr) {
                    if (Objects.equals(policyNo, a)) {
                        return String.format(virtualPlanNameExp,v1ProductName, jobCode);
                    }
                }
            }
        }
        return String.format(virtualPlanNameExp, productName,jobCode);
    }

    /**
     * 众安团险的虚拟计划编码
     *
     * @param rawOrderId
     * @param jobCode
     * @return
     */
    public static String genVirtualPlanCode(String rawOrderId, String jobCode) {
        String virtualPlanCodeExp = rawOrderId + "_%s";
        return String.format(virtualPlanCodeExp, jobCode);
    }

    /**
     * 转换城通用的支付信息模型
     *
     * @param resp
     * @return
     */
    public static QueryOrderVO convert2CommonPay(ZaPayment resp) {
        QueryOrderVO vo = new QueryOrderVO();
        vo.setSourceOrderId(resp.getZaOrderNo());
        vo.setSourceSys(resp.getMerchantCode());
        vo.setOrderNo(resp.getOutTradeNo());
        String payType = convertPayType(resp.getPayChannelGroup());
        vo.setPayType(payType);
        vo.setPayMethod(resp.getChoicePay());
        vo.setPayTrxNo(resp.getZaOrderNo());
        vo.setBankPayTrxNo(resp.getThirdTradeNo());
        vo.setOrderAmount(resp.getOrderAmt());
        vo.setOrderTime(new Date());
        vo.setPayTime(resp.convertPayTime());
        vo.setPayStatus(resp.getPayStatus());
        return vo;
    }

    private static String convertPayType(String payType) {
        if (StringUtils.isNotBlank(payType)) {
            String[] pts = payType.split(",");
            return pts[0].toUpperCase();
        }
        return PayTypeEnum.WXPAY.getTypeCode();
    }

    /**
     * TODO VO转换
     *
     * @param req
     * @return
     */
    public static ZaInvoiceReq convertInvoiceBean(InvoiceVo req) {
        ZaInvoiceReq data = new ZaInvoiceReq();
        BeanCopier copier = BeanCopier.create(InvoiceVo.class, ZaInvoiceReq.class, false);
        copier.copy(req, data, null);
        log.info("data= {}; req= {}", JSON.toJSON(data),JSON.toJSON(req));
        data.setRecipientsEmail(req.getRecipientEmail());
        log.info("after data= {}; req= {}", JSON.toJSON(data), JSON.toJSON(req));

        BigDecimal invoiceAmount = req.calInvoiceAmount();
        invoiceAmount = invoiceAmount.setScale(2, RoundingMode.CEILING);
        data.setInvoiceAmount(invoiceAmount.toString());
        List<InvoiceEndorsementItem> items = new ArrayList<>();

        List<PolicyItem> policyItemList = req.getPolicyItemList();
        for (PolicyItem entry : policyItemList) {
            InvoiceEndorsementItem item = new InvoiceEndorsementItem();
            if (StringUtils.isNotBlank(entry.getEndorsementNo())) {
                item.setEndorsementNo(entry.getEndorsementNo());
            } else {
                item.setEndorsementNo(entry.getPolicyNo());
            }
            item.setEndorsementAmount(String.valueOf(entry.getAmount()));
            items.add(item);
        }
        data.setInvoiceEndorsementInfo(items);
        return data;
    }

    public static List<OrderCoverageDTO> convert2Coverage(List<Clause> clauses) {
        if (clauses == null) {
            return Collections.emptyList();
        }
        List<OrderCoverageDTO> data = new ArrayList<>();
        OrderCoverageDTO cvg = null;
        for (Clause c : clauses) {
            List<Liability> liabs = c.getLiabilityList();
            for (Liability l : liabs) {
                cvg = new OrderCoverageDTO();
                cvg.setSpcId(l.getSpcId());
                cvg.setSpcaId(l.getSpcaId());
                cvg.setCvgAmount(l.getLiabAmount());
                cvg.setCvgNotice(l.getCvgNotice());
                cvg.setCvgCode(l.getLiabCode());
                cvg.setRiskCode(c.getClauseCode());
                cvg.setCvgItemName(l.getCvgItemName());
                List<Factor> factors = l.getLiabFactorList();
                if (factors != null) {
                    List<DutyFactorDTO> dutyFactor = new ArrayList<>();
                    for (Factor f : factors) {
                        DutyFactorDTO flow = new DutyFactorDTO();
                        flow.setId(f.getId());
                        flow.setDutyCode(f.getFactorCode());
                        flow.setFactorValue(f.getFactorValue());
                        dutyFactor.add(flow);
                    }
                    cvg.setDutyFactor(dutyFactor);
                }
                data.add(cvg);
            }
        }
        return data;
    }
}
