package com.cfpamf.ms.insur.weixin.pojo.dto;

import com.cfpamf.ms.insur.admin.pojo.vo.AuthUserVO;
import com.cfpamf.ms.insur.base.bean.BmsUserResponse;
import com.cfpamf.ms.insur.weixin.pojo.vo.WxUserVO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.beans.BeanUtils;

/**
 * 微信用户绑定DTO
 *
 * <AUTHOR>
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class WxUserBindDTO extends WxUserDTO {

    /**
     * 区域
     */
    private String regionName;

    /**
     * 组织
     */
    private String organizationName;

    /**
     * 组织
     */
    private String organizationFullName;

    /**
     * 员工Id
     */
    private String userId;

    /**
     * 员工用户名
     */
    private String userName;

    /**
     * 员工手机号
     */
    private String userMobile;

    /**
     * 组织树
     */
    private String orgPath;

    /**
     * 岗位code
     */
    private String postCode;

    /**
     * 岗位名称
     */
    private String postName;

    /**
     * 批次号
     */
    private String batchNo;

    /**
     * 员工状态
     */
    private String employeeStatus;

    /**
     *
     */
    private String serviceType;

    /** begin add by zhangjian 2021-01-04**/
    private String jobCode;

    private Integer hrUserId;

    private Integer bmsUserId;

    private String mainJobNumber;

    private String bizCode;

    private String regionCode;

    private String userIdCard;

    private String orgCode;
    private Integer hrOrgId;

    /** end add by zhangjian 2021-01-04**/

    public WxUserBindDTO() {
        // 默认构造方法
    }

    public WxUserBindDTO(WxUserVO user) {
        BeanUtils.copyProperties(user, this);
    }

    public void setUserInfo(BmsUserResponse.UserDetail ud) {
        this.setOrganizationName(ud.getBranchName());
        this.setUserId(ud.getEmployeeID());
        this.setUserMobile(ud.getEmployeePhone());
        this.setUserName(ud.getEmployeeName());
        this.setUserType(AuthUserVO.USER_TYPE_EMPLOYEE);
    }
}