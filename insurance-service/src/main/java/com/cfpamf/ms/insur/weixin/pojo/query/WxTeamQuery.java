package com.cfpamf.ms.insur.weixin.pojo.query;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * 微信我的团队query
 *
 * <AUTHOR>
 **/
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class WxTeamQuery extends WxAuthPageQuery {

    /**
     * 代理人姓名或者手机号
     */
    private String agentName;

    /**
     * 团队Id
     */
    private Integer teamId;

    /**
     * 上级代理人Id
     */
    private Integer parentAgentId;
}
