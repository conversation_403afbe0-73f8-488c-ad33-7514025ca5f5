package com.cfpamf.ms.insur.weixin.constant;

import java.util.Objects;

public enum EnumChonghoPay {
    /**
     * 微信
     */
    wechat,
    /**
     * 支付宝
     */
    alipay,
    /**
     * 线上支付
     */
    online,
    /**
     * 线下转账
     */
    offline,
    /**
     * 未知
     */
    unknown;

    public static String mapper(String payway) {
        EnumChonghoPay[] pays = EnumChonghoPay.values();
        for (EnumChonghoPay pay : pays) {
            if (Objects.equals(pay.name(), payway)) {
                return pay.name();
            }
        }
        return unknown.name();
    }

}
