package com.cfpamf.ms.insur.weixin.pojo.query.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
@Data
public class OrderDetailQuery {
    private String id;

    private String policyNo;

    private String userId;

    private Integer agentId;

    private String channel;

    private String openId;

    private String customerAdminId;

    private String keyword;

    @ApiModelProperty("产品属性：团险，个险")
    private String productAttrCode;

    @ApiModelProperty("分享人（管护客户经理）的openId 不为空则认为是分享进来的")
    private String sharerId;

    private String fhOrderId;

}
