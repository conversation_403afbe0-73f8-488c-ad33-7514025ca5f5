package com.cfpamf.ms.insur.weixin.pojo.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 微信产品报价计划DTO
 *
 * <AUTHOR>
 **/
@Data
@ApiModel
public class WxGlProductQuotePlanDTO {

    /**
     * 报价结果Id
     */
    @ApiModelProperty("报价结果Id")
    private String resultId;

    /**
     * 客户名称
     */
    @ApiModelProperty("客户名称")
    private String customerName;

    /**
     * 微信openId
     */
    @ApiModelProperty("微信openId")
    private String openId;

    /**
     * 授权token
     */
    @ApiModelProperty("授权token")
    private String authorization;
}
