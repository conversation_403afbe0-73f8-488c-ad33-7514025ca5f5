package com.cfpamf.ms.insur.weixin.pojo.query;

import com.cfpamf.ms.insur.base.bean.Pageable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * 微信产品Query
 *
 * <AUTHOR>
 **/
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class WxProductQuery extends Pageable {

    /**
     * 微信openId
     */
    private String openId;

    /**
     * token
     */
    private String authorization;

    /**
     * 产品分类
     */
    private String categoryId;

    /**
     * 产品渠道
     */
    private String channel;

    /**
     * 区域树
     */
    private String orgPath;

    /**
     * 区域名称
     */
    private String orgName;

    /**
     * 系统
     */
    private String system;

    /**
     * 查询条件
     */
    private String keyword;

    /**
     * 保险公司Id
     */
    private Integer companyId;

    private Integer productId;
}
