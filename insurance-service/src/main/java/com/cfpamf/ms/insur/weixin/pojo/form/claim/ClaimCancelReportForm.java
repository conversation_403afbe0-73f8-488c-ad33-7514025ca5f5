package com.cfpamf.ms.insur.weixin.pojo.form.claim;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/1/17 14:23
 */
@ApiModel("注销理赔报案资料表单")
@Data
public class ClaimCancelReportForm {

    /**
     * 理赔号
     */
    @ApiModelProperty(value = "理赔号",hidden = true)
    private String  claimNo;
    /**
     * 注销报案原因
     */
    @ApiModelProperty(value = "注销报案原因")
    private String cancelReportCause;

    /**
     * 注销报案理由
     */
    @ApiModelProperty(value = "注销报案理由")
    private String cancelReportReason;

    /**
     * 凭证
     */
    @ApiModelProperty(value = "凭证")
    private List<String> vouchers;

    /**
     * 客户联系电话
     */
    @ApiModelProperty(value = "客户联系电话")
    @NotBlank(message = "客户联系电话不能为空")
    private String customerTelephone;

}
