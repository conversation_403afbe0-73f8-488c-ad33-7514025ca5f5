package com.cfpamf.ms.insur.weixin.pojo.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * @description: 微信订单更新DTO
 * @author: zhang<PERSON>i
 * @create: 2018-07-23 16:24
 **/
@Data
@ApiModel
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class WxOrderUpdDTO extends WxAuthorizationDTO {

    /**
     * 泛华订单Id
     */
    @ApiModelProperty("泛华订单Id")
    private String fhOrderId;
}
