package com.cfpamf.ms.insur.weixin.constant;

/**
 * <AUTHOR>
 */
public enum EnumEndor {
    TO_COMMIT(10, "待提交"),
    COMMITED(0, "已提交(待支付)"),
    PAYED(1, "已支付(待保司处理中)-基本不会有该状态，因为使用保司收银台，我们这边不知道用户是否已经支付"),
    EFFECT(2, "已生效"),

    FAIL(3, "批改失败"),

    CANCEL(-1, "已撤销"),
    DOING(20, "数据处理中(异步等待保司回调通知)");

    Integer code;
    String mark;

    EnumEndor(Integer code, String mark) {
        this.code = code;
        this.mark = mark;
    }

    public Integer getCode() {
        return code;
    }
}
