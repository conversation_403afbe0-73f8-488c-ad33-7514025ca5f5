package com.cfpamf.ms.insur.weixin.pojo.query;

import com.cfpamf.ms.insur.base.bean.Pageable;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.Date;

/**
 * 微信推广精英排行版查询VO
 *
 * <AUTHOR>
 **/
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class WxTseQuery extends Pageable {

    /**
     * 开始时间
     */
    @ApiModelProperty(hidden = true)
    private Date startDate;

    /**
     * 结束时间
     */
    @ApiModelProperty(hidden = true)
    private Date endDate;

    /**
     * 微信openId
     */
    @ApiModelProperty("微信openId")
    private String openId;

    /**
     * 授权token
     */
    @ApiModelProperty("授权token")
    private String authorization;

    /**
     * 排序方式
     */
    @ApiModelProperty(hidden = true)
    private String orderType;
}
