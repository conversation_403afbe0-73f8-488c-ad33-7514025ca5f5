package com.cfpamf.ms.insur.weixin.pojo.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 微信修改用户emailDTO
 *
 * <AUTHOR>
 */
@Data
@ApiModel
public class WxUserEmailDTO {

    /**
     * openId
     */
    @ApiModelProperty("openId")
    private String openId;

    /**
     * token
     */
    @ApiModelProperty("token")
    private String authorization;

    /**
     * 用户邮箱
     */
    @ApiModelProperty("用户邮箱")
    private String userEmail;
}
