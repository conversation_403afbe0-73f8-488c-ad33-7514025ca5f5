package com.cfpamf.ms.insur.weixin.pojo.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * 微信修改代理人父级代理人DTO
 *
 * <AUTHOR>
 **/
@Data
@ApiModel
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class WxAgentModifyParentAgentIdDTO extends WxAuthorizationDTO {

    /**
     * 代理人Id
     */
    @ApiModelProperty("代理人Id")
    private Integer agentId;

    /**
     * 父级代理人
     */
    @ApiModelProperty("父级代理人")
    private Integer parentAgentId;
}
