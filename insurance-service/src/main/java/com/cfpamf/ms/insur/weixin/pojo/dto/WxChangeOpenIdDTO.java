package com.cfpamf.ms.insur.weixin.pojo.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> 2020/7/29 15:27
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class WxChangeOpenIdDTO {

    Integer errcode;

    String errmsg;

    @JsonProperty("result_list")
    List<OpenidMapper> resultList;

    @Getter
    @Setter
    public static class OpenidMapper {

        @JsonProperty("ori_openid")
        String oriOpenid;
        @JsonProperty("new_openid")
        String newOpenid;

        @JsonProperty("err_msg")
        String errMsg;

        @JsonIgnore
        public boolean isSuccess() {
            return Objects.equals("ok", errMsg) && Objects.nonNull(newOpenid);
        }
    }

    @JsonIgnore
    public boolean isSuccess() {
        return Objects.equals(0, errcode);
    }
}
