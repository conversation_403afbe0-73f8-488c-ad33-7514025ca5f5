package com.cfpamf.ms.insur.weixin.service.dictionary;

import com.cfpamf.ms.insur.weixin.pojo.vo.company.CategoryVo;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class ChannelDictionaryService {

    public List<CategoryVo> listTkCategory(){
        List<CategoryVo> data = new ArrayList<>();
        data.add(new CategoryVo("100","机关、团体"));
        data.add(new CategoryVo("110","国家机关"));
        data.add(new CategoryVo("120","党政机关"));
        data.add(new CategoryVo("130","社会团体"));
        data.add(new CategoryVo("140","基层群众自治组织"));
        data.add(new CategoryVo("200","事业单位"));
        data.add(new CategoryVo("210","卫生事业"));
        data.add(new CategoryVo("220","体育事业"));
        data.add(new CategoryVo("230","社会事业"));
        data.add(new CategoryVo("240","教育事业"));
        data.add(new CategoryVo("250","文化艺术业"));
        data.add(new CategoryVo("260","广播电影电视业"));
        data.add(new CategoryVo("270","科学研究业"));
        data.add(new CategoryVo("280","综合技术服务业"));

        data.add(new CategoryVo("300","企业单位"));

        data.add(new CategoryVo("310","国有"));

        data.add(new CategoryVo("320","集体"));

        data.add(new CategoryVo("330","个体"));

        data.add(new CategoryVo("340","私有"));

        data.add(new CategoryVo("350","外资"));

        data.add(new CategoryVo("360","混合所有制"));
        data.add(new CategoryVo("900","其他"));
        return data;

    }

}
