package com.cfpamf.ms.insur.weixin.service.policy;

import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.ms.insur.admin.enums.EnumChannel;
import com.cfpamf.ms.insur.admin.external.ChannelOrderService;
import com.cfpamf.ms.insur.admin.external.whale.WhaleOrderServiceAdapterImpl;
import com.cfpamf.ms.insur.admin.external.zhongan.ZaOrderServiceAdapter;
import com.cfpamf.ms.insur.base.util.SpringFactoryUtil;

public class ServiceConfig {

    /**
     * 团险-获取对应渠道的ServiceBean
     * 目前泰康团险对接逻辑已经迁移到保险业务中台，后续所有的渠道对接都统一迁移到保险业务中台上
     *
     * @param channel
     * @return
     */
    public static ChannelOrderService getService(EnumChannel channel) {
        switch (channel) {
            case ZA:
                return SpringFactoryUtil.getBean(ZaOrderServiceAdapter.class);
            case TK_PAY:
            case XJ:
                return SpringFactoryUtil.getBean(WhaleOrderServiceAdapterImpl.class);
            default:
                throw new MSBizNormalException("-1", "当前渠道信息暂未开放该功能");
        }
    }

    /**
     * 获取对应渠道的ServiceBean
     *
     * @param channel
     * @return
     */
    public static ChannelOrderService getService(String channel) {
        EnumChannel enu = EnumChannel.getChannel(channel);
        if (enu == null) {
            throw new MSBizNormalException("-1", String.format("当前渠道信息暂未开放该功能，请检查:%s", channel));
        }
        return getService(enu);
    }


}
