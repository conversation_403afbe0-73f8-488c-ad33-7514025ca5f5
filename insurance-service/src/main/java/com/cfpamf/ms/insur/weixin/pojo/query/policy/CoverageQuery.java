package com.cfpamf.ms.insur.weixin.pojo.query.policy;

import com.cfpamf.ms.insur.weixin.pojo.vo.product.CoverageAmountVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class CoverageQuery {

    @ApiModelProperty("*责任表Id")
    private Integer spcId;

    @ApiModelProperty("*保障项目编码")
    private String cvgCode;

    @ApiModelProperty("*保障项目编码")
    private String cvgItemName;

    @ApiModelProperty("*保障项目编码")
    private String cvgNameDetail;

    @ApiModelProperty("*险种编码")
    private String riskCode;

    @ApiModelProperty("*责任保额信息")
    private CoverageAmountVo amount;

    @ApiModelProperty("*责任浮动因子")
    private List<DutyFactorFlowQuery> dutyFactor;

    public boolean selfCheck(){
        return amount!=null;
    }

}
