package com.cfpamf.ms.insur.weixin.pojo.query;

import com.cfpamf.ms.insur.base.bean.Pageable;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

/**
 * 微信理赔query
 *
 * <AUTHOR>
 **/
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class WxClaimQuery extends Pageable {

    /**
     * 用户Id
     */
    @ApiModelProperty(value = "用户Id", hidden = true)
    private String userId;

    /**
     * 代理人Id
     */
    @ApiModelProperty(value = "代理人Id", hidden = true)
    private Integer agentId;

    /**
     * 微信openId
     */
    @ApiModelProperty(value = "微信openId")
    private String openId;

    /**
     * 微信token
     */
    @ApiModelProperty(value = "微信token")
    private String authorization;

    /**
     * 被保人/保单号/投保人姓名
     */
    @ApiModelProperty(value = "被保人/保单号/投保人姓名")
    private String keyword;

    /**
     * 理赔状态
     */
    @ApiModelProperty(value = "理赔状态")
    private String claimState;

    /**
     * 理赔注销报案流程状态
     */
    @ApiModelProperty(value = "理赔注销报案流程状态")
    private String claimCancelReportState;
    /**
     * 渠道
     */
    @ApiModelProperty(value = "渠道")
    private String channel;
    /**
     * 查询种类（0 待我审核的 /1 我已审核的）
     */
    @ApiModelProperty(value = "查询种类（0 待我审核的 /1 我已审核的）")
    private Integer queryType;
    /**
     * 区域
     */
    @ApiModelProperty(value = "区域", hidden = true)
    private String regionName;
    /**
     * 机构
     */
    @ApiModelProperty(value = "机构", hidden = true)
    private String orgName;
    /**
     * 是否创新业务对接人
     */
    @ApiModelProperty(value = "是否创新业务对接人", hidden = true)
    private Boolean picRole;
}
