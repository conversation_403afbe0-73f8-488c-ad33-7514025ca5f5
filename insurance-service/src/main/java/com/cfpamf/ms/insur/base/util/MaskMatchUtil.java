package com.cfpamf.ms.insur.base.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import java.util.regex.Pattern;

/**
 * 脱敏数据匹配工具类
 * 用于匹配脱敏后的投保人姓名和车牌号，支持双向脱敏匹配
 * 使用字符串相似度算法和正则表达式匹配
 *
 * <AUTHOR>
 */
@Slf4j
public class MaskMatchUtil {

    /** 姓名匹配的相似度阈值 */
    private static final double NAME_SIMILARITY_THRESHOLD = 0.6;

    /** 车牌号匹配的相似度阈值 */
    private static final double PLATE_NUMBER_SIMILARITY_THRESHOLD = 0.7;

    /** 综合匹配的相似度阈值 */
    private static final double OVERALL_SIMILARITY_THRESHOLD = 0.7;

    /** 姓名在综合相似度中的权重 */
    private static final double NAME_WEIGHT = 0.6;

    /** 车牌号在综合相似度中的权重 */
    private static final double PLATE_NUMBER_WEIGHT = 0.4;

    /**
     * 匹配脱敏后的姓名（支持双向脱敏）
     *
     * @param name1 第一个姓名（可能脱敏）
     * @param name2 第二个姓名（可能脱敏）
     * @return 是否匹配
     */
    public static boolean matchMaskedName(String name1, String name2) {
        log.info("开始姓名匹配: '{}' vs '{}'", name1, name2);

        if (StringUtils.isEmpty(name1) || StringUtils.isEmpty(name2)) {
            log.info("姓名为空，匹配失败");
            return false;
        }

        // 如果完全相同，直接返回true
        if (name1.equals(name2)) {
            log.info("姓名完全相同，匹配成功");
            return true;
        }

        // 计算相似度
        double similarity = calculateSimilarity(name1, name2);
        boolean isMatch = similarity >= NAME_SIMILARITY_THRESHOLD;

        log.info("姓名匹配结果: 相似度={}, 阈值={}, 结果={}",
                similarity, NAME_SIMILARITY_THRESHOLD, isMatch ? "匹配" : "不匹配");

        return isMatch;
    }
    
    /**
     * 匹配脱敏后的车牌号（支持双向脱敏）
     *
     * @param plateNumber1 第一个车牌号（可能脱敏）
     * @param plateNumber2 第二个车牌号（可能脱敏）
     * @return 是否匹配
     */
    public static boolean matchMaskedPlateNumber(String plateNumber1, String plateNumber2) {
        log.info("开始车牌号匹配: '{}' vs '{}'", plateNumber1, plateNumber2);

        if (StringUtils.isEmpty(plateNumber1) || StringUtils.isEmpty(plateNumber2)) {
            log.info("车牌号为空，匹配失败");
            return false;
        }

        // 如果完全相同，直接返回true
        if (plateNumber1.equals(plateNumber2)) {
            log.info("车牌号完全相同，匹配成功");
            return true;
        }

        // 计算相似度
        double similarity = calculateSimilarity(plateNumber1, plateNumber2);
        boolean isMatch = similarity >= PLATE_NUMBER_SIMILARITY_THRESHOLD;

        log.info("车牌号匹配结果: 相似度={}, 阈值={}, 结果={}",
                similarity, PLATE_NUMBER_SIMILARITY_THRESHOLD, isMatch ? "匹配" : "不匹配");

        return isMatch;
    }
    
    /**
     * 综合匹配脱敏后的投保人姓名和车牌号（支持双向脱敏）
     *
     * @param inputName 输入的投保人姓名（可能脱敏）
     * @param inputPlateNumber 输入的车牌号（可能脱敏）
     * @param dbName 数据库中的投保人姓名（可能脱敏）
     * @param dbPlateNumber 数据库中的车牌号（可能脱敏）
     * @return 是否匹配
     */
    public static boolean matchMaskedPersonInfo(String inputName, String inputPlateNumber,
                                               String dbName, String dbPlateNumber) {
        log.info("开始综合匹配 - 输入: 姓名='{}', 车牌号='{}' vs 数据库: 姓名='{}', 车牌号='{}'",
                inputName, inputPlateNumber, dbName, dbPlateNumber);

        // 分别计算姓名和车牌号的相似度
        double nameSimilarity = calculateSimilarityWithDefault(inputName, dbName);
        double plateSimilarity = calculateSimilarityWithDefault(inputPlateNumber, dbPlateNumber);

        log.info("相似度计算结果 - 姓名相似度: {}, 车牌号相似度: {}", nameSimilarity, plateSimilarity);

        // 计算综合相似度（加权平均）
        double overallSimilarity = nameSimilarity * NAME_WEIGHT + plateSimilarity * PLATE_NUMBER_WEIGHT;

        boolean isMatch = overallSimilarity >= OVERALL_SIMILARITY_THRESHOLD;

        log.info("综合匹配结果 - 综合相似度: {} (阈值: {}), 匹配结果: {}",
                overallSimilarity, OVERALL_SIMILARITY_THRESHOLD, isMatch ? "匹配" : "不匹配");

        return isMatch;
    }

    /**
     * 综合匹配脱敏后的投保人信息（重载方法，兼容旧接口）
     *
     * @param inputName 输入的投保人姓名
     * @param inputIdNumber 输入的证件号（在车险场景下作为车牌号使用）
     * @param dbName 数据库中的投保人姓名
     * @param dbIdNumber 数据库中的证件号（在车险场景下作为车牌号使用）
     * @return 是否匹配
     */
    public static boolean matchMaskedCarInsuranceInfo(String inputName, String inputIdNumber,
                                                     String dbName, String dbIdNumber) {
        return matchMaskedPersonInfo(inputName, inputIdNumber, dbName, dbIdNumber);
    }
    
    /**
     * 计算两个字符串的相似度（支持双向脱敏）
     * 两个字符串都可能包含脱敏字符*，需要进行双向匹配
     *
     * @param str1 第一个字符串（可能包含脱敏字符*）
     * @param str2 第二个字符串（可能包含脱敏字符*）
     * @return 相似度（0.0-1.0）
     */
    private static double calculateSimilarity(String str1, String str2) {
        if (StringUtils.isEmpty(str1) || StringUtils.isEmpty(str2)) {
            log.debug("字符串为空: str1={}, str2={}", str1, str2);
            return 0.0;
        }

        log.debug("开始计算相似度: str1='{}', str2='{}'", str1, str2);

        // 如果完全相同，直接返回1.0
        if (str1.equals(str2)) {
            log.debug("字符串完全相同，相似度=1.0");
            return 1.0;
        }

        // 双向脱敏匹配：尝试两个方向的匹配
        double similarity1 = calculateDirectionalSimilarity(str1, str2);
        double similarity2 = calculateDirectionalSimilarity(str2, str1);

        // 取两个方向匹配的最大值
        double maxSimilarity = Math.max(similarity1, similarity2);
        log.debug("双向匹配结果: str1->str2={}, str2->str1={}, 最终相似度={}",
                 similarity1, similarity2, maxSimilarity);

        return maxSimilarity;
    }

    /**
     * 计算单向相似度（str1可能包含脱敏字符，str2作为参考）
     *
     * @param maskedStr 可能包含脱敏字符的字符串
     * @param referenceStr 参考字符串
     * @return 相似度（0.0-1.0）
     */
    private static double calculateDirectionalSimilarity(String maskedStr, String referenceStr) {
        log.debug("计算单向相似度: masked='{}', reference='{}'", maskedStr, referenceStr);

        // 方法1：正则表达式匹配
        if (isRegexMatch(maskedStr, referenceStr)) {
            log.debug("正则表达式匹配成功，相似度=1.0");
            return 1.0;
        }

        // 方法2：自定义相似度计算（要求长度相同）
        if (maskedStr.length() == referenceStr.length()) {
            double similarity = calculateCustomSimilarity(maskedStr, referenceStr);
            log.debug("自定义相似度计算结果: {}", similarity);
            return similarity;
        }

        // 方法3：编辑距离算法（适用于长度不同的情况）
        double similarity = calculateEditDistanceSimilarity(maskedStr, referenceStr);
        log.debug("编辑距离相似度计算结果: {}", similarity);
        return similarity;
    }
    
    /**
     * 使用正则表达式进行匹配
     * 将脱敏字符串中的'*'替换为'.'进行正则匹配
     * 
     * @param maskedStr 脱敏字符串
     * @param fullStr 完整字符串
     * @return 是否匹配
     */
    private static boolean isRegexMatch(String maskedStr, String fullStr) {
        try {
            // 将*替换为.，构建正则表达式
            String regex = maskedStr.replace("*", ".");
            Pattern pattern = Pattern.compile("^" + regex + "$");
            return pattern.matcher(fullStr).matches();
        } catch (Exception e) {
            // 如果正则表达式有问题，返回false
            return false;
        }
    }
    
    /**
     * 计算自定义相似度
     * 逐个字符比较，脱敏字符'*'作为通配符
     * 
     * @param maskedStr 脱敏字符串
     * @param fullStr 完整字符串
     * @return 相似度（0-1之间）
     */
    private static double calculateCustomSimilarity(String maskedStr, String fullStr) {
        // 如果长度不同，使用编辑距离算法
        if (maskedStr.length() != fullStr.length()) {
            return calculateEditDistanceSimilarity(maskedStr, fullStr);
        }
        
        int matchCount = 0;
        int totalComparableChars = 0;
        
        for (int i = 0; i < maskedStr.length(); i++) {
            char maskedChar = maskedStr.charAt(i);
            char fullChar = fullStr.charAt(i);
            
            if (maskedChar == '*') {
                // 脱敏字符，跳过不计入比较
                continue;
            }
            
            totalComparableChars++;
            if (maskedChar == fullChar) {
                matchCount++;
            }
        }
        
        // 如果没有可比较的字符，返回1（完全匹配）
        if (totalComparableChars == 0) {
            return 1.0;
        }
        
        return (double) matchCount / totalComparableChars;
    }
    
    /**
     * 使用编辑距离计算相似度
     * 适用于长度不同的字符串
     * 
     * @param str1 字符串1
     * @param str2 字符串2
     * @return 相似度（0-1之间）
     */
    private static double calculateEditDistanceSimilarity(String str1, String str2) {
        int maxLen = Math.max(str1.length(), str2.length());
        if (maxLen == 0) {
            return 1.0;
        }
        
        int editDistance = calculateEditDistance(str1, str2);
        return 1.0 - (double) editDistance / maxLen;
    }
    
    /**
     * 计算编辑距离（Levenshtein距离）
     * 考虑'*'作为通配符
     * 
     * @param str1 字符串1
     * @param str2 字符串2
     * @return 编辑距离
     */
    private static int calculateEditDistance(String str1, String str2) {
        int m = str1.length();
        int n = str2.length();
        
        int[][] dp = new int[m + 1][n + 1];
        
        // 初始化
        for (int i = 0; i <= m; i++) {
            dp[i][0] = i;
        }
        for (int j = 0; j <= n; j++) {
            dp[0][j] = j;
        }
        
        // 动态规划计算编辑距离
        for (int i = 1; i <= m; i++) {
            for (int j = 1; j <= n; j++) {
                char c1 = str1.charAt(i - 1);
                char c2 = str2.charAt(j - 1);
                
                if (c1 == c2 || c1 == '*' || c2 == '*') {
                    // 字符相同或其中一个是通配符
                    dp[i][j] = dp[i - 1][j - 1];
                } else {
                    // 字符不同，取最小操作数
                    dp[i][j] = 1 + Math.min(Math.min(dp[i - 1][j], dp[i][j - 1]), dp[i - 1][j - 1]);
                }
            }
        }
        
        return dp[m][n];
    }
    
    /**
     * 计算相似度，如果字符串为空则返回默认值
     * 
     * @param str1 字符串1
     * @param str2 字符串2
     * @return 相似度
     */
    private static double calculateSimilarityWithDefault(String str1, String str2) {
        if (StringUtils.isEmpty(str1) && StringUtils.isEmpty(str2)) {
            return 1.0; // 都为空，认为匹配
        }
        if (StringUtils.isEmpty(str1) || StringUtils.isEmpty(str2)) {
            return 0.0; // 一个为空一个不为空，不匹配
        }
        return calculateSimilarity(str1, str2);
    }
}
