package com.cfpamf.ms.insur.base.util;

import org.apache.commons.lang.StringUtils;

/**
 * 脱敏数据匹配工具类
 * 用于匹配脱敏后的姓名和证件号
 * 
 * <AUTHOR>
 */
public class MaskMatchUtil {

    /**
     * 匹配脱敏后的姓名
     * 脱敏规则：采用投保人姓名+年牌号模糊匹配，去除姓名、年牌号中脱敏的部分
     * 
     * @param inputName 输入的姓名（可能脱敏）
     * @param dbName 数据库中的姓名（可能脱敏）
     * @return 是否匹配
     */
    public static boolean matchMaskedName(String inputName, String dbName) {
        if (StringUtils.isEmpty(inputName) || StringUtils.isEmpty(dbName)) {
            return false;
        }
        
        // 如果完全相同，直接返回true
        if (inputName.equals(dbName)) {
            return true;
        }
        
        // 去除脱敏字符进行匹配
        String cleanInputName = removeMaskChars(inputName);
        String cleanDbName = removeMaskChars(dbName);
        
        // 如果去除脱敏字符后长度小于1，无法匹配
        if (cleanInputName.length() < 1 || cleanDbName.length() < 1) {
            return false;
        }
        
        // 检查非脱敏部分是否匹配
        return isNameMatch(cleanInputName, cleanDbName);
    }
    
    /**
     * 匹配脱敏后的证件号
     * 由于证件号可能是虚拟的，因此不能用身份证进行精确匹配
     * 
     * @param inputIdNumber 输入的证件号（可能脱敏）
     * @param dbIdNumber 数据库中的证件号（可能脱敏）
     * @return 是否匹配
     */
    public static boolean matchMaskedIdNumber(String inputIdNumber, String dbIdNumber) {
        if (StringUtils.isEmpty(inputIdNumber) || StringUtils.isEmpty(dbIdNumber)) {
            return false;
        }
        
        // 如果完全相同，直接返回true
        if (inputIdNumber.equals(dbIdNumber)) {
            return true;
        }
        
        // 由于证件号可能是虚拟的，不能进行精确匹配
        // 这里返回false，主要依靠姓名匹配
        return false;
    }
    
    /**
     * 综合匹配脱敏后的姓名和证件号
     * 
     * @param inputName 输入的姓名
     * @param inputIdNumber 输入的证件号
     * @param dbName 数据库中的姓名
     * @param dbIdNumber 数据库中的证件号
     * @return 是否匹配
     */
    public static boolean matchMaskedPersonInfo(String inputName, String inputIdNumber, 
                                               String dbName, String dbIdNumber) {
        // 主要依靠姓名匹配，证件号作为辅助
        boolean nameMatch = matchMaskedName(inputName, dbName);
        boolean idMatch = matchMaskedIdNumber(inputIdNumber, dbIdNumber);
        
        // 如果姓名匹配，则认为匹配成功
        // 如果姓名不匹配但证件号匹配，也认为匹配成功
        return nameMatch || idMatch;
    }
    
    /**
     * 去除字符串中的脱敏字符（*）
     * 
     * @param str 原字符串
     * @return 去除脱敏字符后的字符串
     */
    private static String removeMaskChars(String str) {
        if (StringUtils.isEmpty(str)) {
            return str;
        }
        return str.replaceAll("\\*", "");
    }
    
    /**
     * 检查两个姓名是否匹配
     * 匹配规则：
     * 1. 如果两个姓名去除脱敏字符后完全相同，匹配成功
     * 2. 如果一个姓名包含另一个姓名的所有字符，匹配成功
     * 
     * @param name1 姓名1
     * @param name2 姓名2
     * @return 是否匹配
     */
    private static boolean isNameMatch(String name1, String name2) {
        if (StringUtils.isEmpty(name1) || StringUtils.isEmpty(name2)) {
            return false;
        }
        
        // 完全相同
        if (name1.equals(name2)) {
            return true;
        }
        
        // 检查是否一个包含另一个
        if (name1.contains(name2) || name2.contains(name1)) {
            return true;
        }
        
        // 检查字符重叠度
        return hasCommonChars(name1, name2);
    }
    
    /**
     * 检查两个字符串是否有共同字符
     * 
     * @param str1 字符串1
     * @param str2 字符串2
     * @return 是否有共同字符
     */
    private static boolean hasCommonChars(String str1, String str2) {
        if (StringUtils.isEmpty(str1) || StringUtils.isEmpty(str2)) {
            return false;
        }
        
        for (char c : str1.toCharArray()) {
            if (str2.indexOf(c) >= 0) {
                return true;
            }
        }
        return false;
    }
}
