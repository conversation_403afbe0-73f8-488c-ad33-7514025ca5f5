package com.cfpamf.ms.insur.base.util;

import org.apache.commons.lang.StringUtils;
import java.util.regex.Pattern;

/**
 * 脱敏数据匹配工具类
 * 用于匹配脱敏后的姓名和证件号，支持脱敏位置不固定的情况
 * 使用字符串相似度算法和正则表达式匹配
 * 
 * <AUTHOR>
 */
public class MaskMatchUtil {

    /** 姓名匹配的相似度阈值 */
    private static final double NAME_SIMILARITY_THRESHOLD = 0.6;
    
    /** 证件号匹配的相似度阈值 */
    private static final double ID_NUMBER_SIMILARITY_THRESHOLD = 0.8;
    
    /** 综合匹配的相似度阈值 */
    private static final double OVERALL_SIMILARITY_THRESHOLD = 0.7;
    
    /** 姓名在综合相似度中的权重 */
    private static final double NAME_WEIGHT = 0.7;
    
    /** 证件号在综合相似度中的权重 */
    private static final double ID_NUMBER_WEIGHT = 0.3;

    /**
     * 匹配脱敏后的姓名
     * 使用字符串相似度算法和正则表达式匹配
     * 
     * @param maskedName 脱敏的姓名
     * @param fullName 完整的姓名
     * @return 是否匹配
     */
    public static boolean matchMaskedName(String maskedName, String fullName) {
        if (StringUtils.isEmpty(maskedName) || StringUtils.isEmpty(fullName)) {
            return false;
        }
        
        // 如果完全相同，直接返回true
        if (maskedName.equals(fullName)) {
            return true;
        }
        
        // 计算相似度
        double similarity = calculateSimilarity(maskedName, fullName);
        return similarity >= NAME_SIMILARITY_THRESHOLD;
    }
    
    /**
     * 匹配脱敏后的证件号
     * 
     * @param maskedIdNumber 脱敏的证件号
     * @param fullIdNumber 完整的证件号
     * @return 是否匹配
     */
    public static boolean matchMaskedIdNumber(String maskedIdNumber, String fullIdNumber) {
        if (StringUtils.isEmpty(maskedIdNumber) || StringUtils.isEmpty(fullIdNumber)) {
            return false;
        }
        
        // 如果完全相同，直接返回true
        if (maskedIdNumber.equals(fullIdNumber)) {
            return true;
        }
        
        // 计算相似度
        double similarity = calculateSimilarity(maskedIdNumber, fullIdNumber);
        return similarity >= ID_NUMBER_SIMILARITY_THRESHOLD;
    }
    
    /**
     * 综合匹配脱敏后的姓名和证件号
     * 
     * @param inputName 输入的姓名
     * @param inputIdNumber 输入的证件号
     * @param dbName 数据库中的姓名
     * @param dbIdNumber 数据库中的证件号
     * @return 是否匹配
     */
    public static boolean matchMaskedPersonInfo(String inputName, String inputIdNumber, 
                                               String dbName, String dbIdNumber) {
        // 分别计算姓名和证件号的相似度
        double nameSimilarity = calculateSimilarityWithDefault(inputName, dbName);
        double idSimilarity = calculateSimilarityWithDefault(inputIdNumber, dbIdNumber);
        
        // 计算综合相似度（加权平均）
        double overallSimilarity = nameSimilarity * NAME_WEIGHT + idSimilarity * ID_NUMBER_WEIGHT;
        
        return overallSimilarity >= OVERALL_SIMILARITY_THRESHOLD;
    }
    
    /**
     * 计算两个字符串的相似度
     * 考虑脱敏字符（*）作为通配符
     * 
     * @param maskedStr 脱敏字符串
     * @param fullStr 完整字符串
     * @return 相似度（0-1之间）
     */
    private static double calculateSimilarity(String maskedStr, String fullStr) {
        if (StringUtils.isEmpty(maskedStr) || StringUtils.isEmpty(fullStr)) {
            return 0.0;
        }
        
        // 方法1：正则表达式匹配
        if (isRegexMatch(maskedStr, fullStr)) {
            return 1.0;
        }
        
        // 方法2：自定义相似度计算
        return calculateCustomSimilarity(maskedStr, fullStr);
    }
    
    /**
     * 使用正则表达式进行匹配
     * 将脱敏字符串中的'*'替换为'.'进行正则匹配
     * 
     * @param maskedStr 脱敏字符串
     * @param fullStr 完整字符串
     * @return 是否匹配
     */
    private static boolean isRegexMatch(String maskedStr, String fullStr) {
        try {
            // 将*替换为.，构建正则表达式
            String regex = maskedStr.replace("*", ".");
            Pattern pattern = Pattern.compile("^" + regex + "$");
            return pattern.matcher(fullStr).matches();
        } catch (Exception e) {
            // 如果正则表达式有问题，返回false
            return false;
        }
    }
    
    /**
     * 计算自定义相似度
     * 逐个字符比较，脱敏字符'*'作为通配符
     * 
     * @param maskedStr 脱敏字符串
     * @param fullStr 完整字符串
     * @return 相似度（0-1之间）
     */
    private static double calculateCustomSimilarity(String maskedStr, String fullStr) {
        // 如果长度不同，使用编辑距离算法
        if (maskedStr.length() != fullStr.length()) {
            return calculateEditDistanceSimilarity(maskedStr, fullStr);
        }
        
        int matchCount = 0;
        int totalComparableChars = 0;
        
        for (int i = 0; i < maskedStr.length(); i++) {
            char maskedChar = maskedStr.charAt(i);
            char fullChar = fullStr.charAt(i);
            
            if (maskedChar == '*') {
                // 脱敏字符，跳过不计入比较
                continue;
            }
            
            totalComparableChars++;
            if (maskedChar == fullChar) {
                matchCount++;
            }
        }
        
        // 如果没有可比较的字符，返回1（完全匹配）
        if (totalComparableChars == 0) {
            return 1.0;
        }
        
        return (double) matchCount / totalComparableChars;
    }
    
    /**
     * 使用编辑距离计算相似度
     * 适用于长度不同的字符串
     * 
     * @param str1 字符串1
     * @param str2 字符串2
     * @return 相似度（0-1之间）
     */
    private static double calculateEditDistanceSimilarity(String str1, String str2) {
        int maxLen = Math.max(str1.length(), str2.length());
        if (maxLen == 0) {
            return 1.0;
        }
        
        int editDistance = calculateEditDistance(str1, str2);
        return 1.0 - (double) editDistance / maxLen;
    }
    
    /**
     * 计算编辑距离（Levenshtein距离）
     * 考虑'*'作为通配符
     * 
     * @param str1 字符串1
     * @param str2 字符串2
     * @return 编辑距离
     */
    private static int calculateEditDistance(String str1, String str2) {
        int m = str1.length();
        int n = str2.length();
        
        int[][] dp = new int[m + 1][n + 1];
        
        // 初始化
        for (int i = 0; i <= m; i++) {
            dp[i][0] = i;
        }
        for (int j = 0; j <= n; j++) {
            dp[0][j] = j;
        }
        
        // 动态规划计算编辑距离
        for (int i = 1; i <= m; i++) {
            for (int j = 1; j <= n; j++) {
                char c1 = str1.charAt(i - 1);
                char c2 = str2.charAt(j - 1);
                
                if (c1 == c2 || c1 == '*' || c2 == '*') {
                    // 字符相同或其中一个是通配符
                    dp[i][j] = dp[i - 1][j - 1];
                } else {
                    // 字符不同，取最小操作数
                    dp[i][j] = 1 + Math.min(Math.min(dp[i - 1][j], dp[i][j - 1]), dp[i - 1][j - 1]);
                }
            }
        }
        
        return dp[m][n];
    }
    
    /**
     * 计算相似度，如果字符串为空则返回默认值
     * 
     * @param str1 字符串1
     * @param str2 字符串2
     * @return 相似度
     */
    private static double calculateSimilarityWithDefault(String str1, String str2) {
        if (StringUtils.isEmpty(str1) && StringUtils.isEmpty(str2)) {
            return 1.0; // 都为空，认为匹配
        }
        if (StringUtils.isEmpty(str1) || StringUtils.isEmpty(str2)) {
            return 0.0; // 一个为空一个不为空，不匹配
        }
        return calculateSimilarity(str1, str2);
    }
}
