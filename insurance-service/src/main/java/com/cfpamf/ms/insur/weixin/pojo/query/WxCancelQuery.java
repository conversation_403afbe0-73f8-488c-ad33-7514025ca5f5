package com.cfpamf.ms.insur.weixin.pojo.query;

import com.cfpamf.ms.insur.admin.pojo.query.BaseQuery;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR> 2020/1/20 16:41
 */
@Data
public class WxCancelQuery extends BaseQuery {

    /**
     * 被保人/保单号/投保人姓名
     */
    @ApiModelProperty(value = "订单号/被保人/保单号/投保人姓名")
    private String keyword;

    @ApiModelProperty(value = "险种类型 个险-person 团险-group", allowableValues = "person,group")
    private String productAttrCode;

    @ApiModelProperty(value = "当前所处节点", hidden = true)
    private String taskKey;

    @ApiModelProperty(value = "处理中", hidden = true)
    private Boolean doing = Boolean.FALSE;

    @ApiModelProperty(value = "是否可以新增", hidden = true)
    private Boolean add = Boolean.FALSE;

    @ApiModelProperty(value = "是否查询身份证", hidden = true)
    private Boolean keywordLikeIdCard = Boolean.FALSE;

    @ApiModelProperty(value = "是否查询名字", hidden = true)
    private Boolean keywordLikeName = Boolean.FALSE;

    @ApiModelProperty(value = "是否查询保单或订单号", hidden = true)
    private Boolean keywordLikePolicyNo = Boolean.FALSE;

    @ApiModelProperty(value = "保单id", hidden = true)
    private Integer insuredId;

    @ApiModelProperty(value = "历史已经有的环节", hidden = true)
    private String hisTaskKey;

    @ApiModelProperty(value = "查询所有未结束的流程", hidden = true)
    private Boolean noEnd = Boolean.FALSE;
    //
    @ApiModelProperty(value = "退保id", hidden = true)
    private Integer cancelId;
}
