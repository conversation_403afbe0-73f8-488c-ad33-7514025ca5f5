package com.cfpamf.ms.insur.weixin.pojo.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

/**
 * 微信预下单DTO
 *
 * <AUTHOR>
 **/
@Data
@ApiModel
@ToString(callSuper = true)
public class WxPreOrderDTO {

    /**
     * 微信openId
     */
    @ApiModelProperty("微信openId")
    private String openId;

    /**
     * 授权token
     */
    @ApiModelProperty("授权token")
    private String authorization;

    /**
     * 支付类型编码
     */
    @ApiModelProperty("支付类型编码")
    private String payType;

    /**
     * 支付方式
     */
    @ApiModelProperty("支付方式")
    private String payMethod;

    /**
     * 支付方式
     */
    @ApiModelProperty("支付方式")
    private String fhOrderId;

    /**
     * 前端同步回调页面
     */
    @ApiModelProperty("前端同步回调页面")
    private String returnUrl;
}
