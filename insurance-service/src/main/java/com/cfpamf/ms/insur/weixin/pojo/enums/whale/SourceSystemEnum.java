package com.cfpamf.ms.insur.weixin.pojo.enums.whale;

import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.ms.insur.base.exception.ExcptEnum;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

@Getter
@Slf4j
public enum SourceSystemEnum {
    Order_Source("order","来源于订单系统"),
    Policy_Source("policy","来源于保单系统"),
    Zhnx_Order_Source("zhnxOrder","来源于中和农信订单系统"),
    Out_System_Source("outer","来源于外部系统");

    private String type;

    private String desc;

    SourceSystemEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static SourceSystemEnum findBType(String type) {
        SourceSystemEnum[] sourceSystemEnums = SourceSystemEnum.values();
        SourceSystemEnum currentSourceSystemEnum = null;
        for (SourceSystemEnum sourceSystemEnum : sourceSystemEnums) {
            if (sourceSystemEnum.getType().equals(type)) {
                currentSourceSystemEnum = sourceSystemEnum;
            }
        }
        if (currentSourceSystemEnum == null) {
            log.error("type 未找到对应的SourceSystemEnum 枚举值");
            throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR);
        }
        return currentSourceSystemEnum;
    }
}
