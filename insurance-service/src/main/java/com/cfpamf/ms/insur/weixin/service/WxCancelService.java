package com.cfpamf.ms.insur.weixin.service;

import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.ms.bms.facade.vo.UserDetailVO;
import com.cfpamf.ms.insur.admin.constant.SmConstants;
import com.cfpamf.ms.insur.admin.dao.safes.SmCancelMapper;
import com.cfpamf.ms.insur.admin.enums.EnumBmsRole;
import com.cfpamf.ms.insur.admin.enums.EnumFlowInsCancel;
import com.cfpamf.ms.insur.admin.enums.EnumUserType;
import com.cfpamf.ms.insur.admin.pojo.dto.cancel.CancelDTO;
import com.cfpamf.ms.insur.admin.pojo.po.SmCancel;
import com.cfpamf.ms.insur.admin.service.SmCancelService;
import com.cfpamf.ms.insur.base.exception.BizException;
import com.cfpamf.ms.insur.base.exception.ExcptEnum;
import com.cfpamf.ms.insur.base.service.BusinessTokenService;
import com.cfpamf.ms.insur.base.util.HttpRequestUtil;
import com.cfpamf.ms.insur.base.util.PermissionUtil;
import com.cfpamf.ms.insur.base.util.ThreadUserUtil;
import com.cfpamf.ms.insur.weixin.annotation.WxAutoAuthQuery;
import com.cfpamf.ms.insur.weixin.pojo.dto.WxCancelDTO;
import com.cfpamf.ms.insur.weixin.pojo.query.WxCancelQuery;
import com.cfpamf.ms.insur.weixin.pojo.vo.WxSessionVO;
import com.cfpamf.ms.insur.weixin.pojo.vo.cancel.WxCancelPolicyVO;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * <AUTHOR> 2020/1/19 17:39
 */
@Service
public class WxCancelService extends WxAbstractService {

    @Autowired
    SmCancelMapper cancelMapper;

    @Autowired
    private BusinessTokenService tokenService;
    /**
     * 退保业务处理
     */
    @Autowired
    SmCancelService cancelService;

    @Autowired
    private PermissionUtil permissionUtil;
    /**
     * 微信申请
     *
     * @param dto
     */
    public SmCancel apply(WxCancelDTO dto) {

        String wxOpenId = HttpRequestUtil.getWxOpenId();
        String token = HttpRequestUtil.getToken();
        WxSessionVO session = checkAuthority(wxOpenId, token);
        boolean validToken = tokenService.validateBusinessToken(session.getUserId(), SmConstants.SM_CANCEL_BUSINESS_CODE, dto.getToken());
        if (!validToken) {
            throw new BizException(ExcptEnum.INVALID_TOKEN_501009);
        }
        tokenService.deleteBusinessToken(wxOpenId, SmConstants.SM_CANCEL_BUSINESS_CODE, dto.getToken());
        dto.setCreateBy(session.getUserId());
        dto.setCreateRole(session.getUserType());
        SmCancel apply = cancelService.apply(dto);
        return apply;

    }


    /**
     * 微信申请
     *
     * @param dto
     */
    public SmCancel applyNotPro(WxCancelDTO dto) {

        String wxOpenId = HttpRequestUtil.getWxOpenId();
        String token = HttpRequestUtil.getToken();
        WxSessionVO session = checkAuthority(wxOpenId, token);
        boolean validToken = tokenService.validateBusinessToken(session.getUserId(), SmConstants.SM_CANCEL_BUSINESS_CODE, dto.getToken());
        if (!validToken) {
            throw new BizException(ExcptEnum.INVALID_TOKEN_501009);
        }
        tokenService.deleteBusinessToken(wxOpenId, SmConstants.SM_CANCEL_BUSINESS_CODE, dto.getToken());
        dto.setCreateBy(session.getUserId());
        dto.setCreateRole(session.getUserType());
        SmCancel apply = cancelService.applyNoProcess(dto, session.getUserId());
        return apply;

    }


    /**
     * 可以申请退保的列表
     *
     * @param query
     * @return
     */
    @WxAutoAuthQuery
    public PageInfo<WxCancelPolicyVO> canCancels(WxCancelQuery query) {
        query.setAdd(Boolean.TRUE);
        PageInfo<WxCancelPolicyVO> wxCancelPolicyVOPageInfo = cancelService.pageCancel(query);

        wxCancelPolicyVOPageInfo.getList().forEach(co -> {
            co.setCancelState(null);
            co.setTaskKey(null);
        });
        return wxCancelPolicyVOPageInfo;
    }

    /**
     * 当前用户相关
     *
     * @param query
     * @return
     */
    @WxAutoAuthQuery(allRole = EnumBmsRole.BIZ_SUPPORT)
    public PageInfo<WxCancelPolicyVO> withMe(WxCancelQuery query) {
        query.setAdd(Boolean.FALSE);
        return cancelService.pageCancel(query);
    }

    /**
     * 获取待办数量
     *
     * @param query
     * @return
     */
    @WxAutoAuthQuery(allRole = EnumBmsRole.BIZ_SUPPORT)
    public long getTodoCount(WxCancelQuery query) {
        query.setAdd(Boolean.FALSE);
        UserDetailVO userDetailVO = ThreadUserUtil.USER_DETAIL_TL.get();
        //主任
        if (Boolean.TRUE.equals(query.getPicRole())) {
            query.setTaskKey(EnumFlowInsCancel.ORG_ADMIN_AUDIT.getEventId());
            //保险业务中心 业务支持岗
        } else if (userDetailVO.getRoleList()
                .stream().anyMatch(ro -> Objects.equals(ro.getRoleCode(), EnumBmsRole.BIZ_SUPPORT.getCode()))) {
            query.setTaskKey(EnumFlowInsCancel.SAFE_CENTER.getEventId());
        } else {
            query.setTaskKey(EnumFlowInsCancel.UT_APPLY.getEventId());
        }
        return PageHelper.count(() -> cancelService.listCanCancelPolicy(query));
    }

    /**
     * 取消退保
     */
    @WxAutoAuthQuery(allRole = EnumBmsRole.BIZ_SUPPORT, userType = EnumUserType.EMPLOYEE)
    public void cancelCancel(CancelDTO dto) {
        cancelService.cancelCancel(dto);
    }

    /**
     * 待我审核的
     * 总部角色看总部审核阶段 渠道pco看机构数据
     *
     * @param wxCancelQuery
     * @return
     */
    public PageInfo<WxCancelPolicyVO> withMeAuditing(WxCancelQuery wxCancelQuery) {
        WxSessionVO session = checkAuthority();
        UserDetailVO userDetailVO = ThreadUserUtil.USER_DETAIL_TL.get();
        if (permissionUtil.isHeadRole(userDetailVO)) {
            //总部角色
            wxCancelQuery.setTaskKey(EnumFlowInsCancel.SAFE_CENTER.getEventId());
        } else {
            //渠道pco
            if (permissionUtil.isChannelPCO(userDetailVO)) {
                wxCancelQuery.setTaskKey(EnumFlowInsCancel.ORG_ADMIN_AUDIT.getEventId());
                wxCancelQuery.setRegionName(session.getRegionName());
                wxCancelQuery.setOrgName(session.getOrganizationName());
            }else{
                throw new MSBizNormalException("","当前用户无权限");
            }
        }
        return cancelService.pageCancel(wxCancelQuery);
    }

    /**
     * 我已审核的
     *
     * @param wxCancelQuery
     * @return
     */
    public PageInfo<WxCancelPolicyVO> withMeAudited(WxCancelQuery wxCancelQuery) {
        WxSessionVO session = checkAuthority();
        UserDetailVO userDetailVO = ThreadUserUtil.USER_DETAIL_TL.get();
        if (permissionUtil.isHeadRole(userDetailVO)) {
            //总部角色
            wxCancelQuery.setHisTaskKey(EnumFlowInsCancel.SAFE_CENTER.getEventId());
        } else {
            //渠道pco
            if (permissionUtil.isChannelPCO(userDetailVO)) {
                wxCancelQuery.setHisTaskKey(EnumFlowInsCancel.ORG_ADMIN_AUDIT.getEventId());
                wxCancelQuery.setRegionName(session.getRegionName());
                wxCancelQuery.setOrgName(session.getOrganizationName());
            }
        }
        return cancelService.pageCancel(wxCancelQuery);
    }
}
