package com.cfpamf.ms.insur.weixin.service;

import com.cfpamf.ms.insur.admin.enums.EnumOrderSubChannel;
import com.cfpamf.ms.insur.admin.pojo.vo.SmProductDetailVO;
import com.cfpamf.ms.insur.admin.service.SmProductService;
import com.cfpamf.ms.insur.admin.service.order.AutoGscOrderService;
import com.cfpamf.ms.insur.admin.service.order.SmOrderServiceWrapper;
import com.cfpamf.ms.insur.weixin.pojo.vo.WxSessionVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 微信产品查询service
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class WxProductJumpService extends WxAbstractService {

    /**
     * 微信生成userid的规则
     */
    static final String WECHAT_TK_USER_ID_TEMP = "wx|%s";

    @Autowired
    AutoGscOrderService orderService;

    @Autowired
    SmOrderServiceWrapper wrapper;

    @Autowired
    SmProductService productService;

    /**
     * 获取跳转到国寿车险的地址
     *
     * @param productId
     * @return
     */
    public String getJumpAutoGsc(Integer productId) {
        WxSessionVO wxSessionVO = checkAuthority();
        String userId = wxSessionVO.getUserId();
        return orderService.getJumpUrl(userId,
                String.format(WECHAT_TK_USER_ID_TEMP, wxSessionVO.getWxOpenId()), productId);
    }


    /**
     * 公共的跳转方法
     * @param productId
     * @return
     */
    public String getCommonJump(Integer productId) {
        SmProductDetailVO product = productService.getProductById(productId);
        WxSessionVO wxSessionVO = checkAuthority();
        return wrapper.findServiceByChannel(product.getChannel())
                .getH5JumpUrl(productId, wxSessionVO.getUserId(), EnumOrderSubChannel.XIANGZHU.getCode(), null);
    }
}
