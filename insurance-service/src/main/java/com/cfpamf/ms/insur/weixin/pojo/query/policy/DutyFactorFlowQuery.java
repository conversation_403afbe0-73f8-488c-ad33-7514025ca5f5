package com.cfpamf.ms.insur.weixin.pojo.query.policy;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class DutyFactorFlowQuery {

    @ApiModelProperty("主键Id")
    private Integer id;

    @ApiModelProperty("责任id")
    private Integer spcId;

    @ApiModelProperty("责任编码")
    private String dutyCode;

    @ApiModelProperty("责任因子名")
    private String dutyFactorName;

    @ApiModelProperty("责任因子编码")
    private String dutyFactorCode;

    @ApiModelProperty("属性描述")
    private String factorValue;

    @ApiModelProperty("属性名")
    private String factorName;

    @ApiModelProperty("选项名")
    private String optionName;

    @ApiModelProperty("保费浮动比例")
    private BigDecimal flow;

}
