package com.cfpamf.ms.insur.weixin.pojo.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * 微信修改代理人类别DTO
 *
 * <AUTHOR>
 **/
@Data
@ApiModel
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class WxAgentModifyTypeDTO extends WxAuthorizationDTO {

    /**
     * 代理人Id
     */
    @ApiModelProperty("代理人Id")
    private Integer agentId;

    /**
     * 代理人类别(0普通代理人, 1客户经理，2亲属代理人)
     */
    @ApiModelProperty("代理人类别(0普通代理人, 1客户经理，2亲属代理人) ")
    private Integer agentType;
}
