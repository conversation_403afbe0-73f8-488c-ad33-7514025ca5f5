package com.cfpamf.ms.insur.weixin.service.policy;


import com.cfpamf.ms.insur.admin.enums.EnumChannel;
import com.cfpamf.ms.insur.admin.external.zhongan.ZaApiProperties;
import com.cfpamf.ms.insur.weixin.common.IdGenerator;
import com.cfpamf.ms.insur.weixin.pojo.vo.apply.common.GroupQuote;
import com.cfpamf.ms.insur.weixin.pojo.vo.apply.zhongan.request.ZaGroupQuoteReq;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


/**
 * 众安团险报价类
 */
@Service()
public class ZaQuoteService extends AbstractQuoteService{

    @Autowired
    private ZaApiProperties zaconfig;

    @Override
    public String quote4Group(GroupQuote req) {
        //        ZaGroupQuoteReq zaQuote = req.getZaGroupQuote();


        return null;
    }

    /**
     * 数据修正
     * @param data
     */
    private void revisesBean(ZaGroupQuoteReq data){
        if(StringUtils.isBlank(data.getChannelOrderNo())){
            String orderNo = IdGenerator.getNextNo(EnumChannel.ZA.getCode());
            data.setChannelOrderNo(orderNo);
        }



    }
}
