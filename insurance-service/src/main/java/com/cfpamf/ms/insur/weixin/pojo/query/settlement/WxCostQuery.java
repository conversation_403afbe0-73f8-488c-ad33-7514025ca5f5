package com.cfpamf.ms.insur.weixin.pojo.query.settlement;

import com.cfpamf.ms.insur.base.bean.Pageable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.Date;

@Data
@ApiModel("微信端24版我的推广费查询")
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class WxCostQuery extends Pageable {


    @ApiModelProperty(value = "推荐人工号", hidden = true)
    private String userId;

    @ApiModelProperty(value = "微信openId")
    private String openId;

    @ApiModelProperty(value = "授权token")
    private String authorization;

    @ApiModelProperty(value = "客户负责人Id")
    private String customerAdminId;


    @ApiModelProperty(value = "开始时间")
    private Date startTime;

    @ApiModelProperty(value = "结束时间")
    private Date endTime;

    /**
     * 长短险标志
     */
    @ApiModelProperty("长短险标志")
    private Integer longShortFlag;

    /**
     * 订单类型 0普通订单 1分销订单（是否分销单 0否 1是）
     */
    @ApiModelProperty("订单类型 0普通订单 1分销订单")
    private Integer distributionFlag;

    /**
     * 整村推进 0否 1是
     */
    @ApiModelProperty("整村推进 0否 1是")
    private Integer ruralProxyFlag;

    /**
     * 保单号
     */
    @ApiModelProperty("保单号")
    private String policyNo;

    /**
     * 投保人
     */
    @ApiModelProperty("投保人")
    private String applicantName;
    /**
     * 被保人姓名
     */
    @ApiModelProperty("被保人")
    private String insuredName;
}
