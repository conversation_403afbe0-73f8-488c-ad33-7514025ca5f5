package com.cfpamf.ms.insur.admin.renewal.entity;

import lombok.Data;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

@Table(name = "policy_renewal_base_info")
@Data
public class PolicyRenewalBaseInfo {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 原保单号（不能为空）
     */
    @Column(name = "old_policy_no", nullable = false, length = 64)
    private String oldPolicyNo;

    /**
     * 新保单号
     */
    @Column(name = "new_policy_no", length = 100)
    private String newPolicyNo;

    /**
     * 投保人姓名
     */
    @Column(name = "applicant_person_name", length = 128)
    private String applicantPersonName;

    /**
     * 投保人证件号
     */
    @Column(name = "applicant_id_number", length = 64)
    private String applicantIdNumber;

    /**
     * 被保人姓名（随机取的一个，不能作为关联表的条件）
     */
    @Column(name = "insured_person_name", length = 128)
    private String insuredPersonName;

    /**
     * 被保人证件（随机取的一个，不能作为关联表的条件）
     */
    @Column(name = "insured_id_number", length = 64)
    private String insuredIdNumber;

    /**
     * 保单生效时间
     */
    @Column(name = "start_time")
    @Temporal(TemporalType.TIMESTAMP)
    private Date startTime;

    /**
     * 保单失效时间
     */
    @Column(name = "invalid_time")
    @Temporal(TemporalType.TIMESTAMP)
    private Date invalidTime;

    /**
     * 订单总保费
     */
    @Column(name = "premium", precision = 10, scale = 2)
    private BigDecimal premium;

    /**
     * 小鲸本地险种编码
     */
    @Column(name = "product_code", length = 80)
    private String productCode;

    /**
     * 主险名称
     */
    @Column(name = "product_name", length = 255)
    private String productName;

    /**
     * 商品名称
     */
    @Column(name = "commodity_name", length = 255)
    private String commodityName;

    /**
     * 销售类型
     */
    @Column(name = "sales_type", length = 50)
    private String salesType;

    /**
     * 一级分类名称
     */
    @Column(name = "class_one_name", length = 64)
    private String classOneName;

    /**
     * 二级分类名称
     */
    @Column(name = "class_two_name", length = 64)
    private String classTwoName;

    /**
     * 险种分类名称
     */
    @Column(name = "risk_catagory_name", length = 64)
    private String riskCatagoryName;

    /**
     * 组类别
     */
    @Column(name = "group_type", length = 50)
    private String groupType;

    /**
     * 兼容农保产品id
     */
    @Column(name = "product_id")
    private Integer productId;

    /**
     * 兼容农保计划id
     */
    @Column(name = "plan_id")
    private Integer planId;

    /**
     * 宽限天数
     */
    @Column(name = "grace_day")
    private Integer graceDay;

    /**
     * 到期前天数
     */
    @Column(name = "before_expiration_day")
    private Integer beforeExpirationDay;

    /**
     * 到期后天数
     */
    @Column(name = "after_expiration_day")
    private Integer afterExpirationDay;

    /**
     * 可续保开始时间
     */
    @Column(name = "renewal_start_time")
    @Temporal(TemporalType.TIMESTAMP)
    private Date renewalStartTime;

    /**
     * 可续保结束时间
     */
    @Column(name = "renewal_end_time")
    @Temporal(TemporalType.TIMESTAMP)
    private Date renewalEndTime;
    /**
     * 续保成功时间
     */
    @Column(name = "renewal_end_time")
    private Date renewedSuccessTime;

    /**
     * 管护经理分支编码
     */
    @Column(name = "customer_org_code", length = 50)
    private String customerOrgCode;

    /**
     * 管护经理分支名称
     */
    @Column(name = "customer_org_name", length = 50)
    private String customerOrgName;

    /**
     * 管护经理ID
     */
    @Column(name = "customer_admin_id", length = 50)
    private String customerAdminId;

    /**
     * 推荐人分支编码
     */
    @Column(name = "recommend_org_code", length = 50)
    private String recommendOrgCode;

    /**
     * 推荐人分支名称
     */
    @Column(name = "recommend_org_name", length = 50)
    private String recommendOrgName;

    /**
     * 推荐人ID
     */
    @Column(name = "recommend_id", length = 30)
    private String recommendId;

    /**
     * 续保类型：renewal-续保, transfer-转保
     */
    @Column(name = "renewal_type", length = 30)
    private String renewalType;

    /**
     * 状态: init-初始化, waited-待续保, renewing-续保中(用于转投保过程状态),
     * renewed-已续/转保, cancel-已断保
     */
    @Column(name = "ins_status", length = 30)
    private String insStatus;

    /**
     * 是否生成待办
     */
    @Column(name = "todo_state", columnDefinition = "tinyint default 0")
    private Integer todoState = 0;

    /**
     * 待办生成时间
     */
    @Column(name = "todo_generate_time")
    @Temporal(TemporalType.TIMESTAMP)
    private Date todoGenerateTime;

    /**
     * 待办完成时间
     */
    @Column(name = "todo_finish_time")
    @Temporal(TemporalType.TIMESTAMP)
    private Date todoFinishTime;

    /**
     * 新保交单时间
     */
    @Column(name = "new_order_time")
    @Temporal(TemporalType.TIMESTAMP)
    private Date newOrderTime;

    /**
     * 新报保费
     */
    @Column(name = "new_premium", precision = 16, scale = 2)
    private BigDecimal newPremium;

    /**
     * 新保产品编码
     */
    @Column(name = "new_product_code", length = 64)
    private String newProductCode;

    /**
     * 新保产品名称
     */
    @Column(name = "new_product_name", length = 128)
    private String newProductName;

    /**
     * 启用标志
     */
    @Column(name = "enabled_flag", columnDefinition = "tinyint default 0")
    private Integer enabledFlag = 0;

    /**
     * 创建时间（自动填充）
     */
    @Column(name = "create_time", nullable = false, updatable = false)
    @Temporal(TemporalType.TIMESTAMP)
    private Date createTime;

    /**
     * 更新时间（自动更新）
     */
    @Column(name = "update_time", nullable = false)
    @Temporal(TemporalType.TIMESTAMP)
    private Date updateTime;
}
