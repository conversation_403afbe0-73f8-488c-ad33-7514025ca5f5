package com.cfpamf.ms.insur.weixin.service;

import com.cfpamf.ms.insur.admin.dao.safes.AuthUserMapper;
import com.cfpamf.ms.insur.admin.dao.safes.SmCustNotifyMapper;
import com.cfpamf.ms.insur.admin.dao.safes.SmCustNotifyMsgMapper;
import com.cfpamf.ms.insur.admin.enums.EnumBmsRole;
import com.cfpamf.ms.insur.admin.enums.EnumUserType;
import com.cfpamf.ms.insur.admin.pojo.po.SmCustNotifyMsg;
import com.cfpamf.ms.insur.admin.pojo.vo.SmCustNotifyVO;
import com.cfpamf.ms.insur.admin.pojo.vo.UserVO;
import com.cfpamf.ms.insur.admin.service.SmCustNotifyService;
import com.cfpamf.ms.insur.admin.service.UserService;
import com.cfpamf.ms.insur.base.bean.Pageable;
import com.cfpamf.ms.insur.base.constant.BaseConstants;
import com.cfpamf.ms.insur.weixin.annotation.WxAutoAuthQuery;
import com.cfpamf.ms.insur.weixin.pojo.query.WxCustNotifyQuery;
import com.cfpamf.ms.insur.weixin.pojo.vo.WxSessionVO;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> 2020/1/19 17:39
 */
@Service
public class WxCustNotifyService extends WxAbstractService {

    @Autowired
    SmCustNotifyMapper notifyMapper;

    @Autowired
    SmCustNotifyMsgMapper notifyMsgMapper;

    @Autowired
    SmCustNotifyService smCustNotifyService;

    @Autowired
    AuthUserMapper userMapper;
    @Autowired
    UserService userService;

    /**
     * 查询列表
     * 机构对接人查询
     *
     * @return
     */
    @WxAutoAuthQuery(allRole = {EnumBmsRole.BIZ_SUPPORT, EnumBmsRole.BIZ_MANAGER},
            userType = EnumUserType.EMPLOYEE)
    public PageInfo<SmCustNotifyVO> queryByPic(WxCustNotifyQuery notifyQuery) {

        // * 如果没有区域说明是总部角色 （微信端没有可以看所有数据的角色）
        //*  在微信端签收跟进那里，只看总部的数据；后台系统可以看所有的数据。
        if (StringUtils.isBlank(notifyQuery.getRegionName())) {
            notifyQuery.setRegionName(BaseConstants.ORG_REGION_HQ);
        }
        //事务专员只看未签收的数据
        notifyQuery.setIsSign(Boolean.FALSE);
        return PageHelper.startPage(notifyQuery.getPage(), notifyQuery.getSize())
                .doSelectPageInfo(() -> notifyMapper.listSimpleList(notifyQuery));
    }

    /**
     * 查询客户经理
     *
     * @param notifyQuery
     * @return
     */
    @WxAutoAuthQuery(allRole = {EnumBmsRole.BIZ_SUPPORT, EnumBmsRole.BIZ_MANAGER}, userType = EnumUserType.EMPLOYEE)
    public PageInfo<SmCustNotifyVO> queryByCustomAdmin(WxCustNotifyQuery notifyQuery) {
        //只能查自己的
        WxSessionVO sessionVO = checkAuthority();
        notifyQuery.setUserId(sessionVO.getUserId());

        return PageHelper.startPage(notifyQuery.getPage(), notifyQuery.getSize())
                .doSelectPageInfo(() -> notifyMapper.listSimpleList(notifyQuery));
    }

    public SmCustNotifyVO getDetail(Integer id) {

        SmCustNotifyVO detail = smCustNotifyService.detail(id);
        if (Objects.nonNull(detail)) {
            detail.setCustNotifyContent(null);
        }
        return detail;
    }

    public int addMsg(SmCustNotifyMsg msg) {
        return smCustNotifyService.addMsg(msg);
    }

    public List<SmCustNotifyMsg> msgs(Integer id) {
        return smCustNotifyService.handMsg(id);
    }

    /**
     * 查询当前角色所在分支列表
     *
     * @param notifyQuery
     * @param pageable
     * @param keyword
     * @return
     */
    @WxAutoAuthQuery(allRole = {EnumBmsRole.BIZ_SUPPORT, EnumBmsRole.BIZ_MANAGER}, userType = EnumUserType.EMPLOYEE)
    public PageInfo<UserVO> queryUserList(WxCustNotifyQuery notifyQuery, Pageable pageable, String keyword) {
        String orgName = notifyQuery.getOrgName();
        orgName = StringUtils.isBlank(orgName) ? BaseConstants.ORG_BRANCH_HQ : orgName;
        return userService.getUsersByOrgPage(keyword, orgName, pageable);
    }
}
