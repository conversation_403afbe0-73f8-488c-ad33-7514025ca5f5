package com.cfpamf.ms.insur.weixin.pojo.query;

import com.cfpamf.ms.insur.base.bean.Pageable;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * 分页获取异常数据查询Query
 *
 * <AUTHOR>
 **/
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class WxExcepIdnoQuery extends Pageable {
    @ApiModelProperty(value = "客户姓名/证件号码", required = false)
    private String custInfo;

    @ApiModelProperty(value = "工号", required = false)
    private String submitStaffInfo;
}
