package com.cfpamf.ms.insur.weixin.constant;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.experimental.FieldDefaults;

import java.util.Objects;

@Getter
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public enum EnumOrderQueryKeyType {
    /**
     * 前端传1，2，3，4,传3，4的时候需要转变为5，6，7，8
     */
    ORDER_NO(1,"订单号"),
    POLICY_NO(2,"保单号"),
    INSURED(3,"被保人"),
    APPLICANT(4,"投保人"),
    INSURED_NAME(5, "被保人名字"),
    INSURED_ID_CARD(6, "被保人证件号"),
    APPLICANT_NAME(7, "投保人名字"),
    APPLICANT_ID_CARD(8,"投保人证件号");
    private Integer code;
    private String desc;

    public boolean isMe(Integer code) {
        return Objects.equals(code, this.code);
    }
}
