package com.cfpamf.ms.insur.commission.utils.excel;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.metadata.CellData;
import com.alibaba.excel.metadata.CellExtra;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.fastjson.JSON;
import com.cfpamf.ms.insur.commission.converter.CommissionUploadConverter;
import com.cfpamf.ms.insur.commission.domain.CommissionUploadDetail;
import com.cfpamf.ms.insur.commission.mapper.CommissionUploadDetailMapper;
import com.cfpamf.ms.insur.commission.po.CommissionUploadFilePO;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> 2022/12/22 10:02
 */
@Slf4j
@FieldDefaults(level = AccessLevel.PRIVATE)
public class CommissionUploadDetailDataListener implements ReadListener<Map<Integer, String>> {

    /**
     *
     */
    private static final int BATCH_COUNT = 5000;
    /**
     * 缓存的数据
     */
    List<CommissionUploadDetail> cachedDataList = new ArrayList<>(BATCH_COUNT);

    CommissionUploadDetailMapper mapper;

    Map<Integer, String> head = new LinkedHashMap<>();

    CommissionUploadFilePO file;

    /**
     * 如果使用了spring,请使用这个构造方法。每次创建Listener的时候需要把spring管理的类传进来
     */
    public CommissionUploadDetailDataListener(CommissionUploadDetailMapper mapper, CommissionUploadFilePO file) {
        this.mapper = mapper;
        this.file = file;
        log.info("this.file={}",this.file);
    }

    @Override
    public void onException(Exception e, AnalysisContext analysisContext) throws Exception {

    }

    @Override
    public void invokeHead(Map<Integer, CellData> headMap, AnalysisContext context) {

        headMap.forEach((key, val) -> head.put(key, StringUtils.trim(val.getStringValue())));
    }


    /**
     * 这个每一条数据解析都会来调用
     *
     * @param data    one row value. Is is same as {@link AnalysisContext#readRowHolder()}
     * @param context
     */
    @Override
    public void invoke(Map<Integer, String> data, AnalysisContext context) {
        log.info("解析到一条数据:{}", JSON.toJSONString(data));
        cachedDataList.add(convert(data));
        // 达到BATCH_COUNT了，需要去存储一次数据库，防止数据几万条数据在内存，容易OOM
        if (cachedDataList.size() >= BATCH_COUNT) {
            saveData();
            // 存储完成清理 list
            cachedDataList = new ArrayList<>(BATCH_COUNT);
        }
    }

    @Override
    public void extra(CellExtra cellExtra, AnalysisContext analysisContext) {

    }

    /**
     * 所有数据解析完成了 都会来调用
     *
     * @param context
     */
    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        // 这里也要保存数据，确保最后遗留的数据也存储到数据库
        saveData();
        log.info("所有数据解析完成！");
    }

    @Override
    public boolean hasNext(AnalysisContext analysisContext) {
        return true;
    }


    private CommissionUploadDetail convert(Map<Integer, String> map) {

        String regionName = map.get(0);
        String orgName = map.get(1);
        String jobNumber = map.get(2);
        String name = map.get(3);
        CommissionUploadDetail detail = new CommissionUploadDetail();
        detail.setUploadId(Long.valueOf(file.getId()));
        detail.setCommissionMonth(file.getCommissionMonth());
        detail.setRegionName(regionName);
        detail.setJobNumber(jobNumber);
        detail.setUserName(name);
        detail.setOrgName(orgName);
        detail.setCommissionDataType(file.getCommissionDataType());
        final LinkedHashMap<String, Object> item = new LinkedHashMap<>();
        map.entrySet().stream().filter(en -> en.getKey() > 3)
                .forEach(en -> {
                    String s = head.get(en.getKey());
                    if (StringUtils.isNotBlank(s)) {
                        item.put(s, en.getValue());
                    }
                });
        detail.setCommissionItem(item);
        log.info("detail={}",JSON.toJSONString(detail));
        return detail;
    }

    /**
     * 加上存储数据库
     */
    private void saveData() {
        mapper.insertListOrUpdateItem(CommissionUploadConverter.INS.domain2Po(cachedDataList));
    }

}
