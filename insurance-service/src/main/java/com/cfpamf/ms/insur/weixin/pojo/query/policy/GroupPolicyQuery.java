package com.cfpamf.ms.insur.weixin.pojo.query.policy;

import com.cfpamf.ms.insur.base.bean.Pageable;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class GroupPolicyQuery extends Pageable{


    @ApiModelProperty("关键字")
    private String keyword;

    @ApiModelProperty(value = "出单标志:0=未出单;1=已出单")
    private Integer issueFlag;

    @ApiModelProperty(value = "用户Id",hidden = true)
    private String userId;

    @ApiModelProperty(value = "用户Id",hidden = true)
    private String openId;

    @ApiModelProperty(value = "代理人Id",hidden = true)
    private Integer agentId;

}
