package com.cfpamf.ms.insur.weixin.pojo.form.claim;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 理赔注销报案申请审核表单
 *
 * <AUTHOR>
 * @date 2022/1/18 17:12
 */
@ApiModel("理赔注销报案申请审核表单")
@Data
public class ClaimCancelReportExamineForm {

    @ApiModelProperty("步骤选项")
    @NotBlank(message = "步骤选项不能为空")
    private String optionCode;

    @ApiModelProperty("步骤输入")
    private String optionValue;

    @ApiModelProperty("注销理赔报案资料表单-补充资料的时候不能为空")
    private ClaimCancelReportForm claimCancelReportForm;
}
