package com.cfpamf.ms.insur.weixin.util;


import com.cfpamf.common.ms.exception.MSBizNormalException;
import org.apache.commons.lang3.StringUtils;

public class UnitConvertor {

    private final static String PERIOD_REG="\\d+(年|月|日){1}";

    private final static String PERIOD_MONTH_REG="\\d+月";

    public static String convertTimeUnit(String timeUnit) {
        String unit = "月";
        if(StringUtils.isBlank(timeUnit)){
            return unit;
        }
        switch (timeUnit) {
            case "DAY":
                unit = "日";
                break;
            case "MONTH":
                unit = "月";
                break;
            case "YEAR":
                unit = "年";
                break;
            default:throw new MSBizNormalException("-1","不支持该时间单位的转换");
        }
        return unit;
    }

    public static String month2Year(String period) {
        if(period==null){
            return period;
        }
        if(!period.matches(PERIOD_MONTH_REG)){
            return period;
        }
        String time = period.substring(0,period.length()-1);
        Integer numb = Integer.valueOf(time);
        if(numb%12==0){
            return numb/12+"年";
        }
        return period;
    }

    public static boolean matchPeriod(String period) {
        if(period==null){
            return false;
        }
        return period.matches(PERIOD_REG);
    }

    public static void main(String[]a){
        String data = "12年";
        System.out.println(data.substring(data.length()-1));
    }
}
