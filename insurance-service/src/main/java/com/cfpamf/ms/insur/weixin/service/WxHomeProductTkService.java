package com.cfpamf.ms.insur.weixin.service;

import com.cfpamf.ms.insur.admin.service.SmProductService;
import com.cfpamf.ms.insur.admin.service.order.TkAutoOrderService;
import com.cfpamf.ms.insur.weixin.pojo.vo.WxSessionVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 微信产品查询service
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class WxHomeProductTkService extends WxAbstractService {

    /**
     * 微信生成泰康userid的规则
     */
    static final String WECHAT_TK_USER_ID_TEMP = "%s|wx|%s";

    @Autowired
    TkAutoOrderService autoService;

    @Autowired
    SmProductService productService;

    public String jumpAuto(int productId) {
        WxSessionVO wxSessionVO = checkAuthority();
        return autoService.getJumpAutoUri(String.format(WECHAT_TK_USER_ID_TEMP, wxSessionVO.getUserId(), wxSessionVO.getWxOpenId()), productId);
    }
}
