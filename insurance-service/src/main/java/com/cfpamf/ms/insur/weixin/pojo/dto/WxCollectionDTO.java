package com.cfpamf.ms.insur.weixin.pojo.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * @description: 为宁用户收藏DTO
 * @author: zhang<PERSON>i
 * @create: 2018-07-30 09:27
 **/
@Data
@ApiModel
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class WxCollectionDTO extends WxAuthorizationDTO {

    /**
     * id
     */
    @ApiModelProperty(value = "id", hidden = true)
    private Integer id;

    /**
     * 用户Id
     */
    @ApiModelProperty(value = "用户Id", hidden = true)
    private String userId;

    /**
     * 产品Id
     */
    @ApiModelProperty("产品Id")
    private Integer productId;
}
