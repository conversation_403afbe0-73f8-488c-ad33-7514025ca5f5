package com.cfpamf.ms.insur.weixin.common;

public class AntiDos {
//    private int alarm;
//    private int serious;
//    /**
//     * 统计时间窗口
//     */
//    private int antiDosSpanMs;
//    private ScheduledFuture<?> lastScheduledFuture;
//    private ConcurrentHashMap<Object, VisitInfo> visitInfos = new ConcurrentHashMap<>();
//
//    private ConcurrentHashMap<Object, BlackInfo> blacks = new ConcurrentHashMap<>();
//    private static AntiDos antiDos;
//    static {
//        antiDos = new AntiDos();
//        antiDos.initSweeper(0, null);
//    }
//    public static AntiDos getInstance() {
//        return antiDos;
//    }
//    private AntiDos(int alarm, int antiDosSpanSec) {
//        this.alarm = alarm;
//        this.serious=alarm*2;
//        this.antiDosSpanMs = antiDosSpanSec * 1000;
//    }
//
//    /**
//     * 初始化默认值
//     */
//    private AntiDos() {
//        this(100, 300);
//    }
//
//
//
//    private AntiDos initSweeper(int sweepSecSpan, ScheduledExecutorService sweepScheduler) {
//        initSweeper(sweepSecSpan, sweepSecSpan, sweepScheduler);
//        return this;
//    }
//
//    private AntiDos initSweeper(int initialDelay, int sweepSecSpan, ScheduledExecutorService sweepScheduler) {
//        int sweepSecSpan_l = sweepSecSpan < 1 ? 300 : sweepSecSpan;
//        int initialDelay_l = initialDelay < 1 ? 300 : initialDelay;
//        ScheduledExecutorService sweepScheduler_l = sweepScheduler == null ? Executors.newScheduledThreadPool(2) : sweepScheduler;
//
//        if (lastScheduledFuture != null)
//            lastScheduledFuture.cancel(true);
//
//        lastScheduledFuture = sweepScheduler_l.scheduleAtFixedRate(new Runnable() {
//            @Override
//            public void run() {
//                try {
//                    long now = System.currentTimeMillis();
//
//                    for (Map.Entry<Object, VisitInfo> e : visitInfos.entrySet()) {
//                        VisitInfo info = e.getValue();
//                        if (info.isStale(now))
//                            removeVisitInfo(e.getKey());
//                    }
//
//                    for (Map.Entry<Object, BlackInfo> e : blacks.entrySet()) {
//                        BlackInfo info = e.getValue();
//                        if (info.isStale(now))
//                            removeBlackInfo(e.getKey());
//                    }
//
//
//                } catch (Throwable e) {
//                }
//            }
//        }, initialDelay_l, sweepSecSpan_l, TimeUnit.SECONDS);
//        return this;
//    }
//
//    private void removeVisitInfo(Object checkid) {
//        visitInfos.remove(checkid);
//    }
//    private void removeBlackInfo(Object checkid) {
//        blacks.remove(checkid);
//    }
//
//
//    public boolean visit(Object checkid) {
//        VisitInfo ori = visitInfos.putIfAbsent(checkid, new VisitInfo());
//        if (ori != null)
//            return ori.visit(checkid);
//        return true;
//    }
//
//
//    /**
//     * 访问者
//     */
//    private class VisitInfo {
//        private AtomicInteger counter;
//        private long createTime;
//
//        public VisitInfo(){
//            counter=new AtomicInteger();
//            createTime=System.currentTimeMillis();
//        }
//
//        public boolean isStale() {
//            return System.currentTimeMillis() - createTime >= antiDosSpanMs;
//        }
//
//        public boolean isStale(long now) {
//            return now - createTime >= antiDosSpanMs;
//        }
//
//        public boolean visit(Object checkid) {
//
//            int count = counter.incrementAndGet();
//            if (isStale())
//                removeVisitInfo(checkid);
//
//            if(blacks.contains(checkid))
//                return false;
//
//            if(count>=serious) {
//                blacks.put(checkid, new BlackInfo(createTime, 7200000l));
//                return false;
//            }
//
//            if (count >= alarm) {
//                blacks.put(checkid, new BlackInfo(createTime, 3600000l));
//                return false;
//            }
//            return true;
//        }
//
//    }
//    /**
//     * 黑名单信息
//     */
//    private class BlackInfo {
//        public long initTime;
//        public long forbidTime;
//
//        public BlackInfo(long initTime,long forbidTime) {
//            this.initTime=initTime;
//            this.forbidTime=forbidTime;
//        }
//
//        public boolean isStale(long now){
//            return now - this.initTime >= this.forbidTime;
//        }
//    }
}
