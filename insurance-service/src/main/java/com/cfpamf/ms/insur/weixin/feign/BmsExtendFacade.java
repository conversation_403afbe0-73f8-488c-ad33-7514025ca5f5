package com.cfpamf.ms.insur.weixin.feign;

import com.cfpamf.common.ms.result.Result;
import com.cfpamf.ms.insur.assistant.pojo.vo.OrganizationTreeVo;
import com.cfpamf.ms.insur.weixin.pojo.vo.OrganizationParentVo;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 已有bms facade 接口扩展的新接口
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024年03月05日
 */
@FeignClient(name = "bms-service-biz", url = "${bms.api.url}")
public interface BmsExtendFacade {

    @ApiOperation(value = "根据orgCode获取所有上级组织机构详情")
    @PostMapping(value = "/org/getParentOrganizationsByOrgCodes", headers = {
            "Content-Type: application/json",
            "Accept: application/json"
    })
    Result<List<OrganizationParentVo>> getParentOrganizationsByOrgCodes(@RequestBody List<String> orgCodes);

    @ApiOperation(value = "根据机构编码获取以此机构为根节点的的事业部(分支事业部)、区域、片区、分支树结构信息", notes = "/org/listRootBranchBuAreaBranchBySuperOrgCode?orgCode=xxx")
    @GetMapping(value = "/org/listRootBranchBuAreaBranchBySuperOrgCode")
    Result<List<OrganizationTreeVo>> listRootBranchBuAreaBranchBySuperOrgCode(@RequestParam("orgCode") String orgCode);


    @ApiOperation(
            value = "获取整个组织机构树",
            notes = "/org/getOrgTree"
    )
    @GetMapping({"/org/getOrgTree"})
    Result<List<OrganizationTreeVo>> getOrganizationTree(@RequestHeader("authorization") String authorization);
}
