package com.cfpamf.ms.insur.weixin.annotation;

import com.cfpamf.ms.insur.admin.enums.EnumBmsRole;
import com.cfpamf.ms.insur.admin.enums.EnumUserType;

import java.lang.annotation.Documented;
import java.lang.annotation.Inherited;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import static java.lang.annotation.ElementType.METHOD;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

/**
 * 权限的优先级
 * <p> 如果是角色匹配 则不做任何处理 return</p>
 * <p> 如果是机构管理员并且需要判断 添加区域机构权限 return</p>
 * 如果binduserType 添加userId或agentId return
 * allRole > orgAdminArea > bindUserType
 *
 * <AUTHOR> 2020/2/17 15:02
 */
@Documented
@Inherited
@Retention(RUNTIME)
@Target({METHOD})
public @interface WxAutoAuthQuery {

    EnumUserType[] userType() default {EnumUserType.AGENT, EnumUserType.EMPLOYEE};

    /**
     * 查看所有权限的角色 优先级 1
     *
     * @return
     */
    EnumBmsRole[] allRole() default {};

    /**
     * 如果是机构对接人是否需要添加区域权限 优先级2
     *
     * @return
     */
    boolean orgAdminArea() default true;

}
