package com.cfpamf.ms.insur.commission.domain;

import com.cfpamf.ms.insur.admin.pojo.po.BasePO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.time.YearMonth;

/**
 * <AUTHOR> 2022/12/16 15:38
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class CommissionUploadFile extends BasePO {

    @ApiModelProperty(value = "导入月份", example = "2022-01")
    @NotNull(message = "导入月份不能为空")
    YearMonth commissionMonth;

    @NotBlank(message = "文件名称不能为空")
    String fileName;

    @NotBlank(message = "文件地址不能为空")
    String fileUrl;

    @NotBlank(message = "佣金数据类型 保险insurance 生服life_services")
    String commissionDataType;

    @ApiModelProperty(hidden = true)
    LocalDateTime uploadTime;

    @ApiModelProperty(hidden = true)
    String creatorName;

    @ApiModelProperty(hidden = true)
    String creatorJobNumber;

}
