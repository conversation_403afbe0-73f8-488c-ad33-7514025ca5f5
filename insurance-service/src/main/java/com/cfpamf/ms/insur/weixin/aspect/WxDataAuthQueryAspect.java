package com.cfpamf.ms.insur.weixin.aspect;

import com.alibaba.fastjson.JSON;
import com.cfpamf.ms.bms.facade.vo.UserDetailVO;
import com.cfpamf.ms.insur.admin.enums.EnumBmsRole;
import com.cfpamf.ms.insur.admin.enums.EnumUserType;
import com.cfpamf.ms.insur.admin.pojo.query.BaseQuery;
import com.cfpamf.ms.insur.admin.service.UserService;
import com.cfpamf.ms.insur.base.exception.BizException;
import com.cfpamf.ms.insur.base.exception.ExcptEnum;
import com.cfpamf.ms.insur.base.util.HttpRequestUtil;
import com.cfpamf.ms.insur.base.util.PermissionUtil;
import com.cfpamf.ms.insur.base.util.ThreadUserUtil;
import com.cfpamf.ms.insur.weixin.annotation.WxAutoAuthQuery;
import com.cfpamf.ms.insur.weixin.pojo.vo.WxSessionVO;
import com.cfpamf.ms.insur.weixin.service.WxCancelService;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 微信数据权限查询基类构建[这里只做了一件事 就是封装基本的查询条件]
 *
 * <AUTHOR> 2020/2/17 15:04
 */
@Aspect
@Component
@Slf4j
public class WxDataAuthQueryAspect {

    /**
     * 随便拿的一个 抽象service的子类
     */
    @Autowired
    WxCancelService cancelService;

    @Autowired
    PermissionUtil permissionUtil;

    @Autowired
    private UserService userService;

    @Pointcut("@annotation(com.cfpamf.ms.insur.weixin.annotation.WxAutoAuthQuery)")
    public void query() {
        throw new UnsupportedOperationException();
    }

    @Around("query()")
    public Object around(final ProceedingJoinPoint joinPoint) throws Throwable {
        UserDetailVO userDetailVO = ThreadUserUtil.USER_DETAIL_TL.get();
        try {
            init(joinPoint, userDetailVO);
            return joinPoint.proceed();
        } finally {
            ThreadUserUtil.USER_DETAIL_TL.remove();
        }

    }

    private void init(final ProceedingJoinPoint joinPoint, UserDetailVO userDetailVO) {
        Object[] args = joinPoint.getArgs();

        Signature signature = joinPoint.getSignature();
        if (signature instanceof MethodSignature) {
            Method method = ((MethodSignature) signature).getMethod();
            WxAutoAuthQuery annotation = method.getAnnotation(WxAutoAuthQuery.class);
            if (annotation == null) {
                return;
            }

            EnumUserType[] enumUserTypes = annotation.userType();
            String wxOpenId = HttpRequestUtil.getWxOpenId();
            String token = HttpRequestUtil.getToken();
            WxSessionVO session = cancelService.checkAuthority(HttpRequestUtil.getWxOpenId(), HttpRequestUtil.getToken());

            // 操作权限
            if (enumUserTypes.length > 0) {
                boolean access = false;
                for (EnumUserType enumUserType : enumUserTypes) {
                    if (Objects.equals(enumUserType.getKey(), session.getUserType())) {
                        access = true;
                    }
                }
                if (!access) {
                    log.warn("微信用户没有模块权限wxOpenId={}, authorization={}, session={}", wxOpenId, token, JSON.toJSONString(session));
                    throw new BizException(ExcptEnum.INVALID_USER_AUTH_844448);
                }
            }
            List<UserDetailVO.UserRoleVO> roleList = userDetailVO.getRoleList();
            //如果当前角色拥有所有权限直接return
            if (annotation.allRole().length > 0) {

                List<String> list = Stream.of(annotation.allRole()).map(EnumBmsRole::getCode).collect(Collectors.toList());
                if (roleList.stream().anyMatch(role -> list.contains(role.getRoleCode()))) {
                    return;
                }
            }
            //机构管理员 主任
            if (annotation.orgAdminArea() && session.isOrgHead()) {

                for (Object arg : args) {
                    if (arg instanceof BaseQuery) {
                        BaseQuery tmp = (BaseQuery) arg;
                        tmp.setChannel(session.getChannel());
                        tmp.setRegionName(session.getRegionName());
                        tmp.setOrgName(session.getOrganizationName());
                        //修改成是否为主任
                        //设置是否创新业务对接人
                        tmp.setPicRole(session.isOrgHead());
                    }
                }
                return;
            }
            //客户经理权限
            for (Object arg : args) {
                if (arg instanceof BaseQuery) {
                    BaseQuery tmp = (BaseQuery) arg;
                    if (session.isBindEmployee()) {
                        tmp.setUserId(session.getUserId());
                    } else if (session.isBindAgent()) {
                        tmp.setAgentId(session.getAgentId());
                    }
                    tmp.setChannel(session.getChannel());
                }

            }
        }

    }

}
