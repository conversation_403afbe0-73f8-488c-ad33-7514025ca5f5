
#### 动态规则 最终脚本保存到数据库
会  将所有的注解为@Service 的bean添加到上下文 所以不要使用***Service的变量 
将订单信息 转换成json传到groovy 中

```groovy


package groovy.vaild

import com.cfpamf.cmis.common.utils.IdcardUtils
import com.cfpamf.ms.insur.base.pojo.dto.ValidOrderParam
import com.cfpamf.ms.insur.base.pojo.dto.ValidResult
import org.apache.commons.lang3.StringUtils

ValidResult execute(ValidOrderParam params) {

    def request = params.getRequest()

    def person = request.getInsuredPerson()
    if (person == null || person.isEmpty()) {
        return ValidResult.error("被保人不能为空")
    }
    //校验被保人
    for (p in person) {
        def res = validBirAndCard(p.getBirthday(), p.getIdNumber(), smCmpySettingService.getCommonCode(params.getPlanVO().getCompanyId(), "sex", p.getPersonGender()))
        if (!res.result)
            return res
    }
    //校验投保人
    def info = request.getProposerInfo()
    if (info == null) {
        return ValidResult.error("投保人不能为空")
    }
    validBirAndCard(info.getBirthday(), info.getIdNumber(), smCmpySettingService.getCommonCode(params.getPlanVO().getCompanyId(), "sex", info.getPersonGender()))
    return ValidResult.SUCCESS
}

ValidResult validBirAndCard(def birthday, def idCardNum, def sex) {
    if (StringUtils.isBlank(birthday)) {
        return ValidResult.error("生日不能为空")
    }
    if (StringUtils.isBlank(idCardNum)) {
        return ValidResult.error("证件号码不能为空")
    }
//如果是身份证
    if (IdcardUtils.validateCard(idCardNum)) {
        String birthByIdCard = IdcardUtils.getBirthByIdCard(idCardNum);
        String s = birthday.replaceAll("-", "");
        if (!Objects.equals(birthByIdCard, s)) {
            return ValidResult.error("身份证${idCardNum}与生日不匹配[${birthday}]")
        }
    }
    // 如果公用码值不为空 则校验
    if (sex != null) {

    }

    return ValidResult.SUCCESS
}

```
