package groovy.claim

import com.alibaba.druid.util.StringUtils
import com.cfpamf.ms.insur.admin.pojo.vo.claim.ClaimEmailAllParams

/**
 * <AUTHOR>
 * @Date 2024/10/24 10:30
 * @Version 1.0
 * 理赔邮件发送-鲸喜人保全家福综合保障
 */
Object execute(ClaimEmailAllParams claimEmailAllParams) {

    if (!StringUtils.isEmpty(claimEmailAllParams.getProductName())) {
        return StringUtils.equals(claimEmailAllParams.getProductName(), "鲸喜人保全家福综合保障（API）")
    }
    return Boolean.FALSE

}