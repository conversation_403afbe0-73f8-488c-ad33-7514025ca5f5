<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>案件结论报告</title>
    <style>
        * {
            margin: 0;
            padding: 0;
        }
        body{
            zoom: 2;
        }
        .flex-row-center-center {
            display: flex;
            justify-content: center;
            align-items: center;
        }
        .flex-row-start-start {
            display: flex;
            justify-content: flex-start;
            align-items: flex-start;
        }
        .line-clamp-2{
            overflow: hidden;
            -webkit-line-clamp: 2;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-box-orient: vertical;
        }
        .page-container {
            padding: 12px;
            padding-bottom: 36px;
            background: url('https://oss-xjxhserver.xiaowhale.com/syscontent/20241119/0fc101636b9342d49444a90d99427268.png') 0 0 no-repeat, #5D73FF;
            background-size: 100%;
            font-family: PingFang SC;
        }

        .page-container .p-job-number {
            margin: 7px 0;
            text-align: right;
            color: #fff;
            font-size: 12px;
        }

        .page-container .p-title {
            margin: 12px 0 19px 0;
            font-size: 40px;
            font-family: WDCH;
            text-align: center;
            color: #fff;
        }

        .page-container .p-title span {
            color: #ffe136;
        }

        .page-container .p-title .p-title-img{
            width: 157px;
            height: 39px;
        }

        .page-container .p-title .mr-8{
            margin-right: 4px;
        }

        .page-container .p-card {
            padding: 10px 15px 25px 15px;
            margin-bottom: 12px;
            border-radius: 16px;
            background: #FFFFFF;
        }

        .page-container .p-card .title {
            margin-bottom: 3px;
            position: relative;
            z-index: 2;
            color: #333;
            font-family: PingFang SC;
            font-size: 16px;
            font-weight: 600;
        }

        .page-container .p-card .title::before {
            position: absolute;
            z-index: -1;
            left: 0;
            bottom: 3px;
            content: '';
            width: 64px;
            height: 6px;
            border-radius: 4px;
            background: #FFE136;
        }

        .page-container .p-card .content,.content-title {
            font-family: PingFang SC;
            font-size: 14px;
            font-weight: 400;
        }

        .page-container .p-card .content-title {
            margin-top: 10px;
        }

        .page-container .p-card .rows .row {
            min-height: 32px;
            line-height: 32px;
            font-size: 14px;
        }

        .page-container .p-card .rows .row .row-title {
            min-width: 91px;
            color: #666;
        }

        .page-container .p-card .rows .row .row-title .wrap {
            display: block;
            margin-top: -13px;
        }

        .page-container .p-card .rows .row .row-value {
            min-height: 32px;
            color: #333;
            word-break: break-all;
            font-family: PingFang SC;
        }
        .page-container .p-card .rows .row .row-value-red {
            font-weight: bolder;
            color:#F53841;
        }

        .page-container .p-card-firstly {
            padding: 10px 15px;
        }

        .page-container .gradual-style, .page-container .the-last-gradual-style {
            margin-bottom: 0;
            position: relative;
            padding-top: 20px;
            box-shadow: 0px 12px 10px 8px rgba(255, 236, 236, 0.5) inset;
        }

        .page-container .gradual-style::before, .page-container .the-last-gradual-style::before {
            position: absolute;
            left: 18px;
            top: 0;
            content: '';
            width: 316px;
            height: 5px;
            background: url('https://oss-xjxhserver.xiaowhale.com/syscontent/20241120/e7b7010e4f45481aa6916d20e510502c.png') 0 0 no-repeat;
            background-size: 100%;
        }

        .page-container .gradual-style::after, .page-container .the-last-gradual-style::after {
            position: absolute;
            left: 18px;
            bottom: 0;
            content: '';
            width: 316px;
            height: 5px;
            background: url('https://oss-xjxhserver.xiaowhale.com/syscontent/20241120/ce38ddf23d874e69bb03c507b3ec045a.png') 0 0 no-repeat;
            background-size: 100%;
        }

        .page-container .the-last-gradual-style::after {
            background: none !important;
        }
        .page-container .gradual-style:last-child::after{
            background: none !important;
        }


        .page-container .richtext-card {
            font-size: 14px;
            font-family: PingFang SC;
        }

        .page-container .richtext-card .title {
            margin-bottom: 13px;
        }

        .page-container .pin {
            margin-bottom: 0;
            position: relative;
            box-shadow: 0px 12px 10px 8px rgba(255, 236, 236, 0.5) inset;
        }

        .page-container .pin::before {
            content: '';
            position: absolute;
            z-index: 3;
            right: 0;
            top: -22px;
            width: 64px;
            height: 64px;
            background: url('https://oss-xjxhserver.xiaowhale.com/syscontent/20241119/a66c54fd99ea46b9945b71562a18fea5.png') 0 0 no-repeat;
            background-size: 100%;
        }

        .page-container .pin::after {
            position: absolute;
            left: 18px;
            bottom: 0;
            content: '';
            width: 316px;
            height: 5px;
            background: url('https://oss-xjxhserver.xiaowhale.com/syscontent/20241120/ce38ddf23d874e69bb03c507b3ec045a.png') 0 0 no-repeat;
            background-size: 100%;
        }

        .page-container .p-respect {
            margin-top: 16px;
            margin-left: 14px;
            width: 96px;
            height: 22px;
            font-family: PingFang SC;
            font-size: 16px;
            font-weight: 600;
            color: #fff;
            position: relative;
        }

        .page-container .p-respect::after {
            content: '';
            position: absolute;
            z-index: 3;
            right: -22px;
            bottom: -12px;
            width: 101px;
            height: 16px;
            background: url('https://oss-xjxhserver.xiaowhale.com/syscontent/20241120/648bf10f113a4807b31363daf5a5784c.png') 0 0 no-repeat;
            background-size: 100%;
        }

        .page-container .p-sign {
            margin-right: 14px;
            color: #fff;
            font-size: 14px;
            text-align: right;
        }

        .page-container .p-sign p {
            margin-bottom: 4px;
        }
        .display-none {
            display: none;
        }
    </style>
</head>
<body>
<div id="app" class="page-container">
    <div class="p-job-number">
        报告编号 <span id="finishReportNo"></span>
    </div>
    <div class="p-title flex-row-center-center">
        <img
                class="p-title-img"
                src="https://safesfiles.oss-cn-beijing.aliyuncs.com/safes/ZHNX33656/caseReport_202412051610.png"
                alt="案件结论报告"
        >
    </div>
    <div class="p-card  p-card-firstly">
    <div class="title">
        <span id="orgName"></span>:
    </div>
    <div class="content">
        首先向贵机构/部门全体同仁表示真挚的问候。感谢贵机构/部门对理赔服务的信任，非常荣幸为贵机构/部门提供客户理赔服务。现将最新理赔案件结案情况告知如下：
    </div>
</div>
<div class="p-card pin">
    <div class="title">
        保单信息
    </div>
    <div class="rows">
        <div class="row flex-row-start-start">
            <div class="row-title">
                保险公司
            </div>
            <div class="row-value line-clamp-2" id="companyFullName"></div>
        </div>
        <div class="row flex-row-start-start">
            <div class="row-title">
                保单号
            </div>
            <div class="row-value line-clamp-2" id="policyNo"></div>
        </div>
        <div class="row flex-row-start-start">
            <div class="row-title">
                投保产品
            </div>
            <div class="row-value line-clamp-2" id="productName"></div>
        </div>
        <div class="row flex-row-start-start">
            <div class="row-title">
                投保人
            </div>
            <div class="row-value line-clamp-2" id="appntName"></div>
        </div>
        <div class="row flex-row-start-start">
            <div class="row-title">
                被保险人
            </div>
            <div class="row-value line-clamp-2" id="insuredName"></div>
        </div>
        <div class="row flex-row-start-start">
            <div class="row-title">
                保险期限
            </div>
            <div class="row-value line-clamp-2" id="insurancePeriod"></div>
        </div>
        <div class="row flex-row-start-start">
            <div class="row-title">
                保费
            </div>
            <div class="row-value line-clamp-2" id="totalAmount"></div>
        </div>
    </div>
</div>
<div class="p-card gradual-style" id="riskInfoWrapper">
    <div class="title">
        理赔结果
    </div>
    <div class="rows">
        <div class="row flex-row-start-start">
            <div class="row-title">
                理赔案件号
            </div>
            <div class="row-value line-clamp-2" id="claimNo"></div>
        </div>
        <div class="row flex-row-start-start">
            <div class="row-title">
                出险类型
            </div>
            <div class="row-value line-clamp-2" id="riskTypeName"></div>
        </div>
        <div class="row flex-row-start-start">
            <div class="row-title">
                报案日期
            </div>
            <div class="row-value line-clamp-2" id="createTime"></div>
        </div>
        <div class="row flex-row-start-start">
            <div class="row-title">
                结案日期
            </div>
            <div class="row-value line-clamp-2" id="closeDate"></div>
        </div>
        <div class="row flex-row-start-start">
            <div class="row-title">
                保司理赔结论
            </div>
            <div class="row-value row-value-red" id="insuranceClaimConclusion"></div>
        </div>
        <div class="row flex-row-start-start display-none" id="payMoneyWrapper">
            <div class="row-title">
                赔付金额
            </div>
            <div class="row-value line-clamp-2" id="payMoney"></div>
        </div>
    </div>
</div>

<div class="p-card gradual-style display-none" id="caseDetailWrapper">
    <div class="title">
        案件详情通报
    </div>
    <div class="rows">
        <div class="content-title">
            经保险公司详细审核相关报案材料，调查取证及依据保险合同条款的约定，本次索赔申请无法获得赔付，具体原因如下：
        </div>
        <div class="content-title">
            一、索赔案件基本情况
        </div>
        <div class="row flex-row-start-start">
            <div class="row-title">
                出险日期
            </div>
            <div class="row-value line-clamp-2" id="riskTime"></div>
        </div>
        <div class="row flex-row-start-start">
            <div class="row-title">
                出险地点
            </div>
            <div class="row-value line-clamp-2" id="riskAddress"> </div>
        </div>
        <div class="row flex-row-start-start">
            <div class="row-title">
                出险类型
            </div>
            <div class="row-value line-clamp-2" id="riskTypeName2"> </div>
        </div>
        <div class="row flex-row-start-start">
            <div class="row-title">
                出险原因
            </div>
            <div class="row-value line-clamp-2" id="riskDesc"> </div>
        </div>

        <div class="content-title">
            二、拒赔详细情况
        </div>
        <div class="row flex-row-start-start">
            <div class="row-title">
                拒赔类型
            </div>
            <div class="row-value line-clamp-2" id="rejectType"> </div>
        </div>
        <div class="row">
            <div class="row-title">
                具体赔付原因如下：
            </div>
            <div class="row-value" id="rejectReason" ></div>
        </div>
    </div>
</div>

<div class="p-card gradual-style display-none" id="closeCaseWrapper">
    <div class="title">
        结案说明
    </div>
    <div class="rows">
        根据客户提供的资料及保险公司审核，上述理赔案件已顺利结案。保险公司将在1-10个工作日内将款项汇入客户提供的账户内。
    </div>
</div>

<div class="p-card gradual-style richtext-card the-last-gradual-style" id="followingStepsWrapper">
    <div class="title">
        后续服务
    </div>
    <div class="rows">
        如果您在后续有任何问题或需要进一步的支持，请随时与我们联系。我们将继续为您提供优质的保险服务，让温暖的保险服务走进千家万户。
    </div>
</div>

<div class="p-respect">
    此致 敬礼!
</div>
<div class="p-sign">
    <p>小鲸向海保险服务</p>
    <p id="triggerFinishReportTime"></p>
</div>
</div>

<script>
    // 将 <script> 中的内容粘贴到这里
    document.addEventListener('DOMContentLoaded', function () {

        // 保单信息
        const options = { year: 'numeric', month: 'long', day: 'numeric' };
        const finishReportNo = document.getElementById('finishReportNo');
        const claimNo = document.getElementById('claimNo');
        const orgName = document.getElementById('orgName');
        const companyFullName = document.getElementById('companyFullName');
        const policyNo = document.getElementById('policyNo');
        const productName = document.getElementById('productName');
        const appntName = document.getElementById('appntName');
        const insuredName = document.getElementById('insuredName');
        const insurancePeriod = document.getElementById('insurancePeriod');
        const totalAmount = document.getElementById('totalAmount');

        // 理赔结果
        const createTime = document.getElementById('createTime');
        const closeDate = document.getElementById('closeDate');
        const insuranceClaimConclusion = document.getElementById('insuranceClaimConclusion');
        const riskTypeName = document.getElementById('riskTypeName');
        const payMoney = document.getElementById('payMoney');
        const friendlyReminderWrapper = document.getElementById('friendlyReminderWrapper');
        const followingStepsWrapper = document.getElementById('followingStepsWrapper');
        // const reportTime = document.getElementById('reportTime');
        const triggerFinishReportTime = document.getElementById('triggerFinishReportTime');
        const riskInfoWrapper = document.getElementById('riskInfoWrapper');
        const payMoneyWrapper = document.getElementById('payMoneyWrapper');

        // 案件详情通报
        const caseDetailWrapper = document.getElementById('caseDetailWrapper');
        const riskTime = document.getElementById('riskTime');
        const riskAddress = document.getElementById('riskAddress');
        const riskTypeName2 = document.getElementById('riskTypeName2');
        const riskDesc = document.getElementById('riskDesc');
        const rejectType = document.getElementById('rejectType');
        const rejectReason = document.getElementById('rejectReason');

        const closeCaseWrapper = document.getElementById('closeCaseWrapper');

        // 模拟数据
        const wxClaimDetailVO = ${data};
        function formatDate(d) {
            return new Date(d).toLocaleDateString('zh-CN', options);
        }
        <#noparse>
        // 设置数据
        // 保单信息
        finishReportNo.textContent = wxClaimDetailVO.finishReportNo;
        claimNo.textContent = wxClaimDetailVO.claimNo;
        orgName.textContent = wxClaimDetailVO.orgName;
        companyFullName.textContent = wxClaimDetailVO.orderInfo.companyFullName;
        policyNo.textContent = wxClaimDetailVO.policyInfo.policyNo;
        productName.textContent = wxClaimDetailVO.productName;
        appntName.textContent = wxClaimDetailVO.appntInfo.appntName;
        insuredName.textContent = wxClaimDetailVO.insuredInfos.map(item => item.insuredName).join('，');
        insurancePeriod.textContent = `${formatDate(wxClaimDetailVO.orderInfo.startTime)} - ${formatDate(wxClaimDetailVO.orderInfo.endTime)}`;
        totalAmount.textContent = `${wxClaimDetailVO.orderInfo.totalAmount}元`;

        // 理赔结果
        createTime.textContent = formatDate(wxClaimDetailVO.createTime);
        closeDate.textContent = formatDate(wxClaimDetailVO.closeDate);
        insuranceClaimConclusion.textContent = wxClaimDetailVO.insuranceClaimConclusion;
        riskTypeName.innerHTML = wxClaimDetailVO.riskTypeName || '-';
        payMoney.textContent = `${wxClaimDetailVO.payMoney}元`;
        triggerFinishReportTime.textContent = formatDate(wxClaimDetailVO.triggerFinishReportTime);

        // 案件详情通报
        riskTime.textContent = formatDate(wxClaimDetailVO.riskTime);
        riskAddress.innerHTML = wxClaimDetailVO.riskAddress || '-';
        riskTypeName2.innerHTML = wxClaimDetailVO.riskTypeName || '-';
        riskDesc.innerHTML = wxClaimDetailVO.riskDesc || '-';
        rejectType.textContent = wxClaimDetailVO.rejectType;
        rejectReason.innerHTML = wxClaimDetailVO.rejectReason || '-';

        // 结案类型判断
        if(wxClaimDetailVO.finishState === 'payed'){
            closeCaseWrapper.classList.remove('display-none');
            payMoneyWrapper.classList.remove('display-none');
        }else if (wxClaimDetailVO.finishState === 'payRejected'){
            caseDetailWrapper.classList.remove('display-none');
        }
    });
</script>
</#noparse>
</body>
</html>