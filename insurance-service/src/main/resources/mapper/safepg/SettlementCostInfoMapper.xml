<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safepg.SettlementCostInfoMapper">

    <select id="querySafePgCostInfo" resultType="com.cfpamf.ms.insur.admin.settlement.dto.SettlementCostInfoDTO">
        select
        sci.applicant_name as applicantName,
        sci.company_code as companyCode,
        sci.company_name as companyName,
        sci.applicant_time as applicantTime,
        sci.approved_time as approvedTime,
        sci.order_time as orderTime,
        sci.enforce_time as enforceTime,
        sci.rural_proxy_flag as ruralProxyFlag,
        sci.distribution_flag as distributionFlag,
        sci.policy_product_type as policyProductType,
        sci.long_short_flag as mainLongShortFlag,
        sci.policy_status as policyStatus,
        sci.revisit_status as revisitStatus,
        sci.revisit_result as revisitResult,
        sci.revisit_time as revisitTime,
        sci.receipt_status as receiptStatus,
        sci.receipt_time as receiptTime,
        sci.surrender_term_period as surrenderTermPeriod,
        sci.termination_product_code as terminationProductCode,
        sci.vehicle_vessel_tax as vehicleVesselTax,
        sci.vehicle_vessel_tax_rate as vehicleVesselTaxRate,
        sci.vehicle_business_score as vehicleBusinessScore,

        sci.cost_code as costCode,sci.cost_policy_id as costPolicyId, sci.contract_code as contractCode, sci.settlement_institution as settlementInstitution,
        sci.settlement_institution_name as settlementInstitutionName,sci.business_account_time as businessAccountTime, sci.settlement_event_code as settlementEventCode,
        sci.settlement_event_desc as settlementEventDesc,
        sci.initial_event_code as initialEventCode,sci.settlement_subject_code as settlementSubjectCode, sci.settlement_subject_name as settlementSubjectName,
        sci.owner_channel_code as ownerChannelCode,
        sci.owner_code as ownerCode,sci.owner_name as ownerName, sci.owner_org_code as ownerOrgCode, sci.owner_third_code as ownerThirdCode,
        sci.owner_third_org as ownerThirdOrg,sci.owner_third_org_name as ownerThirdOrgName, sci.cost_type as costType, sci.commission_type as commissionType,
        sci.cost_rate as costRate,sci.cost_actual_rate as costActualRate, sci.cost_amount as costAmount, sci.grant_rate as grantRate,
        sci.grant_amount as grantAmount,sci.correction_flag as correctionFlag, sci.source_cost_code as sourceCostCode, sci.source_generate_type as sourceGenerateType,
        sci.correction_type as correctionType,sci.correction_user as correctionUser, sci.correction_op_type as correctionOpType, sci.correction_time as correctionTime,
        sci.correction_remark as correctionRemark,sci.insured_code as insuredCode, sci.insured_name as insuredName, sci.insured_id_card as insuredIdCard,
        sci.product_code as productCode,sci.product_name as productName, sci.protocol_product_code as protocolProductCode, sci.insurance_type as insuranceType,
        sci.product_status as productStatus,sci.protocol_product_name as protocolProductName, sci.plan_code as planCode, sci.plan_name as planName,
        sci.long_short_flag_risk as longShortFlag,sci.product_group as productGroup, sci.level2_code as level2Code, sci.level3_code as level3Code,
        sci.product_type as productType,sci.main_insurance as mainInsurance, sci.additional_risks_type as additionalRisksType, sci.effective_date as effectiveDate,
        sci.end_date as endDate,sci.renewal_year as renewalYear, sci.renewal_period as renewalPeriod, sci.payable_time as payableTime,
        sci.reality_time as realityTime,sci.insured_period_type as insuredPeriodType, sci.insured_period as insuredPeriod, sci.period_type as periodType,
        sci.payment_period_type as paymentPeriodType,sci.payment_period as paymentPeriod, sci.premium as premium, sci.business_premium as businessPremium,
        sci.discount_premium as discountPremium,sci.product_premium_total as productPremiumTotal, sci.product_premium as productPremium, sci.preservation_code as preservationCode,
        sci.endorsement_no as endorsementNo,sci.endorsement_time as endorsementTime, sci.preservation_type as preservationType, sci.preservation_project as preservationProject,
        sci.preservation_period as preservationPeriod,sci.preservation_effect_time as preservationEffectTime, sci.surrender_time as surrenderTime, sci.surrender_amount as surrenderAmount,
        sci.hesitate_surrender as hesitateSurrender,sci.agricultural_machinery_flag as agriculturalMachineryFlag, sci.single_propose_flag as singleProposeFlag, sci.policy_no as policyNo,
        sci.company_policy_no as companyPolicyNo,sci.is_self_flag isSelfFlag,sci.is_buyback_l3d isBuybackL3d,
        sci.is_loan_flag as isLoanFlag,sci.is_valid_loan as isValidLoan,sci.commodity_name as commodityName,
        sci.goods_name,sci.business_discount_premium
        from commission.dwd_settlement_cost_info_offline sci
        where 1=1
        <if test='isSelfFlag != null'>
            <if test="isSelfFlag == 1">
                and sci.is_self_flag = 1
            </if>
            <if test="isSelfFlag == 0">
                and sci.is_self_flag = 0
            </if>
        </if>
        <if test='isBuybackL3d != null'>
            <if test="isBuybackL3d == 1">
                and sci.is_buyback_l3d = 1
            </if>
            <if test="isBuybackL3d == 0">
                and sci.is_buyback_l3d = 0
            </if>
        </if>
        <if test='isLoanFlag != null'>
            <if test="isLoanFlag == 1">
                and sci.is_loan_flag = 1
            </if>
            <if test="isLoanFlag == 0">
                and sci.is_loan_flag = 0
            </if>
        </if>
        <if test='isValidLoan != null'>
            <if test="isValidLoan == 1">
                and sci.isValidLoan = 1
            </if>
            <if test="isValidLoan == 0">
                and sci.isValidLoan = 0
            </if>
        </if>
        <!--业务记账时间-->
        <if test='businessAccountStartTime != null'>
            <![CDATA[ AND sci.business_account_time>=#{businessAccountStartTime} ]]>
        </if>
        <if test='businessAccountEndTime != null'>
            <![CDATA[ AND sci.business_account_time<=#{businessAccountEndTime} ]]>
        </if>
        <!--投保人证件号-->
        <if test='applicantIdCard != null'>
            <![CDATA[ AND sci.applicant_id_card=#{applicantIdCard} ]]>
        </if>
        <!--投保人姓名-->
        <if test='applicantName != null'>
            <![CDATA[ AND sci.applicant_name=#{applicantName} ]]>
        </if>
        <!--投保人手机号-->
        <if test='applicantMobile != null'>
            <![CDATA[ AND sci.applicant_mobile=#{applicantMobile} ]]>
        </if>
        <!--被保人证件号-->
        <if test='insuredIdCard != null'>
            <![CDATA[ AND sci.insured_id_card=#{insuredIdCard} ]]>
        </if>
        <!--被保人姓名-->
        <if test='insuredName != null'>
            <![CDATA[ AND sci.insured_name=#{insuredName} ]]>
        </if>
        <!--被保人手机号-->
        <if test='insuredMobile != null'>
            <![CDATA[ AND sci.insured_mobile=#{insuredMobile} ]]>
        </if>

        <!--保单号-->
        <if test='policyNo != null'>
            <![CDATA[ AND sci.policy_no=#{policyNo} ]]>
        </if>
        <!--保险公司-->
        <if test='companyCode != null'>
            <![CDATA[ AND sci.company_code=#{companyCode} ]]>
        </if>

        <!--投保时间-->
        <if test='approvedTimeStart != null'>
            <![CDATA[ AND sci.approved_time>=#{approvedTimeStart} ]]>
        </if>
        <if test='approvedTimeEnd != null'>
            <![CDATA[ AND sci.approved_time<=#{approvedTimeEnd} ]]>
        </if>

        <!--生效时间-->
        <if test='enforceTimeStart != null'>
            <![CDATA[ AND sci.enforce_time>=#{enforceTimeStart} ]]>
        </if>
        <if test='enforceTimeEnd != null'>
            <![CDATA[ AND sci.enforce_time<=#{enforceTimeEnd} ]]>
        </if>
        <!--所属机构-->
        <if test='orgCodeList != null and orgCodeList.size() > 0'>
            AND owner_third_org in
            <foreach collection="orgCodeList" open="(" close=")" separator="," item = "item">
                #{item}
            </foreach>
        </if>

        <!--险种编码-->
        <if test='productCodeList != null and productCodeList.size() > 0'>
            AND sci.product_code in
            <foreach collection="productCodeList" open="(" close=")" separator="," item = "item">
                #{item}
            </foreach>
        </if>
        <!--编码-->
        <if test='companyCodeList != null and companyCodeList.size() > 0'>
            AND sci.company_code in
            <foreach collection="companyCodeList" open="(" close=")" separator="," item = "item">
                #{item}
            </foreach>
        </if>

        <!--编码-->
        <if test='commodityNameList != null and commodityNameList.size() > 0'>
            AND sci.commodity_name in
            <foreach collection="commodityNameList" open="(" close=")" separator="," item = "item">
                #{item}
            </foreach>
        </if>

        <!--编码-->
        <if test='goodsNameList != null and goodsNameList.size() > 0'>
            AND sci.goods_name in
            <foreach collection="goodsNameList" open="(" close=")" separator="," item = "item">
                #{item}
            </foreach>
        </if>




        <!--工号-->
        <if test='ownerThirdCode != null'>
            AND sci.owner_third_code=#{ownerThirdCode}
        </if>

        <!--保单大类 个团车财-->
        <if test='policyProductType != null'>
            AND sci.policy_product_type=#{policyProductType}
        </if>

        <!--保单状态-->
        <if test='policyStatus != null'>
            AND sci.policy_status=#{policyStatus}
        </if>
        <!--长度短险-->
        <if test='longShortFlag != null'>
            AND sci.long_short_flag=#{longShortFlag}
        </if>
        <!--险种编码-->
        <if test='productCode != null'>
            AND sci.product_code=#{productCode}
        </if>

        <!---->
        <if test='ruralProxyFlag != null'>
            AND sci.rural_proxy_flag=#{ruralProxyFlag}
        </if>
        <!---->
        <if test='distributionFlag != null'>
            AND sci.distribution_flag=#{distributionFlag}
        </if>

        <!--批单号-->
        <if test='endorsementNo != null'>
            AND sci.endorsement_no=#{endorsementNo}
        </if>
        <!--所属渠道-->
        <if test='ownerChannelCode != null'>
            AND sci.owner_channel_code=#{ownerChannelCode}
        </if>
        <!--结算科目-->
        AND sci.settlement_subject_code='FIRST_BASIC_COMM'
        order by sci.business_account_time desc

        LIMIT  #{limit} OFFSET #{limit}*(#{page}-1)

    </select>

    <select id="countSafePgCostInfo" resultType="java.lang.Integer">
        select
        count(1)
        from commission.dwd_settlement_cost_info_offline sci
        where 1=1
        <if test='isSelfFlag != null'>
            <if test="isSelfFlag == 1">
                and sci.is_self_flag = 1
            </if>
            <if test="isSelfFlag == 0">
                and sci.is_self_flag = 0
            </if>
        </if>
        <if test='isBuybackL3d != null'>
            <if test="isBuybackL3d == 1">
                and sci.is_buyback_l3d = 1
            </if>
            <if test="isBuybackL3d == 0">
                and sci.is_buyback_l3d = 0
            </if>
        </if>
        <if test='isLoanFlag != null'>
            <if test="isLoanFlag == 1">
                and sci.is_loan_flag = 1
            </if>
            <if test="isLoanFlag == 0">
                and sci.is_loan_flag = 0
            </if>
        </if>
        <if test='isValidLoan != null'>
            <if test="isValidLoan == 1">
                and sci.isValidLoan = 1
            </if>
            <if test="isValidLoan == 0">
                and sci.isValidLoan = 0
            </if>
        </if>
        <!--业务记账时间-->
        <if test='businessAccountStartTime != null'>
            <![CDATA[ AND sci.business_account_time>=#{businessAccountStartTime} ]]>
        </if>
        <if test='businessAccountEndTime != null'>
            <![CDATA[ AND sci.business_account_time<=#{businessAccountEndTime} ]]>
        </if>
        <!--投保人证件号-->
        <if test='applicantIdCard != null'>
            <![CDATA[ AND sci.applicant_id_card=#{applicantIdCard} ]]>
        </if>
        <!--投保人姓名-->
        <if test='applicantName != null'>
            <![CDATA[ AND sci.applicant_name=#{applicantName} ]]>
        </if>
        <!--投保人手机号-->
        <if test='applicantMobile != null'>
            <![CDATA[ AND sci.applicant_mobile=#{applicantMobile} ]]>
        </if>
        <!--被保人证件号-->
        <if test='insuredIdCard != null'>
            <![CDATA[ AND sci.insured_id_card=#{insuredIdCard} ]]>
        </if>
        <!--被保人姓名-->
        <if test='insuredName != null'>
            <![CDATA[ AND sci.insured_name=#{insuredName} ]]>
        </if>
        <!--被保人手机号-->
        <if test='insuredMobile != null'>
            <![CDATA[ AND sci.insured_mobile=#{insuredMobile} ]]>
        </if>

        <!--保单号-->
        <if test='policyNo != null'>
            <![CDATA[ AND sci.policy_no=#{policyNo} ]]>
        </if>
        <!--保险公司-->
        <if test='companyCode != null'>
            <![CDATA[ AND sci.company_code=#{companyCode} ]]>
        </if>

        <!--投保时间-->
        <if test='approvedTimeStart != null'>
            <![CDATA[ AND sci.approved_time>=#{approvedTimeStart} ]]>
        </if>
        <if test='approvedTimeEnd != null'>
            <![CDATA[ AND sci.approved_time<=#{approvedTimeEnd} ]]>
        </if>

        <!--生效时间-->
        <if test='enforceTimeStart != null'>
            <![CDATA[ AND sci.enforce_time>=#{enforceTimeStart} ]]>
        </if>
        <if test='enforceTimeEnd != null'>
            <![CDATA[ AND sci.enforce_time<=#{enforceTimeEnd} ]]>
        </if>
        <!--所属机构-->
        <if test='orgCodeList != null and orgCodeList.size() > 0'>
            AND owner_third_org in
            <foreach collection="orgCodeList" open="(" close=")" separator="," item = "item">
                #{item}
            </foreach>
        </if>



        <!--险种编码-->
        <if test='productCodeList != null and productCodeList.size() > 0'>
            AND sci.product_code in
            <foreach collection="productCodeList" open="(" close=")" separator="," item = "item">
                #{item}
            </foreach>
        </if>
        <!--编码-->
        <if test='companyCodeList != null and companyCodeList.size() > 0'>
            AND sci.company_code in
            <foreach collection="companyCodeList" open="(" close=")" separator="," item = "item">
                #{item}
            </foreach>
        </if>

        <!--编码-->
        <if test='commodityNameList != null and commodityNameList.size() > 0'>
            AND sci.commodity_name in
            <foreach collection="commodityNameList" open="(" close=")" separator="," item = "item">
                #{item}
            </foreach>
        </if>

        <!--编码-->
        <if test='goodsNameList != null and goodsNameList.size() > 0'>
            AND sci.goods_name in
            <foreach collection="goodsNameList" open="(" close=")" separator="," item = "item">
                #{item}
            </foreach>
        </if>

        <!--工号-->
        <if test='ownerThirdCode != null'>
            AND sci.owner_third_code=#{ownerThirdCode}
        </if>

        <!--保单大类 个团车财-->
        <if test='policyProductType != null'>
            AND sci.policy_product_type=#{policyProductType}
        </if>

        <!--保单状态-->
        <if test='policyStatus != null'>
            AND sci.policy_status=#{policyStatus}
        </if>
        <!--长度短险-->
        <if test='longShortFlag != null'>
            AND sci.long_short_flag=#{longShortFlag}
        </if>
        <!--险种编码-->
        <if test='productCode != null'>
            AND sci.product_code=#{productCode}
        </if>

        <!---->
        <if test='ruralProxyFlag != null'>
            AND sci.rural_proxy_flag=#{ruralProxyFlag}
        </if>
        <!---->
        <if test='distributionFlag != null'>
            AND sci.distribution_flag=#{distributionFlag}
        </if>

        <!--批单号-->
        <if test='endorsementNo != null'>
            AND sci.endorsement_no=#{endorsementNo}
        </if>
        <!--所属渠道-->
        <if test='ownerChannelCode != null'>
            AND sci.owner_channel_code=#{ownerChannelCode}
        </if>
        <!--结算科目-->
        AND sci.settlement_subject_code='FIRST_BASIC_COMM'
    </select>

    <select id="sumSafePgCostSmy" resultType="com.cfpamf.ms.insur.admin.settlement.vo.SettlementCostSummaryVO">
        select count(1) as totalQty,
        count(distinct sci.contract_code) as policyQty,
        sum(sci.premium) as totalPremium,
        sum(sci.discount_premium) as discountTotalPremium,
        sum(sci.grant_amount) as settlementAmount,
        sum(case when (sci.rural_proxy_flag =1 or sci.distribution_flag=1) then 0 else sci.grant_amount end) as cmsAmount
        from commission.dwd_settlement_cost_info_offline sci
        where 1=1
        <if test='isSelfFlag != null'>
            <if test="isSelfFlag == 1">
                and sci.is_self_flag = 1
            </if>
            <if test="isSelfFlag == 0">
                and sci.is_self_flag = 0
            </if>
        </if>
        <if test='isBuybackL3d != null'>
            <if test="isBuybackL3d == 1">
                and sci.is_buyback_l3d = 1
            </if>
            <if test="isBuybackL3d == 0">
                and sci.is_buyback_l3d = 0
            </if>
        </if>
        <if test='isLoanFlag != null'>
            <if test="isLoanFlag == 1">
                and sci.is_loan_flag = 1
            </if>
            <if test="isLoanFlag == 0">
                and sci.is_loan_flag = 0
            </if>
        </if>
        <if test='isValidLoan != null'>
            <if test="isValidLoan == 1">
                and sci.isValidLoan = 1
            </if>
            <if test="isValidLoan == 0">
                and sci.isValidLoan = 0
            </if>
        </if>
        <!--业务记账时间-->
        <if test='businessAccountStartTime != null'>
            <![CDATA[ AND sci.business_account_time>=#{businessAccountStartTime} ]]>
        </if>
        <if test='businessAccountEndTime != null'>
            <![CDATA[ AND sci.business_account_time<=#{businessAccountEndTime} ]]>
        </if>
        <!--投保人证件号-->
        <if test='applicantIdCard != null'>
            <![CDATA[ AND sci.applicant_id_card=#{applicantIdCard} ]]>
        </if>
        <!--投保人姓名-->
        <if test='applicantName != null'>
            <![CDATA[ AND sci.applicant_name=#{applicantName} ]]>
        </if>
        <!--投保人手机号-->
        <if test='applicantMobile != null'>
            <![CDATA[ AND sci.applicant_mobile=#{applicantMobile} ]]>
        </if>
        <!--被保人证件号-->
        <if test='insuredIdCard != null'>
            <![CDATA[ AND sci.insured_id_card=#{insuredIdCard} ]]>
        </if>
        <!--被保人姓名-->
        <if test='insuredName != null'>
            <![CDATA[ AND sci.insured_name=#{insuredName} ]]>
        </if>
        <!--被保人手机号-->
        <if test='insuredMobile != null'>
            <![CDATA[ AND sci.insured_mobile=#{insuredMobile} ]]>
        </if>

        <!--保单号-->
        <if test='policyNo != null'>
            <![CDATA[ AND sci.policy_no=#{policyNo} ]]>
        </if>
        <!--保险公司-->
        <if test='companyCode != null'>
            <![CDATA[ AND sci.company_code=#{companyCode} ]]>
        </if>

        <!--投保时间-->
        <if test='approvedTimeStart != null'>
            <![CDATA[ AND sci.approved_time>=#{approvedTimeStart} ]]>
        </if>
        <if test='approvedTimeEnd != null'>
            <![CDATA[ AND sci.approved_time<=#{approvedTimeEnd} ]]>
        </if>

        <!--生效时间-->
        <if test='enforceTimeStart != null'>
            <![CDATA[ AND sci.enforce_time>=#{enforceTimeStart} ]]>
        </if>
        <if test='enforceTimeEnd != null'>
            <![CDATA[ AND sci.enforce_time<=#{enforceTimeEnd} ]]>
        </if>
        <!--所属机构-->
        <if test='orgCodeList != null and orgCodeList.size() > 0'>
            AND owner_third_org in
            <foreach collection="orgCodeList" open="(" close=")" separator="," item = "item">
                #{item}
            </foreach>
        </if>


        <!--险种编码-->
        <if test='productCodeList != null and productCodeList.size() > 0'>
            AND sci.product_code in
            <foreach collection="productCodeList" open="(" close=")" separator="," item = "item">
                #{item}
            </foreach>
        </if>
        <!--编码-->
        <if test='companyCodeList != null and companyCodeList.size() > 0'>
            AND sci.company_code in
            <foreach collection="companyCodeList" open="(" close=")" separator="," item = "item">
                #{item}
            </foreach>
        </if>

        <!--编码-->
        <if test='commodityNameList != null and commodityNameList.size() > 0'>
            AND sci.commodity_name in
            <foreach collection="commodityNameList" open="(" close=")" separator="," item = "item">
                #{item}
            </foreach>
        </if>

        <!--编码-->
        <if test='goodsNameList != null and goodsNameList.size() > 0'>
            AND sci.goods_name in
            <foreach collection="goodsNameList" open="(" close=")" separator="," item = "item">
                #{item}
            </foreach>
        </if>

        <!--工号-->
        <if test='ownerThirdCode != null'>
            AND sci.owner_third_code=#{ownerThirdCode}
        </if>

        <!--保单大类 个团车财-->
        <if test='policyProductType != null'>
            AND sci.policy_product_type=#{policyProductType}
        </if>

        <!--保单状态-->
        <if test='policyStatus != null'>
            AND sci.policy_status=#{policyStatus}
        </if>
        <!--长度短险-->
        <if test='longShortFlag != null'>
            AND sci.long_short_flag=#{longShortFlag}
        </if>
        <!--险种编码-->
        <if test='productCode != null'>
            AND sci.product_code=#{productCode}
        </if>

        <!---->
        <if test='ruralProxyFlag != null'>
            AND sci.rural_proxy_flag=#{ruralProxyFlag}
        </if>
        <!---->
        <if test='distributionFlag != null'>
            AND sci.distribution_flag=#{distributionFlag}
        </if>

        <!--批单号-->
        <if test='endorsementNo != null'>
            AND sci.endorsement_no=#{endorsementNo}
        </if>
        <!--所属渠道-->
        <if test='ownerChannelCode != null'>
            AND sci.owner_channel_code=#{ownerChannelCode}
        </if>
        <!--结算科目-->
        AND sci.settlement_subject_code='FIRST_BASIC_COMM'
    </select>
</mapper>