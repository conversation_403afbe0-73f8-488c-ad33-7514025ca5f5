<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safepg.InsuranceOrderCommissionLongMapper">

    <select id="querySafePgCommission"
            resultType="com.cfpamf.ms.insur.admin.pojo.vo.commission.SmPgLongInsuranceCommissionVO">
        SELECT
            term_num as termNum,
            insured_id_number as insuredIdNumber,
            app_status as appStatus,
            product_name as productName,
            company_name as companyName,
            product_id as productId,
            policy_no as policyNo,
            channel as channel,
            insured_person_name AS insuredPersonName,
            applicant_person_name AS applicantPersonName,
            total_amount as totalAmount,
            converted_amount as convertedAmount,
            settlement_commission as settlementCommission,
            pay_commission_amount as payCommissionAmount,
            recommend_region_name AS recommendRegionName,
            recommend_organization_name AS recommendOrganizationName,
            recommend_user_name AS recommendUserName,
            recommend_user_id as recommendUserId,
            invite_name inviteName,
            create_time as createTime,
            account_time as accountTime,
            payment_time,
            recommend_id,
            customer_admin_id,
            order_id as fhOrderId,
            customer_admin_name AS customerAdminName,
            customer_admin_id AS customerAdminId,
            user_admin_name as userAdminName,
            user_master_name as userMasterName,
            original_amount as originalAmount,
            activity_code,product_activity_code as villageActivity,type,
            loan_month,loan_year,buyback_3d,is_label
            from phoenix.dwd_insurance_order_commission_long
            where 1=1
            <if test='isLabel != null'>
                <if test="isLabel == 1">
                    and is_label = 1
                </if>
                <if test="isLabel == 0">
                    and is_label = 0
                </if>
            </if>
            <if test='loanMonth != null'>
                <if test="loanMonth == 1">
                    and loan_month = 1
                </if>
                <if test="loanMonth == 0">
                    and loan_month = 0
                </if>
            </if>
            <if test='loanYear != null'>
                <if test="loanYear == 1">
                    and loan_year = 1
                </if>
                <if test="loanYear == 0">
                    and loan_year = 0
                </if>
            </if>
            <if test='buyback3d != null'>
                <if test="buyback3d == 1">
                    and buyback_3d = 1
                </if>
                <if test="buyback3d == 0">
                    and buyback_3d = 0
                </if>
            </if>
            <if test='villageActivity != null'>
                <if test="villageActivity == 1">
                    and (product_activity_code is null or product_activity_code ='')
                </if>
                <if test="villageActivity == 0">
                    and product_activity_code is not null and product_activity_code !=''
                </if>
            </if>
            <if test='createDateStart != null'>
                <![CDATA[ AND create_time>=#{createDateStart} ]]>
            </if>
            <if test='createDateEnd != null'>
                <![CDATA[ AND create_time<=#{createDateEnd} ]]>
            </if>
            <if test='paymentDateStart != null'>
                <![CDATA[ AND payment_time>=#{paymentDateStart} ]]>
            </if>
            <if test='paymentDateEnd != null'>
                <![CDATA[ AND payment_time<=#{paymentDateEnd} ]]>
            </if>
            <if test='orderNo != null'>
                <if test="fullMatch">
                    AND order_id = #{orderNo}
                </if>
                <if test="!fullMatch">
                    AND order_id LIKE CONCAT(#{orderNo},'%')
                </if>
            </if>
            <if test='inviteName != null'>
                and invite_name = #{inviteName}
            </if>
            <if test='policyNo !=  null'>
                AND policy_no like CONCAT(#{policyNo},'%')
            </if>
            <if test="termNum != null ">
                and term_num = #{termNum}
            </if>
            <if test='regionName != null'>
                AND recommend_region_name=#{regionName}
            </if>
            <if test='orgName != null'>
                AND recommend_organization_name=#{orgName}
            </if>

            <if test='applicantdName != null'>
                AND
                <if test="applicantdType == 1">
                    applicant_person_name LIKE CONCAT(#{applicantdName},'%')
                </if>
                <if test="applicantdType == 2">
                    aplicant_id_number LIKE CONCAT(#{applicantdName},'%')
                </if>
                <if test="applicantdType == 3">
                    applicant_cell_phone LIKE CONCAT(#{applicantdName},'%')
                </if>
            </if>
            <if test='insuredName != null'>
                AND
                <if test="insuredType == 1">
                    insured_person_name LIKE CONCAT(#{insuredName},'%')
                </if>
                <if test="insuredType == 2">
                    insured_id_number LIKE CONCAT(#{insuredName},'%')
                </if>
                <if test="insuredType == 3">
                    insured_cell_phone LIKE CONCAT(#{insuredName},'%')
                </if>
            </if>
            <if test='productId != null'>
                AND product_id=#{productId}
            </if>
            <if test='productIds != null and productIds.size() > 0'>
                AND product_id in
                <foreach collection="productIds" open="(" close=")" separator="," item = "item">
                    #{item}
                </foreach>
            </if>
            <if test='recommendId != null'>
                AND recommend_id=#{recommendId}
            </if>
            <if test='customerAdminId != null'>
                AND customer_admin_id=#{customerAdminId}
            </if>
            <if test='companyId != null'>
                AND company_id=#{companyId}
            </if>
            <if test='productAttrCode != null and productAttrCode=="person"'>
                AND product_attr_code=#{productAttrCode}
            </if>
            <if test='productAttrCode != null and productAttrCode=="group"'>
                AND product_attr_code in ('group','employer')
            </if>

            <if test='channel != null'>
                AND channel=#{channel}
            </if>
            <if test='accountDateStart != null'>
                <![CDATA[ AND account_time>=#{accountDateStart}  ]]>
            </if>
            <if test='accountDateEnd != null'>
                <![CDATA[ AND account_time<=#{accountDateEnd}  ]]>
            </if>
            <if test='appStatus != null '>
                AND app_status = #{appStatus}
            </if>

            <if test='userId != null'>
                AND recommend_id=#{userId}
            </if>

            <if test='orgPath !=null '>
                and recommend_org_path like CONCAT(#{orgPath},'%')
            </if>
            <if test="reconciliationStatus != null">
                <if test="reconciliationStatus == 0">
                    and reconciliation_status = 0
                </if>
                <if test="reconciliationStatus == 1">
                    and reconciliation_status is null
                </if>
                <if test="reconciliationStatus == 2">
                    and reconciliation_status in ('1','2','3','4','5','6','7','9')
                </if>
            </if>
            <if test="addCommissionStatus != null">
                <if test="addCommissionStatus == 0">
                    and add_commission_amount = 0
                </if>
                <if test="addCommissionStatus == 1">
                    and add_commission_amount > 0
                </if>
            </if>
            ORDER BY account_time DESC
            <if test='startRow != null'>
                LIMIT  #{size} OFFSET #{startRow}
            </if>
    </select>
    <select id="queryCommissionCount" resultType="java.lang.Long">
        select count(1)
        from phoenix.dwd_insurance_order_commission_long
        where 1=1
        <if test='isLabel != null'>
            <if test="isLabel == 1">
                and is_label = 1
            </if>
            <if test="isLabel == 0">
                and is_label = 0
            </if>
        </if>
        <if test='loanMonth != null'>
            <if test="loanMonth == 1">
                and loan_month = 1
            </if>
            <if test="loanMonth == 0">
                and loan_month = 0
            </if>
        </if>
        <if test='loanYear != null'>
            <if test="loanYear == 1">
                and loan_year = 1
            </if>
            <if test="loanYear == 0">
                and loan_year = 0
            </if>
        </if>
        <if test='buyback3d != null'>
            <if test="buyback3d == 1">
                and buyback_3d = 1
            </if>
            <if test="buyback3d == 0">
                and buyback_3d = 0
            </if>
        </if>
        <if test='villageActivity != null'>
            <if test="villageActivity == 1">
                and (product_activity_code is null or product_activity_code ='')
            </if>
            <if test="villageActivity == 0">
                and product_activity_code is not null and product_activity_code !=''
            </if>
        </if>
        <if test='createDateStart != null'>
            <![CDATA[ AND create_time>=#{createDateStart} ]]>
        </if>
        <if test='createDateEnd != null'>
            <![CDATA[ AND create_time<=#{createDateEnd} ]]>
        </if>
        <if test='paymentDateStart != null'>
            <![CDATA[ AND payment_time>=#{paymentDateStart} ]]>
        </if>
        <if test='paymentDateEnd != null'>
            <![CDATA[ AND payment_time<=#{paymentDateEnd} ]]>
        </if>
        <if test='orderNo != null'>
            <if test="fullMatch">
                AND order_id = #{orderNo}
            </if>
            <if test="!fullMatch">
                AND order_id LIKE CONCAT(#{orderNo},'%')
            </if>
        </if>
        <if test='inviteName != null'>
            and invite_name = #{inviteName}
        </if>
        <if test='policyNo !=  null'>
            AND policy_no like CONCAT(#{policyNo},'%')
        </if>
        <if test="termNum != null ">
            and term_num = #{termNum}
        </if>
        <if test='regionName != null'>
            AND recommend_region_name=#{regionName}
        </if>
        <if test='orgName != null'>
            AND recommend_organization_name=#{orgName}
        </if>

        <if test='applicantdName != null'>
            AND
            <if test="applicantdType == 1">
                applicant_person_name LIKE CONCAT(#{applicantdName},'%')
            </if>
            <if test="applicantdType == 2">
                aplicant_id_number LIKE CONCAT(#{applicantdName},'%')
            </if>
            <if test="applicantdType == 3">
                applicant_cell_phone LIKE CONCAT(#{applicantdName},'%')
            </if>
        </if>
        <if test='insuredName != null'>
            AND
            <if test="insuredType == 1">
                insured_person_name LIKE CONCAT(#{insuredName},'%')
            </if>
            <if test="insuredType == 2">
                insured_id_number LIKE CONCAT(#{insuredName},'%')
            </if>
            <if test="insuredType == 3">
                insured_cell_phone LIKE CONCAT(#{insuredName},'%')
            </if>
        </if>
        <if test='productId != null'>
            AND product_id=#{productId}
        </if>
        <if test='productIds != null and productIds.size() > 0'>
            AND product_id in
            <foreach collection="productIds" open="(" close=")" separator="," item = "item">
                #{item}
            </foreach>
        </if>
        <if test='recommendId != null'>
            AND recommend_id=#{recommendId}
        </if>
        <if test='customerAdminId != null'>
            AND customer_admin_id=#{customerAdminId}
        </if>
        <if test='companyId != null'>
            AND company_id=#{companyId}
        </if>
        <if test='productAttrCode != null and productAttrCode=="person"'>
            AND product_attr_code=#{productAttrCode}
        </if>
        <if test='productAttrCode != null and productAttrCode=="group"'>
            AND product_attr_code in ('group','employer')
        </if>

        <if test='channel != null'>
            AND channel=#{channel}
        </if>
        <if test='accountDateStart != null'>
            <![CDATA[ AND account_time>=#{accountDateStart}  ]]>
        </if>
        <if test='accountDateEnd != null'>
            <![CDATA[ AND account_time<=#{accountDateEnd}  ]]>
        </if>
        <if test='appStatus != null '>
            AND app_status = #{appStatus}
        </if>

        <if test='userId != null'>
            AND recommend_id=#{userId}
        </if>

        <if test='orgPath !=null '>
            and recommend_org_path like CONCAT(#{orgPath},'%')
        </if>
        <if test="reconciliationStatus != null">
            <if test="reconciliationStatus == 0">
                and reconciliation_status = 0
            </if>
            <if test="reconciliationStatus == 1">
                and reconciliation_status is null
            </if>
            <if test="reconciliationStatus == 2">
                and reconciliation_status in ('1','2','3','4','5','6','7','9')
            </if>
        </if>
        <if test="addCommissionStatus != null">
            <if test="addCommissionStatus == 0">
                and add_commission_amount = 0
            </if>
            <if test="addCommissionStatus == 1">
                and add_commission_amount > 0
            </if>
        </if>
    </select>
    <select id="queryLongCommissionSummary" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmOrderSummaryVO">
        SELECT
        sum(total_amount) as orderAmount,
        SUM(pay_commission_amount) AS paymentAmount,
        SUM(settlement_commission) AS settlementAmount,
        SUM(add_commission_amount) AS addCommissionAmount,
        SUM(converted_amount) as convertedAmount
        FROM phoenix.dwd_insurance_order_commission_long
        WHERE 1=1
        <if test='isLabel != null'>
            <if test="isLabel == 1">
                and is_label = 1
            </if>
            <if test="isLabel == 0">
                and is_label = 0
            </if>
        </if>
        <if test='loanMonth != null'>
            <if test="loanMonth == 1">
                and loan_month = 1
            </if>
            <if test="loanMonth == 0">
                and loan_month = 0
            </if>
        </if>
        <if test='loanYear != null'>
            <if test="loanYear == 1">
                and loan_year = 1
            </if>
            <if test="loanYear == 0">
                and loan_year = 0
            </if>
        </if>
        <if test='buyback3d != null'>
            <if test="buyback3d == 1">
                and buyback_3d = 1
            </if>
            <if test="buyback3d == 0">
                and buyback_3d = 0
            </if>
        </if>
        <if test='villageActivity != null'>
            <if test="villageActivity == 1">
                and (product_activity_code is null or product_activity_code ='')
            </if>
            <if test="villageActivity == 0">
                and product_activity_code is not null and product_activity_code !=''
            </if>
        </if>
        <if test='createDateStart != null'>
            <![CDATA[ AND create_time>=#{createDateStart} ]]>
        </if>
        <if test='createDateEnd != null'>
            <![CDATA[ AND create_time<=#{createDateEnd} ]]>
        </if>
        <if test='paymentDateStart != null'>
            <![CDATA[ AND payment_time>=#{paymentDateStart} ]]>
        </if>
        <if test='paymentDateEnd != null'>
            <![CDATA[ AND payment_time<=#{paymentDateEnd} ]]>
        </if>
        <if test='orderNo != null'>
            <if test="fullMatch">
                AND order_id = #{orderNo}
            </if>
            <if test="!fullMatch">
                AND order_id LIKE CONCAT(#{orderNo},'%')
            </if>
        </if>
        <if test='inviteName != null'>
            and invite_name = #{inviteName}
        </if>
        <if test='policyNo !=  null'>
            AND policy_no like CONCAT(#{policyNo},'%')
        </if>
        <if test="termNum != null ">
            and term_num = #{termNum}
        </if>
        <if test='regionName != null'>
            AND recommend_region_name=#{regionName}
        </if>
        <if test='orgName != null'>
            AND recommend_organization_name=#{orgName}
        </if>

        <if test='applicantdName != null'>
            AND
            <if test="applicantdType == 1">
                applicant_person_name LIKE CONCAT(#{applicantdName},'%')
            </if>
            <if test="applicantdType == 2">
                aplicant_id_number LIKE CONCAT(#{applicantdName},'%')
            </if>
            <if test="applicantdType == 3">
                applicant_cell_phone LIKE CONCAT(#{applicantdName},'%')
            </if>
        </if>
        <if test='insuredName != null'>
            AND
            <if test="insuredType == 1">
                insured_person_name LIKE CONCAT(#{insuredName},'%')
            </if>
            <if test="insuredType == 2">
                insured_id_number LIKE CONCAT(#{insuredName},'%')
            </if>
            <if test="insuredType == 3">
                insured_cell_phone LIKE CONCAT(#{insuredName},'%')
            </if>
        </if>
        <if test='productId != null'>
            AND product_id=#{productId}
        </if>
        <if test='productIds != null and productIds.size() > 0'>
            AND product_id in
            <foreach collection="productIds" open="(" close=")" separator="," item = "item">
                #{item}
            </foreach>
        </if>
        <if test='recommendId != null'>
            AND recommend_id=#{recommendId}
        </if>
        <if test='customerAdminId != null'>
            AND customer_admin_id=#{customerAdminId}
        </if>
        <if test='companyId != null'>
            AND company_id=#{companyId}
        </if>
        <if test='productAttrCode != null and productAttrCode=="person"'>
            AND product_attr_code=#{productAttrCode}
        </if>
        <if test='productAttrCode != null and productAttrCode=="group"'>
            AND product_attr_code in ('group','employer')
        </if>

        <if test='channel != null'>
            AND channel=#{channel}
        </if>
        <if test='accountDateStart != null'>
            <![CDATA[ AND account_time>=#{accountDateStart}  ]]>
        </if>
        <if test='accountDateEnd != null'>
            <![CDATA[ AND account_time<=#{accountDateEnd}  ]]>
        </if>
        <if test='appStatus != null '>
            AND app_status = #{appStatus}
        </if>

        <if test='userId != null'>
            AND recommend_id=#{userId}
        </if>

        <if test='orgPath !=null '>
            and recommend_org_path like CONCAT(#{orgPath},'%')
        </if>
        <if test="reconciliationStatus != null">
            <if test="reconciliationStatus == 0">
                and reconciliation_status = 0
            </if>
            <if test="reconciliationStatus == 1">
                and reconciliation_status is null
            </if>
            <if test="reconciliationStatus == 2">
                and reconciliation_status in ('1','2','3','4','5','6','7','9')
            </if>
        </if>
        <if test="addCommissionStatus != null">
            <if test="addCommissionStatus == 0">
                and add_commission_amount = 0
            </if>
            <if test="addCommissionStatus == 1">
                and add_commission_amount > 0
            </if>
        </if>
    </select>
</mapper>