<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safepg.InsuranceOrderCommissionShortMapper">

    <select id="listOrderCommissionV3" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmOrderListVO">
        SELECT
        channel,
        create_time as createTime,
        account_time as accountTime,
        payment_time as paymentTime,
        fh_order_id as fhOrderId,
        total_amount,
        start_time as startTime,
        end_time as endTime,
        recommend_region_name AS recommendRegionName,
        recommend_organization_name AS recommendOrganizationName,
        recommend_user_name AS recommendUserName,
        recommend_user_name AS recommendUserMobile,
        recommend_user_id AS recommendUserId,
        user_master_name AS userMasterName,
        user_admin_name AS userAdminName ,
        entry_date AS entryDate,
        post_name AS postName,
        applicant_person_name AS applicantPersonName,
        aplicant_id_number AS aplicantIdNumber,
        applicant_cell_phone AS applicantCellPhone,
        insured_person_name AS insuredPersonName,
        insured_id_number AS insuredIdNumber,
        insured_cell_phone AS insuredCellPhone,
        product_name,
        product_id AS productId,
        plan_name as planName,
        company_name as companyName,
        app_status as appStatus,
        policy_no policyNo,
        payment_proportion as paymentProportion,
        paymeny_commission as paymenyCommission,
        settlement_proportion AS settlementProportion,
        settlement_commission AS settlementCommission,
        converted_amount AS convertedAmount,
        amount as amount,
        pay_type as pay_type,
        pay_period as pay_period,
        valid_period as valid_period,
        converted_proportion AS convertedProportion,
        final_status As finalStatus
        ,talk_order talkOrder,invite_name as inviteName
        ,detail_create_time detailCreateTime,
        start_time as startTime,product_type as productType,activity_code as activity_code,
        village_activity as villageActivity,type as type
        ,loan_month,loan_year,buyback_3d,order_type,is_label,is_loan_flag,has_main_insurance,is_valid_loan
        FROM phoenix.dwd_insurance_order_commission_short
        WHERE 1=1
        <if test='isLabel != null'>
            <if test="isLabel == 1">
                and is_label = 1
            </if>
            <if test="isLabel == 0">
                and is_label = 0
            </if>
        </if>

        <if test='isLoanFlag != null'>
            and is_loan_flag = #{isLoanFlag}
        </if>
        <if test='isValidLoan != null'>
            and is_valid_loan = #{isValidLoan}
        </if>
        <if test='hasMainInsurance != null'>
            and has_main_insurance = #{hasMainInsurance}
        </if>
        <if test='loanMonth != null'>
            <if test="loanMonth == 1">
                and loan_month = 1
            </if>
            <if test="loanMonth == 0">
                and loan_month = 0
            </if>
        </if>
        <if test='loanYear != null'>
            <if test="loanYear == 1">
                and loan_year = 1
            </if>
            <if test="loanYear == 0">
                and loan_year = 0
            </if>
        </if>
        <if test='buyback3d != null'>
            <if test="buyback3d == 1">
                and buyback_3d = 1
            </if>
            <if test="buyback3d == 0">
                and buyback_3d = 0
            </if>
        </if>
        <if test="orderType!=null">
            and order_type = #{orderType}
        </if>
        <if test='villageActivity != null'>
            <if test="villageActivity == 1">
                and (village_activity is null or village_activity ='')
            </if>
            <if test="villageActivity == 0">
                and village_activity is not null and village_activity !=''
            </if>
        </if>

        <if test='createDateStart != null'>
            <![CDATA[ AND create_time>=#{createDateStart} ]]>
        </if>
        <if test='createDateEnd != null'>
            <![CDATA[ AND create_time<=#{createDateEnd} ]]>
        </if>
        <if test='paymentDateStart != null'>
            <![CDATA[ AND payment_time>=#{paymentDateStart} ]]>
        </if>
        <if test='paymentDateEnd != null'>
            <![CDATA[ AND payment_time<=#{paymentDateEnd} ]]>
        </if>
        <if test='orderNo != null'>
            <if test="fullMatch">
                AND fh_order_id = #{orderNo}
            </if>
            <if test="!fullMatch">
                AND fh_order_id LIKE CONCAT(#{orderNo},'%')
            </if>
        </if>
        <if test='talkOrder != null'>
            <if test="talkOrder == true">
                and talk_order == true
            </if>
            <if test="talkOrder == false">
                and talk_order == false
            </if>
        </if>
        <if test='inviteName != null'>
            and invite_name = #{inviteName}
        </if>
        <if test='policyNo !=  null'>
            AND policy_no like CONCAT(#{policyNo},'%')
        </if>
        <if test='regionName != null'>
            AND recommend_region_name=#{regionName}
        </if>
        <if test='orgName != null'>
            AND recommend_organization_name=#{orgName}
        </if>

        <if test='applicantdName != null'>
            AND
            <if test="applicantdType == 1">
                applicant_person_name LIKE CONCAT(#{applicantdName},'%')
            </if>
            <if test="applicantdType == 2">
                aplicant_id_number LIKE CONCAT(#{applicantdName},'%')
            </if>
            <if test="applicantdType == 3">
                applicant_cell_phone LIKE CONCAT(#{applicantdName},'%')
            </if>
        </if>
        <if test='insuredName != null'>
            AND
            <if test="insuredType == 1">
                insured_person_name LIKE CONCAT(#{insuredName},'%')
            </if>
            <if test="insuredType == 2">
                insured_id_number LIKE CONCAT(#{insuredName},'%')
            </if>
            <if test="insuredType == 3">
                insured_cell_phone LIKE CONCAT(#{insuredName},'%')
            </if>
        </if>
        <if test='productId != null'>
            AND product_id=#{productId}
        </if>
        <if test='productIds != null and productIds.size() > 0'>
            AND product_id in
            <foreach collection="productIds" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test='recommendId != null'>
            AND recommend_user_id=#{recommendId}
        </if>

        <if test='companyId != null'>
            AND company_id=#{companyId}
        </if>
        <if test='productAttrCode != null and productAttrCode=="person"'>
            AND product_attr_code=#{productAttrCode}
        </if>
        <if test='productAttrCode != null and productAttrCode=="group"'>
            AND product_attr_code in ('group','employer')
        </if>

        <if test='channel != null'>
            AND channel=#{channel}
        </if>
        <if test='accountDateStart != null'>
            <![CDATA[ AND account_time>=#{accountDateStart}  ]]>
        </if>
        <if test='accountDateEnd != null'>
            <![CDATA[ AND account_time<=#{accountDateEnd}  ]]>
        </if>
        <if test='appStatus != null '>
            AND app_status = #{appStatus}
        </if>

        <if test='userId != null'>
            AND recommend_user_id=#{userId}
        </if>

        <if test='orgPath !=null '>
            and recommend_org_path like CONCAT(#{orgPath},'%')
        </if>
        <if test='productType2 != null'>
            and product_type = #{productType2}
        </if>

        <if test='startTimeStart != null'>
            <![CDATA[ AND start_time>=#{startTimeStart}  ]]>
        </if>
        <if test='startTimeEnd != null'>
            <![CDATA[ AND start_time<=#{startTimeEnd}  ]]>
        </if>
        ORDER BY account_time DESC
        <if test='startRow != null'>
            LIMIT #{size} OFFSET #{startRow}
        </if>
    </select>
    <select id="countOrderCommissionV3" resultType="java.lang.Integer">
        SELECT
            COUNT(1)
        FROM phoenix.dwd_insurance_order_commission_short
        WHERE 1=1
        <if test='isLabel != null'>
            <if test="isLabel == 1">
                and is_label = 1
            </if>
            <if test="isLabel == 0">
                and is_label = 0
            </if>
        </if>
        <if test='loanMonth != null'>
            <if test="loanMonth == 1">
                and loan_month = 1
            </if>
            <if test="loanMonth == 0">
                and loan_month = 0
            </if>
        </if>
        <if test='isLoanFlag != null'>
            and is_loan_flag = #{isLoanFlag}
        </if>
        <if test='isValidLoan != null'>
            and is_valid_loan = #{isValidLoan}
        </if>
        <if test='hasMainInsurance != null'>
            and has_main_insurance = #{hasMainInsurance}
        </if>
        <if test='loanYear != null'>
            <if test="loanYear == 1">
                and loan_year = 1
            </if>
            <if test="loanYear == 0">
                and loan_year = 0
            </if>
        </if>
        <if test='buyback3d != null'>
            <if test="buyback3d == 1">
                and buyback_3d = 1
            </if>
            <if test="buyback3d == 0">
                and buyback_3d = 0
            </if>
        </if>
        <if test="orderType!=null">
            and order_type = #{orderType}
        </if>
        <if test='villageActivity != null'>
            <if test="villageActivity == 1">
                and (village_activity is null or village_activity ='')
            </if>
            <if test="villageActivity == 0">
                and village_activity is not null and village_activity !=''
            </if>
        </if>

        <if test='createDateStart != null'>
            <![CDATA[ AND create_time>=#{createDateStart} ]]>
        </if>
        <if test='createDateEnd != null'>
            <![CDATA[ AND create_time<=#{createDateEnd} ]]>
        </if>
        <if test='paymentDateStart != null'>
            <![CDATA[ AND payment_time>=#{paymentDateStart} ]]>
        </if>
        <if test='paymentDateEnd != null'>
            <![CDATA[ AND payment_time<=#{paymentDateEnd} ]]>
        </if>
        <if test='orderNo != null'>
            <if test="fullMatch">
                AND fh_order_id = #{orderNo}
            </if>
            <if test="!fullMatch">
                AND fh_order_id LIKE CONCAT(#{orderNo},'%')
            </if>
        </if>
        <if test='talkOrder != null'>
            <if test="talkOrder == true">
                and talk_order == true
            </if>
            <if test="talkOrder == false">
                and talk_order == false
            </if>
        </if>
        <if test='inviteName != null'>
            and invite_name = #{inviteName}
        </if>
        <if test='policyNo !=  null'>
            AND policy_no like CONCAT(#{policyNo},'%')
        </if>
        <if test='regionName != null'>
            AND recommend_region_name=#{regionName}
        </if>
        <if test='orgName != null'>
            AND recommend_organization_name=#{orgName}
        </if>

        <if test='applicantdName != null'>
            AND
            <if test="applicantdType == 1">
                applicant_person_name LIKE CONCAT(#{applicantdName},'%')
            </if>
            <if test="applicantdType == 2">
                aplicant_id_number LIKE CONCAT(#{applicantdName},'%')
            </if>
            <if test="applicantdType == 3">
                applicant_cell_phone LIKE CONCAT(#{applicantdName},'%')
            </if>
        </if>
        <if test='insuredName != null'>
            AND
            <if test="insuredType == 1">
                insured_person_name LIKE CONCAT(#{insuredName},'%')
            </if>
            <if test="insuredType == 2">
                insured_id_number LIKE CONCAT(#{insuredName},'%')
            </if>
            <if test="insuredType == 3">
                insured_cell_phone LIKE CONCAT(#{insuredName},'%')
            </if>
        </if>
        <if test='productId != null'>
            AND product_id=#{productId}
        </if>
        <if test='productIds != null and productIds.size() > 0'>
            AND product_id in
            <foreach collection="productIds" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test='recommendId != null'>
            AND recommend_user_id=#{recommendId}
        </if>

        <if test='companyId != null'>
            AND company_id=#{companyId}
        </if>
        <if test='productAttrCode != null and productAttrCode=="person"'>
            AND product_attr_code=#{productAttrCode}
        </if>
        <if test='productAttrCode != null and productAttrCode=="group"'>
            AND product_attr_code in ('group','employer')
        </if>

        <if test='channel != null'>
            AND channel=#{channel}
        </if>
        <if test='accountDateStart != null'>
            <![CDATA[ AND account_time>=#{accountDateStart}  ]]>
        </if>
        <if test='accountDateEnd != null'>
            <![CDATA[ AND account_time<=#{accountDateEnd}  ]]>
        </if>
        <if test='appStatus != null '>
            AND app_status = #{appStatus}
        </if>

        <if test='userId != null'>
            AND recommend_user_id=#{userId}
        </if>

        <if test='orgPath !=null '>
            and recommend_org_path like CONCAT(#{orgPath},'%')
        </if>
        <if test='productType2 != null'>
            and product_type = #{productType2}
        </if>

        <if test='startTimeStart != null'>
            <![CDATA[ AND start_time>=#{startTimeStart}  ]]>
        </if>
        <if test='startTimeEnd != null'>
            <![CDATA[ AND start_time<=#{startTimeEnd}  ]]>
        </if>
    </select>
    <select id="getOrderCommissionSummaryV3" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmOrderSummaryVO">
        SELECT
            SUM(total_amount) AS orderAmount,
            SUM(paymeny_commission) AS paymentAmount,
            SUM(settlement_commission) AS settlementAmount,
            SUM(add_commission_amount) AS addCommissionAmount,
            SUM(converted_amount) as convertedAmount
        FROM phoenix.dwd_insurance_order_commission_short
        WHERE 1=1
        <if test='isLabel != null'>
            <if test="isLabel == 1">
                and is_label = 1
            </if>
            <if test="isLabel == 0">
                and is_label = 0
            </if>
        </if>
        <if test='loanMonth != null'>
            <if test="loanMonth == 1">
                and loan_month = 1
            </if>
            <if test="loanMonth == 0">
                and loan_month = 0
            </if>
        </if>
        <if test='loanYear != null'>
            <if test="loanYear == 1">
                and loan_year = 1
            </if>
            <if test="loanYear == 0">
                and loan_year = 0
            </if>
        </if>
        <if test='buyback3d != null'>
            <if test="buyback3d == 1">
                and buyback_3d = 1
            </if>
            <if test="buyback3d == 0">
                and buyback_3d = 0
            </if>
        </if>
        <if test="orderType!=null">
            and order_type = #{orderType}
        </if>
        <if test='villageActivity != null'>
            <if test="villageActivity == 1">
                and (village_activity is null or village_activity ='')
            </if>
            <if test="villageActivity == 0">
                and village_activity is not null and village_activity !=''
            </if>
        </if>

        <if test='createDateStart != null'>
            <![CDATA[ AND create_time>=#{createDateStart} ]]>
        </if>
        <if test='createDateEnd != null'>
            <![CDATA[ AND create_time<=#{createDateEnd} ]]>
        </if>
        <if test='paymentDateStart != null'>
            <![CDATA[ AND payment_time>=#{paymentDateStart} ]]>
        </if>
        <if test='paymentDateEnd != null'>
            <![CDATA[ AND payment_time<=#{paymentDateEnd} ]]>
        </if>
        <if test='orderNo != null'>
            <if test="fullMatch">
                AND fh_order_id = #{orderNo}
            </if>
            <if test="!fullMatch">
                AND fh_order_id LIKE CONCAT(#{orderNo},'%')
            </if>
        </if>
        <if test='talkOrder != null'>
            <if test="talkOrder == true">
                and talk_order == true
            </if>
            <if test="talkOrder == false">
                and talk_order == false
            </if>
        </if>
        <if test='inviteName != null'>
            and invite_name = #{inviteName}
        </if>
        <if test='policyNo !=  null'>
            AND policy_no like CONCAT(#{policyNo},'%')
        </if>
        <if test='regionName != null'>
            AND recommend_region_name=#{regionName}
        </if>
        <if test='orgName != null'>
            AND recommend_organization_name=#{orgName}
        </if>

        <if test='applicantdName != null'>
            AND
            <if test="applicantdType == 1">
                applicant_person_name LIKE CONCAT(#{applicantdName},'%')
            </if>
            <if test="applicantdType == 2">
                aplicant_id_number LIKE CONCAT(#{applicantdName},'%')
            </if>
            <if test="applicantdType == 3">
                applicant_cell_phone LIKE CONCAT(#{applicantdName},'%')
            </if>
        </if>
        <if test='insuredName != null'>
            AND
            <if test="insuredType == 1">
                insured_person_name LIKE CONCAT(#{insuredName},'%')
            </if>
            <if test="insuredType == 2">
                insured_id_number LIKE CONCAT(#{insuredName},'%')
            </if>
            <if test="insuredType == 3">
                insured_cell_phone LIKE CONCAT(#{insuredName},'%')
            </if>
        </if>
        <if test='productId != null'>
            AND product_id=#{productId}
        </if>
        <if test='productIds != null and productIds.size() > 0'>
            AND product_id in
            <foreach collection="productIds" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test='recommendId != null'>
            AND recommend_user_id=#{recommendId}
        </if>

        <if test='companyId != null'>
            AND company_id=#{companyId}
        </if>
        <if test='productAttrCode != null and productAttrCode=="person"'>
            AND product_attr_code=#{productAttrCode}
        </if>
        <if test='productAttrCode != null and productAttrCode=="group"'>
            AND product_attr_code in ('group','employer')
        </if>

        <if test='channel != null'>
            AND channel=#{channel}
        </if>
        <if test='accountDateStart != null'>
            <![CDATA[ AND account_time>=#{accountDateStart}  ]]>
        </if>
        <if test='accountDateEnd != null'>
            <![CDATA[ AND account_time<=#{accountDateEnd}  ]]>
        </if>
        <if test='appStatus != null '>
            AND app_status = #{appStatus}
        </if>

        <if test='userId != null'>
            AND recommend_user_id=#{userId}
        </if>

        <if test='orgPath !=null '>
            and recommend_org_path like CONCAT(#{orgPath},'%')
        </if>
        <if test='productType2 != null'>
            and product_type = #{productType2}
        </if>

        <if test='startTimeStart != null'>
            <![CDATA[ AND start_time>=#{startTimeStart}  ]]>
        </if>
        <if test='startTimeEnd != null'>
            <![CDATA[ AND start_time<=#{startTimeEnd}  ]]>
        </if>
    </select>
</mapper>