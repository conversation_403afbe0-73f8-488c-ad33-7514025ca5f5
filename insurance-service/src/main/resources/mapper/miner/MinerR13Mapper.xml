<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cfpamf.ms.insur.admin.dao.miner.MinerR13Mapper">

    <select id="select36R13" resultType="com.cfpamf.ms.insur.admin.pojo.po.miner.TempMinerR13">
        select due_month, area_name, department_name org_name, r13, pt
        from insurance.rpt_insure_department_r13
        where pt =
              (select max(pt)
               from insurance.rpt_insure_department_r13)
          and due_month between '2023-03' and '2023-06'
    </select>
</mapper>