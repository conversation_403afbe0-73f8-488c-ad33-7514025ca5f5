<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.admin.dao.dc.SmDcReportMapper">

    <select id="listSmCustTransferRegion" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmCustTransferRegionVO">
        SELECT
            area_name AS regionName,
            SUM(insured_count) AS safesPerQty,
            SUM(loaner_count) AS loanPerQty,
            SUM( offline_loaner_count) AS safesBorrowerPerQty,
            SUM( offline_shareloan_count) AS safesCoBorrowerPerQty,
            SUM( offline_guarantor_count) AS safesGuarantorPerQty,
            SUM(loan_rel_count) AS safesRelevanterPerQty,

            SUM(no_loaner_count) AS safesNoLoanPerQty,
            CASE WHEN SUM(loan_rel_count)=0 THEN 0 ELSE SUM(offline_shareloan_count+offline_guarantor_count)*1.0/SUM(loan_rel_count) END AS safesOtherTransferRatio,
            CASE WHEN SUM(insured_count)=0 THEN 0 ELSE SUM(no_loaner_count)*1.0/SUM(insured_count) END AS safesNoLoanRatio,


            CASE WHEN SUM(loaner_count)=0 THEN 0 ELSE SUM(offline_loaner_count)*1.0/SUM(loaner_count) END AS safesBorrowerTransferRatio,
            CASE WHEN SUM(insured_count)=0 THEN 0 ELSE SUM(offline_loaner_count)*1.0/SUM(insured_count) END AS safesBorrowerRatio,
            CASE WHEN SUM(insured_count)=0 THEN 0 ELSE SUM(offline_shareloan_count+offline_guarantor_count)*1.0/SUM(insured_count) END AS safesLoanRatio
            FROM rpt.insured_loan_trans_static_monthly
        WHERE area_cde != 'Total'
            AND district_cde = 'Total'

        <if test="endDate!=null">
            <![CDATA[ AND rpt_date = to_date(#{endDate,jdbcType=VARCHAR},'yyyy-MM-dd')    ]]>
        </if>
        <if test="regionName!=null">
            AND area_name = #{regionName}
        </if>
        <if test='branchBuName != null'>
            AND division_name = #{branchBuName}
        </if>
        GROUP BY area_name
        ORDER BY area_name ASC
    </select>
    <select id="sumSmCustTransferRegion" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmCustTransferRegionVO">
        SELECT
        SUM(insured_count) AS safesPerQty,
        SUM(loaner_count) AS loanPerQty,
        SUM( offline_loaner_count) AS safesBorrowerPerQty,
        SUM( offline_shareloan_count) AS safesCoBorrowerPerQty,
        SUM( offline_guarantor_count) AS safesGuarantorPerQty,
        SUM(loan_rel_count) AS safesRelevanterPerQty,

        SUM(no_loaner_count) AS safesNoLoanPerQty,
        CASE WHEN SUM(loan_rel_count)=0 THEN 0 ELSE SUM(offline_shareloan_count+offline_guarantor_count)*1.0/SUM(loan_rel_count) END AS safesOtherTransferRatio,
        CASE WHEN SUM(insured_count)=0 THEN 0 ELSE SUM(no_loaner_count)*1.0/SUM(insured_count) END AS safesNoLoanRatio,


        CASE WHEN SUM(loaner_count)=0 THEN 0 ELSE SUM(offline_loaner_count)*1.0/SUM(loaner_count) END AS safesBorrowerTransferRatio,
        CASE WHEN SUM(insured_count)=0 THEN 0 ELSE SUM(offline_loaner_count)*1.0/SUM(insured_count) END AS safesBorrowerRatio,
        CASE WHEN SUM(insured_count)=0 THEN 0 ELSE SUM(offline_shareloan_count+offline_guarantor_count)*1.0/SUM(insured_count) END AS safesLoanRatio
        FROM rpt.insured_loan_trans_static_monthly
        WHERE area_cde != 'Total'
        AND district_cde = 'Total'

        <if test="endDate!=null">
            <![CDATA[ AND rpt_date = to_date(#{endDate,jdbcType=VARCHAR},'yyyy-MM-dd')     ]]>
        </if>
        <if test="regionName!=null">
            AND area_name = #{regionName}
        </if>
        <if test='branchBuName != null'>
            AND division_name = #{branchBuName}
        </if>
    </select>
    <select id="listSmCustTransferOrg" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmCustTransferOrgVO">
        SELECT
            area_name AS regionName,
            bch_name AS orgName,
            SUM(insured_count) AS safesPerQty,
            SUM(loaner_count) AS loanPerQty,
            SUM( offline_loaner_count) AS safesBorrowerPerQty,
            SUM( offline_shareloan_count) AS safesCoBorrowerPerQty,
            SUM( offline_guarantor_count) AS safesGuarantorPerQty,
            SUM(loan_rel_count) AS safesRelevanterPerQty,

            SUM(no_loaner_count) AS safesNoLoanPerQty,
            CASE WHEN SUM(loan_rel_count)=0 THEN 0 ELSE SUM(offline_shareloan_count+offline_guarantor_count)*1.0/SUM(loan_rel_count) END AS safesOtherTransferRatio,
            CASE WHEN SUM(insured_count)=0 THEN 0 ELSE SUM(no_loaner_count)*1.0/SUM(insured_count) END AS safesNoLoanRatio,


            CASE WHEN SUM(loaner_count)=0 THEN 0 ELSE SUM(offline_loaner_count)*1.0/SUM(loaner_count) END AS safesBorrowerTransferRatio,
            CASE WHEN SUM(insured_count)=0 THEN 0 ELSE SUM(offline_loaner_count)*1.0/SUM(insured_count) END AS safesBorrowerRatio,
            CASE WHEN SUM(insured_count)=0 THEN 0 ELSE SUM(offline_shareloan_count+offline_guarantor_count)*1.0/SUM(insured_count) END AS safesLoanRatio
        FROM rpt.insured_loan_trans_static_monthly
        WHERE area_cde != 'Total'
            AND bch_cde != 'Total'
            AND (district_cde != 'Total' or district_cde is null)
        AND usr_cde = 'Total'

            <if test="endDate!=null">
                <![CDATA[ AND rpt_date = to_date(#{endDate,jdbcType=VARCHAR},'yyyy-MM-dd')     ]]>
            </if>
            <if test="regionName!=null">
                AND area_name = #{regionName}
            </if>
            <if test="orgName!=null">
                AND bch_name = #{orgName}
            </if>
            <if test='branchBuName != null'>
                AND division_name = #{branchBuName}
            </if>
            <if test='zoneName != null'>
                AND district_name = #{zoneName,jdbcType=VARCHAR}
            </if>

        GROUP BY area_name, bch_name
        ORDER BY area_name ASC, bch_name ASC
    </select>
    <select id="sumSmCustTransferOrg" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmCustTransferOrgVO">
        SELECT

        SUM(insured_count) AS safesPerQty,
        SUM(loaner_count) AS loanPerQty,
        SUM( offline_loaner_count) AS safesBorrowerPerQty,
        SUM( offline_shareloan_count) AS safesCoBorrowerPerQty,
        SUM( offline_guarantor_count) AS safesGuarantorPerQty,
        SUM(loan_rel_count) AS safesRelevanterPerQty,

        SUM(no_loaner_count) AS safesNoLoanPerQty,
        CASE WHEN SUM(loan_rel_count)=0 THEN 0 ELSE SUM(offline_shareloan_count+offline_guarantor_count)*1.0/SUM(loan_rel_count) END AS safesOtherTransferRatio,
        CASE WHEN SUM(insured_count)=0 THEN 0 ELSE SUM(no_loaner_count)*1.0/SUM(insured_count) END AS safesNoLoanRatio,


        CASE WHEN SUM(loaner_count)=0 THEN 0 ELSE SUM(offline_loaner_count)*1.0/SUM(loaner_count) END AS safesBorrowerTransferRatio,
        CASE WHEN SUM(insured_count)=0 THEN 0 ELSE SUM(offline_loaner_count)*1.0/SUM(insured_count) END AS safesBorrowerRatio,
        CASE WHEN SUM(insured_count)=0 THEN 0 ELSE SUM(offline_shareloan_count+offline_guarantor_count)*1.0/SUM(insured_count) END AS safesLoanRatio
        FROM rpt.insured_loan_trans_static_monthly
        WHERE area_cde != 'Total'
        AND bch_cde != 'Total'
        AND (district_cde != 'Total' or district_cde is null)
        AND usr_cde = 'Total'

        <if test="endDate!=null">
            <![CDATA[ AND rpt_date = to_date(#{endDate,jdbcType=VARCHAR},'yyyy-MM-dd')     ]]>
        </if>
        <if test="regionName!=null">
            AND area_name = #{regionName}
        </if>
        <if test="orgName!=null">
            AND bch_name = #{orgName}
        </if>
        <if test='branchBuName != null'>
            AND division_name = #{branchBuName}
        </if>
        <if test='zoneName != null'>
            AND district_name = #{zoneName,jdbcType=VARCHAR}
        </if>
    </select>
    <select id="listSmCustTransferList" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmCustTransferListVO">
        SELECT
            t1.fhorderid AS fhOrderId,
            t1.area_name AS regionName,
            t1.bch_name AS organizationName,
            t1.recommendid AS recommendId,
            t1.recommendna AS recommendName,
            t1.policyholde AS appName,
            t1.policyholde1 AS appMobile,
            t1.insured_nam AS insName,
            t1.insured_cel AS insMobile,
            t1.productname AS productName,
            CASE WHEN t1.is_offline_ = 0 THEN '否' ELSE '是' END  AS isBorrower,
            CASE WHEN t1.is_offline_1 = 0 THEN '否' ELSE '是' END AS isCoBorrower,
            CASE WHEN t1.is_offline_2 = 0 THEN '否' ELSE '是' END AS isGuarantor,
            t1.order_amt AS orderAmount
        FROM rpt.insured_cust_type_summary_monthly t1
        WHERE 1=1

        <if test="endDate!=null">
            <![CDATA[ AND t1.rpt_date = #{endDate}    ]]>
        </if>
        <if test="regionName!=null">
            AND t1.area_name = #{regionName}
        </if>
        <if test="orgName!=null">
            AND t1.bch_name = #{orgName}
        </if>
        <if test='branchBuName != null'>
            AND division_name = #{branchBuName}
        </if>
        <if test='zoneName != null'>
            AND district_name = #{zoneName,jdbcType=VARCHAR}
        </if>
    </select>

    <select id="listSmPersonalBusinessDetails" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmBusinessDetailsPersonalVO">
        SELECT to_char(to_date(#{startDate},'yyyy-mm-dd'), 'yyyy-mm-dd') as startDate
        ,to_char(to_date(#{endDate},'yyyy-mm-dd'), 'yyyy-mm-dd') as endDate
        ,area_name as regionName
        ,bch_name as orgName
        ,usr_name as userName
        ,main_usr_cde as userId
        ,job_name as jobName
        ,join_date as joinDate
        ,cast(sum(COALESCE(company_age_coefficient,0))/${monthEndDayList.size}  AS decimal(10,4)) as companyAgeCoefficient
        ,sum(COALESCE(insured_cnt,0)-COALESCE(surrender_cnt,0)) as insuredCnt
        ,cast(sum(COALESCE(insured_amt,0.0000)-COALESCE(surrender_amt,0.0000)) AS decimal(10,2)) as insuredAmt
        ,sum(COALESCE(insured_noloan_cust,0)-COALESCE(surrender_noloan_cust,0)) as insuredNoloanCust
        ,cast((case when sum(COALESCE(insured_cust,0)-COALESCE(surrender_cust,0))=0 then 0 else sum(COALESCE(insured_noloan_cust,0.0000)-COALESCE(surrender_noloan_cust,0))/sum(COALESCE(insured_cust,0)-COALESCE(surrender_cust,0)) end)*100 AS decimal(10,2)) as insuredNoloanCustRatio
        ,sum(COALESCE(insured_noloan_cnt,0)-COALESCE(surrender_noloan_cnt,0)) as insuredNoloanCnt
        ,cast(sum(COALESCE(insured_noloan_amt,0.0000)-COALESCE(surrender_noloan_amt,0.0000)) AS decimal(10,2)) as insuredNoloanAmt
        ,cast((case when sum(COALESCE(insured_amt,0.0000)-COALESCE(surrender_amt,0.0000))=0 then 0 else sum(COALESCE(insured_noloan_amt,0.0000)-COALESCE(surrender_noloan_amt,0.0000))/sum(COALESCE(insured_amt,0.0000)-COALESCE(surrender_amt,0.0000)) end)*100 AS decimal(10,2)) as insuredNoloanAmtRatio
        ,sum(COALESCE(insured_stay_cust,0)) as insuredStayCust
        ,cast(sum(COALESCE(insured_stay_amt,0)) AS decimal(10,2)) as insuredStayAmt
        ,cast((case when sum(COALESCE(insured_amt,0.0000)-COALESCE(surrender_amt,0.0000))=0 then 0 else sum(COALESCE(insured_stay_amt,0.0000))/sum(COALESCE(insured_amt,0.0000)-COALESCE(surrender_amt,0.0000)) end)*100 AS decimal(10,2)) as insuredStayAmtRatio
        ,cast(sum(case when COALESCE(insured_cust_2019,0)=0 then 0 else COALESCE(insured_stay_cust,0.0000)/insured_cust_2019 end)*100 AS decimal(10,2)) as insuredStayCustRatio
        ,sum(COALESCE(insured_stay_loan_cust,0)) as insuredStayLoanCust
        ,cast(sum(COALESCE(insured_stay_loan_amt,0)) AS decimal(10,2)) as insuredStayLoanAmt
        ,cast(sum(case when COALESCE(insured_loan_cust_2019,0)=0 then 0 else COALESCE(insured_stay_loan_cust,0.0000)/insured_loan_cust_2019 end)*100 AS decimal(10,2)) as insuredStayLoanCustRatio
        ,sum(COALESCE(insured_stay_noloan_cust,0)) as insuredStayNoloanCust
        ,cast(sum(COALESCE(insured_stay_noloan_amt,0)) AS decimal(10,2)) as insuredStayNoloanAmt
        ,cast(sum(case when COALESCE(insured_noloan_cust_2019,0)=0 then 0 else COALESCE(insured_stay_noloan_cust,0.0000)/insured_noloan_cust_2019 end)*100 AS decimal(10,2)) as insuredStayNoloanCustRatio
        FROM
        rpt.insurance_employee_order_m_dip
        WHERE 1=1
        <choose>
            <when test="monthEndDayList!=null and monthEndDayList.size>0">
                AND rpt_date IN
                <foreach collection="monthEndDayList" item="monthEndDay" open="(" close=")" index="index" separator=",">
                    to_date(#{monthEndDay,jdbcType=VARCHAR},'yyyy-MM-dd')
                </foreach>
            </when>
            <otherwise>
                and 1=0
            </otherwise>
        </choose>
        <if test='regionName != null'>
            AND area_name = #{regionName,jdbcType=VARCHAR}
        </if>
        <if test='orgName != null'>
            AND bch_name = #{orgName,jdbcType=VARCHAR}
        </if>
        <if test='userName != null'>
            AND (main_usr_cde LIKE CONCAT(#{userName,jdbcType=VARCHAR},'%') OR usr_name LIKE CONCAT(#{userName,jdbcType=VARCHAR},'%'))
        </if>
        <if test='branchBuName != null'>
            AND division_name = #{branchBuName}
        </if>
        <if test='zoneName != null'>
            AND district_name = #{zoneName,jdbcType=VARCHAR}
        </if>
        <if test='userId != null'>
            AND main_usr_cde = #{userId,jdbcType=VARCHAR}
        </if>

        GROUP BY main_usr_cde,usr_name,area_cde,area_name,bch_cde,bch_name,job_name,join_date


        <choose>
            <when test="sort!=null and sort==''">
                ORDER BY area_cde ASC,bch_cde ASC,main_usr_cde ASC
            </when>
            <!-- 按保费从高到低排序 -->
            <when test="sort!=null and sort=='insuredAmt'">
                ORDER BY insuredAmt DESC
            </when>
            <!-- 按单量从高到低排序 -->
            <when test="sort!=null and sort=='insuredCnt'">
                ORDER BY insuredCnt DESC
            </when>

            <!-- 按人均非信贷客户保费从高到低排序 -->
            <when test="sort!=null and sort=='insuredNoloanAmt'">
                ORDER BY insuredNoloanAmt DESC
            </when>
            <!-- 按人均非信贷客户保费从高到低排序 -->
            <when test="sort!=null and sort=='insuredNoloanCnt'">
                ORDER BY insuredNoloanCnt DESC
            </when>

        </choose>
    </select>

    <select id="listSmOrgBusinessDetails" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmBusinessDetailsOrgVO">
        SELECT to_char(to_date(#{startDate},'yyyy-mm-dd'), 'yyyy-mm-dd') as startDate
        ,to_char(to_date(#{endDate},'yyyy-mm-dd'), 'yyyy-mm-dd') as endDate
        ,area_name as regionName
        ,bch_name as orgName
        ,cast(sum(sumCnt)/${monthEndDayList.size}  AS decimal(10,4)) as convertedCount
        ,sum(insured_cnt) as insuredCnt
        ,cast(sum(insured_amt) AS decimal(10,2)) as insuredAmt
        ,cast((case when (sum(sumCnt)/${monthEndDayList.size})=0 then 0.0000 else sum(insured_cnt)/(sum(sumCnt)/${monthEndDayList.size}) end) AS decimal(10,2)) as insuredCntAvg
        ,cast((case when (sum(sumCnt)/${monthEndDayList.size})=0 then 0.0000 else sum(insured_amt)/(sum(sumCnt)/${monthEndDayList.size}) end) AS decimal(10,2)) as insuredAmtAvg
        ,sum(insured_noloan_cust) as insuredNoloanCust
        ,cast((case when sum(insured_cust)=0 then 0 else sum(insured_noloan_cust)/sum(insured_cust) end)*100 AS decimal(10,2)) as insuredNoloanCustRatio
        ,sum(insured_noloan_cnt) as insuredNoloanCnt
        ,cast(sum(insured_noloan_amt) AS decimal(10,2)) as insuredNoloanAmt
        ,cast((case when (sum(sumCnt)/${monthEndDayList.size})=0 then 0.0000 else sum(insured_noloan_cnt)/(sum(sumCnt)/${monthEndDayList.size}) end) AS decimal(10,2)) as insuredNoloanCntAvg
        ,cast((case when (sum(sumCnt)/${monthEndDayList.size})=0 then 0.0000 else sum(insured_noloan_amt)/(sum(sumCnt)/${monthEndDayList.size}) end) AS decimal(10,2)) as insuredNoloanAmtAvg
        ,cast((case when sum(insured_amt)=0 then 0 else sum(insured_noloan_amt)/sum(insured_amt) end)*100 AS decimal(10,2)) as insuredNoloanAmtRatio
        ,sum(insured_stay_cust) as insuredStayCust
        ,cast(sum(insured_stay_amt) AS decimal(10,2)) as insuredStayAmt
        ,cast((case when sum(insured_amt)=0 then 0 else sum(insured_stay_amt)/sum(insured_amt) end)*100 AS decimal(10,2)) as insuredStayAmtRatio
        ,cast(sum(insuredStayCustRatio) AS decimal(10,2)) as insuredStayCustRatio
        ,sum(insured_stay_loan_cust) as insuredStayLoanCust
        ,cast(sum(insured_stay_loan_amt) AS decimal(10,2)) as insuredStayLoanAmt
        ,cast(sum(insuredStayLoanCustRatio) AS decimal(10,2)) as insuredStayLoanCustRatio
        ,sum(insured_stay_noloan_cust) as insuredStayNoloanCust
        ,cast(sum(insured_stay_noloan_amt) AS decimal(10,2)) as insuredStayNoloanAmt
        ,cast(sum(insuredStayNoloanCustRatio) AS decimal(10,2)) as insuredStayNoloanCustRatio
        from (
            SELECT count(1),rpt_date,area_cde,area_name,bch_cde,bch_name
            ,sum(COALESCE(insured_cust,0)-COALESCE(surrender_cust,0)) as insured_cust
            ,sum(COALESCE(insured_cnt,0)-COALESCE(surrender_cnt,0)) as insured_cnt
            ,sum(COALESCE(insured_amt,0.0000)-COALESCE(surrender_amt,0.0000)) as insured_amt
            ,sum(COALESCE(insured_noloan_cust,0)-COALESCE(surrender_noloan_cust,0)) as insured_noloan_cust
            ,sum(COALESCE(insured_noloan_cnt,0)-COALESCE(surrender_noloan_cnt,0)) as insured_noloan_cnt
            ,sum(COALESCE(insured_noloan_amt,0.0000)-COALESCE(surrender_noloan_amt,0.0000)) as insured_noloan_amt
            ,sum(COALESCE(insured_stay_cust,0)) as insured_stay_cust
            ,sum(COALESCE(insured_stay_amt,0.0000)) as insured_stay_amt
            ,sum(COALESCE(insured_stay_loan_cust,0)) as insured_stay_loan_cust
            ,sum(COALESCE(insured_stay_loan_amt, 0.0000)) as insured_stay_loan_amt
            ,sum(COALESCE(insured_stay_noloan_cust,0)) as insured_stay_noloan_cust
            ,sum(COALESCE(insured_stay_noloan_amt,0.0000)) as insured_stay_noloan_amt
            ,cast((case when sum(COALESCE(insured_cust_2019,0))=0 then 0 else sum(COALESCE(insured_stay_cust,0.0000))/sum(insured_cust_2019) end)*100 AS decimal(10,4)) as insuredStayCustRatio
            ,cast((case when sum(COALESCE(insured_loan_cust_2019,0))=0 then 0 else sum(COALESCE(insured_stay_loan_cust,0.0000))/sum(insured_loan_cust_2019) end)*100 AS decimal(10,4)) as insuredStayLoanCustRatio
            ,cast((case when sum(COALESCE(insured_noloan_cust_2019,0))=0 then 0 else sum(COALESCE(insured_stay_noloan_cust,0.0000))/sum(insured_noloan_cust_2019) end)*100 AS decimal(10,4)) as insuredStayNoloanCustRatio
            ,sum(COALESCE(company_age_coefficient,0.0000)) as sumCnt
            FROM
            rpt.insurance_employee_order_m_dip
            WHERE 1=1
            <choose>
                <when test="monthEndDayList!=null and monthEndDayList.size>0">
                    AND rpt_date IN
                    <foreach collection="monthEndDayList" item="monthEndDay" open="(" close=")" index="index" separator=",">
                        to_date(#{monthEndDay,jdbcType=VARCHAR},'yyyy-MM-dd')
                    </foreach>
                </when>
                <otherwise>
                    and 1=0
                </otherwise>
            </choose>
            <if test='regionName != null'>
                AND area_name = #{regionName}
            </if>
            <if test='orgName != null'>
                AND bch_name = #{orgName}
            </if>
            <if test='branchBuName != null'>
                AND division_name = #{branchBuName}
            </if>
            <if test='zoneName != null'>
                AND district_name = #{zoneName,jdbcType=VARCHAR}
            </if>
            GROUP BY area_cde,area_name,bch_cde,bch_name,rpt_date
        ) a
        GROUP BY area_cde,area_name,bch_cde,bch_name
        <choose>
            <when test="sort!=null and sort==''">
                ORDER BY area_cde ASC,bch_cde ASC
            </when>
            <!-- 按保费从高到低排序 -->
            <when test="sort!=null and sort=='insuredAmt'">
                ORDER BY insuredAmt DESC
            </when>
            <!-- 按单量从高到低排序 -->
            <when test="sort!=null and sort=='insuredCnt'">
                ORDER BY insuredCnt DESC
            </when>
            <!-- 按人均保费从高到低排序 -->
            <when test="sort!=null and sort=='insuredAmtAvg'">
                ORDER BY insuredAmtAvg DESC
            </when>

            <!-- 按人均单量从高到低排序 -->
            <when test="sort!=null and sort=='insuredCntAvg'">
                ORDER BY insuredCntAvg DESC
            </when>

            <!-- 按人均非信贷客户保费从高到低排序 -->
            <when test="sort!=null and sort=='insuredNoloanAmtAvg'">
                ORDER BY insuredNoloanAmtAvg DESC
            </when>

            <!-- 按人均非信贷客户单量从高到低排序 -->
            <when test="sort!=null and sort=='insuredNoloanCntAvg'">
                ORDER BY insuredNoloanCntAvg DESC
            </when>

        </choose>
    </select>

    <select id="listSmRegionBusinessDetails" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmBusinessDetailsRegionVO">
        SELECT to_char(to_date(#{startDate},'yyyy-mm-dd'), 'yyyy-mm-dd') as startDate
        ,to_char(to_date(#{endDate},'yyyy-mm-dd'), 'yyyy-mm-dd') as endDate
        ,area_name as regionName
        ,cast(sum(sumCnt)/${monthEndDayList.size}  AS decimal(10,4)) as convertedCount
        ,sum(insured_cnt) as insuredCnt
        ,cast(sum(insured_amt) AS decimal(10,2)) as insuredAmt
        ,cast((case when (sum(sumCnt)/${monthEndDayList.size})=0 then 0.0000 else sum(insured_cnt)/(sum(sumCnt)/${monthEndDayList.size}) end) AS decimal(10,2)) as insuredCntAvg
        ,cast((case when (sum(sumCnt)/${monthEndDayList.size})=0 then 0.0000 else sum(insured_amt)/(sum(sumCnt)/${monthEndDayList.size}) end) AS decimal(10,2)) as insuredAmtAvg
        ,sum(insured_noloan_cust) as insuredNoloanCust
        ,cast((case when sum(insured_cust)=0 then 0 else sum(insured_noloan_cust)/sum(insured_cust) end)*100 AS decimal(10,2)) as insuredNoloanCustRatio
        ,sum(insured_noloan_cnt) as insuredNoloanCnt
        ,cast(sum(insured_noloan_amt) AS decimal(10,2)) as insuredNoloanAmt
        ,cast((case when (sum(sumCnt)/${monthEndDayList.size})=0 then 0.0000 else sum(insured_noloan_cnt)/(sum(sumCnt)/${monthEndDayList.size}) end) AS decimal(10,2)) as insuredNoloanCntAvg
        ,cast((case when (sum(sumCnt)/${monthEndDayList.size})=0 then 0.0000 else sum(insured_noloan_amt)/(sum(sumCnt)/${monthEndDayList.size}) end) AS decimal(10,2)) as insuredNoloanAmtAvg
        ,cast((case when sum(insured_amt)=0 then 0 else sum(insured_noloan_amt)/sum(insured_amt) end)*100 AS decimal(10,2)) as insuredNoloanAmtRatio
        ,sum(insured_stay_cust) as insuredStayCust
        ,cast(sum(insured_stay_amt) AS decimal(10,2)) as insuredStayAmt
        ,cast((case when sum(insured_amt)=0 then 0 else sum(insured_stay_amt)/sum(insured_amt) end)*100 AS decimal(10,2)) as insuredStayAmtRatio
        ,cast(sum(insuredStayCustRatio) AS decimal(10,2)) as insuredStayCustRatio
        ,sum(insured_stay_loan_cust) as insuredStayLoanCust
        ,cast(sum(insured_stay_loan_amt) AS decimal(10,2)) as insuredStayLoanAmt
        ,cast(sum(insuredStayLoanCustRatio) AS decimal(10,2)) as insuredStayLoanCustRatio
        ,sum(insured_stay_noloan_cust) as insuredStayNoloanCust
        ,cast(sum(insured_stay_noloan_amt) AS decimal(10,2)) as insuredStayNoloanAmt
        ,cast(sum(insuredStayNoloanCustRatio) AS decimal(10,2)) as insuredStayNoloanCustRatio
        from (
        SELECT count(1),rpt_date,area_cde,area_name
        ,sum(COALESCE(insured_cust,0)-COALESCE(surrender_cust,0)) as insured_cust
        ,sum(COALESCE(insured_cnt,0)-COALESCE(surrender_cnt,0)) as insured_cnt
        ,sum(COALESCE(insured_amt,0.0000)-COALESCE(surrender_amt,0.0000)) as insured_amt
        ,sum(COALESCE(insured_noloan_cust,0)-COALESCE(surrender_noloan_cust,0)) as insured_noloan_cust
        ,sum(COALESCE(insured_noloan_cnt,0)-COALESCE(surrender_noloan_cnt,0)) as insured_noloan_cnt
        ,sum(COALESCE(insured_noloan_amt,0.0000)-COALESCE(surrender_noloan_amt,0.0000)) as insured_noloan_amt
        ,sum(COALESCE(insured_stay_cust,0)) as insured_stay_cust
        ,sum(COALESCE(insured_stay_amt,0.0000)) as insured_stay_amt
        ,sum(COALESCE(insured_stay_loan_cust,0)) as insured_stay_loan_cust
        ,sum(COALESCE(insured_stay_loan_amt, 0.0000)) as insured_stay_loan_amt
        ,sum(COALESCE(insured_stay_noloan_cust,0)) as insured_stay_noloan_cust
        ,sum(COALESCE(insured_stay_noloan_amt,0.0000)) as insured_stay_noloan_amt
        ,cast((case when sum(COALESCE(insured_cust_2019,0))=0 then 0 else sum(COALESCE(insured_stay_cust,0.0000))/sum(insured_cust_2019) end)*100 AS decimal(10,4)) as insuredStayCustRatio
        ,cast((case when sum(COALESCE(insured_loan_cust_2019,0))=0 then 0 else sum(COALESCE(insured_stay_loan_cust,0.0000))/sum(insured_loan_cust_2019) end)*100 AS decimal(10,4)) as insuredStayLoanCustRatio
        ,cast((case when sum(COALESCE(insured_noloan_cust_2019,0))=0 then 0 else sum(COALESCE(insured_stay_noloan_cust,0.0000))/sum(insured_noloan_cust_2019) end)*100 AS decimal(10,4)) as insuredStayNoloanCustRatio
        ,sum(COALESCE(company_age_coefficient,0.0000)) as sumCnt
        FROM
        rpt.insurance_employee_order_m_dip
        WHERE 1=1
        <choose>
            <when test="monthEndDayList!=null and monthEndDayList.size>0">
                AND rpt_date IN
                <foreach collection="monthEndDayList" item="monthEndDay" open="(" close=")" index="index" separator=",">
                    to_date(#{monthEndDay,jdbcType=VARCHAR},'yyyy-MM-dd')
                </foreach>
            </when>
            <otherwise>
                and 1=0
            </otherwise>
        </choose>
        <if test='regionName != null'>
            AND area_name = #{regionName}
        </if>
        <if test='branchBuName != null'>
            AND division_name = #{branchBuName}
        </if>

        GROUP BY area_cde,area_name,rpt_date
        ) a
        GROUP BY area_cde,area_name


        <choose>
            <when test="sort!=null and sort==''">
                ORDER BY area_cde ASC
            </when>
            <!-- 按保费从高到低排序 -->
            <when test="sort!=null and sort=='insuredAmt'">
                ORDER BY insuredAmt DESC
            </when>
            <!-- 按单量从高到低排序 -->
            <when test="sort!=null and sort=='insuredCnt'">
                ORDER BY insuredCnt DESC
            </when>
            <!-- 按人均保费从高到低排序 -->
            <when test="sort!=null and sort=='insuredAmtAvg'">
                ORDER BY insuredAmtAvg DESC
            </when>

            <!-- 按人均单量从高到低排序 -->
            <when test="sort!=null and sort=='insuredCntAvg'">
                ORDER BY insuredCntAvg DESC
            </when>

            <!-- 按人均非信贷客户保费从高到低排序 -->
            <when test="sort!=null and sort=='insuredNoloanAmtAvg'">
                ORDER BY insuredNoloanAmtAvg DESC
            </when>

            <!-- 按人均非信贷客户单量从高到低排序 -->
            <when test="sort!=null and sort=='insuredNoloanCntAvg'">
                ORDER BY insuredNoloanCntAvg DESC
            </when>

        </choose>
    </select>

    <!--add by zhangjian 2020-05-18-->
    <select id="listSmBranchBuBusinessDetails" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmBusinessDetailsBranchBuVO">
        SELECT to_char(to_date(#{startDate},'yyyy-mm-dd'), 'yyyy-mm-dd') as startDate
        ,to_char(to_date(#{endDate},'yyyy-mm-dd'), 'yyyy-mm-dd') as endDate
        ,division_name as branchBuName
        ,cast(sum(sumCnt)/${monthEndDayList.size}  AS decimal(10,4)) as convertedCount
        ,sum(insured_cnt) as insuredCnt
        ,cast(sum(insured_amt) AS decimal(10,2)) as insuredAmt
        ,cast((case when (sum(sumCnt)/${monthEndDayList.size})=0 then 0.0000 else sum(insured_cnt)/(sum(sumCnt)/${monthEndDayList.size}) end) AS decimal(10,2)) as insuredCntAvg
        ,cast((case when (sum(sumCnt)/${monthEndDayList.size})=0 then 0.0000 else sum(insured_amt)/(sum(sumCnt)/${monthEndDayList.size}) end) AS decimal(10,2)) as insuredAmtAvg
        ,sum(insured_noloan_cust) as insuredNoloanCust
        ,cast((case when sum(insured_cust)=0 then 0 else sum(insured_noloan_cust)/sum(insured_cust) end)*100 AS decimal(10,2)) as insuredNoloanCustRatio
        ,sum(insured_noloan_cnt) as insuredNoloanCnt
        ,cast(sum(insured_noloan_amt) AS decimal(10,2)) as insuredNoloanAmt
        ,cast((case when (sum(sumCnt)/${monthEndDayList.size})=0 then 0.0000 else sum(insured_noloan_cnt)/(sum(sumCnt)/${monthEndDayList.size}) end) AS decimal(10,2)) as insuredNoloanCntAvg
        ,cast((case when (sum(sumCnt)/${monthEndDayList.size})=0 then 0.0000 else sum(insured_noloan_amt)/(sum(sumCnt)/${monthEndDayList.size}) end) AS decimal(10,2)) as insuredNoloanAmtAvg
        ,cast((case when sum(insured_amt)=0 then 0 else sum(insured_noloan_amt)/sum(insured_amt) end)*100 AS decimal(10,2)) as insuredNoloanAmtRatio
        ,sum(insured_stay_cust) as insuredStayCust
        ,cast(sum(insured_stay_amt) AS decimal(10,2)) as insuredStayAmt
        ,cast((case when sum(insured_amt)=0 then 0 else sum(insured_stay_amt)/sum(insured_amt) end)*100 AS decimal(10,2)) as insuredStayAmtRatio
        ,cast(sum(insuredStayCustRatio) AS decimal(10,2)) as insuredStayCustRatio
        ,sum(insured_stay_loan_cust) as insuredStayLoanCust
        ,cast(sum(insured_stay_loan_amt) AS decimal(10,2)) as insuredStayLoanAmt
        ,cast(sum(insuredStayLoanCustRatio) AS decimal(10,2)) as insuredStayLoanCustRatio
        ,sum(insured_stay_noloan_cust) as insuredStayNoloanCust
        ,cast(sum(insured_stay_noloan_amt) AS decimal(10,2)) as insuredStayNoloanAmt
        ,cast(sum(insuredStayNoloanCustRatio) AS decimal(10,2)) as insuredStayNoloanCustRatio
        from (
        SELECT count(1),rpt_date,division_cde,division_name
        ,sum(COALESCE(insured_cust,0)-COALESCE(surrender_cust,0)) as insured_cust
        ,sum(COALESCE(insured_cnt,0)-COALESCE(surrender_cnt,0)) as insured_cnt
        ,sum(COALESCE(insured_amt,0.0000)-COALESCE(surrender_amt,0.0000)) as insured_amt
        ,sum(COALESCE(insured_noloan_cust,0)-COALESCE(surrender_noloan_cust,0)) as insured_noloan_cust
        ,sum(COALESCE(insured_noloan_cnt,0)-COALESCE(surrender_noloan_cnt,0)) as insured_noloan_cnt
        ,sum(COALESCE(insured_noloan_amt,0.0000)-COALESCE(surrender_noloan_amt,0.0000)) as insured_noloan_amt
        ,sum(COALESCE(insured_stay_cust,0)) as insured_stay_cust
        ,sum(COALESCE(insured_stay_amt,0.0000)) as insured_stay_amt
        ,sum(COALESCE(insured_stay_loan_cust,0)) as insured_stay_loan_cust
        ,sum(COALESCE(insured_stay_loan_amt, 0.0000)) as insured_stay_loan_amt
        ,sum(COALESCE(insured_stay_noloan_cust,0)) as insured_stay_noloan_cust
        ,sum(COALESCE(insured_stay_noloan_amt,0.0000)) as insured_stay_noloan_amt
        ,cast((case when sum(COALESCE(insured_cust_2019,0))=0 then 0 else sum(COALESCE(insured_stay_cust,0.0000))/sum(insured_cust_2019) end)*100 AS decimal(10,4)) as insuredStayCustRatio
        ,cast((case when sum(COALESCE(insured_loan_cust_2019,0))=0 then 0 else sum(COALESCE(insured_stay_loan_cust,0.0000))/sum(insured_loan_cust_2019) end)*100 AS decimal(10,4)) as insuredStayLoanCustRatio
        ,cast((case when sum(COALESCE(insured_noloan_cust_2019,0))=0 then 0 else sum(COALESCE(insured_stay_noloan_cust,0.0000))/sum(insured_noloan_cust_2019) end)*100 AS decimal(10,4)) as insuredStayNoloanCustRatio
        ,sum(COALESCE(company_age_coefficient,0.0000)) as sumCnt
        FROM
        rpt.insurance_employee_order_m_dip
        WHERE 1=1
        <choose>
            <when test="monthEndDayList!=null and monthEndDayList.size>0">
                AND rpt_date IN
                <foreach collection="monthEndDayList" item="monthEndDay" open="(" close=")" index="index" separator=",">
                    to_date(#{monthEndDay,jdbcType=VARCHAR},'yyyy-MM-dd')
                </foreach>
            </when>
            <otherwise>
                and 1=0
            </otherwise>
        </choose>
        <if test='branchBuName != null'>
            AND division_name = #{branchBuName}
        </if>
        <if test='branchBuCode != null'>
            AND division_cde = #{branchBuCode}
        </if>
        GROUP BY division_cde,division_name,rpt_date
        ) a
        GROUP BY division_cde,division_name


        <choose>
            <when test="sort!=null and sort==''">
                ORDER BY division_cde ASC
            </when>
            <!-- 按保费从高到低排序 -->
            <when test="sort!=null and sort=='insuredAmt'">
                ORDER BY insuredAmt DESC
            </when>
            <!-- 按单量从高到低排序 -->
            <when test="sort!=null and sort=='insuredCnt'">
                ORDER BY insuredCnt DESC
            </when>
            <!-- 按人均保费从高到低排序 -->
            <when test="sort!=null and sort=='insuredAmtAvg'">
                ORDER BY insuredAmtAvg DESC
            </when>

            <!-- 按人均单量从高到低排序 -->
            <when test="sort!=null and sort=='insuredCntAvg'">
                ORDER BY insuredCntAvg DESC
            </when>

            <!-- 按人均非信贷客户保费从高到低排序 -->
            <when test="sort!=null and sort=='insuredNoloanAmtAvg'">
                ORDER BY insuredNoloanAmtAvg DESC
            </when>

            <!-- 按人均非信贷客户单量从高到低排序 -->
            <when test="sort!=null and sort=='insuredNoloanCntAvg'">
                ORDER BY insuredNoloanCntAvg DESC
            </when>

        </choose>
    </select>


    <select id="listSmPersonalCommissionReport" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmPersonalCommissionReportVO">
        SELECT  to_char(to_date(#{startDate},'yyyy-mm-dd'), 'yyyy-mm') as monthStr
        ,area_name as regionName
        ,bch_name as orgName
        ,usr_name as userName
        ,main_usr_cde as userId
        ,finance_org_code as financeOrgCode
        ,finance_org_name as financeOrgName
        ,sum(COALESCE(insured_cnt,0)) as insuredCnt
        ,cast(sum(COALESCE(insured_amt,0.0000)) AS decimal(10,2)) as insuredAmt
        ,cast(sum(COALESCE(cmsn_amt_base,0.0000)) AS decimal(10,2)) as cmsnAmtBase
        ,cast(sum(COALESCE(cmsn_amt_plus,0.0000)) AS decimal(10,2)) as cmsnAmtPlus
        ,cast(sum(COALESCE(rspl_cmsn_amt,0.0000)) AS decimal(10,2)) as rsplCmsnAmt
        ,cast(sum(COALESCE(spvsr_cmsn_amt,0.0000)) AS decimal(10,2)) as spvsrCmsnAmt
        ,cast(sum(COALESCE(mang_cmsn_amt,0.0000)) AS decimal(10,2)) as mangCmsnAmt
        ,cast(sum(COALESCE(cmsn_total,0.0000)) AS decimal(10,2)) as cmsnTotal
        FROM
        rpt.insurance_cmsn_mip
        WHERE 1=1
        <choose>
            <when test="monthEndDayList!=null and monthEndDayList.size==1">
                AND rpt_date = to_date(#{monthEndDayList[0],jdbcType=VARCHAR},'yyyy-MM-dd')
            </when>
            <otherwise>
                and 1=0
            </otherwise>
        </choose>
        <if test='branchBuName != null'>
            AND division_name = #{branchBuName}
        </if>
        <if test='zoneName != null'>
            AND district_name = #{zoneName,jdbcType=VARCHAR}
        </if>
        <if test='regionName != null'>
            AND area_name = #{regionName,jdbcType=VARCHAR}
        </if>
        <if test='orgName != null'>
            AND bch_name = #{orgName,jdbcType=VARCHAR}
        </if>
        <if test='userName != null'>
            AND (main_usr_cde LIKE CONCAT(#{userName,jdbcType=VARCHAR},'%') OR usr_name LIKE CONCAT(#{userName,jdbcType=VARCHAR},'%'))
        </if>
        <if test='channel != null'>
            AND (channel = #{channel,jdbcType=VARCHAR} OR channel_name = #{channel,jdbcType=VARCHAR})
        </if>
        <if test='channels != null'>
            AND (channel in
            <foreach collection="channels" item="item" open="(" close=")" separator=",">
                 #{item,jdbcType=VARCHAR}</foreach>
            )
        </if>
        <if test='userId != null'>
            AND main_usr_cde = #{userId,jdbcType=VARCHAR}
        </if>
        GROUP BY main_usr_cde,usr_name,area_cde,area_name,bch_cde,bch_name,finance_org_code,finance_org_name
        ORDER BY area_cde ASC,bch_cde ASC,main_usr_cde ASC
    </select>

    <select id="listSmOrgCommissionReport" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmOrgCommissionReportVO">
        SELECT  to_char(to_date(#{startDate},'yyyy-mm-dd'), 'yyyy-mm-dd') as startDate
        ,to_char(to_date(#{endDate},'yyyy-mm-dd'), 'yyyy-mm-dd') as endDate
        ,area_name as regionName
        ,bch_name as orgName
        ,sum(COALESCE(insured_cnt,0)) as insuredCnt
        ,cast(sum(COALESCE(insured_amt,0.0000)) AS decimal(10,2)) as insuredAmt
        ,cast(sum(COALESCE(cmsn_amt_setl,0.0000)) AS decimal(10,2)) as cmsnIncome
        ,cast(sum(COALESCE(cmsn_total,0.0000)) AS decimal(10,2)) as cmsnExpe
        FROM
        rpt.insurance_cmsn_mip
        WHERE 1=1
        <choose>
            <when test="monthEndDayList!=null and monthEndDayList.size>0">
                AND rpt_date IN
                <foreach collection="monthEndDayList" item="monthEndDay" open="(" close=")" index="index" separator=",">
                    to_date(#{monthEndDay,jdbcType=VARCHAR},'yyyy-MM-dd')
                </foreach>
            </when>
            <otherwise>
                and 1=0
            </otherwise>
        </choose>
        <if test='branchBuName != null'>
            AND division_name = #{branchBuName}
        </if>
        <if test='zoneName != null'>
            AND district_name = #{zoneName,jdbcType=VARCHAR}
        </if>
        <if test='regionName != null'>
            AND area_name = #{regionName,jdbcType=VARCHAR}
        </if>
        <if test='orgName != null'>
            AND bch_name = #{orgName,jdbcType=VARCHAR}
        </if>
        <if test='channel != null'>
            AND (channel = #{channel,jdbcType=VARCHAR} OR channel_name = #{channel,jdbcType=VARCHAR})
        </if>
        GROUP BY area_cde,area_name,bch_cde,bch_name
        ORDER BY area_cde ASC,bch_cde ASC
    </select>

    <select id="listSmPersonalCustStayReport" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmReportCustStayPersonalVO">
        SELECT to_char(to_date(#{startDate},'yyyy-mm-dd'), 'yyyy-mm-dd') as startDate
        ,to_char(to_date(#{endDate},'yyyy-mm-dd'), 'yyyy-mm-dd') as endDate
        ,division_name as branchBuName
        ,division_code as branchBuCode
        ,area_name as regionName
        ,area_code as regionCode
        ,district_name as zoneName
        ,district_code as zoneCode
        ,bch_name as orgName
        ,bch_code as orgCode
        ,emp_name as userName
        ,main_emp_id as userId
        ,case when #{type}='1' then COALESCE(m_policyholder_stay,0) else COALESCE(y_policyholder_stay,0) end as policyholderStay
        ,case when #{type}='1' then COALESCE(m_stay_qty,0) else COALESCE(y_stay_qty,0) end as stayQty
        ,cast((case when #{type}='1' then COALESCE(m_stay_amount,0.0000) else COALESCE(y_stay_amount,0.0000) end) AS decimal(10,2)) as stayAmount
        ,cast((
        case when #{type}='1'
        then (case when COALESCE(m_qty,0)=0 then 0 else COALESCE(m_stay_qty,0.0000)/COALESCE(m_qty,0) end)
        else (case when COALESCE(y_qty,0)=0 then 0 else COALESCE(y_stay_qty,0.0000)/COALESCE(y_qty,0) end)
        end
        )*100 AS decimal(10,2)) as stayQtyRatio
        ,cast((
        case when #{type}='1'
        then (case when COALESCE(m_amount,0)=0 then 0 else COALESCE(m_stay_amount,0.0000)/COALESCE(m_amount,0) end)
        else (case when COALESCE(y_amount,0)=0 then 0 else COALESCE(y_stay_amount,0.0000)/COALESCE(y_amount,0) end)
        end
        )*100 AS decimal(10,2)) as stayAmountRatio
        ,cast((
        case when #{type}='1'
        then (case when COALESCE(m_policyholder,0)=0 then 0 else COALESCE(m_policyholder_stay,0.0000)/m_policyholder end)
        else (case when COALESCE(y_policyholder,0)=0 then 0 else COALESCE(y_policyholder_stay,0.0000)/y_policyholder end)
        end
        )*100 AS decimal(10,2)) as custStayRatio
        ,case when #{type}='1' then COALESCE(m_policyholder_loan_stay,0) else COALESCE(y_policyholder_loan_stay,0) end as policyholderLoanStay
        ,case when #{type}='1' then COALESCE(m_stay_qty_loan,0) else COALESCE(y_stay_qty_loan,0) end as stayQtyLoan
        ,cast((case when #{type}='1' then COALESCE(m_stay_amount_loan,0.0000) else COALESCE(y_stay_amount_loan,0.0000) end) AS decimal(10,2)) as stayAmountLoan
        ,cast((
        case when #{type}='1'
        then (case when COALESCE(m_policyholder_loan,0)=0 then 0 else COALESCE(m_policyholder_loan_stay,0.0000)/m_policyholder_loan end)
        else (case when COALESCE(y_policyholder_loan,0)=0 then 0 else COALESCE(y_policyholder_loan_stay,0.0000)/y_policyholder_loan end)
        end
        )*100 AS decimal(10,2)) as custStayLoanRatio
        ,case when #{type}='1' then COALESCE(m_policyholder_loan_rel_stay,0) else COALESCE(y_policyholder_loan_rel_stay,0) end as policyholderLoanRelStay
        ,case when #{type}='1' then COALESCE(m_stay_qty_loan_rel,0) else COALESCE(y_stay_qty_loan_rel,0) end as stayQtyLoanRel
        ,cast((case when #{type}='1' then COALESCE(m_stay_amount_loan_rel,0.0000) else COALESCE(y_stay_amount_loan_rel,0.0000) end) AS decimal(10,2)) as stayAmountLoanRel
        ,cast((
        case when #{type}='1'
        then (case when COALESCE(m_policyholder_loan_rel,0)=0 then 0 else COALESCE(m_policyholder_loan_rel_stay,0.0000)/m_policyholder_loan_rel end)
        else (case when COALESCE(y_policyholder_loan_rel,0)=0 then 0 else COALESCE(y_policyholder_loan_rel_stay,0.0000)/y_policyholder_loan_rel end)
        end
        )*100 AS decimal(10,2)) as custStayLoanRelRatio
        ,case when #{type}='1' then COALESCE(m_policyholder_noloan_stay,0) else COALESCE(y_policyholder_noloan_stay,0) end as policyholderNoloanStay
        ,case when #{type}='1' then COALESCE(m_stay_qty_noloan,0) else COALESCE(y_stay_qty_noloan,0) end as stayQtyNoloan
        ,cast((case when #{type}='1' then COALESCE(m_stay_amount_noloan,0.0000) else COALESCE(y_stay_amount_noloan,0.0000) end) AS decimal(10,2)) as stayAmountNoloan
        ,cast((
        case when #{type}='1'
        then (case when COALESCE(m_policyholder_noloan,0)=0 then 0 else COALESCE(m_policyholder_noloan_stay,0.0000)/m_policyholder_noloan end)
        else (case when COALESCE(y_policyholder_noloan,0)=0 then 0 else COALESCE(y_policyholder_noloan_stay,0.0000)/y_policyholder_noloan end)
        end
        )*100 AS decimal(10,2)) as custStayNoloanRatio
        FROM
        rpt.insurance_cust_stay_emp_mip
        WHERE 1=1
        <choose>
            <when test='monthEndDayList!=null and monthEndDayList.size>0'>
                AND rpt_date = to_date(#{monthEndDayList[${monthEndDayList.size-1}],jdbcType=VARCHAR},'yyyy-MM-dd')
            </when>
            <otherwise>
                and 1=0
            </otherwise>
        </choose>
        <if test='branchBuName != null'>
            AND division_name = #{branchBuName,jdbcType=VARCHAR}
        </if>
        <if test='regionName != null'>
            AND area_name = #{regionName,jdbcType=VARCHAR}
        </if>
        <if test='zoneName != null'>
            AND district_name = #{zoneName,jdbcType=VARCHAR}
        </if>
        <if test='orgName != null'>
            AND bch_name = #{orgName,jdbcType=VARCHAR}
        </if>
        <if test='userName != null'>
            AND (emp_name LIKE CONCAT(#{userName,jdbcType=VARCHAR},'%') OR main_emp_id LIKE CONCAT(#{userName,jdbcType=VARCHAR},'%'))
        </if>
        <if test='userId != null'>
            AND main_emp_id = #{userId,jdbcType=VARCHAR}
        </if>

        <if test='branchCode!=null and branchCode!=""'>
            AND (division_code=#{branchCode,jdbcType=VARCHAR} or area_code=#{branchCode,jdbcType=VARCHAR} or district_code=#{branchCode,jdbcType=VARCHAR} or bch_code=#{branchCode,jdbcType=VARCHAR})
        </if>
            ORDER BY
            <choose>
                <when test='sortType=="1"'>
                    policyholderStay
                </when>
                <when test='sortType=="2"'>
                    custStayLoanRatio
                </when>
                <when test='sortType=="3"'>
                    custStayLoanRatio
                </when>
                <when test='sortType=="4"'>
                    custStayLoanRelRatio
                </when>
                <when test='sortType=="5"'>
                    custStayNoloanRatio
                </when>
                <otherwise>
                    bch_code
                </otherwise>
            </choose>
            <choose>
                <when test='sortMode=="1"'>
                   ASC
                </when>
                <otherwise>
                   DESC
                </otherwise>
            </choose>
            ,main_emp_id ASC
    </select>

    <select id="listSmOrgCustStayReport" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmReportCustStayOrgVO">
        SELECT to_char(to_date(#{startDate},'yyyy-mm-dd'), 'yyyy-mm-dd') as startDate
        ,to_char(to_date(#{endDate},'yyyy-mm-dd'), 'yyyy-mm-dd') as endDate
        ,division_name as branchBuName
        ,division_code as branchBuCode
        ,area_name as regionName
        ,area_code as regionCode
        ,district_name as zoneName
        ,district_code as zoneCode
        ,bch_name as orgName
        ,bch_code as orgCode
        ,case when #{type}='1' then COALESCE(m_policyholder_stay,0) else COALESCE(y_policyholder_stay,0) end as policyholderStay
        ,case when #{type}='1' then COALESCE(m_stay_qty,0) else COALESCE(y_stay_qty,0) end as stayQty
        ,cast((case when #{type}='1' then COALESCE(m_stay_amount,0.0000) else COALESCE(y_stay_amount,0.0000) end) AS decimal(10,2)) as stayAmount
        ,cast((
        case when #{type}='1'
        then (case when COALESCE(m_qty,0)=0 then 0 else COALESCE(m_stay_qty,0.0000)/COALESCE(m_qty,0) end)
        else (case when COALESCE(y_qty,0)=0 then 0 else COALESCE(y_stay_qty,0.0000)/COALESCE(y_qty,0) end)
        end
        )*100 AS decimal(10,2)) as stayQtyRatio
        ,cast((
        case when #{type}='1'
        then (case when COALESCE(m_amount,0)=0 then 0 else COALESCE(m_stay_amount,0.0000)/COALESCE(m_amount,0) end)
        else (case when COALESCE(y_amount,0)=0 then 0 else COALESCE(y_stay_amount,0.0000)/COALESCE(y_amount,0) end)
        end
        )*100 AS decimal(10,2)) as stayAmountRatio
        ,cast((
        case when #{type}='1'
        then (case when COALESCE(m_policyholder,0)=0 then 0 else COALESCE(m_policyholder_stay,0.0000)/m_policyholder end)
        else (case when COALESCE(y_policyholder,0)=0 then 0 else COALESCE(y_policyholder_stay,0.0000)/y_policyholder end)
        end
        )*100 AS decimal(10,2)) as custStayRatio
        ,case when #{type}='1' then COALESCE(m_policyholder_loan_stay,0) else COALESCE(y_policyholder_loan_stay,0) end as policyholderLoanStay
        ,case when #{type}='1' then COALESCE(m_stay_qty_loan,0) else COALESCE(y_stay_qty_loan,0) end as stayQtyLoan
        ,cast((case when #{type}='1' then COALESCE(m_stay_amount_loan,0.0000) else COALESCE(y_stay_amount_loan,0.0000) end) AS decimal(10,2)) as stayAmountLoan
        ,cast((
        case when #{type}='1'
        then (case when COALESCE(m_policyholder_loan,0)=0 then 0 else COALESCE(m_policyholder_loan_stay,0.0000)/m_policyholder_loan end)
        else (case when COALESCE(y_policyholder_loan,0)=0 then 0 else COALESCE(y_policyholder_loan_stay,0.0000)/y_policyholder_loan end)
        end
        )*100 AS decimal(10,2)) as custStayLoanRatio
        ,case when #{type}='1' then COALESCE(m_policyholder_loan_rel_stay,0) else COALESCE(y_policyholder_loan_rel_stay,0) end as policyholderLoanRelStay
        ,case when #{type}='1' then COALESCE(m_stay_qty_loan_rel,0) else COALESCE(y_stay_qty_loan_rel,0) end as stayQtyLoanRel
        ,cast((case when #{type}='1' then COALESCE(m_stay_amount_loan_rel,0.0000) else COALESCE(y_stay_amount_loan_rel,0.0000) end) AS decimal(10,2)) as stayAmountLoanRel
        ,cast((
        case when #{type}='1'
        then (case when COALESCE(m_policyholder_loan_rel,0)=0 then 0 else COALESCE(m_policyholder_loan_rel_stay,0.0000)/m_policyholder_loan_rel end)
        else (case when COALESCE(y_policyholder_loan_rel,0)=0 then 0 else COALESCE(y_policyholder_loan_rel_stay,0.0000)/y_policyholder_loan_rel end)
        end
        )*100 AS decimal(10,2)) as custStayLoanRelRatio
        ,case when #{type}='1' then COALESCE(m_policyholder_noloan_stay,0) else COALESCE(y_policyholder_noloan_stay,0) end as policyholderNoloanStay
        ,case when #{type}='1' then COALESCE(m_stay_qty_noloan,0) else COALESCE(y_stay_qty_noloan,0) end as stayQtyNoloan
        ,cast((case when #{type}='1' then COALESCE(m_stay_amount_noloan,0.0000) else COALESCE(y_stay_amount_noloan,0.0000) end) AS decimal(10,2)) as stayAmountNoloan
        ,cast((
        case when #{type}='1'
        then (case when COALESCE(m_policyholder_noloan,0)=0 then 0 else COALESCE(m_policyholder_noloan_stay,0.0000)/m_policyholder_noloan end)
        else (case when COALESCE(y_policyholder_noloan,0)=0 then 0 else COALESCE(y_policyholder_noloan_stay,0.0000)/y_policyholder_noloan end)
        end
        )*100 AS decimal(10,2)) as custStayNoloanRatio
        FROM
        rpt.insurance_cust_stay_bch_mip
        WHERE 1=1
        <choose>
            <when test='monthEndDayList!=null and monthEndDayList.size>0'>
                AND rpt_date = to_date(#{monthEndDayList[${monthEndDayList.size-1}],jdbcType=VARCHAR},'yyyy-MM-dd')
            </when>
            <otherwise>
                and 1=0
            </otherwise>
        </choose>
        <if test='branchBuName != null'>
            AND division_name = #{branchBuName,jdbcType=VARCHAR}
        </if>
        <if test='regionName != null'>
            AND area_name = #{regionName,jdbcType=VARCHAR}
        </if>
        <if test='zoneName != null'>
            AND district_name = #{zoneName,jdbcType=VARCHAR}
        </if>
        <if test='orgName != null'>
            AND bch_name = #{orgName,jdbcType=VARCHAR}
        </if>
        <if test='branchCode!=null and branchCode!=""'>
            AND (division_code=#{branchCode,jdbcType=VARCHAR} or area_code=#{branchCode,jdbcType=VARCHAR} or district_code=#{branchCode,jdbcType=VARCHAR} or bch_code=#{branchCode,jdbcType=VARCHAR})
        </if>
            ORDER BY
            <choose>
                <when test='sortType=="1"'>
                    policyholderStay
                </when>
                <when test='sortType=="2"'>
                    custStayLoanRatio
                </when>
                <when test='sortType=="3"'>
                    custStayLoanRatio
                </when>
                <when test='sortType=="4"'>
                    custStayLoanRelRatio
                </when>
                <when test='sortType=="5"'>
                    custStayNoloanRatio
                </when>
                <otherwise>
                    bch_code
                </otherwise>
            </choose>
            <choose>
                <when test='sortMode=="1"'>
                    ASC
                </when>
                <otherwise>
                    DESC
                </otherwise>
            </choose>
    </select>

    <select id="listSmRegionCustStayReport" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmReportCustStayRegionVO">
              SELECT  to_char(to_date(#{startDate},'yyyy-mm-dd'), 'yyyy-mm-dd') as startDate
              ,to_char(to_date(#{endDate},'yyyy-mm-dd'), 'yyyy-mm-dd') as endDate
				,branchBuName
				,branchBuCode
				,regionName
				,regionCode
				,policyholderStay
				,stayQty
				,stayAmount
				,cast((case when stayQtySum=0 then 0 else COALESCE(stayQty,0.0000)/stayQtySum end)*100 AS decimal(10,2)) as stayQtyRatio
				,cast((case when stayAmountSum=0 then 0 else COALESCE(stayAmount,0.0000)/stayAmountSum end)*100 AS decimal(10,2)) as stayAmountRatio
				,cast((case when policyholder=0 then 0 else COALESCE(policyholderStay,0.0000)/policyholder end)*100 AS decimal(10,2)) as custStayRatio
				,policyholderLoanStay
				,stayQtyLoan
				,stayAmountLoan
				,cast((case when policyholderLoan=0 then 0 else COALESCE(policyholderLoanStay,0.0000)/policyholderLoan end)*100 AS decimal(10,2)) as custStayLoanRatio
				,policyholderLoanRelStay
				,stayQtyLoanRel
				,stayAmountLoanRel
				,cast((case when policyholderLoanRel=0 then 0 else COALESCE(policyholderLoanRelStay,0.0000)/policyholderLoanRel end)*100 AS decimal(10,2)) as custStayLoanRelRatio
				,policyholderNoloanStay
				,stayQtyNoloan
				,stayAmountNoloan
				,cast((case when policyholderNoloan=0 then 0 else COALESCE(policyholderNoloanStay,0.0000)/policyholderNoloan end)*100 AS decimal(10,2)) as custStayNoloanRatio
              FROM (
								SELECT division_name as branchBuName
							,division_code as branchBuCode
							,area_name as regionName
							,area_code as regionCode
							,sum(case when #{type}='1' then COALESCE(m_policyholder_stay,0) else COALESCE(y_policyholder_stay,0) end) as policyholderStay
							,sum(case when #{type}='1' then COALESCE(m_stay_qty,0) else COALESCE(y_stay_qty,0) end) as stayQty
							,cast(sum((case when #{type}='1' then COALESCE(m_stay_amount,0.0000) else COALESCE(y_stay_amount,0.0000) end)) AS decimal(10,2)) as stayAmount
                         ,sum(case when #{type}='1' then COALESCE(m_qty,0)	else COALESCE(y_qty,0) end) as stayQtySum
                         ,sum(case when #{type}='1'	then COALESCE(m_amount,0)	else COALESCE(y_amount,0)	end) as stayAmountSum
							,sum(case when #{type}='1' then COALESCE(m_policyholder,0) else COALESCE(y_policyholder,0) end) as policyholder
							,sum(case when #{type}='1' then COALESCE(m_policyholder_loan_stay,0) else COALESCE(y_policyholder_loan_stay,0) end) as policyholderLoanStay
							,sum(case when #{type}='1' then COALESCE(m_stay_qty_loan,0) else COALESCE(y_stay_qty_loan,0) end) as stayQtyLoan
							,cast(sum(case when #{type}='1' then COALESCE(m_stay_amount_loan,0.0000) else COALESCE(y_stay_amount_loan,0.0000) end) AS decimal(10,2)) as stayAmountLoan
							,sum(case when #{type}='1' then COALESCE(m_policyholder_loan,0) else COALESCE(y_policyholder_loan,0) end) as policyholderLoan
							,sum(case when #{type}='1' then COALESCE(m_policyholder_loan_rel_stay,0) else COALESCE(y_policyholder_loan_rel_stay,0) end) as policyholderLoanRelStay
							,sum(case when #{type}='1' then COALESCE(m_stay_qty_loan_rel,0) else COALESCE(y_stay_qty_loan_rel,0) end) as stayQtyLoanRel
							,cast(sum(case when #{type}='1' then COALESCE(m_stay_amount_loan_rel,0.0000) else COALESCE(y_stay_amount_loan_rel,0.0000) end) AS decimal(10,2)) as stayAmountLoanRel
							,sum(case when #{type}='1' then COALESCE(m_policyholder_loan_rel,0) else COALESCE(y_policyholder_loan_rel,0) end) as policyholderLoanRel
							,sum(case when #{type}='1' then COALESCE(m_policyholder_noloan_stay,0) else COALESCE(y_policyholder_noloan_stay,0) end) as policyholderNoloanStay
							,sum(case when #{type}='1' then COALESCE(m_stay_qty_noloan,0) else COALESCE(y_stay_qty_noloan,0) end) as stayQtyNoloan
							,cast(sum(case when #{type}='1' then COALESCE(m_stay_amount_noloan,0.0000) else COALESCE(y_stay_amount_noloan,0.0000) end) AS decimal(10,2)) as stayAmountNoloan
							,sum(case when #{type}='1' then COALESCE(m_policyholder_noloan,0) else COALESCE(y_policyholder_noloan,0) end) as policyholderNoloan
							FROM
							rpt.insurance_cust_stay_bch_mip
                        WHERE 1=1
                        <choose>
                            <when test='monthEndDayList!=null and monthEndDayList.size>0'>
                                AND rpt_date = to_date(#{monthEndDayList[${monthEndDayList.size-1}],jdbcType=VARCHAR},'yyyy-MM-dd')
                            </when>
                            <otherwise>
                                and 1=0
                            </otherwise>
                        </choose>
                        <if test='branchBuName != null'>
                            AND division_name = #{branchBuName,jdbcType=VARCHAR}
                        </if>
                        <if test='regionName != null'>
                            AND area_name = #{regionName,jdbcType=VARCHAR}
                        </if>
                        <if test='branchCode!=null and branchCode!=""'>
                            AND (division_code=#{branchCode,jdbcType=VARCHAR} or area_code=#{branchCode,jdbcType=VARCHAR})
                        </if>
			               GROUP BY division_code,division_name,area_code,area_name
               ) a
                    ORDER BY
                    <choose>
                        <when test='sortType=="1"'>
                            policyholderStay
                        </when>
                        <when test='sortType=="2"'>
                            custStayLoanRatio
                        </when>
                        <when test='sortType=="3"'>
                            custStayLoanRatio
                        </when>
                        <when test='sortType=="4"'>
                            custStayLoanRelRatio
                        </when>
                        <when test='sortType=="5"'>
                            custStayNoloanRatio
                        </when>
                        <otherwise>
                            regionCode
                        </otherwise>
                    </choose>
                    <choose>
                        <when test='sortMode=="1"'>
                            ASC
                        </when>
                        <otherwise>
                            DESC
                        </otherwise>
                    </choose>
    </select>

    <select id="listSmBranchBuCustStayReport" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmReportCustStayBranchBuVO">
              SELECT  to_char(to_date(#{startDate},'yyyy-mm-dd'), 'yyyy-mm-dd') as startDate
               ,to_char(to_date(#{endDate},'yyyy-mm-dd'), 'yyyy-mm-dd') as endDate
				,branchBuName
				,branchBuCode
				,policyholderStay
				,stayQty
				,stayAmount
				,cast((case when stayQtySum=0 then 0 else COALESCE(stayQty,0.0000)/stayQtySum end)*100 AS decimal(10,2)) as stayQtyRatio
				,cast((case when stayAmountSum=0 then 0 else COALESCE(stayAmount,0.0000)/stayAmountSum end)*100 AS decimal(10,2)) as stayAmountRatio
				,cast((case when policyholder=0 then 0 else COALESCE(policyholderStay,0.0000)/policyholder end)*100 AS decimal(10,2)) as custStayRatio
				,policyholderLoanStay
				,stayQtyLoan
				,stayAmountLoan
				,cast((case when policyholderLoan=0 then 0 else COALESCE(policyholderLoanStay,0.0000)/policyholderLoan end)*100 AS decimal(10,2)) as custStayLoanRatio
				,policyholderLoanRelStay
				,stayQtyLoanRel
				,stayAmountLoanRel
				,cast((case when policyholderLoanRel=0 then 0 else COALESCE(policyholderLoanRelStay,0.0000)/policyholderLoanRel end)*100 AS decimal(10,2)) as custStayLoanRelRatio
				,policyholderNoloanStay
				,stayQtyNoloan
				,stayAmountNoloan
				,cast((case when policyholderNoloan=0 then 0 else COALESCE(policyholderNoloanStay,0.0000)/policyholderNoloan end)*100 AS decimal(10,2)) as custStayNoloanRatio
        FROM (
								SELECT division_name as branchBuName
							,division_code as branchBuCode
							,sum(case when #{type}='1' then COALESCE(m_policyholder_stay,0) else COALESCE(y_policyholder_stay,0) end) as policyholderStay
							,sum(case when #{type}='1' then COALESCE(m_stay_qty,0) else COALESCE(y_stay_qty,0) end) as stayQty
							,cast(sum((case when #{type}='1' then COALESCE(m_stay_amount,0.0000) else COALESCE(y_stay_amount,0.0000) end)) AS decimal(10,2)) as stayAmount
                         ,sum(case when #{type}='1' then COALESCE(m_qty,0)	else COALESCE(y_qty,0) end) as stayQtySum
                         ,sum(case when #{type}='1'	then COALESCE(m_amount,0)	else COALESCE(y_amount,0)	end) as stayAmountSum
                         ,sum(case when #{type}='1' then COALESCE(m_policyholder,0) else COALESCE(y_policyholder,0) end) as policyholder
							,sum(case when #{type}='1' then COALESCE(m_policyholder_loan_stay,0) else COALESCE(y_policyholder_loan_stay,0) end) as policyholderLoanStay
							,sum(case when #{type}='1' then COALESCE(m_stay_qty_loan,0) else COALESCE(y_stay_qty_loan,0) end) as stayQtyLoan
							,cast(sum(case when #{type}='1' then COALESCE(m_stay_amount_loan,0.0000) else COALESCE(y_stay_amount_loan,0.0000) end) AS decimal(10,2)) as stayAmountLoan
							,sum(case when #{type}='1' then COALESCE(m_policyholder_loan,0) else COALESCE(y_policyholder_loan,0) end) as policyholderLoan
							,sum(case when #{type}='1' then COALESCE(m_policyholder_loan_rel_stay,0) else COALESCE(y_policyholder_loan_rel_stay,0) end) as policyholderLoanRelStay
							,sum(case when #{type}='1' then COALESCE(m_stay_qty_loan_rel,0) else COALESCE(y_stay_qty_loan_rel,0) end) as stayQtyLoanRel
							,cast(sum(case when #{type}='1' then COALESCE(m_stay_amount_loan_rel,0.0000) else COALESCE(y_stay_amount_loan_rel,0.0000) end) AS decimal(10,2)) as stayAmountLoanRel
							,sum(case when #{type}='1' then COALESCE(m_policyholder_loan_rel,0) else COALESCE(y_policyholder_loan_rel,0) end) as policyholderLoanRel
							,sum(case when #{type}='1' then COALESCE(m_policyholder_noloan_stay,0) else COALESCE(y_policyholder_noloan_stay,0) end) as policyholderNoloanStay
							,sum(case when #{type}='1' then COALESCE(m_stay_qty_noloan,0) else COALESCE(y_stay_qty_noloan,0) end) as stayQtyNoloan
							,cast(sum(case when #{type}='1' then COALESCE(m_stay_amount_noloan,0.0000) else COALESCE(y_stay_amount_noloan,0.0000) end) AS decimal(10,2)) as stayAmountNoloan
							,sum(case when #{type}='1' then COALESCE(m_policyholder_noloan,0) else COALESCE(y_policyholder_noloan,0) end) as policyholderNoloan
							FROM
							rpt.insurance_cust_stay_bch_mip
                        WHERE 1=1
                        <choose>
                            <when test='monthEndDayList!=null and monthEndDayList.size>0'>
                                AND rpt_date = to_date(#{monthEndDayList[${monthEndDayList.size-1}],jdbcType=VARCHAR},'yyyy-MM-dd')
                            </when>
                            <otherwise>
                                and 1=0
                            </otherwise>
                        </choose>
                        <if test='branchBuName != null'>
                            AND division_name = #{branchBuName,jdbcType=VARCHAR}
                        </if>
                        <if test='branchCode!=null and branchCode!=""'>
                            AND (division_code=#{branchCode,jdbcType=VARCHAR})
                        </if>
			            GROUP BY division_code,division_name
                   ) a
                    ORDER BY
                    <choose>
                        <when test='sortType=="1"'>
                            policyholderStay
                        </when>
                        <when test='sortType=="2"'>
                            custStayLoanRatio
                        </when>
                        <when test='sortType=="3"'>
                            custStayLoanRatio
                        </when>
                        <when test='sortType=="4"'>
                            custStayLoanRelRatio
                        </when>
                        <when test='sortType=="5"'>
                            custStayNoloanRatio
                        </when>
                        <otherwise>
                            branchBuCode
                        </otherwise>
                    </choose>
                    <choose>
                        <when test='sortMode=="1"'>
                            ASC
                        </when>
                        <otherwise>
                            DESC
                        </otherwise>
                    </choose>
    </select>

    <select id="listSmSummaryCustStayReport" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmReportCustStaySummaryVO">
              SELECT  to_char(to_date(#{startDate},'yyyy-mm-dd'), 'yyyy-mm-dd') as startDate
              ,to_char(to_date(#{endDate},'yyyy-mm-dd'), 'yyyy-mm-dd') as endDate
				,policyholderStay
				,stayQty
				,stayAmount
				,cast((case when stayQtySum=0 then 0 else COALESCE(stayQty,0.0000)/stayQtySum end)*100 AS decimal(10,2)) as stayQtyRatio
				,cast((case when stayAmountSum=0 then 0 else COALESCE(stayAmount,0.0000)/stayAmountSum end)*100 AS decimal(10,2)) as stayAmountRatio
				,cast((case when policyholder=0 then 0 else COALESCE(policyholderStay,0.0000)/policyholder end)*100 AS decimal(10,2)) as custStayRatio
				,policyholderLoanStay
				,stayQtyLoan
				,stayAmountLoan
				,cast((case when policyholderLoan=0 then 0 else COALESCE(policyholderLoanStay,0.0000)/policyholderLoan end)*100 AS decimal(10,2)) as custStayLoanRatio
				,policyholderLoanRelStay
				,stayQtyLoanRel
				,stayAmountLoanRel
				,cast((case when policyholderLoanRel=0 then 0 else COALESCE(policyholderLoanRelStay,0.0000)/policyholderLoanRel end)*100 AS decimal(10,2)) as custStayLoanRelRatio
				,policyholderNoloanStay
				,stayQtyNoloan
				,stayAmountNoloan
				,cast((case when policyholderNoloan=0 then 0 else COALESCE(policyholderNoloanStay,0.0000)/policyholderNoloan end)*100 AS decimal(10,2)) as custStayNoloanRatio
        FROM (
								SELECT sum(case when #{type}='1' then COALESCE(m_policyholder_stay,0) else COALESCE(y_policyholder_stay,0) end) as policyholderStay
							,sum(case when #{type}='1' then COALESCE(m_stay_qty,0) else COALESCE(y_stay_qty,0) end) as stayQty
							,cast(sum((case when #{type}='1' then COALESCE(m_stay_amount,0.0000) else COALESCE(y_stay_amount,0.0000) end)) AS decimal(10,2)) as stayAmount
                        ,sum(case when #{type}='1' then COALESCE(m_qty,0)	else COALESCE(y_qty,0) end) as stayQtySum
                        ,sum(case when #{type}='1'	then COALESCE(m_amount,0)	else COALESCE(y_amount,0)	end) as stayAmountSum
                         ,sum(case when #{type}='1' then COALESCE(m_policyholder,0) else COALESCE(y_policyholder,0) end) as policyholder
							,sum(case when #{type}='1' then COALESCE(m_policyholder_loan_stay,0) else COALESCE(y_policyholder_loan_stay,0) end) as policyholderLoanStay
							,sum(case when #{type}='1' then COALESCE(m_stay_qty_loan,0) else COALESCE(y_stay_qty_loan,0) end) as stayQtyLoan
							,cast(sum(case when #{type}='1' then COALESCE(m_stay_amount_loan,0.0000) else COALESCE(y_stay_amount_loan,0.0000) end) AS decimal(10,2)) as stayAmountLoan
							,sum(case when #{type}='1' then COALESCE(m_policyholder_loan,0) else COALESCE(y_policyholder_loan,0) end) as policyholderLoan
							,sum(case when #{type}='1' then COALESCE(m_policyholder_loan_rel_stay,0) else COALESCE(y_policyholder_loan_rel_stay,0) end) as policyholderLoanRelStay
							,sum(case when #{type}='1' then COALESCE(m_stay_qty_loan_rel,0) else COALESCE(y_stay_qty_loan_rel,0) end) as stayQtyLoanRel
							,cast(sum(case when #{type}='1' then COALESCE(m_stay_amount_loan_rel,0.0000) else COALESCE(y_stay_amount_loan_rel,0.0000) end) AS decimal(10,2)) as stayAmountLoanRel
							,sum(case when #{type}='1' then COALESCE(m_policyholder_loan_rel,0) else COALESCE(y_policyholder_loan_rel,0) end) as policyholderLoanRel
							,sum(case when #{type}='1' then COALESCE(m_policyholder_noloan_stay,0) else COALESCE(y_policyholder_noloan_stay,0) end) as policyholderNoloanStay
							,sum(case when #{type}='1' then COALESCE(m_stay_qty_noloan,0) else COALESCE(y_stay_qty_noloan,0) end) as stayQtyNoloan
							,cast(sum(case when #{type}='1' then COALESCE(m_stay_amount_noloan,0.0000) else COALESCE(y_stay_amount_noloan,0.0000) end) AS decimal(10,2)) as stayAmountNoloan
							,sum(case when #{type}='1' then COALESCE(m_policyholder_noloan,0) else COALESCE(y_policyholder_noloan,0) end) as policyholderNoloan
							FROM
							rpt.insurance_cust_stay_bch_mip
                        WHERE 1=1
                        <choose>
                            <when test='monthEndDayList!=null and monthEndDayList.size>0'>
                                AND rpt_date = to_date(#{monthEndDayList[${monthEndDayList.size-1}],jdbcType=VARCHAR},'yyyy-MM-dd')
                            </when>
                            <otherwise>
                                and 1=0
                            </otherwise>
                        </choose>
	       ) a
    </select>
</mapper>
