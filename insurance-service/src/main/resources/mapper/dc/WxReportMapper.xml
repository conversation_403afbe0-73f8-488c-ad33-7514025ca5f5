<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.weixin.dao.dc.WxReportMapper">

    <select id="listWxReportEmployeeAchmt" resultType="com.cfpamf.ms.insur.weixin.pojo.vo.WxReportEmployeeVO">
        SELECT
        area_nm AS regionName,
        bch_nm AS orgName,
        ofcr_bkey AS employeejobNumber,
        ofcr_nm AS employeeName,
        SUM(Insured_Cnt) AS orderQty,
        SUM(Insured_Amt) AS orderAmount,
        SUM(Insured_Check_Cnt) AS checkQty,
        SUM(Insured_Check_Cnt)/10 AS checkCnt
        FROM
        rpt.insured_perf_static_detail_d
        WHERE
        Is_check = 1
        AND Role_Nm = '客户经理'
        <if test='regionName != null'>
            AND area_nm = #{regionName}
        </if>
        <if test='orgName != null'>
            AND bch_nm = #{orgName}
        </if>
        <![CDATA[ AND rpt_date >= #{startDate}  ]]>
        <![CDATA[ AND rpt_date <= #{endDate}  ]]>
        <if test='employee != null'>
            AND (ofcr_bkey LIKE CONCAT(#{employee},'%') OR ofcr_nm LIKE CONCAT(#{employee},'%'))
        </if>
        GROUP BY ofcr_bkey, area_nm, bch_nm, ofcr_nm
        ORDER BY area_nm ASC, bch_nm ASC, ofcr_bkey ASC
    </select>

    <select id="listWxReportRegionAchmt" resultType="com.cfpamf.ms.insur.weixin.pojo.vo.WxReportRegionVO">
        SELECT
        t1.area_nm AS regionName,
        (CASE WHEN MIN(t2.check_person) IS NOT NULL THEN MIN(t2.check_person) ELSE 0 END) * 10 AS goalOrderQty,
        SUM(t1.Insured_Check_Cnt) AS checkOrderQty,
        CASE WHEN SUM(t1.Insured_GoalCnt) > 0
            THEN SUM(t1.Insured_Check_Cnt)/SUM(t1.Insured_GoalCnt)
            ELSE 0
        END AS checkCnt,
        CASE WHEN SUM(t2.check_person) > 0
            THEN (CASE WHEN SUM(t2.Check_Cnt_Un60) IS NOT NULL THEN SUM(t2.Check_Cnt_Un60) ELSE 0 END) / SUM(t2.check_person)
            ELSE 0
        END AS checkUn6Cnt
        FROM
        rpt.insured_perf_static_detail_d t1
        LEFT JOIN
        (
            SELECT
            area_nm,
            COUNT(0) AS check_person,
            SUM(Check_Un60) AS Check_Cnt_Un60
            FROM
            (
                SELECT
                area_nm,
                CASE WHEN SUM(Insured_Check_Cnt) &lt; 6
                    THEN 1
                    ELSE 0
                END AS Check_Un60
                FROM
                rpt.insured_perf_static_detail_d
                WHERE
                <![CDATA[ rpt_date >= #{startDate}  ]]>
                <![CDATA[ AND rpt_date <= #{endDate}  ]]>
                AND Role_Nm = '客户经理'
                AND Is_check = 1
                <if test='regionName != null'>
                    AND area_nm = #{regionName}
                </if>
                GROUP BY ofcr_bkey, area_nm
            ) t1
            GROUP BY area_nm
        ) t2
        ON t2.area_nm = t1.area_nm
        WHERE
        <![CDATA[ t1.rpt_date >= #{startDate}  ]]>
        <![CDATA[ AND t1.rpt_date <= #{endDate}  ]]>
        <if test='regionName != null'>
            AND t1.area_nm = #{regionName}
        </if>
        GROUP BY t1.area_nm
        ORDER BY t1.area_nm
    </select>

    <select id="listWxReportOrgAchmt" resultType="com.cfpamf.ms.insur.weixin.pojo.vo.WxReportOrgVO">
        SELECT
        t1.area_nm AS regionName,
        t1.bch_nm AS orgName,
        (CASE WHEN MIN(t2.check_person) IS NOT NULL THEN MIN(t2.check_person) ELSE 0 END) * 10 AS goalOrderQty,
        SUM(t1.Insured_Check_Cnt) AS checkOrderQty,
        CASE WHEN SUM(t1.Insured_GoalCnt)>0
             THEN SUM(t1.Insured_Check_Cnt)/SUM(t1.Insured_GoalCnt)
             ELSE 0
        END AS checkCnt,
        CASE WHEN SUM(t2.check_person) > 0
             THEN (CASE WHEN SUM(t2.Check_Cnt_Un60) IS NOT NULL THEN SUM(t2.Check_Cnt_Un60) ELSE 0 END) / SUM(t2.check_person)
             ELSE 0
        END AS checkUn6Cnt
        FROM
        rpt.insured_perf_static_detail_d t1
        LEFT JOIN
        (
            SELECT
            area_nm,
            bch_nm,
            COUNT(0) AS check_person,
            SUM(Check_Un60) AS Check_Cnt_Un60
            FROM
            (
                SELECT
                area_nm,
                bch_nm,
                CASE WHEN SUM(Insured_Check_Cnt) &lt; 6
                    THEN 1
                    ELSE 0
                END AS Check_Un60
                FROM
                rpt.insured_perf_static_detail_d
                WHERE
                <![CDATA[ rpt_date >= #{startDate}  ]]>
                <![CDATA[ AND rpt_date <= #{endDate}  ]]>
                AND Role_Nm = '客户经理'
                AND Is_check = 1
                <if test='regionName != null'>
                    AND area_nm = #{regionName}
                </if>
                <if test='orgName != null'>
                    AND bch_nm = #{orgName}
                </if>
                GROUP BY area_nm, bch_nm, ofcr_bkey
            ) t1
            GROUP BY area_nm, bch_nm
        ) t2
        ON t2.area_nm = t1.area_nm AND t2.bch_nm = t1.bch_nm
        WHERE
        <![CDATA[ t1.rpt_date >= #{startDate}  ]]>
        <![CDATA[ AND t1.rpt_date <= #{endDate}  ]]>
        <if test='regionName != null'>
            AND t1.area_nm = #{regionName}
        </if>
        <if test='orgName != null'>
            AND t1.bch_nm = #{orgName}
        </if>
        GROUP BY t1.area_nm, t1.bch_nm
        ORDER BY t1.area_nm, t1.bch_nm
    </select>

    <select id="listWxReportPersonalBusDetailsAchmt" resultType="com.cfpamf.ms.insur.weixin.pojo.vo.WxReportBusDetailsPersonalVO">
        SELECT a.division_name as branchBuName,
        a.division_cde as branchBuCode,
        a.area_name as regionName,
        a.area_cde as regionCode,
        a.district_name as zoneName,
        a.district_cde as zoneCode,
        a.bch_name as orgName,
        a.bch_cde as orgCode,
        a.usr_name as userName,
        a.main_usr_cde as userCode,
        (COALESCE(a.insured_cnt,0)-COALESCE(a.surrender_cnt,0)) as insuredCnt,
        cast((COALESCE(a.insured_amt,0.0000)-COALESCE(a.surrender_amt,0.0000)) AS decimal(10,2)) as insuredAmt,
        cast((COALESCE(a.insured_noloan_amt,0.0000)-COALESCE(a.surrender_noloan_amt,0.0000)) AS decimal(10,2)) as insuredNoloanAmt
        ,COALESCE(b.m_policyholder_stay,0) as insuredStayCust
        ,cast((case when COALESCE(b.m_policyholder,0)=0 then 0 else COALESCE(b.m_policyholder_stay,0.0000)/b.m_policyholder end)*100 AS decimal(10,2)) as insuredStayCustRatio
        FROM
        rpt.insurance_employee_order_m_dip a
        LEFT JOIN rpt.insurance_cust_stay_emp_mip b on a.main_usr_cde = b.main_emp_id
        and a.bch_cde = b.bch_code
        and a.district_cde = b.district_code
        and a.area_cde = b.area_code
        and a.division_cde = b.division_code
        and a.rpt_date = b.rpt_date
        WHERE 1=1
        <choose>
            <when test='monthEndDayList!=null and monthEndDayList.size>0'>
                AND a.rpt_date = to_date(#{monthEndDayList[${monthEndDayList.size-1}],jdbcType=VARCHAR},'yyyy-MM-dd')
            </when>
            <otherwise>
                and 1=0
            </otherwise>
        </choose>
        <if test='branchBuName != null'>
            AND a.division_name = #{branchBuName,jdbcType=VARCHAR}
        </if>
        <if test='regionName != null'>
            AND a.area_name = #{regionName,jdbcType=VARCHAR}
        </if>
        <if test='zoneName != null'>
            AND a.district_name = #{zoneName,jdbcType=VARCHAR}
        </if>
        <if test='orgName != null'>
            AND a.bch_name = #{orgName,jdbcType=VARCHAR}
        </if>
        <if test='employee != null'>
            AND (a.main_usr_cde LIKE CONCAT(#{employee,jdbcType=VARCHAR},'%') OR a.usr_name LIKE CONCAT(#{employee,jdbcType=VARCHAR},'%'))
        </if>
        <choose>
            <when test="sort!=null and sort==''">
                ORDER BY regionCode ASC,orgCode ASC,userCode ASC
            </when>
            <!-- 按保费从高到低排序 -->
            <when test="sort!=null and sort=='insuredAmt'">
                ORDER BY insuredAmt DESC
            </when>
            <!-- 按单量从高到低排序 -->
            <when test="sort!=null and sort=='insuredCnt'">
                ORDER BY insuredCnt DESC
            </when>
            <!-- 按人均非信贷客户保费从高到低排序 -->
            <when test="sort!=null and sort=='insuredNoloanAmt'">
                ORDER BY insuredNoloanAmt DESC
            </when>
            <!-- 留存客户数 -->
            <when test="sort!=null and sort=='insuredStayCust'">
                ORDER BY insuredStayCust DESC
            </when>
            <!-- 留存客户数 -->
            <when test="sort!=null and sort=='insuredStayCustRatio'">
                ORDER BY insuredStayCustRatio DESC
            </when>
        </choose>
    </select>

    <select id="listWxReportOrgBusDetailsAchmt" resultType="com.cfpamf.ms.insur.weixin.pojo.vo.WxReportBusDetailsOrgVO">
        SELECT a.branchBuName,a.regionName,a.zoneName,a.orgName,a.insuredCnt,a.insuredAmt,a.insuredAmtAvg,a.insuredNoloanAmtAvg
        ,COALESCE(b.m_policyholder_stay,0) as insuredStayCust
        ,cast((case when COALESCE(b.m_policyholder,0)=0 then 0 else COALESCE(b.m_policyholder_stay,0.0000)/b.m_policyholder end)*100 AS decimal(10,2)) as insuredStayCustRatio
        from (
            SELECT rpt_date
            ,division_name as branchBuName
            ,division_cde as branchBuCode
            ,area_name as regionName
            ,area_cde as regionCode
            ,district_name as zoneName
            ,district_cde as zoneCode
            ,bch_name as orgName
            ,bch_cde as orgCode
            ,sum(COALESCE(insured_cnt,0)-COALESCE(surrender_cnt,0)) as insuredCnt
            ,cast(sum(COALESCE(insured_amt,0.0000)-COALESCE(surrender_amt,0.0000)) AS decimal(10,2)) as insuredAmt
            ,cast((case when sum(COALESCE(company_age_coefficient,0.0000))=0 then 0.0000 else sum(COALESCE(insured_amt,0.0000)-COALESCE(surrender_amt,0.0000))/sum(COALESCE(company_age_coefficient,0.0000)) end) AS decimal(10,2)) as insuredAmtAvg
            ,cast((case when sum(COALESCE(company_age_coefficient,0.0000))=0 then 0.0000 else sum(COALESCE(insured_noloan_amt,0.0000)-COALESCE(surrender_noloan_amt,0.0000))/sum(COALESCE(company_age_coefficient,0.0000)) end) AS decimal(10,2)) as insuredNoloanAmtAvg
            FROM
            rpt.insurance_employee_order_m_dip
            WHERE 1=1
            <choose>
                <when test='monthEndDayList!=null and monthEndDayList.size>0'>
                    AND rpt_date = to_date(#{monthEndDayList[${monthEndDayList.size-1}],jdbcType=VARCHAR},'yyyy-MM-dd')
                </when>
                <otherwise>
                    and 1=0
                </otherwise>
            </choose>
            <if test='branchBuName != null'>
                AND division_name = #{branchBuName,jdbcType=VARCHAR}
            </if>
            <if test='regionName != null'>
                AND area_name = #{regionName,jdbcType=VARCHAR}
            </if>
            <if test='zoneName != null'>
                AND district_name = #{zoneName,jdbcType=VARCHAR}
            </if>
            <if test='orgName != null'>
                AND bch_name = #{orgName}
            </if>
            GROUP BY division_name,division_cde,area_cde,area_name,district_name,district_cde,bch_cde,bch_name,rpt_date
        ) a
        LEFT JOIN rpt.insurance_cust_stay_bch_mip b on a.orgCode = b.bch_code
        and a.zoneCode = b.district_code
        and a.regionCode = b.area_code
        and a.branchBuCode = b.division_code
        and a.rpt_date = b.rpt_date
        <choose>
            <when test="sort!=null and sort==''">
                ORDER BY regionCode ASC,orgCode ASC
            </when>
            <!-- 按保费从高到低排序 -->
            <when test="sort!=null and sort=='insuredAmt'">
                ORDER BY insuredAmt DESC
            </when>
            <!-- 按单量从高到低排序 -->
            <when test="sort!=null and sort=='insuredCnt'">
                ORDER BY insuredCnt DESC
            </when>
            <!-- 按人均保费从高到低排序 -->
            <when test="sort!=null and sort=='insuredAmtAvg'">
                ORDER BY insuredAmtAvg DESC
            </when>

            <!-- 按人均非信贷客户保费从高到低排序 -->
            <when test="sort!=null and sort=='insuredNoloanAmtAvg'">
                ORDER BY insuredNoloanAmtAvg DESC
            </when>

            <!-- 按客户留存率从高到低排序 -->
            <when test="sort!=null and sort=='insuredStayCustRatio'">
                ORDER BY insuredStayCustRatio DESC
            </when>
        </choose>
    </select>

    <select id="listWxReportRegionBusDetailsAchmt" resultType="com.cfpamf.ms.insur.weixin.pojo.vo.WxReportBusDetailsRegionVO">
        SELECT a.branchBuName,a.regionName,a.insuredCnt,a.insuredAmt,a.insuredAmtAvg,a.insuredNoloanAmtAvg
        ,COALESCE(b.policyholderStay,0) as insuredStayCust
        ,cast((case when COALESCE(b.policyholder,0)=0 then 0 else COALESCE(b.policyholderStay,0.0000)/b.policyholder end)*100 AS decimal(10,2)) as insuredStayCustRatio
        from (
        SELECT rpt_date
        ,division_name as branchBuName
        ,division_cde as branchBuCode
        ,area_name as regionName
        ,area_cde as regionCode
        ,sum(COALESCE(insured_cnt,0)-COALESCE(surrender_cnt,0)) as insuredCnt
        ,cast(sum(COALESCE(insured_amt,0.0000)-COALESCE(surrender_amt,0.0000)) AS decimal(10,2)) as insuredAmt
        ,cast((case when sum(COALESCE(company_age_coefficient,0.0000))=0 then 0.0000 else sum(COALESCE(insured_amt,0.0000)-COALESCE(surrender_amt,0.0000))/sum(COALESCE(company_age_coefficient,0.0000)) end) AS decimal(10,2)) as insuredAmtAvg
        ,cast((case when sum(COALESCE(company_age_coefficient,0.0000))=0 then 0.0000 else sum(COALESCE(insured_noloan_amt,0.0000)-COALESCE(surrender_noloan_amt,0.0000))/sum(COALESCE(company_age_coefficient,0.0000)) end) AS decimal(10,2)) as insuredNoloanAmtAvg
        FROM
        rpt.insurance_employee_order_m_dip
        WHERE 1=1
        <choose>
            <when test='monthEndDayList!=null and monthEndDayList.size>0'>
                AND rpt_date = to_date(#{monthEndDayList[${monthEndDayList.size-1}],jdbcType=VARCHAR},'yyyy-MM-dd')
            </when>
            <otherwise>
                and 1=0
            </otherwise>
        </choose>
        <if test='branchBuName != null'>
            AND division_name = #{branchBuName,jdbcType=VARCHAR}
        </if>
        <if test='regionName != null'>
            AND area_name = #{regionName}
        </if>
        GROUP BY division_name,division_cde,area_cde,area_name,rpt_date
        ) a
        LEFT JOIN (
        SELECT division_code,division_name,area_code,area_name,rpt_date
        ,sum(COALESCE(m_policyholder_stay,0)) as policyholderStay
        ,sum(COALESCE(m_policyholder,0)) as policyholder
        FROM
        rpt.insurance_cust_stay_bch_mip
        GROUP BY division_code,division_name,area_code,area_name,rpt_date
        ) b on a.regionCode = b.area_code
        and a.branchBuCode = b.division_code
        and a.rpt_date = b.rpt_date
        <choose>
            <when test="sort!=null and sort==''">
                ORDER BY regionCode ASC
            </when>
            <!-- 按保费从高到低排序 -->
            <when test="sort!=null and sort=='insuredAmt'">
                ORDER BY insuredAmt DESC
            </when>
            <!-- 按单量从高到低排序 -->
            <when test="sort!=null and sort=='insuredCnt'">
                ORDER BY insuredCnt DESC
            </when>
            <!-- 按人均保费从高到低排序 -->
            <when test="sort!=null and sort=='insuredAmtAvg'">
                ORDER BY insuredAmtAvg DESC
            </when>
            <!-- 按人均非信贷客户保费从高到低排序 -->
            <when test="sort!=null and sort=='insuredNoloanAmtAvg'">
                ORDER BY insuredNoloanAmtAvg DESC
            </when>

            <!-- 按客户留存率从高到低排序 -->
            <when test="sort!=null and sort=='insuredStayCustRatio'">
                ORDER BY insuredStayCustRatio DESC
            </when>
        </choose>
    </select>

    <!--add by zhangjian 2020-05-19 事业部业绩报表-->
    <select id="listWxReportBranchBuBusDetailsAchmt" resultType="com.cfpamf.ms.insur.weixin.pojo.vo.WxReportBusDetailsBranchBuVO">
        SELECT a.branchBuName,a.insuredCnt,a.insuredAmt,a.insuredAmtAvg,a.insuredNoloanAmtAvg
        ,COALESCE(b.policyholderStay,0) as insuredStayCust
        ,cast((case when COALESCE(b.policyholder,0)=0 then 0 else COALESCE(b.policyholderStay,0.0000)/b.policyholder end)*100 AS decimal(10,2)) as insuredStayCustRatio
        from (
            SELECT rpt_date
            ,division_name as branchBuName
            ,division_cde as branchBuCode
            ,sum(COALESCE(insured_cnt,0)-COALESCE(surrender_cnt,0)) as insuredCnt
            ,cast(sum(COALESCE(insured_amt,0.0000)-COALESCE(surrender_amt,0.0000)) AS decimal(10,2)) as insuredAmt
            ,cast((case when sum(COALESCE(company_age_coefficient,0.0000))=0 then 0.0000 else sum(COALESCE(insured_amt,0.0000)-COALESCE(surrender_amt,0.0000))/sum(COALESCE(company_age_coefficient,0.0000)) end) AS decimal(10,2)) as insuredAmtAvg
            ,cast((case when sum(COALESCE(company_age_coefficient,0.0000))=0 then 0.0000 else sum(COALESCE(insured_noloan_amt,0.0000)-COALESCE(surrender_noloan_amt,0.0000))/sum(COALESCE(company_age_coefficient,0.0000)) end) AS decimal(10,2)) as insuredNoloanAmtAvg
            FROM
            rpt.insurance_employee_order_m_dip
            WHERE 1=1
            <choose>
                <when test='monthEndDayList!=null and monthEndDayList.size>0'>
                    AND rpt_date = to_date(#{monthEndDayList[${monthEndDayList.size-1}],jdbcType=VARCHAR},'yyyy-MM-dd')
                </when>
                <otherwise>
                    and 1=0
                </otherwise>
            </choose>
            <if test='branchBuName != null'>
                AND division_name = #{branchBuName}
            </if>
            <if test='branchBuCode != null'>
                AND division_cde = #{branchBuCode}
            </if>
            GROUP BY division_name,division_cde,rpt_date
        ) a
        LEFT JOIN (
        SELECT division_code,division_name,rpt_date
        ,sum(COALESCE(m_policyholder_stay,0)) as policyholderStay
        ,sum(COALESCE(m_policyholder,0)) as policyholder
        FROM
        rpt.insurance_cust_stay_bch_mip
        GROUP BY division_code,division_name,rpt_date
        ) b on a.branchBuCode = b.division_code
        and a.rpt_date = b.rpt_date
        <choose>
            <when test="sort!=null and sort==''">
                ORDER BY branchBuCode ASC
            </when>
            <!-- 按保费从高到低排序 -->
            <when test="sort!=null and sort=='insuredAmt'">
                ORDER BY insuredAmt DESC
            </when>
            <!-- 按单量从高到低排序 -->
            <when test="sort!=null and sort=='insuredCnt'">
                ORDER BY insuredCnt DESC
            </when>
            <!-- 按人均保费从高到低排序 -->
            <when test="sort!=null and sort=='insuredAmtAvg'">
                ORDER BY insuredAmtAvg DESC
            </when>
            <!-- 按人均非信贷客户保费从高到低排序 -->
            <when test="sort!=null and sort=='insuredNoloanAmtAvg'">
                ORDER BY insuredNoloanAmtAvg DESC
            </when>

            <!-- 按客户留存率从高到低排序 -->
            <when test="sort!=null and sort=='insuredStayCustRatio'">
                ORDER BY insuredStayCustRatio DESC
            </when>
        </choose>
    </select>

    <!--<select id="listWxReportPersonalCustStay" resultType="com.cfpamf.ms.insur.weixin.pojo.vo.WxReportCustStayPersonalVO">

    </select>

    <select id="listWxReportOrgCustStay" resultType="com.cfpamf.ms.insur.weixin.pojo.vo.WxReportCustStayOrgVO">

    </select>

    <select id="listWxReportRegionCustStay" resultType="com.cfpamf.ms.insur.weixin.pojo.vo.WxReportCustStayRegionVO">

    </select>

    <select id="listWxReportBranchBuCustStay" resultType="com.cfpamf.ms.insur.weixin.pojo.vo.WxReportCustStayBranchBuVO">

    </select>-->

    <select id="listWxTopSalesEmployee"
            resultType="com.cfpamf.ms.insur.weixin.pojo.vo.WxActivityTopSalesEmployeeVO">
        SELECT
        area_nm AS regionName,
        bch_nm AS organizationName,
        ofcr_nm AS userName,
        <choose>
            <when test="orderType!=null and orderType=='cnt'">
                SUM(Insured_Check_Cnt) AS checkQty
            </when>
            <when test="orderType!=null and orderType=='amt'">
                SUM(Insured_Check_Amt) AS checkQty
            </when>
            <otherwise>
                SUM(Insured_Check_Cnt) AS checkQty
            </otherwise>
        </choose>
        FROM rpt.insured_perf_static_detail_d
        WHERE
        Is_check = 1
        <if test='startDate != null'>
            <![CDATA[ AND rpt_date >= #{startDate}  ]]>
        </if>
        <if test='endDate != null'>
            <![CDATA[ AND rpt_date <= #{endDate}  ]]>
        </if>
        GROUP BY area_nm, bch_nm, ofcr_nm, ofcr_bkey
        <choose>
            <when test="orderType!=null and orderType=='cnt'">
                ORDER BY SUM(Insured_Check_Cnt) DESC, SUM(Insured_Check_Amt) DESC
            </when>
            <when test="orderType!=null and orderType=='amt'">
                ORDER BY SUM(Insured_Check_Amt) DESC, SUM(Insured_Check_Cnt) DESC
            </when>
            <otherwise>
                ORDER BY SUM(Insured_Check_Cnt) DESC, SUM(Insured_Check_Amt) DESC
            </otherwise>
        </choose>
    </select>
</mapper>