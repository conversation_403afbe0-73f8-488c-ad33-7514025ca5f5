<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.order.SmCancelRefundMapper">

    <select id="listByQuery" resultType="com.cfpamf.ms.insur.admin.pojo.vo.order.SmCancelRefundDetailVO">
        select scr.*,
        sp.productName,
        sp1.planName,
        soa.personName applicant_name,
        so.totalAmount,
        au.userName as acceptor_name,
        sc.cancel_no,
        sop.pay_type,
        soi.personName insured_name,
        so.startTime,
        so.endTime,
        so.create_time order_create_time,
        sc.create_time cancel_create_time
        from sm_cancel_refund scr
        left join sm_order so on so.fhOrderId = scr.fh_order_id
        left join sm_product sp on sp.id = so.productId
        left join sm_plan sp1 on sp1.id = so.planId
        left join sm_order_applicant soa on soa.fhOrderId = so.fhOrderId
        left join auth_user au on au.userId = scr.acceptor and au.enabled_flag = 0
        left join sm_order_payment sop on sop.fh_order_id = scr.fh_order_id
        left join sm_cancel sc on sc.fh_order_id = scr.fh_order_id <![CDATA[   and sc.state <> 50]]>
        left join sm_order_insured soi on soi.fhOrderId = scr.fh_order_id and soi.idNumber = scr.id_number and
        scr.policy_no = soi.policyNo
        where
        scr.enabled_flag = 0
        <if test="cancelRefundId != null">
            <![CDATA[ and scr.id  = #{cancelRefundId} ]]>
        </if>
        <if test="sysAcceptStartDate != null">
            <![CDATA[ and scr.sys_accept_time  >= #{sysAcceptStartDate}  ]]>
        </if>

        <if test="sysAcceptEndDate != null">
            <![CDATA[  and scr.sys_accept_time <  #{sysAcceptEndDate}  ]]>
        </if>
        <if test="cancelNo != null">
            <![CDATA[  and sc.cancel_no =#{cancelNo} ]]>
        </if>
        <if test="fhOrderId != null">
            <![CDATA[  and scr.fh_order_id =#{fhOrderId} ]]>
        </if>
        <if test="policyNo != null">
            <![CDATA[  and scr.policy_no =#{policyNo} ]]>
        </if>
        <if test="applicantKeyword != null">
            AND ((soa.personName LIKE CONCAT(#{applicantdName},'%')) OR (soa.idNumber LIKE
            CONCAT(#{applicantdName},'%'))
            OR (soa.cellPhone LIKE CONCAT(#{applicantdName},'%')))

        </if>
        <if test="acceptStartDate != null">
            <![CDATA[ and scr.accept_time  >= #{acceptStartDate}  ]]>
        </if>

        <if test="acceptEndDate != null">
            <![CDATA[  and scr.accept_time <  #{acceptEndDate}  ]]>
        </if>

        <if test="refundStatuses!=null">
            and refund_status in
            <foreach collection="refundStatuses" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>

        order by scr.id desc
    </select>
</mapper>
