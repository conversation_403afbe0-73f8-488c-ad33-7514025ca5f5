<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.reconciliation.SmReconciliationDifferenceMapper">
    <update id="updateBalanceAccountFlagAndReason">
        update sm_reconciliation_difference
        set balance_account_reason = #{reason} ,balance_account_flag = 1
        where id in
        <foreach collection="idSet" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <update id="batchUpdatePolicyState">
        update sm_reconciliation_difference t1
        left JOIN sm_order_insured t2
        on t1.policy_no = t2.policyNo
        set t1.app_status = t2.appStatus
        WHERE t1.company_flag = 1 and t1.reconciliation_id = #{reconciliationId}
        and t1.policy_no in
        <foreach collection="policyList" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <update id="batchUpdateSystemCheckReason">
        update sm_reconciliation_difference
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="system_check_reason =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when id=#{item.id} then #{item.systemCheckReason}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item.id}
        </foreach>
    </update>

    <select id="getByReconciliationId"
            resultType="com.cfpamf.ms.insur.admin.pojo.po.reconciliation.SmReconciliationDifference">
        select *
        from sm_reconciliation_difference
        where reconciliation_id = #{reconciliationId}
          and enabled_flag = 0
    </select>
    <select id="searchSmReconciliationDifference"
            resultType="com.cfpamf.ms.insur.admin.pojo.po.reconciliation.SmReconciliationDifference">
        select * from
        sm_reconciliation_difference
        where 1=1
        <if test="reconciliationId != null ">
            and reconciliation_id = #{reconciliationId}
        </if>
        and serial_number in (
        select serial_number
        from sm_reconciliation_difference
        where
        enabled_flag = 0
        <if test="reconciliationId != null ">
            and reconciliation_id = #{reconciliationId}
        </if>
        <if test="balanceAccountFlag != null ">
            and balance_account_flag = #{balanceAccountFlag}
        </if>
        <if test="policyNo != null and policyNo != '' ">
            and policy_no = #{policyNo}
        </if>
        <if test="applicantName != null and applicantName != '' ">
            and applicant_name = #{applicantName}
        </if>

        )

    </select>

    <select id="getByReconciliationIdAndSerialNumber"
            resultType="com.cfpamf.ms.insur.admin.pojo.po.reconciliation.SmReconciliationDifference">
        select *
        from sm_reconciliation_difference
        where
        enabled_flag = 0
        and reconciliation_id = #{reconciliationId}
        and serial_number in
        <foreach collection="serialNumberList" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>


</mapper>