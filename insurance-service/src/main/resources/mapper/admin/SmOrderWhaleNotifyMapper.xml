<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.order.SmOrderWhaleNotifyMapper">

    <update id="deleteSmOrderWhaleNotify" parameterType="arraylist">
        DELETE FROM sm_order_whale_notify WHERE id IN
        <foreach collection="ids" index="index" item="id" separator=","
                 open="(" close=")">
            #{id}
        </foreach>
    </update>
</mapper>