<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.claim.SmFileConfigMapper">
    <update id="softDeleteByCode">
        UPDATE sm_file_config
        SET enabled_flag = 1
        WHERE code = #{code}
    </update>

    <select id="pageConfig" resultType="com.cfpamf.ms.insur.admin.pojo.po.claim.SmFileConfig">
        SELECT * FROM sm_file_config
        <where>
            <if test="name != null and name != ''">
                AND name LIKE CONCAT('%',#{name},'%')
            </if>
            <if test="code != null and code != ''">
                AND code = #{code} AND eabled_flag = 0
            </if>
            AND enabled_flag = 0
        </where>

    </select>


</mapper>