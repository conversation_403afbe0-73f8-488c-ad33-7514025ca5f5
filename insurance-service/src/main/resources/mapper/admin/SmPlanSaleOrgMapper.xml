<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.SmPlanSaleOrgMapper">
    <select id="listProductPlansById" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmPlanVO">
        SELECT t1.*
        FROM sm_plan t1
        WHERE t1.productId = #{productId}
          AND t1.enabled_flag = 0
          and t1.id in (
            select t11.plan_id
            from sm_plan_sales_org t11
            where t11.product_id = #{productId}
              and t11.enabled_flag = 0
              and (t11.org_name = #{regionName} or t11.org_path is null)
        )
        order by t1.id
    </select>
</mapper>
