<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.order.SmOrderGroupNotifyMapper">

    <update id="fullUpdateNotify" parameterType="com.cfpamf.ms.insur.admin.pojo.po.order.SmOrderGroupNotify">
        update sm_order_group_notify
        SET
        <if test="groupPolicyNo!=null ">
            group_policy_no=#{groupPolicyNo},
        </if>
        <if test="endorsementNo!=null ">
            endorsement_no=#{endorsementNo},
        </if>
        <if test="type!=null ">
            type=#{type},
        </if>
        <if test="opMethod!=null ">
            op_method=#{opMethod},
        </if>
        <if test="status!=null ">
            status=#{status},
        </if>
        <if test="policyState!=null ">
            policy_state=#{policyState},
        </if>
        <if test="groupType!=null ">
            group_type=#{groupType},
        </if>
        <if test="requestId!=null ">
            request_id=#{requestId},
        </if>
        update_time=CURRENT_TIMESTAMP()
        WHERE id=#{id}
    </update>

    <update id="updateNotifyByPolicy" parameterType="com.cfpamf.ms.insur.admin.pojo.po.order.SmOrderGroupNotify">
        update sm_order_group_notify
        SET
        <if test="type!=null ">
            type=#{type},
        </if>
        <if test="opMethod!=null ">
            op_method=#{opMethod},
        </if>
        <if test="status!=null ">
            status=#{status},
        </if>
        <if test="policyState!=null ">
            policy_state=#{policyState},
        </if>
        <if test="groupType!=null ">
            group_type=#{groupType},
        </if>
        <if test="requestId!=null ">
            request_id=#{requestId},
        </if>
        update_time=CURRENT_TIMESTAMP()
        WHERE group_policy_no=#{groupPolicyNo}
              and endorsement_no=#{endorsementNo}
              and channel=#{channel}
    </update>
    
    <select id="listByPolicyNoAndStatus" resultType="com.cfpamf.ms.insur.admin.pojo.po.order.SmOrderGroupNotify">
        select *
        from sm_order_group_notify
        where group_policy_no = #{policyNo}
          <if test="endorsementNo!=null and endorsementNo!=''">
              and endorsement_no = #{endorsementNo}
          </if>
          and status = #{status}
          and enabled_flag = 0
        order by correct_timestamp ,id;
    </select>
    
    <select id="listHistoryByPolicyNo" resultType="com.cfpamf.ms.insur.admin.pojo.po.order.SmOrderGroupNotify">
        select * from sm_order_group_notify
        where group_policy_no = #{policyNo}
          <if test="correctTimestamp!=null and correctTimestamp>0">
              and correct_timestamp &lt; #{correctTimestamp}
          </if>
        order by correct_timestamp,id ;
    </select>

</mapper>