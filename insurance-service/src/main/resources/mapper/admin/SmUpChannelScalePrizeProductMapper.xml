<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.SmUpChannelScalePrizeProductMapper">


    <select id="queryProductListByConfigId" resultType="com.cfpamf.ms.insur.admin.pojo.vo.UpChannelScalePrizeProductVO">
        SELECT t2.product_id AS productId
             , t3.productName
             , t4.companyName
             , t4.id         AS companyId
             , t3.productCategoryId
        FROM sm_up_channel_scale_prize t1
                 LEFT JOIN sm_up_channel_scale_prize_product t2 ON t1.id = t2.channel_scale_prize_id
                 LEFT JOIN sm_product t3 ON t3.id = t2.product_id
                 LEFT JOIN sm_company t4 ON t3.companyId = t4.id
        WHERE t2.channel_scale_prize_id = #{id} AND t1.enabled_flag = 0
    </select>
</mapper>

