<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.AmImageMapper">

    <insert id="insertImage" useGeneratedKeys="true">
       INSERT INTO
         am_image (policyId,imageType,imageName,create_time, update_time)
        VALUES
        (#{policyId}, #{imageType}, #{imageName}, CURRENT_TIMESTAMP(), CURRENT_TIMESTAMP())
    </insert>

    <select id="listImagesByPolicyId" resultType="com.cfpamf.ms.insur.admin.pojo.vo.AmImageVO">
        SELECT * FROM am_image WHERE policyId = #{policyId}
    </select>
</mapper>