<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.SmProductVersionMapper">

    <select id="selectByProductId" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmProductVersionVO">

        select sp.create_time,
               au.userName create_by_user_name,
               sp.version,
               sp.product_id
        from sm_product_version sp
                 left join auth_user au on au.userId = sp.create_by and au.enabled_flag = 0
        where product_id = #{productId}
        order by sp.id desc
    </select>
    <select id="selectVersionBySubTime" resultType="java.lang.Integer">
        select version
        from sm_product_version
        where product_id = #{productId}
        <![CDATA[ and create_time < #{submitTime}  ]]>
        order by id desc
        limit 1
    </select>
</mapper>
