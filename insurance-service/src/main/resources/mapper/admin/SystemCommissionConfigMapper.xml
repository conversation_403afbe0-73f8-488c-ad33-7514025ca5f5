<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.commission.SystemCommissionConfigMapper">
    <select id="listCommissionConfig" resultType="com.cfpamf.ms.insur.admin.pojo.vo.commission.CommissionConfigVO">

        select scc.id,sc.id as company_id,sc.companyName,sp.id as productId,sp.productName,scc.start_time as startTime,scc.end_time as endTime,
        (case when scc.start_time > now() then 0  when now()> scc.end_time then 2 else 1 end) as status,
        au.userName as updateBy,scc.update_time as updateTime,scc.release_flag as releaseFlag
        from system_commission_config scc
        left join sm_product sp on scc.product_id = sp.id and sp.enabled_flag=0
        left join sm_company sc on scc.company_id = sc.id and sc.enabled_flag=0
        left join auth_user au on scc.update_by = au.userId and au.enabled_flag=0
        where scc.type = #{type} and scc.enabled_flag = 0
        <if test="companyId!=null">
            and sp.companyId = #{companyId}
        </if>

        <if test="productName!=null and productName != ''">
            and sp.productName like concat('%',#{productName},'%')
        </if>

        <if test="productId!=null">
            and sp.id = #{productId}
        </if>

        <choose>
            <when test="status != null and status ==0 ">
                and scc.start_time > now()
            </when>
            <when test="status != null and status ==1 ">
                <![CDATA[and scc.start_time <= now() and scc.end_time >= now()]]>
            </when>
            <when test="status != null and status ==2 ">
                <![CDATA[and scc.end_time < now() ]]>
            </when>
            <otherwise>

            </otherwise>
        </choose>

        order by scc.product_id desc,scc.update_time desc


    </select>

    <select id="selectRepeatTimeRecord" resultType="com.cfpamf.ms.insur.admin.pojo.po.commission.SystemCommissionConfig">

        SELECT * FROM system_commission_config
        WHERE product_id = #{productId} and enabled_flag=0 and release_flag = 1 and type=#{type}
        and
         <![CDATA[ start_time<= #{endTime} and end_time>=#{startTime} ]]>
        <!-- 修改当前记录需要排除 -->
        <if test="configId!=null">
            and id !=#{configId}
        </if>


    </select>

    <update id="updateEndTime20231231">
        insert into system_commission_config_log (type,product_id,product_name,config_id,content,end_time,status)
        select
            t1.type,product_id,t2.productName,t1.id config_id
            ,'修改生效中的折算配置结束时间为2023-12-31 23:59:59',end_time,0 status
        from system_commission_config t1
        left join sm_product t2 on t1.`product_id` = t2.id
        where
            type = 3 and t1.enabled_flag = 0
            <![CDATA[and start_time <= now() and end_time >= now()]]>;

        update system_commission_config set end_time = '2023-12-31 23:59:59'
        where type = 3 and enabled_flag = 0
        <![CDATA[ and start_time <= now() and end_time >= now()]]>;
    </update>
</mapper>
