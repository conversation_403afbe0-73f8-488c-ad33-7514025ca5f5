<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.SmPolicyCacheMapper">
    <insert id="insertOnDuplicate">
        insert into sm_policy_cache(order_id, policy_no, order_policy, source_url, oss_key)
        values (#{orderId}, #{policyNo}, #{orderPolicy}, #{sourceUrl}, #{ossKey})
        on duplicate key update source_url = #{sourceUrl},oss_key = #{ossKey}
    </insert>
</mapper>
