<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.SmTaskPremiumConfigMapper">
    <update id="batchUpdateEndTime">
        UPDATE
        sm_task_premium_config
        SET end_time = #{endTime}
        , update_time = now()
        WHERE id IN
        <foreach collection="configIdList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        AND enabled_flag = 0
    </update>

    <select id="queryCrossTimeConfigList" resultType="com.cfpamf.ms.insur.admin.pojo.vo.CrossTimelineProductVO">
        SELECT
        c.start_time as beforeStartTime
        , c.end_time as beforeEndTime
        , p.productName
        FROM sm_task_premium_config c
        LEFT JOIN sm_product p ON c.product_id = p.id
        WHERE c.product_id IN
        <foreach collection="productIdList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        AND ((c.start_time BETWEEN #{startTime} AND #{endTime} ) OR (c.end_time BETWEEN #{startTime} AND #{endTime}))
        AND c.enabled_flag = 0
    </select>
    <select id="queryTableList" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmTaskPremiumConfigVO">
        SELECT
        t1.id,
        t1.product_id productId,
        t2.productName,
        t3.id as companyId,
        t3.companyName,
        t1.premium_standard premiumStandard,
        t1.start_time startTime,
        t1.end_time endTime,
        t1.update_by updateBy,
        t4.userName updateByName,
        t1.update_time updateTime
        FROM
        sm_task_premium_config t1
        LEFT JOIN sm_product t2 ON t2.id = t1.product_id
        LEFT JOIN sm_company t3 ON t2.companyId = t3.id
        LEFT JOIN auth_user t4 ON t4.userId = t1.update_by AND t4.enabled_flag = 0
        <where>
            <if test="query.productName != null and query.productName != ''">
                and t2.productName like concat('%', #{query.productName}, '%')
            </if>
            <if test="query.companyId != null and query.companyId != ''">
                and t2.companyId = #{query.companyId}
            </if>
            <choose>
                <when test="query.state == 'invalid'">
                    and t1.start_time &gt; now()
                </when>
                <when test="query.state == 'validating'">
                    and t1.start_time &lt;= now() and now() &lt; t1.end_time
                </when>
                <when test="query.state == 'deadline'">
                    and now() &gt; t1.end_time
                </when>
                <otherwise>
                </otherwise>
            </choose>

            <if test="ids != null and ids.size() > 0">
                and t1.product_id in
                <foreach collection="ids" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            AND t1.enabled_flag = 0
            order by t1.product_id, t1.update_time desc
        </where>
    </select>

    <select id="queryConflictData" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmTaskPremiumConfigVO">
        SELECT
        c.start_time as beforeStartTime
        , c.end_time as beforeEndTime
        , p.productName
        FROM sm_task_premium_config c
        LEFT JOIN sm_product p ON c.product_id = p.id
        WHERE c.id IN
        <foreach collection="configIdList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        AND #{endTime} &lt;= c.start_time
        AND c.enabled_flag = 0
    </select>
    <select id="queryDetailById" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmTaskPremiumConfigVO">
        select c.id
             , p.id               as productId
             , p.productName
             , c.start_time       as startTime
             , c.premium_standard as premiumStandard
             , c.end_time         as endTime
        FROM sm_task_premium_config c
                 LEFT JOIN sm_product p ON c.product_id = p.id
        WHERE c.id = #{id}
          AND c.enabled_flag = 0
    </select>
    <select id="queryPremiumType" resultType="java.lang.String">
        select premium_standard as premiumStandard
        from sm_task_premium_config
        where product_id = #{productId}
          and #{orderTime} between start_time and end_time
    </select>
    <select id="queryPageByProduct" resultType="java.lang.Integer">
        SELECT product_id FROM(
        SELECT
        t1.product_id, MAX(t1.update_time) update_time
        FROM
        sm_task_premium_config t1
        LEFT JOIN sm_product t2 ON t2.id = t1.product_id
        LEFT JOIN sm_company t3 ON t2.companyId = t3.id
        LEFT JOIN auth_user t4 ON t4.userId = t1.update_by
        <where>
            <if test="query.productName != null and query.productName != ''">
                and t2.productName like concat('%', #{query.productName}, '%')
            </if>
            <if test="query.companyId != null and query.companyId != ''">
                and t2.companyId = #{query.companyId}
            </if>
            <choose>
                <when test="query.state == 'invalid'">
                    and t1.start_time &gt; now()
                </when>
                <when test="query.state == 'validating'">
                    and t1.start_time &lt;= now() and now() &lt; t1.end_time
                </when>
                <when test="query.state == 'deadline'">
                    and now() &gt; t1.end_time
                </when>
                <otherwise>
                </otherwise>
            </choose>
            AND t1.enabled_flag = 0
            group by t1.product_id
            ) temp order by update_time desc
        </where>
    </select>
    <select id="queryExistedPremiumConflict" resultType="java.lang.Integer">
        SELECT
        product_id
        FROM
        sm_task_premium_config
        WHERE
        product_id IN (
        SELECT
        product_id
        FROM
        sm_task_premium_config
        WHERE
        id IN
        <foreach collection="ids" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        AND enabled_flag = 0
        )
        AND id NOT IN
        <foreach collection="ids" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        AND enabled_flag = 0
        AND (start_time BETWEEN #{startTime} AND #{endTime} OR end_time BETWEEN #{startTime} AND #{endTime})
    </select>
    <select id="queryBatchUpdateConflict" resultType="java.lang.Integer">
        SELECT t1.product_id
        FROM sm_task_premium_config t1,
        (
        SELECT start_time as paramStartTime,
        product_id
        FROM sm_task_premium_config
        WHERE id IN
        <foreach collection="ids" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        ) d1
        WHERE d1.product_Id = t1.product_id
        AND id NOT IN
        <foreach collection="ids" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        AND (t1.start_time BETWEEN d1.paramStartTime AND #{endTime} OR
        t1.end_time BETWEEN d1.paramStartTime AND #{endTime})
        AND t1.enabled_flag = 0
    </select>
    <select id="queryBatchSameProductConflict" resultType="java.lang.Integer">
        SELECT product_id
        FROM sm_task_premium_config
        WHERE id IN
        <foreach collection="ids" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        GROUP BY product_id
        HAVING COUNT(product_id) > 1
    </select>
    <select id="queryByProductIds" resultType="com.cfpamf.ms.insur.admin.pojo.po.SmTaskPremiumConfig">
        SELECT *
        FROM sm_task_premium_config
        WHERE product_id IN
        <foreach collection="list" open="(" close=")" item="item" separator=",">
            #{item}
        </foreach>
        AND enabled_flag = 0
    </select>

</mapper>

