<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.CustomerBaseInfoMapper">
    <insert id="batchInsertCustomerBaseInfo" useGeneratedKeys="true" parameterType="java.util.List">
        insert ignore into customer_base_info(insur_cust_no,customer_name,id_type,id_number)
        values
        <foreach collection="list" item="customer" separator=",">
            (concat(date_format(now(),'%Y%m%d%H%i%s'),left(#{customer.idNumber},2),right(#{customer.idNumber},4),substring(md5(rand()),1,10))
            ,#{customer.customerName},#{customer.idType},#{customer.idNumber})
        </foreach>
    </insert>
    <insert id="batchExtractCustInfoFromOrderInsuredByCreateTime">
        insert ignore into customer_base_info(insur_cust_no,customer_name,id_type,id_number)
        select concat(date_format(now(),'%Y%m%d%H%i%s'),left(idNumber,2),right(idNumber,4),substring(md5(rand()),1,10))
        ,personName,idType,idNumber
        from sm_order_insured
        where create_time >= #{beginTime} and #{endTime} > create_time
        and idNumber is not null
    </insert>
    <insert id="batchExtractCustInfoFromOrderApplicantByCreateTime">
        insert ignore into customer_base_info(insur_cust_no,customer_name,id_type,id_number)
        select concat(date_format(now(),'%Y%m%d%H%i%s'),left(idNumber,2),right(idNumber,4),substring(md5(rand()),1,10))
        ,personName,idType,idNumber
        from sm_order_applicant
        where create_time >= #{beginTime} and #{endTime} > create_time
        and idNumber is not null
    </insert>
</mapper>