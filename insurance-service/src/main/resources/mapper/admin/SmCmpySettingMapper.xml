<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.SmCmpySettingMapper">

    <insert id="insertCompanySetting" useGeneratedKeys="true">
        INSERT INTO sm_company_setting (companyId,fieldCode,fhProductId,optionCode,optionName,sorting, enabled_flag,commonCode,channel)
         VALUES (#{companyId}, #{fieldCode}, #{fhProductId}, #{optionCode}, #{optionName}, #{sorting}, 0,#{commonCode},#{channel})
    </insert>

    <insert id="insertCompanySettings" useGeneratedKeys="true">
        INSERT INTO sm_company_setting
        (companyId,fieldCode,fhProductId,optionCode,optionName,sorting, enabled_flag,commonCode,channel)
        VALUES
        <foreach collection="dtos" item="item" index="index" separator=",">
            (#{item.companyId}, #{item.fieldCode}, #{item.fhProductId}, #{item.optionCode}, #{item.optionName},
            #{item.sorting}, 0,#{item.commonCode},#{item.channel})
        </foreach>
    </insert>

    <update id="updateCompanySetting">
        UPDATE sm_company_setting SET companyId=#{companyId},fieldCode=#{fieldCode},fhProductId=#{fhProductId}, optionCode=#{optionCode},optionName=#{optionName},sorting=#{sorting},enabled_flag=#{deleteFlag},
                                      commonCode = #{commonCode},channel = #{channel}
                                      WHERE id=#{id}
    </update>

    <select id="listCompanySettings" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmCompanySettingVO">
        SELECT t1.*, t2.companyName AS companyName, t2.companyIdentifier AS companyCode,t3.name AS fieldName FROM
        sm_company_setting t1
        LEFT JOIN sm_company t2 ON t1.companyId = t2.id
        LEFT JOIN dictionary t3 ON t1.fieldCode = t3.code AND t3.type='orderField' AND t3.enabled_flag = 0
        WHERE t1.enabled_flag=0
        <if test='query.keyword != null'>
            AND ( t2.companyName LIKE CONCAT('%',#{query.keyword},'%') OR t2.companyAbbre LIKE
            CONCAT('%',#{query.keyword},'%') OR t2.companyIdentifier LIKE CONCAT('%',#{query.keyword},'%')
            OR t3.name LIKE CONCAT('%',#{query.keyword},'%') OR t1.optionCode LIKE CONCAT('%',#{query.keyword},'%')
            OR t1.optionName LIKE CONCAT('%',#{query.keyword},'%') OR t1.fhProductId LIKE
            CONCAT('%',#{query.keyword},'%'))
        </if>
          <!-- 如果带id查询一定要传渠道 -->
        <if test='query.companyId != null'>
            AND companyId=#{query.companyId}
        </if>
          <if test="query.channel != null">
              and channel = #{query.channel}
          </if>

        <if test='query.fhProductId != null'>
            AND fhProductId=#{query.fhProductId}
        </if>
        <if test='query.fieldCode != null'>
            AND fieldCode=#{query.fieldCode}
        </if>
        ORDER BY companyId DESC, fieldCode DESC, fhProductId DESC
    </select>

    <select id="selectList" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmCompanySettingVO">
        select t1.optionCode, t1.optionName,t1.commonCode,t5.option_name settingName,t2.companyName AS companyName, t2.companyIdentifier AS companyCode,
               t3.name AS fieldName,t4.name as channelName
        from sm_company_setting t1
                 LEFT JOIN sm_company t2 ON t1.companyId = t2.id and t2.enabled_flag = 0
                 LEFT JOIN dictionary t3 ON t1.fieldCode = t3.code AND t3.type='orderField' AND t3.enabled_flag = 0
                 left join dictionary t4 on t1.channel = t4.code and t4.type = 'channel' AND t4.enabled_flag = 0
                 left join sm_common_setting t5 on t5.option_code = t1.commonCode and t5.field_code = t1.fieldCode and t5.enabled_flag = 0
        where t1.enabled_flag = 0
    </select>

    <select id="listWxCompanySettings" resultType="com.cfpamf.ms.insur.weixin.pojo.vo.WxFormFieldOptionVO">
        SELECT t1.optionCode, t1.optionName,t1.fieldCode FROM sm_company_setting t1
        WHERE t1.enabled_flag=0
        <if test='query.companyId != null'>
            AND t1.companyId=#{query.companyId}
        </if>
          <if test="query.channel!=null">
              AND t1.channel=#{query.channel}
          </if>
        <if test='query.fhProductId != null'>
            AND t1.fhProductId=#{query.fhProductId}
        </if>
        <if test='query.fhProductId == null'>
            AND t1.fhProductId IS NULL
        </if>
        <if test='query.fieldCode != null'>
            AND t1.fieldCode=#{query.fieldCode}
        </if>
        ORDER BY t1.sorting ASC
    </select>

    <select id="countCompanySetting" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM sm_company_setting WHERE companyId=#{companyId} AND enabled_flag=0
    </select>

    <update id="deleteCompanySetting">
        UPDATE sm_company_setting SET  enabled_flag=1  WHERE id=#{id};
    </update>
</mapper>
