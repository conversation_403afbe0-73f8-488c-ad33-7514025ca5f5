<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.product.SmProductAttrMapper">
    <insert id="insertHisList">
        insert into sm_product_attr_history(version, product_id, attr_code, attr_val)
        values
        <foreach collection="addList" item="item" separator=",">
            (#{version},#{item.productId},#{item.attrCode},#{item.attrVal})
        </foreach>
    </insert>
    <delete id="deleteHistory">
        delete
        from sm_product_attr_history
        where product_id = #{productId}
          and version = #{version}
    </delete>
    <delete id="deleteByProduct">
        delete from sm_product_attr where product_id = #{productId}
    </delete>


</mapper>
