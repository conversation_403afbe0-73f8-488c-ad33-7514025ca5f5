<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.admin.renewal.dao.SmOrderRenewalTermMapper">

    <update id="batchUpdate">
        update sm_order_renewal_term
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="renewal_policy_no =case" suffix="end,">
                <foreach collection="renewalTermDTOList" item="item" index="index">
                    when policy_no=#{item.policyNo} and term_num = #{item.termNum} then #{item.renewalPolicyNo}
                </foreach>
            </trim>

            <trim prefix="renewal_amount =case" suffix="end,">
                <foreach collection="renewalTermDTOList" item="item" index="index">
                    when policy_no=#{item.policyNo} and term_num = #{item.termNum} then #{item.renewalAmount}

                </foreach>
            </trim>

            <trim prefix="due_time =case" suffix="end,">
                <foreach collection="renewalTermDTOList" item="item" index="index">
                    when policy_no=#{item.policyNo} and term_num = #{item.termNum} then #{item.dueTime}
                </foreach>
            </trim>

            <trim prefix="grace_days =case" suffix="end,">
                <foreach collection="renewalTermDTOList" item="item" index="index">
                    when policy_no=#{item.policyNo} and term_num = #{item.termNum} then #{item.graceDays}
                </foreach>
            </trim>

            <trim prefix="payment_time =case" suffix="end,">
                <foreach collection="renewalTermDTOList" item="item" index="index">
                    when policy_no=#{item.policyNo} and term_num = #{item.termNum} then #{item.paymentTime}
                </foreach>
            </trim>

            <trim prefix="payment_way =case" suffix="end,">
                <foreach collection="renewalTermDTOList" item="item" index="index">
                    when policy_no=#{item.policyNo} and term_num = #{item.termNum} then #{item.paymentWay}
                </foreach>
            </trim>

            <trim prefix="renewal_status =case" suffix="end,">
                <foreach collection="renewalTermDTOList" item="item" index="index">
                    when policy_no=#{item.policyNo} and term_num = #{item.termNum} then #{item.renewalStatus}
                </foreach>
            </trim>

        </trim>
        where policy_no in
        <foreach collection="renewalTermDTOList" index="index" item="item" separator="," open="(" close=")">
            #{item.policyNo}
        </foreach>
    </update>


    <select id="getRenewalTermOrderSimpleDTO"
            resultType="com.cfpamf.ms.insur.admin.pojo.dto.renewal.RenewalTermOrderSimpleDTO">
        select t2.totalAmount as orderAmount,
        t2.customerAdminId as customerAdminId ,
        t2.fhOrderId as orderId,
        t1.policyNo as policyNo,
        t2.channel,
        t2.startTime,t2.endTime
        from sm_order_insured t1
        LEFT JOIN sm_order t2 on t1.fhOrderId = t2.fhOrderId
        WHERE t1.policyNo in
        <foreach collection="policyNoList" index="index" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        <if test="channel!= null and channel !=''">
            and t2.channel = #{channel}
        </if>

    </select>

    <select id="getSmRenewalTermRecordVoByOrderId"
            resultType="com.cfpamf.ms.insur.admin.pojo.vo.renewal.SmRenewalTermRecordVo">
        select renewal_amount,
               term_num,
               total_term,
               due_time,
               grace_days,
               payment_way,
               renewal_status,
               reinstate,
               payment_time,
               policy_no,
               bank_card
        from sm_order_renewal_term
        where order_id = #{orderId}
          and enabled_flag = 0
        order by due_time desc
    </select>
    <sql id="renewalTermQuery">
        select
            t1.id as id,
            t5.id as insuredSn,
            t1.order_id as orderId,
            t3.productName as productName,
            t1.policy_no as policyNo,
            t4.personName as applicantPersonName,
            t5.personName as insuredPersonName,
            t1.renewal_amount as renewalAmount,
            t1.term_num as termNum,
            t1.total_term as totalTerm,
            t1.payment_time as paymentTime,
            t1.due_time as dueTime,
            t1.renewal_status as renewalStatus,
            t1.share_flag as shareFlag,
            t1.grace_days as graceDays,
            t1.customer_admin_id as customerAdminId,
            t2.totalAmount as rawOrderAmount,
            t6.userName as customerAdminName,
            t6.regionName as customerAdminRegionName,
            t6.organizationName as customerAdminOrgName,
            t8.result intention,
            t8.contact_way as serviceMode,
            t14.label_value as selfInsured,
            t4.idNumber as applicantIdNumber,
            t5.idNumber AS insuredIdNumber
        from sm_order_renewal_term t1
        left join sm_order t2 on t2.fhOrderId = t1.order_id
        left join sm_product t3 on t2.productId = t3.id
        LEFT JOIN sm_order_applicant t4 ON t2.fhOrderId = t4.fhOrderId
        LEFT JOIN sm_order_insured t5 ON t2.fhOrderId = t5.fhOrderId
        LEFT JOIN auth_user t6 ON t2.customerAdminId = t6.userId AND t6.enabled_flag = 0
        LEFT JOIN sm_order_renewal_term_notify_log t8 on t8.policy_no = t1.policy_no and t8.term_num = t1.term_num and newest = 1
        left join sm_order_label t14 on t14.fh_order_id = t1.order_id  and t14.label_type='self_insurance'
    </sql>

    <select id="searchRenewalTerm" resultType="com.cfpamf.ms.insur.admin.pojo.vo.renewal.SmRenewalTermVo">
        <include refid="renewalTermQuery"></include>
        where t1.term_num >= 2 and t1.enabled_flag=0
        <if test="renewalStatus!=null">
            AND t1.renewal_status = #{renewalStatus}
        </if>
        <if test="renewalStatus == 0">
            <if test="dueTimeStartTime != null ">
                AND t1.due_time >= #{dueTimeStartTime}
            </if>
            <if test="dueTimeEndTime != null">
                <![CDATA[ AND  t1.due_time <= #{dueTimeEndTime} ]]>
            </if>
        </if>
        <if test="renewalStatus == 1">
            <if test="dueTimeStartTime != null ">
                AND t1.due_time >= #{dueTimeStartTime}
            </if>
            <if test="dueTimeEndTime != null">
                <![CDATA[ AND  t1.due_time <= #{dueTimeEndTime} ]]>
            </if>
            <if test="payTimeStartTime != null ">
                AND t1.payment_time >= #{payTimeStartTime}
            </if>
            <if test="payTimeEndTime != null">
                <![CDATA[ AND  t1.payment_time <= #{payTimeEndTime} ]]>
            </if>
        </if>
        <if test="renewalStatus == 2">
            <if test="dueTimeStartTime != null ">
                AND adddate(t1.due_time,INTERVAL ifnull(grace_days,0) DAY) >= #{dueTimeStartTime}
            </if>
            <if test="dueTimeEndTime != null">
                <![CDATA[ AND  adddate(t1.due_time,INTERVAL ifnull(grace_days,0) DAY) <= #{dueTimeEndTime} ]]>
            </if>
        </if>
        <if test='productId != null'>
            AND t3.id = #{productId}
        </if>
        <if test='policyNo != null'>
            AND t1.policy_no LIKE CONCAT(#{policyNo},'%')
        </if>
        <if test='customerAdminId != null'>
            AND t2.customerAdminId=#{customerAdminId}
        </if>
        <if test='userId != null'>
            AND t2.customerAdminId=#{userId}
        </if>
        <if test='applicantPersonName != null'>
            AND t4.personName LIKE CONCAT(#{applicantPersonName},'%')
        </if>
        <if test='insuredPersonName != null'>
            AND t5.personName LIKE CONCAT(#{insuredPersonName},'%')
        </if>
        <if test='regionCode != null'>
            AND t6.regionCode=#{regionCode}
        </if>
        <if test='organizationCode != null'>
            AND t6.orgCode=#{organizationCode}
        </if>
        <if test='orgCode != null'>
            AND t6.orgCode=#{orgCode}
        </if>
        <if test='orgPath !=null '>
            and t6.orgCode in (select orgCode from organization where orgPath like CONCAT(#{orgPath},'%'))
        </if>
        <if test='intention != null'>
            <choose>
                <when test="intention == 99">
                    and t8.id is null
                </when>
                <when test = 'intention == 4'>
                    and t8.contact_way = #{intention}
                </when>
                <otherwise>
                    and t8.result = #{intention} and t8.contact_way!=4
                </otherwise>
            </choose>
        </if>
        order by t1.due_time asc
    </select>

    <select id="searchBySmRenewalTermQuery" resultType="com.cfpamf.ms.insur.admin.pojo.vo.renewal.SmRenewalTermVo">
        <include refid="renewalTermQuery"></include>
        where 1=1
        <if test="renewalStatus!=null">
            AND t1.renewal_status = #{renewalStatus}
        </if>
        <if test="dueTimeStartTime != null">
            AND t1.due_time >= #{dueTimeStartTime}
        </if>
        <if test="dueTimeEndTime != null">
            <![CDATA[ AND  t1.due_time <= #{dueTimeEndTime} ]]>
        </if>
        <if test='productId != null'>
            AND t3.id = #{productId}
        </if>
        <if test='policyNo != null'>
            AND t1.policy_no LIKE CONCAT(#{policyNo},'%')
        </if>
        <if test='customerAdminId != null'>
            AND t2.customerAdminId=#{customerAdminId}
        </if>
        <if test='applicantPersonName != null'>
            AND t4.personName LIKE CONCAT(#{applicantPersonName},'%')
        </if>
        <if test='insuredPersonName != null'>
            AND t5.personName LIKE CONCAT(#{insuredPersonName},'%')
        </if>
        <if test='regionCode != null'>
            AND t6.regionCode=#{regionCode}
        </if>
        <if test='organizationCode != null'>
            AND t6.orgCode=#{organizationCode}
        </if>
        and t1.term_num >= 2 and t1.enabled_flag=0
        order by t1.due_time asc
    </select>

    <select id="searchByWxRenewalTermQuery"
            resultType="com.cfpamf.ms.insur.admin.pojo.vo.renewal.SmRenewalTermVo">
        select
        t1.id as id,
        t1.order_id as orderId,
        t3.productName as productName,
        t1.policy_no as policyNo,
        t4.personName as applicantPersonName,
        t5.personName as insuredPersonName,
        t1.renewal_amount as renewalAmount,
        t1.term_num as termNum,
        t1.total_term as totalTerm,
        t1.payment_time as paymentTime,
        t1.due_time as dueTime,
        t1.renewal_status as renewalStatus,
        t1.share_flag as shareFlag,
        t1.grace_days as graceDays,
        t6.userName as customerAdminName,
        t8.result intention,
        t8.contact_way as serviceMode
        from sm_order_renewal_term t1
        left join sm_order t2 on t2.fhOrderId = t1.order_id
        left join sm_product t3 on t2.productId = t3.id
        LEFT JOIN sm_order_applicant t4 ON t2.fhOrderId = t4.fhOrderId
        LEFT JOIN sm_order_insured t5 ON t2.fhOrderId = t5.fhOrderId
        LEFT JOIN auth_user t6 ON t2.customerAdminId = t6.userId AND t6.enabled_flag = 0
        LEFT JOIN sm_order_renewal_term_notify_log t8 on t8.policy_no = t1.policy_no and t8.term_num = t1.term_num and newest = 1
        where 1=1 and t1.enabled_flag=0
        <if test="renewalStatus!=null">
            AND t1.renewal_status = #{renewalStatus}
        </if>
        <if test="dueTimeStartTime != null">
            AND t1.due_time >= #{dueTimeStartTime}
        </if>
        <if test="dueTimeEndTime != null">
            <![CDATA[ AND  t1.due_time <= #{dueTimeEndTime} ]]>
        </if>
        <if test='keyword != null and keyword != ""'>
            AND ( t5.personName LIKE CONCAT(#{keyword},'%')
            OR t4.personName LIKE CONCAT(#{keyword},'%')
            OR t1.policy_no LIKE CONCAT(#{keyword},'%')
            OR t3.productName LIKE CONCAT(#{keyword},'%') )
        </if>
        <if test='customerAdminId != null'>
            AND t2.customerAdminId=#{customerAdminId}
        </if>
        <if test='intention != null'>
            <choose>
                <when test="intention == 99">
                    and t8.id is null
                </when>
                <when test = "intention == 4">
                    and t8.contact_way = #{intention}
                </when>
                <otherwise>
                    and t8.result = #{intention} and t8.contact_way!=4
                </otherwise>
            </choose>
        </if>
        <choose>
            <when test=" timeSortType=='DESC'">
                ORDER BY t1.due_time DESC
            </when>
            <when test="timeSortType=='ASC'">
                ORDER BY t1.due_time ASC
            </when>
            <when test=" renewalAmountSortType == 'DESC'">
                ORDER BY t2.totalAmount DESC
            </when>
            <when test="renewalAmountSortType == 'ASC'">
                ORDER BY t2.totalAmount ASC
            </when>
            <otherwise>
                order by t1.due_time desc
            </otherwise>
        </choose>
    </select>

    <select id="searchCountByCustomer"
            resultType="java.lang.Integer">
        select
        count(t1.id)
        from sm_order_renewal_term t1
        left join sm_order t2 on t2.fhOrderId = t1.order_id
        left join sm_product t3 on t2.productId = t3.id
        LEFT JOIN sm_order_applicant t4 ON t2.fhOrderId = t4.fhOrderId
        LEFT JOIN sm_order_insured t5 ON t2.fhOrderId = t5.fhOrderId
        LEFT JOIN auth_user t6 ON t2.customerAdminId = t6.userId AND t6.enabled_flag = 0
        where 1=1 and t1.enabled_flag=0
        <if test="renewalStatus!=null">
            AND t1.renewal_status = #{renewalStatus}
        </if>
        <if test='customerAdminId != null'>
            AND t2.customerAdminId=#{customerAdminId}
        </if>
    </select>

    <select id="calculationStatistics"
            resultType="com.cfpamf.ms.insur.admin.pojo.vo.renewal.RenewalTermStatisticsVo">
        select
        sum(case renewal_status when 0 then 1 else 0 end) as uncollected,
        sum(case renewal_status when 1 then 1 else 0 end) as received,
        sum(case renewal_status when 2 then 1 else 0 end) as invalid
        from sm_order_renewal_term t1
        left join sm_order t2 on t2.fhOrderId = t1.order_id
        left join sm_product t3 on t2.productId = t3.id
        LEFT JOIN sm_order_applicant t4 ON t2.fhOrderId = t4.fhOrderId
        LEFT JOIN sm_order_insured t5 ON t2.fhOrderId = t5.fhOrderId
        where 1=1 and t1.enabled_flag=0
        <if test="dueTimeStartTime != null">
            AND t1.due_time >= #{dueTimeStartTime}
        </if>
        <if test="dueTimeEndTime != null">
            <![CDATA[ AND  t1.due_time <= #{dueTimeEndTime} ]]>
        </if>
        <if test='keyword != null and keyword !=""'>
            AND ( t5.personName LIKE CONCAT(#{keyword},'%')
            OR t4.personName LIKE CONCAT(#{keyword},'%')
            OR t1.policy_no LIKE CONCAT(#{keyword},'%')
            OR t3.productName LIKE CONCAT(#{keyword},'%') )
        </if>
        <if test='customerAdminId != null'>
            AND t2.customerAdminId=#{customerAdminId}
        </if>
    </select>


    <update id="updateShareFlag">
        update sm_order_renewal_term
        set share_flag = #{shareFlag}
        where id = #{id}
          and enabled_flag = 0
    </update>


    <update id="logicDelete">
        update sm_order_renewal_term
        set enabled_flag = 1,update_time=CURRENT_TIMESTAMP()
        where policy_no = #{policyNo}
          and term_num = #{termNum}
          and renewal_status=0
          and enabled_flag = 0
    </update>

    <update id="batchReplaceInto">
        insert into
            sm_order_renewal_term (order_id,
            policy_no,
            channel,
            order_amount,
            term_num,
            total_term,
            renewal_policy_no,
            renewal_amount,
            due_time,
            grace_days,
            payment_time,
            payment_way,
            renewal_status,
            customer_admin_id,
            update_time,
            bank_card,
            renewal_success_sync_date
        ) values
        <foreach collection="renewalTermDTOList" item="item" index="index" separator=",">
            (#{item.orderId}, #{item.policyNo}, #{item.channel}, #{item.orderAmount}, #{item.termNum},
            #{item.totalTerm}, #{item.renewalPolicyNo}, #{item.renewalAmount},
            #{item.dueTime},#{item.graceDays},#{item.paymentTime},#{item.paymentWay},
            #{item.renewalStatus},#{item.customerAdminId},
            CURRENT_TIMESTAMP(),#{item.bankCard},#{item.renewalSuccessSyncDate})
        </foreach>
        ON DUPLICATE KEY UPDATE
<!--        order_id = values(order_id),-->
<!--        policy_no = values(policy_no),-->
<!--        channel = values(channel),-->
        term_num = values(term_num),
        total_term = values(total_term),
        order_amount = values(order_amount),
        renewal_policy_no = values(renewal_policy_no),
        renewal_amount = values(renewal_amount),
        due_time = values(due_time),
        grace_days = values(grace_days),
        payment_time = values(payment_time),
        payment_way = values(payment_way),
        renewal_status = values(renewal_status),
        customer_admin_id = values(customer_admin_id),
        update_time = values(update_time),
        bank_card = values(bank_card),
        renewal_success_sync_date = values(renewal_success_sync_date)
    </update>

    <select id="getRenewalTermWxNotifyDomain"
            resultType="com.cfpamf.ms.insur.admin.service.renewalterm.RenewalTermWaitPayWxNotifyDomain">
        select
        t1.id as renewalTermId,
        t2.endTime as policyEndTime,
        t3.productName as productName,
        t1.policy_no as policyNo,
        t4.personName as applicantPersonName,
        t1.renewal_amount as renewalAmount,
        t2.totalAmount as estimateRenewalAmount,
        DATEDIFF(DATE_ADD(t2.startTime, INTERVAL (t1.term_num - 1) year),now()) as wholeYearBeforeDay,
        t1.term_num as wholeYear,
        t1.due_time as dueTime,
        t6.wxOpenId
        from sm_order_renewal_term t1
        left join sm_order t2 on t2.fhOrderId = t1.order_id
        left join sm_product t3 on t2.productId = t3.id
        LEFT JOIN sm_order_applicant t4 ON t2.fhOrderId = t4.fhOrderId
        LEFT JOIN auth_user t6 ON t2.customerAdminId = t6.userId AND t6.enabled_flag = 0
        where t1.renewal_status = #{renewalStatus} and t1.enabled_flag = 0 and
        DATEDIFF(t1.due_time,now()) in
        <foreach collection="waitBeforeDayList" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
        <![CDATA[ AND t1.wait_payment_notice_date < CURDATE() ]]>
    </select>

    <select id="selectCountByPolicyAndStatus" resultType="java.lang.Integer">
        select count(1)
        from sm_order_renewal_term where policy_no = #{policyNo} and enabled_flag = 0
        <if test="renewalStatus!= null">
            and renewal_status = #{renewalStatus}
        </if>
    </select>
    <select id="getRenewalTermPaySuccessWxNotifyDomain"
            resultType="com.cfpamf.ms.insur.admin.service.renewalterm.RenewalTermPaySuccessWxNotifyDomain">
        select t1.id             as renewalTermId,
               t2.fhOrderId,
               t3.productName    as productName,
               t1.policy_no      as policyNo,
               t4.personName     as applicantPersonName,
               t1.payment_time   as paymentTime,
               t1.renewal_amount as paymentAmount,
               t6.wxOpenId
        from sm_order_renewal_term t1
                 left join sm_order t2 on t2.fhOrderId = t1.order_id
                 left join sm_product t3 on t2.productId = t3.id
                 LEFT JOIN sm_order_applicant t4 ON t2.fhOrderId = t4.fhOrderId
                 LEFT JOIN auth_user t6 ON t2.customerAdminId = t6.userId AND t6.enabled_flag = 0
        where t1.renewal_status = 1
          and t1.term_num >= 2
          and t1.enabled_flag = 0
          and payment_success_notice_flag = 0;
    </select>

    <update id="updatePaySuccessNoticeFlag">
        update sm_order_renewal_term
        set payment_success_notice_flag = #{noticeFlag}
        where id = #{id}
    </update>
    <update id="updateWaitPaymentNoticeDate">
        update sm_order_renewal_term
        set wait_payment_notice_date = CURDATE()
        where id = #{id}
    </update>
    <update id="updateRenewalTermOverStatus">
        UPDATE sm_order_renewal_term
        SET renewal_status = 2
        WHERE renewal_status = 0
          and now() > DATE_ADD(
                due_time,
                INTERVAL ifnull( grace_days, #{defaultGraceDays}) DAY)
    </update>
    <update id="batchUpdateReinstate" parameterType="java.util.List">
        <foreach collection="successRenewalTermList" item="item" index="index" open="" close="" separator=";">
            UPDATE sm_order_renewal_term
            SET reinstate = 1 ,
            renewal_status = 1 ,
            payment_time = #{item.paymentTime}
            WHERE renewal_status = 0
            and policy_no = #{item.policyNo}
            <![CDATA[ AND  term_num < #{item.termNum} ]]>
        </foreach>
    </update>
    <update id="updateWaitRenewalCustomerAdminId">
        UPDATE sm_order_renewal_term
        set customer_admin_id = #{newValue}
        WHERE policy_no = #{policyNo}
          and customer_admin_id = #{oldValue}
    </update>


    <select id="getSmOrderRenewalTerm" resultType="com.cfpamf.ms.insur.admin.renewal.entity.SmOrderRenewalTerm">
        select * from sm_order_renewal_term
        where policy_no in
        <foreach collection="policyNoList" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
        and enabled_flag = 0
    </select>
    <select id="getSmOrderRenewalTermByPolicyNoAndTermNum"
            resultType="com.cfpamf.ms.insur.admin.renewal.entity.SmOrderRenewalTerm">
        select *
        from sm_order_renewal_term
        where policy_no = #{policyNo}
          and term_num = #{termNum}
          and enabled_flag = 0
    </select>

    <update id="updateHistoryData">
        update sm_order_renewal_term
        set renewal_success_sync_date = payment_time
        WHERE renewal_status = 1
          and renewal_success_sync_date is null;
    </update>
    <select id="queryRenewalTermByPolicys"
            resultType="com.cfpamf.ms.insur.admin.renewal.entity.FastRenewalTermOrder">
        select
            a.order_id,
            a.policy_no,
            a.channel,
            a.term_num,
            a.total_term,
            a.order_amount,
            a.renewal_amount,
            a.due_time,
            a.grace_days,
            a.payment_time,
            a.renewal_status,
            a.reinstate,
            a.bank_card,
            a.customer_admin_id,
            p.policy_state
        from sm_order_renewal_term a
        left join sm_order_policy p on a.policy_no=p.policy_no and a.channel=p.channel
        where a.enabled_flag = 0
        and a.policy_no in
        <foreach collection="policyNos" open="(" close=")" separator="," item="no" >
            #{no}
        </foreach>
    </select>

    <!-- 查询没有险种层的续期数据 -->
    <select id="listRenewalTermByNoProduct"
            resultType="com.cfpamf.ms.insur.admin.renewal.entity.SmOrderRenewalTerm">
        select
            a.*
        from sm_order_renewal_term a
        where a.enabled_flag = 0
        and   a.renewal_status != 1
        and   not exists(select 1 from sm_order_renewal_term_risk b where a.order_id=b.order_id and a.term_num=b.term_num)
        and   exists(select 1 from sm_order_risk_duty c where a.order_id=c.fh_order_id and c.app_status='1' and c.enabled_flag=0)
        <if test="policyNoList!=null and policyNoList.size>0">
            and  a.policy_no in
            <foreach collection="policyNoList" open="(" close=")" separator="," item="no" >
                #{no}
            </foreach>
        </if>
        limit #{offSet},#{pageSize}
    </select>


    <update id="updateCustomerAdminIdByOrderIdAndTermNum">
        update sm_order_renewal_term
        set customer_admin_id = #{customerAdminId}
        WHERE  order_id= #{orderId}
          and term_num = #{termNum};
    </update>

    <select id="queryByPolicyList"
            resultType="com.cfpamf.ms.insur.admin.renewal.entity.FastRenewalTermOrder">
        SELECT *
        FROM sm_order_renewal_term
        WHERE enabled_flag = 0
        AND policy_no in
        <foreach collection="data" separator="," open="(" close=")" item="policyNo">
            #{policyNo}
        </foreach>
    </select>
    <select id="queryPolicyProvideWhaleDataByEndorId"
            resultType="com.cfpamf.ms.insur.admin.pojo.vo.third.RenewalTermManagerProviderVO">
        SELECT `term_num` as term, `policy_no` as policyNo, `customer_admin_id` as customerAdminId
        FROM `sm_order_renewal_term`  where enabled_flag = 0 and  policy_no in
        <foreach collection="list" separator="," item="item" open="(" close=")">
            #{item}
        </foreach>

        limit 3000

    </select>

    <insert id="batchInsertRenewalTermList">
        insert into sm_order_renewal_term (
            order_id,
            policy_no,
            channel,
            order_amount,
            term_num,
            total_term,
            renewal_policy_no,
            renewal_amount,
            due_time,
            grace_days,
            payment_time,
            payment_way,
            renewal_status,
            customer_admin_id,
            create_by,
            create_time,
            update_by,
            update_time,
            bank_card,
            renewal_success_sync_date
        ) values
        <foreach collection="data" item="item" index="index" separator=",">
            (
                #{item.orderId},
                #{item.policyNo},
                #{item.channel},
                #{item.orderAmount},
                #{item.termNum},
                #{item.totalTerm},
                #{item.renewalPolicyNo},
                #{item.renewalAmount},
                #{item.dueTime},
                #{item.graceDays},
                #{item.paymentTime},
                #{item.paymentWay},
                #{item.renewalStatus},
                #{item.customerAdminId},
                #{item.createBy},
                CURRENT_TIMESTAMP(),
                #{item.updateBy},
                CURRENT_TIMESTAMP(),
                #{item.bankCard},
                #{item.renewalSuccessSyncDate}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
        renewal_amount = values(renewal_amount),
        customer_admin_id = values(customer_admin_id),
        payment_time = values(payment_time),
        renewal_status = values(renewal_status),

        update_time = CURRENT_TIMESTAMP(),
        renewal_success_sync_date = values(renewal_success_sync_date)
    </insert>

</mapper>