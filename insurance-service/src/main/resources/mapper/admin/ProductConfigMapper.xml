<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.product.ProductConfigMapper">

    <select id="queryList" resultType="com.cfpamf.ms.insur.admin.pojo.po.product.SmProductConfig">
        select
        *
        from sm_product_config t
        where t.enabled_flag=0
        <if test=" colony != null and colony != '' ">
            and colony=#{colony}
        </if>

        <if test=" productIdList != null ">
            and product_id in
            <foreach collection="productIdList" close=")" open="(" item="id" separator=",">
                #{id}
            </foreach>
        </if>
    </select>

</mapper>