<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.reconciliation.SmReconciliationPolicyCacheMapper">
    <resultMap id="BaseResultMap" type="com.cfpamf.ms.insur.admin.pojo.po.reconciliation.SmReconciliationPolicyCache">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="policy_no" jdbcType="VARCHAR" property="policyNo"/>
        <result column="app_status" jdbcType="TINYINT" property="appStatus"/>
        <result column="reconciliation_status" jdbcType="TINYINT" property="reconciliationStatus"/>
        <result column="enabled_flag" jdbcType="TINYINT" property="enabledFlag"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <insert id="insertOrUpdateData">
        insert sm_reconciliation_policy_cache(policy_no,app_status,reconciliation_status)
        select policy_no,app_status,reconciliation_status from sm_reconciliation_policy t
        where exists (
            select 1 from (select max(id) as max_id from sm_reconciliation_policy group by policy_no,app_status) m
            where t.id=m.max_id
        ) and date_format(create_time,'%Y-%m-%d')= date_format(now(),'%Y-%m-%d')
        ON DUPLICATE KEY UPDATE
        policy_no = values(policy_no),app_status=VALUES(app_status),reconciliation_status=VALUES(reconciliation_status);
    </insert>

</mapper>