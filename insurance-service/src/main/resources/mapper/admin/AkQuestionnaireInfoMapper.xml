<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.aicheck.AkQuestionnaireInfoMapper">
    <resultMap id="BaseResultMap" type="com.cfpamf.ms.insur.admin.pojo.po.aicheck.AkQuestionnaireInfo">
        <id column="id" jdbcType="INTEGER" property="id" />
        <result column="questionnaire_code" jdbcType="VARCHAR" property="questionnaireCode" />
        <result column="node_code" jdbcType="VARCHAR" property="nodeCode" />
        <result column="node_name" jdbcType="VARCHAR" property="nodeName" />
        <result column="node_type" jdbcType="VARCHAR" property="nodeType" />
        <result column="node_code_path" jdbcType="VARCHAR" property="nodeCodePath" />
        <result column="node_remark" jdbcType="VARCHAR" property="nodeRemark" />
        <result column="p_node_code_path" jdbcType="VARCHAR" property="pNodeCodePath" />
        <result column="leaf_flag" jdbcType="INTEGER" property="leafFlag" />
        <result column="create_by" jdbcType="VARCHAR" property="createBy" />
        <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
        <result column="version" jdbcType="INTEGER" property="version" />
        <result column="enabled_flag" jdbcType="INTEGER" property="enabledFlag" />
    </resultMap>
    <sql id="BaseTable">
        ak_questionnaire_info
    </sql>
    <select id="listDiseaseCategory" resultMap="BaseResultMap">
        SELECT t1.*
        FROM ak_questionnaire_info t1
        WHERE t1.enabled_flag=0 and t1.questionnaire_code =#{questionnaireCode}
        and t1.node_type='disease_category' ORDER BY t1.node_code ASC
    </select>
    <select id="queryQuestions" resultType="com.cfpamf.ms.insur.admin.pojo.dto.product.QuestionDTO">
        SELECT *
        FROM ak_product_question_history t1
        WHERE t1.product_id=#{productId}
        and version=#{version}
    </select>

    <update id="updateQuestion" parameterType="com.cfpamf.ms.insur.admin.pojo.dto.product.QuestionDTO">
        update ak_product_question set code=#{code},
        question=#{question}
        where id=#{id}
    </update>

    <insert id="addQuestion" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO ak_product_question (
            product_id,
            code,
            question,
            status,
            update_time)
        VALUES (
            #{dto.productId},
            #{dto.code},
            #{dto.question},
            -1,
            CURRENT_TIMESTAMP()
        )
    </insert>
    <insert id="addQuestionHistory" useGeneratedKeys="true">
        INSERT INTO ak_product_question_history (
            product_id,
            code,
            question,
            update_time,operator,version)
        VALUES (
            #{dto.productId},
            #{dto.code},
            #{dto.question},
            CURRENT_TIMESTAMP(),
            #{dto.operator},
            #{version}
        )
    </insert>
    <delete id="deleteQuestionHistory">
        DELETE from
            ak_product_question_history
        where product_id=#{productId}
        and version = #{version}
    </delete>

</mapper>