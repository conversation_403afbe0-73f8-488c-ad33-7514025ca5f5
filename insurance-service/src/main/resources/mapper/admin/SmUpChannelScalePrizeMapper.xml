<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.SmUpChannelScalePrizeMapper">

    <select id="getMaxValue" resultType="java.lang.Long">
        SELECT MAX(CAST(RIGHT (plan_code, 5), LONG))
        FROM sm_up_channel_scale_prize
    </select>

    <select id="pageQuery" resultType="com.cfpamf.ms.insur.admin.pojo.vo.UpChannelScalePrizeVO">
        SELECT p.id
        ,p.channel
        ,p.plan_code planCode
        ,p.plan_type plantype
        ,p.plan_name planName
        ,p.start_time startTime
        ,p.end_time endTime
        ,p.plan_state planState
        ,p.update_by updateBy
        ,p.update_time updateTime
        ,u.userName updateByName
        FROM sm_up_channel_scale_prize p LEFT JOIN auth_user u ON p.update_by = u.userId AND u.enabled_flag = 0
        <where>
            <if test="channel != null and channel != ''">
                AND p.channel = #{channel}
            </if>
            <if test="planType != null and planType != ''">
                AND p.plan_type = #{planType}
            </if>
            <if test="startTime != null">
                AND #{startTime} &lt;= p.start_time
            </if>
            <if test="endTime != null">
                AND #{endTime} &gt;= end_time
            </if>
            <choose>
                <when test="planState == 'unpublished' or planState == 'paused' or planState == 'abandon'">
                    AND p.plan_state = #{planState}
                </when>
                <when test="planState == 'end'">
                    AND now() &gt;= p.end_time AND p.plan_state = 'published'
                </when>
                <when test="planState == 'running'">
                    AND (now() &gt; p.start_time AND now() &lt; p.end_time AND p.plan_state = 'published')
                </when>
                <when test="planState == 'unstart'">
                    AND now() &lt; p.start_time AND p.plan_state = 'published'
                </when>
            </choose>
            AND p.enabled_flag = 0
        </where>
        ORDER BY p.end_time DESC
    </select>
</mapper>

