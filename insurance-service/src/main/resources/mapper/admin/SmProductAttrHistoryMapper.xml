<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.product.SmProductAttrHistoryMapper">

    <select id="listProductAttr" resultType="com.cfpamf.ms.insur.admin.pojo.po.product.SmProductAttrHistory">
        select
            *
        from sm_product_attr_history
        where product_id=#{productId}
        and   version=#{version}
        and   enabled_flag=0
        <if test="attrCode!=null and attrCode!=''">
            and attr_code=#{attrCode}
        </if>
    </select>

    <update id="saveOrUpdate" parameterType="com.cfpamf.ms.insur.admin.pojo.po.product.SmProductAttrHistory">
        insert into sm_product_attr_history(version,product_id,attr_code,attr_val)
        values
        (#{version},#{productId},#{attrCode},#{attrVal})
        ON DUPLICATE KEY UPDATE
        version = values(version),
        attr_code = values(attr_code),
        attr_val = values(attr_val),
        update_time = now()

    </update>

</mapper>