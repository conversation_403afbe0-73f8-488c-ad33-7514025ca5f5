<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.claim.SmClaimLabelMapper">

    <resultMap id="BaseResultMap" type="com.cfpamf.ms.insur.admin.pojo.po.claim.SmClaimLabel">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="claimId" column="claim_id" jdbcType="INTEGER"/>
            <result property="label" column="label" jdbcType="DATE"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="enabledFlag" column="enabled_flag" jdbcType="TINYINT"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,claim_id,label,
        update_time,create_time,enabled_flag,
        create_by,update_by
    </sql>

    <select id="listLabelByClaimId" resultType="com.cfpamf.ms.insur.admin.pojo.po.claim.SmClaimLabel">
        select * from sm_claim_label where claim_id = #{claimId} and enabled_flag = 0
        order by id desc
    </select>

    <select id="listLabelByClaimIdList" resultType="com.cfpamf.ms.insur.admin.pojo.po.claim.SmClaimLabel">
        select * from sm_claim_label where claim_id in
        <foreach collection="list" close=")" open="(" separator="," item="item">
            #{item}
        </foreach>
        and enabled_flag = 0
        order by id desc
    </select>

    <select id="groupByClaimId" resultType="com.cfpamf.ms.insur.admin.pojo.po.claim.SmClaimLabel">
        select group_concat(label SEPARATOR '、') as label, claim_id from sm_claim_label where claim_id in
        <foreach collection="list" close=")" open="(" separator="," item="item">
            #{item}
        </foreach>
        and enabled_flag = 0
        group by claim_id
    </select>

    <select id="listComplainEmployeeCode" resultType="java.lang.String">
        select distinct o.customeradminid   from  sm_claim_label l
        left join sm_claim c  on l.`claim_id`  = c.`id` and l.`enabled_flag` =0
        LEFT JOIN sm_order_insured d on c.`insuredId`  = d.id
        LEFT JOIN sm_order o ON o.fhOrderId = d.fhOrderId
        where l.`label` like '%投诉%' and l.`enabled_flag` =0;
    </select>

</mapper>
