<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.AmSupplyMapper">

    <insert id="insertSupply" useGeneratedKeys="true">
        INSERT INTO am_supply (policyId,itemCode,itemName,itemInputType,itemOptions,create_time, update_time)
        VALUES
        (#{policyId}, #{itemCode}, #{itemName}, #{itemInputType}, #{itemOptions}, CURRENT_TIMESTAMP(), CURRENT_TIMESTAMP())
    </insert>

    <select id="listSupplysByPolicyId" resultType="com.cfpamf.ms.insur.admin.pojo.vo.AmSupplyVO">
        SELECT * FROM am_supply WHERE policyId = #{policyId}
    </select>
</mapper>