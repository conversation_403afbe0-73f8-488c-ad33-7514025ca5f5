<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cfpamf.ms.insur.admin.renewal.dao.PhoenixEmpTodoMapper">
    <update id="updateRenewShortTdo">
        update
            phoenix_emp_todo t1
                left join insurance_renewal t2 on t1.target_id = t2.old_policy_no
        set t1.state ='TIMEOUT'
        where biz_type = 'RENEW_SHORT'
          and t1.state = 'TODO'
        and t2.ins_status = 'cancel'
    </update>

    <update id="updateRenewLongTdo">
        update
            phoenix_emp_todo t1
                left join sm_order_renewal_term t2 on t1.target_id = concat(t2.policy_no, '-', term_num)
        set t1.state ='TIMEOUT'
        where biz_type = 'RENEW_LONG'
          and t1.state = 'TODO'
          and t2.renewal_status = 2
    </update>

    <update id="updateRenewLongTDone">
        update
            phoenix_emp_todo t1
            left join sm_order_renewal_term t2 on t1.target_id = concat(t2.policy_no, '-', term_num)
            set t1.state ='DONE',remark='续期',finish_time = now()
        where biz_type = 'RENEW_LONG'
          and t1.state = 'TODO'
          and t2.renewal_status = 1
    </update>



</mapper>