<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.SmOrderRenewalTermNotifyLogMapper">

    <select id="getByRenewalTermId"
            resultType="com.cfpamf.ms.insur.admin.pojo.vo.renewal.SmOrderRenewalTermNotifyLogVo">
        select t1.*,case when t1.create_by = 'system' then '系统' else t3.userName end as createUser
        from sm_order_renewal_term_notify_log t1
                 left join sm_order_renewal_term t2 on t1.policy_no = t2.policy_no and t1.term_num = t2.term_num and t2.enabled_flag = 0
                    left join auth_user t3 on t1.create_by = t3.userId and t3.enabled_flag = 0
        where t2.id = #{renewalTermId}
          and t1.enabled_flag = 0
        order by t1.create_time desc
            limit #{size};

    </select>

    <select id="queryByRenewalTermIdList"
            resultType="com.cfpamf.ms.insur.admin.pojo.vo.renewal.SmOrderRenewalTermNotifyLogVo">
        SELECT
            *,create_by as createUser
        FROM
        sm_order_renewal_term_notify_log
        WHERE
        id IN (
            SELECT
                max( a.id )
            FROM
            sm_order_renewal_term_notify_log a
            LEFT JOIN sm_order_renewal_term b ON a.policy_no = b.policy_no AND a.term_num = b.term_num and b.enabled_flag=0
            WHERE
                a.enabled_flag = 0
                AND b.id IN
                <foreach collection="renewalTermList" separator="," item="ite" open="(" close=")">
                    #{ite}
                </foreach>
            GROUP BY a.policy_no, a.term_num
        )
    </select>

    <update id="updateNewest">
        update sm_order_renewal_term_notify_log set newest = 0
        where policy_no = #{policyNo} and term_num = #{termNum} and newest = 1
    </update>

    <update id="updateNewestByPolicyNos">
        <foreach collection="list" item="item" separator=";">
            update sm_order_renewal_term_notify_log set newest = 0
            where
            policy_no = #{item.policyNo} and term_num = #{item.termNum} and newest = 1
        </foreach>
    </update>

    <insert id="insertFollow">
        insert into sm_order_renewal_term_notify_log(policy_no, term_num,result,remark, create_time, create_by,contact_way)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.policyNo}, #{item.termNum} , 98 , '已续期', #{item.paymentTime}, 'system',0)
        </foreach>
    </insert>


    <select id="selectRenewTermList" resultType="com.cfpamf.ms.insur.admin.pojo.dto.renewal.RenewalTermDTO">
        select t.`policy_no` ,t.`term_num` ,t.`payment_time` from `sm_order_renewal_term` t
        left join `phoenix_emp_todo` t2 on t2.target_id = concat(t.policy_no, '-', t.term_num)
        left join `sm_order_renewal_term_notify_log` t1 on t.`policy_no` = t1.`policy_no` and t.`term_num` = t1.`term_num`
        and t1.`newest` = 1 and t1.remark = '已续期'
        where t.`renewal_status` = 1 and t.`term_num` >1 and t1.id is null and t2.id is not null
        group by t.`policy_no` ,t.`term_num` ,t.`payment_time`
    </select>
</mapper>