<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.SmProductPosterMapper">

    <insert id="insertProductPoster" useGeneratedKeys="true">
        INSERT INTO sm_product_poster(productId, posterUrl, words, create_time, update_time)
        VALUES
        (#{productId}, #{posterUrl}, #{words}, CURRENT_TIMESTAMP(), CURRENT_TIMESTAMP())
    </insert>

    <update id="updateProductPoster">
      UPDATE sm_product_poster SET productId=#{productId},posterUrl=#{posterUrl},words=#{words}, update_time=CURRENT_TIMESTAMP() WHERE id=#{id}
    </update>

    <select id="listProductPosters" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmProductPosterVO">
        SELECT t2.id, t1.productName, t1.id AS productId, t2.posterUrl, t2.words  FROM sm_product_poster t2
        LEFT JOIN sm_product t1 ON t1.id = t2.productId
        WHERE t2.enabled_flag = 0
        <if test = 'keyword != null' >
            AND t1.productName LIKE CONCAT('%',#{keyword},'%')
        </if>
        ORDER BY companyId ASC, productName ASC
    </select>

    <select id="getProductPosterById" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmProductPosterVO">
        SELECT * from sm_product_poster WHERE enabled_flag = 0 AND id=#{id}
    </select>

    <select id="getProductPosterByProductId" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmProductPosterVO">
        SELECT * from sm_product_poster WHERE productId=#{productId} AND enabled_flag = 0 LIMIT 1
    </select>

    <select id="getProductPostersByProductId" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmProductPosterVO">
        SELECT * from sm_product_poster WHERE productId=#{productId} AND enabled_flag = 0
    </select>

    <update id="deleteProductPoster">
        UPDATE sm_product_poster SET enabled_flag = 1 WHERE id=#{id}
    </update>
</mapper>