<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.sys.SystemNotifyMapper">
    <select id="selectByQuery" resultType="com.cfpamf.ms.insur.admin.pojo.po.sys.SystemNotify">

        select *
        from system_notify t
        where t.enabled_flag = 0
        <if test="start!=null">
            and t.start_time >= #{start}
        </if>

        <if test="end!=null">
            <![CDATA[and   t.end_time >= #{end} ]]>
        </if>
        <if test="endTime!=null">
            <![CDATA[and   t.end_time >= #{endTime} ]]>
        </if>

        <if test="notifyType!=null">
            <![CDATA[and   t.notify_type = #{notifyType} ]]>
        </if>
        <if test="state!=null">
            <![CDATA[  and t.state = #{state} ]]>
        </if>

        <if test="channel!=null">
            and channel like concat('%',#{channel},'%')
        </if>

        <if test="platform!=null">
            and platform like concat('%',#{platform},'%')
        </if>
        <if test="doing">
            <![CDATA[
              start_time < now()
          and end_time > now() and state = 10]]>
        </if>
        order by id desc


    </select>
    <select id="countMaintenance" resultType="java.lang.Integer">
        select count(1)
        from system_notify
        where
        <![CDATA[
              start_time < now()
          and end_time > now()]]>
        <if test="channel!=null">
            and channel like concat('%', #{channel}, '%')
        </if>
        <if test="platform!=null">
            and platform like concat('%', #{platform}, '%')
        </if>
        and state = 10
        and enabled_flag = 0
    </select>
    <select id="selectMaintenance" resultType="com.cfpamf.ms.insur.admin.pojo.po.sys.SystemNotify">
        select *
        from system_notify
        where
        <![CDATA[
              start_time < now()
          and end_time > now()]]>
        <if test="channel!=null">
            and channel like concat('%', #{channel}, '%')
        </if>
        <if test="platform!=null">
            and platform like concat('%', #{platform}, '%')
        </if>
        and state = 10
        and enabled_flag = 0
        order by id desc
        limit 1
    </select>
</mapper>
