<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.UserAuthorityMapper">

    <insert id="insertUserAuthority" useGeneratedKeys="true">
      INSERT INTO user_authority (userId,moduleCode,moduleName) VALUES (#{userId}, #{moduleCode}, #{moduleName})
    </insert>

    <update id="deleteUserAuthority">
      UPDATE user_authority SET enabled_flag = 1 WHERE userId=#{userId}
    </update>

    <select id="listUserAuthoritysByUserMobile" resultType="com.cfpamf.ms.insur.admin.pojo.vo.UserAuthorityVO">
        SELECT * FROM user_authority t1 LEFT JOIN auth_user t2 ON t1.userId = t2.userId AND t2.enabled_flag = 0 WHERE t1.enabled_flag = 0 AND t2.userMobile = #{userMobile}
    </select>

    <select id="listUserAuthoritysByUserId" resultType="com.cfpamf.ms.insur.admin.pojo.vo.UserAuthorityVO">
        SELECT * FROM user_authority t1 LEFT JOIN auth_user t2 ON t1.userId = t2.userId AND t2.enabled_flag = 0 WHERE t1.enabled_flag = 0 AND t2.userId = #{userId}
    </select>
</mapper>