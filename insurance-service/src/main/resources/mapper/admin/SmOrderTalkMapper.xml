<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.pco.SmOrderTalkMapper">
    <select id="getTalkPerson" resultType="com.cfpamf.ms.insur.admin.pojo.po.pco.SmActTalkPerson">

        select p.*
        from sm_act_talk t
        left join sm_act_talk_person p on t.id = p.talk_id
        where
       <![CDATA[  start_time < #{createTime}
          and date_add(date(start_time), INTERVAL 30 DAY) > #{createTime}
          ]]> and (
            <foreach collection="persons" item="item" separator="or">
                (person_name = #{item.personName} and person_mobile = #{item.personMobile} )
            </foreach>
        ) and t.state = 2
        order by t.id desc
        limit 1
    </select>
    <select id="selectOrderTalkInfo" resultType="com.cfpamf.ms.insur.admin.pojo.vo.pco.SmOrderTalkVO">
        select sot.*, sat.expert_number, sat.pco_job_number, au.userName expert_name, aupc.userName pco_name
        from sm_order_talk sot
                 left join sm_act_talk sat on sot.talk_id = sat.id
                 left join auth_user au on au.userId = sat.expert_number and au.enabled_flag = 0
                 left join auth_user aupc on aupc.userId = sat.pco_job_number and aupc.enabled_flag = 0
        where fh_order_id = #{fhOrderId}
    </select>
</mapper>
