<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.product.SmPlanRiskDutyHistoryMapper">

    <select id="getSmPlanRiskDutyList" resultType="com.cfpamf.ms.insur.admin.pojo.vo.product.SmPlanRiskDutyVO">
        select
        t1.*,
        t2.duty_name,
        t2.duty_code,
        t2.id as sysRiskDutyId,
        t2.display_sort,
        t2.amount_type as amountType,
        t2.duty_detail
        from sm_plan_risk_duty_history t1
        left join sys_risk_duty t2 on t1.duty_key = t2.duty_key and t1.duty_version = t2.version and t2.enabled_flag = 0
        where t1.enabled_flag = 0
        and t1.version = #{version}
        and t2.id is not null
        and t1.sm_plan_risk_id in
        <foreach collection="planRiskIdList" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <update id="softDelete">
        update sm_plan_risk_duty_history
        set enabled_flag = 1
        where plan_id = #{planId}
          and version = #{version}
    </update>

    <insert id="initNewHistory">
        insert into sm_plan_risk_duty_history(plan_id, product_id, sm_plan_risk_id, plan_risk_duty_id, duty_key,
                                              duty_version,
                                              amount_text,
                                              risk_duty_amount_id_arr,
                                              included_risk_insured_amount,
                                              mandatory,
                                              version,
                                              update_by,
                                              update_time, enabled_flag, create_by, create_time)
        SELECT plan_id,
               product_id,
               sm_plan_risk_id,
               id,
               duty_key,
               duty_version,
               amount_text,
               risk_duty_amount_id_arr,
               included_risk_insured_amount,
               mandatory,
               #{version},
               update_by,
               update_time,
               enabled_flag,
               create_by,
               create_time
        from sm_plan_risk_duty
        where product_id = #{productId}
          and enabled_flag = 0

    </insert>


</mapper>
