<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.SmCmpyRegionMapper">

    <insert id="insertCompanyRegions" useGeneratedKeys="true">
        INSERT INTO sm_company_region (companyId,parentId,regionId,regionCode,regionName,sorting, create_by,
        create_time, update_by,update_time)
        VALUES
        <foreach collection="dtos" item="item" index="index" separator=",">
            (#{item.companyId}, #{item.parentId}, #{item.regionId}, #{item.regionCode}, #{item.regionName},
            #{item.sorting}, #{item.modifyBy}, CURRENT_TIMESTAMP(), #{item.modifyBy}, CURRENT_TIMESTAMP())
        </foreach>
    </insert>

    <insert id="insertProductRegion">
        INSERT INTO sm_product_region (product_id,type,region_code,region_name)
        VALUES
        <foreach collection="data" item="item" separator=",">
            (#{item.productId}, #{item.type}, #{item.regionCode}, #{item.regionName})
        </foreach>
    </insert>

    <update id="deleteCompanyRegion">
        UPDATE sm_company_region
        SET enabled_flag = 1
        WHERE companyId = #{companyId}
    </update>

    <select id="listCompanyRegions" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmCompanyRegionVO">
        SELECT t1.*, t2.regionCode AS parentRegionCode FROM sm_company_region t1
        LEFT JOIN sm_company_region t2 ON t1.parentId = t2.regionId AND t1.companyId=t2.companyId
        WHERE t1.companyId=#{companyId}
        AND t1.enabled_flag = 0
        <if test='parentId != null'>
            AND t1.parentId=#{parentId}
        </if>
        <if test='parentId == null and !findAll'>
            AND t1.parentId IS NULL
        </if>
        ORDER BY t1.regionCode ASC, t1.sorting ASC
    </select>

    <select id="getByCode" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmCompanyRegionTreeVO">
        select * from sm_company_region where
        companyId=#{companyId}
        <if test="code!=null">
            and regionCode =#{code}
        </if>
        <if test="regionId!=null">
            and regionId=#{regionId}
        </if>
        order by id desc
        limit 1
    </select>

</mapper>
