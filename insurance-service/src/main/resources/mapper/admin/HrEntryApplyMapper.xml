<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.hr.HrEntryApplyMapper">


    <select id="selectAutoMaybe" resultType="com.cfpamf.ms.insur.admin.pojo.po.hr.HrEntryApply">
        select *
        from hr_entry_apply
        where end_time is null
    </select>
    <select id="selectByProInsId" resultType="com.cfpamf.ms.insur.admin.pojo.po.hr.HrEntryApply">
        select *
        from hr_entry_apply
        where process_instance_id = #{processInstanceId}
    </select>
    <select id="listByQuery" resultType="com.cfpamf.ms.insur.admin.pojo.vo.hr.HrEntryApplyVO">
        select hr.import_id,
        hr.bms_user_id,
        hr.id_number,
        he.name,
        he.mobile,
        au.regionName,
        au.organizationName,
        hr.start_time,
        hr.end_time,
        hr.flow_status,
        hr.id,
        hr.process_instance_id,
        he.id employeeId
        from hr_entry_apply hr
        left join hr_employee he on hr.id = he.apply_id
        left join auth_user au on au.userId = he.zhnx_job_number and au.enabled_flag = 0
        left join hr_entry_process hep on
        hep.apply_bms_user_id = hr.bms_user_id and hr.flow_code = hep.node_key
        where 1=1

        <if test='applyId != null'>
            AND hr.id =#{applyId}
        </if>
        <if test='regionName != null'>
            AND au.regionName=#{regionName}
        </if>
        <if test='orgName != null'>
            AND au.organizationName=#{orgName}
        </if>
        <if test="startTimeStart != null">
            <![CDATA[ and hr.start_time >= #{startTimeStart}  ]]>
        </if>

        <if test="startTimeEnd != null">
            <![CDATA[ and hr.start_time < #{startTimeEnd}  ]]>
        </if>

        <if test="endTimeStart != null">
            <![CDATA[ and hr.end_time >= #{endTimeStart}  ]]>
        </if>

        <if test="endTimeEnd != null">
            <![CDATA[ and hr.end_time < #{endTimeEnd}  ]]>
        </if>

        <if test="employeeKeyword!=null and employeeKeyword!=''">
            and (
            he.name like CONCAT('%',<trim>#{employeeKeyword}</trim>,'%')
            or he.mobile like CONCAT('%',<trim>#{employeeKeyword}</trim>,'%')
            or he.zhnx_job_number like CONCAT('%',<trim>#{employeeKeyword}</trim>,'%')
            )
        </if>
        <if test="flowStatus !=null">
            and hr.flow_status = #{flowStatus}
        </if>
        <!-- 我发起的 -->
        <if test="applyIsMe == true">
            and hr.create_by = #{userId}
        </if>
        <!-- 待我处理的 -->
        <if test="doingIsMe == true">
            and hep.candidate = #{userId}
        </if>
        <!-- 我已处理的 -->
        <if test="doneIsMe == true">
            and exists(select 1 from act_hi_taskinst iht where
            iht.PROC_INST_ID_ = hr.process_instance_id
            and iht.ASSIGNEE_= #{userId}
            )
        </if>

        order by hr.start_time desc
    </select>
</mapper>
