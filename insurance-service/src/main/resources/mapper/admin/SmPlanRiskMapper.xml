<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.product.SmPlanRiskMapper">

    <update id="pushNewVersion">
        update sm_plan_risk spr
            left join sm_plan_risk_history sprh
        on spr.id = sprh.plan_risk_id and sprh.enabled_flag = 0
            set
                spr.plan_id = sprh.plan_id,
                spr.product_id = sprh.product_id,
                spr.risk_key = sprh.risk_key,
                spr.risk_version = sprh.risk_version,
                spr.included_total_insured_amount = sprh.included_total_insured_amount,
                spr.enabled_flag = sprh.enabled_flag,
                spr.create_by = sprh.create_by,
                spr.create_time = sprh.create_time,
                spr.update_by = sprh.update_by,
                spr.sort_field = sprh.sort_field,
                spr.update_time = sprh.update_time
        where sprh.product_id = #{productId}
          and sprh.version = #{version}
          and sprh.enabled_flag = 0
    </update>
</mapper>