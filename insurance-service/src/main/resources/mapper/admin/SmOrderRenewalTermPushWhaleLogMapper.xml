<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.renewalTerm.SmOrderRenewalTermPushWhaleLogMapper">

    <select id="listByChannelList" resultType="com.cfpamf.ms.insur.admin.pojo.po.renewal.SmOrderRenewalTermPushWhaleLogPo">
        select
            *
        from sm_order_renewal_term_push_whale_log
        where status=#{status}
        <if test="channelList!=null and channelList.size>0">
            and channel in
            <foreach collection="channelList" item="channel" open="(" close=")" separator=",">
                #{channel}
            </foreach>
        </if>
        limit #{limit}

    </select>

    <update id="updatePeriod" parameterType="com.cfpamf.ms.insur.admin.pojo.po.renewal.SmOrderRenewalTermPushWhaleLogPo">
        update sm_order_renewal_term_push_whale_log
            set status=#{status},
            message=#{message},
            update_time=now()
        where policy_no=#{policyNo}
        and term_num=#{termNum}
        and status!=1
    </update>

</mapper>