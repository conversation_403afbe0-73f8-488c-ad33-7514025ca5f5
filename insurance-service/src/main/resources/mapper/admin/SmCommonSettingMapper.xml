<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.SmCommonSettingMapper">
    <select id="listOption2Company" resultType="com.cfpamf.ms.insur.admin.pojo.dto.SmCommonSettingVO">
        select smc.field_code, smc.option_name, scs.companyId, scs.optionCode
        from sm_common_setting smc
        left join sm_company_setting scs on smc.option_code = scs.commonCode
        and smc.field_code = scs.fieldCode
        and scs.enabled_flag = 0
        where field_code in
        <foreach item="item" index="index" open="(" separator="," close=")" collection="fieldCode">
            #{item}
        </foreach>
        and smc.enabled_flag = 0
        <if test="optionName!=null and optionName.size()>0">
        and option_name in
        <foreach item="item" index="index" open="(" separator="," close=")" collection="optionName">
            #{item}
        </foreach>
        </if>
        and scs.companyId = #{companyId}
    </select>

    <select id="selectList" resultType="com.cfpamf.ms.insur.admin.pojo.dto.SmCommonSettingVO">
        select t2.name dictionaryName,t1.field_code fieldCode,t1.option_code optionCode,
               t1.option_name optionName,CONCAT(t2.name,'-',t1.option_name) typeName
        from sm_common_setting t1
                 left join dictionary t2 on t2.code = t1.field_code and t2.type = 'orderField' and t2.enabled_flag = 0
        where t1.enabled_flag = 0
    </select>

    <select id="queryListOption" resultType="com.cfpamf.ms.insur.admin.pojo.dto.SmCommonSettingVO">
        select smc.field_code, smc.option_name, scs.companyId, scs.optionCode
        from sm_common_setting smc
        left join sm_company_setting scs on smc.option_code = scs.commonCode
        and smc.field_code = scs.fieldCode
        and scs.enabled_flag = 0
        where field_code in
        <foreach item="item" index="index" open="(" separator="," close=")" collection="fieldCode">
            #{item}
        </foreach>
        and smc.enabled_flag = 0
        <if test="optionName!=null and optionName.size()>0">
            and option_name in
            <foreach item="item" index="index" open="(" separator="," close=")" collection="optionName">
                #{item}
            </foreach>
        </if>
    </select>
</mapper>
