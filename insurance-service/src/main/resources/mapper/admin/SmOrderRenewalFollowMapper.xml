<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.admin.renewal.dao.SmOrderRenewalFollowMapper">
    <select id="selectByPolicyNo" resultType="com.cfpamf.ms.insur.admin.pojo.dto.renewal.SmOrderRenewalFollowDto">
        select
            t1.id,t1.policy_no,follow_time
             ,case when t1.follow_people = 'system' then '系统' else t2.userName end followPeopleName
             ,follow_people,service_mode,intention,'' intent,reason,reason_remark,t1.remark,next_time,
            '' voiceAddress,'' recordJson,0 talking_time_len,'INTERRUPTION' taskType,t3.record_url
        from sm_order_renewal_follow t1
                 left join auth_user t2 on t2.userId = t1.follow_people and t2.enabled_flag = 0
                  left join call_record t3 on t3.follow_record_id = t1.id
        where t1.policy_no = #{policyNo}
          and t1.enabled_flag = 0
        union all
        select id,target_id policy_no,
               create_time as follow_time,
               '机器人' followPeopleName,'机器人' followPeople,'phone' service_mode,
               '' intention,intent,'' reason,'' reasonRemark,'' remark,create_time nextTime,
               voice_address,record_json,talking_time_len,task_type,'' recordUrl
        from ai_follow_record where target_id = #{policyNo} and task_type ='RENEW_SHORT'
        order by follow_time desc,id desc
    </select>

    <update id="updateNewest">
        update sm_order_renewal_follow set newest = 0
        where policy_no = #{policyNo} and newest = 1
    </update>

    <select id="getBefore1DayList" resultType="com.cfpamf.ms.insur.weixin.pojo.vo.renewal.WxRenewalFollowListVo">
        select t1.order_id,t1.policy_no,t1.service_mode
             ,t1.intention,t1.next_method,t1.next_time,t2.wxOpenId,t4.productName,t5.personName
        from sm_order_renewal_follow t1
                 left join auth_user t2 on t1.follow_people = t2.userId
                 left join sm_order t3 on t1.order_id = t3.fhorderid
                 left join sm_product t4 on t4.id = t3.productid
                 left join sm_order_applicant t5 on t5.fhorderid = t3.fhorderid
        where t1.newest = 1
          and DATE_FORMAT(now(),'%Y-%m-%d') = DATE_FORMAT(date_sub(t1.next_time,interval 1 day),'%Y-%m-%d')
    </select>

    <insert id="insertFollow">
        update sm_order_renewal_follow set newest = 0
        where policy_no = #{renewal.oldPolicyNo} and newest = 1;
        insert into sm_order_renewal_follow(order_id, policy_no,intention, follow_time, follow_people)
        VALUES (#{renewal.oldOrderId}, #{renewal.oldPolicyNo} , 'renewed', now(), 'system')
    </insert>

    <select id="getExpirePolicyList" resultType="com.cfpamf.ms.insur.admin.pojo.dto.renewal.SmOrderRenewalFollowDto">
        select t.*,t1.productid,t1.planId
        from sm_order_renewal_follow t
        left join insurance_renewal t3 on t.policy_no = t3.old_policy_no
        left join sm_order t1 on t.order_id = t1.fhorderid
        left join sm_renewal_config t2 on t2.product_id = t1.productId and t2.enabled_flag = 0
        where t.intention = 'transfer' and t.transfer_order_id is not null
        and DATE_FORMAT(NOW(), '%Y-%m-%d') = DATE_FORMAT(DATE_ADD(t1.endTime, INTERVAL t2.after_expiration_day DAY), '%Y-%m-%d')
        and t3.ins_status != 'renewed'
        group by t.transfer_order_id,t.policy_no
        </select>

    <select id="listExistTransferPolicyNo" resultType="java.lang.String">
        select t.transfer_policy_no
        from sm_order_renewal_follow t
        where t.transfer_policy_no in
        <foreach collection="transferPolicyList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
</mapper>