<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.order.SmOrderDDDMapper">
    <select id="selectPayedByOrderIds" resultType="com.cfpamf.ms.insur.admin.pojo.dto.order.SimpleOrderDTO">

        select fhOrderId,create_time createTime,payStatus,subChannel,paymentTime
        from sm_order
        where fhOrderId in
        <foreach collection="fhOrderIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach> and payStatus = '2'
    </select>

    <select id="selectChannelByOrderIds" resultType="com.cfpamf.ms.insur.admin.pojo.po.SmOrder">
        select fhOrderId,productId,channel,subChannel,recommend_channel from sm_order
        where fhOrderId in
        <foreach collection="orderIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <update id="upChannelOrderIds">
        UPDATE sm_order
        SET channel = #{channel},
            subChannel= #{subChannel},
            recommend_channel=#{recommendChannel}
        WHERE
            fhOrderId = #{fhOrderId}
            LIMIT 1
    </update>

    <update id="deleteSmOrderCoverage" parameterType="arraylist">
        UPDATE sm_order_policy set `amount` = null WHERE fh_order_id IN
        <foreach collection="fhOrderIds" index="index" item="fhOrderId" separator=","
                 open="(" close=")">
            #{fhOrderId}
        </foreach>
    </update>

    <update id="upSmOrderPaymentTime">
        UPDATE sm_order SET paymentTime = create_time where fhOrderId IN
        <foreach collection="fhOrderIds" index="index" item="fhOrderId" separator=","
                 open="(" close=")">
            #{fhOrderId}
        </foreach>
    </update>
</mapper>
