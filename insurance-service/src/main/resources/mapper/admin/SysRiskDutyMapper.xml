<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.product.risk.SysRiskDutyMapper">

    <update id="updateDutySort">
        update sys_risk_duty
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="display_sort =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when id=#{item.id} then #{item.displaySort}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item.id}
        </foreach>
    </update>
</mapper>
