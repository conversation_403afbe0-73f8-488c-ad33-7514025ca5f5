<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.SmClaimMapper">
    <sql id="smClaimColumn">
        t0.id AS id,
        t0.riskType,
        t0.claimNo,
        t0.riskTime,
        t0.riskDesc,
        t0.claimResult,
        t0.risk_reason_accident_type as riskReasonAccidentType ,
        t0.claim_risk_reason as claimRiskReason,
        DATE_FORMAT(t0.create_time ,'%Y-%m-%d %H:%i:%s') AS reportTime,
        t0.settlement,
        t0.estimatedAmount,
        t0.lastFollowUp,
        t0.medicalInsur,
        t0.otherInsur,
        CASE WHEN t0.expressTime != '0000-00-00 00:00:00' THEN t0.expressTime ELSE NULL END AS expressTime,
        COUNT(t00.id) AS apdUpPayTimes,
        SUM(CASE WHEN t00.payMoney IS NULL THEN 0 ELSE t00.payMoney END) AS apdUpPayMoney,
        t0.payMoney,
        t0.dataTime,
        t0.finishState,
        t0.finishResult,
        t0.finishTime,
        t0.note,
        t1.channel,
        t0.create_time AS createTime,
        t1.fhOrderId AS fhOrderId,
        t1.orderState AS orderState,
        t1.payStatus AS payStatus,
        t1.unitPrice * t1.qty AS totalAmount,
        t1.startTime AS startTime,
        t1.endTime AS endTime,
        t1.productId AS productId,
        t2.personName AS applicantPersonName,
        t2.idNumber AS aplicantIdNumber,
        t2.cellPhone AS applicantCellPhone,
        t2.email AS applicantEmail,
        t2.personGender AS applicantPersonGender,
        t2.birthday AS applicantBirthday,
        t3.policyNo AS policyNo,
        t3.appStatus AS appStatus,
        t3.personName AS insuredPersonName,
        t3.idNumber AS insuredIdNumber,
        t3.cellPhone AS insuredCellPhone,
        t3.email AS insuredEmail,
        t3.personGender AS insuredPersonGender,
        t3.birthday AS insuredBirthday,
        t3.relationship AS insuredRelationship,
        t3.downloadURL AS downloadURL,
        t4.productName AS productName,
        t4.companyId AS companyId,
        t5.companyName AS companyName,
        t6.userName AS recommendUserName,
        t6.userId AS recommendUserId,
        t6.userMobile AS recommendUserMobile,
        t6.regionName AS recommendRegionName,
        t6.organizationFullName AS recommendOrganizationName,
        t7.planName AS planName,
        t9.agentName,
        t9.agentMobile,
        t10.lastFollowTime,
        t0.process_type
    </sql>
    <select id="listSmClaims" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmClaimVO">
        SELECT
        <include refid="smClaimColumn"/>
        , t4.productAttrCode
        , t0.expect_hasten_time
        , t0.hasten_count
        , t0.`timeout`
        , t0.`reminded`
        , t0.`visiting_occupation`
        , t0.`visiting_date`
        , t0.`visiting_hospital`
        , t0.`visiting_hospital_name`
        , t0.claimState
        , t0.finish_type
        , t0.exclude_org_sign
        , t0.current_status_memo
        FROM sm_claim t0
        LEFT JOIN sm_claim t00 ON t00.insuredId=t0.insuredId <![CDATA[ AND t00.create_time<=t0.create_time ]]> AND
        t00.finishState = 'payed'
        LEFT JOIN sm_order_insured t3 ON t0.insuredId=t3.id
        LEFT JOIN sm_order t1 ON t3.fhOrderId=t1.fhOrderId
        LEFT JOIN sm_order_applicant t2 ON t2.fhOrderId=t1.fhOrderId
        LEFT JOIN sm_product t4 ON t4.id=t1.productId
        LEFT JOIN sm_company t5 ON t5.id=t4.companyId
        LEFT JOIN auth_user t6 ON t6.userId=t1.customerAdminId AND t6.enabled_flag = 0
        LEFT JOIN sm_plan t7 ON t7.id=t1.planId
        LEFT JOIN sm_agent t9 ON t9.agentId=t1.agentId
        LEFT JOIN (select claim_id,max(create_time) as lastFollowTime from sm_claim_follow_up group by claim_id) t10 on
        t0.id=t10.claim_id
        WHERE 1=1
        <if test='reportDateStart != null'>
            <![CDATA[ AND t0.create_time>=#{reportDateStart} ]]>
        </if>
        <if test='reportDateEnd != null'>
            <![CDATA[ AND t0.create_time<=#{reportDateEnd} ]]>
        </if>
        <if test="settlement != null and settlement !=''">
            AND t0.settlement like CONCAT(#{settlement},'%')
        </if>
        <if test='riskDateStart != null'>
            <![CDATA[ AND t0.riskTime>=#{riskDateStart} ]]>
        </if>
        <if test='riskDateEnd != null'>
            <![CDATA[ AND t0.riskTime<=#{riskDateEnd} ]]>
        </if>
        <if test='finishDateStart != null'>
            <![CDATA[ AND t0.finishTime>=#{finishDateStart} ]]>
        </if>
        <if test='finishDateEnd != null'>
            <![CDATA[ AND t0.finishTime<=#{finishDateEnd} ]]>
        </if>
        <if test='claimNo != null'>
            AND t0.claimNo=#{claimNo}
        </if>
        <if test='policyNo != null'>
            AND t3.policyNo=#{policyNo}
        </if>
        <if test='custManager != null'>
            AND ((t6.userName LIKE CONCAT('%',#{custManager},'%')) OR (t6.userId LIKE
            CONCAT('%',#{custManager},'%')))
        </if>
        <if test='insuredPerson != null'>
            AND ((t3.personName LIKE CONCAT('%',#{insuredPerson},'%')) OR (t3.idNumber LIKE
            CONCAT('%',#{insuredPerson},'%')) OR (t3.cellPhone LIKE CONCAT('%',#{insuredPerson},'%')))
        </if>
        <if test='riskType != null'>
            AND t0.riskType=#{riskType}
        </if>
        <!--2020-05-18 将保司核赔中的邮寄资料分开显示 delete by zhangjian
        <if test='claimState != null and claimState=="stepToPay"'>
            AND t0.claimState IN ('stepToPay','stepPostExpress')
        </if>
        -->
        <if test='claimState != null and claimState=="stepDataPrepare"'>
            AND t0.claimState IN ('stepDataPrepare')
        </if>
        <if test='claimState != null and claimState=="stepDataPrepare2"'>
            AND t0.claimState IN ('stepDataPrepare2', 'stepDataPrepare3')
        </if>
        <if test='claimState != null  and claimState!="stepDataPrepare"'>
            AND t0.claimState=#{claimState}
        </if>
        <!-- 未结案 -->
        <if test="claimState == null and claimId == null">
            <![CDATA[  AND t0.claimState<>'stepFinish' ]]>
        </if>
        <if test='finishState != null and finishState=="claimCanceled"'>
            AND t0.finishState IN ('claimCanceled')
        </if>
        <if test='finishState != null and finishState!="claimCanceled"'>
            AND t0.finishState=#{finishState}
        </if>
        <if test='regionName != null'>
            AND t6.regionName=#{regionName}
        </if>
        <if test='orgName != null'>
            AND t6.organizationFullName=#{orgName}
        </if>
        <if test='productId != null'>
            AND t1.productId=#{productId}
        </if>
        <if test='agentId != null'>
            AND t1.agentId=#{agentId}
        </if>
        <if test='claimId != null'>
            AND t0.id=#{claimId}
        </if>
        <if test='otherReimbursement != null and otherReimbursement=="medicalInsur"'>
            AND t0.medicalInsur=#{reimbursementValue}
        </if>
        <if test='otherReimbursement != null and otherReimbursement=="otherInsur"'>
            AND t0.otherInsur=#{reimbursementValue}
        </if>

        <if test="companyId !=null">
            AND t5.id = #{companyId}
        </if>

        <if test="productType != null and productType != ''">
            AND t4.productType = #{productType}
        </if>

        <if test='applicant != null'>
            AND
            <if test="applicantType == 1">
                t2.personName LIKE CONCAT(#{applicant},'%')
            </if>
            <if test="applicantType == 2">
                t2.idNumber LIKE CONCAT(#{applicant},'%')
            </if>
            <if test="applicantType == 3">
                t2.cellPhone LIKE CONCAT(#{applicant},'%')
            </if>
        </if>
        <if test='labelCode != null'>
            AND t0.id in (
            select d.claim_id from sm_claim_rule_label_detail d,sm_claim_rule_label_record r where d.claim_id = r.claim_id and d.batch_no = r.batch_no
            and d.label_code=#{labelCode}
            <if test="labelValueCode !=null">
                and d.value_code =#{labelValueCode}
            </if>
            and  d.`enabled_flag` =0 and r.`enabled_flag` =0
            )
        </if>
        <if test='sendCloseNotify != null'>
            <if test='sendCloseNotify == 1'>
                AND exists (select 1 from sm_claim_finish_notify cfn where cfn.claim_id = t0.id and cfn.enabled_flag=0)
            </if>
            <if test='sendCloseNotify == 0'>
                AND not exists (select 1 from sm_claim_finish_notify cfn where cfn.claim_id = t0.id and cfn.enabled_flag=0)
            </if>
        </if>
        GROUP BY t0.id
        <choose>
            <when test='claimState != null and claimState != "stepDataPrepare" and claimState != "stepDataPrepare2"'>
                ORDER BY t0.reminded desc, -t0.timeout desc, t0.create_time ASC
            </when>
            <otherwise>
                ORDER BY t0.reminded desc, -t0.timeout desc, t0.create_time DESC
            </otherwise>
        </choose>

    </select>

    <select id="listWxClaims" resultType="com.cfpamf.ms.insur.weixin.pojo.vo.claim.WxClaimCancelReportListVo">
        SELECT null as claimCancelReportId,
        null as claimCancelReportState ,
        null as claimCancelReportResult ,
        t0.id AS claimId,t0.id AS id, t0.insuredId, t0.claimNo, t0.riskType, t0.riskTime, t0.riskDesc, t0.claimResult,
        t0.claimState,
        DATE_FORMAT(t0.create_time ,'%Y-%m-%d %H:%i:%s') AS reportTime,
        t4.productName AS productName, t7.planName AS planName,t3.policyNo AS policyNo,
        t3.personName AS insuredPersonName, t0.update_time AS updateTime,t1.customerAdminId as customerAdminId,
        (case when t0.id = null
        then t4.channel
        else t0.channel
        end) as channel,t4.companyId
        ,t4.id as productId
        ,t0.process_type
        ,t0.finishTime
        ,t8.personName as applicantName
        , t6.hrOrgId
        , t0.finishState
        , t0.finishResult
        , t0.reminded
        , t0.expect_hasten_time
        , t0.timeout
        , t6.userName as customerAdminName
        , t0.expect_wait_day as expectCloseDay
        , t0.create_time as sortCreateTime
        ,t0.evaluation_status as evaluationStatus
        ,t0.evaluation_score as evaluationScore
        FROM sm_claim t0
        LEFT JOIN sm_order_insured t3 ON t0.insuredId=t3.id
        LEFT JOIN sm_order t1 ON t3.fhOrderId=t1.fhOrderId
        LEFT JOIN sm_product t4 ON t4.id=productId
        LEFT JOIN auth_user t6 ON t6.userId=t1.customerAdminId AND t6.enabled_flag = 0
        LEFT JOIN sm_plan t7 ON t7.id=t1.planId
        LEFT JOIN sm_order_applicant t8 ON t3.fhOrderId=t8.fhOrderId
        WHERE 1 = 1
        <if test='userId != null'>
            AND t1.customerAdminId = #{userId}
        </if>
        <if test='agentId != null'>
            AND t1.agentId = #{agentId}
        </if>
        <if test='keyword != null'>
            AND ((t3.personName LIKE CONCAT(#{keyword},'%')) OR (t3.idNumber LIKE
            CONCAT(#{keyword},'%')) OR (t3.cellPhone LIKE CONCAT(#{keyword},'%'))
            OR (t3.policyNo LIKE CONCAT(#{keyword},'%'))
            )
        </if>
        <if test='claimState != null and claimState=="inProcess"'>
            AND t0.claimState IN (
                                'stepGuide'
                                , 'stepDataCheckByPic'
                                , 'stepDataCheckBySafesCenter'
                                , 'stepToPay'
                                , 'stepDataPrepare'
                                , 'stepDataPrepare2'
                                , 'stepDataPrepare3'
                                , 'stepPostExpress')
        </if>
        <if test='claimState != null and claimState=="stepToPay"'>
            AND t0.claimState = 'stepToPay'
        </if>
        <!--add by zhangjian 2020-05-18 待提交资料列表要显示“邮寄纸质资料”的数据-->
        <if test='claimState != null and claimState=="stepDataPrepare"'>
            AND t0.claimState IN ('stepDataPrepare', 'stepDataPrepare2','stepDataPrepare3','stepPostExpress')
        </if>
        <if test='claimState != null and claimState!="stepToPay" and claimState!="inProcess" and claimState!="stepDataPrepare"'>
            AND t0.claimState=#{claimState}
        </if>
        <if test='queryType == 1'>
            AND t0.claimState != 'stepGuide' AND t0.claimState != 'stepDataCheckByPic' AND t0.claimResult != '资料提交' AND
            t0.claimState != 'claimCancelReport'
        </if>
        <if test='queryType == 0'>
            AND t0.claimState = 'stepDataCheckByPic'
        </if>
        <if test='channel != null'>
            AND t4.channel=#{channel}
        </if>
        <if test='regionName != null'>
            AND t6.regionName=#{regionName}
        </if>
        <if test='orgName != null'>
            AND t6.organizationName=#{orgName}
        </if>
        <if test="queryType == 1">
            UNION ALL
            (
            SELECT
            t2.id as claimCancelReportId,
            t2.current_state as claimCancelReportState ,
            t2.current_result as claimCancelReportResult,
            t0.id AS claimId,
            t0.id AS id, t0.insuredId, t0.claimNo, t0.riskType, t0.riskTime, t0.riskDesc, t0.claimResult,
            t0.claimState,
            DATE_FORMAT(t0.create_time ,'%Y-%m-%d %H:%i:%s') AS reportTime,
            t4.productName AS productName, t7.planName AS planName,t3.policyNo AS policyNo,
            t3.personName AS insuredPersonName, t0.update_time AS updateTime,t1.customerAdminId as customerAdminId
            , (case when t0.id = null
            then t4.channel
            else t0.channel
            end) as channel,t4.companyId
            ,t4.id as productId
            ,t0.process_type
            ,t0.finishTime
            , t8.personName as applicantName
            , t6.hrOrgId
            , t0.finishState
            , t0.finishResult
            , t0.reminded
            , t0.expect_hasten_time
            , t0.timeout
            , t6.userName as customerAdminName
            , t0.expect_wait_day as expectCloseDay
            , t0.create_time as sortCreateTime
            ,t0.evaluation_status as evaluationStatus
            ,t0.evaluation_score as evaluationScore
            FROM sm_claim_cancel_report t2
            LEFT JOIN sm_claim t0 ON t0.claimNo = t2.claim_no
            LEFT JOIN sm_order_insured t3 ON t0.insuredId=t3.id
            LEFT JOIN sm_order t1 ON t3.fhOrderId=t1.fhOrderId
            LEFT JOIN sm_product t4 ON t4.id=productId
            LEFT JOIN auth_user t6 ON t6.userId=t1.customerAdminId AND t6.enabled_flag = 0
            LEFT JOIN sm_plan t7 ON t7.id=t1.planId
            LEFT JOIN sm_order_applicant t8 ON t3.fhOrderId=t8.fhOrderId
            WHERE 1 = 1
            <if test='userId != null'>
                AND t1.customerAdminId = #{userId}
            </if>
            <if test='agentId != null'>
                AND t1.agentId = #{agentId}
            </if>
            <if test='keyword != null'>
                AND ((t3.personName LIKE CONCAT(#{keyword},'%')) OR (t3.idNumber LIKE
                CONCAT(#{keyword},'%')) OR (t3.cellPhone LIKE CONCAT(#{keyword},'%'))
                OR (t3.policyNo LIKE CONCAT(#{keyword},'%'))
                )
            </if>
            and t2.current_state != 'cancelReportApply' and t2.current_state != 'stepDataCheckByPic'
            <if test='channel != null'>
                AND t4.channel=#{channel}
            </if>
            <if test='regionName != null'>
                AND t6.regionName=#{regionName}
            </if>
            <if test='orgName != null'>
                AND t6.organizationName=#{orgName}
            </if>
            )
            ORDER BY updateTime DESC
        </if>
        <if test='queryType == 0'>
            UNION ALL
            (
            SELECT
            t2.id as claimCancelReportId,
            t2.current_state as claimCancelReportState,
            t2.current_result as claimCancelReportResult,
            t0.id AS claimId,
            t0.id AS id, t0.insuredId, t0.claimNo, t0.riskType, t0.riskTime, t0.riskDesc, t0.claimResult,
            t0.claimState,
            DATE_FORMAT(t0.create_time ,'%Y-%m-%d %H:%i:%s') AS reportTime,
            t4.productName AS productName, t7.planName AS planName,t3.policyNo AS policyNo,
            t3.personName AS insuredPersonName, t0.update_time AS updateTime,t1.customerAdminId as customerAdminId,
            (case when t0.id = null
            then t4.channel
            else t0.channel
            end) as channel,t4.companyId
            ,t4.id as productId
            ,t0.process_type
            ,t0.finishTime
            , t8.personName as applicantName
            , t6.hrOrgId
            , t0.finishState
            , t0.finishResult
            , t0.reminded
            , t0.expect_hasten_time
            , t0.timeout
            , t6.userName as customerAdminName
            , t0.expect_wait_day as expectCloseDay
            , t0.create_time as sortCreateTime
            ,t0.evaluation_status as evaluationStatus
            ,t0.evaluation_score as evaluationScore
            FROM sm_claim_cancel_report t2
            LEFT JOIN sm_claim t0 ON t0.claimNo = t2.claim_no
            LEFT JOIN sm_order_insured t3 ON t0.insuredId=t3.id
            LEFT JOIN sm_order t1 ON t3.fhOrderId=t1.fhOrderId
            LEFT JOIN sm_product t4 ON t4.id=productId
            LEFT JOIN auth_user t6 ON t6.userId=t1.customerAdminId AND t6.enabled_flag = 0
            LEFT JOIN sm_plan t7 ON t7.id=t1.planId
            LEFT JOIN sm_order_applicant t8 ON t3.fhOrderId=t8.fhOrderId
            WHERE 1 = 1
            <if test='userId != null'>
                AND t1.customerAdminId = #{userId}
            </if>
            <if test='agentId != null'>
                AND t1.agentId = #{agentId}
            </if>
            <if test='keyword != null'>
                AND ((t3.personName LIKE CONCAT(#{keyword},'%')) OR (t3.idNumber LIKE
                CONCAT(#{keyword},'%')) OR (t3.cellPhone LIKE CONCAT(#{keyword},'%'))
                OR (t3.policyNo LIKE CONCAT(#{keyword},'%'))
                )
            </if>

            AND t0.claimState = 'claimCancelReport' and t2.current_state = 'stepDataCheckByPic'

            <if test='channel != null'>
                AND t4.channel=#{channel}
            </if>
            <if test='regionName != null'>
                AND t6.regionName=#{regionName}
            </if>
            <if test='orgName != null'>
                AND t6.organizationName=#{orgName}
            </if>
            )
            ORDER BY reminded DESC, sortCreateTime ASC
        </if>
        <if test='queryType != 1 and queryType != 0'>
                <choose>
                    <when test="claimState == 'inProcess'">
                        ORDER BY t0.reminded DESC, t0.create_time DESC
                    </when>
                    <when test="claimState == 'stepFinish'">
                        ORDER BY t0.finishTime DESC
                    </when>
                    <otherwise>
                        ORDER BY t0.update_time DESC
                    </otherwise>
                </choose>

        </if>
    </select>

    <select id="listSmClaimsByInsuredIds" resultType="com.cfpamf.ms.insur.weixin.pojo.vo.claim.WxClaimCancelReportListVo">
        SELECT null as claimCancelReportId,
            null as claimCancelReportState ,
            null as claimCancelReportResult ,
            t0.id AS claimId,t0.id AS id, t0.insuredId, t0.claimNo, t0.riskType, t0.riskTime, t0.riskDesc, t0.claimResult,
            t0.claimState,
            DATE_FORMAT(t0.create_time ,'%Y-%m-%d %H:%i:%s') AS reportTime,
            t4.productName AS productName, t7.planName AS planName,t3.policyNo AS policyNo,
            t3.personName AS insuredPersonName, t0.update_time AS updateTime,t1.customerAdminId as customerAdminId,
            (case when t0.id = null
            then t4.channel
            else t0.channel
            end) as channel,t4.companyId
            ,t4.id as productId
            ,t0.process_type
        FROM sm_claim t0
            LEFT JOIN sm_order_insured t3 ON t0.insuredId=t3.id
            LEFT JOIN sm_order t1 ON t3.fhOrderId=t1.fhOrderId
            LEFT JOIN sm_product t4 ON t4.id=productId
            LEFT JOIN auth_user t6 ON t6.userId=t1.customerAdminId AND t6.enabled_flag = 0
            LEFT JOIN sm_plan t7 ON t7.id=t1.planId
        WHERE
            t0.insuredId in
        <foreach collection="insuredIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        ORDER BY t0.update_time DESC,t0.id desc
    </select>

    <select id="listWxClaimTodoList" resultType="com.cfpamf.ms.insur.weixin.pojo.vo.WxClaimListVo">
        SELECT * FROM (
        (
        SELECT t0.id AS id, t0.insuredId, t0.claimNo, t0.riskType, t0.riskTime, t0.riskDesc, t0.claimResult,
        t0.claimState,
        DATE_FORMAT(t0.create_time ,'%Y-%m-%d %H:%i:%s') AS reportTime,
        t4.productName AS productName, t7.planName AS planName,t3.policyNo AS policyNo,
        t3.personName AS insuredPersonName, t0.claimStateTime AS updateTime,
        CASE WHEN t0.claimState IN ('stepDataPrepare','stepDataPrepare2','stepDataPrepare3') THEN TIMESTAMPDIFF(DAY,
        t0.claimStateTime ,NOW())-3
        WHEN t0.claimState = 'stepPostExpress' THEN TIMESTAMPDIFF(DAY, t0.claimStateTime ,NOW())-3
        ELSE TIMESTAMPDIFF(DAY, t0.claimStateTime ,NOW())-2
        END AS expireDays,
        t0.channel as channel,
        t0.process_type as processType
        , t6.hrOrgId
        , t0.finishState
        , t0.finishResult
        , t0.reminded
        , t0.expect_hasten_time
        , t0.timeout
        , t6.userName as customerAdminName
        , t8.personName as applicantName
        , t8.personName as apptPersonName
        , t0.expect_wait_day as expectCloseDay
        FROM sm_claim t0
        LEFT JOIN sm_order_insured t3 ON t0.insuredId=t3.id
        LEFT JOIN sm_order t1 ON t3.fhOrderId=t1.fhOrderId
        LEFT JOIN sm_product t4 ON t4.id=productId
        LEFT JOIN sm_company t5 ON t5.id=t4.companyId
        LEFT JOIN auth_user t6 ON t6.userId=t1.customerAdminId AND t0.create_role = 'employee' AND t6.enabled_flag = 0
        LEFT JOIN sm_plan t7 ON t7.id=t1.planId
        LEFT JOIN sm_order_applicant t8 ON t8.fhOrderId=t1.fhOrderId
        WHERE 1 = 1
        <if test='userId != null'>
            AND t1.customerAdminId = #{userId}
        </if>
        <if test='agentId != null'>
            AND t1.agentId = #{agentId}
        </if>
        <if test='keyword != null'>
            AND ((t3.personName LIKE CONCAT('%',#{keyword},'%'))
                     OR (t8.personName LIKE CONCAT('%',#{keyword},'%'))
            OR (t3.policyNo LIKE CONCAT('%',#{keyword},'%'))
            )
        </if>
        AND (
        t0.reminded = 1
        and t0.claimState IN ('stepDataPrepare','stepDataPrepare2','stepDataPrepare3')
        )
        )
        <if test='picRole == null'>
            ) t1
        </if>
        <if test='picRole'>
            UNION
            (
            SELECT t0.id AS id, t0.insuredId, t0.claimNo, t0.riskType, t0.riskTime, t0.riskDesc, t0.claimResult,
            t0.claimState,
            DATE_FORMAT(t0.create_time ,'%Y-%m-%d %H:%i:%s') AS reportTime,
            t4.productName AS productName, t7.planName AS planName,t3.policyNo AS policyNo,
            t3.personName AS insuredPersonName, t0.claimStateTime AS updateTime,
            TIMESTAMPDIFF(DAY, t0.claimStateTime ,NOW())-2 AS expireDays,t0.channel as channel,
            t0.process_type as processType
            , t6.hrOrgId
            , t0.finishState
            , t0.finishResult
            , t0.reminded
            , t0.expect_hasten_time
            , t0.timeout
            , t6.userName as customerAdminName
            , t8.personName as applicantName
            , t8.personName as apptPersonName
            , t0.expect_wait_day as expectCloseDay
            FROM sm_claim t0
            LEFT JOIN sm_order_insured t3 ON t0.insuredId=t3.id
            LEFT JOIN sm_order t1 ON t3.fhOrderId=t1.fhOrderId
            LEFT JOIN sm_product t4 ON t4.id=productId
            LEFT JOIN sm_company t5 ON t5.id=t4.companyId
            LEFT JOIN auth_user t6 ON t6.userId=t1.customerAdminId AND t6.enabled_flag = 0
            LEFT JOIN sm_plan t7 ON t7.id=t1.planId
            LEFT JOIN sm_order_applicant t8 ON t8.fhOrderId=t1.fhOrderId
            WHERE 1 = 1
            <if test='keyword != null'>
                AND ((t3.personName LIKE CONCAT('%',#{keyword},'%'))
                OR (t8.personName LIKE CONCAT('%',#{keyword},'%'))
                OR (t3.policyNo LIKE CONCAT('%',#{keyword},'%'))
                )
            </if>
            AND (
            t0.claimState = 'stepDataCheckByPic'
            AND t6.regionName=#{regionName}
            AND t6.organizationName=#{orgName}
            AND t0.reminded = 1
            )
            )
            ) t1
        </if>
        ORDER BY t1.updateTime DESC
    </select>

    <select id="listWxFollowUpClaim" resultType="com.cfpamf.ms.insur.weixin.pojo.vo.WxClaimListVo">
        SELECT
            t0.id AS id,
            t0.insuredId,
            t0.claimNo,
            t0.riskType,
            t0.riskTime,
            t0.riskDesc,
            t0.claimResult,
            t0.claimState,
        t6.hrOrgId,
            t1.customerAdminId,
            DATE_FORMAT(t0.create_time ,'%Y-%m-%d %H:%i:%s') AS reportTime,
            t0.lastFollowUp,
            t4.productName AS productName,
            t7.planName AS planName,
            t3.policyNo AS policyNo,
            TIMESTAMPDIFF(DAY, t0.claimStateTime ,NOW()) AS expireDays,
            t3.personName AS insuredPersonName,
            t2.personName AS apptPersonName,
            t6.userName AS customerAdminName,
            t6.userMobile AS customerAdminMobile,
            t0.claimStateTime AS updateTime,
        (case when t0.id = null
        then t4.channel
        else t0.channel
        end) as channel,t4.companyId
        ,t0.process_type as processType
        ,t0.expect_wait_day as expectCloseDay
        FROM sm_claim t0
        LEFT JOIN sm_order_insured t3 ON t0.insuredId=t3.id
        LEFT JOIN sm_order_applicant t2 ON t2.fhOrderId=t3.fhOrderId
        LEFT JOIN sm_order t1 ON t3.fhOrderId=t1.fhOrderId
        LEFT JOIN sm_product t4 ON t4.id=productId
        LEFT JOIN sm_company t5 ON t5.id=t4.companyId
        LEFT JOIN auth_user t6 ON t6.userId=t1.customerAdminId and t6.enabled_flag =0
        LEFT JOIN sm_plan t7 ON t7.id=t1.planId
        WHERE 1 = 1
        AND t6.regionName=#{regionName}
        AND t6.organizationName=#{orgName}
        <if test="userId !=null">
            and t6.userId = #{userId}
        </if>
        <if test=" claimState=='unFinish' ">
            AND t0.claimState != 'stepFinish'
        </if>
        <if test=" claimState=='stepFinish' ">
            AND t0.claimState = 'stepFinish'
        </if>
        <if test='keyword != null'>
            AND (
            (t3.personName LIKE CONCAT(#{keyword},'%'))
            OR (t3.idNumber LIKE CONCAT(#{keyword},'%'))
            OR (t3.cellPhone LIKE CONCAT(#{keyword},'%'))
            OR (t3.policyNo LIKE CONCAT(#{keyword},'%'))
            OR (t2.personName LIKE CONCAT(#{keyword},'%'))
            )
        </if>
        ORDER BY expireDays DESC
    </select>

    <insert id="insertClaimFollowUp">
        INSERT INTO sm_claim_follow_up(claim_id, follow_up_detail, create_by, parent_follow_up_id, type, current_node, create_by_code, consult_type, current_progress, file_url)
        VALUES (#{claimId}, #{followUpDetail}, #{createBy}, #{parentFollowUpId}, #{type}, #{currentNode}, #{createByCode}, #{consultType}, #{currentNode}, #{fileUrlJoin});
    </insert>

    <select id="listClaimFollowUpListById" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmClaimFollowUpVO">
        SELECT t1.*, t2.wxImgUrl, t1.file_url as fileUrlJoin
        FROM sm_claim_follow_up t1
                 left join auth_user t2 on t1.create_by_code = t2.userId and t2.enabled_flag = 0
        WHERE claim_id = #{claimId} and t1.enabled_flag = 0
        ORDER BY create_time DESC
    </select>

    <select id="listClaimFollowUpListByIdList" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmClaimFollowUpVO">
        SELECT t1.*
        FROM sm_claim_follow_up t1
        WHERE t1.claim_id in
        <foreach collection="list" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        and t1.type = 'hasten' and t1.reply_state = '0'
        ORDER BY create_time DESC
    </select>


    <select id="getSmClaimById" resultType="com.cfpamf.ms.insur.weixin.pojo.vo.WxClaimDetailVO">
        SELECT t1.*, DATE_FORMAT(t1.create_time, '%Y-%m-%d %H:%i:%s') AS reportTime, t1.create_time as createTime
        FROM sm_claim t1
        WHERE t1.id = #{id} LIMIT 1
    </select>

    <select id="getSmClaimByInsuredId" resultType="com.cfpamf.ms.insur.weixin.pojo.vo.WxClaimDetailVO">
        SELECT t1.*, DATE_FORMAT(t1.create_time, '%Y-%m-%d %H:%i:%s') AS reportTime
        FROM sm_claim t1
        WHERE t1.insuredId = #{insuredId}
    </select>

    <insert id="insertSmClaim" parameterType="com.cfpamf.ms.insur.weixin.pojo.dto.WxClaimDTO"
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO sm_claim
        (claimNo, insuredId, riskType, riskTime, riskDesc, claimState, claimResult, claimStateTime, estimatedAmount,
         claim_risk_reason, risk_reason_accident_type,
         create_time, create_by, create_role, update_time, channel, malignant_tumour, process_type, settlement
         )
        VALUES (#{claimNo}, #{insuredId}, #{riskType}, #{riskTime}, #{riskDesc}, #{claimState}, #{claimResult},
                CURRENT_TIMESTAMP(), #{estimatedAmount}, #{claimRiskReason}, #{riskReasonAccidentType},
                CURRENT_TIMESTAMP(), #{createBy}, #{createRole}, CURRENT_TIMESTAMP(), #{channel}, #{malignantTumour}, #{processType}, #{settlement}
                )
    </insert>


    <insert id="insertImportSmClaim" parameterType="com.cfpamf.ms.insur.weixin.pojo.dto.WxClaimDTO"
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO sm_claim
        (claimNo, insuredId, riskType, riskTime, riskDesc, claimState, claimResult, claimStateTime, estimatedAmount,
         claim_risk_reason, risk_reason_accident_type,
         create_time, create_by, create_role, update_time, channel, malignant_tumour, process_type, settlement)
        VALUES (#{claimNo}, #{insuredId}, #{riskType}, #{riskTime}, #{riskDesc}, #{claimState}, #{claimResult},
                CURRENT_TIMESTAMP(), #{estimatedAmount}, #{claimRiskReason}, #{riskReasonAccidentType},
                #{importCreateTime}, #{createBy}, #{createRole}, CURRENT_TIMESTAMP(), #{channel}, #{malignantTumour}, #{processType}, #{settlement})
    </insert>

    <update id="updateClaimStatus">
        UPDATE sm_claim
        SET claimState = #{step.sCode},
        claimResult = #{step.sName}
        <if test='step.sCode == "stepToPay" and oCode == "dataCheckPassAndMailEicBySafesCenter"'>
            ,dataTime = #{oTime}
        </if>
        <if test='step.sCode == "stepDataCheckBySafesCenter"'>
            ,safesCenterApprovalTimes = safesCenterApprovalTimes+1
        </if>
        ,claimStateTime = CURRENT_TIMESTAMP()
        ,update_time = CURRENT_TIMESTAMP()
        ,update_by = #{updateBy}
        , expect_hasten_time = #{expectHastenTime}
        , timeout = #{expectHastenTime}
        , hasten_count = 0
        , reminded = 0
        WHERE id = #{id}
    </update>

    <update id="updateClaimNote">
        UPDATE sm_claim
        SET note = #{note}
        WHERE id = #{id}
    </update>

    <update id="updateClaimSettlement">
        UPDATE sm_claim
        SET settlement = #{settlement}
        WHERE id = #{id}
    </update>

    <update id="updateClaimRiskType">
        UPDATE sm_claim
        SET riskType = #{riskType}
        WHERE id = #{id}
    </update>

    <update id="updateClaimInfo">
        UPDATE sm_claim
        SET riskType = #{riskType},
        riskTime=#{riskTime},
        claim_risk_reason = #{claimRiskReason},
        risk_reason_accident_type = #{riskReasonAccidentType},
        riskDesc=#{riskDesc}
        <if test="medicalInsur!=null">
            ,medicalInsur=#{medicalInsur}
        </if>
        <if test="otherInsur!=null">
            ,otherInsur=#{otherInsur}
        </if>
        <if test="estimatedAmount!=null and estimatedAmount>0">
            ,estimatedAmount=#{estimatedAmount}
        </if>
        <if test="visitingOccupation != null and visitingOccupation != ''">
            , visiting_occupation = #{visitingOccupation}
        </if>
        <if test="visitingDate != null and visitingDate != ''">
            , visiting_date = #{visitingDate}
        </if>
        <if test="visitingHospital != null and visitingHospital != ''">
            , visiting_hospital = #{visitingHospital}
        </if>
        <if test="visitingHospitalName != null and visitingHospitalName != ''">
            , visiting_hospital_name = #{visitingHospitalName}
        </if>
        <if test="accidentTypeJoins != null and accidentTypeJoins != ''">
            , accidentTypeJoin = #{accidentTypeJoins}
        </if>
        WHERE id = #{id}


    </update>

    <update id="updateReportClaimRiskTime">
        UPDATE sm_report_claim
        SET claimRiskTime = #{claimRiskTime}
        WHERE claimId = #{claimId}
    </update>

    <update id="updateFinishInfo">
        UPDATE sm_claim
        SET
        finishTime=#{dto.oTime}
        ,finishState=#{dto.oCode}
        ,finishResult=#{dto.oName}
        ,update_by=#{updateBy}
        , finish_type = #{dto.finishType}
        <if test='dto.oType == 1'>
            ,payMoney=#{dto.oValue}
        </if>
        <if test='dto.oType != 1'>
            ,payMoney=null
        </if>
        <if test='dto.evaluationStatus!=null'>
            ,evaluation_status=#{dto.evaluationStatus}
            ,evaluation_score=#{dto.evaluationScore}
        </if>
        WHERE id = #{id}
    </update>

    <update id="updateProgress">
        UPDATE sm_claim_progress
        SET oCode=#{dto.oCode}
          , oName=#{dto.oName}
          , oType=#{dto.oType}
          , oValue=#{dto.oValue}
          , dataJson=#{dto.dataJson}
          , update_by=#{dto.createBy}
        WHERE claimId = #{claimId}
          AND oCode = #{finishState}
    </update>

    <select id="listSmClaimProgressList" resultType="com.cfpamf.ms.insur.admin.pojo.vo.ProgressVO">
        SELECT t1.id       AS progressId
             , t1.id
             , t1.claimId
             , t1.sCode
             , t1.sName
             , t1.oCode
             , t1.oName
             , t1.oType
             , t1.oValue
             , (CASE
                    WHEN t1.oCode = 'appeared' THEN t3.riskTime
                    WHEN (t1.sCode = 'stepPostExpress' and t3.expressTime != '0000-00-00 00:00:00') THEN t3.expressTime
                    ELSE t1.create_time
            END)           as create_time
             , t1.create_by
             , t1.update_time
             , t1.update_by
             , t1.enabled_flag
             , t1.dataJson
             , t1.settlement
             , t2.userName AS createBy
        FROM sm_claim_progress t1
                 LEFT JOIN auth_user t2 on t1.create_by = t2.userId
                 LEFT JOIN sm_claim t3 on t3.id = t1.claimId
        WHERE t1.claimId = #{claimId}
    </select>

    <insert id="insertProgress" parameterType="com.cfpamf.ms.insur.admin.pojo.dto.ProgressDTO"
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO sm_claim_progress
            (claimId, sCode, sName, oCode, oName, oType, oValue, dataJson, settlement, create_time, create_by)
        VALUES (#{claimId}, #{sCode}, #{sName}, #{oCode}, #{oName}, #{oType}, #{oValue}, #{dataJson}, #{settlement},
                #{oTime}, #{createBy})
    </insert>

    <update id="updateClaimAccidentTypes">
        UPDATE sm_claim
        SET accidentTypeJoin = #{accidentTypeJoin}
        <if test="medicalInsur!=null">
            ,medicalInsur=#{medicalInsur}
        </if>
        <if test="otherInsur!=null">
            ,otherInsur=#{otherInsur}
        </if>
        <if test="visitingHospital!=null">
            ,visiting_hospital=#{visitingHospital}
        </if>
        <if test="otherInsur!=null">
            ,visiting_hospital_name=#{visitingHospitalName}
        </if>
        <if test="otherInsur!=null">
            ,visiting_date=#{visitingDate}
        </if>
        <if test="visitingOccupation!=null">
            ,visiting_occupation=#{visitingOccupation}
        </if>
        WHERE id = #{claimId}
    </update>

    <select id="listSmClaimFileUnitByClaimIdReal" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmClaimFileUnitVO">
        SELECT *
        FROM sm_claim_file
        WHERE enabled_flag = 0
          AND claimId = #{claimId}
    </select>

    <select id="listSmClaimFileUnitByClaimFileIdListReal"
            resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmClaimFileUnitVO">
        SELECT *
        FROM sm_claim_file
        WHERE enabled_flag = 0
        and claimId = #{claimId}
        AND cfId in
        <foreach collection="claimFileIdList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <insert id="insertClaimFiles">
        insert INTO sm_claim_file(claimId, fileTypeCode, fileTypeName, fileUrl,new_flag, za_key, file_unique_code)
        VALUES
        <foreach collection="dtos" item="item" index="index" separator=",">
            (#{item.claimId}, #{item.fileTypeCode}, #{item.fileTypeName}, #{item.fileUrl},#{item.newFlag}, #{item.zaKey}, #{item.fileUniqueCode})
        </foreach>
    </insert>

    <insert id="updateClaimFileZaKey">
        UPDATE sm_claim_file
        SET za_key = #{zaKey}
        WHERE cfId = #{id}
    </insert>

    <update id="deleteClaimFiles">
        UPDATE sm_claim_file
        SET enabled_flag = 1
        WHERE claimId = #{claimId}
    </update>

    <select id="listSmClaimExpressByClaimId" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmClaimExpressVO">
        SELECT *
        FROM sm_claim_express
        WHERE enabled_flag = 0
          AND claimId = #{claimId}
    </select>

    <insert id="insertClaimExpress">
        insert INTO sm_claim_express(claimId, expressDate, expressCompanyName, expressNo, expressUrlJoin)
        VALUES (#{claimId}, #{expressDate}, #{expressCompanyName}, #{expressNo}, #{expressUrlJoin})
    </insert>

    <update id="deleteClaimExpress">
        UPDATE sm_claim_express
        SET enabled_flag = 1
        WHERE claimId = #{claimId}
    </update>

    <select id="listClaimSummaryByRegion" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmClaimRegionSummaryVO">
        SELECT
        <if test='startDate == null'>
            MIN(t5.create_time) AS fromDate,
        </if>
        <if test='startDate != null'>
            #{startDate} AS fromDate,
        </if>
        <if test='endDate == null'>
            NOW() AS toDate,
        </if>
        <if test='endDate != null'>
            #{endDate} AS toDate,
        </if>
        t0.orgName AS regionName,
        SUM(CASE WHEN t5.finishState IN ('payed','payRejected','claimCanceled') THEN 1 ELSE 0 END) AS finishCount,
        SUM(CASE WHEN t5.finishState = 'payRejected' THEN 1 ELSE 0 END) AS rejectCount,
        SUM(CASE WHEN t5.finishState IN ('claimCanceled') THEN 1 ELSE 0 END) AS cancelCount,
        SUM(CASE WHEN t5.finishState = 'payed' THEN 1 ELSE 0 END) AS payCount,
        SUM(CASE WHEN t5.finishState = 'payed'AND t5.riskType IN ('1','2','5','6') THEN 1 ELSE 0 END) AS payCount1,
        SUM(CASE WHEN t5.finishState = 'payed'AND t5.riskType = '4'THEN 1 ELSE 0 END) AS payCount2,
        SUM(CASE WHEN t5.finishState = 'payed'AND t5.riskType = '3' THEN 1 ELSE 0 END) AS payCount3,

        SUM(
        CASE WHEN t5.finishState IN ('payed','payRejected') AND t5.claimExpressTime > '0000-00-00' THEN
        TIMESTAMPDIFF(DAY, t5.claimExpressTime, t5.claimFinishTime)
        WHEN t5.finishState IN ('payed','payRejected') AND t5.claimDataTime >'0000-00-00' THEN TIMESTAMPDIFF(DAY,
        t5.claimDataTime, t5.claimFinishTime)
        ELSE 0 END)
        /SUM(CASE WHEN t5.finishState IN ('payed','payRejected') THEN 1 ELSE 0 END)
        AS avgTime,

        SUM(CASE WHEN t5.riskType IN ('1','2','5','6') AND t5.finishState = 'payed' THEN t5.payMoney ELSE 0 END)
        /SUM(CASE WHEN t5.riskType IN ('1','2','5','6') AND t5.finishState = 'payed' THEN 1 ELSE 0 END) AS avgPay1,
        SUM(CASE WHEN t5.riskType = '4' AND t5.finishState = 'payed' THEN t5.payMoney ELSE 0 END)
        /SUM(CASE WHEN t5.riskType = '4' AND t5.finishState = 'payed' THEN 1 ELSE 0 END) AS avgPay21,
        SUM(CASE WHEN t5.riskType = '3' AND t5.finishState = 'payed' THEN t5.payMoney ELSE 0 END)
        /SUM(CASE WHEN t5.riskType = '3' AND t5.finishState = 'payed' THEN 1 ELSE 0 END) AS avgPay31,
        SUM(t5.payMoney) AS totalPay,
        SUM(CASE WHEN t5.riskType IN ('1','2','5','6') THEN t5.payMoney ELSE 0 END) AS totalPay1,
        SUM(CASE WHEN t5.riskType = '4' THEN t5.payMoney ELSE 0 END) AS totalPay2,
        SUM(CASE WHEN t5.riskType = '3' THEN t5.payMoney ELSE 0 END) AS totalPay3

        FROM organization t0
        LEFT JOIN (
        SELECT t11.*, t16.cvgAmount,t2.orgName as regionName1
        FROM sm_report_claim t11 LEFT JOIN organization t2 on t11.regionCode=t2.orgCode

        LEFT JOIN sm_product_coverage t15 ON t15.productId = t11.productId AND t15.cvgType = 'main' AND t15.enabled_flag
        = 0
        LEFT JOIN sm_product_coverage_amount t16 ON t16.spcId = t15.spcId AND t16.planId = t11.planId AND
        t16.enabled_flag = 0
        ) t5 ON t5.regionName1 = t0.orgName
        <if test='startDate != null'>
            <![CDATA[ AND t5.claimFinishTime>=#{startDate} ]]>
        </if>
        <if test='endDate != null'>
            <![CDATA[ AND t5.claimFinishTime<=#{endDate} ]]>
        </if>
        <if test='startDateR != null'>
            <![CDATA[ AND t5.claimCreateTime>=#{startDateR} ]]>
        </if>
        <if test='endDateR != null'>
            <![CDATA[ AND t5.claimCreateTime<=#{endDateR} ]]>
        </if>
        WHERE t0.orgType=1 AND t0.orgName IS NOT NULL
        <if test='regionName != null'>
            AND t0.orgName = #{regionName}
        </if>
        GROUP BY
        t0.id
        ORDER BY
        t0.id ASC
    </select>

    <select id="listClaimRegionUnPayCount" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmClaimRegionSummaryVO">
        SELECT
        t1.regionName,
        COUNT(1) AS caseCount,
        SUM(CASE WHEN t1.finishState IS NULL THEN 1 ELSE 0 END) AS unPayCount,
        SUM(CASE WHEN t1.finishState IS NULL AND t1.riskType IN ('1','2','5','6') THEN 1 ELSE 0 END) AS unPayCount1,
        SUM(CASE WHEN t1.finishState IS NULL AND t1.riskType = '4' THEN 1 ELSE 0 END) AS unPayCount2,
        SUM(CASE WHEN t1.finishState IS NULL AND t1.riskType = '3' THEN 1 ELSE 0 END) AS unPayCount3,
        SUM(CASE WHEN t1.finishState IS NULL AND t1.riskType = '4' THEN t4.cvgAmount ELSE 0 END) AS totalUnPay2,
        SUM(CASE WHEN t1.finishState IS NULL AND t1.riskType = '3' THEN t4.cvgAmount ELSE 0 END) AS totalUnPay3
        FROM
        sm_report_claim t1
        LEFT JOIN sm_product_coverage t41
        ON t41.productId = t1.productId AND t41.cvgType = 'main' AND t41.enabled_flag = 0
        LEFT JOIN sm_product_coverage_amount t4
        ON t4.planId = t1.planId AND t4.spcId = t41.spcId AND t4.enabled_flag = 0
        WHERE
        1 = 1
        <if test='startDate != null'>
            <![CDATA[ AND t1.claimCreateTime>=#{startDate} ]]>
        </if>
        <if test='endDate != null'>
            <![CDATA[ AND t1.claimCreateTime<=#{endDate} ]]>
        </if>
        <if test='regionName != null'>
            AND t1.regionName = #{regionName}
        </if>
        GROUP BY t1.regionName
    </select>

    <select id="listClaimRegionUnPayCountByOrg" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmClaimRegionSummaryVO">
        SELECT
        t2.orgName as regionName,
        COUNT(1) AS caseCount,
        SUM(CASE WHEN t1.finishState IS NULL THEN 1 ELSE 0 END) AS unPayCount,
        SUM(CASE WHEN t1.finishState IS NULL AND t1.riskType IN ('1','2','5','6') THEN 1 ELSE 0 END) AS unPayCount1,
        SUM(CASE WHEN t1.finishState IS NULL AND t1.riskType = '4' THEN 1 ELSE 0 END) AS unPayCount2,
        SUM(CASE WHEN t1.finishState IS NULL AND t1.riskType = '3' THEN 1 ELSE 0 END) AS unPayCount3,
        SUM(CASE WHEN t1.finishState IS NULL AND t1.riskType = '4' THEN t4.cvgAmount ELSE 0 END) AS totalUnPay2,
        SUM(CASE WHEN t1.finishState IS NULL AND t1.riskType = '3' THEN t4.cvgAmount ELSE 0 END) AS totalUnPay3
        FROM
        sm_report_claim t1 LEFT JOIN organization t2 on t1.regionCode=t2.orgCode

        LEFT JOIN sm_product_coverage t41
        ON t41.productId = t1.productId AND t41.cvgType = 'main' AND t41.enabled_flag = 0
        LEFT JOIN sm_product_coverage_amount t4
        ON t4.planId = t1.planId AND t4.spcId = t41.spcId AND t4.enabled_flag = 0
        WHERE
        1 = 1
        <if test='startDate != null'>
            <![CDATA[ AND t1.claimCreateTime>=#{startDate} ]]>
        </if>
        <if test='endDate != null'>
            <![CDATA[ AND t1.claimCreateTime<=#{endDate} ]]>
        </if>
        <if test='regionName != null'>
            AND t2.orgName = #{regionName}
        </if>
        GROUP BY t2.orgName
    </select>

    <select id="listClaimSummaryByOrg" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmClaimOrgSummaryVO">
        SELECT
        <if test='startDate == null'>
            MIN(t5.claimCreateTime) AS fromDate,
        </if>
        <if test='startDate != null'>
            #{startDate} AS fromDate,
        </if>
        <if test='endDate == null'>
            NOW() AS toDate,
        </if>
        <if test='endDate != null'>
            #{endDate} AS toDate,
        </if>
        t1.orgName AS regionName,
        t0.orgName AS orgName,
        SUM(CASE WHEN t5.finishState IN ('payed','payRejected','claimCanceled') THEN 1 ELSE 0 END) AS finishCount,
        SUM(CASE WHEN t5.finishState = 'payRejected' THEN 1 ELSE 0 END) AS rejectCount,
        SUM(CASE WHEN t5.finishState IN ('claimCanceled') THEN 1 ELSE 0 END) AS cancelCount,
        SUM(CASE WHEN t5.finishState = 'payed' THEN 1 ELSE 0 END) AS payCount,
        SUM(CASE WHEN t5.finishState = 'payed'AND t5.riskType IN ('1','2','5','6') THEN 1 ELSE 0 END) AS payCount1,
        SUM(CASE WHEN t5.finishState = 'payed'AND t5.riskType = '4'THEN 1 ELSE 0 END) AS payCount2,
        SUM(CASE WHEN t5.finishState = 'payed'AND t5.riskType = '3' THEN 1 ELSE 0 END) AS payCount3,

        SUM(
        CASE WHEN t5.finishState IN ('payed','payRejected') AND t5.claimExpressTime >'0000-00-00' THEN
        TIMESTAMPDIFF(DAY, t5.claimExpressTime, t5.claimFinishTime)
        WHEN t5.finishState IN ('payed','payRejected') AND t5.claimDataTime >'0000-00-00' THEN TIMESTAMPDIFF(DAY,
        t5.claimDataTime, t5.claimFinishTime)
        ELSE 0 END)
        /SUM(CASE WHEN t5.finishState IN ('payed','payRejected') THEN 1 ELSE 0 END)
        AS avgTime,

        SUM(CASE WHEN t5.riskType IN ('1','2','5','6') AND t5.finishState = 'payed' THEN t5.payMoney ELSE 0 END)
        /SUM(CASE WHEN t5.riskType IN ('1','2','5','6') AND t5.finishState = 'payed' THEN 1 ELSE 0 END) AS avgPay1,
        SUM(CASE WHEN t5.riskType = '4' AND t5.finishState = 'payed' THEN t5.payMoney ELSE 0 END)
        /SUM(CASE WHEN t5.riskType = '4' AND t5.finishState = 'payed' THEN 1 ELSE 0 END) AS avgPay21,
        SUM(CASE WHEN t5.riskType = '3' AND t5.finishState = 'payed' THEN t5.payMoney ELSE 0 END)
        /SUM(CASE WHEN t5.riskType = '3' AND t5.finishState = 'payed' THEN 1 ELSE 0 END) AS avgPay31,
        SUM(t5.payMoney) AS totalPay,
        SUM(CASE WHEN t5.riskType IN ('1','2','5','6') THEN t5.payMoney ELSE 0 END) AS totalPay1,
        SUM(CASE WHEN t5.riskType = '4' THEN t5.payMoney ELSE 0 END) AS totalPay2,
        SUM(CASE WHEN t5.riskType = '3' THEN t5.payMoney ELSE 0 END) AS totalPay3

        FROM organization t0
        LEFT JOIN organization t1 ON t1.hrOrgId= t0.hrParentId
        LEFT JOIN (
        SELECT t11.*, t16.cvgAmount
        FROM sm_report_claim t11
        LEFT JOIN sm_product_coverage t15 ON t15.productId = t11.productId AND t15.cvgType = 'main' AND t15.enabled_flag
        = 0
        LEFT JOIN sm_product_coverage_amount t16 ON t16.spcId = t15.spcId AND t16.planId = t11.planId AND
        t16.enabled_flag = 0
        ) t5 ON t5.organizationName = t0.orgName
        <if test='startDate != null'>
            <![CDATA[ AND t5.claimFinishTime>=#{startDate} ]]>
        </if>
        <if test='endDate != null'>
            <![CDATA[ AND t5.claimFinishTime<=#{endDate} ]]>
        </if>
        <if test='startDateR != null'>
            <![CDATA[ AND t5.claimCreateTime>=#{startDateR} ]]>
        </if>
        <if test='endDateR != null'>
            <![CDATA[ AND t5.claimCreateTime<=#{endDateR} ]]>
        </if>
        WHERE t0.orgType=2 AND t1.orgName IS NOT NULL
        <if test='regionName != null'>
            AND t1.orgName = #{regionName}
        </if>
        <if test='orgName != null'>
            AND t0.orgName = #{orgName}
        </if>
        GROUP BY
        t0.id
        ORDER BY
        t0.id ASC, t1.id ASC
    </select>

    <select id="listClaimOrgUnPayCount" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmClaimOrgSummaryVO">
        SELECT
        t1.regionName,
        t1.organizationName AS orgName,
        COUNT(1) AS caseCount,
        SUM(CASE WHEN t1.finishState IS NULL THEN 1 ELSE 0 END) AS unPayCount,
        SUM(CASE WHEN t1.finishState IS NULL AND t1.riskType IN ('1','2','5','6') THEN 1 ELSE 0 END) AS unPayCount1,
        SUM(CASE WHEN t1.finishState IS NULL AND t1.riskType = '4' THEN 1 ELSE 0 END) AS unPayCount2,
        SUM(CASE WHEN t1.finishState IS NULL AND t1.riskType = '3' THEN 1 ELSE 0 END) AS unPayCount3,
        SUM(CASE WHEN t1.finishState IS NULL AND t1.riskType = '4' THEN t4.cvgAmount ELSE 0 END) AS totalUnPay2,
        SUM(CASE WHEN t1.finishState IS NULL AND t1.riskType = '3' THEN t4.cvgAmount ELSE 0 END) AS totalUnPay3
        FROM
        sm_report_claim t1
        LEFT JOIN sm_product_coverage t41
        ON t41.productId = t1.productId AND t41.cvgType = 'main' AND t41.enabled_flag = 0
        LEFT JOIN sm_product_coverage_amount t4
        ON t4.planId = t1.planId AND t4.spcId = t41.spcId AND t4.enabled_flag = 0
        WHERE
        1 = 1
        <if test='startDate != null'>
            <![CDATA[ AND t1.claimCreateTime>=#{startDate} ]]>
        </if>
        <if test='endDate != null'>
            <![CDATA[ AND t1.claimCreateTime<=#{endDate} ]]>
        </if>
        <if test='regionName != null'>
            AND t1.regionName = #{regionName}
        </if>
        <if test='orgName != null'>
            AND t1.organizationName = #{orgName}
        </if>
        GROUP BY t1.regionName, t1.organizationName
    </select>

    <select id="listClaimOrgUnPayCountByOrg" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmClaimOrgSummaryVO">
        SELECT
        t3.orgName as regionName,
        t1.organizationName AS orgName,
        COUNT(1) AS caseCount,
        SUM(CASE WHEN t1.finishState IS NULL THEN 1 ELSE 0 END) AS unPayCount,
        SUM(CASE WHEN t1.finishState IS NULL AND t1.riskType IN ('1','2','5','6') THEN 1 ELSE 0 END) AS unPayCount1,
        SUM(CASE WHEN t1.finishState IS NULL AND t1.riskType = '4' THEN 1 ELSE 0 END) AS unPayCount2,
        SUM(CASE WHEN t1.finishState IS NULL AND t1.riskType = '3' THEN 1 ELSE 0 END) AS unPayCount3,
        SUM(CASE WHEN t1.finishState IS NULL AND t1.riskType = '4' THEN t4.cvgAmount ELSE 0 END) AS totalUnPay2,
        SUM(CASE WHEN t1.finishState IS NULL AND t1.riskType = '3' THEN t4.cvgAmount ELSE 0 END) AS totalUnPay3
        FROM
        sm_report_claim t1 LEFT JOIN organization t2 on t1.organizationCode=t2.orgCode
        LEFT JOIN organization t3 ON t3.hrOrgId= t2.hrParentId
        LEFT JOIN sm_product_coverage t41
        ON t41.productId = t1.productId AND t41.cvgType = 'main' AND t41.enabled_flag = 0
        LEFT JOIN sm_product_coverage_amount t4
        ON t4.planId = t1.planId AND t4.spcId = t41.spcId AND t4.enabled_flag = 0
        WHERE
        1 = 1
        <if test='startDate != null'>
            <![CDATA[ AND t1.claimCreateTime>=#{startDate} ]]>
        </if>
        <if test='endDate != null'>
            <![CDATA[ AND t1.claimCreateTime<=#{endDate} ]]>
        </if>
        <if test='regionName != null'>
            AND t3.orgName = #{regionName}
        </if>
        <if test='orgName != null'>
            AND t1.organizationName = #{orgName}
        </if>
        GROUP BY t3.orgName, t1.organizationName
    </select>

    <select id="listClaimProductUnPayCount" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmClaimProductSummaryVO">
        SELECT
        t1.planId,
        COUNT(1) AS caseCount,
        SUM( CASE WHEN t1.finishState IS NULL THEN 1 ELSE 0 END) AS unPayCount,
        SUM( CASE WHEN t1.finishState IS NULL AND t1.riskType IN ('1','2','5','6') THEN 1 ELSE 0 END) AS unPayCount1,
        SUM( CASE WHEN t1.finishState IS NULL AND t1.riskType = '4' THEN 1 ELSE 0 END) AS unPayCount2,
        SUM( CASE WHEN t1.finishState IS NULL AND t1.riskType = '3' THEN 1 ELSE 0 END) AS unPayCount3
        FROM
        sm_report_claim t1
        WHERE
        t1.planId > 0
        <if test='startDate != null'>
            <![CDATA[ AND t1.claimCreateTime>=#{startDate} ]]>
        </if>
        <if test='endDate != null'>
            <![CDATA[ AND t1.claimCreateTime<=#{endDate} ]]>
        </if>
        <if test='productName != null'>
            AND t1.productName LIKE CONCAT(#{productName},'%')
        </if>
        <if test='companyId != null'>
            AND t1.companyId = #{companyId}
        </if>
        GROUP BY t1.planId
    </select>

    <select id="listClaimAvgTime" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmClaimProductSummaryVO">
        SELECT
        t1.planId,
        t1.regionName,
        t1.organizationName,
        SUM(CASE WHEN TIMESTAMPDIFF(DAY, t1.orderStartTime, t1.claimRiskTime)>0 THEN 1 ELSE 0 END) AS riskCount,
        SUM(CASE WHEN TIMESTAMPDIFF(DAY, t1.orderStartTime, t1.claimRiskTime)>0 THEN TIMESTAMPDIFF(DAY,
        t1.orderStartTime, t1.claimRiskTime) ELSE 0 END) AS riskCycleTime
        FROM
        sm_report_claim t1
        WHERE
        t1.planId > 0
        <if test='startDate != null'>
            <![CDATA[ AND t1.claimRiskTime>=#{startDate} ]]>
        </if>
        <if test='endDate != null'>
            <![CDATA[ AND t1.claimRiskTime<=#{endDate} ]]>
        </if>
        <if test='productName != null'>
            AND t1.productName LIKE CONCAT(#{productName},'%')
        </if>
        <if test='companyId != null'>
            AND t1.companyId = #{companyId}
        </if>
        <if test="queryType == 0">
            GROUP BY t1.planId
        </if>
        <if test="queryType == 1">
            GROUP BY t1.regionName
        </if>
        <if test="queryType == 2">
            GROUP BY t1.regionName, t1.organizationName
        </if>
    </select>

    <select id="listClaimSummaryByProduct" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmClaimProductSummaryVO">
        SELECT
        <if test='startDate == null'>
            MIN(t5.claimCreateTime) AS fromDate,
        </if>
        <if test='startDate != null'>
            #{startDate} AS fromDate,
        </if>
        <if test='endDate == null'>
            NOW() AS toDate,
        </if>
        <if test='endDate != null'>
            #{endDate} AS toDate,
        </if>
        t1.id AS productId,
        t2.id AS planId,
        t1.productName,
        t2.planName,
        SUM(CASE WHEN t5.finishState IN ('payed','payRejected','claimCanceled') THEN 1 ELSE 0 END) AS finishCount,
        SUM(CASE WHEN t5.finishState = 'payRejected' THEN 1 ELSE 0 END) AS rejectCount,
        SUM(CASE WHEN t5.finishState ='claimCanceled' THEN 1 ELSE 0 END) AS cancelCount,
        SUM(CASE WHEN t5.finishState = 'payed' THEN 1 ELSE 0 END) AS payCount,
        SUM(CASE WHEN t5.finishState = 'payed'AND t5.riskType IN ('1','2','5','6') THEN 1 ELSE 0 END) AS payCount1,
        SUM(CASE WHEN t5.finishState = 'payed'AND t5.riskType = '4'THEN 1 ELSE 0 END) AS payCount2,
        SUM(CASE WHEN t5.finishState = 'payed'AND t5.riskType = '3' THEN 1 ELSE 0 END) AS payCount3,

        SUM(
        CASE WHEN t5.finishState IN ('payed','payRejected') AND t5.claimExpressTime >'0000-00-00' THEN
        TIMESTAMPDIFF(DAY, t5.claimExpressTime, t5.claimFinishTime)
        WHEN t5.finishState IN ('payed','payRejected') AND t5.claimDataTime >'0000-00-00' THEN TIMESTAMPDIFF(DAY,
        t5.claimDataTime, t5.claimFinishTime)
        ELSE 0 END)
        /SUM(CASE WHEN t5.finishState IN ('payed','payRejected') THEN 1 ELSE 0 END)
        AS avgTime,

        SUM(CASE WHEN TIMESTAMPDIFF(DAY, t5.orderStartTime, t5.claimRiskTime)>0 THEN TIMESTAMPDIFF(DAY,
        t5.orderStartTime,
        t5.claimRiskTime) ELSE 0 END)
        /SUM(CASE WHEN TIMESTAMPDIFF(DAY, t5.orderStartTime, t5.claimRiskTime)>0 THEN 1 ELSE 0 END) AS riskCycleTime,

        SUM(CASE WHEN t5.finishState IS NULL THEN 1 ELSE 0 END) AS unPayCount,

        SUM(CASE WHEN t5.riskType IN ('1','2','5','6') AND t5.finishState = 'payed' THEN t5.payMoney ELSE 0 END)
        /SUM(CASE WHEN t5.riskType IN ('1','2','5','6') AND t5.finishState = 'payed' THEN 1 ELSE 0 END) AS avgPay1,
        SUM(CASE WHEN t5.riskType = '4' AND t5.finishState = 'payed' THEN t5.payMoney ELSE 0 END) /SUM(CASE WHEN
        t5.riskType = '4' AND t5.finishState = 'payed' THEN 1 ELSE 0 END) AS avgPay21,
        SUM(CASE WHEN t5.riskType = '3' AND t5.finishState = 'payed' THEN t5.payMoney ELSE 0 END) /SUM(CASE WHEN
        t5.riskType = '3' AND t5.finishState = 'payed' THEN 1 ELSE 0 END) AS avgPay31,
        t112.cvgAmount AS avgPay2,
        t112.cvgAmount AS avgPay3,
        SUM(t5.payMoney) AS totalPay,
        SUM(CASE WHEN t5.riskType IN ('1','2','5','6') THEN t5.payMoney ELSE 0 END) AS totalPay1,
        SUM(CASE WHEN t5.riskType = '4' THEN t5.payMoney ELSE 0 END) AS totalPay2,
        SUM(CASE WHEN t5.riskType = '3' THEN t5.payMoney ELSE 0 END) AS totalPay3

        FROM sm_product t1
        LEFT JOIN sm_plan t2 ON t1.id = t2.productId
        LEFT JOIN sm_product_coverage t111 ON t111.productId = t1.id AND t111.cvgType = 'main' AND t111.enabled_flag = 0
        LEFT JOIN sm_product_coverage_amount t112 ON t112.productId = t1.id AND t111.spcId = t112.spcId AND t112.planId
        = t2.id AND t112.enabled_flag = 0
        LEFT JOIN sm_report_claim t5 ON t2.id = t5.planId
        <if test='startDate != null'>
            <![CDATA[ AND t5.claimFinishTime>=#{startDate} ]]>
        </if>
        <if test='endDate != null'>
            <![CDATA[ AND t5.claimFinishTime<=#{endDate} ]]>
        </if>
        LEFT JOIN (
        SELECT
        COUNT(1) AS productCaseCount,
        t1.productId
        FROM
        sm_report_claim t1
        WHERE 1 = 1
        <if test='startDate != null'>
            <![CDATA[ AND t1.claimFinishTime>=#{startDate} ]]>
        </if>
        <if test='endDate != null'>
            <![CDATA[ AND t1.claimFinishTime<=#{endDate} ]]>
        </if>
        <if test='productName != null'>
            AND t1.productName LIKE CONCAT(#{productName},'%')
        </if>
        <if test='companyId != null'>
            AND t1.companyId = #{companyId}
        </if>
        GROUP BY
        t1.productId
        ) t6 ON t6.productId = t1.id
        WHERE
        t1.enabled_flag = 0
        AND
        t2.enabled_flag = 0
        <if test='productName != null'>
            AND t1.productName LIKE CONCAT(#{productName},'%')
        </if>
        <if test='companyId != null'>
            AND t1.companyId = #{companyId}
        </if>
        GROUP BY
        t2.id
        ORDER BY
        t6.productCaseCount DESC, t1.id ASC, t2.id ASC
    </select>

    <select id="getClaimSummary" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmClaimSummaryVO">
        SELECT
        COUNT(DISTINCT t1.claimId) AS caseCount,
        SUM(CASE WHEN t1.finishState IN ('payed','payRejected','claimCanceled') THEN 1 ELSE 0 END) AS finishCount,
        SUM(CASE WHEN t1.finishState = 'payRejected' THEN 1 ELSE 0 END) AS rejectCount,
        SUM(CASE WHEN t1.finishState IN ('claimCanceled') THEN 1 ELSE 0 END) AS cancelCount,
        SUM(CASE WHEN t1.finishState = 'payed' THEN 1 ELSE 0 END) AS payCount,
        SUM(CASE WHEN t1.finishState = 'payed'AND t1.riskType IN ('1','2','5','6') THEN 1 ELSE 0 END) AS payCount1,
        SUM(CASE WHEN t1.finishState = 'payed'AND t1.riskType = '4'THEN 1 ELSE 0 END) AS payCount2,
        SUM(CASE WHEN t1.finishState = 'payed'AND t1.riskType = '3' THEN 1 ELSE 0 END) AS payCount3,

        SUM(
        CASE WHEN t1.finishState IN ('payed','payRejected') AND t1.claimExpressTime > '0000-00-00' THEN
        TIMESTAMPDIFF(DAY, t1.claimExpressTime, t1.claimFinishTime)
        WHEN t1.finishState IN ('payed','payRejected') AND t1.claimDataTime > '0000-00-00' THEN TIMESTAMPDIFF(DAY,
        t1.claimDataTime, t1.claimFinishTime)
        ELSE 0 END)
        /SUM(CASE WHEN t1.finishState IN ('payed','payRejected') THEN 1 ELSE 0 END)
        AS avgTime,

        SUM(CASE WHEN TIMESTAMPDIFF(DAY, t1.orderStartTime, t1.claimRiskTime)>0 THEN TIMESTAMPDIFF(DAY,
        t1.orderStartTime, t1.claimRiskTime) ELSE 0 END)
        /SUM(CASE WHEN TIMESTAMPDIFF(DAY, t1.orderStartTime, t1.claimRiskTime)>0 THEN 1 ELSE 0 END) AS riskCycleTime,
        SUM(CASE WHEN t1.claimId IS NOT NULL AND t1.finishState IS NULL THEN 1 ELSE 0 END) AS unPayCount,
        SUM(CASE WHEN t1.riskType IN ('1','2','5','6') AND t1.finishState = 'payed' THEN t1.payMoney ELSE 0 END)
        /SUM(CASE WHEN t1.riskType IN ('1','2','5','6') AND t1.finishState = 'payed' THEN 1 ELSE 0 END) AS avgPay1,
        SUM(CASE WHEN t1.riskType = '4' AND t1.finishState = 'payed' THEN t1.payMoney ELSE 0 END) /SUM(CASE WHEN
        t1.riskType = '4' AND t1.finishState = 'payed' THEN 1 ELSE 0 END) AS avgPay21,
        SUM(CASE WHEN t1.riskType = '3' AND t1.finishState = 'payed' THEN t1.payMoney ELSE 0 END) /SUM(CASE WHEN
        t1.riskType = '3' AND t1.finishState = 'payed' THEN 1 ELSE 0 END) AS avgPay31,
        SUM(CASE WHEN t1.riskType IN ('1','2','5','6') THEN t1.payMoney ELSE 0 END) AS totalPay1,
        SUM(CASE WHEN t1.riskType = '4' THEN t1.payMoney ELSE 0 END) AS totalPay2,
        SUM(CASE WHEN t1.riskType = '3' THEN t1.payMoney ELSE 0 END) AS totalPay3

        FROM sm_report_claim t1
        WHERE 1 = 1
        <if test='startDate != null'>
            <![CDATA[ AND t1.claimFinishTime>=#{startDate} ]]>
        </if>
        <if test='endDate != null'>
            <![CDATA[ AND t1.claimFinishTime<=#{endDate} ]]>
        </if>
        <if test='startDateR != null'>
            <![CDATA[ AND t1.claimCreateTime>=#{startDateR} ]]>
        </if>
        <if test='endDateR != null'>
            <![CDATA[ AND t1.claimCreateTime<=#{endDateR} ]]>
        </if>
        <if test='startDateK != null'>
            <![CDATA[ AND t1.claimRiskTime>=#{startDateK} ]]>
        </if>
        <if test='endDateK != null'>
            <![CDATA[ AND t1.claimRiskTime<=#{endDateK} ]]>
        </if>
        <if test='regionName != null'>
            AND t1.regionName = #{regionName}
        </if>
        <if test='orgName != null'>
            AND t1.organizationName = #{orgName}
        </if>
        <if test='productName != null'>
            AND t1.productName LIKE CONCAT('%',#{productName},'%')
        </if>
        <if test='companyId != null'>
            AND t1.companyId = #{companyId}
        </if>
    </select>

    <select id="getClaimSummaryByOrg" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmClaimSummaryVO">
        SELECT
        COUNT(DISTINCT t1.claimId) AS caseCount,
        SUM(CASE WHEN t1.finishState IN ('payed','payRejected','claimCanceled') THEN 1 ELSE 0 END) AS finishCount,
        SUM(CASE WHEN t1.finishState = 'payRejected' THEN 1 ELSE 0 END) AS rejectCount,
        SUM(CASE WHEN t1.finishState IN ('claimCanceled') THEN 1 ELSE 0 END) AS cancelCount,
        SUM(CASE WHEN t1.finishState = 'payed' THEN 1 ELSE 0 END) AS payCount,
        SUM(CASE WHEN t1.finishState = 'payed'AND t1.riskType IN ('1','2','5','6') THEN 1 ELSE 0 END) AS payCount1,
        SUM(CASE WHEN t1.finishState = 'payed'AND t1.riskType = '4'THEN 1 ELSE 0 END) AS payCount2,
        SUM(CASE WHEN t1.finishState = 'payed'AND t1.riskType = '3' THEN 1 ELSE 0 END) AS payCount3,
        SUM(
        CASE WHEN t1.finishState IN ('payed','payRejected') AND t1.claimExpressTime > '0000-00-00' THEN
        TIMESTAMPDIFF(DAY, t1.claimExpressTime, t1.claimFinishTime)
        WHEN t1.finishState IN ('payed','payRejected') AND t1.claimDataTime > '0000-00-00' THEN TIMESTAMPDIFF(DAY,
        t1.claimDataTime, t1.claimFinishTime)
        ELSE 0 END)
        /SUM(CASE WHEN t1.finishState IN ('payed','payRejected') THEN 1 ELSE 0 END)
        AS avgTime,

        SUM(CASE WHEN TIMESTAMPDIFF(DAY, t1.orderStartTime, t1.claimRiskTime)>0 THEN TIMESTAMPDIFF(DAY,
        t1.orderStartTime, t1.claimRiskTime) ELSE 0 END)
        /SUM(CASE WHEN TIMESTAMPDIFF(DAY, t1.orderStartTime, t1.claimRiskTime)>0 THEN 1 ELSE 0 END) AS riskCycleTime,
        SUM(CASE WHEN t1.claimId IS NOT NULL AND t1.finishState IS NULL THEN 1 ELSE 0 END) AS unPayCount,
        SUM(CASE WHEN t1.riskType IN ('1','2','5','6') AND t1.finishState = 'payed' THEN t1.payMoney ELSE 0 END)
        /SUM(CASE WHEN t1.riskType IN ('1','2','5','6') AND t1.finishState = 'payed' THEN 1 ELSE 0 END) AS avgPay1,
        SUM(CASE WHEN t1.riskType = '4' AND t1.finishState = 'payed' THEN t1.payMoney ELSE 0 END) /SUM(CASE WHEN
        t1.riskType = '4' AND t1.finishState = 'payed' THEN 1 ELSE 0 END) AS avgPay21,
        SUM(CASE WHEN t1.riskType = '3' AND t1.finishState = 'payed' THEN t1.payMoney ELSE 0 END) /SUM(CASE WHEN
        t1.riskType = '3' AND t1.finishState = 'payed' THEN 1 ELSE 0 END) AS avgPay31,
        SUM(CASE WHEN t1.riskType IN ('1','2','5','6') THEN t1.payMoney ELSE 0 END) AS totalPay1,
        SUM(CASE WHEN t1.riskType = '4' THEN t1.payMoney ELSE 0 END) AS totalPay2,
        SUM(CASE WHEN t1.riskType = '3' THEN t1.payMoney ELSE 0 END) AS totalPay3
        FROM sm_report_claim t1 LEFT JOIN organization t2 on t1.organizationCode=t2.orgCode
        LEFT JOIN organization t3 ON t3.hrOrgId= t2.hrParentId
        WHERE 1 = 1
        <if test='startDate != null'>
            <![CDATA[ AND t1.claimFinishTime>=#{startDate} ]]>
        </if>
        <if test='endDate != null'>
            <![CDATA[ AND t1.claimFinishTime<=#{endDate} ]]>
        </if>
        <if test='startDateR != null'>
            <![CDATA[ AND t1.claimCreateTime>=#{startDateR} ]]>
        </if>
        <if test='endDateR != null'>
            <![CDATA[ AND t1.claimCreateTime<=#{endDateR} ]]>
        </if>
        <if test='startDateK != null'>
            <![CDATA[ AND t1.claimRiskTime>=#{startDateK} ]]>
        </if>
        <if test='endDateK != null'>
            <![CDATA[ AND t1.claimRiskTime<=#{endDateK} ]]>
        </if>
        <if test='regionName != null'>
            AND t3.orgName = #{regionName}
        </if>
        <if test='orgName != null'>
            AND t1.organizationName = #{orgName}
        </if>
        <if test='productName != null'>
            AND t1.productName LIKE CONCAT('%',#{productName},'%')
        </if>
        <if test='companyId != null'>
            AND t1.companyId = #{companyId}
        </if>
    </select>

    <select id="listClaimRiskSummary" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmClaimRiskSummaryVO">
        SELECT
        <if test='queryType != null and queryType == 0'>
            t1.planId,
        </if>
        <if test='queryType != null and queryType == 1'>
            t1.regionName,
        </if>
        <if test='queryType != null and queryType == 2'>
            t1.regionName,
            t1.organizationName,
        </if>
        t2.qty * 1.00 / t1.qty AS riskRation,
        t2.qty AS riskQty,
        t1.qty AS appQty
        FROM (
        SELECT t1.planId,
        sum(qty) as qty
        <if test='queryType != null and queryType > 0'>
            ,t1.regionName,
            t1.organizationName
        </if>
        FROM sm_report_policy t1
        WHERE 1 = 1
        <if test='startDate != null'>
            <![CDATA[ AND t1.startTime >= #{startDate} ]]>
        </if>
        <if test='endDate != null'>
            <![CDATA[ AND t1.startTime <= #{endDate} ]]>
        </if>
        <if test='productName != null'>
            AND t1.productName LIKE CONCAT(#{productName}, '%')
        </if>
        <if test='regionName != null'>
            AND t1.regionName = #{regionName}
        </if>
        <if test='orgName != null'>
            AND t1.organizationName = #{orgName}
        </if>
        <if test='companyId != null'>
            AND t1.companyId = #{companyId}
        </if>
        <if test='queryType != null and queryType == 0'>
            GROUP BY t1.planId
        </if>
        <if test='queryType != null and queryType == 1'>
            GROUP BY t1.regionName
        </if>
        <if test='queryType != null and queryType == 2'>
            GROUP BY t1.regionName, t1.organizationName
        </if>
        ) t1
        LEFT JOIN (
        SELECT t1.planId,
        COUNT(1) AS qty
        <if test='queryType != null and queryType > 0'>
            ,t1.regionName,
            t1.organizationName
        </if>
        FROM sm_report_claim t1
        WHERE 1=1
        <if test='startDate != null'>
            <![CDATA[ AND t1.orderStartTime >= #{startDate} ]]>
        </if>
        <if test='endDate != null'>
            <![CDATA[ AND t1.orderStartTime <= #{endDate} ]]>
        </if>
        <if test='productName != null'>
            AND t1.productName LIKE CONCAT(#{productName}, '%')
        </if>
        <if test='regionName != null'>
            AND t1.regionName = #{regionName}
        </if>
        <if test='orgName != null'>
            AND t1.organizationName = #{orgName}
        </if>
        <if test='companyId != null'>
            AND t1.companyId = #{companyId}
        </if>
        <if test='queryType != null and queryType == 0'>
            GROUP BY t1.planId
        </if>
        <if test='queryType != null and queryType == 1'>
            GROUP BY t1.regionName
        </if>
        <if test='queryType != null and queryType == 2'>
            GROUP BY t1.regionName, t1.organizationName
        </if>
        ) t2
        ON
        <if test='queryType != null and queryType == 0'>
            t1.planId = t2.planId
        </if>
        <if test='queryType != null and queryType == 1'>
            t1.regionName = t2.regionName
        </if>
        <if test='queryType != null and queryType == 2'>
            t1.regionName = t2.regionName AND t1.organizationName = t2.organizationName
        </if>
    </select>

    <select id="listClaimRiskSummaryByOrg" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmClaimRiskSummaryVO">
        SELECT
        <if test='queryType != null and queryType == 0'>
            t1.planId,
        </if>
        <if test='queryType != null and queryType == 1'>
            t1.regionName,
        </if>
        <if test='queryType != null and queryType == 2'>
            t1.regionName,
            t1.organizationName,
        </if>
        t2.qty * 1.00 / t1.qty AS riskRation,
        t2.qty AS riskQty,
        t1.qty AS appQty
        FROM (
        SELECT t1.planId,
        sum(qty) as qty
        <if test='queryType != null and queryType > 0'>
            ,t3.orgName as regionName,
            t1.organizationName
        </if>
        FROM sm_report_policy t1 LEFT JOIN organization t2 on t1.organizationCode=t2.orgCode
        LEFT JOIN organization t3 ON t3.hrOrgId= t2.hrParentId
        WHERE 1 = 1
        <if test='startDate != null'>
            <![CDATA[ AND t1.startTime >= #{startDate} ]]>
        </if>
        <if test='endDate != null'>
            <![CDATA[ AND t1.startTime <= #{endDate} ]]>
        </if>
        <if test='productName != null'>
            AND t1.productName LIKE CONCAT(#{productName}, '%')
        </if>
        <if test='regionName != null'>
            AND t3.orgName = #{regionName}
        </if>
        <if test='orgName != null'>
            AND t1.organizationName = #{orgName}
        </if>
        <if test='companyId != null'>
            AND t1.companyId = #{companyId}
        </if>
        <if test='queryType != null and queryType == 0'>
            GROUP BY t1.planId
        </if>
        <if test='queryType != null and queryType == 1'>
            GROUP BY t3.orgName
        </if>
        <if test='queryType != null and queryType == 2'>
            GROUP BY t3.orgName, t1.organizationName
        </if>
        ) t1
        LEFT JOIN (
        SELECT t1.planId,
        COUNT(1) AS qty
        <if test='queryType != null and queryType > 0'>
            ,t3.orgName as regionName,
            t1.organizationName
        </if>
        FROM sm_report_claim t1 LEFT JOIN organization t2 on t1.organizationCode=t2.orgCode
        LEFT JOIN organization t3 ON t3.hrOrgId= t2.hrParentId
        WHERE 1=1
        <if test='startDate != null'>
            <![CDATA[ AND t1.orderStartTime >= #{startDate} ]]>
        </if>
        <if test='endDate != null'>
            <![CDATA[ AND t1.orderStartTime <= #{endDate} ]]>
        </if>
        <if test='productName != null'>
            AND t1.productName LIKE CONCAT(#{productName}, '%')
        </if>
        <if test='regionName != null'>
            AND t3.orgName = #{regionName}
        </if>
        <if test='orgName != null'>
            AND t1.organizationName = #{orgName}
        </if>
        <if test='companyId != null'>
            AND t1.companyId = #{companyId}
        </if>
        <if test='queryType != null and queryType == 0'>
            GROUP BY t1.planId
        </if>
        <if test='queryType != null and queryType == 1'>
            GROUP BY t3.orgName
        </if>
        <if test='queryType != null and queryType == 2'>
            GROUP BY t3.orgName, t1.organizationName
        </if>
        ) t2
        ON
        <if test='queryType != null and queryType == 0'>
            t1.planId = t2.planId
        </if>
        <if test='queryType != null and queryType == 1'>
            t1.regionName = t2.regionName
        </if>
        <if test='queryType != null and queryType == 2'>
            t1.regionName = t2.regionName AND t1.organizationName = t2.organizationName
        </if>
    </select>

    <select id="getClaimWxNotify" resultType="com.cfpamf.ms.insur.weixin.pojo.vo.WxClaimNotifyVO">
        SELECT t1.claimNo,
               t1.settlement,
               t2.personName AS customerName,
               t1.payMoney,
               t4.productName,
               t1.finishState,
               t1.claimState,
               t6.oValue     AS rejectReason,
               t5.userName   AS customerAdminName,
                t5.userId   AS customerAdminId,
               t5.wxOpenId,
               t1.process_type as processType
        FROM sm_claim t1
                 LEFT JOIN sm_order_insured t2
                           ON t2.id = t1.insuredId
                 LEFT JOIN sm_order t3
                           ON t3.fhOrderId = t2.fhOrderId
                 LEFT JOIN sm_product t4
                           ON t4.id = t3.productId
                 LEFT JOIN auth_user t5
                           ON (t5.userId = t3.customerAdminId OR t5.agentId = t3.agentId) AND t5.enabled_flag = 0
                 LEFT JOIN sm_claim_progress t6
                           ON t6.claimId = t1.id AND t6.oCode = t1.finishState
        WHERE t1.id = #{claimId}
          AND t5.wxOpenId IS NOT NULL
        ORDER BY t6.update_time DESC LIMIT 1
    </select>

    <select id="getMaxClaimNo" resultType="java.lang.String">
        SELECT MAX(claimNo)
        FROM sm_claim
    </select>

    <select id="getUnPayedMemo" resultType="com.cfpamf.ms.insur.admin.pojo.dto.ProgressDTO">
        select id,claimId,oCode,oValue,dataJson
        from sm_claim_progress
        where claimId in
        <foreach collection="claimIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        and oCode in( 'payRejected','claimCanceled')
    </select>

    <select id="listClaimOrderSummary" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmClaimOrderSummaryVO">
        SELECT
        t1.regionName,
        t1.organizationName AS orgName,
        t1.productId, t1.planId,
        SUM(t1.qty) AS totalQty,
        SUM(t1.totalAmount) AS orderAmount
        FROM sm_report_order t1
        WHERE 1 = 1
        <if test='startDate != null'>
            <![CDATA[ AND t1.rptDate >= #{startDate} ]]>
        </if>
        <if test='endDate != null'>
            <![CDATA[ AND t1.rptDate <= #{endDate} ]]>
        </if>
        <if test='productName != null'>
            AND t1.productName LIKE CONCAT(#{productName}, '%')
        </if>
        <if test='companyId != null'>
            AND t1.companyId = #{companyId}
        </if>
        <if test='regionName != null'>
            AND t1.regionName = #{regionName}
        </if>
        <if test='orgName != null'>
            AND t1.organizationName = #{orgName}
        </if>
        <if test='queryType != null and queryType == 0'>
            GROUP BY t1.productId, t1.planId
        </if>
        <if test='queryType != null and queryType == 1'>
            GROUP BY t1.regionName
        </if>
        <if test='queryType != null and queryType == 2'>
            GROUP BY t1.regionName, t1.organizationName
        </if>
    </select>

    <select id="listClaimOrderSummaryByOrg" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmClaimOrderSummaryVO">
        SELECT
        t3.orgName as regionName,
        t1.organizationName AS orgName,
        t1.productId, t1.planId,
        SUM(t1.qty) AS totalQty,
        SUM(t1.totalAmount) AS orderAmount
        FROM sm_report_order t1 LEFT JOIN organization t2 on t1.organizationCode=t2.orgCode
        LEFT JOIN organization t3 ON t3.hrOrgId= t2.hrParentId
        WHERE 1 = 1
        <if test='startDate != null'>
            <![CDATA[ AND t1.rptDate >= #{startDate} ]]>
        </if>
        <if test='endDate != null'>
            <![CDATA[ AND t1.rptDate <= #{endDate} ]]>
        </if>
        <if test='productName != null'>
            AND t1.productName LIKE CONCAT(#{productName}, '%')
        </if>
        <if test='companyId != null'>
            AND t1.companyId = #{companyId}
        </if>
        <if test='regionName != null'>
            AND t3.orgName = #{regionName}
        </if>
        <if test='orgName != null'>
            AND t1.organizationName = #{orgName}
        </if>
        <if test='queryType != null and queryType == 0'>
            GROUP BY t1.productId, t1.planId
        </if>
        <if test='queryType != null and queryType == 1'>
            GROUP BY t3.orgName
        </if>
        <if test='queryType != null and queryType == 2'>
            GROUP BY t3.orgName, t1.organizationName
        </if>
    </select>

    <select id="getWxClaimExpress" resultType="com.cfpamf.ms.insur.weixin.pojo.vo.WxClaimExpressReceiverVO">
        SELECT dataJson
        FROM sm_claim_progress
        WHERE claimId = #{claimId}
          AND oCode = 'postExpress'
        ORDER BY create_time DESC LIMIT 1
    </select>

    <select id="getWxClaimNeedData" resultType="com.cfpamf.ms.insur.weixin.pojo.vo.WxClaimNeedDataVO">
        SELECT oValue AS needData, settlement
        FROM sm_claim_progress
        WHERE claimId = #{claimId}
          AND oCode = 'moreData'
        ORDER BY create_time DESC LIMIT 1
    </select>

    <select id="getClaimCustomerAdminUser" resultType="com.cfpamf.ms.insur.admin.pojo.vo.AuthUserVO">
        SELECT t4.*
        FROM sm_claim t1
                 LEFT JOIN sm_order_insured t2 ON t1.insuredId = t2.id
                 LEFT JOIN sm_order t3 ON t2.fhOrderId = t3.fhOrderId
                 LEFT JOIN auth_user t4 ON t4.userId = t3.customerAdminId AND t4.enabled_flag = 0
        WHERE t1.id = #{claimId}
    </select>
    <select id="getClaimOrder" resultType="com.cfpamf.ms.insur.weixin.pojo.vo.WxClaimListVo">
        SELECT t0.id                                            AS id,
               t0.insuredId,
               t0.claimNo,
               t0.riskType,
               t0.riskTime,
               t0.riskDesc,
               t0.claimResult,
               t0.claimState,
               DATE_FORMAT(t0.create_time, '%Y-%m-%d %H:%i:%s') AS reportTime,
               t4.productName                                   AS productName,
               t7.planName                                      AS planName,
               t3.policyNo                                      AS policyNo,
               t3.personName                                    AS insuredPersonName,
               t0.update_time                                   AS updateTime,
               t1.customerAdminId                               as customerAdminId
        FROM sm_claim t0
                 LEFT JOIN sm_order_insured t3 ON t0.insuredId = t3.id
                 LEFT JOIN sm_order t1 ON t3.fhOrderId = t1.fhOrderId
                 LEFT JOIN sm_product t4 ON t4.id = productId
                 LEFT JOIN auth_user t6 ON t6.userId = t1.customerAdminId AND t6.enabled_flag = 0
                 LEFT JOIN sm_plan t7 ON t7.id = t1.planId
        where t0.claimNo = #{claimNo}
    </select>

    <select id="getClaimOrderByRiskTime" resultType="com.cfpamf.ms.insur.weixin.pojo.vo.WxClaimListVo">
        SELECT t0.id                                            AS id,
               t0.insuredId,
               t0.claimNo,
               t0.riskType,
               t0.riskTime,
               t0.riskDesc,
               t0.claimResult,
               t0.claimState,
               DATE_FORMAT(t0.create_time, '%Y-%m-%d %H:%i:%s') AS reportTime,
               t4.productName                                   AS productName,
               t7.planName                                      AS planName,
               t3.policyNo                                      AS policyNo,
               t3.personName                                    AS insuredPersonName,
               t0.update_time                                   AS updateTime,
               t1.customerAdminId                               as customerAdminId,
               t6.wxopenId as customerAdminWxOpenid,
               t6.userName as customerAdminName,
               t0.settlement as settlement
        FROM sm_claim t0
                 LEFT JOIN sm_order_insured t3 ON t0.insuredId = t3.id
                 LEFT JOIN sm_order t1 ON t3.fhOrderId = t1.fhOrderId
                 LEFT JOIN sm_product t4 ON t4.id = productId
                 LEFT JOIN auth_user t6 ON t6.userId = t1.customerAdminId AND t6.enabled_flag = 0
                 LEFT JOIN sm_plan t7 ON t7.id = t1.planId
        where 1=1
        <if test="startDate!=null">
            and t0.riskTime >= #{startDate}
        </if>
        <if test="endDate!= null">
            <![CDATA[ and t0.riskTime <= #{endDate} ]]>
        </if>
        aND t0.claimState IN ('stepDataPrepare', 'stepDataPrepare2', 'stepDataPrepare3','stepDataCheckByPic')
        and t4.long_insurance = 1
    </select>

    <update id="updateClaimExpressTime">
        UPDATE sm_claim
        SET expressTime = #{expressTime}
        WHERE id = #{claimId}
    </update>

    <update id="updateClaimProgressExpressTime">
        UPDATE sm_claim_progress
        SET dataJson = #{dataJson}
        WHERE id = #{progressId}
    </update>

    <update id="updateClaimLastFollowUp">
        UPDATE sm_claim
        SET lastFollowUp = #{followUpDetail}
        WHERE id = #{claimId}
    </update>

    <update id="updateClaim">
        update sm_claim
        set riskTime = #{riskTime},
        riskType = #{riskType},
        riskDesc = #{riskDesc},
        claim_risk_reason = #{claimRiskReason},
        risk_reason_accident_type = #{riskReasonAccidentType},
        estimatedAmount = #{estimatedAmount},
        otherInsur = #{otherInsur},
        medicalInsur = #{medicalInsur}
        <if test="visitingOccupation != null and visitingOccupation != ''">
            , visiting_occupation = #{visitingOccupation}
        </if>
        <if test="visitingDate != null and visitingDate != ''">
            , visiting_date = #{visitingDate}
        </if>
        <if test="visitingHospital != null and visitingHospital != ''">
            , visiting_hospital = #{visitingHospital}
        </if>
        <if test="visitingHospitalName != null and visitingHospitalName != ''">
            , visiting_hospital_name = #{visitingHospitalName}
        </if>
        <if test="accidentTypeJoins != null and accidentTypeJoins != ''">
            , accidentTypeJoin = #{accidentTypeJoins}
        </if>
        WHERE id = #{id}
    </update>

    <update id="updateClaimRiskReason">
        update sm_claim
        set claim_risk_reason         = #{claimRiskReason},
            risk_reason_accident_type = #{riskReasonAccidentType}
        WHERE id = #{id}
    </update>

    <update id="updateClaimState">
        update sm_claim
        set active_state = #{state}
        WHERE claimNo = #{claimNo}
    </update>
    <update id="updateLastProgressDataJson">
        UPDATE sm_claim_progress
        SET dataJson = #{dataJson},
            update_time = #{oTime}
        WHERE id = (SELECT temp.id
                    FROM (SELECT id
                          FROM sm_claim_progress c
                          WHERE c.sCode = 'stepToPay'
                            AND c.claimId = #{claimId}
                          ORDER BY c.create_time DESC limit 1) temp limit 1
        )
    </update>
    <update id="updateZaProgress">
        UPDATE sm_claim_progress
        SET oCode=#{dto.oCode}
          , oName=#{dto.oName}
          , oType=#{dto.oType}
          , oValue=#{dto.oValue}
          <if test="dto.dataJson != null and dto.dataJson != ''">
              , dataJson= JSON_MERGE_PATCH(dataJson, #{dto.dataJson})
          </if>
        WHERE id = (SELECT temp.id
                    FROM (SELECT id
                          FROM sm_claim_progress c
                          WHERE c.sCode = 'stepToPay'
                            AND c.claimId = #{claimId}
                          ORDER BY c.id DESC limit 1) temp limit 1
            )
    </update>
    <update id="updateProgressDataJson">
        UPDATE sm_claim_progress
        SET dataJson = #{dataJson}
        WHERE id = #{progressId}
    </update>

    <select id="getByClaimId" resultType="com.cfpamf.ms.insur.admin.pojo.po.claim.SmClaim">
        SELECT id,
               claimNo,
               insuredId,
               riskType,
               payMoney,
               riskTime,
               riskDesc,
               dataTime,
               claimState,
               claimResult,
               finishState,
               finishResult,
               finishTime,
               note,
               create_time,
               create_by,
               update_time,
               update_by,
               enabled_flag,
               accidentTypeJoin,
               claimStateTime,
               settlement,
               safesCenterApprovalTimes,
               estimatedAmount,
               lastFollowUp,
               medicalInsur,
               otherInsur,
               risk_reason_accident_type,
               claim_risk_reason,
               active_state,
               channel,
               process_type,
               CASE

                   WHEN expressTime != '0000-00-00 00:00:00' THEN expressTime
                   ELSE NULL
                   END AS expressTime,
               expect_hasten_time,
               hasten_count,
               timeout,
               reminded,
               visiting_occupation,
               visiting_date,
               visiting_hospital,
               visiting_hospital_name,
                evaluation_status,
                evaluation_score
        FROM sm_claim
        WHERE id = #{claimId}
    </select>
    <select id="getByClaimNo" resultType="com.cfpamf.ms.insur.admin.pojo.po.claim.SmClaim">
        SELECT id,
               claimNo,
               insuredId,
               riskType,
               payMoney,
               riskTime,
               riskDesc,
               dataTime,
               claimState,
               claimResult,
               finishState,
               finishResult,
               finishTime,
               note,
               create_time,
               create_by,
               update_time,
               update_by,
               enabled_flag,
               accidentTypeJoin,
               claimStateTime,
               settlement,
               safesCenterApprovalTimes,
               estimatedAmount,
               lastFollowUp,
               medicalInsur,
               otherInsur,
               risk_reason_accident_type,
               claim_risk_reason,
               active_state,
               channel,
               CASE

                   WHEN expressTime != '0000-00-00 00:00:00' THEN expressTime
                   ELSE NULL
                   END AS expressTime,
               current_status_memo
        FROM sm_claim
        WHERE claimNo = #{claimNo}
    </select>
    <select id="selectClosedProgress" resultType="com.cfpamf.ms.insur.admin.pojo.vo.ProgressVO">
        SELECT id as progressId, dataJson
        FROM sm_claim_progress
        WHERE claimId = #{claimId}
          AND oCode = #{status}
        ORDER BY update_time DESC LIMIT 1
    </select>
    <select id="getLastClaimExpressByClaimId" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmClaimExpressVO">
        SELECT *
        FROM sm_claim_express
        WHERE claimId = #{claimId}
        ORDER BY update_time DESC LIMIT 1
    </select>
    <select id="getCompanyIdByInsuredId" resultType="java.lang.Integer">
        select sp.companyId
        from sm_order_insured soi
                 left join sm_order so on soi.fhOrderId = so.fhOrderId
                 left join sm_product sp on sp.id = so.productId
        where soi.id = #{insuredId}
    </select>
    <select id="getCompanyIdByClaimId" resultType="java.lang.Integer">
        select sp.companyId
        from sm_claim sc
                 left join sm_order_insured soi on sc.insuredId = soi.id
                 left join sm_order so on soi.fhOrderId = so.fhOrderId
                 left join sm_product sp on sp.id = so.productId
        where sc.id = #{claimId}

    </select>



    <select id="listImportValidateParams"
            resultType="com.cfpamf.ms.insur.weixin.pojo.vo.CaseClosedValidateImportVo">
        SELECT
        t1.`policyNo`
        , t1.`personName` as insuredName
        , t3.`productAttrCode`
        , t4.id as claimId
        , t4.`claimState`
        , t4.`channel` as claimChannel
        , t4.claimResult
        , t3.companyId
        , t4.create_time as claimCreateTime
        , t4.process_type
        , t4.finishState
        , t4.finishTime
        , t4.payMoney
        , t2.startTime
        , t2.endTime
        , t1.id as insuredId
        FROM
        `sm_order_insured` t1
        LEFT JOIN `sm_order` t2 on t1.`fhOrderId` = t2.`fhOrderId`
        LEFT JOIN `sm_product` t3 on t3.id = t2.`productId`
        LEFT JOIN `sm_claim` t4 on t4.`insuredId` = t1.id and t4.`claimState` NOT IN('stepFinish', 'claimCancelReport')
        WHERE
        t1.`enabled_flag` = 0
        and t3.`enabled_flag` = 0
        and t2.`enabled_flag` = 0
        and t1.`policyNo` in
        <foreach collection="list" item="item" close=")" open="(" separator=",">
            #{item}
        </foreach>
    </select>
    <select id="listStepGuideClaims" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmClaimVO">

        SELECT
        <include refid="smClaimColumn" />
        FROM sm_claim t0
        LEFT JOIN sm_claim t00 ON t00.insuredId=t0.insuredId <![CDATA[ AND t00.create_time<=t0.create_time ]]> AND
        t00.finishState = 'payed'
        LEFT JOIN sm_order_insured t3 ON t0.insuredId=t3.id
        LEFT JOIN sm_order t1 ON t3.fhOrderId=t1.fhOrderId
        LEFT JOIN sm_order_applicant t2 ON t2.fhOrderId=t1.fhOrderId
        LEFT JOIN sm_product t4 ON t4.id=t1.productId
        LEFT JOIN sm_company t5 ON t5.id=t4.companyId
        LEFT JOIN auth_user t6 ON t6.userId=t1.customerAdminId AND t6.enabled_flag = 0
        LEFT JOIN sm_plan t7 ON t7.id=t1.planId
        LEFT JOIN sm_agent t9 ON t9.agentId=t1.agentId
        LEFT JOIN (select claim_id,max(create_time) as lastFollowTime from sm_claim_follow_up group by claim_id) t10 on
        t0.id=t10.claim_id
        <where>
            <if test='fuzzyKey != null and fuzzyKey != ""'>
                AND ((t3.personName LIKE CONCAT(#{fuzzyKey},'%')) OR t2.personName LIKE CONCAT(#{fuzzyKey},'%') OR t3.policyNo = #{fuzzyKey})
            </if>
            AND t0.claimState='stepGuide'
        </where>
        GROUP BY t0.id
        ORDER BY t0.create_time DESC

    </select>
    <select id="listStepGuideClaimsHandled" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmClaimGuideHandledVO">
        SELECT
        t0.id AS id,
        t0.riskType,
        t0.claimNo,
        t0.riskTime,
        t0.riskDesc,
        t0.claimResult,
        t0.risk_reason_accident_type as riskReasonAccidentType ,
        t0.claim_risk_reason as claimRiskReason,
        DATE_FORMAT(t0.create_time ,'%Y-%m-%d %H:%i:%s') AS reportTime,
        t0.settlement,
        t0.estimatedAmount,
        t0.lastFollowUp,
        t0.medicalInsur,
        t0.otherInsur,
        CASE WHEN t0.expressTime != '0000-00-00 00:00:00' THEN t0.expressTime ELSE NULL END AS expressTime,
        t0.payMoney,
        t0.dataTime,
        t0.finishState,
        t0.finishResult,
        t0.finishTime,
        t0.note,
        t1.channel,
        t0.create_time AS createTime,
        t1.fhOrderId AS fhOrderId,
        t1.orderState AS orderState,
        t1.payStatus AS payStatus,
        t1.unitPrice * t1.qty AS totalAmount,
        t1.startTime AS startTime,
        t1.endTime AS endTime,
        t1.productId AS productId,
        t2.personName AS applicantPersonName,
        t2.idNumber AS aplicantIdNumber,
        t2.cellPhone AS applicantCellPhone,
        t2.email AS applicantEmail,
        t2.personGender AS applicantPersonGender,
        t2.birthday AS applicantBirthday,
        t3.policyNo AS policyNo,
        t3.appStatus AS appStatus,
        t3.personName AS insuredPersonName,
        t3.idNumber AS insuredIdNumber,
        t3.cellPhone AS insuredCellPhone,
        t3.email AS insuredEmail,
        t3.personGender AS insuredPersonGender,
        t3.birthday AS insuredBirthday,
        t3.relationship AS insuredRelationship,
        t3.downloadURL AS downloadURL,
        t4.productName AS productName,
        t4.companyId AS companyId,
        t5.companyName AS companyName,
        t6.userName AS recommendUserName,
        t6.userId AS recommendUserId,
        t6.userMobile AS recommendUserMobile,
        t6.regionName AS recommendRegionName,
        t6.organizationFullName AS recommendOrganizationName,
        t11.oValue as guideContent,
        t11.create_time as guideContent
        FROM sm_claim_progress t11
        LEFT join sm_claim t0 on t11.claimId = t0.id
        LEFT JOIN sm_order_insured t3 ON t0.insuredId=t3.id
        LEFT JOIN sm_order t1 ON t3.fhOrderId=t1.fhOrderId
        LEFT JOIN sm_order_applicant t2 ON t2.fhOrderId=t1.fhOrderId
        LEFT JOIN sm_product t4 ON t4.id=t1.productId
        LEFT JOIN sm_company t5 ON t5.id=t4.companyId
        LEFT JOIN auth_user t6 ON t6.userId=t1.customerAdminId AND t6.enabled_flag = 0
        <where>
            t11.create_by=#{settlement}
            <if test='fuzzyKey != null and fuzzyKey != ""'>
                AND ((t3.personName LIKE CONCAT(#{fuzzyKey},'%')) OR t2.personName LIKE CONCAT(#{fuzzyKey},'%') OR t3.policyNo = #{fuzzyKey})
            </if>
        AND t11.sCode = 'stepGuide'
        </where>
        ORDER BY t11.create_time DESC
    </select>

    <select id="getProductIdByClaimId" resultType="java.lang.Integer">
        select so.productId
        from sm_claim sc
                 left join sm_order_insured soi on sc.insuredId = soi.id
                 left join sm_order so on soi.fhOrderId = so.fhOrderId
        where sc.enabled_flag = 0 and soi.enabled_flag = 0 and so.enabled_flag = 0 and sc.id = #{claimId}
        limit 1
    </select>
    <select id="deduceByInsuredId"
            resultType="com.cfpamf.ms.insur.admin.service.claim.impl.ClaimDeduceContextParams">
        select so.productId, sp.channel, sp.companyId, soi.id as insuredId
        from  sm_order_insured soi
                 left join sm_order so on soi.fhOrderId = so.fhOrderId
                left join sm_product sp on sp.id = so.productId
        where sp.enabled_flag = 0 and soi.enabled_flag = 0 and so.enabled_flag = 0 and soi.id = #{insuredId}
        limit 1
    </select>

    <select id="deduceByClaimId"
            resultType="com.cfpamf.ms.insur.admin.service.claim.impl.ClaimDeduceContextParams">
        select so.productId, sp.channel, sp.companyId, soi.id as insuredId, sc.riskType
        from sm_claim sc
                  left join  sm_order_insured soi on sc.insuredId = soi.id
                  left join sm_order so on soi.fhOrderId = so.fhOrderId
                  left join sm_product sp on sp.id = so.productId
        where sp.enabled_flag = 0 and soi.enabled_flag = 0 and so.enabled_flag = 0 and sc.id = #{id}
        limit 1
    </select>

    <select id="listLoanCustomer" resultType="java.lang.String">
        select id_number from dwd_claim_cust_loan where
                                              id_number in
                                          <foreach collection="list" close=")" open="(" item="item" separator=",">
                                              #{item}
                                          </foreach>
    </select>

    <delete id="truncateLoanCustomer">
        delete from dwd_claim_cust_loan where 1=1
    </delete>

    <select id="getPolicyNoByClaimId" resultType="java.lang.String">
        select t1.policyNo
        from sm_order_insured t1
                 inner join sm_claim t2 on t1.id = t2.insuredId
        where t2.id = #{claimId}
          and t1.enabled_flag = 0
          and t2.enabled_flag = 0
    </select>

    <select id="queryUnClosedCaseByInsuredIdAndRiskType"
            resultType="com.cfpamf.ms.insur.admin.pojo.po.claim.SmClaim">
        select id,
               claimNo,
               insuredId,
               riskType,
               payMoney,
               riskTime,
               riskDesc,
               dataTime,
               claimState,
               claimResult,
               finishState,
               finishResult,
               finishTime,
               note,
               create_time,
               create_by,
               update_time,
               update_by,
               enabled_flag,
               accidentTypeJoin,
               claimStateTime,
               settlement,
               safesCenterApprovalTimes,
               estimatedAmount,
               lastFollowUp,
               medicalInsur,
               otherInsur,
               risk_reason_accident_type,
               claim_risk_reason,
               active_state,
               channel,
               CASE

                   WHEN expressTime != '0000-00-00 00:00:00' THEN expressTime
                   ELSE NULL
                   END AS expressTime
               , process_type
        from sm_claim
        where insuredId = #{insuredId}
          and riskType = #{riskType}
          and claimState != 'stepFinish'
          limit 1
    </select>

    <select id="listByInsuredId" resultType="com.cfpamf.ms.insur.admin.pojo.form.claim.ClaimUnreportClosedImportKeyVo">
        SELECT insuredId, finishTime, payMoney
        FROM sm_claim
        WHERE insuredId = #{insuredId}
          and finishState = #{finishState}
          and claimState = 'stepFinish'
        and enabled_flag = 0
    </select>

    <select id="listHastenList" resultType="com.cfpamf.ms.insur.admin.pojo.po.claim.SmClaim">
        select id, claimState from sm_claim where
        <![CDATA[ expect_hasten_time < now() ]]>
        and claimState in('stepDataPrepare2', 'stepDataCheckByPic', 'stepToPay', 'stepDataCheckBySafesCenter', 'stepPostExpress')
        <if test="start != null and limit != null">
            limit #{start}, #{limit}
        </if>
    </select>

    <select id="queryByInsuredId" resultType="com.cfpamf.ms.insur.admin.pojo.vo.claim.SmClaimHastenVo">

        select t1.id         as insuredId
             , t1.personName as insuredName
             , t2.customerAdminId
             , t3.userName   as customerAdminName
        from sm_order_insured t1
                 left join sm_order t2 on t1.fhOrderId = t2.fhOrderId
                 left join auth_user t3 on t3.userId = t2.customerAdminId
        where t1.id = #{insuredId}
          and t2.enabled_flag = 0
          and t3.enabled_flag = 0

    </select>

    <update id="repairZaClaimFileData">
        UPDATE `sm_claim_file` SET enabled_flag = 0 WHERE claimId = #{claimId} AND  za_key is not null
    </update>

    <update id="repairClaimFileData">
        UPDATE `sm_claim_file` SET enabled_flag = 1 WHERE claimId = #{claimId} AND  za_key is null
    </update>

    <update id="manualUpdateCurrentNode">
        UPDATE  `sm_claim` SET `claimState` = 'stepToPay', claimResult = '保司核赔' WHERE `id` = #{claimId}
    </update>

    <update id="manualDeleteNode">
        DELETE FROM `sm_claim_progress` WHERE `claimId` = #{claimId} AND id IN
            <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
    </update>

    <update id="manualUpdateProgress">
        UPDATE `sm_claim_progress` SET `oCode` = NULL , `oValue` = NULL WHERE `claimId` = #{claimId} AND id = #{id}
    </update>

    <update id="updateHastenTime">
        update sm_claim
        set expect_hasten_time = #{vo.expectHastenTime},
        <if test='state == "1" and opeType == "auto"' >
            hasten_count = ifNull(hasten_count, 0) + 1,
        </if>
        reminded = 1
        where id = #{vo.id}

    </update>
    <update id="updateReadHastenTime">
        update sm_claim
        set expect_hasten_time = #{expectHastenTime},
        reminded = 0
        where id = #{claimId}
    </update>
    <update id="updateExpectCloseDay">
        update sm_claim
        set expect_wait_day = #{expectWaitDay}
        where id = #{claimId}
    </update>

    <update id="tagFileByClaimId">
        update sm_claim_file
        set file_approve_node= JSON_ARRAY_APPEND(file_approve_node, '$', #{nodeCode})
        WHERE claimId = #{claimId}
          and !JSON_CONTAINS(file_approve_node, concat('[','"', #{nodeCode},'"', ']'), '$')
          and enabled_flag = 0
    </update>

    <update id="tagFile">
        update sm_claim_file
        set file_approve_node= #{node}
        WHERE
          enabled_flag = 0 and cfid in
        <foreach collection="list" item="item" separator="," open="(" close=")" >
            #{item}
        </foreach>

    </update>

    <update id="updateFollowUpStates">
        update sm_claim_follow_up set reply_state = 1 where follow_up_id = #{parentFollowUpId} and reply_state = 0
    </update>


    <select id="listSupplementFileByClaimId" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmClaimFileUnitVO">
        SELECT *
        FROM sm_claim_file
        WHERE claimId = #{claimId}
          and !JSON_CONTAINS(file_approve_node, concat('[','"', #{nodeCode},'"', ']'), '$')
          and enabled_flag = 0
    </select>

    <select id="selectByNeedMoreData" resultType="com.cfpamf.ms.insur.admin.pojo.vo.ProgressVO">
        select *
        from sm_claim_progress
        where claimId = #{claimId}
          and oCode = 'moreData'
        order by id desc
        limit 1
    </select>
    <select id="queryHistoryHandeledClaim" resultType="java.lang.Integer">
        SELECT distinct(t0.id)
        from `sm_claim` t0
        where t0.`process_type` in ('MAIL', 'ZA_API', 'MAIL_TEMPLATE')
          and t0.`claimState` not in ('stepFinish', 'stepAppear', 'stepReport', 'stepGuide');


    </select>
    <select id="queryByIdList" resultType="com.cfpamf.ms.insur.admin.pojo.po.claim.SmClaim">
        SELECT t0.process_type,
        t0.id AS id,
        t0.riskType,
        t0.claimNo,
        t0.riskTime,
        t0.riskDesc,
        t0.claimResult,
        t0.risk_reason_accident_type as riskReasonAccidentType,
        t0.claim_risk_reason as claimRiskReason,
        DATE_FORMAT(t0.create_time, '%Y-%m-%d %H:%i:%s') AS reportTime,
        t0.settlement,
        t0.estimatedAmount,
        t0.lastFollowUp,
        t0.medicalInsur,
        t0.otherInsur,
        CASE WHEN t0.expressTime != '0000-00-00 00:00:00' THEN t0.expressTime ELSE NULL END AS expressTime,
        t0.payMoney,
        t0.dataTime,
        t0.finishState,
        t0.finishResult,
        t0.finishTime,
        t0.note,
        t0.create_time AS createTime,
        t0.claimState
        from `sm_claim` t0
        where
        t0.id in
        <foreach collection="list" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>
    <select id="pageFollowupList" resultType="com.cfpamf.ms.insur.admin.pojo.vo.claim.SmClaimFollowUpListVo">

        select
        t2.settlement,
        t2.claimState as currentState,
        t5.userId as customerAdminId,
        t5.userName as customerAdminName,
        t3.policyNo,
        t2.claimNo,
        t6.personName as applicantName,
        t3.personName as insuredName,
        t1.create_time,
        t4.startTime,
        t4.endTime,
        t5.regionName,
        t5.organizationName as orgName,
        t7.productName,
        t2.id as claimId,
        t1.follow_up_id,
        t1.follow_up_detail,
        t11.follow_up_detail as reply,
        t9.name consultType,
        t11.create_by as replier,
        t11.create_time as replyTime,
        (case when t8.total_amount is not null then t8.total_amount else t4.unitPrice * t4.qty
        end) as amount
        from sm_claim_follow_up t1
        LEFT JOIN sm_claim_follow_up t11 on t1.`follow_up_id` =t11.`parent_follow_up_Id` and t11.`type` = 'reply'
        inner join sm_claim t2 on t1.claim_id = t2.id
        inner join sm_order_insured t3 on t2.insuredId = t3.id
        left join sm_order t4 on t3.fhorderId = t4.fhorderId
        left join sm_order_applicant t6 on t6.fhorderId = t4.fhorderId
        left join auth_user t5 on t4.customerAdminId = t5.userId and t5.enabled_flag = 0
        left join sm_product t7 on t7.id = t4.productId
        left join sm_order_item t8 on t3.fhOrderId = t8.fh_order_id and t3.idNumber = t8.id_number and
        t3.appStatus=t8.app_status
        LEFT JOIN `dictionary` t9 on t9.type in('claim-consult-closed','claim-consult-processing') and t1.consult_type = t9.code
        <where>
            and t1.type = 'hasten'
            and t1.reply_state = #{replyStatus}
            and t1.enabled_flag = 0
            <if test="policyNo != null and policyNo != ''">
                and t3.policyNo = #{policyNo}
            </if>

            <if test="insuredInfo != null and insuredInfo != ''">
                and (t3.personName like CONCAT('%',#{insuredInfo},'%')
                OR t3.idNumber like CONCAT('%',#{insuredInfo},'%')
                OR t3.cellPhone like CONCAT('%',#{insuredInfo},'%'))
            </if>

            <if test="applicantInfo != null and applicantInfo != ''">
                and (t6.personName like CONCAT('%',#{applicantInfo},'%')
                OR t6.idNumber like CONCAT('%',#{applicantInfo},'%')
                OR t6.cellPhone like CONCAT('%',#{applicantInfo},'%'))
            </if>

            <if test="policyNo != null and policyNo != ''">
                and t3.policyNo = #{policyNo}
            </if>

            <if test="customerAdminInfo != null and customerAdminInfo != ''">
                and (
                t4.customerAdminId = #{customerAdminInfo}
                OR t5.userName = #{customerAdminInfo}
                )
            </if>

            <if test="claimNo != null and claimNo != ''">
                and t2.claimNo = #{claimNo}
            </if>

            <if test="settlement != null and settlement != ''">
                and t2.settlement = #{settlement}
            </if>
        </where>

    </select>
    <select id="queryAllAiParamsByClaimId"
            resultType="com.cfpamf.ms.insur.admin.pojo.dto.claim.ClaimAiDescParams">
        select sco.companyName
        from sm_claim sc
                 left join  sm_order_insured soi on sc.insuredId = soi.id
                 left join sm_order so on soi.fhOrderId = so.fhOrderId
                 left join sm_product sp on sp.id = so.productId
                 left join sm_company sco on sco.id = sp.companyId
        where sp.enabled_flag = 0 and soi.enabled_flag = 0 and so.enabled_flag = 0 and sc.id = #{id} and sco.enabled_flag = 0
        limit 1
    </select>


    <update id="updateFileInfo">
        update sm_claim_file
        <set>
            <if test="fileApproveNode!=null and fileApproveNode != '' ">
                file_approve_node = #{fileApproveNode},
            </if>
            <if test="newFlag!=null">
                new_flag = #{newFlag},
            </if>
            <if test="fileTypeCode!=null and fileTypeCode != '' ">
                fileTypeCode = #{fileTypeCode},
            </if>
            <if test="fileTypeName!=null and fileTypeName != ''">
                fileTypeName = #{fileTypeName},
            </if>
        </set>
        where cfId = #{cfId}
    </update>

    <update id="updateProgressInfo">
        update sm_claim_progress
        <set>
            <if test="sCode!=null and sCode != '' ">
                sCode = #{sCode},
            </if>
            <if test="sName!=null and sName != '' ">
                sName = #{sName},
            </if>
            <if test="oCode!=null and oCode != '' ">
                oCode = #{oCode},
            </if>
            <if test="oName!=null and oName != '' ">
                oName = #{oName},
            </if>
            <if test="oName!=null and oName != '' ">
                oName = #{oName},
            </if>
            <if test="oValue!=null and oValue != '' ">
                oValue = #{oValue},
            </if>
            <if test="dataJson!=null and dataJson != '' ">
                dataJson = #{dataJson},
            </if>
        </set>
        where id = #{id}

    </update>
    <update id="updateHistory">
        update sm_claim set timeout = DATE_ADD(update_time, INTERVAL +15 DAY)
                          , update_time =  update_time
                        where timeout is null and claimState = 'stepToPay'
    </update>
    <update id="updateSafesCenterTimes">
        update sm_claim set safesCenterApprovalTimes = safesCenterApprovalTimes+1 where id = #{id}
    </update>

    <update id="updateExclude">
        update sm_claim set exclude_org_sign = #{state} where id = #{claimId}
    </update>
    <update id="updateCurrentNodeMemo">
        update sm_claim set current_status_memo = #{memo} where id = #{id}
    </update>



    <select id="queryEvaluationPage" resultType="com.cfpamf.ms.insur.admin.pojo.vo.claim.evaluation.SmClaimEvaluationPageVo">
        SELECT
            a.id,a.claim_id,c.policyNo,b.claimNo,b.settlement,
            e.productName,f.companyName,e.channel as channelName,
            b.finishTime,a.evaluation_score,a.create_time,a.operator_id,a.operator_name,
            a.operator_post_name,a.operator_region_name,a.operator_organization_name
        FROM sm_claim_evaluation a
        LEFT JOIN sm_claim b ON a.claim_id=b.id
        LEFT JOIN sm_order_insured c ON b.insuredId=c.id
        LEFT JOIN sm_order d ON c.fhOrderId=d.fhOrderId
        LEFT JOIN sm_product e ON d.productId=e.id
        LEFT JOIN sm_company f ON e.companyId=f.id
        WHERE 1=1
        <if test='query.evaluationDateStart != null'>
            <![CDATA[ AND a.create_time>=#{query.evaluationDateStart} ]]>
        </if>
        <if test='query.evaluationDateEnd != null'>
            <![CDATA[ AND a.create_time<#{query.evaluationDateEnd} ]]>
        </if>
        <if test='query.finishDateStart != null'>
            <![CDATA[ AND b.finishTime>=#{query.finishDateStart} ]]>
        </if>
        <if test='query.finishDateEnd != null'>
            <![CDATA[ AND b.finishTime<=#{query.finishDateEnd} ]]>
        </if>
        <if test="query.settlement != null and query.settlement !=''">
            AND b.settlement like CONCAT(#{query.settlement},'%')
        </if>
        <if test="query.orgName != null and query.orgName!=''">
            AND a.operator_organization_name=#{query.orgName}
        </if>
        <if test="query.regionName != null and query.regionName!=''">
            AND a.operator_region_name=#{query.regionName}
        </if>
        <if test="query.companyId !=null">
            AND f.id = #{query.companyId}
        </if>
        <if test="query.policyNo !=null and query.policyNo!=''">
            AND c.policyNo = #{query.policyNo}
        </if>
        <if test="query.evaluationScore !=null and query.evaluationScore!=''">
            AND a.evaluation_score = #{query.evaluationScore}
        </if>
        <if test="query.productId !=null">
            AND e.id = #{query.productId}
        </if>
        <if test='query.operatorId != null'>
            AND a.operator_id=#{query.operatorId}
        </if>
        order by a.create_time desc
    </select>
    <select id="listStepPrepareImportByInsuredId"
            resultType="com.cfpamf.ms.insur.admin.pojo.form.claim.ClaimStepPrepareImportKeyVo">
        SELECT insuredId, riskType, DATE_FORMAT(riskTime, '%Y-%m-%d') as riskTime
        FROM sm_claim
        WHERE insuredId = #{insuredId}
          and (finishState != 'claimCanceled' or finishState is null)
          and enabled_flag = 0
    </select>

</mapper>
