<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.BmsSyncRecordMapper">
    <select id="getLastSyncRecordBySyncType" resultType="com.cfpamf.ms.insur.admin.pojo.po.BmsSyncRecord">
      select * from bms_sync_record
      where sync_type= #{syncType}
        <if test='syncStatus != null'>
            and sync_status = #{syncStatus}
        </if>
      order by id desc limit 1
    </select>

    <select id="getSyncRecordBySyncType" resultType="com.cfpamf.ms.insur.admin.pojo.po.BmsSyncRecord">
        select * from bms_sync_record
        where sync_type= #{syncType}
        order by id desc limit <if test="size != null and size > 0">#{size}</if><if test="size == null or size == 0">48</if>
    </select>
</mapper>
