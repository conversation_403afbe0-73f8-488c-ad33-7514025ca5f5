<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.claim.SmClaimCancelReportMapper">

    <select id="getByClaimCancelReportId" resultType="com.cfpamf.ms.insur.weixin.pojo.vo.claim.WxClaimCancelReportVo">
        select t1.*, t2.id as claimId, t3.userName as createUser
        from sm_claim_cancel_report t1
                 left join sm_claim t2 on t1.claim_no = t2.claimNo
                 left join auth_user t3 on t1.create_by = t3.userId
        where t1.id = #{claimCancelReportId} and t3.enabled_flag = 0
    </select>

    <select id="getWxClaimCancelReportNotifyVoByClaimCancelReportId" resultType="com.cfpamf.ms.insur.weixin.pojo.vo.claim.WxClaimCancelReportNotifyVo">
        select t1.*,t2.settlement as settlement ,t2.id as claimId, t3.userName as createUser,t3.wxOpenId as createUserWxOpenId,
               t4.personName AS customerName, au.userName as customerAdminName
        from sm_claim_cancel_report t1
                 left join sm_claim t2 on t1.claim_no = t2.claimNo
                 left join auth_user t3 on t1.create_by = t3.userId
                 LEFT JOIN sm_order_insured t4 ON t4.id = t2.insuredId
                 LEFT JOIN sm_order t5 ON t4.fhOrderId = t5.fhOrderId
                 LEFT JOIN auth_user au ON ((au.userId=t5.customerAdminId AND t2.create_role = 'employee')
                                                OR (au.agentId = t5.agentId AND t2.create_role = 'agent')) AND au.enabled_flag = 0
        where t1.id = #{claimCancelReportId} and t3.enabled_flag = 0
    </select>
    <select id="search" resultType="com.cfpamf.ms.insur.admin.pojo.vo.claim.SmClaimCancelReportVo">
        SELECT
        sccr.id as claimCancelReportId,
        t0.settlement,
        t0.claimNo,
        t0.id as id,
        sccr.current_result as currentStateName ,
        sccr.current_state as currentStateCode ,
        t3.policyNo AS policyNo,
        sccr.cancel_report_cause AS cancelReportCause,
        sccr.create_time AS creatTime,
        t2.personName AS applicantPersonName,
        t6.userName AS recommendUserName,
        t6.userId AS recommendUserId,
        t1.startTime AS startTime,
        t1.endTime AS endTime,
        t1.unitPrice * t1.qty AS totalAmount,
        t6.regionName AS recommendRegionName,
        t4.productName AS productName,
        t7.planName AS planName,
        t6.organizationFullName AS recommendOrganizationName,
        t3.personName AS insuredPersonName,
        t4.companyId,
        t3.personGender AS insuredPersonGender,
        t3.birthday AS insuredBirthday,
        t0.riskTime,
        t4.productAttrCode,
        t3.idNumber as insuredIdNumber,
        t2.idNumber as applicantIdNumber
        FROM sm_claim_cancel_report sccr
        LEFT JOIN sm_claim t0 on t0.claimNo = sccr.claim_no
        LEFT JOIN sm_order_insured t3 ON t0.insuredId=t3.id
        LEFT JOIN sm_order t1 ON t3.fhOrderId=t1.fhOrderId
        LEFT JOIN sm_order_applicant t2 ON t2.fhOrderId=t1.fhOrderId
        LEFT JOIN sm_product t4 ON t4.id=t1.productId
        LEFT JOIN sm_company t5 ON t5.id=t4.companyId
        LEFT JOIN auth_user t6 ON t6.userId=t1.customerAdminId AND t6.enabled_flag = 0
        LEFT JOIN sm_plan t7 ON t7.id=t1.planId
        WHERE 1=1
        <if test="claimCancelReportState != null and claimCancelReportState != ''">
            AND sccr.current_state = #{claimCancelReportState}
        </if>
        <if test='reportDateStart != null'>
            <![CDATA[ AND t0.create_time>=#{reportDateStart} ]]>
        </if>
        <if test='reportDateEnd != null'>
            <![CDATA[ AND t0.create_time<=#{reportDateEnd} ]]>
        </if>
        <if test="settlement != null and settlement !=''">
            AND t0.settlement like CONCAT(#{settlement},'%')
        </if>
        <if test='riskDateStart != null'>
            <![CDATA[ AND t0.riskTime>=#{riskDateStart} ]]>
        </if>
        <if test='riskDateEnd != null'>
            <![CDATA[ AND t0.riskTime<=#{riskDateEnd} ]]>
        </if>
        <if test='finishDateStart != null'>
            <![CDATA[ AND t0.finishTime>=#{finishDateStart} ]]>
        </if>
        <if test='finishDateEnd != null'>
            <![CDATA[ AND t0.finishTime<=#{finishDateEnd} ]]>
        </if>
        <if test='claimNo != null'>
            AND t0.claimNo=#{claimNo}
        </if>
        <if test='policyNo != null'>
            AND t3.policyNo=#{policyNo}
        </if>
        <if test='custManager != null'>
            AND ((t6.userName LIKE CONCAT('%',#{custManager},'%')) OR (t6.userId LIKE
            CONCAT('%',#{custManager},'%')))
        </if>
        <if test='insuredPerson != null'>
            AND ((t3.personName LIKE CONCAT('%',#{insuredPerson},'%')) OR (t3.idNumber LIKE
            CONCAT('%',#{insuredPerson},'%')) OR (t3.cellPhone LIKE CONCAT('%',#{insuredPerson},'%')))
        </if>
        <if test='riskType != null'>
            AND t0.riskType=#{riskType}
        </if>
        <if test='finishState != null and finishState=="claimCanceled"'>
            AND t0.finishState IN ('claimCanceled')
        </if>
        <if test='finishState != null and finishState!="claimCanceled"'>
            AND t0.finishState=#{finishState}
        </if>
        <if test='regionName != null'>
            AND t6.regionName=#{regionName}
        </if>
        <if test='orgName != null'>
            AND t6.organizationFullName=#{orgName}
        </if>
        <if test='productId != null'>
            AND t1.productId=#{productId}
        </if>
        <if test='agentId != null'>
            AND t1.agentId=#{agentId}
        </if>
        <if test='claimId != null'>
            AND t0.id=#{claimId}
        </if>
        <if test='otherReimbursement != null and otherReimbursement=="medicalInsur"'>
            AND t0.medicalInsur=#{reimbursementValue}
        </if>
        <if test='otherReimbursement != null and otherReimbursement=="otherInsur"'>
            AND t0.otherInsur=#{reimbursementValue}
        </if>
        <if test='labelCode != null'>
            AND t0.id in (
            select d.claim_id from sm_claim_rule_label_detail d,sm_claim_rule_label_record r where d.claim_id = r.claim_id and d.batch_no = r.batch_no
            and d.label_code=#{labelCode}
            <if test="labelValueCode !=null">
                and d.value_code =#{labelValueCode}
            </if>
            and  d.`enabled_flag` =0 and r.`enabled_flag` =0
            )
        </if>

        GROUP BY sccr.id
        ORDER BY sccr.create_time DESC
    </select>

    <select id="searchWxClaimCancelReport"
            resultType="com.cfpamf.ms.insur.weixin.pojo.vo.claim.WxClaimCancelReportListVo">
        SELECT
        sccr.id as claimCancelReportId,
        sccr.current_result as claimCancelReportResult ,
        sccr.current_state as claimCancelReportState,
        t0.id AS claimId, t0.insuredId, t0.claimNo, t0.riskType, t0.riskTime, t0.riskDesc, t0.claimResult,
        t0.claimState,
        DATE_FORMAT(t0.create_time ,'%Y-%m-%d %H:%i:%s') AS reportTime,
        t4.productName AS productName, t7.planName AS planName,t3.policyNo AS policyNo,
        t3.personName AS insuredPersonName, t0.update_time AS updateTime,t1.customerAdminId as customerAdminId
        FROM sm_claim_cancel_report sccr
        LEFT JOIN sm_claim t0 on t0.claimNo = sccr.claim_no
        LEFT JOIN sm_order_insured t3 ON t0.insuredId=t3.id
        LEFT JOIN sm_order t1 ON t3.fhOrderId=t1.fhOrderId
        LEFT JOIN sm_product t4 ON t4.id=productId
        LEFT JOIN auth_user t6 ON t6.userId=t1.customerAdminId AND t6.enabled_flag = 0
        LEFT JOIN sm_plan t7 ON t7.id=t1.planId
        WHERE 1 = 1
        <if test='userId != null'>
            AND t1.customerAdminId = #{userId}
        </if>
        <if test='agentId != null'>
            AND t1.agentId = #{agentId}
        </if>
        <if test='keyword != null'>
            AND ((t3.personName LIKE CONCAT(#{keyword},'%')) OR (t3.idNumber LIKE
            CONCAT(#{keyword},'%')) OR (t3.cellPhone LIKE CONCAT(#{keyword},'%'))
            OR (t3.policyNo LIKE CONCAT(#{keyword},'%'))
            )
        </if>
        <if test="claimCancelReportState!= null and claimCancelReportState!=''">
            AND sccr.current_state = #{claimCancelReportState}
        </if>

        <if test='channel != null'>
            AND t4.channel=#{channel}
        </if>
        <if test='regionName != null'>
            AND t6.regionName=#{regionName}
        </if>
        <if test='orgName != null'>
            AND t6.organizationName=#{orgName}
        </if>
        ORDER BY sccr.create_time DESC
    </select>
    <select id="getOverdueDayClaimCancelReport"
            resultType="com.cfpamf.ms.insur.weixin.pojo.vo.claim.WxClaimCancelReportVo">
        select t1.*, t2.id as claimId, t3.userName as createUser
        from sm_claim_cancel_report t1
        left join sm_claim t2 on t1.claim_no = t2.claimNo
        left join auth_user t3 on t1.create_by = t3.userId
        where
        DATEDIFF (now(),t1.update_time) >= #{overdueDay}
        and t1.enabled_flag = 0
        and t1.current_state in
        <foreach collection="stateList" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
</mapper>
