<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.claim.SmClaimFinishNotifyMapper">

    <resultMap id="BaseResultMap" type="com.cfpamf.ms.insur.admin.pojo.po.claim.SmClaimFinishNotify">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="claimId" column="claim_id" jdbcType="INTEGER"/>
            <result property="riskAddress" column="risk_address" jdbcType="VARCHAR"/>
            <result property="riskTypeName" column="risk_type_name" jdbcType="VARCHAR"/>
            <result property="riskDesc" column="risk_desc" jdbcType="VARCHAR"/>
            <result property="rejectReason" column="reject_reason" jdbcType="VARCHAR"/>
            <result property="finishReportNo" column="finish_report_no" jdbcType="VARCHAR"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="enabledFlag" column="enabled_flag" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,claim_id,risk_address,
        risk_type_name,risk_desc,reject_reason,
        finishReportNo,update_by,update_time,
        create_time,enabled_flag
    </sql>
    <select id="maxNo" resultType="java.lang.String">
        select max(finish_report_no) from sm_claim_finish_notify
    </select>
</mapper>
