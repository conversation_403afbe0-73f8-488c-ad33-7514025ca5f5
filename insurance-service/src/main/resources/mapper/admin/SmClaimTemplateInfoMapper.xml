<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.claim.SmClaimTemplateInfoMapper">

    <resultMap id="BaseResultMap" type="com.cfpamf.ms.insur.admin.pojo.po.claim.SmClaimTemplateInfo">
        <id property="id" column="id" jdbcType="INTEGER"/>
        <result property="companyId" column="company_id" jdbcType="INTEGER"/>
        <result property="selectProduct" column="select_product" jdbcType="INTEGER"/>
        <result property="productId" column="product_id" jdbcType="INTEGER"/>
        <result property="riskType" column="risk_type" jdbcType="VARCHAR"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="enabledFlag" column="enabled_flag" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,company_id,select_product,
        product_id,risk_type,create_by,
        update_by,update_time,create_time,
        enabled_flag
    </sql>

    <sql id = "templateCondition">

        <choose>
            <when test="selectProduct == 1">
                and t1.product_type = #{productType}
                <choose>
                    <when test="productId != null">
                        and t1.product_id = #{productId}
                    </when>
                    <otherwise>
                        and t1.product_id is null
                    </otherwise>
                </choose>
            </when>
            <when test="selectProduct == 0">
                and t1.product_id is null
                and t1.product_type is null
            </when>
        </choose>
        and t1.enabled_flag = 0
        and t1.select_product = #{selectProduct}
    </sql>

    <update id="logicDelByIds">
        update sm_claim_template_info
        set enabled_flag = 1
        where id in
        <foreach collection="list" close=")" open="(" separator="," item="item">
            #{item}
        </foreach>
    </update>

    <select id="queryByGroup" resultType="java.lang.Integer">
        select  id from sm_claim_template_info
        where
        company_id = #{companyId}
        <choose>
            <when test="riskType != null and riskType != ''">
                and risk_type = #{riskType}
            </when>
            <otherwise>
                and risk_type is null
            </otherwise>
        </choose>
        <choose>
            <when test="productId != null and productId != ''">
                and
                product_id = #{productId}
            </when>
            <otherwise>
                and
                product_id is null
            </otherwise>
        </choose>
        <choose>
            <when test="productType != null and productType != '' ">
                and
                product_type = #{productType}
            </when>
            <otherwise>
                and
                product_type is null
            </otherwise>
        </choose>
        and enabled_flag = 0
    </select>





    <select id="pageQuery" resultType="com.cfpamf.ms.insur.admin.pojo.vo.claim.SmClaimTemplatePageVo">

        select * from (
        select t1.company_id, t4.companyName, t1.product_id, t3.productName, t1.risk_type,
        t1.product_type, t6.name as productTypeName, t1.batch_no,
        (case when t3.state = 2 then 0 else t3.state end) as state,
        t1.select_product
        from sm_claim_template_info t1
        left join sm_product t3 on t3.id = t1.product_id AND t3.enabled_flag=0
        left join sm_company t4 on t1.company_id = t4.id
        LEFT JOIN dictionary t6 on t1.product_type = t6.code and t6.type ='productGroup' AND t6.enabled_flag=0
        <where>
            <if test="companyId != null">
                and t1.company_id =#{companyId}
            </if>
            <if test="riskType !=null and riskType != ''">
                and t1.risk_type = #{riskType}
            </if>
            <if test="productId !=null ">
                and t1.product_id = #{productId}
            </if>
            <if test="productType !=null and productType !=''">
                and t1.product_type = #{productType}
            </if>
            <if test="fileTypeList != null and fileTypeList.size()>0">
                and t1.file_type_code in
                <foreach collection="fileTypeList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            and t1.enabled_flag = 0
            and t4.enabled_flag = 0
        </where>
        group by t1.company_id, t4.companyName, t1.product_id, t3.productName, t1.risk_type, t1.product_type
        ) temp order by temp.company_id, temp.state desc

    </select>
    <select id="sendFileList" resultType="com.cfpamf.ms.insur.admin.pojo.po.claim.SmClaimTemplateInfo">
        select *
        from sm_claim_template_info t1
        where
        t1.company_id =#{companyId}
        <include refid="templateCondition"/>
        and risk_type in
        <foreach collection="riskTypeList" separator="," open="(" close=")" item="item">
            #{item}
        </foreach>
        and enabled_flag = 0
    </select>

    <select id="listExistedTemplateFile"
            resultType="com.cfpamf.ms.insur.admin.pojo.po.claim.SmClaimTemplateInfo">
        select *
        from sm_claim_template_info
        where company_id = #{companyId}
        and risk_type in
        <foreach collection="riskTypeList" separator="," open="(" close=")" item="item">
            #{item}
        </foreach>
        <choose>
            <when test="selectProduct == 1">
                and product_type in
                <foreach collection="productTypeList" separator="," open="(" close=")" item="item">
                    #{item}
                </foreach>
                <choose>
                    <when test="productIdList != null and productIdList.size>0">
                        and product_id in
                        <foreach collection="productIdList" separator="," open="(" close=")" item="item">
                            #{item}
                        </foreach>
                    </when>
                    <otherwise>
                        and product_id is null
                    </otherwise>
                </choose>
            </when>
            <otherwise>
                and product_type is null
                and product_id is null
            </otherwise>
        </choose>
        <if test="fileTypeList != null and fileTypeList.size > 0">
            and file_type_code in
            <foreach collection="fileTypeList" separator="," open="(" close=")" item="item">
                #{item}
            </foreach>
        </if>
        and select_product = #{selectProduct}
        and enabled_flag = 0
    </select>

    <select id="pageChildren" resultType="com.cfpamf.ms.insur.admin.pojo.vo.claim.SmClaimTemplateChildrenVo">
        select t1.*, t5.name as fileTypeName from sm_claim_template_info t1
        left join dictionary t5 on t5.code = t1.file_type_code and t5.type='claim-template-file-type' and t5.enabled_flag=0
        where t1.company_id = #{companyId}
        <choose>
            <when test="riskType != null and riskType != ''">
                and t1.risk_type = #{riskType}
            </when>
            <otherwise>
                and t1.risk_type is null
            </otherwise>
        </choose>
        <choose>
            <when test="productId != null and productId != ''">
                and
                t1.product_id = #{productId}
            </when>
            <otherwise>
                and
                t1.product_id is null
            </otherwise>
        </choose>
        <choose>
            <when test="productType != null and productType != '' ">
                and
                t1.product_type = #{productType}
            </when>
            <otherwise>
                and
                t1.product_type is null
            </otherwise>
        </choose>
        and t1.enabled_flag = 0

    </select>
    <select id="detailByGroup" resultType="com.cfpamf.ms.insur.admin.pojo.vo.claim.SmClaimTemplateDetailAllVO">

        select t1.*, t4.companyName, t3.productName, t1.risk_type,
        t1.product_type, t6.name as productTypeName,
        (case when t3.state = 2 then 0 else t3.state end) as state
        from sm_claim_template_info t1
        left join sm_product t3 on t3.id = t1.product_id AND t3.enabled_flag=0
        left join sm_company t4 on t1.company_id = t4.id
        LEFT JOIN dictionary t6 on t1.product_type = t6.code and t6.type ='productGroup' AND t6.enabled_flag = 0
        <where>
            t1.company_id =#{companyId}
            <if test="riskType !=null and riskType != ''">
                and t1.risk_type = #{riskType}
            </if>
            <choose>
                <when test="productId != null and productId != ''">
                    and
                    t1.product_id = #{productId}
                </when>
                <otherwise>
                    and
                    t1.product_id is null
                </otherwise>
            </choose>
            <choose>
                <when test="productType != null and productType != ''">
                    and
                    t1.product_type = #{productType}
                </when>
                <otherwise>
                    and
                    t1.product_type is null
                </otherwise>
            </choose>
            and t1.enabled_flag = 0
            and t4.enabled_flag = 0

        </where>
    </select>

    <select id="detailWxRiskTypeByGroup" resultType="com.cfpamf.ms.insur.admin.pojo.vo.DictionaryVO">

        select distinct(t1.risk_type) as code
        from sm_claim_template_info t1
        <where>
            t1.company_id =#{companyId}
            <include refid="templateCondition"/>
            and t1.enabled_flag = 0
        </where>

    </select>

    <select id="pageGuide" resultType="com.cfpamf.ms.insur.weixin.pojo.vo.claim.WxClaimTemplateGuideVo">

        select * from (
        select t2.id, t2.companyName, t2.reportPhoneNo, t2.id as companyId, t2.companyLogoImageUrl
        from sm_claim_template_info t1
        left join sm_company t2 on t1.company_id = t2.id
        left join sm_product t3 on t1.product_id = t3.id and t3.enabled_flag = 0
        <where>
            <if test="companyName != null and companyName != ''">
                t2.companyName like CONCAT('%', #{companyName}, '%')
            </if>

            <if test="productName != null and productName != ''">
                and t3.productName like CONCAT('%', #{productName}, '%')
            </if>
            and t2.enabled_flag = 0
            and t1.enabled_flag = 0
        </where>
        group by t1.company_id
        ) temp order by temp.companyId
        limit #{start},#{size}

    </select>

    <select id="listGuideChild"
            resultType="com.cfpamf.ms.insur.weixin.pojo.vo.claim.WxClaimGuideChildrenParamsVo">

        select t1.select_product
             , t1.product_type
             , t1.product_id
             , t3.productName
             , t2.name as productTypeName
             , t1.company_id
        from sm_claim_template_info t1
                 left join dictionary t2 on t1.product_type = t2.code and t2.type = 'productGroup'  and t2.enabled_flag = 0
                 left join sm_product t3 on t3.id = t1.product_id and t3.enabled_flag = 0
                 left join sm_company t4 on t1.company_id = t4.id
        <where>
            t1.company_id = #{vo.companyId}
            <if test="vo.companyName != null and vo.companyName != ''">
               and t4.companyName like CONCAT('%', #{vo.companyName}, '%')
            </if>

            <if test="vo.productName != null and vo.productName != ''">
                and t3.productName like CONCAT('%', #{vo.productName}, '%')
            </if>
            and t4.enabled_flag = 0
            and t1.enabled_flag = 0
        </where>
        group by t1.select_product, t1.product_type, t1.product_id
        order by t1.product_id desc, t1.product_type desc, t1.select_product
        limit #{start}, 6

    </select>
    <select id="count" resultType="java.lang.Integer">
        select count(distinct(t2.id))
        from sm_claim_template_info t1
        left join sm_company t2 on t1.company_id = t2.id
        left join sm_product t3 on t1.product_id = t3.id and t3.enabled_flag = 0
        <where>
            t2.enabled_flag = 0
            and t1.enabled_flag = 0
            <if test="companyName != null and companyName != ''">
             and   t2.companyName like CONCAT('%', #{companyName}, '%')
            </if>

            <if test="productName != null and productName != ''">
                and t3.productName like CONCAT('%', #{productName}, '%')
            </if>

        </where>


    </select>

</mapper>
