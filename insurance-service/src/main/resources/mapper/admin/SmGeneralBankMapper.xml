<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.SmGeneralBankMapper">
    <!-- resultMap 映射 -->
    <resultMap id="SmGeneralBankResultMap" type="com.cfpamf.ms.insur.admin.pojo.SmGeneralBank">
        <id property="id" column="id" />
        <result property="bankName" column="bank_name" />
        <result property="bankCode" column="bank_code" />
        <result property="companyCode" column="company_code" />
        <result property="type" column="type" />
        <result property="createBy" column="create_by" />
        <result property="updateTime" column="update_time" javaType="java.util.Date" />
        <result property="createTime" column="create_time" javaType="java.util.Date" />
        <result property="enabledFlag" column="enabled_flag" />
    </resultMap>

    <insert id="saveBatch" parameterType="java.util.List">
        INSERT INTO sm_general_bank
        ( bank_name,bank_code,company_code,type )
        VALUES
        <choose>
            <when test="smGeneralBankList != null and smGeneralBankList.size() > 0">
                <foreach collection="smGeneralBankList" item="smGeneralBank" separator="," >
                    (#{smGeneralBank.bankName},#{smGeneralBank.bankCode},#{smGeneralBank.companyCode},#{smGeneralBank.type})
                </foreach>
            </when>
        </choose>
    </insert>
</mapper>