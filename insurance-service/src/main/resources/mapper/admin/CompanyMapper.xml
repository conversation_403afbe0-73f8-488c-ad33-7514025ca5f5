<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.CompanyMapper">


    <select id="listPageCompanys" resultType="com.cfpamf.ms.insur.admin.pojo.vo.CompanyVO" parameterType="com.cfpamf.ms.insur.admin.pojo.query.CompanyListQuery">
        SELECT t1.*, t1.update_time AS modifyTime
        FROM sm_company t1
        WHERE t1.enabled_flag=0
        <if test='params.keyWord != null and params.keyWord != ""'>
            AND (t1.companyName LIKE CONCAT('%',#{params.keyWord},'%') OR t1.companyAbbre LIKE CONCAT('%',#{params.keyWord},'%') OR
            t1.companyIdentifier LIKE CONCAT('%',#{params.keyWord},'%') OR t1.reportPhoneNo LIKE CONCAT('%',#{params.keyWord},'%'))
        </if>
        <if test='params.cooperationState != null and params.cooperationState != ""'>
            AND cooperation_state = #{params.cooperationState}
        </if>
        <if test='params.signDealCompany != null and params.signDealCompany != ""'>
            AND sign_deal_company = #{params.signDealCompany}
        </if>
        <if test="params.displayFlag!=null">
            and display_flag = #{params.displayFlag}
        </if>
        ORDER BY t1.sort asc, update_time desc
    </select>

    <select id="listCompanys" resultType="com.cfpamf.ms.insur.admin.pojo.vo.CompanyVO">
        SELECT t1.*, t1.update_time AS modifyTime
        FROM sm_company t1
        WHERE t1.enabled_flag=0
        <if test='keyword != null'>
            AND (t1.companyName LIKE CONCAT('%',#{keyword},'%') OR t1.companyAbbre LIKE CONCAT('%',#{keyword},'%') OR
            t1.companyIdentifier LIKE CONCAT('%',#{keyword},'%') OR t1.reportPhoneNo LIKE CONCAT('%',#{keyword},'%'))
        </if>
        ORDER BY t1.create_time DESC
    </select>

    <select id="getCompanysByPageOrderByProductCount" resultType="com.cfpamf.ms.insur.admin.pojo.vo.CompanyVO">
        SELECT t1.* FROM sm_company t1
        LEFT JOIN sm_product t2 ON t1.id = t2.companyId AND t2.state =1 AND t2.productAttrCode = 'person'
        WHERE t1.enabled_flag=0
        <if test='keyword != null'>
            AND (t1.companyName LIKE CONCAT('%',#{keyword},'%') OR t1.companyAbbre LIKE CONCAT('%',#{keyword},'%') OR
            t1.companyIdentifier LIKE CONCAT('%',#{keyword},'%') OR t1.reportPhoneNo LIKE CONCAT('%',#{keyword},'%'))
        </if>
        <if test='cooperationState != null'>
            AND cooperation_state = #{cooperationState}
        </if>
        <if test='signDealCompany != null'>
            AND sign_deal_company = #{signDealCompany}
        </if>
        ORDER BY t1.sort desc
    </select>

    <select id="getCompanyById" resultType="com.cfpamf.ms.insur.admin.pojo.vo.CompanyVO">
        SELECT * FROM sm_company WHERE id = #{id}
    </select>

    <insert id="insertCompany" useGeneratedKeys="true">
        INSERT INTO sm_company(companyName, companyAbbre, companyIdentifier, reportPhoneNo, companyLogoImageUrl, companyLogoThumbUrl, companyIntroduce, update_by, update_time, enabled_flag, sign_deal_company, cooperation_state, cooperation_start_time, cooperation_end_time, sort,display_flag)
        VALUES
            (#{companyName}, #{companyAbbre}, #{companyIdentifier}, #{reportPhoneNo}, #{companyLogoImageUrl}, #{companyLogoThumbUrl}, #{companyIntroduce}, #{modifyBy}, CURRENT_TIMESTAMP(), 0, #{signDealCompany}, #{cooperationState}, #{cooperationStartTime}, #{cooperationEndTime}, #{sort},#{displayFlag})
    </insert>

    <update id="updateCompany">
        UPDATE sm_company SET companyName= #{companyName}, companyAbbre= #{companyAbbre}, companyIdentifier= #{companyIdentifier}, reportPhoneNo= #{reportPhoneNo}, companyIntroduce= #{companyIntroduce},
                              companyLogoImageUrl=#{companyLogoImageUrl}, companyLogoThumbUrl=#{companyLogoThumbUrl}, update_by= #{modifyBy}, update_time=CURRENT_TIMESTAMP(),
                              sign_deal_company= #{signDealCompany}, cooperation_state = #{cooperationState}, cooperation_start_time = #{cooperationStartTime}, cooperation_end_time = #{cooperationEndTime}, sort = #{sort}
        ,display_flag=#{displayFlag}
        WHERE id = #{id}
    </update>

    <update id="deleteCompanyById">
        UPDATE sm_company SET enabled_flag=1, update_by=#{modifyBy}, update_time=CURRENT_TIMESTAMP() WHERE id = #{id}
    </update>

    <select id="listCompanyContacts" resultType="com.cfpamf.ms.insur.admin.pojo.vo.CompanyContactVO">
        SELECT t1.*, t2.companyName FROM sm_company_contact t1
        LEFT JOIN sm_company t2 ON t1.companyId = t2.id
        WHERE t1.enabled_flag = 0
        <if test="companyId != null">
            AND t1.companyId = #{companyId}
        </if>
        <if test="subCompanyCode != null">
            AND t1.subCompanyCode = #{subCompanyCode}
        </if>
    </select>

    <insert id="insertCompanyContact">
        INSERT INTO sm_company_contact (
            companyId,
            contactName,
            contactMobile,
            contactAddress,
            claimOtherMsg,
            subCompanyCode
        )
        VALUES(
                  #{companyId},
                  #{contactName},
                  #{contactMobile},
                  #{contactAddress},
                  #{claimOtherMsg},
                  #{subCompanyCode}
              )
    </insert>

    <update id="updateCompanyContact">
        UPDATE sm_company_contact
        SET companyId = #{companyId},
            contactName = #{contactName},
            contactMobile = #{contactMobile},
            contactAddress = #{contactAddress},
            claimOtherMsg = #{claimOtherMsg},
            subCompanyCode = #{subCompanyCode}
        WHERE ccId = #{ccId}
    </update>

    <update id="deleteCompanyContact">
        UPDATE sm_company_contact SET enabled_flag = 1 WHERE ccId = #{ccId}
    </update>

    <select id="selectByIds" resultType="com.cfpamf.ms.insur.admin.pojo.vo.CompanyVO">
        select * from sm_company
        where enabled_flag = 0
        <choose>
            <when test="ids != null and ids.size() > 0">
                and id in
                <foreach collection="ids" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </when>
            <otherwise>
                and id = -1
            </otherwise>
        </choose>
    </select>
    <select id="selectByCompanyName" resultType="com.cfpamf.ms.insur.admin.pojo.vo.CompanyVO">
        select * from sm_company
        where enabled_flag = 0
        and companyName = #{companyName}

    </select>
</mapper>