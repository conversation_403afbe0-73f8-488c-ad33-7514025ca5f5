<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.claim.SmClaimProgressTimelinessMapper">

<!--    <resultMap id="BaseResultMap" type="com.cfpamf.ms.insur.admin.pojo.po.claim.SmClaimProgressTimeliness">-->
<!--            <id property="id" column="id" jdbcType="INTEGER"/>-->
<!--            <result property="claimId" column="claim_id" jdbcType="INTEGER"/>-->
<!--            <result property="currentCode" column="current_code" jdbcType="VARCHAR"/>-->
<!--            <result property="currentCodeName" column="current_code_name" jdbcType="VARCHAR"/>-->
<!--            <result property="optionCode" column="option_code" jdbcType="VARCHAR"/>-->
<!--            <result property="optionCodeName" column="option_code_name" jdbcType="VARCHAR"/>-->
<!--            <result property="optionType" column="option_type" jdbcType="VARCHAR"/>-->
<!--            <result property="optionValue" column="option_value" jdbcType="VARCHAR"/>-->
<!--            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>-->
<!--            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>-->
<!--            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>-->
<!--            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>-->
<!--            <result property="enabledFlag" column="enabled_flag" jdbcType="TINYINT"/>-->
<!--            <result property="dataJson" column="data_json" jdbcType="VARCHAR"/>-->
<!--            <result property="settlement" column="settlement" jdbcType="VARCHAR"/>-->
<!--            <result property="costTime" column="cost_time" jdbcType="INTEGER"/>-->
<!--            <result property="progressId" column="progress_id" jdbcType="INTEGER"/>-->
<!--            <result property="lastProgressTime" column="last_progress_time" jdbcType="TIMESTAMP"/>-->
<!--            <result property="currentProgressTime" column="current_progress_time" jdbcType="TIMESTAMP"/>-->
<!--    </resultMap>-->

<!--    <sql id="Base_Column_List">-->
<!--        id,claim_id,current_code,-->
<!--        current_code_name,option_code,option_code_name,-->
<!--        option_type,option_value,create_time,-->
<!--        create_by,update_time,update_by,-->
<!--        enabled_flag,data_json,settlement,-->
<!--        cost_time,progress_id,last_progress_time,-->
<!--        current_progress_time-->
<!--    </sql>-->

<!--    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">-->
<!--        select-->
<!--        <include refid="Base_Column_List" />-->
<!--        from sm_claim_progress_timeliness-->
<!--        where  id = #{id,jdbcType=INTEGER} -->
<!--    </select>-->

<!--    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">-->
<!--        delete from sm_claim_progress_timeliness-->
<!--        where  id = #{id,jdbcType=INTEGER} -->
<!--    </delete>-->
<!--    -->
<!--    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cfpamf.ms.insur.admin.pojo.po.claim.SmClaimProgressTimeliness" useGeneratedKeys="true">-->
<!--        insert into sm_claim_progress_timeliness-->
<!--        ( id,claim_id,current_code-->
<!--        ,current_code_name,option_code,option_code_name-->
<!--        ,option_type,option_value,create_time-->
<!--        ,create_by,update_time,update_by-->
<!--        ,enabled_flag,data_json,settlement-->
<!--        ,cost_time,progress_id,last_progress_time-->
<!--        ,current_progress_time)-->
<!--        values (#{id,jdbcType=INTEGER},#{claimId,jdbcType=INTEGER},#{currentCode,jdbcType=VARCHAR}-->
<!--        ,#{currentCodeName,jdbcType=VARCHAR},#{optionCode,jdbcType=VARCHAR},#{optionCodeName,jdbcType=VARCHAR}-->
<!--        ,#{optionType,jdbcType=VARCHAR},#{optionValue,jdbcType=VARCHAR},#{createTime,jdbcType=TIMESTAMP}-->
<!--        ,#{createBy,jdbcType=VARCHAR},#{updateTime,jdbcType=TIMESTAMP},#{updateBy,jdbcType=VARCHAR}-->
<!--        ,#{enabledFlag,jdbcType=TINYINT},#{dataJson,jdbcType=VARCHAR},#{settlement,jdbcType=VARCHAR}-->
<!--        ,#{costTime,jdbcType=INTEGER},#{progressId,jdbcType=INTEGER},#{lastProgressTime,jdbcType=TIMESTAMP}-->
<!--        ,#{currentProgressTime,jdbcType=TIMESTAMP})-->
<!--    </insert>-->
<!--    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cfpamf.ms.insur.admin.pojo.po.claim.SmClaimProgressTimeliness" useGeneratedKeys="true">-->
<!--        insert into sm_claim_progress_timeliness-->
<!--        <trim prefix="(" suffix=")" suffixOverrides=",">-->
<!--                <if test="id != null">id,</if>-->
<!--                <if test="claimId != null">claim_id,</if>-->
<!--                <if test="currentCode != null">current_code,</if>-->
<!--                <if test="currentCodeName != null">current_code_name,</if>-->
<!--                <if test="optionCode != null">option_code,</if>-->
<!--                <if test="optionCodeName != null">option_code_name,</if>-->
<!--                <if test="optionType != null">option_type,</if>-->
<!--                <if test="optionValue != null">option_value,</if>-->
<!--                <if test="createTime != null">create_time,</if>-->
<!--                <if test="createBy != null">create_by,</if>-->
<!--                <if test="updateTime != null">update_time,</if>-->
<!--                <if test="updateBy != null">update_by,</if>-->
<!--                <if test="enabledFlag != null">enabled_flag,</if>-->
<!--                <if test="dataJson != null">data_json,</if>-->
<!--                <if test="settlement != null">settlement,</if>-->
<!--                <if test="costTime != null">cost_time,</if>-->
<!--                <if test="progressId != null">progress_id,</if>-->
<!--                <if test="lastProgressTime != null">last_progress_time,</if>-->
<!--                <if test="currentProgressTime != null">current_progress_time,</if>-->
<!--        </trim>-->
<!--        <trim prefix="values (" suffix=")" suffixOverrides=",">-->
<!--                <if test="id != null">id = #{id,jdbcType=INTEGER},</if>-->
<!--                <if test="claimId != null">claim_id = #{claimId,jdbcType=INTEGER},</if>-->
<!--                <if test="currentCode != null">current_code = #{currentCode,jdbcType=VARCHAR},</if>-->
<!--                <if test="currentCodeName != null">current_code_name = #{currentCodeName,jdbcType=VARCHAR},</if>-->
<!--                <if test="optionCode != null">option_code = #{optionCode,jdbcType=VARCHAR},</if>-->
<!--                <if test="optionCodeName != null">option_code_name = #{optionCodeName,jdbcType=VARCHAR},</if>-->
<!--                <if test="optionType != null">option_type = #{optionType,jdbcType=VARCHAR},</if>-->
<!--                <if test="optionValue != null">option_value = #{optionValue,jdbcType=VARCHAR},</if>-->
<!--                <if test="createTime != null">create_time = #{createTime,jdbcType=TIMESTAMP},</if>-->
<!--                <if test="createBy != null">create_by = #{createBy,jdbcType=VARCHAR},</if>-->
<!--                <if test="updateTime != null">update_time = #{updateTime,jdbcType=TIMESTAMP},</if>-->
<!--                <if test="updateBy != null">update_by = #{updateBy,jdbcType=VARCHAR},</if>-->
<!--                <if test="enabledFlag != null">enabled_flag = #{enabledFlag,jdbcType=TINYINT},</if>-->
<!--                <if test="dataJson != null">data_json = #{dataJson,jdbcType=VARCHAR},</if>-->
<!--                <if test="settlement != null">settlement = #{settlement,jdbcType=VARCHAR},</if>-->
<!--                <if test="costTime != null">cost_time = #{costTime,jdbcType=INTEGER},</if>-->
<!--                <if test="progressId != null">progress_id = #{progressId,jdbcType=INTEGER},</if>-->
<!--                <if test="lastProgressTime != null">last_progress_time = #{lastProgressTime,jdbcType=TIMESTAMP},</if>-->
<!--                <if test="currentProgressTime != null">current_progress_time = #{currentProgressTime,jdbcType=TIMESTAMP},</if>-->
<!--        </trim>-->
<!--    </insert>-->
<!--    <update id="updateByPrimaryKeySelective" parameterType="com.cfpamf.ms.insur.admin.pojo.po.claim.SmClaimProgressTimeliness">-->
<!--        update sm_claim_progress_timeliness-->
<!--        <set>-->
<!--                <if test="claimId != null">-->
<!--                    claim_id = #{claimId,jdbcType=INTEGER},-->
<!--                </if>-->
<!--                <if test="currentCode != null">-->
<!--                    current_code = #{currentCode,jdbcType=VARCHAR},-->
<!--                </if>-->
<!--                <if test="currentCodeName != null">-->
<!--                    current_code_name = #{currentCodeName,jdbcType=VARCHAR},-->
<!--                </if>-->
<!--                <if test="optionCode != null">-->
<!--                    option_code = #{optionCode,jdbcType=VARCHAR},-->
<!--                </if>-->
<!--                <if test="optionCodeName != null">-->
<!--                    option_code_name = #{optionCodeName,jdbcType=VARCHAR},-->
<!--                </if>-->
<!--                <if test="optionType != null">-->
<!--                    option_type = #{optionType,jdbcType=VARCHAR},-->
<!--                </if>-->
<!--                <if test="optionValue != null">-->
<!--                    option_value = #{optionValue,jdbcType=VARCHAR},-->
<!--                </if>-->
<!--                <if test="createTime != null">-->
<!--                    create_time = #{createTime,jdbcType=TIMESTAMP},-->
<!--                </if>-->
<!--                <if test="createBy != null">-->
<!--                    create_by = #{createBy,jdbcType=VARCHAR},-->
<!--                </if>-->
<!--                <if test="updateTime != null">-->
<!--                    update_time = #{updateTime,jdbcType=TIMESTAMP},-->
<!--                </if>-->
<!--                <if test="updateBy != null">-->
<!--                    update_by = #{updateBy,jdbcType=VARCHAR},-->
<!--                </if>-->
<!--                <if test="enabledFlag != null">-->
<!--                    enabled_flag = #{enabledFlag,jdbcType=TINYINT},-->
<!--                </if>-->
<!--                <if test="dataJson != null">-->
<!--                    data_json = #{dataJson,jdbcType=VARCHAR},-->
<!--                </if>-->
<!--                <if test="settlement != null">-->
<!--                    settlement = #{settlement,jdbcType=VARCHAR},-->
<!--                </if>-->
<!--                <if test="costTime != null">-->
<!--                    cost_time = #{costTime,jdbcType=INTEGER},-->
<!--                </if>-->
<!--                <if test="progressId != null">-->
<!--                    progress_id = #{progressId,jdbcType=INTEGER},-->
<!--                </if>-->
<!--                <if test="lastProgressTime != null">-->
<!--                    last_progress_time = #{lastProgressTime,jdbcType=TIMESTAMP},-->
<!--                </if>-->
<!--                <if test="currentProgressTime != null">-->
<!--                    current_progress_time = #{currentProgressTime,jdbcType=TIMESTAMP},-->
<!--                </if>-->
<!--        </set>-->
<!--        where   id = #{id,jdbcType=INTEGER} -->
<!--    </update>-->
<!--    <update id="updateByPrimaryKey" parameterType="com.cfpamf.ms.insur.admin.pojo.po.claim.SmClaimProgressTimeliness">-->
<!--        update sm_claim_progress_timeliness-->
<!--        set -->
<!--            claim_id =  #{claimId,jdbcType=INTEGER},-->
<!--            current_code =  #{currentCode,jdbcType=VARCHAR},-->
<!--            current_code_name =  #{currentCodeName,jdbcType=VARCHAR},-->
<!--            option_code =  #{optionCode,jdbcType=VARCHAR},-->
<!--            option_code_name =  #{optionCodeName,jdbcType=VARCHAR},-->
<!--            option_type =  #{optionType,jdbcType=VARCHAR},-->
<!--            option_value =  #{optionValue,jdbcType=VARCHAR},-->
<!--            create_time =  #{createTime,jdbcType=TIMESTAMP},-->
<!--            create_by =  #{createBy,jdbcType=VARCHAR},-->
<!--            update_time =  #{updateTime,jdbcType=TIMESTAMP},-->
<!--            update_by =  #{updateBy,jdbcType=VARCHAR},-->
<!--            enabled_flag =  #{enabledFlag,jdbcType=TINYINT},-->
<!--            data_json =  #{dataJson,jdbcType=VARCHAR},-->
<!--            settlement =  #{settlement,jdbcType=VARCHAR},-->
<!--            cost_time =  #{costTime,jdbcType=INTEGER},-->
<!--            progress_id =  #{progressId,jdbcType=INTEGER},-->
<!--            last_progress_time =  #{lastProgressTime,jdbcType=TIMESTAMP},-->
<!--            current_progress_time =  #{currentProgressTime,jdbcType=TIMESTAMP}-->
<!--        where   id = #{id,jdbcType=INTEGER} -->
<!--    </update>-->

    <delete id="deleteByClaimIds">
        delete from sm_claim_progress_timeliness where claim_id in
        <foreach collection="list" separator="," open="(" close=")" item="item">
            #{item}
        </foreach>
    </delete>

    <select id="listLatest" resultType="java.lang.Integer">

        SELECT DISTINCT( `claimId`)  FROM `sm_claim_progress` WHERE `update_time` BETWEEN #{startTime} and #{endTime}
        limit #{start},#{size}

    </select>

    <select id="countLatest" resultType="java.lang.Integer">

        SELECT count(DISTINCT( `claimId`))  FROM `sm_claim_progress` WHERE `update_time` BETWEEN #{startTime} and #{endTime}

    </select>
    <select id="listProgressByClaimId" resultType="com.cfpamf.ms.insur.admin.pojo.po.claim.SmClaimProgress">

        select t1.*, t2.process_type from sm_claim_progress t1 inner join sm_claim t2  on t2.id = t1.claimId
                    where t1.enabled_flag = 0 and t2.enabled_flag = 0 and
                    t1.claimId in
        <foreach collection="list" separator="," open="(" close=")" item="item">
                    #{item}
        </foreach>
                and t2.process_type != 'IMPORT'


    </select>
    <select id="listProgressTimelinessByClaimIdList"
            resultType="com.cfpamf.ms.insur.admin.pojo.po.claim.SmClaimProgressTimeliness">
        select * from sm_claim_progress_timeliness where claim_id in
        <foreach collection="list" separator="," open="(" close=")" item="item">
            #{item}
        </foreach>
    </select>

</mapper>
