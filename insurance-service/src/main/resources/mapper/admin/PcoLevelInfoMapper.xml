<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.pco.PcoLevelInfoMapper">
    <insert id="insertLevelList">
        insert into pco_level_info(ding_user_id, userId, job_code, pco_level,online_score, offline_score,performance
        ,be_qualified,be_newest,create_by,claim_timeliness_ratio)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.dingUserId}, #{item.userId}, #{item.jobCode}, #{item.pcoLevel}, #{item.onlineScore}, #{item.offlineScore}
            ,#{item.performance},#{item.beQualified},#{item.beNewest},#{item.createBy},#{item.claimTimelinessRatio})
        </foreach>
    </insert>

    <select id="countByJobCode" resultType="java.lang.Integer">
        select count(1)
        from pco_level_info
        where job_code = #{jobCode}
        and enabled_flag = 0
    </select>
</mapper>