<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.AuthUserMapper">

    <select id="getAuthUserByUserMobile" resultType="com.cfpamf.ms.insur.admin.pojo.vo.AuthUserVO">
        SELECT *
        FROM auth_user
        WHERE enabled_flag = 0
          AND userMobile = #{userMobile}
        LIMIT 1
    </select>

    <select id="getAuthUserByUserId" resultType="com.cfpamf.ms.insur.admin.pojo.vo.AuthUserVO">
        SELECT *
        FROM auth_user
        WHERE enabled_flag = 0
          AND userId = #{userId}
        LIMIT 1
    </select>

    <select id="getAuthUserByUserIdCard" resultType="com.cfpamf.ms.insur.admin.pojo.vo.AuthUserVO">
        SELECT *
        FROM auth_user
        WHERE enabled_flag = 0
          AND userIdCard = #{userIdCard}
        LIMIT 1
    </select>

    <select id="getAuthUserById" resultType="com.cfpamf.ms.insur.admin.pojo.vo.AuthUserVO">
        SELECT *
        FROM auth_user
        WHERE enabled_flag = 0
          AND id = #{id}
    </select>

    <select id="listUsersByPageWithJobNumbers" resultType="com.cfpamf.ms.insur.admin.pojo.vo.UserVO">
        SELECT * FROM auth_user WHERE userType='employee' AND enabled_flag = 0
        AND userId IN
        <foreach collection="jobNumbers" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
        ORDER BY regionName DESC, organizationName DESC, userId ASC, create_time DESC
    </select>

    <select id="listUserNameByUserId" resultType="com.cfpamf.ms.insur.admin.pojo.vo.AuthUserNameVO">
        SELECT DISTINCT userId,userName FROM auth_user WHERE enabled_flag = 0
        AND userId in
        <foreach collection="jobNumbers" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <update id="deleteUser">
        UPDATE auth_user
        SET enabled_flag = 1
        WHERE id = #{id}
    </update>

    <insert id="insertUser" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO auth_user(regionName, organizationName, organizationFullName, userId, userName, userMobile,
                              userType, orgPath, regionCode, orgCode, hrOrgId, userIdCard, batchNo, postCode, postName,
                              status, serviceType, jobCode, create_time, update_time)
        values (#{regionName}, #{organizationName}, #{organizationFullName}, #{userId}, #{userName}, #{userMobile},
                #{userType}, #{orgPath}, #{regionCode}, #{orgCode}, #{hrOrgId}, #{userIdCard}, #{batchNo}, #{postCode},
                #{postName}, #{status}, #{serviceType}, #{jobCode}, CURRENT_TIMESTAMP(), CURRENT_TIMESTAMP())
    </insert>

    <insert id="insertUsers" useGeneratedKeys="true">
        INSERT INTO auth_user(regionName, organizationName, userId, userName, userMobile, wxOpenId, wxNickName,
        userType, create_time, update_time)
        values
        <foreach collection="dtos" item="item" index="index" separator=",">
            (#{item.regionName}, #{item.organizationName}, #{item.userId}, #{item.userName}, #{item.userMobile},
            #{item.wxOpenId}, #{item.wxNickName}, #{item.userType}, CURRENT_TIMESTAMP(), CURRENT_TIMESTAMP())
        </foreach>
    </insert>

    <select id="listChangeUsersV1" resultType="com.cfpamf.ms.insur.admin.pojo.vo.UserVO">
        SELECT
        DISTINCT u.userId ,
        u.userName,
        u.userMobile,
        u.wxNickName,
        u.wxImgUrl,
        u.wxOpenId,
        u.userType,
        u.switchTime,
        u.bizCode,
        u.mainJobNumber,
        u.create_time
        FROM auth_user u
        left join user_post p on u.userId = p.job_number
        WHERE u.userType='employee' AND u.enabled_flag = 0 and p.job_code is not null
        <if test='keyword != null'>
            AND (u.userId LIKE CONCAT('%',#{keyword},'%') OR u.userName LIKE CONCAT('%',#{keyword},'%') OR u.userMobile LIKE
            CONCAT('%',#{keyword},'%')
            OR p.region_name LIKE CONCAT('%',#{keyword},'%') OR u.wxNickName LIKE CONCAT('%',#{keyword},'%') OR
            p.organization_name LIKE CONCAT('%',#{keyword},'%') )
        </if>
        <if test="inOffice">
            AND u.status != '8' and p.service_status=0
        </if>
        <if test='regionName != null'>
            AND u.regionName=#{regionName}
        </if>
        <if test='orgName != null'>
            AND u.organizationFullName=#{orgName}
        </if>

        <if test='regionCode != null'>
            AND u.regionCode=#{regionCode}
        </if>
        <if test='orgCode != null'>
            AND u.orgCode=#{orgCode}
        </if>
        ORDER BY regionName DESC, organizationName DESC, userId ASC, create_time DESC
    </select>
    <select id="listChangeUsers" resultType="com.cfpamf.ms.insur.admin.pojo.vo.UserVO">
        SELECT
        u.userId ,
        u.userName,
        u.userMobile,
        u.wxNickName,
        u.wxImgUrl,
        u.wxOpenId,
        u.userType,
        u.switchTime,
        u.bizCode,
        u.mainJobNumber,
        u.create_time ,
        p.job_code as jobCode,
        p.region_code as regionCode,
        p.region_name as regionName,
        p.org_code as orgCode,
        p.organization_name as organizationName,
        p.org_name as organizationFullName,
        p.post_code as postCode,
        p.post_name as postName,
        (case when u.status!=8 and p.service_status=0 then '3' else  '8' end) as status
        FROM auth_user u
        left join user_post p on u.userId = p.job_number
        WHERE u.userType='employee' AND u.enabled_flag = 0 and p.job_code is not null and p.service_type=0
        <if test='keyword != null'>
            AND (u.userId LIKE CONCAT('%',#{keyword},'%') OR u.userName LIKE CONCAT('%',#{keyword},'%') OR u.userMobile LIKE
            CONCAT('%',#{keyword},'%')
            OR p.region_name LIKE CONCAT('%',#{keyword},'%') OR u.wxNickName LIKE CONCAT('%',#{keyword},'%') OR
            p.organization_name LIKE CONCAT('%',#{keyword},'%') )
        </if>
        <if test="inOffice">
            AND u.status != '8' and p.service_status=0
        </if>

        <if test='regionName != null'>
            AND u.regionName=#{regionName}
        </if>
        <if test='orgName != null'>
            AND u.organizationFullName=#{orgName}
        </if>

        <if test='regionCode != null'>
            AND u.regionCode=#{regionCode}
        </if>
        <if test='orgCode != null'>
            AND u.orgCode=#{orgCode}
        </if>
        ORDER BY regionName DESC, organizationName DESC, userId ASC, create_time DESC
    </select>

    <select id="listUsers" resultType="com.cfpamf.ms.insur.admin.pojo.vo.UserVO">
        SELECT * FROM auth_user WHERE userType='employee' AND enabled_flag = 0
        <if test='keyword != null'>
            AND (userId LIKE CONCAT('%',#{keyword},'%') OR userName LIKE CONCAT('%',#{keyword},'%') OR userMobile LIKE
            CONCAT('%',#{keyword},'%')
            OR regionName LIKE CONCAT('%',#{keyword},'%') OR wxNickName LIKE CONCAT('%',#{keyword},'%') OR
            organizationName LIKE CONCAT('%',#{keyword},'%') )
        </if>
        <if test="inOffice">
            AND status != '8'
        </if>
        ORDER BY regionName DESC, organizationName DESC, userId ASC, create_time DESC
    </select>
    <select id="listUserByOrg" resultType="com.cfpamf.ms.insur.admin.pojo.vo.UserVO">
        SELECT * FROM auth_user WHERE userType='employee' AND enabled_flag = 0
        <if test="keyword != null and  keyword!=''">
            AND (userId LIKE CONCAT('%',#{keyword},'%') OR userName LIKE CONCAT('%',#{keyword},'%') OR
            userMobile LIKE
            CONCAT('%',#{keyword},'%'))
        </if>
        and organizationName=#{orgName}
        AND status != '8'
        ORDER BY userId ASC, create_time DESC
    </select>
    <select id="getAgentByMobileOrOpenId" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmUserAgentVO">
        SELECT *
        FROM auth_user
        WHERE (userMobile = #{agentMobile} OR wxOpenId = #{wxOpenId})
          AND userType IN ('agent', 'employee')
          AND enabled_flag = 0
    </select>

    <update id="updateUser">
        UPDATE auth_user
        SET regionName= #{dto.regionName},
            organizationName= #{dto.organizationName},
            organizationFullName= #{dto.organizationFullName},
            userId= #{dto.userId},
            userName= #{dto.userName},
            userMobile= #{dto.userMobile},
            userType= #{dto.userType},
            status= #{dto.status},
            postCode= #{dto.postCode},
            postName= #{dto.postName},
            orgPath=#{dto.orgPath},
            regionCode=#{dto.regionCode},
            orgCode=#{dto.orgCode},
            userIdCard=#{dto.userIdCard},
            hrOrgId=#{dto.hrOrgId},
            batchNo= #{dto.batchNo},
            serviceType=#{dto.serviceType},
            update_time=CURRENT_TIMESTAMP()
        WHERE id = #{id}
    </update>

    <update id="updateUserLoginInfo">
        UPDATE auth_user
        SET loginId= #{loginId},
            expireTime= #{expireTime}
        WHERE enabled_flag = 0
          AND userId = #{userId};
    </update>

    <update id="updateUserLogoutInfo">
        UPDATE auth_user
        SET loginId    = null,
            expireTime = null
        WHERE userId = #{userId}
    </update>

    <update id="updateUserType">
        UPDATE auth_user
        SET userType = #{userType}
        WHERE id = #{id}
    </update>

    <update id="updateEmployeeStatus">
        UPDATE auth_user
        SET status = #{status}
        WHERE id = #{id}
    </update>

    <select id="getAuthUserByToken" resultType="com.cfpamf.ms.insur.admin.pojo.vo.AuthUserVO">
        SELECT *
        FROM auth_user
        WHERE enabled_flag = 0
          AND loginId = #{loginId}
    </select>

    <select id="getUserByUserId" resultType="com.cfpamf.ms.insur.weixin.pojo.vo.WxUserVO">
        SELECT *
        FROM auth_user
        WHERE enabled_flag = 0
          AND userId = #{userId}
        LIMIT 1
    </select>

    <select id="queryUserList" resultType="com.cfpamf.ms.insur.weixin.pojo.vo.WxUserVO">
        SELECT *
        FROM auth_user
        WHERE enabled_flag = 0
        AND userId in
        <foreach collection="userIdList" item="userId" separator="," open="(" close=")">
            #{userId}
        </foreach>
    </select>

    <select id="getUserInfoByOpenIdAndUserId" resultType="com.cfpamf.ms.insur.weixin.pojo.vo.WxUserVO">
        SELECT *
        FROM auth_user
        WHERE enabled_flag = 0
          AND wxOpenId = #{wxOpenId}
          AND userId = #{userId}
        LIMIT 1
    </select>

    <select id="listUserByWxOpenId" resultType="com.cfpamf.ms.insur.weixin.pojo.vo.WxUserVO">
        SELECT *
        FROM auth_user
        WHERE enabled_flag = 0
          and (userType != 'employee' or (userType = 'employee' and status != 8))
          AND wxOpenId = #{openId}
    </select>
    <!--统一工号后根据微信id查询-->
    <select id="listUserByWxOpenIdAfterUnified" resultType="com.cfpamf.ms.insur.weixin.pojo.vo.WxUserVO">
        SELECT a.id,
               a.userid,
               a.userName,
               a.userMobile,
               a.userType,
               a.wxOpenId,
               a.wxNickName,
               a.wxImgUrl,
               a.expireTime,
               a.loginId,
               a.status,
               a.userIdCard,
               a.agentId,
               a.agentTopUserId,
               a.realVerify,
               a.userEmail,
               a.switchTime,
               case when p.region_code is null then a.regionCode else p.region_code end     as regionCode,
               case when p.region_name is null then a.regionName else p.region_name end     as regionName,
               case when p.org_name is null then a.organizationFullName else p.org_name end as organizationFullName,
               case when p.org_path is null then a.orgPath else p.org_path end              as orgPath,
               case when p.post_code is null then a.postCode else p.post_code end           as postCode,
               case when p.post_name is null then a.postName else p.post_name end           as postName,
               case when p.org_code is null then a.orgCode else p.org_code end              as orgCode,
               case when p.org_name is null then a.organizationName else p.org_name end     as organizationName,
               case when p.hr_org_id is null then a.hrOrgId else p.hr_org_id end            as hrOrgId,
               case when p.service_type is null then a.serviceType else p.service_type end  as serviceType,
               case when p.job_code is null then a.jobCode else p.job_code end              as jobCode
        FROM auth_user a
                 left join user_post p on a.userId = p.job_number and p.enabled_flag = 0 and p.employee_status != 8 and
                                          p.service_status = 0
        WHERE a.enabled_flag = 0
          and (a.userType != 'employee' or (a.userType = 'employee' and a.status != 8))
          AND a.wxOpenId = #{openId}
    </select>


    <select id="listUserByUserMobile" resultType="com.cfpamf.ms.insur.weixin.pojo.vo.WxUserVO">
        SELECT *
        FROM auth_user
        WHERE enabled_flag = 0
          and (userType != 'employee' or (userType = 'employee' and status != 8))
          AND userMobile = #{userMobile}
    </select>

    <!-- 统一工号以后更加手机号码查-->
    <select id="listUserByUserMobileAfterUnified" resultType="com.cfpamf.ms.insur.weixin.pojo.vo.WxUserVO">
        SELECT a.id,
               a.userid,
               a.userName,
               a.userMobile,
               a.userType,
               a.wxOpenId,
               a.wxNickName,
               a.wxImgUrl,
               a.expireTime,
               a.loginId,
               a.status,
               a.userIdCard,
               a.agentId,
               a.agentTopUserId,
               a.realVerify,
               a.userEmail,
               a.switchTime,
               case when p.region_code is null then a.regionCode else p.region_code end     as regionCode,
               case when p.region_name is null then a.regionName else p.region_name end     as regionName,
               case when p.org_name is null then a.organizationFullName else p.org_name end as organizationFullName,
               case when p.org_path is null then a.orgPath else p.org_path end              as orgPath,
               case when p.post_code is null then a.postCode else p.post_code end           as postCode,
               case when p.post_name is null then a.postName else p.post_name end           as postName,
               case when p.org_code is null then a.orgCode else p.org_code end              as orgCode,
               case when p.org_name is null then a.organizationName else p.org_name end     as organizationName,
               case when p.hr_org_id is null then a.hrOrgId else p.hr_org_id end            as hrOrgId,
               case when p.service_type is null then a.serviceType else p.service_type end  as serviceType,
               case when p.job_code is null then a.jobCode else p.job_code end              as jobCode
        FROM auth_user a
                 left join user_post p on a.userId = p.job_number and p.enabled_flag = 0 and p.employee_status != 8 and
                                          p.service_status = 0
        WHERE a.enabled_flag = 0
          and (a.userType != 'employee' or (a.userType = 'employee' and a.status != 8))
          AND userMobile = #{userMobile}
    </select>

    <insert id="insertWxUserInfo" useGeneratedKeys="true">
        INSERT INTO auth_user(wxOpenId, wxNickName, wxImgUrl, userType, create_time, update_time)
        values (#{wxOpenId}, #{wxNickName}, #{wxImgUrl}, #{userType}, CURRENT_TIMESTAMP(), CURRENT_TIMESTAMP())
    </insert>

    <insert id="insertUserWxBindInfo" useGeneratedKeys="true">
        INSERT INTO auth_user(regionCode, regionName, organizationName, organizationFullName, userId, userName,
                              userMobile,
                              userType, wxOpenId, wxNickName, wxImgUrl, orgPath, postCode, postName, status, batchNo,
                              hrOrgId, orgCode, userIdCard, jobCode, bmsUserId, hrUserId, mainJobNumber, bizCode,
                              create_time, update_time)
        values (#{regionCode}, #{regionName}, #{organizationName}, #{organizationFullName}, #{userId}, #{userName},
                #{userMobile},
                #{userType}, #{wxOpenId}, #{wxNickName}, #{wxImgUrl}, #{orgPath}, #{postCode}, #{postName},
                #{employeeStatus}, #{batchNo},
                #{hrOrgId}, #{orgCode}, #{userIdCard}, #{jobCode}, #{bmsUserId}, #{hrUserId}, #{mainJobNumber},
                #{bizCode},
                CURRENT_TIMESTAMP(), CURRENT_TIMESTAMP())
    </insert>

    <update id="updateWxUserInfo">
        UPDATE auth_user
        SET wxNickName=#{wxNickName},
            wxImgUrl= #{wxImgUrl},
            update_time=CURRENT_TIMESTAMP()
        WHERE wxOpenId = #{wxOpenId}
    </update>

    <update id="updateUserEmail">
        UPDATE auth_user
        SET userEmail=#{userEmail}
        WHERE id = #{id}
    </update>

    <update id="updateSwitchTime">
        UPDATE auth_user
        SET switchTime=CURRENT_TIMESTAMP(),
            jobCode=#{jobCode}
            <if test='postCode != null and postCode!=""'>
                , postCode=#{postCode}
            </if>
            <if test='postName != null and postName!=""'>
                , postName=#{postName}
            </if>
        WHERE id = #{id}
    </update>

    <update id="updatedWxUserBindInfo">
        UPDATE auth_user
        SET userId=#{userId},
            userName=#{userName},
            regionName=#{regionName},
            orgPath=#{orgPath},
            postCode=#{postCode},
            postName=#{postName},
            organizationName=#{organizationName},
            organizationFullName=#{organizationFullName},
            userMobile=#{userMobile},
            userType= #{userType},
            status= #{employeeStatus},
            update_time=CURRENT_TIMESTAMP()
        WHERE wxOpenId = #{wxOpenId}
    </update>

    <update id="updatedUserWxBindInfo">
        UPDATE auth_user
        SET wxOpenId   = #{wxOpenId},
            wxImgUrl   = #{wxImgUrl},
            wxNickName = #{wxNickName},
            update_time=CURRENT_TIMESTAMP()
        WHERE userMobile = #{userMobile}
    </update>

    <update id="updatedUserWxBindInfoByUserId">
        UPDATE auth_user
        SET wxOpenId   = #{wxOpenId},
            wxImgUrl   = #{wxImgUrl},
            wxNickName = #{wxNickName},
            update_time=CURRENT_TIMESTAMP()
        WHERE userId = #{userId}
    </update>

    <update id="deleteUserWxInfoByOpenId">
        INSERT INTO auth_user (userType, wxOpenId, wxNickName, wxImgUrl)
        SELECT "weixin", wxOpenId, wxNickName, wxImgUrl
        FROM auth_user
        WHERE wxOpenId = #{wxOpenId}
        LIMIT 1;
        UPDATE auth_user
        SET wxOpenId= NULL,
            wxNickName= NULL,
            wxImgUrl= NULL
        WHERE wxOpenId = #{wxOpenId}
          AND userType != "weixin"
    </update>

    <update id="deleteUnnecessaryWxInfoByOpenId">
        DELETE
        FROM auth_user
        WHERE wxOpenId = #{wxOpenId}
          AND userType = 'weixin'
    </update>

    <update id="deleteUserWxInfoById">
        INSERT INTO auth_user (userType, wxOpenId, wxNickName, wxImgUrl)
        SELECT "weixin", wxOpenId, wxNickName, wxImgUrl
        FROM auth_user
        WHERE id = #{id};
        UPDATE auth_user
        SET wxOpenId=null,
            wxNickName=null,
            wxImgUrl=null,
            create_time=CURRENT_TIMESTAMP()
        WHERE id = #{id};
    </update>

    <select id="getUserAgentByAgentId" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmUserAgentVO">
        SELECT *
        FROM auth_user
        WHERE agentId = #{agentId}
          AND enabled_flag = 0
    </select>

    <select id="getUserAgentByAgentMobile" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmUserAgentVO">
        SELECT *
        FROM auth_user
        WHERE userMobile = #{agentMobile}
          AND enabled_flag = 0
    </select>

    <select id="listUnRegisteredEmployeeeUserIdByOrgPath" resultType="java.lang.String">
        SELECT DISTINCT t1.userId
        FROM auth_user t1
                 LEFT JOIN sm_agent t2 ON t1.agentId = t2.agentId
        WHERE t1.userType = 'employee'
          AND t2.agentId IS NULL
          AND t1.enabled_flag = 0
          AND t1.status != '8'
          AND t1.orgPath LIKE CONCAT(#{orgPath}, '%')
    </select>
    <select id="listUserNormalByUserMobiles" resultType="com.cfpamf.ms.insur.weixin.pojo.vo.WxUserVO">

        SELECT DISTINCT *
        FROM auth_user t1
        WHERE t1.userType = 'employee'
        AND t1.enabled_flag = 0
        AND t1.status != '8'
        AND userMobile in
        <foreach collection="mobiles" item="mobile" separator="," open="(" close=")">
            #{mobile}
        </foreach>
    </select>
    <select id="listUserByIdCards" resultType="com.cfpamf.ms.insur.admin.pojo.dto.UserDTO">
        SELECT *
        FROM auth_user t1
        WHERE t1.userType = 'employee'
        and t1.enabled_flag =0
        and userIdCard in
        <foreach collection="idNumbers" item="idNumber" close=")" open="(" separator=",">
            #{idNumber}
        </foreach>
    </select>
    <select id="getUserPostMasterByIdNo" resultType="com.cfpamf.ms.insur.admin.pojo.po.UserPost">
        select iup.*
        from auth_user iau
                 left join user_post iup on
            iau.userId = iup.job_number
        where iau.userIdCard = #{idNo}
          and iup.service_status = 0
          and iup.service_type = 0
          and iau.enabled_flag = 0
          and iup.enabled_flag = 0
          and userType = 'employee'
        limit 1
    </select>

    <update id="updateWeixinAgentRegisterInfo"
            parameterType="com.cfpamf.ms.insur.admin.pojo.dto.SmAgentRegisterDTO">
        UPDATE auth_user
        SET regionCode=#{regionCode},
            regionName=#{regionName},
            orgCode=#{orgCode},
            organizationName=#{organizationName},
            organizationFullName=#{organizationFullName},
            hrOrgId=#{hrOrgId},
            orgPath=#{orgPath},
            userMobile=#{agentMobile},
            userType=#{userType},
            postCode=#{postCode},
            postName=#{postName},
            agentId=#{agentId},
            agentTopUserId=#{agentTopUserId}
        WHERE wxOpenId = #{wxOpenId}
          AND userType = 'weixin'
    </update>

    <update id="updateEmployeeAgentBindInfo"
            parameterType="com.cfpamf.ms.insur.admin.pojo.dto.SmAgentRegisterDTO">
        UPDATE auth_user
        SET agentId=#{agentId},
            agentTopUserId=#{agentTopUserId}
        WHERE userId = #{agentBindUserId}
    </update>

    <update id="updateUserBindAgentBaseInfo">
        UPDATE auth_user t1
            LEFT JOIN sm_agent t2 ON t1.agentId = t2.agentId
            LEFT JOIN auth_user t3 ON 1 = 1
        SET t1.wxOpenId=t3.wxOpenId,
            t1.wxNickName=t3.wxNickName,
            t1.wxImgUrl=t3.wxImgUrl
        WHERE t2.ccCustNo = #{ccCustNo}
          AND t3.wxOpenId = #{wxOpenId};
        DELETE
        FROM auth_user
        WHERE wxOpenId = #{wxOpenId}
          AND userType = "weixin";
    </update>

    <update id="updateUserBindAgentIdInfo">
        UPDATE auth_user
        SET agentId        = #{agentId},
            agentTopUserId = #{agentTopUserId},
            realVerify     = 1
        WHERE userId = #{userId}
    </update>

    <update id="updateWeixinAgentRealVerifyInfo">
        UPDATE auth_user
        SET userName   = #{agentName},
            userIdCard = #{agentIdCard},
            realVerify = 1
        WHERE agentId = #{agentId}
    </update>

    <update id="updateEmployeeAgentRealVerifyInfo">
        UPDATE auth_user
        SET realVerify = 1
        WHERE agentId = #{agentId}
    </update>
    <update id="batchUpdateEmployeeStatus">
        UPDATE auth_user
        SET status = #{status}
        WHERE userId in
        <foreach collection="users" item="userId"
                 open="(" close=")" separator=",">
            #{userId}
        </foreach>
        <![CDATA[and status<>#{status} ]]>
    </update>

    <insert id="insertAndUpdateEmployeeAgent">
        INSERT INTO sm_agent (regionName, organizationName, agentName, agentMobile, agentBindUserId, agentTopUserId,
                              parentAgentId, levelId, agentType, agentPath, agentJobNumber, teamId, teamPath,
                              agentIdCard, idCardPicUrl1, idCardPicUrl2)
        SELECT regionName,
               organizationName,
               userName,
               userMobile,
               userId,
               userId,
               NULL,
               1,
               1,
               NULL,
               NULL,
               NULL,
               NULL,
               userIdCard,
               NULL,
               NULL
        FROM auth_user
        WHERE userId = #{userId}
          AND enabled_flag = 0;
        INSERT INTO sm_agent_team(agentId, agentTopUserId, teamPathLvel, teamName, teamScale)
        SELECT agentId, agentTopUserId, 1, CONCAT(agentName, '的团队'), 1
        FROM sm_agent
        WHERE agentTopUserId = #{userId};
        UPDATE sm_agent_team
        SET teamPath = teamId
        WHERE agentTopUserId = #{userId};
        UPDATE sm_agent t1 LEFT JOIN sm_agent_team t2 ON t1.agentTopUserId = t2.agentTopUserId
        SET t1.agentPath = t1.agentId,
            t1.teamId    = t2.teamId,
            t1.teamPath  = t2.teamPath
        WHERE t1.agentTopUserId = #{userId};
        UPDATE auth_user t1 LEFT JOIN sm_agent t2 ON t1.userMobile = t2.agentMobile
        SET t1.agentId        = t2.agentId,
            t1.agentTopUserId = t1.userId,
            t1.realVerify     = 1
        WHERE t1.userId = #{userId};
    </insert>


    <update id="batchInsertUser" useGeneratedKeys="true" parameterType="java.util.List">
        INSERT INTO auth_user(id,regionName, organizationName, organizationFullName, userId, userName, userMobile,
        userType, orgPath, regionCode, orgCode, hrOrgId, userIdCard, batchNo, postCode, postName,
        status,serviceType, jobCode,hrUserId,bmsUserId,mainJobNumber)
        values
        <foreach collection="list" item="user" separator=",">
            (#{user.id},#{user.regionName}, #{user.organizationName}, #{user.organizationFullName}, #{user.userId},
            #{user.userName}, #{user.userMobile},
            #{user.userType}, #{user.orgPath}, #{user.regionCode}, #{user.orgCode}, #{user.hrOrgId}, #{user.userIdCard},
            #{user.batchNo}, #{user.postCode},
            #{user.postName}, #{user.status},
            #{user.serviceType},#{user.jobCode},#{user.hrUserId},#{user.bmsUserId},#{user.mainJobNumber})
        </foreach>
        ON DUPLICATE KEY UPDATE
        regionName=values(regionName),organizationName=values(organizationName),organizationFullName=values(organizationFullName)
        ,userId=values(userId),userName=values(userName),userMobile=values(userMobile),userType=values(userType)
        ,orgPath=values(orgPath),regionCode=values(regionCode),orgCode=values(orgCode),hrOrgId=values(hrOrgId)
        ,userIdCard=values(userIdCard),batchNo=values(batchNo),postCode=values(postCode),postName=values(postName)
        ,status=values(status),serviceType=values(serviceType),jobCode=values(jobCode),hrUserId=values(hrUserId)
        ,bmsUserId=values(bmsUserId),mainJobNumber=values(mainJobNumber)

    </update>

    <update id="updateQuitStatusByUserIdCardAndUserType">
        update auth_user
        set status = 8
        where userIdCard = #{userIdCard}
          and userType = 'employee'
    </update>

    <update id="updateQuitStatusUserType">
        update auth_user
        set status = 8
        where userType = 'employee'
    </update>
    <update id="updateUserBizCode">
        update auth_user
        set bizCode = #{bizCode}
        where userIdCard = #{idNumber}
          and userType = 'employee'
          and enabled_flag = 0
    </update>

    <select id="listUsersByJobNumbers" resultType="com.cfpamf.ms.insur.admin.pojo.vo.AuthUserVO">
        SELECT * FROM auth_user WHERE enabled_flag = 0
        AND userId IN
        <foreach collection="jobNumbers" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>

    </select>
    <select id="listUsersByName" resultType="com.cfpamf.ms.insur.admin.pojo.vo.AuthUserVO">
        SELECT * FROM auth_user WHERE enabled_flag = 0
        AND userName IN
        <foreach collection="names" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>

    </select>

    <select id="getAvailableBizCode" resultType="java.lang.String">
        select *
        from (
        <foreach collection="bizCodes" item="bizCode" separator=" union all ">
            select #{bizCode} code
        </foreach>
        from dual) t
        where not exists(
        select 1
        from auth_user
        where bizCode = t.code
        )
    </select>
    <select id="listAllNoBizCodeIdNumbers" resultType="java.lang.String">
        select distinct userIdCard
        from auth_user
        where userType = 'employee'
          and bizCode is null
          and enabled_flag = 0
    </select>
    <select id="getUserBizCodeByIdNumber" resultType="java.lang.String">
        select bizCode
        from auth_user
        where userIdCard = #{idNumber}
          and bizCode is not null
          and enabled_flag = 0
        limit 1
    </select>
    <select id="getMainJobNumberByBizCode" resultType="java.lang.String">
        SELECT mainJobNumber
        from auth_user
        where bizCode = #{bizCode}
          and enabled_flag = 0
           <![CDATA[and status <> 8 ]]>
          and userType = 'employee'
        limit 1
    </select>
    <select id="listOrgUserByPost" resultType="com.cfpamf.ms.insur.admin.pojo.vo.AuthUserVO">
        select *
        from auth_user
        where regionName = #{regionName}
          and organizationName = #{organizationName}
            <![CDATA[and status <> 8 ]]>
        and enabled_flag = 0
        <if test="postCode!= null and postCode !=''">
            and postCode = #{postCode}
        </if>
    </select>



    <select id="listUsersV2" resultType="com.cfpamf.ms.insur.admin.pojo.vo.UserVO">
        SELECT
        u.userId ,
        u.userName,
        u.userMobile,
        u.wxNickName,
        u.wxImgUrl,
        u.wxOpenId,
        u.userType,
        u.switchTime,
        u.bizCode,
        u.mainJobNumber,
        u.create_time ,
        p.job_code as jobCode,
        p.region_code as regionCode,
        p.region_name as regionName,
        p.org_code as orgCode,
        p.organization_name as organizationName,
        p.org_name as organizationFullName,
        p.post_code as postCode,
        p.post_name as postName,
        (case when u.status!=8 and p.service_status=0 then '3' else  '8' end) as status
        FROM auth_user u
        left join user_post p on u.userId = p.job_number
        WHERE u.userType='employee' AND u.enabled_flag = 0 and p.job_code is not null
        <if test='keyword != null'>
            AND (u.userId LIKE CONCAT('%',#{keyword},'%') OR u.userName LIKE CONCAT('%',#{keyword},'%') OR u.userMobile LIKE
            CONCAT('%',#{keyword},'%')
            OR p.region_name LIKE CONCAT('%',#{keyword},'%') OR u.wxNickName LIKE CONCAT('%',#{keyword},'%') OR
            p.organization_name LIKE CONCAT('%',#{keyword},'%') )
        </if>
        <if test="inOffice">
             AND u.status != '8' and p.service_status=0
        </if>
        ORDER BY regionName DESC, organizationName DESC, userId ASC, create_time DESC
    </select>
    <select id="getUserByMobileOrCode" resultType="com.cfpamf.ms.insur.admin.pojo.vo.UserVO">
        SELECT * FROM auth_user t1 WHERE t1.userMobile = #{key} OR t1.userId #{key} AND t1.status !='8' AND t1.enable_flag = 0 limit 1
    </select>

</mapper>
