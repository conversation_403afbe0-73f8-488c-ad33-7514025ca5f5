<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.auto.AutoOrderMapper">
    <update id="logicDelete">
        update auto_order set enabled_flag =1 where order_no = #{fhOrderId}

    </update>

    <insert id="insertAutoOrderBatch" parameterType= "java.util.List">
        INSERT INTO auto_order (order_no, product_id, plan_id, qty,
        order_state,pay_status,pay_id,payment_time, commission_id, premium, amount, recommend_id,
        channel,customer_admin_id,sub_channel,total_amount,tax,submit_time, enabled_flag, create_time, update_time)
        VALUES
        <foreach collection="dtos" item="dto" index="index" separator=",">
            (#{dto.orderNo},#{dto.order.productId},#{dto.order.planId},
            #{dto.order.qty},#{dto.order.orderState},#{dto.order.payStatus},#{dto.order.payId},#{dto.order.paymentTime},
            #{dto.order.commissionId},#{dto.order.premium},#{dto.order.amount},
            #{dto.order.recommendId},#{dto.order.channel},#{dto.order.customerAdminId},
            #{dto.order.subChannel},#{dto.order.totalAmount},#{dto.order.tax},#{dto.order.submitTime},0,CURRENT_TIMESTAMP() , CURRENT_TIMESTAMP())
        </foreach>
    </insert>

    <update id="updateAutoOrderBatch" parameterType= "java.util.List">
        <foreach collection="dtos" item="dto" index="index" separator=";">
            update auto_order set product_id=#{dto.order.productId},plan_id=#{dto.order.planId},
            qty=#{dto.order.qty},commission_id=#{dto.order.commissionId},
            premium=#{dto.order.premium},amount=#{dto.order.amount},
            recommend_id=#{dto.order.recommendId},customer_admin_id=#{dto.order.customerAdminId},
            sub_channel=#{dto.order.subChannel},total_amount=#{dto.order.totalAmount},tax=#{dto.order.tax}
            where order_no=#{dto.orderNo}
        </foreach>
    </update>

    <update id="updateAutoOrderCommissionBatch" parameterType= "java.util.List">
        <foreach collection="dtos" item="dto" index="index" separator=";">
            update auto_order set commission_id=#{dto.commissionId}
            <if test='dto.carBoatTax != null'>
                , tax=#{dto.carBoatTax}
                , tax=#{dto.carBoatTax}
            </if>
            <if test='dto.recommendUserId != null and dto.recommendUserId!=""'>
                , recommend_id=#{dto.recommendUserId},customer_admin_id=#{dto.recommendUserId}
            </if>
            where order_no=#{dto.orderNo}
        </foreach>
    </update>

    <update id="updateAutoOrderCommissionBatch2" parameterType= "java.util.List">
        update auto_order
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="commission_id =case" suffix="end,">
                <foreach collection="dtos" item="item" index="index">
                    when order_no=#{item.orderNo} then #{item.commissionId}
                </foreach>
            </trim>

            <trim prefix="recommend_id =case" suffix="end,">
                <foreach collection="dtos" item="item" index="index">
                    <if test='item.recommendUserId != null and item.recommendUserId!=""'>
                    when order_no=#{item.orderNo} then #{item.recommendUserId}
                    </if>
                </foreach>
            </trim>

            <trim prefix="customer_admin_id =case" suffix="end,">
                <foreach collection="dtos" item="item" index="index">
                    <if test='item.recommendUserId != null and item.recommendUserId!=""'>
                    when order_no=#{item.orderNo} then #{item.recommendUserId}
                    </if>
                </foreach>
            </trim>

            <trim prefix="tax =case" suffix="end,">
                <foreach collection="dtos" item="item" index="index">
                    <if test='item.carBoatTax != null'>
                    when order_no=#{item.orderNo} then #{item.carBoatTax}
                    </if>
                </foreach>
            </trim>
        </trim>
        where order_no in
        <foreach collection="dtos" index="index" item="item" separator="," open="(" close=")">
            #{item.orderNo}
        </foreach>
    </update>
</mapper>
