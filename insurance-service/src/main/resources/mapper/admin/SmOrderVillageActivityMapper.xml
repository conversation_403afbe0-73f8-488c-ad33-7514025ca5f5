<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.order.SmOrderVillageActivityMapper">

    <select id="selectFourVoByFhOrderIds"
            resultType="com.cfpamf.ms.insur.admin.pojo.vo.order.activitie.SmOrderVillageActivityFourVo">
        SELECT
            a.fhOrderId AS fhOrderId,
            a.orderType AS orderType,
            a.customerAdminId AS customerAdminId,
            b.type AS type,
            b.manager_name AS managerName,
            b.manager_id_number AS managerIdNumber,
            b.manager_source AS managerSource,
            b.village_representative_id_number AS villageRepresentativeIdNumber,
            b.village_representative_name AS villageRepresentativeName
        FROM
            sm_order a
        LEFT JOIN sm_order_village_activity b ON a.fhOrderId = b.fh_order_id
        AND b.type = "200"
        AND b.deleted = 0
        WHERE
            a.fhOrderId
        IN
        <foreach collection="fhOrderIds" separator="," item="fhOrderId" open="(" close=")">
            #{fhOrderId,jdbcType=VARCHAR}
        </foreach>
    </select>
</mapper>
