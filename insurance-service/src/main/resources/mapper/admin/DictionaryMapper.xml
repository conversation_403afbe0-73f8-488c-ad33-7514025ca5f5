<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.DictionaryMapper">

    <select id="listDictionarys" resultType="com.cfpamf.ms.insur.admin.pojo.vo.DictionaryVO">
        SELECT * FROM dictionary WHERE 1=1
        <if test='onlyEnabled'>
            AND enabled_flag=0
        </if>
        <if test='type != null'>
            AND type = #{type}
        </if>
        <if test='keyword != null'>
            AND (code LIKE CONCAT('%',#{keyword},'%') OR name LIKE CONCAT('%',#{keyword},'%') )
        </if>
        order by sorting
    </select>
    <select id="listDictionarysByCode" resultType="com.cfpamf.ms.insur.admin.pojo.vo.DictionaryVO">
        SELECT * FROM dictionary WHERE 1=1
        <if test='onlyEnabled'>
            AND enabled_flag=0
        </if>
        <if test='type != null'>
            AND type = #{type}
        </if>
        <if test='channel != null'>
            AND code = #{channel}
        </if>
        order by sorting
    </select>

    <select id="listByTypes" resultType="com.cfpamf.ms.insur.admin.pojo.vo.DictionaryVO">
        SELECT * FROM dictionary WHERE
        enabled_flag=0
        AND type in
        <foreach collection="types" item="type" open="(" close=")" separator=",">#{type}</foreach>
    </select>
    <select id="getByTypeAndName" resultType="com.cfpamf.ms.insur.admin.pojo.vo.DictionaryVO">
        select * from dictionary where enabled_flag=0 and type=#{type} and code = #{code}
    </select>

    <insert id="insertDictionary" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO dictionary (type, code, name)
        VALUES (#{type}, #{code}, #{name})
    </insert>

    <update id="updateDictionary">
        UPDATE dictionary
        SET code=#{code},
            name=#{name}
        WHERE id = #{id}
    </update>

    <update id="deleteDictionary">
        UPDATE dictionary
        SET enabled_flag=1
        WHERE id = #{id}
    </update>
</mapper>
