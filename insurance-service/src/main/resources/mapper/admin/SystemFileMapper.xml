<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.SystemFileMapper">

    <insert id="insertSystemFile" useGeneratedKeys="true">
        INSERT INTO system_file(fileType,filePath,fileName,fileUrl,fileSize,create_by,update_by)
        VALUES
        (#{fileType},#{filePath},#{fileName},#{fileUrl},#{fileSize},#{createBy},#{updateBy})
    </insert>

    <update id="updateOrg">
      UPDATE organization SET batchNo=#{batchNo},hrOrgId=#{hrOrgId},hrParentId=#{hrParentId},orgType=#{orgType},orgName=#{orgName},orgCode=#{orgCode},orgPath=#{orgPath},update_time=CURRENT_TIMESTAMP() WHERE id=#{id}
    </update>

    <select id="listSystemFiles" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SystemFileVO">
        SELECT * FROM system_file
        WHERE enabled_flag=0
        <if test='fileName !=  null'>
            AND fileName LIKE CONCAT('%',#{fileName},'%')
        </if>
        <if test='filePath !=  null and fileName==null'>
            AND filePath = #{filePath}
        </if>
        <if test='filePath !=  null and fileName != null'>
            AND filePath LIKE CONCAT(#{filePath},'%')
        </if>
        <if test='fileFullName !=  null'>
            AND fileName=#{fileFullName}
        </if>
        <choose>
            <when test="fileName !=  null and fileName != ''">
                ORDER BY fileType ASC, create_time DESC
            </when>
            <otherwise>
                ORDER BY display_sort ASC
            </otherwise>
        </choose>
    </select>

    <select id="listFolderSystemFiles" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SystemFileVO">
        SELECT * FROM system_file
        WHERE enabled_flag=0
        AND filePath  LIKE  CONCAT(#{filePath},'%')
    </select>

    <select id="getSystemFileById" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SystemFileVO">
       SELECT * FROM system_file WHERE fileId = #{fileId}
    </select>

    <update id="updateSystemFileName">
        UPDATE system_file SET fileName = #{fileName}, update_by = #{updateBy} WHERE fileId = #{fileId}
    </update>

    <update id="updateSystemFilePath">
        UPDATE system_file SET filePath = #{filePath} WHERE fileId = #{fileId}
    </update>

    <update id="deleteSystemFile">
        UPDATE system_file
        SET enabled_flag = 1
        WHERE fileId IN
        <foreach item="item" index="index" open="(" separator="," close=")" collection="fileIds">
            #{item}
        </foreach>
    </update>

    <update id="updateSort">
        update system_file
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="display_sort =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when fileId =#{item.id} then #{item.displaySort}
                </foreach>
            </trim>
        </trim>
        where fileId in
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item.id}
        </foreach>
    </update>
</mapper>