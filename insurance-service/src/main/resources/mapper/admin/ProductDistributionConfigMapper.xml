<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.admin.distribution.config.dao.ProductDistributionConfigMapper">

    <sql id="Base_Column_List">
        id
        , product_id, mapper_name, enabled_flag, create_by, update_by, create_time, update_time
    </sql>
    <select id="getByProductId"
            resultType="com.cfpamf.ms.insur.admin.distribution.config.entity.ProductDistributionConfig">
        select
        <include refid="Base_Column_List"/>
        from sm_product_distribution_config
        where product_id = #{productId}
        and enabled_flag = 0
    </select>

    <select id="getProductDistributionConfigVoById"
            resultType="com.cfpamf.ms.insur.admin.distribution.config.vo.ProductDistributionConfigVo">

        select spdc.id,
               spdc.product_id,
               sp.productName,
               spdc.mapper_name,
               spdc.update_time as lastUpdateTime,
               au.userName      as lastUpdateUser,
               sc.companyName   as insuranceCompany
        from sm_product_distribution_config spdc
                 left join sm_product sp
                           on sp.id = spdc.product_id and sp.enabled_flag = 0
                 left join auth_user au
                           on au.userId = spdc.update_by and au.enabled_flag = 0
                 left join sm_company sc
                           on sp.companyId = sc.id and sc.enabled_flag = 0
        where spdc.id = #{id}
          and spdc.enabled_flag = 0

    </select>
    <select id="search"
            resultType="com.cfpamf.ms.insur.admin.distribution.config.vo.ProductDistributionConfigVo">

        select spdc.id,
        spdc.product_id,
        sp.productName,
        spdc.mapper_name,
        spdc.update_time as lastUpdateTime,
        au.userName as lastUpdateUser,
        sc.companyName as insuranceCompany
        from sm_product_distribution_config spdc
        left join sm_product sp
        on sp.id = spdc.product_id and sp.enabled_flag = 0
        left join auth_user au
        on au.userId = spdc.update_by and au.enabled_flag = 0
        left join sm_company sc
        on sp.companyId = sc.id and sc.enabled_flag = 0
        where spdc.enabled_flag = 0
        <if test="productName != null and productName!= '' ">
            and sp.productName like CONCAT('%',#{productName},'%')
        </if>
        <if test="productMapperName != null and productMapperName!= '' ">
            and spdc.mapper_name like CONCAT('%',#{productMapperName},'%')
        </if>
        <if test="companyId != null ">
            and sc.id = #{companyId}
        </if>
        order by spdc.create_time desc

    </select>
    <select id="getById"
            resultType="com.cfpamf.ms.insur.admin.distribution.config.entity.ProductDistributionConfig">
        select
        <include refid="Base_Column_List"/>
        from sm_product_distribution_config
        where id = #{id}
        and enabled_flag = 0
    </select>

    <update id="softDelete">
        update sm_product_distribution_config
        set enabled_flag = 1
        where id = #{id}
    </update>

</mapper>