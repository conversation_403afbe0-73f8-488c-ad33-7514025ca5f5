<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.aicheck.AkFamilyQuestionnaireMapper">
    <resultMap id="BaseResultMap" type="com.cfpamf.ms.insur.admin.pojo.po.aicheck.AkFamilyQuestionnaire">
        <id column="id" jdbcType="INTEGER" property="id" />
        <result column="channel" jdbcType="VARCHAR" property="channel" />
        <result column="pre_order_id" jdbcType="VARCHAR" property="preOrderId" />
        <result column="questionnaire_code" jdbcType="VARCHAR" property="questionnaireCode" />
        <result column="person_count" jdbcType="INTEGER" property="personCount" />
        <result column="actual_insurer_count" jdbcType="INTEGER" property="actualInsurerCount" />

        <result column="create_by" jdbcType="VARCHAR" property="createBy" />
        <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
        <result column="enabled_flag" jdbcType="INTEGER" property="enabledFlag" />
    </resultMap>
    <sql id="BaseTable">
        ak_family_questionnaire
    </sql>
</mapper>