<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.admin.renewal.dao.InsuranceRenewMapper">
    <!--    通过订单的保单记录id查找订单信息 end -->
    <resultMap id="OrderDetailVoMap" type="com.cfpamf.ms.insur.admin.renewal.vo.OrderDetailVo">
        <!--        订单信息映射-->
        <result column="id" property="orderInsuredId"/>
        <result column="fhOrderId" property="fhOrderId"/>
        <result column="planId" property="planId"/>
        <result column="productId" property="productId"/>
        <result column="productAttrCode" property="productAttrCode"/>
        <result column="productName" property="productName"/>
        <result column="productAttrCode" property="productAttrCode"/>
        <result column="applicantPersonName" property="applicantPersonName"/>
        <result column="insuredPersonName" property="insuredPersonName"/>
        <result column="insuredAmount" property="insuredAmount"/>
        <result column="policyNo" property="policyNo"/>
        <result column="invalidTime" property="invalidTime"/>
        <result column="startTime" property="startTime"/>
        <result column="recommendUserName" property="recommendUserName"/>
        <result column="surplusDay" property="surplusDay"/>
        <result column="graceDay" property="graceDay"/>
        <result column="renewalOrderId" property="renewalOrderId"/>
        <result column="renewalPolicyNo" property="renewalPolicyNo"/>
        <result column="renewalStatus" property="renewalStatus"/>
        <result column="renewalPlatform" property="renewalPlatform"/>

        <!--        被保人信息映射-->
        <result column="insuredPersonName" property="insuredPerson.name"/>
        <result column="insuredPersonEmail" property="insuredPerson.email"/>
        <result column="insuredPersonIdNumber" property="insuredPerson.idCard"/>
        <result column="insuredPersonCellPhone" property="insuredPerson.mobileNumber"/>
        <result column="insuredPersonOccupationCode" property="insuredPerson.occupationCode"/>
        <result column="insuredPersonOccupationName" property="insuredPerson.occupationName"/>
        <result column="insuredPersonBirthday" property="insuredPerson.birthday"/>
        <!--        投保人信息映射-->
        <result column="applicantPersonName" property="applicantPerson.name"/>
        <result column="applicantPersonEmail" property="applicantPerson.email"/>
        <result column="applicantPersonIdNumber" property="applicantPerson.idCard"/>
        <result column="applicantPersonCellPhone" property="applicantPerson.mobileNumber"/>
        <result column="applicantPersonBirthday" property="applicantPerson.birthday"/>
    </resultMap>

    <insert id="insertInsuranceRenew">
        INSERT INTO insurance_renewal (old_order_id, old_policy_no, new_order_id, new_policy_no, applicant_person_name,
                                       applicant_id_number,insured_person_name,insured_id_number,
                                       ins_status, grace_day, surplus_day, before_expiration_day,
                                       after_expiration_day, total_amount,
                                       product_id, plan_id, product_attr_code, product_name, channel,invalid_time
                                       ,start_time,over_reason,customer_admin_id,wxOpenId,agentId,recommend_id,is_loan_flag,sales_type,risk_category_1,risk_category_2)
        SELECT old_order_id,
               old_policy_no,
               new_order_id,
               new_policy_no,
               applicant_person_name,
               applicant_id_number,
               insured_person_name,
               insured_id_number,
               ins_status,
               grace_day,
               surplus_day,
               before_expiration_day,
               after_expiration_day,
               total_amount,
               product_id,
               plan_id,
               product_attr_code,
               product_name,
               channel,
               invalid_time,
               start_time,
               over_reason,
               customer_admin_id,
               wx_open_id,
               agentId,
               recommend_id,
                is_loan_flag,sales_type,risk_category_1,risk_category_2
        FROM dwd_safes_insurance_renewal t
        ON DUPLICATE KEY UPDATE insurance_renewal.new_order_id           = IF(insurance_renewal.new_order_id='',t.new_order_id,insurance_renewal.new_order_id),
                                 insurance_renewal.new_policy_no         = IF(insurance_renewal.new_policy_no='',t.new_policy_no,insurance_renewal.new_policy_no),
                                insurance_renewal.ins_status             = if(insurance_renewal.ins_status='renewed',insurance_renewal.ins_status,t.ins_status),
                                insurance_renewal.grace_day              = t.grace_day,
                                insurance_renewal.surplus_day            = t.surplus_day,
                                insurance_renewal.before_expiration_day              = t.before_expiration_day,
                                insurance_renewal.after_expiration_day   = t.after_expiration_day,
                                insurance_renewal.over_reason           = t.over_reason,
                                insurance_renewal.total_amount = t.total_amount,
                                insurance_renewal.insured_person_name = t.insured_person_name,
                                insurance_renewal.customer_admin_id = t.customer_admin_id,
                                insurance_renewal.wxOpenId = t.wx_open_id,
                                insurance_renewal.invalid_time = t.invalid_time,
                                insurance_renewal.start_time = t.start_time,
                                insurance_renewal.agentId = t.agentId,
                                insurance_renewal.applicant_person_name = t.applicant_person_name,
                                insurance_renewal.applicant_id_number = t.applicant_id_number,
                                insurance_renewal.insured_id_number = t.insured_id_number,
                                insurance_renewal.product_id = t.product_id,
                                insurance_renewal.plan_id = t.plan_id,
                                insurance_renewal.product_attr_code = t.product_attr_code,
                                insurance_renewal.product_name = t.product_name,
                                insurance_renewal.channel = t.channel,
                                insurance_renewal.recommend_id = t.recommend_id,
                                insurance_renewal.is_loan_flag = t.is_loan_flag,
                                insurance_renewal.sales_type = t.sales_type,
                                insurance_renewal.risk_category_1 = t.risk_category_1,
                                insurance_renewal.risk_category_2 = t.risk_category_2
    </insert>

    <update id="updateInsStatusByPolicyNo">
        update insurance_renewal
        set  new_order_id=#{newOrderId},new_policy_no=#{newPolicyNo}
        <if test="status!=null and status != @com.cfpamf.ms.insur.admin.renewal.enums.OrderRenewalStatus@CANCEL">
            ,ins_status=LCASE(#{status})
        </if>
        <if test="status!=null and status == @com.cfpamf.ms.insur.admin.renewal.enums.OrderRenewalStatus@CANCEL">
            ,ins_status='waited'
        </if>
        <if test="planId!=null">
            ,plan_id = #{planId}
        </if>
        <if test="productId!=null">
            ,product_id = #{productId}
        </if>
        <if test="startTime!=null">
            ,start_time = #{startTime}
        </if>
        where old_policy_no = #{oldPolicyNo}
    </update>

    <!--    搜索微信端续保订单 start-->
    <select id="searchWxRenewalOrderVo"
            resultType="com.cfpamf.ms.insur.admin.renewal.vo.InsuranceRenewedOrderVo">
        SELECT
        t2.id as orderInsuredId,
        t1.old_order_id fhOrderId,
        t1.old_policy_no as policyNo,
        t1.applicant_person_name as applicantPersonName,
        t1.plan_id planId,
        t1.product_attr_code productAttrCode ,
        t1.product_name productName,
        t1.product_id as productId,
        t1.channel,
        t1.insured_person_name as insuredPersonName,
        t1.total_amount as insuredAmount,
        t1.invalid_time as invalidTime,
        t1.start_time as startTime,
        t5.userName as recommendUserName,
        (DATEDIFF(NOW(),t1.invalid_time) - 1) as surplusDay,
        t1.after_expiration_day as graceDay,
        t1.before_expiration_day as advanceDay,
        t7.userName as customerManager,
        t7.organizationName as customerManagerOrganization,
        t7.regionName as customerManagerRegion,
        if(DATEDIFF(NOW(),t1.invalid_time) -1 >= 0 , t1.after_expiration_day - (DATEDIFF(NOW(),t1.invalid_time) - 1) ,
        ABS(DATEDIFF(NOW(),t1.invalid_time) -1) + 1000 ) as sortField,
        t1.over_reason as overReason,
        t1.new_order_id AS renewalOrderId,
        t8.intention,
        t8.service_mode as serviceMode,
        drp.renewal_intention_eval as renewalIntentionEval,
        if(t1.is_loan_flag='1','主营','非主营') as loanFlag,
        t9.renewal_platform as renewalPlatform

        FROM insurance_renewal t1

        LEFT join sm_order_insured t2 on t2.idNumber = t1.insured_id_number and SUBSTRING_INDEX(t2.fhorderId, '_', 1)= t1.old_order_id

        LEFT JOIN auth_user t5 on t1.recommend_id = t5.userId AND t5.enabled_flag = 0

        LEFT JOIN auth_user t7 on t1.customer_admin_id = t7.userId AND t7.enabled_flag = 0

        LEFT JOIN sm_order_renewal_follow t8 on t8.policy_no = t1.old_policy_no and newest = 1

        LEFT JOIN sm_renewal_config t9 on t9.product_id = t1.product_id and t9.enabled_flag = 0

        left join dwd_policy_renewal_indicator_data drp on drp.policy_no = t1.old_policy_no

        WHERE 1=1
        and t2.appStatus = '1'
        <if test="insStatus != null and insStatus!=''">
            AND t1.ins_status = #{insStatus}
        </if>
        <if test='userId != null'>
            AND t1.customer_admin_id=#{userId}
        </if>
        <if test='agentId != null'>
            AND t1.agentId=#{agentId}
        </if>
        <if test='openId != null'>
            AND t1.wxOpenId=#{openId}
        </if>
        <if test='customerAdminId != null'>
            AND t1.customer_admin_id=#{customerAdminId}
        </if>
        <if test='startTime != null '>
            <choose>
                <when test="insStatus == 'waited'">
                    <![CDATA[ AND #{startTime} <=  date_add(t1.invalid_time, interval t1.after_expiration_day day) ]]>
                </when>
                <otherwise>
                    <![CDATA[ AND  t1.invalid_time >= #{startTime} ]]>
                </otherwise>
            </choose>
        </if>
        <if test='endTime  != null'>
            <choose>
                <when test="insStatus == 'waited'">
                    <![CDATA[ AND  #{endTime} >=  date_add(t1.invalid_time, interval t1.before_expiration_day day) ]]>
                </when>
                <otherwise>
                    <![CDATA[ AND  t1.invalid_time <=  #{endTime}  ]]>
                </otherwise>
            </choose>
        </if>
        <if test='channel != null'>
            AND t1.channel=#{channel}
        </if>
        <if test='keyword != null'>
            AND ( t1.applicant_person_name LIKE CONCAT(#{keyword},'%')
            OR t1.insured_person_name LIKE CONCAT(#{keyword},'%')
            OR t1.old_policy_no LIKE CONCAT(#{keyword},'%')
            OR t1.product_Name LIKE CONCAT(#{keyword},'%') )
        </if>
        <if test='intention != null and intention != ""'>
            <if test = 'intention == "noFollowUp"'>
                and t8.id is null
            </if>
            <if test = 'intention != "noFollowUp"'>
                and t8.intention = #{intention}
            </if>
        </if>

<!--        <if test='intentionList != null and intentionList.size>0 '>-->
<!--            AND (-->
<!--            <if test='intentionList.size > 0'>-->
<!--                t8.intention in-->
<!--                <foreach collection="intentionList" item="intent" open="(" close=")" index="index" separator=",">-->
<!--                    #{intent}-->
<!--                </foreach>-->
<!--            </if>-->
<!--            <if test='intentionList.contains("noFollowUp")'>-->
<!--                <if test='intentionList.size > 1'> OR </if>-->
<!--                t8.id IS NULL-->
<!--            </if>-->
<!--            )-->
<!--        </if>-->

        <if test='intentionList != null and intentionList.size > 0'>
            AND (
            <if test='intentionList.contains("noFollowUp")'>
                t8.id IS NULL
                <if test='intentionList.size > 1'> OR </if>
            </if>
            <if test='intentionList.{?#this != "noFollowUp"}.size() > 0'>
                t8.intention IN
                <foreach collection="intentionList" item="intent" open="(" close=")" separator=",">
                    <if test='intent != "noFollowUp"'>
                        #{intent}
                    </if>
                </foreach>
            </if>
            )
        </if>

        <if test='renewalIntentionEvalList != null and renewalIntentionEvalList.size>0 '>
            AND drp.renewal_intention_eval IN
            <foreach collection="renewalIntentionEvalList" item="item" open="(" close=")" index="index" separator=",">
                #{item}
            </foreach>
        </if>

        <choose>
            <when test="overReasonFlag == 1">
                and t1.over_reason != ''
            </when>
            <when test="overReasonFlag == 2">
                and t1.over_reason = ''
            </when>
            <otherwise>
                and 1=1
            </otherwise>
        </choose>
        group by t1.old_policy_no
        <choose>
            <when test="insStatus == 'waited'">
                <choose>
                    <when test=" timeSortType=='DESC'">
                        ORDER BY date_add(t1.invalid_time, INTERVAL 1 DAY) DESC,t1.id desc
                    </when>
                    <when test="timeSortType=='ASC'">
                        ORDER BY date_add(t1.invalid_time, INTERVAL 1 DAY) ASC,t1.id desc
                    </when>
                    <when test=" insuredAmountSortType == 'DESC'">
                        ORDER BY t1.total_Amount DESC,t1.id desc
                    </when>
                    <when test="insuredAmountSortType == 'ASC'">
                        ORDER BY t1.total_Amount ASC,t1.id desc
                    </when>
                    <otherwise>
                        ORDER BY date_add(t1.invalid_time, INTERVAL 1 DAY) ASC,t1.id desc
                    </otherwise>
                </choose>
            </when>
            <otherwise>
                <choose>
                    <when test=" timeSortType=='DESC'">
                        ORDER BY t1.invalid_time DESC,t1.id desc
                    </when>
                    <when test="timeSortType=='ASC'">
                        ORDER BY t1.invalid_time ASC,t1.id desc
                    </when>
                    <when test=" insuredAmountSortType == 'DESC'">
                        ORDER BY t1.total_Amount DESC,t1.id desc
                    </when>
                    <when test="insuredAmountSortType == 'ASC'">
                        ORDER BY t1.total_Amount ASC,t1.id desc
                    </when>
                    <otherwise>
                        ORDER BY t1.invalid_time DESC,t1.id desc
                    </otherwise>
                </choose>
            </otherwise>
        </choose>
    </select>


    <!--    搜索微信端续保订单 start-->
    <select id="listHomePageRenewedOrder"
            resultType="com.cfpamf.ms.insur.admin.renewal.vo.HomePageRenewedOrderVo">
        SELECT

        t1.applicant_person_name as applicantPersonName,
        t1.product_name productName,
        t1.invalid_time as invalidTime,
        drp.renewal_intention_eval as renewalIntentionEval

        FROM insurance_renewal t1

        LEFT join sm_order_insured t2 on t2.idNumber = t1.insured_id_number and SUBSTRING_INDEX(t2.fhorderId, '_', 1)= t1.old_order_id



<!--        LEFT JOIN auth_user t7 on t1.customer_admin_id = t7.userId AND t7.enabled_flag = 0-->

<!--        LEFT JOIN sm_order_renewal_follow t8 on t8.policy_no = t1.old_policy_no and newest = 1-->

<!--        LEFT JOIN sm_renewal_config t9 on t9.product_id = t1.product_id and t9.enabled_flag = 0-->

        LEFT join dwd_policy_renewal_indicator_data drp on t1.old_policy_no = drp.policy_no
        WHERE t1.ins_status = 'waited'
        and t1.customer_admin_id = #{customerAdminId}
        and t2.appStatus = '1'
        and drp.renewal_intention_eval in ('高','中')
        and drp.renewal_follow_info in('未跟进','考虑','愿意续保','联系不上')
            order by drp.renewal_intention_eval desc


<!--        SELECT-->
<!--        drp.applicant_holder_name as applicantPersonName,-->
<!--        drp.policy_product_name productName,-->
<!--        drp.invalid_time as invalidTime,-->
<!--        drp.renewal_intention_eval as renewalIntentionEval-->

<!--        FROM-->
<!--        dwd_policy_renewal_indicator_data drp-->
<!--        WHERE drp.customer_manager_id = #{customerAdminId}-->
<!--        and drp.renewal_intention_eval in ('高','中')-->
<!--        and  drp.renewal_follow_info in('未跟进','考虑','愿意续保','联系不上')-->
<!--        order by drp.renewal_intention_eval desc-->


    </select>

    <!--    搜索微信端续保订单 start-->
    <select id="countHomePageRenewedOrder"
            resultType="java.lang.Integer">
        <!--        SELECT-->

        <!--        count(1)-->

        <!--        FROM insurance_renewal t1-->

        <!--        LEFT join sm_order_insured t2 on t2.idNumber = t1.insured_id_number and SUBSTRING_INDEX(t2.fhorderId, '_', 1)= t1.old_order_id-->



        <!--        LEFT JOIN auth_user t7 on t1.customer_admin_id = t7.userId AND t7.enabled_flag = 0-->

        <!--        LEFT JOIN sm_order_renewal_follow t8 on t8.policy_no = t1.old_policy_no and newest = 1-->

        <!--        LEFT JOIN sm_renewal_config t9 on t9.product_id = t1.product_id and t9.enabled_flag = 0-->

        <!--        LEFT join dwd_policy_renewal_indicator_data drp on t1.old_policy_no = drp.policy_no-->
        <!--        WHERE 1=1-->
        <!--        and t1.customer_admin_id = #{customerAdminId}-->
        <!--        and t2.appStatus = '1'-->
        <!--        and drp.renewal_intention_eval in ('高','中')-->
        <!--        and  (t8.id is null or t8.intention in('willing','consider','lose_contact'))-->


        SELECT
        count(1)

        FROM insurance_renewal t1

        LEFT join sm_order_insured t2 on t2.idNumber = t1.insured_id_number and SUBSTRING_INDEX(t2.fhorderId, '_', 1)= t1.old_order_id



        LEFT join dwd_policy_renewal_indicator_data drp on t1.old_policy_no = drp.policy_no
        WHERE t1.ins_status = 'waited'
        and t1.customer_admin_id = #{customerAdminId}
        and t2.appStatus = '1'
        and drp.renewal_intention_eval in ('高','中')
        and drp.renewal_follow_info in('未跟进','考虑','愿意续保','联系不上')
        order by drp.renewal_intention_eval desc


    </select>



    <!--    搜索微信端待续保订单条数 start-->
    <select id="searchWxWaitRenewalOrderVoCount"
            resultType="java.lang.Integer">
        SELECT
            count(DISTINCT t1.old_policy_no)
        FROM insurance_renewal t1

        LEFT join sm_order_insured t2 on t2.idNumber = t1.insured_id_number and SUBSTRING_INDEX(t2.fhorderId, '_', 1)= t1.old_order_id

        LEFT JOIN auth_user t5 on t1.recommend_id = t5.userId AND t5.enabled_flag = 0

        LEFT JOIN auth_user t7 on t1.customer_admin_id = t7.userId AND t7.enabled_flag = 0

        LEFT JOIN sm_order_renewal_follow t8 on t8.policy_no = t1.old_policy_no and newest = 1

        WHERE t1.ins_status = 'waited' and t2.appStatus = '1'
        <if test='userId != null'>
            AND t1.customer_admin_id=#{userId}
        </if>
        <if test='agentId != null'>
            AND t1.agentId=#{agentId}
        </if>
        <if test='openId != null'>
            AND t1.wxOpenId=#{openId}
        </if>
        <if test='customerAdminId != null'>
            AND t1.customer_admin_id=#{customerAdminId}
        </if>
    </select>
    <!--    搜索微信端待续保订单 end-->

    <select id="queryOrderDetail" resultMap="OrderDetailVoMap"
            parameterType="com.cfpamf.ms.insur.weixin.pojo.query.order.OrderDetailQuery">
        SELECT
        t1.id as orderInsuredId,
        t1.old_order_id fhOrderId,
        t1.plan_id planId,
        t1.product_attr_code productAttrCode,
        t1.product_name productName,
        t1.channel,
        t4.personName as applicantPersonName,
        t4.cellPhone as applicantPersonCellPhone,
        t4.email as applicantPersonEmail,
        t4.idNumber as applicantPersonIdNumber,
        t4.birthday as applicantPersonBirthday,
        t3.id as productId,
        t1.ins_status as renewalStatus,

        t1.total_Amount as insuredAmount,
        t1.old_policy_no as policyNo,
        t1.invalid_time as invalidTime,
        t1.start_Time as startTime,
        t5.userName as recommendUserName,
        (DATEDIFF(NOW(), t1.invalid_time) - 1) as surplusDay,
        t1.after_expiration_day as graceDay,
        t6.companyName as companyName,
        t1.over_reason as overReason,
        t1.new_order_id as renewalOrderId,
        t1.new_policy_no as renewalPolicyNo,
        t7.renewal_platform as renewalPlatform
        FROM insurance_renewal t1
        LEFT JOIN sm_product t3 on t3.id = t1.product_Id
        LEFT JOIN sm_order_applicant t4 on t1.old_order_id = t4.fhOrderId
        LEFT JOIN auth_user t5 on t1.recommend_Id = t5.userId AND t5.enabled_flag = 0
        LEFT JOIN sm_company t6 on t3.companyId = t6.id AND t6.enabled_flag = 0
        LEFT JOIN sm_renewal_config t7 on t7.product_id = t1.product_id and t7.enabled_flag = 0

        WHERE t1.old_policy_no = #{policyNo}
        <if test='userId != null'>
            AND t1.customer_Admin_Id=#{userId}
        </if>
        <if test='agentId != null'>
            AND t1.agentId=#{agentId}
        </if>
        <if test='customerAdminId != null'>
            AND t1.customer_Admin_Id=#{customerAdminId}
        </if>
        LIMIT 1
    </select>

    <select id = "queryInsuredsByPolicyNo" resultType="com.cfpamf.ms.insur.admin.renewal.vo.PersonVo">
        select
            t1.id as orderInsuredId,
            t1.personName as name,
            t1.cellPhone as mobileNumber,
            t1.email as email,
            t1.idNumber as idCard,
            t1.birthday as birthday,
            t1.occupationCode as occupationCode,
            t7.occupationName as occupationName,
            t1.downloadURL as downloadURL,
            case when INSTR (t1.fhorderid ,'_') =0 then 0 else SUBSTRING_INDEX(t1.fhorderid,'_',-1) end orderIdIndex,
            t3.fhOrderId,
            ifnull(t2.th_policy_no, t1.policyNo) as policyNo,
            t2.unit_price as unitPrice,
            t2.qty
        from sm_order_insured t1
        left join sm_order_item t2 on t2.fh_order_id = t1.fhorderid and t2.id_number = t1.idNumber
                                          and t2.app_status = t1.appStatus
        LEFT JOIN sm_order t3 on t1.fhOrderId = t3.fhOrderId
        LEFT JOIN sm_product t4 on t4.id = t3.productId
        LEFT JOIN sm_company_occupation t7
        ON t1.occupationCode = t7.occupationCode and t7.companyId = t4.companyId and
        t7.enabled_flag = 0
        where 1=1
        <choose>
            <when test="productAttrCode == 'group'">
                and t1.appStatus = 1
                and t2.th_policy_no = #{policyNo}
            </when>
            <otherwise>
                and t1.policyNo = #{policyNo}
            </otherwise>
        </choose>
        order by t2.type asc
    </select>

    <!--查询团险批减被保人信息-->
    <select id = "queryInsuredsCancelByPolicyNo" resultType="com.cfpamf.ms.insur.admin.renewal.vo.RenewalOrderItemVO">
        select
            t.id_number as idNumber,
            t.create_time as createTime,
            t.th_policy_no as thPolicyNo,
            t.fh_order_id as fhOrderId,
            case when INSTR (t.fh_order_id ,'_') =0 then 0 else SUBSTRING_INDEX(t.fh_order_id,'_',-1) end orderIdIndex
        from sm_order_item t
        where t.app_Status = 4
        and t.th_policy_no = #{policyNo}
    </select>

    <update id="updateByPolicyNo">
        update insurance_renewal set over_reason = #{overRenewalReason} where old_policy_no = #{policyNo} and ins_status = 'cancel'
    </update>

    <select id="waitRenewalStatistics" resultType="java.lang.Integer">
        SELECT count(distinct t1.old_order_id)
        FROM insurance_renewal t1
        LEFT join sm_order_insured t2 on t2.idNumber = t1.insured_id_number and SUBSTRING_INDEX(t2.fhorderId, '_', 1)= t1.old_order_id
        where t1.ins_status = 'waited' and t2.appStatus = '1'
        <if test='startTime != null '>
            <![CDATA[ AND #{startTime} <=  date_add(t1.invalid_time, interval t1.after_expiration_day day) ]]>

        </if>
        <if test='endTime  != null'>
            <![CDATA[ AND  #{endTime} >=  date_add(t1.invalid_time, interval t1.before_expiration_day day) ]]>

        </if>
        <if test='userId != null'>
            AND t1.customer_Admin_Id=#{userId}
        </if>
        <if test='agentId != null'>
            AND t1.agentId=#{agentId}
        </if>
        <if test='openId != null'>
            AND t1.wxOpenId=#{openId}
        </if>

    </select>
    <!--搜索已断保订单 end-->

    <select id="overRenewalStatistics" resultType="java.lang.Integer">
        SELECT
        count(1)
        FROM insurance_renewal t1
        WHERE t1.ins_status = 'cancel'
        <if test='userId != null'>
            AND t1.customer_Admin_Id=#{userId}
        </if>
        <if test='agentId != null'>
            AND t1.agentId=#{agentId}
        </if>
        <if test='openId != null'>
            AND t1.wxOpenId=#{openId}
        </if>
        <if test='startTime != null '>
            <![CDATA[ AND  t1.invalid_time >= #{startTime} ]]>
        </if>
        <if test='endTime  != null'>
            <![CDATA[ AND  t1.invalid_time <=  #{endTime}  ]]>
        </if>
    </select>

    <select id="renewedStatistics" resultType="java.lang.Integer">
        SELECT
        count(1)
        FROM
        insurance_renewal t1
        WHERE t1.ins_status = 'renewed'
        <if test='userId != null'>
            AND t1.customer_Admin_Id=#{userId}
        </if>
        <if test='agentId != null'>
            AND t1.agentId=#{agentId}
        </if>
        <if test='openId != null'>
            AND t1.wxOpenId=#{openId}
        </if>
        <if test='startTime != null '>
            <![CDATA[ AND  t1.invalid_time >= #{startTime} ]]>
        </if>
        <if test='endTime  != null'>
            <![CDATA[ AND  t1.invalid_time <=  #{endTime}  ]]>
        </if>
    </select>

    <!--    搜索续保订单（后端） start-->
    <select id="searchRenewalOrderVo" resultType="com.cfpamf.ms.insur.admin.renewal.vo.InsuranceRenewedOrderVo">
        SELECT
        t1.id as orderInsuredId,
        t1.old_order_id fhOrderId,
        t1.plan_id planId,
        t1.product_attr_code productAttrCode ,
        t1.product_name productName,
        t1.channel,
        t1.product_id as productId,
        t1.applicant_person_name as applicantPersonName,
        t1.insured_person_name as insuredPersonName,
        t1.total_Amount as insuredAmount,
        t1.old_policy_no as policyNo,
        t1.invalid_time as invalidTime,
        t1.start_Time as startTime,
        t5.userName as recommendUserName,
        (DATEDIFF(NOW(),t1.invalid_time) - 1) as surplusDay,
        t1.after_expiration_day as graceDay,
        t7.userName as customerManager,
        t7.organizationName as customerManagerOrganization,
        t7.regionName as customerManagerRegion,
        t8.intention as intention,
        t8.service_mode as serviceMode,
        t1.new_order_id as renewalOrderId,
        t1.over_reason as overReason,
        t14.label_value as selfInsured,
        t1.applicant_id_number as applicantIdNumber
        FROM insurance_renewal t1

        LEFT join sm_order_insured t2 on t2.idNumber = t1.insured_id_number and SUBSTRING_INDEX(t2.fhorderId, '_', 1) = t1.old_order_id

        LEFT JOIN auth_user t5 on t1.recommend_Id = t5.userId AND t5.enabled_flag = 0

        LEFT JOIN auth_user t7 on t1.customer_Admin_Id = t7.userId AND t7.enabled_flag = 0

        LEFT JOIN sm_order_renewal_follow t8 on t8.policy_no = t1.old_policy_no and newest = 1

        left join sm_order_label t14 on t14.fh_order_id = t1.old_order_id  and t14.label_type='self_insurance'
        WHERE
        1=1 and t2.appStatus = '1'
        <if test="insStatus != null and insStatus!=''">
            AND t1.ins_status = #{insStatus}
        </if>
        <if test="startInvalidTime !=  null">
            AND t1.invalid_time >= #{startInvalidTime}
        </if>
        <if test="endInvalidTime !=  null">
            and  <![CDATA[ t1.invalid_time <= #{endInvalidTime}]]>
        </if>

        <if test="policyNo !=  null and policyNo != ''">
            AND t1.old_policy_no like CONCAT(#{policyNo},'%')
        </if>

        <if test="productName !=  null and productName != ''">
            AND t1.product_name like CONCAT(#{productName},'%')
        </if>

        <if test='applicantdName != null '>
            <if test="applicantdType == 1">
                AND t1.applicant_person_name LIKE CONCAT(#{applicantdName},'%')
            </if>
            <if test="applicantdType == 2">
                AND t1.applicant_id_number LIKE CONCAT(#{applicantdName},'%')
            </if>
        </if>

        <if test='insuredName != null'>
            <if test="insuredType == 1">
                AND t1.insured_person_name LIKE CONCAT(#{insuredName},'%')
            </if>
            <if test="insuredType == 2">
                AND t1.insured_id_number LIKE CONCAT(#{insuredName},'%')
            </if>
        </if>

        <if test="productAttrCode != null and productAttrCode != ''">
            AND t1.product_Attr_Code = #{productAttrCode}
        </if>
        <if test='recommendId != null'>
            AND t1.recommend_Id= #{recommendId}
        </if>

        <if test='customerManagerId != null'>
            AND t1.customer_Admin_Id= #{customerManagerId}
        </if>
        <if test='regionName != null'>
            AND t5.regionName=#{regionName}
        </if>
        <if test='orgName != null'>
            AND t5.organizationFullName=#{orgName}
        </if>
        <if test='orgPath !=null '>
            and t5.orgCode in (select orgCode from organization where orgPath like CONCAT(#{orgPath},'%'))
        </if>
        <if test='intention != null and intention != ""'>
            <if test = 'intention == "noFollowUp"'>
                and t8.id is null
            </if>
            <if test = 'intention != "noFollowUp"'>
                and t8.intention = #{intention}
            </if>
        </if>
        group by t1.old_order_id
        ORDER BY t1.invalid_time ASC,t1.id desc
    </select>

    <select id = "getPersonTransferPolicy" resultType="com.cfpamf.ms.insur.admin.renewal.vo.TransferPolicyVo">
        select t.policyNo,t.idNumber,t1.endTime,t2.productName,t1.fhOrderId,t1.productId,t1.planId
        from sm_order_insured t
        left join sm_order t1 on t1.fhOrderId = t.fhOrderId
        left join sm_product t2 on t1.productId = t2.id
        where t.idNumber = #{idNumber}
        and t2.productAttrCode = 'person'
        and YEAR(t1.endTime) = YEAR(#{endTime})+1
        and t.appStatus = 1
        group by t.policyNo
    </select>


    <select id = "autoQueryPersonTransferPolicy" resultType="com.cfpamf.ms.insur.admin.renewal.vo.TransferPolicyVo">
        select distinct t.policyNo,t.idNumber,t1.endTime,t4.productName,t1.fhOrderId,t1.productId,t1.planId,t1.paymentTime,t1.startTime,
        t1.totalAmount,t1.submitTime
        from sm_order_insured t
        left join sm_order t1 on t1.fhOrderId = t.fhOrderId
        left join sm_product t4 on t1.productId = t4.id
        left join dwd_commodity_main_product_name_map t2 on t1.productId = t2.zhnx_product_id and t1.planId = t2.zhnx_plan_id
        where t.idNumber = #{idNumber}
        and t4.productAttrCode = 'person'
        <![CDATA[ and t1.paymentTime >=#{startDate} and  t1.paymentTime <= #{endDate}]]>
        and t.appStatus = 1
        and t1.endTime > now()
        and t1.startTime>#{startTime}
        and t2.risk_name= #{riskCategory2}
        group by t.policyNo
    </select>

    <select id = "autoQueryGroupTransferPolicy" resultType="com.cfpamf.ms.insur.admin.renewal.vo.TransferPolicyVo">
        select t2.th_policy_no policyNo,t2.id_Number,t1.endTime,t4.productName,
        REVERSE(SUBSTR( REVERSE( t1.fhOrderId ) FROM INSTR( REVERSE( t1.fhOrderId ), '_' ) + 1 )) fhOrderId,
        t1.productId,t1.planId,t1.paymentTime,t1.startTime,
        t1.totalAmount,t1.submitTime
        from sm_order_applicant t
        left join sm_order t1 on t1.fhOrderId = t.fhOrderId
        left join sm_order_item t2 on t2.fh_order_id = t1.fhOrderId
        left join sm_product t4 on t1.productId = t4.id
        left join dwd_commodity_main_product_name_map t3 on t1.productId = t3.zhnx_product_id and t1.planId = t3.zhnx_plan_id
        where t.idNumber = #{idNumber}
        and t4.productAttrCode != 'person'
        <![CDATA[ and t1.paymentTime >=#{startDate} and  t1.paymentTime <= #{endDate}]]>
        and t1.endTime > now()
        and t1.startTime>#{startTime}
        and t3.risk_name= #{riskCategory2}
        group by t2.th_policy_no
    </select>

    <select id = "autoQueryCarTransferPolicy" resultType="com.cfpamf.ms.insur.admin.renewal.vo.TransferPolicyVo">
        select distinct t.policyNo,t.idNumber,t1.endTime,t4.productName,t1.fhOrderId,t1.productId,t1.planId,t1.paymentTime,t1.startTime,
        t1.totalAmount,t1.submitTime,t5.personName as applicantPersonName,t5.idNumber as applicantIdNumber
        from sm_order_insured t
        left join sm_order t1 on t1.fhOrderId = t.fhOrderId
        left join sm_product t4 on t1.productId = t4.id
        left join dwd_commodity_main_product_name_map t2 on t1.productId = t2.zhnx_product_id and t1.planId = t2.zhnx_plan_id
        left join sm_order_applicant t5 on t5.fhOrderId = t1.fhOrderId
        where t4.productAttrCode = 'person'
        <![CDATA[ and t1.paymentTime >=#{startDate} and  t1.paymentTime <= #{endDate}]]>
        and t.appStatus = 1
        and t1.endTime > now()
        and t1.startTime>#{startTime}
        and t2.risk_name= #{riskCategory2}
        group by t.policyNo
    </select>

    <select id = "getGroupTransferPolicy" resultType="com.cfpamf.ms.insur.admin.renewal.vo.TransferPolicyVo">
        select t2.th_policy_no policyNo,t2.id_Number,t1.endTime,t3.productName,
               REVERSE(SUBSTR( REVERSE( t1.fhOrderId ) FROM INSTR( REVERSE( t1.fhOrderId ), '_' ) + 1 )) fhOrderId,
               t1.productId,t1.planId
        from sm_order_applicant t
        left join sm_order t1 on t1.fhOrderId = t.fhOrderId
        left join sm_order_item t2 on t2.fh_order_id = t1.fhOrderId
        left join sm_product t3 on t1.productId = t3.id
        where t.idNumber = #{idNumber}
        and t3.productAttrCode != 'person'
        and t1.endTime > #{endTime}
        group by t2.th_policy_no
    </select>
    <select id="selectByPlanId" resultType="com.cfpamf.ms.insur.admin.pojo.dto.renewal.RenewCountDTO">
        select plan_id,count(distinct old_policy_no) as count
        from insurance_renewal as ir
        where ir.ins_status='waited'
        group by plan_id
    </select>


    <select id = "listWaitInsuranceRenewal" resultType="com.cfpamf.ms.insur.admin.pojo.po.renewal.InsuranceRenewPo">
        select  t.*
        from insurance_renewal t
        left join phoenix_emp_todo p on p.target_id = t.old_policy_no   and p.biz_type = 'RENEW_SHORT'
        where t.ins_status ='waited'
        and t.invalid_time >DATE_SUB(NOW(), INTERVAL 60 DAY)
        and t.risk_category_2 is not null
        and t.todo_state = 1
        and p.state='TODO'
        order by t.update_time asc
        limit #{limitSize}
    </select>

    <select id = "listWaitInsuranceRenewalV2" resultType="com.cfpamf.ms.insur.admin.pojo.dto.renewal.InsuranceRenewDto">
        select  t.old_policy_no as oldPolicyNo, t.invalid_time as invalidTime,t.insured_id_number as insuredIdNumber
        ,t.risk_catagory_name as riskCategory2,t.start_time as startTime,t.applicant_id_number as applicantIdNumber,
        (case  when t.policy_product_type = '团险' then 'group' else 'person' end ) as productAttrCode,
        t.policy_product_type as policyProductType
        from policy_renewal_base_info t
        left join phoenix_emp_todo p on p.target_id = t.old_policy_no   and p.biz_type = 'RENEW_SHORT'
        where t.ins_status in ('init','waited')
        and t.invalid_time >DATE_SUB(NOW(), INTERVAL 60 DAY)
        and t.risk_catagory_name is not null
        and t.todo_state = 1
        and p.state in ('TODO','DONE')
        order by t.update_time asc
        limit #{limitSize}
    </select>

    <select id = "getNewRenewDataByOldOrderId" resultType="com.cfpamf.ms.insur.admin.pojo.dto.renewal.InsuranceRenewDto">
        select  t.old_policy_no as oldPolicyNo, t.invalid_time as invalidTime,t.insured_id_number as insuredIdNumber
        ,t.risk_catagory_name as riskCategory2,t.start_time as startTime,t.applicant_id_number as applicantIdNumber,
        (case  when t.policy_product_type = '团险' then 'group' else 'person' end ) as productAttrCode,
        t.policy_product_type as policyProductType
        from policy_renewal_base_info t
        left join phoenix_emp_todo p on p.target_id = t.old_policy_no   and p.biz_type = 'RENEW_SHORT'
        where t.old_order_Id = #{oldOrderId}
    </select>


</mapper>