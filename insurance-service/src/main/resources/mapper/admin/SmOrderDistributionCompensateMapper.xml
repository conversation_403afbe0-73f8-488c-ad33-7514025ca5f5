<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.order.SmOrderDistributionCompensateMapper">
    <update id="updateCompensateStatus">
        update sm_order_distribution_compensate set compensate_state = #{compensateState}
        where fh_order_id in
        <foreach collection="orderIds" item="orderId" open="(" separator="," close=")">
            #{orderId}
        </foreach>
        and compensate_state = 1;
    </update>

    <update id="updateByOrderIds">
        update sm_order_distribution_compensate set update_time = now()
        where fh_order_id in
        <foreach collection="orderIds" item="orderId" open="(" separator="," close=")">
            #{orderId}
        </foreach>
    </update>
</mapper>