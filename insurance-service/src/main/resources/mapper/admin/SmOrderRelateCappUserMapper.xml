<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.app.SmOrderRelateCappUserMapper">
    <select id="selectOrderIdByIdNumber" resultType="java.lang.String">
        select distinct order_id
        from sm_order_relate_capp_share_user s
                 left join cuser_share u on s.share_capp_user_id = u.id
        where u.id_number = #{idNumber}
    </select>
</mapper>
