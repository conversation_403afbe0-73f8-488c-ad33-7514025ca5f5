<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.commission.SmOrderCommissionItemMapper">

    <!--批量插入，当唯一索引已存在，则按唯一索引来更新记录 -->
    <update id="batchInsertCommissionItem" useGeneratedKeys="true" parameterType="java.util.List">
        insert into
        sm_commission_detail_item(order_id,policy_no,insured_id_number,plan_id,risk_id,risk_name,account_time,policy_status,risk_status,calc_way,
        term_num,plan_amount,amount,original_amount,commission_type,commission_id,commission_rate,commission_amount,enabled_flag,commission_user_id,business_type,business_id
            ,import_flag
        )
        values
        <foreach collection="list" item="det" separator=",">
            (#{det.orderId},#{det.policyNo},#{det.insuredIdNumber},#{det.planId},#{det.riskId},#{det.riskName},#{det.accountTime},#{det.policyStatus},#{det.riskStatus},#{det.calcWay},
            #{det.termNum},#{det.planAmount},#{det.amount},#{det.originalAmount},#{det.commissionType},#{det.commissionId},#{det.commissionRate},#{det.commissionAmount},#{det.enabledFlag}
            ,#{det.commissionUserId},#{det.businessType},#{det.businessId}
            <if test="det.importFlag!=null">
                ,#{det.importFlag}
            </if>
            <if test="det.importFlag==null">
                ,0
            </if>
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
        order_id=values(order_id),policy_no=values(policy_no),insured_id_number=values(insured_id_number),plan_id=values(plan_id),risk_id=values(risk_id),
        risk_name=values(risk_name),
        account_time=values(account_time)
        ,policy_status=values(policy_status),risk_status=values(risk_status),calc_way=values(calc_way),
        term_num=values(term_num),plan_amount=values(plan_amount),amount=values(amount),original_amount=values(original_amount)
        ,commission_type=values(commission_type),commission_id=values(commission_id)
        ,commission_rate=values(commission_rate),commission_amount=values(commission_amount),enabled_flag=values(enabled_flag),commission_user_id=values(commission_user_id)
        ,import_flag = values(import_flag)
    </update>
    <!--批量插入，当唯一索引已存在，则按唯一索引来更新记录 -->
    <update id="batchInsertCommissionItemExceptImport" useGeneratedKeys="true" parameterType="java.util.List">
        insert into
        sm_commission_detail_item(order_id,policy_no,insured_id_number,plan_id,risk_id,risk_name,account_time,policy_status,risk_status,calc_way,
        term_num,plan_amount,amount,original_amount,commission_type,commission_id,commission_rate,commission_amount,enabled_flag,commission_user_id,business_type,business_id
            ,import_flag
        )
        values
        <foreach collection="list" item="det" separator=",">
            (#{det.orderId},#{det.policyNo},#{det.insuredIdNumber},#{det.planId},#{det.riskId},#{det.riskName},#{det.accountTime},#{det.policyStatus},#{det.riskStatus},#{det.calcWay},
            #{det.termNum},#{det.planAmount},#{det.amount},#{det.originalAmount},#{det.commissionType},#{det.commissionId},#{det.commissionRate},#{det.commissionAmount},#{det.enabledFlag}
            ,#{det.commissionUserId},#{det.businessType},#{det.businessId}
            <if test="det.importFlag!=null">
                ,#{det.importFlag}
            </if>
            <if test="det.importFlag==null">
                ,0
            </if>
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
        order_id=values(order_id),policy_no=values(policy_no),insured_id_number=values(insured_id_number),plan_id=values(plan_id),risk_id=values(risk_id),
        risk_name=values(risk_name),
        account_time=values(account_time)
        ,policy_status=values(policy_status),risk_status=values(risk_status),calc_way=values(calc_way),
        term_num=values(term_num),plan_amount=values(plan_amount),amount=values(amount),original_amount=values(original_amount)
        ,commission_type=values(commission_type),commission_id=values(commission_id)
        ,commission_rate= if(import_flag = 1,commission_rate,values(commission_rate))
        ,commission_amount= if(import_flag = 1,commission_amount,values(commission_amount))
        ,enabled_flag=values(enabled_flag),commission_user_id=values(commission_user_id)
        ,import_flag = if(import_flag = 1,import_flag,values(import_flag))
    </update>
    <select id="getSmCommissionDetailItemDTO"
            resultType="com.cfpamf.ms.insur.admin.pojo.dto.commission.SmCommissionDetailItemDTO">
        select t1.*, t2.planName
        from sm_commission_detail_item t1
            left join sm_plan t2
        on t1.plan_id = t2.id
        where t1.policy_no = #{policyNo}
          and t1.insured_id_number = #{insuredIdNumber}
          and t1.policy_status = #{appStatus}
          and t1.term_num = #{termNum}
          and t1.enabled_flag = 0
    </select>

    <update id="batchCorrectIdNumber">
        <foreach collection="data" separator=";" item="order">
            update sm_commission_detail_item
            set insured_id_number = #{order.newValue}
            where order_id = #{order.orderId}
            and   policy_no = #{order.policyNo}
            and   enabled_flag = 0
        </foreach>
    </update>

    <update id="batchCorrectRecommendMan">
        <foreach collection="data" separator=";" item="order">
            update sm_commission_detail_item
            set commission_user_id = #{order.newValue}
            <if test="order.paymentTime!=null ">
                , account_time = #{order.paymentTime}
            </if>
            where order_id = #{order.orderId}
            and   policy_no = #{order.policyNo}
            and   enabled_flag = 0
        </foreach>
    </update>

</mapper>
