<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.SmProductHistoryMapper">

    <select id="listProductPlansById" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmPlanVO">
        SELECT t1.planId as id, planId, t1.version, t1.productId, planName, description, fhProductId, t1.update_by,
        t1.update_time,
        t1.enabled_flag, t1.create_by, t1.create_time, planOrderOutType
        , t2.productId AS productId, t2.productName AS productName, t3.companyName AS companyName,
        t1.min_premium as minPremium
        FROM sm_plan_history t1
        LEFT JOIN sm_product_history t2 ON t1.productId=t2.productId and t2.version = t1.version
        inner join sm_product sp on sp.id = t1.productId
        LEFT JOIN sm_company t3 ON t2.companyId=t3.id
        WHERE t1.enabled_flag = 0
        <if test="version!=null">
            and t1.version = #{version}
        </if>
        <if test="version==null">
            and t1.version = sp.version +1
        </if>
        <if test='productId != null'>
            AND t1.productId = #{productId}
        </if>
        <if test='onlyValid != null'>
            AND t2.enabled_flag=0 AND t3.enabled_flag=0
        </if>
    </select>

    <select id="listProductPlansByIds" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmPlanVO">
        SELECT
        t1.planId as id,
        t1.planId,
        t1.version,
        t1.planName,
        t1.description,
        t1.fhProductId,
        t1.update_by,
        t1.update_time,
        t1.enabled_flag,
        t1.create_by,
        t1.create_time,
        t1.planOrderOutType, t2.productId AS productId, t2.productName AS productName
        FROM sm_plan_history t1
        inner join sm_product tv on t1.productId = tv.id and t1.version = tv.version+1
        LEFT JOIN sm_product_history t2 ON t1.productId = t2.productId and t2.version = t1.version
        WHERE t1.enabled_flag = 0
        AND t2.productId IN
        <foreach item="item" index="index" open="(" separator="," close=")" collection="productIds">
            #{item}
        </foreach>
    </select>

    <select id="getPlanById" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmPlanVO">
        SELECT t1.*, t2.productName, t2.buyLimit, t2.channel, t2.companyId
        FROM sm_plan_history t1
                 LEFT JOIN sm_product t2 ON t1.productId = t2.id
        WHERE t1.planId = #{planId}
          and t1.version = #{version}
    </select>

    <select id="getPlanByFhProductId" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmPlanVO">
        SELECT t1.*, t2.channel, t2.productName
        FROM sm_plan t1
                 LEFT JOIN sm_product t2 ON t1.productId = t2.id
        WHERE t1.enabled_flag = '0'
          AND t2.enabled_flag = '0'
          AND t1.fhProductId = #{fhProductId}
            LIMIT 1
    </select>

    <select id="getProductAttr" resultType="java.lang.String">
        SELECT
        attr_val
        FROM sm_product_attr_history
        WHERE product_id=#{productId}
        and version=#{version}
        <if test="code!=null">
            and attr_code=#{code}
        </if>
    </select>

    <select id="listProductAttr" resultType="com.cfpamf.ms.insur.weixin.pojo.dto.product.ProductAttrDTO">
        SELECT
            attr_code as attrCode,
            attr_val  as attrVal
        FROM sm_product_attr_history
        WHERE product_id=#{productId}
          and version=#{version}
          and enabled_flag=0
    </select>

    <insert id="insertProductAttr">
        INSERT into sm_product_attr_history(product_id,attr_code,attr_val,version)
        VALUES(#{productId},#{attrCode},#{attrVal},#{version})
    </insert>

    <update id="deleteAllCoverage">
        DELETE FROM
            sm_product_coverage_history
        WHERE productId=#{productId}
          and version=#{version}
    </update>




    <select id="getProductById" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmProductDetailVO">
        SELECT t1.productId   as id,
               t1.pid,
               t1.productCategoryId,
               t1.companyId,
               t1.productName,
               t1.productTags,
               t1.productFeature,
               t1.thumbnailImageUrl,
               t1.introduceImageUrl,
               t1.attentions,
               t1.healthNotification,
               t1.buyLimit,
               t1.effectWaitingDay,
               t1.effectWaitingDayMin,
               t1.effectWaitingDayMax,
               t4.state,
               t4.minAmount,
               t4.saleQty,
               t1.product_model,
               t1.underWritingAgeSelect,
               t1.validPeriodSelect,
               t1.sexSelect,
               t1.socialSecuritySelect,
               t1.occuplCategorySelect,
               t1.vehicleSeatNumberSelect,
               t1.update_by,
               t1.update_time,
               t1.enabled_flag,
               t1.create_by,
               t1.create_time,
               t1.channel,
               t1.productAttrCode,
               t1.glUwaFrom,
               t1.glUwaTo,
               t1.glOcpnGroup,
               t1.glProductIntroduce,
               t1.glProductNotice,
               t1.validPeriod,
               t1.productSpecialsJoin,
               t1.onlineChannel,
               t1.protonHeavyIonMedicineSelect,
               t1.specifiedDiseaseSpecificCareSelect,
               t1.japanMedicalTreatmentSelect,
               t1.glQuoteTemplateUrl,
               t1.apiType,
               t1.h5Url,
               t1.activeFlag,
               t1.sortNum,
               t1.productShortName,
               t1.headImageUrl,
               t1.monthSaleCount,
               t1.custNotify,
               t1.version,
               t1.sales_mode as rawSalesMode,
               t1.product_code as productCode,
               t2.name        AS productCategoryName,
               t3.companyName AS companyName,
               t1.priceCode,
               t1.aiCheck,
               t1.aiCheckWay,
               t1.miniPremium,
               t1.productType,
               t1.product_code,
               t1.explain_video,t1.explain_video_img,
               t1.create_type,
               t1.long_insurance,
               t1.product_star_rating
        FROM sm_product_history t1
                 LEFT JOIN dictionary t2 on t1.productCategoryId = t2.id AND t2.enabled_flag = 0
                 LEFT JOIN sm_company t3 on t1.companyId = t3.id
                 left join sm_product t4 on t4.id = t1.productId
        WHERE t1.productId = #{id}
          and t1.version = #{version}
    </select>

    <select id="getMaxSortNum" resultType="java.lang.Integer">
        SELECT IFNULL(max(sortNum), 0)
        from sm_product
        where enabled_flag = 0
    </select>

    <insert id="insertProduct" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO sm_product_history
        (productId, productAttrCode, productCategoryId, companyId, productName, productShortName, productTags,
         productFeature,
         thumbnailImageUrl,
         introduceImageUrl, buyLimit, effectWaitingDayMin,
         effectWaitingDayMax, state,
         channel, glOcpnGroup, glUwaFrom, glUwaTo, validPeriod, productSpecialsJoin,
         apiType, h5Url, headImageUrl,
         update_by, sortNum,
         update_time, enabled_flag,productType,miniPremium,
         product_code,
         sales_mode,
         explain_video,explain_video_img,
         create_type, long_insurance,product_model,product_star_rating)
        VALUES (#{productId}, #{productAttrCode}, #{productCategoryId}, #{companyId}, #{productName},
                #{productShortName}, #{productTags},
                #{productFeature},
                #{thumbnailImageUrl}, #{introduceImageUrl}, #{buyLimit}, #{effectWaitingDayMin},
                #{effectWaitingDayMax}, 0, #{channel},
                #{glOcpnGroup},
                #{glUwaFrom}, #{glUwaTo}, #{validPeriod}, #{productSpecialsJoin},
                #{apiType}, #{h5Url}, #{headImageUrl}, #{modifyBy}, #{sortNum},
                CURRENT_TIMESTAMP(), 0,#{productType},#{miniPremium},#{productCode},#{salesMode},
                #{explainVideo},
                #{explainVideoImg},
                #{createType, typeHandler=org.apache.ibatis.type.EnumTypeHandler},
                #{longInsurance},#{productModel},#{productStarRating})
    </insert>

    <update id="updateSortNum">
        <![CDATA[
        SET @oldSortNum = (select sortNum
                           from sm_product
                           where id = #{id});
        SET @num := CASE WHEN @oldSortNum > #{newSortNum} THEN #{newSortNum} ELSE @oldSortNum - 1 END;
        UPDATE sm_product
        SET sortNum = (@num := (CASE WHEN @num = #{newSortNum} - 1 THEN @num + 2 ELSE @num + 1 END))
        WHERE sortNum >= (CASE WHEN @oldSortNum > #{newSortNum} THEN #{newSortNum} ELSE @oldSortNum END)
          and id != #{id}
        ORDER BY sortNum ASC, id asc;
        UPDATE sm_product
        SET sortNum =#{newSortNum}
        where id = #{id};
        ]]>
    </update>

    <update id="updateProduct" parameterType="com.cfpamf.ms.insur.admin.pojo.dto.SmProductDTO">
        UPDATE sm_product_history
        SET productAttrCode=#{productAttrCode},
            productCategoryId=#{productCategoryId},
            companyId=#{companyId},
            productName=#{productName},
            productShortName=#{productShortName},
            productTags=#{productTags},
            productFeature=#{productFeature},
            thumbnailImageUrl=#{thumbnailImageUrl},
            channel=#{channel},
            headImageUrl=#{headImageUrl},
            introduceImageUrl=#{introduceImageUrl},
            effectWaitingDayMin=#{effectWaitingDayMin},
            effectWaitingDayMax=#{effectWaitingDayMax},
            buyLimit=#{buyLimit},
            glOcpnGroup=#{glOcpnGroup},
            glUwaFrom=#{glUwaFrom},
            glUwaTo=#{glUwaTo},
            validPeriod=#{validPeriod},
            productSpecialsJoin=#{productSpecialsJoin},
            apiType=#{apiType},
            h5Url=#{h5Url},
            update_by=#{modifyBy},
            productType=#{productType},
            miniPremium=#{miniPremium},
            product_code=#{productCode},
            explain_video=#{explainVideo},explain_video_img=#{explainVideoImg},
            create_type=#{createType, typeHandler=org.apache.ibatis.type.EnumTypeHandler},
            sales_mode=#{salesMode},
            product_code=#{productCode},
            update_time=CURRENT_TIMESTAMP(),
            long_insurance=#{longInsurance},
            product_model=#{productModel},
            product_star_rating=#{productStarRating}
        WHERE productId = #{id}
          and version = #{version}

    </update>

    <update id="deleteProduct">
        UPDATE sm_product
        SET enabled_flag = 1,
            update_by=#{modifyBy},
            update_time=CURRENT_TIMESTAMP()
        WHERE id = #{id}

    </update>

    <update id="updateProductStatus">
        UPDATE sm_product
        SET state=#{status},
            update_by=#{modifyBy},
            update_time=CURRENT_TIMESTAMP()
        WHERE id = #{id}
    </update>

    <update id="updateProductActiveFlag">
        UPDATE sm_product
        SET activeFlag=#{activeFlag},
            update_time=CURRENT_TIMESTAMP()
        WHERE id = #{id}
    </update>

    <update id="updateProductAttention">
        UPDATE sm_product_history
        SET attentions=#{dto.attentions},
            update_by=#{dto.modifyBy}
        WHERE productId = #{dto.productId}
          and version = #{version}
    </update>

    <update id="updateProductHealthNotification">
        UPDATE sm_product_history
        SET healthNotification=#{dto.healthNotification},
            aiCheck=#{dto.aiCheck},
            aiCheckWay=#{dto.aiCheckWay},
            update_by=#{dto.modifyBy}
        WHERE productId = #{dto.productId}
          and version = #{version}

    </update>

    <update id="updateProductIntroduce">
        UPDATE sm_product_history
        SET glProductIntroduce=#{dto.glProductIntroduce},
            update_by=#{dto.modifyBy},
            update_time=CURRENT_TIMESTAMP()
        WHERE productId = #{dto.productId}
          and version = #{version}

    </update>

    <update id="updateProductNotice">
        UPDATE sm_product_history
        SET glProductNotice=#{glProductNotice},
            update_by=#{modifyBy},
            update_time=CURRENT_TIMESTAMP()
        WHERE productId = #{productId}
          and version = #{version}

    </update>

    <insert id="insertProductPlans" useGeneratedKeys="true">
        INSERT INTO sm_plan_history (planId,productId,planName,description,fhProductId,update_by,update_time,
        enabled_flag,version,min_premium)
        VALUES
        <foreach collection="plans" item="item" index="index" separator=",">
            (#{item.id},#{item.productId}, #{item.planName}, #{item.description}, #{item.fhProductId},
            #{item.modifyBy},CURRENT_TIMESTAMP(), 0,#{version},#{item.minPremium})
        </foreach>
    </insert>

    <update id="deleteProductPlan">
        UPDATE sm_plan_history
        SET enabled_flag=1,
            update_by=#{modifyBy},
            update_time=CURRENT_TIMESTAMP()
        WHERE planId = #{planId}
          and version = #{version}
    </update>

    <select id="listDistinctFhProductIds" resultType="java.lang.String">
        SELECT distinct fhProductId
        from sm_plan
        WHERE enabled_flag = 0
    </select>

    <insert id="insertProductClause" useGeneratedKeys="true">
        INSERT INTO sm_product_clause_history
        (productId,clauseName,clauseUrl,update_by,update_time,enabled_flag,version,sort)
        VALUES
        <foreach collection="clauses" item="item" index="index" separator=",">
            (#{item.productId}, #{item.clauseName}, #{item.clauseUrl}, #{item.modifyBy}, CURRENT_TIMESTAMP(),
            0,#{version},#{item.sort})
        </foreach>
    </insert>

    <update id="deleteProductClause">
        delete
        from sm_product_clause_history
        WHERE productId = #{id}
          and version = #{version}
    </update>


    <insert id="insertProductFormField" useGeneratedKeys="true">
        INSERT INTO sm_product_form_field_history
        (productId, groupCode,groupName, fieldCode,fieldName, display, required, update_by, update_time,
        enabled_flag,version,params)
        VALUES
        <foreach collection="formFields" item="item" index="index" separator=",">
            (#{item.productId},#{item.groupCode},#{item.groupName},#{item.fieldCode},#{item.fieldName},#{item.display},#{item.required},#{item.modifyBy},
            CURRENT_TIMESTAMP(),0,#{version},#{item.params})
        </foreach>
    </insert>

    <update id="deleteProductFormField">
        UPDATE sm_product_form_field_history
        SET enabled_flag=1,
            update_time=CURRENT_TIMESTAMP()
        WHERE productId = #{productId}
          and version = #{version}
    </update>

    <update id="updateProductOnlinePlatform">
        UPDATE sm_product
        SET onlineChannel = #{onlineChannel}
        WHERE id = #{productId}
    </update>

    <update id="updateProductPriceFactor">
        UPDATE sm_product_history
        SET underWritingAgeSelect=#{underWritingAgeSelect},
            validPeriodSelect=#{validPeriodSelect},
            sexSelect=#{sexSelect},
            socialSecuritySelect=#{socialSecuritySelect},
            occuplCategorySelect=#{occuplCategorySelect},
            vehicleSeatNumberSelect=#{vehicleSeatNumberSelect},
            protonHeavyIonMedicineSelect=#{protonHeavyIonMedicineSelect},
            specifiedDiseaseSpecificCareSelect=#{specifiedDiseaseSpecificCareSelect},
            japanMedicalTreatmentSelect=#{japanMedicalTreatmentSelect},
            update_by=#{modifyBy},
            priceCode= #{priceCode},
            update_time=CURRENT_TIMESTAMP()
        WHERE productId = #{id}
          and version = #{version}

    </update>

    <insert id="insertPlanFactorPrice" useGeneratedKeys="false" keyProperty="id">
        INSERT INTO sm_plan_factor_price_history
        (spfpId,productId, planId, underWritingAgeOptional,validPeriodOptional, sexOptional, socialSecurityOptional,
        vehicleSeatNumberOptional, protonHeavyIonMedicineOptional, specifiedDiseaseSpecificCareOptional,
        japanMedicalTreatmentOptional, price, available, update_by, update_time,
        enabled_flag,version,smokeOptional,occuplCategoryOptional)
        VALUES
        <foreach collection="prices" item="item" index="index" separator=",">
            (#{item.id},#{item.productId}, #{item.planId}, #{item.underWritingAgeOptional}, #{item.validPeriodOptional},
            #{item.sexOptional}, #{item.socialSecurityOptional}, #{item.vehicleSeatNumberOptional},
            #{item.protonHeavyIonMedicineOptional},#{item.specifiedDiseaseSpecificCareOptional},#{item.japanMedicalTreatmentOptional},
            #{item.price}, #{item.available}, #{item.modifyBy}, CURRENT_TIMESTAMP(),
            0,#{version},#{item.smokeOptional},#{item.occuplCategoryOptional})
        </foreach>
    </insert>

    <delete id="deletePlanFactorPrice">
        delete
        from sm_plan_factor_price_history
        where productId = #{productId}
          and version = #{version}
    </delete>

    <update id="pushDutyFactor">
        replace into sm_product_duty_factor(spc_id,product_id, duty_code, duty_factor_code, factor_value,
        factor_name,
        option_name,
        flow, is_default, status,
        create_time, update_time, operator)
        select
            spc_id,product_id, duty_code, duty_factor_code, factor_value,
            factor_name,
            option_name,
            flow, is_default, 0,
            create_time, update_time, operator
        from sm_product_duty_factor_history
        where product_id = #{productId}
          and version = #{version}
    </update>

    <insert id="insertProductFactorOptionals" useGeneratedKeys="true">
        INSERT INTO sm_product_factor_optionals_history
        (spfoId,productId,fieldCode,optionalName,optionalValue,value1,value2,unit1,unit2, update_by, update_time,
        enabled_flag,
        version)
        VALUES
        <foreach collection="optionals" item="item" index="index" separator=",">
            (#{item.id},#{item.productId}, #{item.fieldCode}, #{item.optionalName}, #{item.optionalValue},
            #{item.value1},
            #{item.value2},#{item.unit1}, #{item.unit2}, #{item.modifyBy}, CURRENT_TIMESTAMP(), 0,#{version})
        </foreach>
    </insert>

    <delete id="deleteProductFactorOptionals">
        delete
        from sm_product_factor_optionals_history
        where productId = #{productId}
          and version = #{version}
    </delete>

    <insert id="insertProductClauseContent">
        INSERT INTO sm_product_clause_content_history
            (productId, content, version)
        VALUES (#{productId}, #{content}, #{version})
    </insert>

    <delete id="deleteProductClauseContent">
        delete
        from sm_product_clause_content_history
        where productId = #{productId}
          and version = #{version}
    </delete>

    <insert id="insertPlanFactorPriceRef">
        insert into sm_plan_factor_price_history_ref( productId, spfpnId, liabilityCode, version)
        VALUES
        <foreach collection="prices" item="item" index="index" separator=",">
            <foreach collection="item.liabilities" item="code" separator=",">(
                #{item.productId}, #{item.id},#{code},#{version})
            </foreach>
        </foreach>
    </insert>
    <delete id="deletePlanFactorPriceRef">
        delete
        from sm_plan_factor_price_history_ref
        where productId = #{productId}
          and version = #{version}
    </delete>

    <select id="listFieldOptionals" resultType="com.cfpamf.ms.insur.admin.pojo.dto.SmFactorOptionalDTO">
        SELECT spfoId as id,
               productId,
               fieldCode,
               optionalName,
               optionalValue,
               value1,
               value2,
               unit1,
               unit2,
               update_by,
               update_time,
               enabled_flag,
               create_by,
               create_time,
               version
        FROM sm_product_factor_optionals_history
        WHERE productId = #{productId}
          and version = #{version}
          AND enabled_flag = 0
    </select>

    <select id="getPlanSpecPrice" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmPlanFactorPriceDT">
        SELECT * FROM sm_plan_factor_price_history t1 WHERE t1.enabled_flag=0
        and t1.version = #{version}
        <if test='productId != null'>
            AND t1.productId = #{productId}
        </if>
        <if test='planId != null'>
            AND t1.planId = #{planId}
        </if>
        <if test='underWritingAgeId != null'>
            AND t1.underWritingAgeOptional = #{underWritingAgeId}
        </if>
        <if test='validPeriodId != null'>
            AND t1.validPeriodOptional = #{validPeriodId}
        </if>
        <if test='sexId != null'>
            AND t1.sexOptional = #{sexId}
        </if>
        <if test='socialSecurityId != null'>
            AND t1.socialSecurityOptional = #{socialSecurityId}
        </if>
        <if test='occuplCategoryId != null'>
            AND t1.occuplCategoryOptional = #{occuplCategoryId}
        </if>
        <if test='vehicleSeatNumberId != null'>
            AND t1.vehicleSeatNumberOptional = #{vehicleSeatNumberId}
        </if>
        <if test='available!=null'>
            AND t1.available = #{available}
        </if>
    </select>

    <select id="listProductFormFields" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmProductFormFieldVO">
        SELECT *
        FROM sm_product_form_field_history
        WHERE productId = #{productId}
          and version = #{version}
          AND enabled_flag = 0
        ORDER BY id ASC
    </select>

    <select id="listProductClausesByProductId" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmProductClauseVO">
        SELECT *, productId AS productId
        FROM sm_product_clause_history
        WHERE productId = #{productId}
          AND enabled_flag = 0
          and version = #{version}
        ORDER BY sort ASC, id ASC
    </select>

    <select id="getProductClauseById" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmProductClauseVO">
        SELECT *
        FROM sm_product_clause
        WHERE id = #{id}
    </select>

    <select id="listPlanFactorPrices" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmPlanFactorPriceDT">
        SELECT t1.*,
        t2.name AS socialSecurityName,
        t3.name AS sexName,
        t4.optionalName AS validPeriodName,
        t5.optionalName AS underWritingAgeName,
        t7.optionalName AS vehicleSeatNumberName,
        t8.planName AS planName
        FROM sm_plan_factor_price_history t1
        LEFT JOIN dictionary t2 ON t1.sexOptional = t2.id AND t2.enabled_flag = 0
        LEFT JOIN dictionary t3 ON t1.socialSecurityOptional = t3.id AND t3.enabled_flag = 0
        LEFT JOIN sm_product_factor_optionals_history t4 ON t1.validPeriodOptional = t4.spfoId AND t4.enabled_flag = 0
        and t1.version = t4.version
        LEFT JOIN sm_product_factor_optionals_history t5 ON t1.underWritingAgeOptional = t5.spfoId AND t5.enabled_flag =
        0
        and t1.version = t5.version
        LEFT JOIN sm_product_factor_optionals_history t7
        ON t1.vehicleSeatNumberOptional = t7.spfoId AND t7.enabled_flag = 0
        and t1.version = t7.version
        LEFT JOIN sm_plan_history t8 ON t1.planId = t8.planId AND t8.enabled_flag = 0 and t8.version = t1.version
        WHERE t8.productId = #{id}
        AND t1.enabled_flag = 0 and t1.version = #{version}
        <if test="planIds!=null and planIds.size()>0">
            and t1.planId in
            <foreach item="item" index="index" open="(" separator="," close=")" collection="planIds">
                #{item}
            </foreach>
        </if>

    </select>

    <update id="updateProductMinAmount">
        UPDATE sm_product_history
        SET minAmount=#{minAmount},
            update_by=#{modifyBy},
            update_time=CURRENT_TIMESTAMP()
        WHERE productId = #{id}
          and version = #{version}
    </update>

    <update id="updateProductSaleQty">
        UPDATE sm_product
        SET saleQty=saleQty + #{qty},
            update_by=#{modifyBy},
            update_time=CURRENT_TIMESTAMP()
        WHERE productId = #{id}
    </update>

    <update id="updateProductPlan">
        UPDATE sm_plan_history
        SET planName=#{dto.planName},
            fhProductId=#{dto.fhProductId},
            min_premium=#{dto.minPremium},
            update_time=CURRENT_TIMESTAMP()
        WHERE planId = #{dto.id}
          and version = #{version}
    </update>

    <insert id="insertProductFormLimit" useGeneratedKeys="true">
        INSERT INTO sm_product_form_limit
        (productId,fieldCode,limitCode,update_by,update_time, enabled_flag)
        VALUES
        <foreach collection="occupation" item="item" index="index" separator=",">
            (#{item.productId}, #{item.fieldCode}, #{item.limitCode}, #{item.modifyBy}, CURRENT_TIMESTAMP(), 0)
        </foreach>
    </insert>

    <select id="listProductFormLimits" resultType="com.cfpamf.ms.insur.admin.pojo.vo.ProductFormLimitVO">
        SELECT *
        FROM sm_product_form_limit
        WHERE productId = #{id}
          AND enabled_flag = 0
    </select>

    <update id="deleteProductFormLimit">
        UPDATE sm_product_form_limit
        SET enabled_flag=1,
            update_by=#{modifyBy},
            update_time=CURRENT_TIMESTAMP()
        WHERE productId = #{id}
    </update>

    <select id="countProductByCompanyId" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM sm_product
        WHERE companyId = #{companyId}
    </select>

    <select id="listProductSalesOrgsByProductId" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmProductSalesOrgVO">
        SELECT distinct product_id productId, hr_org_id hrOrgId, org_name orgName, org_path orgPath
        FROM sm_plan_sales_org
        WHERE product_id = #{productId}
          AND enabled_flag = 0
    </select>

    <select id="listProductSalesOrgsByOrgPath" resultType="java.lang.Integer">
        SELECT DISTINCT t1.product_id productId
        FROM sm_plan_sales_org t1
        LEFT JOIN sm_product t2 ON t1.product_id = t2.id
        WHERE
        t1.enabled_flag = 0
        AND t2.state=1
        <if test='orgName != null'>
            AND (t1.org_Name = #{orgName} OR t1.org_Path IS NULL)
        </if>
        <if test='orgName == null'>
            AND t1.org_Path IS NULL
        </if>
    </select>

    <update id="deleteProductSalesOrgs">
        UPDATE sm_product_sales_org
        SET enabled_flag = 1
        WHERE productId = #{productId}
    </update>

    <insert id="insertProductSalesOrgs" useGeneratedKeys="true">
        INSERT INTO sm_product_sales_org
        (productId, hrOrgId, orgName, orgPath, create_by, update_by)
        VALUES
        <foreach collection="psos" item="item" index="index" separator=",">
            (#{item.productId}, #{item.hrOrgId}, #{item.orgName}, #{item.orgPath}, #{item.createBy}, #{item.updateBy} )
        </foreach>
    </insert>

    <select id="listOnlineProductSalesOrgs" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmProductSalesOrgVO">
        SELECT t1.id                              AS productId,
               t1.productName,
               GROUP_CONCAT(DISTINCT t2.org_path) AS orgPath,
               GROUP_CONCAT(DISTINCT t2.org_name) AS orgName
        FROM sm_product t1
                 LEFT JOIN sm_plan_sales_org t2
                           ON t2.product_id = t1.id
                               AND t2.enabled_flag = 0
        WHERE t1.state = 1
        GROUP BY t1.id
    </select>

    <select id="listProductRenews" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmProductRenewVO">
        SELECT
        t1.id AS productId,
        t1.productName,
        t1.state AS productState,
        t3.id AS renewProductId,
        t3.productName AS renewProductName,
        t3.state AS renewProductState,
        GROUP_CONCAT(DISTINCT t4.org_name) AS productSalesOrgs,
        GROUP_CONCAT(DISTINCT t5.org_name) AS renewProductSalesOrgs,
        t2.update_by AS updateBy,
        t2.update_time AS updateTime
        FROM
        sm_product t1
        LEFT JOIN sm_product_renew t2
        ON t2.productId = t1.id
        AND t2.enabled_flag = 0
        LEFT JOIN sm_product t3
        ON t3.id = t2.renewProductId
        LEFT JOIN sm_plan_sales_org t4
        ON t4.product_id = t1.id
        AND t4.enabled_flag = 0
        LEFT JOIN sm_plan_sales_org t5
        ON t5.product_id = t2.renewProductId
        AND t5.enabled_flag = 0
        WHERE
        1 = 1
        <if test='productName != null'>
            AND t1.productName LIKE CONCAT(#{productName}, '%')
        </if>
        <if test='productId != null'>
            AND t1.id = #{productId}
        </if>
        GROUP BY t1.id,
        t3.id
    </select>

    <select id="listWxRenewProductsProductId" resultType="com.cfpamf.ms.insur.weixin.pojo.vo.WxProductRenewVO">
        SELECT t2.id          AS renewProductId,
               t2.productName AS renewProductName,
               t3.state       AS oldProductState,
               t2.h5Url,
               t2.apiType,
               t2.channel
        FROM sm_product_renew t1
                 LEFT JOIN sm_product t2
                           ON t2.id = t1.renewProductId
                 LEFT JOIN sm_product t3
                           ON t3.id = t1.productId
        WHERE t1.productId = #{productId}
          AND t1.enabled_flag = 0
    </select>

    <select id="listRenewProductsByProductId" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmProductRenewVO">
        SELECT t2.id                              AS renewProductId,
               t2.productName                     AS renewProductName,
               GROUP_CONCAT(DISTINCT t3.org_name) AS renewProductSalesOrgs,
               t2.h5Url,
               t2.apiType,
               t2.channel
        FROM sm_product_renew t1
                 LEFT JOIN sm_product t2
                           ON t2.id = t1.renewProductId
                 LEFT JOIN sm_plan_sales_org t3
                           ON t3.product_id = t2.id
                               AND t3.enabled_flag = 0
        WHERE t1.productId = #{productId}
          AND t1.enabled_flag = 0
        GROUP BY t2.id
    </select>

    <select id="selectCicPilotOrgs" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmProductSalesOrgVO">
        SELECT distinct t2.org_path orgPath,
                        t2.org_name orgName
        FROM sm_product t1
                 LEFT JOIN sm_plan_sales_org t2
                           ON t2.product_id = t1.id AND t2.enabled_flag = 0
        WHERE t1.channel = 'cic'
          AND t1.state = 1
    </select>

    <insert id="insertProductRenews" parameterType="com.cfpamf.ms.insur.admin.pojo.dto.SmProductRenewDTO">
        INSERT INTO sm_product_renew (productId, renewProductId, create_by, update_by)
        VALUES
        <foreach collection="renews" item="item" index="index" separator=",">
            (#{item.productId}, #{item.renewProductId}, #{item.createBy}, #{item.updateBy} )
        </foreach>
    </insert>

    <update id="deleteProductRenews">
        UPDATE sm_product_renew
        SET enabled_flag = 1
        WHERE productId = #{productId}
    </update>

    <select id="listProductCoverages" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmProductCoverageVO">
        SELECT *
        FROM sm_product_coverage_history c
        WHERE productId = #{productId}
          and enabled_flag = 0
          and version = #{version};
    </select>

    <insert id="insertProductCoverage">
        INSERT INTO sm_product_coverage_history (spcId,
                                                 productId,
                                                 cvgItemName,
                                                 cvgNameDetail,
                                                 cvgType,
                                                 risk_code,
                                                 plan_id,
                                                 plan_name,
                                                 mandatory,
                                                 cvgRespType, cvgCode, version,create_by,update_by)
        VALUES (#{dto.spcId}, #{dto.productId},
                #{dto.cvgItemName},
                #{dto.cvgNameDetail},
                #{dto.cvgType},
                #{dto.riskCode},
                #{dto.planId},
                #{dto.planName},
                #{dto.mandatory},
                #{dto.cvgRespType}, #{dto.cvgCode}, #{version},#{dto.operator},#{dto.operator});
    </insert>

    <update id="updateProductCoverage">
        UPDATE sm_product_coverage_history
        SET cvgItemName   = #{dto.cvgItemName},
            cvgNameDetail = #{dto.cvgNameDetail},
            cvgType       = #{dto.cvgType},
            cvgRespType   = #{dto.cvgRespType},
            cvgCode       = #{dto.cvgCode},
            plan_id       = #{dto.planId},
            plan_name       = #{dto.planName},
            risk_code     = #{dto.riskCode},
            mandatory     = #{dto.mandatory}
        WHERE spcId = #{dto.spcId}
          and version = #{version}
    </update>

    <update id="deleteProductCoverage">
        UPDATE sm_product_coverage
        SET enabled_flag = 1
        WHERE productId = #{productId}
          AND spcId = #{spcId}
    </update>
    <delete id="deleteAllAmount">
        DELETE FROM sm_product_coverage_amount_history
        WHERE productId = #{productId}
          AND version = #{version}
    </delete>


    <select id="listProductCoverageAmounts"
            resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmProductCoverageAmountVO">
        SELECT t2.cvgItemName,
               t2.cvgType,
               t2.cvgRespType,
               t1.*,
               t2.plan_id as planId,
               t2.plan_name as planName
        FROM sm_product_coverage_amount_history t1
                 LEFT JOIN sm_product_coverage_history t2
                           ON t1.spcId = t2.spcId AND t2.enabled_flag = 0
                               and t1.version = t2.version
        WHERE t1.productId = #{productId}
          and t1.version = #{version}
          and t1.enabled_flag = 0;
    </select>

    <select id="listProductCoverageAmountsByPlanId"
            resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmProductCoverageAmountVO">
        SELECT t2.cvgItemName,
               t2.cvgType,
               t2.cvgRespType,
               t1.*,
               t3.planName
        FROM sm_product_coverage_amount_history t1
                 LEFT JOIN sm_product_coverage_history t2
                           ON t1.spcId = t2.spcId AND t2.enabled_flag = 0
                               and t1.version = t2.version
                 LEFT JOIN sm_plan_history t3
                           ON t1.planId = t3.planId AND t3.enabled_flag = 0
                               and t1.version = t3.version
        WHERE t1.planId = #{planId}
          and t1.version = #{version}
          and t1.enabled_flag = 0
    </select>

    <select id="listProductCoverageAmountBySpcaIds"
            resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmProductCoverageAmountVO">
        SELECT
        t2.cvgItemName,
        t1.*
        FROM
        sm_product_coverage_amount_history t1
        LEFT JOIN sm_product_coverage_history t2
        ON t1.spcId = t2.spcId AND t2.enabled_flag = 0 and t1.version = t2.version
        WHERE t1.enabled_flag = 0 and t1.version = #{version}
        AND t1.spcaId IN
        <foreach item="item" index="index" open="(" separator="," close=")" collection="spcaIds">
            #{item}
        </foreach>
    </select>

    <insert id="insertProductCoverageAmount" useGeneratedKeys="true" keyProperty="spcId">
        INSERT INTO sm_product_coverage_amount_history (spcaId,
                                                        productId,
                                                        planId,
                                                        spcId,
                                                        cvgAmount,
                                                        cvgNotice, version)
        VALUES (#{spcaId},
                #{productId},
                #{planId},
                #{spcId},
                #{cvgAmount},
                #{cvgNotice}, #{version});
    </insert>

    <update id="updateProductCoverageAmount">
        UPDATE sm_product_coverage_amount_history
        SET cvgAmount = #{cvgAmount},
            cvgNotice = #{cvgNotice}
        WHERE spcaId = #{spcaId}
          and version = #{version}
    </update>

    <update id="deleteProductCoverageAmount">
        UPDATE sm_product_coverage_amount_history
        SET enabled_flag = 1
        WHERE spcaId = #{spcaId}
          and version = #{version}
    </update>

    <select id="listProductCoveragePremiums"
            resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmProductCoveragePremiumDTO">
        SELECT t1.*,
               t2.cvgAmount,
               t2.cvgNotice,
               t3.cvgItemName,
               t3.plan_id,
               t3.plan_name
        FROM sm_product_coverage_premium_history t1
                 LEFT JOIN sm_product_coverage_amount_history t2 ON t2.spcaId = t1.spcaId and t2.version = t1.version
                 LEFT JOIN sm_product_coverage_history t3 ON t3.spcId = t1.spcId and t1.version = t3.version
        WHERE t1.productId = #{productId}
          and t1.version = #{version}
          AND t1.enabled_flag = 0;
    </select>

    <update id="updateProductCoveragePremium">
        UPDATE sm_product_coverage_premium_history
        SET premium = #{dto.premium}
        WHERE spcpId = #{dto.spcpId}
          and version = #{version}
    </update>

    <insert id="insertProductCoveragePremium">
        INSERT INTO sm_product_coverage_premium_history (
        spcpId,
        productId,
        occupationGroup,
        spcId,
        spcaId,
        planId,
        premium,version
        )
        VALUES
        <foreach collection="premiums" item="item" index="index" separator=",">
            (#{item.spcpId},#{item.productId}, #{item.occupationGroup}, #{item.spcId}, #{item.spcaId},#{item.planId},
            #{item.premium},#{version})
        </foreach>
    </insert>


    <delete id="deleteAttr">
        DELETE FROM sm_product_attr_history
        WHERE product_id = #{productId}
          and version = #{version}
          AND attr_code=#{attrCode}
    </delete>
    <update id="deleteProductCoveragePremium">
        UPDATE sm_product_coverage_premium_history
        SET enabled_flag = 1
        WHERE productId = #{productId}
          and version = #{version}
    </update>

    <select id="listProductCoverageDiscounts"
            resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmProductCoverageDiscountVO">
        SELECT *
        FROM sm_product_coverage_discount_history
        WHERE productId = #{productId}
          and version = #{version}
          AND enabled_flag = 0
    </select>
    <select id="getDutyFactors"
            resultType="com.cfpamf.ms.insur.admin.pojo.dto.product.DutyFactorDTO">
        SELECT *
        FROM sm_product_duty_factor_history
        WHERE product_Id = #{productId}
          and version = #{version}
    </select>
    <select id="getTimeFactors"
            resultType="com.cfpamf.ms.insur.admin.pojo.dto.product.TimeFactorDTO">
        SELECT *
        FROM sm_product_time_factor_history
        WHERE product_id = #{productId}
          and version = #{version}
    </select>


    <insert id="insertProductCoverageDiscounts">
        INSERT INTO sm_product_coverage_discount_history (spcdId, productId, perQty,min_per_qty, discount, version)
        VALUES (#{dto.spcdId}, #{dto.productId}, #{dto.perQty}, #{dto.minPerQty}, #{dto.discount}, #{version})
    </insert>
    <insert id="insertTimeFactor">
        INSERT INTO sm_product_time_factor_history (product_id, factor_value, factor_name, time_unit, flow,operator,version)
        VALUES (#{dto.productId}, #{dto.factorValue},
                #{dto.factorName},
                #{dto.timeUnit},#{dto.flow},
                #{dto.operator},#{version})
    </insert>

    <update id="updateProductCoverageDiscount">
        UPDATE sm_product_coverage_discount_history
        SET perQty   = #{dto.perQty},
            min_per_qty   = #{dto.minPerQty},
            discount = #{dto.discount}
        WHERE spcdId = #{dto.spcdId}
          and version = #{version};
    </update>

    <update id="updateTimeFactor">
        UPDATE sm_product_time_factor_history
        SET factor_value  = #{dto.factorValue},
            factor_name = #{dto.factorName},
            flow = #{dto.flow}
        WHERE id = #{dto.id}
          and version = #{version};
    </update>

    <update id="updateDutyFactor">
        UPDATE sm_product_duty_factor_history
        SET
            spc_id  = #{dto.spcId},
            duty_code  = #{dto.dutyCode},
            duty_factor_code  = #{dto.dutyFactorCode},
            factor_value  = #{dto.factorValue},
            factor_name = #{dto.factorName},
            flow = #{dto.flow},
            is_default = #{dto.isDefault}
        WHERE id = #{dto.id}
          and version = #{version};
    </update>

    <insert id="insertDutyFactor" parameterType="com.cfpamf.ms.insur.admin.pojo.dto.product.DutyFactorDTO"
            useGeneratedKeys="true" >
        INSERT INTO sm_product_duty_factor_history (
            product_id,
            spc_id,
            duty_code,
            duty_factor_code,
            factor_value,
            option_name,
            factor_name,
            flow,
            is_default,
            operator,
            version
        )
        VALUES (#{dto.productId},
                #{dto.spcId},
                #{dto.dutyCode},
                #{dto.dutyFactorCode},
                #{dto.factorValue},
                #{dto.optionName},
                #{dto.factorName},
                #{dto.flow},
                #{dto.isDefault},
                #{dto.operator},
                #{version}
               )
    </insert>

    <update id="deleteProductCoverageDiscount">
        UPDATE sm_product_coverage_discount_history
        SET enabled_flag = 1
        WHERE spcdId = #{spcdId}
          and version = #{version};
    </update>

    <select id="listWxGroupProducts" resultType="com.cfpamf.ms.insur.weixin.pojo.vo.WxGlProductListVO">
        SELECT
        id AS productId,
        productName,
        thumbnailImageUrl,
        glUwaFrom,
        glUwaTo,
        glOcpnGroup,
        effectWaitingDayMin,
        effectWaitingDayMax,
        minAmount
        FROM
        sm_product
        WHERE productAttrCode = 'group' AND state = 1
        <if test='salesProductIds != null'>
            AND id IN
            <foreach item="item" index="index" open="(" separator="," close=")" collection="salesProductIds">
                #{item}
            </foreach>
        </if>
        ORDER BY create_time ASC
    </select>

    <select id="getGlProductById"
            resultType="com.cfpamf.ms.insur.weixin.pojo.vo.WxGlProductDetailVO">
        SELECT *, id AS productId
        FROM sm_product
        WHERE id = #{productId}
    </select>

    <insert id="insertGlProductQuotePlan" useGeneratedKeys="true" keyProperty="spqpId">
        INSERT INTO sm_product_quote_plan (quoteNo,
                                           quoteType,
                                           customerName,
                                           customerMobile,
                                           productId,
                                           quoteDetail,
                                           totalPerQty,
                                           totalAmount,
                                           discount,
                                           actualAmount,
                                           customerAdminId,
                                           agentId,
                                           customerAdminType,
                                           customerAdminName,
                                           customerAdminMobile,
                                           customerAdminImgUrl)
        VALUES (#{quoteNo},
                #{quoteType},
                #{customerName},
                #{customerMobile},
                #{productId},
                #{quoteDetail},
                #{totalPerQty},
                #{totalAmount},
                #{discount},
                #{actualAmount},
                #{customerAdminId},
                #{agentId},
                #{customerAdminType},
                #{customerAdminName},
                #{customerAdminMobile},
                #{customerAdminImgUrl});
    </insert>


    <select id="listProductQuoteLimit" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmProductQuoteLimitItemVO">
        SELECT *
        FROM sm_product_quote_limit_history
        WHERE productId = #{productId}
          and version = #{version}
          AND enabled_flag = 0
    </select>

    <insert id="insertProductQuoteLimit" useGeneratedKeys="true" keyProperty="dto.spqlId">
        INSERT INTO sm_product_quote_limit_history (spqlId, limitType, productId,
                                                    occupationGroup, minPerQty,
                                                    minPerRatio, sourceSpcId,
                                                    limit_amount,
                                                    limit_amount_notice,
                                                    relyOnSpcId, operation, targetSpcId, amountRatio,
                                                    support_time,unit, version)
        VALUES (#{dto.spqlId}, #{dto.limitType}, #{dto.productId}, #{dto.occupationGroup}, #{dto.minPerQty},
                #{dto.minPerRatio}, #{dto.sourceSpcId},
                #{dto.limitAmount},
                #{dto.limitAmountNotice},
                #{dto.relyOnSpcId}, #{dto.operation}, #{dto.targetSpcId}, #{dto.amountRatio},
                #{dto.supportTime},#{dto.unit}, #{version})
    </insert>
    <insert id="insertHistoryByMain">
        INSERT INTO sm_product_history
        (productId,pid, productCategoryId, companyId, productName, productTags, productFeature, thumbnailImageUrl,
        introduceImageUrl, attentions, healthNotification, buyLimit, effectWaitingDay,
        effectWaitingDayMin, effectWaitingDayMax, state, minAmount, saleQty, underWritingAgeSelect,
        validPeriodSelect, sexSelect, socialSecuritySelect, occuplCategorySelect,
        vehicleSeatNumberSelect, update_by, create_by, channel, glUwaFrom, glUwaTo, glOcpnGroup,
        glProductIntroduce, glProductNotice, validPeriod, productSpecialsJoin, onlineChannel,
        protonHeavyIonMedicineSelect, specifiedDiseaseSpecificCareSelect, japanMedicalTreatmentSelect,
        glQuoteTemplateUrl, h5Url, productShortName, headImageUrl,miniPremium)
        select id,
        pid, productCategoryId, companyId, productName, productTags, productFeature, thumbnailImageUrl,
        introduceImageUrl, attentions, healthNotification, buyLimit, effectWaitingDay,
        effectWaitingDayMin, effectWaitingDayMax, state,
        <if test="minAmt!=null">#{minAmt}</if>
        <if test="minAmt==null">minAmt</if>, saleQty, underWritingAgeSelect,
        validPeriodSelect, sexSelect, socialSecuritySelect, occuplCategorySelect,
        vehicleSeatNumberSelect, update_by, create_by, channel, glUwaFrom, glUwaTo, glOcpnGroup,
        glProductIntroduce, glProductNotice, validPeriod, productSpecialsJoin, onlineChannel,
        protonHeavyIonMedicineSelect, specifiedDiseaseSpecificCareSelect, japanMedicalTreatmentSelect,
        glQuoteTemplateUrl, h5Url, productShortName, headImageUrl,miniPremium
        from sm_product
        where id = #{productId}
    </insert>
    <insert id="pushPlanPriceByHistory">
        replace into sm_plan_factor_price(id, productId, planId, underWritingAgeOptional, validPeriodOptional,
                                          sexOptional,
                                          socialSecurityOptional, vehicleSeatNumberOptional, occuplCategoryOptional,
                                          price, available, update_by, update_time, enabled_flag, create_by,
                                          create_time,
                                          protonHeavyIonMedicineOptional, specifiedDiseaseSpecificCareOptional,
                                          japanMedicalTreatmentOptional, smokeOptional)

        select spfpId,
               productId,
               planId,
               underWritingAgeOptional,
               validPeriodOptional,
               sexOptional,
               socialSecurityOptional,
               vehicleSeatNumberOptional,
               occuplCategoryOptional,
               price,
               available,
               update_by,
               update_time,
               enabled_flag,
               create_by,
               create_time,
               protonHeavyIonMedicineOptional,
               specifiedDiseaseSpecificCareOptional,
               japanMedicalTreatmentOptional,
               smokeOptional
        from sm_plan_factor_price_history
        where productId = #{productId}
          and version = #{version}
    </insert>
    <insert id="insertVersionProduct">
        insert into sm_product_history
        (productId, pid, productCategoryId, companyId, productName, productTags, productFeature, thumbnailImageUrl,
         introduceImageUrl, attentions, healthNotification, buyLimit, effectWaitingDay, effectWaitingDayMin,
         effectWaitingDayMax, state, minAmount, saleQty, underWritingAgeSelect, validPeriodSelect, sexSelect,
         socialSecuritySelect, occuplCategorySelect, vehicleSeatNumberSelect, update_by, update_time, enabled_flag,
         create_by, create_time, channel, productAttrCode, glUwaFrom, glUwaTo, glOcpnGroup, glProductIntroduce,
         glProductNotice, validPeriod, productSpecialsJoin, onlineChannel, protonHeavyIonMedicineSelect,
         specifiedDiseaseSpecificCareSelect, japanMedicalTreatmentSelect, glQuoteTemplateUrl, apiType, h5Url,
         activeFlag, sortNum, productShortName, headImageUrl, monthSaleCount, custNotify, version, aiCheck, aiCheckWay,
         priceCode, productType, miniPremium,sales_mode,
         create_type,long_insurance, explain_video, explain_video_img, product_code,product_model)
        select id,
               pid,
               productCategoryId,
               companyId,
               productName,
               productTags,
               productFeature,
               thumbnailImageUrl,
               introduceImageUrl,
               attentions,
               healthNotification,
               buyLimit,
               effectWaitingDay,
               effectWaitingDayMin,
               effectWaitingDayMax,
               state,
               minAmount,
               saleQty,
               underWritingAgeSelect,
               validPeriodSelect,
               sexSelect,
               socialSecuritySelect,
               occuplCategorySelect,
               vehicleSeatNumberSelect,
               update_by,
               update_time,
               enabled_flag,
               create_by,
               create_time,
               channel,
               productAttrCode,
               glUwaFrom,
               glUwaTo,
               glOcpnGroup,
               glProductIntroduce,
               glProductNotice,
               validPeriod,
               productSpecialsJoin,
               onlineChannel,
               protonHeavyIonMedicineSelect,
               specifiedDiseaseSpecificCareSelect,
               japanMedicalTreatmentSelect,
               glQuoteTemplateUrl,
               apiType,
               h5Url,
               activeFlag,
               sortNum,
               productShortName,
               headImageUrl,
               monthSaleCount,
               custNotify,
               #{version},
               aiCheck,
               aiCheckWay,
               priceCode,
               productType,
               miniPremium,
               sales_mode,
               create_type,
               long_insurance,
               explain_video, explain_video_img,
               product_code,
               product_model
        from sm_product
        where id = #{productId}
          and enabled_flag = 0
    </insert>
    <insert id="insertVersionProductClause">
        insert into sm_product_clause_history(productId, clauseName, clauseUrl, update_by, create_by, version, sort)
        select productId, clauseName, clauseUrl, update_by, create_by, #{version}, sort
        from sm_product_clause
        where productId = #{productId}
          and enabled_flag = 0
    </insert>

    <insert id="insertVersionProductClauseContent">
        insert into sm_product_clause_content_history(productId, content, version)
        select productId, content, #{version}
        from sm_product_clause_content
        where productId = #{productId}
          and enabled_flag = 0
    </insert>


    <insert id="insertVersionProductCoverage">
        insert into sm_product_coverage_history(
            spcId,
            productId,
            cvgItemName,
            cvgNameDetail,
            enabled_flag,
            create_by,
            create_time,
            update_by,
            update_time,
            cvgType,
            cvgType1,
            cvgRespType,
            cvgCode,
            plan_id,
            plan_name,
            risk_code,
            mandatory,
            version)
        select spcId,
               productId,
               cvgItemName,
               cvgNameDetail,
               enabled_flag,
               create_by,
               create_time,
               update_by,
               update_time,
               cvgType,
               cvgType1,
               cvgRespType,
               cvgCode,
               plan_id,
               plan_name,
               risk_code,
               mandatory,
               #{version}
        from sm_product_coverage
        where productId = #{productId}
          and enabled_flag = 0

    </insert>

    <insert id="insertVersionProductCoverageAmount">
        insert into sm_product_coverage_amount_history(spcaId, productId, spcId, cvgAmount, cvgNotice, enabled_flag,
                                                       create_by, create_time, update_by, update_time, planId, version)
        select spcaId,
               productId,
               spcId,
               cvgAmount,
               cvgNotice,
               enabled_flag,
               create_by,
               create_time,
               update_by,
               update_time,
               planId,
               #{version}
        from sm_product_coverage_amount
        where productId = #{productId}
          and enabled_flag = 0
    </insert>

    <insert id="insertVersionProductCoverageDiscount">
        insert into sm_product_coverage_discount_history
        (spcdId, productId, perQty,min_per_qty, discount, enabled_flag, create_by, create_time, update_by, update_time, version)
        select spcdId,
               productId,
               perQty,
               min_per_qty,
               discount,
               enabled_flag,
               create_by,
               create_time,
               update_by,
               update_time,
               #{version}
        from sm_product_coverage_discount
        where productId = #{productId}
          and enabled_flag = 0
    </insert>

    <insert id="insertVersionProductCoveragePremium">
        insert into sm_product_coverage_premium_history
        (spcpId, productId, planId, occupationGroup, spcId, spcaId, premium, create_by, update_by, version)
        SELECT spcpId,
               productId,
               planId,
               occupationGroup,
               spcId,
               spcaId,
               premium,
               create_by,
               update_by,
               #{version}
        from sm_product_coverage_premium
        where productId = #{productId}
          and enabled_flag = 0
    </insert>


    <insert id="insertVersionProductFactorOptionals">
        insert into sm_product_factor_optionals_history
        (spfoId, productId, fieldCode, optionalName, optionalValue, value1, value2, unit1, unit2, update_by, create_by,
         version)
        SELECT id,
               productId,
               fieldCode,
               optionalName,
               optionalValue,
               value1,
               value2,
               unit1,
               unit2,
               update_by,
               create_by,
               #{version}
        from sm_product_factor_optionals
        where productId = #{productId}
          and enabled_flag = 0
    </insert>


    <insert id="insertVersionProductFormField">
        insert into sm_product_form_field_history(productId, groupCode, groupName, fieldCode, fieldName, display,
                                                  required, update_by, create_by, params, version)
        SELECT productId,
               groupCode,
               groupName,
               fieldCode,
               fieldName,
               display,
               required,
               update_by,
               create_by,
               params,
               #{version}
        from sm_product_form_field
        where productId = #{productId}
          and enabled_flag = 0
    </insert>

    <insert id="insertVersionProductFormLimit">
        insert into sm_product_form_limit_history(productId, fieldCode, limitCode, update_by, create_by, version)
        select productId, fieldCode, limitCode, update_by, create_by, #{version}
        from sm_product_form_limit
        where productId = #{productId}
          and enabled_flag = 0;
    </insert>

    <insert id="insertVersionProductNotify">

        insert into sm_product_notify_history(productId, custNotify, custNotifyName, custNotifyUrl, custNotifyContent,
                                              update_time, enabled_flag, create_time, take_time, version)
        SELECT productId,
               custNotify,
               custNotifyName,
               custNotifyUrl,
               custNotifyContent,
               update_time,
               enabled_flag,
               create_time,
               take_time,
               #{version}
        from sm_product_notify
        where productId = #{productId}
          and enabled_flag = 0
    </insert>

    <insert id="insertVersionPlan">
        insert into sm_plan_history(planId, version, productId, planName, description, fhProductId, update_by,
                                    update_time, enabled_flag, create_by, create_time, planOrderOutType, plan_code,
                                    plan_type, min_premium)

        SELECT id,
               #{version},
               productId,
               planName,
               description,
               fhProductId,
               update_by,
               update_time,
               enabled_flag,
               create_by,
               create_time,
               planOrderOutType,
               plan_code,
               plan_type,
               min_premium
        from sm_plan
        where productId = #{productId}
          and enabled_flag = 0
    </insert>

    <insert id="insertVersionPlanFactorPrice">
        insert into sm_plan_factor_price_history
        (productId, planId, underWritingAgeOptional, validPeriodOptional, sexOptional, socialSecurityOptional,
         vehicleSeatNumberOptional, occuplCategoryOptional, price, available, update_by, create_by,
         protonHeavyIonMedicineOptional, specifiedDiseaseSpecificCareOptional, japanMedicalTreatmentOptional,
         smokeOptional, version)
        SELECT productId,
               planId,
               underWritingAgeOptional,
               validPeriodOptional,
               sexOptional,
               socialSecurityOptional,
               vehicleSeatNumberOptional,
               occuplCategoryOptional,
               price,
               available,
               update_by,
               create_by,
               protonHeavyIonMedicineOptional,
               specifiedDiseaseSpecificCareOptional,
               japanMedicalTreatmentOptional,
               smokeOptional,
               #{version}
        from sm_plan_factor_price
        where productId = #{productId}
          and enabled_flag = 0
    </insert>
    <insert id="insertVersionProductQuoteLimit">
        insert into sm_product_quote_limit_history(spqlId, productId, limitType, occupationGroup, minPerQty,
                                                   limit_amount,limit_amount_notice,
                                                   minPerRatio, sourceSpcId, relyOnSpcId, operation, targetSpcId,
                                                   amountRatio,
                                                   support_time,unit,
                                                   enabled_flag, create_by, create_time, update_by,
                                                   update_time, version)
        select spqlId,
               productId,
               limitType,
               occupationGroup,
               minPerQty,
               limit_amount,
               limit_amount_notice,
               minPerRatio,
               sourceSpcId,
               relyOnSpcId,
               operation,
               targetSpcId,
               amountRatio,
               support_time,
               unit,
               enabled_flag,
               create_by,
               create_time,
               update_by,
               update_time,
               #{version}
        from sm_product_quote_limit
        where productId = #{productId}
          and enabled_flag = 0
    </insert>

    <insert id="insertVersionDutyFactor">
        insert into sm_product_duty_factor_history(product_id,spc_id, duty_code, duty_factor_code,
                                                   factor_value, factor_name,option_name,
                                                   flow,is_default, operator,version)
        select
            product_id,
            spc_id,
            duty_code,
            duty_factor_code,
            factor_value,
            factor_name,
            option_name,
            flow,
            is_default,
            operator,
            #{version}
        from sm_product_duty_factor_history
        where product_Id = #{productId}
          and version = #{oldVersion}
    </insert>

    <insert id="insertVersionTimeFactor">
        insert into sm_product_time_factor_history(product_id, factor_value, factor_name, time_unit, flow,
                                                   operator, version)
        select
            product_id,
            factor_value,
            factor_name,
            time_unit,
            flow,
            operator,
            #{version}
        from sm_product_time_factor_history
        where product_Id = #{productId}
          and version = #{oldVersion}
    </insert>
    <insert id="insertVersionQuestion">
        insert into ak_product_question_history(product_id, code, question, operator, version)
        select
            product_id,
            code,
            question,
            operator,
            #{version}
        from ak_product_question_history
        where product_Id = #{productId}
          and version = #{oldVersion}
    </insert>

    <insert id="pushOccupationLimits">
        INSERT INTO sm_product_form_limit
            (productId, fieldCode, limitCode, update_by, update_time, enabled_flag)
        select productId, fieldCode, limitCode, update_by, update_time, enabled_flag
        from sm_product_form_limit_history
        where enabled_flag = 0
          and productId = #{productId}
          and version = #{version}
    </insert>


    <update id="pushProductQuestion">
        DELETE FROM ak_product_question WHERE product_id=#{productId};
        INSERT INTO ak_product_question(product_id, code, question)
        select
            product_id,
            code,
            question
        from  ak_product_question_history
        where product_id = #{productId}
          and   version = #{version};
    </update>

    <!--

    # sm_product_notify
    # sm_product_notify_history

    insert into sm_product_notify_history(productId, custNotify, custNotifyName, custNotifyUrl, custNotifyContent, update_time, enabled_flag, create_time, take_time, version)
     SELECT productId, custNotify, custNotifyName, custNotifyUrl, custNotifyContent, update_time, enabled_flag, create_time, take_time, version
    from sm_product_notify
    where productId = 1
      and enabled_flag = 0;-->

    <update id="updateGlProductQuotePlan">
        UPDATE sm_product_quote_plan
        SET quoteType    = #{quoteType},
            customerName = #{customerName}
        WHERE spqpId = #{spqpId}
    </update>

    <update id="updateProductQuoteLimit">
        UPDATE sm_product_quote_limit
        SET occupationGroup = #{occupationGroup},
            minPerQty       = #{minPerQty},
            minPerRatio     = #{minPerRatio},
            sourceSpcId     = #{sourceSpcId},
            relyOnSpcId     = #{relyOnSpcId},
            operation       = #{operation},
            targetSpcId     = #{targetSpcId},
            amountRatio     = #{amountRatio}
        WHERE spqlId = #{spqlId}
    </update>

    <update id="deleteProductQuoteLimit">
        UPDATE sm_product_quote_limit_history
        SET enabled_flag = 1
        WHERE spqlId = #{spqlId}
          and version = #{version}
    </update>

    <update id="deleteAllProductQuoteLimit">
        UPDATE sm_product_quote_limit_history
        SET enabled_flag = 1
        WHERE productId = #{productId}
          and version = #{version}
    </update>
    <update id="updateMonthSale">
        update sm_product sp inner join (SELECT productId, COUNT(1) AS saleCount
            FROM sm_order
            WHERE
       <![CDATA[DATE_SUB(CURDATE(), INTERVAL 1 MONTH) <= create_time ]]>
        AND payStatus = 2
            GROUP BY productId) t on sp.id = t.productId
            set sp.monthSaleCount = t.saleCount
        where t.productId is not null
    </update>
    <update id="updateProductCustNotify">
        update sm_product_history
        set custNotify = #{dto.custNotify}
        where productId = #{dto.id}
          and version = #{version}
    </update>

    <update id="pushMainByHistory">
        UPDATE sm_product sp
            left join sm_product_history sph
        on sp.id = sph.productId and sph.version = #{version}
            SET sp.productAttrCode=sph.productAttrCode,
                sp.productCategoryId=sph.productCategoryId,
                sp.companyId=sph.companyId,
                sp.productName=sph.productName,
                sp.productShortName=sph.productShortName,
                sp.productTags=sph.productTags,
                sp.productFeature=sph.productFeature,
                sp.thumbnailImageUrl=sph.thumbnailImageUrl,
                sp.channel=sph.channel,
                sp.headImageUrl=sph.headImageUrl,
                sp.introduceImageUrl=sph.introduceImageUrl,
                sp.effectWaitingDayMin=sph.effectWaitingDayMin,
                sp.effectWaitingDayMax=sph.effectWaitingDayMax,
                sp.minAmount = sph.minAmount,
                sp.buyLimit=sph.buyLimit,
                sp.glOcpnGroup=sph.glOcpnGroup,
                sp.glUwaFrom=sph.glUwaFrom,
                sp.glUwaTo=sph.glUwaTo,
                sp.validPeriod=sph.validPeriod,
                sp.productSpecialsJoin=sph.productSpecialsJoin,
                sp.apiType=sph.apiType,
                sp.h5Url=sph.h5Url,
                sp.update_by=sph.update_by,
                sp.update_time=CURRENT_TIMESTAMP(),
                sp.version = sph.version,
                sp.attentions = sph.attentions,
                sp.healthNotification = sph.healthNotification,
                sp.protonHeavyIonMedicineSelect=sph.protonHeavyIonMedicineSelect,
                sp.specifiedDiseaseSpecificCareSelect=sph.specifiedDiseaseSpecificCareSelect,
                sp.japanMedicalTreatmentSelect =sph.japanMedicalTreatmentSelect,
                sp.glQuoteTemplateUrl = sph.glQuoteTemplateUrl,
                sp.sexSelect = sph.sexSelect,
                sp.underWritingAgeSelect = sph.underWritingAgeSelect,
                sp.validPeriodSelect= sph.validPeriodSelect,
                sp.occuplCategorySelect = sph.occuplCategorySelect,
                sp.vehicleSeatNumberSelect=sph.vehicleSeatNumberSelect,
                sp.glProductNotice = sph.glProductNotice,
                sp.glProductIntroduce = sph.glProductIntroduce,
                sp.custNotify = sph.custNotify,
                sp.priceCode=sph.priceCode,
                sp.aiCheck = sph.aiCheck,
                sp.aiCheckWay = sph.aiCheckWay,
                sp.miniPremium = sph.miniPremium,
                sp.productType =sph.productType,
                sp.sales_mode=sph.sales_mode,
                sp.create_type =sph.create_type,
                sp.long_insurance =sph.long_insurance,
                sp.explain_video =sph.explain_video, sp.explain_video_img =sph.explain_video_img,
                sp.product_code =sph.product_code,
                sp.product_model = sph.product_model
        WHERE sph.productId = #{productId}
          and sph.version = #{version}
          and sp.id = #{productId}
    </update>
    <update id="pushPlanByHistory">
        replace
        into sm_plan(id, productId, planName, description, fhProductId, enabled_flag, create_by,
                             planOrderOutType,plan_code,plan_type,min_premium)
        select planId,
               productId,
               planName,
               description,
               fhProductId,
               enabled_flag,
               create_by,
               planOrderOutType,
               plan_code,
               plan_type,
               min_premium
        from sm_plan_history
        where productId = #{productId}
          and version = #{version}
    </update>

    <update id="pushHealthQuestion">
        replace
        into ak_product_question(product_id, code, question, status)
        select
            product_id,
            code,
            question,
            0
        from ak_product_question_history
        where product_id = #{productId}
          and version = #{version}
    </update>
    <update id="pushTimeFactor">
        replace
        into sm_product_time_factor(product_id, factor_value, factor_name,time_unit,flow)
        select
            product_id,
            factor_value,
            factor_name,
            time_unit,
            flow
        from sm_product_time_factor_history
        where product_id = #{productId}
          and version = #{version}
    </update>

    <update id="updateProductCustNotifyDetail">
        update sm_product_notify_history
        set custNotify=#{dto.custNotify},
            custNotifyName=#{dto.custNotifyName},
            custNotifyUrl=#{dto.custNotifyUrl},
            custNotifyContent=#{dto.custNotifyContent},
            take_time=#{dto.takeTime}
        where productId = #{dto.productId}
          and version = #{version}
    </update>
    <update id="pushProductFactorOptionals">
        replace into sm_product_factor_optionals(id, productId, fieldCode, optionalName, optionalValue, value1, value2,
                                                 unit1, unit2, update_by, update_time, enabled_flag, create_by,
                                                 create_time)
        SELECT spfoId,
               productId,
               fieldCode,
               optionalName,
               optionalValue,
               value1,
               value2,
               unit1,
               unit2,
               update_by,
               update_time,
               enabled_flag,
               create_by,
               create_time
        from sm_product_factor_optionals_history
        where productId = #{productId}
          and version = #{version}

    </update>
    <update id="pushProductCoverage">
        replace into sm_product_coverage(spcId, productId, cvgItemName, cvgNameDetail, enabled_flag, create_by,
                                         create_time, update_by, update_time, cvgType, cvgType1, cvgRespType, cvgCode,
                                        plan_id,plan_name,risk_code,mandatory)
        select spcId,
               productId,
               cvgItemName,
               cvgNameDetail,
               enabled_flag,
               create_by,
               create_time,
               update_by,
               update_time,
               cvgType,
               cvgType1,
               cvgRespType,
               cvgCode,
               plan_id,
               plan_name,
               risk_code,
               mandatory
        from sm_product_coverage_history
        where productId = #{productId}
          and version = #{version}
    </update>
    <update id="pushProductCoverageDiscount">
        replace into sm_product_coverage_discount(spcdId, productId, perQty,min_per_qty, discount, enabled_flag, create_by,
                                                  create_time, update_by, update_time)
        select spcdId,
               productId,
               perQty,
               min_per_qty,
               discount,
               enabled_flag,
               create_by,
               create_time,
               update_by,
               update_time
        from sm_product_coverage_discount_history
        where productId = #{productId}
          and version = #{version}
    </update>
    <update id="pushProductCoverageAmount">
        replace into sm_product_coverage_amount(spcaId, productId, spcId, cvgAmount, cvgNotice, enabled_flag, create_by,
                                                create_time, update_by, update_time, planId)
        SELECT spcaId,
               productId,
               spcId,
               cvgAmount,
               cvgNotice,
               enabled_flag,
               create_by,
               create_time,
               update_by,
               update_time,
               planId
        from sm_product_coverage_amount_history
        where productId = #{productId}
          and version = #{version}
    </update>
    <update id="pushProductNotify">
        replace into sm_product_notify(productid, custnotify, custnotifyname, custnotifyurl, custnotifycontent,
                                       update_time, enabled_flag, create_time, take_time)

        SELECT productId,
               custNotify,
               custNotifyName,
               custNotifyUrl,
               custNotifyContent,
               update_time,
               enabled_flag,
               create_time,
               take_time
        from sm_product_notify_history
        where productId = #{productId}
          and version = #{version}
    </update>
    <update id="pushProductPremium">
        replace into sm_product_coverage_premium(spcpId, productId, planId, occupationGroup, spcId, spcaId, premium,
                                                 enabled_flag, create_by, create_time, update_by, update_time)
        select spcpId,
               productId,
               planId,
               occupationGroup,
               spcId,
               spcaId,
               premium,
               enabled_flag,
               create_by,
               create_time,
               update_by,
               update_time
        from sm_product_coverage_premium_history
        where productId = #{productId}
          and version = #{version}
    </update>
    <update id="pushProductQuoteLimit">
        replace into sm_product_quote_limit(spqlId, productId, limitType,
                                            occupationGroup, minPerQty, minPerRatio,
                                            limit_amount,
                                            limit_amount_notice,
                                            sourceSpcId, relyOnSpcId, operation, targetSpcId, amountRatio,
                                            support_time,unit,
                                            enabled_flag,
                                            create_by, create_time, update_by, update_time)
        SELECT spqlId,
               productId,
               limitType,
               occupationGroup,
               minPerQty,
               minPerRatio,
               limit_amount,
               limit_amount_notice,
               sourceSpcId,
               relyOnSpcId,
               operation,
               targetSpcId,
               amountRatio,
               support_time,
               unit,
               enabled_flag,
               create_by,
               create_time,
               update_by,
               update_time
        from sm_product_quote_limit_history
        where productId = #{productId}
          and version = #{version}

    </update>
    <insert id="pushProductFormField">
        insert into sm_product_form_field(productId, groupCode, groupName, fieldCode, fieldName, display, required,
                                          params,
                                          update_by, create_by)
        SELECT productId,
               groupCode,
               groupName,
               fieldCode,
               fieldName,
               display,
               required,
               params,
               update_by,
               create_by
        from sm_product_form_field_history
        where productId = #{productId}
          and version = #{version}
          and enabled_flag = 0
    </insert>

    <select id="listGlProductQuotes" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmGlProductQuoteListVO">
        SELECT
        t1.*,
        t2.userId,
        t2.userName,
        t2.userMobile,
        t2.regionName,
        t2.organizationName,
        t3.agentMobile,
        t3.agentName,
        t4.productName
        FROM sm_product_quote_plan t1
        LEFT JOIN auth_user t2 ON t1.customerAdminId = t2.userId
        LEFT JOIN sm_agent t3 ON t1.agentId = t3.agentId
        LEFT JOIN sm_product t4 ON t1.productId = t4.id
        WHERE 1 = 1
        <if test="startDate != null">
            <![CDATA[ AND t1.create_time >= #{startDate} ]]>
        </if>
        <if test="endDate != null">
            <![CDATA[ AND t1.create_time <= #{endDate} ]]>
        </if>
        <if test="quoteNo != null">
            AND t1.quoteNo LIKE CONCAT (#{quoteNo},'%')
        </if>
        <if test="customerName != null">
            AND t1.customerName LIKE CONCAT (#{customerName},'%')
        </if>
        <if test="regionName != null">
            AND t2.regionName = #{regionName}
        </if>
        <if test="organizationName != null">
            AND t2.organizationName = #{organizationName}
        </if>
        <if test="userName != null">
            AND (t2.userId LIKE CONCAT(#{userName}, '%') OR t2.userName LIKE CONCAT(#{userName}, '%') OR t2.userMobile
            LIKE CONCAT(#{userName}, '%'))
        </if>
        <if test="agentName != null">
            AND (t3.agentName LIKE CONCAT(#{agentName}, '%') OR t3.agentMobile LIKE CONCAT(#{agentName}, '%'))
        </if>
        ORDER BY t1.create_time DESC
    </select>
    <select id="listProductMinAmtByOrgName" resultType="com.cfpamf.ms.insur.admin.pojo.dto.ProductMinAmtDTO">
        SELECT t1.product_id productId,spfp.planId, min(price) amount
        FROM sm_plan_sales_org t1
        LEFT JOIN sm_product t2 ON t1.product_id = t2.id
        left join sm_plan_factor_price spfp on t1.plan_id = spfp.planId and spfp.enabled_flag = 0
        WHERE t1.enabled_flag = 0
        and t2.id IN
        <foreach item="item" index="index" open="(" separator="," close=")" collection="salesProductIds">
            #{item}
        </foreach>
        and t1.org_name = #{orgName}
        group by productId,planId
    </select>
    <select id="listPlanByFhProductIds" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmPlanVO">
        SELECT t1.*, t2.productName,t2.companyId
        FROM sm_plan t1
        LEFT JOIN sm_product t2 ON t1.productId = t2.id
        WHERE t1.enabled_flag = '0'
        AND t2.enabled_flag = '0'
        <if test="channel!=null">
            and t2.channel = #{channel}
        </if>
        AND t1.fhProductId in
        <foreach item="item" index="index" open="(" separator="," close=")" collection="fhProductIds">
            #{item}
        </foreach>
    </select>
    <select id="countPlanPrice" resultType="java.lang.Integer">
        select count(1)
        from sm_plan_factor_price_history
        where productId = #{productId}
          and version = #{version}
    </select>
    <select id="getProductClauseContent" resultType="java.lang.String">
        select content
        from sm_product_clause_content_history
        where productId = #{productId}
          and version = #{version}
            limit 1
    </select>
    <select id="getPlanByPlanIdList" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmPlanVO">
        SELECT t1.planId as id, planId, t1.version, t1.productId, planName, description, fhProductId, t1.update_by,
        t1.update_time,
        t1.enabled_flag, t1.create_by, t1.create_time, planOrderOutType
        , t2.productId AS productId, t2.productName AS productName, t3.companyName AS companyName
        FROM sm_plan_history t1
        LEFT JOIN sm_product_history t2 ON t1.productId=t2.productId and t2.version = t1.version
        inner join sm_product sp on sp.id = t1.productId
        LEFT JOIN sm_company t3 ON t2.companyId=t3.id
        WHERE t1.enabled_flag = 0
        AND t1.version = sp.version +1
        AND t2.enabled_flag=0 AND t3.enabled_flag=0
        AND t1.planId in
        <foreach item="item" index="index" open="(" separator="," close=")" collection="planIdList">
            #{item}
        </foreach>

    </select>
    <select id="listProductPlans" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmPlanVO">
        SELECT t1.planId as id, planId, t1.version, t1.productId, planName, description, fhProductId, t1.update_by,
        t1.update_time,
        t1.enabled_flag, t1.create_by, t1.create_time, planOrderOutType
        , t2.productId AS productId, t2.productName AS productName
        FROM sm_plan_history t1
        LEFT JOIN sm_product_history t2 ON t1.productId=t2.productId and t2.version = t1.version
        inner join sm_product sp on sp.id = t1.productId
        WHERE t1.enabled_flag = 0
        and t1.version = sp.version +1
        <if test='planId != null'>
            AND t1.planId = #{planId}
        </if>
        <if test='planName != null'>
            AND t1.planName like CONCAT(#{planName}, '%')
        </if>
        <if test='productName != null'>
            AND sp.productName like CONCAT(#{productName}, '%')
        </if>
        AND t2.enabled_flag=0
    </select>
    <insert id="pushProductClause">
        insert into sm_product_clause(productId, clauseName, clauseUrl, update_by, create_by, sort)
        SELECT productId, clauseName, clauseUrl, update_by, create_by, sort
        from sm_product_clause_history
        where productId = #{productId}
          and version = #{version}
          and enabled_flag = 0
    </insert>
    <insert id="pushProductClauseContent">
        insert into sm_product_clause_content(productId, content)
        SELECT productId, content
        from sm_product_clause_content_history
        where productId = #{productId}
          and version = #{version}
          and enabled_flag = 0
    </insert>


    <update id="pushProductQuestionnaire">
        replace into ak_product_questionnaire(product_id, questionnaire_code, file_name, file_url, create_by,
                                              create_time, update_by, update_time, version, enabled_flag)
        SELECT product_id,
               questionnaire_code,
               file_name,
               file_url,
               create_by,
               create_time,
               update_by,
               update_time,
               version,
               enabled_flag
        from ak_product_questionnaire_his
        where product_id = #{productId}
          and version = #{version}
    </update>

    <update id="pushProductConfirm">
        replace into sm_product_confirm(id, product_id, confirm_name, file_name, file_url, file_content, enabled_flag,display_sort)
        select spc_id, product_id, confirm_name, file_name, file_url, file_content, enabled_flag,display_sort
        from sm_product_confirm_history
        where product_id = #{productId}
          and version = #{version}
    </update>


    <insert id="pushProductAttr">
        insert into sm_product_attr(product_id, attr_code, attr_val)
        select
            product_id,
            attr_code,
            attr_val
        from sm_product_attr_history
        where product_id = #{productId}
          and version = #{updateVersion}
          and enabled_flag = 0
    </insert>

    <insert id="insertVersionProductQuestionnaire">
        insert into ak_product_questionnaire_his(product_id, questionnaire_code, file_name, file_url, version)
        select product_id, questionnaire_code, file_name, file_url, #{version}
        from ak_product_questionnaire
        where product_id = #{productId}
          and enabled_flag = 0
    </insert>
    <insert id="insertVersionProductConfirm">
        insert into sm_product_confirm_history (version, spc_id, product_id, confirm_name, file_name, file_url,
                                                file_content, enabled_flag,display_sort)
        select #{version},
               id,
               product_id,
               confirm_name,
               file_name,
               file_url,
               file_content,
               enabled_flag,
               display_sort
        from sm_product_confirm
        where product_id = #{productId}
          and enabled_flag = 0
    </insert>
    <insert id="insertVersionProductAttr">
        insert into sm_product_attr_history(version, product_id, attr_code, attr_val)
        select #{version}, product_id, attr_code, attr_val
        from sm_product_attr
        where product_id = #{productId}
          and enabled_flag = 0
    </insert>
    <delete id="deleteDutyFactor">
        delete from sm_product_duty_factor_history
        where product_id = #{productId}
          and version = #{version}
    </delete>

    <delete id="deleteTimeFactor">
        delete from sm_product_time_factor_history
        where product_id = #{productId}
          and version = #{version}
    </delete>

    <delete id="deleteProductCoverageAllDiscount">
        delete from sm_product_coverage_discount_history
        where productId = #{productId}
          and version = #{version}
    </delete>

    <select id="getMaxVersionProductData" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmProductListVO">
        select ifnull(max(t1.version),0)+1 version,t.id id from sm_product t
        left join sm_product_version t1 on t1.product_id = t.id
        group by t.id
    </select>

    <update id="dropTemporaryTable">
        drop temporary table if exists ${tableName}
    </update>
</mapper>
