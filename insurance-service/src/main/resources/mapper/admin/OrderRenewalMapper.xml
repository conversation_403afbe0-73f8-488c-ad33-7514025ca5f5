<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.admin.renewal.dao.OrderRenewalMapper">
    <resultMap id="BaseResultMap" type="com.cfpamf.ms.insur.admin.renewal.entity.OrderRenewal">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="old_order_id" jdbcType="VARCHAR" property="oldOrderId"/>
        <result column="old_policy_no" jdbcType="VARCHAR" property="oldPolicyNo"/>
        <result column="new_order_id" jdbcType="VARCHAR" property="newOrderId"/>
        <result column="new_policy_no" jdbcType="VARCHAR" property="newPolicyNo"/>
        <result column="type" jdbcType="VARCHAR" property="type"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="product_id" jdbcType="INTEGER" property="productId"/>
        <result column="plan_id" jdbcType="INTEGER" property="planId"/>
        <result column="start_time" jdbcType="TIMESTAMP" property="startTime"/>
        <result column="id_number" jdbcType="VARCHAR" property="idNumber"/>
        <result column="enabled_flag" jdbcType="TINYINT" property="enabledFlag"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , old_order_id, old_policy_no, new_order_id, new_policy_no, `type`, `status`, product_id,
    plan_id, start_time, id_number, enabled_flag, create_by, update_by, create_time, update_time
    </sql>
    <select id="getByOldOrderId" resultType="com.cfpamf.ms.insur.admin.renewal.entity.OrderRenewal">
        select
        <include refid="Base_Column_List"></include>
        from sm_order_renewal
        where old_order_id = #{oldOrderId}
        and enabled_flag = 0;
    </select>
    <select id="getByNewOrderId" resultType="com.cfpamf.ms.insur.admin.renewal.entity.OrderRenewal">
        select
        <include refid="Base_Column_List"></include>
        from sm_order_renewal
        where new_order_id = #{newOrderId}
        and enabled_flag = 0;
    </select>
    <select id="getByOldOrderIdAndStatusIsRenewed"
            resultType="com.cfpamf.ms.insur.admin.renewal.entity.OrderRenewal">
        select
        <include refid="Base_Column_List"></include>
        from sm_order_renewal
        where old_order_id = #{oldOrderId}
        and status = 'RENEWED'
        and enabled_flag = 0;
    </select>

    <select id="getInsurancePolicyLatestRecord" resultType="com.cfpamf.ms.insur.admin.renewal.entity.OrderRenewal">
        select
        <include refid="Base_Column_List"></include>
        from sm_order_renewal
        where old_policy_no = #{oldPolicyNo}
        and enabled_flag = 0
        order by field(`status`, 'CANCEL', 'RENEWED', 'WAITED') limit 1;
    </select>
    <select id="listRenewalByOldPolicyNoList" resultType="com.cfpamf.ms.insur.admin.renewal.entity.OrderRenewal">
        select
        <include refid="Base_Column_List"/>
        from sm_order_renewal
        where old_policy_no in
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        and enabled_flag = 0
    </select>

    <update id="batchUpdateOrderRenewal" parameterType="java.util.List">
        update sm_order_renewal
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="new_policy_no =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when id=#{item.id} then #{item.newPolicyNo}
                </foreach>
            </trim>

            <trim prefix="status =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when id=#{item.id} then #{item.status}
                </foreach>
            </trim>

            <trim prefix="product_id =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when id=#{item.id} then #{item.productId}
                </foreach>
            </trim>

            <trim prefix="plan_id =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when id=#{item.id} then #{item.planId}
                </foreach>
            </trim>

            <trim prefix="start_time =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when id=#{item.id} then #{item.startTime}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item.id}
        </foreach>
    </update>

    <select id="listRenewedByNewOrderIdList" resultType="com.cfpamf.ms.insur.admin.renewal.entity.OrderRenewal">
        select
        <include refid="Base_Column_List"/>
        from sm_order_renewal
        where new_order_id in
        <foreach collection="newOrderIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        and status='RENEWED'
        and enabled_flag = 0
    </select>
</mapper>