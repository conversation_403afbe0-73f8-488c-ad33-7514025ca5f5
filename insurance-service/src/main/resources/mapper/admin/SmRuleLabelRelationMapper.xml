<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.label.SmRuleLabelRelationMapper">
    <update id="batchInsert" useGeneratedKeys="true" parameterType="java.util.List">
        insert into sm_rule_label_relation(rule_id,label_id,label_code,label_name,value_code,value_desc,enabled_flag,create_by,update_by)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.ruleId},#{item.labelId},#{item.labelCode},#{item.labelName},#{item.valueCode},#{item.valueDesc},#{item.enabledFlag},#{item.createBy},#{item.updateBy})
        </foreach>
        ON DUPLICATE KEY UPDATE
        rule_id=values(rule_id),label_id=values(label_id),label_code=values(label_code)
        ,label_name=values(label_name),value_code=values(value_code),value_desc=values(value_desc)
        ,enabled_flag=values(enabled_flag),create_by=values(create_by),update_by=values(update_by)
    </update>
</mapper>
