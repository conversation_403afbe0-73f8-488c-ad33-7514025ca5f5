<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.claim.SmClaimPosterApplyMapper">

    <select id="querySmClaimById" resultType="com.cfpamf.ms.insur.admin.pojo.po.claim.SmClaimPosterApply">
        SELECT
            t0.id smClaimId,
            t0.claimNo,
            t1.startTime as insurancePeriod,
            concat(t6.regionName, '-', t6.organizationFullName) as location,
            t4.id as insuranceProducts,
            t0.riskType as accidentType,
            t1.unitPrice as premium,
            t0.payMoney as compensationAmount
        FROM
            sm_claim t0
                LEFT JOIN sm_order_insured t3 ON t0.insuredId = t3.id
                LEFT JOIN sm_order t1 ON t3.fhOrderId = t1.fhOrderId
                LEFT JOIN sm_product t4 ON t4.id = t1.productId
                LEFT JOIN auth_user t6 ON t6.userId = t1.customerAdminId AND t6.enabled_flag = 0
        WHERE t0.id= #{id};
    </select>

    <select id="querySmClaimPosterApplyList" resultType="com.cfpamf.ms.insur.admin.pojo.vo.claim.SmClaimPosterApplyVO">
        select ap.sm_claim_id,
            ap.claim_no,
            ap.insurance_period,
            ap.location,
            ap.accident_type,
            ap.insurance_products,
            ap.approval_status,
            ap.premium,
            ap.compensation_amount,
            ap.apply_user_name,
            ap.apply_time,
            ap.approval_user_id,
            ap.approval_user_name,
            ap.approval_time,
            au.regionName,
            au.regionCode,
            au.orgCode,
            au.organizationFullName,
            cp.id                   as companyId,
            cp.companyName          as companyName
        from sm_claim_poster_apply ap
        inner join sm_claim cl on ap.sm_claim_id = cl.id
        LEFT JOIN sm_order_insured oi ON cl.insuredId = oi.id
        LEFT JOIN sm_order od ON oi.fhOrderId = od.fhOrderId
        LEFT JOIN sm_product pd ON pd.id = od.productId
        left join sm_company cp on pd.companyId = cp.id
        LEFT JOIN auth_user au ON au.userId = od.customerAdminId AND `au`.enabled_flag = 0
        <where>
            <if test="claimNo != null and claimNo != ''">
                and ap.claim_no = #{claimNo}
            </if>
            <if test="applyTimeStart != null">
                and ap.apply_time &gt;= #{applyTimeStart}
            </if>
            <if test="applyTimeEnd != null">
                and ap.apply_time &lt;= #{applyTimeEnd}
            </if>
            <if test="applyUserName != null and applyUserName != ''">
                and ap.apply_user_name = #{applyUserName}
            </if>
            <if test="approvalStatus != null and approvalStatus != ''">
                and ap.approval_status = #{approvalStatus}
            </if>
            <if test="location != null and location != ''">
                and ap.location like concat('%',#{location},'%')
            </if>
            <if test="regionCodes != null and regionCodes.size() > 0">
                and au.regionCode in
                <foreach collection="regionCodes" index="index" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="orgCodes != null and orgCodes.size() > 0">
                and au.orgCode in
                <foreach collection="orgCodes" index="index" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
<!--            <if test="insuranceProductsList != null and insuranceProductsList.size() > 0">-->
<!--                and pd.id in-->
<!--                <foreach collection="insuranceProductsList" index="index" item="item" separator="," open="(" close=")">-->
<!--                    #{item}-->
<!--                </foreach>-->
<!--            </if>-->
            <if test="insuranceProductsList != null and insuranceProductsList.size() > 0">
                and
                <foreach collection="insuranceProductsList" item="item" index="index" separator="or" open="(" close=")">
                    find_in_set(#{item} , ap.insurance_products) > 0
                </foreach>
            </if>
            <if test="accidentType != null and accidentType.size() > 0">
                and ap.accident_type in
                <foreach collection="accidentType" index="index" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="companyIds != null and companyIds.size() > 0">
                and pd.companyId in
                <foreach collection="companyIds" index="index" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        order by ap.create_time desc
    </select>

    <update id="updateByPostApplyId" parameterType="com.cfpamf.ms.insur.admin.pojo.po.claim.SmClaimPosterApply">
        update sm_claim_poster_apply
        <trim prefix="set" suffixOverrides=",">
            <if test="approvalStatus != null">
                approval_status = #{approvalStatus},
            </if>
            <if test="approvalUserId != null">
                approval_user_id = #{approvalUserId},
            </if>
            <if test="approvalUserName != null">
                approval_user_name = #{approvalUserName},
            </if>
            <if test="approvalContent != null">
                approval_content = #{approvalContent},
            </if>
            <if test="approvalTime != null">
                approval_time = #{approvalTime},
            </if>
            <if test="insuranceProducts != null">
                insurance_products = #{insuranceProducts},
            </if>
            <if test="premium != null">
                premium = #{premium},
            </if>
            <if test="compensationAmount != null">
                compensation_amount = #{compensationAmount},
            </if>
            <if test="caseContent != null">
                case_content = #{caseContent},
            </if>
            <if test="posterId != null">
                poster_id = #{posterId},
            </if>
            <if test="posterUrl != null">
                poster_url = #{posterUrl},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="updateUser != null">
                update_user = #{updateUser},
            </if>
            <if test="saleText != null and saleText != ''">
                sale_text = #{saleText},
            </if>
        </trim>
        where id = #{id}
    </update>

</mapper>