<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cfpamf.ms.insur.admin.renewal.dao.SmOrderRenewalTermRiskMapper">

    <select id="listByOrderIdList" resultType="com.cfpamf.ms.insur.admin.renewal.entity.SmOrderRenewalTermRisk">
        select
            *
        from sm_order_renewal_term_risk
        where order_id in
        <foreach collection="orderIdList" open="(" close=")" separator="," item="orderId">
            #{orderId}
        </foreach>
        and term_num=#{period}
        <if test="appStatus!=null and appStatus!=''">
            and app_status=#{appStatus}
        </if>
        and enabled_flag=0
    </select>

    <select id="queryRenewalTermList" resultType="com.cfpamf.ms.insur.admin.renewal.entity.SmOrderRenewalTermRisk">
        select
            *
        from sm_order_renewal_term_risk
        where order_id in
        <foreach collection="orderIdList" open="(" close=")" separator="," item="orderId">
            #{orderId}
        </foreach>
        <if test="appStatus!=null and appStatus!=''">
            and app_status=#{appStatus}
        </if>
        and enabled_flag=0
    </select>


    
    <update id="batchUpdate" >
        <foreach collection="riskList" separator=";" item="vo">
            update sm_order_renewal_term_risk
            set
            premium=#{vo.premium},
            update_time=now()
            where order_id=#{vo.orderId}
            and term_num=#{vo.termNum}
            and insured_id_number=#{vo.insuredIdNumber}
            and risk_code=#{vo.riskCode}
        </foreach>
    </update>

    <select id="listRiskPremiumByFhOrderId" resultType="com.cfpamf.ms.insur.admin.pojo.po.order.SmOrderRiskDuty">
        select t.order_id  ,t1.risk_id,t.risk_name,t.insured_id_number,t.insured_period_type,t.insured_period,
               t.period_type,t.payment_period,t.payment_period_type,t.app_status,t.main_insurance,
               sum(t.premium) as premium,sum(t.amount) as amount,sum(t.refund_amount) as refund_amount,
               t.surrender_time,t1.surrender_type
        from sm_order_renewal_term_risk t
        left join sm_order_risk_duty t1 on t1.fh_order_id = t.order_id
        and t1.insured_id_number = t.insured_id_number and t.risk_code = t1.risk_code
        where order_id=#{orderId} and term_num = #{termNum} and t.premium>0 and t.enabled_flag=0
        group by t.order_id ,t1.id,t.insured_id_number
    </select>
</mapper>
