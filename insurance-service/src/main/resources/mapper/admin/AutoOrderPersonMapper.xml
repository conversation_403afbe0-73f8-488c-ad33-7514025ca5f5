<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.auto.AutoOrderPersonMapper">
    <update id="logicDelete">
        update auto_order set enabled_flag =1 where order_no = #{fhOrderId}

    </update>

    <insert id="insertAutoOrderPersonBatch" parameterType= "java.util.List">
        INSERT INTO auto_order_person (order_no, person_flag, person_name, person_gender,
        id_type,id_number,birthday,cellPhone, email, area, address, enabled_flag, create_time, update_time)
        VALUES
        <foreach collection="dtos" item="dto" index="index" separator=",">
        <foreach collection="dto.people" item="item" index="index" separator=",">
            (#{dto.orderNo},#{item.personFlag},#{item.personName},
            #{item.personGender},#{item.idType},#{item.idNumber},#{item.birthday},#{item.cellphone},
            #{item.email},#{item.area},#{item.address},0,CURRENT_TIMESTAMP() , CURRENT_TIMESTAMP())
        </foreach>
        </foreach>
    </insert>

    <update id="updateAutoOrderPersonBatch" parameterType= "java.util.List">
        <foreach collection="dtos" item="dto" index="index" separator=";">
        <foreach collection="dto.people" item="item" index="index" separator=";">
            update auto_order_person set person_name=#{item.personName},person_gender=#{item.personGender},
            id_type=#{item.idType},id_number=#{item.idNumber},
            birthday=#{item.birthday},cellPhone=#{item.cellphone},
            email=#{item.email},area=#{item.area},
            address=#{item.address}
            where order_no=#{dto.orderNo} and person_flag = #{item.personFlag}
        </foreach>
        </foreach>
    </update>
</mapper>
