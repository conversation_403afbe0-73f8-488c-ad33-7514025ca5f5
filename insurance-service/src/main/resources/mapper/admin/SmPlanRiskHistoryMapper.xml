<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.product.SmPlanRiskHistoryMapper">
    <insert id="initNewHistory">
        insert into sm_plan_risk_history(plan_id, product_id, plan_risk_id, risk_key, risk_version,
                                         included_total_insured_amount, version,
                                         update_by,
                                         update_time, enabled_flag, create_by, create_time, sort_field)
        SELECT plan_id,
               product_id,
               id,
               risk_key,
               risk_version,
               included_total_insured_amount,
               #{version},
               update_by,
               update_time,
               enabled_flag,
               create_by,
               create_time,
               sort_field
        from sm_plan_risk
        where product_id = #{productId}
          and enabled_flag = 0


    </insert>
    <update id="softDelete">
        update sm_plan_risk_history
        set enabled_flag = 1
        where plan_id = #{planId}
          and version = #{version}
    </update>

    <select id="getSmPlanRiskList" resultType="com.cfpamf.ms.insur.admin.pojo.vo.product.SmPlanRiskVO">
        select
        t1.included_total_insured_amount,
        t1.plan_id,
        t1.risk_key,
        t1.risk_version,
        t1.plan_risk_id,
        t2.risk_name,
        t2.risk_type,
        t2.risk_code,
        t2.id as sysRiskId,
        t3.required
        from sm_plan_risk_history t1
        left join sys_risk t2 on t1.risk_key = t2.risk_key and t1.risk_version = t2.version
        left join sm_product_risk_history t3 on  t1.product_id = t3.product_id and t2.risk_key = t3.risk_key and t2.version = t3.risk_version and t1.version = t3.version and t3.enabled_flag = 0
        where t1.version = #{version}
        and   t1.enabled_flag = 0
        and   t1.plan_id in
        <foreach collection="planIdList" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
        order by t1.sort_field ;
    </select>
    <select id="getSmPlanRiskListByProductId"
            resultType="com.cfpamf.ms.insur.admin.pojo.vo.product.SmPlanRiskVO">
        select t1.included_total_insured_amount,
               t1.plan_id,
               t1.risk_key,
               t1.risk_version,
               t1.plan_risk_id,
               t2.risk_name,
               t2.risk_type,
               t2.risk_code,
               t3.required
        from sm_plan_risk_history t1
                left join sm_plan sp on sp.id = t1.plan_id
                 left join sys_risk t2 on t2.risk_key = t1.risk_key
            and t1.risk_version = t2.version
                 left join sm_product_risk_history t3 on t3.risk_key = t2.risk_key
            and t3.risk_version = t2.version and t1.version = t3.version and t3.enabled_flag = 0 and t3.product_id =
                                                                                                     t1.product_id
        where t1.version = #{version}
          and t1.enabled_flag = 0 and sp.enabled_flag = 0
          and t1.product_id = #{productId}
        order by t1.sort_field
    </select>


    <select id="listPlanRiskInfo" resultType="com.cfpamf.ms.insur.admin.pojo.vo.product.PlanRiskInfoVO">
        select
        t1.id as planRiskId,
        t1.plan_id,
        t2.id as riskId,
        t2.version,
        t2.risk_name,
        t2.risk_type,
        t2.risk_code,
        t2.risk_key,
        t2.pay_way,
        t2.covered_years,
        t2.valid_period,
        t2.hesitation_period,
        t2.waiting_period
        from sm_plan_risk_history t1
        left join sys_risk t2 on t2.risk_key = t1.risk_key
        and t1.risk_version = t2.version
        left join sm_product_risk_history t3 on t3.risk_key = t2.risk_key
        and t3.risk_version = t2.version and t1.version = t3.version and t3.enabled_flag = 0 and t3.product_id =
        t1.product_id
        where t1.version = #{version}
        and t1.enabled_flag = 0
        and t1.plan_id in
        <foreach collection="planIdList" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
        order by t1.sort_field ;
    </select>
</mapper>
