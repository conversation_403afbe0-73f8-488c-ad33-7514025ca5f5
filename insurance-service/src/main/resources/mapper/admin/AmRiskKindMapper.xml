<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.AmRiskKindMapper">

    <insert id="insertRiskKind" useGeneratedKeys="true">
        INSERT INTO am_riskkind (policyId,riskCode,riskName,amount,notDeductible,premium,ncfPremium,create_time, update_time) VALUES (#{policyId}, #{riskCode}, #{riskName}, #{amount}, #{notDeductible}, #{premium}, #{ncfPremium}, CURRENT_TIMESTAMP(), CURRENT_TIMESTAMP())
    </insert>

    <select id="listRiskKindsByPolicyId" resultType="com.cfpamf.ms.insur.admin.pojo.vo.AmRiskKindVO">
        SELECT * FROM am_riskkind WHERE policyId = #{policyId}
    </select>
</mapper>