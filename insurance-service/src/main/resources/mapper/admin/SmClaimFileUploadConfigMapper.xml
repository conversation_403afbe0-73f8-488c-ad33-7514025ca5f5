<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.claim.SmClaimFileUploadConfigMapper">


    <select id="queryClaimRuleCode" resultType="java.lang.String">
        select code from system_groovy_rule where type = 'claim' and enabled_flag = 0
    </select>
    <select id="queryScriptCodeList" resultType="com.cfpamf.ms.insur.admin.pojo.po.claim.GroovyCodeEntity">
        select type, groovy_code as script, code as scriptCode from system_groovy_rule
        <where>
            code in
            <foreach collection="codeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
            and type = #{type}
            and enabled_flag = 0
        </where>
    </select>

</mapper>