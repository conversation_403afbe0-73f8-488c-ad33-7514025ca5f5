<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.claim.SmClaimReimbursementGtccMapper">

    <resultMap id="BaseResultMap" type="com.cfpamf.ms.insur.admin.pojo.po.claim.SmClaimReimbursementGtcc">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="claimId" column="claim_id" jdbcType="INTEGER"/>
            <result property="instSerialNo" column="inst_serial_no" jdbcType="VARCHAR"/>
            <result property="reportorName" column="reportor_name" jdbcType="VARCHAR"/>
            <result property="reportorEmail" column="reportor_email" jdbcType="VARCHAR"/>
            <result property="accidentPersonStatusCd" column="accident_person_status_cd" jdbcType="VARCHAR"/>
            <result property="reportorMobilePhone" column="reportor_mobile_phone" jdbcType="VARCHAR"/>
            <result property="insuredCertNo" column="insured_cert_no" jdbcType="VARCHAR"/>
            <result property="reportorRelationCd" column="reportor_relation_cd" jdbcType="VARCHAR"/>
            <result property="policyNo" column="policy_no" jdbcType="VARCHAR"/>
            <result property="accidentReasonTypeCd" column="accident_reason_type_cd" jdbcType="VARCHAR"/>
            <result property="accidentDesc" column="accident_desc" jdbcType="VARCHAR"/>
            <result property="accidentName" column="accident_name" jdbcType="VARCHAR"/>
            <result property="provinceCode" column="province_code" jdbcType="VARCHAR"/>
            <result property="cityCode" column="city_code" jdbcType="VARCHAR"/>
            <result property="areaCode" column="area_code" jdbcType="VARCHAR"/>
            <result property="accidentDetailAddress" column="accident_detail_address" jdbcType="VARCHAR"/>
            <result property="clinicHospital" column="clinic_hospital" jdbcType="VARCHAR"/>
            <result property="insuredName" column="insured_name" jdbcType="VARCHAR"/>
            <result property="insuredMobilePhone" column="insured_mobile_phone" jdbcType="VARCHAR"/>
            <result property="payAccountName" column="pay_account_name" jdbcType="VARCHAR"/>
            <result property="payAccountNo" column="pay_account_no" jdbcType="VARCHAR"/>
            <result property="payAccountType" column="pay_account_type" jdbcType="VARCHAR"/>
            <result property="certTypeCd" column="cert_type_cd" jdbcType="VARCHAR"/>
            <result property="certNo" column="cert_no" jdbcType="INTEGER"/>
            <result property="bankcode" column="bankCode" jdbcType="VARCHAR"/>
            <result property="openbankname" column="openBankName" jdbcType="VARCHAR"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="enabledFlag" column="enabled_flag" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,claim_id,inst_serial_no,
        reportor_name,reportor_email,accident_person_status_cd,
        reportor_mobile_phone,insured_cert_no,reportor_relation_cd,
        policy_no,accident_reason_type_cd,accident_desc,
        accident_name,province_code,city_code,
        area_code,accident_detail_address,clinic_hospital,
        insured_name,insured_mobile_phone,pay_account_name,
        pay_account_no,pay_account_type,cert_type_cd,
        cert_no,bankCode,openBankName,
        create_by,update_time,create_time,
        enabled_flag
    </sql>
    <update id="updateReportNoByClaimId">
        update sm_claim_reimbursement_gtcc set report_no = #{reportNo}, company_accident_time = #{companyAccidentTime} where claim_id = #{claimId}
    </update>
</mapper>
