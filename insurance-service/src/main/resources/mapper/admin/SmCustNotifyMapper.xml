<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.SmCustNotifyMapper">
    <select id="listAdminQuery" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmCustNotifyVO">
        select
        sc.*,
        so.fhOrderId,
        sp.productName,
        sp.companyId,
        sp2.planName,
        soi.policyNo,
        soa.personName applicant_person_name,
        soa.personGender applicant_person_gender,
        soa.idNumber applicant_id_number,
        soa.cellPhone applicant_mobile,
        soi.personName insured_person_name,
        soi.idNumber insured_id_number,
        soi.cellPhone insured_mobile,
        soi.personGender insured_person_gender,
        soi.birthday insured_birthday,
        (so.qty * so.unitPrice) totalAmount,
        so.startTime,
        so.endTime,
        so.create_time,
        so.create_time orderCreateTime,
        soi.appStatus,
        sc.state cancel_state,
        t6.userMobile admin_user_mobile ,
        t6.userName admin_user_name ,
        t6.userId admin_user_id,
        t6.wxOpenId,
        t6.regionName,
        t6.organizationName
        from
        sm_cust_notify sc
        LEFT JOIN sm_order so on sc.order_id = so.fhOrderId
        LEFT join sm_product sp on so.productId = sp.id
        LEFT join sm_order_applicant soa on so.fhOrderId = soa.fhOrderId
        LEFT join sm_order_insured soi on sc.insured_id = soi.id
        LEFT join sm_plan sp2 on sp2.id = so.planId
        LEFT JOIN auth_user t6 ON t6.userId=so.customerAdminId and t6.enabled_flag = 0
        <where>
            <!--            <if test="custNotifyId!=null">-->
            <!--                sc.id =#{custNotifyId}-->
            <!--            </if>-->
            <if test="state!=null">
                and sc.state = #{state}
            </if>
            <if test="isSign!= null and isSign">
                and sc.confirm_way>0
            </if>
            <if test="isSign!= null and isSign==false">
                and sc.confirm_way=0
            </if>
            <if test='userId != null'>
                AND so.customerAdminId = #{userId}
            </if>
            <if test='fhOrderId != null'>
                AND sc.order_id LIKE CONCAT(#{fhOrderId},'%')
            </if>

            <if test='policyNo !=  null'>
                AND soi.policyNo=#{policyNo}
            </if>
            <if test='productName !=  null'>
                AND sp.productName LIKE CONCAT('%',#{productName},'%')
            </if>
            <if test="sendTimeStart!=null">
                <![CDATA[   and sc.last_send_time >= #{sendTimeStart} ]]>
            </if>
            <if test="sendTimeEnd!=null">
                <![CDATA[   and sc.last_send_time < #{sendTimeEnd} ]]>
            </if>

            <if test="signTimeStart!=null">
                <![CDATA[   and sc.sign_time >= #{signTimeStart} ]]>
            </if>
            <if test="signTimeEnd!=null">
                <![CDATA[   and sc.sign_time < #{signTimeEnd} ]]>
            </if>
            <if test='applicantKeyword != null'>
                AND (
                (soa.personName LIKE CONCAT(#{applicantKeyword},'%'))
                or
                (soa.idNumber LIKE CONCAT(#{applicantKeyword},'%'))
                or
                (soa.cellPhone LIKE CONCAT(#{applicantKeyword},'%'))
                )
            </if>
            <if test='insuredKeyword != null'>
                AND (
                (soi.personName LIKE CONCAT(#{insuredKeyword},'%'))
                or
                (soi.idNumber LIKE CONCAT(#{insuredKeyword},'%'))
                or
                (soi.cellPhone LIKE CONCAT(#{insuredKeyword},'%'))
                )
            </if>
            <if test='regionName != null'>
                AND t6.regionName=#{regionName}
            </if>
            <if test='orgName != null'>
                AND t6.organizationName=#{orgName}
            </if>
        </where>
        order by sc.id desc
    </select>
    <select id="listSimpleList" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmCustNotifyVO">
        select
        sc.*,
        sp.productName,
        sp.companyId,
        sp2.planName,
        soi.policyNo,
        soa.personName applicant_person_name,
        soa.personGender applicant_person_gender,
        soa.idNumber applicant_id_number,
        soa.cellPhone applicant_mobile,
        soi.personName insured_person_name,
        soi.idNumber insured_id_number,
        soi.cellPhone insured_mobile,
        soi.personGender insured_person_gender,
        soi.birthday insured_birthday,
        (so.qty * so.unitPrice) totalAmount,
        so.startTime,
        so.endTime,
        so.create_time orderCreateTime,
        soi.appStatus,
        sc.state cancel_state,
        t6.userMobile admin_user_mobile ,
        t6.userName admin_user_name ,
        t6.userId admin_user_id,
               t6.wxOpenId,
        t6.regionName,
        t6.organizationName,
        t7.userMobile recommendUserMobile ,
        t7.userName recommendUserName ,
        t7.userId recommendUserId,
        soi.downloadURL,
        s.companyName,
        so.channel,
        soi.id insreud_id
        from
        sm_cust_notify sc
        LEFT JOIN sm_order so on sc.order_id = so.fhOrderId
        LEFT join sm_product sp on so.productId = sp.id
        LEFT join sm_order_applicant soa on so.fhOrderId = soa.fhOrderId
        LEFT join sm_order_insured soi on sc.insured_id = soi.id
        LEFT join sm_plan sp2 on sp2.id = so.planId
        LEFT JOIN auth_user t6 ON t6.userId=so.customerAdminId and t6.enabled_flag = 0
        LEFT JOIN auth_user t7 ON t7.userId=so.recommendId and t7.enabled_flag = 0
        left join sm_company s on sp.companyId = s.id
        <where>
            <if test="custNotifyId!=null">
                sc.id =#{custNotifyId}
            </if>
            <if test="state!=null">
                and sc.state = #{state}
            </if>
            <if test="isSign!= null and isSign">
                and sc.confirm_way>0
            </if>
            <if test="isSign!= null and isSign==false">
                and sc.confirm_way=0
            </if>
            <if test="confirmWay!=null">
                and sc.confirm_way = #{confirmWay}
            </if>
            <if test='userId != null'>
                AND so.customerAdminId = #{userId}
            </if>
            <if test="keyword">
                AND ( sp.productName like CONCAT(#{keyword},'%')
                or soa.personName LIKE CONCAT(#{keyword},'%')
                or soa.idNumber LIKE CONCAT(#{keyword},'%')
                or soi.personName LIKE CONCAT(#{keyword},'%')
                or soi.idNumber LIKE CONCAT(#{keyword},'%')
                )
            </if>
            <if test='regionName != null'>
                AND t6.regionName=#{regionName}
            </if>
            <if test='orgName != null'>
                AND t6.organizationName=#{orgName}
            </if>
            <if test="applicantIdNumber != null">
                and sc.id_number = #{applicantIdNumber}
            </if>
            <if test="ids != null">
                and sc.id in
                <foreach collection="ids" item="id" close=")" open="(" separator=",">
                    #{id}
                </foreach>
            </if>
        </where>
        order by sc.id desc
    </select>
    <select id="getSmsDTO" resultType="com.cfpamf.ms.insur.admin.pojo.dto.SmCustNotifySMSDTO">
        select soa.personName applicantName,
               soi.personName insuredName,
               sp.productName productName,
               soa.cellPhone  cellPhone
        from sm_cust_notify scn
                 left join sm_order_insured soi on scn.insured_id = soi.id
                 left join sm_order_applicant soa on soa.fhOrderId = scn.order_id
                 left join sm_product sp on scn.product_id = sp.id
        where scn.id = #{id}
    </select>
    <select id="listExcel" resultType="com.cfpamf.ms.insur.admin.pojo.vo.excel.SmCustNotifyExcelVO">
        select t6.regionName,
        t6.organizationName ,
        t6.userName,
        so.fhOrderId orderId,
        so.create_time orderCreateTime,
        soa.personName applicantName,
        soi.personName insuredName,
        sp.productName,
        soi.policyNo,
        sc.last_send_time,
        if(sc.state>0,'已查阅','未查阅') state,
        sc.read_time,
        sc.confirm_way,
        sc.sign_time,
        sc.newest_mag,
        sc.newest_time progressTime
        from sm_cust_notify sc
        LEFT JOIN sm_order so on sc.order_id = so.fhOrderId
        LEFT join sm_product sp on so.productId = sp.id
        LEFT join sm_order_applicant soa on so.fhOrderId = soa.fhOrderId
        LEFT join sm_order_insured soi on so.fhOrderId = soi.fhOrderId
        LEFT join sm_plan sp2 on sp2.id = so.planId
        LEFT JOIN auth_user t6 ON t6.userId = so.customerAdminId and t6.enabled_flag = 0
        <where>
            <if test="state!=null">
                and sc.state = #{state}
            </if>
            <if test="isSign!= null and isSign == true">
                and sc.confirm_way>0
            </if>
            <if test="isSign!= null and isSign == false">
                and sc.confirm_way=0
            </if>
            <if test='userId != null'>
                AND so.customerAdminId = #{userId}
            </if>
            <if test='fhOrderId != null'>
                AND sc.order_id LIKE CONCAT(#{fhOrderId},'%')
            </if>

            <if test='policyNo !=  null'>
                AND soi.policyNo=#{policyNo}
            </if>
            <if test='productName !=  null'>
                AND sp.productName LIKE CONCAT('%',#{productName},'%')
            </if>
            <if test='applicantKeyword != null'>
                AND (
                (soa.personName LIKE CONCAT(#{applicantKeyword},'%'))
                or
                (soa.idNumber LIKE CONCAT(#{applicantKeyword},'%'))
                or
                (soa.cellPhone LIKE CONCAT(#{applicantKeyword},'%'))
                )
            </if>
            <if test='insuredKeyword != null'>
                AND (
                (soi.personName LIKE CONCAT(#{insuredKeyword},'%'))
                or
                (soi.idNumber LIKE CONCAT(#{insuredKeyword},'%'))
                or
                (soi.cellPhone LIKE CONCAT(#{insuredKeyword},'%'))
                )
            </if>
            <if test='regionName != null'>
                AND t6.regionName=#{regionName}
            </if>
            <if test='orgName != null'>
                AND t6.organizationName=#{orgName}
            </if>
            <if test="sendTimeStart!=null">
                <![CDATA[   and sc.last_send_time >= #{sendTimeStart} ]]>
            </if>
            <if test="sendTimeEnd!=null">
                <![CDATA[   and sc.last_send_time < #{sendTimeEnd} ]]>
            </if>

            <if test="signTimeStart!=null">
                <![CDATA[   and sc.sign_time >= #{signTimeStart} ]]>
            </if>
            <if test="signTimeEnd!=null">
                <![CDATA[   and sc.sign_time < #{signTimeEnd} ]]>
            </if>
            <if test='policyNo !=  null'>
                AND soi.policyNo=#{policyNo}
            </if>
            <if test='productName !=  null'>
                AND sp.productName LIKE CONCAT('%',#{productName},'%')
            </if>
        </where>
        order by sc.id desc
    </select>
</mapper>
