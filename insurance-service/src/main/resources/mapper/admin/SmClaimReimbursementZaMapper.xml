<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.claim.SmClaimReimbursementZaMapper">

    <update id="updateZaReportNoByClaimId">
        UPDATE sm_claim_reimbursement_za set za_report_no = #{reportNo} ,za_report_time = #{zaReportTime} WHERE claim_id = #{claimId}
    </update>

    <update id="updateZaByClaimIdList">
        UPDATE sm_claim_reimbursement_za
        set payee_bank_district_code = ''
          , payee_bank_district_name = null
        WHERE claim_id in
        <foreach collection="list" close=")" open="(" separator="," item="item" >
            #{item}
        </foreach>
    </update>
</mapper>