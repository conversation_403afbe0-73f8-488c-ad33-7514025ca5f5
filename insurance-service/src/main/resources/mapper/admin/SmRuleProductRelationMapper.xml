<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.label.SmRuleProductRelationMapper">
    <update id="batchInsert" useGeneratedKeys="true" parameterType="java.util.List">
        insert into sm_rule_product_relation(rule_id,product_code,product_name,enabled_flag,create_by,update_by)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.ruleId},#{item.productCode},#{item.productName},#{item.enabledFlag},#{item.createBy},#{item.updateBy})
        </foreach>
        ON DUPLICATE KEY UPDATE
        rule_id=values(rule_id),product_code=values(product_code),product_name=values(product_name)
        ,enabled_flag=values(enabled_flag),create_by=values(create_by),update_by=values(update_by)
    </update>
</mapper>
