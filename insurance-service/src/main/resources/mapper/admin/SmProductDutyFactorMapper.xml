<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.product.SmProductDutyFactorMapper">
<!--    <insert id="insertDutyFactor"-->
<!--            parameterType="com.cfpamf.ms.insur.admin.pojo.dto.product.DutyFactorDTO"-->
<!--            useGeneratedKeys="true" >-->
<!--        INSERT INTO sm_product_duty_factor (-->
<!--            product_id,-->
<!--            spc_id,-->
<!--            duty_code,-->
<!--            duty_factor_code,-->
<!--            factor_value,-->
<!--            factor_name,-->
<!--            option_name,-->
<!--            flow,-->
<!--            is_default,-->
<!--            status-->
<!--        )-->
<!--        VALUES (-->
<!--            #{data.productId},-->
<!--            #{data.spcId},-->
<!--            #{data.dutyCode},-->
<!--            #{data.dutyFactorCode},-->
<!--            #{data.factorValue},-->
<!--            #{data.factorName},-->
<!--            #{data.optionName},-->
<!--            #{data.flow},-->
<!--            #{data.isDefault},-->
<!--            #{data.status}-->
<!--        )-->
<!--    </insert>-->

</mapper>
