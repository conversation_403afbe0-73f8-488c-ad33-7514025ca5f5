<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.TmpOrderValidPolicyMapper">
    <select id="selectExists" resultType="com.cfpamf.ms.insur.admin.pojo.po.order.impor.TmpOrderValidPolicy">
        select t.*, soi.fhOrderId dbFhOrderId
        from tmp_order_valid_policy t
                 inner join sm_order_insured soi on t.policy_no = soi.policyNo
        where batch = #{batchNo}
    </select>
    <select id="selectPolicyExists"
            resultType="com.cfpamf.ms.insur.admin.pojo.po.order.impor.TmpOrderValidPolicy">
        select t.*, soi.fhOrderId dbFhOrderId
        from tmp_order_valid_policy t
                 inner join sm_order_insured soi on t.policy_no = soi.policyNo
        where batch = #{batchNo}
    </select>
    <select id="selectExistsOnlyDb"
            resultType="com.cfpamf.ms.insur.admin.pojo.po.order.impor.TmpOrderValidPolicy">

        select
            id,
            soi.idNumber insuredIdNumber,
            soi.policyNo,
            soi.fhOrderId,
            soi.fhOrderId dbFhOrderId,
            appStatus
        from sm_order_insured soi
        where policyNo in
        <foreach collection="policies" item="policyNo" separator="," open="(" close=")">
            #{policyNo}
        </foreach>
    </select>


    <select id="listSmOrderInsuredByPolicyNos" resultType="com.cfpamf.ms.insur.admin.pojo.po.order.impor.TmpOrderValidPolicy">
        SELECT soi.idNumber insuredIdNumber,soi.policyNo,soi.fhOrderId,soi.fhOrderId dbFhOrderId,
        soi.id,soi.appStatus,so.productId,so.planId,so.totalAmount,so.paymentTime,so.recommendId
        FROM sm_order_insured soi left join sm_order so on soi.fhorderId = so.fhorderId
        WHERE soi.appStatus=1 and so.payStatus=2 and soi.policyNo IN
        <foreach collection="policyNos" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <select id="listSmOrderInsuredByOrderIds" resultType="com.cfpamf.ms.insur.admin.pojo.po.order.impor.TmpOrderValidPolicy">
        SELECT soi.idNumber insuredIdNumber,soi.policyNo,soi.fhOrderId,soi.fhOrderId dbFhOrderId,
        soi.id,soi.appStatus,so.productId,so.planId,so.totalAmount,so.paymentTime,so.recommendId,p.productAttrCode,detail.business_id businessId
        ,detail.account_time accountTime,detail.amount commissionAmount,detail.original_Amount,detail.term_num
        FROM sm_order_insured soi left join sm_order so on soi.fhorderId = so.fhorderId
        left join sm_product p on p.id = so.productId
        left join sm_commission_detail detail on detail.order_id = soi.fhorderId and detail.policy_status = soi.appStatus and soi.idNumber = detail.insured_id_number
        WHERE so.payStatus=2 and soi.fhOrderId IN
        <foreach collection="orderIds" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="getFastInsuredList"
            resultType="com.cfpamf.ms.insur.admin.pojo.po.order.impor.FastImportInsuredPO">
        select a.fhOrderId as orderId,idNumber,b.appStatus
        from sm_order a left join sm_order_insured b
        on   a.fhOrderId = b.fhOrderId
        where a.fhOrderId like concat(#{orderId},"%")
        and   a.payStatus='2'
        and   b.enabled_flag='0'
        and b.idNumber in
        <foreach collection="idNumberList" item="idNumber" index="index" open="(" separator="," close=")">
            #{idNumber}
        </foreach>
    </select>
</mapper>
