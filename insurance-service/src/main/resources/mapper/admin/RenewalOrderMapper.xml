<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cfpamf.ms.insur.admin.renewal.dao.RenewalOrderMapper">

    <!--    搜索待续保订单 start-->
    <select id="searchWaitRenewalOrderVo" resultType="com.cfpamf.ms.insur.admin.renewal.vo.WaitRenewalOrderVo">
        SELECT
        t1.id as orderInsuredId,
        t1.fhOrderId,
        t2.planId,
        t3.productAttrCode ,
        t3.productName,
        t3.channel,
        t3.id as productId,
        t4.personName as applicantPersonName,
        t1.personName as insuredPersonName,
        t2.totalAmount as insuredAmount,
        t1.policyNo as policyNo,
        t2.endTime as invalidTime,
        t2.startTime as startTime,
        t5.userName as recommendUserName,
        (DATEDIFF(NOW(),t2.endTime) - 1) as surplusDay,
        t6.after_expiration_day as graceDay,
        t7.userName as customerManager,
        t7.organizationName as customerManagerOrganization,
        t7.regionName as customerManagerRegion,
        t8.intention as intention,
        t8.service_mode as serviceMode
        FROM sm_order_insured t1

        LEFT JOIN sm_order t2 on t1.fhOrderId = t2.fhOrderId

        LEFT JOIN sm_product t3 on t3.id = t2.productId

        LEFT JOIN sm_order_applicant t4 on t1.fhOrderId = t4.fhOrderId

        LEFT JOIN auth_user t5 on t2.recommendId = t5.userId AND t5.enabled_flag = 0

        LEFT JOIN sm_renewal_config t6 ON t2.planId = t6.plan_id and t6.enabled_flag = 0

        LEFT JOIN auth_user t7 on t2.customerAdminId = t7.userId AND t7.enabled_flag = 0

        LEFT JOIN sm_order_renewal_follow t8 on t8.order_id = t1.fhOrderId and newest = 1

        WHERE

        1=1
        <if test="startInvalidTime !=  null">
            AND t2.endTime >= #{startInvalidTime}
        </if>
        <if test="endInvalidTime !=  null">
            and  <![CDATA[ t2.endTime <= #{endInvalidTime}]]>
        </if>
        and t1.appStatus = '1'
        AND t1.fhOrderId not in ( SELECT old_order_id from sm_order_renewal WHERE STATUS = 'RENEWED' )

        <if test="policyNo !=  null and policyNo != ''">
            AND t1.policyNo like CONCAT(#{policyNo},'%')
        </if>

        <if test="productName !=  null and productName != ''">
            AND t3.productName like CONCAT(#{productName},'%')
        </if>

        <if test='applicantdName != null '>
            AND
            <if test="applicantdType == 1">
                t4.personName LIKE CONCAT(#{applicantdName},'%')
            </if>
            <if test="applicantdType == 2">
                t4.idNumber LIKE CONCAT(#{applicantdName},'%')
            </if>
        </if>

        <if test='insuredName != null'>
            AND
            <if test="insuredType == 1">
                t1.personName LIKE CONCAT(#{insuredName},'%')
            </if>
            <if test="insuredType == 2">
                t1.idNumber LIKE CONCAT(#{insuredName},'%')
            </if>
        </if>

        <if test="productAttrCode != null and productAttrCode != ''">
            AND t3.productAttrCode = #{productAttrCode}
        </if>
        <if test='recommendId != null'>
            AND t2.recommendId= #{recommendId}
        </if>

        <if test='customerManagerId != null'>
            AND t2.customerAdminId= #{customerManagerId}
        </if>
        <if test='regionName != null'>
            AND t5.regionName=#{regionName}
        </if>
        <if test='orgName != null'>
            AND t5.organizationFullName=#{orgName}
        </if>
        <if test='orgPath !=null '>
            and t5.orgCode in (select orgCode from organization where orgPath like CONCAT(#{orgPath},'%'))
        </if>
        <if test='intention != null and intention != ""'>
            <if test = 'intention == "noFollowUp"'>
                and t8.id is null
            </if>
            <if test = 'intention != "noFollowUp"'>
                and t8.intention = #{intention}
            </if>
        </if>
        AND (DATEDIFF(NOW(),t2.endTime) - 1) >= t6.before_expiration_day
        <![CDATA[ AND DATEDIFF(NOW(),t2.endTime) <= t6.after_expiration_day]]>
        ORDER BY t2.endTime ASC
        <if test='limitRow != null'>
            LIMIT #{limitRow}, #{pageSize}
        </if>
    </select>
    <!--搜索待续保订单 end-->
    <select id="getWaitRenewalOrderCount" resultType="java.lang.Integer">
        SELECT
        count(1)
        FROM sm_order_insured t1

        LEFT JOIN sm_order t2 on t1.fhOrderId = t2.fhOrderId

        LEFT JOIN sm_product t3 on t3.id = t2.productId

        LEFT JOIN sm_order_applicant t4 on t1.fhOrderId = t4.fhOrderId

        LEFT JOIN auth_user t5 on t2.recommendId = t5.userId AND t5.enabled_flag = 0

        LEFT JOIN sm_renewal_config t6 ON t2.planId = t6.plan_id and t6.enabled_flag = 0

        LEFT JOIN sm_order_renewal_follow t8 on t8.order_id = t1.fhOrderId and newest = 1

        WHERE 1=1
        <if test="startInvalidTime !=  null">
            AND t2.endTime >= #{startInvalidTime}
        </if>
        <if test="endInvalidTime !=  null">
            and  <![CDATA[ t2.endTime <= #{endInvalidTime}]]>
        </if>
        and t1.appStatus = '1'
        AND t1.fhOrderId not in ( SELECT old_order_id from sm_order_renewal WHERE STATUS = 'RENEWED' )


        <if test="policyNo !=  null and policyNo != ''">
            AND t1.policyNo like CONCAT(#{policyNo},'%')
        </if>

        <if test="productName !=  null and productName != ''">
            AND t3.productName like CONCAT(#{productName},'%')
        </if>
        <if test="productType !=  null and productType != ''">
            AND t3.productType = #{productType}
        </if>

        <if test='applicantdName != null '>
            AND
            <if test="applicantdType == 1">
                t4.personName LIKE CONCAT(#{applicantdName},'%')
            </if>
            <if test="applicantdType == 2">
                t4.idNumber LIKE CONCAT(#{applicantdName},'%')
            </if>
            <if test="applicantdType == 3">
                t4.cellPhone LIKE CONCAT(#{applicantdName},'%')
            </if>
        </if>

        <if test='insuredName != null'>
            AND
            <if test="insuredType == 1">
                t1.personName LIKE CONCAT(#{insuredName},'%')
            </if>
            <if test="insuredType == 2">
                t1.idNumber LIKE CONCAT(#{insuredName},'%')
            </if>
            <if test="insuredType == 3">
                t1.cellPhone LIKE CONCAT(#{insuredName},'%')
            </if>
        </if>

        <if test="productAttrCode != null and productAttrCode != ''">
            AND t3.productAttrCode = #{productAttrCode}
        </if>
        <if test='recommendId != null'>
            AND t2.recommendId= #{recommendId}
        </if>

        <if test='customerManagerId != null'>
            AND t2.customerAdminId= #{customerManagerId}
        </if>
        <if test='regionName != null'>
            AND t5.regionName=#{regionName}
        </if>
        <if test='orgName != null'>
            AND t5.organizationFullName=#{orgName}
        </if>
        <if test='orgPath !=null '>
            and t5.orgCode in (select orgCode from organization where orgPath like CONCAT(#{orgPath},'%'))
        </if>
        <if test='intention != null and intention != ""'>
            <if test = 'intention == "noFollowUp"'>
                and t8.id is null
            </if>
            <if test = 'intention != "noFollowUp"'>
                and t8.intention = #{intention}
            </if>
        </if>
        AND (DATEDIFF(NOW(),t2.endTime) - 1) >= t6.before_expiration_day
        <![CDATA[ AND DATEDIFF(NOW(),t2.endTime) <= t6.after_expiration_day]]>
    </select>

    <!--        搜索已续购订单 start-->
    <select id="searchRenewedOrderVo" resultType="com.cfpamf.ms.insur.admin.renewal.vo.RenewedOrderVo">
        SELECT
        t1.id as orderInsuredId,
        t1.fhOrderId,
        t2.planId,
        t3.productAttrCode ,
        t3.productName,
        t3.channel,
        t3.id as productId,
        t4.personName as applicantPersonName,
        t1.personName as insuredPersonName,
        t2.totalAmount as insuredAmount,
        t1.policyNo as policyNo,
        t2.endTime as invalidTime,
        t2.startTime as startTime,
        t5.userName as recommendUserName,
        t8.new_order_id as renewalOrderId,
        t7.userName as customerManager,
        t7.organizationName as customerManagerOrganization,
        t7.regionName as customerManagerRegion,
        t9.intention as intention,
        t9.service_mode as serviceMode
        FROM sm_order_insured t1

        LEFT JOIN sm_order t2 on t1.fhOrderId = t2.fhOrderId

        LEFT JOIN sm_product t3 on t3.id = t2.productId

        LEFT JOIN sm_order_applicant t4 on t1.fhOrderId = t4.fhOrderId

        LEFT JOIN auth_user t5 on t2.recommendId = t5.userId AND t5.enabled_flag = 0

        LEFT JOIN sm_renewal_config t6 ON t2.planId = t6.plan_id and t6.enabled_flag = 0

        LEFT JOIN auth_user t7 on t2.customerAdminId = t7.userId AND t7.enabled_flag = 0

        LEFT JOIN sm_order_renewal t8 on t8.old_order_id = t1.fhOrderId and t8.id_number = t1.idNumber and
        t8.enabled_flag = 0

        LEFT JOIN sm_order_renewal_follow t9 on t9.order_id = t1.fhOrderId and newest = 1

        WHERE t8.status = 'RENEWED'
        <if test="startInvalidTime!=null ">
            <![CDATA[   and t2.endTime >= #{startInvalidTime}]]>
        </if>
        <if test="endInvalidTime!=null">
            <![CDATA[   and t2.endTime <= #{endInvalidTime}]]>
        </if>
        <if test="policyNo!=null and policyNo!=''">
            AND t1.policyNo like CONCAT(#{policyNo},'%')
        </if>

        <if test="productName!=null and productName!=''">
            AND t3.productName like CONCAT(#{productName},'%')
        </if>
        <if test="productType!=null and productType!=''">
            AND t3.productType = #{productType}
        </if>

        <if test='applicantdName != null '>
            AND
            <if test="applicantdType == 1">
                t4.personName LIKE CONCAT(#{applicantdName},'%')
            </if>
            <if test="applicantdType == 2">
                t4.idNumber LIKE CONCAT(#{applicantdName},'%')
            </if>
            <if test="applicantdType == 3">
                t4.cellPhone LIKE CONCAT(#{applicantdName},'%')
            </if>
        </if>

        <if test='insuredName != null'>
            AND
            <if test="insuredType == 1">
                t1.personName LIKE CONCAT(#{insuredName},'%')
            </if>
            <if test="insuredType == 2">
                t1.idNumber LIKE CONCAT(#{insuredName},'%')
            </if>
            <if test="insuredType == 3">
                t1.cellPhone LIKE CONCAT(#{insuredName},'%')
            </if>
        </if>

        <if test="productAttrCode != null and productAttrCode != ''">
            AND t3.productAttrCode = #{productAttrCode}
        </if>
        <if test='recommendId != null'>
            AND t2.recommendId= #{recommendId}
        </if>

        <if test='customerManagerId != null'>
            AND t2.customerAdminId= #{customerManagerId}
        </if>
        <if test='regionName != null'>
            AND t5.regionName=#{regionName}
        </if>
        <if test='orgName != null'>
            AND t5.organizationFullName=#{orgName}
        </if>
        <if test='orgPath !=null '>
            and t5.orgCode in (select orgCode from organization where orgPath like CONCAT(#{orgPath},'%'))
        </if>
        <if test='intention != null and intention != ""'>
            <if test = 'intention == "noFollowUp"'>
                and t9.id is null
            </if>
            <if test = 'intention != "noFollowUp"'>
                and t9.intention = #{intention}
            </if>
        </if>
        ORDER BY t1.create_time DESC


    </select>

    <!--        搜索已续购订单 end-->
    <!--    通过订单id搜索order的基础信息列表 start-->
    <select id="searchBaseOrderVoByFhOrderIdList"
            resultType="com.cfpamf.ms.insur.admin.renewal.vo.BaseOrderVo">
        SELECT
        t1.fhOrderId,
        t1.planId,
        t3.productAttrCode,
        t3.productName,
        t3.channel,
        t3.id as productId,
        t2.personName AS insuredPersonName,
        t1.totalAmount AS insuredAmount,
        t2.policyNo AS policyNo,
        t1.endTime AS invalidTime,
        t2.id as orderInsuredId,
        t1.startTime AS startTime
        FROM
        sm_order t1
        LEFT JOIN sm_order_insured t2 ON t1.fhOrderId = t2.fhOrderId
        LEFT JOIN sm_product t3 ON t3.id = t1.productId
        WHERE
        t1.fhOrderId IN
        <foreach collection="fhOrderIdList" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <!--    通过订单id搜索order的基础信息列表 end -->

    <!--    搜索已断保订单 start-->
    <select id="searchOverRenewalOrderVo" resultType="com.cfpamf.ms.insur.admin.renewal.vo.OverRenewalOrderVo">

        SELECT
        t1.id as orderInsuredId,
        t1.fhOrderId,
        t2.planId,
        t3.productAttrCode ,
        t3.productName,
        t3.id as productId,
        t3.channel,
        t4.personName as applicantPersonName,
        t1.personName as insuredPersonName,
        t2.totalAmount as insuredAmount,
        t1.policyNo as policyNo,
        t2.endTime as invalidTime,
        t2.startTime as startTime,
        t5.userName as recommendUserName,
        DATEDIFF(NOW(),t2.endTime) as surplusDay,
        t6.after_expiration_day as graceDay,
        t1.over_reason as overReason,
        t7.userName as customerManager,
        t7.organizationName as customerManagerOrganization,
        t7.regionName as customerManagerRegion,
        t9.intention as intention,
        t9.service_mode as serviceMode
        FROM sm_order_insured t1

        LEFT JOIN sm_order t2 on t1.fhOrderId = t2.fhOrderId

        LEFT JOIN sm_product t3 on t3.id = t2.productId

        LEFT JOIN sm_order_applicant t4 on t1.fhOrderId = t4.fhOrderId

        LEFT JOIN auth_user t5 on t2.recommendId = t5.userId AND t5.enabled_flag = 0

        LEFT JOIN sm_renewal_config t6 ON t2.planId = t6.plan_id and t6.enabled_flag = 0

        LEFT JOIN auth_user t7 on t2.customerAdminId = t7.userId AND t7.enabled_flag = 0

        LEFT JOIN sm_order_renewal_follow t9 on t9.order_id = t1.fhOrderId and newest = 1
        WHERE t1.appStatus = '1' AND t1.fhOrderId not in ( SELECT old_order_id from sm_order_renewal WHERE STATUS =
        'RENEWED' )
        <if test="startInvalidTime != null ">
            <![CDATA[   and t2.endTime >= #{startInvalidTime}]]>
        </if>
        <if test="endInvalidTime != null ">
            <![CDATA[   and t2.endTime <= #{endInvalidTime}]]>
        </if>
        <if test="policyNo !=  null and policyNo != ''">
            AND t1.policyNo like CONCAT(#{policyNo},'%')
        </if>

        <if test="productName !=  null and productName != ''">
            AND t3.productName like CONCAT(#{productName},'%')
        </if>
        <if test="productType !=  null and productType != ''">
            AND t3.productType = #{productType}
        </if>

        <if test='applicantdName != null '>
            AND
            <if test="applicantdType == 1">
                t4.personName LIKE CONCAT(#{applicantdName},'%')
            </if>
            <if test="applicantdType == 2">
                t4.idNumber LIKE CONCAT(#{applicantdName},'%')
            </if>
            <if test="applicantdType == 3">
                t4.cellPhone LIKE CONCAT(#{applicantdName},'%')
            </if>
        </if>

        <if test='insuredName != null'>
            AND
            <if test="insuredType == 1">
                t1.personName LIKE CONCAT(#{insuredName},'%')
            </if>
            <if test="insuredType == 2">
                t1.idNumber LIKE CONCAT(#{insuredName},'%')
            </if>
            <if test="insuredType == 3">
                t1.cellPhone LIKE CONCAT(#{insuredName},'%')
            </if>
        </if>

        <if test="productAttrCode != null and productAttrCode != ''">
            AND t3.productAttrCode = #{productAttrCode}
        </if>
        <if test='recommendId != null'>
            AND t2.recommendId= #{recommendId}
        </if>

        <if test='customerManagerId != null'>
            AND t2.customerAdminId= #{customerManagerId}
        </if>
        <if test='regionName != null'>
            AND t5.regionName=#{regionName}
        </if>
        <if test='orgName != null'>
            AND t5.organizationFullName=#{orgName}
        </if>
        <if test='orgPath !=null '>
            and t5.orgCode in (select orgCode from organization where orgPath like CONCAT(#{orgPath},'%'))
        </if>
        <if test='intention != null and intention != ""'>
            <if test = 'intention == "noFollowUp"'>
                and t9.id is null
            </if>
            <if test = 'intention != "noFollowUp"'>
                and t9.intention = #{intention}
            </if>
        </if>
        AND DATEDIFF(NOW(),t2.endTime) > t6.after_expiration_day
        ORDER BY t2.endTime DESC
    </select>
    <!--    搜索已断保订单 end -->

    <!--    通过订单的保单记录id查找订单信息 start-->
    <select id="findByOrderInsuredId" resultMap="OrderDetailVoMap">
        SELECT t1.id                             as orderInsuredId,
               t1.fhOrderId,
               t2.planId,
               t3.productAttrCode,
               t3.productName,
               t3.channel,
               t4.personName                     as applicantPersonName,
               t4.cellPhone                      as applicantPersonCellPhone,
               t4.email                          as applicantPersonEmail,
               t4.idNumber                       as applicantPersonIdNumber,
               t4.birthday                       as applicantPersonBirthday,

               t1.personName                     as insuredPersonName,
               t1.cellPhone                      as insuredPersonCellPhone,
               t1.email                          as insuredPersonEmail,
               t1.idNumber                       as insuredPersonIdNumber,
               t1.birthday                       as insuredPersonBirthday,
               t1.occupationCode                 as insuredPersonOccupationCode,
               t7.occupationName                 as insuredPersonOccupationName,

               t1.downloadURL                    as downloadURL,
               t2.totalAmount                    as insuredAmount,
               t1.policyNo                       as policyNo,
               t2.endTime                        as invalidTime,
               t2.startTime                      as startTime,
               t5.userName                       as recommendUserName,
               (DATEDIFF(NOW(), t2.endTime) - 1) as surplusDay,
               t9.after_expiration_day           as graceDay,
               t6.companyName                    as companyName,
               t1.over_reason                    as overReason,
               t8.`status`                       as renewalStatus,
               t8.new_order_id                   as renewalOrderId
        FROM sm_order_insured t1
                 LEFT JOIN sm_order t2 on t1.fhOrderId = t2.fhOrderId
                 LEFT JOIN sm_product t3 on t3.id = t2.productId
                 LEFT JOIN sm_order_applicant t4 on t1.fhOrderId = t4.fhOrderId
                 LEFT JOIN auth_user t5 on t2.recommendId = t5.userId AND t5.enabled_flag = 0
                 LEFT JOIN sm_company t6 on t3.companyId = t6.id AND t6.enabled_flag = 0
                 LEFT JOIN sm_company_occupation t7
                           ON t1.occupationCode = t7.occupationCode and t7.companyId = t3.companyId and
                              t7.enabled_flag = 0
                 LEFT JOIN sm_order_renewal t8
                           on t8.old_order_id = t1.fhOrderId and t8.id_number = t1.idNumber and t8.enabled_flag = 0
                 LEFT JOIN sm_renewal_config t9 ON t2.planId = t9.plan_id and t9.enabled_flag = 0

        WHERE t1.id =
              #{orderInsuredId}
        order by field(t8.`status`, 'CANCEL', 'RENEWED', 'WAITED') LIMIT 1
    </select>
    <select id="queryOrderDetail" resultMap="OrderDetailVoMap"
            parameterType="com.cfpamf.ms.insur.weixin.pojo.query.order.OrderDetailQuery">
        SELECT
        t1.id as orderInsuredId,
        t1.fhOrderId,
        t2.planId,
        t3.productAttrCode,
        t3.productName,
        t3.channel,
        t4.personName as applicantPersonName,
        t4.cellPhone as applicantPersonCellPhone,
        t4.email as applicantPersonEmail,
        t4.idNumber as applicantPersonIdNumber,
        t4.birthday as applicantPersonBirthday,

        t1.personName as insuredPersonName,
        t1.cellPhone as insuredPersonCellPhone,
        t1.email as insuredPersonEmail,
        t1.idNumber as insuredPersonIdNumber,
        t1.birthday as insuredPersonBirthday,
        t1.occupationCode as insuredPersonOccupationCode,
        t7.occupationName as insuredPersonOccupationName,

        t1.downloadURL as downloadURL,
        t2.totalAmount as insuredAmount,
        t1.policyNo as policyNo,
        t2.endTime as invalidTime,
        t2.startTime as startTime,
        t5.userName as recommendUserName,
        (DATEDIFF(NOW(), t2.endTime) - 1) as surplusDay,
        t9.after_expiration_day as graceDay,
        t6.companyName as companyName,
        t1.over_reason as overReason,
        t8.new_order_id as renewalOrderId
        FROM sm_order_insured t1
        LEFT JOIN sm_order t2 on t1.fhOrderId = t2.fhOrderId
        LEFT JOIN sm_product t3 on t3.id = t2.productId
        LEFT JOIN sm_order_applicant t4 on t1.fhOrderId = t4.fhOrderId
        LEFT JOIN auth_user t5 on t2.recommendId = t5.userId AND t5.enabled_flag = 0
        LEFT JOIN sm_company t6 on t3.companyId = t6.id AND t6.enabled_flag = 0
        LEFT JOIN sm_company_occupation t7
        ON t1.occupationCode = t7.occupationCode and t7.companyId = t3.companyId and
        t7.enabled_flag = 0
        LEFT JOIN sm_order_renewal t8
        on t8.old_order_id = t1.fhOrderId and t8.id_number = t1.idNumber and t8.enabled_flag = 0
        LEFT JOIN sm_renewal_config t9 ON t2.planId = t9.plan_id and t9.enabled_flag = 0

        WHERE t1.id = #{id}
        <if test='userId != null'>
            AND t2.customerAdminId=#{userId}
        </if>
        <if test='agentId != null'>
            AND t2.agentId=#{agentId}
        </if>
        <if test='customerAdminId != null'>
            AND t2.customerAdminId=#{customerAdminId}
        </if>
        order by field(t8.`status`, 'CANCEL', 'RENEWED', 'WAITED') LIMIT 1
    </select>

    <!--    通过订单的保单记录id查找订单信息 start-->
    <select id="findByFhOrderId" resultMap="OrderDetailVoMap">
        SELECT t1.id                             as orderInsuredId,
               t1.fhOrderId,
               t2.planId,
               t3.productAttrCode,
               t3.productName,
               t3.channel,
               t3.id                             as productId,
               t4.personName                     as applicantPersonName,
               t4.cellPhone                      as applicantPersonCellPhone,
               t4.email                          as applicantPersonEmail,
               t4.idNumber                       as applicantPersonIdNumber,
               t4.birthday                       as applicantPersonBirthday,

               t1.personName                     as insuredPersonName,
               t1.cellPhone                      as insuredPersonCellPhone,
               t1.email                          as insuredPersonEmail,
               t1.idNumber                       as insuredPersonIdNumber,
               t1.birthday                       as insuredPersonBirthday,
               t1.occupationCode                 as insuredPersonOccupationCode,
               t7.occupationName                 as insuredPersonOccupationName,

               t1.downloadURL                    as downloadURL,
               t2.totalAmount                    as insuredAmount,
               t1.policyNo                       as policyNo,
               t2.endTime                        as invalidTime,
               t2.startTime                      as startTime,
               t5.userName                       as recommendUserName,
               (DATEDIFF(NOW(), t2.endTime) - 1) as surplusDay,
               t9.after_expiration_day           as graceDay,
               t6.companyName                    as companyName,
               t1.over_reason                    as overReason,
               t8.new_order_id                   as renewalOrderId
        FROM sm_order_insured t1
                 LEFT JOIN sm_order t2 on t1.fhOrderId = t2.fhOrderId
                 LEFT JOIN sm_product t3 on t3.id = t2.productId
                 LEFT JOIN sm_order_applicant t4 on t1.fhOrderId = t4.fhOrderId
                 LEFT JOIN auth_user t5 on t2.recommendId = t5.userId AND t5.enabled_flag = 0
                 LEFT JOIN sm_company t6 on t3.companyId = t6.id AND t6.enabled_flag = 0
                 LEFT JOIN sm_company_occupation t7
                           ON t1.occupationCode = t7.occupationCode and t7.companyId = t3.companyId and
                              t7.enabled_flag = 0
                 LEFT JOIN sm_order_renewal t8
                           on t8.old_order_id = t1.fhOrderId and t8.id_number = t1.idNumber and t8.enabled_flag = 0
                 LEFT JOIN sm_renewal_config t9 ON t2.planId = t9.plan_id and t9.enabled_flag = 0


        WHERE t1.fhOrderId = #{fhOrderId}
        order by field(t8.`status`, 'CANCEL', 'RENEWED', 'WAITED') LIMIT 1
    </select>
    <!--    通过订单的保单记录id查找订单信息 start-->
    <select id="findByPolicyNo" resultMap="OrderDetailVoMap">
        SELECT t1.id                             as orderInsuredId,
               t1.fhOrderId,
               t2.planId,
               t3.productAttrCode,
               t3.productName,
               t3.channel,
               t3.id                             as productId,
               t4.personName                     as applicantPersonName,
               t4.cellPhone                      as applicantPersonCellPhone,
               t4.email                          as applicantPersonEmail,
               t4.idNumber                       as applicantPersonIdNumber,
               t4.birthday                       as applicantPersonBirthday,

               t1.personName                     as insuredPersonName,
               t1.cellPhone                      as insuredPersonCellPhone,
               t1.email                          as insuredPersonEmail,
               t1.idNumber                       as insuredPersonIdNumber,
               t1.birthday                       as insuredPersonBirthday,
               t1.occupationCode                 as insuredPersonOccupationCode,
               t7.occupationName                 as insuredPersonOccupationName,

               t1.downloadURL                    as downloadURL,
               t2.totalAmount                    as insuredAmount,
               t1.policyNo                       as policyNo,
               t2.endTime                        as invalidTime,
               t2.startTime                      as startTime,
               t5.userName                       as recommendUserName,
               (DATEDIFF(NOW(), t2.endTime) - 1) as surplusDay,
               t9.after_expiration_day           as graceDay,
               t6.companyName                    as companyName,
               t1.over_reason                    as overReason,
               t8.new_order_id                   as renewalOrderId
        FROM sm_order_insured t1
                 LEFT JOIN sm_order t2 on t1.fhOrderId = t2.fhOrderId
                 LEFT JOIN sm_product t3 on t3.id = t2.productId
                 LEFT JOIN sm_order_applicant t4 on t1.fhOrderId = t4.fhOrderId
                 LEFT JOIN auth_user t5 on t2.recommendId = t5.userId AND t5.enabled_flag = 0
                 LEFT JOIN sm_company t6 on t3.companyId = t6.id AND t6.enabled_flag = 0
                 LEFT JOIN sm_company_occupation t7
                           ON t1.occupationCode = t7.occupationCode and t7.companyId = t3.companyId and
                              t7.enabled_flag = 0
                 LEFT JOIN sm_order_renewal t8
                           on t8.old_order_id = t1.fhOrderId and t8.id_number = t1.idNumber and t8.enabled_flag = 0
                 LEFT JOIN sm_renewal_config t9 ON t2.planId = t9.plan_id and t9.enabled_flag = 0


        WHERE t1.policyNo = #{policyNo}
        order by field(t8.`status`, 'CANCEL', 'RENEWED', 'WAITED') LIMIT 1
    </select>

    <!--    通过订单的保单记录id查找订单信息 end -->
    <resultMap id="OrderDetailVoMap" type="com.cfpamf.ms.insur.admin.renewal.vo.OrderDetailVo">
        <!--        订单信息映射-->
        <result column="id" property="orderInsuredId"/>
        <result column="fhOrderId" property="fhOrderId"/>
        <result column="planId" property="planId"/>
        <result column="productAttrCode" property="productAttrCode"/>
        <result column="productName" property="productName"/>
        <result column="productAttrCode" property="productAttrCode"/>
        <result column="applicantPersonName" property="applicantPersonName"/>
        <result column="insuredPersonName" property="insuredPersonName"/>
        <result column="insuredAmount" property="insuredAmount"/>
        <result column="policyNo" property="policyNo"/>
        <result column="invalidTime" property="invalidTime"/>
        <result column="startTime" property="startTime"/>
        <result column="recommendUserName" property="recommendUserName"/>
        <result column="surplusDay" property="surplusDay"/>
        <result column="graceDay" property="graceDay"/>
        <result column="renewalOrderId" property="renewalOrderId"/>
        <result column="renewalStatus" property="renewalStatus"/>
        <result column="renewalPlatform" property="renewalPlatform"/>

        <!--        被保人信息映射-->
        <result column="insuredPersonName" property="insuredPerson.name"/>
        <result column="insuredPersonEmail" property="insuredPerson.email"/>
        <result column="insuredPersonIdNumber" property="insuredPerson.idCard"/>
        <result column="insuredPersonCellPhone" property="insuredPerson.mobileNumber"/>
        <result column="insuredPersonOccupationCode" property="insuredPerson.occupationCode"/>
        <result column="insuredPersonOccupationName" property="insuredPerson.occupationName"/>
        <result column="insuredPersonBirthday" property="insuredPerson.birthday"/>
        <!--        投保人信息映射-->
        <result column="applicantPersonName" property="applicantPerson.name"/>
        <result column="applicantPersonEmail" property="applicantPerson.email"/>
        <result column="applicantPersonIdNumber" property="applicantPerson.idCard"/>
        <result column="applicantPersonCellPhone" property="applicantPerson.mobileNumber"/>
        <result column="applicantPersonBirthday" property="applicantPerson.birthday"/>
    </resultMap>


    <!--    搜索微信端待续保订单 start-->
    <select id="searchWxWaitRenewalOrderVo"
            resultType="com.cfpamf.ms.insur.admin.renewal.vo.WaitRenewalOrderVo">
        SELECT
        t1.id as orderInsuredId,
        t1.fhOrderId,
        t2.planId,
        t3.productAttrCode ,
        t3.productName,
        t3.id as productId,
        t3.channel,
        t4.personName as applicantPersonName,
        t1.personName as insuredPersonName,
        t2.totalAmount as insuredAmount,
        t1.policyNo as policyNo,
        t2.endTime as invalidTime,
        t2.startTime as startTime,
        t5.userName as recommendUserName,
        (DATEDIFF(NOW(),t2.endTime) - 1) as surplusDay,
        t6.after_expiration_day as graceDay,
        t6.before_expiration_day as advanceDay,
        t7.userName as customerManager,
        t7.organizationName as customerManagerOrganization,
        t7.regionName as customerManagerRegion,
        if(DATEDIFF(NOW(),t2.endTime) -1 >= 0 , t6.after_expiration_day - (DATEDIFF(NOW(),t2.endTime) - 1) ,
        ABS(DATEDIFF(NOW(),t2.endTime) -1) + 1000 ) as sortField,
        t8.intention,
        t8.service_mode as serviceMode

        FROM sm_order_insured t1

        LEFT JOIN sm_order t2 on t1.fhOrderId = t2.fhOrderId

        LEFT JOIN sm_product t3 on t3.id = t2.productId

        LEFT JOIN sm_order_applicant t4 on t1.fhOrderId = t4.fhOrderId

        LEFT JOIN auth_user t5 on t2.recommendId = t5.userId AND t5.enabled_flag = 0

        LEFT JOIN sm_renewal_config t6 ON t2.planId = t6.plan_id and t6.enabled_flag = 0

        LEFT JOIN auth_user t7 on t2.customerAdminId = t7.userId AND t7.enabled_flag = 0

        LEFT JOIN sm_order_renewal_follow t8 on t8.order_id = t1.fhOrderId and newest = 1

        WHERE t1.appStatus = '1'

        AND t1.fhOrderId not in ( SELECT old_order_id from sm_order_renewal WHERE STATUS = 'RENEWED' )
        <if test='userId != null'>
            AND t2.customerAdminId=#{userId}
        </if>
        <if test='agentId != null'>
            AND t2.agentId=#{agentId}
        </if>
        <if test='openId != null'>
            AND t2.wxOpenId=#{openId}
        </if>
        <if test='customerAdminId != null'>
            AND t2.customerAdminId=#{customerAdminId}
        </if>

        <if test='startTime != null '>
            <![CDATA[ AND #{startTime} <=  date_add(t2.endTime, interval t6.after_expiration_day day) ]]>

        </if>
        <if test='endTime  != null'>
            <![CDATA[ AND  #{endTime} >=  date_add(t2.endTime, interval t6.before_expiration_day day) ]]>

        </if>
        <if test='channel != null'>
            AND t3.channel=#{channel}
        </if>
        <if test='keyword != null'>
            AND ( t1.personName LIKE CONCAT(#{keyword},'%')
            OR t4.personName LIKE CONCAT(#{keyword},'%')
            OR t1.policyNo LIKE CONCAT(#{keyword},'%')
            OR t3.productName LIKE CONCAT(#{keyword},'%') )
        </if>
        <if test='intention != null and intention != ""'>
            <if test = 'intention == "noFollowUp"'>
                and t8.id is null
            </if>
            <if test = 'intention != "noFollowUp"'>
                and t8.intention = #{intention}
            </if>
        </if>
        AND (DATEDIFF(NOW(),t2.endTime) - 1) >= t6.before_expiration_day
        <![CDATA[ AND DATEDIFF(NOW(),t2.endTime) <= t6.after_expiration_day]]>
        <choose>
            <when test=" timeSortType=='DESC'">
                ORDER BY date_add(t2.endTime, INTERVAL 1 DAY) DESC
            </when>
            <when test="timeSortType=='ASC'">
                ORDER BY date_add(t2.endTime, INTERVAL 1 DAY) ASC
            </when>
            <when test=" insuredAmountSortType == 'DESC'">
                ORDER BY t2.totalAmount DESC
            </when>
            <when test="insuredAmountSortType == 'ASC'">
                ORDER BY t2.totalAmount ASC
            </when>
            <otherwise>
                ORDER BY date_add(t2.endTime, INTERVAL 1 DAY) ASC
            </otherwise>
        </choose>
    </select>

    <!--    搜索微信端待续保订单条数 start-->
    <select id="searchWxWaitRenewalOrderVoCount"
            resultType="java.lang.Integer">
        SELECT
        count(DISTINCT t1.policyNo)

        FROM sm_order_insured t1

        LEFT JOIN sm_order t2 on t1.fhOrderId = t2.fhOrderId

        LEFT JOIN sm_product t3 on t3.id = t2.productId

        LEFT JOIN sm_order_applicant t4 on t1.fhOrderId = t4.fhOrderId

        LEFT JOIN auth_user t5 on t2.recommendId = t5.userId AND t5.enabled_flag = 0

        LEFT JOIN sm_renewal_config t6 ON t2.planId = t6.plan_id and t6.enabled_flag = 0

        LEFT JOIN auth_user t7 on t2.customerAdminId = t7.userId AND t7.enabled_flag = 0

        LEFT JOIN sm_order_renewal_follow t8 on t8.order_id = t1.fhOrderId and newest = 1 and type = 'RENEW'

        WHERE t1.appStatus = '1'
        <if test='userId != null'>
            AND t2.customerAdminId=#{userId}
        </if>
        <if test='agentId != null'>
            AND t2.agentId=#{agentId}
        </if>
        <if test='openId != null'>
            AND t2.wxOpenId=#{openId}
        </if>
        <if test='customerAdminId != null'>
            AND t2.customerAdminId=#{customerAdminId}
        </if>
        AND t1.fhOrderId not in ( SELECT old_order_id from sm_order_renewal WHERE STATUS = 'RENEWED' )
        AND (DATEDIFF(NOW(),t2.endTime) - 1) >= t6.before_expiration_day
        <![CDATA[ AND DATEDIFF(NOW(),t2.endTime) <= t6.after_expiration_day]]>
    </select>
    <!--    搜索微信端待续保订单 end-->

    <!--    搜索已续购订单 start-->
    <select id="searchWxRenewedOrderVo" resultType="com.cfpamf.ms.insur.admin.renewal.vo.RenewedOrderVo">
        SELECT
        t1.id AS orderInsuredId,
        t1.fhOrderId,
        t2.planId,
        t3.productAttrCode,
        t3.productName,
        t3.id AS productId,
        t3.channel,
        t4.personName AS applicantPersonName,
        t1.personName AS insuredPersonName,
        t2.totalAmount AS insuredAmount,
        t1.policyNo AS policyNo,
        t2.endTime AS invalidTime,
        t2.startTime AS startTime,
        t5.userName AS recommendUserName,
        t8.new_order_id AS renewalOrderId,
        t7.userName AS customerManager,
        t7.organizationName AS customerManagerOrganization,
        t7.regionName AS customerManagerRegion
        FROM
        sm_order_renewal t8
        LEFT JOIN sm_order_insured t1 ON t8.old_order_id = t1.fhOrderId
        AND t8.id_number = t1.idNumber

        LEFT JOIN sm_order t2 ON t1.fhOrderId = t2.fhOrderId
        LEFT JOIN sm_product t3 ON t3.id = t2.productId
        LEFT JOIN sm_order_applicant t4 ON t1.fhOrderId = t4.fhOrderId
        LEFT JOIN auth_user t5 ON t2.recommendId = t5.userId
        AND t5.enabled_flag = 0
        LEFT JOIN sm_renewal_config t6 ON t2.planId = t6.plan_id
        AND t6.enabled_flag = 0
        LEFT JOIN auth_user t7 ON t2.customerAdminId = t7.userId
        AND t7.enabled_flag = 0
        WHERE
        t8.STATUS = 'RENEWED' AND t8.enabled_flag = 0
        <if test='userId != null'>
            AND t2.customerAdminId=#{userId}
        </if>
        <if test='agentId != null'>
            AND t2.agentId=#{agentId}
        </if>
        <if test='openId != null'>
            AND t2.wxOpenId=#{openId}
        </if>
        <if test='customerAdminId != null'>
            AND t2.customerAdminId=#{customerAdminId}
        </if>
        <if test='startTime != null '>
            <![CDATA[ AND  t2.endTime >= #{startTime} ]]>
        </if>
        <if test='endTime  != null'>
            <![CDATA[ AND  t2.endTime <=  #{endTime}  ]]>
        </if>
        <if test='channel != null'>
            AND t3.channel=#{channel}
        </if>
        <if test='keyword != null'>
            AND ( t1.personName LIKE CONCAT(#{keyword},'%')
            OR t4.personName LIKE CONCAT(#{keyword},'%')
            OR t1.policyNo LIKE CONCAT(#{keyword},'%')
            OR t3.productName LIKE CONCAT(#{keyword},'%') )
        </if>
        <choose>
            <when test=" timeSortType=='DESC'">
                ORDER BY t2.endTime DESC
            </when>
            <when test="timeSortType=='ASC'">
                ORDER BY t2.endTime ASC
            </when>
            <when test=" insuredAmountSortType == 'DESC'">
                ORDER BY t2.totalAmount DESC
            </when>
            <when test="insuredAmountSortType == 'ASC'">
                ORDER BY t2.totalAmount ASC
            </when>
            <otherwise>
                ORDER BY t2.endTime DESC
            </otherwise>
        </choose>
    </select>
    <!--    搜索已续购订单 end-->

    <!--    搜索已断保订单 start-->
    <select id="searchWxOverRenewalOrderVo"
            resultType="com.cfpamf.ms.insur.admin.renewal.vo.OverRenewalOrderVo">
        SELECT
        t1.id as orderInsuredId,
        t1.fhOrderId,
        t2.planId,
        t3.productAttrCode ,
        t3.productName,
        t3.id as productId,
        t3.channel,
        t4.personName as applicantPersonName,
        t1.personName as insuredPersonName,
        t2.totalAmount as insuredAmount,
        t1.policyNo as policyNo,
        t2.endTime as invalidTime,
        t2.startTime as startTime,
        t5.userName as recommendUserName,
        (DATEDIFF(NOW(),t2.endTime) - 1) as surplusDay,
        t6.after_expiration_day as graceDay,
        t1.over_reason as overReason,
        t7.userName as customerManager,
        t7.organizationName as customerManagerOrganization,
        t7.regionName as customerManagerRegion
        FROM sm_order_insured t1

        LEFT JOIN sm_order t2 on t1.fhOrderId = t2.fhOrderId

        LEFT JOIN sm_product t3 on t3.id = t2.productId

        LEFT JOIN sm_order_applicant t4 on t1.fhOrderId = t4.fhOrderId

        LEFT JOIN auth_user t5 on t2.recommendId = t5.userId AND t5.enabled_flag = 0

        LEFT JOIN sm_renewal_config t6 ON t2.planId = t6.plan_id and t6.enabled_flag = 0

        LEFT JOIN auth_user t7 on t2.customerAdminId = t7.userId AND t7.enabled_flag = 0

        WHERE t1.appStatus = '1'
        AND t2.create_time >= DATE_SUB(now(),INTERVAL 2 YEAR )
        AND t1.fhOrderId not in ( SELECT old_order_id from sm_order_renewal WHERE STATUS ='RENEWED' )
        <if test='userId != null'>
            AND t2.customerAdminId=#{userId}
        </if>
        <if test='agentId != null'>
            AND t2.agentId=#{agentId}
        </if>
        <if test='openId != null'>
            AND t2.wxOpenId=#{openId}
        </if>
        <if test='customerAdminId != null'>
            AND t2.customerAdminId=#{customerAdminId}
        </if>
        <if test='startTime != null '>
            <![CDATA[ AND  t2.endTime >= #{startTime} ]]>
        </if>
        <if test='endTime  != null'>
            <![CDATA[ AND  t2.endTime <=  #{endTime}  ]]>
        </if>
        <if test='channel != null'>
            AND t3.channel=#{channel}
        </if>
        <if test='keyword != null'>
            AND ( t1.personName LIKE CONCAT(#{keyword},'%')
            OR t4.personName LIKE CONCAT(#{keyword},'%')
            OR t1.policyNo LIKE CONCAT(#{keyword},'%')
            OR t3.productName LIKE CONCAT(#{keyword},'%') )
        </if>
        AND DATEDIFF(NOW(),t2.endTime) > t6.after_expiration_day
        <choose>
            <when test="overReasonFlag == 1">
                and t1.over_reason is not null
            </when>
            <when test="overReasonFlag == 2">
                and t1.over_reason is null
            </when>
            <otherwise>
                and 1=1
            </otherwise>
        </choose>
        <choose>
            <when test=" timeSortType=='DESC'">
                ORDER BY t2.endTime DESC
            </when>
            <when test="timeSortType=='ASC'">
                ORDER BY t2.endTime ASC
            </when>
            <when test=" insuredAmountSortType == 'DESC'">
                ORDER BY t2.totalAmount DESC
            </when>
            <when test="insuredAmountSortType == 'ASC'">
                ORDER BY t2.totalAmount ASC
            </when>
            <otherwise>
                ORDER BY t2.endTime DESC
            </otherwise>
        </choose>
    </select>
    <select id="waitRenewalStatistics" resultType="java.lang.Integer">
        SELECT count(1)
        FROM sm_order_insured t1
        LEFT JOIN sm_order t2 on t1.fhOrderId = t2.fhOrderId
        LEFT JOIN sm_renewal_config t6 ON t2.planId = t6.plan_id and t6.enabled_flag = 0
        WHERE t1.appStatus = '1'
        AND (DATEDIFF(NOW(),t2.endTime) - 1) >= t6.before_expiration_day
        <![CDATA[ AND DATEDIFF(NOW(),t2.endTime) <= t6.after_expiration_day]]>
        <if test='startTime != null '>
            <![CDATA[ AND #{startTime} <=  date_add(t2.endTime, interval t6.after_expiration_day day) ]]>

        </if>
        <if test='endTime  != null'>
            <![CDATA[ AND  #{endTime} >=  date_add(t2.endTime, interval t6.before_expiration_day day) ]]>

        </if>
        <if test='userId != null'>
            AND t2.customerAdminId=#{userId}
        </if>
        <if test='agentId != null'>
            AND t2.agentId=#{agentId}
        </if>
        <if test='openId != null'>
            AND t2.wxOpenId=#{openId}
        </if>
        AND t1.fhOrderId not in (SELECT old_order_id from sm_order_renewal WHERE STATUS = 'RENEWED')
    </select>
    <!--搜索已断保订单 end-->

    <select id="overRenewalStatistics" resultType="java.lang.Integer">
        SELECT
        count(1)
        FROM sm_order_insured t1

        LEFT JOIN sm_order t2 on t1.fhOrderId = t2.fhOrderId

        LEFT JOIN sm_renewal_config t6 ON t2.planId = t6.plan_id and t6.enabled_flag = 0

        WHERE t1.appStatus = '1' AND t1.fhOrderId not in ( SELECT old_order_id from sm_order_renewal WHERE STATUS =
        'RENEWED' )
        <if test='userId != null'>
            AND t2.customerAdminId=#{userId}
        </if>
        <if test='agentId != null'>
            AND t2.agentId=#{agentId}
        </if>
        <if test='openId != null'>
            AND t2.wxOpenId=#{openId}
        </if>
        <if test='startTime != null '>
            <![CDATA[ AND  t2.endTime >= #{startTime} ]]>
        </if>
        <if test='endTime  != null'>
            <![CDATA[ AND  t2.endTime <  #{endTime}  ]]>
        </if>
        AND DATEDIFF(NOW(),t2.endTime) > t6.after_expiration_day
    </select>

    <select id="renewedStatistics" resultType="java.lang.Integer">
        SELECT
        count(1)
        FROM
        sm_order_renewal t8
        LEFT JOIN sm_order_insured t1 ON t8.old_order_id = t1.fhOrderId
        AND t8.id_number = t1.idNumber
        LEFT JOIN sm_order t2 ON t1.fhOrderId = t2.fhOrderId
        WHERE
        t8.STATUS = 'RENEWED' AND t8.enabled_flag = 0
        <if test='userId != null'>
            AND t2.customerAdminId=#{userId}
        </if>
        <if test='agentId != null'>
            AND t2.agentId=#{agentId}
        </if>
        <if test='openId != null'>
            AND t2.wxOpenId=#{openId}
        </if>
        <if test='startTime != null '>
            <![CDATA[ AND  t2.endTime >= #{startTime} ]]>
        </if>
        <if test='endTime  != null'>
            <![CDATA[ AND  t2.endTime <  #{endTime}  ]]>
        </if>
    </select>

    <select id="getOrderRenewalVo" resultType="com.cfpamf.ms.insur.admin.renewal.vo.OrderRenewalVo">
        SELECT t1.id                                                   as orderInsuredId,
               t1.idNumber                                             as insuredIdNumber,
               t1.fhOrderId,
               t2.planId,
               t3.productAttrCode,
               t3.productName,
               t3.channel,
               t4.personName                                           as applicantPersonName,
               t4.cellPhone                                            as applicantPersonCellPhone,
               t4.email                                                as applicantPersonEmail,
               t4.idNumber                                             as applicantPersonIdNumber,
               t4.birthday                                             as applicantPersonBirthday,

               t1.personName                                           as insuredPersonName,
               t1.cellPhone                                            as insuredPersonCellPhone,
               t1.email                                                as insuredPersonEmail,
               t1.idNumber                                             as insuredPersonIdNumber,
               t1.birthday                                             as insuredPersonBirthday,
               t1.occupationCode                                       as insuredPersonOccupationCode,
               t7.occupationName                                       as insuredPersonOccupationName,

               t1.downloadURL                                          as downloadURL,
               t2.totalAmount                                          as insuredAmount,
               t1.policyNo                                             as policyNo,
               t2.endTime                                              as invalidTime,
               t2.startTime                                            as startTime,
               t5.userName                                             as recommendUserName,
               (DATEDIFF(NOW(), t2.endTime) - 1)                       as surplusDay,
               t9.after_expiration_day                                 as graceDay,
               t6.companyName                                          as companyName,
               t1.over_reason                                          as overReason,
               t8.new_order_id                                         as renewalOrderId,
               t8.`status`                                             as renewalStatus,
               (DATEDIFF(NOW(), t2.endTime) > t9.after_expiration_day) as overRenewalPeriod
        FROM sm_order_insured t1
                 LEFT JOIN sm_order t2 on t1.fhOrderId = t2.fhOrderId
                 LEFT JOIN sm_product t3 on t3.id = t2.productId
                 LEFT JOIN sm_order_applicant t4 on t1.fhOrderId = t4.fhOrderId
                 LEFT JOIN auth_user t5 on t2.recommendId = t5.userId AND t5.enabled_flag = 0
                 LEFT JOIN sm_company t6 on t3.companyId = t6.id AND t6.enabled_flag = 0
                 LEFT JOIN sm_company_occupation t7
                           ON t1.occupationCode = t7.occupationCode and t7.companyId = t3.companyId and
                              t7.enabled_flag = 0
                 LEFT JOIN sm_order_renewal t8
                           on t8.old_order_id = t1.fhOrderId and t8.id_number = t1.idNumber and t8.enabled_flag = 0
                 LEFT JOIN sm_renewal_config t9 ON t2.planId = t9.plan_id and t9.enabled_flag = 0

        WHERE t1.policyNo = #{policyNo}
          and t1.appStatus = '1'
        order by field(t8.`status`, 'CANCEL', 'RENEWED', 'WAITED') LIMIT 1
    </select>


    <!--    添加断保原因 start-->
    <update id="addOverRenewalReason">
        update sm_order_insured
        set over_reason = #{overRenewalReason}
        where id = #{orderInsuredId}
    </update>
    <!--    添加断保原因 start-->
    <update id="addInsuredsOverRenewalReason">
        <foreach collection="orderInsuredIds" item="item" separator=";">
            update sm_order_insured
            set over_reason = #{overRenewalReason}
            where id = #{item}
        </foreach>
    </update>
    <!--    添加断保原因 end-->
    <select id="getRenewalRemindDTOList"
            resultType="com.cfpamf.ms.insur.admin.job.renewal.dto.RenewalRemindDTO">
        SELECT t3.productName,
        t4.personName as applicantName,
        t4.cellPhone as applicantMobile,
        t2.endTime as endTime,
        (DATEDIFF(NOW(), t2.endTime) - 1) as expireDay,
        ifnull(t6.after_expiration_day, 0) as graceDay,
        t7.userName as customerAdminName,
        t7.userMobile as customerAdminMobile
        FROM sm_order_insured t1
        LEFT JOIN sm_order t2 on t1.fhOrderId = t2.fhOrderId
        LEFT JOIN sm_product t3 on t3.id = t2.productId
        LEFT JOIN sm_order_applicant t4 on t1.fhOrderId = t4.fhOrderId
        LEFT JOIN sm_renewal_config t6 ON t2.planId = t6.plan_id and t6.enabled_flag = 0
        LEFT JOIN auth_user t7 on t2.customerAdminId = t7.userId AND t7.enabled_flag = 0

        WHERE t1.appStatus = '1'
        AND t1.fhOrderId not in ( SELECT old_order_id from sm_order_renewal WHERE STATUS = 'RENEWED' )
        <if test="null != productIdList and productIdList.size > 0">
            and t3.id in
            <foreach collection="productIdList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="null != expireDayList and expireDayList.size > 0">
            and (DATEDIFF(NOW(), t2.endTime) - 1) in
            <foreach collection="expireDayList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <!--    搜索已断保订单 start-->
    <select id="countWaitRenewalCountByOrderId"
            resultType="java.lang.Integer">
        SELECT
        count(*)

        FROM sm_order_insured t1

        LEFT JOIN sm_order t2 on t1.fhOrderId = t2.fhOrderId

        LEFT JOIN sm_product t3 on t3.id = t2.productId

        LEFT JOIN sm_order_applicant t4 on t1.fhOrderId = t4.fhOrderId

        LEFT JOIN auth_user t5 on t2.recommendId = t5.userId AND t5.enabled_flag = 0

        LEFT JOIN sm_renewal_config t6 ON t2.planId = t6.plan_id and t6.enabled_flag = 0

        LEFT JOIN auth_user t7 on t2.customerAdminId = t7.userId AND t7.enabled_flag = 0

        WHERE t1.appStatus = '1'
        AND t2.fhOrderId = #{orderId}
        and t2.productId = #{productId}

        AND t1.fhOrderId not in ( SELECT old_order_id from sm_order_renewal WHERE STATUS = 'RENEWED' )

        AND t2.customerAdminId=#{customerAdminId}
        AND <![CDATA[ #{startTime} <=  date_add(t2.endTime, interval t6.after_expiration_day day) ]]>
        AND <![CDATA[ #{endTime} >=  date_add(t2.endTime, interval t6.before_expiration_day day) ]]>

        AND  <![CDATA[ (DATEDIFF(NOW(),t2.endTime) - 1) >= t6.before_expiration_day ]]>
        AND <![CDATA[  DATEDIFF(NOW(),t2.endTime) <= t6.after_expiration_day ]]>
    </select>
</mapper>
