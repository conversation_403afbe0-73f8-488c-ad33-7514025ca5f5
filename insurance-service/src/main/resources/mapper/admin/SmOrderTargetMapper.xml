<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.base.dao.SmOrderTargetMapper">

    <select id="listRegionOrderTargets" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmOrderTargetVO">
        SELECT t1.id,
        1 AS type,
        t0.orgName AS regionName,
        t1.year AS year,
        t1.yearPersonTarget,
        t1.yearAmountTarget,
        t1.month_amount_01 AS monthAmountTarget01,
        t1.month_amount_02 AS monthAmountTarget02,
        t1.month_amount_03 AS monthAmountTarget03,
        t1.month_amount_04 AS monthAmountTarget04,
        t1.month_amount_05 AS monthAmountTarget05,
        t1.month_amount_06 AS monthAmountTarget06,
        t1.month_amount_07 AS monthAmountTarget07,
        t1.month_amount_08 AS monthAmountTarget08,
        t1.month_amount_09 AS monthAmountTarget09,
        t1.month_amount_10 AS monthAmountTarget10,
        t1.month_amount_11 AS monthAmountTarget11,
        t1.month_amount_12 AS monthAmountTarget12,
        t1.month_person_01 AS monthPersonActual01,
        t1.month_person_02 AS monthPersonActual02,
        t1.month_person_03 AS monthPersonActual03,
        t1.month_person_04 AS monthPersonActual04,
        t1.month_person_05 AS monthPersonActual05,
        t1.month_person_06 AS monthPersonActual06,
        t1.month_person_07 AS monthPersonActual07,
        t1.month_person_08 AS monthPersonActual08,
        t1.month_person_09 AS monthPersonActual09,
        t1.month_person_10 AS monthPersonActual10,
        t1.month_person_11 AS monthPersonActual11,
        t1.month_person_12 AS monthPersonActual12,
        t2.monthAmountActual01,
        t2.monthAmountActual02,
        t2.monthAmountActual03,
        t2.monthAmountActual04,
        t2.monthAmountActual05,
        t2.monthAmountActual06,
        t2.monthAmountActual07,
        t2.monthAmountActual08,
        t2.monthAmountActual09,
        t2.monthAmountActual10,
        t2.monthAmountActual11,
        t2.monthAmountActual12,
        t2.monthPersonActual01,
        t2.monthPersonActual02,
        t2.monthPersonActual03,
        t2.monthPersonActual04,
        t2.monthPersonActual05,
        t2.monthPersonActual06,
        t2.monthPersonActual07,
        t2.monthPersonActual08,
        t2.monthPersonActual09,
        t2.monthPersonActual10,
        t2.monthPersonActual11,
        t2.monthPersonActual12,
        t1.create_by,
        t1.create_time,
        t1.update_by,
        t1.update_time
        FROM organization t0
        LEFT JOIN sm_sales_target t1
        ON t1.regionName = t0.orgName AND t1.type = 1
        LEFT JOIN
        (
        SELECT
        t11.regionName,
        MAX(CASE ym WHEN '01' THEN qty ELSE 0 END ) AS monthPersonActual01,
        MAX(CASE ym WHEN '01' THEN amount ELSE 0 END ) AS monthAmountActual01,
        MAX(CASE ym WHEN '02' THEN qty ELSE 0 END ) AS monthPersonActual02,
        MAX(CASE ym WHEN '02' THEN amount ELSE 0 END ) AS monthAmountActual02,
        MAX(CASE ym WHEN '03' THEN qty ELSE 0 END ) AS monthPersonActual03,
        MAX(CASE ym WHEN '03' THEN amount ELSE 0 END ) AS monthAmountActual03,
        MAX(CASE ym WHEN '04' THEN qty ELSE 0 END ) AS monthPersonActual04,
        MAX(CASE ym WHEN '04' THEN amount ELSE 0 END ) AS monthAmountActual04,
        MAX(CASE ym WHEN '05' THEN qty ELSE 0 END ) AS monthPersonActual05,
        MAX(CASE ym WHEN '05' THEN amount ELSE 0 END ) AS monthAmountActual05,
        MAX(CASE ym WHEN '06' THEN qty ELSE 0 END ) AS monthPersonActual06,
        MAX(CASE ym WHEN '06' THEN amount ELSE 0 END ) AS monthAmountActual06,
        MAX(CASE ym WHEN '07' THEN qty ELSE 0 END ) AS monthPersonActual07,
        MAX(CASE ym WHEN '07' THEN amount ELSE 0 END ) AS monthAmountActual07,
        MAX(CASE ym WHEN '08' THEN qty ELSE 0 END ) AS monthPersonActual08,
        MAX(CASE ym WHEN '08' THEN amount ELSE 0 END ) AS monthAmountActual08,
        MAX(CASE ym WHEN '09' THEN qty ELSE 0 END ) AS monthPersonActual09,
        MAX(CASE ym WHEN '09' THEN amount ELSE 0 END ) AS monthAmountActual09,
        MAX(CASE ym WHEN '10' THEN qty ELSE 0 END ) AS monthPersonActual10,
        MAX(CASE ym WHEN '10' THEN amount ELSE 0 END ) AS monthAmountActual10,
        MAX(CASE ym WHEN '11' THEN qty ELSE 0 END ) AS monthPersonActual11,
        MAX(CASE ym WHEN '11' THEN amount ELSE 0 END ) AS monthAmountActual11,
        MAX(CASE ym WHEN '12' THEN qty ELSE 0 END ) AS monthPersonActual12,
        MAX(CASE ym WHEN '12' THEN amount ELSE 0 END ) AS monthAmountActual12
        FROM
        (
        SELECT t3.regionName,
        DATE_FORMAT(t1.submitTime, '%m') AS ym,
        COUNT(t1.fhOrderId) AS qty,
        SUM(t1.unitPrice*t1.qty) AS amount
        FROM sm_order t1
        LEFT JOIN sm_order_insured t2
        ON t1.fhOrderId = t2.fhOrderId
        LEFT JOIN auth_user t3
        ON t3.userId = t1.recommendId AND t3.enabled_flag = 0
        WHERE t3.regionName IS NOT NULL AND t2.appStatus='1'
        <![CDATA[ AND t1.submitTime >=  DATE_FORMAT(#{startDate}, '%Y-%m-%d') ]]>
        <![CDATA[ AND t1.submitTime <=  DATE_FORMAT(#{endDate}, '%Y-%m-%d') ]]>
        GROUP BY t3.regionName, DATE_FORMAT(t1.submitTime, '%m')
        ) t11
        GROUP BY t11.regionName
        ) t2
        ON t2.regionName=t0.orgName
        WHERE t0.orgType=1 AND t0.orgName IS NOT NULL
        <if test='regionName != null'>
            AND t0.orgName = #{regionName}
        </if>
    </select>

    <select id="listOrgOrderTargets" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmOrderTargetVO">
        SELECT t1.id,
        2 AS type,
        t3.orgName AS regionName,
        t0.orgName AS organizationName,
        t1.year AS year,
        t1.yearPersonTarget,
        t1.yearAmountTarget,
        t1.month_amount_01 AS monthAmountTarget01,
        t1.month_amount_02 AS monthAmountTarget02,
        t1.month_amount_03 AS monthAmountTarget03,
        t1.month_amount_04 AS monthAmountTarget04,
        t1.month_amount_05 AS monthAmountTarget05,
        t1.month_amount_06 AS monthAmountTarget06,
        t1.month_amount_07 AS monthAmountTarget07,
        t1.month_amount_08 AS monthAmountTarget08,
        t1.month_amount_09 AS monthAmountTarget09,
        t1.month_amount_10 AS monthAmountTarget10,
        t1.month_amount_11 AS monthAmountTarget11,
        t1.month_amount_12 AS monthAmountTarget12,
        t1.month_person_01 AS monthPersonActual01,
        t1.month_person_02 AS monthPersonActual02,
        t1.month_person_03 AS monthPersonActual03,
        t1.month_person_04 AS monthPersonActual04,
        t1.month_person_05 AS monthPersonActual05,
        t1.month_person_06 AS monthPersonActual06,
        t1.month_person_07 AS monthPersonActual07,
        t1.month_person_08 AS monthPersonActual08,
        t1.month_person_09 AS monthPersonActual09,
        t1.month_person_10 AS monthPersonActual10,
        t1.month_person_11 AS monthPersonActual11,
        t1.month_person_12 AS monthPersonActual12,
        t2.monthAmountActual01,
        t2.monthAmountActual02,
        t2.monthAmountActual03,
        t2.monthAmountActual04,
        t2.monthAmountActual05,
        t2.monthAmountActual06,
        t2.monthAmountActual07,
        t2.monthAmountActual08,
        t2.monthAmountActual09,
        t2.monthAmountActual10,
        t2.monthAmountActual11,
        t2.monthAmountActual12,
        t2.monthPersonActual01,
        t2.monthPersonActual02,
        t2.monthPersonActual03,
        t2.monthPersonActual04,
        t2.monthPersonActual05,
        t2.monthPersonActual06,
        t2.monthPersonActual07,
        t2.monthPersonActual08,
        t2.monthPersonActual09,
        t2.monthPersonActual10,
        t2.monthPersonActual11,
        t2.monthPersonActual12,
        t1.create_by,
        t1.create_time,
        t1.update_by,
        t1.update_time
        FROM organization t0
        LEFT JOIN organization t3 ON t3.hrOrgId = t0.hrParentId
        LEFT JOIN sm_sales_target t1
        ON t1.regionName = t0.orgName AND t1.type = 2
        LEFT JOIN
        (
        SELECT
        t11.organizationName,
        MAX(CASE ym WHEN '01' THEN qty ELSE 0 END ) AS monthPersonActual01,
        MAX(CASE ym WHEN '01' THEN amount ELSE 0 END ) AS monthAmountActual01,
        MAX(CASE ym WHEN '02' THEN qty ELSE 0 END ) AS monthPersonActual02,
        MAX(CASE ym WHEN '02' THEN amount ELSE 0 END ) AS monthAmountActual02,
        MAX(CASE ym WHEN '03' THEN qty ELSE 0 END ) AS monthPersonActual03,
        MAX(CASE ym WHEN '03' THEN amount ELSE 0 END ) AS monthAmountActual03,
        MAX(CASE ym WHEN '04' THEN qty ELSE 0 END ) AS monthPersonActual04,
        MAX(CASE ym WHEN '04' THEN amount ELSE 0 END ) AS monthAmountActual04,
        MAX(CASE ym WHEN '05' THEN qty ELSE 0 END ) AS monthPersonActual05,
        MAX(CASE ym WHEN '05' THEN amount ELSE 0 END ) AS monthAmountActual05,
        MAX(CASE ym WHEN '06' THEN qty ELSE 0 END ) AS monthPersonActual06,
        MAX(CASE ym WHEN '06' THEN amount ELSE 0 END ) AS monthAmountActual06,
        MAX(CASE ym WHEN '07' THEN qty ELSE 0 END ) AS monthPersonActual07,
        MAX(CASE ym WHEN '07' THEN amount ELSE 0 END ) AS monthAmountActual07,
        MAX(CASE ym WHEN '08' THEN qty ELSE 0 END ) AS monthPersonActual08,
        MAX(CASE ym WHEN '08' THEN amount ELSE 0 END ) AS monthAmountActual08,
        MAX(CASE ym WHEN '09' THEN qty ELSE 0 END ) AS monthPersonActual09,
        MAX(CASE ym WHEN '09' THEN amount ELSE 0 END ) AS monthAmountActual09,
        MAX(CASE ym WHEN '10' THEN qty ELSE 0 END ) AS monthPersonActual10,
        MAX(CASE ym WHEN '10' THEN amount ELSE 0 END ) AS monthAmountActual10,
        MAX(CASE ym WHEN '11' THEN qty ELSE 0 END ) AS monthPersonActual11,
        MAX(CASE ym WHEN '11' THEN amount ELSE 0 END ) AS monthAmountActual11,
        MAX(CASE ym WHEN '12' THEN qty ELSE 0 END ) AS monthPersonActual12,
        MAX(CASE ym WHEN '12' THEN amount ELSE 0 END ) AS monthAmountActual12
        FROM
        (
        SELECT
        t3.organizationName,
        DATE_FORMAT(t1.submitTime, '%m') AS ym,
        COUNT(t1.fhOrderId) AS qty,
        SUM(t1.unitPrice*t1.qty) AS amount
        FROM sm_order t1
        LEFT JOIN sm_order_insured t2
        ON t1.fhOrderId = t2.fhOrderId
        LEFT JOIN auth_user t3
        ON t3.userId = t1.recommendId AND t3.enabled_flag = 0
        WHERE t3.regionName IS NOT NULL AND t2.appStatus='1'
        <![CDATA[ AND t1.submitTime >=  DATE_FORMAT(#{startDate}, '%Y-%m-%d') ]]>
        <![CDATA[ AND t1.submitTime <=  DATE_FORMAT(#{endDate}, '%Y-%m-%d') ]]>
        GROUP BY t3.organizationName, DATE_FORMAT(t1.submitTime, '%m')
        ) t11
        GROUP BY t11.organizationName
        ) t2
        ON t2.organizationName=t0.orgName
        WHERE t0.orgType=2 AND t0.orgName IS NOT NULL
        <if test='regionName != null'>
            AND t3.orgName = #{regionName}
        </if>
        <if test='orgName != null'>
            AND t0.orgName = #{orgName}
        </if>
    </select>

    <select id="listEmployeeOrderTargets" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmOrderTargetVO">
        SELECT t1.id,
        3 AS type,
        t0.regionName AS regionName,
        t0.organizationName AS organizationName,
        t0.userId AS userId,
        t0.userName AS userName,
        t1.year AS year,
        t1.yearPersonTarget,
        t1.yearAmountTarget,
        t1.month_amount_01 AS monthAmountTarget01,
        t1.month_amount_02 AS monthAmountTarget02,
        t1.month_amount_03 AS monthAmountTarget03,
        t1.month_amount_04 AS monthAmountTarget04,
        t1.month_amount_05 AS monthAmountTarget05,
        t1.month_amount_06 AS monthAmountTarget06,
        t1.month_amount_07 AS monthAmountTarget07,
        t1.month_amount_08 AS monthAmountTarget08,
        t1.month_amount_09 AS monthAmountTarget09,
        t1.month_amount_10 AS monthAmountTarget10,
        t1.month_amount_11 AS monthAmountTarget11,
        t1.month_amount_12 AS monthAmountTarget12,
        t1.month_person_01 AS monthPersonActual01,
        t1.month_person_02 AS monthPersonActual02,
        t1.month_person_03 AS monthPersonActual03,
        t1.month_person_04 AS monthPersonActual04,
        t1.month_person_05 AS monthPersonActual05,
        t1.month_person_06 AS monthPersonActual06,
        t1.month_person_07 AS monthPersonActual07,
        t1.month_person_08 AS monthPersonActual08,
        t1.month_person_09 AS monthPersonActual09,
        t1.month_person_10 AS monthPersonActual10,
        t1.month_person_11 AS monthPersonActual11,
        t1.month_person_12 AS monthPersonActual12,
        t2.monthAmountActual01,
        t2.monthAmountActual02,
        t2.monthAmountActual03,
        t2.monthAmountActual04,
        t2.monthAmountActual05,
        t2.monthAmountActual06,
        t2.monthAmountActual07,
        t2.monthAmountActual08,
        t2.monthAmountActual09,
        t2.monthAmountActual10,
        t2.monthAmountActual11,
        t2.monthAmountActual12,
        t2.monthPersonActual01,
        t2.monthPersonActual02,
        t2.monthPersonActual03,
        t2.monthPersonActual04,
        t2.monthPersonActual05,
        t2.monthPersonActual06,
        t2.monthPersonActual07,
        t2.monthPersonActual08,
        t2.monthPersonActual09,
        t2.monthPersonActual10,
        t2.monthPersonActual11,
        t2.monthPersonActual12,
        t1.create_by,
        t1.create_time,
        t1.update_by,
        t1.update_time
        FROM auth_user t0
        LEFT JOIN organization t3
        ON t3.orgName = t0.organizationName
        LEFT JOIN sm_sales_target t1
        ON t1.userId = t0.userId AND t1.type = 3
        LEFT JOIN
        (
        SELECT
        t11.userId,
        MAX(CASE ym WHEN '01' THEN qty ELSE 0 END ) AS monthPersonActual01,
        MAX(CASE ym WHEN '01' THEN amount ELSE 0 END ) AS monthAmountActual01,
        MAX(CASE ym WHEN '02' THEN qty ELSE 0 END ) AS monthPersonActual02,
        MAX(CASE ym WHEN '02' THEN amount ELSE 0 END ) AS monthAmountActual02,
        MAX(CASE ym WHEN '03' THEN qty ELSE 0 END ) AS monthPersonActual03,
        MAX(CASE ym WHEN '03' THEN amount ELSE 0 END ) AS monthAmountActual03,
        MAX(CASE ym WHEN '04' THEN qty ELSE 0 END ) AS monthPersonActual04,
        MAX(CASE ym WHEN '04' THEN amount ELSE 0 END ) AS monthAmountActual04,
        MAX(CASE ym WHEN '05' THEN qty ELSE 0 END ) AS monthPersonActual05,
        MAX(CASE ym WHEN '05' THEN amount ELSE 0 END ) AS monthAmountActual05,
        MAX(CASE ym WHEN '06' THEN qty ELSE 0 END ) AS monthPersonActual06,
        MAX(CASE ym WHEN '06' THEN amount ELSE 0 END ) AS monthAmountActual06,
        MAX(CASE ym WHEN '07' THEN qty ELSE 0 END ) AS monthPersonActual07,
        MAX(CASE ym WHEN '07' THEN amount ELSE 0 END ) AS monthAmountActual07,
        MAX(CASE ym WHEN '08' THEN qty ELSE 0 END ) AS monthPersonActual08,
        MAX(CASE ym WHEN '08' THEN amount ELSE 0 END ) AS monthAmountActual08,
        MAX(CASE ym WHEN '09' THEN qty ELSE 0 END ) AS monthPersonActual09,
        MAX(CASE ym WHEN '09' THEN amount ELSE 0 END ) AS monthAmountActual09,
        MAX(CASE ym WHEN '10' THEN qty ELSE 0 END ) AS monthPersonActual10,
        MAX(CASE ym WHEN '10' THEN amount ELSE 0 END ) AS monthAmountActual10,
        MAX(CASE ym WHEN '11' THEN qty ELSE 0 END ) AS monthPersonActual11,
        MAX(CASE ym WHEN '11' THEN amount ELSE 0 END ) AS monthAmountActual11,
        MAX(CASE ym WHEN '12' THEN qty ELSE 0 END ) AS monthPersonActual12,
        MAX(CASE ym WHEN '12' THEN amount ELSE 0 END ) AS monthAmountActual12
        FROM
        (
        SELECT
        t3.userId,
        DATE_FORMAT(t1.submitTime, '%m') AS ym,
        COUNT(t1.fhOrderId) AS qty,
        SUM(t1.unitPrice*t1.qty) AS amount
        FROM sm_order t1
        LEFT JOIN sm_order_insured t2
        ON t1.fhOrderId = t2.fhOrderId
        LEFT JOIN auth_user t3
        ON t3.userId = t1.recommendId AND t3.enabled_flag = 0
        WHERE t3.regionName IS NOT NULL AND t2.appStatus='1'
        <![CDATA[ AND t1.submitTime >=  DATE_FORMAT(#{startDate}, '%Y-%m-%d')  ]]>
        <![CDATA[ AND t1.submitTime <=  DATE_FORMAT(#{endDate}, '%Y-%m-%d')  ]]>
        GROUP BY t3.userId, DATE_FORMAT(t1.submitTime, '%m')
        ) t11
        GROUP BY t11.userId
        ) t2
        ON t2.userId=t0.userId
        WHERE t0.userType='employee' AND t0.enabled_flag = 0 AND t0.userId IS NOT NULL
        <if test='regionName != null'>
            AND t0.regionName = #{regionName}
        </if>
        <if test='orgName != null'>
            AND t0.organizationName = #{orgName}
        </if>
        ORDER BY t3.id ASC, t0.organizationName ASC
    </select>

    <insert id="insertOrderTarget" parameterType="com.cfpamf.ms.insur.admin.pojo.vo.ProgressVO"
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO sm_sales_target
        (type, regionName, organizationName, userId, year, yearPersonTarget, yearAmountTarget, month_amount_01,
        month_amount_02, month_amount_03, month_amount_04, month_amount_05, month_amount_06, month_amount_07,
        month_amount_08, month_amount_09, month_amount_10, month_amount_11, month_amount_12, month_person_01,
        month_person_02, month_person_03, month_person_04, month_person_05, month_person_06, month_person_07,
        month_person_08, month_person_09, month_person_10, month_person_11, month_person_12, create_by, create_date)
        VALUES
        (#{type}, #{regionName}, #{organizationName}, #{userId}, #{year}, #{yearPersonTarget}, #{yearAmountTarget},
        #{monthAmountTarget01}, #{monthAmountTarget02}, #{monthAmountTarget03}, #{monthAmountTarget04},
        #{monthAmountTarget05}, #{monthAmountTarget06}, #{monthAmountTarget07}, #{monthAmountTarget08},
        #{monthAmountTarget09}, #{monthAmountTarget10}, #{monthAmountTarget11}, #{monthAmountTarget12},
        #{monthPersonTarget01}, #{monthPersonTarget02}, #{monthPersonTarget03}, #{monthPersonTarget04},
        #{monthPersonTarget05}, #{monthPersonTarget06}, #{monthPersonTarget07}, #{monthPersonTarget08},
        #{monthPersonTarget09}, #{monthPersonTarget10}, #{monthPersonTarget11}, #{monthPersonTarget12}, #{createBy},
        CURRENT_TIMESTAMP())
    </insert>


    <update id="updateOrderTarget" parameterType="com.cfpamf.ms.insur.admin.pojo.dto.SmOrderTargetDTO">
        UPDATE sm_sales_target SET yearPersonTarget=#{yearPersonTarget},
        yearAmountTarget=#{yearAmountTarget},
        month_amount_01=#{monthAmountTarget01},
        month_amount_02=#{monthAmountTarget02},
        month_amount_03=#{monthAmountTarget03},
        month_amount_04=#{monthAmountTarget04},
        month_amount_05=#{monthAmountTarget05},
        month_amount_06=#{monthAmountTarget06},
        month_amount_07=#{monthAmountTarget07},
        month_amount_08=#{monthAmountTarget08},
        month_amount_09=#{monthAmountTarget09},
        month_amount_10=#{monthAmountTarget10},
        month_amount_11=#{monthAmountTarget11},
        month_amount_12=#{monthAmountTarget12},
        month_person_01=#{monthPersonTarget01},
        month_person_02=#{monthPersonTarget02},
        month_person_03=#{monthPersonTarget03},
        month_person_04=#{monthPersonTarget04},
        month_person_05=#{monthPersonTarget05},
        month_person_06=#{monthPersonTarget06},
        month_person_07=#{monthPersonTarget07},
        month_person_08=#{monthPersonTarget08},
        month_person_09=#{monthPersonTarget09},
        month_person_10=#{monthPersonTarget10},
        month_person_11=#{monthPersonTarget11},
        month_person_12=#{monthPersonTarget12},
        update_by=#{modifyBy},
        update_time=CURRENT_TIMESTAMP()
        WHERE id = #{id}
    </update>

</mapper>