<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.reconciliation.SmReconciliationPolicyMapper">
    <resultMap id="BaseResultMap" type="com.cfpamf.ms.insur.admin.pojo.po.reconciliation.SmReconciliationPolicy">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="reconciliation_id" jdbcType="INTEGER" property="reconciliationId"/>
        <result column="policy_no" jdbcType="VARCHAR" property="policyNo"/>
        <result column="app_status" jdbcType="TINYINT" property="appStatus"/>
        <result column="reconciliation_status" jdbcType="TINYINT" property="reconciliationStatus"/>
        <result column="enabled_flag" jdbcType="TINYINT" property="enabledFlag"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , reconciliation_id, policy_no, app_status, reconciliation_status, enabled_flag,
    create_by, update_by, create_time, update_time
    </sql>

    <update id="updateBalanceAccountFlag">
        update sm_reconciliation_policy
        set balance_account_flag = 1
        where reconciliation_id = #{reconciliationId}
          and policy_no = #{policyNo}
          and app_status = #{appStatus}
    </update>
    <update id="updateBalanceAccountFlagByPolicyKeyList">
        update sm_reconciliation_policy
        set balance_account_flag = 1
        where reconciliation_id = #{reconciliationId}
        and CONCAT(policy_no ,'_',app_status ) in
        <foreach collection="policyKeySet" index="index" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </update>

    <update id="batchUpdatePolicyState">
        update sm_reconciliation_policy t1
        left JOIN sm_order_insured t2
        on t1.policy_no = t2.policyNo
        set t1.app_status = t2.appStatus
        WHERE t1.reconciliation_id = #{reconciliationId}
        and t1.policy_no in
        <foreach collection="policyList" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <select id="getSmReconciliationPolicy"
            resultType="com.cfpamf.ms.insur.admin.pojo.dto.reconciliation.ReconciledPolicyDTO">
        select
        t1.policy_no as policyNo,
        t1.app_status as appStatus,
        t1.reconciliation_status as reconciliationStatus,
        t1.balance_account_flag as balanceAccountFlag,
        t2.import_time as reconciliationTime
        from sm_reconciliation_policy t1
        left join sm_reconciliation t2 on t1.reconciliation_id = t2.id and t2.enabled_flag = 0
        where 1=1
        <if test="startTime != null">
            and t2.import_time >= #{startTime}
        </if>
        <if test="endTime != null">
            <![CDATA[ and  t2.import_time <= #{endTime} ]]>
        </if>
        <if test="channel != null and channel != ''">
            and t2.channel = #{channel}
        </if>
        and t1.policy_no in
        <foreach collection="policyList" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="getNewestSmReconciliationPolicyByPolicyList"
            resultType="com.cfpamf.ms.insur.admin.pojo.dto.reconciliation.ReconciledPolicyDTO">
        SELECT t1.policy_no             as policyNo,
               t1.app_status            as appStatus,
               t1.reconciliation_status as reconciliationStatus,
               t1.balance_account_flag  as balanceAccountFlag
        from sm_reconciliation_policy t1,
             (
                 SELECT MAX(id) as id FROM `sm_reconciliation_policy` GROUP BY policy_no, app_status
             ) t2
        WHERE t1.id = t2.id
        <if test="policyList != null">
            and t1.policy_no in
            <foreach collection="policyList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="policyList == null">
            and 1!=1
        </if>
    </select>


</mapper>