<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.whale.WhaleDataRevisionMapper">


    <select id="qryPolicyProductType" resultType="java.lang.String">
        SELECT
            distinct c.`productAttrCode`
        FROM
            sm_order_insured a
                LEFT JOIN sm_order b ON a.fhOrderId = b.fhOrderId
                LEFT JOIN sm_product c ON b.productId=c.id
        WHERE a.policyNo=#{policyNo}
    </select>

    <select id="qryRiskStatusCorrection" resultType="com.cfpamf.ms.insur.admin.pojo.po.SmOrderInsured">
        select
            b.`fhOrderId` ,
            b.`appStatus`
        from
            `sm_order_insured` b
        where
        `fhOrderId` in (
            select
                a.`fh_order_id`
            from
                sm_order_risk_duty a
            where
                a.`app_status` = "-2"
                and a.`create_time` between "2024-03-04 15:00:01" and "2025-03-04 15:00:01"
                group by
                a.`fh_order_id`)
        group by b.fhOrderId,b.appStatus
    </select>

    <update id="changePolicyCode">
        update sm_order_insured  set policyNo=#{newPolicyCode},update_time=now()  where policyNo=#{policyCode};
        update sm_order_policy  set policy_no=#{newPolicyCode},update_time=now()  where policy_no=#{policyCode};
        update sm_order_risk_duty set policy_no=#{newPolicyCode},update_time=now()  where policy_no=#{policyCode};
    </update>

    <update id="changePolicyCodeGroup">
        update sm_order_insured set policyNo=#{newPolicyCode},update_time=now()  where policyNo=#{policyCode};
        update sm_order_item set th_policy_no=#{newPolicyCode},policy_no=#{newPolicyCode},update_time=now()  where th_policy_no=#{policyCode};
        update sm_order_policy set policy_no=#{newPolicyCode},update_time=now()  where policy_no=#{policyCode};
        update sm_order_risk_duty set policy_no=#{newPolicyCode},update_time=now()  where policy_no=#{policyCode};
    </update>

    <update id="changeEndorsementNo">
        update sm_order_insured set `policyNo` = REPLACE(policyNo,#{endorsementNo},#{newEndorsementNo}) where `policyNo` like concat(#{endorsementNo},'%');
        update sm_order_item set `th_endorsement_no` =#{newEndorsementNo},`policy_no` = REPLACE(policy_no,#{endorsementNo},#{newEndorsementNo}) where `policy_no` like concat(#{endorsementNo},'%');
    </update>

    <update id="changeEndorsementNoSurrender">
        update sm_order_policy set surrender_no = #{newEndorsementNo} where surrender_no=#{endorsementNo} and policy_no=#{policyCode}
    </update>

    <update id="removePolicy">
        UPDATE sm_order SET enabled_flag='-1' WHERE fhOrderId LIKE concat(#{fhOrderId},'_%') OR fhOrderId = #{fhOrderId};
        update `sm_order_insured` set `enabled_flag` =-1, `update_time` =now() where `policyNo` = #{policyCode};
        update `sm_order_item` set `enabled_flag` =-1, `update_time` =now() where `th_policy_no` = #{policyCode};
        update `sm_order_policy` set `enabled_flag` =-1, `update_time` =now() where `policy_no` = #{policyCode};
        update `sm_order_risk_duty` set `enabled_flag` =-1, `update_time` =now() where `policy_no` = #{policyCode};
        UPDATE sm_order_renewal_term SET enabled_flag='-1'  WHERE order_id = #{fhOrderId};
        UPDATE sm_order_renewal_term_risk SET enabled_flag='-1'  WHERE order_id = #{fhOrderId};
    </update>

    <update id="removePolicyPhysics">
        delete from  sm_order where fhOrderId LIKE concat(#{fhOrderId},'_%') OR fhOrderId = #{fhOrderId};
        delete from  sm_order_applicant where fhOrderId = #{fhOrderId};
        delete from  sm_order_insured where fhOrderId LIKE concat(#{fhOrderId},'_%') OR fhOrderId = #{fhOrderId};
        delete from  sm_order_item where `th_policy_no` = #{policyCode};
        delete from  sm_order_policy where `policy_no` = #{policyCode};
        delete from  sm_order_risk_duty where `policy_no` = #{policyCode};
        delete from  sm_order_renewal_term where order_id = #{fhOrderId};
        delete from  sm_order_renewal_term_risk where order_id = #{fhOrderId};
        delete from sm_order_extend_whale where fh_order_id =#{fhOrderId};
        delete from sm_order_car where fh_order_id =#{fhOrderId};
        delete from auto_order where order_no=#{fhOrderId};
        delete from auto_order_policy where order_no=#{fhOrderId};
        delete from auto_order_car where order_no=#{fhOrderId};
        delete from auto_order_person where order_no=#{fhOrderId};
        delete from auto_order_risk where order_no=#{fhOrderId};
    </update>

    <update id="removeEndorsementNo">
        update sm_order_insured set enabled_flag=-1 where `policyNo` like concat(#{endorsementNo},'%');
        update sm_order_item set enabled_flag=-1 where `th_policy_no` = #{endorsementNo};
    </update>

    <update id="removeEndorsementNo2">
        UPDATE sm_order SET enabled_flag='-1' WHERE fhOrderId IN
        <foreach collection="fhOrderIds" item="fhOrderId" separator="," open="(" close=")">
            #{fhOrderId}
        </foreach>;
    </update>

    <update id="changePolicyPremium">
        update sm_order set `totalAmount` =#{premium}, `unitPrice` =#{premium}, `update_time` =now() where `fhOrderId` =#{fhOrderId} and totalAmount=#{sourcePremium};
        update sm_order_policy set `premium` =#{premium},`update_time` =now() where `fh_order_id` =#{fhOrderId} and premium=#{sourcePremium};
    </update>

    <update id="changePolicyPremiumGroup">
        <foreach collection="insuredList" index="index" item="insured">
            update sm_order_item set `total_amount`  =#{insured.premium}, `unit_price`  =#{insured.premium}, `update_time` =now() where `fh_order_id` =#{fhOrderId} and id_number=#{insured.insuredIdCard};
        </foreach>
    </update>

    <update id="changeEndorsementPremium">
        <foreach collection="insuredList" index="index" item="insured">
            update sm_order_item set `total_amount`  =#{insured.premium}, `unit_price`  =#{insured.premium}, `update_time` =now() where th_endorsement_no =#{endorsementNo} and id_number=#{insured.insuredIdCard};
        </foreach>
    </update>

    <update id="changeEndorsementPremiumTwo">
        <foreach collection="dataRevise" index="index" item="item">
            update sm_order set `totalAmount` =#{item.totalAmount}, `unitPrice` =#{item.unitPrice}, `update_time` =now() where `fhOrderId` =#{item.fhOrderId};
        </foreach>
    </update>

    <update id="changeRenewalRealityTime">
        update sm_order_renewal_term set `payment_time` =#{paymentTime}, `update_time` =now() where `policy_no` =#{policyCode} and `term_num` =#{period};
    </update>

    <update id="changeRenewalRealityPremium">
        update sm_order_renewal_term set `renewal_amount` =#{premium}, `update_time` =now() where `policy_no` =#{policyCode} and `term_num` =#{period} ;
        <foreach collection="insuredList" index="index" item="insured">
            <foreach collection="insured.insuredProduct" index="index" item="product">
                update sm_order_renewal_term_risk set `premium` =#{product.premium}, `update_time` =now() where `order_id` =#{fhOrderId} and `term_num` =#{period} and risk_code=#{product.productCode} and insured_id_number =#{insured.insuredIdCard};
            </foreach>
        </foreach>
    </update>

    <update id="changeRenewalFallback">
        update sm_order_renewal_term set `renewal_amount` = null,payment_time=null,renewal_status=0,renewal_success_sync_date=null, `update_time` =now() where `policy_no` =#{policyCode} and `term_num` =#{period} ;
        update sm_order_renewal_term_risk set `premium` =null, `update_time` =now() where `order_id` =#{fhOrderId} and `term_num` =#{period} ;
    </update>

    <update id="changePolicyReferrer">
        update sm_order set
            customerAdminId=#{newRecommendId},
            recommendId=#{newRecommendId},
            recommendMasterName=#{newRecommendMasterName},
            recommendAdminName=#{newRecommendAdminName},
            recommendEntryDate=#{newEntryDate},
            recommendPostName=#{newRecommendPostName},
            recommendJobCode=#{newRecommendJobCode},
            recommendMainJobNumber=#{newRecommendMainJobNumber},
            customerAdminJobCode=#{newRecommendJobCode},
            customerAdminMainJobNumber=#{newRecommendMainJobNumber},
            recommendOrgCode=#{newRecommendOrgCode},
            customerAdminOrgCode=#{newRecommendOrgCode},
            update_time =now()
        where
        fhOrderId=#{fhOrderId}
    </update>

    <select id="qrySmOrderFailureTimeUpVo"
            resultType="com.cfpamf.ms.insur.admin.pojo.vo.order.SmOrderFailureTimeUpVo">
        SELECT
            a.fhOrderId as fhOrderId,
            a.endTime as endTime
        FROM
            `sm_order` a left join sm_order_insured b on a.fhOrderId =b.fhOrderId
        WHERE
            a.`fhOrderId` LIKE "XJ%"
            and a.`endTime` like '%00:00:00'
            and b.appStatus  not in ('4')
            group by
                fhOrderId
            ORDER BY
                a.`create_time` DESC limit #{lim}
    </select>

    <update id="upSmOrderFailureTimeUpVo">
        <foreach collection="upVos" index="index" item="upVo">
            update sm_order set `endTime` = #{upVo.endTime}, `update_time` = #{upVo.updateTime} where `fhOrderId` = #{upVo.fhOrderId};
        </foreach>
    </update>

    <update id="removeEndorsementNo3">
        update sm_order set enabled_flag=-1 where `fhOrderId` = #{fhOrderId};
        update sm_order_insured set enabled_flag=-1 where `fhOrderId`=#{fhOrderId};
        update sm_order_item set enabled_flag=-1 where `fh_order_id` = #{fhOrderId};
    </update>
</mapper>