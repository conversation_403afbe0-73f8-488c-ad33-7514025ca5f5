<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.SmOrderImportMapper">

    <select id="queryImportList" resultType="com.cfpamf.ms.insur.admin.pojo.po.order.impor.SmOrderImport">
        select * from sm_order_import
        where 1=1
        <if test="startTime!=null">
            and create_time>=#{startTime}
        </if>
        <if test="endTime!=null">
            and create_time &lt; #{endTime}
        </if>
        <if test="type==1">
            and type =1
        </if>
        <if test="type!=1">
            and (type = 0 or type is null)
        </if>
        order by id desc
    </select>

</mapper>