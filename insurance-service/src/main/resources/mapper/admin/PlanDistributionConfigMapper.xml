<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.admin.distribution.config.dao.PlanDistributionConfigMapper">

    <sql id="Base_Column_List">
        id
        ,plan_id, mapper_name, enabled_flag, create_by, update_by, create_time, update_time
    </sql>


    <select id="getProductDistributionConfigItemByProductId"
            resultType="com.cfpamf.ms.insur.admin.distribution.config.vo.ProductDistributionConfigItemVo">
        select spdc.id,
               sp.id as planId,
               sp.planName,
               spdc.mapper_name,
               sp.productId
        from sm_plan sp
                 left join sm_plan_distribution_config spdc
                           on sp.id = spdc.plan_id and spdc.enabled_flag = 0
        where sp.productId = #{productId}
          and sp.enabled_flag = 0
    </select>


    <select id="getByProductId"
            resultType="com.cfpamf.ms.insur.admin.distribution.config.entity.PlanDistributionConfig">
        select
        <include refid="Base_Column_List"/>
        from sm_plan_distribution_config
        where enabled_flag = 0 and plan_id in (
        select id
        from sm_plan
        where productId = #{productId}
        )
    </select>
    <select id="getByProductIdList"
            resultType="com.cfpamf.ms.insur.admin.distribution.config.vo.ProductDistributionConfigItemVo">
        select spdc.id,
        sp.id as planId,
        sp.planName,
        spdc.mapper_name,
        sp.productId
        from sm_plan sp
        left join sm_plan_distribution_config spdc
        on sp.id = spdc.plan_id and spdc.enabled_flag = 0
        where sp.enabled_flag = 0
        and sp.productId in
        <foreach item="item" index="index" open="(" separator="," close=")" collection="productIdList">
            #{item}
        </foreach>
    </select>
    <select id="search" resultType="com.cfpamf.ms.insur.admin.distribution.config.vo.PlanDistributionConfigVo">
        select
        sp.planName,
        spdc.mapper_name as planMapperName,
        spdc.id,
        spdc.plan_id as planId,
        sp2.productName,
        spdc2.mapper_name as productMapperName
        from sm_plan_distribution_config spdc
        left join sm_plan sp
        on sp.id = spdc.plan_id and sp.enabled_flag = 0
        left join sm_product sp2
        on sp.productId = sp2.id and sp2.enabled_flag = 0
        left join sm_product_distribution_config spdc2
        on sp2.id = spdc2.product_id and spdc2.enabled_flag = 0
        where spdc.enabled_flag = 0
        <if test="productName != null and productName!= '' ">
            and sp2.productName like CONCAT('%',#{productName},'%')
        </if>
        <if test="planName != null and planName!= '' ">
            and sp.planName like CONCAT('%',#{planName},'%')
        </if>
        <if test="planId != null">
            and spdc.plan_id = #{planId}
        </if>
        order by spdc.update_time desc
    </select>

    <update id="batchUpdate">
        update sm_plan_distribution_config
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="mapper_name =case" suffix="end,">
                <foreach collection="planDistributionConfigList" item="item" index="index">
                    when id=#{item.id} then #{item.mapperName}
                </foreach>
            </trim>
            <trim prefix="update_by =case" suffix="end,">
                <foreach collection="planDistributionConfigList" item="item" index="index">
                    when id=#{item.id} then #{item.updateBy}
                </foreach>
            </trim>

        </trim>
        where id in
        <foreach collection="planDistributionConfigList" index="index" item="item" separator="," open="(" close=")">
            #{item.id}
        </foreach>
    </update>
    <update id="softDeleteByProductId">
        update sm_plan_distribution_config
        set enabled_flag = 1
        where plan_id in (
            select id
            from sm_plan
            where productId = #{productId}
        )
    </update>

</mapper>