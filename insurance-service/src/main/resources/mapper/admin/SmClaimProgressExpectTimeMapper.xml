<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.claim.SmClaimProgressExpectTimeMapper">

    <resultMap id="BaseResultMap" type="com.cfpamf.ms.insur.admin.pojo.po.claim.SmClaimProgressExpectTime">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="claimid" column="claimId" jdbcType="INTEGER"/>
            <result property="scode" column="sCode" jdbcType="VARCHAR"/>
            <result property="sname" column="sName" jdbcType="VARCHAR"/>
            <result property="expectWaitDay" column="expect_wait_day" jdbcType="INTEGER"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="enabledFlag" column="enabled_flag" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,claimId,sCode,
        sName,expect_wait_day,create_time,
        create_by,update_time,update_by,
        enabled_flag
    </sql>
</mapper>
