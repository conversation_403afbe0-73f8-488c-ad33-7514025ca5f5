<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cfpamf.ms.insur.admin.convertedpremium.dao.ConvertedPremiumMapper">
    <update id="batchUpdateConvertedPremiumOrderAmount">
        update sm_order_converted_premium
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="order_amount =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when id=#{item.id} then #{item.orderAmount}
                </foreach>
            </trim>

            <trim prefix="converted_premium =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when id=#{item.id} then #{item.convertedPremium}
                </foreach>
            </trim>

            <trim prefix="converted_premium_config_id =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when id=#{item.id} then #{item.convertedPremiumConfigId}
                </foreach>
            </trim>

            <trim prefix="proportion =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when id=#{item.id} then #{item.proportion}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item.id}
        </foreach>
    </update>
    <!--    通过订单id查找折算保费start-->
    <select id="findByFhOrderId"
            resultType="com.cfpamf.ms.insur.admin.convertedpremium.entity.OrderConvertedPremium">
        select *
        from sm_order_converted_premium
        where enabled_flag = '0' and fh_order_id = #{fhOrderId}
    </select>
    <!--    通过订单id查找折算保费end-->

    <update id="updateOrderAmount">
        UPDATE  sm_order_converted_premium
        set order_amount = #{cancelAmount},
            converted_premium =  #{cancelAmount} * (case when #{appStatus} = '4' THEN -1 ELSE 1 END) * proportion /100
        where policy_no=#{policyNo} and app_status =#{appStatus}
    </update>
</mapper>