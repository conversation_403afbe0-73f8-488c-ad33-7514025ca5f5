<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.claim.SmClaimHospitalMapper">

    <update id="softDelete">
        UPDATE sm_claim_hospital
        SET enabled_flag = 1
    </update>
    <select id="listProvince" resultType="java.lang.String">
        SELECT DISTINCT (province)
        FROM sm_claim_hospital
        where enabled_flag = 0
    </select>

    <select id="listCity" resultType="java.lang.String">
        SELECT DISTINCT (city)
        FROM sm_claim_hospital
        where enabled_flag = 0
          AND province = #{province}
    </select>
    <select id="listHospital" resultType="com.cfpamf.ms.insur.admin.pojo.po.claim.SmClaimHospital">
        SELECT * FROM sm_claim_hospital
        <where>
            enabled_flag = 0
            <if test="province != null and province != ''">
                AND province = #{province}
            </if>
            <if test="city != null and city != ''">
                AND city = #{city}
            </if>
            <if test="fuzzyKey != null and fuzzyKey != ''">
                AND hospital_name LIKE concat('%', #{fuzzyKey}, '%')
            </if>
        </where>
    </select>
</mapper>