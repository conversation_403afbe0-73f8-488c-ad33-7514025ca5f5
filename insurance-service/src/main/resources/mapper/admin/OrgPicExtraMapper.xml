<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.OrgPicExtraMapper">

    <insert id="insertOrgPicExtra" useGeneratedKeys="true">
        INSERT INTO org_pic_extra (regionHrId, regionName, orgHrId, orgName, userId, userName, userJobPost,jobCode,orgCode,mainJobNumber)
        VALUES (#{regionHrId}, #{regionName}, #{orgHrId}, #{orgName}, #{userId}, #{userName}, #{userJobPost},#{jobCode},#{orgCode},#{mainJobNumber})
    </insert>

    <select id="listOrgPicExtra" resultType="com.cfpamf.ms.insur.admin.pojo.vo.OrgPicExtraVO">
        SELECT * FROM org_pic_extra WHERE enabled_flag=0
        <if test='keyword != null'>
            AND (userId LIKE CONCAT('%',#{keyword},'%') OR userName LIKE CONCAT('%',#{keyword},'%')
            OR regionName LIKE CONCAT('%',#{keyword},'%') OR orgName LIKE CONCAT('%',#{keyword},'%') )
        </if>
        <if test='regionName  != null'>
            AND regionName=#{regionName}
        </if>
        <if test='orgName  != null'>
            AND orgName=#{orgName}
        </if>
    </select>

    <select id="getOrgPicExtraByUserId" resultType="com.cfpamf.ms.insur.admin.pojo.vo.OrgPicExtraVO">
        SELECT *
        FROM org_pic_extra
        WHERE enabled_flag = 0
          AND userId = #{userId}
        LIMIT 1
    </select>
    <select id="getOrgPicExtraByUserIdAndOrg" resultType="com.cfpamf.ms.insur.admin.pojo.vo.OrgPicExtraVO">
        SELECT *
        FROM org_pic_extra
        WHERE enabled_flag = 0
          AND mainJobNumber = #{userId}
          AND orgName = #{orgName}
        LIMIT 1
    </select>

    <select id="getOrgPicExtraByRegionAndOrg" resultType="com.cfpamf.ms.insur.admin.pojo.vo.OrgPicExtraVO">
        SELECT *
        FROM org_pic_extra
        WHERE enabled_flag = 0
          AND regionName = #{regionName}
          AND orgName = #{orgName}
        LIMIT 1
    </select>
    <select id="listByCustomerUserId" resultType="com.cfpamf.ms.insur.admin.pojo.vo.OrgPicExtraVO">
        select ope.*
        from auth_user au
                 inner join
             org_pic_extra ope on au.regionName = ope.regionName
                 and au.organizationName = ope.orgName and ope.enabled_flag = 0
        where au.userId = #{customerUserId}
    </select>
    <select id="listOrgPicUserInfoByAdminUserId" resultType="com.cfpamf.ms.insur.admin.pojo.vo.AuthUserVO">
        select oau.*
        from auth_user au
                 inner join
             org_pic_extra ope on au.regionName = ope.regionName
                 and au.organizationName = ope.orgName and ope.enabled_flag = 0
             inner join auth_user oau on oau.userId = ope.userId and ope.enabled_flag = 0
        where au.userId = #{userId}
    </select>

    <update id="deleteOrgPicExtraByOrgId">
        UPDATE org_pic_extra
        SET enabled_flag=1
        WHERE regionHrId = #{regionHrId}
          AND orgHrId = #{orgHrId}
    </update>

    <update id="deleteOrgPicExtraById">
        UPDATE org_pic_extra
        SET enabled_flag=1
        WHERE id = #{id}
    </update>

    <update id="updateOrgPicExtra">
        UPDATE org_pic_extra
        SET regionHrId = #{regionHrId},
            regionName = #{regionName},
            orgHrId    = #{orgHrId},
            orgName    = #{orgName},
            userId=#{userId},
            userName=#{userName},
            userJobPost=#{userJobPost},
            jobCode=#{jobCode},
            orgCode=#{orgCode},
            mainJobNumber=#{mainJobNumber}

        WHERE id = #{id}
    </update>
</mapper>
