<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.claim.SmClaimPushMonthMapper">

    <resultMap id="BaseResultMap" type="com.cfpamf.ms.insur.admin.pojo.po.claim.SmClaimPushMonth">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="regionName" column="region_name" jdbcType="VARCHAR"/>
            <result property="organizationName" column="organization_name" jdbcType="VARCHAR"/>
            <result property="payMoney" column="pay_money" jdbcType="VARCHAR"/>
            <result property="payedNum" column="payed_num" jdbcType="VARCHAR"/>
            <result property="rejectNum" column="reject_num" jdbcType="VARCHAR"/>
            <result property="month" column="month" jdbcType="VARCHAR"/>
            <result property="sendTo" column="send_to" jdbcType="VARCHAR"/>
            <result property="enabledFlag" column="enabled_flag" jdbcType="TINYINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,region_name,organization_name,
        pay_money,payed_num,reject_num,
        month,send_to,enabled_flag,
        create_time,update_time
    </sql>
    <update id="logicDeleteByMonth">
        update sm_claim_push_month set enabled_flag = 1 where month = #{month} and service_type = #{serviceType}
    </update>

    <select id="claimSummaryOrgList" resultType="com.cfpamf.ms.insur.admin.pojo.po.claim.SmClaimPushMonth">

        SELECT t4.regionName,
               t4.organizationName,
               t4.regionCode ,
               t4.orgCode as organizationCode,
               sum(case finishState when 'payed' then 1 else 0 end) as payedNum,
               sum(case finishState when 'payRejected' then 1 else 0 end) as rejectNum,
               SUM(`payMoney`) as payMoney,
               left(t1.`finishTime`, 7) as month
        FROM `sm_claim` t1
                 LEFT JOIN `sm_order_insured` t2 on t1.`insuredId` = t2.`id` and t2.enabled_flag = 0
                 LEFT JOIN `sm_order` t3 on t3.`fhOrderId` = t2.`fhOrderId` and t3.enabled_flag = 0
                 LEFT JOIN `auth_user` t4 on t4.userId = t3.`customerAdminId` and t4.enabled_flag = 0
        WHERE t1.`claimState` = 'stepFinish'
          and `finishState` IN ('payed', 'payRejected')
          and t1.`enabled_flag` = 0
          and left(t1.`finishTime`, 7) = #{month}
        GROUP BY t4.regionName, t4.organizationName;

    </select>

    <select id="claimSummaryRegionList" resultType="com.cfpamf.ms.insur.admin.pojo.po.claim.SmClaimPushMonth">

        SELECT t4.regionName
             , t4.regionCode
             , sum(case finishState when 'payed' then 1 else 0 end) as payedNum
             , sum(case finishState when 'payRejected' then 1 else 0 end) as rejectNum
             , SUM(`payMoney`) as payMoney
             , left(t1.`finishTime`, 7) as month
        FROM `sm_claim` t1
                 LEFT JOIN `sm_order_insured` t2 on t1.`insuredId` = t2.`id` and t2.enabled_flag = 0
                 LEFT JOIN `sm_order` t3 on t3.`fhOrderId` = t2.`fhOrderId` and t3.enabled_flag = 0
                 LEFT JOIN `auth_user` t4 on t4.userId = t3.`customerAdminId` and t4.enabled_flag = 0
        WHERE t1.`claimState` = 'stepFinish'
          and `finishState` IN ('payed', 'payRejected')
          and t1.`enabled_flag` = 0
          and left(t1.`finishTime`, 7) = #{month}
        GROUP BY t4.regionName;

    </select>
    <select id="exportRegion" resultType="com.cfpamf.ms.insur.admin.claim.vo.ClaimExportPushVO">
        SELECT t2.`policyNo`
             , t1.`finishResult`
             , ifnull(t1.`payMoney`, '--') as payMoney
             , concat(t4.regionName ,'-' , t4.organizationName) as regionName
             , t4.userName
             , t1.`finishTime`
             , t5.productName
             , t2.personName
        FROM `sm_claim` t1
                 LEFT JOIN `sm_order_insured` t2 on t1.`insuredId` = t2.`id` and t2.enabled_flag = 0
                 LEFT JOIN `sm_order` t3 on t3.`fhOrderId` = t2.`fhOrderId` and t3.enabled_flag = 0
                 LEFT JOIN `auth_user` t4 on t4.userId = t3.`customerAdminId` and t4.enabled_flag = 0
                 LEFT JOIN `sm_product` t5 on t5.id = t3.productId and t5.enabled_flag = 0
        WHERE t1.`claimState` = 'stepFinish'
          and `finishState` IN ('payed', 'payRejected')
          and t1.`enabled_flag` = 0
          and left(t1.`finishTime`, 7) = #{month}
          and t4.regionName = #{regionName}

    </select>
    <select id="exportOrg" resultType="com.cfpamf.ms.insur.admin.claim.vo.ClaimExportPushVO">

        SELECT t2.`policyNo`
             , t1.`finishResult`
             , ifnull(t1.`payMoney`, '--') as payMoney
             , concat(t4.regionName ,'-' , t4.organizationName) as regionName
             , t4.userName
             , t1.`finishTime`
             , t5.productName
             , t2.personName
        FROM `sm_claim` t1
                 LEFT JOIN `sm_order_insured` t2 on t1.`insuredId` = t2.`id` and t2.enabled_flag = 0
                 LEFT JOIN `sm_order` t3 on t3.`fhOrderId` = t2.`fhOrderId` and t3.enabled_flag = 0
                 LEFT JOIN `auth_user` t4 on t4.userId = t3.`customerAdminId` and t4.enabled_flag = 0
                 LEFT JOIN `sm_product` t5 on t5.id = t3.productId and t5.enabled_flag = 0
        WHERE t1.`claimState` = 'stepFinish'
          and `finishState` IN ('payed', 'payRejected')
          and t1.`enabled_flag` = 0
          and left(t1.`finishTime`, 7) = #{month}
          and t4.orgCode = #{orgCode}
    </select>

    <select id="exportWeekOrgOrRegion" resultType="com.cfpamf.ms.insur.admin.claim.vo.ClaimPushWeekVO">
        SELECT t4.regionName,
               t4.orgCode,
               concat(t4.regionName, '-', t4.organizationName) as regionOrg,
               t4.userName,
               t5.productName,
               t2.`policyNo`,
               t2.personName,
               t1.`riskType`,
               t1.`riskDesc`,
               DATE_FORMAT(t1.`create_time`, '%Y-%m-%d')       as createTime,
               t1.`claimState`,
               t1.claimResult,
               t1.`finishResult`,
               t1.finishState,
               t6.`dataJson`,
               t6.`oValue`,
               t10.productAttrCode,
               t7.idNumber                                     as insuredIdNumber,
               t9.idNumber                                     as applicantIdNumber,
               t1.process_type
        FROM `sm_claim` t1
                 LEFT JOIN `sm_order_insured` t2 on t1.`insuredId` = t2.`id`
            and t2.enabled_flag = 0
                 LEFT JOIN `sm_order` t3 on t3.`fhOrderId` = t2.`fhOrderId`
            and t3.enabled_flag = 0
                 LEFT JOIN `auth_user` t4 on t4.userId = t3.`customerAdminId`
            and t4.enabled_flag = 0
                 LEFT JOIN `sm_product` t5 on t5.id = t3.productId
            and t5.enabled_flag = 0
                 LEFT JOIN `sm_claim_progress` t6 on t1.id = t6.`claimId`
                 LEFT JOIN sm_order_insured t7 ON t1.insuredId = t7.id
                 LEFT JOIN sm_order t8 ON t7.fhOrderId = t8.fhOrderId
                 LEFT JOIN sm_order_applicant t9 ON t9.fhOrderId = t8.fhOrderId
                 LEFT JOIN sm_product t10 ON t10.id = t8.productId
        WHERE t1.`claimState` = 'stepFinish'
          and `finishState` = 'payRejected'
          and t1.`enabled_flag` = 0
          and t1.finishTime between #{startTime} and #{endTime}
          and t6.`oCode` = 'payRejected'
          and t6.`sCode` = 'stepToPay'
        GROUP BY t1.id
        union all
        SELECT t4.regionName,
               t4.orgCode,
               concat(t4.regionName, '-', t4.organizationName) as regionOrg,
               t4.userName,
               t5.productName,
               t2.`policyNo`,
               t2.personName,
               t1.`riskType`,
               t1.`riskDesc`,
               DATE_FORMAT(t1.`create_time`, '%Y-%m-%d')       as createTime,
               t1.`claimState`,
               t1.claimResult,
               t1.`finishResult`,
               t1.finishState,
               null                                            as `dataJson`,
               null                                            as `oValue`,
               t10.productAttrCode,
               t7.idNumber                                     as insuredIdNumber,
               t9.idNumber                                     as applicantIdNumber,
               t1.process_type
        FROM `sm_claim` t1
                 LEFT JOIN `sm_order_insured` t2 on t1.`insuredId` = t2.`id`
            and t2.enabled_flag = 0
                 LEFT JOIN `sm_order` t3 on t3.`fhOrderId` = t2.`fhOrderId`
            and t3.enabled_flag = 0
                 LEFT JOIN `auth_user` t4 on t4.userId = t3.`customerAdminId`
            and t4.enabled_flag = 0
                 LEFT JOIN `sm_product` t5 on t5.id = t3.productId
            and t5.enabled_flag = 0
                 LEFT JOIN sm_order_insured t7 ON t1.insuredId = t7.id
                 LEFT JOIN sm_order t8 ON t7.fhOrderId = t8.fhOrderId
                 LEFT JOIN sm_order_applicant t9 ON t9.fhOrderId = t8.fhOrderId
                 LEFT JOIN sm_product t10 ON t10.id = t8.productId
        WHERE t1.`claimState` != 'stepFinish'
          and t1.`enabled_flag` = 0
          and t1.`create_time` &lt; #{endTime}
          and t1.riskType in ('5', '8', '4', '3')

    </select>
</mapper>
