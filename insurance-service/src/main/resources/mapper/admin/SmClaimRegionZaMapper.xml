<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.claim.SmClaimRegionZaMapper">
    <select id="queryRegionByBankCode" resultType="com.cfpamf.ms.insur.admin.claim.vo.ZaClaimRegionInfoVO">
        SELECT t1.parent_id, t1.code, t1.name, t1.id
        FROM sm_claim_region_za t1
        WHERE t1.`code` IN (SELECT DISTINCT(district_code) FROM sm_claim_bank_za WHERE head_bank_code = #{bankCode})
    </select>

    <select id="queryRegionByIds" resultType="com.cfpamf.ms.insur.admin.claim.vo.ZaClaimRegionInfoVO">
        SELECT t1.parent_id, t1.code, t1.name, t1.id
        FROM sm_claim_region_za t1
        WHERE t1.`id` IN
        <foreach collection="list" separator="," close=")" open="(" item="item">
            #{item}
        </foreach>
    </select>
</mapper>