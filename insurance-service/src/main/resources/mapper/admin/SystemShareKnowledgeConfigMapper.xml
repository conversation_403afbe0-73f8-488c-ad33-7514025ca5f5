<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.sys.SystemShareKnowledgeConfigMapper">
    <select id="getShareKnowledgeConfigByObjId" resultType="com.cfpamf.ms.insur.admin.pojo.po.sys.SystemShareKnowledgeConfig">

        select c.*
        from system_share_knowledge_config  c
        left join system_share_ref r on c.id = r.share_id
        where r.obj_id = #{objId}
        and c.type=#{type} and c.enabled_flag=0 and r.enabled_flag=0
        limit 1
    </select>

    <select id="listProductShareKnowledge" resultType="com.cfpamf.ms.insur.admin.pojo.po.sys.SystemShareKnowledgeConfig">

        select c.*
        from system_share_knowledge_config  c
        where c.type = 'product' and c.enabled_flag=0
        <if test="objId!=null">
            and c.id in (select share_id from system_share_ref where obj_id=#{objId} and enabled_flag=0 )
        </if>
        <if test="shareTitle!=null">
            and c.share_title LIKE CONCAT('%',#{shareTitle},'%')
        </if>
        order by c.update_time desc
    </select>

    <select id="countProductShareKnowledge" resultType="java.lang.Integer">

        select count(c.id)
        from system_share_knowledge_config  c
        where c.type = 'product' and c.enabled_flag=0
        <if test="objId!=null">
            and c.id in (select share_id from system_share_ref where obj_id=#{objId} and enabled_flag=0 )
        </if>
        <if test="shareTitle!=null">
            and c.share_title LIKE CONCAT('%',#{shareTitle},'%')
        </if>
        order by c.update_time desc
    </select>

    <select id="listProductShareRefByType" resultType="com.cfpamf.ms.insur.admin.pojo.po.sys.SystemShareRef">

        select c.*
        from system_share_ref  c
        where  c.enabled_flag=0
        <if test="type!=null">
            and c.share_id in (select id from system_share_knowledge_config where type=#{type} and enabled_flag=0 )
        </if>
        <if test="shareIdList!=null and shareIdList.size>0">
            AND c.share_id in
            <foreach item="item" index="index" open="(" separator="," close=")" collection="shareIdList">
                #{item}
            </foreach>
        </if>

    </select>
    <select id="selectRandomDetail"
            resultType="com.cfpamf.ms.insur.admin.pojo.po.sys.SystemShareKnowledgeConfig">
        select *
        from system_share_knowledge_config
        where enabled_flag = 0
        order by rand()
        limit 1
    </select>

</mapper>
