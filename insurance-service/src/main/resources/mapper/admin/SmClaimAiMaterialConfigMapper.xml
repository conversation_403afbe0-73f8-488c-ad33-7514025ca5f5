<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.claim.SmClaimAiMaterialConfigMapper">

    <resultMap id="BaseResultMap" type="com.cfpamf.ms.insur.admin.pojo.po.claim.SmClaimAiMaterialConfig">
        <id column="id" property="id"/>
        <result column="material_type" property="materialType"/>
        <result column="sub_type" property="subType"/>
        <result column="classification_rule" property="classificationRule"/>
        <result column="claim_process" property="claimProcess"/>
        <result column="update_by" property="updateBy"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="enabled_flag" property="enabledFlag"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, material_type, sub_type, classification_rule, claim_process, 
        update_by, create_time, update_time, enabled_flag
    </sql>

    <insert id="insert" parameterType="com.cfpamf.ms.insur.admin.pojo.po.claim.SmClaimAiMaterialConfig" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO sm_claim_ai_material_config (material_type, sub_type, classification_rule, claim_process,
                                       update_by, create_by, create_time, update_time, enabled_flag)
        VALUES (#{materialType}, #{subType}, #{classificationRule}, #{claimProcess},
                #{updateBy}, #{createBy}, NOW(), NOW(), 0)
    </insert>

    <update id="update" parameterType="com.cfpamf.ms.insur.admin.pojo.po.claim.SmClaimAiMaterialConfig">
        UPDATE sm_claim_ai_material_config
        SET material_type       = #{materialType},
            sub_type            = #{subType},
            classification_rule = #{classificationRule},
            claim_process       = #{claimProcess},
            update_by       = #{updateBy},
            update_time         = NOW()
        WHERE id = #{id}
          AND enabled_flag = 0
    </update>

    <update id="deleteById">
        UPDATE sm_claim_ai_material_config
        SET enabled_flag = 1,
            update_time  = NOW()
        WHERE id = #{id}
          AND enabled_flag = 0
    </update>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM sm_claim_ai_material_config
        WHERE id = #{id} AND enabled_flag = 0
    </select>

    <select id="selectList" resultType="com.cfpamf.ms.insur.admin.pojo.vo.claim.SmClaimAiMaterialConfigPageVO">
        SELECT
        t1.*, t2.userName as updateByName
        FROM sm_claim_ai_material_config t1
        left join auth_user t2 on t1.update_by = t2.userId AND t2.enabled_flag = 0
        <where>
            AND t1.enabled_flag = 0
            <if test="materialType != null and materialType != ''">
                AND t1.material_type LIKE CONCAT('%', #{materialType}, '%')
            </if>
            <if test="subType != null and subType != ''">
                AND t1.sub_type LIKE CONCAT('%', #{subType}, '%')
            </if>
            <if test="claimProcess != null and claimProcess != ''">
                AND t1.claim_process LIKE CONCAT('%', #{claimProcess}, '%')
            </if>
        </where>
        ORDER BY t1.update_time DESC
    </select>

    <select id="selectCount" resultType="int">
        SELECT COUNT(*)
        FROM sm_claim_ai_material_config
        <where>
            AND enabled_flag = 0
            <if test="materialType != null and materialType != ''">
                AND material_type LIKE CONCAT('%', #{materialType}, '%')
            </if>
            <if test="subType != null and subType != ''">
                AND sub_type LIKE CONCAT('%', #{subType}, '%')
            </if>
            <if test="claimProcess != null and claimProcess != ''">
                AND claim_process LIKE CONCAT('%', #{claimProcess}, '%')
            </if>
        </where>
    </select>
    <select id="selectByProcessTypeAndFileTypeCode"
            resultType="com.cfpamf.ms.insur.admin.pojo.po.claim.SmClaimAiMaterialConfig">
        select * from sm_claim_ai_material_config where claim_process = #{processType} and material_type = #{fileTypeCode} AND enabled_flag = 0
    </select>


</mapper> 