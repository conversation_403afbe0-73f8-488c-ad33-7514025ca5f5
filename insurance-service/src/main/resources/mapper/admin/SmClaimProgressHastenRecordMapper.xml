<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.claim.SmClaimProgressHastenRecordMapper">

    <resultMap id="BaseResultMap" type="com.cfpamf.ms.insur.admin.pojo.po.claim.SmClaimProgressHastenRecord">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="claimId" column="claim_id" jdbcType="INTEGER"/>
            <result property="progressId" column="progress_id" jdbcType="INTEGER"/>
            <result property="currentCode" column="current_code" jdbcType="VARCHAR"/>
            <result property="expectHastenTime" column="expect_hasten_time" jdbcType="TIMESTAMP"/>
            <result property="sendTo" column="send_to" jdbcType="VARCHAR"/>
            <result property="state" column="state" jdbcType="VARCHAR"/>
            <result property="hastenReason" column="hasten_reason" jdbcType="VARCHAR"/>
            <result property="actualHastenTime" column="actual_hasten_time" jdbcType="TIMESTAMP"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="enabledFlag" column="enabled_flag" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,claim_id,progress_id,
        current_code,expect_hasten_time,send_to,
        state,hasten_reason,actual_hasten_time,
        create_time,create_by,update_time,
        update_by,enabled_flag
    </sql>
    <select id="selectByClaimIdAndState"
            resultType="com.cfpamf.ms.insur.admin.pojo.po.claim.SmClaimProgressHastenRecord">

        select *
        from sm_claim_progress_hasten_record
        where claim_id = #{claimId}
        order by create_time desc
        limit 1

    </select>

    <select id="selectByClaimIdAndStateSuc"
            resultType="com.cfpamf.ms.insur.admin.pojo.po.claim.SmClaimProgressHastenRecord">

        select *
        from sm_claim_progress_hasten_record
        where claim_id = #{claimId} and state = 1
        order by create_time desc
        limit 1

    </select>

    <select id="calExpectTime" resultType="java.lang.Integer">
        SELECT CEILING(SUM(
        IFNULL( closed_number, 0 )* IFNULL( summary_cycle, 0 )
        )/ SUM( IFNULL( closed_number, 0 ) ) )
        FROM dwd_claim_monthly_statistic
        <where>
            <if test="productId != null">
                and product_id = #{productId}
            </if>
            <if test="companyId != null">
                and insurance_company_id = #{companyId}
            </if>
            <if test="riskType != null and riskType != ''">
                and claim_risk_type = #{riskType}
            </if>
            and rpt_month between STR_TO_DATE(DATE_SUB(now(), INTERVAL 3 YEAR) ,'%Y-%m-%d')  and STR_TO_DATE(now(),'%Y-%m-%d')
            and closed_number != 0
        </where>

    </select>
</mapper>
