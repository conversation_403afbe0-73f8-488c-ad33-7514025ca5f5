<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.OrgMapper">

    <insert id="insertOrg" useGeneratedKeys="true">
        INSERT INTO organization (batchNo, hrOrgId, hrParentId, orgType, orgName, orgCode, orgPath, create_time,
                                  update_time)
        VALUES (#{batchNo}, #{hrOrgId}, #{hrParentId}, #{orgType}, #{orgName}, #{orgCode}, #{orgPath},
                CURRENT_TIMESTAMP(), CURRENT_TIMESTAMP())
    </insert>

    <update id="updateOrg">
        UPDATE organization
        SET batchNo=#{batchNo},
            hrOrgId=#{hrOrgId},
            hrParentId=#{hrParentId},
            orgType=#{orgType},
            orgName=#{orgName},
            orgCode=#{orgCode},
            orgPath=#{orgPath},
            update_time=CURRENT_TIMESTAMP()
        WHERE id = #{id}
    </update>

    <select id="listOrgs" resultType="com.cfpamf.ms.insur.admin.pojo.vo.OrgVO">
        SELECT *
        FROM organization
    </select>

    <select id="getOrgByHrOrgId" resultType="com.cfpamf.ms.insur.admin.pojo.vo.OrgVO">
        SELECT t1.*, t2.orgName AS parentOrgName, t2.orgCode AS parentOrgCode
        FROM organization t1
                 LEFT JOIN organization t2 ON t1.hrParentId = t2.hrOrgId
        WHERE t1.hrOrgId = #{hrOrgId}
    </select>
    <select id="listByOrgNames" resultType="com.cfpamf.ms.insur.admin.pojo.vo.OrgVO">
        select * from organization
        where orgName in
        <foreach item="item" collection="orgNames" open="(" close=")" separator=",">
            #{item}
        </foreach>
and enabled_flag = 0
    </select>

    <select id="listOrgByRegionCode" resultType="com.cfpamf.ms.insur.admin.pojo.vo.OrgVO">
        select * from organization
        where hrParentId in
        (select hrOrgId from organization where orgCode = #{regionCode})
        and enabled_flag = 0
    </select>

    <update id="deleteAllOrgs">
        DELETE
        FROM organization
        WHERE batchNo IS NOT NULL
    </update>
</mapper>
