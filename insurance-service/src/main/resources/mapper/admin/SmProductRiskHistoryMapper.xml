<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.product.SmProductRiskHistoryMapper">
    <resultMap id="BaseResultMap" type="com.cfpamf.ms.insur.admin.pojo.po.product.SmProductRiskHistory">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="product_id" jdbcType="INTEGER" property="productId"/>
        <result column="risk_key" jdbcType="VARCHAR" property="riskKey"/>
        <result column="risk_version" jdbcType="INTEGER" property="riskVersion"/>
        <result column="required" jdbcType="TINYINT" property="required"/>
        <result column="main_guarantee" jdbcType="TINYINT" property="mainGuarantee"/>
        <result column="version" jdbcType="INTEGER" property="version"/>
        <result column="enabled_flag" jdbcType="TINYINT" property="enabledFlag"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , product_id, risk_key, risk_version, required, main_guarantee, version, enabled_flag,
    create_by, update_by, create_time, update_time
    </sql>
    <update id="softDeleteByProductIdAndVersion">
        update sm_product_risk_history
        set enabled_flag = 1
        where product_id = #{productId}
          and version = #{version}
    </update>
    <insert id="initNewHistory">
        insert into sm_product_risk_history(version, product_id, risk_key, risk_version, required, main_guarantee,
                                            update_by,
                                            update_time, enabled_flag, create_by, create_time)
        SELECT #{version},
               product_id,
               risk_key,
               risk_version,
               required,
               main_guarantee,
               update_by,
               update_time,
               enabled_flag,
               create_by,
               create_time
        from sm_product_risk
        where product_id = #{productId}
          and enabled_flag = 0
    </insert>
    <select id="getProductRisk" resultType="com.cfpamf.ms.insur.admin.pojo.vo.product.SmProductRiskVO">
        select t1.main_guarantee,
               t1.required,
               t2.*,
               t3.limit_age
        from sm_product_risk_history t1
                 left join sys_risk t2 on t2.risk_key = t1.risk_key
            and t1.risk_version = t2.version
                 left join sys_risk_limit t3 on t3.risk_key = t2.risk_key
            and t3.version = t2.version
        where t1.enabled_flag = 0
          and t1.product_id = #{productId}
          and t1.version = #{version}
    </select>

</mapper>