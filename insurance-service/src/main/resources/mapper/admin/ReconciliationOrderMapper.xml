<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.order.ReconciliationOrderMapper">

    <select id="getReconciliationPolicyList"
            resultType="com.cfpamf.ms.insur.admin.pojo.dto.reconciliation.ReconciliationPolicyDTO">
        SELECT
        REVERSE(
        SUBSTR( REVERSE( t1.`policyNo` ) FROM INSTR( REVERSE( t1.`policyNo` ), '_' ) + 1 )) AS noLinePolicyNo,
        t1.`policyNo`,
        t1.appStatus,
        t1.appPersonName AS applicantName,
        sum( t1.`totalAmount` ) AS policyAmount,
        case when t3.long_insurance = 0 and t2.term_num is null then 1 else t2.`term_num` end as termNum
        FROM
        sm_order_commission t1
        left join sm_commission_detail t2 on t2.policy_No = t1.policyNo and t1.appStatus = t2.policy_status and t1.insidNumber = t2.insured_id_number
        left join sm_product t3 on t1.productId = t3.id
        WHERE  (t1.appStatus = 4 or t1.appStatus = 1)
        <if test="reconciliationStartTime != null">
            and t1.accountTime >= #{reconciliationStartTime}
        </if>
        <if test="reconciliationEndTime != null">
            <![CDATA[ and t1.accountTime <= #{reconciliationEndTime} ]]>
        </if>
        <if test='channels != null and channels.size() > 0'>
            AND t1.channel in
            <foreach collection="channels" open="(" close=")" separator="," item = "item">
                #{item}
            </foreach>
        </if>
        <if test="productId != null">
            and t1.productId = #{productId}
        </if>
        <if test="longInsurance != null">
            and t3.long_insurance = #{longInsurance}
        </if>
        GROUP by REVERSE(SUBSTR(REVERSE(t1.`policyNo`) FROM INSTR(REVERSE(t1.`policyNo`), '_') +1)), t1.appStatus,t2.term_num

    </select>
    <select id="getReconciliationPolicyListByPolicyNoList"
            resultType="com.cfpamf.ms.insur.admin.pojo.po.SmOrderInsured">
        select * from sm_order_insured t1
        where policyNo
        in
        <foreach collection="policyNoList" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <select id="getReconciliationPolicyListByPolicyNo"
            resultType="com.cfpamf.ms.insur.admin.pojo.po.SmOrderInsured">
        select *
        from sm_order_insured t1
        where policyNo = #{policyNo}

    </select>
    <select id="getReconciliationPolicy"
            resultType="com.cfpamf.ms.insur.admin.pojo.dto.reconciliation.ReconciliationPolicyDTO">
        select t1.accountTime,
        t1.appStatus,
        t1.policyNo,
        REVERSE(SUBSTR(REVERSE(t1.`policyNo`) FROM INSTR(REVERSE(t1.`policyNo`), '_') +1)) as noLinePolicyNo,
        t1.totalAmount as policyAmount
        from sm_order_commission t1
        where
        t1.channel = #{channel}
        <![CDATA[  and t1.accountTime <= #{endTime}]]>
        and t1.accountTime >= #{startTime}
        and REVERSE(SUBSTR(REVERSE(t1.`policyNo`) FROM INSTR(REVERSE(t1.`policyNo`), '_') +1)) in
        <foreach collection="policyNoList" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

</mapper>