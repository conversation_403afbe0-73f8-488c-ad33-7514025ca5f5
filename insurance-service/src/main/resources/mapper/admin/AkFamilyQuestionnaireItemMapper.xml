<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.aicheck.AkFamilyQuestionnaireItemMapper">
    <resultMap id="BaseResultMap" type="com.cfpamf.ms.insur.admin.pojo.po.aicheck.AkFamilyQuestionnaireItem">
        <id column="id" jdbcType="INTEGER" property="id" />
        <result column="questionnaire_id" jdbcType="INTEGER" property="questionnaireId" />
<!--        <result column="questionnaire_code" jdbcType="VARCHAR" property="questionnaireCode" />-->
        <result column="person_name" jdbcType="VARCHAR" property="personName" />
        <result column="id_number" jdbcType="VARCHAR" property="idNumber" />
        <result column="disease_code" jdbcType="VARCHAR" property="diseaseCode" />
        <result column="questions" jdbcType="VARCHAR" property="questions" />
        <result column="result_code" jdbcType="VARCHAR" property="resultCode" />
        <result column="uw_question_path" jdbcType="VARCHAR" property="uwQuestionPath" />
        <result column="uw_conclusion" jdbcType="VARCHAR" property="uwConclusion" />
        <result column="create_by" jdbcType="VARCHAR" property="createBy" />
        <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
        <result column="enabled_flag" jdbcType="INTEGER" property="enabledFlag" />
    </resultMap>
    <sql id="BaseTable">
        ak_family_questionnaire_item
    </sql>
</mapper>