<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.SystemPushSettingMapper">

    <select id="listSystemPushSettings" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SystemPushSettingVO">
        SELECT * FROM system_push_setting WHERE pushType = #{pushType} AND enabled_flag = 0
    </select>

    <select id="getSystemPushSettingById" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SystemPushSettingVO">
        SELECT * FROM system_push_setting
        WHERE spsId = #{spsId}
        <if test="pushType!=null">
            AND pushType = #{pushType}
        </if>
    </select>

    <select id="countSystemPushSetting" resultType="java.lang.Integer">
        SELECT COUNT(1) FROM system_push_setting WHERE
        pushType = #{pushType}
        AND userType IN (#{userType}, 'all')
        AND activeFlag = 1
        AND enabled_flag = 0
        AND (
        ( <![CDATA[  startTime <= #{startTime}   AND #{startTime} <= endTime ]]>  )
        OR
        ( <![CDATA[  startTime <= #{endTime}     AND #{endTime} <= endTime ]]>  )
        )
        <if test="excludeSpsId!=null">
            AND spsId != #{excludeSpsId}
        </if>
    </select>

    <insert id="insertSystemPushSetting" useGeneratedKeys="true">
        INSERT INTO system_push_setting (pushType, title, startTime, endTime, userType, activeFlag, showType, dataDateType, showImageUrl, clickJumpUrl)
        VALUES
        (#{pushType}, #{title}, #{startTime}, #{endTime}, #{userType}, 1, #{showType}, #{dataDateType}, #{showImageUrl} ,#{clickJumpUrl})
    </insert>

    <update id="updateSystemPushSetting">
     UPDATE system_push_setting
     SET
     title = #{title},
     startTime = #{startTime},
     endTime = #{endTime},
     userType = #{userType},
     activeFlag = #{activeFlag},
     showType = #{showType},
     dataDateType = #{dataDateType},
     showImageUrl = #{showImageUrl},
     clickJumpUrl = #{clickJumpUrl}
     WHERE spsId = #{spsId}
  </update>

    <update id="updateSystemPushSettingStatus">
     UPDATE system_push_setting
     SET
     activeFlag = #{activeFlag}
     WHERE spsId = #{spsId}
  </update>

    <delete id="deleteSystemPushSetting">
     UPDATE system_push_setting SET enabled_flag = 1 WHERE spsId = #{spsId}
  </delete>
</mapper>