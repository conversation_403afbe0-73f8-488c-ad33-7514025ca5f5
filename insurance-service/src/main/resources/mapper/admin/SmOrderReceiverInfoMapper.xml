<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.order.SmOrderReceiverInfoMapper">
    <resultMap id="BaseResultMap" type="com.cfpamf.ms.insur.admin.receiver.entity.SmOrderReceiverInfo">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="fh_order_id" jdbcType="VARCHAR" property="fhOrderId"/>
        <result column="receiver_phone" jdbcType="VARCHAR" property="receiverPhone"/>
        <result column="receiver_name" jdbcType="VARCHAR" property="receiverName"/>
        <result column="receiver_address" jdbcType="VARCHAR" property="receiverAddress"/>
        <result column="receiver_area" jdbcType="VARCHAR" property="receiverArea"/>
        <result column="receiver_area_code" jdbcType="VARCHAR" property="receiverAreaCode"/>
        <result column="express_company" jdbcType="VARCHAR" property="expressCompany"/>
        <result column="courier_number" jdbcType="VARCHAR" property="courierNumber"/>
        <result column="enabled_flag" jdbcType="TINYINT" property="enabledFlag"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , fh_order_id, receiver_phone, receiver_name, receiver_address, receiver_area,
    receiver_area_code, express_company, courier_number, enabled_flag, create_by, update_by, 
    create_time, update_time
    </sql>

    <select id="getByFhOrderId" resultMap="BaseResultMap">
        select *
        from sm_order_receiver_info
        where fh_order_id = #{fhOrderId}
          and enabled_flag = 0;
    </select>

    <select id="getByFhOrderIdList" resultType="com.cfpamf.ms.insur.admin.receiver.vo.SmOrderReceiverInfoVo">
        select *
        from sm_order_receiver_info
        where
        enabled_flag = 0 and
        fh_order_id in
        <foreach collection="fhOrderIdList" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="getProductReceiverInfoField" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmProductFormFieldVO">
        SELECT *
        FROM sm_product_form_field
        WHERE enabled_flag = 0
        and groupCode = 'pickupInfo'
        and productId in
        <foreach item="item" index="index" open="(" separator="," close=")" collection="productIdList">
            #{item}
        </foreach>
    </select>

    <select id="getProductReceiverInfoFieldByProductId"
            resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmProductFormFieldVO">
        SELECT *
        FROM sm_product_form_field
        WHERE enabled_flag = 0
          and groupCode = 'pickupInfo'
          and productId = #{productId}
    </select>

    <select id="getSmOrderReceiverInfoVoByFhOrderId"
            resultType="com.cfpamf.ms.insur.admin.receiver.vo.SmOrderReceiverInfoVo">
        select *
        from sm_order_receiver_info
        where fh_order_id = #{fhOrderId}
          and enabled_flag = 0;
    </select>
</mapper>