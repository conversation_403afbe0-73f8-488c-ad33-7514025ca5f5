<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.order.SmOrderCarMapper">
    <insert id="insertOrderCarBatch" useGeneratedKeys="true">
        INSERT INTO sm_order_car (fh_order_id, car_plate_no, car_numb_type, chassis_number, car_type, engine_no, approved_num, car_use_nature,
        vehicle_nature, enabled_flag, create_time, update_time)
        VALUES
        <foreach collection="dtos" item="dto" index="index" separator=",">
            (#{dto.fhOrderId}, #{dto.carInfo.carPlateNo}, #{dto.carInfo.carNumbType}, #{dto.carInfo.chassisNumber},
            #{dto.carInfo.carType},
            #{dto.carInfo.engineNo}, #{dto.carInfo.approvedNum}, #{dto.carInfo.carUseNature},
            #{dto.carInfo.vehicleNature},0,CURRENT_TIMESTAMP() , CURRENT_TIMESTAMP())
        </foreach>
    </insert>

    <update id="updateOrderCarBatch" parameterType= "java.util.List">
        <foreach collection="dtos" item="dto" index="index" separator=";">
         update sm_order_car
         set car_plate_no=#{dto.carInfo.carPlateNo},car_numb_type=#{dto.carInfo.carNumbType},
            chassis_number= #{dto.carInfo.chassisNumber},car_type=#{dto.carInfo.carType},
            engine_no=#{dto.carInfo.engineNo},approved_num=#{dto.carInfo.approvedNum},
            car_use_nature=#{dto.carInfo.carUseNature},vehicle_nature=#{dto.carInfo.vehicleNature}
        where fh_order_id=#{dto.fhOrderId}
        </foreach>
    </update>

</mapper>
