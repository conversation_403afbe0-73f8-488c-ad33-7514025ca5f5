<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.SystemActivityMapper">
    <resultMap id="SystemActivityMap" type="com.cfpamf.ms.insur.admin.pojo.vo.SystemActivityVO">
        <result column="saId" property="saId"/>
        <result column="title" property="title"/>
        <result column="imageUrl" property="imageUrl"/>
        <result column="startTime" property="startTime"/>
        <result column="endTime" property="endTime"/>
        <result column="content" property="content"/>
        <result column="regions" property="regions" typeHandler="com.cfpamf.ms.insur.base.dao.MySqlJsonHandler"/>
        <result column="products" property="products" typeHandler="com.cfpamf.ms.insur.base.dao.MySqlJsonHandler"/>
    </resultMap>

    <select id="listSystemActivitys" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SystemActivityVO"
            resultMap="SystemActivityMap">
        SELECT * FROM system_activity WHERE enabled_flag = 0
        <if test="startDate!=null and endDate!=null">
            AND
            (
            (
            <![CDATA[ startTime >= #{startDate} ]]>
            <![CDATA[ AND startTime <= #{endDate} ]]>
            )
            OR
            (
            <![CDATA[ endTime >= #{startDate} ]]>
            <![CDATA[ AND endTime <= #{endDate} ]]>
            )
            OR
            (
            <![CDATA[ startTime >= #{startDate} ]]>
            <![CDATA[ AND endTime <= #{endDate} ]]>
            )
            OR
            (
            <![CDATA[ startTime <= #{startDate} ]]>
            <![CDATA[ AND endTime >= #{endDate} ]]>
            )
            )
        </if>
        <if test="startDate!=null and endDate==null">
            <![CDATA[ AND endTime > #{startDate} ]]>
        </if>
        <if test="startDate==null and endDate!=null">
            <![CDATA[ AND startTime < #{endDate} ]]>
        </if>
        <if test="title!=null">
            AND title LIKE CONCAT( '%', #{title}, '%')
        </if>
        <if test="activeFlag!=null">
            AND activeFlag = #{activeFlag}
        </if>
    </select>

    <select id="listWxActivitys" resultType="com.cfpamf.ms.insur.weixin.pojo.vo.WxActivityListVO">
        SELECT * FROM system_activity WHERE enabled_flag = 0 AND activeFlag = 1
        <![CDATA[ AND endTime >= CURRENT_TIMESTAMP() ]]>
        <if test="regionName!=null">
            AND (JSON_CONTAINS( regions->"$[*]" ,'"${regionName}"', "$") OR JSON_CONTAINS( regions->"$[*]" ,'"无限制"',
            "$"))
        </if>
        <if test="regionName==null">
            AND JSON_CONTAINS( regions->"$[*]" ,'"无限制"', "$")
        </if>
        ORDER BY startTime DESC
    </select>

    <select id="getSystemActivityById" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SystemActivityVO"
            resultMap="SystemActivityMap">
        SELECT * FROM system_activity WHERE enabled_flag = 0 AND saId = #{saId}
    </select>

    <insert id="insertSystemActivity" useGeneratedKeys="true">
        INSERT INTO system_activity (title, imageUrl, startTime, endTime, content, regions, products, activeFlag)
        VALUES
        (#{title}, #{imageUrl}, #{startTime}, #{endTime}, #{content}, #{regions,typeHandler=com.cfpamf.ms.insur.base.dao.MySqlJsonHandler},
          #{products,typeHandler=com.cfpamf.ms.insur.base.dao.MySqlJsonHandler}, 1)
    </insert>

    <update id="updateSystemActivity">
      UPDATE system_activity
         SET
         title = #{title},
         imageUrl = #{imageUrl},
         startTime = #{startTime},
         endTime = #{endTime},
         content = #{content},
         activeFlag = #{activeFlag},
         regions = #{regions,typeHandler=com.cfpamf.ms.insur.base.dao.MySqlJsonHandler},
         products = #{products,typeHandler=com.cfpamf.ms.insur.base.dao.MySqlJsonHandler}
         WHERE saId = #{saId}
    </update>

    <update id="updateSystemActivityStatus">
     UPDATE system_activity
     SET
     activeFlag = #{activeFlag}
     WHERE saId = #{saId}
    </update>

    <delete id="deleteSystemActivity">
     UPDATE system_activity SET enabled_flag = 1 WHERE saId = #{saId}
    </delete>
</mapper>