<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.commission.SmOrderCommissionDetailMapper">

    <!--批量插入，当唯一索引已存在，则按唯一索引来更新记录，支付佣金、结算佣金、折算保费三个字段已经存在，则不更新该字段值， -->
    <update id="batchInsertCommissionDetail" useGeneratedKeys="true" parameterType="java.util.List">
        insert into
        sm_commission_detail(order_id,policy_no,insured_id_number,plan_id,risk_id,term_num,account_time,policy_status,amount,original_amount,payment_commission_id,payment_rate,payment_amount
        ,settlement_commission_id,settlement_rate,settlement_amount,conversion_commission_id,conversion_rate,conversion_amount
        ,payment_risk_json,settlement_risk_json,conversion_risk_json
        ,enabled_flag,commission_user_id,business_type,business_id)
        values
        <foreach collection="list" item="det" separator=",">
            (#{det.orderId},#{det.policyNo},#{det.insuredIdNumber},#{det.planId},#{det.riskId},#{det.termNum},#{det.accountTime},#{det.policyStatus},#{det.amount},#{det.originalAmount},#{det.paymentCommissionId},#{det.paymentRate},#{det.paymentAmount}
            ,#{det.settlementCommissionId},#{det.settlementRate},#{det.settlementAmount},#{det.conversionCommissionId},#{det.conversionRate},#{det.conversionAmount}
            ,#{det.paymentRiskJson},#{det.settlementRiskJson},#{det.conversionRiskJson}
            ,#{det.enabledFlag},#{det.commissionUserId},#{det.businessType},#{det.businessId}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
        amount = values(amount)
        ,original_amount = values(original_amount)
        ,payment_amount = if(payment_amount is not null,payment_amount,values(payment_amount))
        ,settlement_amount=if(settlement_amount is not null,settlement_amount,values(settlement_amount))
        ,conversion_amount=if(conversion_amount is not null,conversion_amount,values(conversion_amount))
        ,payment_risk_json = if(payment_amount and payment_risk_json,payment_risk_json,values(payment_risk_json))
        ,settlement_risk_json=if(settlement_amount and
        settlement_risk_json,settlement_risk_json,values(settlement_risk_json))
        ,conversion_risk_json=if(conversion_amount and
        conversion_risk_json,conversion_risk_json,values(conversion_risk_json))
    </update>


    <!--批量插入，已存在则覆盖以前计算值，用于佣金记录重算，用于人工确认需要重新计算，再触发 -->
    <update id="batchReCalcCommissionDetail" useGeneratedKeys="true" parameterType="java.util.List">
        insert into
        sm_commission_detail(order_id,policy_no,insured_id_number,plan_id,risk_id,term_num,account_time,policy_status,amount,original_amount,payment_commission_id,payment_rate,payment_amount
        ,settlement_commission_id,settlement_rate,settlement_amount,conversion_commission_id,conversion_rate,conversion_amount
        ,payment_risk_json,settlement_risk_json,conversion_risk_json
        ,enabled_flag,commission_user_id,business_type,business_id,import_flag)
        values
        <foreach collection="list" item="det" separator=",">
            (#{det.orderId},#{det.policyNo},#{det.insuredIdNumber},#{det.planId},#{det.riskId},#{det.termNum},#{det.accountTime},#{det.policyStatus},#{det.amount},#{det.originalAmount},#{det.paymentCommissionId},#{det.paymentRate},#{det.paymentAmount}
            ,#{det.settlementCommissionId},#{det.settlementRate},#{det.settlementAmount},#{det.conversionCommissionId},#{det.conversionRate},#{det.conversionAmount}
            ,#{det.paymentRiskJson},#{det.settlementRiskJson},#{det.conversionRiskJson}
            ,#{det.enabledFlag},#{det.commissionUserId},#{det.businessType},#{det.businessId}
            <if test="det.importFlag!=null">
                ,#{det.importFlag}
            </if>
            <if test="det.importFlag==null">
                ,0
            </if>
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
        account_time = values(account_time)
        ,amount = values(amount)
        ,original_amount = values(original_amount)
        <if test="det.paymentRate!=null">
            ,payment_commission_id=values(payment_commission_id)
            ,payment_rate=values(payment_rate)
            ,payment_amount = values(payment_amount)
        </if>
        <if test="det.settlementRate!=null">
            ,settlement_commission_id=values(settlement_commission_id)
            ,settlement_rate=values(settlement_rate)
            ,settlement_amount=values(settlement_amount)
        </if>
        <if test="det.conversionRate!=null">
            ,conversion_commission_id=values(conversion_commission_id)
            ,conversion_rate=values(conversion_rate)
            ,conversion_amount=values(conversion_amount)
        </if>
        ,payment_risk_json = values(payment_risk_json)


        ,settlement_risk_json=values(settlement_risk_json)
        ,conversion_risk_json=values(conversion_risk_json)
        ,commission_user_id = values(commission_user_id)
        ,business_type = values(business_type)
        ,business_id = values(business_id)
        ,import_flag = values(import_flag)
    </update>

    <update id="updateOrderCommissionCancelAmount">
        UPDATE sm_commission_detail
        set amount            = #{cancelAmount},
        conversion_amount = #{cancelAmount} * (case when #{appStatus} = '4' THEN -1 ELSE 1 END) * conversion_rate /
        100,
        settlement_amount = #{cancelAmount} * (case when #{appStatus} = '4' THEN -1 ELSE 1 END) * settlement_rate /
                            100,
        payment_amount = #{cancelAmount} * (case when #{appStatus} = '4' THEN -1 ELSE 1 END) * payment_rate /
                            100
        where policy_no = #{policyNo}
        and policy_status = #{appStatus}
    </update>

    <select id="queryCommission" resultType="com.cfpamf.ms.insur.admin.pojo.vo.commission.SmLongInsuranceCommissionVO">
        SELECT
        t1.term_num as termNum,
        t1.insured_id_number as insuredIdNumber,
        t1.policy_status as appStatus,
        t3.productName as productName,
        t4.companyName,
        t3.id as productId,
        t1.policy_no as policyNo,
        t7.channel as channel,
        t6.personName AS insuredPersonName,
        soa.personName AS applicantPersonName,
        sum(if(t1.policy_status='4',round(t1.amount*-1,2),t1.amount)) as totalAmount,
        sum(t1.conversion_amount) as convertedAmount,
        sum(t1.settlement_amount) as settlementCommission,
        sum(t1.payment_amount) as payCommissionAmount,
        t2.regionName AS recommendRegionName,
        t2.organizationFullName AS recommendOrganizationName,
        t2.userName AS recommendUserName,
        t2.userId as recommendUserId,
        if(sot.id is null,false,true) talkOrder,
        sot.invite_name inviteName,
        t7.create_time as createTime,
        t1.account_time as accountTime,
        t7.paymentTime,
        t7.recommendId,
        t7.customerAdminId,
        t1.order_id as fhOrderId,
        t1.conversion_risk_json conversionRiskJson,
        t1.payment_risk_json paymentRiskJson,
        t1.settlement_risk_json settlementRiskJson,
        t8.userName AS customerAdminName,
        t8.userId AS customerAdminId,
        t7.recommendAdminName as userAdminName,
        t7.recommendMasterName as userMasterName,
        t1.original_amount as originalAmount,
        t12.activity_code,t12.product_activity_code as villageActivity,t12.type
        ,t13.userName as commissionUserName,t1.commission_user_id as commissionUserId
        FROM
        sm_commission_detail t1
        LEFT JOIN sm_order t7
        ON t7.fhOrderId = t1.order_id
        LEFT JOIN auth_user t2
        ON t7.recommendId = t2.userId AND t2.enabled_flag = 0
        LEFT JOIN auth_user t8
        ON t7.customerAdminId = t8.userId AND t8.enabled_flag = 0
        LEFT JOIN sm_product t3
        ON t7.productId = t3.id
        LEFT JOIN sm_company t4
        ON t3.companyId = t4.id
        LEFT JOIN sm_order_insured t6
        on t6.fhOrderId = t1.order_id and t1.insured_id_number= t6.idNumber and t1.policy_status = t6.appStatus
        left join sm_order_talk sot on sot.fh_order_id = t1.order_id
        left join sm_order_applicant soa on soa.fhOrderId = t7.fhOrderId
        left join sm_reconciliation_policy_cache t11
        on t11.policy_no = REVERSE(SUBSTR( REVERSE( t1.`policy_No` ) FROM INSTR( REVERSE( t1.`policy_No` ), '_' ) + 1 )) and t11.app_status = t1.policy_status
        left join sm_order_village_activity t12 on t7.fhOrderId=t12.fh_order_id and t12.type = 100
        left join  auth_user t13 ON t1.commission_user_id = t13.userId AND t13.enabled_flag = 0
        WHERE 1=1 and t3.long_insurance = 0
        <if test='villageActivity != null'>
            <if test="villageActivity == 1">
                and (t12.product_activity_code is null or t12.product_activity_code ='')
            </if>
            <if test="villageActivity == 0">
                and t12.product_activity_code is not null and t12.product_activity_code !=''
            </if>
        </if>
        <if test='createDateStart != null'>
            <![CDATA[ AND t7.create_time>=#{createDateStart} ]]>
        </if>
        <if test='createDateEnd != null'>
            <![CDATA[ AND t7.create_time<=#{createDateEnd} ]]>
        </if>
        <if test='paymentDateStart != null'>
            <![CDATA[ AND t7.paymentTime>=#{paymentDateStart} ]]>
        </if>
        <if test='paymentDateEnd != null'>
            <![CDATA[ AND t7.paymentTime<=#{paymentDateEnd} ]]>
        </if>
        <if test='orderNo != null'>
            <if test="fullMatch">
                AND t1.order_id = #{orderNo}
            </if>
            <if test="!fullMatch">
                AND t1.order_id LIKE CONCAT(#{orderNo},'%')
            </if>
        </if>
        <if test='talkOrder != null'>
            <if test="talkOrder == true">
                and sot.id > 0
            </if>
            <if test="talkOrder == false">
                and sot.id is null
            </if>
        </if>
        <if test='inviteName != null'>
            and sot.invite_name = #{inviteName}
        </if>
        <if test='policyNo !=  null'>
            AND t1.policy_no like CONCAT(#{policyNo},'%')
        </if>
        <if test="termNum != null ">
            and t1.term_num = #{termNum}
        </if>
        <if test='regionName != null'>
            AND t2.regionName=#{regionName}
        </if>
        <if test='orgName != null'>
            AND t2.organizationFullName=#{orgName}
        </if>

        <if test='applicantdName != null'>
            AND
            <if test="applicantdType == 1">
                soa.personName LIKE CONCAT(#{applicantdName},'%')
            </if>
            <if test="applicantdType == 2">
                soa.idNumber LIKE CONCAT(#{applicantdName},'%')
            </if>
            <if test="applicantdType == 3">
                soa.cellPhone LIKE CONCAT(#{applicantdName},'%')
            </if>
        </if>
        <if test='insuredName != null'>
            AND
            <if test="insuredType == 1">
                t6.personName LIKE CONCAT(#{insuredName},'%')
            </if>
            <if test="insuredType == 2">
                t6.idNumber LIKE CONCAT(#{insuredName},'%')
            </if>
            <if test="insuredType == 3">
                t6.cellPhone LIKE CONCAT(#{insuredName},'%')
            </if>
        </if>
        <if test='productId != null'>
            AND t7.productId=#{productId}
        </if>
        <if test='productIds != null and productIds.size() > 0'>
            AND t7.productId in
            <foreach collection="productIds" open="(" close=")" separator="," item = "item">
                #{item}
            </foreach>
        </if>
        <if test='recommendId != null'>
            AND t7.recommendId=#{recommendId}
        </if>
        <if test='customerAdminId != null'>
            AND t7.customerAdminId=#{customerAdminId}
        </if>
        <if test='companyId != null'>
            AND t3.companyId=#{companyId}
        </if>
        <if test='productAttrCode != null and productAttrCode=="person"'>
            AND t3.productAttrCode=#{productAttrCode}
        </if>
        <if test='productAttrCode != null and productAttrCode=="group"'>
            AND t3.productAttrCode in ('group','employer')
        </if>

        <if test='channel != null'>
            AND t7.channel=#{channel}
        </if>
        <if test='accountDateStart != null'>
            <![CDATA[ AND t1.account_time>=#{accountDateStart}  ]]>
        </if>
        <if test='accountDateEnd != null'>
            <![CDATA[ AND t1.account_time<=#{accountDateEnd}  ]]>
        </if>
        <if test='appStatus != null '>
            AND t1.policy_status = #{appStatus}
        </if>

        <if test='userId != null'>
            AND t7.recommendId=#{userId}
        </if>

        <if test='orgPath !=null '>
            and t2.orgCode in (select orgCode from organization where orgPath like CONCAT(#{orgPath},'%'))
        </if>
        <if test="reconciliationStatus != null">
            <if test="reconciliationStatus == 0">
                and t11.reconciliation_status = 0
            </if>
            <if test="reconciliationStatus == 1">
                and t11.reconciliation_status is null
            </if>
            <if test="reconciliationStatus == 2">
                and t11.reconciliation_status in (1,2,3,4,5,6,7,9)
            </if>
        </if>
        <if test="addCommissionStatus != null">
            <if test="addCommissionStatus == 0">
                and t1.order_id in (select order_id from sm_add_commission_detail t12
                where t12.order_id = t1.order_id AND t12.insured_id_number = t1.insured_id_number
                AND t12.policy_no = t1.policy_no AND t12.plan_id = t1.plan_id and t12.term_num = t1.term_num
                and t12.proportion != 0 group by  order_id, `policy_no` , `insured_id_number` , `plan_id` , `term_num`)
            </if>
            <if test="addCommissionStatus == 1">
                and t1.order_id not in (select order_id from sm_add_commission_detail t12
                where t12.order_id = t1.order_id AND t12.insured_id_number = t1.insured_id_number
                AND t12.policy_no = t1.policy_no AND t12.plan_id = t1.plan_id and t12.term_num = t1.term_num
                and t12.proportion != 0 group by  order_id, `policy_no` , `insured_id_number` , `plan_id` , `term_num`)
            </if>
        </if>
        GROUP BY t1.policy_no, t1.insured_id_number ,t1.policy_status,t1.term_num
        ORDER BY t1.account_time DESC
        <if test='startRow != null'>
            LIMIT #{startRow}, #{size}
        </if>
    </select>
    <select id="queryCommissionCount" resultType="java.lang.Integer">
        select count(1)
        from ( SELECT
        1
        FROM
        sm_commission_detail t1
        LEFT JOIN sm_order t7
        ON t7.fhOrderId = t1.order_id
        LEFT JOIN auth_user t2
        ON t7.recommendId = t2.userId AND t2.enabled_flag = 0
        LEFT JOIN sm_product t3
        ON t7.productId = t3.id
        LEFT JOIN sm_order_insured t6
        on t6.fhOrderId = t1.order_id and t1.insured_id_number= t6.idNumber and t1.policy_status = t6.appStatus
        left join sm_order_talk sot on sot.fh_order_id = t1.order_id
        left join sm_order_applicant soa on soa.fhOrderId = t7.fhOrderId
        left join sm_reconciliation_policy_cache t11
        on t11.policy_no = REVERSE(SUBSTR( REVERSE( t1.`policy_No` ) FROM INSTR( REVERSE( t1.`policy_No` ), '_' ) + 1 )) and t11.app_status = t1.policy_status
        left join sm_order_village_activity t12 on t7.fhOrderId=t12.fh_order_id and t12.type = 100
        WHERE 1=1 and t3.long_insurance = 0
        <if test='villageActivity != null'>
            <if test="villageActivity == 1">
                and (t12.product_activity_code is null or t12.product_activity_code ='')
            </if>
            <if test="villageActivity == 0">
                and t12.product_activity_code is not null and t12.product_activity_code !=''
            </if>
        </if>
        <if test='createDateStart != null'>
            <![CDATA[ AND t7.create_time>=#{createDateStart} ]]>
        </if>
        <if test='createDateEnd != null'>
            <![CDATA[ AND t7.create_time<=#{createDateEnd} ]]>
        </if>
        <if test='paymentDateStart != null'>
            <![CDATA[ AND t7.paymentTime>=#{paymentDateStart} ]]>
        </if>
        <if test='paymentDateEnd != null'>
            <![CDATA[ AND t7.paymentTime<=#{paymentDateEnd} ]]>
        </if>
        <if test='orderNo != null'>
            <if test="fullMatch">
                AND t1.order_id = #{orderNo}
            </if>
            <if test="!fullMatch">
                AND t1.order_id LIKE CONCAT(#{orderNo},'%')
            </if>
        </if>
        <if test='talkOrder != null'>
            <if test="talkOrder == true">
                and sot.id > 0
            </if>
            <if test="talkOrder == false">
                and sot.id is null
            </if>
        </if>
        <if test='inviteName != null'>
            and sot.invite_name = #{inviteName}
        </if>
        <if test='policyNo !=  null'>
            AND t1.policy_no like CONCAT(#{policyNo},'%')
        </if>
        <if test="termNum != null ">
            and t1.term_num = #{termNum}
        </if>
        <if test='regionName != null'>
            AND t2.regionName=#{regionName}
        </if>
        <if test='orgName != null'>
            AND t2.organizationFullName=#{orgName}
        </if>

        <if test='applicantdName != null'>
            AND
            <if test="applicantdType == 1">
                soa.personName LIKE CONCAT(#{applicantdName},'%')
            </if>
            <if test="applicantdType == 2">
                soa.idNumber LIKE CONCAT(#{applicantdName},'%')
            </if>
            <if test="applicantdType == 3">
                soa.cellPhone LIKE CONCAT(#{applicantdName},'%')
            </if>
        </if>
        <if test='insuredName != null'>
            AND
            <if test="insuredType == 1">
                t6.personName LIKE CONCAT(#{insuredName},'%')
            </if>
            <if test="insuredType == 2">
                t6.idNumber LIKE CONCAT(#{insuredName},'%')
            </if>
            <if test="insuredType == 3">
                t6.cellPhone LIKE CONCAT(#{insuredName},'%')
            </if>
        </if>
        <if test='productId != null'>
            AND t7.productId=#{productId}
        </if>
        <if test='productIds != null and productIds.size() > 0'>
            AND t7.productId in
            <foreach collection="productIds" open="(" close=")" separator="," item = "item">
                #{item}
            </foreach>
        </if>
        <if test='recommendId != null'>
            AND t7.recommendId=#{recommendId}
        </if>
        <if test='customerAdminId != null'>
            AND t7.customerAdminId=#{customerAdminId}
        </if>
        <if test='companyId != null'>
            AND t3.companyId=#{companyId}
        </if>
        <if test='productAttrCode != null and productAttrCode=="person"'>
            AND t3.productAttrCode=#{productAttrCode}
        </if>
        <if test='productAttrCode != null and productAttrCode=="group"'>
            AND t3.productAttrCode in ('group','employer')
        </if>

        <if test='channel != null'>
            AND t7.channel=#{channel}
        </if>
        <if test='accountDateStart != null'>
            <![CDATA[ AND t1.account_time>=#{accountDateStart}  ]]>
        </if>
        <if test='accountDateEnd != null'>
            <![CDATA[ AND t1.account_time<=#{accountDateEnd}  ]]>
        </if>
        <if test='appStatus != null '>
            AND t1.policy_status = #{appStatus}
        </if>

        <if test='userId != null'>
            AND t7.recommendId=#{userId}
        </if>

        <if test='orgPath !=null '>
            and t2.orgCode in (select orgCode from organization where orgPath like CONCAT(#{orgPath},'%'))
        </if>
        <if test="reconciliationStatus != null">
            <if test="reconciliationStatus == 0">
                and t11.reconciliation_status = 0
            </if>
            <if test="reconciliationStatus == 1">
                and t11.reconciliation_status is null
            </if>
            <if test="reconciliationStatus == 2">
                and t11.reconciliation_status in (1,2,3,4,5,6,7,9)
            </if>
        </if>
        <if test="addCommissionStatus != null">
            <if test="addCommissionStatus == 0">
                and t1.order_id in (select order_id from sm_add_commission_detail t12
                where t12.order_id = t1.order_id AND t12.insured_id_number = t1.insured_id_number
                AND t12.policy_no = t1.policy_no AND t12.plan_id = t1.plan_id and t12.term_num = t1.term_num
                and t12.proportion != 0 group by  order_id, `policy_no` , `insured_id_number` , `plan_id` , `term_num`)
            </if>
            <if test="addCommissionStatus == 1">
                and t1.order_id not in (select order_id from sm_add_commission_detail t12
                where t12.order_id = t1.order_id AND t12.insured_id_number = t1.insured_id_number
                AND t12.policy_no = t1.policy_no AND t12.plan_id = t1.plan_id and t12.term_num = t1.term_num
                and t12.proportion != 0 group by  order_id, `policy_no` , `insured_id_number` , `plan_id` , `term_num`)
            </if>
        </if>
        GROUP BY t1.policy_no, t1.insured_id_number ,t1.policy_status,t1.term_num) a

    </select>
    <select id="queryLongCommissionSummary" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmOrderSummaryVO">
        SELECT
        sum(if(t1.policy_status='4',round(t1.amount*-1,2),t1.amount)) as orderAmount,
        SUM(if(sot.id is null,t1.payment_amount,t1.payment_amount * .7)) AS paymentAmount,
        SUM(t1.settlement_amount) AS settlementAmount,
        ROUND(
            SUM(case
                when (t1.policy_status = '4' and ifnull(t9.add_commission_amount, 0) > 0 and ifnull(t1.original_amount, 0) > 0) then -1*t1.original_amount*t9.proportion/100
                when (t1.policy_status = '4' and ifnull(t9.add_commission_amount, 0) > 0 and ifnull(t1.original_amount, 0) = 0) then -1*t1.amount*t9.proportion/100
                when (t1.policy_status = '1' and 0 > ifnull(t9.add_commission_amount, 0)) then -1*t9.add_commission_amount
                else t9.add_commission_amount end)
        ,2) AS addCommissionAmount,
        SUM(IFNULL(t1.conversion_amount, 0)) as convertedAmount
        FROM
        sm_commission_detail t1
        LEFT JOIN sm_order t7
        ON t7.fhOrderId = t1.order_id
        LEFT JOIN auth_user t2
        ON t7.recommendId = t2.userId AND t2.enabled_flag = 0
        LEFT JOIN auth_user t8
        ON t7.customerAdminId = t8.userId AND t8.enabled_flag = 0
        LEFT JOIN sm_product t3
        ON t7.productId = t3.id
        LEFT JOIN sm_order_insured t6
        on t6.fhOrderId = t1.order_id and t1.insured_id_number= t6.idNumber and t1.policy_status = t6.appStatus
        left join sm_order_talk sot on sot.fh_order_id = t1.order_id
        left join sm_order_applicant soa on soa.fhOrderId = t7.fhOrderId
        LEFT JOIN sm_add_commission_detail_sum t9 ON t9.order_id = t1.order_id AND t9.insured_id_number = t1.insured_id_number AND t9.policy_no = t1.policy_no and t9.term_num = t1.term_num
        left join sm_reconciliation_policy_cache t11
        on t11.policy_no = REVERSE(SUBSTR( REVERSE( t1.`policy_No` ) FROM INSTR( REVERSE( t1.`policy_No` ), '_' ) + 1 )) and t11.app_status = t1.policy_status
        left join sm_order_village_activity t12 on t7.fhOrderId=t12.fh_order_id and t12.type=100
        WHERE 1=1 and t3.long_insurance = 0
        <if test='villageActivity != null'>
            <if test="villageActivity == 1">
-                and (t12.product_activity_code is null or t12.product_activity_code ='')
            </if>
            <if test="villageActivity == 0">
                and t12.product_activity_code is not null and t12.product_activity_code !=''
            </if>
        </if>
        <if test='createDateStart != null'>
            <![CDATA[ AND t7.create_time>=#{createDateStart} ]]>
        </if>
        <if test='createDateEnd != null'>
            <![CDATA[ AND t7.create_time<=#{createDateEnd} ]]>
        </if>
        <if test='paymentDateStart != null'>
            <![CDATA[ AND t7.paymentTime>=#{paymentDateStart} ]]>
        </if>
        <if test='paymentDateEnd != null'>
            <![CDATA[ AND t7.paymentTime<=#{paymentDateEnd} ]]>
        </if>
        <if test='orderNo != null'>
            <if test="fullMatch">
                AND t1.order_id = #{orderNo}
            </if>
            <if test="!fullMatch">
                AND t1.order_id LIKE CONCAT(#{orderNo},'%')
            </if>
        </if>
        <if test='talkOrder != null'>
            <if test="talkOrder == true">
                and sot.id > 0
            </if>
            <if test="talkOrder == false">
                and sot.id is null
            </if>
        </if>
        <if test='inviteName != null'>
            and sot.invite_name = #{inviteName}
        </if>
        <if test='policyNo !=  null'>
            AND t1.policy_no like CONCAT(#{policyNo},'%')
        </if>
        <if test="termNum != null ">
            and t1.term_num = #{termNum}
        </if>
        <if test='regionName != null'>
            AND t2.regionName=#{regionName}
        </if>
        <if test='orgName != null'>
            AND t2.organizationFullName=#{orgName}
        </if>

        <if test='applicantdName != null'>
            AND
            <if test="applicantdType == 1">
                soa.personName LIKE CONCAT(#{applicantdName},'%')
            </if>
            <if test="applicantdType == 2">
                soa.idNumber LIKE CONCAT(#{applicantdName},'%')
            </if>
            <if test="applicantdType == 3">
                soa.cellPhone LIKE CONCAT(#{applicantdName},'%')
            </if>
        </if>
        <if test='insuredName != null'>
            AND
            <if test="insuredType == 1">
                t6.personName LIKE CONCAT(#{insuredName},'%')
            </if>
            <if test="insuredType == 2">
                t6.idNumber LIKE CONCAT(#{insuredName},'%')
            </if>
            <if test="insuredType == 3">
                t6.cellPhone LIKE CONCAT(#{insuredName},'%')
            </if>
        </if>
        <if test='productId != null'>
            AND t7.productId=#{productId}
        </if>
        <if test='productIds != null and productIds.size() > 0'>
            AND t7.productId in
            <foreach collection="productIds" open="(" close=")" separator="," item = "item">
                #{item}
            </foreach>
        </if>
        <if test='recommendId != null'>
            AND t7.recommendId=#{recommendId}
        </if>
        <if test='customerAdminId != null'>
            AND t7.customerAdminId=#{customerAdminId}
        </if>
        <if test='companyId != null'>
            AND t3.companyId=#{companyId}
        </if>
        <if test='productAttrCode != null and productAttrCode=="person"'>
            AND t3.productAttrCode=#{productAttrCode}
        </if>
        <if test='productAttrCode != null and productAttrCode=="group"'>
            AND t3.productAttrCode in ('group','employer')
        </if>

        <if test='channel != null'>
            AND t7.channel=#{channel}
        </if>
        <if test='accountDateStart != null'>
            <![CDATA[ AND t1.account_time>=#{accountDateStart}  ]]>
        </if>
        <if test='accountDateEnd != null'>
            <![CDATA[ AND t1.account_time<=#{accountDateEnd}  ]]>
        </if>
        <if test='appStatus != null '>
            AND t1.policy_status = #{appStatus}
        </if>

        <if test='userId != null'>
            AND t7.recommendId=#{userId}
        </if>

        <if test='orgPath !=null '>
            and t2.orgCode in (select orgCode from organization where orgPath like CONCAT(#{orgPath},'%'))
        </if>
        <if test="reconciliationStatus != null">
            <if test="reconciliationStatus == 0">
                and t11.reconciliation_status = 0
            </if>
            <if test="reconciliationStatus == 1">
                and t11.reconciliation_status is null
            </if>
            <if test="reconciliationStatus == 2">
                and t11.reconciliation_status in (1,2,3,4,5,6,7,9)
            </if>
        </if>
        <if test="addCommissionStatus != null">
            <if test="addCommissionStatus == 0">
                and t9.add_commission_amount > 0
            </if>
            <if test="addCommissionStatus == 1">
                and (t9.add_commission_amount is null or t9.add_commission_amount = 0)
            </if>
        </if>
    </select>
    <update id="batchCorrectIdNumber">
        <foreach collection="data" separator=";" item="order">
            update sm_commission_detail
            set insured_id_number = #{order.newValue}
            where order_id = #{order.orderId}
            and   policy_no = #{order.policyNo}
            and   enabled_flag = 0
        </foreach>
    </update>

    <update id="batchCorrectRecommendMan">
        <foreach collection="data" separator=";" item="order">
            update sm_commission_detail
            set commission_user_id = #{order.newValue}
            <if test="order.paymentTime!=null ">
                , account_time = #{order.paymentTime}
            </if>
            where order_id = #{order.orderId}
            and   policy_no = #{order.policyNo}
            and   enabled_flag = 0
        </foreach>
    </update>

    <update id="updateCommissionDetail">
        <if test="redo.commissionSceneTypeEnum.getCode() != 'endorsementNo'">
            update sm_commission_detail
            set ${redo.commissionSceneTypeEnum.fieldName} = #{redo.afterFiledValue}
            where order_id = #{redo.orderId}
            <if test="redo.beforeFiledValue != null and redo.beforeFiledValue != ''">
                and ${redo.commissionSceneTypeEnum.fieldName} = #{redo.beforeFiledValue}
            </if>
            <if test="redo.termNum != null and redo.termNum != 0">
                and term_num = #{redo.termNum}
            </if>
            ;
            update sm_commission_detail_item
            set ${redo.commissionSceneTypeEnum.fieldName} = #{redo.afterFiledValue}
            where order_id = #{redo.orderId}
            <if test="redo.beforeFiledValue != null and redo.beforeFiledValue != ''">
                and ${redo.commissionSceneTypeEnum.fieldName} = #{redo.beforeFiledValue}
            </if>
            <if test="redo.termNum != null and redo.termNum != 0">
                and term_num = #{redo.termNum}
            </if>
            ;
            update sm_commission_detail_performance
            set ${redo.commissionSceneTypeEnum.fieldName} = #{redo.afterFiledValue}
            <if test="redo.commissionSceneTypeEnum.getCode() == 'planId'">
                ,data_id = concat(order_id, '|', policy_no, '|', insured_id_number, '|', #{redo.afterFiledValue}, '|', risk_id, '|', term_num, '|',policy_status)
            </if>
            where order_id = #{redo.orderId}
            <if test="redo.beforeFiledValue != null and redo.beforeFiledValue != ''">
                and ${redo.commissionSceneTypeEnum.fieldName} = #{redo.beforeFiledValue}
            </if>
            <if test="redo.termNum != null and redo.termNum != 0">
                and term_num = #{redo.termNum}
            </if>
            ;
        </if>
        <if test="redo.commissionSceneTypeEnum.getCode == 'endorsementNo'">
            update sm_commission_detail
            set policy_no=REPLACE(policy_no,#{redo.beforeFiledValue},#{redo.afterFiledValue})
            where order_id = #{redo.orderId};
            update sm_commission_detail_item
            set policy_no=REPLACE(policy_no,#{redo.beforeFiledValue},#{redo.afterFiledValue})
            where order_id = #{redo.orderId};
            update sm_commission_detail_performance
            set policy_no=REPLACE(policy_no,#{redo.beforeFiledValue},#{redo.afterFiledValue})
            where order_id = #{redo.orderId};
        </if>
    </update>

    <update id="updateCommissionRate">
            update sm_commission_detail set
            <if test="redo.commissionType == 1">
                `settlement_rate` = #{redo.commissionRate}
                ,settlement_amount = amount*#{redo.commissionRate}/100
            </if>
            <if test="redo.commissionType == 2">
                `payment_rate` = #{redo.commissionRate}
                ,payment_amount = amount*#{redo.commissionRate}/100
            </if>
            <if test="redo.commissionType == 3">
                `conversion_rate` = #{redo.commissionRate}
                ,conversion_amount = amount*#{redo.commissionRate}/100
            </if>
            where order_id = #{redo.orderId};
            update sm_commission_detail_item
            set commission_rate=#{redo.commissionRate},commission_amount = plan_amount*#{redo.commissionRate}/100
            where order_id = #{redo.orderId} and commission_type = #{redo.commissionType};
    </update>

    <delete id="deleteCommissionDetail">
            delete from sm_commission_detail where order_id = #{redo.orderId}
            <if test="redo.policyNo != null and redo.policyNo != ''">
                and policy_no = #{redo.policyNo}
            </if>
            <if test="redo.termNum != null and redo.termNum != 0">
                and term_num = #{redo.termNum}
            </if>
            <if test="redo.insuredIdNumber != null and redo.insuredIdNumber != ''">
                and insured_id_number = #{redo.insuredIdNumber}
            </if>
            <if test="redo.policyStatus != null and redo.policyStatus != ''">
                and policy_status = #{redo.policyStatus}
            </if>
            and enabled_flag = 0;
            delete from sm_commission_detail_item where order_id = #{redo.orderId}
            <if test="redo.policyNo != null and redo.policyNo != ''">
                and policy_no = #{redo.policyNo}
            </if>
            <if test="redo.termNum != null and redo.termNum != 0">
                and term_num = #{redo.termNum}
            </if>
            <if test="redo.insuredIdNumber != null and redo.insuredIdNumber != ''">
                and insured_id_number = #{redo.insuredIdNumber}
            </if>
            <if test="redo.policyStatus != null and redo.policyStatus != ''">
                and policy_status = #{redo.policyStatus}
            </if>
            and enabled_flag = 0;
            delete from sm_commission_detail_performance where order_id = #{redo.orderId}
            <if test="redo.policyNo != null and redo.policyNo != ''">
                and policy_no = #{redo.policyNo}
            </if>
            <if test="redo.termNum != null and redo.termNum != 0">
                and term_num = #{redo.termNum}
            </if>
            <if test="redo.insuredIdNumber != null and redo.insuredIdNumber != ''">
                and insured_id_number = #{redo.insuredIdNumber}
            </if>
            <if test="redo.policyStatus != null and redo.policyStatus != ''">
                and policy_status = #{redo.policyStatus}
            </if>
            and enabled_flag = 0;
    </delete>

    <delete id="deleteEndorsementCommissionDetail">
            delete from sm_commission_detail where policy_no like CONCAT('%',#{redo.policyNo},'%')
            <if test="redo.termNum != null and redo.termNum != 0">
                and term_num = #{redo.termNum}
            </if>
            <if test="redo.insuredIdNumber != null and redo.insuredIdNumber != ''">
                and insured_id_number = #{redo.insuredIdNumber}
            </if>
            <if test="redo.policyStatus != null and redo.policyStatus != ''">
                and policy_status = #{redo.policyStatus}
            </if>
            and enabled_flag = 0;
            delete from sm_commission_detail_item where policy_no like CONCAT('%',#{redo.policyNo},'%')
            <if test="redo.termNum != null and redo.termNum != 0">
                and term_num = #{redo.termNum}
            </if>
            <if test="redo.insuredIdNumber != null and redo.insuredIdNumber != ''">
                and insured_id_number = #{redo.insuredIdNumber}
            </if>
            <if test="redo.policyStatus != null and redo.policyStatus != ''">
                and policy_status = #{redo.policyStatus}
            </if>
            and enabled_flag = 0;
            delete from sm_commission_detail_performance where policy_no like CONCAT('%',#{redo.policyNo},'%')
            <if test="redo.termNum != null and redo.termNum != 0">
                and term_num = #{redo.termNum}
            </if>
            <if test="redo.insuredIdNumber != null and redo.insuredIdNumber != ''">
                and insured_id_number = #{redo.insuredIdNumber}
            </if>
            <if test="redo.policyStatus != null and redo.policyStatus != ''">
                and policy_status = #{redo.policyStatus}
            </if>
            and enabled_flag = 0;
    </delete>

    <insert id="backupsData">
        insert into
        sm_commission_detail_backups (
            commission_detail_id,
            order_id,
            policy_no,
            insured_id_number,
            plan_id,
            risk_id,
            account_time,
            policy_status,
            amount,
            payment_commission_id,
            payment_rate,
            payment_amount,
            settlement_commission_id,
            settlement_rate,
            settlement_amount,
            conversion_commission_id,
            conversion_rate,
            conversion_amount,
            enabled_flag,
            create_by,
            update_by,
            term_num,
            payment_risk_json,
            settlement_risk_json,
            conversion_risk_json,
            commission_user_id,
            business_type,
            business_id,
            import_flag,
            original_amount
        )
        select
            id,
            order_id,
            policy_no,
            insured_id_number,
            plan_id,
            risk_id,
            account_time,
            policy_status,
            amount,
            payment_commission_id,
            payment_rate,
            payment_amount,
            settlement_commission_id,
            settlement_rate,
            settlement_amount,
            conversion_commission_id,
            conversion_rate,
            conversion_amount,
            enabled_flag,
            create_by,
            update_by,
            term_num,
            payment_risk_json,
            settlement_risk_json,
            conversion_risk_json,
            commission_user_id,
            business_type,
            business_id,
            import_flag,
            original_amount
        from
            sm_commission_detail
        where
            order_id = #{orderId}
        ;
        insert into
        sm_commission_detail_item_backups (
            item_id,
            order_id,
            policy_no,
            insured_id_number,
            plan_id,
            risk_id,
            risk_name,
            account_time,
            policy_status,
            risk_status,
            calc_way,
            plan_amount,
            amount,
            term_num,
            commission_type,
            commission_id,
            commission_rate,
            commission_amount,
            commission_user_id,
            enabled_flag,
            create_by,
            update_by,
            business_type,
            business_id,
            import_flag,
            original_amount
        )
        select
            id,
            order_id,
            policy_no,
            insured_id_number,
            plan_id,
            risk_id,
            risk_name,
            account_time,
            policy_status,
            risk_status,
            calc_way,
            plan_amount,
            amount,
            term_num,
            commission_type,
            commission_id,
            commission_rate,
            commission_amount,
            commission_user_id,
            enabled_flag,
            create_by,
            update_by,
            business_type,
            business_id,
            import_flag,
            original_amount
        from sm_commission_detail_item
        where order_id = #{orderId}
        ;
        insert into sm_commission_detail_performance_backups (
            performance_id,
            order_id,
            policy_no,
            insured_id_number,
            plan_id,
            risk_id,
            account_time,
            policy_status,
            amount,
            term_num,
            performance_rate,
            performance_amount,
            commission_user_id,
            enabled_flag,
            data_id
        )
        select
            id,
            order_id,
            policy_no,
            insured_id_number,
            plan_id,
            risk_id,
            account_time,
            policy_status,
            amount,
            term_num,
            performance_rate,
            performance_amount,
            commission_user_id,
            enabled_flag,
            data_id
        from sm_commission_detail_performance
        where order_id = #{orderId}
        ;
    </insert>
</mapper>
