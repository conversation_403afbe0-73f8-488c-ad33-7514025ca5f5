<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.commission.SmCommissionDetailPerformanceMapper">
<!--    22年实收车险逻辑-->
    <insert id="insertAutoPolicy">
<![CDATA[
     insert into sm_commission_detail_performance(order_id, policy_no, insured_id_number, plan_id, risk_id, account_time,
                                             policy_status,
                                             amount, term_num,
                                             performance_rate, performance_amount,
                                             commission_user_id, data_id)
with tmp as (select max(account_time) end_time, orgCode, lag_amt
             from (select t1.account_time,
                          orgCode,
                          t.order_id,
                          sum(if(t.policy_status = 4, -t.amount, t.amount))              order_amount,
                          sum(sum(if(t.policy_status = 4, -t.amount, t.amount)))
                                 over (partition by orgCode order by t.order_id asc, t.account_time asc) lag_amt
                   from sm_commission_detail t
                            left join sm_plan p on t.plan_id = p.id
                            inner join auth_user u on t.commission_user_id = u.userId and u.enabled_flag = 0
                            left join sm_order so on so.fhOrderId = t.order_id
                            left join sm_order_insured soi
                                      on soi.fhOrderId = t.order_id and soi.idNumber = t.insured_id_number
                            left join sm_commission_detail t1 on t.order_id = t1.order_id and t1.policy_status = '1'
                   where so.paymentTime between #{start} and #{end}
                     and t.account_time between #{start} and #{end}
                     and u.orgCode in (select org_code from sm_commission_tmp_config_4)
                     and p.productId in (select spq.id from sm_product spq where spq.productType = 'CX')
                   group by t.account_time, orgCode
                   order by t.account_time) t
                      inner join
                  sm_commission_tmp_config_4 sctc on sctc.org_code = t.orgCode
             where lag_amt < sctc.scale_val
             group by orgCode)
select t.order_id,
       t.policy_no,
       t.insured_id_number,
       t.plan_id,
       t.risk_id,
       t.account_time,
       t.policy_status,
       t.amount,
       t.term_num,
       -- 如果是配置外的数据就是0%
       if(tmp.end_time is null, 10,
           -- 如果是配置内的数据  并且是额度内计算100% 否则记录10
          if(t.account_time <= tmp.end_time, 100, 10))                                           performance_rate,
       if(tmp.end_time is null, t.amount * .1, if(t.account_time <= tmp.end_time, t.amount, t.amount * .1)) performance_amount,

       t.commission_user_id,
       concat(order_id, '|', t.policy_no, '|', insured_id_number, '|', plan_id, '|', risk_id, '|', term_num, '|',
              policy_status) as                                                                data_id
from sm_commission_detail t
         left join auth_user u on t.commission_user_id = u.userId and u.enabled_flag = 0
         left join sm_order_insured soi on soi.idNumber = t.insured_id_number and t.order_id = soi.fhOrderId
         left join tmp on u.orgCode = tmp.orgCode
where t.account_time between #{start} and #{end}
  and t.policy_status = '1'
  and t.plan_id in (select p1.id
                    from sm_plan p1
                             inner join sm_product p2 on p1.productId = p2.id
                    where p2.productType = 'CX')
ON DUPLICATE KEY UPDATE performance_amount = values(performance_amount),
                        performance_rate=values(performance_rate),
                        amount             = values(amount)
        ]]>
    </insert>

    <insert id="insertAutoPolicyBoundaryValue">
<![CDATA[
        insert into sm_commission_detail_performance(order_id, policy_no, insured_id_number, plan_id, risk_id, account_time,
                                                     policy_status,
                                                     amount, term_num,
                                                     performance_rate, performance_amount,
                                                     commission_user_id, data_id)
        with tmp as (select min(account_time) end_time, orgCode, lag_amt
                     from (select account_time,
                                  orgCode,
                                  sum(if(policy_status = 4, -amount, amount))              order_amount,
                                  sum(sum(if(policy_status = 4, -amount, amount)))
                                                                                           over (partition by orgCode order by t.order_id asc, account_time asc) lag_amt
                                ,lead(if(policy_status = 4, -amount, amount)) over (partition by orgCode order by t.order_id asc, account_time asc)            next_amt
                           from sm_commission_detail t
                                    left join sm_plan p on t.plan_id = p.id
                                    inner join auth_user u on t.commission_user_id = u.userId and u.enabled_flag = 0
                                    left join sm_order so on so.fhOrderId = t.order_id
                                    left join sm_order_insured soi
                                              on soi.fhOrderId = t.order_id and soi.idNumber = t.insured_id_number
                           where so.paymentTime between #{start} and #{end}
                             and t.account_time between #{start} and #{end}
                             and u.orgCode in (select org_code from sm_commission_tmp_config_4)
                             and p.productId in (select spq.id from sm_product spq where spq.productType = 'CX')
                           group by account_time, orgCode
                           order by account_time) t
                              inner join
                          sm_commission_tmp_config_4 sctc on sctc.org_code = t.orgCode
                     where lag_amt >= sctc.scale_val and (next_amt>0 or next_amt is null)
                     group by orgCode)
        select t.order_id,
               t.policy_no,
               t.insured_id_number,
               t.plan_id,
               t.risk_id,
               t.account_time,
               t.policy_status,
               t.amount,
               t.term_num,
               -- 如果是配置外的数据就是0%
               100 performance_rate,
               t.amount performance_amount,

               t.commission_user_id,
               concat(order_id, '|', t.policy_no, '|', insured_id_number, '|', plan_id, '|', risk_id, '|', term_num, '|',
                      policy_status) as                                                                data_id
        from sm_commission_detail t
                 left join auth_user u on t.commission_user_id = u.userId and u.enabled_flag = 0
                 left join sm_order_insured soi on soi.idNumber = t.insured_id_number and t.order_id = soi.fhOrderId
                 left join tmp on u.orgCode = tmp.orgCode
        where tmp.end_time = t.account_time
          and t.policy_status = '1'
          and t.plan_id in (select p1.id
                            from sm_plan p1
                                     inner join sm_product p2 on p1.productId = p2.id
                            where p2.productType = 'CX')
            ON DUPLICATE KEY UPDATE performance_amount = values(performance_amount),
            performance_rate=values(performance_rate),
                                 amount             = values(amount)
        ]]>
    </insert>

<!--    23年实收车险逻辑-->
<!--    <insert id="insertAutoPolicy">-->
<!--<![CDATA[-->
<!--        insert into sm_commission_detail_performance(order_id, policy_no, insured_id_number, plan_id, risk_id, account_time,-->
<!--                                                     policy_status,-->
<!--                                                     amount, term_num,-->
<!--                                                     performance_rate, performance_amount,-->
<!--                                                     commission_user_id, data_id)-->
<!--        with tmp as (select max(account_time) end_time, regionName, order_amount,lag_amt-->
<!--            from (select t1.account_time,-->
<!--                         regionName,-->
<!--                         t.policy_status policy_status,-->
<!--                         sum(if(t.policy_status = 4, -t.amount, t.amount))              order_amount,-->
<!--                         sum(sum(if(t.policy_status = 4, -t.amount, t.amount)))-->
<!--                             over (order by t.order_id asc,t.account_time asc) lag_amt-->
<!--                  from sm_commission_detail t-->
<!--                           left join sm_plan p on t.plan_id = p.id-->
<!--                           inner join auth_user u on t.commission_user_id = u.userId and u.enabled_flag = 0-->
<!--                           left join sm_order so on so.fhOrderId = t.order_id-->
<!--                           left join sm_order_insured soi-->
<!--                                     on soi.fhOrderId = t.order_id and soi.idNumber = t.insured_id_number-->
<!--                           left join sm_commission_detail t1 on t.order_id = t1.order_id and t1.policy_status = '1'-->
<!--                  where so.paymentTime between #{start} and #{end}-->
<!--                    and t.account_time between #{start} and #{end}-->
<!--                    and p.productId in (select spq.id from sm_product spq where spq.productType = 'CX')-->
<!--                  group by t.account_time,regionName) t-->

<!--            where lag_amt <= ********-->

<!--            group by regionName)-->
<!--        select t.order_id,-->
<!--               t.policy_no,-->
<!--               t.insured_id_number,-->
<!--               t.plan_id,-->
<!--               t.risk_id,-->
<!--               t.account_time,-->
<!--               t.policy_status,-->
<!--               t.amount,-->
<!--               t.term_num,-->
<!--               &#45;&#45; 如果是配置内的数据  并且是额度内计算100% 否则记录10-->
<!--               if(tmp.end_time is null, 0,if(t.account_time <= tmp.end_time or tmp.end_time is null, 100, 10)) performance_rate,-->
<!--               if(tmp.end_time is null, 0,if(t.account_time <= tmp.end_time or tmp.end_time is null, t.amount, t.amount * .1)) performance_amount,-->

<!--               t.commission_user_id,-->
<!--               concat(order_id, '|', t.policy_no, '|', insured_id_number, '|', plan_id, '|', risk_id, '|', term_num, '|',-->
<!--                      policy_status) as                                                                data_id-->
<!--        from sm_commission_detail t-->
<!--                 left join auth_user u on t.commission_user_id = u.userId and u.enabled_flag = 0-->
<!--                 left join sm_order_insured soi on soi.idNumber = t.insured_id_number and t.order_id = soi.fhOrderId-->
<!--                 left join tmp on u.regionName = tmp.regionName-->
<!--        where t.account_time between #{start} and #{end}-->
<!--          and t.policy_status = '1'-->
<!--          and t.plan_id in (select p1.id-->
<!--                            from sm_plan p1-->
<!--                                     inner join sm_product p2 on p1.productId = p2.id-->
<!--                            where p2.productType = 'CX')-->
<!--            ON DUPLICATE KEY UPDATE performance_amount = values(performance_amount),-->
<!--            performance_rate=values(performance_rate),-->
<!--                                 amount             = values(amount)-->
<!--        ]]>-->
<!--    </insert>-->

<!--    <insert id="insertAutoPolicyBoundaryValue">-->
<!--<![CDATA[-->
<!--        insert into sm_commission_detail_performance(order_id, policy_no, insured_id_number, plan_id, risk_id, account_time,-->
<!--                                                     policy_status,-->
<!--                                                     amount, term_num,-->
<!--                                                     performance_rate, performance_amount,-->
<!--                                                     commission_user_id, data_id)-->
<!--        select t.order_id,-->
<!--               t.policy_no,-->
<!--               t.insured_id_number,-->
<!--               t.plan_id,-->
<!--               t.risk_id,-->
<!--               t.account_time,-->
<!--               t.policy_status,-->
<!--               t.amount,-->
<!--               t.term_num,-->
<!--               100                                          performance_rate,-->
<!--               t.amount performance_amount,-->

<!--               t.commission_user_id,-->
<!--               concat(order_id, '|', t.policy_no, '|', insured_id_number, '|', plan_id, '|', risk_id, '|', term_num, '|',-->
<!--                      policy_status) as                                                                data_id-->
<!--        from sm_commission_detail t-->
<!--                 left join auth_user u on t.commission_user_id = u.userId and u.enabled_flag = 0-->
<!--                 left join sm_order_insured soi on soi.idNumber = t.insured_id_number and t.order_id = soi.fhOrderId-->
<!--        where t.account_time > #{date}-->
<!--          and t.policy_status = '1'-->
<!--          and t.plan_id in (select p1.id-->
<!--                            from sm_plan p1-->
<!--                                     inner join sm_product p2 on p1.productId = p2.id-->
<!--                            where p2.productType = 'CX')-->
<!--            order by t.account_time asc-->
<!--            limit 1-->
<!--            ON DUPLICATE KEY UPDATE performance_amount = values(performance_amount),-->
<!--            performance_rate=values(performance_rate),-->
<!--                                 amount             = values(amount)-->
<!--        ]]>-->
<!--    </insert>-->
    <update id="updatePolicyLong36">

        insert into sm_commission_detail_performance(order_id, policy_no, insured_id_number, plan_id, risk_id, account_time,
                                                     policy_status,
                                                     amount,
                                                     term_num,
                                                     performance_rate, performance_amount,
                                                     commission_user_id, data_id)
        select t.order_id,
               ifnull(t.policy_no, '')      policy_no,
               insured_id_number,
               plan_id,
               risk_id,
               account_time,
               policy_status,
               amount,
               t.term_num,
               (case
                    when t2.r13 >= 1 then 100
                    when t2.r13 >= .9 then 70
                    when t2.r13 >= .85 then 60
                    else 50 end)          performance_rate,
               amount * (case
                             when t2.r13 >= 1 then 1
                             when t2.r13 >= .9 then .7
                             when t2.r13 >= .85 then .6
                             else .5 end) performance_amount,
               commission_user_id
                ,
               concat(t.order_id, '|', t.policy_no, '|', insured_id_number, '|', plan_id, '|', risk_id, '|',
                      t.term_num, '|',
                      policy_status) as   data_id
        from sm_commission_detail t
                 left join sm_order_renewal_term t1 on t1.policy_no = t.policy_no
                 left join auth_user a on t.commission_user_id = a.userId and a.enabled_flag = 0
                 left join temp_miner_r13 t2
                           on t2.org_name = a.organizationName and t2.due_month = date_format(t1.due_time, '%Y-%m')
                               and t1.term_num = t.term_num
        where t.account_time between #{start}
            and #{end}
          and t2.due_month between '2023-03' and '2023-06'
          and t.plan_id in (select p1.id
                            from sm_plan p1
                                     inner join sm_product p2 on p1.productId = p2.id
                            where p2.productType != 'CX')
          and t.term_num = 2
        ON DUPLICATE KEY
            UPDATE performance_amount =
                       values(performance_amount),
                   performance_rate=
                       values(performance_rate),
                   amount             =
                       values(amount);
    </update>

    <select id="maxTimeOfAuto" resultType="com.cfpamf.ms.insur.admin.pojo.dto.commission.SmCommissionDetailHandleDTO">
        <![CDATA[
        select account_time,lag_amt
        from (select t1.account_time,
                     regionName,
                     t.policy_status policy_status,
                     sum(if(t.policy_status = 4, -t.amount, t.amount))              order_amount,
                     sum(sum(if(t.policy_status = 4, -t.amount, t.amount)))
                         over (order by t.order_id asc,t.account_time asc) lag_amt
              from sm_commission_detail t
                       left join sm_plan p on t.plan_id = p.id
                       inner join auth_user u on t.commission_user_id = u.userId and u.enabled_flag = 0
                       left join sm_order so on so.fhOrderId = t.order_id
                       left join sm_order_insured soi
                                 on soi.fhOrderId = t.order_id and soi.idNumber = t.insured_id_number
                       left join sm_commission_detail t1 on t.order_id = t1.order_id and t1.policy_status = '1'
              where so.paymentTime between #{start} and #{end}
                and t.account_time between #{start} and #{end}
                and p.productId in (select spq.id from sm_product spq where spq.productType = 'CX')
              group by t.account_time,regionName) t
        where lag_amt <= ********
        order by account_time desc,lag_amt desc limit 1
        ]]>
    </select>

    <insert id="insertAutoPolicyCancel">
<![CDATA[
       insert into sm_commission_detail_performance(order_id, policy_no, insured_id_number, plan_id, risk_id, account_time,
                                             policy_status,
                                             amount, term_num,
                                             performance_rate, performance_amount,
                                             commission_user_id, data_id)
with tmp as (select concat(order_id, '|', t.policy_no, '|', insured_id_number, '|', plan_id, '|', risk_id, '|',
                           term_num, '|1') as data_id, t.amount amount
             from sm_commission_detail t
             where t.account_time between #{start} and #{end}
               and t.policy_status = '4'
               and t.plan_id in (select p1.id
                                 from sm_plan p1
                                          inner join sm_product p2 on p1.productId = p2.id
                                 where p2.productType = 'CX'))

select order_id,
       policy_no,
       insured_id_number,
       plan_id,
       risk_id,
       account_time,
       '4' policy_status,
       tmp.amount,
       term_num,
       performance_rate,
       tmp.amount*performance_rate/100,
       commission_user_id,
       concat(order_id, '|', t.policy_no, '|', insured_id_number, '|', plan_id, '|', risk_id, '|',
              term_num, '|4')
from sm_commission_detail_performance t
         left join tmp on tmp.data_id = t.data_id
       where tmp.data_id is not null
ON DUPLICATE KEY
    UPDATE performance_amount =values(performance_amount),
           performance_rate=values(performance_rate),
           amount    =values(amount)
        ]]>
    </insert>

    <insert id="insertPolicyNotCx">
      <![CDATA[
      insert into sm_commission_detail_performance(order_id, policy_no, insured_id_number, plan_id, risk_id, account_time,
                                             policy_status,
                                             amount,
                                             term_num,
                                             performance_rate, performance_amount,
                                             commission_user_id, data_id)
select order_id,
       ifnull(policy_no, '')     policy_no,
       insured_id_number,
       plan_id,
       risk_id,
       account_time,
       policy_status,
       amount,
       term_num,
       (case term_num
            when 2 then 50
            when 3 then 30
            else 100 end)        performance_rate,
       amount * (case term_num
                     when 2 then .5
                     when 3 then .3
                     else 1 end) performance_amount,
       commission_user_id
        ,
       concat(order_id, '|', t.policy_no, '|', insured_id_number, '|', plan_id, '|', risk_id, '|',
              term_num, '|',
              policy_status) as  data_id
from sm_commission_detail t
where t.account_time between #{start} and #{end}
  and t.plan_id in (select p1.id
                    from sm_plan p1
                             inner join sm_product p2 on p1.productId = p2.id
                    where p2.productType != 'CX')
ON DUPLICATE KEY
    UPDATE performance_amount = values(performance_amount),
           performance_rate= values(performance_rate),
           amount  =values(amount)
      ]]>
    </insert>
</mapper>
