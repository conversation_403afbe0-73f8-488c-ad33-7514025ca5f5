<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.AmPolicyMapper">

    <insert id="insertPolicy" useGeneratedKeys="true">
       INSERT INTO am_policy (taskId,prvId,prvName,respCode,errorMsg,channelId,channelUserId,taskState,taskStateDescription,carLicenseNo,carVehicleName,
         carProperty,carOwnerName,carOwnerPhone,applicantName,applicantIdcardType,applicantIdcardNo,applicantPhone,applicantEmail,insuredName,insuredIdcardType,
         insuredIdcardNo,insuredPhone,insuredEmail,efcInsureStartDate,efcInsureEndDate,efcInsureAmount,efcInsurePremium,efcInsurePolicyNo,efcInsureDiscountRate,
         taxInsureFee,taxInsureLateFee,bizInsureStartDate,bizInsureEndDate,bizInsurePremium,bizInsureDiscountRate,bizInsurePolicyNo,bizInsureNfcPremium,insureTotalPremium,
         msgType,deliveryType,deliveryName,deliveryPhone,deliveryProvince,deliveryCity,deliveryArea,deliveryAddress,deliveryZip,deliveryExpressCompanyName,deliveryExpressNumber,
         deliverySyElecPolicyFilePath,deliveryJpElecPolicyFilePath,deliveryOutDept,inspectionCode,quoteValidTime,payValidTime,srBizScore,srTrafficScore,srBizRate,srTrafficRate,
         create_time, update_time, enabled_flag)
       VALUES
          (#{taskId}, #{prvId}, #{prvName}, #{respCode}, #{errorMsg}, #{channelId}, #{channelUserId}, #{taskState}, #{taskStateDescription}, #{carLicenseNo}, #{carVehicleName},
          #{carProperty}, #{carOwnerName}, #{carOwnerPhone}, #{applicantName}, #{applicantIdcardType}, #{applicantIdcardNo}, #{applicantPhone}, #{applicantEmail}, #{insuredName},
          #{insuredIdcardType}, #{insuredIdcardNo}, #{insuredPhone}, #{insuredEmail}, #{efcInsureStartDate}, #{efcInsureEndDate}, #{efcInsureAmount}, #{efcInsurePremium},
          #{efcInsurePolicyNo}, #{efcInsureDiscountRate}, #{taxInsureFee}, #{taxInsureLateFee}, #{bizInsureStartDate}, #{bizInsureEndDate}, #{bizInsurePremium},
          #{bizInsureDiscountRate}, #{bizInsurePolicyNo}, #{bizInsureNfcPremium}, #{insureTotalPremium}, #{msgType}, #{deliveryType}, #{deliveryName}, #{deliveryPhone},
          #{deliveryProvince}, #{deliveryCity}, #{deliveryArea}, #{deliveryAddress}, #{deliveryZip}, #{deliveryExpressCompanyName}, #{deliveryExpressNumber},
          #{deliverySyElecPolicyFilePath}, #{deliveryJpElecPolicyFilePath}, #{deliveryOutDept}, #{inspectionCode}, #{quoteValidTime}, #{payValidTime}, #{srBizScore}, #{srTrafficScore},
          #{srBizRate}, #{srTrafficRate}, CURRENT_TIMESTAMP(), CURRENT_TIMESTAMP(), 0)
    </insert>

    <select id="getPolicyById" resultType="com.cfpamf.ms.insur.admin.pojo.vo.AmAmPolicyCreateListVO">
        SELECT * FROM am_policy WHERE id=#{id}
    </select>

    <select id="getPolicyByTaskIdAndState" resultType="com.cfpamf.ms.insur.admin.pojo.vo.AmAmPolicyCreateListVO">
        SELECT * FROM am_policy
        WHERE 1=1
        <if test='taskId != null'>
            AND taskId=#{taskId}
        </if>
        <if test='taskState != null'>
            AND taskState=#{taskState}
        </if>
        <if test='prvId != null'>
            AND prvId=#{prvId}
        </if>
        LIMIT 1
    </select>

    <select id="listPolicys" resultType="com.cfpamf.ms.insur.admin.pojo.vo.AmAmPolicyCreateListVO">
        SELECT t1.*,
        'efc' AS riskKindType,
        t3.create_time AS policyTime,
        t1.create_time AS orderCreateTime,
        t2.userName AS channelUserName,
        t2.regionName AS channelUserRegion,
        t2.organizationFullName AS channelUserOrganization,
        t2.userMobile AS channelUserMobile
        FROM am_policy t1
        LEFT JOIN auth_user t2
        ON t1.channelUserId = t2.userId AND t2.enabled_flag = 0
        LEFT JOIN am_policy t3
        ON t1.taskId = t3.taskId
        AND t3.prvId = t1.prvId
        AND t3.taskState = '23'
        WHERE t1.enabled_flag = '0' AND t1.efcInsureStartDate > ''
        <if test='query.taskNo != null'>
            AND t1.taskId = #{query.taskNo}
        </if>
        <if test='query.createStartDate != null'>
            <![CDATA[ AND t1.create_time >= #{query.createStartDate} ]]>
        </if>
        <if test='query.createEndDate != null'>
            <![CDATA[ AND t1.create_time <= #{query.createEndDate} ]]>
        </if>
        <if test='query.policyStartDate != null'>
            <![CDATA[ AND t3.create_time >= #{query.policyStartDate} ]]>
        </if>
        <if test='query.policyEndDate != null'>
            <![CDATA[ AND t3.create_time <= #{query.policyEndDate} ]]>
        </if>
        <if test='query.taskState != null'>
            AND t1.taskState IN
            <foreach collection="query.taskState" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test='query.riskType == "biz" '>
            AND 1=2
        </if>
        <if test='query.riskType == "unknow"'>
            AND 1=2
        </if>
        <if test='query.policyStartDateJ != null'>
            <![CDATA[ AND STR_TO_DATE(t1.efcInsureStartDate, '%Y-%m-%d') >= #{query.policyStartDateJ}]]>
        </if>
        <if test='query.policyEndDateJ != null'>
            <![CDATA[AND STR_TO_DATE(t1.efcInsureStartDate, '%Y-%m-%d') <= #{query.policyEndDateJ}]]>
        </if>
        <if test='query.policyStartDateS != null'>
            <![CDATA[ AND STR_TO_DATE(t1.bizInsureStartDate, '%Y-%m-%d') >= #{query.policyStartDateS}]]>
        </if>
        <if test='query.policyEndDateS != null'>
            <![CDATA[ AND STR_TO_DATE(t1.bizInsureStartDate, '%Y-%m-%d') <= #{query.policyEndDateS}]]>
        </if>
        <if test='query.prvName != null'>
            AND t1.prvName LIKE CONCAT('%',#{query.prvName},'%')
        </if>
        <if test='query.policyNo != null'>
            AND ((t1.efcInsurePolicyNo = #{query.policyNo} AND t1.riskKindType = 'efc') OR (t1.bizInsurePolicyNo =
            #{query.policyNo} AND t1.riskKindType = 'biz'))
        </if>
        <if test='query.referee != null'>
            AND (t2.userMobile LIKE CONCAT('%',#{query.referee},'%') OR t2.userId LIKE CONCAT('%',#{query.referee},'%')
            OR
            t2.userName LIKE CONCAT('%',#{query.referee},'%'))
        </if>
        <if test='query.insuredName != null'>
            AND (t1.insuredName LIKE CONCAT('%',#{query.insuredName},'%') OR t1.insuredIdcardNo LIKE
            CONCAT('%',#{query.insuredName},'%'))
        </if>
        <if test='query.carLicenseNo != null'>
            AND t1.carLicenseNo LIKE CONCAT('%',#{query.carLicenseNo},'%')
        </if>
        <if test='query.regionName != null'>
            AND t2.regionName=#{query.regionName}
        </if>
        <if test='query.orgName != null'>
            AND t2.organizationFullName=#{query.orgName}
        </if>
        <if test='query.orgPath != null'>
            AND t2.orgPath LIKE CONCAT(#{query.orgPath},'%')
        </if>
        <if test='query.applicantName != null'>
            AND (t1.applicantName LIKE CONCAT('%',#{query.applicantName},'%') OR t1.applicantIdcardNo LIKE
            CONCAT('%',#{query.applicantName},'%'))
        </if>
        <if test='query.carOwnerName != null'>
            AND t1.carOwnerName LIKE CONCAT('%',#{query.carOwnerName},'%')
        </if>
        UNION ALL
        SELECT t1.*,
        'biz' AS riskKindType,
        t3.create_time AS policyTime,
        t1.create_time AS orderCreateTime,
        t2.userName AS channelUserName,
        t2.regionName AS channelUserRegion,
        t2.organizationFullName AS channelUserOrganization,
        t2.userMobile AS channelUserMobile
        FROM am_policy t1
        LEFT JOIN auth_user t2
        ON t1.channelUserId = t2.userId AND t2.enabled_flag = 0
        LEFT JOIN am_policy t3
        ON t1.taskId = t3.taskId
        AND t3.prvId = t1.prvId
        AND t3.taskState = '23'
        WHERE t1.enabled_flag = '0' AND t1.bizInsureStartDate > ''
        <if test='query.taskNo != null'>
            AND t1.taskId = #{query.taskNo}
        </if>
        <if test='query.createStartDate != null'>
            <![CDATA[ AND t1.create_time >= #{query.createStartDate} ]]>
        </if>
        <if test='query.createEndDate != null'>
            <![CDATA[ AND t1.create_time <= #{query.createEndDate} ]]>
        </if>
        <if test='query.policyStartDate != null'>
            <![CDATA[ AND t3.create_time >= #{query.policyStartDate} ]]>
        </if>
        <if test='query.policyEndDate != null'>
            <![CDATA[ AND t3.create_time <= #{query.policyEndDate} ]]>
        </if>
        <if test='query.taskState != null'>
            AND t1.taskState IN
            <foreach collection="query.taskState" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test='query.riskType == "efc"'>
            AND 1=2
        </if>
        <if test='query.riskType == "unknow"'>
            AND 1=2
        </if>
        <if test='query.policyStartDateJ != null'>
            <![CDATA[ AND STR_TO_DATE(t1.efcInsureStartDate, '%Y-%m-%d') >= #{query.policyStartDateJ}]]>
        </if>
        <if test='query.policyEndDateJ != null'>
            <![CDATA[AND STR_TO_DATE(t1.efcInsureStartDate, '%Y-%m-%d') <= #{query.policyEndDateJ}]]>
        </if>
        <if test='query.policyStartDateS != null'>
            <![CDATA[ AND STR_TO_DATE(t1.bizInsureStartDate, '%Y-%m-%d') >= #{query.policyStartDateS}]]>
        </if>
        <if test='query.policyEndDateS != null'>
            <![CDATA[ AND STR_TO_DATE(t1.bizInsureStartDate, '%Y-%m-%d') <= #{query.policyEndDateS}]]>
        </if>
        <if test='query.prvName != null'>
            AND t1.prvName LIKE CONCAT('%',#{query.prvName},'%')
        </if>
        <if test='query.policyNo != null'>
            AND ((t1.efcInsurePolicyNo = #{query.policyNo} AND t1.riskKindType = 'efc') OR (t1.bizInsurePolicyNo =
            #{query.policyNo}
            AND t1.riskKindType = 'biz'))
        </if>
        <if test='query.referee != null'>
            AND (t2.userMobile LIKE CONCAT('%',#{query.referee},'%') OR t2.userId LIKE CONCAT('%',#{query.referee},'%')
            OR
            t2.userName LIKE CONCAT('%',#{query.referee},'%'))
        </if>
        <if test='query.insuredName != null'>
            AND (t1.insuredName LIKE CONCAT('%',#{query.insuredName},'%') OR t1.insuredIdcardNo LIKE
            CONCAT('%',#{query.insuredName},'%'))
        </if>
        <if test='query.carLicenseNo != null'>
            AND t1.carLicenseNo LIKE CONCAT('%',#{query.carLicenseNo},'%')
        </if>
        <if test='query.regionName != null'>
            AND t2.regionName=#{query.regionName}
        </if>
        <if test='query.orgName != null'>
            AND t2.organizationFullName=#{query.orgName}
        </if>
        <if test='query.orgPath != null'>
            AND t2.orgPath LIKE CONCAT(#{query.orgPath},'%')
        </if>
        <if test='query.applicantName != null'>
            AND (t1.applicantName LIKE CONCAT('%',#{query.applicantName},'%') OR t1.applicantIdcardNo LIKE
            CONCAT('%',#{query.applicantName},'%'))
        </if>
        <if test='query.carOwnerName != null'>
            AND t1.carOwnerName LIKE CONCAT('%',#{query.carOwnerName},'%')
        </if>
        ORDER BY create_time DESC, taskId DESC, prvId ASC, riskKindType ASC
    </select>

    <update id="deletePolicyByTaskId">
        UPDATE am_policy SET enabled_flag =1 WHERE taskId=#{taskId}
    </update>

    <select id="listGroups" resultType="com.cfpamf.ms.insur.admin.pojo.vo.DashboardVO$NameQtyAmount">
        SELECT
        DATE_FORMAT(create_time,'%Y-%m-%d') AS name,
        COUNT(DISTINCT taskId) AS qty,
        SUM(efcInsurePremium+taxInsureFee) AS jqAmount,
        SUM(bizInsurePremium) AS syAmount
        FROM am_policy
        WHERE enabled_flag = 0
        <if test='startDate != null'>
            <![CDATA[AND create_time >=]]> #{startDate}
        </if>
        <if test='endDate != null'>
            <![CDATA[AND create_time <= ]]> #{endDate}
        </if>
        <if test='taskState != null'>
            AND taskState IN
            <foreach collection="taskState" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test='type == "efc"'>
            <![CDATA[AND efcInsureStartDate != '' ]]>
        </if>
        <if test='type == "biz"'>
            <![CDATA[AND bizInsureStartDate != '' ]]>
        </if>
        GROUP BY DATE_FORMAT(create_time,'%Y-%m-%d')
    </select>

    <select id="getAmSummary" resultType="com.cfpamf.ms.insur.admin.pojo.vo.AmSummaryVo">
        SELECT
        SUM(t00.taxInsureFee + t00.efcInsurePremium) AS jqPolicyAmount,
        SUM(t00.bizInsurePremium) AS syPolicyAmount,
        SUM(CASE WHEN t00.riskKindName='efc' THEN 1 ELSE 0 END) AS jqOrderQty,
        SUM(CASE WHEN t00.taskState=23 AND riskKindName='efc' THEN 1 ELSE 0 END) AS jqPolicyQty,
        SUM(CASE WHEN t00.riskKindName='biz' THEN 1 ELSE 0 END) AS syOrderQty,
        SUM(CASE WHEN t00.taskState=23 AND t00.riskKindName='biz' THEN 1 ELSE 0 END) AS syPolicyQty,
        COUNT(DISTINCT taskId) AS orderQty,
        SUM(t00.taxInsureFee + t00.efcInsurePremium + t00.bizInsurePremium) AS policyAmount
        FROM (
        (
        SELECT t1.taskState, CONVERT(t1.taxInsureFee, DECIMAL(10,2)) AS taxInsureFee, CONVERT(t1.efcInsurePremium,
        DECIMAL(10,2)) AS efcInsurePremium, 0.00 AS bizInsurePremium, t1.taskId, 'efc' AS riskKindName
        FROM am_policy t1
        LEFT JOIN auth_user t2
        ON t1.channelUserId = t2.userId AND t2.enabled_flag = 0
        LEFT JOIN am_policy t3
        ON t1.taskId = t3.taskId
        AND t3.prvId = t1.prvId
        AND t3.taskState = '23'
        WHERE t1.enabled_flag = '0' AND t1.efcInsureStartDate > ''
        <if test='query.taskNo != null'>
            AND t1.taskId = #{query.taskNo}
        </if>
        <if test='query.createStartDate != null'>
            <![CDATA[ AND t1.create_time >= #{query.createStartDate} ]]>
        </if>
        <if test='query.createEndDate != null'>
            <![CDATA[ AND t1.create_time <= #{query.createEndDate} ]]>
        </if>
        <if test='query.policyStartDate != null'>
            <![CDATA[ AND t3.create_time >= #{query.policyStartDate} ]]>
        </if>
        <if test='query.policyEndDate != null'>
            <![CDATA[ AND t3.create_time <= #{query.policyEndDate} ]]>
        </if>
        <if test='query.taskState != null'>
            AND t1.taskState IN
            <foreach collection="query.taskState" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test='query.riskType == "biz" '>
            AND 1=2
        </if>
        <if test='query.riskType == "unknow"'>
            AND 1=2
        </if>
        <if test='query.policyStartDateJ != null'>
            <![CDATA[ AND STR_TO_DATE(t1.efcInsureStartDate, '%Y-%m-%d') >= #{query.policyStartDateJ}]]>
        </if>
        <if test='query.policyEndDateJ != null'>
            <![CDATA[AND STR_TO_DATE(t1.efcInsureStartDate, '%Y-%m-%d') <= #{query.policyEndDateJ}]]>
        </if>
        <if test='query.policyStartDateS != null'>
            <![CDATA[ AND STR_TO_DATE(t1.bizInsureStartDate, '%Y-%m-%d') >= #{query.policyStartDateS}]]>
        </if>
        <if test='query.policyEndDateS != null'>
            <![CDATA[ AND STR_TO_DATE(t1.bizInsureStartDate, '%Y-%m-%d') <= #{query.policyEndDateS}]]>
        </if>
        <if test='query.prvName != null'>
            AND t1.prvName LIKE CONCAT('%',#{query.prvName},'%')
        </if>
        <if test='query.policyNo != null'>
            AND ((t1.efcInsurePolicyNo = #{query.policyNo} AND t1.riskKindType = 'efc') OR (t1.bizInsurePolicyNo =
            #{query.policyNo} AND t1.riskKindType = 'biz'))
        </if>
        <if test='query.referee != null'>
            AND (t2.userMobile LIKE CONCAT('%',#{query.referee},'%') OR t2.userId LIKE CONCAT('%',#{query.referee},'%')
            OR t2.userName LIKE CONCAT('%',#{query.referee},'%'))
        </if>
        <if test='query.insuredName != null'>
            AND t1.insuredName LIKE CONCAT('%',#{query.insuredName},'%')
        </if>
        <if test='query.carLicenseNo != null'>
            AND t1.carLicenseNo LIKE CONCAT('%',#{query.carLicenseNo},'%')
        </if>
        <if test='query.regionName != null'>
            AND t2.regionName LIKE CONCAT('%',#{query.regionName},'%')
        </if>
        <if test='query.orgName != null'>
            AND t2.organizationName LIKE CONCAT('%',#{query.orgName},'%')
        </if>
        <if test='query.applicantName != null'>
            AND t1.applicantName LIKE CONCAT('%',#{query.applicantName},'%')
        </if>
        <if test='query.carOwnerName != null'>
            AND t1.carOwnerName LIKE CONCAT('%',#{query.carOwnerName},'%')
        </if>
        )
        UNION ALL
        (
        SELECT t1.taskState, 0.00 AS taxInsureFee, 0.00 AS efcInsurePremium, CONVERT(t1.bizInsurePremium, DECIMAL(10,2))
        AS bizInsurePremium, t1.taskId, 'biz' AS riskKindName
        FROM am_policy t1
        LEFT JOIN auth_user t2
        ON t1.channelUserId = t2.userId AND t2.enabled_flag = 0
        LEFT JOIN am_policy t3
        ON t1.taskId = t3.taskId
        AND t3.prvId = t1.prvId
        AND t3.taskState = '23'
        WHERE t1.enabled_flag = '0' AND t1.bizInsureStartDate > ''
        <if test='query.taskNo != null'>
            AND t1.taskId = #{query.taskNo}
        </if>
        <if test='query.createStartDate != null'>
            <![CDATA[ AND t1.create_time >= #{query.createStartDate} ]]>
        </if>
        <if test='query.createEndDate != null'>
            <![CDATA[ AND t1.create_time <= #{query.createEndDate} ]]>
        </if>
        <if test='query.policyStartDate != null'>
            <![CDATA[ AND t3.create_time >= #{query.policyStartDate} ]]>
        </if>
        <if test='query.policyEndDate != null'>
            <![CDATA[ AND t3.create_time <= #{query.policyEndDate} ]]>
        </if>
        <if test='query.taskState != null'>
            AND t1.taskState IN
            <foreach collection="query.taskState" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test='query.riskType == "efc"'>
            AND 1=2
        </if>
        <if test='query.riskType == "unknow"'>
            AND 1=2
        </if>
        <if test='query.policyStartDateJ != null'>
            <![CDATA[ AND STR_TO_DATE(t1.efcInsureStartDate, '%Y-%m-%d') >= #{query.policyStartDateJ}]]>
        </if>
        <if test='query.policyEndDateJ != null'>
            <![CDATA[AND STR_TO_DATE(t1.efcInsureStartDate, '%Y-%m-%d') <= #{query.policyEndDateJ}]]>
        </if>
        <if test='query.policyStartDateS != null'>
            <![CDATA[ AND STR_TO_DATE(t1.bizInsureStartDate, '%Y-%m-%d') >= #{query.policyStartDateS}]]>
        </if>
        <if test='query.policyEndDateS != null'>
            <![CDATA[ AND STR_TO_DATE(t1.bizInsureStartDate, '%Y-%m-%d') <= #{query.policyEndDateS}]]>
        </if>
        <if test='query.prvName != null'>
            AND t1.prvName LIKE CONCAT('%',#{query.prvName},'%')
        </if>
        <if test='query.policyNo != null'>
            AND ((t1.efcInsurePolicyNo = #{query.policyNo} AND t1.riskKindType = 'efc') OR (t1.bizInsurePolicyNo =
            #{query.policyNo} AND t1.riskKindType = 'biz'))
        </if>
        <if test='query.referee != null'>
            AND (t2.userMobile LIKE CONCAT('%',#{query.referee},'%') OR t2.userId LIKE CONCAT('%',#{query.referee},'%')
            OR t2.userName LIKE CONCAT('%',#{query.referee},'%'))
        </if>
        <if test='query.insuredName != null'>
            AND t1.insuredName LIKE CONCAT('%',#{query.insuredName},'%')
        </if>
        <if test='query.carLicenseNo != null'>
            AND t1.carLicenseNo LIKE CONCAT('%',#{query.carLicenseNo},'%')
        </if>
        <if test='query.regionName != null'>
            AND t2.regionName LIKE CONCAT('%',#{query.regionName},'%')
        </if>
        <if test='query.orgName != null'>
            AND t2.organizationName LIKE CONCAT('%',#{query.orgName},'%')
        </if>
        <if test='query.applicantName != null'>
            AND t1.applicantName LIKE CONCAT('%',#{query.applicantName},'%')
        </if>
        <if test='query.carOwnerName != null'>
            AND t1.carOwnerName LIKE CONCAT('%',#{query.carOwnerName},'%')
        </if>
        )
        ) t00
    </select>
</mapper>