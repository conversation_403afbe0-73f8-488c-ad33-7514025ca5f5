<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.claim.SmClaimReimbursementKpMapper">

    <resultMap id="BaseResultMap" type="com.cfpamf.ms.insur.admin.pojo.po.claim.SmClaimReimbursementKp">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="claimId" column="claim_id" jdbcType="INTEGER"/>
            <result property="zaReportNo" column="za_report_no" jdbcType="VARCHAR"/>
            <result property="accidentName" column="accident_name" jdbcType="VARCHAR"/>
            <result property="accidentRelationToInsured" column="accident_relation_to_insured" jdbcType="VARCHAR"/>
            <result property="reporterName" column="reporter_name" jdbcType="VARCHAR"/>
            <result property="reporterRelationToInsured" column="reporter_relation_to_insured" jdbcType="VARCHAR"/>
            <result property="reporterPhone" column="reporter_phone" jdbcType="VARCHAR"/>
            <result property="reporterCertType" column="reporter_cert_type" jdbcType="VARCHAR"/>
            <result property="reporterCertNo" column="reporter_cert_no" jdbcType="INTEGER"/>
            <result property="reporterMail" column="reporter_mail" jdbcType="VARCHAR"/>
            <result property="payeeWay" column="payee_way" jdbcType="VARCHAR"/>
            <result property="payeeName" column="payee_name" jdbcType="VARCHAR"/>
            <result property="bankCard" column="bank_card" jdbcType="VARCHAR"/>
            <result property="depositBankCode" column="deposit_bank_code" jdbcType="VARCHAR"/>
            <result property="zfbAccount" column="zfb_account" jdbcType="VARCHAR"/>
            <result property="depositBankName" column="deposit_bank_name" jdbcType="VARCHAR"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="enabledFlag" column="enabled_flag" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,claim_id,za_report_no,
        accident_name,accident_relation_to_insured,reporter_name,
        reporter_relation_to_insured,reporter_phone,reporter_cert_type,
        reporter_cert_no,reporter_mail,payee_way,
        payee_name,bank_card,deposit_bank_code,
        zfb_account,deposit_bank_name,create_by,
        update_time,create_time,enabled_flag
    </sql>
    <update id="updateKpReportNoByClaimId">
        UPDATE sm_claim_reimbursement_kp set za_report_no = #{reportNo}  WHERE claim_id = #{claimId}
    </update>
</mapper>
