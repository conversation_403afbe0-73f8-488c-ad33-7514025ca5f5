<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.order.SmOrderDistributionMapper">
    <update id="updateAmountByPushBatch">
        update sm_order_distribution t0
            left join sm_order_distribution_batch_push t1 on t0.fh_order_id = t1.order_id
        set t0.distribution_amount = if(t0.distribution_level = 1, level_one_order_commission, level_two_order_commission)
        where t1.batch_no = #{batchNo}
          and t0.distribution_type = 2 and t1.policy_status = '1'
    </update>

    <update id="deleteByFhOrderId">
        insert into sm_order_distribution_backups(fh_order_id,virtual_order_no,policy_no
        ,distribution_amount,distribution_order_no,distribution_state,distribution_cust_name,distribution_cust_mobile
        ,distribution_level,id_number,distribution_type)
        select
            fh_order_id,virtual_order_no,policy_no
            ,distribution_amount,distribution_order_no,distribution_state
            ,distribution_cust_name,distribution_cust_mobile
            ,distribution_level,id_number,distribution_type
        from sm_order_distribution where fh_order_id=#{fhOrderId};

        delete from sm_order_distribution WHERE fh_order_id=#{fhOrderId};
    </update>

    <update id="updateDistributionCustName">
        <foreach collection="list" item="item" separator=";">
            update sm_order_distribution t0
            set t0.distribution_cust_name = #{item.distributionCustName},t0.distribution_level = #{item.distributionLevelAfter}
            where t0.fh_order_id = #{item.fhOrderId}
              and t0.distribution_level = #{item.distributionLevelBefore}
        </foreach>
    </update>

    <select id = "selectDistributionByOrderIds" resultType="com.cfpamf.ms.insur.admin.pojo.po.order.SmOrderDistribution">
        select `fh_order_id`,GROUP_CONCAT(distribution_order_no) as distribution_order_no
        from `sm_order_distribution` where fh_order_id in
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        group by `fh_order_id`
    </select>

    <update id="updateDistributionLevel">
        <foreach collection="list" item="item" separator=";">
            update sm_order_distribution
            set distribution_level = #{item.distributionLevel}
            where fh_order_id = #{item.fhOrderId}
              and distribution_order_no = #{item.distributionOrderNo}
        </foreach>
    </update>
</mapper>