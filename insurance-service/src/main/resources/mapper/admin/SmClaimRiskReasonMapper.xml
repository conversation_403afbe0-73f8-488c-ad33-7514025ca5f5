<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.claim.SmClaimRiskReasonMapper">

    <select id="getByLastReasonId" resultType="com.cfpamf.ms.insur.admin.pojo.vo.claim.ClaimRiskReasonVo">
        SELECT *
        FROM `sm_claim_risk_reason`
        WHERE last_reason_id = #{lastReasonId}
    </select>
    <select id="getAll" resultType="com.cfpamf.ms.insur.admin.pojo.vo.claim.ClaimRiskReasonVo">
        SELECT *
        FROM `sm_claim_risk_reason`
    </select>
</mapper>