<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.order.SmOrderApplicantMapper">

    <update id="applicantInfoChange">
        update sm_order_applicant
        set personName=#{personName},
            personGender=#{personGender},
            idType=#{idType},
            idNumber=#{idNumber},
            birthday=#{birthday}
        where fhOrderId LIKE concat(#{fhOrderId},'_%') OR fhOrderId = #{fhOrderId};
    </update>

</mapper>