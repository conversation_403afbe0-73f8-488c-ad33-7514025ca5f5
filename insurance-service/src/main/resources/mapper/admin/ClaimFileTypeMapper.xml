<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cfpamf.ms.insur.admin.claim.dao.ClaimFileTypeMapper">
    <sql id="allFieldNotId">
        code
        ,
        `name`,
        example_file_url_arr_json,
        example_file_description,
        create_time,
        create_by,
        update_time,
        update_by,
        deleted,
        create_role
    </sql>

    <sql id="allField">
        id
        ,
        code,
        `name`,
        example_file_url_arr_json,
        example_file_description,
        create_time,
        create_by,
        update_time,
        update_by,
        deleted,
        create_role
    </sql>

    <!--    保存理赔文件类型 start-->
    <insert id="save" parameterType="com.cfpamf.ms.insur.admin.claim.entity.ClaimFileTypeEntity" useGeneratedKeys="true"
            keyProperty="id">
        insert INTO sm_claim_file_type
        (
        <include refid="allFieldNotId"/>
        )
        VALUES(#{code},#{name},
        #{exampleFileUrlArrJson},#{exampleFileDescription},
        #{createTime},#{createBy},
        #{updateTime},#{updateBy},
        #{deleted},#{createRole}
        )
    </insert>
    <!--    保存理赔文件类型 end-->

    <!--    id查找start-->
    <select id="findById" resultType="com.cfpamf.ms.insur.admin.claim.entity.ClaimFileTypeEntity">
        select
        <include refid="allField"/>
        from sm_claim_file_type
        where id = #{id} and deleted = 0
    </select>

    <!--    id查找end-->

    <!--    code查找start-->
    <select id="findByCode" resultType="com.cfpamf.ms.insur.admin.claim.entity.ClaimFileTypeEntity">
        select
        <include refid="allField"/>
        from sm_claim_file_type
        where code = #{code} and deleted = 0
    </select>
    <!--    code查找end-->

</mapper>