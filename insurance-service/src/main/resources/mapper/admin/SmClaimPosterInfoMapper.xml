<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.claim.SmClaimPosterInfoMapper">

    <select id="querySmClaimById" resultType="com.cfpamf.ms.insur.admin.pojo.po.claim.SmClaimPosterInfo">
        SELECT
            t1.startTime as insurancePeriod,
            t6.regionName as regionName,
            t6.organizationFullName as organizationFullName,
            t4.id as insuranceProducts,
            t0.riskType as accidentType,
            t1.unitPrice as premium,
            t0.payMoney as compensationAmount
        FROM
            sm_claim t0
                LEFT JOIN sm_order_insured t3 ON t0.insuredId = t3.id
                LEFT JOIN sm_order t1 ON t3.fhOrderId = t1.fhOrderId
                LEFT JOIN sm_product t4 ON t4.id = t1.productId
                LEFT JOIN auth_user t6 ON t6.userId = t1.customerAdminId AND t6.enabled_flag = 0
        WHERE t0.id= #{id};
    </select>
</mapper>