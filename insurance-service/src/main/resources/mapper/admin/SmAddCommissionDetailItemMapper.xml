<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.commission.SmAddCommissionDetailMapper">

    <sql id="Base_Column_List">
        id
        , order_id, policy_no, insured_id_number, plan_id, risk_id, term_num, commission_type,
        proportion, data_id, uuid, enabled_flag, create_time, update_time
    </sql>
    <insert id="insertListDuplicateUpdate">
        INSERT INTO sm_add_commission_detail
        (order_id,policy_no,insured_id_number,plan_id,risk_id,term_num,status,proportion,amount,add_commission_amount,uuid)
        VALUES
        <foreach collection="list" item="record" separator=",">
            (#{record.orderId},#{record.policyNo},#{record.insuredIdNumber},#{record.planId},#{record.riskId},#{record.termNum},#{record.status},#{record.proportion},#{record.amount},#{record.addCommissionAmount},#{record.uuid})
        </foreach>
        ON DUPLICATE KEY UPDATE proportion = values(proportion),
        add_commission_amount=VALUES(add_commission_amount),
        amount =values(amount)
    </insert>
    <select id="selectBackByOrderId"
            resultType="com.cfpamf.ms.insur.admin.pojo.po.commission.SmAddCommissionDetail">
        select scd.order_id,
        scd.policy_no,
        scd.insured_id_number,
        scd.plan_id,
        scd.risk_id,
        scd.term_num,
        t.proportion,
        '4' status,
        scd.amount,
        round(t.proportion * scd.amount * -1, 2) add_commission_amount,
        uuid
        from sm_add_commission_detail t
        left join sm_commission_detail_item scd
        on t.order_id = scd.order_id
        and t.insured_id_number = scd.insured_id_number
        and t.term_num = scd.term_num
        and t.risk_id = scd.risk_id
        and t.plan_id = scd.plan_id
        and commission_type = 3
        where t.order_id = #{orderId}
        and (scd.policy_status = '4' or scd.risk_status = '4')
    </select>


    <delete id="batchCorrectIdNumber">
        <foreach collection="data" item="order" separator=";">
            delete from sm_add_commission_detail
            where order_id = #{order.orderId}
            and   policy_no = #{order.policyNo}
            and   insured_id_number = #{order.oldValue}
            and   enabled_flag = 0
        </foreach>
    </delete>

    <select id="getAddCommission"
            resultType="com.cfpamf.ms.insur.admin.pojo.po.commission.SmAddCommissionDetail">
        select *
        from sm_add_commission_detail
        where policy_no = #{policyNo}
        and insured_id_number = #{insuredIdNumber}
        and status = #{appStatus}
        and term_num = #{termNum}
    </select>


</mapper>
