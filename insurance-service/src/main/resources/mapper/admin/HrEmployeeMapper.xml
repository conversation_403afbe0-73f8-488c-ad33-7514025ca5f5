<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.hr.HrEmployeeMapper">
    <select id="getMaxJobNumberHead" resultType="java.lang.String">
        select ifnull(max(hr_no), 'YTDL00000') maxNo
        from hr_employee
        where
        <![CDATA[   hr_no <= 'YTDL02000'
        ]]>

    </select>

    <select id="getMaxJobNumberBranch" resultType="java.lang.String">
        select ifnull(max(hr_no), 'YTDL02001') maxNo
        from hr_employee
        where
        <![CDATA[   hr_no > 'YTDL02000'
        ]]>

    </select>
    <select id="selectAuthUserByIdNumber" resultType="com.cfpamf.ms.insur.admin.pojo.vo.AuthUserVO">
        select au.*
        from auth_user au
        where au.userIdCard = #{idNumber}
          and au.enabled_flag = 0
    </select>
    <select id="listByQuery" resultType="com.cfpamf.ms.insur.admin.pojo.vo.hr.HrEmployeeVO">
        select he.id,
        he.apply_id,
        he.hr_no,
        he.photo_url,
        he.name,
        he.id_type,
        he.id_number,
        he.mobile,
        he.nation,
        he.sex,
        he.birthday,
        he.education,
        he.marriage,
        he.politics_status,
        he.email,
        he.address,
        he.bank_no,
        he.bank_info,
        he.emergency_linkman,
        he.emergency_linkman_phone,
        he.flow_code,
        he.flow_status,
        he.employee_status,
        he.special_type,
        he.contract_status,
        he.bfoq_status,
        he.remark,
        he.create_time,
        he.enabled_flag,
        au.organizationName,
        au.regionName,
        he.agent_org,he.entry_date
        from hr_employee he
        left join auth_user au on
        au.enabled_flag = 0 and au.userId = he.zhnx_job_number
        where 1=1

        <if test="employeeId!=null">
            and he.id = #{employeeId}
        </if>
        <if test='regionName != null'>
            AND au.regionName=#{regionName}
        </if>
        <if test='orgName != null'>
            AND au.organizationName=#{orgName}
        </if>
        <if test="entryDateStart != null">
            <![CDATA[ and he.entry_date >= #{entryDateStart}  ]]>
        </if>

        <if test="entryDateEnd != null">
            <![CDATA[ and he.entry_date <= #{entryDateEnd}  ]]>
        </if>

        <if test="employeeKeyword!=null and employeeKeyword!=''">
            and (
            he.name like CONCAT('%',<trim>#{employeeKeyword}</trim>,'%')
            or he.mobile like CONCAT('%',<trim>#{employeeKeyword}</trim>,'%')
            or he.zhnx_job_number like CONCAT('%',<trim>#{employeeKeyword}</trim>,'%')
            or he.hr_no like CONCAT('%',<trim>#{employeeKeyword}</trim>,'%')
            )
        </if>

        <if test="employeeStatus != null">
            and he.employee_status= #{employeeStatus}
        </if>
        <if test="specialType != null">
            and he.special_type= #{specialType}
        </if>
        <if test="contractStatus != null">and he.contract_status= #{contractStatus}
        </if>
        <if test="bfoqStatus != null">and he.bfoq_status= #{bfoqStatus}
        </if>
        <if test="enabledFlag != null">and he.enabled_flag= #{enabledFlag}
        </if>
        order by he.id desc
    </select>
    <select id="selectAuthUserByJobNumber" resultType="com.cfpamf.ms.insur.admin.pojo.vo.AuthUserVO">
        select au.*
        from auth_user au
        where au.userId = #{jobNumber}
          and au.enabled_flag = 0
        limit 1
    </select>
    <select id="selectByEventLoss" resultType="com.cfpamf.ms.insur.admin.pojo.vo.hr.HrEmployeeVO">
        select he.*
        from hr_entry_apply hea
                 inner join hr_employee he on hea.id = he.apply_id
        where hea.flow_status = '30'
        <![CDATA[ and he.employee_status < 3
        ]]>
    </select>
</mapper>
