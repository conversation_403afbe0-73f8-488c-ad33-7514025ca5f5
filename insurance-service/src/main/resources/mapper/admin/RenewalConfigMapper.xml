<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cfpamf.ms.insur.admin.renewal.dao.RenewalConfigMapper">

    <!--    通过计划ID获取有效的续订配置 start-->
    <select id="getActiveRenewalConfigByPlanId"
            resultType="com.cfpamf.ms.insur.admin.renewal.entity.RenewalConfig">
        select *
        from sm_renewal_config
        where enabled_flag = '0'
          and plan_id = #{planId}
    </select>
    <!--    通过计划ID获取有效的续订配置 end -->

    <!--    搜索产品计划续保配置视图对象 start-->
    <select id="findPlanRenewalConfigVoByProductIdList"
            resultType="com.cfpamf.ms.insur.admin.renewal.vo.PlanRenewalConfigVo">
        SELECT t1.*,
        t3.id as productId,
        t3.productName as productName,
        t3.state as productStatus,
        t2.planName as planName,
        t2.id as planId
        from sm_renewal_config t1
        LEFT JOIN sm_plan t2 on t1.plan_id = t2.id
        LEFT join sm_product t3 on t2.productId = t3.id
        WHERE t1.enabled_flag = 0
        and t2.productId in
        <foreach collection="productIdList" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <!--    搜索产品计划续保配置视图对象 end-->

    <!--    搜索产品计划续保配置视图对象 start-->
    <select id="findPlanRenewalConfigVoByProductId"
            resultType="com.cfpamf.ms.insur.admin.renewal.vo.PlanRenewalConfigVo">
        SELECT t1.*,
               t3.id          as productId,
               t3.productName as productName,
               t3.state       as productStatus,
               t2.planName    as planName,
               t2.id          as planId
        from sm_renewal_config t1
                 LEFT JOIN sm_plan t2 on t1.plan_id = t2.id
                 LEFT JOIN sm_product t3 on t2.productId = t3.id
        WHERE t1.enabled_flag = 0
          and t2.productId = #{productId}

    </select>
    <!--    搜索产品计划续保配置视图对象 end-->

    <!--    搜索产品计划续保配置视图对象 start-->
    <select id="getRenewalConfigById"
            resultType="com.cfpamf.ms.insur.admin.renewal.entity.RenewalConfig">
        SELECT *
        from sm_renewal_config
        WHERE id = #{id} and enabled_flag = 0
    </select>
    <!--    搜索产品计划续保配置视图对象 end-->

    <!--    搜索产品计划续保配置视图对象 start-->
    <select id="searchProductRenewalConfigVo"
            resultType="com.cfpamf.ms.insur.admin.renewal.vo.ProductRenewalConfigVo">
        SELECT
        t1.id as productId,
        t1.productName as productName,
        t1.state as productStatus
        from sm_product t1
        LEFT JOIN sm_plan t2 on t2.productId = t1.id  and t2.enabled_flag = 0
        LEFT JOIN sm_renewal_config t3 on t3.plan_id = t2.id and t3.enabled_flag = 0
        WHERE t1.enabled_flag = 0
        <if test=" renewalProductId!=null and renewalProductId !=''">
            and (locate(#{renewalProductId}
            , t3.change_product_id_list)
            or locate(#{renewalProductId}
            , t3.renewal_product_id_list))
        </if>
        <if test="productStatus!=null">
            AND t1.state = #{productStatus}
        </if>
        <if test="productName!=null and productName!=''">
            AND t1.productName LIKE CONCAT('%',#{productName},'%')
        </if>
        group by t1.id,t1.productName,t1.state
    </select>
    <select id="getPlanRenewalConfigByPlanId"
            resultType="com.cfpamf.ms.insur.admin.renewal.vo.PlanRenewalConfigVo">
        SELECT t1.*,
               t3.id          as productId,
               t3.productName as productName,
               t3.state       as productStatus,
               t2.planName    as planName,
               t2.id          as planId
        from sm_renewal_config t1
                 LEFT JOIN sm_plan t2 on t1.plan_id = t2.id
                 LEFT join sm_product t3 on t2.productId = t3.id
        WHERE t1.enabled_flag = 0
          and t1.plan_id = #{planId}

    </select>
    <!--    搜索产品计划续保配置视图对象 end-->

    <select id="getActiveRenewalConfigByProductId"
            resultType="com.cfpamf.ms.insur.admin.renewal.entity.RenewalConfig">
        SELECT t1.*,
               t3.id          as productId,
               t3.productName as productName,
               t3.state       as productStatus
        from sm_renewal_config t1
                 LEFT JOIN sm_plan t2 on t1.plan_id = t2.id
                 LEFT join sm_product t3 on t2.productId = t3.id
        WHERE t1.enabled_flag = 0
          and t3.id = #{productId}
    </select>

    <update id="deleteByProductId">
        update sm_renewal_config set enabled_flag = 1 where renewal_platform = 'xj' and product_id = #{productId}
    </update>
</mapper>