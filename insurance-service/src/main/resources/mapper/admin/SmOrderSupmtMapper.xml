<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.SmOrderSupmtMapper">

    <insert id="insertSmOrderSupmtRecords" useGeneratedKeys="true">
        INSERT INTO sm_order_supplement(fhOrderId, result, create_by, create_time)
        VALUES
       (#{fhOrderId}, #{result}, #{createBy}, CURRENT_TIMESTAMP())
    </insert>

    <select id="listSmOrderSupmtRecords" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmOrderSupmtRecordVO">
        SELECT t1.id, t1.create_time AS createTime, t2.userName AS createBy, t3.create_time AS orderCreateTime,
        t1.fhOrderId AS fhOrderId, t4.personName AS applicantPersonName, t5.personName AS insuredPersonName,
        t7.productName AS productName, t3.unitPrice*t3.qty AS totalAmount, t6.regionName AS recommendRegionName,
        t6.organizationFullName AS recommendOrganizationName, t6.userId AS recommendUserId, t6.userName AS
        recommendUserName,
        t6.userMobile AS recommendUserMobile, t5.policyNo AS policyNo, t7.productAttrCode,
        sa.agentId,
        sa.agentName
        FROM sm_order_supplement t1
        LEFT JOIN auth_user t2 ON t1.create_by = t2.userId AND t2.enabled_flag = 0
        LEFT JOIN sm_order t3 ON t1.fhOrderId = t3.fhOrderId
        LEFT JOIN sm_order_applicant t4 ON t1.fhOrderId = t4.fhOrderId
        LEFT JOIN sm_order_insured t5 ON t1.fhOrderId = t5.fhOrderId
        LEFT JOIN auth_user t6 ON t3.recommendId = t6.userId AND t6.enabled_flag = 0
        LEFT JOIN sm_product t7 ON t3.productId = t7.id
        left join sm_agent sa on sa.agentId = t3.agentId
        WHERE 1=1
        <if test='createDateStart != null'>
            <![CDATA[ AND  t3.create_time>=#{createDateStart}  ]]>
        </if>
        <if test='createDateEnd != null'>
            <![CDATA[ AND t3.create_time<#{createDateEnd}  ]]>
        </if>
        <if test='spmDateStart != null'>
            <![CDATA[ AND  t1.create_time>=#{spmDateStart}  ]]>
        </if>
        <if test='spmDateEnd != null'>
            <![CDATA[ AND t1.create_time<#{spmDateEnd}  ]]>
        </if>
        <if test='recommendPerson != null'>
            AND (t6.userId LIKE CONCAT(#{recommendPerson},'%')
            OR t6.userName LIKE CONCAT(#{recommendPerson},'%')
            OR t6.userMobile LIKE CONCAT(#{recommendPerson},'%') )
        </if>
        <if test='fhOrderId != null'>
            AND t1.fhOrderId LIKE CONCAT('%', #{fhOrderId} ,'%')
        </if>
        <if test='applicantPersonName != null'>
            AND (t4.personName LIKE CONCAT(#{applicantPersonName},'%')
            OR t4.idNumber LIKE CONCAT(#{applicantPersonName},'%')
            OR t4.cellPhone LIKE CONCAT(#{applicantPersonName},'%') )
        </if>
        <if test='insuredPersonName != null'>
            AND (t5.personName LIKE CONCAT(#{insuredPersonName},'%')
            OR t5.idNumber LIKE CONCAT(#{insuredPersonName},'%')
            OR t5.cellPhone LIKE CONCAT(#{insuredPersonName},'%') )
        </if>
        <if test='agentName != null'>
            AND (sa.agentName LIKE CONCAT(#{agentName} ,'%') OR sa.agentMobile LIKE CONCAT(#{agentName} ,'%'))
        </if>
        ORDER BY t1.create_time DESC
    </select>
</mapper>
