<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.admin.renewal.dao.DwdCommodityMainProductNameMapMapper">



    <select id="getDwdCommodityMainProductNameMapByPlanId"
            resultType="com.cfpamf.ms.insur.admin.renewal.entity.DwdCommodityMainProductNameMap">

        select * from dwd_commodity_main_product_name_map
        where zhnx_product_id = #{productId} and zhnx_plan_id = #{planId} and
    </select>





</mapper>