<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.SmOccupationMapper">

    <insert id="insertOccupations" useGeneratedKeys="true">
        INSERT INTO sm_company_occupation (companyId, parentCode,categoryCode, categoryName,occupationCode,
        occupationName, occupationGroup, create_by, create_time, update_by, update_time, enabled_flag)
        VALUES
        <foreach collection="occupationList" item="item" index="index" separator=",">
            (#{item.companyId},#{item.parentCode},#{item.categoryCode},#{item.categoryName},#{item.occupationCode},#{item.occupationName},#{item.occupationGroup},#{item.modifyBy},
            CURRENT_TIMESTAMP(), #{item.modifyBy}, CURRENT_TIMESTAMP(),0)
        </foreach>
    </insert>

    <select id="listOccupations" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmOccupationVO">
        SELECT
            parentCode,
            categoryCode,
            categoryName,
            occupationCode,
            occupationName,
            occupationGroup
        FROM sm_company_occupation WHERE enabled_flag=0
        AND companyId = #{id}
        <if test='parentCode != null'>
            AND parentCode = #{parentCode}
        </if>
        <if test='parentCode == null and !findAll'>
            AND parentCode IS NULL
        </if>
        <if test=' includeGroups != null and includeGroups.size>0 '>
            AND (
                occupationGroup IN
                <foreach item="item" index="index" open="(" separator="," close=")" collection="includeGroups">
                    #{item}
                </foreach>
                OR occupationGroup IS NULL
            )
        </if>
    </select>

    <select id="listOccupationWithGroups" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmOccupationVO">
        SELECT parentCode,categoryCode,categoryName,occupationCode,occupationName,occupationGroup FROM
        sm_company_occupation WHERE enabled_flag=0
        AND companyId = #{id}
        <if test='parentCode != null'>
            AND parentCode = #{parentCode}
            AND
            (
            categoryCode IN (
            SELECT parentCode FROM sm_company_occupation
            WHERE companyId = #{id} AND occupationGroup IN
            <foreach item="item" index="index" open="(" separator="," close=")" collection="includeGroups">
                #{item}
            </foreach>
            )
            OR occupationGroup IN
            <foreach item="item" index="index" open="(" separator="," close=")" collection="includeGroups">
                #{item}
            </foreach>
            )
        </if>
        <if test='parentCode == null'>
            AND parentCode IS NULL
            AND categoryCode IN
            (
                SELECT parentCode FROM sm_company_occupation
                WHERE categoryCode IN (
                SELECT parentCode FROM sm_company_occupation
                WHERE companyId = #{id} AND occupationGroup IN
                <foreach item="item" index="index" open="(" separator="," close=")" collection="includeGroups">
                    #{item}
                </foreach>
            )
            )
        </if>
    </select>

    <select id="getOccupationGroupList" resultType="java.lang.String">
        SELECT DISTINCT occupationGroup
        FROM sm_company_occupation
        WHERE enabled_flag = 0
          AND companyId = #{id}
          AND occupationGroup != ''
        ORDER BY occupationGroup
    </select>

    <select id="getOccupationByCompanyIdAndCode" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmOccupationVO">
        SELECT *
        FROM sm_company_occupation
        WHERE enabled_flag = 0
          AND companyId = #{companyId}
          AND occupationCode = #{occupationCode}
        LIMIT 1
    </select>

    <update id="deleteProductOccupationLimits">
        delete
        from sm_product_form_limit
        WHERE productId = #{productId}
          AND fieldCode = #{fieldCode}
    </update>
    <update id="deleteProductOccupationLimitsHistory">
        delete
        from sm_product_form_limit_history
        WHERE productId = #{productId}
          AND fieldCode = #{fieldCode}
          and version = #{version}
    </update>

    <update id="deleteOccupations">
        UPDATE sm_company_occupation
        SET enabled_flag=1,
            update_by=#{modifyBy},
            update_time=CURRENT_TIMESTAMP()
        WHERE companyId = #{companyId}
    </update>

    <insert id="insertProductOccupationLimits" useGeneratedKeys="true">
        INSERT INTO sm_product_form_limit
        (productId, fieldCode,limitCode, update_by, update_time, enabled_flag)
        VALUES
        <foreach collection="occupationList" item="item" index="index" separator=",">
            (#{item.productId},#{item.fieldCode},#{item.limitCode},#{item.modifyBy}, CURRENT_TIMESTAMP(),0)
        </foreach>
    </insert>
    <insert id="insertProductOccupationLimitsHistory" useGeneratedKeys="true">
        INSERT INTO sm_product_form_limit_history
        (productId, fieldCode,limitCode, update_by, update_time, enabled_flag,version)
        VALUES
        <foreach collection="occupationList" item="item" index="index" separator=",">
            (#{item.productId},#{item.fieldCode},#{item.limitCode},#{item.modifyBy}, CURRENT_TIMESTAMP(),0,#{version})
        </foreach>
    </insert>
    <insert id="insertOccupationsByHistory">
        INSERT INTO sm_product_form_limit
            (productId, fieldCode, limitCode, update_by, update_time, enabled_flag)
        select productId, fieldCode, limitCode, update_by, update_time, enabled_flag
        from sm_product_form_limit_history
        where productId = #{productId}
          and version = #{version}
    </insert>

    <select id="getOccupationHotList" resultType="com.cfpamf.ms.insur.weixin.pojo.vo.WxTreeVO">
        SELECT t3.parentCode      AS parent,
               t2.occupationCode  AS value,
               t3.occupationName  AS name,
               t3.occupationGroup AS type
        FROM sm_order t1
                 LEFT JOIN sm_order_insured t2 ON t1.fhOrderId = t2.fhOrderId
                 LEFT JOIN sm_product t0 ON t0.id = t1.productId
                 LEFT JOIN sm_company_occupation t3
                           ON t0.companyId = t3.companyId AND t3.occupationCode = t2.occupationCode AND
                              t3.enabled_flag = 0
        WHERE t1.create_time > DATE_SUB(CURDATE(), INTERVAL 6 MONTH)
          AND t1.productId = #{productId}
          AND t1.payStatus = 2
          AND t3.occupationName IS NOT NULL
        GROUP BY t3.occupationCode
        ORDER BY COUNT(1) DESC
        LIMIT #{size}
    </select>

    <select id="listOcup" resultType="com.cfpamf.ms.insur.weixin.pojo.vo.WxTreeVO">
        SELECT  t1.parentCode AS parent,
                t1.occupationCode AS value,
                t1.occupationName AS name,
                t1.occupationGroup AS type
        FROM sm_company_occupation t1
        WHERE t1.companyId = #{companyId}
        AND enabled_flag = 0
        AND t1.occupationCode IN
        <foreach collection="ocupCodes" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
        <if test='ocupGroup != null'>
            AND (
                occupationGroup IN
                <foreach item="item" index="index" open="(" separator="," close=")" collection="ocupGroup">
                    #{item}
                </foreach>
                OR occupationGroup IS NULL
            )
        </if>
    </select>

    <select id="listOcupWithCodes" resultType="com.cfpamf.ms.insur.weixin.pojo.vo.WxTreeVO">
        SELECT t1.parentCode AS parent, t1.occupationCode AS value, t1.occupationName AS name, t1.occupationGroup AS
        type
        FROM sm_company_occupation t1
        WHERE t1.companyId = #{companyId}
        AND enabled_flag = 0
        AND t1.occupationCode IN
        <foreach collection="ocupCodes" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="getByCompanyIdAndOccCodes" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmOccupationTreeVO">
        select max(id) id,categoryCode,categoryName,occupationCode,categoryName,occupationGroup
        from sm_company_occupation
        where companyId = #{companyId}
        and occupationCode in
        <foreach collection="occCodes" item="occCode" open="(" close=")" separator=",">
            #{occCode}
        </foreach>
            group by occupationCode
    </select>
    <select id="getByCompanyIdAndOccCode" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmOccupationTreeVO">
        select *
        from sm_company_occupation
        where companyId = #{companyId}
        <if test="occCode!=null">
            and occupationCode = #{occCode}
        </if>
        <if test="categoryCode!=null">
            and categoryCode = #{categoryCode}
        </if>
        order by id desc
        limit 1
    </select>
</mapper>
