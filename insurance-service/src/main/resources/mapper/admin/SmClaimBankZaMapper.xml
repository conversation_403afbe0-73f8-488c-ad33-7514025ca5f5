<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.claim.SmClaimBankZaMapper">

    <update id="softDelete">
        update sm_claim_bank_za
        set enabled_flag = 0
    </update>

    <select id="selectHeadBank" resultType="com.cfpamf.ms.insur.admin.claim.vo.ZaBankInfoVO">
        SELECT DISTINCT head_bank_code as bankCode, head_bank_name as bankCodeName
        FROM sm_claim_bank_za
        where za_enable = 1
        <if test="keyword != null and keyword != '' ">
            and head_bank_name like concat('%',concat(#{keyword},'%'))
        </if>
    </select>

    <select id="selectBranchBank" resultType="com.cfpamf.ms.insur.admin.claim.vo.ZaBankInfoVO">
        SELECT DISTINCT branch_bank_code as bankCode, branch_bank_name as bankCodeName
        FROM sm_claim_bank_za
        <where>
            za_enable = 1
            <if test="bankCode != null and bankCode !=''">
                AND head_bank_code = #{bankCode}
            </if>
            <if test="districtCode != null and districtCode !=''">
                AND district_code = #{districtCode}
            </if>
        </where>
    </select>


</mapper>