<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.product.risk.SysRiskMapper">

    <select id="selectByQuery" resultType="com.cfpamf.ms.insur.admin.pojo.vo.product.SysRiskVO">
        select sr.*,sc.companyName company_name,l.limit_age
        from
        sys_risk sr left join sm_company sc on sr.company_id = sc.id
        left join sys_risk_limit l on l.risk_key = sr.risk_key
        and l.version = sr.version
        where sr.id in (
        select max(id) id
        from sys_risk sr
        group by sr.risk_key
        ) and sr.enabled_flag = 0
        <if test="companyId!=null">
            and company_id = #{companyId}
        </if>
        <if test="riskType != null">
            and risk_type = #{riskType}
        </if>
        <if test="riskName != null">
            and (risk_name like concat('%',#{riskName},'%') OR risk_code = #{riskName})
        </if>
        <if test="riskClassify != null">
            and risk_classify = #{riskClassify}
        </if>
        group by sr.risk_key
        order by sr.update_time desc
    </select>
    <select id="selectByKey" resultType="com.cfpamf.ms.insur.admin.pojo.po.product.risk.SysRisk">
        select *
        from sys_risk
        where risk_key = #{key}
          and version = #{version}
    </select>
    <select id="selectTestByOrderId"
            resultType="com.cfpamf.ms.insur.admin.pojo.form.product.TestPremiumDutyForm">
        select sord.fh_order_id
             , sord.insured_id_number
             , sord.risk_id sys_risk_id
             , sr.risk_key
             , sr.risk_code
             , sord.duty_id sys_risk_duty_id
             , srd.duty_key
             , srd.duty_code
             , srd.version  duty_version
             , sord.premium
             , sord.amount
             , sord.risk_name
             , sord.duty_name
        from sm_order_risk_duty sord
                 left join sys_risk sr on sord.risk_id = sr.id
                 left join sys_risk_duty srd on srd.id = sord.duty_id
        where sord.fh_order_id = #{orderId}
    </select>
</mapper>
