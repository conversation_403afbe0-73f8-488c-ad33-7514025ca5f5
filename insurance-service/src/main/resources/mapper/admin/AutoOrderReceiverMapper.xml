<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.auto.AutoOrderReceiverMapper">
    <update id="logicDelete">
        update auto_order set enabled_flag =1 where order_no = #{fhOrderId}

    </update>

    <insert id="insertAutoOrderReceiverBatch" parameterType= "java.util.List">
        INSERT INTO auto_order_receiver (order_no, receiver_name, receiver_cell, receiver_address,
        courier_no,courier_company, enabled_flag, create_time, update_time)
        VALUES
        <foreach collection="dtos" item="dto" index="index" separator=",">
            (#{dto.orderNo},#{dto.receiver.receiverName},#{dto.receiver.receiverCell},
            #{dto.receiver.receiverAddress},#{dto.receiver.courierNo},#{dto.receiver.courierCompany},
            0,CURRENT_TIMESTAMP() , CURRENT_TIMESTAMP())
        </foreach>
    </insert>

    <update id="updateAutoOrderReceiverBatch" parameterType= "java.util.List">
        <foreach collection="dtos" item="dto" index="index" separator=";">
            update auto_order_receiver set receiver_name=#{dto.receiver.receiverName},receiver_cell=#{dto.receiver.receiverCell},
            receiver_address=#{dto.receiver.receiverAddress},courier_no=#{dto.receiver.courierNo},
            courier_company=#{dto.receiver.courierCompany}
            where order_no=#{dto.orderNo}
        </foreach>
    </update>
</mapper>
