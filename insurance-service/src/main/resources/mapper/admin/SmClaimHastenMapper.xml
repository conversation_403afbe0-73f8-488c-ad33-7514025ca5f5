<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.SmClaimHastenMapper">

    <sql id="selectAll">
        claimId,
            hasten_time,
            create_by,
            update_by,
    </sql>
    <select id="fetchVoByQuery" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmClaimHastenVO">
        select t0.id AS id,
        t0.riskType,
        t0.claimNo,
        t0.riskTime,
        t0.riskDesc,
        t0.claimResult,
        t0.claimState,
        t0.create_time AS reportTime,
        t0.claimStateTime,
        t0.timeout,
        t7.userName AS agentName,
        t7.userMobile AS agentMobile,
        t0.dataTime,
        t0.note,
        t0.settlement,
        t0.settlement as settlementName,
        t0.lastFollowUp,
        if(t0.claimState = 'stepDataCheckByPic', if(t6.regionName='总部','HOLDER', ifnull(if(t0.create_role='employee',
        t8.userName, t9.userName),'未配置')), if(t0.create_role='employee', t6.userName, t7.userName) ) processor,
        if(t0.claimState = 'stepDataCheckByPic', if(t6.regionName='总部','HOLDER', if(t0.create_role='employee',
        t8.userId, t9.userId)), if(t0.create_role='employee', t6.userId, t7.userId) ) processorNo,
        t1.create_time AS createTime,
        t1.fhOrderId AS fhOrderId,
        t1.orderState AS orderState,
        t1.payStatus AS payStatus,
        t1.unitPrice * t1.qty AS totalAmount,
        t3.policyNo AS policyNo,
        t3.appStatus AS appStatus,
        t1.startTime AS startTime,
        t1.endTime AS endTime,
        t3.downloadURL AS downloadURL,
        t3.personName AS insuredPersonName,
        t3.idNumber AS insuredIdNumber,
        t3.cellPhone AS insuredCellPhone,
        t3.email AS insuredEmail,
        t6.userName AS recommendUserName,
        t6.userId AS recommendUserId,
        t3.personGender AS insuredPersonGender,
        t3.relationship AS insuredRelationship,
        t6.userMobile AS recommendUserMobile,
        t6.regionName AS recommendRegionName,
        t6.organizationFullName AS recommendOrganizationName,
        htTime hastenTime,
        timestampdiff(DAY, htTime, now()) htDateDiff
        from (
        select c.*,
        timestampdiff(DAY, c.claimStateTime, now()) dateDiff,
        if(c.claimState = 'stepDataCheckByPic', timestampdiff(DAY, c.claimStateTime, now()) - 2,
        timestampdiff(DAY, c.claimStateTime, now()) - 3) timeout
        from sm_claim c
        left join
        (
        select count(1) t, claimId from sm_claim_progress where sCode = 'stepDataCheckByPic' group by claimId
        ) t on t.claimId = c.id and t.claimId = c.id and c.claimState = 'stepDataPrepare'
        where c.claimState in (
        'stepDataPrepare1',
        'stepDataPrepare2',
        'stepDataPrepare3',
        'stepPostExpress',
        'stepDataCheckByPic',
        'stepToPay')
        or (c.claimState = 'stepDataPrepare' and t.t > 0)
        ) t0
        left join (
        select claimId, claimState, max(hastenTime) htTime
        from sm_claim_hasten t00
        group by claimId, claimState
        ) ht on ht.claimId = t0.id and ht.claimState=t0.claimState and ht.htTime>t0.claimStateTime
        LEFT JOIN sm_order_insured t3 ON t0.insuredId = t3.id
        LEFT JOIN sm_order t1 ON t3.fhOrderId = t1.fhOrderId
        LEFT JOIN auth_user t6 ON t6.userId = t1.customerAdminId AND t6.enabled_flag = 0
        LEFT JOIN auth_user t7 ON t7.agentId = t1.agentId AND t7.enabled_flag = 0
        LEFT JOIN org_pic_extra t8 ON t6.hrOrgId = t8.orgHrId and t8.enabled_flag = 0
        LEFT JOIN org_pic_extra t9 ON t7.hrOrgId = t9.orgHrId and t9.enabled_flag = 0
        <where>
            <if test='reportDateStart != null'>
                <![CDATA[ AND t0.create_time>=#{reportDateStart} ]]>
            </if>
            <if test='reportDateEnd != null'>
                <![CDATA[ AND t0.create_time<=#{reportDateEnd} ]]>
            </if>
            <if test='riskDateStart != null'>
                <![CDATA[ AND t0.riskTime>=#{riskDateStart} ]]>
            </if>
            <if test='riskDateEnd != null'>
                <![CDATA[ AND t0.riskTime<=#{riskDateEnd} ]]>
            </if>
            <if test='claimNo != null'>
                AND t0.claimNo=#{claimNo}
            </if>
            <if test='policyNo != null'>
                AND t3.policyNo=#{policyNo}
            </if>
            <if test='custManager != null'>
                AND ((t6.userName LIKE CONCAT('%',#{custManager},'%')) OR (t6.userId LIKE
                CONCAT('%',#{custManager},'%')))
            </if>
            <if test='insuredPerson != null'>
                AND ((t3.personName LIKE CONCAT('%',#{insuredPerson},'%')) OR (t3.idNumber LIKE
                CONCAT('%',#{insuredPerson},'%')) OR (t3.cellPhone LIKE CONCAT('%',#{insuredPerson},'%')))
            </if>
            <if test='riskType != null'>
                AND t0.riskType=#{riskType}
            </if>
            <if test='regionName != null'>
                AND t6.regionName=#{regionName}
            </if>
            <if test='orgName != null'>
                AND t6.organizationFullName=#{orgName}
            </if>
            <if test='agentId != null'>
                AND t1.agentId=#{agentId}
            </if>
            <if test="timeout!=null">
                <![CDATA[ AND  timeout >=#{timeout} ]]>
            </if>
            <if test="hastenBetween!=null">
                <![CDATA[ AND  timestampdiff(DAY, htTime, now())  >=#{hastenBetween} ]]>
            </if>
            <if test='settlement != null and settlement!=""'>
                AND t0.settlement=#{settlement}
            </if>
            <if test='claimStates != null and claimStates.size>0'>
                AND t0.claimState in
                <foreach collection="claimStates" close=")" open="(" item="item" separator="," >
                    #{item}
                </foreach>
            </if>
        </where>
    </select>
    <select id="lastResult" resultType="com.cfpamf.ms.insur.admin.pojo.dto.ClaimProgressDTO">
        select
        t2.personName AS customerName,
        c.claimNo,
        c.settlement,
        cp.claimId,
        cp.oValue,
        cp.create_time,
        c.create_by,
        cp.update_time,
        c.update_by,
        cp.enabled_flag,
        cp.dataJson,
        c.claimStateTime,
        c.claimState,
        <!--  只有是待审核的时候查询机构对接人 其他都是通知创建人 -->
        if(c.claimState = 'stepDataCheckByPic', if(au.regionName='总部','HOLDER',opeau.wxOpenId),
        au.wxOpenId) wxOpenId,
        if(c.claimState = 'stepDataCheckByPic',if(au.regionName='总部','HOLDER',ope.userId), au.userId)
        processorUserId,
        if(c.claimState = 'stepDataCheckByPic',if(au.regionName='总部','HOLDER',ope.userName), au.userName)
        processorUserName,
        htTime hastenTime,
        au.userName as customerAdminName
        from sm_claim_progress cp
        left join sm_claim c on c.id = cp.claimId
        left join (
        select claimId, claimState, max(hastenTime) htTime
        from sm_claim_hasten t00
        group by claimId, claimState
        ) ht on ht.claimId = c.id and ht.claimState=c.claimState and ht.htTime>c.claimStateTime
        LEFT JOIN sm_order_insured t2 ON t2.id = c.insuredId
        LEFT JOIN sm_order t1 ON t2.fhOrderId = t1.fhOrderId
        LEFT JOIN auth_user au ON ((au.userId=t1.customerAdminId AND c.create_role = 'employee') OR (au.agentId =
        t1.agentId AND c.create_role = 'agent')) AND au.enabled_flag = 0
        left join org_pic_extra ope on ope.orgHrId = au.hrOrgId and ope.enabled_flag = 0
        left join auth_user opeau on opeau.userId = ope.userId and opeau.enabled_flag = 0
        where cp.id in (
        select max(id)
        from sm_claim_progress tcp
        where tcp.claimId in
        <foreach collection="claimIds" item="claimId" open="(" close=")" separator=",">
            #{claimId}
        </foreach>
        group by claimId)
    </select>

    <select id="statisticNotifyDate"
            resultType="com.cfpamf.ms.insur.admin.claim.form.PushOutTimeDayVO">
        SELECT `settlement`,
        COUNT(1) as count
        FROM (SELECT c.*,
        IF
        (
        c.claimState = 'stepDataCheckByPic',
        timestampdiff(
        DAY,
        c.claimStateTime,
        now()) - 2,
        timestampdiff(
        DAY,
        c.claimStateTime,
        now()) - 3
        ) timeout
        FROM sm_claim c
        LEFT JOIN (SELECT count(1) t,
        claimId
        FROM sm_claim_progress
        WHERE sCode = 'stepDataCheckByPic'
        GROUP BY claimId) t ON t.claimId = c.id
        AND t.claimId = c.id
        AND c.claimState = 'stepDataPrepare'
        WHERE c.claimState = 'stepToPay') t0
        LEFT JOIN (SELECT claimId,
        claimState,
        max(hastenTime) htTime
        FROM sm_claim_hasten t00
        GROUP BY claimId,
        claimState) ht ON ht.claimId = t0.id
        AND ht.claimState = t0.claimState
        AND ht.htTime > t0.claimStateTime
        LEFT JOIN sm_order_insured t3 ON t0.insuredId = t3.id
        LEFT JOIN sm_order t1 ON t3.fhOrderId = t1.fhOrderId
        LEFT JOIN auth_user t6 ON t6.userId = t1.customerAdminId
        AND t6.enabled_flag = 0
        LEFT JOIN auth_user t7 ON t7.agentId = t1.agentId
        AND t7.enabled_flag = 0
        LEFT JOIN org_pic_extra t8 ON t6.hrOrgId = t8.orgHrId
        AND t8.enabled_flag = 0
        LEFT JOIN org_pic_extra t9 ON t7.hrOrgId = t9.orgHrId
        AND t9.enabled_flag = 0
        WHERE t0.timeout >= #{outDay}
        and t0.settlement is not null
        and t0.`riskType` in
        <foreach collection="riskTypeList" close=")" open="(" separator="," item="item">
            #{item}
        </foreach>
        GROUP BY t0.`settlement`

    </select>
</mapper>
