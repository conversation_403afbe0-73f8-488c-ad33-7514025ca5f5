<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.order.SmOrderDistributionBatchPushPushMapper">
    <insert id="insertLastMonthAllOrder">
        insert into sm_order_distribution_batch_push
        (batch_no, order_id, account_time, level_one_user_id_number, level_two_user_id_number,
         level_one_order_commission,
         level_two_order_commission, customer_id_no, customer_name, customer_phone_num, pay_amount, policy_status,
         product_id,product_mapper_name,
         commission_settlement_date, push_state,is_life_service_partner)
        SELECT #{start},
           fh_order_id,
           account_time,
           level_one_user_id_number,
           level_two_user_id_number,
           level_one_order_commission,
           level_two_order_commission,
           customer_id_no,
           customer_name,
           customer_phone_num,
           pay_amount,
           policy_status,
           t0.product_id,
           t4.mapper_name,
           now(),
           0,
           t0.is_life_service_partner
        from dwa_sm_order_distribution_settlement t0
                 left join sm_product_distribution_config t4 on t0.product_id = t4.product_id  and t4.enabled_flag = 0
        where <![CDATA[  account_time >= #{start} and   account_time<  #{end}  ]]>
        on duplicate key update batch_no = #{start}
    </insert>
    <insert id="insertListOrUpdate">
        insert into sm_order_distribution_batch_push
        (batch_no, order_id, account_time, level_one_user_id_number, level_two_user_id_number,
        level_one_order_commission,
        level_two_order_commission, customer_id_no, customer_name, customer_phone_num, pay_amount, policy_status,
        product_id,product_mapper_name,
        commission_settlement_date, push_state)
        VALUES
        <foreach collection="pushes" item="push" separator=",">
            (#{push.batchNo},#{push.orderId},#{push.accountTime},#{push.levelOneUserIdNumber},#{push.levelTwoUserIdNumber}
            ,#{push.levelOneOrderCommission},#{push.levelTwoOrderCommission},#{push.customerIdNo},#{push.customerName},#{push.customerPhoneNum}
            ,#{push.payAmount},#{push.policyStatus},#{push.productId},#{push.productMapperName},#{push.commissionSettlementDate},#{push.pushState})
        </foreach>
        on duplicate key update
        batch_no = values(batch_no)
    </insert>

    <update id="updateByBatchNoAndOrderId">
        <foreach collection="pushList" item="item" separator=";">
            update sm_order_distribution_batch_push
            set push_state = #{item.pushState},error_msg = #{item.errorMsg}
            where order_id = #{item.orderId} and policy_status = 4
        </foreach>
    </update>

    <select id="getDistributionCommissionByPage" resultType="com.cfpamf.ms.insur.admin.renewal.vo.OrderDistribution4CommissionVo">
        select
            `activity`.`manager_code` manageCode,
            `push`.`id` ,
            `activity`.`manager_name` manageName,
            `sett`.`policy_no` ,
            `sett`.`endorsement_no` endorsementNo,
            `push`.`order_id` orderId,
            `push`.`level_one_order_commission` commissionAmt,
            `push`.monthly_batch_no ,
            `push`.`policy_status`
        from `sm_order_distribution_batch_push` push
        left join `dwa_sm_order_distribution_settlement` sett on push.`order_id` = sett.`fh_order_id` and push.`policy_status` = `sett`.`policy_status`
        left join `sm_order_village_activity` activity on push.`order_id` = `activity`.`fh_order_id` and activity.`type` = '200'
        where push.`push_state` = 1 and sync_whale_state = 0 and monthly_batch_no = #{query.monthBatchNo}
        <if test="query.size != null">
            limit #{query.offset}, #{query.size}
        </if>
    </select>

    <select id="getDistributionCommissionByPageV2" resultType="com.cfpamf.ms.insur.admin.renewal.vo.OrderDistribution4CommissionVo">
        with dis_order as (
            select `fh_order_id` ,min(distribution_amount) distribution_amount
            from `sm_order_distribution`
            group by `fh_order_id`
        )
        select
            if(dis.commission_id_number = push.level_one_user_id_number, push.level_one_user_id_number , push.level_two_user_id_number) manageCode,
            `push`.`id` ,
            dis.distribution_cust_name manageName,
            `sett`.`policy_no` ,
            `sett`.`endorsement_no` endorsementNo,
            `push`.`order_id` orderId,
            case when push.level_one_user_id_number = push.level_two_user_id_number then if(dis.distribution_amount > dis_order.distribution_amount,push.level_two_order_commission,`push`.`level_one_order_commission`)
                 when dis.commission_id_number = push.level_one_user_id_number then `push`.`level_one_order_commission`
                 when dis.commission_id_number = push.level_two_user_id_number then push.level_two_order_commission
            end as commissionAmt,
            `push`.monthly_batch_no ,
            `push`.`policy_status`,
            dis.id as orderDistributionId
        from `sm_order_distribution_batch_push` push
        left join `dwa_sm_order_distribution_settlement` sett on push.`order_id` = sett.`fh_order_id` and push.`policy_status` = `sett`.`policy_status`
        left join sm_order_distribution dis on push.`order_id` = dis.`fh_order_id`
        left join dis_order dis_order on dis.`fh_order_id` = dis_order.`fh_order_id`
        where push.`push_state` = 1 and sync_whale_state = 0 and dis.distribution_level = 1 and monthly_batch_no = #{query.monthBatchNo}
        <if test="query.size != null">
            limit #{query.offset}, #{query.size}
        </if>
    </select>

    <update id="updateSyncState">
        update sm_order_distribution_batch_push
        set sync_whale_state = 1
        where id in
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </update>
</mapper>