<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cfpamf.ms.insur.admin.claim.dao.ClaimConfigMapper">

    <sql id="allFieldNotId">
        product_id
        ,
        `name`,
        required_file_arr_json,
        not_required_file_arr_json,
        create_time,
        create_by,
        update_time,
        update_by,
        deleted,
        create_role
    </sql>

    <sql id="allField">
        id
        ,
        product_id,
        `name`,
        required_file_arr_json,
        not_required_file_arr_json,
        create_time,
        create_by,
        update_time,
        update_by,
        deleted,
        create_role
    </sql>

    <insert id="save">

    </insert>
    <insert id="batchSave">

    </insert>
</mapper>