<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.claim.SmClaimImportResultMapper">

    <select id="pageList" resultType="com.cfpamf.ms.insur.admin.claim.entity.SmClaimImportResult">
        select * from sm_claim_import_result
        <where>
            <if test="startTime != null and startTime != ''">
                create_time > #{startTime}
            </if>
            <if test="endTime != null and endTime != ''">
                and create_time &lt;  DATE_ADD(#{endTime}, INTERVAL 1 DAY)
            </if>
        </where>
        order by create_time desc
    </select>
</mapper>