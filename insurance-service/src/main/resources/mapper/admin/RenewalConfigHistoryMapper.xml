<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cfpamf.ms.insur.admin.renewal.dao.RenewalConfigHistoryMapper">

    <select id="selectByProductId" resultType="com.cfpamf.ms.insur.admin.renewal.vo.ProductRenewalConfigHistoryVo">
        select t.version,t1.userName as createBy,t.create_Time,t.product_id from sm_renewal_config_history t
        left join auth_user t1 on t.create_by = t1.userId and t.enabled_flag = 0
        where t.product_id = #{productId}
        group by t.product_id,t.version
        order by t.version desc
    </select>

    <insert id="insertVersion" parameterType="com.cfpamf.ms.insur.admin.renewal.entity.RenewalConfigHistory">
        INSERT INTO `sm_renewal_config_history` (`plan_id`, `before_expiration_day`, `after_expiration_day`,
                                         `renewal_product_id_list`, `renewal_plan_id_list`, `renewal_hint`,
                                         `change_product_id_list`, `change_plan_id_list`, `change_hint`,
                                         `enabled_flag`, `create_by`, `update_by`,`config_id`,`version`,
                                         `before_send_sms_interval`, `after_send_sms_interval`, `break_guarantee_hint`,
                                         `recommend_plan_id`, `product_id`, `renewal_platform`)
        select `plan_id`, `before_expiration_day`, `after_expiration_day`,
               `renewal_product_id_list`, `renewal_plan_id_list`, `renewal_hint`,
               `change_product_id_list`, `change_plan_id_list`, `change_hint`,
               `enabled_flag`, #{createBy}, #{updateBy},`config_id`,version+1 as version,
               `before_send_sms_interval`, `after_send_sms_interval`, `break_guarantee_hint`,
               `recommend_plan_id`, `product_id`, `renewal_platform`
        from sm_renewal_config_history t
        where t.product_id = #{productId} and version = #{version}
        <if test="configId != null">
            and t.config_id != #{configId}
        </if>
    </insert>

    <!--    搜索产品计划续保配置视图对象 start-->
    <select id="findPlanRenewalConfigHistoryVoByProductId"
            resultType="com.cfpamf.ms.insur.admin.renewal.vo.PlanRenewalConfigVo">
        SELECT t1.*,
               t3.id          as productId,
               t3.productName as productName,
               t3.state       as productStatus,
               t2.planName    as planName,
               t2.id          as planId
        from sm_renewal_config_history t1
                 LEFT JOIN sm_plan t2 on t1.plan_id = t2.id
                 LEFT JOIN sm_product t3 on t2.productId = t3.id
        WHERE t1.enabled_flag = 0
          and t1.product_Id = #{productId} and t1.version = #{version}
          and t1.plan_id is not null
    </select>
</mapper>
