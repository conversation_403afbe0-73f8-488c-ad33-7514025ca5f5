<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.SystemSettingMapper">

    <select id="listSystemSettings" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SystemSettingVO">
        SELECT * FROM system_setting WHERE type= #{type} AND enabled_flag = 0
        <if test='activeFlag != null'>
            AND active_flag = #{activeFlag}
        </if>
        ORDER BY sorting
    </select>

    <insert id="insertSystemSetting" parameterType="com.cfpamf.ms.insur.admin.pojo.dto.SystemSettingDTO"
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO system_setting
        (type, code, value, active_flag, enabled_flag, sorting, create_by, create_time, update_time)
        VALUES
        (#{type}, #{code}, #{value}, #{activeFlag}, 0, #{sorting}, #{createBy}, CURRENT_TIMESTAMP(),
        CURRENT_TIMESTAMP())
    </insert>

    <insert id="batchInsertSystemSettings">
        INSERT INTO system_setting
        (type, code, value, active_flag, enabled_flag, sorting, create_by, create_time, update_time)
        VALUES
        <foreach collection="dtos" item="item" index="index" separator=",">
            (#{item.type}, #{item.code}, #{item.value}, #{item.activeFlag}, 0, #{item.sorting}, #{item.createBy},
            CURRENT_TIMESTAMP(), CURRENT_TIMESTAMP())
        </foreach>
    </insert>

    <update id="updateSystemSetting" parameterType="com.cfpamf.ms.insur.admin.pojo.dto.SystemSettingDTO">
        UPDATE system_setting SET
        code=#{code},
        value=#{value},
        active_flag=#{activeFlag},
        sorting=#{sorting},
        update_by=#{createBy},
        update_time=CURRENT_TIMESTAMP()
        WHERE id = #{id}
    </update>

    <update id="deleteSystemSettingByType">
        UPDATE system_setting SET enabled_flag = 1 WHERE type = #{type}
    </update>

    <update id="deleteSystemSetting">
        UPDATE system_setting SET enabled_flag = 1 WHERE id = #{id}
    </update>

</mapper>