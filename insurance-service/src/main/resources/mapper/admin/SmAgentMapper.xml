<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.SmAgentMapper">

    <select id="listAgents" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmAgentVO">
        SELECT
        t1.*,
        t2.agentName AS parentAgentName,
        t2.agentMobile AS parentAgentMobile,
        t2.agentJobNumber AS parentAgentJobNumber,
        t4.teamName AS teamName
        FROM
        sm_agent t1
        LEFT JOIN sm_agent t2
        ON t1.parentAgentId = t2.agentId
        LEFT JOIN sm_agent_level_allowance_setting t3
        ON t3.levelId = t1.levelId
        LEFT JOIN sm_agent_team t4
        ON t4.teamId = t1.teamId
        WHERE t1.agentStatus > -1
        <if test='agentName != null'>
            AND (
            t1.agentName LIKE CONCAT(#{agentName}, "%")
            OR t1.agentMobile LIKE CONCAT(#{agentName}, "%")
            OR t1.agentJobNumber LIKE CONCAT(#{agentName}, "%")
            )
        </if>
        <if test='agentLevel != null'>
            AND t1.levelId=#{agentLevel}
        </if>
        <if test='agentType != null'>
            AND t1.agentType=#{agentType}
        </if>
        <if test='regionName != null'>
            AND t1.regionName=#{regionName}
        </if>
        <if test='organizationName != null'>
            AND t1.organizationName=#{organizationName}
        </if>
        <if test='agentStatus != null'>
            AND t1.agentStatus=#{agentStatus}
        </if>
        <if test='teamId != null'>
            AND t1.teamId=#{teamId}
        </if>
        <if test='teamName != null'>
            AND t4.teamName LIKE CONCAT(#{teamName}, "%")
        </if>
        <if test='parentAgentId != null and agentName==null'>
            AND t1.parentAgentId=#{parentAgentId}
        </if>
        ORDER BY t1.agentId DESC
    </select>

    <select id="listAgentsByTeamId" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmAgentVO">
        SELECT * FROM sm_agent WHERE teamId = #{teamId} AND agentStatus > -1
   </select>
    <select id="getAgentSimpleByAgentId" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmAgentVO">
        SELECT * from sm_agent where agentId = #{agentId}
   </select>

    <select id="getAgentByAgentId" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmAgentVO">
        SELECT
        t1.*,
        t2.agentName AS parentAgentName,
        t2.agentMobile AS parentAgentMobile,
        t2.agentJobNumber AS parentAgentJobNumber,
        t3.levelName AS agentLevel,
        t4.teamName,
        t5.wxImgUrl AS agentWxImgUrl
        FROM
        sm_agent t1
        LEFT JOIN sm_agent t2
        ON t1.parentAgentId = t2.agentId
        LEFT JOIN sm_agent_level_allowance_setting t3
        ON t3.levelId = t1.levelId
        LEFT JOIN sm_agent_team t4
        ON t4.teamId = t1.teamId
        LEFT JOIN auth_user t5
        ON t5.agentId = t1.agentId AND t5.enabled_flag = 0
        WHERE t1.agentId = #{agentId}
        LIMIT 1
   </select>

    <select id="getAgentByAgentBindUserId" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmAgentVO">
        SELECT
        t1.*,
        t2.agentName AS parentAgentName,
        t2.agentMobile AS parentAgentMobile,
        t2.agentJobNumber AS parentAgentJobNumber,
        '' AS agentLevel,
        t4.teamName,
        t5.wxImgUrl AS agentWxImgUrl
        FROM
        sm_agent t1
        LEFT JOIN sm_agent t2
        ON t1.parentAgentId = t2.agentId
        LEFT JOIN sm_agent_level_allowance_setting t3
        ON t3.levelId = t1.levelId
        LEFT JOIN sm_agent_team t4
        ON t4.teamId = t1.teamId
        LEFT JOIN auth_user t5
        ON t5.agentId = t1.agentId AND t5.enabled_flag = 0
        WHERE t1.agentBindUserId = #{userId}
        LIMIT 1
   </select>

    <select id="listSingleAgentCms" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmAgentTimeOrderDetailVO">
        SELECT
        t1.accountTime,
        t1.fhOrderId,
        t4.productName ,
        t41.planName,
        t1.totalAmount,
        t1.appStatus,
        t1.appPersonName AS applicantPersonName,
        t1.appCellPhone AS applicantCellPhone,
        t1.insPersonName AS insuredPersonName,
        t1.insCellPhone AS insuredCellPhone
        FROM
        sm_order_commission t1
        LEFT JOIN sm_product t4
        ON t4.id = t1.productId
        LEFT JOIN sm_plan t41
        ON t41.id = t1.planId
        LEFT JOIN sm_agent t5
        ON t5.agentId = t1.agentId
        WHERE t1.agentId = #{agentId}
        <if test='startDate != null'>
            <![CDATA[ AND t1.accountTime>=#{startDate} ]]>
        </if>
        <if test='endDate != null'>
            <![CDATA[ AND t1.accountTime<=#{endDate} ]]>
        </if>
        ORDER BY t1.accountTime DESC
    </select>

    <select id="getSingleAgentCmsSmy" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmAgentTimeOrderSmyVO">
        SELECT
        SUM(qty) AS orderQty,
        SUM(totalAmount) AS orderAmount,
        SUM(paymentAmount) AS paymentCommissionAmount
        FROM
        sm_order_commission t1
        LEFT JOIN sm_product t4
        ON t4.id = t1.productId
        WHERE t1.agentId=#{agentId}
        <if test='startDate != null'>
            <![CDATA[ AND t1.accountTime>=#{startDate} ]]>
        </if>
        <if test='endDate != null'>
            <![CDATA[ AND t1.accountTime<=#{endDate} ]]>
        </if>
    </select>

    <select id="listAgentTeams" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmTeamListVO">
        SELECT
        t1.agentId,
        t1.teamId,
        t1.teamName,
        t2.agentName,
        t2.agentMobile,
        t2.agentBindUserId,
        t2.regionName,
        t2.organizationName,
        t1.teamScale
        FROM sm_agent_team t1
        LEFT JOIN sm_agent t2 ON t1.agentId = t2.agentId
        WHERE t2.agentStatus > -1
        <if test='regionName != null'>
            AND t2.regionName=#{regionName}
        </if>
        <if test='organizationName != null'>
            AND t2.organizationName=#{organizationName}
        </if>
        <if test='teamName != null'>
            AND t1.teamName LIKE CONCAT(#{teamName}, "%")
        </if>
        <if test='agentName != null'>
            AND (t2.agentName LIKE CONCAT(#{agentName}, '%') OR t2.agentMobile LIKE CONCAT(#{agentName}, '%') OR
            t2.agentJobNumber LIKE CONCAT(#{agentName}, '%'))
        </if>
        <if test='keyword != null'>
            AND (t2.agentName LIKE CONCAT(#{keyword}, '%') OR t2.agentMobile LIKE CONCAT(#{keyword}, '%')
            OR t2.agentJobNumber LIKE CONCAT(#{keyword}, '%')
            OR t1.teamName LIKE CONCAT(#{keyword}, '%')
            OR t1.teamId = #{keyword}
            )
        </if>
        GROUP BY t1.teamId
        ORDER BY t1.teamId DESC
    </select>

    <select id="getAgentTeamByTeamId" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmTeamDetailVO">
        SELECT
        t1.teamId,
        t1.agentId,
        t1.teamName,
        t2.agentName,
        t2.agentMobile,
        t2.agentJobNumber,
        t2.agentBindUserId,
        t1.teamScale,
        t2.regionName,
        t2.organizationName
        FROM sm_agent_team t1
        LEFT JOIN sm_agent t2 ON t1.agentId = t2.agentId
        WHERE t1.teamId = #{teamId}
    </select>

    <select id="listSingleAgentTeamOrder" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmAgentTeamOrderDetailVO">
        SELECT
        #{startDate} AS accountTimeFrom,
        #{endDate} AS accountTimeTo,
        SUM(t5.qty) AS orderQty,
        SUM(CASE WHEN t5.totalAmount >= 50 THEN t5.qty ELSE 0 END) AS checkQty,
        SUM(t5.totalAmount) AS orderAmount,
        t1.agentId,
        t1.agentName,
        t1.agentMobile
        FROM
        sm_agent t1
        LEFT JOIN sm_order_commission t5
        ON t5.agentId = t1.agentId
        WHERE t1.teamId=#{teamId} AND t1.agentStatus > -1
        <if test='startDate != null'>
            <![CDATA[ AND t5.accountTime>=#{startDate} ]]>
        </if>
        <if test='endDate != null'>
            <![CDATA[ AND t5.accountTime<=#{endDate} ]]>
        </if>
        <if test='agentName != null'>
            AND (t1.agentName LIKE CONCAT(#{agentName},"%") OR t1.agentMobile LIKE CONCAT(#{agentName},"%") OR
            t1.agentJobNumber LIKE CONCAT(#{agentName},"%"))
        </if>
        GROUP BY t1.agentId
        ORDER BY SUM(t5.totalAmount) DESC
    </select>

    <select id="getSingleAgentTeamOrderSmy" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmAgentTeamOrderSmyVO">
        SELECT
        SUM(t2.qty) AS orderQty,
        SUM(CASE WHEN t2.totalAmount >= 50 THEN t2.qty ELSE 0 END) AS checkQty,
        SUM(t2.totalAmount) AS orderAmount
        FROM
        sm_agent t1
        LEFT JOIN sm_order_commission t2
        ON t2.agentId = t1.agentId
        WHERE t1.teamId=#{teamId} AND t1.agentStatus > -1
        <if test='startDate != null'>
            <![CDATA[ AND t2.accountTime>=#{startDate} ]]>
        </if>
        <if test='endDate != null'>
            <![CDATA[ AND t2.accountTime<=#{endDate} ]]>
        </if>
        <if test='agentName != null'>
            AND (
            t1.agentName LIKE CONCAT(#{agentName},"%") OR t1.agentJobNumber LIKE CONCAT(#{agentName},"%") OR
            t1.agentJobNumber LIKE CONCAT(#{agentName},"%")
            )
        </if>
    </select>

    <select id="listAgentCms" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmAgentTimeOrderListVO">
        SELECT
        #{startDate} AS accountTimeFrom,
        #{endDate} AS accountTimeTo,
        SUM(t3.qty) AS orderQty,
        SUM(CASE WHEN t3.totalAmount >= 50 THEN t3.qty ELSE 0 END) AS checkQty,
        SUM(t3.totalAmount) AS orderAmount,
        t1.agentId,
        t1.agentName,
        t1.agentMobile,
        t1.agentBindUserId,
        t2.teamName,
        t1.regionName,
        t1.organizationName
        FROM
        sm_agent t1
        LEFT JOIN sm_agent_team t2
        ON t2.teamId = t1.teamId
        LEFT JOIN sm_order_commission t3
        ON t3.agentId = t1.agentId
        <if test='startDate != null'>
            <![CDATA[ AND t3.accountTime>=#{startDate} ]]>
        </if>
        <if test='endDate != null'>
            <![CDATA[ AND t3.accountTime<=#{endDate} ]]>
        </if>
        WHERE t1.agentStatus > -1
        <if test='agentName != null'>
            AND (t1.agentName LIKE CONCAT(#{agentName},"%") OR t1.agentMobile LIKE CONCAT(#{agentName},"%") OR
            t1.agentJobNumber LIKE CONCAT(#{agentName},"%"))
        </if>
        <if test='teamName != null'>
            AND (t2.teamId=#{teamName} OR t2.teamName LIKE CONCAT(#{teamName},"%"))
        </if>
        <if test='regionName != null'>
            AND t1.regionName=#{regionName}
        </if>
        <if test='organizationName != null'>
            AND t1.organizationName=#{organizationName}
        </if>
        GROUP BY t1.agentId DESC
    </select>

    <select id="getAgentCmsSmy" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmAgentTimeOrderSmyVO">
        SELECT
        SUM(t3.qty) AS orderQty,
        SUM(CASE WHEN t3.totalAmount >= 50 THEN t3.qty ELSE 0 END) AS checkQty,
        SUM(t3.totalAmount) AS orderAmount
        FROM
        sm_agent t1
        LEFT JOIN sm_agent_team t2
        ON t2.teamId = t1.teamId
        LEFT JOIN sm_order_commission t3
        ON t3.agentId = t1.agentId
        WHERE t1.agentStatus > -1
        <if test='startDate != null'>
            <![CDATA[ AND t3.accountTime>=#{startDate} ]]>
        </if>
        <if test='endDate != null'>
            <![CDATA[ AND t3.accountTime<=#{endDate} ]]>
        </if>
        <if test='agentName != null'>
            AND (t1.agentName LIKE CONCAT(#{agentName},"%") OR t1.agentJobNumber LIKE CONCAT(#{agentName},"%") OR
            t1.agentJobNumber LIKE CONCAT(#{agentName},"%"))
        </if>
        <if test='teamName != null'>
            AND (t2.teamId=#{teamName} OR t2.teamName LIKE CONCAT(#{teamName},"%"))
        </if>
        <if test='regionName != null'>
            AND t1.regionName=#{regionName}
        </if>
        <if test='organizationName != null'>
            AND t1.organizationName=#{organizationName}
        </if>
    </select>

    <select id="listAgentTeamCms" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmAgentTeamOrderListVO">
        SELECT
        #{startDate} AS accountTimeFrom,
        #{endDate} AS accountTimeTo,
        SUM(t3.qty) AS orderQty,
        SUM(t3.totalAmount) AS teamOrderAmount,
        SUM(CASE WHEN t3.totalAmount >= 50 THEN t3.qty ELSE 0 END) AS checkQty,
        SUM(t3.paymentAmount) AS teamOrderCms,
        count(DISTINCT t3.agentId) AS perQty,
        t1.teamId,
        t1.teamName,
        t1.teamScale,
        t4.agentId,
        t4.agentName,
        t4.agentMobile,
        t4.agentJobNumber,
        t4.agentBindUserId,
        t4.regionName,
        t4.organizationName
        FROM
        sm_agent_team t1
        LEFT JOIN sm_agent t2
        ON t1.teamId = t2.teamId
        LEFT JOIN sm_order_commission t3
        ON t3.agentId = t2.agentId
        <if test='startDate != null'>
            <![CDATA[ AND t3.accountTime>=#{startDate} ]]>
        </if>
        <if test='endDate != null'>
            <![CDATA[ AND t3.accountTime<=#{endDate} ]]>
        </if>
        LEFT JOIN sm_agent t4
        ON t4.agentId = t1.agentId
        WHERE t2.agentStatus > -1
        <if test='teamId != null'>
            AND t1.teamId=#{teamId}
        </if>
        <if test='teamName != null'>
            AND t1.teamName LIKE CONCAT(#{teamName}, '%')
        </if>
        <if test='regionName != null'>
            AND t4.regionName=#{regionName}
        </if>
        <if test='organizationName != null'>
            AND t4.organizationName=#{organizationName}
        </if>
        GROUP BY t1.teamId DESC
    </select>

    <select id="getAgentTeamCmsSmy" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmAgentTeamOrderSmyVO">
        SELECT
        SUM(t1.qty) AS orderQty,
        SUM(CASE WHEN t1.totalAmount >= 50 THEN t1.qty ELSE 0 END) AS checkQty,
        SUM(t1.totalAmount) AS orderAmount
        FROM
        sm_order_commission t1
        LEFT JOIN sm_agent t4
        ON t4.agentId = t1.agentId
        LEFT JOIN sm_agent t5
        ON t5.agentId = t1.agentId
        LEFT JOIN sm_agent_team t6
        ON t6.teamId = t5.teamId
        WHERE t1.agentId > 0
        <if test='startDate != null'>
            <![CDATA[ AND t1.accountTime>=#{startDate} ]]>
        </if>
        <if test='endDate != null'>
            <![CDATA[ AND t1.accountTime<=#{endDate} ]]>
        </if>
        <if test='teamId != null'>
            AND t6.teamId=#{teamId}
        </if>
        <if test='teamName != null'>
            AND t6.teamName=#{teamName}
        </if>
        <if test='regionName != null'>
            AND t4.regionName=#{regionName}
        </if>
        <if test='organizationName != null'>
            AND t4.organizationName=#{organizationName}
        </if>
    </select>

    <select id="getWxAgentTeamHome" resultType="com.cfpamf.ms.insur.weixin.pojo.vo.WxTeamHomeVO">
        SELECT
        t1.agentId,
        t1.teamId,
        t2.teamName,
        t1.agentName,
        t2.teamScale,
        t3.levelName AS agentLevel,
        t4.teamAllowance,
        t5.teamOrderAmount
        FROM
        sm_agent t1
        LEFT JOIN sm_agent_team t2
        ON t1.agentId = t2.agentId
        LEFT JOIN sm_agent_level_allowance_setting t3
        ON t1.levelId = t3.levelId
        LEFT JOIN
        (SELECT
        SUM(allowanceAmount) AS teamAllowance
        FROM
        sm_agent_allowance_month
        WHERE agentId = #{agentId}
        ) t4
        ON 1 = 1
        LEFT JOIN
        (SELECT
        SUM(totalAmount) AS teamOrderAmount
        FROM
        sm_agent t1
        LEFT JOIN sm_order_commission t2 on t1.agentId = t2.agentId
        WHERE t1.teamId = #{teamId}
        ) t5
        ON 1 = 1
        WHERE t1.agentId = #{agentId}
    </select>

    <select id="listAgentLevelAllowanceSettings"
            resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmAgentLevelAllowanceSettingVO">
        SELECT * FROM sm_agent_level_allowance_setting WHERE enabled_flag = 0
        ORDER BY sorting ASC, teamScaleFrom ASC, teamScaleTo ASC, teamPerRatio ASC, teamOrderQty ASC, teamOrderAmount ASC
    </select>

    <insert id="insertAgentLevelBonusSetting" useGeneratedKeys="true" keyProperty="levelId">
        INSERT INTO sm_agent_level_allowance_setting (
            levelName,
            teamScaleFrom,
            teamScaleTo,
            teamPerRatio,
            teamOrderQty,
            teamOrderAmount,
            allowanceRatio,
            sorting
        )
        VALUES
          (
            #{levelName},
            #{teamScaleFrom},
            #{teamScaleTo},
            #{teamPerRatio},
            #{teamOrderQty},
            #{teamOrderAmount},
            #{allowanceRatio},
            #{sorting}
          );
    </insert>

    <update id="updateAgentTeamScale">
        UPDATE
        sm_agent_team t1
        SET
        t1.teamScale =
        (
        SELECT
        COUNT(1)
        FROM
        sm_agent
        WHERE teamId = #{teamId}
        AND agentStatus > -1
        )
        WHERE t1.teamId = #{teamId}
    </update>

    <update id="updateAgentStatus">
        UPDATE
        sm_agent
        SET agentStatus = #{agentStatus}
        WHERE
        agentId IN
        <foreach collection="agentIds" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <update id="updateAgentLevel">
        UPDATE
        sm_agent
        SET levelId = #{levelId}
        WHERE
        agentId = #{agentId}
    </update>

    <update id="updateAgentTeamName">
        UPDATE
        sm_agent_team
        SET teamName = #{teamName}
        WHERE
        teamId = #{teamId}
    </update>

    <update id="updateAgentType">
        UPDATE
        sm_agent
        SET agentType = #{agentType}
        WHERE
        agentId = #{agentId}
    </update>

    <update id="updateAgentParentAgentId">
        UPDATE
        sm_agent t1
        LEFT JOIN sm_agent t2 ON t2.agentId = #{newParentAgentId}
        SET t1.parentAgentId = #{newParentAgentId},
        t1.agentPath = CONCAT(t2.agentPath, '/', t1.agentId),
        t1.teamId = t2.teamId,
        t1.teamPath = t2.teamPath
        WHERE 1=1
        <if test='oldParentAgentId != null'>
            AND t1.parentAgentId = #{oldParentAgentId}
        </if>
        <if test='agentId != null'>
            AND t1.agentId = #{agentId}
        </if>
    </update>

    <update id="updateAgentEmployeeInfo">
        UPDATE auth_user SET agentTopUserId = #{userId} WHERE userId = #{userId};
        UPDATE sm_agent SET agentBindUserId = #{userId}, agentTopUserId = #{userId}, agentPath = agentId, parentAgentId = null, agentPathLevel=1, agentType = 1 WHERE agentMobile = #{agentMobile};
        INSERT INTO sm_agent_team(agentId, agentTopUserId, teamPathLvel, teamName, teamScale) SELECT agentId, agentTopUserId, 1, CONCAT(agentName, '的团队'), 1 FROM sm_agent WHERE agentTopUserId = #{userId};
        UPDATE sm_agent_team SET teamPath = teamId WHERE agentTopUserId=#{userId};
        UPDATE sm_agent t1 LEFT JOIN sm_agent_team t2 ON t1.agentTopUserId = t2.agentTopUserId  SET t1.agentPath = t1.agentId, t1.teamId = t2.teamId, t1.teamPath = t2.teamPath  WHERE t1.agentTopUserId = #{userId};
        UPDATE sm_agent t1 LEFT JOIN sm_agent t2 ON t1.parentAgentId = t2.agentId SET t1.teamId = t2.teamId, t1.teamPath = t2.teamPath, t1.agentPath = CONCAT(t2.agentPath, "/" , t1.agentId), t1.agentTopUserId = #{userId} WHERE  t2.agentBindUserId = #{userId};
  </update>

    <update id="updateAgentLevelBonusSetting">
        UPDATE sm_agent_level_allowance_setting SET
          levelName = #{levelName},
          teamScaleFrom = #{teamScaleFrom},
          teamScaleTo = #{teamScaleTo},
          teamPerRatio = #{teamPerRatio},
          teamOrderQty = #{teamOrderQty},
          teamOrderAmount = #{teamOrderAmount},
          allowanceRatio = #{allowanceRatio}
        WHERE levelId = #{levelId}
    </update>


    <update id="deleteAgentLevelBonusSetting">
        UPDATE sm_agent_level_allowance_setting SET enabled_flag = 1 WHERE levelId = #{levelId}
    </update>

    <select id="listAgentCmsSettings" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmAgentCmsSettingVO">
        SELECT * FROM sm_agent_commission_setting WHERE enabled_flag = 0
    </select>

    <update id="updateAgentCmsSetting">
       UPDATE sm_agent_commission_setting
       SET fstLevelRatio = #{fstLevelRatio}, sedLevelRatio = #{sedLevelRatio}
       WHERE sacsId = #{sacsId}
    </update>

    <insert id="insertAgentCmsSetting">
      INSERT INTO sm_agent_commission_setting(agentType,fstLevelRatio, sedLevelRatio) VALUES (#{agentType},#{fstLevelRatio},#{sedLevelRatio}) ;
    </insert>

    <select id="listAgentMonthAllowance"
            resultType="com.cfpamf.ms.insur.admin.pojo.vo.AgentMonthAllowanceVO">
        SELECT * FROM sm_agent_allowance_month
        WHERE 1=1
        <if test='yearMonth != null'>
            AND yearMonth = #{yearMonth}
        </if>
        <if test='teamName != null'>
            AND teamName = #{teamName}
        </if>
        <if test='teamId != null'>
            AND teamId = #{teamId}
        </if>
        <if test='agentId != null'>
            AND agentId = #{agentId}
        </if>
    </select>

    <insert id="insertAgentMonthAllowance" useGeneratedKeys="true" keyProperty="atbsId">
        INSERT INTO sm_agent_allowance_month (
          yearMonth,
          teamId,
          teamName,
          agentId,
          agentName,
          agentMobile,
          agentJobNumber,
          teamOrderAmount,
          teamOrderCms,
          allowanceRatio,
          allowanceAmount,
          levelId,
          teamScale,
          personQty,
          orderQty
        )
        VALUES
          (
            #{yearMonth},
            #{teamId},
            #{teamName},
            #{agentId},
            #{agentName},
            #{agentMobile},
            #{agentJobNumber},
            #{teamOrderAmount},
            #{teamOrderCms},
            #{allowanceRatio},
            #{allowanceAmount},
            #{levelId},
            #{teamScale},
            #{personQty},
            #{orderQty}
          );
    </insert>

    <select id="listWxTeamAgents" resultType="com.cfpamf.ms.insur.weixin.pojo.vo.WxAgentVO">
        SELECT t1.agentId, t1.agentMobile, t1.agentName, t1.agentJobNumber, t1.agentType, t2.wxImgUrl AS agentWxImgUrl
        FROM sm_agent t1
        LEFT JOIN auth_user t2 ON t1.agentId = t2.agentId AND t2.enabled_flag = 0
        WHERE
        t1.teamId = #{teamId} AND t1.agentStatus > -1
        <if test='parentAgentId != null and agentName == null'>
            AND t1.parentAgentId = #{parentAgentId}
        </if>
        <if test='agentName != null'>
            AND (
            t1.agentName LIKE CONCAT(#{agentName},'%')
            OR t1.agentMobile LIKE CONCAT(#{agentName},'%')
            OR t1.agentJobNumber LIKE CONCAT(#{agentName},'%')
            )
        </if>
        GROUP BY t1.agentId
        ORDER BY t1.agentId ASC
    </select>

    <select id="listTeamParentAgents" resultType="com.cfpamf.ms.insur.weixin.pojo.vo.WxAgentVO">
        SELECT t1.agentId, t1.agentMobile, t1.agentName, t1.agentJobNumber, t1.agentType
        FROM sm_agent t1
        WHERE
        t1.teamId = #{teamId} AND t1.agentStatus > -1 AND agentType IN (1, 2)
        ORDER BY t1.agentId ASC
    </select>

    <select id="listAgentCmsDetails" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmAgentCmsDetailVO">
        SELECT * FROM sm_agent_commission WHERE fhOrderId = #{fhOrderId}
    </select>

    <select id="listAgentMonthCms" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmAgentMonthCmsVO">
        SELECT
        #{startDate} AS accountTimeFrom,
        #{endDate} AS accountTimeTo,
        t2.agentName, t2.agentMobile, t2.agentBindUserId, t3.teamName,
        SUM(CASE WHEN cmsType = 1 THEN qty ELSE 0 END ) AS orderQty,
        SUM(CASE WHEN cmsType = 1 THEN totalAmount ELSE 0 END ) AS orderAmount,
        SUM(fstLevelCms) AS fstLevelCms,
        SUM(sedLevelCms) AS sedLevelCms,
        SUM(allowance) AS allowance
        FROM sm_agent t2
        LEFT JOIN sm_agent_commission t1 ON t1.cmsAgentId = t2.agentId
        <if test='startDate != null '>
            <![CDATA[ AND accountTime >= #{startDate} ]]>
        </if>
        <if test='endDate != null '>
            <![CDATA[ AND accountTime <= #{endDate} ]]>
        </if>
        LEFT JOIN sm_agent_team t3 ON t3.teamid = t2.teamid
        WHERE 1=1
        <if test='agentName != null '>
            AND (
            t2.agentName LIKE CONCAT(#{agentName},'%')
            OR t2.agentMobile LIKE CONCAT(#{agentName},'%')
            OR t2.agentJobNumber LIKE CONCAT(#{agentName},'%')
            )
        </if>
        <if test='teamName != null '>
            AND t3.teamName LIKE CONCAT(#{teamName},'%')
        </if>
        <if test='teamId != null '>
            AND t3.teamId = #{teamId}
        </if>
        GROUP BY t2.agentId
    </select>

    <select id="listWxTeamCmsSmy" resultType="com.cfpamf.ms.insur.weixin.pojo.vo.WxAgentTeamCmsSmyVO">
        SELECT
        t1.agentId, t1.agentName, t1.agentMobile,
        SUM(CASE WHEN t2.cmsType = 1 THEN t2.qty ELSE 0 END ) AS orderQty,
        SUM(CASE WHEN t2.cmsType = 1 THEN t2.totalAmount ELSE 0 END ) AS totalAmount,
        SUM(t2.fstLevelCms) AS fstLevelCms,
        SUM(t2.sedLevelCms) AS sedLevelCms
        FROM sm_agent t1
        LEFT JOIN sm_agent_commission t2 ON t2.cmsAgentId = t1.agentId AND <![CDATA[ t2.cmsType < 3 ]]>
        WHERE t1.teamId=#{teamId} AND t1.agentStatus > -1
        <if test='agentId != null '>
            AND t1.agentId = #{agentId}
        </if>
        <if test='startDate != null '>
            <![CDATA[ AND t2.accountTime >= #{startDate} ]]>
        </if>
        <if test='endDate != null '>
            <![CDATA[ AND t2.accountTime <= #{endDate} ]]>
        </if>
        <if test='agentName != null '>
            AND (
            t1.agentName LIKE CONCAT(#{agentName},'%')
            OR t1.agentMobile LIKE CONCAT(#{agentName},'%')
            OR t1.agentJobNumber LIKE CONCAT(#{agentName},'%')
            )
        </if>
        GROUP BY t1.agentId
    </select>

    <select id="countTeamByTeamName" resultType="java.lang.Integer">
        SELECT COUNT(1) FROM sm_agent_team WHERE teamName = #{teamName} AND teamId != #{excludeTeamId}
    </select>

    <select id="listWxAgentCmsSmy" resultType="com.cfpamf.ms.insur.weixin.pojo.vo.WxAgentTeamCmsDetailVO">
        SELECT t1.*, t2.productName, t3.planName,  t4.agentName AS sedAgentName, t5.agentName AS fstAgentName
        FROM sm_agent_commission t1
        LEFT JOIN sm_product t2 ON t1.productId = t2.id
        LEFT JOIN sm_plan t3 ON t1.planId = t3.id
        LEFT JOIN sm_agent t4 ON t4.agentId = t1.orderAgentId
        LEFT JOIN sm_agent t5 ON t5.agentId = t1.cmsAgentId
        WHERE t1.cmsAgentId = #{agentId} AND  <![CDATA[ t1.cmsType < 3 ]]>
        <if test='agentName != null '>
            AND
            (
            t4.agentName LIKE CONCAT(#{agentName},'%') OR t4.agentMobile LIKE CONCAT(#{agentName},'%')
            )
        </if>
        <if test='startDate != null '>
            <![CDATA[ AND t1.accountTime >= #{startDate} ]]>
        </if>
        <if test='endDate != null '>
            <![CDATA[ AND t1.accountTime <= #{endDate} ]]>
        </if>
        ORDER BY t1.accountTime DESC
    </select>

    <insert id="insertAgentCmsDetail">
        INSERT INTO sm_agent_commission (
          cmsType,
          accountTime,
          fhOrderId,
          productId,
          planId,
          insIdNumber,
          totalAmount,
          qty,
          appStatus,
          orderAgentId,
          cmsAgentId,
          fstLevelCms,
          sedLevelCms,
          allowance
        )
        VALUES
          (
            #{cmsType},
            #{accountTime},
            #{fhOrderId},
            #{productId},
            #{planId},
            #{insIdNumber},
            #{totalAmount},
            #{qty},
            #{appStatus},
            #{orderAgentId},
            #{cmsAgentId},
            #{fstLevelCms},
            #{sedLevelCms},
            #{allowance}
          ) ;
    </insert>

    <insert id="insertAgentCmsSurrenderDetail">
        INSERT INTO sm_agent_commission (cmsType, accountTime, fhOrderId, productId, planId
            , insIdNumber, totalAmount, qty, appStatus, orderAgentId
            , cmsAgentId, fstLevelCms, sedLevelCms, allowance)
        SELECT cmsType, accountTime, fhOrderId, productId, planId
            , insIdNumber, -totalAmount, -qty, 4, orderAgentId
            , cmsAgentId, -fstLevelCms, -sedLevelCms, allowance
        FROM sm_agent_commission
        WHERE fhOrderId = #{fhOrderId}
            AND insIdNumber = #{insIdNumber}
    </insert>

    <delete id="deleteAgentCmsDetail">
        DELETE FROM sm_agent_commission WHERE sacId = #{sacId}
    </delete>

    <delete id="deleteAgentCommissionByOrderId">
        DELETE FROM sm_agent_commission WHERE fhOrderId = #{fhOrderId}
    </delete>

    <insert id="insertAgent" useGeneratedKeys="true" keyProperty="agentId">
        INSERT INTO sm_agent (
        agentMobile,
        agentTopUserId,
        agentBindUserId,
        parentAgentId,
        agentPath,
        agentStatus,
        agentJobNumber,
        teamId,
        teamPath,
        levelId,
        regionName,
        organizationName,
        ccUserNo
        )
        VALUES(
        #{agentMobile},
        #{agentTopUserId},
        #{agentBindUserId},
        #{parentAgentId},
        #{agentPath},
        #{agentStatus},
        #{agentJobNumber},
        #{teamId},
        #{teamPath},
        #{levelId},
        #{regionName},
        #{organizationName},
        #{ccUserNo}
        );
    </insert>

    <select id="getAgentByAgentMobile" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmAgentVO">
        SELECT * FROM sm_agent WHERE agentMobile = #{agentMobile}  AND agentStatus = 0 LIMIT 1
    </select>

    <update id="updateAgentPathInfo">
        UPDATE sm_agent SET agentPath =#{agentPath} WHERE agentId = #{agentId}
    </update>

    <insert id="insertAgentTeam" useGeneratedKeys="true" keyProperty="teamId">
        INSERT INTO sm_agent_team (agentId, agentTopUserId,teamName, teamPath, teamPathLevel, teamLevel, teamScale)
        VALUES (#{agentId}, #{agentTopUserId}, NULL, NULL, 1 ,1, 1)
    </insert>

    <update id="updateAgentTeamInfo">
        UPDATE sm_agent t1 LEFT JOIN sm_agent_team t2 ON t1.agentId = t2.agentId SET t1.teamId = t2.teamId, t1.teamPath = t2.teamPath WHERE t1.agentId = #{agentId};
    </update>

    <update id="updateTeamPathInfo">
        UPDATE sm_agent_team t1 SET t1.teamPath = t1.teamId WHERE t1.agentId = #{agentId};
    </update>

    <update id="updateTeamNameInfo">
        UPDATE sm_agent_team t1 SET t1.teamName = #{teamName} WHERE t1.agentId = #{agentId};
    </update>

    <update id="updateAgentRealVerifyInfo">
        UPDATE sm_agent
        SET
        agentName = #{agentName},
        agentIdCard = #{agentIdCard},
        idCardPicUrl1 = #{idCardPicUrl1},
        idCardPicUrl2 = #{idCardPicUrl2},
        agentBankName = #{agentBankName},
        agentBankCardNo = #{agentBankCardNo},
        agentStatus = 0,
        ccCustNo = #{ccCustNo}
        WHERE agentId = #{agentId}
    </update>
</mapper>
