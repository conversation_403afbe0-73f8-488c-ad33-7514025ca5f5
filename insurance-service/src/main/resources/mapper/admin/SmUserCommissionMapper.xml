<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.SmUserCommissionMapper">
    <select id="listByQuery" resultType="com.cfpamf.ms.insur.facade.vo.UserCommissionVO">
        select month,org_code, company, ifnull(sum(commission_total),0) amt
        from sm_user_commission
            where month = #{yearMonth}
                <if test="orgCode!=null">
                   and org_code = #{orgCode}
                </if>
        group by org_code

    </select>
</mapper>
