<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.label.SmRuleOccurCondRelationMapper">
    <update id="batchInsert" useGeneratedKeys="true" parameterType="java.util.List">
        insert into sm_rule_occur_cond_relation(rule_id,cond_code,cond_name,enabled_flag,create_by,update_by)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.ruleId},#{item.condCode},#{item.condName},#{item.enabledFlag},#{item.createBy},#{item.updateBy})
        </foreach>
        ON DUPLICATE KEY UPDATE
        rule_id=values(rule_id),cond_code=values(cond_code),cond_name=values(cond_name)
        ,enabled_flag=values(enabled_flag),create_by=values(create_by),update_by=values(update_by)
    </update>
</mapper>
