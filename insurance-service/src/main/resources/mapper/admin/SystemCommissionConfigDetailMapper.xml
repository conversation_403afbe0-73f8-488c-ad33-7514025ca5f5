<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.commission.SystemCommissionConfigDetailMapper">

    <select id="listCommissionConfigDetailByProductIdAndMatchingTime"
            resultType="com.cfpamf.ms.insur.admin.pojo.po.commission.SystemCommissionConfigDetail">

        SELECT sccd.* FROM system_commission_config scc
        left join system_commission_config_detail sccd on scc.id = sccd.config_id
        WHERE scc.product_id = #{productId} and scc.enabled_flag=0 and sccd.enabled_flag=0
          and scc.release_flag = 1 AND scc.type = 2
        and
        <![CDATA[ scc.start_time<= #{matchingTime} and #{matchingTime} <= scc.end_time ]]>
        <if test="planId!=null">
            and sccd.plan_id = #{planId}
        </if>
    </select>

    <select id="listCommissionConfigDetailByProductId"
            resultType="com.cfpamf.ms.insur.admin.pojo.po.commission.SystemCommissionConfigDetail">

        SELECT sccd.* FROM system_commission_config scc
        left join system_commission_config_detail sccd on scc.id = sccd.configId
        WHERE scc.product_id = #{productId} and scc.enabled_flag=0 and sccd.enabled_flag=0
        <if test="planId!=null">
            and sccd.plan_id = #{planId}
        </if>
        order by scc.update_time desc;
    </select>

    <select id="queryEffectiveConfigByPlans"
            resultType="com.cfpamf.ms.insur.admin.pojo.po.commission.FastSysCommissionConfigPO">
        select
                t1.config_id,
                t1.plan_id,
                t1.commission_rate,
                t2.type,
                t2.product_id,
                t2.start_time,
                t2.end_time
        from system_commission_config_detail t1
        left join system_commission_config t2 on t1.config_id=t2.id
        where t2.enabled_flag = 0
        <if test = "releaseFlag!=null" >
            and t2.release_flag = #{releaseFlag}
        </if>
        <![CDATA[ AND t2.start_time <= now() ]]>
        <![CDATA[ AND t2.end_time   >= now() ]]>
        AND t1.plan_id in
        <foreach item="item" index="index" open="(" separator="," close=")" collection="planIdList">
            #{item}
        </foreach>
    </select>
    <select id="queryCommissionConfig"
            resultType="com.cfpamf.ms.insur.admin.pojo.po.commission.FastSysCommissionConfigPO">
        select
        t1.config_id,
        t1.plan_id,
        t1.commission_rate,
        t2.type,
        t2.product_id,
        t2.start_time,
        t2.end_time
        from system_commission_config_detail t1
        left join system_commission_config t2 on t1.config_id=t2.id
        where t2.enabled_flag = 0
        <if test = "releaseFlag!=null" >
            and t2.release_flag = #{releaseFlag}
        </if>
        AND t1.plan_id in
        <foreach item="item" index="index" open="(" separator="," close=")" collection="planIdList">
            #{item}
        </foreach>
    </select>

    <update id="updateCommissionRate">
        insert into system_commission_config_log
            (type,product_id,product_name,plan_id,plan_Name,config_id,config_detail_id,period_num
            ,commission_rate,content,end_time,status)
        select
            t2.`type`,t3.`id`,t3.productName,t1.`plan_id`,t4.planName,t2.id,t1.id,t1.`period_num`,t1.`commission_rate`
            ,'变更2、3期佣金比例为10%' as content,t2.`end_time`,0 as status
        from `system_commission_config_detail` t1
        left join `system_commission_config` t2 on t1.`config_id` = t2.`id` and t2.`enabled_flag` =0 and t2.`type` =3
        left join `sm_product` t3 on t2.product_id = t3.`id`
        left join `sm_plan` t4 on t1.`plan_id` = t4.id
        where
            t3.`long_insurance` = 0
            <![CDATA[
                and ((t2.start_time <= now() and t2.end_time >= now() or t2.end_time < now()))
            ]]>
            and t1.`period_num` in (2,3) and t1.`enabled_flag` =0;

        update `system_commission_config_detail` t1
        left join `system_commission_config` t2 on t1.`config_id` = t2.`id` and t2.`enabled_flag` =0 and t2.`type` =3
        left join `sm_product` t3 on t2.product_id = t3.`id`
        left join `sm_plan` t4 on t1.`plan_id` = t4.id
        set t1.`commission_rate` = 10
        where
            t3.`long_insurance` = 0
            <![CDATA[
                and ((t2.start_time <= now() and t2.end_time >= now() or t2.end_time < now()))
            ]]>
            and t1.`period_num` in (2,3) and t1.`enabled_flag` =0;
    </update>
</mapper>
