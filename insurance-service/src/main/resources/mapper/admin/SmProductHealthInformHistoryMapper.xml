<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.product.SmProductHealthInformHistoryMapper">
    <insert id="initNewHistoryVersion">
        INSERT INTO sm_product_health_inform_history (product_id, product_health_inform_id, `name`,
                                                      inform_object, risk_code, content, ai_check_flag, ai_check_way,
                                                      question_bank_file_path, version,
                                                      enabled_flag, create_by, update_by, create_time, update_time)
        SELECT product_id,
               id,
               `name`,
               inform_object,
               risk_code,
               content,
               ai_check_flag,
               ai_check_way,
               question_bank_file_path,
               #{version},
               enabled_flag,
               create_by,
               update_by,
               create_time,
               update_time
        from sm_product_health_inform
        where product_id = #{productId}
          and enabled_flag = 0

    </insert>

    <select id="selectByProductIdAndVersion"
            resultType="com.cfpamf.ms.insur.admin.pojo.vo.product.SmProductHealthInformVo">
        select *
        from sm_product_health_inform_history
        where product_id = #{productId}
          and version = #{version}
          and enabled_flag = 0
    </select>


    <select id="selectByProductHealthInformIdAndVersion"
            resultType="com.cfpamf.ms.insur.admin.pojo.vo.product.SmProductHealthInformVo">
        select *
        from sm_product_health_inform_history
        where product_id = #{productId}
          and version = #{version}
          and product_health_inform_id = #{productHealthInformId}
          and enabled_flag = 0
    </select>
</mapper>
