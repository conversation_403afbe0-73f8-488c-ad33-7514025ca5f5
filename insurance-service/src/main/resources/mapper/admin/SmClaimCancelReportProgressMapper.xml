<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.claim.SmClaimCancelReportProgressMapper">
    <select id="getProgressList" resultType="com.cfpamf.ms.insur.admin.pojo.vo.ProgressVO">
        SELECT t1.id           as progressId
             , t1.step_code    as sCode
             , t1.step_name    as sName
             , t1.option_code  as oCode
             , t1.option_name  as oName
             , t1.option_type  as oType
             , t1.option_value as oValue
             , t1.create_time  as create_time
             , t2.userName     AS createBy
        FROM sm_claim_cancel_report_progress t1
                 LEFT JOIN auth_user t2 on t1.create_by = t2.userId
        WHERE t1.claim_cancel_report_id = #{claimCancelReportId}

    </select>
</mapper>