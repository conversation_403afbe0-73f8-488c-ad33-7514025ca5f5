<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.admin.claim.dao.SmClaimMailConfigMapper">

    <select id="queryClaimEmailParams"
            resultType="com.cfpamf.ms.insur.admin.pojo.vo.claim.ClaimEmailAllParams">
        select t2.policyNo
        from sm_claim t1
                 inner join sm_order_insured t2 on t1.insuredId = t2.id
                 inner join sm_order t3 on t2.fhOrderId = t3.fhOrderId
                 inner join sm_product t4 on t3.productId = t4.id
        where t1.enabled_flag = 0
          and t3.enabled_flag = 0
          and t4.enabled_flag = 0
          and t2.enabled_flag = 0
          and t1.id = #{claimId}
    </select>
    <select id="queryMailPage" resultType="com.cfpamf.ms.insur.admin.claim.vo.SmClaimMailConfigVO">
        select t1.*, t2.companyName, t3.name as scriptName
        from sm_claim_mail_config t1
                 left join sm_company t2 on t1.company_id = t2.id
                 left join system_groovy_rule t3 on t1.script_id = t3.id
        where t1.company_id = #{companyId}
          and t1.enabled_flag = 0
    </select>
    <select id="getConfigByCompanyId" resultType="com.cfpamf.ms.insur.admin.claim.vo.SmClaimMailConfigVO">
        select t1.*, t2.companyName, t3.name as scriptName, t3.groovy_code as script, t3.code as scriptCode, t3.id as scriptId
        from sm_claim_mail_config t1
                 left join sm_company t2 on t1.company_id = t2.id
                 left join system_groovy_rule t3 on t1.script_id = t3.id
        where t1.company_id = #{companyId}
          and t1.enabled_flag = 0
    </select>

    <select id="pageCompanyClaimMail" resultType="com.cfpamf.ms.insur.admin.claim.vo.SmClaimMailListVO">
        SELECT
            t1.companyIdentifier,
            t1.id AS companyId,
            t1.companyName,
            CASE
                WHEN t2.id IS NULL THEN
                    '未配置' ELSE '已配置'
                END AS config,
            t2.update_by,
            t2.update_time,
            t3.userName as updateByName
        FROM
            sm_company t1
                LEFT JOIN (
                SELECT
                    *
                FROM
                    sm_claim_mail_config t
                        INNER JOIN (
                        SELECT
                            company_Id AS companyId,
                            MAX( update_time ) AS updateTime
                        FROM
                            sm_claim_mail_config
                        GROUP BY
                            company_id
                    ) temp ON t.company_id = temp.companyId
                        AND temp.updateTime = t.update_time
            ) t2 ON t1.id = t2.company_id and t2.enabled_flag = 0
                LEFT JOIN auth_user t3 ON t3.userid = t2.update_by  and t3.enabled_flag = 0
        <where>
            <if test="companyId != null">
                and t1.id = #{companyId}
            </if>
        </where>

    </select>
</mapper>
