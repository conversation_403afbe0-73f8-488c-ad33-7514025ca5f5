<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.product.SmProductRiskMapper">
    <resultMap id="BaseResultMap" type="com.cfpamf.ms.insur.admin.pojo.po.product.SmProductRisk">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="product_id" jdbcType="INTEGER" property="productId"/>
        <result column="risk_key" jdbcType="VARCHAR" property="riskKey"/>
        <result column="risk_version" jdbcType="INTEGER" property="riskVersion"/>
        <result column="required" jdbcType="TINYINT" property="required"/>
        <result column="main_guarantee" jdbcType="TINYINT" property="mainGuarantee"/>
        <result column="enabled_flag" jdbcType="TINYINT" property="enabledFlag"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , product_id, risk_key, risk_version, required, main_guarantee, enabled_flag, create_by,
    update_by, create_time, update_time
    </sql>
    <update id="pushNewVersion">
        insert into sm_product_risk(product_id, risk_key, risk_version, required, main_guarantee, enabled_flag,
                                    create_by,
                                    create_time)
        select product_id,
               risk_key,
               risk_version,
               required,
               main_guarantee,
               enabled_flag,
               create_by,
               create_time
        from sm_product_risk_history
        where product_id = #{productId}
          and version = #{version}
          and enabled_flag = 0
    </update>
    <update id="softDelete">
        update sm_product_risk
        set enabled_flag = 1
        where product_id = #{productId}
    </update>

</mapper>