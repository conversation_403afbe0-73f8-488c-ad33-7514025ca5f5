<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.auto.AutoOrderCarMapper">
    <update id="logicDelete">
        update auto_order set enabled_flag =1 where order_no = #{fhOrderId}

    </update>

    <insert id="insertAutoOrderCarBatch" parameterType= "java.util.List">
        INSERT INTO auto_order_car (order_no, owner_person_name, owner_person_gender, owner_id_type,
        owner_id_number,owner_birthday,owner_cell_phone,is_transfer, use_props, car_model_name, first_reg_date, full_load,
        engine_num,transfer_date,plate_num,seat_cnt,price,car_user_type,syvehicle_type_code,displacement,vin,is_new_car, enabled_flag, create_time, update_time)
        VALUES
        <foreach collection="dtos" item="dto" index="index" separator=",">
            (#{dto.orderNo},#{dto.car.ownerPersonName},#{dto.car.ownerPersonGender},
            #{dto.car.ownerIdType},#{dto.car.ownerIdNumber},#{dto.car.ownerBirthday},#{dto.car.ownerCellPhone},#{dto.car.isTransfer},
            #{dto.car.useProps},#{dto.car.carModelName},#{dto.car.firstRegDate},
            #{dto.car.fullLoad},#{dto.car.engineNum},#{dto.car.transferDate},
            #{dto.car.plateNum},#{dto.car.seatCnt},#{dto.car.price},#{dto.car.carUserType},
            #{dto.car.syvehicleTypeCode},#{dto.car.displacement},#{dto.car.vin},#{dto.car.isNewCar},
            0,CURRENT_TIMESTAMP() , CURRENT_TIMESTAMP())
        </foreach>
    </insert>

    <update id="updateAutoOrderCarBatch" parameterType= "java.util.List">
        <foreach collection="dtos" item="dto" index="index" separator=";">
            update auto_order_car set owner_person_name=#{dto.car.ownerPersonName},owner_person_gender=#{dto.car.ownerPersonGender},
            owner_id_type=#{dto.car.ownerIdType},owner_id_number=#{dto.car.ownerIdNumber},
            owner_birthday=#{dto.car.ownerBirthday},owner_cell_phone=#{dto.car.ownerCellPhone},
            is_transfer=#{dto.car.isTransfer},use_props=#{dto.car.useProps},
            car_model_name=#{dto.car.carModelName},first_reg_date=#{dto.car.firstRegDate},full_load=#{dto.car.fullLoad},
            engine_num=#{dto.car.engineNum},transfer_date=#{dto.car.transferDate},
            plate_num=#{dto.car.plateNum},seat_cnt=#{dto.car.seatCnt},price=#{dto.car.price},car_user_type=#{dto.car.carUserType},
            syvehicle_type_code=#{dto.car.syvehicleTypeCode},displacement=#{dto.car.displacement},vin=#{dto.car.vin},is_new_car=#{dto.car.isNewCar}
            where order_no=#{dto.orderNo}
        </foreach>
    </update>
</mapper>
