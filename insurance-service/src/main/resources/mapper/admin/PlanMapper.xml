<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.product.PlanMapper">

    <select id="getById" resultType="com.cfpamf.ms.insur.admin.pojo.po.product.SmPlan">
        select *
        from sm_plan
        where id = #{id}
          and enabled_flag = 0
    </select>
    <select id="getByProductId" resultType="com.cfpamf.ms.insur.admin.pojo.po.product.SmPlan">
        select *
        from sm_plan
        where productId = #{productId}
          and enabled_flag = 0
    </select>
    <select id="getPlanSimpleVoByProductIdList"
            resultType="com.cfpamf.ms.insur.admin.distribution.config.vo.PlanSimpleVo">
        select id,planName,productId
        from sm_plan
        where
        enabled_flag = 0
        and productId in
        <foreach collection="productIdList" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectMinAmount" resultType="java.math.BigDecimal">
        select min(min_premium)
        from sm_plan
        where productId = #{productId}
          and enabled_flag = 0
    </select>

    <select id="selectMinAmountByPlanIdList" resultType="java.math.BigDecimal">
        select min(min_premium)
        from sm_plan
        where
        id in
        <foreach collection="salePlanIds" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
        and enabled_flag = 0
    </select>
</mapper>