<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.CustomerMapper">

    <insert id="insertCustomer" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO customer (fromXinDai, customerType, customerName, gender, idType, idNumber, birthday, cellPhone,
                              email, area, address, annualIncome, occupation, apPolicyQty, apPolicyAmount, isPolicyQty,
                              isPolicyAmount, create_time, update_time, areaName, newestAdmin, orgCode, addressProvider)
        VALUES (#{fromXinDai}, #{customerType}, #{customerName}, #{gender}, #{idType}, #{idNumber}, #{birthday},
                #{cellPhone}, #{email}, #{area}, #{address}, #{annualIncome}, #{occupation}, #{apPolicyQty},
                #{apPolicyAmount}, #{isPolicyQty}, #{isPolicyAmount}, CURRENT_TIMESTAMP(), CURRENT_TIMESTAMP(),
                #{areaName}, #{newestAdmin}, #{orgCode}, #{addressProvider})
    </insert>

    <update id="updateCustomer">
        Update customer SET

        <if test='customerName != null'>
            customerName = #{customerName},
        </if>
        <if test='gender != null'>
            gender = #{gender},
        </if>
        <if test='cellPhone != null'>
            cellPhone = #{cellPhone},
        </if>
        <if test='email != null'>
            email = #{email},
        </if>
        <if test='birthday != null'>
            birthday = #{birthday},
        </if>
        <if test='address != null'>
            address = #{address},
        </if>
        <if test='annualIncome != null'>
            annualIncome = #{annualIncome},
        </if>
        <if test='occupation != null'>
            occupation = #{occupation},
        </if>
        <if test='apPolicyQty != null'>
            apPolicyQty = apPolicyQty+#{apPolicyQty},
        </if>
        <if test='apPolicyAmount != null'>
            apPolicyAmount = apPolicyAmount+#{apPolicyAmount},
        </if>
        <if test='isPolicyQty != null'>
            isPolicyQty = isPolicyQty+#{isPolicyQty},
        </if>
        <if test='newestAdmin != null'>
            newestAdmin = #{newestAdmin},
        </if>
        <if test='areaName != null'>
            areaName = #{areaName},
        </if>
        <if test='area != null'>
            area = #{area},
        </if>
        <if test='addressProvider != null'>
            addressProvider = #{addressProvider},
        </if>
        <if test='orgCode != null'>
            orgCode = #{orgCode},
        </if>
        <if test='addressProvider != null'>
            addressProvider = #{addressProvider},
        </if>
        update_time = CURRENT_TIMESTAMP()
        WHERE idNumber = #{idNumber}
    </update>

    <select id="listCustomerIdNumbers" resultType="com.cfpamf.ms.insur.weixin.pojo.vo.WxCustomerVO">
        SELECT *, id AS customerId FROM customer WHERE IdNumber IN
        <foreach collection="idNumbers" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="queryCustomerByIdCard" resultType="com.cfpamf.ms.insur.weixin.pojo.dto.customer.WxCustomerDTO">
        SELECT *, id AS customerId FROM customer WHERE IdNumber IN
        <foreach collection="idNumbers" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>


    <insert id="insertCustomerRelationship" useGeneratedKeys="true">
        INSERT INTO customer_relationship (customerId1, customerId2, relationship, create_time, update_time)
        VALUES (#{customerId1}, #{customerId2}, #{relationship}, CURRENT_TIMESTAMP(), CURRENT_TIMESTAMP())
    </insert>

    <select id="listCustomerRelationship" resultType="com.cfpamf.ms.insur.admin.pojo.vo.CustomerRelationshipVO">
        SELECT *
        FROM customer_relationship
        WHERE customerId1 = #{customerId}
    </select>

    <insert id="insertCustomerProperty" useGeneratedKeys="true">
        INSERT INTO customer_property (customerId, carPlateNo, carManufacturerModel, chassisNumber, engineNo,
                                       approvedNum, hourseType, hourseNo, propertyAddress, hourseAge, create_time,
                                       update_time)
        VALUES (#{customerId}, #{carPlateNo}, #{carManufacturerModel}, #{chassisNumber}, #{engineNo}, #{approvedNum},
                #{hourseType}, #{hourseNo}, #{propertyAddress}, #{hourseAge}, CURRENT_TIMESTAMP(), CURRENT_TIMESTAMP())
    </insert>

    <select id="listCustomerProperty" resultType="com.cfpamf.ms.insur.admin.pojo.vo.CustomerPropertyVO">
        SELECT *
        FROM customer_property
        WHERE customerId = #{customerId}
    </select>

    <select id="listWxCustomer" resultType="com.cfpamf.ms.insur.weixin.pojo.vo.WxCustomerVO">
        SELECT DISTINCT t2.id AS customerId, t2.customerName, t2.birthday, t2.idType, t2.idNumber, t2.cellPhone,
        t2.email, t2.address, t2.fromXinDai,t2.areaName,t2.area
        <if test='userType == "employee"'>
            FROM customer_admin t1
            LEFT JOIN customer t2 ON t1.customerId = t2.id
            WHERE t1.customerAdminId=#{userId}
        </if>
        <if test='userType == "agent"'>
            FROM customer_agent t1
            LEFT JOIN customer t2 ON t1.customerId = t2.id
            WHERE t1.agentId=#{userId}
        </if>
        AND t2.id IS NOT NULL
        <if test='keyword != null'>
            AND (t2.customerName LIKE CONCAT('%',#{keyword},'%') OR t2.cellPhone LIKE CONCAT('%',#{keyword},'%'))
        </if>
        ORDER BY t2.customerName ASC
    </select>

    <select id="getCustomerById" resultType="com.cfpamf.ms.insur.weixin.pojo.vo.WxCustBaseVO">
        SELECT *, id AS customerId
        FROM customer
        WHERE id = #{customerId}
    </select>

    <select id="getCustomerByIdNumber" resultType="com.cfpamf.ms.insur.weixin.pojo.vo.WxCustBaseVO">
        SELECT *, id AS customerId
        FROM customer
        WHERE idNumber = #{idNumber} LIMIT 1
    </select>

    <select id="listWxCustomerShipById" resultType="com.cfpamf.ms.insur.weixin.pojo.vo.WxCustShipVO">
        SELECT t2.*, t2.id AS customerId, t1.relationship
        FROM customer_relationship t1
                 LEFT JOIN customer t2 ON t1.customerId2 = t2.id
        WHERE t1.customerId1 = #{customerId}
    </select>

    <select id="queryCustomerShipByCustomerId" resultType="com.cfpamf.ms.insur.weixin.pojo.vo.WxCustShipVO">
        SELECT t2.*, t2.id AS customerId, t1.relationship
        FROM customer_relationship t1
        LEFT JOIN customer t2 ON t1.customerId2 = t2.id
        WHERE t2.id = #{customerId}
    </select>

    <select id="listWxCustomerShipByIdNumber" resultType="com.cfpamf.ms.insur.weixin.pojo.vo.WxCustShipVO">
        SELECT t2.*, t2.id AS customerId, t1.relationship
        FROM customer_relationship t1
        LEFT JOIN customer t2 ON t1.customerId2 = t2.id
        WHERE t2.IdNumber = #{idNumber}
    </select>

    <select id="listWxCustomerPropertyById" resultType="com.cfpamf.ms.insur.admin.pojo.vo.CustPropertyVO">
        SELECT t1.*, t1.id AS customerId
        FROM customer_property t1
                 LEFT JOIN customer t2 ON t1.customerId = t2.id
        WHERE t2.id = #{customerId}
    </select>

    <select id="listWxCustomerPropertyByIdNumber" resultType="com.cfpamf.ms.insur.admin.pojo.vo.CustPropertyVO">
        SELECT t1.*, t1.id AS customerId
        FROM customer_property t1
                 LEFT JOIN customer t2 ON t1.customerId = t2.id
        WHERE t2.IdNumber = #{idNumber}
    </select>

    <select id="listCustomerAdmins" resultType="com.cfpamf.ms.insur.admin.pojo.vo.CustomerAdminVO">
        SELECT *
        FROM customer_admin
        WHERE customerAdminId = #{customerAdminId}
    </select>

    <select id="listCustomerAgents" resultType="com.cfpamf.ms.insur.admin.pojo.vo.CustomerAgentVO">
        SELECT *
        FROM customer_agent
        WHERE agentId = #{agentId}
    </select>

    <insert id="insertCustomerRemd">
        INSERT INTO customer_admin(customerId, customerAdminId, customerAdminJobCode, create_time, update_time)
        VALUES (#{customerId}, #{customerAdminId}, #{customerAdminJobCode}, CURRENT_TIMESTAMP(), CURRENT_TIMESTAMP())
    </insert>

    <insert id="insertCustomerRemdLog">
        INSERT INTO customer_admin_log(customerId, customerNewAdminId, customerNewAdminJobCode, customerOldAdminId,
                                       customerOldAdminJobCode, create_by, create_time,
                                       update_time)
        VALUES (#{customerId}, #{customerNewAdminId}, #{customerNewAdminJobCode}, #{customerOldAdminId},
                #{customerOldAdminJobCode}, #{create_by}, CURRENT_TIMESTAMP(),
                CURRENT_TIMESTAMP())
    </insert>

    <insert id="insertCustomerRemdLogs">
        INSERT INTO customer_admin_log(customerId, customerNewAdminId,customerNewAdminJobCode,
        customerOldAdminId,customerOldAdminJobCode, create_by, create_time,
        update_time)
        VALUES
        <foreach collection="dtos" item="item" index="index" separator=",">
            (#{item.customerId}, #{item.customerNewAdminId},#{item.customerNewAdminJobCode},
            #{item.customerOldAdminId},#{item.customerOldAdminJobCode}, #{item.createBy},
            CURRENT_TIMESTAMP(), CURRENT_TIMESTAMP())
        </foreach>
    </insert>

    <insert id="insertCustomerRemdLogsV2">
        INSERT INTO customer_admin_log(customerId, customerNewAdminId,customerNewAdminJobCode,
        customerOldAdminId, create_by, create_time,
        update_time)
        VALUES
        <foreach collection="dtos" item="item" index="index" separator=",">
            (#{item.customerId}, #{item.customerNewAdminId},#{item.customerNewAdminJobCode},
            #{item.customerOldAdminId}, #{item.createBy},
            CURRENT_TIMESTAMP(), CURRENT_TIMESTAMP())
        </foreach>
    </insert>

    <insert id="insertCustomerAgent">
        INSERT INTO customer_agent(customerId, agentId)
        VALUES (#{customerId}, #{agentId})
    </insert>
    <insert id="insertOrUpdateCustomerEducation">
        insert into customer_education(customerId, studentType, schoolType, schoolNature, schoolName, schoolClass,
                                       studentId)
        values (#{customerId}, #{studentType}, #{schoolType}, #{schoolNature}, #{schoolName}, #{schoolClass},
                #{studentId}) on duplicate
        update studentType = #{studentType}, schoolType = #{schoolType}, schoolNature = #{schoolNature},
            schoolName = #{schoolName},
            schoolClass = #{schoolClass}, studentId = #{studentId}
    </insert>
    <insert id="insertCustomerByOld2New">
        insert into customer_admin_log(customerId, customerNewAdminId, customerOldAdminId, create_by, create_time,
                                       update_by,
                                       update_time, enabled_flag, customerNewAdminJobCode, customerOldAdminJobCode)

        SELECT customerId
             , #{newAdminId}
             , customerAdminId
             , 'system'
             , now()
             , 'system'
             , now()
             , 0
             , #{newAdminJobCode}
             , customerAdminJobCode
        from customer_admin
        where customerAdminId = #{oldAdminId}
    </insert>
    <insert id="insertCustomerRemdLogsV1">
        insert into customer_admin_log(customerId, customerNewAdminId, customerOldAdminId, create_by, create_time,
                                       update_by,
                                       update_time, enabled_flag, customerNewAdminJobCode, customerOldAdminJobCode)

        SELECT customerId
             , #{newAdminId}
             , customerAdminId
             , #{createBy}
             , now()
             , #{createBy}
             , now()
             , 0
             , #{newAdminJobCode}
             , customerAdminJobCode
        from customer_admin
        where customerAdminId = #{oldAdminId}
    </insert>

    <update id="updateCustomerAdmin">
        UPDATE customer_admin SET
        lastCustomerAdmin=customerAdminId,
        customerAdminId=#{newAdminId},customerAdminJobCode = #{newAdminJobCode}
        WHERE customerAdminId = #{oldAdminId} and customerAdminJobCode=#{oldAdminJobCode}
        <if test="customerIds !=null and customerIds.size() > 0">
            AND customerId IN
            <foreach collection="customerIds" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </update>

    <update id="updateCustomerPolicyInfo">
        UPDATE customer
        SET apPolicyQty=#{apQty},
            apPolicyAmount=#{apAmount},
            isPolicyQty=#{isQty},
            isPolicyAmount=#{isAmount}
        WHERE idNumber = #{idNumber}
    </update>
    <update id="updateCustomerAdminV1">
        UPDATE customer_admin SET
        lastCustomerAdmin=customerAdminId,
        customerAdminId=#{newAdminId},customerAdminJobCode = #{newAdminJobCode}
        WHERE customerAdminId = #{oldAdminId}
        <if test="customerIds !=null and customerIds.size() > 0">
            AND customerId IN
            <foreach collection="customerIds" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </update>

    <select id="listCustomerIdsByRecommendInfo" resultType="java.lang.Integer">
        SELECT
        t1.customerId
        FROM customer_admin t1
        LEFT JOIN auth_user t2 ON t1.customerAdminId = t2.userId AND t2.enabled_flag = 0
        WHERE 1 = 1
        <if test='userId != null'>
            AND t1.customerAdminId = #{userId}
        </if>
        <if test='customerAdmin != null'>
            AND ((t2.userId LIKE CONCAT('%',#{customerAdmin},'%')) OR (t2.userName LIKE
            CONCAT('%',#{customerAdmin},'%')) OR (t2.userMobile LIKE CONCAT('%',#{customerAdmin},'%')))
        </if>
        <if test='regionName != null'>
            AND t2.regionName=#{regionName}
        </if>
        <if test='organizationName != null'>
            AND t2.organizationFullName=#{organizationName}
        </if>

        <if test='regionCode != null'>
            AND t2.regionCode=#{regionCode}
        </if>
        <if test='orgCode != null'>
            AND t2.orgCode=#{orgCode}
        </if>


    </select>

    <select id="listCustomerIdsByAgentInfo" resultType="java.lang.Integer">
        SELECT
        t1.customerId
        FROM customer_agent t1
        WHERE t1.agentId = #{agentId}
        <if test='customerIds != null'>
            AND t1.customerId IN
            <foreach item="item" index="index" open="(" separator="," close=")" collection="customerIds">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="listCustomerAdminInfo" resultType="com.cfpamf.ms.insur.admin.pojo.vo.CustomerListVO">
        SELECT
        t1.customerId,
        GROUP_CONCAT(DISTINCT t2.userId) AS customerAdminId,
        GROUP_CONCAT(DISTINCT t2.userName) AS customerAdminName,
        GROUP_CONCAT(DISTINCT t2.regionName) AS customerAdminRegionName,
        GROUP_CONCAT(DISTINCT t2.organizationFullName) AS customerAdminOrgName,
        GROUP_CONCAT(DISTINCT t2.status) AS customerAdminStatus
        FROM customer_admin t1
        LEFT JOIN auth_user t2 ON t1.customerAdminId = t2.userId AND t2.enabled_flag = 0
        WHERE
        1=1
        <if test='customerIds != null'>
            AND t1.customerId IN
            <foreach item="item" index="index" open="(" separator="," close=")" collection="customerIds">
                #{item}
            </foreach>
        </if>
        GROUP BY t1.customerId
    </select>
    <select id="listCustomerAgentInfo" resultType="com.cfpamf.ms.insur.admin.pojo.vo.CustomerListVO">
        SELECT
        t1.customerId,
        GROUP_CONCAT(DISTINCT t2.agentName) AS customerAgentName,
        GROUP_CONCAT(DISTINCT t2.agentMobile) AS customerAgentMobile
        FROM customer_agent t1
        LEFT JOIN sm_agent t2 ON t1.agentId = t2.agentId
        WHERE
        1=1
        <if test='customerIds != null'>
            AND t1.customerId IN
            <foreach item="item" index="index" open="(" separator="," close=")" collection="customerIds">
                #{item}
            </foreach>
        </if>
        GROUP BY t1.customerId
    </select>

    <select id="listCustomerByCustomerAdmin" resultType="com.cfpamf.ms.insur.admin.pojo.vo.CustomerListVO">
        SELECT t1.id                                                             AS customerId,
               t1.create_time                                                    AS createTime,
               t1.customerType,
               t1.customerName,
               t1.idNumber,
               t1.cellPhone,
               t1.email,
               t1.fromXinDai,
               (CASE WHEN apPolicyQty IS NULL THEN 0 ELSE apPolicyQty END)       AS apptPolicyQty,
               (CASE WHEN apPolicyAmount IS NULL THEN 0 ELSE apPolicyAmount END) AS apptPolicyAmount,
               (CASE WHEN isPolicyQty IS NULL THEN 0 ELSE isPolicyQty END)       AS insuredPolicyQty,
               (CASE WHEN isPolicyAmount IS NULL THEN 0 ELSE isPolicyAmount END) AS insuredPolicyAmount
        FROM customer t1
        WHERE t1.id IN (SELECT customerId FROM customer_admin where customerAdminId = #{customerAdmin})
    </select>

    <select id="listCustomerByIdList" resultType="com.cfpamf.ms.insur.admin.pojo.vo.CustomerListVO">
        SELECT t1.id AS customerId,
        t1.create_time AS createTime,
        t1.customerType, t1.customerName, t1.idNumber, t1.cellPhone, t1.email,
        t1.fromXinDai,
        (CASE WHEN apPolicyQty IS NULL THEN 0 ELSE apPolicyQty END) AS apptPolicyQty,
        (CASE WHEN apPolicyAmount IS NULL THEN 0 ELSE apPolicyAmount END) AS apptPolicyAmount,
        (CASE WHEN isPolicyQty IS NULL THEN 0 ELSE isPolicyQty END) AS insuredPolicyQty,
        (CASE WHEN isPolicyAmount IS NULL THEN 0 ELSE isPolicyAmount END) AS insuredPolicyAmount
        FROM customer t1
        WHERE
        t1.id IN
        <foreach item="item" index="index" open="(" separator="," close=")" collection="idList">
            #{item}
        </foreach>
    </select>
    <select id="listCustomer" resultType="com.cfpamf.ms.insur.admin.pojo.vo.CustomerListVO">
        SELECT t1.id AS customerId,
        t1.create_time AS createTime,
        t1.customerType, t1.customerName, t1.idNumber, t1.cellPhone, t1.email,
        t1.fromXinDai,
        (CASE WHEN apPolicyQty IS NULL THEN 0 ELSE apPolicyQty END) AS apptPolicyQty,
        (CASE WHEN apPolicyAmount IS NULL THEN 0 ELSE apPolicyAmount END) AS apptPolicyAmount,
        (CASE WHEN isPolicyQty IS NULL THEN 0 ELSE isPolicyQty END) AS insuredPolicyQty,
        (CASE WHEN isPolicyAmount IS NULL THEN 0 ELSE isPolicyAmount END) AS insuredPolicyAmount
        FROM customer t1
        WHERE
        1 = 1
        <if test='customerName != null'>
            AND ((t1.customerName LIKE CONCAT(#{customerName},'%')) OR (t1.cellPhone LIKE
            CONCAT(#{customerName},'%')) OR (t1.idNumber LIKE CONCAT(#{customerName},'%')))
        </if>
        <if test='customerType != null'>
            AND t1.customerType=#{customerType}
        </if>
        <if test='customerAdmin == "blank"'>
            AND t1.id NOT IN (SELECT customerId FROM customer_admin)
        </if>
        <if test='customerIds != null'>
            AND t1.id IN
            <foreach item="item" index="index" open="(" separator="," close=")" collection="customerIds">
                #{item}
            </foreach>
        </if>
        <if test='policyQtyFrom != null and qtyType != null and qtyType==0 '>
            <![CDATA[  AND t1.apPolicyQty>=#{policyQtyFrom}  ]]>
        </if>
        <if test='policyQtyTo != null and qtyType != null and qtyType==0'>
            <![CDATA[  AND t1.apPolicyQty < #{policyQtyTo}  ]]>
        </if>
        <if test='policyQtyFrom != null and qtyType != null and qtyType==1 '>
            <![CDATA[  AND t1.isPolicyQty>=#{policyQtyFrom}  ]]>
        </if>
        <if test='policyQtyTo != null and qtyType != null and qtyType==1'>
            <![CDATA[  AND t1.isPolicyQty < #{policyQtyTo}  ]]>
        </if>
        <if test='policyAmountFrom != null and amountType != null and qtyType==0'>
            <![CDATA[  AND t1.apPolicyAmount >= #{policyAmountFrom}  ]]>
        </if>
        <if test='policyAmountTo != null and amountType != null and qtyType==0'>
            <![CDATA[   AND t1.apPolicyAmount <= #{policyAmountTo}  ]]>
        </if>
        <if test='policyAmountFrom != null and amountType != null and qtyType==1'>
            <![CDATA[  AND t1.isPolicyAmount >= #{policyAmountFrom}  ]]>
        </if>
        <if test='policyAmountTo != null and amountType != null and qtyType==1'>
            <![CDATA[   AND t1.isPolicyAmount <= #{policyAmountTo}  ]]>
        </if>
        <if test='createDateStart != null'>
            <![CDATA[ AND t1.create_time>=#{createDateStart} ]]>
        </if>
        <if test='createDateEnd != null'>
            <![CDATA[ AND t1.create_time<=#{createDateEnd} ]]>
        </if>
        ORDER BY t1.create_time DESC
        <if test='startRow != null'>
            LIMIT #{startRow}, #{size}
        </if>
    </select>

    <select id="listCustomerSelection" resultType="com.cfpamf.ms.insur.admin.pojo.vo.CustomerListVO">
        SELECT t1.id AS customerId,
            t1.create_time AS createTime,
            max(t2.create_time) admin_time,
            t1.customerType, t1.customerName, t1.idNumber, t1.cellPhone, t1.email,
            t1.fromXinDai,
            t2.customerAdminId as currentCustomerAdminId,
            t2.lastCustomerAdmin as lastCustomerAdminId
        FROM customer t1
        left join customer_admin t2 on `customerId` =  t1.id
        WHERE
        1 = 1
        <if test='customerName != null'>
            AND ((t1.customerName LIKE CONCAT(#{customerName},'%')) OR (t1.cellPhone LIKE
            CONCAT(#{customerName},'%')) OR (t1.idNumber LIKE CONCAT(#{customerName},'%')))
        </if>
        <if test='lastCustomerAdmin != null and lastCustomerAdmin != ""'>
            AND t2.lastCustomerAdmin = #{lastCustomerAdmin}
        </if>
        <if test='customerType != null'>
            AND t1.customerType=#{customerType}
        </if>
        <if test='customerAdmin == "blank"'>
            AND t2.id is null
        </if>
        <if test='customerAdmin!="blank" and customerAdmin != null'>
            AND t2.customerAdminId = #{customerAdmin}
        </if>
        <if test='customerIds != null'>
            AND t1.id IN
            <foreach item="item" index="index" open="(" separator="," close=")" collection="customerIds">
                #{item}
            </foreach>
        </if>
        group by t1.id
        <if test='customerAdmin == "blank"'>
            ORDER BY t1.create_time DESC,t1.id desc
        </if>
        <if test='customerAdmin!="blank" and customerAdmin != null'>
            ORDER BY t2.create_time DESC,t1.id desc
        </if>
        <if test='startRow != null'>
            LIMIT #{startRow}, #{size}
        </if>
    </select>

    <select id="countCustomerSelection" resultType="java.lang.Long">
        SELECT  count(DISTINCT(t1.id))
        FROM customer t1
        left join customer_admin t2 on `customerId` =  t1.id
        WHERE 1 = 1
        <if test='customerName != null'>
            AND ((t1.customerName LIKE CONCAT(#{customerName},'%')) OR (t1.cellPhone LIKE
            CONCAT(#{customerName},'%')) OR (t1.idNumber LIKE CONCAT(#{customerName},'%')))
        </if>
        <if test='lastCustomerAdmin != null and lastCustomerAdmin != ""'>
            AND t2.lastCustomerAdmin = #{lastCustomerAdmin}
        </if>
        <if test='customerType != null'>
            AND t1.customerType=#{customerType}
        </if>
        <if test='customerAdmin == "blank"'>
            AND t2.id is null
        </if>
        <if test='customerAdmin!="blank" and customerAdmin != null'>
            AND t2.customerAdminId = #{customerAdmin}
        </if>
        <if test='customerIds != null'>
            AND t1.id IN
            <foreach item="item" index="index" open="(" separator="," close=")" collection="customerIds">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="countCustomers" resultType="java.lang.Long">
        SELECT COUNT(1)
        FROM customer t1
        WHERE
        1 = 1
        <if test='customerName != null'>
            AND ((t1.customerName LIKE CONCAT(#{customerName},'%')) OR (t1.cellPhone LIKE
            CONCAT(#{customerName},'%')) OR (t1.idNumber LIKE CONCAT(#{customerName},'%')))
        </if>
        <if test='customerType != null'>
            AND t1.customerType=#{customerType}
        </if>
        <if test='customerAdmin == "blank"'>
            AND t1.id NOT IN (SELECT customerId FROM customer_admin)
        </if>
        <if test='customerIds != null'>
            AND t1.id IN
            <foreach item="item" index="index" open="(" separator="," close=")" collection="customerIds">
                #{item}
            </foreach>
        </if>
        <if test='policyQtyFrom != null and qtyType != null and qtyType==0 '>
            <![CDATA[  AND t1.apPolicyQty>=#{policyQtyFrom}  ]]>
        </if>
        <if test='policyQtyTo != null and qtyType != null and qtyType==0'>
            <![CDATA[  AND t1.apPolicyQty < #{policyQtyTo}  ]]>
        </if>
        <if test='policyQtyFrom != null and qtyType != null and qtyType==1 '>
            <![CDATA[  AND t1.isPolicyQty>=#{policyQtyFrom}  ]]>
        </if>
        <if test='policyQtyTo != null and qtyType != null and qtyType==1'>
            <![CDATA[  AND t1.isPolicyQty < #{policyQtyTo}  ]]>
        </if>
        <if test='policyAmountFrom != null and amountType != null and qtyType==0'>
            <![CDATA[  AND t1.apPolicyAmount >= #{policyAmountFrom}  ]]>
        </if>
        <if test='policyAmountTo != null and amountType != null and qtyType==0'>
            <![CDATA[   AND t1.apPolicyAmount <= #{policyAmountTo}  ]]>
        </if>
        <if test='policyAmountFrom != null and amountType != null and qtyType==1'>
            <![CDATA[  AND t1.isPolicyAmount >= #{policyAmountFrom}  ]]>
        </if>
        <if test='policyAmountTo != null and amountType != null and qtyType==1'>
            <![CDATA[   AND t1.isPolicyAmount <= #{policyAmountTo}  ]]>
        </if>
        <if test='createDateStart != null'>
            <![CDATA[ AND t1.create_time>=#{createDateStart} ]]>
        </if>
        <if test='createDateEnd != null'>
            <![CDATA[ AND t1.create_time<=#{createDateEnd} ]]>
        </if>
    </select>

    <select id="listCustomerAdminLog" resultType="com.cfpamf.ms.insur.admin.pojo.vo.CustomerAdminLogVO">
        SELECT DISTINCT t1.id,t1.customerId,t1.customerNewAdminId,t1.customerOldAdminId,t1.create_by AS
        createBy,t1.create_time AS createTime,
        t2.customerName AS customerName,
        t2.idNumber AS customerIdCard,
        t3.userName AS customerOldAdminIdName,
        t4.userName AS customerNewAdminName,
        t5.userName AS createByName
        FROM customer_admin_log t1
        left JOIN customer t2 on t1.customerId = t2.id
        LEFT JOIN auth_user t3 ON t1.customerOldAdminId = t3.userId
        LEFT JOIN auth_user t4 ON t1.customerNewAdminId = t4.userId
        LEFT JOIN auth_user t5 ON t1.create_by = t5.userId
        WHERE
        1 = 1
        <if test='customerName != null'>
            AND ((t2.customerName LIKE CONCAT(#{customerName},'%')) OR (t2.cellPhone LIKE
            CONCAT(#{customerName},'%')) OR (t2.idNumber LIKE CONCAT(#{customerName},'%')))
        </if>
        <if test='customerOldAdminName != null'>
            AND ((t3.userId LIKE CONCAT(#{customerOldAdminName},'%')) OR (t3.userName LIKE
            CONCAT(#{customerOldAdminName},'%')) OR (t3.userMobile LIKE CONCAT(#{customerOldAdminName},'%')))
        </if>
        <if test='customerNewAdminName != null'>
            AND ((t4.userId LIKE CONCAT(#{customerNewAdminName},'%')) OR (t4.userName LIKE
            CONCAT(#{customerNewAdminName},'%')) OR (t4.userMobile LIKE CONCAT(#{customerNewAdminName},'%')))
        </if>
        <if test='regionName != null'>
            AND (t3.regionName=#{regionName} or t4.regionName=#{regionName})
        </if>
        <if test='organizationName != null'>
            AND ( t3.organizationFullName=#{organizationName} or t4.organizationFullName=#{organizationName})
        </if>

        <if test='regionCode != null'>
            AND (t3.regionCode=#{regionCode} or t4.regionCode=#{regionCode})
        </if>
        <if test='orgCode != null'>
            AND ( t3.orgCode=#{orgCode} or t4.orgCode=#{orgCode})
        </if>
        <if test='createDateStart != null'>
            <![CDATA[ AND t1.create_time>=#{createDateStart} ]]>
        </if>
        <if test='createDateEnd != null'>
            <![CDATA[ AND t1.create_time<=#{createDateEnd} ]]>
        </if>
        ORDER BY t1.create_time DESC
        <if test='startRow != null'>
            LIMIT #{startRow}, #{size}
        </if>
    </select>

    <select id="listCustomerAdminLogCount" resultType="java.lang.Long">
        SELECT COUNT(DISTINCT t1.id)
        FROM customer_admin_log t1
        left JOIN customer t2 on t1.customerId = t2.id
        LEFT JOIN auth_user t3 ON t1.customerOldAdminId = t3.userId
        LEFT JOIN auth_user t4 ON t1.customerNewAdminId = t4.userId
        LEFT JOIN auth_user t5 ON t1.create_by = t5.userId
        WHERE
        1 = 1
        <if test='customerName != null'>
            AND ((t2.customerName LIKE CONCAT(#{customerName},'%')) OR (t2.cellPhone LIKE
            CONCAT(#{customerName},'%')) OR (t2.idNumber LIKE CONCAT(#{customerName},'%')))
        </if>
        <if test='customerOldAdminName != null'>
            AND ((t3.userId LIKE CONCAT(#{customerOldAdminName},'%')) OR (t3.userName LIKE
            CONCAT(#{customerOldAdminName},'%')) OR (t3.userMobile LIKE CONCAT(#{customerOldAdminName},'%')))
        </if>
        <if test='customerNewAdminName != null'>
            AND ((t4.userId LIKE CONCAT(#{customerNewAdminName},'%')) OR (t4.userName LIKE
            CONCAT(#{customerNewAdminName},'%')) OR (t4.userMobile LIKE CONCAT(#{customerNewAdminName},'%')))
        </if>
        <if test='regionName != null'>
            AND (t3.regionName=#{regionName} or t4.regionName=#{regionName})
        </if>
        <if test='organizationName != null'>
            AND ( t3.organizationFullName=#{organizationName} or t4.organizationFullName=#{organizationName})
        </if>

        <if test='regionCode != null'>
            AND (t3.regionCode=#{regionCode} or t4.regionCode=#{regionCode})
        </if>
        <if test='orgCode != null'>
            AND ( t3.orgCode=#{orgCode} or t4.orgCode=#{orgCode})
        </if>
        <if test='createDateStart != null'>
            <![CDATA[ AND t1.create_time>=#{createDateStart} ]]>
        </if>
        <if test='createDateEnd != null'>
            <![CDATA[ AND t1.create_time<=#{createDateEnd} ]]>
        </if>
    </select>

    <select id="getUnRegisterCustomer" resultType="com.cfpamf.ms.insur.admin.pojo.vo.CustomerRegisterVO">
        SELECT id,
               customerType,
               customerName,
               gender,
               idType,
               IdNumber,
               birthday,
               cellPhone,
               email,
               area,
               custNo,
               address,
               annualIncome,
               occupation,
               addressProvider,
               orgCode,
               newestAdmin
        FROM customer
        WHERE id = #{customerId}
    </select>

    <select id="listCustomerPolicys" resultType="com.cfpamf.ms.insur.facade.vo.CustomerPolicyVO$PolicyInfo">
        SELECT
        t3.id AS policyId,
        t4.productName,
        t1.qty * t1.unitPrice AS policyAmount,
        t2.personName AS insName,
        t2.idNumber AS insIdNumber ,
        t2.cellPhone AS insMobile,
        t3.personName AS appName,
        t3.idNumber AS appIdNumber,
        t3.cellPhone AS appMobile,
        IFNULL(t3.insured_time,t1.paymentTime) as insuredTime,
        t1.startTime,
        t1.endTime,
        t3.surrender_time as surrenderTime,
        t3.appStatus
        FROM
        sm_order t1
        LEFT JOIN sm_order_applicant t2
        ON t2.`fhOrderId` = t1.`fhOrderId`
        LEFT JOIN sm_order_insured t3
        ON t3.`fhOrderId` = t1.`fhOrderId`
        LEFT JOIN sm_product t4
        ON t4.id = t1.productId
        WHERE t1.payStatus = 2

        AND t2.idNumber IN
        <foreach item="item" index="index" open="(" separator="," close=")" collection="idNumbers">
            #{item}
        </foreach>
        UNION
        SELECT
        t3.id AS policyId,
        t4.productName,
        t1.qty * t1.unitPrice AS policyAmount,
        t2.personName AS insName,
        t2.idNumber AS insIdNumber ,
        t2.cellPhone AS insMobile,
        t3.personName AS appName,
        t3.idNumber AS appIdNumber,
        t3.cellPhone AS appMobile,
        IFNULL(t3.insured_time,t1.paymentTime) as insuredTime,
        t1.startTime,
        t1.endTime,
        t3.surrender_time as surrenderTime,
        t3.appStatus
        FROM
        sm_order t1
        LEFT JOIN sm_order_applicant t2
        ON t2.`fhOrderId` = t1.`fhOrderId`
        LEFT JOIN sm_order_insured t3
        ON t3.`fhOrderId` = t1.`fhOrderId`
        LEFT JOIN sm_product t4
        ON t4.id = t1.productId
        WHERE t1.payStatus = 2

        AND t3.idNumber IN
        <foreach item="item" index="index" open="(" separator="," close=")" collection="idNumbers">
            #{item}
        </foreach>
    </select>

    <select id="updateCustomerRegisterInfo">
        UPDATE customer
        SET custNo = #{custNo}
        WHERE id = #{id}
    </select>

    <select id="listPolicy4OMS" resultType="com.cfpamf.ms.insur.facade.vo.CustomerPolicyOMSVO">
        SELECT t1.qty * t1.unitPrice AS amount,
        t3.appStatus,
        t2.personName AS applicantPersonName,
        t2.idNumber AS applicantIdNumber,
        t3.personName AS insuredPersonName,
        t3.idNumber AS insuredIdNumber,
        t1.submitTime,
        sc.end_time
        FROM sm_order t1
        LEFT JOIN sm_order_applicant t2 ON t2.`fhOrderId` = t1.`fhOrderId`
        LEFT JOIN sm_order_insured t3 ON t3.`fhOrderId` = t1.`fhOrderId`
        left join auth_user au on au.userId = t1.recommendId and au.enabled_flag = 0
        left join sm_cancel sc on sc.insured_id = t3.id and sc.state = 40
        WHERE
        <if test="!insured">
            t2.idNumber in
            <foreach item="item" index="index" open="(" separator="," close=")" collection="idNumbers">
                #{item}
            </foreach>
        </if>
        <if test="insured">
            t3.idNumber in
            <foreach item="item" index="index" open="(" separator="," close=")" collection="idNumbers">
                #{item}
            </foreach>
        </if>
    </select>
    <select id="listCustomerIdsLackAdmin" resultType="java.lang.Integer">
        SELECT t1.id
        FROM customer t1 LEFT JOIN `customer_admin` t2 ON t1.id= t2.`customerId`
        WHERE t2.id IS NULL
    </select>
    <select id="listCustomersLackAdmin" resultType="com.cfpamf.ms.insur.admin.pojo.vo.CustomerListVO">
        SELECT t1.id                                                             AS customerId,
               t1.create_time                                                    AS createTime,
               t1.customerType,
               t1.customerName,
               t1.idNumber,
               t1.cellPhone,
               t1.email,
               t1.fromXinDai,
               (CASE WHEN apPolicyQty IS NULL THEN 0 ELSE apPolicyQty END)       AS apptPolicyQty,
               (CASE WHEN apPolicyAmount IS NULL THEN 0 ELSE apPolicyAmount END) AS apptPolicyAmount,
               (CASE WHEN isPolicyQty IS NULL THEN 0 ELSE isPolicyQty END)       AS insuredPolicyQty,
               (CASE WHEN isPolicyAmount IS NULL THEN 0 ELSE isPolicyAmount END) AS insuredPolicyAmount
        FROM customer t1 LEFT JOIN `customer_admin` t2 ON t1.id= t2.`customerId`
        WHERE t2.id IS NULL
    </select>
    <select id="selectCustomerList" resultType="com.cfpamf.ms.insur.admin.external.whale.model.CustomerInfoVO">
        select idNumber as idNo,customerName as name
        from customer
        where id in
        <foreach item="item" index="index" open="(" separator="," close=")" collection="customerIds">
            #{item}
        </foreach>
    </select>

</mapper>
