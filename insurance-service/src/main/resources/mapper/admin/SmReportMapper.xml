<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.SmReportMapper">

    <select id="getSmSmSmy" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmSmyVO">
        SELECT CASE WHEN t1.todayAmount IS NULL THEN 0 ELSE t1.todayAmount END         AS todayAmount,
               CASE WHEN t1.todayQty IS NULL THEN 0 ELSE t1.todayQty END               AS todayQty,
               CASE WHEN t2.ystdayAmount IS NULL THEN 0 ELSE t2.ystdayAmount END       AS ystdayAmount,
               CASE WHEN t2.ystdayQty IS NULL THEN 0 ELSE t2.ystdayQty END             AS ystdayQty,
               CASE WHEN t3.thisMonthAmount IS NULL THEN 0 ELSE t3.thisMonthAmount END AS thisMonthAmount,
               CASE WHEN t3.thisMonthQty IS NULL THEN 0 ELSE t3.thisMonthQty END       AS thisMonthQty,
               CASE WHEN t4.thisYearQty IS NULL THEN 0 ELSE t4.thisYearQty END         AS thisYearQty,
               CASE WHEN t4.thisYearAmount IS NULL THEN 0 ELSE t4.thisYearAmount END   AS thisYearAmount,
               CASE WHEN t5.pastYearQty IS NULL THEN 0 ELSE t5.pastYearQty END         AS pastYearQty,
               CASE WHEN t5.pastYearAmount IS NULL THEN 0 ELSE t5.pastYearAmount END   AS pastYearAmount,
               CASE WHEN t6.addUpAmount IS NULL THEN 0 ELSE t6.addUpAmount END         AS addUpAmount,
               CASE WHEN t6.addUpQty IS NULL THEN 0 ELSE t6.addUpQty END               AS addUpQty
        FROM (
                 SELECT SUM(totalAmount) AS todayAmount, SUM(qty) AS todayQty
                 FROM sm_order_commission
                 WHERE
        <![CDATA[ accountTime >= #{todayFromTime}
                   AND accountTime <= #{todayEndTime} ]]> AND appStatus IN (1, 4)
             ) t1
                 LEFT JOIN (
            SELECT SUM(totalAmount) AS ystdayAmount, SUM(qty) AS ystdayQty
            FROM sm_report_order
            WHERE
        <![CDATA[ rptDate >= #{yestFromTime}
              AND rptDate <= #{yestEndTime} ]]>
        ) t2 ON 1 = 1
                 LEFT JOIN (
            SELECT SUM(totalAmount) AS thisMonthAmount, SUM(qty) AS thisMonthQty
            FROM sm_report_order
            WHERE
        <![CDATA[ rptDate >= #{thisMonthFromTime} ]]>
        ) t3 ON 1 = 1
                 LEFT JOIN (
            SELECT SUM(totalAmount) AS thisYearAmount, SUM(qty) AS thisYearQty
            FROM sm_report_order
            WHERE
        <![CDATA[ rptDate >= #{thisYearFromTime} ]]>
        ) t4 ON 1 = 1
                 LEFT JOIN (
            SELECT SUM(totalAmount) AS pastYearAmount, SUM(qty) AS pastYearQty
            FROM sm_report_order
            WHERE
        <![CDATA[ rptDate < #{thisYearFromTime} ]]>
        ) t5 ON 1 = 1
                 LEFT JOIN (
            SELECT SUM(totalAmount) AS addUpAmount, SUM(qty) AS addUpQty FROM sm_report_order
        ) t6 ON 1 = 1
    </select>

    <select id="getSmReportTrendGroup" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmReportGroupVO">
        SELECT
        DATE_FORMAT(t1.rptDate,#{groupName}) AS NAME,
        SUM(t1.qty) AS qty,
        SUM(t1.totalAmount) AS amount
        FROM sm_report_order t1
        WHERE 1 = 1
        <if test='startTime != null and endTime != null'>
            <![CDATA[ AND t1.rptDate>= #{startTime} AND t1.rptDate <= #{endTime} ]]>
        </if>
        <if test='regionName != null'>
            AND t1.regionName= #{regionName}
        </if>
        <if test='orgName != null'>
            AND t1.organizationName= #{orgName}
        </if>
        GROUP BY DATE_FORMAT(t1.rptDate, #{groupName})
        ORDER BY DATE_FORMAT(t1.rptDate, #{groupName}) ASC
    </select>

    <select id="getSmRegionTrendGroup" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmReportGroupVO">
        SELECT
        (CASE WHEN t0.orgName IS NULL THEN '其他' ELSE t0.orgName END) AS NAME,
        (CASE WHEN SUM(t11.qty) IS NULL THEN 0 ELSE SUM(t11.qty) END) AS qty,
        (CASE WHEN SUM(t11.amount) IS NULL THEN 0 ELSE SUM(t11.amount) END) AS amount
        FROM organization t0
        LEFT JOIN
        (
        SELECT t1.regionName,
        SUM(t1.qty) AS qty,
        SUM(t1.totalAmount) AS amount
        FROM sm_report_order t1
        WHERE 1=1
        <if test='startDate != null'>
            <![CDATA[ AND t1.rptDate >= #{startDate} ]]>
        </if>
        <if test='endDate != null'>
            <![CDATA[ AND t1.rptDate <= #{endDate} ]]>
        </if>
        GROUP BY t1.userId
        ) t11 ON t11.regionName = t0.orgName OR (t11.regionName is NULL AND t0.orgName IS NULL)
        WHERE t0.orgType=1
        GROUP BY t0.orgName
        ORDER BY amount DESC, qty DESC
    </select>

    <select id="getSmSalesTopRegion" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmSalesTopVO$TopSalesRegion">
        SELECT
        t1.organizationName AS organizationName,
        SUM(t1.totalAmount) AS totalAmount
        FROM sm_report_order t1
        WHERE t1.organizationName IS NOT NULL
        <if test='startDate != null'>
            <![CDATA[ AND t1.rptDate >= #{startDate} ]]>
        </if>
        <if test='endDate != null'>
            <![CDATA[ AND t1.rptDate <= #{endDate} ]]>
        </if>
        GROUP BY t1.organizationName
        ORDER BY totalAmount DESC
        LIMIT 10
    </select>

    <select id="getSmSalesTopPerson" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmSalesTopVO$TopSalesPerson">
        SELECT t1.userName AS salesMan,
        t1.organizationName AS organizationName,
        SUM(t1.totalAmount) AS totalAmount
        FROM sm_report_order t1
        WHERE t1.userId IS NOT NULL AND t1.organizationName IS NOT NULL
        <if test='startDate != null'>
            <![CDATA[ AND t1.rptDate >= #{startDate} ]]>
        </if>
        <if test='endDate != null'>
            <![CDATA[ AND t1.rptDate <= #{endDate} ]]>
        </if>
        GROUP BY t1.userId
        ORDER BY totalAmount DESC
        LIMIT 10
    </select>

    <select id="getSmSalesTopProduct" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmSalesTopVO$TopSalesProduct">
        SELECT t1.productName AS productName, sum(t1.qty) AS salesQty, SUM(t1.totalAmount) AS totalAmount
        FROM sm_report_order t1
        WHERE 1=1
        <if test='startDate != null'>
            <![CDATA[ AND t1.rptDate >= #{startDate} ]]>
        </if>
        <if test='endDate != null'>
            <![CDATA[ AND t1.rptDate <= #{endDate} ]]>
        </if>
        GROUP BY t1.productId
        ORDER BY salesQty DESC
        LIMIT 10
    </select>

    <select id="getSmProductSalesSmy" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmProductSalesSmyVO">
        SELECT
        t0.productName AS productName,t0.productType,
        CASE WHEN SUM(t1.qty) IS NULL THEN 0 ELSE SUM(t1.qty) END AS qty,
        CASE WHEN SUM(t1.totalAmount) IS NULL THEN 0 ELSE SUM(t1.totalAmount) END AS amount
        FROM sm_product t0
        LEFT JOIN
        (
        SELECT t1.productId,
        SUM(t1.totalAmount) AS totalAmount,
        SUM(t1.qty) AS qty
        FROM sm_report_order t1
        WHERE 1 = 1
        <if test='startDate != null'>
            <![CDATA[ AND t1.rptDate >= #{startDate} ]]>
        </if>

        <if test='endDate != null'>
            <![CDATA[ AND t1.rptDate <= #{endDate} ]]>
        </if>
        <if test='regionName != null'>
            AND t1.regionName = #{regionName}
        </if>
        <if test='orgName != null'>
            AND t1.organizationName = #{orgName}
        </if>
        GROUP BY t1.productId
        ) t1 ON t1.productId = t0.id
        WHERE t0.enabled_flag=0
        <if test='productId != null'>
            AND t0.id = #{productId}
        </if>
        <if test='productType !=null'>
            <![CDATA[ AND t0.productType  = #{productType} ]]>
        </if>
        <if test='companyId != null'>
            AND t0.companyId = #{companyId}
        </if>
        GROUP BY t0.id
        ORDER BY amount DESC, qty DESC
        <if test='startRow != null'>
            LIMIT #{startRow}, #{size}
        </if>
    </select>

    <select id="countProductSalesSmy" resultType="java.lang.Long">
        SELECT COUNT(1) FROM sm_product t0 WHERE t0.enabled_flag=0
        <if test='productId != null'>
            AND t0.id = #{productId}
        </if>
        <if test='companyId != null'>
            AND t0.companyId = #{companyId}
        </if>
        <if test='productType !=null'>
           AND t0.productType  = #{productType}
        </if>
    </select>

    <select id="sumSmProductSalesSmy" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmProductSalesSmyVO">
        SELECT
        IFNULl(SUM(IFNULL(t1.qty,0)),0) as qty,
        IFNULl(SUM(IFNULL(t1.totalAmount,0)),0) as amount
        FROM sm_product t0
        LEFT JOIN
        (
        SELECT t1.productId,
        SUM(t1.qty) AS qty,
        SUM(t1.totalAmount) AS totalAmount
        FROM sm_report_order t1
        WHERE 1= 1
        <if test='startDate != null'>
            <![CDATA[ AND t1.rptDate >= #{startDate} ]]>
        </if>
        <if test='endDate != null'>
            <![CDATA[ AND t1.rptDate <= #{endDate} ]]>
        </if>
        <if test='regionName != null'>
            AND t1.regionName = #{regionName}
        </if>
        <if test='orgName != null'>
            AND t1.organizationName = #{orgName}
        </if>
        GROUP BY t1.productId
        ) t1 ON t1.productId = t0.id
        WHERE t0.enabled_flag=0
        <if test='productId != null'>
            AND t0.id = #{productId}
        </if>
        <if test='productType !=null'>
            <![CDATA[ AND t0.productType  = #{productType} ]]>
        </if>
        <if test='companyId != null'>
            AND t0.companyId = #{companyId}
        </if>
    </select>


    <select id="getSmStaffSalesSmy" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmStaffSalesSmyVO">
        SELECT
        <if test='startDate == null'>
            t11.rptDate AS startDate,
        </if>
        <if test='startDate != null'>
            #{startDate} AS startDate,
        </if>
        <if test='endDate == null'>
            NOW() AS endDate,
        </if>
        <if test='endDate != null'>
            #{endDate} AS endDate,
        </if>
        (CASE WHEN t00.regionName IS NULL THEN '其他' ELSE t00.regionName END) AS regionName,
        (CASE WHEN t00.organizationFullName IS NULL THEN '其他' ELSE t00.organizationFullName END) AS organizationName,
        t00.userId AS userId,
        t00.userName AS userName,
        CASE WHEN t11.amount IS NULL THEN 0 ELSE t11.amount END AS amount,
        CASE WHEN t11.orderQty IS NULL THEN 0 ELSE t11.orderQty END AS orderQty,
        CASE WHEN t11.orderAmountAvg IS NULL THEN 0 ELSE t11.orderAmountAvg END AS orderAmountAvg
        FROM auth_user t00
        LEFT JOIN organization t01
        ON t00.regionName = t01.orgName AND t01.orgType = 1
        LEFT JOIN organization t02
        ON t00.organizationFullName = t02.orgName AND t01.orgType = 2
        LEFT JOIN
        (SELECT
        MIN(t1.rptDate) AS rptDate,
        t1.userId AS userId,
        SUM(t1.qty) AS orderQty,
        SUM(t1.totalAmount) AS amount ,
        SUM(t1.totalAmount)/ SUM(t1.qty) AS orderAmountAvg
        FROM sm_report_order t1
        WHERE
        1 = 1
        <if test='startDate != null'>
            <![CDATA[ AND t1.rptDate >= #{startDate} ]]>
        </if>
        <if test='endDate != null'>
            <![CDATA[ AND t1.rptDate <= #{endDate} ]]>
        </if>
        <if test='onlyCustomerManager != null and onlyCustomerManager'>
            AND t1.userPostCode='1081'
        </if>
        GROUP BY t1.userId ) t11
        ON t11.userId = t00.userId OR (t00.userId IS NULL AND t11.userId IS NULL )
        WHERE t00.userType='employee' AND t00.enabled_flag = 0
        <if test='onlyCustomerManager != null and onlyCustomerManager'>
            AND t00.postCode='1081'
        </if>
        <if test='regionName != null'>
            AND t00.regionName=#{regionName}
        </if>
        <if test='orgName != null'>
            AND t00.organizationName=#{orgName}
        </if>
        <if test='userName != null'>
            AND t00.userName=#{userName}
        </if>
        <if test='userStatus == 8'>
            AND t00.status='8'
        </if>
        <if test='userStatus == 3'>
            AND t00.status IN ('2', '3')
        </if>
        ORDER BY t01.id ASC, t02.id ASC
        <if test='startRow != null'>
            LIMIT #{startRow}, #{size}
        </if>
    </select>

    <select id="countSmStaffSalesSmy" resultType="java.lang.Long">
        SELECT
        COUNT(1)
        FROM auth_user t00
        WHERE t00.userType='employee' AND t00.enabled_flag = 0
        <if test='onlyCustomerManager != null and onlyCustomerManager'>
            AND t00.postCode='1081'
        </if>
        <if test='regionName != null'>
            AND t00.regionName=#{regionName}
        </if>
        <if test='orgName != null'>
            AND t00.organizationName=#{orgName}
        </if>
        <if test='userName != null'>
            AND t00.userName=#{userName}
        </if>
        <if test='userStatus == 8'>
            AND t00.status='8'
        </if>
        <if test='userStatus == 3'>
            AND t00.status IN ('2', '3')
        </if>
    </select>

    <select id="sumSmStaffSalesSmy" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmStaffSalesSmyVO">
        SELECT
        CASE WHEN SUM(t1.totalAmount) IS NULL THEN 0 ELSE SUM(t1.totalAmount) END AS amount,
        CASE WHEN SUM(t1.qty) IS NULL THEN 0 ELSE SUM(t1.qty) END AS orderQty,
        CASE WHEN SUM(t1.qty) >0 THEN SUM(t1.totalAmount)/SUM(t1.qty) ELSE 0 END AS orderAmountAvg
        FROM auth_user t3
        LEFT JOIN sm_report_order t1
        ON t3.userId = t1.userId AND t3.enabled_flag = 0
        WHERE
        t3.userType='employee' AND t3.enabled_flag = 0
        <if test='startDate != null'>
            <![CDATA[ AND t1.rptDate >= #{startDate} ]]>
        </if>
        <if test='endDate != null'>
            <![CDATA[ AND t1.rptDate <= #{endDate} ]]>
        </if>
        <if test='onlyCustomerManager != null and onlyCustomerManager'>
            AND t3.postCode='1081'
        </if>
        <if test='userName != null'>
            AND (
            t3.userName LIKE CONCAT (#{userName},'%')
            OR t3.userId LIKE CONCAT (#{userName},'%')
            OR t3.userMobile LIKE CONCAT (#{userName},'%')
            )
        </if>
        <if test='regionName != null'>
            AND t3.regionName=#{regionName}
        </if>
        <if test='orgName != null'>
            AND t3.organizationFullName=#{orgName}
        </if>
        <if test='userStatus == 8'>
            AND t3.status='8'
        </if>
        <if test='userStatus == 3'>
            AND t3.status IN ('2', '3')
        </if>
    </select>

    <select id="getSmRegionSalesSmy" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmRegionSalesSmyVO">
        SELECT
        <if test='startDate == null'>
            t11.rptDate AS startDate,
        </if>
        <if test='startDate != null'>
            #{startDate} AS startDate,
        </if>
        <if test='endDate == null'>
            NOW() AS endDate,
        </if>
        <if test='endDate != null'>
            #{endDate} AS endDate,
        </if>
        (CASE WHEN t00.orgName IS NULL THEN '其他' ELSE t00.orgName END) AS regionName,
        (CASE WHEN t11.personQty IS NULL THEN 0 ELSE t11.personQty END) AS personQty,
        (CASE WHEN t11.amount IS NULL THEN 0 ELSE t11.amount END) AS amount,
        (CASE WHEN t11.orderQty IS NULL THEN 0 ELSE t11.orderQty END) AS orderQty,
        (CASE WHEN t11.amountAvg IS NULL THEN 0 ELSE t11.amountAvg END) AS amountAvg,
        (CASE WHEN t11.orderQtyAvg IS NULL THEN 0 ELSE t11.orderQtyAvg END) AS orderQtyAvg,
        (CASE WHEN t11.orderAmountAvg IS NULL THEN 0 ELSE t11.orderAmountAvg END) AS orderAmountAvg
        FROM organization t00
        LEFT JOIN
        (SELECT
        t1.regionName AS regionName,
        t1.userPostCode AS postCode,
        MIN(t1.rptDate) AS rptDate,
        COUNT(DISTINCT t1.userId) AS personQty,
        SUM(t1.totalAmount) AS amount,
        SUM(t1.qty) AS orderQty,
        SUM(t1.totalAmount)/COUNT(DISTINCT t1.userId) AS amountAvg,
        SUM(t1.qty)/COUNT(DISTINCT t1.userId) AS orderQtyAvg,
        SUM(t1.totalAmount)/SUM(t1.qty) AS orderAmountAvg
        FROM sm_report_order t1
        WHERE 1=1
        <if test='startDate != null'>
            <![CDATA[ AND t1.rptDate >= #{startDate} ]]>
        </if>
        <if test='endDate != null'>
            <![CDATA[ AND t1.rptDate <= #{endDate} ]]>
        </if>
        <if test='onlyCustomerManager != null and onlyCustomerManager'>
            AND t1.userPostCode='1081'
        </if>
        GROUP BY t1.regionName ) t11
        ON t11.regionName = t00.orgName OR (t11.regionName IS NULL AND t00.orgName IS NULL)
        WHERE t00.orgType=1
        <if test='regionName != null'>
            AND t00.orgName=#{regionName}
        </if>
        ORDER BY t00.id ASC
        <if test='startRow != null'>
            LIMIT #{startRow}, #{size}
        </if>
    </select>

    <select id="countSmRegionSalesSmy" resultType="java.lang.Long">
        SELECT
        COUNT(1)
        FROM organization t00
        WHERE t00.orgType=1
        <if test='regionName != null'>
            AND t00.orgName=#{regionName}
        </if>
    </select>

    <select id="sumSmRegionSalesSmy" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmRegionSalesSmyVO">
        SELECT
        COUNT(DISTINCT t1.userId) AS personQty,
        SUM(t1.totalAmount) AS amount,
        SUM(t1.qty) AS orderQty,
        SUM(t1.totalAmount)/COUNT(DISTINCT t1.userId) AS amountAvg,
        SUM(t1.qty)/COUNT(DISTINCT t1.userId) AS orderQtyAvg,
        SUM(t1.totalAmount)/SUM(t1.qty) AS orderAmountAvg
        FROM sm_report_order t1
        WHERE
        1 = 1
        <if test='startDate != null'>
            <![CDATA[ AND t1.rptDate >= #{startDate} ]]>
        </if>
        <if test='endDate != null'>
            <![CDATA[ AND t1.rptDate <= #{endDate} ]]>
        </if>
        <if test='onlyCustomerManager != null and onlyCustomerManager'>
            AND t1.userPostCode='1081'
        </if>
        <if test='regionName != null'>
            AND t1.regionName=#{regionName}
        </if>
    </select>

    <select id="getSmOrgSalesSmy" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmOrgSalesSmyVO">
        SELECT
        <if test='startDate == null'>
            t11.rptDate AS startDate,
        </if>
        <if test='startDate != null'>
            #{startDate} AS startDate,
        </if>
        <if test='endDate == null'>
            NOW() AS endDate,
        </if>
        <if test='endDate != null'>
            #{endDate} AS endDate,
        </if>
        (CASE WHEN t01.orgName IS NULL THEN '其他' ELSE t01.orgName END) AS regionName,
        (CASE WHEN t00.orgName IS NULL THEN '其他' ELSE t00.orgName END) AS organizationName,
        (CASE WHEN t11.personQty IS NULL THEN 0 ELSE t11.personQty END) AS personQty,
        (CASE WHEN t11.amount IS NULL THEN 0 ELSE t11.amount END) AS amount,
        (CASE WHEN t11.orderQty IS NULL THEN 0 ELSE t11.orderQty END) AS orderQty,
        (CASE WHEN t11.amountAvg IS NULL THEN 0 ELSE t11.amountAvg END) AS amountAvg,
        (CASE WHEN t11.orderQtyAvg IS NULL THEN 0 ELSE t11.orderQtyAvg END) AS orderQtyAvg,
        (CASE WHEN t11.orderAmountAvg IS NULL THEN 0 ELSE t11.orderAmountAvg END) AS orderAmountAvg
        FROM organization t00
        LEFT JOIN organization t01
        ON t01.hrOrgId= t00.hrParentId AND t01.orgType=1
        LEFT JOIN
        (SELECT
        t1.regionName,
        t1.organizationName,
        t1.userPostCode AS postCode,
        MIN(t1.rptDate) AS rptDate,
        COUNT(DISTINCT t1.userId) AS personQty,
        SUM(t1.totalAmount) AS amount,
        SUM(t1.qty) AS orderQty,
        SUM(t1.totalAmount)/COUNT(DISTINCT t1.userId) AS amountAvg,
        SUM(t1.qty)/COUNT(DISTINCT t1.userId) AS orderQtyAvg,
        SUM(t1.totalAmount)/SUM(t1.qty) AS orderAmountAvg
        FROM sm_report_order t1
        WHERE
        1 = 1
        <if test='startDate != null'>
            <![CDATA[ AND t1.rptDate>= #{startDate} ]]>
        </if>
        <if test='endDate != null'>
            <![CDATA[ AND t1.rptDate <=  #{endDate} ]]>
        </if>
        <if test='onlyCustomerManager != null and onlyCustomerManager'>
            AND t1.userPostCode='1081'
        </if>
        GROUP BY t1.organizationName ) t11
        ON t11.organizationName = t00.orgName OR (t11.organizationName IS NULL AND t00.orgName IS NULL)
        WHERE t00.orgType=2
        <if test='regionName != null'>
            AND t01.orgName=#{regionName}
        </if>
        <if test='orgName != null'>
            AND t00.orgName=#{orgName}
        </if>
        ORDER BY t01.id ASC, t00.id ASC
        <if test='startRow != null'>
            LIMIT #{startRow}, #{size}
        </if>
    </select>

    <select id="countSmOrgSalesSmy" resultType="java.lang.Long">
        SELECT
        COUNT(1)
        FROM organization t00
        LEFT JOIN organization t01
        ON t01.hrOrgId= t00.hrParentId AND t01.orgType=1
        WHERE t00.orgType=2
        <if test='regionName != null'>
            AND t01.orgName=#{regionName}
        </if>
        <if test='orgName != null'>
            AND t00.orgName=#{orgName}
        </if>
        ORDER BY t01.id ASC, t00.id ASC
    </select>

    <select id="sumSmOrgSalesSmy" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmOrgSalesSmyVO">
        SELECT
        COUNT(DISTINCT t1.userId) AS personQty,
        SUM(t1.totalAmount) AS amount,
        SUM(t1.qty) AS orderQty,
        SUM(t1.totalAmount)/COUNT(DISTINCT t1.userId) AS amountAvg,
        SUM(t1.qty)/COUNT(DISTINCT t1.userId) AS orderQtyAvg,
        SUM(t1.totalAmount)/SUM(t1.qty) AS orderAmountAvg
        FROM sm_report_order t1
        WHERE
        1 = 1
        <if test='startDate != null'>
            <![CDATA[ AND t1.rptDate>= #{startDate} ]]>
        </if>
        <if test='endDate != null'>
            <![CDATA[ AND t1.rptDate <= #{endDate} ]]>
        </if>
        <if test='onlyCustomerManager != null and onlyCustomerManager'>
            AND t1.userPostCode='1081'
        </if>
        <if test='regionName != null'>
            AND t1.regionName=#{regionName}
        </if>
        <if test='orgName != null'>
            AND t1.organizationName=#{orgName}
        </if>
    </select>

    <select id="getMaxOrderReportDay" resultType="java.util.Date">
        SELECT MAX(rptDate)
        FROM sm_report_order
    </select>

    <insert id="insertDayOrderReport">
        INSERT INTO sm_report_order (rptDate,
                                     regionCode,
                                     regionName,
                                     organizationCode,
                                     organizationName,
                                     userId,
                                     jobCode,
                                     userName,
                                     userPostCode,
                                     productId,
                                     productName,
                                     planId,
                                     planName,
                                     companyId,
                                     companyName,
                                     qty,
                                     totalAmount,
                                     paymentAmount,
                                     settlementAmount)
        SELECT #{startTime}             AS rptDate,
               t2.regionCode,
               t2.regionName,
               t2.orgCode,
               t2.organizationName,
               t2.userId,
               t2.jobCode,
               t2.userName,
               t2.postCode,
               t1.productId,
               t3.productName,
               t1.planId,
               t5.planName,
               t1.companyId,
               t4.companyName,
               SUM(t1.qty)              AS qty,
               SUM(t1.totalAmount)      AS totalAmount,
               SUM(t1.paymentAmount)    AS paymentAmount,
               SUM(t1.settlementAmount) AS settlementAmount
        FROM sm_order_commission t1
                 LEFT JOIN auth_user t2 ON t1.recommendId = t2.userId AND t2.enabled_flag = 0
                 LEFT JOIN sm_product t3 ON t1.productId = t3.id
                 LEFT JOIN sm_company t4 ON t3.companyId = t4.id
                 LEFT JOIN sm_plan t5 ON t1.planId = t5.id
        WHERE 1 = 1
        <![CDATA[ AND t1.accountTime >= #{startTime} ]]>
        <![CDATA[ AND t1.accountTime <= #{endTime} ]]>
        GROUP BY t2.userId, t1.productId, t1.planId
    </insert>

    <delete id="deleteDayBatchReport">
        DELETE
        FROM sm_report_order
        WHERE rptDate = #{rptDate}
    </delete>

    <select id="getMaxClaimReportDay" resultType="java.util.Date">
        SELECT MAX(claimCreateTime)
        FROM sm_report_claim
    </select>

    <insert id="insertDayClaimReport">
        INSERT INTO sm_report_claim (
        claimId,
        rptDate,
        riskType,
        productId,
        productName,
        planId,
        planName,
        companyId,
        companyName,
        regionCode,
        regionName,
        organizationCode,
        organizationName,
        customerAdminId,
        customerAdminJobCode,
        customerAdminName,
        orderStartTime,
        claimCreateTime,
        claimRiskTime,
        claimDataTime,
        claimExpressTime,
        claimFinishTime,
        claimState,
        claimResult,
        finishState,
        finishResult,
        payMoney,
        create_time,
        update_time
        )
        SELECT
        t1.id AS claimId,
        #{claimStartTime} AS rptDate,
        t1.riskType,
        t5.id AS productId,
        t5.productName,
        t6.id AS planId,
        t6.planName,
        t7.id AS companyId,
        t7.companyName,
        t4.regionCode,
        t4.regionName,
        t4.orgCode AS organizationCode,
        t4.organizationName ,
        t4.userId AS customerAdminId,
        t4.jobCode As cutomerAdminJobCode,
        t4.userName AS customerAdminName,
        t3.startTime AS orderStartTime,
        t1.create_time AS claimCreateTime,
        t1.riskTime AS claimRiskTime,
        t1.dataTime AS claimDataTime,
        t1.expressTime AS claimExpressTime,
        t1.finishTime AS claimFinishTime,
        t1.claimState,
        t1.claimResult,
        t1.finishState,
        t1.finishResult,
        t1.payMoney,
        t1.create_time,
        t1.update_time
        FROM sm_claim t1
        LEFT JOIN sm_order_insured t2 ON t1.insuredId = t2.id
        LEFT JOIN sm_order t3 ON t3.fhOrderId = t2.fhOrderId
        LEFT JOIN auth_user t4 ON t4.userId = t3.customerAdminId AND t4.enabled_flag = 0
        LEFT JOIN sm_product t5 ON t5.id = t3.productId
        LEFT JOIN sm_plan t6 ON t6.id = t3.planId
        LEFT JOIN sm_company t7 ON t7.id = t5.companyId
        WHERE 1=1
        <if test="claimStartTime != null">
            <![CDATA[ AND t1.create_time >= #{claimStartTime} ]]>
        </if>
        <if test="claimEndTime != null">
            <![CDATA[ AND t1.create_time <= #{claimEndTime} ]]>
        </if>
    </insert>

    <update id="updateClaimReport">
        UPDATE sm_report_claim t0
        LEFT JOIN sm_claim t1 ON t1.id = t0.claimId
        LEFT JOIN sm_order_insured t2 ON t1.insuredId = t2.id
        LEFT JOIN sm_order t3 ON t3.fhOrderId = t2.fhOrderId
        LEFT JOIN auth_user t4 ON t4.userId = t3.customerAdminId AND t4.enabled_flag = 0
        LEFT JOIN sm_product t5 ON t5.id = t3.productId
        LEFT JOIN sm_plan t6 ON t6.id = t3.planId
        LEFT JOIN sm_company t7 ON t7.id = t5.companyId
        SET
        t0.productId =t5.id ,
        t0.productName = t5.productName,
        t0.planId = t6.id ,
        t0.planName = t6.planName,
        t0.companyId = t7.id ,
        t0.companyName = t7.companyName,
        t0.regionCode = t4.regionCode,
        t0.regionName = t4.regionName,
        t0.organizationCode = t4.orgCode ,
        t0.organizationName = t4.organizationName ,
        t0.customerAdminId = t4.userId ,
        t0.customerAdminName = t4.userName ,
        t0.orderStartTime = t3.startTime ,
        t0.claimCreateTime = t1.create_time ,
        t0.claimRiskTime = t1.riskTime ,
        t0.claimDataTime = t1.dataTime ,
        t0.claimExpressTime = t1.expressTime ,
        t0.claimFinishTime = t1.finishTime ,
        t0.claimState = t1.claimState,
        t0.claimResult = t1.claimResult,
        t0.finishState = t1.finishState,
        t0.finishResult = t1.finishResult,
        t0.riskType = t1.riskType,
        t0.payMoney = t1.payMoney,
        t0.create_time = t1.create_time,
        t0.update_time = t1.update_time
        WHERE 1=1
        <if test="claimIds != null">
            t1.id IN
            <foreach collection="claimIds" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="updateStartTime != null">
            <![CDATA[ AND t1.update_time >= #{updateStartTime} ]]>
        </if>
        <if test="updateEndTime != null">
            <![CDATA[ AND t1.update_time <= #{updateEndTime} ]]>
        </if>
    </update>

    <delete id="deleteClaimReport">
        DELETE FROM sm_report_claim
        WHERE 1=1
        <if test="claimStartTime != null">
            <![CDATA[ AND claimCreateTime >= #{claimStartTime} ]]>
        </if>
        <if test="claimEndTime != null">
            <![CDATA[ AND claimCreateTime <= #{claimEndTime} ]]>
        </if>
    </delete>

    <insert id="insertDayPolicyReport">
        INSERT INTO sm_report_policy (rptDate,
                                      regionCode,
                                      regionName,
                                      organizationCode,
                                      organizationName,
                                      companyId,
                                      companyName,
                                      productId,
                                      productName,
                                      planId,
                                      planName,
                                      startTime,
                                      qty,
                                      totalAmount)
        SELECT #{startTime}     AS rptDate,
               t4.regionCode,
               t4.regionName,
               t4.orgCode       AS organizationCode,
               t4.organizationName,
               t2.companyId,
               t5.companyName,
               t1.productId,
               t2.productName,
               t1.planId,
               t3.planName,
               t1.startTime,
               SUM(qty)         AS qty,
               SUM(totalAmount) AS totalAmount
        FROM sm_order_commission t1
                 LEFT JOIN sm_product t2 ON t1.productId = t2.id
                 LEFT JOIN sm_plan t3 ON t1.planId = t3.id
                 LEFT JOIN auth_user t4 ON t4.userId = t1.recommendId
                 LEFT JOIN sm_company t5 ON t5.id = t2.companyId
        WHERE 1 = 1
        <![CDATA[ AND t1.accountTime >= #{startTime} ]]>
        <![CDATA[ AND t1.accountTime <= #{endTime} ]]>
        GROUP BY t4.regionName, t4.organizationName, t1.planId, t1.startTime
    </insert>

    <delete id="deleteAllDayOrderReport">
        DELETE
        FROM sm_report_order;
    </delete>

    <delete id="deleteAllDayPolicyReport">
        DELETE
        FROM sm_report_policy;
    </delete>

    <delete id="deleteAllDayClaimReport">
        DELETE
        FROM sm_report_claim;
    </delete>
    <!--    查询指定时间的12周字符表达式 start-->
    <select id="getTwelveWeek" resultType="java.util.Map">
        SELECT DATE_FORMAT(DATE_SUB(#{endDate}, INTERVAL 11 WEEK), '%Y-%u') AS week12,
               DATE_FORMAT(DATE_SUB(#{endDate}, INTERVAL 10 WEEK), '%Y-%u') AS week11,
               DATE_FORMAT(DATE_SUB(#{endDate}, INTERVAL 9 WEEK), '%Y-%u')  AS week10,
               DATE_FORMAT(DATE_SUB(#{endDate}, INTERVAL 8 WEEK), '%Y-%u')  AS week9,
               DATE_FORMAT(DATE_SUB(#{endDate}, INTERVAL 7 WEEK), '%Y-%u')  AS week8,
               DATE_FORMAT(DATE_SUB(#{endDate}, INTERVAL 6 WEEK), '%Y-%u')  AS week7,
               DATE_FORMAT(DATE_SUB(#{endDate}, INTERVAL 5 WEEK), '%Y-%u')  AS week6,
               DATE_FORMAT(DATE_SUB(#{endDate}, INTERVAL 4 WEEK), '%Y-%u')  AS week5,
               DATE_FORMAT(DATE_SUB(#{endDate}, INTERVAL 3 WEEK), '%Y-%u')  AS week4,
               DATE_FORMAT(DATE_SUB(#{endDate}, INTERVAL 2 WEEK), '%Y-%u')  AS week3,
               DATE_FORMAT(DATE_SUB(#{endDate}, INTERVAL 1 WEEK), '%Y-%u')  AS week2,
               DATE_FORMAT(DATE_SUB(#{endDate}, INTERVAL 0 WEEK), '%Y-%u')  AS week1;
    </select>
    <!--    查询指定时间的12周字符表达式 end-->

</mapper>
