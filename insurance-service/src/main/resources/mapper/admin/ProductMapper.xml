<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.product.ProductMapper">

    <select id="getById" resultType="com.cfpamf.ms.insur.admin.pojo.po.product.SmProduct">
        select *
        from sm_product
        where id = #{id}
          and enabled_flag = 0
    </select>
    <select id="getByIdList" resultType="com.cfpamf.ms.insur.admin.pojo.po.product.SmProduct">
        select *
        from sm_product
        where
        enabled_flag = 0
        <choose>
            <when test="idList != null and idList.size() > 0">
                and id in
                <foreach collection="idList" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </when>
            <otherwise>
                and id = -1
            </otherwise>
        </choose>
    </select>

    <select id="listProductSalesOrgByOrgPath" resultType="java.lang.Integer">
        SELECT DISTINCT t1.product_id
        FROM sm_plan_sales_org t1
        LEFT JOIN sm_product t2 ON t1.product_id = t2.id
        WHERE
        t1.enabled_flag = 0
        AND t2.state=1
        <if test='orgName != null'>
            AND (t1.org_name = #{orgName} OR t1.org_path IS NULL)
        </if>
        <if test='orgName == null'>
            AND t1.org_path IS NULL
        </if>
    </select>
    <select id="searchConfigurableProduct"
            resultType="com.cfpamf.ms.insur.admin.distribution.config.vo.ProductSimpleVo">
        select sp.id, sp.productName
        from sm_product sp
        where sp.enabled_flag = 0
        and sp.state = 1
        <if test="productName != null and productName!= '' ">
            and sp.productName like CONCAT('%',#{productName},'%')
        </if>
        and id not in
        (
        select product_id
        from sm_product_distribution_config
        where enabled_flag = 0
        )
    </select>

    <select id="getRecommendedProductByIdList"
            resultType="com.cfpamf.ms.insur.admin.pojo.vo.product.RecommendedProductVO">
        SELECT
        t1.id,
        t1.productName,
        t2.name as productType,
        t3.companyName
        FROM sm_product t1
        LEFT JOIN dictionary t2 on t2.type = 'productGroup' and t1.productType = t2.code
        LEFT JOIN sm_company t3 on t1.companyId = t3.id
        WHERE
        t1.id in
        <foreach collection="idList" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="queryQuoteLimit"
            resultType="com.cfpamf.ms.insur.admin.distribution.config.vo.ProductSimpleVo">
        select sp.id, sp.productName
        from sm_product sp
        where sp.enabled_flag = 0
        and sp.state = 1
        <if test="productName != null and productName!= '' ">
            and sp.productName like CONCAT('%',#{productName},'%')
        </if>
        and id not in
        (
        select product_id
        from sm_product_distribution_config
        where enabled_flag = 0
        )
    </select>

    <select id="queryPremiumFactor" resultType="com.cfpamf.ms.insur.admin.pojo.dto.product.PremiumFlow">
        select
            *
        from sm_product_premium_flow
        where product_id = #{productId}
        and enabled_flag = 0
        <if test=" planId!=null ">
            and plan_id=#{planId}
        </if>
        <if test=" type!=null and type!='' ">
            and type=#{type}
        </if>
    </select>
</mapper>