<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cfpamf.ms.insur.admin.convertedpremium.dao.ConvertedPremiumConfigMapper">

    <!--    通过id查找折算保费配置 start-->
    <select id="findById"
            resultType="com.cfpamf.ms.insur.admin.convertedpremium.entity.ConvertedPremiumConfig">
        select *
        from sm_product_converted_premium_config
        where id = #{id}
          and enabled_flag = 0;
    </select>
    <!--    通过id查找折算保费配置 end-->

    <select id="searchByProductForm"
            resultType="com.cfpamf.ms.insur.admin.convertedpremium.vo.ConvertedPremiumConfigVo">
        SELECT
        t2.productName,
        t1.fhProductId as productId,
        t1.planName as productPlan,
        t4.companyName,
        t3.payment_period AS paymentPeriod,
        t3.proportion,
        t3.start_term_validity AS startTermValidity,
        t3.end_term_validity AS endTermValidity,
        t5.userName AS lastConfigUser,
        t3.update_time AS updateTime,
        t3.id as id
        FROM
        sm_plan t1
        LEFT JOIN sm_product t2 ON t1.productId = t2.id
        AND t2.enabled_flag = '0'
        LEFT JOIN sm_product_converted_premium_config t3 ON t3.plan_id = t1.id
        AND t3.enabled_flag = '0'
        LEFT JOIN sm_company t4 ON t4.id = t2.companyId
        AND t4.enabled_flag = '0'
        LEFT JOIN auth_user t5 ON t3.update_by = t5.mainJobNumber AND t5.enabled_flag = '0'
        WHERE
        t1.enabled_flag = '0'
        <if test="productId != null and productId !='' ">
            AND t1.fhProductId = #{productId}
        </if>
        <if test="productName != null and productName !='' ">
            AND t2.productName LIKE CONCAT('%',#{productName},'%')
        </if>
        ORDER BY
        <choose>
            <when test="sortField != null and sortField !='' ">
                #{sortField}
            </when>
            <otherwise>
                t3.update_time
            </otherwise>
        </choose>
        <choose>
            <when test="sortType != null and sortType !='' ">
                #{sortType}
            </when>
            <otherwise>
                DESC
            </otherwise>
        </choose>
    </select>


    <select id="searchConvertedPremiumConfigByProductForm"
            resultType="com.cfpamf.ms.insur.admin.convertedpremium.vo.ConvertedPremiumConfigVo">
        SELECT
        t2.productName,
        t1.fhProductId as productId,
        t1.planName as productPlan,
        t4.companyName,
        t3.payment_period AS paymentPeriod,
        t3.proportion,
        t3.start_term_validity AS startTermValidity,
        t3.end_term_validity AS endTermValidity,
        t5.userName AS lastConfigUser,
        t3.update_time AS updateTime,
        t3.id as id,
        t3.plan_id as planId
        FROM
        sm_product_converted_premium_config t3
        LEFT JOIN sm_plan t1 ON t3.plan_id = t1.id

        LEFT JOIN sm_product t2 ON t1.productId = t2.id

        LEFT JOIN sm_company t4 ON t4.id = t2.companyId

        LEFT JOIN auth_user t5 ON t3.update_by = t5.mainJobNumber AND t5.enabled_flag = '0'
        WHERE
        t3.enabled_flag = '0'
        <if test="productId != null and productId !='' ">
            AND t1.fhProductId = #{productId}
        </if>
        <if test="productName != null and productName !='' ">
            AND t2.productName LIKE CONCAT('%',#{productName},'%')
        </if>
        ORDER BY
        <choose>
            <when test="sortField != null and sortField !='' ">
                #{sortField}
            </when>
            <otherwise>
                t3.update_time
            </otherwise>
        </choose>
        <choose>
            <when test="sortType != null and sortType !='' ">
                #{sortType}
            </when>
            <otherwise>
                DESC
            </otherwise>
        </choose>
    </select>
    <!--    通过计划id集合搜索折算保费配置 start-->
    <select id="findByPlanIdList"
            resultType="com.cfpamf.ms.insur.admin.convertedpremium.entity.ConvertedPremiumConfig">
        select *
        from sm_product_converted_premium_config
        where enabled_flag = 0
        AND plan_id in
        <foreach item="item" index="index" open="(" separator="," close=")" collection="planIdList">
            #{item}
        </foreach>
    </select>
    <!--    通过计划id集合搜索折算保费配置 end-->

    <!--    通过计划id和支付时间获取折算保费配置start-->
    <select id="getByPlanIdAndPayTime"
            resultType="com.cfpamf.ms.insur.admin.convertedpremium.entity.ConvertedPremiumConfig">
        select *
        from sm_product_converted_premium_config
        where enabled_flag = 0
          AND plan_id = #{planId}
        <![CDATA[ AND DATE_ADD(end_term_validity, INTERVAL 1 DAY) >= #{payTime} ]]>
          <![CDATA[ AND start_term_validity <= #{payTime}
        ]]>
    </select>
    <!--    通过计划id和支付时间获取折算保费配置 end-->
    <!--  通过产品id获取折算保费配置集合 start  -->
    <select id="getConvertedPremiumConfigListByProductIdAndNow"
            resultType="com.cfpamf.ms.insur.admin.convertedpremium.entity.ConvertedPremiumConfig">
        select t1.*
        from sm_product_converted_premium_config t1
                 left join sm_plan t2 on t1.plan_id = t2.id
        where t1.enabled_flag = 0
          and t2.productId = #{productId}
              <![CDATA[ AND DATE_ADD(t1.end_term_validity, INTERVAL 1 DAY) >= now() ]]>
          <![CDATA[ AND t1.start_term_validity <= now()
        ]]>
    </select>

    <select id="getConvertedPremiumConfig4Version"
            resultType="com.cfpamf.ms.insur.admin.convertedpremium.entity.ConvertedPremiumConfig">
        select
            t1.*
        from sm_product_converted_premium_config t1
        left join sm_plan_history t2 on t1.plan_id = t2.planId and t2.version=#{version}
        where t1.enabled_flag = 0
        and t2.productId = #{productId}
        <![CDATA[
            AND DATE_ADD(t1.end_term_validity, INTERVAL 1 DAY) >= now()
        ]]>
        <![CDATA[
            AND t1.start_term_validity <= now()
        ]]>
    </select>

    <!--  通过产品id获取折算保费配置集合 end  -->

    <select id="queryEffectiveConfigByPlans"
            resultType="com.cfpamf.ms.insur.admin.convertedpremium.entity.ConvertedPremiumConfig">
        select *
        from sm_product_converted_premium_config t1
        where t1.enabled_flag = 0
        <![CDATA[ AND DATE_ADD(t1.end_term_validity, INTERVAL 1 DAY) >= now() ]]>
        <![CDATA[ AND t1.start_term_validity <= now() ]]>
        AND t1.plan_id in
        <foreach item="item" index="index" open="(" separator="," close=")" collection="planIdList">
            #{item}
        </foreach>
    </select>
</mapper>