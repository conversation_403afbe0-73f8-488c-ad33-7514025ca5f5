<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.UserPostMapper">
    <!--批量插入，当唯一索引已存在，则按唯一索引来更新记录 （用jobcode做唯一索引，与凌鹏和晓君确认，jobcode可以作为唯一索引用）-->
    <update id="batchInsertUserPost" useGeneratedKeys="true" parameterType="java.util.List">
        insert into
        user_post(job_code,main_job_number,job_number,hr_user_id,region_code,region_name,hr_region_id,hr_org_id,org_code,org_name,organization_name
        ,org_path,post_id,post_code,post_name,service_type,employee_status,start_date,stop_date
        ,last_work_date,service_status,batch_no,bms_user_id
        ,user_admin_id,user_admin_name,user_master_id,user_master_name,entry_date)
        values
        <foreach collection="list" item="userPost" separator=",">
        (#{userPost.jobCode},#{userPost.mainJobNumber},#{userPost.jobNumber},#{userPost.hrUserId},#{userPost.regionCode},#{userPost.regionName},#{userPost.hrRegionId},#{userPost.hrOrgId},#{userPost.orgCode},#{userPost.orgName},#{userPost.organizationName}
            ,#{userPost.orgPath},#{userPost.postId},#{userPost.postCode},#{userPost.postName},#{userPost.serviceType},#{userPost.employeeStatus},#{userPost.startDate},#{userPost.stopDate}
            ,#{userPost.lastWorkDate},#{userPost.serviceStatus},#{userPost.batchNo},#{userPost.bmsUserId}
            ,#{userPost.userAdminId},#{userPost.userAdminName},#{userPost.userMasterId},#{userPost.userMasterName},#{userPost.entryDate}
        )
        </foreach>
        ON DUPLICATE KEY UPDATE
        job_code=values(job_code),main_job_number=values(main_job_number),job_number=values(job_number),hr_user_id=values(hr_user_id),region_code=values(region_code),region_name=values(region_name)
       ,hr_region_id=values(hr_region_id),hr_org_id=values(hr_org_id),org_code=values(org_code)
        ,org_name=values(org_name),organization_name=values(organization_name),org_path=values(org_path),post_id=values(post_id),post_code=values(post_code)
        ,post_name=values(post_name),service_type=values(service_type),employee_status=values(employee_status),start_date=values(start_date)
        ,stop_date=values(stop_date),last_work_date=values(last_work_date),service_status=values(service_status),batch_no=values(batch_no)
        ,bms_user_id=values(bms_user_id)
        ,user_admin_id=values(user_admin_id),user_admin_name=values(user_admin_name),user_master_id=values(user_master_id),user_master_name=values(user_master_name),entry_date=values(entry_date)
    </update>

    <update id="updateEndServiceStatusByHrUserId">
        update user_post set service_status = 1 where hr_user_id = #{hrUserId}
    </update>

    <update id="updateEndServiceStatus">
        update user_post set service_status = 1
    </update>

    <select id="selectUserPostByJobCode" resultType="com.cfpamf.ms.insur.admin.pojo.po.UserPost">
        select <include refid="queryColumn"/>
        from user_post where job_code = #{jobCode}
    </select>

    <sql id="queryColumn">
        job_code as jobCode,main_job_number as mainJobNumber,job_number as jobNumber
        ,hr_user_id as hrUserId,region_code as regionCode,region_name as regionName
        ,hr_region_id as hrRegionId,hr_org_id as hrOrgId,org_code as orgCode,org_name as orgName,organization_name as organizationName
        ,org_path as orgPath,post_id as postId,post_code as postCode,post_name as postName
        ,service_type as serviceType,employee_status as employeeStatus,start_date as startDate
        ,stop_date as stopDate,last_work_date lastWorkDate,service_status as serviceStatus,batch_no as batchNo,bms_user_id as bmsUserId
        ,user_admin_id as userAdminId,user_admin_name as userAdminName,user_master_id as userMasterId,user_master_name as userMasterName
    </sql>

    <select id="listUserPostByJobCodes" resultType="com.cfpamf.ms.insur.admin.pojo.po.UserPost">
        select <include refid="queryColumn"/>
        from user_post where job_code in
        <foreach collection="jobCodeList" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="queryByUserIdList" resultType="com.cfpamf.ms.insur.admin.pojo.po.UserPost">
        select
            <include refid="queryColumn"/>
        from user_post
        where service_status = 0
        and   job_number in
        <foreach collection="userIdList" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
        order by service_type
    </select>
    <select id="querySupervisorUserPost" resultType="com.cfpamf.ms.insur.admin.pojo.po.UserPost">
        select t1.*
        from user_post t1
        left join auth_user t2 on t2.userId = t1.job_number and t2.enabled_flag = 0
        where t1.main_job_number in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and t1.service_status = 0
        <![CDATA[
          and t1.employee_status <> 8
        ]]>
        and org_name like '%管理部'
    </select>

    <select id="listByPostId" resultType="com.cfpamf.ms.insur.admin.pojo.po.UserPost">
        select t1.*
        from user_post t1
                 left join auth_user t2 on t2.userId = t1.job_number and t2.enabled_flag = 0
        where post_code = #{postCode}
          and service_status = 0
       <![CDATA[ and region_code <> '1'
          and employee_status <> 8
        ]]>
    </select>

</mapper>
