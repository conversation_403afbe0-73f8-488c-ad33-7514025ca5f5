<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.SystemMsgCenterMapper">
    <select id="listUncheckMsg" resultType="com.cfpamf.ms.insur.admin.pojo.dto.SystemMsgCenterDTO">
        select c.id,c.op_time,c.msg_id,t.buz_id,t.buz_type,t.buz_desc,t.result_code,t.result_msg
        ,c.check_flag,c.check_time,c.user_id,c.org_code
        from system_msg_content t,system_msg_center c
        where t.id = c.msg_id
        <if test="checkFlag!=null">
            and c.check_flag = #{checkFlag}
        </if>
        <if test="userId!=null and userId!=''">
            and c.user_id = #{userId}
        </if>
        <if test="orgCode!=null and orgCode!=''">
            and c.org_code = #{orgCode}
        </if>
        order by c.op_time desc
    </select>

    <select id="listMsgByResultCode" resultType="com.cfpamf.ms.insur.admin.pojo.dto.SystemMsgCenterDTO">
        select c.id,c.op_time,c.msg_id,t.buz_id,t.buz_type,t.buz_desc,t.result_code,t.result_msg
        ,c.check_flag,c.check_time,c.user_id,c.org_code
        from system_msg_content t,system_msg_center c
        where t.id = c.msg_id
        <if test="resultCode!=null">
            and t.result_code = #{resultCode}
        </if>
        <if test="userId!=null and userId!=''">
            and c.user_id = #{userId}
        </if>
        <if test="orgCode!=null and orgCode!=''">
            and c.org_code = #{orgCode}
        </if>
        order by c.op_time desc
    </select>
</mapper>
