<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.product.SmProductHealthInformMapper">

    <update id="softDeleteByProduct">
        update sm_product_health_inform
        set enabled_flag = 1
        where product_id = #{productId}
    </update>

    <update id="pushNewVersion">
        update sm_product_health_inform sphi
            left join sm_product_health_inform_history sphih
        on sphi.id = sphih.product_health_inform_id and sphih.enabled_flag = 0
            set
                sphi.product_id = sphih.product_id,
                sphi.name = sphih.name,
                sphi.inform_object = sphih.inform_object,
                sphi.risk_code = sphih.risk_code,
                sphi.content = sphih.content,
                sphi.ai_check_flag = sphih.ai_check_flag,
                sphi.ai_check_way = sphih.ai_check_way,
                sphi.question_bank_file_path = sphih.question_bank_file_path,
                sphi.enabled_flag = sphih.enabled_flag,
                sphi.create_by = sphih.create_by,
                sphi.create_time = sphih.create_time,
                sphi.update_by = sphih.update_by,
                sphi.update_time = sphih.update_time
        where sphih.product_id = #{productId}
          and sphih.version = #{version}
          and sphih.enabled_flag = 0
    </update>
</mapper>