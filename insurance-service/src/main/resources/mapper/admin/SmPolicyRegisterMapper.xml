<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.order.SmPolicyRegisterMapper">
    <select id="listSimple" resultType="com.cfpamf.ms.insur.admin.pojo.po.order.SmPolicyRegister">
        select *
        from sm_policy_register
        where create_by = #{createBy}
        <if test="keyword !=null and keyword!=''">
            and (policy_no like concat(#{keyword}, '%') or applicant_name like concat(#{keyword}, '%'))
        </if>
        order by id desc
    </select>
    <select id="listByQuery" resultType="com.cfpamf.ms.insur.admin.pojo.vo.order.SmPolicyRegisterVO">
        select spr.*,
        au.regionName as region_name,
        au.userMobile as user_mobile,
        au.organizationName as organization_name
        from sm_policy_register spr
        left join auth_user au on au.userId = spr.user_id and au.enabled_flag = 0
        <where>
            <if test="subDateStart !=null">
                <![CDATA[  spr.create_time >= #{subDateStart} ]]>
            </if>
            <if test="subDateEnd !=null">
                <![CDATA[ and spr.create_time < #{subDateEnd} ]]>
            </if>
            <if test="state!=null">and state=#{state}</if>

            <if test="recommendId!=null">
                AND ((au.userId LIKE CONCAT(#{recommendId},'%')) OR (au.userName LIKE CONCAT(#{recommendId},'%')))
            </if>

            <if test="userId!=null">
            and au.userId = #{userId}
            </if>
            <if test="policyNo!=null">and policy_no=#{policyNo}</if>
            <if test="orgName!=null">
                and organizationName =#{orgName}
            </if>
            <if test="regionName!=null">and regionName = #{regionName}</if>
            <if test="userMobile !=null">
                and au.userMobile LIKE CONCAT('%',#{userMobile},'%')
            </if>

            <if test="applicantName !=null">
                and spr.applicant_name LIKE CONCAT('%',#{applicantName},'%')
            </if>
        </where>
        order by spr.id desc
    </select>
</mapper>
