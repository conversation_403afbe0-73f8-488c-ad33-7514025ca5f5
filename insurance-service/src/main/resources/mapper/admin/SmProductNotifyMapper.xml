<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.SmProductNotifyMapper">
    <select id="selectByProductId4His" resultType="com.cfpamf.ms.insur.admin.pojo.po.SmProductNotify">

        select id,
               productId,
               custNotify,
               custNotifyName,
               custNotifyUrl,
               custNotifyContent,
               update_time,
               enabled_flag,
               create_time,
               take_time,
               version
        from sm_product_notify_history
        where productId = #{productId}
          and version = #{version}
          and enabled_flag = 0
    </select>
</mapper>
