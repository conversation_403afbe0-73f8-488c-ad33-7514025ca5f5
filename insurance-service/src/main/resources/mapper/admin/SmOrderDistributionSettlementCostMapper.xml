<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.order.SmOrderDistributionSettlementCostMapper">
    <select id="getVersion" resultType="java.lang.Integer">
        select ifnull(max(version),0)+1 max_version
        from sm_order_distribution_settlement_cost
        where fh_order_id = #{fhOrderId}
    </select>
</mapper>