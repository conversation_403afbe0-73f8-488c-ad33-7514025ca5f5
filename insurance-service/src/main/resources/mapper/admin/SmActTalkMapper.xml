<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.pco.SmActTalkMapper">
    <select id="selectByQuery" resultType="com.cfpamf.ms.insur.admin.pojo.vo.pco.SmActTalkVO">

        select a.*, au.userName expert_name, aupc.userName pco_name
        from sm_act_talk a
        left join auth_user au on au.userId = a.expert_number and au.enabled_flag = 0
        left join auth_user aupc on aupc.userId = a.pco_job_number and aupc.enabled_flag = 0
        where a.enabled_flag = 0
        <if test="state != null"><![CDATA[ and a.state = #{state} ]]></if>
        <if test="talkId != null"><![CDATA[ and a.id = #{talkId} ]]></if>
        <if test="startDate != null"><![CDATA[ and a.start_time >= #{startDate} ]]></if>
        <if test="endDate != null"><![CDATA[ and a.start_time < #{endDate} ]]></if>
        <if test="pcoJobNumber != null"><![CDATA[ and a.pco_job_number = #{pcoJobNumber} ]]></if>
        <if test="expertNumber != null"><![CDATA[ and a.expert_number = #{expertNumber} ]]></if>
        <if test="orgName != null"><![CDATA[ and a.organization_name = #{orgName} ]]></if>
        <if test="regionName != null"><![CDATA[ and a.region_name = #{regionName} ]]></if>
        order by id desc
    </select>
</mapper>
