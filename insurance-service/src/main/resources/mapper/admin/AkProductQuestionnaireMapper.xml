<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.aicheck.AkProductQuestionnaireMapper">
    <resultMap id="BaseResultMap" type="com.cfpamf.ms.insur.admin.pojo.vo.aicheck.AkProductQuestionnaireVO">
        <id column="id" jdbcType="INTEGER" property="id" />
        <result column="product_id" jdbcType="INTEGER" property="productId" />
        <result column="questionnaire_code" jdbcType="VARCHAR" property="questionnaireCode" />
        <result column="file_name" jdbcType="VARCHAR" property="fileName" />
        <result column="file_url" jdbcType="VARCHAR" property="fileUrl" />
        <result column="create_by" jdbcType="VARCHAR" property="createBy" />
        <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
        <result column="version" jdbcType="INTEGER" property="version" />
        <result column="enabled_flag" jdbcType="INTEGER" property="enabledFlag" />
    </resultMap>
    <sql id="BaseTable">
        ak_product_questionnaire
    </sql>
</mapper>