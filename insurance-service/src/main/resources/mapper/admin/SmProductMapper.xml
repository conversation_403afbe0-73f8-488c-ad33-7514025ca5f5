<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.SmProductMapper">

    <select id="listWxProducts" resultType="com.cfpamf.ms.insur.weixin.pojo.vo.WxProductListVO">
        SELECT t1.id, t1.channel, t1.productName,t1.productShortName, t1.productFeature, t1.thumbnailImageUrl,
        t1.minAmount, t1.saleQty
        FROM sm_product t1
        WHERE t1.state=1 AND t1.enabled_flag=0
        AND t1.productAttrCode in ('person','employer')
        <if test='productCategoryId != null'>
            AND (t1.productCategoryId = #{productCategoryId} OR t1.productCategoryId LIKE CONCAT(#{productCategoryId},
            ',%')
            OR t1.productCategoryId LIKE CONCAT('%,',#{productCategoryId}, ',%') OR t1.productCategoryId LIKE
            CONCAT('%,',#{productCategoryId}))
        </if>
        <if test='channel != null'>
            AND t1.channel = #{channel}
        </if>
        <if test='companyId != null'>
            AND t1.companyId = #{companyId}
        </if>
        <if test='salesProductIds != null'>
            AND t1.id IN
            <foreach item="item" index="index" open="(" separator="," close=")" collection="salesProductIds">
                #{item}
            </foreach>
        </if>
        <if test='keyword != null'>
            AND MATCH(t1.productName, t1.productTags, t1.productFeature) AGAINST(#{keyword} IN BOOLEAN MODE)
        </if>
        ORDER BY t1.companyId ASC, t1.productName ASC
    </select>

    <select id="listWxHotProducts" resultType="com.cfpamf.ms.insur.weixin.pojo.vo.WxProductListVO">
        SELECT t1.id, t1.channel, t1.productName,t1.productShortName, t1.productFeature, t1.thumbnailImageUrl,
        t1.productAttrCode,t1.minAmount, t2.times AS
        saleQty
        FROM sm_product t1
        LEFT JOIN
        (SELECT productId, COUNT(1) AS times FROM sm_order WHERE
        <![CDATA[ DATE_SUB(CURDATE(), INTERVAL 1 MONTH) <= create_time ]]> AND payStatus = 2 GROUP BY productId ) t2
        ON t1.id = t2.productId
        WHERE t1.state=1 AND t1.enabled_flag=0
        AND t1.productAttrCode in ('person','employer')
        <if test='channel != null'>
            AND t1.channel = #{channel}
        </if>
        <if test='salesProductIds != null'>
            AND t1.id IN
            <foreach item="item" index="index" open="(" separator="," close=")" collection="salesProductIds">
                #{item}
            </foreach>
        </if>
        ORDER BY t2.times DESC, t1.productName ASC
        LIMIT 5
    </select>

    <select id="getWxProductDetailById" resultType="com.cfpamf.ms.insur.weixin.pojo.vo.WxProductDetailVO">
        SELECT t1.id,
               t1.channel,
               t1.productName,
               t1.productShortName,
               t1.productFeature,
               t1.productTags,
               t1.introduceImageUrl,
               t1.companyId,
               t2.companyLogoImageUrl,
               t2.companyName,
               t1.attentions,
               t1.buyLimit,
               t1.effectWaitingDayMin,
               t1.effectWaitingDayMax,
               t1.attentions,
               t1.healthNotification,
               t1.minAmount,
               t1.aiCheck,
               t1.aiCheckWay

        FROM sm_product t1
                 LEFT JOIN sm_company t2 ON t1.companyId = t2.id
        WHERE t1.id = #{id}
          AND t1.state = 1
          AND t1.enabled_flag = 0
    </select>

    <select id="listBackendProducts" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmProductListVO">
        SELECT t2.*, t2.update_by AS modifyBy, t2.update_time AS modifyTime, t3.name AS productCategoryName,
        t4.companyName AS companyName, GROUP_CONCAT(DISTINCT t5.org_name) AS salesOrgName
        FROM sm_product t2
        LEFT JOIN dictionary t3 on t2.productCategoryId = t3.id AND t3.enabled_flag=0
        LEFT JOIN sm_company t4 on t2.companyId = t4.id AND t4.enabled_flag=0
        LEFT JOIN sm_plan_sales_org t5 on t5.product_id = t2.id AND t5.enabled_flag=0
        WHERE t2.enabled_flag=0
        <if test='query.productName != null and query.productName != ""'>
            AND t2.productName LIKE CONCAT('%',#{query.productName},'%')
        </if>
        <if test='query.fhProductId != null'>
            AND t2.id IN (SELECT productId FROM sm_plan t11 WHERE t11.fhProductId =#{query.fhProductId} )
        </if>
        <if test='query.productCategoryId != null'>
            AND (t2.productCategoryId = #{query.productCategoryId} OR t2.productCategoryId LIKE
            CONCAT(#{query.productCategoryId}, ',%')
            OR t2.productCategoryId LIKE CONCAT('%,',#{query.productCategoryId}, ',%') OR t2.productCategoryId LIKE
            CONCAT('%,',#{query.productCategoryId}))
        </if>
        <if test='query.companyId != null'>
            AND t2.companyId =#{query.companyId}
        </if>
        <if test='query.state != null'>
            AND t2.state =#{query.state}
        </if>
        <if test='query.activeFlag != null'>
            AND t2.activeFlag =#{query.activeFlag}
        </if>
        <if test='query.productAttrCode != null'>
            AND t2.productAttrCode =#{query.productAttrCode}
        </if>
        <if test="query.onlineChannel != null and query.onlineChannel!=''">
            and t2.onlineChannel like CONCAT('%',#{query.onlineChannel}, '%')
        </if>
        <if test="query.channel != null and query.channel != ''">
            and t2.channel= #{query.channel}
        </if>
        <if test="query.productType != null and query.productType != ''">
            and t2.productType = #{query.productType}
        </if>
        <if test="query.productTypeList != null and query.productTypeList.size>0">
            and t2.productType in
            <foreach collection="query.productTypeList" item="item" separator="," open="("  close=")" >
                #{item}
            </foreach>
        </if>
        GROUP BY t2.id
        ORDER BY t2.sortNum asc,t2.update_time DESC
    </select>

    <select id="listNewestBackendProducts" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmProductListVO">
        SELECT t6.productId id,
        t6.product_code as productCode,
        t6.productCategoryId,
        t6.channel,
        t6.productAttrCode,
        t3.name AS productCategoryName,
        t6.companyId,
        t4.companyName AS companyName,
        t6.productName,
        t6.productShortName,
        t6.headImageUrl,
        t6.productTags,
        t6.productFeature,
        t6.thumbnailImageUrl,
        t2.state,
        GROUP_CONCAT(DISTINCT t5.org_name) AS salesOrgName,
        t2.onlineChannel,
        t6.update_by AS modifyBy,
        t6.update_time AS modifyTime,
        t6.apiType,
        t6.h5Url,
        t6.activeFlag,
        t6.sortNum,
        t6.custNotify,
        t6.version,
        t6.create_type,
        t6.long_insurance,
        t6.product_model
        FROM sm_product_history t6
        left join sm_product t2 on t6.productId = t2.id
        LEFT JOIN dictionary t3 on t6.productCategoryId = t3.id AND t3.enabled_flag=0
        LEFT JOIN sm_company t4 on t6.companyId = t4.id AND t4.enabled_flag=0
        LEFT JOIN sm_plan_sales_org t5 on t5.product_id = t6.productId AND t5.enabled_flag=0
        WHERE t2.enabled_flag=0
        <if test="query.productName != null and query.productName != '' ">
            AND t6.productName LIKE CONCAT('%',#{query.productName},'%')
        </if>
        <if test='query.fhProductId != null'>
            AND t2.id IN (SELECT productId FROM sm_plan t11 WHERE t11.fhProductId =#{query.fhProductId} )
        </if>
        <if test='query.productCategoryId != null'>
            AND (t2.productCategoryId = #{query.productCategoryId} OR t2.productCategoryId LIKE
            CONCAT(#{query.productCategoryId}, ',%')
            OR t2.productCategoryId LIKE CONCAT('%,',#{query.productCategoryId}, ',%') OR t2.productCategoryId LIKE
            CONCAT('%,',#{query.productCategoryId}))
        </if>
        <if test='query.companyId != null'>
            AND t2.companyId =#{query.companyId}
        </if>
        <if test='query.state != null'>
            AND t2.state =#{query.state}
        </if>
        <if test='query.activeFlag != null'>
            AND t2.activeFlag =#{query.activeFlag}
        </if>
        <if test='query.productAttrCode != null'>
            AND t2.productAttrCode =#{query.productAttrCode}
        </if>
        <if test="query.onlineChannel != null and query.onlineChannel!=''">
            and t2.onlineChannel like CONCAT('%',#{query.onlineChannel}, '%')
        </if>
        <if test="query.channel != null and query.channel != ''">
            and t2.channel= #{query.channel}
        </if>
        GROUP BY t6.productId,t6.version
        ORDER BY t2.sortNum asc,t2.update_time DESC
    </select>

    <select id="listActivityProducts" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmActivityProductVO">
        SELECT t1.*, t1.id AS productId,
        t4.companyName AS companyName
        FROM sm_product t1
        LEFT JOIN sm_company t4 on t1.companyId = t4.id AND t4.enabled_flag=0
        WHERE t1.enabled_flag = 0
        <if test='productName != null'>
            AND t1.productName LIKE CONCAT('%',#{productName},'%')
        </if>
        <if test='categoryId != null'>
            AND (t1.productCategoryId = #{categoryId} OR t1.productCategoryId LIKE CONCAT(#{categoryId}, ',%')
            OR t1.productCategoryId LIKE CONCAT('%,',#{categoryId}, ',%') OR t1.productCategoryId LIKE
            CONCAT('%,',#{categoryId}))
        </if>
        <if test='companyId != null'>
            AND t1.companyId =#{companyId}
        </if>
        <if test='channel != null and channel != ""'>
            AND t1.channel =#{channel}
        </if>
        <if test='productIds != null'>
            AND t1.id IN
            <foreach item="item" index="index" open="(" separator="," close=")" collection="productIds">
                #{item}
            </foreach>
        </if>
        <choose>
            <when test="states != null">
                AND t1.state IN
                <foreach collection="states" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                AND t1.state = 1
            </otherwise>
        </choose>
        ORDER BY t1.id ASC
    </select>

    <select id="listProductPlansById" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmPlanVO">
        SELECT t1.*, t2.id AS productId, t2.productName AS productName, t3.companyName AS companyName FROM sm_plan t1
        LEFT JOIN sm_product t2 ON t1.productId=t2.id
        LEFT JOIN sm_company t3 ON t2.companyId=t3.id
        WHERE t1.enabled_flag = 0
        <if test='productId != null'>
            AND t1.productId = #{productId}
        </if>
        <if test='onlyValid != null'>
            AND t2.enabled_flag=0 AND t3.enabled_flag=0
        </if>
    </select>

    <select id="getPlanListByPlanIdList" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmPlanVO">
        SELECT t1.*, t2.id AS productId, t2.productName AS productName, t2.state as productStatus
        FROM sm_plan t1
        LEFT JOIN sm_product t2 ON t1.productId=t2.id
        WHERE t1.enabled_flag = 0
        AND t1.id in
        <foreach item="item" index="index" open="(" separator="," close=")" collection="planIdList">
            #{item}
        </foreach>
    </select>

    <select id="listProductPlansByIds" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmPlanVO">
        SELECT t1.*, t2.id AS productId, t2.productName AS productName FROM sm_plan t1
        LEFT JOIN sm_product t2 ON t1.productId=t2.id
        WHERE t1.enabled_flag = 0
        AND productId IN
        <foreach item="item" index="index" open="(" separator="," close=")" collection="productIds">
            #{item}
        </foreach>
    </select>

    <select id="getPlanById" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmPlanVO">
        SELECT t1.*, t2.productName, t2.buyLimit, t2.channel, t2.companyId
        FROM sm_plan t1
        LEFT JOIN sm_product t2 ON t1.productId = t2.id
        WHERE t1.id = #{planId}
    </select>

    <select id="getPlanByOrderId" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmPlanVO">
        SELECT t1.*, t2.productName, t2.buyLimit, t2.channel, t2.companyId
        FROM sm_plan t1
        LEFT JOIN sm_product t2 ON t1.productId = t2.id
        left join sm_order t3 on t1.id=t3.planId
        WHERE t3.fhOrderId = #{orderId}
    </select>

    <select id="getActivePlanById" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmPlanVO">
        SELECT t1.*, t2.productName, t2.buyLimit, t2.channel, t2.companyId
        FROM sm_plan t1
                 LEFT JOIN sm_product t2 ON t1.productId = t2.id
        WHERE t1.id = #{planId}
          and t1.enabled_flag = '0'
          AND t2.enabled_flag = '0'
    </select>

    <select id="getPlanByPlanIdList" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmPlanVO">
        SELECT t1.*
        FROM sm_plan t1
        WHERE
        t1.enabled_flag = '0' AND t1.id in
        <foreach item="item" index="index" open="(" separator="," close=")" collection="planIdList">
            #{item}
        </foreach>
    </select>


    <select id="getPlanByFhProductId" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmPlanVO">
        SELECT t1.*, t2.channel, t2.productName, t2.companyId
        FROM sm_plan t1
                 LEFT JOIN sm_product t2 ON t1.productId = t2.id
        WHERE t1.enabled_flag = '0'
          AND t2.enabled_flag = '0'
          AND t1.fhProductId = #{fhProductId}
        LIMIT 1
    </select>

    <select id="getProductById" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmProductDetailVO">
        SELECT t1.*, t2.name AS productCategoryName, t3.companyName AS companyName
        FROM sm_product t1
                 LEFT JOIN dictionary t2 on t1.productCategoryId = t2.id AND t2.enabled_flag = 0
                 LEFT JOIN sm_company t3 on t1.companyId = t3.id
        WHERE t1.id = #{id}
    </select>

    <select id="getMaxSortNum" resultType="java.lang.Integer">
        SELECT IFNULL(max(sortNum), 0)
        from sm_product
        where enabled_flag = 0
    </select>

    <insert id="insertProduct" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO sm_product
        (productAttrCode, productCategoryId, companyId, productName, productShortName, productTags, productFeature,
         thumbnailImageUrl,
         introduceImageUrl, buyLimit, effectWaitingDayMin,
         effectWaitingDayMax, state,
         channel, glOcpnGroup, glUwaFrom, glUwaTo, validPeriod, productSpecialsJoin,
         apiType, h5Url, headImageUrl,
         update_by, sortNum,
         update_time, enabled_flag, version, create_by, productType, miniPremium,
         product_code,
         sales_mode,
         explain_video, explain_video_img, create_type, long_insurance,product_model,product_star_rating)
        VALUES (#{productAttrCode}, #{productCategoryId}, #{companyId}, #{productName},
                #{productShortName}, #{productTags},
                #{productFeature},
                #{thumbnailImageUrl}, #{introduceImageUrl}, #{buyLimit}, #{effectWaitingDayMin},
                #{effectWaitingDayMax}, 0, #{channel},
                #{glOcpnGroup},
                #{glUwaFrom}, #{glUwaTo}, #{validPeriod}, #{productSpecialsJoin},
                #{apiType}, #{h5Url}, #{headImageUrl}, #{modifyBy}, #{sortNum},
                CURRENT_TIMESTAMP(), 0, #{version}, #{modifyBy}, #{productType}, #{miniPremium}, #{productCode},
                #{salesMode},
                #{explainVideo},
                #{explainVideoImg},
                #{createType, typeHandler=org.apache.ibatis.type.EnumTypeHandler},
                #{longInsurance},#{productModel},#{productStarRating})
    </insert>

    <update id="updateSortNum">
        <![CDATA[
        SET @oldSortNum = (select sortNum
                           from sm_product
                           where id = #{id});
        SET @num := CASE WHEN @oldSortNum > #{newSortNum} THEN #{newSortNum} ELSE @oldSortNum - 1 END;
        UPDATE sm_product
        SET sortNum = (@num := (CASE WHEN @num = #{newSortNum} - 1 THEN @num + 2 ELSE @num + 1 END))
        WHERE sortNum >= (CASE WHEN @oldSortNum > #{newSortNum} THEN #{newSortNum} ELSE @oldSortNum END)
          and id != #{id}
        ORDER BY sortNum ASC, id asc;
        UPDATE sm_product
        SET sortNum =#{newSortNum}
        where id = #{id};
        ]]>
    </update>

    <update id="updateProduct">
        UPDATE sm_product
        SET productAttrCode=#{productAttrCode},
            productCategoryId=#{productCategoryId},
            companyId=#{companyId},
            productName=#{productName},
            productShortName=#{productShortName},
            productTags=#{productTags},
            productFeature=#{productFeature},
            thumbnailImageUrl=#{thumbnailImageUrl},
            channel=#{channel},
            headImageUrl=#{headImageUrl},
            introduceImageUrl=#{introduceImageUrl},
            effectWaitingDayMin=#{effectWaitingDayMin},
            effectWaitingDayMax=#{effectWaitingDayMax},
            buyLimit=#{buyLimit},
            glOcpnGroup=#{glOcpnGroup},
            glUwaFrom=#{glUwaFrom},
            glUwaTo=#{glUwaTo},
            validPeriod=#{validPeriod},
            productSpecialsJoin=#{productSpecialsJoin},
            apiType=#{apiType},
            h5Url=#{h5Url},
            update_by=#{modifyBy},
            miniPremium=#{miniPremium},
            sales_mode=#{salesMode},
            update_time=CURRENT_TIMESTAMP(),
            long_insurance=#{longInsurance},
            productModel=#{prodyctModel}
        WHERE id = #{id}

    </update>

    <update id="deleteProduct">
        UPDATE sm_product
        SET enabled_flag = 1,
            update_by=#{modifyBy},
            update_time=CURRENT_TIMESTAMP()
        WHERE id = #{id}

    </update>

    <update id="updateProductStatus">
        UPDATE sm_product
        SET state=#{status},
            update_by=#{modifyBy},
            update_time=CURRENT_TIMESTAMP()
        WHERE id = #{id}
    </update>

    <update id="updateProductActiveFlag">
        UPDATE sm_product
        SET activeFlag=#{activeFlag},
            update_time=CURRENT_TIMESTAMP()
        WHERE id = #{id}
    </update>

    <update id="updateProductAttention">
        UPDATE sm_product
        SET attentions=#{attentions},
            update_by=#{modifyBy},
            update_time=CURRENT_TIMESTAMP()
        WHERE id = #{productId}

    </update>

    <update id="updateProductHealthNotification">
        UPDATE sm_product
        SET healthNotification=#{healthNotification},
            update_by=#{modifyBy},
            update_time=CURRENT_TIMESTAMP()
        WHERE id = #{productId}

    </update>

    <update id="updateProductIntroduce">
        UPDATE sm_product
        SET glProductIntroduce=#{glProductIntroduce},
            update_by=#{modifyBy},
            update_time=CURRENT_TIMESTAMP()
        WHERE id = #{productId}

    </update>

    <update id="updateProductNotice">
        UPDATE sm_product
        SET glProductNotice=#{glProductNotice},
            update_by=#{modifyBy},
            update_time=CURRENT_TIMESTAMP()
        WHERE id = #{productId}

    </update>

    <insert id="insertProductPlans" useGeneratedKeys="true">
        INSERT INTO sm_plan (productId,planName,description,fhProductId,min_premium,update_by,update_time, enabled_flag)
        VALUES
        <foreach collection="plans" item="item" index="index" separator=",">
            (#{item.productId}, #{item.planName}, #{item.description}, #{item.fhProductId},
            #{item.minPremium},#{item.modifyBy},CURRENT_TIMESTAMP(), 0)
        </foreach>
    </insert>

    <insert id="insertProductPlansDisable" parameterType="java.util.List"
            keyProperty="id" useGeneratedKeys="true">
        INSERT INTO sm_plan (productId,planName,description,fhProductId,min_premium,update_by,update_time, enabled_flag)
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.productId}, #{item.planName}, #{item.description}, #{item.fhProductId},
            #{item.minPremium},#{item.modifyBy},CURRENT_TIMESTAMP(), 1)
        </foreach>
    </insert>

    <update id="deleteProductPlan">
        UPDATE sm_plan
        SET enabled_flag=1,
            update_by=#{modifyBy},
            update_time=CURRENT_TIMESTAMP()
        WHERE id = #{id}
    </update>

    <select id="listDistinctFhProductIds" resultType="java.lang.String">
        SELECT distinct fhProductId
        from sm_plan
        WHERE enabled_flag = 0
    </select>

    <insert id="insertProductClause" useGeneratedKeys="true">
        INSERT INTO sm_product_clause
        (productId,clauseName,clauseUrl,update_by,update_time,enabled_flag,sort)
        VALUES
        <foreach collection="clauses" item="item" index="index" separator=",">
            (#{item.productId}, #{item.clauseName}, #{item.clauseUrl}, #{item.modifyBy}, CURRENT_TIMESTAMP(), 0,sort)
        </foreach>
    </insert>

    <update id="deleteProductClause">
        UPDATE sm_product_clause
        SET enabled_flag=1,
            update_by=#{modifyBy},
            update_time=CURRENT_TIMESTAMP()
        WHERE productId = #{id}
    </update>

    <delete id="deleteProductClauseContent">
        delete
        from sm_product_clause_content
        WHERE productId = #{productId}
    </delete>

    <insert id="insertProductFormField" useGeneratedKeys="true">
        INSERT INTO sm_product_form_field
        (productId, groupCode,groupName, fieldCode,fieldName, display, required,params, update_by, update_time,
        enabled_flag)
        VALUES
        <foreach collection="formFields" item="item" index="index" separator=",">
            (#{item.productId},#{item.groupCode},#{item.groupName},#{item.fieldCode},#{item.fieldName},#{item.display},#{item.required},#{item.params},#{item.modifyBy},
            CURRENT_TIMESTAMP(),0)
        </foreach>
    </insert>

    <update id="deleteProductFormField">
        UPDATE sm_product_form_field
        SET enabled_flag=1,
            update_by=#{modifyBy},
            update_time=CURRENT_TIMESTAMP()
        WHERE productId = #{id}
    </update>

    <update id="deleteDutyFactor">
        UPDATE sm_product_duty_factor
        SET status= -1,
            update_time=CURRENT_TIMESTAMP()
        WHERE product_id = #{productId}
    </update>

    <update id="updateProductOnlinePlatform">
        UPDATE sm_product
        SET onlineChannel = #{onlineChannel}
        WHERE id = #{productId}
    </update>

    <update id="updateProductPriceFactor">
        UPDATE sm_product
        SET underWritingAgeSelect=#{underWritingAgeSelect},
            validPeriodSelect=#{validPeriodSelect},
            sexSelect=#{sexSelect},
            socialSecuritySelect=#{socialSecuritySelect},
            occuplCategorySelect=#{occuplCategorySelect},
            vehicleSeatNumberSelect=#{vehicleSeatNumberSelect},
            protonHeavyIonMedicineSelect=#{protonHeavyIonMedicineSelect},
            specifiedDiseaseSpecificCareSelect=#{specifiedDiseaseSpecificCareSelect},
            japanMedicalTreatmentSelect=#{japanMedicalTreatmentSelect},
            update_by=#{modifyBy},
            update_time=CURRENT_TIMESTAMP()
        WHERE id = #{id}

    </update>

    <insert id="insertPlanFactorPrice" useGeneratedKeys="true">
        INSERT INTO sm_plan_factor_price
        (productId, planId, underWritingAgeOptional,validPeriodOptional, sexOptional, socialSecurityOptional,
        vehicleSeatNumberOptional, protonHeavyIonMedicineOptional, specifiedDiseaseSpecificCareOptional,
        japanMedicalTreatmentOptional, price, available, update_by, update_time, enabled_flag)
        VALUES
        <foreach collection="prices" item="item" index="index" separator=",">
            (#{item.productId}, #{item.planId}, #{item.underWritingAgeOptional}, #{item.validPeriodOptional},
            #{item.sexOptional}, #{item.socialSecurityOptional}, #{item.vehicleSeatNumberOptional},
            #{item.protonHeavyIonMedicineOptional},#{item.specifiedDiseaseSpecificCareOptional},#{item.japanMedicalTreatmentOptional},
            #{item.price}, #{item.available}, #{item.modifyBy}, CURRENT_TIMESTAMP(), 0)
        </foreach>
    </insert>
    <insert id="insertPlanFactorPriceDisable" useGeneratedKeys="true"
            parameterType="java.util.List" keyProperty="id">
        INSERT INTO sm_plan_factor_price
        (productId, planId, underWritingAgeOptional,validPeriodOptional, sexOptional, socialSecurityOptional,
        vehicleSeatNumberOptional, protonHeavyIonMedicineOptional, specifiedDiseaseSpecificCareOptional,
        japanMedicalTreatmentOptional, price, available, update_by, update_time, enabled_flag)
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.productId}, #{item.planId}, #{item.underWritingAgeOptional}, #{item.validPeriodOptional},
            #{item.sexOptional}, #{item.socialSecurityOptional}, #{item.vehicleSeatNumberOptional},
            #{item.protonHeavyIonMedicineOptional},#{item.specifiedDiseaseSpecificCareOptional},#{item.japanMedicalTreatmentOptional},
            #{item.price}, #{item.available}, #{item.modifyBy}, CURRENT_TIMESTAMP(), 1)
        </foreach>
    </insert>

    <update id="deletePlanFactorPrice">
        UPDATE sm_plan_factor_price
        SET enabled_flag=1,
            update_by=#{modifyBy},
            update_time=CURRENT_TIMESTAMP()
        WHERE productId = #{id}
    </update>

    <insert id="insertProductFactorOptionals" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO sm_product_factor_optionals
        (productId,fieldCode,optionalName,optionalValue,value1,value2,unit1,unit2, update_by, update_time, enabled_flag)
        VALUES
        <foreach collection="optionals" item="item" index="index" separator=",">
            (#{item.productId}, #{item.fieldCode}, #{item.optionalName}, #{item.optionalValue}, #{item.value1},
            #{item.value2},#{item.unit1}, #{item.unit2}, #{item.modifyBy}, CURRENT_TIMESTAMP(), 1)
        </foreach>
    </insert>

    <insert id="insertProductFactorOptionalsDisable"
            parameterType="java.util.List"
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO sm_product_factor_optionals
        (productId,fieldCode,optionalName,optionalValue,value1,value2,unit1,unit2, update_by, update_time, enabled_flag)
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.productId}, #{item.fieldCode}, #{item.optionalName}, #{item.optionalValue}, #{item.value1},
            #{item.value2},#{item.unit1}, #{item.unit2}, #{item.modifyBy}, CURRENT_TIMESTAMP(), 1)
        </foreach>
    </insert>

    <insert id="insertProductFactorOptionalsHis">
        INSERT INTO sm_product_factor_optionals
        (productId, fieldCode, optionalName, optionalValue, value1, value2, unit1, unit2, update_by, update_time,
         enabled_flag)
        select productId,
               fieldCode,
               optionalName,
               optionalValue,
               value1,
               value2,
               unit1,
               unit2,
               update_by,
               update_time,
               enabled_flag
        from sm_product_factor_optionals_history
        where productId = #{productId}
          and version = #{version}
    </insert>

    <update id="deleteProductFactorOptionals">
        UPDATE sm_product_factor_optionals
        SET enabled_flag=1,
            update_by=#{modifyBy},
            update_time=CURRENT_TIMESTAMP()
        WHERE productId = #{id}
    </update>

    <select id="listFieldOptionals" resultType="com.cfpamf.ms.insur.admin.pojo.dto.SmFactorOptionalDTO">
        SELECT *
        FROM sm_product_factor_optionals
        WHERE productId = #{id}
          AND enabled_flag = 0
    </select>

    <select id="getPlanSpecPrice" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmPlanFactorPriceDT">
        SELECT * FROM sm_plan_factor_price t1 WHERE t1.enabled_flag=0
        <if test='productId != null'>
            AND t1.productId = #{productId}
        </if>
        <if test='planId != null'>
            AND t1.planId = #{planId}
        </if>
        <if test='underWritingAgeId != null'>
            AND t1.underWritingAgeOptional = #{underWritingAgeId}
        </if>
        <if test='validPeriodId != null'>
            AND t1.validPeriodOptional = #{validPeriodId}
        </if>
        <if test='sexId != null'>
            AND t1.sexOptional = #{sexId}
        </if>
        <if test='socialSecurityId != null'>
            AND t1.socialSecurityOptional = #{socialSecurityId}
        </if>
        <if test='occuplCategoryId != null'>
            AND t1.occuplCategoryOptional = #{occuplCategoryId}
        </if>
        <if test='vehicleSeatNumberId != null'>
            AND t1.vehicleSeatNumberOptional = #{vehicleSeatNumberId}
        </if>
        <if test='available!=null'>
            AND t1.available = #{available}
        </if>
    </select>

    <select id="listProductFormFields" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmProductFormFieldVO">
        SELECT *
        FROM sm_product_form_field
        WHERE productId = #{id}
          AND enabled_flag = 0
        ORDER BY id ASC
    </select>

    <select id="listProductClausesByProductId" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmProductClauseVO">
        SELECT *, id AS productId
        FROM sm_product_clause
        WHERE productId = #{id}
          AND enabled_flag = 0
        ORDER BY id ASC
    </select>

    <select id="getProductClauseById" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmProductClauseVO">
        SELECT *
        FROM sm_product_clause
        WHERE id = #{id}
    </select>

    <select id="listPlanFactorPrices" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmPlanFactorPriceDT">
        SELECT t1.*,
        t2.name AS socialSecurityName,
        t3.name AS sexName,
        t4.optionalName AS validPeriodName,
        t5.optionalName AS underWritingAgeName,
        t7.optionalName AS vehicleSeatNumberName,
        t8.planName AS planName
        FROM sm_plan_factor_price t1
        LEFT JOIN dictionary t2 ON t1.sexOptional = t2.id AND t2.enabled_flag = 0
        LEFT JOIN dictionary t3 ON t1.socialSecurityOptional = t3.id AND t3.enabled_flag = 0
        LEFT JOIN sm_product_factor_optionals t4 ON t1.validPeriodOptional = t4.id AND t4.enabled_flag = 0
        LEFT JOIN sm_product_factor_optionals t5 ON t1.underWritingAgeOptional = t5.id AND t5.enabled_flag = 0
        LEFT JOIN sm_product_factor_optionals t7
        ON t1.vehicleSeatNumberOptional = t7.id AND t7.enabled_flag = 0
        LEFT JOIN sm_plan t8 ON t1.planId = t8.id AND t8.enabled_flag = 0
        WHERE
        t1.enabled_flag = 0
        <if test="id!= null">
            AND t8.productId = #{id}
        </if>
        <if test="planIds!=null and planIds.size()>0">
            and t1.planId in
            <foreach item="item" index="index" open="(" separator="," close=")" collection="planIds">
                #{item}
            </foreach>
        </if>

    </select>

    <update id="updateProductMinAmount">
        UPDATE sm_product
        SET minAmount=#{minAmount},
            update_by=#{modifyBy},
            update_time=CURRENT_TIMESTAMP()
        WHERE id = #{id}
    </update>

    <update id="updateProductSaleQty">
        UPDATE sm_product
        SET saleQty=saleQty + #{qty},
            update_by=#{modifyBy},
            update_time=CURRENT_TIMESTAMP()
        WHERE productId = #{id}
    </update>

    <update id="updateProductPlan">
        UPDATE sm_plan
        SET planName=#{planName},
            fhProductId=#{fhProductId},
            update_time=CURRENT_TIMESTAMP()
        WHERE id = #{id}
    </update>

    <insert id="insertProductFormLimit" useGeneratedKeys="true">
        INSERT INTO sm_product_form_limit
        (productId,fieldCode,limitCode,update_by,update_time, enabled_flag)
        VALUES
        <foreach collection="occupation" item="item" index="index" separator=",">
            (#{item.productId}, #{item.fieldCode}, #{item.limitCode}, #{item.modifyBy}, CURRENT_TIMESTAMP(), 0)
        </foreach>
    </insert>

    <select id="listProductFormLimits" resultType="com.cfpamf.ms.insur.admin.pojo.vo.ProductFormLimitVO">
        SELECT *
        FROM sm_product_form_limit
        WHERE productId = #{id}
        AND   enabled_flag = 0
    </select>

    <update id="deleteProductFormLimit">
        UPDATE sm_product_form_limit
        SET enabled_flag=1,
            update_by=#{modifyBy},
            update_time=CURRENT_TIMESTAMP()
        WHERE productId = #{id}
    </update>

    <select id="countProductByCompanyId" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM sm_product
        WHERE companyId = #{companyId}
    </select>

    <select id="listProductSalesOrgsByProductId" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmProductSalesOrgVO">
        SELECT distinct product_id productId, hr_org_id hrOrgId, org_name orgName, org_path orgPath
        FROM sm_plan_sales_org
        WHERE product_id = #{productId}
          AND enabled_flag = 0
    </select>

    <select id="listProductSalesOrgsByOrgPath" resultType="java.lang.Integer">
        SELECT DISTINCT t1.product_id productId
        FROM sm_plan_sales_org t1
        LEFT JOIN sm_product t2 ON t1.product_id = t2.id
        WHERE
        t1.enabled_flag = 0
        AND t2.state=1
        <if test='orgName != null'>
            AND (t1.org_Name = #{orgName} OR t1.org_Path IS NULL)
        </if>
        <if test='orgName == null'>
            AND t1.org_Path IS NULL
        </if>
    </select>

    <update id="deleteProductSalesOrgs">
        UPDATE sm_product_sales_org
        SET enabled_flag = 1
        WHERE productId = #{productId}
    </update>

    <insert id="insertProductSalesOrgs" useGeneratedKeys="true">
        INSERT INTO sm_product_sales_org
        (productId, hrOrgId, orgName, orgPath, create_by, update_by)
        VALUES
        <foreach collection="psos" item="item" index="index" separator=",">
            (#{item.productId}, #{item.hrOrgId}, #{item.orgName}, #{item.orgPath}, #{item.createBy}, #{item.updateBy} )
        </foreach>
    </insert>

    <select id="listOnlineProductSalesOrgs" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmProductSalesOrgVO">
        SELECT t1.id                              AS productId,
               t1.productName,
               GROUP_CONCAT(DISTINCT t2.org_path) AS orgPath,
               GROUP_CONCAT(DISTINCT t2.org_name) AS orgName
        FROM sm_product t1
                 LEFT JOIN sm_plan_sales_org t2
                           ON t2.product_id = t1.id
                               AND t2.enabled_flag = 0
        WHERE t1.state = 1
        GROUP BY t1.id
    </select>

    <select id="listProductRenews" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmProductRenewVO">
        SELECT
        t1.id AS productId,
        t1.productName,
        t1.state AS productState,
        t3.id AS renewProductId,
        t3.productName AS renewProductName,
        t3.state AS renewProductState,
        GROUP_CONCAT(DISTINCT t4.org_name) AS productSalesOrgs,
        GROUP_CONCAT(DISTINCT t5.org_name) AS renewProductSalesOrgs,
        t2.update_by AS updateBy,
        t2.update_time AS updateTime
        FROM
        sm_product t1
        LEFT JOIN sm_product_renew t2
        ON t2.productId = t1.id
        AND t2.enabled_flag = 0
        LEFT JOIN sm_product t3
        ON t3.id = t2.renewProductId
        LEFT JOIN sm_plan_sales_org t4
        ON t4.product_id = t1.id
        AND t4.enabled_flag = 0
        LEFT JOIN sm_plan_sales_org t5
        ON t5.product_id = t2.renewProductId
        AND t5.enabled_flag = 0
        WHERE
        1 = 1
        <if test='productName != null'>
            AND t1.productName LIKE CONCAT(#{productName}, '%')
        </if>
        <if test='productId != null'>
            AND t1.id = #{productId}
        </if>
        GROUP BY t1.id,
        t3.id
    </select>

    <select id="listWxRenewProductsProductId" resultType="com.cfpamf.ms.insur.weixin.pojo.vo.WxProductRenewVO">
        SELECT t2.id          AS renewProductId,
               t2.productName AS renewProductName,
               t3.state       AS oldProductState,
               t2.h5Url,
               t2.apiType,
               t2.channel
        FROM sm_product_renew t1
                 LEFT JOIN sm_product t2
                           ON t2.id = t1.renewProductId
                 LEFT JOIN sm_product t3
                           ON t3.id = t1.productId
        WHERE t1.productId = #{productId}
          AND t1.enabled_flag = 0
    </select>

    <select id="listRenewProductsByProductId" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmProductRenewVO">
        SELECT t2.id                              AS renewProductId,
               t2.productName                     AS renewProductName,
               GROUP_CONCAT(DISTINCT t3.org_name) AS renewProductSalesOrgs,
               t2.h5Url,
               t2.apiType,
               t2.channel
        FROM sm_product_renew t1
                 LEFT JOIN sm_product t2
                           ON t2.id = t1.renewProductId
                 LEFT JOIN sm_plan_sales_org t3
                           ON t3.product_id = t2.id
                               AND t3.enabled_flag = 0
        WHERE t1.productId = #{productId}
          AND t1.enabled_flag = 0
        GROUP BY t2.id
    </select>

    <select id="selectCicPilotOrgs" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmProductSalesOrgVO">
        SELECT distinct t2.org_path orgPath,
                        t2.org_name orgName
        FROM sm_product t1
                 LEFT JOIN sm_plan_sales_org t2
                           ON t2.product_id = t1.id AND t2.enabled_flag = 0
        WHERE t1.channel = 'cic'
          AND t1.state = 1
    </select>

    <insert id="insertProductRenews" parameterType="com.cfpamf.ms.insur.admin.pojo.dto.SmProductRenewDTO">
        INSERT INTO sm_product_renew (productId, renewProductId, create_by, update_by)
        VALUES
        <foreach collection="renews" item="item" index="index" separator=",">
            (#{item.productId}, #{item.renewProductId}, #{item.createBy}, #{item.updateBy} )
        </foreach>
    </insert>

    <update id="deleteProductRenews">
        UPDATE sm_product_renew
        SET enabled_flag = 1
        WHERE productId = #{productId}
    </update>

    <select id="listProductCoverages" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmProductCoverageVO">
        SELECT *
        FROM sm_product_coverage
        WHERE productId = #{productId}
          and enabled_flag = 0;
    </select>

    <insert id="insertProductCoverage" useGeneratedKeys="true" keyProperty="spcId">
        INSERT INTO sm_product_coverage (productId,
                                         cvgItemName,
                                         cvgNameDetail,
                                         cvgType,
                                         cvgRespType)
        VALUES (#{productId},
                #{cvgItemName},
                #{cvgNameDetail},
                #{cvgType},
                #{cvgRespType});
    </insert>
    <insert id="insertProductCoverageDisable" useGeneratedKeys="true" keyProperty="spcId">
        INSERT INTO sm_product_coverage (productId,
                                         cvgItemName,
                                         cvgNameDetail,
                                         cvgType,
                                         plan_id,
                                         plan_name,
                                         risk_code,
                                         mandatory,
                                         cvgRespType,
                                         cvgCode, enabled_flag)
        VALUES (#{productId},
                #{cvgItemName},
                #{cvgNameDetail},
                #{cvgType},
                #{planId},
                #{planName},
                #{riskCode},
                #{mandatory},
                #{cvgRespType}, #{cvgCode}, 1);
    </insert>

    <update id="updateProductCoverage">
        UPDATE sm_product_coverage
        SET cvgItemName   = #{cvgItemName},
            cvgNameDetail = #{cvgNameDetail},
            cvgType       = #{cvgType},
            cvgRespType   = #{cvgRespType}
        WHERE spcId = #{spcId}
    </update>

    <update id="deleteProductCoverage">
        UPDATE sm_product_coverage
        SET enabled_flag = 1
        WHERE productId = #{productId}
          AND spcId = #{spcId}
    </update>


    <select id="listProductCoverageAmounts"
            resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmProductCoverageAmountVO">
        SELECT t2.cvgItemName,
               t2.cvgType,
               t2.cvgRespType,
               t1.*,
               t3.planName,
               t2.cvgCode
        FROM sm_product_coverage_amount t1
                 LEFT JOIN sm_product_coverage t2
                           ON t1.spcId = t2.spcId AND t2.enabled_flag = 0
                 LEFT JOIN sm_plan t3
                           ON t1.planId = t3.id AND t3.enabled_flag = 0
        WHERE t1.productId = #{productId}
          and t1.enabled_flag = 0;
    </select>
    <select id="listPlanCoverageAmounts"
            resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmProductCoverageAmountVO">
        SELECT t2.cvgItemName,
               t2.cvgType,
               t2.cvgRespType,
               t2.cvgCode,
               t1.*
        FROM sm_product_coverage_amount t1
                 LEFT JOIN sm_product_coverage t2
                           ON t1.spcId = t2.spcId AND t2.enabled_flag = 0
                 LEFT JOIN sm_plan t3
                           ON t1.planId = t3.id AND t3.enabled_flag = 0
        WHERE t1.planId = #{planId}
          and t1.enabled_flag = 0
    </select>

    <select id="listProductCoverageAmountBySpcaIds"
            resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmProductCoverageAmountVO">
        SELECT
        t2.cvgItemName,
        t1.*
        FROM
        sm_product_coverage_amount t1
        LEFT JOIN sm_product_coverage t2
        ON t1.spcId = t2.spcId AND t2.enabled_flag = 0
        WHERE t1.enabled_flag = 0
        AND t1.spcaId IN
        <foreach item="item" index="index" open="(" separator="," close=")" collection="spcaIds">
            #{item}
        </foreach>
    </select>

    <insert id="insertProductCoverageAmount" useGeneratedKeys="true" keyProperty="spcId">
        INSERT INTO sm_product_coverage_amount (productId,
                                                planId,
                                                spcId,
                                                cvgAmount,
                                                cvgNotice)
        VALUES (#{productId},
                #{planId},
                #{spcId},
                #{cvgAmount},
                #{cvgNotice});
    </insert>

    <update id="updateProductCoverageAmount">
        UPDATE sm_product_coverage_amount
        SET cvgAmount = #{cvgAmount},
            cvgNotice = #{cvgNotice}
        WHERE spcaId = #{spcaId}
    </update>

    <update id="deleteProductCoverageAmount">
        UPDATE sm_product_coverage_amount
        SET enabled_flag = 1
        WHERE spcaId = #{spcaId}
    </update>

    <select id="listProductCoveragePremiums"
            resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmProductCoveragePremiumDTO">
        SELECT t1.*,
               t2.cvgAmount,
               t2.cvgNotice,
               t3.cvgItemName
        FROM sm_product_coverage_premium t1
                 LEFT JOIN sm_product_coverage_amount t2 ON t2.spcaId = t1.spcaId
                 LEFT JOIN sm_product_coverage t3 ON t3.spcId = t2.spcId
        WHERE t1.productId = #{productId}
          AND t1.enabled_flag = 0;
    </select>

    <select id="queryPremiumByPlan"
            resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmProductCoveragePremiumDTO">
        SELECT t1.*,
               t2.cvgAmount,
               t2.cvgNotice,
               t3.cvgItemName
        FROM sm_product_coverage_premium t1
                 LEFT JOIN sm_product_coverage_amount t2 ON t2.spcaId = t1.spcaId
                 LEFT JOIN sm_product_coverage t3 ON t3.spcId = t2.spcId
        WHERE t1.productId = #{productId}
          AND t1.planId = #{planId}
          AND t1.enabled_flag = 0;
    </select>

    <update id="updateProductCoveragePremium">
        UPDATE sm_product_coverage_premium
        SET premium = #{premium}
        WHERE spcpId = #{spcpId}
    </update>

    <insert id="insertProductCoveragePremium">
        INSERT INTO sm_product_coverage_premium (
        productId,
        occupationGroup,
        spcId,
        spcaId,
        premium
        )
        VALUES
        <foreach collection="premiums" item="item" index="index" separator=",">
            (#{item.productId}, #{item.occupationGroup}, #{item.spcId}, #{item.spcaId}, #{item.premium})
        </foreach>
    </insert>
    <insert id="insertProductCoveragePremiumDisable" parameterType="java.util.List"
            useGeneratedKeys="true" keyProperty="spcpId">
        INSERT INTO sm_product_coverage_premium (
        productId,
        occupationGroup,
        spcId,
        spcaId,
        premium,enabled_flag
        )
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.productId}, #{item.occupationGroup}, #{item.spcId}, #{item.spcaId}, #{item.premium},1)
        </foreach>
    </insert>

    <update id="deleteProductCoveragePremium">
        UPDATE sm_product_coverage_premium
        SET enabled_flag = 1
        WHERE productId = #{productId};
    </update>

    <select id="listProductCoverageDiscounts"
            resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmProductCoverageDiscountVO">
        SELECT *
        FROM sm_product_coverage_discount
        WHERE productId = #{productId}
          AND enabled_flag = 0
    </select>

    <insert id="insertProductCoverageDiscounts">
        INSERT INTO sm_product_coverage_discount (productId, perQty, min_per_qty, discount)
        VALUES (#{productId}, #{perQty}, #{minPerQty}, #{discount})
    </insert>
    <insert id="insertProductCoverageDiscountsDisable" useGeneratedKeys="true" keyProperty="spcdId">
        INSERT INTO sm_product_coverage_discount (productId, perQty, min_per_qty, discount, enabled_flag)
        VALUES (#{productId}, #{perQty}, #{minPerQty}, #{discount}, 1)
    </insert>
    <insert id="insertTimeFactorDisable" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO sm_product_time_factor (product_id, factor_value, factor_name, time_unit, flow, enable_flag)
        VALUES (#{productId}, #{factorValue}, #{factorName}, #{timeUnit}, #{flow}, 1)
    </insert>
    <insert id="insertDutyFactor" parameterType="com.cfpamf.ms.insur.admin.pojo.dto.product.DutyFactorDTO"
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO sm_product_duty_factor (product_id,
                                            spc_id,
                                            duty_code,
                                            duty_factor_code,
                                            factor_value,
                                            factor_name,
                                            option_name,
                                            flow,
                                            is_default)
        VALUES (#{dto.productId},
                #{dto.spcId},
                #{dto.dutyCode},
                #{dto.dutyFactorCode},
                #{dto.factorValue},
                #{dto.factorName},
                #{dto.optionName},
                #{dto.flow},
                #{dto.isDefault})
    </insert>


    <update id="updateProductCoverageDiscount">
        UPDATE sm_product_coverage_discount
        SET perQty   = #{perQty},
            discount = #{discount}
        WHERE spcdId = #{spcdId};
    </update>

    <update id="deleteProductCoverageDiscount">
        UPDATE sm_product_coverage_discount
        SET enabled_flag = 1
        WHERE spcdId = #{spcdId};
    </update>

    <select id="listWxGroupProducts" resultType="com.cfpamf.ms.insur.weixin.pojo.vo.WxGlProductListVO">
        SELECT
        id AS productId,
        productName,
        thumbnailImageUrl,
        glUwaFrom,
        glUwaTo,
        glOcpnGroup,
        effectWaitingDayMin,
        effectWaitingDayMax,
        minAmount
        FROM
        sm_product
        WHERE productAttrCode = 'group' AND state = 1
        <if test='salesProductIds != null'>
            AND id IN
            <foreach item="item" index="index" open="(" separator="," close=")" collection="salesProductIds">
                #{item}
            </foreach>
        </if>
        ORDER BY create_time ASC
    </select>

    <select id="getGlProductById"
            resultType="com.cfpamf.ms.insur.weixin.pojo.vo.WxGlProductDetailVO">
        SELECT *, id AS productId
        FROM sm_product
        WHERE id = #{productId}
    </select>

    <insert id="insertGlProductQuotePlan" useGeneratedKeys="true" keyProperty="spqpId">
        INSERT INTO sm_product_quote_plan (quoteNo,
                                           quoteType,
                                           customerName,
                                           customerMobile,
                                           productId,
                                           plan_id,
                                           quoteDetail,
                                           totalPerQty,
                                           totalAmount,
                                           discount,
                                           actualAmount,
                                           customerAdminId,
                                           agentId,
                                           customerAdminType,
                                           customerAdminName,
                                           customerAdminMobile,
                                           old_order_id,
                                           customerAdminImgUrl)
        VALUES (#{quoteNo},
                #{quoteType},
                #{customerName},
                #{customerMobile},
                #{productId},
                #{planId},
                #{quoteDetail},
                #{totalPerQty},
                #{totalAmount},
                #{discount},
                #{actualAmount},
                #{customerAdminId},
                #{agentId},
                #{customerAdminType},
                #{customerAdminName},
                #{customerAdminMobile},
                #{oldOrderId},
                #{customerAdminImgUrl});
    </insert>


    <select id="listProductQuoteLimit" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmProductQuoteLimitItemVO">
        SELECT *
        FROM sm_product_quote_limit
        WHERE productId = #{productId}
          AND enabled_flag = 0
    </select>

    <insert id="insertProductQuoteLimit" useGeneratedKeys="true" keyProperty="spqlId">
        INSERT INTO sm_product_quote_limit (limitType, productId, occupationGroup, minPerQty, minPerRatio, sourceSpcId,
                                            relyOnSpcId, operation, targetSpcId, amountRatio)
        VALUES (#{limitType}, #{productId}, #{occupationGroup}, #{minPerQty}, #{minPerRatio}, #{sourceSpcId},
                #{relyOnSpcId}, #{operation}, #{targetSpcId}, #{amountRatio})
    </insert>

    <insert id="insertProductQuoteLimitDisable" useGeneratedKeys="true" keyProperty="spqlId">
        INSERT INTO sm_product_quote_limit (limitType, productId, occupationGroup,
                                            minPerQty, minPerRatio, sourceSpcId,
                                            limit_amount,
                                            limit_amount_notice,
                                            relyOnSpcId, operation, targetSpcId,
                                            amountRatio, support_time, unit, enabled_flag)
        VALUES (#{limitType}, #{productId}, #{occupationGroup}, #{minPerQty}, #{minPerRatio}, #{sourceSpcId},
                #{limitAmount}, #{limitAmountNotice},
                #{relyOnSpcId}, #{operation}, #{targetSpcId}, #{amountRatio}, #{supportTime}, #{unit}, 1)
    </insert>

    <insert id="insertProductCoverageAmountDisable" useGeneratedKeys="true" keyProperty="spcaId">
        INSERT INTO sm_product_coverage_amount (productId,
                                                planId,
                                                spcId,
                                                cvgAmount,
                                                cvgNotice, enabled_flag)
        VALUES (#{productId},
                #{planId},
                #{spcId},
                #{cvgAmount},
                #{cvgNotice}, 1);
    </insert>

    <update id="updateGlProductQuotePlan">
        UPDATE sm_product_quote_plan
        SET quoteType    = #{quoteType},
            customerName = #{customerName}
        WHERE spqpId = #{spqpId}
    </update>

    <update id="updateProductQuoteLimit">
        UPDATE sm_product_quote_limit
        SET occupationGroup = #{occupationGroup},
            minPerQty       = #{minPerQty},
            minPerRatio     = #{minPerRatio},
            sourceSpcId     = #{sourceSpcId},
            relyOnSpcId     = #{relyOnSpcId},
            operation       = #{operation},
            targetSpcId     = #{targetSpcId},
            amountRatio     = #{amountRatio}
        WHERE spqlId = #{spqlId}
    </update>

    <update id="deleteProductQuoteLimit">
        UPDATE sm_product_quote_limit
        SET enabled_flag = 1
        WHERE spqlId = #{spqlId}
    </update>

    <update id="deleteAllProductQuoteLimit">
        UPDATE sm_product_quote_limit
        SET enabled_flag = 1
        WHERE productId = #{productId}
    </update>
    <update id="updateMonthSale">
        update sm_product sp inner join (SELECT productId, count(1) AS saleCount
                                         FROM sm_order
                                         WHERE
        <![CDATA[DATE_SUB(CURDATE(), INTERVAL 1 MONTH) <= create_time ]]>
        AND payStatus = 2
                                         GROUP BY productId) t
            on sp.id = t.productId
        set sp.monthSaleCount = t.saleCount
        where t.productId is not null
    </update>
    <update id="updateProductCustNotify">
        update sm_product
        set custNotify = #{custNotify}
        where id = #{id}

    </update>

    <select id="listGlProductQuotes" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmGlProductQuoteListVO">
        SELECT
        t1.*,
        t2.userId,
        t2.userName,
        t2.userMobile,
        t2.regionName,
        t2.organizationName,
        t3.agentMobile,
        t3.agentName,
        t4.productName
        FROM sm_product_quote_plan t1
        LEFT JOIN auth_user t2 ON t1.customerAdminId = t2.userId
        LEFT JOIN sm_agent t3 ON t1.agentId = t3.agentId
        LEFT JOIN sm_product t4 ON t1.productId = t4.id
        WHERE 1 = 1
        <if test="startDate != null">
            <![CDATA[ AND t1.create_time >= #{startDate} ]]>
        </if>
        <if test="endDate != null">
            <![CDATA[ AND t1.create_time <= #{endDate} ]]>
        </if>
        <if test="quoteNo != null">
            AND t1.quoteNo LIKE CONCAT (#{quoteNo},'%')
        </if>
        <if test="customerName != null">
            AND t1.customerName LIKE CONCAT (#{customerName},'%')
        </if>
        <if test="regionName != null">
            AND t2.regionName = #{regionName}
        </if>
        <if test="organizationName != null">
            AND t2.organizationName = #{organizationName}
        </if>
        <if test="userName != null">
            AND (t2.userId LIKE CONCAT(#{userName}, '%') OR t2.userName LIKE CONCAT(#{userName}, '%') OR t2.userMobile
            LIKE CONCAT(#{userName}, '%'))
        </if>
        <if test="agentName != null">
            AND (t3.agentName LIKE CONCAT(#{agentName}, '%') OR t3.agentMobile LIKE CONCAT(#{agentName}, '%'))
        </if>
        ORDER BY t1.create_time DESC
    </select>
    <select id="listProductMinAmtByOrgName" resultType="com.cfpamf.ms.insur.admin.pojo.dto.ProductMinAmtDTO">
        SELECT t1.product_id productId,spfp.planId, min(price) amount
        FROM sm_plan_sales_org t1
        LEFT JOIN sm_product t2 ON t1.product_id = t2.id
        left join sm_plan_factor_price spfp on t1.plan_id = spfp.planId and spfp.enabled_flag = 0
        WHERE t1.enabled_flag = 0 and spfp.available = 1
        and t2.id IN
        <foreach item="item" index="index" open="(" separator="," close=")" collection="salesProductIds">
            #{item}
        </foreach>
        and (t1.org_name = #{orgName} or t1.org_path is null)
        group by productId,planId
    </select>
    <select id="listPlanByFhProductIds" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmPlanVO">
        SELECT t1.*, t2.productName,t2.companyId,t2.productAttrCode,t2.channel
        FROM sm_plan t1
        LEFT JOIN sm_product t2 ON t1.productId = t2.id
        WHERE t1.enabled_flag = '0'
        AND t2.enabled_flag = '0'
        <if test="channel!=null">
            and t2.channel = #{channel}
        </if>
        AND t1.fhProductId in
        <foreach item="item" index="index" open="(" separator="," close=")" collection="fhProductIds">
            #{item}
        </foreach>
    </select>

    <select id="getProductExtendByType" resultType="com.cfpamf.ms.insur.admin.pojo.dto.product.SmProductExtend">
        SELECT *
        FROM sm_product_extend t
        where product_id=#{productId}
        <if test="type!=null">
            and type=#{type}
        </if>
    </select>
    <delete id="deletePremiumFile">
        DELETE
        FROM sm_product_extend
        where product_id = #{productId}
          and type = 1
    </delete>

    <insert id="insertProductExtend">
        INSERT INTO sm_product_extend
            (product_id, type, attr_name, attr_value, operator, create_time)
        VALUES (#{dto.productId}, #{dto.type}, #{dto.attrName}, #{dto.attrValue},
                #{dto.operator}, CURRENT_TIMESTAMP())
    </insert>

    <select id="queryActiveProduct" resultType="java.lang.Integer">
        SELECT id
        FROM sm_product
        WHERE enabled_flag = 0
    </select>
    <insert id="insertPremiumFlow" parameterType="com.cfpamf.ms.insur.admin.pojo.dto.product.PremiumFlow"
            useGeneratedKeys="true">
        INSERT INTO sm_product_premium_flow (
        product_id,
        plan_id,
        type,
        factor_value,
        factor_name,
        flow,
        version,
        enabled_flag,
        operator)
        VALUES
        <foreach collection="data" item="item" index="index" separator=",">
            ( #{item.productId},
            #{item.planId},
            #{item.type},
            #{item.factorValue},
            #{item.factorName},
            #{item.flow},
            #{version},
            #{item.enabledFlag},
            #{item.operator}
            )
        </foreach>
    </insert>
    <select id="queryPremiumFlow" resultType="com.cfpamf.ms.insur.admin.pojo.dto.product.PremiumFlow">
        select f.*,
               p.planName
        from sm_product_premium_flow f
                 left join sm_plan_history p on f.plan_id = p.planId and p.version = #{version}
        where f.product_id = #{productId}
          and f.type = #{type}
          and f.enabled_flag = 0
    </select>
    <select id="selectCountGroupQuote" resultType="java.lang.Integer">
        select count(1)
        from sm_product_quote_plan
        where productId = #{productId}
    </select>
    <select id="selectCountPlanBook" resultType="java.lang.Integer">
        select count(1)
        from operation_prospectus
        where product_id = #{productId}
    </select>
    <select id="queryByProductIdList" resultType="com.cfpamf.ms.insur.admin.pojo.po.product.SmProduct">
        select * from  sm_product where id in
                                  <foreach collection="list" item="item" open="(" close=")" separator=",">
                                      #{item}
                                  </foreach>
    </select>
    <select id="queryProductPlanRiskInfo" resultType="com.cfpamf.ms.insur.admin.pojo.dto.SmProductPlanRiskInfoDTO">
        select
            t1.id productId,t1.productName ,t1.companyId
            ,t2.`id` planId,t2.planName
            ,t4.id riskId,t4.`risk_name`,t4.risk_type
        from sm_product t1
        left join sm_plan t2 on t1.id = t2.productId and t2.`enabled_flag` =0
        left join `sm_plan_risk` t3 on t2.id = t3.`plan_id` and t3.`enabled_flag` =0
        left join sys_risk t4 on t4.risk_key = t3.`risk_key` and t4.`enabled_flag` = 0
        where
            t1.`enabled_flag` = 0 and (t4.risk_type = 1 or t4.risk_type is null) and t1.`long_insurance` = 1
        group by t1.id,t2.id
    </select>

    <update id="updateProductClaimInfo">
        UPDATE sm_product
        SET friendly_reminder = #{friendlyReminder},
            following_steps = #{followingSteps}
        WHERE id = #{id}
    </update>
</mapper>
