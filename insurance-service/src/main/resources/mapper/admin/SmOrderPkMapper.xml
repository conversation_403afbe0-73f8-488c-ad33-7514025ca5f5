<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.order.SmOrderPkMapper">

    <select id="selectPkMq" resultType="com.cfpamf.ms.insur.admin.pojo.vo.order.RealIndicatorsMqSafesVO">
        select t1.recommendId      user_code,
               t3.userName         user_name,
               t1.recommendOrgCode branch_code,
               t2.orgName          branch_name,
               t1.paymentTime      time,
               t1.fhOrderId        idempotent,
               sum(conversion_amount) indicators_value
        from sm_order t1
                 left join organization t2 on t1.recommendOrgCode = t2.orgCode
                 left join auth_user t3 on t1.recommendId = t3.userId and t3.enabled_flag = 0
                 left join sm_commission_detail t4 on t4.order_id = t1.fhOrderId
        where fhOrderId = #{fhOrderId}
         and t1.paymentTime > '2023-01-01'
        group by fhOrderId
    </select>
</mapper>