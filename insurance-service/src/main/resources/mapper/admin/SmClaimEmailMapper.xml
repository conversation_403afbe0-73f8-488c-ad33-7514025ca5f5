<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.claim.SmClaimEmailMapper">
    <resultMap id="BaseResultMap" type="com.cfpamf.ms.insur.admin.pojo.po.claim.SmClaimEmail">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="claim_id" jdbcType="INTEGER" property="claimId"/>
        <result column="states" jdbcType="VARCHAR" property="states"/>
        <result column="theme" jdbcType="VARCHAR" property="theme"/>
        <result column="file_form_list" jdbcType="OTHER" property="fileFormList"/>
        <result column="content" jdbcType="VARCHAR" property="content"/>
        <result column="all_enclosure" jdbcType="TINYINT" property="allEnclosure"/>
        <result column="auto_send" jdbcType="TINYINT" property="autoSend"/>
        <result column="to_email" jdbcType="VARCHAR" property="toEmail"/>
        <result column="form_email" jdbcType="VARCHAR" property="formEmail"/>
        <result column="channel" jdbcType="VARCHAR" property="channel"/>
        <result column="channel_email_code" jdbcType="VARCHAR" property="channelEmailCode"/>
        <result column="enabled_flag" jdbcType="TINYINT" property="enabledFlag"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , claim_id, states, theme, file_form_list, content, all_enclosure, auto_send, to_email,
    form_email, channel, channel_email_code, enabled_flag, create_by, update_by, create_time, 
    update_time
    </sql>

</mapper>