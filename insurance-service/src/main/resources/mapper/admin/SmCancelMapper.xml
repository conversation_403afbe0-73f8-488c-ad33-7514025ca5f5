<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.SmCancelMapper">
    <update id="updateStateByProInsId">
        update sm_cancel
        set task_key=#{taskDefinitionKey}
        where process_instance_id = #{processInstanceId}
    </update>

    <select id="listCanCancelPolicy" resultType="com.cfpamf.ms.insur.weixin.pojo.vo.cancel.WxCancelPolicyVO">
        select
        sc.id ,
        sc.id cancel_id,
        sc.process_instance_id ,
        so.fhOrderId,
        so.productId,
        soi.id insured_id,
        sp.productName,
        sp.companyId,
        sp2.planName,
        ifnull(sc.policy_no,soi.policyNo) policyNo,
        soa.personName applicant_person_name,
        soa.personGender applicant_person_gender,
        soa.idNumber applicant_id_number,
        soa.cellPhone applicant_mobile,
        soi.personName insured_person_name,
        soi.idNumber insured_id_number,
        soi.cellPhone insured_mobile,
        soi.birthday insured_birthday,
        soi.personGender insured_person_gender,
        (so.qty * so.unitPrice) totalAmount,
        so.startTime,
        so.endTime,
        so.create_time,
        sc.create_time cancel_create_time,
        soi.appStatus,
        sc.state cancel_state,
        sc.to_company_policy_state,
        sc.cancel_no,
        sc.task_key,<!-- 当前负责人信息 -->
        t6.userMobile admin_user_mobile ,
        t6.userName admin_user_name ,
        t6.userId admin_user_id ,
        so.recommendId recommendUserId,<!-- 推荐人信息 -->
        t7.userName recommendUserName,
        t7.userMobile recommendUserMobile,
        sc.company_cn,
        sc.company_cn_desc,
        sc.company_cn_creator,
        sc.company_cn_create_time,
        t6.regionName,
        t6.organizationName,
        t6.wxOpenId admin_open_id,
        so.channel
        from
        <if test="add"><!--申请退保请 只能用大表为主表 -->
            sm_order so
            LEFT join sm_product sp on so.productId = sp.id
            LEFT join sm_order_applicant soa on so.fhOrderId = soa.fhOrderId
            LEFT join sm_order_insured soi on so.fhOrderId = soi.fhOrderId
            LEFT join sm_plan sp2 on sp2.id = so.planId
            LEFT JOIN auth_user t6 ON t6.userId=so.customerAdminId and t6.enabled_flag = 0
            LEFT JOIN auth_user t7 ON t7.userId=so.recommendId and t7.enabled_flag = 0
            left join sm_cancel sc on sc.insured_id = soi.id
        </if>
        <if test="!add"><!-- 新增后的主表不一样-->
            sm_cancel sc
            LEFT JOIN sm_order so on sc.fh_order_id = so.fhOrderId
            LEFT join sm_product sp on so.productId = sp.id
            LEFT join sm_order_applicant soa on so.fhOrderId = soa.fhOrderId
            LEFT join sm_order_insured soi on so.fhOrderId = soi.fhOrderId
            LEFT join sm_plan sp2 on sp2.id = so.planId
            LEFT JOIN auth_user t6 ON t6.userId=so.customerAdminId and t6.enabled_flag = 0
            LEFT JOIN auth_user t7 ON t7.userId=so.recommendId and t7.enabled_flag = 0
        </if>
        where
        1=1
        <if test="cancelId ==null and insuredId==null and add">
            and soi.appStatus = '1'
        </if>
        <if test="cancelId != null">
            and sc.id = #{cancelId}
        </if>
        <if test="insuredId!=null">
            and soi.id = #{insuredId}
        </if>
        <if test="add and insuredId==null">
            <![CDATA[    and so.endTime > now()
           and soi.id not in ( select insured_id
                    from sm_cancel tc where
                    tc.state<>50 ) ]]>
        </if>
        <if test="!add and cancelId ==null">
            and sc.id is not null
        </if>
        <if test="taskKey!=null">
            and sc.task_key = #{taskKey}
        </if>
        <if test="doing"><!--  查询处理中 即排除提交资料环节的所有 -->
            <![CDATA[ and sc.task_key not in ('ut_apply','cancel_end') ]]>
        </if>
        <if test="productAttrCode!=null and productAttrCode!=''">
            and sp.productAttrCode = #{productAttrCode}
        </if>
        <if test='userId != null'>
            AND so.customerAdminId = #{userId}
        </if>
        <if test='agentId != null'>
            AND so.agentId = #{agentId}
        </if>
        <if test='keyword != null'>
            AND (
            <if test="keywordLikeName">
                (soa.personName LIKE CONCAT(#{keyword},'%'))
                or (soi.personName LIKE CONCAT(#{keyword},'%'))
            </if>
            <if test="keywordLikePolicyNo">
                (soi.policyNo LIKE CONCAT(#{keyword},'%'))
                or (soi.fhOrderId LIKE CONCAT(#{keyword},'%'))
            </if>
            <if test="keywordLikeIdCard">
                <if test="keywordLikePolicyNo">or</if>
                (soa.idNumber LIKE CONCAT(#{keyword},'%'))
                or (soi.idNumber LIKE CONCAT(#{keyword},'%'))
            </if>
            )
        </if>
        <if test='regionName != null'>
            AND t6.regionName=#{regionName}
        </if>
        <if test='orgName != null'>
            AND t6.organizationName=#{orgName}
        </if>
        <if test='noEnd'>
            <![CDATA[ and sc.task_key <>'cancel_end' ]]>
        </if>
        <if test="hisTaskKey">
            and exists (
            select 1 from act_hi_taskinst tp
            where tp.PROC_INST_ID_ = sc.process_instance_id
            and tp.TASK_DEF_KEY_= #{hisTaskKey}
            and  tp.end_time_ is not null
            )
        </if>
        order by so.id desc
    </select>
    <select id="getCancelOrderDetail" resultType="com.cfpamf.ms.insur.admin.pojo.vo.cancel.CancelOrderDetail">
        select so.fhOrderId,
               sp.productName,
               sp.companyId,
               sp2.planName,
               soi.policyNo,
               soa.personName          applicant_person_name,
               soa.personGender        applicant_person_gender,
               soa.idNumber            applicant_id_number,
               soa.cellPhone           applicant_mobile,
               soi.personName          insured_person_name,
               soi.idNumber            insured_id_number,
               soi.cellPhone           insured_mobile,
               soi.personGender        insured_person_gender,
               soi.birthday            insured_birthday,
               (so.qty * so.unitPrice) total_Amount,
               t6.userName             customer_admin_name,
               t7.userName             recommend_user_name,
               so.startTime,
               so.endTime,
               so.create_time,
               soi.appStatus,
               t4.companyAbbre,
               t4.companyName,
               sp.channel,
               sp.companyId,
               scbd.area_name          bank_area_name,
               scbd.bank_of_deposit,
               sc.company_cn,
               sc.company_cn_desc,
               sc.company_cn_creator,
               sc.company_cn_create_time,
               t6.regionName,
               t6.organizationName,
               t6.wxOpenId             admin_open_id,
               t4.companyIdentifier
        from sm_cancel sc
                 left join sm_order so on sc.fh_order_id = so.fhOrderId
                 LEFT join sm_product sp on so.productId = sp.id
                 LEFT join sm_order_applicant soa on so.fhOrderId = soa.fhOrderId
                 LEFT join sm_order_insured soi on so.fhOrderId = soi.fhOrderId
                 LEFT join sm_plan sp2 on sp2.id = so.planId
                 LEFT JOIN sm_company t4 ON t4.id = sp.companyId
                 LEFT JOIN auth_user t6 ON t6.userId = so.customerAdminId and t6.enabled_flag = 0
                 LEFT JOIN auth_user t7 ON t7.userId = so.recommendId and t7.enabled_flag = 0
                 left join sm_cancel_bank_deposit scbd on scbd.cancel_id = sc.id
        where sc.id = #{cancelId}
    </select>


    <select id="getCancelDetailByCancelIdAndInsuredId" resultType="com.cfpamf.ms.insur.admin.pojo.vo.cancel.CancelOrderDetail">
        select so.fhOrderId,
                sp.productName,
                sp.companyId,
                sp2.planName,
                soi.policyNo,
                soa.personName          applicant_person_name,
                soa.personGender        applicant_person_gender,
                soa.idNumber            applicant_id_number,
                soa.cellPhone           applicant_mobile,
                soi.personName          insured_person_name,
                soi.idNumber            insured_id_number,
                soi.cellPhone           insured_mobile,
                soi.personGender        insured_person_gender,
                soi.birthday            insured_birthday,
                ifnull(itm.`total_amount`, so.`unitPrice`  * so.`qty`) as total_Amount,
                t6.userName             customer_admin_name,
                t7.userName             recommend_user_name,
                so.startTime,
                so.endTime,
                so.create_time,
                soi.appStatus,
                t4.companyAbbre,
                t4.companyName,
                sp.channel,
                sp.companyId,
                scbd.area_id          bank_area_Code,
                scbd.area_name          bank_area_name,
                scbd.bank_of_deposit,
                scbd.bank_code,
                scbd.bank_name,
                scbd.account_no,
                sc.company_cn,
                sc.company_cn_desc,
                sc.company_cn_creator,
                sc.company_cn_create_time,
                t6.regionName,
                t6.organizationName,
                t6.wxOpenId             admin_open_id,
                t4.companyIdentifier
        from sm_cancel sc
            left join sm_order so on sc.fh_order_id = so.fhOrderId
            LEFT join sm_product sp on so.productId = sp.id
            LEFT join sm_order_applicant soa on so.fhOrderId = soa.fhOrderId
            LEFT join sm_order_insured soi on so.fhOrderId = soi.fhOrderId
            left join sm_order_item itm on soi.`idNumber`  = itm.`id_number` and itm.`fh_order_id` = soi.`fhOrderId`
            LEFT join sm_plan sp2 on sp2.id = so.planId
            LEFT JOIN sm_company t4 ON t4.id = sp.companyId
            LEFT JOIN auth_user t6 ON t6.userId = so.customerAdminId and t6.enabled_flag = 0
            LEFT JOIN auth_user t7 ON t7.userId = so.recommendId and t7.enabled_flag = 0
            left join sm_cancel_bank_deposit scbd on scbd.cancel_id = sc.id
        where sc.id = #{cancelId}
        and soi.id = #{insuredId}
    </select>


    <select id="getCancelOrderDetailByInsuredId"
            resultType="com.cfpamf.ms.insur.admin.pojo.vo.cancel.CancelOrderDetail">
        select so.fhOrderId,
               sp.productName,
               sp.companyId,
               sp2.planName,
               soi.policyNo,
               soa.personName          applicant_person_name,
               soa.personGender        applicant_person_gender,
               soa.idNumber            applicant_id_number,
               soa.cellPhone           applicant_mobile,
               soi.personName          insured_person_name,
               soi.idNumber            insured_id_number,
               soi.cellPhone           insured_mobile,
               soi.personGender        insured_person_gender,
               soi.birthday            insured_birthday,
               (so.qty * so.unitPrice) total_Amount,
               t6.userName             customer_user_name,
               so.startTime,
               so.endTime,
               so.create_time,
               soi.appStatus,
               t4.companyName,
               sp.channel,
               t6.regionName,
               t6.organizationName
        from sm_order so
                 LEFT join sm_product sp on so.productId = sp.id
                 LEFT join sm_order_applicant soa on so.fhOrderId = soa.fhOrderId
                 LEFT join sm_order_insured soi on so.fhOrderId = soi.fhOrderId
                 LEFT join sm_plan sp2 on sp2.id = so.planId
                 LEFT JOIN sm_company t4 ON t4.id = sp.companyId
                 LEFT JOIN auth_user t6 ON t6.userId = so.customerAdminId and t6.enabled_flag = 0
        where soi.id = #{insuredId}
    </select>
    <select id="listAdminQuery" resultType="com.cfpamf.ms.insur.weixin.pojo.vo.cancel.WxCancelPolicyVO">
        select
        sc.id ,
        sc.id cancel_id,
        sc.process_instance_id ,
        so.fhOrderId,
        soi.id insured_id,
        sp.productName,
        sp.companyId,
        sp2.planName,
        sc.policy_no,
        soa.personName applicant_person_name,
        soa.personGender applicant_person_gender,
        soa.idNumber applicant_id_number,
        soa.cellPhone applicant_mobile,
        soi.personName insured_person_name,
        soi.idNumber insured_id_number,
        soi.cellPhone insured_mobile,
        soi.personGender insured_person_gender,
        soi.birthday insured_birthday,
        ifnull(itm.`total_amount`, so.`unitPrice`  * so.`qty`) as total_Amount,
        so.startTime,
        so.endTime,
        so.create_time,
        sc.create_time cancel_create_time,
        soi.appStatus,
        sc.state cancel_state,
        sc.to_company_policy_state,
        sc.cancel_no,
        sc.task_key,<!-- 当前负责人信息 -->
        t6.userMobile admin_user_mobile ,
        t6.userName admin_user_name ,
        t6.userId admin_user_id,
        sc.company_cn,
        sc.company_cn_desc,
        sc.company_cn_creator,
        sc.company_cn_create_time,
        if(sc.cancel_reason is null or sc.cancel_reason = '', sc.cancel_reason_type,sc.cancel_reason) cancel_reason,
        t6.regionName,
        t6.organizationName,
        sc.end_time cancel_end_time
        <if test="taskKey == 'ut_company_do' or taskKey=='cancel_end'">
            , (select max(tmp.START_TIME_)
            from act_hi_taskinst tmp
            where tmp.PROC_INST_ID_ = sc.process_instance_id and tmp.TASK_DEF_KEY_='ut_company_do') to_company_time
        </if>
        from
        sm_cancel sc
        LEFT JOIN sm_order so on sc.fh_order_id = so.fhOrderId
        LEFT join sm_product sp on so.productId = sp.id
        LEFT join sm_order_applicant soa on so.fhOrderId = soa.fhOrderId
        LEFT join sm_order_insured soi on so.fhOrderId = soi.fhOrderId
        left join sm_order_item itm on soi.`idNumber`  = itm.`id_number` and itm.`fh_order_id` = soi.`fhOrderId`
        LEFT join sm_plan sp2 on sp2.id = so.planId
        LEFT JOIN auth_user t6 ON t6.userId=so.customerAdminId and t6.enabled_flag = 0
        <where>
            <if test="applyStart!=null">
                <![CDATA[ and  sc.create_time > #{applyStart}]]>
            </if>
            <if test="applyEnd!=null">
                <![CDATA[ and  sc.create_time < #{applyEnd}]]>
            </if>
            <if test="endStart!=null">
                <![CDATA[ and  sc.end_time > #{endStart}]]>
            </if>
            <if test="endEnd!=null">
                <![CDATA[ and  sc.end_time < #{endEnd}]]>
            </if>
            <if test="fhOrderId != null">
                and sc.fh_order_id = #{fhOrderId}
            </if>
            <if test="policyNo!=null">
                and sc.policy_no = #{policyNo}
            </if>
            <if test="taskKey!=null">
                and sc.task_key = #{taskKey}
            </if>
            <if test="all">
                <![CDATA[ and  sc.task_key <>'cancel_end']]>
            </if>
            <if test="productAttrCode!=null and productAttrCode!=''">
                and sp.productAttrCode = #{productAttrCode}
            </if>
            <if test='userId != null'>
                AND so.customerAdminId = #{userId}
            </if>
            <if test='cancelState != null'>
                AND sc.state = #{cancelState}
            </if>
            <if test='policyState!=null'>
                <if test="taskKey =='ut_company_do' or taskKey == 'cancel_end'">
                    AND sc.to_company_policy_state = #{policyState}
                </if>
                <if test="taskKey !='ut_company_do'">
                    AND
                    ( (sc.to_company_policy_state is null and
                    <if test="policyState==1">
                        <![CDATA[  so.startTime<now()) ]]>
                    </if>
                    <if test="policyState==0">
                        <![CDATA[  so.startTime>now()) ]]>
                    </if>
                    or sc.to_company_policy_state = #{policyState})
                </if>

            </if>
            <if test='applicantKeyword != null'>
                AND (
                (soa.personName LIKE CONCAT(#{applicantKeyword},'%'))
                or
                (soa.idNumber LIKE CONCAT(#{applicantKeyword},'%'))
                or
                (soa.cellPhone LIKE CONCAT(#{applicantKeyword},'%'))
                )
            </if>
            <if test='insuredKeyword != null'>
                AND (
                (soi.personName LIKE CONCAT(#{insuredKeyword},'%'))
                or
                (soi.idNumber LIKE CONCAT(#{insuredKeyword},'%'))
                or
                (soi.cellPhone LIKE CONCAT(#{insuredKeyword},'%'))
                )
            </if>

            <if test="companyCnState==1">
                and sc.company_cn_creator is not null
            </if>
            <if test="companyCnState==0">
                and sc.company_cn_creator is null
            </if>
            <if test='regionName != null'>
                AND t6.regionName=#{regionName}
            </if>
            <if test='orgName != null'>
                AND t6.organizationName=#{orgName}
            </if>
            <if test='noEnd'>
                <![CDATA[ and sc.task_key <>'cancel_end' ]]>
            </if>
            <if test='companyId!=null'>
                and sp.companyId=#{companyId}
            </if>
        </where>
        order by sc.id desc

    </select>
    <select id="selectByInsIdNumberAndOrder" resultType="com.cfpamf.ms.insur.admin.pojo.po.SmCancel">
        select sc.*
        from sm_cancel sc
        inner join sm_order_insured soi on sc.insured_id = soi.id
        where sc.fh_order_id = #{fhOrderId}
          <if test="idNumbers!=null and idNumbers.size() >0">
            and soi.idNumber in
            <foreach collection="idNumbers" item="idNumber" open="(" close=")" separator=",">
                #{idNumber}
            </foreach>
          </if>
        <![CDATA[ and sc.state <40 ]]>
    </select>
</mapper>
