<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.claim.SmWeekdayMapper">


    <select id="queryIntervalExceptHoliday" resultType="com.cfpamf.ms.insur.admin.pojo.po.claim.SmWeekday">
        select *
        from sm_weekday
        where weekday_status = 1
          and work_day > #{localDate}
        order by work_day
        limit #{days}
    </select>
</mapper>
