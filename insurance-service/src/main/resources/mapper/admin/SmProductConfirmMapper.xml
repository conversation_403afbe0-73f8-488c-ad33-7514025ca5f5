<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.product.SmProductConfirmMapper">

    <insert id="insertHisList">
        insert into sm_product_confirm_history(version, spc_id, confirm_name,product_id, file_name, file_url, file_content,display_sort)
        values
        <foreach collection="addList" item="item" separator=",">
            (#{version},#{item.id},#{item.confirmName},#{item.productId},#{item.fileName},#{item.fileUrl},#{item.fileContent},#{item.displaySort})
        </foreach>
    </insert>

    <update id="deleteHistory">
        update sm_product_confirm_history
        set enabled_flag = 1
        where product_id = #{productId}
          and version = #{version}
    </update>
    <update id="updateHis">
        update sm_product_confirm_history
        set file_name=#{model.fileName},
            file_url=#{model.fileUrl},
            confirm_name=#{model.confirmName},
            file_content=#{model.fileContent},
            display_sort=#{model.displaySort},
            enabled_flag = 0
        where product_id = #{model.productId}
          and version = #{version}
          and spc_id = #{model.id}
    </update>
</mapper>
