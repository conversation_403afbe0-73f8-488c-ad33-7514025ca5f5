<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.reconciliation.SmReconciliationMapper">
    <select id="searchSmReconciliation"
            resultType="com.cfpamf.ms.insur.admin.pojo.vo.reconciliation.SmReconciliationVo">
        select
        t1.id ,
        t1.channel ,
        t1.product_id as productId ,
        t3.productName as productName,
        t1.file_url as fileUrl,
        t1.start_time as startTime,
        t1.end_time as endTime,
        t1.import_time as importTime,
        t1.channel_amount as channelAmount,
        t1.our_company_amount as ourCompanyAmount,
        t1.total_number as totalNumber,
        t1.correct_number as correctNumber,
        t1.difference_detail as differenceDetail,
        count(DISTINCT(t2.serial_number)) as balanceAccountCount,
        au.userName as operator
        from sm_reconciliation t1
        left join auth_user au on
        au.enabled_flag = 0 and au.userId = t1.operator
        LEFT JOIN sm_reconciliation_difference t2 on t1.id = t2.reconciliation_id and t2.balance_account_flag = 1
        left join sm_product t3
        on t3.id = t1.product_id
        where 1= 1
        <if test="channel != null and channel!=''">
            and t1.channel = #{channel}
        </if>
        <if test="reconciliationStartTime != null">
            and t1.end_time >= #{reconciliationStartTime}
        </if>
        <if test="reconciliationEndTime != null">
            <![CDATA[  and t1.start_time <= #{reconciliationEndTime}]]>
        </if>
        <if test="importStartTime != null">
            and t1.import_time >= #{importStartTime}
        </if>
        <if test="importEndTime != null">
            <![CDATA[  and t1.import_time <= #{importEndTime}]]>
        </if>
        GROUP BY t1.id
        order by t1.id desc

    </select>
</mapper>