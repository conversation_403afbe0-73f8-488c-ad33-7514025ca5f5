<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.product.SmPlanHistoryMapper">
    <update id="softDelete">
        update sm_plan_history
        set enabled_flag = 1
        WHERE planId = #{planId}
          and version = #{version}
    </update>
    <update id="updateByPlanIdAndVersion">
        update sm_plan_history
        set
        <if test="planName != null and planName != ''">
            planName = #{planName},
        </if>
        <if test="description != null and description != ''">
            description = #{description},
        </if>
        <if test="fhProductId != null and fhProductId != ''">
            fhProductId = #{fhProductId},
        </if>
        <if test="updateBy != null and updateBy != ''">
            update_by = #{updateBy},
        </if>
        <if test="planOrderOutType != null and planOrderOutType != ''">
            planOrderOutType = #{planOrderOutType},
        </if>
        <if test="planCode != null and planCode != ''">
            plan_code = #{planCode},
        </if>
        <if test="planType != null and planType != ''">
            plan_type = #{planType},
        </if>
        <if test="minPremium != null ">
            min_premium = #{minPremium}
        </if>
        WHERE planId = #{planId}
        and version = #{version}
        and enabled_flag = 0
    </update>

    <select id="getSmLongInsurancePlanVo"
            resultType="com.cfpamf.ms.insur.admin.pojo.vo.product.SmLongInsurancePlanVo">
        select planId,
               productId,
               planName,
               description,
               fhProductId,
               planOrderOutType,
               plan_code,
               plan_type,
               min_premium
        from sm_plan_history
        where productId = #{productId}
          and version = #{version}
          and enabled_flag = 0
    </select>
    <select id="getSmLongInsurancePlanVoByPlanId"
            resultType="com.cfpamf.ms.insur.admin.pojo.vo.product.SmLongInsurancePlanVo">
        select planId,
               productId,
               planName,
               description,
               fhProductId,
               planOrderOutType,
               plan_code,
               plan_type,
               min_premium
        from sm_plan_history
        where planId = #{planId}
          and version = #{version}
          and enabled_flag = 0
    </select>
    <select id="selectMinAmount" resultType="java.math.BigDecimal">
        select min(min_premium)
        from sm_plan_history
        where productId = #{productId}
          and version = #{version}
          and enabled_flag = 0
    </select>
    <select id="queryOrderGuaranteeInfo"
            resultType="com.cfpamf.ms.insur.admin.pojo.vo.product.OrderGuaranteeDutyInfoVO">
        SELECT
            t1.cvgItemName AS riskName,
            t1.id AS risk_id,
            t1.cvgItemName AS dutyName,
            t2.cvgAmount AS riskAmount,
            t2.cvgAmount AS dutyAmount
        FROM
            sm_product_coverage_history t1
                INNER JOIN sm_product_coverage_amount_history t2 ON t1.spcId = t2.spcId
                INNER JOIN sm_plan_history t3 ON t3.planId = t2.planId
        WHERE
            t3.planId = #{planId}
          AND t1.enabled_flag = 0
          AND t2.enabled_flag = 0
          AND t3.enabled_flag = 0
          AND t1.version = #{version}
          AND t2.version = #{version}
          AND t3.version = #{version}


    </select>

    <select id = "queryPlanRiskByProductId" resultType="com.cfpamf.ms.insur.admin.pojo.dto.product.SmPlanRiskDTO">
        select t.id productId,
               t1.planid planId,
               t1.planName,
               t3.id as riskId,
               t3.risk_name riskName,
               CONCAT(t1.planName,"-",t3.risk_name) planRiskName,
               t2.risk_key,
               t3.pay_way payWay,
               t3.covered_years coveredYears,
               t3.valid_period validPeriod
        from sm_product t
        left join sm_plan_history t1 on t1.productId = t.id and t1.enabled_flag = 0 and t1.version = #{version}
        left join sm_plan_risk_history t2 on t2.plan_Id = t1.planid and t2.version =  #{version}
        left join sys_risk t3 on t3.risk_key = t2.risk_key
        where t.id = #{productId}
    </select>

    <select id="queryPlanByfhProductIds" resultType="com.cfpamf.ms.insur.admin.pojo.po.product.SmPlanHistory">
        SELECT
            t1.*
        FROM
            sm_plan_history t1
                INNER JOIN (
                SELECT
                    planId,
                    MAX(version) AS max_version
                FROM
                    sm_plan_history
                GROUP BY
                    planId
            ) t2 ON t1.planId = t2.planId
                AND t1.version = t2.max_version
                AND t1.enabled_flag = 0
                AND t1.fhProductId IN
        <foreach item="item" index="index" open="(" separator="," close=")" collection="fhProductIds">
            #{item}
        </foreach>
        LIMIT 200000
    </select>
</mapper>