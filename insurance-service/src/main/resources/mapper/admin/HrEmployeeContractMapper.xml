<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.hr.HrEmployeeContractMapper">
    <select id="listByQuery" resultType="com.cfpamf.ms.insur.admin.pojo.vo.hr.HrEmployeeContractVO">
        select
        he.id employee_id,
        hec.id ,
        he.contract_status,
        au.regionName,au.organizationName,he.entry_date,he.id_number,
        he.name,he.hr_no,hec.sign_date,hec.start_date,hec.end_date,hec.contract_type,
        he.mobile,he.entry_date
        from hr_employee he
        left join hr_employee_contract hec
        on he.id = hec.employee_id
        left join auth_user au on
        au.enabled_flag = 0 and au.userId = he.zhnx_job_number
        where he.employee_status=3
        <if test="doing == true ">
            and he.contract_opt=0
        </if>
        <if test="done == true ">
            and he.contract_opt=1
        </if>
        <if test="contactStatus != null">
            AND he.contract_status = #{contactStatus}
        </if>
        <if test='regionName != null'>
            AND au.regionName=#{regionName}
        </if>
        <if test='orgName != null'>
            AND au.organizationName=#{orgName}
        </if>

        <if test="entryDateStart != null">
            <![CDATA[ and he.entry_date >= #{entryDateStart}  ]]>
        </if>

        <if test="entryDateEnd != null">
            <![CDATA[ and he.entry_date <= #{entryDateEnd}  ]]>
        </if>

        <if test="contractSignDateStart != null">
            <![CDATA[ and hec.sign_date >= #{contractSignDateStart}  ]]>
        </if>

        <if test="contractSignDateEnd != null">
            <![CDATA[ and hec.sign_date < #{contractSignDateEnd}  ]]>
        </if>
        <if test="contractEndDateStart != null">
            <![CDATA[ and hec.end_date >= #{contractEndDateStart}  ]]>
        </if>

        <if test="contractEndDateEnd != null">
            <![CDATA[ and hec.end_date < #{contractEndDateEnd}  ]]>
        </if>
        <if test="employeeKeyword!=null and employeeKeyword!=''">
            and (
            he.name like CONCAT('%',<trim>#{employeeKeyword}</trim>,'%')
            or he.mobile like CONCAT('%',<trim>#{employeeKeyword}</trim>,'%')
            or he.zhnx_job_number like CONCAT('%',<trim>#{employeeKeyword}</trim>,'%')
            or he.hr_no like CONCAT('%',<trim>#{employeeKeyword}</trim>,'%')
            )
        </if>
        <if test="doing == true ">
            order by he.contract_status desc,he.entry_date desc
        </if>
        <if test="done == true ">
            order by  hec.sign_date desc
        </if>
    </select>
</mapper>
