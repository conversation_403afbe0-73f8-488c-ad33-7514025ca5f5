<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.BakWxOpenidMapperMapper">
    <insert id="insertByAuthUser">
        insert into bak_wx_openid_mapper(old_open_id, new_open_id, old_app, new_app, state)
        select distinct wxOpenId, null, #{oldAppId}, #{newAppId}, 0
        from auth_user
        <![CDATA[   where wxOpenId is not null
          and wxOpenId <> ''
        ]]>
    </insert>
    <update id="replaceList">
        replace into bak_wx_openid_mapper(id, old_open_id, new_open_id, old_app, new_app,state,remark)
        values
        <foreach collection="models" item="model" separator=",">
            (#{model.id},#{model.oldOpenId},#{model.newOpenId},#{model.oldApp},#{model.newApp},#{model.state},#{model.remark})
        </foreach>
    </update>
</mapper>

