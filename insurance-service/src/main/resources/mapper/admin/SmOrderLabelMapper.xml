<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.SmOrderLabelMapper">
    <insert id="insertLabel" useGeneratedKeys="true">
        INSERT INTO SM_ORDER_LABEL (fh_order_id,label_type,label_type_desc,label_desc,label_value,
                                    enabled_flag,create_time,create_by,update_time,update_by)
        VALUES (#{fhOrderId},#{labelType},#{labelTypeDesc},#{labelDesc},#{labelValue},
               #{enabledFlag},#{createTime},#{createBy},#{updateTime},#{updateBy})
    </insert>

    <select id="list" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmOrderLabelVO">
        SELECT
               t.fh_order_id,
               t.label_type,
               t.label_type_desc,
               t.label_value,
               t.create_time
        FROM SM_ORDER_LABEL t
        WHERE t.enabled_flag = 0
        <if test='fhOrderId != null and fhOrderId!=""'>
            and t.fh_order_id = #{fhOrderId}
        </if>
        <if test='labelType != null and labelType!=""'>
            and t.label_type = #{labelType}
        </if>
    </select>
</mapper>
