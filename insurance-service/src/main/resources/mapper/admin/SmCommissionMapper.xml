<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.SmCommissionMapper">

    <insert id="insertCommissionSetting" useGeneratedKeys="true">
        INSERT INTO sm_commission_setting (planId,paymentProportion, settlementProportion,saleLevel,startDate, endDate, create_by, create_time, update_by, update_time, enabled_flag)
        VALUES
        (#{planId}, #{paymentProportion},#{settlementProportion}, #{saleLevel},#{startDate},#{endDate}, #{modifyBy}, CURRENT_TIMESTAMP(), #{modifyBy}, CURRENT_TIMESTAMP(), 0)
    </insert>

    <select id="listCommissionSettings" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmCommissionSettingVO">
        SELECT t1.*, t2.planName AS planName, t2.fhProductId AS fhProductId, t4.companyName AS companyName, t4.update_by AS
        modifyBy, t4.update_time AS modifyTime,t3.productName as productName
        FROM sm_commission_setting t1
        INNER JOIN sm_plan t2 ON t1.planId = t2.id AND t2.enabled_flag=0
        INNER JOIN sm_product t3 ON t2.productId = t3.id AND t3.enabled_flag=0
        LEFT JOIN sm_company t4 ON t3.companyId = t4.id AND t4.enabled_flag=0
        WHERE t1.enabled_flag=0
        <if test='productName != null'>
            AND ( t2.planName LIKE CONCAT('%',#{productName},'%') OR t3.productName LIKE CONCAT('%',#{productName},'%')
            OR t4.companyName LIKE CONCAT('%',#{productName},'%') )
        </if>
        <if test='fhProductId != null'>
            AND t2.fhProductId = #{fhProductId}
        </if>
        <if test='date != null'>
            <![CDATA[  AND startDate <= #{date} AND endDate >= #{date} ]]>
        </if>
        ORDER BY t3.id, t2.id ASC
    </select>

    <select id="listCommissionSettingsByProductId" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmCommissionSettingVO">
        SELECT
          t1.*,
          t2.planName AS planName,
          t2.fhProductId AS fhProductId,
          t4.companyName AS companyName,
          t4.update_by AS modifyBy,
          t4.update_time AS modifyTime,
          t3.productName as productName
        FROM sm_commission_setting t1
        INNER JOIN sm_plan t2 ON t1.planId = t2.id AND t2.enabled_flag=0
        INNER JOIN sm_product t3 ON t2.productId = t3.id AND t3.enabled_flag=0
        LEFT JOIN sm_company t4 ON t3.companyId = t4.id AND t4.enabled_flag=0
        WHERE t1.enabled_flag=0
        AND t2.productId=#{productId}
        <if test='now != null'>
            <![CDATA[  AND startDate <= #{now} AND endDate >= #{now} ]]>
        </if>
        ORDER BY t3.id, t2.id ASC


    </select>


    <select id="listCommissionSettingsByTimeRange" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmCommissionSettingVO">
        SELECT t1.* FROM sm_commission_setting t1
        WHERE t1.enabled_flag=0
        AND t1.planId = #{planId}
        <![CDATA[  AND ((startDate <= #{sDate} AND endDate >= #{sDate}) OR (startDate <= #{eDate} AND endDate >= #{eDate})
        OR (startDate >= #{sDate} AND endDate <= #{eDate})
         OR (startDate <= #{sDate} AND endDate >= #{eDate}) ) ]]>
        <if test='cid != null'>
            AND id != #{cid}
        </if>
        ORDER BY t1.update_time DESC
    </select>

    <select id="getCommissionSettingByPlanId" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmCommissionSettingVO">
        SELECT * FROM sm_commission_setting WHERE planId = #{planId} <![CDATA[ AND startDate <= #{datumTime} AND endDate >= #{datumTime}]]> ORDER BY update_time DESC LIMIT 1
    </select>

    <select id="getCommissionSettingByPlanIds" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmCommissionSettingVO">
        SELECT * FROM sm_commission_setting WHERE  enabled_flag=0
        <![CDATA[ AND startDate <= #{datumTime} AND endDate >= #{datumTime} ]]>
        AND planId IN
        <foreach collection="planIds" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <update id="updateCommissionSetting">
        UPDATE sm_commission_setting SET paymentProportion=#{paymentProportion},settlementProportion=#{settlementProportion},saleLevel=#{saleLevel},
        startDate=#{startDate},endDate=#{endDate},enabled_flag=#{deleteFlag}, update_by=#{modifyBy},update_time=CURRENT_TIMESTAMP() WHERE id=#{id}
    </update>

    <update id="deleteCommissionSetting">
        UPDATE sm_commission_setting SET enabled_flag=1 WHERE id=#{id}
    </update>


    <select id="listCommissionSettingsByFhProductIds" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmCommissionSettingVO">
        SELECT t1.*, t2.planName AS planName, t2.fhProductId AS fhProductId,t2.productId productId
        FROM sm_commission_setting t1
        INNER JOIN sm_plan t2 ON t1.planId = t2.id AND t2.enabled_flag=0
        WHERE t1.enabled_flag=0

        <if test='fhProductIds != null and fhProductIds.size() >0 '>
        AND t2.fhProductId in
        <foreach item="item" index="index" open="(" separator="," close=")" collection="fhProductIds">
            #{item}
        </foreach>
        </if>


    </select>

    <select id="listCommissionSettingsByPlanIds" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmCommissionSettingVO">
        SELECT t1.*, t2.planName AS planName, t2.fhProductId AS fhProductId,t2.productId productId
        FROM sm_commission_setting t1
        INNER JOIN sm_plan t2 ON t1.planId = t2.id AND t2.enabled_flag=0
        WHERE t1.enabled_flag=0

        <if test='planIds != null and planIds.size() >0 '>
            AND t2.id in
            <foreach item="item" index="index" open="(" separator="," close=")" collection="planIds">
                #{item}
            </foreach>
        </if>
        order by t1.id desc;

    </select>


    <select id="getMaxId" resultType="java.lang.Integer">
        SELECT max(id)
        FROM sm_commission_setting
    </select>

    <select id="selectCarSetting" resultType="com.cfpamf.ms.insur.admin.pojo.dto.SmCommissionSettingDTO">
        SELECT *
        FROM sm_commission_setting
        where planId=#{planId} and paymentProportion=#{paymentProportion} and settlementProportion=#{settlementProportion} and  saleLevel=#{saleLevel}
        limit 1;
    </select>

    <select id="selectByIds" resultType="com.cfpamf.ms.insur.admin.pojo.dto.SmCommissionSettingDTO">
        SELECT *
        FROM sm_commission_setting
        where id in
        <foreach item="item" index="index" open="(" separator="," close=")" collection="ids">
            #{item}
        </foreach>
    </select>
</mapper>
