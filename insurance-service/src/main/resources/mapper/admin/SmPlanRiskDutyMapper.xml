<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.product.SmPlanRiskDutyMapper">
    <update id="pushNewVersion">
        update sm_plan_risk_duty sprd
            left join sm_plan_risk_duty_history sprdh
        on sprdh.plan_risk_duty_id = sprd.id and sprdh.enabled_flag = 0
            set
                sprd.plan_id = sprdh.plan_id,
                sprd.product_id = sprdh.product_id,
                sprd.sm_plan_risk_id = sprdh.sm_plan_risk_id,
                sprd.duty_key = sprdh.duty_key,
                sprd.duty_version = sprdh.duty_version,
                sprd.amount_text = sprdh.amount_text,
                sprd.risk_duty_amount_id_arr = sprdh.risk_duty_amount_id_arr,
                sprd.included_risk_insured_amount = sprdh.included_risk_insured_amount,
                sprd.mandatory = sprdh.mandatory,
                sprd.enabled_flag = sprdh.enabled_flag,
                sprd.mandatory = sprdh.mandatory,
                sprd.create_by = sprdh.create_by,
                sprd.update_by = sprdh.update_by,
                sprd.create_time = sprdh.create_time,
                sprd.update_time = sprdh.update_time
        where
            sprdh.product_id = #{productId}
          and sprdh.version = #{version}
          and sprdh.enabled_flag = 0
    </update>

    <select id="listRiskPremiumByFhOrderId" resultType="com.cfpamf.ms.insur.admin.pojo.po.order.SmOrderRiskDuty">
        select fh_order_id  ,risk_id,risk_name,insured_id_number,insured_period_type,insured_period,
        period_type,payment_period,payment_period_type,app_status,main_insurance,
        sum(premium) as premium,sum(amount) as amount,sum(refund_amount) as refund_amount,
        surrender_time,surrender_type
        from sm_order_risk_duty
        where fh_order_id=#{fhOrderId} and premium>0 and enabled_flag=0
        group by fh_order_id ,risk_id,insured_id_number
    </select>
</mapper>
