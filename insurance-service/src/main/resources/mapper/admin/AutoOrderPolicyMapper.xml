<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.auto.AutoOrderPolicyMapper">
    <update id="logicDelete">
        update auto_order set enabled_flag =1 where order_no = #{fhOrderId}

    </update>

    <insert id="insertAutoOrderPolicyBatch" parameterType= "java.util.List">
        INSERT INTO auto_order_policy (order_no, risk_code, risk_name, policy_no,
        policy_url,plan_id,policy_state,premium, amount,  pay_status,
        start_date,end_date,issue_date,sub_policy_no, enabled_flag, create_time, update_time)
        VALUES
        <foreach collection="dtos" item="dto" index="index" separator=",">
        <foreach collection="dto.policies" item="item" index="index" separator=",">
            (#{dto.orderNo}, #{item.policy.riskCode}, #{item.policy.riskName}, #{item.policy.policyNo}, #{item.policy.policyUrl},
            #{item.policy.planId}, #{item.policy.policyState}, #{item.policy.premium}, #{item.policy.amount},#{item.policy.payStatus},
            #{item.policy.startDate},
            #{item.policy.endDate}, #{item.policy.issueDate}, #{item.policy.subPolicyNo},0, CURRENT_TIMESTAMP(), CURRENT_TIMESTAMP())
        </foreach>
        </foreach>
    </insert>

    <update id="updateAutoOrderPolicyBatch" parameterType= "java.util.List">
        <foreach collection="dtos" item="dto" index="index" separator=";">
        <foreach collection="dto.policies" item="item" index="index" separator=";">
            update auto_order_policy set risk_code=#{item.policy.riskCode},risk_name=#{item.policy.riskName},
            policy_no=#{item.policy.policyNo},policy_url=#{item.policy.policyUrl},
            plan_id=#{item.policy.planId},policy_state=#{item.policy.policyState},
            premium=#{item.policy.premium},amount=#{item.policy.amount},
            pay_status=#{item.policy.payStatus},start_date=#{item.policy.startDate},end_date=#{item.policy.endDate},
            issue_date=#{item.policy.issueDate},sub_policy_no=#{item.policy.subPolicyNo}
            where order_no=#{dto.orderNo}
        </foreach>
        </foreach>
    </update>

    <update id="updateAutoOrderPolicyCancelBatch" parameterType= "java.util.List">
        <foreach collection="dtos" item="dto" index="index" separator=";">
            <foreach collection="dto.policies" item="item" index="index" separator=";">
                update auto_order_policy set policy_state=#{item.policy.policyState},
                    surrender_time=now()
                where order_no=#{dto.orderNo} and policy_no=#{item.policy.policyNo}
            </foreach>
        </foreach>
    </update>
</mapper>
