<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.product.SmProductLabelMapper">
    <update id="batchInsertProductLabel" useGeneratedKeys="true" parameterType="java.util.List">
        insert into
        sm_product_label(product_id,label_type,label_type_desc,label_desc,label_value,create_by,update_by)
        values
        <foreach collection="list" item="label" separator=",">
        (#{label.productId},#{label.labelType},#{label.labelTypeDesc},#{label.labelDesc},#{label.labelValue},#{label.createBy},#{label.updateBy}
        )
        </foreach>
        ON DUPLICATE KEY UPDATE
        product_id=values(product_id),label_type=values(label_type),label_type_desc=values(label_type_desc),label_desc=values(label_desc)
        ,label_value=values(label_value),update_by=values(update_by)
    </update>


</mapper>
