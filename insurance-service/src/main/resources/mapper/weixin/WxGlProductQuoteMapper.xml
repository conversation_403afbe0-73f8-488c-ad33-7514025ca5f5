<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.weixin.dao.safes.WxGlProductQuoteMapper">

    <select id="getMaxProductQuoteNo" resultType="java.lang.String">
		SELECT MAX(quoteNo) FROM sm_product_quote_plan
	</select>

    <select id="getProductQuoteDetail" resultType="java.lang.String">
		SELECT quoteDetail FROM sm_product_quote_plan WHERE spqpId = #{spqpId}
	</select>

    <select id="listWxGlProductQuotePlans" resultType="com.cfpamf.ms.insur.weixin.pojo.vo.WxGlProductQuoteListVO">
        SELECT t1.*, t2.productName, t2.thumbnailImageUrl, t2.glQuoteTemplateUrl AS productTemplateUrl FROM sm_product_quote_plan t1
        LEFT JOIN sm_product t2 ON t1.productId = t2.id
        WHERE t1.enabled_flag = 0
        <if test="userId != null">
            AND t1.customerAdminId = #{userId}
        </if>
        <if test="agentId != null">
            AND t1.agentId = #{agentId}
        </if>
        <if test="keyword != null">
            AND (t1.quoteNo LIKE CONCAT (#{keyword},'%') OR t1.customerName LIKE CONCAT (#{keyword},'%') )
        </if>
        <if test="startDate != null">
            <![CDATA[ AND t1.create_time > #{startDate} ]]>
        </if>
        <if test="productId != null">
            AND t1.productId = #{productId}
        </if>
        ORDER BY t1.create_time DESC
        <if test="limitItem != null">
            LIMIT 0, #{limitItem}
        </if>
    </select>
</mapper>