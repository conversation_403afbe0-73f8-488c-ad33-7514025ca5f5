<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.weixin.dao.safes.WxProductMapper">

    <select id="listWxProducts" resultType="com.cfpamf.ms.insur.weixin.pojo.vo.WxProductListVO">
        SELECT t1.id, t1.channel, t1.productAttrCode, t1.productName, t1.productFeature, t1.thumbnailImageUrl,
        t1.minAmount, t1.saleQty,
        t1.apiType,t1.h5Url, t1.sortNum,t1.productTags AS productTagsJoin,
        t1.miniPremium,
        t1.product_code,
        t1.explain_video, t1.explain_video_img,
        t1.create_type
        FROM sm_product t1
        WHERE t1.state=1 AND t1.enabled_flag=0
        AND t1.productAttrCode in ('person','employer')
        and t1.id not in (select product_id from sm_product_label where label_type='renewal_product' and label_value='Y')
        AND t1.onlineChannel LIKE '%xiangzhu%'
        <if test='productCategoryId != null'>
            AND (t1.productCategoryId = #{productCategoryId} OR t1.productCategoryId LIKE CONCAT(#{productCategoryId},
            ',%')
            OR t1.productCategoryId LIKE CONCAT('%,',#{productCategoryId}, ',%') OR t1.productCategoryId LIKE
            CONCAT('%,',#{productCategoryId}))
        </if>
        <if test='productId != null'>
            AND t1.id = #{productId}
        </if>
        <if test='channel != null'>
            AND t1.channel = #{channel}
        </if>
        <if test='companyId != null'>
            AND t1.companyId = #{companyId}
        </if>
        <if test='salesProductIds != null'>
            AND t1.id IN
            <foreach item="item" index="index" open="(" separator="," close=")" collection="salesProductIds">
                #{item}
            </foreach>
        </if>
        <if test="keyword != null and keyword!='' ">
            AND
              (MATCH(t1.productName, t1.productTags, t1.productFeature) AGAINST(CONCAT(#{keyword},'*') IN BOOLEAN MODE)
            or t1.productName like CONCAT('%',#{keyword},'%')
                    )

        </if>
        ORDER BY sortNum ASC,t1.companyId ASC, t1.productName ASC
    </select>

    <select id="listWxProductsWithGroup" resultType="com.cfpamf.ms.insur.weixin.pojo.vo.WxProductListVO">
        SELECT t1.id, t1.channel, t1.productAttrCode, t1.productName, t1.productFeature, t1.thumbnailImageUrl,
        t1.minAmount, t1.saleQty, t1.sortNum
        ,t1.h5Url,
        t1.apiType,t1.productTags AS productTagsJoin,
        t1.miniPremium,
        t1.product_code,
        t1.explain_video,t1.explain_video_img,
        t1.create_type
        FROM sm_product t1
        WHERE t1.state=1 AND t1.enabled_flag=0
        AND t1.onlineChannel LIKE '%xiangzhu%'
        <if test='salesProductIds != null'>
            AND t1.id IN
            <foreach item="item" index="index" open="(" separator="," close=")" collection="salesProductIds">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="listWxHotProducts" resultType="com.cfpamf.ms.insur.weixin.pojo.vo.WxProductListVO">
        SELECT t1.id, t1.channel, t1.productName, t1.productFeature, t1.thumbnailImageUrl, t1.minAmount, t1.sortNum
        ,t1.h5Url,
        t1.apiType,t1.productTags AS productTagsJoin,
        t1.productAttrCode,
        t1.miniPremium,
        t1.product_code,
        t1.explain_video,t1.explain_video_img,
        t1.create_type
        FROM sm_product t1

        WHERE t1.state=1 AND t1.enabled_flag=0 AND t1.onlineChannel LIKE '%xiangzhu%'
        AND t1.productAttrCode in ('person','employer')
        and t1.id not in (select product_id from sm_product_label where label_type='renewal_product' and label_value='Y')
        <if test='channel != null'>
            AND t1.channel = #{channel}
        </if>
        <if test='salesProductIds != null'>
            AND t1.id IN
            <foreach item="item" index="index" open="(" separator="," close=")" collection="salesProductIds">
                #{item}
            </foreach>
        </if>
        ORDER BY t1.monthSaleCount DESC, t1.productName ASC
        LIMIT 5
    </select>

    <select id="listWxActiveProducts" resultType="com.cfpamf.ms.insur.weixin.pojo.vo.WxProductListVO">
        SELECT t1.id, t1.channel, t1.productName, t1.productFeature, t1.thumbnailImageUrl, t1.minAmount, t1.sortNum
        ,t1.h5Url,
        t1.apiType,t1.productTags AS productTagsJoin
        ,
        t1.productAttrCode,
        t1.miniPremium,
        t1.product_code,
        t1.explain_video, t1.explain_video_img,
        t1.create_type
        FROM sm_product t1
        WHERE t1.state=1 AND t1.enabled_flag=0 AND t1.onlineChannel LIKE '%xiangzhu%'
        AND t1.productAttrCode in ('person','employer')
        and t1.id not in (select product_id from sm_product_label where label_type='renewal_product' and label_value='Y')
        and t1.activeFlag = 1
        <if test='channel != null'>
            AND t1.channel = #{channel}
        </if>
        <if test='salesProductIds != null'>
            AND t1.id IN
            <foreach item="item" index="index" open="(" separator="," close=")" collection="salesProductIds">
                #{item}
            </foreach>
        </if>
        ORDER BY t1.sortNum ,t1.monthSaleCount DESC, t1.productName ASC
    </select>

    <select id="getWxProductDetailById" resultType="com.cfpamf.ms.insur.weixin.pojo.vo.WxProductDetailVO">
        SELECT t1.id,
               t1.aiCheck,
               t1.aiCheckWay,
               t1.version,
               t1.channel,
               t1.productName,
               t1.productFeature,
               t1.productTags   AS productTagsJoin,
               t1.introduceImageUrl,
               t1.companyId,
               t2.companyLogoImageUrl,
               t2.companyName,
               t1.attentions,
               t1.buyLimit,
               t1.effectWaitingDayMin,
               t1.effectWaitingDayMax,
               t1.attentions,
               t1.healthNotification,
               t1.minAmount,
               t1.productSpecialsJoin,
               t1.glProductNotice,
               t2.reportPhoneNo AS companyReportPhoneNo,
               t1.h5Url,
               t1.apiType,
               t1.headImageUrl,
               t1.productShortName,
               t1.custNotify,
               t1.miniPremium,
               t1.explain_video, t1.explain_video_img,
               t1.product_code,
				t1.sales_mode as salesMode,
               t1.create_type
        FROM sm_product t1
        LEFT JOIN sm_company t2 ON t1.companyId = t2.id
        WHERE t1.id = #{id}
        AND t1.state = 1
        AND t1.enabled_flag = 0
    </select>

    <select id="listActivityProducts" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmActivityProductVO">
        SELECT t1.*, t1.id AS productId,
        t4.companyName AS companyName
        FROM sm_product t1
        LEFT JOIN sm_company t4 on t1.companyId = t4.id AND t4.enabled_flag=0
        WHERE t1.enabled_flag = 0 AND t1.productAttrCode in ('person','employer') AND t1.state = 1
        <if test='productName != null'>
            AND t1.productName LIKE CONCAT('%',#{productName},'%')
        </if>
        <if test='categoryId != null'>
            AND (t1.productCategoryId = #{categoryId} OR t1.productCategoryId LIKE CONCAT(#{categoryId}, ',%')
            OR t1.productCategoryId LIKE CONCAT('%,',#{categoryId}, ',%') OR t1.productCategoryId LIKE
            CONCAT('%,',#{categoryId}))
        </if>
        <if test='companyId != null'>
            AND t1.companyId =#{companyId}
        </if>
        <if test='productIds != null'>
            AND t1.id IN
            <foreach item="item" index="index" open="(" separator="," close=")" collection="productIds">
                #{item}
            </foreach>
        </if>
        ORDER BY ,t1.id ASC
    </select>

    <select id="listWxRenewProductsProductId" resultType="com.cfpamf.ms.insur.weixin.pojo.vo.WxProductRenewVO">
        SELECT t2.id          AS renewProductId,
               t2.productName AS renewProductName,
               t3.state       AS oldProductState
        FROM sm_product_renew t1
                 LEFT JOIN sm_product t2
                           ON t2.id = t1.renewProductId
                 LEFT JOIN sm_product t3
                           ON t3.id = t1.productId
        WHERE t1.productId = #{productId}
          AND t1.enabled_flag = 0
    </select>

    <select id="listWxGroupProducts" resultType="com.cfpamf.ms.insur.weixin.pojo.vo.WxGlProductListVO">
        SELECT
            id AS productId,
            productName,
            thumbnailImageUrl,
            glUwaFrom,
            glUwaTo,
            glOcpnGroup,
            effectWaitingDayMin,
            effectWaitingDayMax,
            minAmount,
            apiType,h5Url,
            productAttrCode,
            create_type as createType
        FROM
        sm_product
        WHERE productAttrCode = 'group'
        AND state = 1
        <if test='salesProductIds != null'>
            AND id IN
            <foreach item="item" index="index" open="(" separator="," close=")" collection="salesProductIds">
                #{item}
            </foreach>
        </if>
        ORDER BY sortNum ASC
    </select>
    <resultMap id="groupProductMap" type="com.cfpamf.ms.insur.weixin.pojo.dto.product.GroupProductListDTO">
        <result column="id"                 property="productId"            jdbcType="INTEGER" />
        <result column="productName"        property="productName"          jdbcType="VARCHAR" />
        <result column="channel"            property="channel"          jdbcType="VARCHAR" />
        <result column="thumbnailImageUrl"  property="thumbnailImageUrl"    jdbcType="VARCHAR" />
        <result column="glUwaFrom"          property="glUwaFrom"            jdbcType="VARCHAR" />
        <result column="glUwaTo"            property="glUwaTo"              jdbcType="VARCHAR" />
        <result column="glOcpnGroup"        property="glOcpnGroup"          jdbcType="VARCHAR" />
        <result column="effectWaitingDayMin" property="effectWaitingDayMin" jdbcType="VARCHAR" />
        <result column="effectWaitingDayMax" property="effectWaitingDayMax" jdbcType="VARCHAR" />
        <result column="minAmount"          property="minAmount" jdbcType="VARCHAR" />
        <result column="apiType"            property="apiType" jdbcType="VARCHAR" />
        <result column="h5Url"              property="h5Url" jdbcType="VARCHAR" />
        <result column="productAttrCode"    property="productAttrCode" jdbcType="VARCHAR" />
        <result column="create_type"        property="createType" jdbcType="VARCHAR" />
        <collection property="attrList" ofType="com.cfpamf.ms.insur.weixin.pojo.dto.product.ProductAttrDTO">
            <result column="attr_code"       property="attrCode"         jdbcType="VARCHAR" />
            <result column="attr_val"        property="attrVal"          jdbcType="VARCHAR" />
        </collection>
    </resultMap>
    <resultMap id="productAttrMap" type="com.cfpamf.ms.insur.weixin.pojo.dto.product.ProductAttrDTO">
        <result column="attr_code"       property="attrCode"         jdbcType="VARCHAR" />
        <result column="attr_val"        property="attrVal"          jdbcType="VARCHAR" />
    </resultMap>
    <select id="queryGroupProductList" resultMap="groupProductMap">
        SELECT
            p.id,
            p.channel,
            productName,
            thumbnailImageUrl,
            glUwaFrom,
            glUwaTo,
            glOcpnGroup,
            effectWaitingDayMin,
            effectWaitingDayMax,
            minAmount,
            apiType,h5Url,
            productAttrCode,
            create_type,
            a.attr_code,
            a.attr_val
        FROM
        sm_product p left join sm_product_attr a on p.id=a.product_id and a.attr_code= 'isFree'
        WHERE productAttrCode = 'group'
        AND state = 1
        <if test='salesProductIds != null'>
            AND p.id IN
            <foreach item="item" index="index" open="(" separator="," close=")" collection="salesProductIds">
                #{item}
            </foreach>
        </if>
        ORDER BY sortNum ASC
    </select>
    <select id="countGroupProductList" resultMap="groupProductMap">
        SELECT
            count(1)
        FROM
        sm_product p
        WHERE productAttrCode = 'group'
        AND state = 1
        <if test='salesProductIds != null'>
            AND p.id IN
            <foreach item="item" index="index" open="(" separator="," close=")" collection="salesProductIds">
                #{item}
            </foreach>
        </if>
        ORDER BY sortNum ASC
    </select>


    <select id="getGlProductById"
            resultType="com.cfpamf.ms.insur.weixin.pojo.vo.WxGlProductDetailVO">
        SELECT t1.*, t1.id AS productId, t2.companyName
        FROM sm_product t1
                 LEFT JOIN sm_company t2 ON t1.companyId = t2.id
        WHERE t1.id = #{productId}
    </select>

    <select id="listProductClausesByProductId" resultType="com.cfpamf.ms.insur.weixin.pojo.vo.WxProductClauseVO">
        SELECT *, id AS productId
        FROM sm_product_clause
        WHERE productId = #{id}
          AND enabled_flag = 0
        ORDER BY sort ASC, id ASC
    </select>

    <select id="listProductCoverages" resultType="com.cfpamf.ms.insur.weixin.pojo.vo.WxProductCoverageVO">
        SELECT *
        FROM sm_product_coverage
        WHERE productId = #{productId}
          and enabled_flag = 0;
    </select>

    <select id="listProductQuoteLimit" resultType="com.cfpamf.ms.insur.weixin.pojo.vo.WxProductQuoteLimitItemVO">
        SELECT *
        FROM sm_product_quote_limit
        WHERE productId = #{productId}
          AND enabled_flag = 0
    </select>

    <select id="getProductPosterByProductId" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmProductPosterVO">
        SELECT *
        from sm_product_poster
        WHERE productId = #{productId}
          AND enabled_flag = 0
        LIMIT 1
    </select>

    <select id="getGlProductQuotePlan"
            resultType="com.cfpamf.ms.insur.weixin.pojo.vo.WxGlProductQuoteVO">
        SELECT *
        FROM sm_product_quote_plan
        WHERE spqpId = #{spqpId}
    </select>

    <delete id="deleteWxGlProductQuotePlan">
        UPDATE sm_product_quote_plan
        SET enabled_flag = 1
        WHERE spqpId = #{spqpId}
    </delete>

    <select id="listProductSalesOrgsByOrgPath" resultType="java.lang.Integer">
        SELECT DISTINCT t1.product_id
        FROM sm_plan_sales_org t1
        LEFT JOIN sm_product t2 ON t1.product_id = t2.id
        WHERE
        t1.enabled_flag = 0
        AND t2.state=1
        <if test='orgName != null'>
            AND (t1.org_name = #{orgName} OR t1.org_path IS NULL)
        </if>
        <if test='orgName == null'>
            AND t1.org_path IS NULL
        </if>
    </select>
    <select id="listProductByOrg" resultType="com.cfpamf.ms.insur.weixin.pojo.dto.homepage.SalesProductDTO">
        SELECT  t1.product_id,
                min(t3.min_premium) as min_premium
        FROM sm_plan_sales_org t1
        LEFT JOIN sm_product t2 ON t1.product_id = t2.id
        LEFT JOIN sm_plan t3 ON t1.plan_id = t3.id
        WHERE
        t1.enabled_flag = 0
        AND t2.state=1
        and t2.id not in (select product_id from sm_product_label where label_type='renewal_product' and label_value='Y')
        <if test='orgName != null'>
            AND (t1.org_name = #{orgName} OR t1.org_path IS NULL)
        </if>
        <if test='orgName == null'>
            AND t1.org_path IS NULL
        </if>
        group by t1.product_id
    </select>
    <!--    <select id="listProductSalesOrgsByOrgPath" resultType="java.lang.Integer">-->
    <!--        SELECT DISTINCT t1.productId-->
    <!--        FROM sm_product_sales_org t1-->
    <!--        LEFT JOIN sm_product t2 ON t1.productId = t2.id-->
    <!--        WHERE-->
    <!--        t1.enabled_flag = 0-->
    <!--        AND t2.state=1-->
    <!--        <if test='orgName != null'>-->
    <!--            AND (t1.orgName = #{orgName} OR t1.orgPath IS NULL)-->
    <!--        </if>-->
    <!--        <if test='orgName == null'>-->
    <!--            AND t1.orgPath IS NULL-->
    <!--        </if>-->
    <!--    </select>-->

    <select id="listProductSalesOrgsByProductId" resultType="com.cfpamf.ms.insur.weixin.pojo.vo.WxProductSalesOrgVO">
        SELECT productId, hrOrgId, orgName, orgPath
        FROM sm_product_sales_org
        WHERE productId = #{productId}
          AND enabled_flag = 0
    </select>
    <select id="listProductSalePlanByProductId" resultType="com.cfpamf.ms.insur.weixin.pojo.vo.WxProductSalesOrgVO">
        SELECT distinct product_id , hr_org_id, org_name, org_path
        FROM sm_plan_sales_org
        WHERE product_id = #{productId}
          AND enabled_flag = 0
    </select>

    <select id="listWxProductPlanCoverages" resultType="com.cfpamf.ms.insur.weixin.pojo.vo.WxPlanCoverageVO">
        SELECT t1.spcaId, t1.cvgAmount, t1.cvgNotice, t2.cvgType, t2.cvgItemName, t2.cvgNameDetail
        FROM sm_product_coverage_amount t1
                 LEFT JOIN sm_product_coverage t2 ON t1.spcId = t2.spcId
        WHERE t1.planId = #{planId}
          AND t1.enabled_flag = 0
          AND t2.enabled_flag = 0
    </select>

    <select id="getProductClauseContent" resultType="java.lang.String">
        select content from sm_product_clause_content where productId = #{productId}
 and enabled_flag = 0
    </select>
    <select id="getProductSelectionList" resultType="com.cfpamf.ms.insur.weixin.pojo.vo.product.ProductSelectionVO">
        SELECT
            v.productName, v.productId
        FROM
            sm_product_history v,
            (
                SELECT
                    MAX( h.version ) version,
                    productId
                FROM
                    sm_product_history h
                WHERE h.enabled_flag = 0
                GROUP BY
                    h.productId
            ) temp
        WHERE
            temp.version = v.version
          AND temp.productId = v.productId
          AND v.enabled_flag = 0
    </select>

    <select id="getCoverageAmountList" resultType="com.cfpamf.ms.insur.weixin.pojo.po.product.CoverageAmountPo">
        SELECT
            a.productId,
            a.spcaId,
            a.spcId,
            a.cvgNotice,
            a.cvgAmount,
            c.cvgItemName
        FROM sm_product_coverage_amount a left join sm_product_coverage c
        on a.spcId=c.spcId and c.enabled_flag=0
        WHERE a.productId = #{productId}
        AND a.enabled_flag = 0
    </select>
</mapper>
