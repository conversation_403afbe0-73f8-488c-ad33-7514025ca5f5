<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.weixin.dao.safes.WxProductHistoryMapper">

    <select id="listWxProducts" resultType="com.cfpamf.ms.insur.weixin.pojo.vo.WxProductListVO">
        SELECT t1.id, t1.channel, t1.productAttrCode, t1.productName, t1.productFeature, t1.thumbnailImageUrl,
        t1.minAmount, t1.saleQty,
        t1.apiType,t1.h5Url, t1.sortNum
        FROM sm_product t1
        WHERE t1.state=1 AND t1.enabled_flag=0
        AND t1.productAttrCode in ('person','employer')
        AND t1.onlineChannel LIKE '%xiangzhu%'
        <if test='productCategoryId != null'>
            AND (t1.productCategoryId = #{productCategoryId} OR t1.productCategoryId LIKE CONCAT(#{productCategoryId},
            ',%')
            OR t1.productCategoryId LIKE CONCAT('%,',#{productCategoryId}, ',%') OR t1.productCategoryId LIKE
            CONCAT('%,',#{productCategoryId}))
        </if>
        <if test='channel != null'>
            AND t1.channel = #{channel}
        </if>
        <if test='companyId != null'>
            AND t1.companyId = #{companyId}
        </if>
        <if test='salesProductIds != null'>
            AND t1.id IN
            <foreach item="item" index="index" open="(" separator="," close=")" collection="salesProductIds">
                #{item}
            </foreach>
        </if>
        <if test='keyword != null'>
            AND MATCH(t1.productName, t1.productTags, t1.productFeature) AGAINST(CONCAT(#{keyword},'*') IN BOOLEAN MODE)
        </if>
        ORDER BY sortNum ASC,t1.companyId ASC, t1.productName ASC
    </select>

    <select id="listWxProductsWithGroup" resultType="com.cfpamf.ms.insur.weixin.pojo.vo.WxProductListVO">
        SELECT t1.id, t1.channel, t1.productAttrCode, t1.productName, t1.productFeature, t1.thumbnailImageUrl,
        t1.minAmount, t1.saleQty, t1.sortNum
        ,t1.h5Url,
        t1.apiType
        FROM sm_product t1
        WHERE t1.state=1 AND t1.enabled_flag=0
        AND t1.onlineChannel LIKE '%xiangzhu%'
        <if test='salesProductIds != null'>
            AND t1.id IN
            <foreach item="item" index="index" open="(" separator="," close=")" collection="salesProductIds">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="listWxHotProducts" resultType="com.cfpamf.ms.insur.weixin.pojo.vo.WxProductListVO">
        SELECT t1.id, t1.channel, t1.productName, t1.productFeature, t1.thumbnailImageUrl, t1.minAmount, t1.sortNum ,t1.h5Url,
        t1.productAttrCode, t1.apiType
        FROM sm_product t1

        WHERE t1.state=1 AND t1.enabled_flag=0 AND t1.onlineChannel LIKE '%xiangzhu%'
        AND t1.productAttrCode in ('person','employer')
        <if test='channel != null'>
            AND t1.channel = #{channel}
        </if>
        <if test='salesProductIds != null'>
            AND t1.id IN
            <foreach item="item" index="index" open="(" separator="," close=")" collection="salesProductIds">
                #{item}
            </foreach>
        </if>
        ORDER BY t1.monthSaleCount DESC, t1.productName ASC
        LIMIT 5
    </select>

    <select id="listWxActiveProducts" resultType="com.cfpamf.ms.insur.weixin.pojo.vo.WxProductListVO">
        SELECT t1.id, t1.channel, t1.productName, t1.productFeature, t1.thumbnailImageUrl, t1.minAmount,  t1.sortNum ,t1.h5Url,
        t1.productAttrCode,t1.apiType
        FROM sm_product t1
        WHERE t1.state=1 AND t1.enabled_flag=0 AND t1.onlineChannel LIKE '%xiangzhu%'
        AND t1.productAttrCode in ('person','employer')
        and t1.activeFlag = 1
        <if test='channel != null'>
            AND t1.channel = #{channel}
        </if>
        <if test='salesProductIds != null'>
            AND t1.id IN
            <foreach item="item" index="index" open="(" separator="," close=")" collection="salesProductIds">
                #{item}
            </foreach>
        </if>
        ORDER BY t1.monthSaleCount DESC, t1.productName ASC
    </select>

    <select id="getWxProductDetailById" resultType="com.cfpamf.ms.insur.weixin.pojo.vo.WxProductDetailVO">
        SELECT t1.id,
               t1.version,
               t1.channel,
               t1.productName,
               t1.productFeature,
               t1.productTags   AS productTagsJoin,
               t1.introduceImageUrl,
               t1.companyId,
               t2.companyLogoImageUrl,
               t2.companyName,
               t1.attentions,
               t1.buyLimit,
               t1.effectWaitingDayMin,
               t1.effectWaitingDayMax,
               t1.attentions,
               t1.healthNotification,
               t1.minAmount,
               t1.productSpecialsJoin,
               t1.glProductNotice,
               t2.reportPhoneNo AS companyReportPhoneNo,
               t1.h5Url,
               t1.apiType,
               t1.headImageUrl,
               t1.productShortName,
               t1.custNotify
        FROM sm_product_history t1
                 LEFT JOIN sm_company t2 ON t1.companyId = t2.id
        WHERE t1.productId = #{id}
          and t1.version = #{version}
          AND t1.enabled_flag = 0
    </select>

    <select id="listActivityProducts" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmActivityProductVO">
        SELECT t1.*, t1.id AS productId,
        t4.companyName AS companyName
        FROM sm_product t1
        LEFT JOIN sm_company t4 on t1.companyId = t4.id AND t4.enabled_flag=0
        WHERE t1.enabled_flag = 0 AND t1.productAttrCode = 'person' AND t1.state = 1
        <if test='productName != null'>
            AND t1.productName LIKE CONCAT('%',#{productName},'%')
        </if>
        <if test='categoryId != null'>
            AND (t1.productCategoryId = #{categoryId} OR t1.productCategoryId LIKE CONCAT(#{categoryId}, ',%')
            OR t1.productCategoryId LIKE CONCAT('%,',#{categoryId}, ',%') OR t1.productCategoryId LIKE
            CONCAT('%,',#{categoryId}))
        </if>
        <if test='companyId != null'>
            AND t1.companyId =#{companyId}
        </if>
        <if test='productIds != null'>
            AND t1.id IN
            <foreach item="item" index="index" open="(" separator="," close=")" collection="productIds">
                #{item}
            </foreach>
        </if>
        ORDER BY ,t1.id ASC
    </select>

    <select id="listWxRenewProductsProductId" resultType="com.cfpamf.ms.insur.weixin.pojo.vo.WxProductRenewVO">
        SELECT t2.id          AS renewProductId,
               t2.productName AS renewProductName,
               t3.state       AS oldProductState
        FROM sm_product_renew t1
                 LEFT JOIN sm_product t2
                           ON t2.id = t1.renewProductId
                 LEFT JOIN sm_product t3
                           ON t3.id = t1.productId
        WHERE t1.productId = #{productId}
          AND t1.enabled_flag = 0
    </select>

    <select id="listWxGroupProducts" resultType="com.cfpamf.ms.insur.weixin.pojo.vo.WxGlProductListVO">
        SELECT
        id AS productId,
        productName,
        thumbnailImageUrl,
        glUwaFrom,
        glUwaTo,
        glOcpnGroup,
        effectWaitingDayMin,
        effectWaitingDayMax,
        minAmount
        FROM
        sm_product
        WHERE productAttrCode = 'group' AND state = 1
        <if test='salesProductIds != null'>
            AND id IN
            <foreach item="item" index="index" open="(" separator="," close=")" collection="salesProductIds">
                #{item}
            </foreach>
        </if>
        ORDER BY create_time ASC
    </select>

    <select id="getGlProductById"
            resultType="com.cfpamf.ms.insur.weixin.pojo.vo.WxGlProductDetailVO">
        SELECT t1.*, t1.id AS productId, t2.companyName
        FROM sm_product t1
                 LEFT JOIN sm_company t2 ON t1.companyId = t2.id
        WHERE t1.id = #{productId}
    </select>

    <select id="listProductClausesByProductId" resultType="com.cfpamf.ms.insur.weixin.pojo.vo.WxProductClauseVO">
        SELECT *, id AS productId
        FROM sm_product_clause_history
        WHERE productId = #{productId} and version = #{version}
          AND enabled_flag = 0
        ORDER BY sort ASC, id ASC
    </select>

    <select id="listProductCoverages" resultType="com.cfpamf.ms.insur.weixin.pojo.vo.WxProductCoverageVO">
        SELECT *
        FROM sm_product_coverage
        WHERE productId = #{productId}
          and enabled_flag = 0;
    </select>

    <select id="listProductQuoteLimit" resultType="com.cfpamf.ms.insur.weixin.pojo.vo.WxProductQuoteLimitItemVO">
        SELECT *
        FROM sm_product_quote_limit
        WHERE productId = #{productId}
          AND enabled_flag = 0
    </select>

    <select id="getProductPosterByProductId" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmProductPosterVO">
        SELECT *
        from sm_product_poster
        WHERE productId = #{productId}
          AND enabled_flag = 0
        LIMIT 1
    </select>

    <select id="getGlProductQuotePlan"
            resultType="com.cfpamf.ms.insur.weixin.pojo.vo.WxGlProductQuoteVO">
        SELECT *
        FROM sm_product_quote_plan
        WHERE spqpId = #{spqpId}
    </select>

    <delete id="deleteWxGlProductQuotePlan">
        UPDATE sm_product_quote_plan
        SET enabled_flag = 1
        WHERE spqpId = #{spqpId}
    </delete>

    <select id="listProductSalesOrgsByOrgPath" resultType="java.lang.Integer">
        SELECT DISTINCT t1.product_id
        FROM sm_plan_sales_org t1
        LEFT JOIN sm_product t2 ON t1.product_id = t2.id
        WHERE
        t1.enabled_flag = 0
        AND t2.state=1
        <if test='orgName != null'>
            AND (t1.org_name = #{orgName} OR t1.org_path IS NULL)
        </if>
        <if test='orgName == null'>
            AND t1.org_path IS NULL
        </if>
    </select>
    <!--    <select id="listProductSalesOrgsByOrgPath" resultType="java.lang.Integer">-->
    <!--        SELECT DISTINCT t1.productId-->
    <!--        FROM sm_product_sales_org t1-->
    <!--        LEFT JOIN sm_product t2 ON t1.productId = t2.id-->
    <!--        WHERE-->
    <!--        t1.enabled_flag = 0-->
    <!--        AND t2.state=1-->
    <!--        <if test='orgName != null'>-->
    <!--            AND (t1.orgName = #{orgName} OR t1.orgPath IS NULL)-->
    <!--        </if>-->
    <!--        <if test='orgName == null'>-->
    <!--            AND t1.orgPath IS NULL-->
    <!--        </if>-->
    <!--    </select>-->

    <select id="listProductSalesOrgsByProductId" resultType="com.cfpamf.ms.insur.weixin.pojo.vo.WxProductSalesOrgVO">
        SELECT productId, hrOrgId, orgName, orgPath
        FROM sm_product_sales_org
        WHERE productId = #{productId}
          AND enabled_flag = 0
    </select>
    <select id="listProductSalePlanByProductId" resultType="com.cfpamf.ms.insur.weixin.pojo.vo.WxProductSalesOrgVO">
        SELECT distinct product_id , hr_org_id, org_name, org_path
        FROM sm_plan_sales_org
        WHERE product_id = #{productId}
          AND enabled_flag = 0
    </select>

    <select id="listWxProductPlanCoverages" resultType="com.cfpamf.ms.insur.weixin.pojo.vo.WxPlanCoverageVO">
        SELECT t1.spcaId, t1.cvgAmount, t1.cvgNotice, t2.cvgType, t2.cvgItemName, t2.cvgNameDetail
        FROM sm_product_coverage_amount_history t1
                 LEFT JOIN sm_product_coverage_history t2 ON t1.spcId = t2.spcId
                        and t1.version = t2.version
        WHERE t1.planId = #{planId} and t1.version = #{version}
          AND t1.enabled_flag = 0
          AND t2.enabled_flag = 0
    </select>

    <select id="getProductClauseContent" resultType="java.lang.String">
        select content from sm_product_clause_content_history where productId = #{productId}
                                                                and version=#{version}
 and enabled_flag = 0
    </select>

</mapper>
