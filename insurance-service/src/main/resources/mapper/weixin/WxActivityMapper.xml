<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.weixin.dao.safes.WxActivityMapper">
    <resultMap id="SystemActivityMap" type="com.cfpamf.ms.insur.weixin.pojo.vo.WxActivityVO">
        <result column="saId" property="saId"/>
        <result column="title" property="title"/>
        <result column="imageUrl" property="imageUrl"/>
        <result column="startTime" property="startTime"/>
        <result column="endTime" property="endTime"/>
        <result column="content" property="content"/>
        <result column="regions" property="regions" typeHandler="com.cfpamf.ms.insur.base.dao.MySqlJsonHandler"/>
        <result column="products" property="products" typeHandler="com.cfpamf.ms.insur.base.dao.MySqlJsonHandler"/>
    </resultMap>

    <select id="listWxActivitys" resultType="com.cfpamf.ms.insur.weixin.pojo.vo.WxActivityListVO">
        SELECT * FROM system_activity WHERE enabled_flag = 0 AND activeFlag = 1
        <![CDATA[ AND endTime >= CURRENT_TIMESTAMP() ]]>
        <if test="regionName!=null">
            AND (JSON_CONTAINS( regions->"$[*]" ,'"${regionName}"', "$") OR JSON_CONTAINS( regions->"$[*]" ,'"无限制"',
            "$"))
        </if>
        <if test="regionName==null">
            AND JSON_CONTAINS( regions->"$[*]" ,'"无限制"', "$")
        </if>
        ORDER BY startTime DESC
    </select>

    <select id="getSystemActivityById" resultType="com.cfpamf.ms.insur.weixin.pojo.vo.WxActivityVO" resultMap="SystemActivityMap">
        SELECT * FROM system_activity WHERE enabled_flag = 0 AND saId = #{saId}
    </select>
</mapper>