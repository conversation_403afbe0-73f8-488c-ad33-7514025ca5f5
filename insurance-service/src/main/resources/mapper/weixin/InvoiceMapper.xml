<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.weixin.dao.safes.InvoiceMapper">
    <select id="queryPolicyItem" resultType="com.cfpamf.ms.insur.weixin.pojo.vo.group.PolicyItem">
        SELECT
             o.fhOrderId as orderId,
             o.policy_no as policyNo,
             null as endorsementNo,
             o.totalAmount as amount
        FROM sm_order o
        where o.fhOrderId=#{orderId}
        AND NOT exists (select 1 from sm_policy_invoice_relation r where o.fhOrderId=r.order_id and r.endorsement_no='000')
        UNION ALL
        SELECT
             e.raw_order_id as orderId,
             e.policy_no as policyNo,
             e.endorsement_no as endorsementNo,
             e.amount as amount
        FROM sm_order_endor e
        where e.raw_order_id=#{orderId}
        AND   e.status=2
        AND NOT exists (select 1 from sm_policy_invoice_relation r where e.endorsement_no=r.endorsement_no)
        AND e.amount != 0
    </select>
</mapper>
