<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.channel.BankMapper">

    <select id="queryByChannel" resultType="com.cfpamf.ms.insur.admin.pojo.po.channel.Bank">
        select *
        from sm_bank
        where channel=#{channel}
        <if test="keyword!=null and keyword!='' ">
            and name like concat("%",#{keyword},"%")
        </if>
        and status=0
    </select>
</mapper>
