<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.weixin.dao.safes.EndorMapper">

    <select id="queryByEndorNo" resultType="com.cfpamf.ms.insur.weixin.pojo.dto.policy.Endor">
        SELECT
            e.*
        FROM sm_order_endor e
        WHERE e.endorsement_no = #{endorsementNo}
        <if test="channel!=null and channel!='' ">
            and e.channel = #{channel}
        </if>
    </select>

    <update id="updateByApplyId" parameterType="com.cfpamf.ms.insur.weixin.pojo.dto.policy.Endor">
        update sm_order_endor
        set
            endorsement_no=#{endor.endorsementNo},
            status=#{endor.status},
            e_policy_url=#{endor.ePolicyUrl},
            update_time=now()
        where apply_endorsement_no=#{endor.applyEndorsementNo}
        and   channel = #{endor.channel}
    </update>

    <update id="updateByEndorsementNo" parameterType="com.cfpamf.ms.insur.weixin.pojo.dto.policy.Endor">
        update sm_order_endor
        set
        <if test="data.status!=null">
            status=#{data.status},
        </if>
        <if test="data.ePolicyUrl!=null">
            e_policy_url=#{data.ePolicyUrl},
        </if>
        update_time=now()
        where endorsement_no = #{data.endorsementNo}
        <if test="data.policyNo!=null and data.policyNo!=''">
            and   policy_no = #{data.policyNo}
        </if>
        and   channel = #{data.channel}
    </update>

    <select id="queryEndor" resultType="com.cfpamf.ms.insur.weixin.pojo.dto.policy.Endor">
        select
            *
        from sm_order_endor t
        where channel=#{channel}
        <if test=" endorId != null ">
            and order_id = #{endorId}
        </if>
        <if test=" endorsementNo != null ">
            and endorsement_no = #{endorsementNo}
        </if>
    </select>

    <!-- 批改订单只展示:待支付,保司审核处理中(线下转账),已撤销;这三个状态 -->
    <select id="queryEndorByOrderInfo" resultType="com.cfpamf.ms.insur.weixin.pojo.dto.policy.Endor">
        select
            t.*
        from sm_order_endor t
        left join sm_order t1 on t1.fhorderId = t.raw_order_id
        LEFT JOIN sm_product t4 ON t4.id=t1.productId
        where t.status in ('0','-1','20')
        <if test='endorStatus != null'>
            and t.status=#{endorStatus}
        </if>
        <if test='userId != null'>
            and t1.customerAdminId=#{userId}
        </if>
        <if test='agentId != null'>
            and t1.agentId=#{agentId}
        </if>
        <if test=' orderId != null and orderId !=""'>
            and t.order_id = #{orderId}
        </if>
        <if test='policyNo != null and policyNo !=""'>
            and t.policy_no = #{policyNo}
        </if>
        <if test=" channel != null ">
            and t.channel = #{channel}
        </if>
        <if test='companyId != null and companyId != ""'>
            AND t4.companyId = #{companyId}
        </if>
        <if test='productId != null and productId != ""'>
            AND t4.id = #{productId}
        </if>

    </select>


    <select id="listByEmptyEPolicyUrl" resultType="com.cfpamf.ms.insur.weixin.pojo.dto.policy.Endor">
        select
            *
        from sm_order_endor
        where status=2
        and (e_policy_url is null or e_policy_url = '')
        <if test="endorsementNo!=null and endorsementNo!=''">
            and endorsement_no=#{endorsementNo}
        </if>
        limit #{offset},#{size}
    </select>

</mapper>
