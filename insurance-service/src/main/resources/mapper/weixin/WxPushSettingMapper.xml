<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.weixin.dao.safes.WxPushSettingMapper">

    <select id="getSystemPushSetting" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SystemPushSettingVO">
        SELECT * FROM system_push_setting WHERE pushType = #{pushType} AND activeFlag = 1 AND enabled_flag = 0
        <if test="userType!=null">
            AND userType IN (#{userType}, 'all')
        </if>
        <![CDATA[  AND startTime <= #{date} ]]>
        <![CDATA[  AND #{date} <= endTime
        ]]>
    </select>
</mapper>