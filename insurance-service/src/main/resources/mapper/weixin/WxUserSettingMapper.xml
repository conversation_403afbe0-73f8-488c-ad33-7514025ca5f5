<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.weixin.dao.safes.WxUserSettingMapper">

    <insert id="insertUserShowCmsSetting" useGeneratedKeys="true">
        INSERT INTO wx_user_setting (userId,stCode,stValue) VALUES (#{userId}, #{stCode}, #{stValue})
    </insert>

    <select id="getUserShowCmsSetting" resultType="com.cfpamf.ms.insur.weixin.pojo.vo.WxUserSettingVO">
        SELECT * FROM wx_user_setting WHERE userId = #{userId} AND stCode=#{stCode} AND enabled_flag = 0 LIMIT 1
    </select>

    <update id="deleteUserShowCmsSetting">
        UPDATE wx_user_setting SET enabled_flag = 1 WHERE userId = #{userId} AND stCode=#{stCode}
    </update>
</mapper>