<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.weixin.dao.safes.WxOrderMapper">

    <select id="getOrderInsuredByInsId" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmOrderInsuredVO">
        SELECT t1.*, t2.planFactorPriceOptionJson, t4.companyName, t3.channel, t5.agentName, t5.agentMobile, t4.companyLogoThumbUrl, t4.reportPhoneNo
        FROM sm_order_insured t1
                 LEFT JOIN sm_order t2 ON t1.fhOrderId = t2.fhOrderId
                 LEFT JOIN sm_product t3 ON t2.productId = t3.id
                 LEFT JOIN sm_company t4 ON t4.id = t3.companyId
                 LEFT JOIN sm_agent t5 ON t5.agentId = t2.agentId
        WHERE t1.id = #{id} LIMIT 1
    </select>
    <select id="getOrderInsuredByEndorsementNo" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmOrderInsuredVO">
        SELECT t1.*, t2.planFactorPriceOptionJson, t4.companyName, t3.channel, t5.agentName, t5.agentMobile
        FROM sm_order_insured t1
                 LEFT JOIN sm_order t2 ON t1.fhOrderId = t2.fhOrderId
                 LEFT JOIN sm_product t3 ON t2.productId = t3.id
                 LEFT JOIN sm_company t4 ON t4.id = t3.companyId
                 LEFT JOIN sm_agent t5 ON t5.agentId = t2.agentId
                 left join sm_order_item t6 on t6.fh_order_id = t1.fhOrderId
        WHERE t6.th_endorsement_no = #{endorsementNo} and t1.downloadURL is not null LIMIT 1
    </select>

    <select id="getOrderInsured" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmOrderInsuredVO"
            parameterType="com.cfpamf.ms.insur.weixin.pojo.query.order.OrderDetailQuery">
        SELECT t1.*, t2.planFactorPriceOptionJson, t4.companyName, t3.channel, t5.agentName, t5.agentMobile
        FROM sm_order_insured t1
        LEFT JOIN sm_order t2 ON t1.fhOrderId = t2.fhOrderId
        LEFT JOIN sm_product t3 ON t2.productId = t3.id
        LEFT JOIN sm_company t4 ON t4.id = t3.companyId
        LEFT JOIN sm_agent t5 ON t5.agentId = t2.agentId
        WHERE t1.id = #{id}
        <if test='userId != null'>
            AND t2.customerAdminId=#{userId}
        </if>
        <if test='agentId != null'>
            AND t2.agentId=#{agentId}
        </if>
        LIMIT 1
    </select>

    <select id="getOrderInsuredByOrderId" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmOrderInsuredVO">
        SELECT t1.*, t2.planFactorPriceOptionJson, t4.companyName, t3.channel, t5.agentName, t5.agentMobile
        FROM sm_order_insured t1
        LEFT JOIN sm_order t2 ON t1.fhOrderId = t2.fhOrderId
        LEFT JOIN sm_product t3 ON t2.productId = t3.id
        LEFT JOIN sm_company t4 ON t4.id = t3.companyId
        LEFT JOIN sm_agent t5 ON t5.agentId = t2.agentId
        WHERE t2.fhorderid = #{orderId}
    </select>


    <select id="listOrderInsuredPersonNamesByOrderId" resultType="com.cfpamf.ms.insur.base.bean.IdName">
        SELECT t1.fhOrderId AS id, t1.personName AS name
        FROM sm_order_insured t1
        WHERE t1.fhOrderId IN
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="getWxOrderChannel" resultType="com.cfpamf.ms.insur.weixin.pojo.vo.WxOrderChannelVO">
        select channel, subChannel
        FROM sm_order
        WHERE fhOrderId = #{fhOrderId}
    </select>


    <select id="countWxUnPayOrder" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM sm_order t1
                 LEFT JOIN sm_order_insured t2 ON t1.fhOrderId = t2.fhOrderId
        WHERE t1.wxOpenId = #{wxOpenId}
          AND t2.appStatus = '2'
          AND t1.create_time >= #{fromDate}
    </select>


    <insert id="deleteWxOrder" useGeneratedKeys="true">
        INSERT INTO wx_user_order (fhOrderId, status, update_by, create_time, update_time) VALUE ( #{fhOrderId}, 1, #{userId}, CURRENT_TIMESTAMP (), CURRENT_TIMESTAMP ());
    </insert>

    <select id="listWxPolicyCmsList" resultType="com.cfpamf.ms.insur.weixin.pojo.vo.WxUserCmsListVo">
        SELECT t1.id, t1.fhOrderId, t1.policyNo, t1.create_time, t3.productName, t4.planName, t1.insPersonName AS
        insuredPersonName,t1.insIdNumber as insuredIdNumber,
        t1.appStatus,
        t1.accountTime,
        case when l.label_value='Y' then d.amount else t1.totalAmount end totalAmount,
        if(t5.id is null ,case when l.label_value='Y' then d.payment_amount else t1.paymentAmount end,case when
        l.label_value='Y' then d.payment_amount else t1.paymentAmount end * .7) AS paymentCommission,
        if(t5.id is null,false,true) talkOrder,
        if(t12.id is null,false,true) distributionOrder,
        if(t3.long_insurance = 1,false,true) as longInsuranceOrder,
        if(d.term_num is null ,case when t3.long_insurance = 1 then null else 1 end ,d.term_num) as termNum,
        t5.invite_name inviteName,
        t5.invite_type inviteType

        FROM sm_order_commission t1
        LEFT JOIN sm_product t3 ON t3.id = t1.productId
        LEFT JOIN sm_product_label l on t1.productId = l.product_id and l.label_type='calc_new_commission' and
        l.enabled_flag = 0
        LEFT JOIN sm_plan t4 ON t4.id=t1.planId

        left join sm_order_talk t5 on t1.fhOrderId = t5.fh_order_id
        left join sm_commission_detail d on d.order_id = t1.fhorderid
        and d.insured_id_number = t1.insIdNumber and d.policy_no = t1.policyNo and d.policy_status = t1.appStatus
        left join sm_order_distribution t12 on t12.fh_order_id = t1.fhOrderId and t12.id_number = t1.insIdNumber
        WHERE t1.recommendId=#{userId}
        <if test='startTime != null'>
            <![CDATA[ AND t1.accountTime>=#{startTime}]]>
        </if>
        <if test='endTime != null'>
            <![CDATA[ AND t1.accountTime<=#{endTime}]]>
        </if>
        <if test='channel != null'>
            AND t3.channel=#{channel}
        </if>

        <choose>
            <when test="orderType != null and orderType == 0">
                and t3.long_insurance = 1 and t12.id is null and t5.id is null
            </when>
            <when test="orderType != null and orderType == 1">
                and t3.long_insurance = 1 and t12.id is not null
            </when>
            <when test="orderType != null and orderType == 2">
                and t3.long_insurance = 1 and t5.id is not null
            </when>
            <when test="orderType != null and orderType == 3">
                and t3.long_insurance = 0
            </when>
        </choose>



        ORDER BY t1.accountTime DESC
    </select>

    <select id="listWxPolicyList" resultType="com.cfpamf.ms.insur.weixin.pojo.vo.WxPolicyListVo">
        SELECT t3.id, t4.channel, t1.fhOrderId,
        (case when t7.total_amount is not null then t7.total_amount else t1.unitPrice * t1.qty
        end) AS totalAmount,

        t1.payStatus, t3.appStatus,
        t1.create_time, t2.personName AS applicantPersonName, t3.personName AS insuredPersonName,
        t3.policyNo AS policyNo, t4.productName, t5.planName, t1.startTime, t1.endTime, t3.appStatus, t6.agentId,
        t6.agentName,t3.downloadURL, t4.id as productId
        FROM sm_order t1
        <if test='(userId != null or customerAdminId != null) and (claimable != null and claimable)'>
            FORCE INDEX (customerAdminId)
        </if>
        LEFT JOIN sm_order_applicant t2 ON t1.fhOrderId=t2.fhOrderId
        LEFT JOIN sm_order_insured t3 ON t1.fhOrderId=t3.fhOrderId
        left join sm_order_item t7 on t3.fhOrderId = t7.fh_order_id and t3.idNumber = t7.id_number and
        t3.appStatus=t7.app_status
        LEFT JOIN sm_product t4 ON t4.id=t1.productId
        LEFT JOIN sm_plan t5 ON t5.id=t1.planId
        LEFT JOIN sm_agent t6 ON t6.agentId=t1.agentId
        WHERE t3.appStatus='1'
        <if test='userId != null'>
            AND t1.customerAdminId=#{userId}
        </if>
        <if test='customerAdminId != null'>
            AND t1.customerAdminId=#{customerAdminId}
        </if>
        <if test='agentId != null'>
            AND t1.agentId=#{agentId}
        </if>
        <if test='policyStatus != null and  policyStatus and claimable==null'>
            <![CDATA[ AND  t1.endTime >= CURRENT_TIMESTAMP() ]]>
        </if>
        <if test='policyStatus != null and !policyStatus'>
            <![CDATA[ AND  t1.endTime < CURRENT_TIMESTAMP() ]]>
        </if>

        <if test='insuredType!=null and insuredType==5 and insuredName!=null and insuredName!=""'>
            and t3.personName LIKE CONCAT(#{insuredName},'%')
        </if>
        <if test='insuredType!=null and insuredType==6 and insuredName!=null and insuredName!=""'>
            and t3.idNumber LIKE CONCAT(#{insuredName},'%')
        </if>
        <if test='applicantType!=null and applicantType==7 and applicantName!=null and applicantName!=""'>
            and t2.personName LIKE CONCAT(#{applicantName},'%')
        </if>
        <if test='applicantType!=null and applicantType==8 and applicantName!=null and applicantName!=""'>
            and t2.idNumber LIKE CONCAT(#{applicantName},'%')
        </if>
        <if test='orderId != null and orderId !=""'>
            AND t1.fhOrderId LIKE CONCAT(#{orderId},'%')
        </if>
        <if test='policyNo != null and policyNo !=""'>
            AND t3.policyNo LIKE CONCAT(#{policyNo},'%')
        </if>

        <if test='startTime != null '>
            <![CDATA[ AND  t1.create_time >= #{startTime} ]]>
        </if>
        <if test='endTime  != null'>
            <![CDATA[ AND  t1.create_time <  #{endTime}  ]]>
        </if>
        <if test='claimable != null and claimable'>
            <![CDATA[  AND t1.startTime < CURRENT_TIMESTAMP()  AND date_add(t1.endTime, INTERVAL 2 YEAR) > CURRENT_TIMESTAMP()  ]]>
            <if test="fuzzyKey != null and fuzzyKey != ''">
                AND (
                    t3.policyNo LIKE CONCAT(#{fuzzyKey},'%')
                    OR t2.personName LIKE CONCAT(#{fuzzyKey},'%')
                    OR t3.personName LIKE CONCAT(#{fuzzyKey},'%')
                    )
            </if>
        </if>
        <if test='idNumber != null and idNumberType != null and idNumberType == 0'>
            AND t2.idNumber = #{idNumber}
        </if>
        <if test='idNumber != null and idNumberType != null and idNumberType == 1'>
            AND t3.idNumber = #{idNumber}
        </if>
        <if test='channel != null'>
            AND t4.channel=#{channel}
        </if>
        ORDER BY t1.create_time DESC
    </select>
    <select id="queryTemporaryOrderList" resultType="com.cfpamf.ms.insur.weixin.pojo.vo.order.OrderDraftList">
        SELECT
            t1.order_id as orderId,
            t4.channel,
            t1.create_time as createTime,
            t4.productName,
            t4.id as productId,
            t4.thumbnailImageUrl,
            t8.companyName,
            t1.basic_info,
            t1.product,
            t1.applicant,
            t4.productAttrCode as productType,
            t1.basic_info as basicInfo
        FROM sm_order_draft t1
        LEFT JOIN sm_product t4 ON t4.id=t1.product_Id
        LEFT JOIN sm_company t8 ON t4.companyId=t8.id
        WHERE
        1=1
        <if test="userId !=null ">
            and t1.user_id = #{userId}
        </if>
        <if test='insuredName!=null and insuredName!=""'>
            and t1.insured LIKE CONCAT('%',#{insuredName},'%')
        </if>
        <if test='applicantName!=null and applicantName!=""'>
            and t1.applicant LIKE CONCAT('%',#{applicantName},'%')
        </if>
        <if test='orderId != null and orderId !=""'>
            AND t1.order_id LIKE CONCAT('%',#{orderId},'%')
        </if>

        ORDER BY t1.create_time DESC
    </select>

    <select id="listWxOrderList" resultType="com.cfpamf.ms.insur.weixin.pojo.vo.WxOrderListVo">
        SELECT MIN(t3.id) AS id,
        t4.channel,
        t1.fhOrderId,
        t1.totalAmount,
        t1.paymentTime,
        t1.payStatus,
        t1.create_time ,t2.personName AS applicantPersonName,
        t4.productName,
        t5.planName,
        t7.agentId,
        t7.agentName,
        DATE_FORMAT(t1.endTime ,'%Y-%m-%d %H:%i:%s') as endTime,
        t3.appStatus,
        t4.id as productId,
        t4.productAttrCode as productType,
        orbi.bind_status as bindStatus,
        t4.thumbnailImageUrl,
        t10.visit_status as returnVisitStatus,
        ifnull(t10.need_visit, 0) as needVisit,
        t1.qty as insuredNum,
        t3.policyNo
        FROM sm_order t1
        LEFT JOIN sm_order_applicant t2 ON t1.fhOrderId=t2.fhOrderId
        LEFT JOIN sm_order_insured t3 ON t1.fhOrderId=t3.fhOrderId
        LEFT JOIN sm_product t4 ON t4.id=t1.productId
        LEFT JOIN sm_plan t5 ON t5.id=t1.planId
        left join sm_order_renew_bind_info orbi on t3.fhorderid = orbi.fh_order_id and t3.policyNo = orbi.policy_no
        LEFT JOIN wx_user_order t6 ON t6.fhOrderId=t1.fhOrderId
        LEFT JOIN sm_order_policy t10 ON t10.fh_order_id=t1.fhOrderId
        LEFT JOIN sm_order_receiver_info t9 ON t9.fh_order_id=t1.fhOrderId
        <if test='userId != null'>
            AND t6.update_by=#{userId}
        </if>
        <if test='agentId != null'>
            AND t6.update_by=#{agentId}
        </if>
        <if test='openId != null'>
            AND t6.update_by=#{openId}
        </if>
        LEFT JOIN sm_agent t7 ON t7.agentId=t1.agentId
        WHERE t1.enabled_flag = 0
        <![CDATA[
        AND (t6.status != 1 OR t6.status IS NULL)
        AND (t1.payStatus != '0' or (t1.payStatus = '0' and DATE_SUB(CURDATE(), INTERVAL 30 DAY) <= t1.create_time ))
        ]]>
        <if test='userId != null'>
            AND t1.customerAdminId=#{userId}
        </if>
        <if test='agentId != null'>
            AND t1.agentId=#{agentId}
        </if>
        <if test='customerAdminId != null'>
            AND t1.customerAdminId=#{customerAdminId}
        </if>
        <if test='payStatus != null'>
            AND t1.payStatus=#{payStatus}
        </if>
        <if test='appStatus != null and appStatus=="4" '>
            AND t3.appStatus IN ('3', '4')
        </if>
        <if test='startTime != null '>
            <![CDATA[ AND  t1.create_time >= #{startTime} ]]>
        </if>
        <if test='endTime  != null'>
            <![CDATA[ AND  t1.create_time <  #{endTime}  ]]>
        </if>
        <if test='insuredType!=null and insuredType==5 and insuredName!=null and insuredName!=""'>
            and t3.personName LIKE CONCAT(#{insuredName},'%')
        </if>
        <if test='insuredType!=null and insuredType==6 and insuredName!=null and insuredName!=""'>
            and t3.idNumber LIKE CONCAT(#{insuredName},'%')
        </if>
        <if test='applicantType!=null and applicantType==7 and applicantName!=null and applicantName!=""'>
            and t2.personName LIKE CONCAT(#{applicantName},'%')
        </if>
        <if test='applicantType!=null and applicantType==8 and applicantName!=null and applicantName!=""'>
            and t2.idNumber LIKE CONCAT(#{applicantName},'%')
        </if>
        <if test='orderId != null and orderId !=""'>
            AND t1.fhOrderId LIKE CONCAT(#{orderId},'%')
        </if>
        <if test='policyNo != null and policyNo !=""'>
            AND t3.policyNo LIKE CONCAT(#{policyNo},'%')
        </if>

        <if test='channel != null'>
            AND t4.channel=#{channel}
        </if>

        <if test='companyId != null and companyId != ""'>
            AND t4.companyId = #{companyId}
        </if>
        <if test='productId != null and productId != ""'>
            AND t4.id = #{productId}
        </if>
        <if test='visitStatus != null and visitStatus.size > 0'>
            AND (
            t10.visit_status in
            <foreach collection="visitStatus" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
            <if test='visitStatus.contains("undo") '>
                OR t10.visit_status is null
            </if>
            )
        </if>
        <if test='bindStatus != null and bindStatus != ""'>
            AND ( orbi.bind_status = #{bindStatus}
            <if test='bindStatus == "undo"'>
                OR orbi.bind_status is null
            </if>
            )
        </if>
        <choose>
            <when test='bindAddress != null and bindAddress=="1"'>
                AND t9.id is not null
            </when>
            <when test='bindAddress != null and bindAddress=="0"'>
                AND t9.id is null
            </when>
        </choose>
        GROUP BY t1.fhOrderId
        ORDER BY t1.create_time DESC
    </select>

    <select id="listApplyUnionCorrectOrder" resultType="com.cfpamf.ms.insur.weixin.pojo.vo.WxOrderListVo">
        select t.* from
        (
            <include refid="queryApplyOrder_sql" />
            <if test='queryEndorFlag == "0"'>
                union all
                <include refid="queryCorrectOrder_sql" />
            </if>
        ) t
        order by create_time desc
    </select>

    <sql id="queryApplyOrder_sql" >
        SELECT MIN(t3.id) AS id,
        t4.channel,
        t1.fhOrderId,
        t1.totalAmount,
        t1.paymentTime,
        t1.payStatus,
        t1.create_time ,t2.personName AS applicantPersonName,
        t4.productName,
        t5.planName,
        t7.agentId,
        t7.agentName,
        DATE_FORMAT(t1.endTime ,'%Y-%m-%d %H:%i:%s') as endTime,
        t3.appStatus,
        t4.id as productId,
        t4.productAttrCode as productType,
        orbi.bind_status as bindStatus,
        t4.thumbnailImageUrl,
        t10.visit_status as returnVisitStatus,
        ifnull(t10.need_visit, 0) as needVisit,
        t1.qty as insuredNum,
        t3.policyNo,
        null as endorsementNo,
        null as op_type
        FROM sm_order t1
        LEFT JOIN sm_order_applicant t2 ON t1.fhOrderId=t2.fhOrderId
        LEFT JOIN sm_order_insured t3 ON t1.fhOrderId=t3.fhOrderId
        LEFT JOIN sm_product t4 ON t4.id=t1.productId
        LEFT JOIN sm_plan t5 ON t5.id=t1.planId
        left join sm_order_renew_bind_info orbi on t3.fhorderid = orbi.fh_order_id and t3.policyNo = orbi.policy_no
        LEFT JOIN wx_user_order t6 ON t6.fhOrderId=t1.fhOrderId
        LEFT JOIN sm_order_policy t10 ON t10.fh_order_id=t1.fhOrderId
        LEFT JOIN sm_order_receiver_info t9 ON t9.fh_order_id=t1.fhOrderId
        <if test='userId != null'>
            AND t6.update_by=#{userId}
        </if>
        <if test='agentId != null'>
            AND t6.update_by=#{agentId}
        </if>
        <if test='openId != null'>
            AND t6.update_by=#{openId}
        </if>
        LEFT JOIN sm_agent t7 ON t7.agentId=t1.agentId
        WHERE t1.enabled_flag = 0
        <if test='userId != null'>
            AND t1.customerAdminId=#{userId}
        </if>
        <if test='agentId != null'>
            AND t1.agentId=#{agentId}
        </if>
        <if test='openId != null'>
            AND t1.wxOpenId=#{openId}
        </if>
        <if test='customerAdminId != null'>
            AND t1.customerAdminId=#{customerAdminId}
        </if>
        <![CDATA[
        AND (t6.status != 1 OR t6.status IS NULL)
        AND (t1.payStatus != '0' or (t1.payStatus = '0' and DATE_SUB(CURDATE(), INTERVAL 30 DAY) <= t1.create_time ))
        ]]>
        <if test='payStatus != null'>
            AND t1.payStatus=#{payStatus}
        </if>
        <if test='appStatus != null'>
            AND t3.appStatus IN ('3', '4')
        </if>
        <if test='startTime != null '>
            <![CDATA[ AND  t1.create_time >= #{startTime} ]]>
        </if>
        <if test='endTime  != null'>
            <![CDATA[ AND  t1.create_time <  #{endTime}  ]]>
        </if>
        <if test='insuredType!=null and insuredType==5 and insuredName!=null and insuredName!=""'>
            and t3.personName LIKE CONCAT(#{insuredName},'%')
        </if>
        <if test='insuredType!=null and insuredType==6 and insuredName!=null and insuredName!=""'>
            and t3.idNumber LIKE CONCAT(#{insuredName},'%')
        </if>
        <if test='applicantType!=null and applicantType==7 and applicantName!=null and applicantName!=""'>
            and t2.personName LIKE CONCAT(#{applicantName},'%')
        </if>
        <if test='applicantType!=null and applicantType==8 and applicantName!=null and applicantName!=""'>
            and t2.idNumber LIKE CONCAT(#{applicantName},'%')
        </if>
        <if test='orderId != null and orderId !=""'>
            AND t1.fhOrderId LIKE CONCAT(#{orderId},'%')
        </if>
        <if test='policyNo != null and policyNo !=""'>
            AND t3.policyNo LIKE CONCAT(#{policyNo},'%')
        </if>

        <if test='channel != null'>
            AND t4.channel=#{channel}
        </if>
        <if test='orderState != null'>
            AND t1.orderState=#{orderState}
        </if>
        <if test='companyId != null and companyId != ""'>
            AND t4.companyId = #{companyId}
        </if>
        <if test='productId != null and productId != ""'>
            AND t4.id = #{productId}
        </if>
        <if test='visitStatus != null and visitStatus.size > 0'>
            AND (
            t10.visit_status in
            <foreach collection="visitStatus" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
            <if test='visitStatus.contains("undo") '>
                OR t10.visit_status is null
            </if>
            )
        </if>
        <if test='bindStatus != null and bindStatus != ""'>
            AND ( orbi.bind_status = #{bindStatus}
            <if test='bindStatus == "undo"'>
                OR orbi.bind_status is null
            </if>
            )
        </if>
        <choose>
            <when test='bindAddress != null and bindAddress=="1"'>
                AND t9.id is not null
            </when>
            <when test='bindAddress != null and bindAddress=="0"'>
                AND t9.id is null
            </when>
        </choose>
        GROUP BY t1.fhOrderId
    </sql>

    <sql id="queryCorrectOrder_sql" >
        select
            null as id,
            t.channel,
            t.order_id as fhOrderId,
            t.amount as totalAmount,
            null as paymentTime,
            t.status as payStatus,
            t.create_time,
            null as applicantPersonName,
            t4.productName as productName,
            null as planName,
            t1.agentId,
            null as agentName,
            null as endTime,
            t.status as payStatus,
            t4.id as productId,
            t4.productAttrCode as productType,
            null as bindStatus,
            t4.thumbnailImageUrl as thumbnailImageUrl,
            null as returnVisitStatus,
            null as needVisit,
            null as insuredNum,
            t.policy_no as policyNo,
            t.endorsement_no as endorsementNo,
            t.op_type as opType
        from sm_order_endor t
        left join sm_order t1 on t1.fhorderId = t.raw_order_id
        LEFT JOIN sm_product t4 ON t4.id=t1.productId
        where t.status = #{endorStatus}
        <if test='userId != null'>
            AND t1.customerAdminId=#{userId}
        </if>
        <if test='agentId != null'>
            AND t1.agentId=#{agentId}
        </if>
        <if test='openId != null'>
            AND t1.wxOpenId=#{openId}
        </if>
        <if test=' orderId != null and orderId !=""'>
            and t.order_id = #{orderId}
        </if>
        <if test='policyNo != null and policyNo !=""'>
            and t.endorsement_no = #{policyNo}
        </if>
        <if test=" channel != null ">
            and t.channel = #{channel}
        </if>
        <if test='companyId != null and companyId != ""'>
            AND t4.companyId = #{companyId}
        </if>
        <if test='productId != null and productId != ""'>
            AND t4.id = #{productId}
        </if>
        <if test='insuredName!=null and insuredName!=""'>
            and 1!=1
        </if>
        <if test='insuredName!=null and insuredName!=""'>
            and 1!=1
        </if>
        <if test='visitStatus != null and visitStatus.size > 0'>
            and 1!=1
        </if>
        <if test='bindAddress != null and bindAddress != ""'>
            AND 1!=1
        </if>
        <if test='bindStatus != null and bindStatus != ""'>
            AND 1!=1
        </if>
    </sql>


    <select id="getWxPolicySummary" resultType="com.cfpamf.ms.insur.weixin.pojo.vo.WxCmsSmyVo">
        SELECT
        SUM(t1.qty) AS orderQty,
        SUM(t1.totalAmount ) AS orderAmount,
        SUM(if(t12.id is null,if(t5.id is null, t1.paymentAmount , t1.paymentAmount * .7),0)) AS cmsAmount
        FROM
        sm_order_commission t1
        LEFT JOIN sm_product t3
        ON t3.id = productId
        LEFT JOIN sm_plan t4
        ON t4.id = t1.planId
        left join sm_order_talk t5 on t1.fhOrderId = t5.fh_order_id
        left join sm_order_distribution t12 on t12.fh_order_id = t1.fhOrderId and t12.id_number = t1.insIdNumber
        WHERE 1=1
        <if test='userId != null'>
            AND t1.recommendId=#{userId}
        </if>
        <if test='customerAdminId != null'>
            AND t1.customerAdminId=#{customerAdminId}
        </if>
        <if test='agentId != null'>
            AND t1.agentId=#{agentId}
        </if>
        <if test='startTime != null'>
            <![CDATA[ AND t1.accountTime >= #{startTime}  ]]>
        </if>
        <if test='endTime != null'>
            <![CDATA[ AND t1.accountTime <= #{endTime} ]]>
        </if>
        <if test='idNumber != null and idNumberType != null and idNumberType==0'>
            AND t1.appIdNumber=#{idNumber}
        </if>
        <if test='idNumber != null and idNumberType != null and idNumberType==1'>
            AND t1.insIdNumber=#{idNumber}
        </if>
        <if test='channel != null'>
            AND t1.channel=#{channel}
        </if>

        <choose>
            <when test="orderType != null and orderType == 0">
                and t3.long_insurance = 1 and t12.id is null and t5.id is null
            </when>
            <when test="orderType != null and orderType == 1">
                and t3.long_insurance = 1 and t12.id is not null
            </when>
            <when test="orderType != null and orderType == 2">
                and t3.long_insurance = 1 and t5.id is not null
            </when>
            <when test="orderType != null and orderType == 3">
                and t3.long_insurance = 0
            </when>
        </choose>



    </select>

    <select id="listWxCustomerPolicyList" resultType="com.cfpamf.ms.insur.admin.pojo.vo.CustomerPolicyVO">
        SELECT t3.id, t1.fhOrderId, t1.unitPrice*t1.qty AS totalAmount, t1.payStatus, t3.appStatus, t1.create_time,
        t3.policyNo AS policyNo, t4.productName, t5.planName, t3.downloadURL,
        t6.userId AS recommendId, t6.userName AS recommendName,
        t3.personName AS insuredPersonName, t3.cellPhone AS insuredCellPhone,
        t8.personName AS apptPersonName, t8.cellPhone AS apptCellPhone,
        t7.userId AS customerAdminId, t7.userName AS customerAdminName
        FROM sm_order t1 LEFT JOIN sm_order_applicant t2 ON t1.fhOrderId=t2.fhOrderId
        LEFT JOIN sm_order_insured t3 ON t1.fhOrderId=t3.fhOrderId
        LEFT JOIN sm_order_applicant t8 ON t1.fhOrderId=t8.fhOrderId
        LEFT JOIN sm_product t4 ON t4.id=t1.productId
        LEFT JOIN sm_plan t5 ON t5.id=t1.planId
        LEFT JOIN auth_user t6 ON t6.userId=t1.recommendId AND t6.enabled_flag = 0
        LEFT JOIN auth_user t7 ON t7.userId=t1.customerAdminId AND t7.enabled_flag = 0
        WHERE t3.appStatus='1'
        <if test='type == 0'>
            AND t2.idNumber = #{idNumber}
        </if>
        <if test='type == 1'>
            AND t3.idNumber = #{idNumber}
        </if>
        ORDER BY t1.create_time DESC
    </select>

    <select id="listWxRenews" resultType="com.cfpamf.ms.insur.weixin.pojo.vo.WxRenewListVO">
        SELECT MIN(t3.id) AS id, t1.fhOrderId, t1.totalAmount, t1.payStatus, t1.create_time, t2.personName AS
        applicantPersonName, t1.startTime, t1.endTime,
        t3.personName AS insuredPersonName,t4.id as productId, t4.productName, t5.planName, TIMESTAMPDIFF(DAY,
        CURRENT_TIMESTAMP(),
        t1.endTime)+1 AS leftDays
        FROM sm_order t1 LEFT JOIN sm_order_applicant t2 ON t1.fhOrderId=t2.fhOrderId
        LEFT JOIN sm_order_insured t3 ON t1.fhOrderId=t3.fhOrderId
        LEFT JOIN sm_product t4 ON t4.id=t1.productId
        LEFT JOIN sm_plan t5 ON t5.id=t1.planId
        LEFT JOIN sm_order t6 ON t1.renewOrderId=t6.fhOrderId
        WHERE t3.appStatus = '1'
        AND TIMESTAMPDIFF(DAY, t1.startTime, t1.endTime) > 360
        AND (t1.renewOrderId IS NULL OR t6.payStatus != '2')
        <if test='customerAdminId != null'>
            AND t1.customerAdminId=#{customerAdminId}
        </if>
        <if test='agentId != null'>
            AND t1.agentId=#{agentId}
        </if>
        <if test='startTime != null '>
            <![CDATA[ AND  t1.endTime >= #{startTime} ]]>
        </if>
        <if test='endTime  != null'>
            <![CDATA[ AND  t1.endTime <=  #{endTime}  ]]>
        </if>
        <if test='keyword != null and keyword != ""'>
            AND (t1.fhOrderId LIKE CONCAT('%',#{keyword},'%') OR t2.personName LIKE CONCAT('%',#{keyword},'%') OR
            t3.personName LIKE CONCAT('%',#{keyword},'%') OR t3.policyNo LIKE CONCAT('%',#{keyword},'%') )
        </if>
        <if test='channel != null'>
            AND t4.channel=#{channel}
        </if>
        GROUP BY t1.fhOrderId
        ORDER BY t1.create_time DESC
    </select>

    <select id="listWxRenewNotifys" resultType="com.cfpamf.ms.insur.weixin.pojo.vo.WxRenewNotifyVO">
        SELECT
        t3.userName AS userName,
        t3.userMobile AS userMobile,
        t3.wxOpenId AS wxOpenId,
        COUNT(DISTINCT t1.fhOrderId) AS orderQty,
        COUNT(1) AS personQty,
        SUM(t1.qty * t1.unitPrice) AS amount
        FROM
        sm_order t1
        LEFT JOIN sm_order_insured t2
        ON t1.fhOrderId = t2.fhOrderId
        LEFT JOIN auth_user t3
        ON (t1.customerAdminId = t3.userId OR t1.agentId = t3.agentId)
        LEFT JOIN sm_product t4
        ON t4.id=t1.productId
        LEFT JOIN sm_order t5
        ON t1.renewOrderId=t5.fhOrderId
        LEFT JOIN sm_renewal_config t6 ON t1.planId = t6.plan_id and t6.enabled_flag = 0
        WHERE t2.appStatus = '1'
        AND TIMESTAMPDIFF(DAY, t1.startTime, t1.endTime) > 360
        AND t3.wxOpenId IS NOT NULL
        AND t1.fhOrderId not in ( SELECT old_order_id from sm_order_renewal WHERE STATUS = 'RENEWED' )
        <if test='startTime != null '>
            AND (DATEDIFF( #{startTime},t1.endTime) - 1) >= t6.before_expiration_day
        </if>
        <if test='endTime  != null'>
            <![CDATA[ AND DATEDIFF(#{endTime},t1.endTime) <= t6.after_expiration_day]]>
        </if>
        GROUP BY t3.wxOpenId
    </select>
    <select id="listWxOrderSimpleList" resultType="com.cfpamf.ms.insur.weixin.pojo.vo.WxOrderSimpleVo">
        SELECT t1.fhOrderId,
               t1.productId,
               t1.planId,
               t1.fhOrderId,
               t1.create_time AS createTime,
               t4.productName,
               t5.planName,
               t2.personName  AS appPersonName,
               t2.idNumber    AS appIdNumber,
               t3.personName  AS insPersonName,
               t3.idNumber    AS insIdNumber,
               t1.totalAmount,
               t1.payStatus,
               t1.startTime,
               t1.endTime,
               t4.productAttrCode
        FROM sm_order t1
                 LEFT JOIN sm_order_applicant t2 ON t2.fhOrderId = t1.fhOrderId
                 LEFT JOIN sm_order_insured t3 ON t3.fhOrderId = t1.fhOrderId
                 LEFT JOIN sm_product t4 ON t4.id = t1.productId
                 LEFT JOIN sm_plan t5 ON t5.id = t1.planId
        WHERE t1.fhOrderId = #{fhOrderId}
    </select>
    <insert id="saveUploadInsured">
        insert into sm_order_insured_upload
        (
            request_id,
            policy_no,
            branch_id,
            name,
            id_card,
            op_type,
            occup_code,
            occup_name,
            occup_group,
            operator
        )
        values
        <foreach collection="data" item="ite" separator=",">
            (
            #{ite.requestId},
            #{ite.policyNo},
            #{ite.branchId},
            #{ite.name},
            #{ite.idCard},
            #{opType},
            #{ite.occupCode},
            #{ite.occupName},
            #{ite.occupGroup},
            #{ite.operator}
            )
        </foreach>
    </insert>
    <select id="queryOccupationByCompany" resultType="com.cfpamf.ms.insur.weixin.pojo.dto.product.OccupationDTO">
        select occupationCode,
               occupationName,
               occupationGroup
        from sm_company_occupation
        where   companyId = #{companyId}
        and     occupationCode is not null
    </select>

    <select id="queryInsuredListFromTemp" resultType="com.cfpamf.ms.insur.weixin.pojo.dto.product.InsuredDTO">
        select
            a.name as personName,
            a.id_card as idNumber,
            a.occup_code as occupationCode,
            a.occup_name as occupationName,
            a.occup_group as occupationGroup,
            a.branch_id,
            a.policy_no
        from sm_order_insured_upload a
        where request_id=#{requestId}
        <if test=" opType!=null ">
            and a.op_type=#{opType}
        </if>
    </select>
    <select id="queryInsuredList" resultType="com.cfpamf.ms.insur.weixin.pojo.dto.product.InsuredDTO">
        select personName       as name,
               idNumber         as id_card,
               occupationCode   as occup_code,
               occupation_group as occup_group
        from sm_order_insured
        where fhOrderId = #{orderId}
    </select>
    <select id="queryOrderInfo" resultType="com.cfpamf.ms.insur.admin.external.fh.dto.FhOrderInfo">
        select fhOrderId as orderId,
               totalAmount,
               submitTime,
               startTime,
               endTime,
               validPeriod,
               underWritingAge,
               paymentTime
        from sm_order
        where fhOrderId = #{orderId}
    </select>
    <select id="queryOrderProduct" resultType="com.cfpamf.ms.insur.weixin.pojo.dto.product.OrderProductDTO">
        select *
        from sm_order_product
        where order_id = #{orderId}
    </select>
    <select id="getApplicant" resultType="com.cfpamf.ms.insur.weixin.pojo.dto.product.ApplicantDTO">
        select *
        from sm_order_applicant
        where fhOrderId = #{orderId}
    </select>

    <select id="queryFastInsureds" resultType="com.cfpamf.ms.insur.admin.external.fh.dto.FastInsuredDTO">
        select *
        from sm_order_insured
        where fhOrderId = #{orderId}
    </select>
    <select id="queryFastOrder" parameterType="java.lang.String"
            resultType="com.cfpamf.ms.insur.weixin.pojo.request.order.OrderBasicVo">
        SELECT
            *
        FROM sm_order
        where fhOrderId = #{orderId}
        order by create_time desc
        limit 1
    </select>
    <select id="queryOrderQuestion" resultType="com.cfpamf.ms.insur.weixin.pojo.dto.product.AiQuestion">
        SELECT *
        FROM sm_order_question
        WHERE order_id = #{orderId}
    </select>

    <select id="queryGroupOrder"
            parameterType="com.cfpamf.ms.insur.weixin.pojo.query.policy.GroupPolicyQuery"
            resultType="com.cfpamf.ms.insur.weixin.pojo.dto.policy.PolicyListDTO">
        select
            o.policy_no,
            o.appNo,
            p.productName,
            o.startTime,
            o.endTime,
            o.channel,
            o.totalAmount,
            o.fhOrderId as orderId,
            a.personName,
            o.create_time
        from sm_order o
        left join sm_order_applicant a on o.fhOrderId=a.fhOrderId
        left join sm_product p on o.productId=p.id
        where o.customerAdminId=#{userId}
        <if test='agentId != null'>
            AND o.agentId=#{agentId}
        </if>
        and p.productAttrCode='group'
        <if test=" issueFlag!=null and issueFlag==1 ">
            and o.payStatus = '2'
            and o.policy_no is not null
            and position("_" in o.fhOrderId) = 0
        </if>
        <if test="keyword!=null and keyword!='' ">
            and (o.policy_no like concat('%',#{keyword},"%") or a.personName like concat('%',#{keyword},"%"))
        </if>
        order by o.create_time desc
    </select>

    <select id="queryGroupInvoiceList"
            parameterType="com.cfpamf.ms.insur.weixin.pojo.query.policy.GroupPolicyQuery"
            resultType="com.cfpamf.ms.insur.weixin.pojo.dto.policy.PolicyListDTO">
        select a.*,b.amount as invoiceAmount
        FROM
        (
            select
                o.policy_no,
                p.companyId,
                c.companyName,
                o.planId,
                p.productName,
                o.startTime,
                o.endTime,
                o.totalAmount,
                o.fhOrderId as orderId,
                a.personName,
                o.create_time,
                r.invoice_id as invoiceId
            from sm_order o
            left join sm_order_applicant a on o.fhOrderId=a.fhOrderId
            left join sm_product p on o.productId=p.id
            left join sm_company c on p.companyId=c.id
            left join sm_policy_invoice_relation r on o.fhOrderId=r.order_id and r.endorsement_no='000'
            where o.customerAdminId=#{userId}
            and p.productAttrCode='group'
            <if test='agentId != null'>
                AND o.agentId=#{agentId}
            </if>
            <if test='openId != null'>
                AND o.wxOpenId=#{openId}
            </if>
            <if test=" issueFlag!=null and issueFlag==1 ">
                and o.payStatus = '2'
                and o.policy_no is not null
                and locate('_',o.fhOrderId)=0
                and (o.endorsement_no='000' or o.endorsement_no is null)
            </if>
            <if test="keyword!=null and keyword!='' ">
                and (
                    a.personName like concat('%',#{keyword},"%")
                    or exists (
                            select 1 from sm_order_item e
                            where o.fhOrderId=e.fh_order_id
                            and (
                                e.th_policy_no like concat('%',#{keyword},"%")
                                or e.th_endorsement_no like concat('%',#{keyword},"%")
                            )
                    )
                )
            </if>
            order by o.create_time desc
        ) a left join
        (
            select
            e.raw_order_id as orderId, sum(e.amount) as amount
            from sm_order_endor e
            where e.status=2
            and not exists (select 1 from sm_policy_invoice_relation r where e.endorsement_no=r.endorsement_no)
            group by e.raw_order_id
        ) b
        on a.orderId=b.orderId
    </select>


    <!--新佣金表微信端提成-->
    <select id="getWxPolicySummaryV3" resultType="com.cfpamf.ms.insur.weixin.pojo.vo.WxCmsSmyVo">
        SELECT
        SUM(case when d.policy_status='1' then t1.qty else t1.qty * -1 end) AS orderQty,
        SUM(case when d.policy_status='1' then d.amount else d.amount * -1 end) AS orderAmount,
        SUM(if(t5.id is null and t1.orderType=0,
            d.payment_amount,
            if(t1.orderType=0,d.payment_amount,0) * .7)) AS cmsAmount,
            sum(
        case
        when (d.policy_status = '4' and a.add_commission_amount > 0 and ifnull(d.original_amount, 0) > 0) then if(t1.orderType=0,d.original_amount*a.proportion/100,0)*-1
        when (d.policy_status = '4' and ifnull(a.add_commission_amount, 0) > 0 and ifnull(d.original_amount, 0) = 0) then if(t1.orderType=0,d.amount*a.proportion/100,0)*-1
        else
            if(t1.orderType=0,ifnull(a.add_commission_amount,0),0)
        end
        ) as addCommissionAmount

        FROM
        sm_commission_detail d
        left join sm_order t1 on d.order_id = t1.fhOrderId
        left join sm_add_commission_detail_sum a on a.order_id = d.order_id and a.policy_no = d.policy_no and a.insured_id_number = d.insured_id_number
                    and d.term_num = a.term_num
        LEFT JOIN sm_product t3
        ON t3.id = t1.productId
        LEFT JOIN sm_plan t4
        ON t4.id = d.plan_id
        left join sm_order_talk t5 on t1.fhOrderId = t5.fh_order_id
        <if test='idNumber != null and idNumberType != null and idNumberType==0'>
            left join sm_order_applicant soa on soa.fhOrderId = t1.fhOrderId
        </if>
        <if test='idNumber != null and idNumberType != null and idNumberType==1'>
            left join sm_order_insured soi on soi.fhOrderId = d.order_id and soi.idNumber= d.insured_id_number
        </if>
        WHERE 1=1
        <if test='userId != null'>
            AND t1.recommendId=#{userId}
        </if>
        <if test='customerAdminId != null'>
            AND t1.customerAdminId=#{customerAdminId}
        </if>
        <if test='startTime != null'>
            <![CDATA[ AND d.account_time >= #{startTime}  ]]>
        </if>
        <if test='endTime != null'>
            <![CDATA[ AND d.account_time <= #{endTime} ]]>
        </if>
        <if test='idNumber != null and idNumberType != null and idNumberType==0'>
            AND soa.idNumber=#{idNumber}
        </if>
        <if test='idNumber != null and idNumberType != null and idNumberType==1'>
            AND soi.idNumber=#{idNumber}
        </if>
        <if test='channel != null'>
            AND t3.channel=#{channel}
        </if>

        <choose>
            <when test="orderType != null and orderType == 0">
                and t3.long_insurance = 1 and t1.orderType=0 and t5.id is null
            </when>
            <when test="orderType != null and orderType == 1">
                and t3.long_insurance = 1 and t1.orderType=1
            </when>
            <when test="orderType != null and orderType == 2">
                and t3.long_insurance = 1 and t5.id is not null
            </when>
            <when test="orderType != null and orderType == 3">
                and t3.long_insurance = 0
            </when>
        </choose>
    </select>


    <select id="listWxPolicyCmsListV3" resultType="com.cfpamf.ms.insur.weixin.pojo.vo.WxUserCmsListVo">
        SELECT d.id, t1.fhOrderId, d.policy_no policyNo, d.create_time, t3.productName, t4.planName, soi.personName AS
        insuredPersonName,soi.idNumber as insuredIdNumber,
        d.policy_status appStatus,
        d.account_time accountTime,

        case when d.policy_status='1' then d.amount else d.amount * -1 end totalAmount,
        if(t5.id is null ,
            if(t1.orderType=0,d.payment_amount,0),
            if(t1.orderType=0,d.payment_amount,0) * .7) AS paymentCommission,
        if(t5.id is null,false,true) talkOrder,
        if(t1.orderType=0,false,true) distributionOrder,

        if(t3.long_insurance = 1,false,true) as longInsuranceOrder,
        if(d.term_num is null ,case when t3.long_insurance = 1 then null else 1 end ,d.term_num) as termNum,

        t5.invite_name inviteName
        ,t5.invite_type inviteType
        ,d.original_amount originalAmount
        FROM sm_commission_detail d
        left join sm_order t1 on d.order_id = t1.fhOrderId
        LEFT JOIN sm_product t3 on t3.id=t1.productId

        LEFT JOIN sm_plan t4 ON t4.id=t1.planId
        left join sm_order_talk t5 on t1.fhOrderId = t5.fh_order_id
        left join sm_order_insured soi on d.order_id = soi.fhorderid and d.policy_no = soi.policyNo and
        d.insured_id_number = soi.idNumber

        WHERE t1.recommendId=#{userId} and t1.payStatus=2
        <if test='startTime != null'>
            <![CDATA[ AND d.account_time>=#{startTime}]]>
        </if>
        <if test='endTime != null'>
            <![CDATA[ AND d.account_time<=#{endTime}]]>
        </if>
        <if test='channel != null'>
            AND t3.channel=#{channel}
        </if>
        <choose>
            <when test="orderType != null and orderType == 0">
                and t3.long_insurance = 1 and t1.orderType =0 and t5.id is null
            </when>
            <when test="orderType != null and orderType == 1">
                and t3.long_insurance = 1 and t1.orderType =1
            </when>
            <when test="orderType != null and orderType == 2">
                and t3.long_insurance = 1 and t5.id is not null
            </when>
            <when test="orderType != null and orderType == 3">
                and t3.long_insurance = 0
            </when>
        </choose>
        ORDER BY d.account_time DESC
    </select>

    <select id="getOrderAddCommissionAmount" resultType="com.cfpamf.ms.insur.weixin.pojo.dto.CmsOrderAmountDTO">
        SELECT
        t1.fhOrderId AS fhOrderId,
        t1.policyNo AS policyNo,
        t1.insIdNumber AS insuredIdNumber
        FROM
        sm_order_commission t1
        LEFT JOIN sm_product t3
        ON t3.id = t1.productId
        left join sm_order_talk t5 on t1.fhOrderId = t5.fh_order_id
        left join sm_order_distribution t12 on t12.fh_order_id = t1.fhOrderId and t12.id_number = t1.insIdNumber
        WHERE 1=1
        <if test='userId != null'>
            AND t1.recommendId=#{userId}
        </if>
        <if test='customerAdminId != null'>
            AND t1.customerAdminId=#{customerAdminId}
        </if>
        <if test='agentId != null'>
            AND t1.agentId=#{agentId}
        </if>
        <if test='startTime != null'>
            <![CDATA[ AND t1.accountTime >= #{startTime}  ]]>
        </if>
        <if test='endTime != null'>
            <![CDATA[ AND t1.accountTime <= #{endTime} ]]>
        </if>
        <if test='idNumber != null and idNumberType != null and idNumberType==0'>
            AND t1.appIdNumber=#{idNumber}
        </if>
        <if test='idNumber != null and idNumberType != null and idNumberType==1'>
            AND t1.insIdNumber=#{idNumber}
        </if>
        <if test='channel != null'>
            AND t1.channel=#{channel}
        </if>

        <choose>
            <when test="orderType != null and orderType == 0">
                and t3.long_insurance = 1 and t12.id is null and t5.id is null
            </when>
            <when test="orderType != null and orderType == 1">
                and t3.long_insurance = 1 and t12.id is not null
            </when>
            <when test="orderType != null and orderType == 2">
                and t3.long_insurance = 1 and t5.id is not null
            </when>
            <when test="orderType != null and orderType == 3">
                and t3.long_insurance = 0
            </when>
        </choose>
        group by t1.fhOrderId,t1.policyNo,t1.insIdNumber
    </select>

    <select id="queryApplicant" resultType="com.cfpamf.ms.insur.weixin.pojo.dto.product.ApplicantDTO">
        select * from sm_order_applicant where fhOrderId=#{orderId}
    </select>
    <select id="queryEndorByNativeOrderList" resultType="com.cfpamf.ms.insur.weixin.pojo.dto.policy.Endor">
        select
            *
        from sm_order_endor e
        where raw_order_id in
        <foreach collection="orderList" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
        and op_type=#{opType}
        and status=#{status}
    </select>

    <select id="getOneInvoiceOrder" resultType="com.cfpamf.ms.insur.weixin.pojo.dto.order.InvoiceOrderDTO">
        select
        i.policyNo,p.productName,a.personName as applicantName,o.totalAmount
        from sm_order o,sm_order_insured i,sm_order_applicant a,sm_product p
        where o.fhOrderId = i.fhOrderId
            and o.fhOrderId = a.fhOrderId
            and o.productId = p.id
            and i.policyNo=#{policyNo}
        limit 1;
    </select>

    <select id="queryWxProductPremium" resultType="com.cfpamf.ms.insur.weixin.pojo.vo.WxProductPremiumVO">
        SELECT
            pca.spcId AS spcId,
            pca.spcaId AS spcaId,
            pca.cvgNotice AS cvgNotice,
            pca.cvgAmount AS cvgAmount,
            pc.cvgCode AS dutyCode,
	        pc.risk_code AS riskCode,
            pc.cvgItemName AS cvgItemName
        FROM
            sm_product_coverage_amount pca
        INNER JOIN sm_product_coverage pc ON pc.spcId = pca.spcId

        WHERE
            1=1
            and pc.productId = #{productId}
    </select>

    <select id="queryDutyFactorDTO" resultType="com.cfpamf.ms.insur.admin.pojo.dto.product.DutyFactorDTO">
        SELECT
            t.id AS id,
            t.spc_id AS spcId,
            t.factor_name AS factorName,
            t.factor_value AS factorValue,
            t.option_name AS optionName,
            t.duty_factor_code AS dutyFactorCode,
            t.duty_code AS dutyCode
        FROM
            sm_product_duty_factor t
        WHERE
            t.product_id = #{productId}
        AND t.spc_id = #{spcId}
        AND t.duty_code = #{dutyCode}
        AND t.duty_factor_code = #{dutyFactorCode}
        AND t.factor_value = #{factorValue}
        AND t.`status` = 0
    </select>

    <select id="getInsuredIdsByCustomerId" resultType="java.lang.Long">
        select
            t.id
        from
            sm_order_insured t
            left join customer t2 on t2.`IdNumber` = t.`idNumber`
            left join `sm_order` t1 on t.`fhOrderId` = t1.`fhOrderId`
            left join sm_product t3 on t3.id = t1.`productId`
        where
            t2.id=#{customerId} and t3.`productAttrCode` = 'person'
        UNION ALL
        select
            t3.id
        from
            `sm_order_applicant` t
            left join customer t2 on t2.`IdNumber` = t.`idNumber`
            left join `sm_order` t1 on t.`fhOrderId` = t1.`fhOrderId`
            left join `sm_order_insured` t3 on t.`fhOrderId` = t3.fhOrderId
        where
            t2.id=#{customerId}
    </select>

    <select id="queryGroupOrderFilterXjChannel"
            parameterType="com.cfpamf.ms.insur.weixin.pojo.query.policy.GroupPolicyQuery"
            resultType="com.cfpamf.ms.insur.weixin.pojo.dto.policy.PolicyListDTO">
        select
        o.policy_no,
        o.appNo,
        p.productName,
        o.startTime,
        o.endTime,
        o.channel,
        o.totalAmount,
        o.fhOrderId as orderId,
        a.personName,
        o.create_time
        from sm_order o
        left join sm_order_applicant a on o.fhOrderId=a.fhOrderId
        left join sm_product p on o.productId=p.id
        where o.customerAdminId=#{userId}
        <if test='agentId != null'>
            AND o.agentId=#{agentId}
        </if>
        and p.productAttrCode='group'
        <if test=" issueFlag!=null and issueFlag==1 ">
            and o.payStatus = '2'
            and o.policy_no is not null
            and position("_" in o.fhOrderId) = 0
            and o.fhOrderId not like 'XJ%'
        </if>
        <if test="keyword!=null and keyword!='' ">
            and (o.policy_no like concat('%',#{keyword},"%") or a.personName like concat('%',#{keyword},"%"))
        </if>
        order by o.create_time desc
    </select>


    <select id="listAiMyPolicyList" resultType="com.cfpamf.ms.insur.admin.pojo.vo.AiMyPolicy">
        SELECT t4.productName as cardTitle, t3.personName as cardUserName, t5.claimResult as stateDescription, t3.policyNo,t5.id as claimId
        FROM sm_order t1
        LEFT JOIN sm_order_insured t3 ON t1.fhOrderId=t3.fhOrderId
        LEFT JOIN sm_product t4 ON t4.id=t1.productId
        INNER JOIN sm_claim t5 on t5.insuredId=t3.id
        <where>
            t3.appStatus='1'
            and t1.customerAdminId=#{customerAdminId}
            <if test='policyNo != null and policyNo !=""'>
                AND t3.policyNo LIKE CONCAT(#{policyNo},'%')
            </if>
            <if test='insuredType!=null and insuredType==5 and insuredName!=null and insuredName!=""'>
                and t3.personName LIKE CONCAT(#{insuredName},'%')
            </if>
            <if test='insuredType!=null and insuredType==6 and insuredName!=null and insuredName!=""'>
                and t3.idNumber LIKE CONCAT(#{insuredName},'%')
            </if>
        </where>
        ORDER BY t1.create_time DESC
    </select>
</mapper>
