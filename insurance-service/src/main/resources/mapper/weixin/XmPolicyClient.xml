<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.weixin.dao.safes.OrderJobMapper">

    <select id="queryJob" resultType="com.cfpamf.ms.insur.weixin.pojo.dto.policy.OrderJob">
        SELECT
          *
        FROM sm_order_job
        WHERE status=0
        and create_time > DATE_SUB(CURDATE(),INTERVAL 3 DAY)
        order by create_time
        limit #{start},#{size}
    </select>
</mapper>
