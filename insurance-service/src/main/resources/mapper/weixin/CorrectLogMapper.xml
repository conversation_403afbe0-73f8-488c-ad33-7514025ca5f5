<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.weixin.dao.safes.correct.CorrectLogMapper">

    <update id="insertListAutoGenKey" parameterType="java.util.List">
        INSERT INTO sm_order_insured_correct_log (
            order_id,
            branch_id,
            id_type,
            id_number,
            name,
            occupation_code,
            occupation_group,
            before_id_type,
            before_id_number,
            before_name,
            before_occupation_code
        )
        VALUES
        <foreach collection="data" item="item" index="index" separator=",">
            (
                #{item.orderId},
                #{item.branchId},
                #{item.idType},
                #{item.idNumber},
                #{item.name},
                #{item.occupationCode},
                #{item.occupationGroup},
                #{item.beforeIdType},
                #{item.beforeIdNumber},
                #{item.beforeName},
                #{item.beforeOccupationCode}
            )
        </foreach>
    </update>
</mapper>
