<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.weixin.dao.safes.WxCollectionMapper">

    <insert id="insertUserCollection" useGeneratedKeys="true" keyProperty="id">
       INSERT INTO wx_user_collection(userId, productId, create_time, update_time) VALUE (#{userId}, #{productId}, CURRENT_TIMESTAMP(), CURRENT_TIMESTAMP())
    </insert>

    <select id="countUserCollection" resultType="java.lang.Integer">
      SELECT count(1) FROM wx_user_collection t1  WHERE productId = #{productId} AND t1.userId = #{userId}
    </select>

    <select id="getUserCollectionList" resultType="com.cfpamf.ms.insur.admin.pojo.vo.UserCollectionListVO">
        SELECT t1.id, t1.productId, t2.productName, t2.state, t2.thumbnailImageUrl, t2.minAmount, t2.productFeature,
               t2.create_type productCreateType
        FROM wx_user_collection t1 LEFT JOIN sm_product t2 ON t1.productId = t2.id
        WHERE t1.userId = #{userId}
        <if test = 'channel != null' >
            AND t2.channel=#{channel}
        </if>
        ORDER BY t1.create_time DESC
    </select>

    <update id="deleteUserCollection">
        DELETE FROM wx_user_collection WHERE productId = #{productId} AND userId = #{userId}
    </update>
</mapper>
