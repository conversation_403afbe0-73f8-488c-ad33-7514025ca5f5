<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.order.SmOrderDraftMapper">

    <update id="updateOrder" parameterType="com.cfpamf.ms.insur.weixin.pojo.dto.order.OrderDraftDTO">
        UPDATE sm_order_draft
        SET
        <if test="basicInfo !=null ">
            basic_info=#{basicInfo},
        </if>
        <if test="product !=null ">
            product=#{product},
        </if>
        <if test="applicant !=null ">
            applicant=#{applicant},
        </if>
        <if test="invoice !=null ">
            invoice=#{invoice},
        </if>
        <if test="insured !=null ">
            insured=#{insured},
        </if>
        update_time=CURRENT_TIMESTAMP()
        WHERE order_id=#{orderId}
    </update>

</mapper>
