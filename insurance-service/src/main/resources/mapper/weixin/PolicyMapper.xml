<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.weixin.dao.safes.PolicyMapper">

    <select id="getProductDetailById" resultType="com.cfpamf.ms.insur.weixin.pojo.dto.product.ProductDTO">
        SELECT
            p.id,
            p.productName,
            p.product_code as productCode,
            p.productTags,
            p.productFeature,
            p.introduceImageUrl,
            p.aiCheck,
            p.aiCheckWay,
            p.version,
            p.channel,
            p.companyId,
            p.attentions,
            p.buyLimit,
            p.effectWaitingDayMin,
            p.effectWaitingDayMax,
            p.healthNotification,
            p.minAmount,
            p.productSpecialsJoin,
            p.glProductNotice,
            p.h5Url,
            p.apiType,
            p.thumbnailImageUrl,
            p.headImageUrl,
            p.productShortName,
            p.custNotify,
            p.sales_mode as salesMode,
            p.glUwaFrom,
            p.glUwaTo,
            p.glOcpnGroup,
            p.validPeriod,
            p.explain_video as explainVideo,
            c.reportPhoneNo,
            c.companyLogoImageUrl,
            p.companyId,
            c.companyName
        FROM sm_product p
        LEFT JOIN sm_company c ON p.companyId = c.id
        WHERE p.id = #{id}
        AND p.state = 1
        AND p.enabled_flag = 0
    </select>
    <select id="getProductDetailByOrderId" resultType="com.cfpamf.ms.insur.weixin.pojo.dto.product.ProductDTO">
        SELECT
            p.id,
            p.productName,
            p.product_code as productCode,
            p.productTags,
            p.productFeature,
            p.introduceImageUrl,
            p.aiCheck,
            p.aiCheckWay,
            p.version,
            p.channel,
            p.companyId,
            p.attentions,
            p.buyLimit,
            p.effectWaitingDayMin,
            p.effectWaitingDayMax,
            p.healthNotification,
            p.minAmount,
            p.productSpecialsJoin,
            p.glProductNotice,
            p.h5Url,
            p.apiType,
            p.thumbnailImageUrl,
            p.headImageUrl,
            p.productShortName,
            p.custNotify,
            p.sales_mode as salesMode,
            p.glUwaFrom,
            p.glUwaTo,
            p.glOcpnGroup,
            p.validPeriod,
            p.explain_video as explainVideo,
            c.reportPhoneNo,
            c.companyLogoImageUrl,
            p.companyId,
            c.companyName
        FROM
        sm_order o
        LEFT JOIN sm_product p ON o.productId=p.id
        LEFT JOIN sm_company c ON p.companyId = c.id
        WHERE o.fhOrderId = #{orderId}
    </select>

    <select id="getPlans" resultType="com.cfpamf.ms.insur.weixin.pojo.dto.product.PlanDTO">
        SELECT
            p.id,
            p.productId,
            p.planName,
            p.description,
            p.fhProductId,
            p.enabled_flag as enabledFlag,
            p.planOrderOutType,
            p.plan_code as planCode,
            p.plan_type as planType,
            p.min_premium as minPremium
        FROM sm_plan p
        WHERE p.productId = #{productId}
        AND p.enabled_flag = 0
        <if test="regionName!=null and regionName!='' ">
            and p.id in (
                select
                    so.plan_id
                from sm_plan_sales_org so
                where so.product_id = #{productId}
                and so.enabled_flag = 0
                and (so.org_name = #{regionName} or so.org_path is null)
            )
        </if>
        order by p.id
    </select>

    <select id="getCoverage" resultType="com.cfpamf.ms.insur.weixin.pojo.vo.product.CoverageVo">
        SELECT
            c.spcId,
            c.productId,
            c.cvgItemName,
            c.cvgNameDetail,
            c.cvgType,
            c.cvgRespType,
            c.cvgCode,
            c.plan_id as planId,
            c.plan_name as planName,
            c.risk_code as riskCode,
            c.mandatory as mandatory
        FROM sm_product_coverage c
        WHERE c.productId = #{productId}
        AND c.enabled_flag = 0
    </select>
    <select id="queryCoverageById" resultType="com.cfpamf.ms.insur.weixin.pojo.vo.product.CoverageVo">
        SELECT
            c.spcId,
            c.productId,
            c.cvgItemName,
            c.cvgNameDetail,
            c.cvgType,
            c.cvgRespType,
            c.cvgCode,
            c.plan_id as planId,
            c.plan_name as planName,
            c.risk_code as riskCode,
            c.mandatory as mandatory
        FROM sm_product_coverage c
        WHERE c.spcId = #{cvgId}
        AND c.enabled_flag = 0
    </select>


    <select id="getCoverageAmounts" resultType="com.cfpamf.ms.insur.weixin.pojo.vo.product.CoverageAmountVo">
        SELECT
            c.spcaId,
            c.spcId,
            c.productId,
            c.cvgAmount,
            c.cvgNotice
        FROM sm_product_coverage_amount c
        WHERE c.productId = #{productId}
        AND c.enabled_flag = 0
    </select>
    <select id="getDutyFactorFlows" resultType="com.cfpamf.ms.insur.weixin.pojo.dto.product.DutyFactorFlowDTO">
        SELECT
            c.id,
            c.spc_id,
            c.product_id,
            c.duty_code,
            c.duty_factor_code,
            d.name as duty_factor_name,
            c.option_name,
            c.factor_value,
            c.factor_name,
            c.flow,
            c.is_default
        FROM sm_product_duty_factor c,sys_duty_config d
        WHERE
        c.duty_factor_code=d.code
        and c.product_id = #{productId}
        AND c.status = 0
    </select>
    <select id="queryQuoteLimit" resultType="com.cfpamf.ms.insur.weixin.pojo.dto.product.QuoteLimitDTO">
        SELECT
            spqlId,
            productId,
            limitType,
            occupationGroup,
            minPerQty,
            minPerRatio,
            sourceSpcId,
            limit_amount as limitAmount,
            limit_amount_notice as limitAmountNotice,
            relyOnSpcId,
            operation,
            targetSpcId,
            amountRatio,
            support_time,
            unit
        FROM sm_product_quote_limit
        WHERE productId = #{productId}
        AND enabled_flag = 0
    </select>


    <!--********************************************************************************************-->
    <select id="listWxProductsWithGroup" resultType="com.cfpamf.ms.insur.weixin.pojo.vo.WxProductListVO">
        SELECT t1.id, t1.channel, t1.productAttrCode, t1.productName, t1.productFeature, t1.thumbnailImageUrl,
        t1.minAmount, t1.saleQty, t1.sortNum
        ,t1.h5Url,
        t1.apiType,t1.productTags AS productTagsJoin
        FROM sm_product t1
        WHERE t1.state=1 AND t1.enabled_flag=0
        AND t1.onlineChannel LIKE '%xiangzhu%'
        <if test='salesProductIds != null'>
            AND t1.id IN
            <foreach item="item" index="index" open="(" separator="," close=")" collection="salesProductIds">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="listWxHotProducts" resultType="com.cfpamf.ms.insur.weixin.pojo.vo.WxProductListVO">
        SELECT t1.id, t1.channel, t1.productName, t1.productFeature, t1.thumbnailImageUrl, t1.minAmount, t1.sortNum
        ,t1.h5Url,
        t1.apiType,t1.productTags AS productTagsJoin
        FROM sm_product t1

        WHERE t1.state=1 AND t1.enabled_flag=0 AND t1.onlineChannel LIKE '%xiangzhu%'
        AND t1.productAttrCode in ('person','employer')
        <if test='channel != null'>
            AND t1.channel = #{channel}
        </if>
        <if test='salesProductIds != null'>
            AND t1.id IN
            <foreach item="item" index="index" open="(" separator="," close=")" collection="salesProductIds">
                #{item}
            </foreach>
        </if>
        ORDER BY t1.monthSaleCount DESC, t1.productName ASC
        LIMIT 5
    </select>

    <select id="listWxActiveProducts" resultType="com.cfpamf.ms.insur.weixin.pojo.vo.WxProductListVO">
        SELECT t1.id, t1.channel, t1.productName, t1.productFeature, t1.thumbnailImageUrl, t1.minAmount,  t1.sortNum ,t1.h5Url,
        t1.apiType,t1.productTags AS productTagsJoin
        FROM sm_product t1
        WHERE t1.state=1 AND t1.enabled_flag=0 AND t1.onlineChannel LIKE '%xiangzhu%'
        AND t1.productAttrCode in ('person','employer')
        and t1.activeFlag = 1
        <if test='channel != null'>
            AND t1.channel = #{channel}
        </if>
        <if test='salesProductIds != null'>
            AND t1.id IN
            <foreach item="item" index="index" open="(" separator="," close=")" collection="salesProductIds">
                #{item}
            </foreach>
        </if>
        ORDER BY t1.sortNum ,t1.monthSaleCount DESC, t1.productName ASC
    </select>

    <select id="getWxProductDetailById" resultType="com.cfpamf.ms.insur.weixin.pojo.vo.WxProductDetailVO">
        SELECT t1.id,
               t1.aiCheck,
               t1.aiCheckWay,
               t1.version,
               t1.channel,
               t1.productName,
               t1.productFeature,
               t1.productTags   AS productTagsJoin,
               t1.introduceImageUrl,
               t1.companyId,
               t2.companyLogoImageUrl,
               t2.companyName,
               t1.attentions,
               t1.buyLimit,
               t1.effectWaitingDayMin,
               t1.effectWaitingDayMax,
               t1.attentions,
               t1.healthNotification,
               t1.minAmount,
               t1.productSpecialsJoin,
               t1.glProductNotice,
               t2.reportPhoneNo AS companyReportPhoneNo,
               t1.h5Url,
               t1.apiType,
               t1.headImageUrl,
               t1.productShortName,
               t1.custNotify,
               t1.sales_mode as salesMode,
               t1.product_code as productCode
        FROM sm_product t1
        LEFT JOIN sm_company t2 ON t1.companyId = t2.id
        WHERE t1.id = #{id}
          AND t1.state = 1
          AND t1.enabled_flag = 0
    </select>

    <select id="listActivityProducts" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmActivityProductVO">
        SELECT t1.*, t1.id AS productId,
        t4.companyName AS companyName
        FROM sm_product t1
        LEFT JOIN sm_company t4 on t1.companyId = t4.id AND t4.enabled_flag=0
        WHERE t1.enabled_flag = 0 AND t1.productAttrCode in ('person','employer') AND t1.state = 1
        <if test='productName != null'>
            AND t1.productName LIKE CONCAT('%',#{productName},'%')
        </if>
        <if test='categoryId != null'>
            AND (t1.productCategoryId = #{categoryId} OR t1.productCategoryId LIKE CONCAT(#{categoryId}, ',%')
            OR t1.productCategoryId LIKE CONCAT('%,',#{categoryId}, ',%') OR t1.productCategoryId LIKE
            CONCAT('%,',#{categoryId}))
        </if>
        <if test='companyId != null'>
            AND t1.companyId =#{companyId}
        </if>
        <if test='productIds != null'>
            AND t1.id IN
            <foreach item="item" index="index" open="(" separator="," close=")" collection="productIds">
                #{item}
            </foreach>
        </if>
        ORDER BY ,t1.id ASC
    </select>

    <select id="listWxRenewProductsProductId" resultType="com.cfpamf.ms.insur.weixin.pojo.vo.WxProductRenewVO">
        SELECT t2.id          AS renewProductId,
               t2.productName AS renewProductName,
               t3.state       AS oldProductState
        FROM sm_product_renew t1
                 LEFT JOIN sm_product t2
                           ON t2.id = t1.renewProductId
                 LEFT JOIN sm_product t3
                           ON t3.id = t1.productId
        WHERE t1.productId = #{productId}
          AND t1.enabled_flag = 0
    </select>

    <select id="listWxGroupProducts" resultType="com.cfpamf.ms.insur.weixin.pojo.vo.WxGlProductListVO">
        SELECT
        id AS productId,
        productName,
        thumbnailImageUrl,
        glUwaFrom,
        glUwaTo,
        glOcpnGroup,
        effectWaitingDayMin,
        effectWaitingDayMax,
        minAmount
        FROM
        sm_product
        WHERE productAttrCode = 'group' AND state = 1
        <if test='salesProductIds != null'>
            AND id IN
            <foreach item="item" index="index" open="(" separator="," close=")" collection="salesProductIds">
                #{item}
            </foreach>
        </if>
        ORDER BY create_time ASC
    </select>

    <select id="getGlProductById"
            resultType="com.cfpamf.ms.insur.weixin.pojo.vo.WxGlProductDetailVO">
        SELECT t1.*, t1.id AS productId, t2.companyName
        FROM sm_product t1
                 LEFT JOIN sm_company t2 ON t1.companyId = t2.id
        WHERE t1.id = #{productId}
    </select>

    <select id="listProductClausesByProductId" resultType="com.cfpamf.ms.insur.weixin.pojo.vo.WxProductClauseVO">
        SELECT *, id AS productId
        FROM sm_product_clause
        WHERE productId = #{id}
          AND enabled_flag = 0
        ORDER BY id ASC
    </select>

    <select id="listProductCoverages" resultType="com.cfpamf.ms.insur.weixin.pojo.vo.WxProductCoverageVO">
        SELECT *
        FROM sm_product_coverage
        WHERE productId = #{productId}
          and enabled_flag = 0;
    </select>

    <select id="listProductQuoteLimit" resultType="com.cfpamf.ms.insur.weixin.pojo.vo.WxProductQuoteLimitItemVO">
        SELECT *
        FROM sm_product_quote_limit
        WHERE productId = #{productId}
          AND enabled_flag = 0
    </select>

    <select id="getProductPosterByProductId" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmProductPosterVO">
        SELECT *
        from sm_product_poster
        WHERE productId = #{productId}
          AND enabled_flag = 0
        LIMIT 1
    </select>

    <select id="getGlProductQuotePlan"
            resultType="com.cfpamf.ms.insur.weixin.pojo.vo.WxGlProductQuoteVO">
        SELECT *
        FROM sm_product_quote_plan
        WHERE spqpId = #{spqpId}
    </select>

    <delete id="deleteWxGlProductQuotePlan">
        UPDATE sm_product_quote_plan
        SET enabled_flag = 1
        WHERE spqpId = #{spqpId}
    </delete>

    <select id="listProductSalesOrgsByOrgPath" resultType="java.lang.Integer">
        SELECT DISTINCT t1.product_id
        FROM sm_plan_sales_org t1
        LEFT JOIN sm_product t2 ON t1.product_id = t2.id
        WHERE
        t1.enabled_flag = 0
        AND t2.state=1
        <if test='orgName != null'>
            AND (t1.org_name = #{orgName} OR t1.org_path IS NULL)
        </if>
        <if test='orgName == null'>
            AND t1.org_path IS NULL
        </if>
    </select>
    <!--    <select id="listProductSalesOrgsByOrgPath" resultType="java.lang.Integer">-->
    <!--        SELECT DISTINCT t1.productId-->
    <!--        FROM sm_product_sales_org t1-->
    <!--        LEFT JOIN sm_product t2 ON t1.productId = t2.id-->
    <!--        WHERE-->
    <!--        t1.enabled_flag = 0-->
    <!--        AND t2.state=1-->
    <!--        <if test='orgName != null'>-->
    <!--            AND (t1.orgName = #{orgName} OR t1.orgPath IS NULL)-->
    <!--        </if>-->
    <!--        <if test='orgName == null'>-->
    <!--            AND t1.orgPath IS NULL-->
    <!--        </if>-->
    <!--    </select>-->

    <select id="listProductSalesOrgsByProductId" resultType="com.cfpamf.ms.insur.weixin.pojo.vo.WxProductSalesOrgVO">
        SELECT productId, hrOrgId, orgName, orgPath
        FROM sm_product_sales_org
        WHERE productId = #{productId}
          AND enabled_flag = 0
    </select>
    <select id="listProductSalePlanByProductId" resultType="com.cfpamf.ms.insur.weixin.pojo.vo.WxProductSalesOrgVO">
        SELECT distinct product_id , hr_org_id, org_name, org_path
        FROM sm_plan_sales_org
        WHERE product_id = #{productId}
          AND enabled_flag = 0
    </select>

    <select id="listWxProductPlanCoverages" resultType="com.cfpamf.ms.insur.weixin.pojo.vo.WxPlanCoverageVO">
        SELECT t1.spcaId, t1.cvgAmount, t1.cvgNotice, t2.cvgType, t2.cvgItemName, t2.cvgNameDetail
        FROM sm_product_coverage_amount t1
                 LEFT JOIN sm_product_coverage t2 ON t1.spcId = t2.spcId
        WHERE t1.planId = #{planId}
          AND t1.enabled_flag = 0
          AND t2.enabled_flag = 0
    </select>

    <select id="getProductClauseContent" resultType="java.lang.String">
        select
            content
        from sm_product_clause_content where productId = #{productId}
        and enabled_flag = 0
        order by create_time desc
        limit 1
    </select>
    <select id="queryProductClausesByProductId" resultType="com.cfpamf.ms.insur.weixin.pojo.vo.product.ClauseVo">
        SELECT
            *
        FROM sm_product_clause
        WHERE productId = #{productId}
        AND enabled_flag = 0
        ORDER BY id ASC
    </select>


    <select id="queryHealth" resultType="com.cfpamf.ms.insur.weixin.pojo.vo.product.QuestionVo">
        select
            code,
            question
        from ak_product_question
        where product_id = #{productId}
        and status = 0
    </select>

    <select id="querySelfConfirm" resultType="com.cfpamf.ms.insur.weixin.pojo.dto.product.SelfConfirmDTO">
        select
            id,
            product_id as productId,
            confirm_name as confirmName,
            file_name as fileName,
            file_url as fileUrl,
            file_content as fileContent,
            enabled_flag as enabledFlag
        from sm_product_confirm
        where product_id = #{productId}
        and enabled_flag = 0
       order by display_sort asc
    </select>

    <select id="queryPremium" resultType="com.cfpamf.ms.insur.weixin.pojo.dto.product.PremiumDTO">
        select
            spcpId,
            productId,
            spcId,
            planId,
            spcaId,
            occupationGroup,
            premium
        from sm_product_coverage_premium
        where productId = #{productId}
        and planId = #{planId}
        and occupationGroup = #{ocpGroup}
        and enabled_flag = 0
    </select>

    <select id="queryPremiumByPlan" resultType="com.cfpamf.ms.insur.weixin.pojo.dto.product.PremiumDTO">
        select
            spcpId,
            productId,
            spcId,
            planId,
            spcaId,
            occupationGroup,
            premium
        from sm_product_coverage_premium
        where productId = #{productId}
        and planId = #{planId}
        and enabled_flag = 0
    </select>

    <select id="queryPolicyInfo" resultType="com.cfpamf.ms.insur.weixin.pojo.vo.product.EndorPolicyInfo">
        SELECT
            o.productId,
            p.productName,
            o.fhOrderId as orderId,
            o.policy_no as policyNo,
            o.startTime,
            o.endTime,
            o.channel,
            a.personName as applicantName,
            o.totalAmount as totalPremium,
            c.companyName,
            c.id as companyId
        FROM sm_order o
        left join sm_order_applicant a on o.fhOrderId=a.fhOrderId
        left join sm_product p on o.productId = p.id
        left join sm_company c on p.companyId=c.id
        WHERE o.fhOrderId=#{orderId}
        and   o.policy_no = #{policyNo}
        and   (endorsement_no='000' or endorsement_no is null)
    </select>
    <select id="queryPolicyByVo"
            parameterType="com.cfpamf.ms.insur.weixin.pojo.query.order.OrderDetailQuery"
            resultType="com.cfpamf.ms.insur.weixin.pojo.vo.product.EndorPolicyInfo">
        SELECT
        o.productId,
        p.productName,
        o.fhOrderId as orderId,
        o.policy_no as policyNo,
        o.startTime,
        o.endTime,
        o.channel,
        a.personName as applicantName,
        o.totalAmount as totalPremium,
        c.companyName,
        c.id as companyId
        FROM sm_order o
        left join sm_order_applicant a on o.fhOrderId=a.fhOrderId
        left join sm_product p on o.productId = p.id
        left join sm_company c on p.companyId=c.id
        WHERE o.fhOrderId=#{id}
        AND   o.policy_no = #{policyNo}
        AND   (endorsement_no='000' or endorsement_no is null)
        <if test='userId != null'>
            AND o.customerAdminId=#{userId}
        </if>
        <if test='agentId != null'>
            AND o.agentId=#{agentId}
        </if>
    </select>

    <select id="queryEndorConfig" resultType="com.cfpamf.ms.insur.weixin.pojo.vo.product.EndorConfig">
        SELECT
            min_addition_effective_time as minAdditionEffectiveTime,
            max_addition_effective_time as maxAdditionEffectiveTime,
            min_reduction_effective_time as minReductionEffectiveTime,
            max_reduction_effective_time as maxReductionEffectiveTime,
            endor_tips as endorTips,
            addition_max_limit as additionMaxLimit,
            reduction_max_limit as reductionMaxLimit,
            addition_job_class as additionJobClass,
            reduction_max_ratio as reductionMaxRatio,
            overplus_min_limit as overplusMinLimit,
            endor_max_limit as endorMaxLimit
        FROM sm_product_endor
        WHERE product_id=#{productId}
    </select>
    <select id="queryEndorConfigByOrder" resultType="com.cfpamf.ms.insur.weixin.pojo.vo.product.EndorConfig">
        SELECT
            min_addition_effective_time as minAdditionEffectiveTime,
            max_addition_effective_time as maxAdditionEffectiveTime,
            min_reduction_effective_time as minReductionEffectiveTime,
            max_reduction_effective_time as maxReductionEffectiveTime,
            endor_tips as endorTips,
            addition_max_limit as additionMaxLimit,
            reduction_max_limit as reductionMaxLimit,
            addition_job_class as additionJobClass,
            reduction_max_ratio as reductionMaxRatio,
            overplus_min_limit as overplusMinLimit,
            endor_max_limit as endorMaxLimit
        FROM sm_order o left join sm_product_endor e on o.productId=e.product_id
        WHERE o.fhOrderId=#{orderId}
    </select>

    <select id="queryJobList" resultType="java.lang.String">
        SELECT occupation_group FROM sm_order_insured
        where fhOrderId=#{orderId}
        <if test="policyNo!=null and policyNo!='' ">
            and policyNo=#{policyNo}
        </if>
    </select>
    <select id="queryInsureds"
            parameterType="com.cfpamf.ms.insur.weixin.pojo.query.order.OrderDetailQuery"
            resultType="com.cfpamf.ms.insur.weixin.pojo.dto.order.InsuredListDTO">
        SELECT
            a.id,
            b.fhOrderId,
            a.active_branch_id as activeBranchId,
            a.plan_code as planCode,
            a.branch_id as branchId,
            a.total_amount as totalAmount,
            b.personName,
            b.personGender,
            b.birthday,
            b.idType,
            b.idNumber,
            b.occupationCode,
            IFNULL(e.occupation_name,f.occupationName) as occupationName,
            b.occupation_group as occupationGroup,
            b.downloadURL,
            a.app_status as appStatus,
            b.insured_time as insuredTime
        FROM sm_order_insured b
        left join sm_order_item a on a.fh_order_id = b.fhOrderId and a.id_number = b.idNumber and a.enabled_flag = 0
        left join sm_order c on b.fhOrderId = c.fhOrderId
        left join sm_product d on  d.id = c.productId
        left join sm_product_occupation e on d.id = e.product_id and e.type = 1 and e.occupation_code = b.occupationCode and e.enabled_flag = 0
        LEFT JOIN sm_company_occupation f ON f.occupationCode = b.occupationCode and f.companyId = d.companyId and f.enabled_flag = 0
        where b.enabled_flag =0
        <if test=" id!=null and id!='' ">
            and a.fh_order_id like concat(#{id},"%")
        </if>
        <if test=" policyNo!=null and policyNo!='' ">
            and a.th_policy_no = #{policyNo}
        </if>
        <if test=" keyword!=null and keyword!='' ">
            and (b.personName like concat("%",#{keyword},"%") or b.idNumber like concat(#{keyword},"%"))
        </if>
        order by a.id
    </select>

    <insert id="copyInsured"
            parameterType="com.cfpamf.ms.insur.weixin.pojo.vo.apply.common.GroupInsured" >
        insert into sm_order_insured(
            endor_id,
            fhOrderId,
            relationship,
            personName,
            personGender,
            idType,
            idNumber,
            birthday,
            cellPhone,
            email,
            annualIncome,
            flightNo,
            flightTime,
            occupationCode,
            occupation_group,
            destinationCountryText,
            area,
            address,
            appStatus,
            policyNo,
            downloadURL,
            enabled_flag,
            create_by,
            update_by,
            studentType,
            schoolType,
            schoolNature,
            schoolName,
            schoolClass,
            studentId,
            hdrType,
            idPeriodStart,
            idPeriodEnd,
            insured_time,
            surrender_time,
            isSecurity,
            addressProvider,
            areaName,
            smoke,
            over_reason
        )  select
                #{data.endorId},
                a.fhOrderId,
        a.relationship,
                #{data.personName},
                a.personGender,
                #{data.idType},
                #{data.idNumber},
                #{data.birthday},
                a.cellPhone,
                a.email,
                a.annualIncome,
                a.flightNo,
                a.flightTime,
                #{data.occupationCode},
                a.occupation_group,
                a.destinationCountryText,
                a.area,
                a.address,
                2,
        a.policyNo,
        a.downloadURL,
        a.enabled_flag,
        a.create_by,
        a.update_by,
        a.studentType,
        a.schoolType,
        a.schoolNature,
        a.schoolName,
        a.schoolClass,
        a.studentId,
        a.hdrType,
        a.idPeriodStart,
        a.idPeriodEnd,
        a.insured_time,
        a.surrender_time,
        a.isSecurity,
        a.addressProvider,
        a.areaName,
        a.smoke,
        a.over_reason
        from sm_order_insured a left join sm_order_item b
        on   a.fhOrderId=b.fh_order_id
        and  a.idNumber=b.id_number
        and  a.appStatus=b.app_status
        where a.appStatus = 1
        and   a.fhOrderId like concat(#{orderId},"%")
        and   b.branch_id  = #{data.branchId}
        order by a.id desc
        limit 1
    </insert>
    <insert id="copyInsuredItem"
            parameterType="com.cfpamf.ms.insur.weixin.pojo.vo.apply.common.GroupInsured" >
        insert into sm_order_item(
            endor_id,
            fh_order_id,
            th_policy_no,
            th_endorsement_no,
            branch_id,
            active_branch_id,
            type,
            product_id,
            plan_id,
            plan_code,
            app_status,
            id_type,
            id_number,
            unit_price,
            qty,
            total_amount,
            endorsement_amount,
            enabled_flag,
            create_by,
            update_by,
            policy_no,
            commission_id,
            interface_version,
            insured_amount
        )  select
        #{data.endorId},
            fh_order_id,
            th_policy_no,
            th_endorsement_no,
            branch_id,
            active_branch_id,
            type,
            product_id,
            plan_id,
            plan_code,
            2,
            #{data.idType},
            #{data.idNumber},
            unit_price,
            qty,
            total_amount,
            endorsement_amount,
            enabled_flag,
            create_by,
            update_by,
            policy_no,
            commission_id,
            interface_version,
            insured_amount
        from sm_order_item
        where th_policy_no  = #{data.policyNo}
        and   branch_id  = #{data.branchId}
        order by id desc
        limit 1
    </insert>

    <insert id="copyApplicant" >
        insert into sm_order_applicant(
        fhOrderId,
        personName,
        personGender,
        idType,
        idNumber,
        birthday,
        cellPhone,
        email,
        area,
        address,
        create_by,
        update_by,
        idPeriodStart,
        idPeriodEnd,
        addressProvider,
        areaName,
        business_license,
        provice_code,
        city_code,
        country_code,
        vatInvoice,
        extend_field
        )  select
            #{newOrderId},
            personName,
            personGender,
            idType,
            idNumber,
            birthday,
            cellPhone,
            email,
            area,
            address,
            create_by,
            update_by,
            idPeriodStart,
            idPeriodEnd,
            addressProvider,
            areaName,
            business_license,
            provice_code,
            city_code,
            country_code,
            vatInvoice,
            extend_field
        from sm_order_applicant
        where fhOrderId  = #{orderId}
        limit 1
    </insert>
    <insert id="copyMainOrder" >
        insert into sm_order(
        fhOrderId,
        productId,
        recommendId,
        wxOpenId,
        planId,
        unitPrice,
        qty,
        totalAmount,
        startTime,
        endTime,
        validPeriod,
        submitTime,
        noticeCode,
        noticeMsg,
        orderState,
        payStatus,
        commissionId,
        extractFlag,
        paymentTime,
        customerAdminId,
        underWritingAge,
        channel,
        appNo,
        policy_no,
        payId,
        payUrl,
        renewOrderId,
        recommendMasterName,
        recommendAdminName,
        recommendEntryDate,
        recommendPostName,
        agentId,
        subChannel,
        planFactorPriceOptionJson,
        recommendJobCode,
        recommendMainJobNumber,
        customerAdminJobCode,
        customerAdminMainJobNumber,
        recommendMasterJobCode,
        recommendAdminJobCode,
        recommendOrgCode,
        customerAdminOrgCode,
        orderOutType,
        productType,
        orderType,
        endorsement_no,
        add_commission_proportion
        )  select
        #{newOrderId},
        productId,
        recommendId,
        wxOpenId,
        planId,
        unitPrice,
        qty,
        0,
        #{startTime},
        endTime,
        validPeriod,
        submitTime,
        noticeCode,
        noticeMsg,
        2,
        1,
        commissionId,
        extractFlag,
        paymentTime,
        customerAdminId,
        underWritingAge,
        channel,
        appNo,
        policy_no,
        payId,
        payUrl,
        renewOrderId,
        recommendMasterName,
        recommendAdminName,
        recommendEntryDate,
        recommendPostName,
        agentId,
        subChannel,
        planFactorPriceOptionJson,
        recommendJobCode,
        recommendMainJobNumber,
        customerAdminJobCode,
        customerAdminMainJobNumber,
        recommendMasterJobCode,
        recommendAdminJobCode,
        recommendOrgCode,
        customerAdminOrgCode,
        orderOutType,
        productType,
        orderType,
        endorsement_no,
        add_commission_proportion
        from sm_order
        where fhOrderId  = #{orderId}
        limit 1
    </insert>


    <select id="queryRawInsureds" resultType="com.cfpamf.ms.insur.weixin.pojo.dto.order.InsuredListDTO">
        SELECT
        b.id,
        personName,
        personGender,
        birthday,
        idType,
        idNumber,
        occupationCode,
        occupation_group as occupationGroup,
        a.app_status as appStatus,
        b.downloadURL as downloadURL
        FROM sm_order_item a left join sm_order_insured b on a.fh_order_id=b.fhOrderId and a.id_number=b.idNumber
        where a.fh_order_id =#{orderId}
    </select>

    <select id="queryEndorInsuredList" resultType="com.cfpamf.ms.insur.weixin.pojo.dto.order.InsuredListDTO">
        select
            i.fh_order_id,
            i.endor_id as endorId,
            o.personName,
            o.personGender,
            o.birthday,
            o.idType,
            o.idNumber,
            o.occupationCode,
            o.occupation_group as occupationGroup,
            i.app_status as appStatus,
            i.branch_id as branchId,
            i.active_branch_id as activeBranchId,
            o.downloadURL as downloadURL
        from sm_order_item i
        left join sm_order_insured o on i.fh_order_id=o.fhOrderId and i.id_number=o.idNumber
        where i.th_policy_no=#{policyNo}
        and   th_endorsement_no=#{endorNo}
    </select>

    <select id="getEndorRawOrder"
            parameterType="com.cfpamf.ms.insur.weixin.pojo.query.order.OrderDetailQuery"
            resultType="com.cfpamf.ms.insur.weixin.pojo.dto.policy.EndorListDTO">
        SELECT
            fhOrderId as orderId,
            totalAmount as amount,
            appNo ,
            orderOutType as orderType,
            policy_no as policyNo,
            channel,
            null as endorsementNo,
            orderState as status,
            0 as opType,
            create_time as createTime
        FROM sm_order o
        where fhOrderId=#{id}
        <if test="policyNo!=null and policyNo!='' ">
            and policy_no=#{policyNo}
        </if>
        <if test='userId != null'>
            AND o.customerAdminId=#{userId}
        </if>
        <if test='agentId != null'>
            AND o.agentId=#{agentId}
        </if>
        limit 1
    </select>

    <select id="getInvoiceRawOrder"
            parameterType="com.cfpamf.ms.insur.weixin.pojo.query.order.OrderDetailQuery"
            resultType="com.cfpamf.ms.insur.weixin.pojo.dto.policy.EndorListDTO">
        SELECT
            o.fhOrderId as orderId,
            o.totalAmount as amount,
            orderOutType as orderType,
            o.policy_no as policyNo,
            o.channel,
            p.companyId,
            null as endorsementNo,
            o.orderState as status,
            0 as opType,
            o.create_time as createTime,
            r.invoice_id as invoiceId
        FROM sm_order o
        LEFT JOIN sm_product p on o.productId=o.id
        LEFT JOIN sm_company c ON p.companyId=c.id
        LEFT JOIN sm_policy_invoice_relation r on o.fhOrderId=r.order_id and r.endorsement_no='000'
        WHERE fhOrderId=#{id}
        <if test="policyNo!=null and policyNo!='' ">
            and o.policy_no=#{policyNo}
        </if>
        <if test='userId != null'>
            AND o.customerAdminId=#{userId}
        </if>
        <if test='openId != null'>
            AND o.wxOpenId=#{openId}
        </if>
        limit 1
    </select>

    <select id="getEndorList" resultType="com.cfpamf.ms.insur.weixin.pojo.dto.policy.EndorListDTO">
        SELECT
              order_id as orderId,
              order_id as endorId,
              amount,
              proposal_no as appNo,
              order_type as orderType,
              policy_no as policyNo,
              channel,
              endorsement_no as endorsementNo,
              status,
              op_type as opType,
              create_time as createTime
        FROM sm_order_endor o
        where policy_no=#{policyNo}
        order by createTime desc
        limit 0,500
    </select>

    <select id="getEndorList4Invoice" resultType="com.cfpamf.ms.insur.weixin.pojo.dto.policy.EndorListDTO">
        SELECT
            o.order_id as orderId,
            amount,
            order_type as orderType,
            o.policy_no as policyNo,
            channel,
            o.endorsement_no as endorsementNo,
            status,
            op_type as opType,
            o.create_time as createTime,
            r.invoice_id as invoiceId
        FROM sm_order_endor o
        LEFT JOIN sm_policy_invoice_relation r on o.endorsement_no=r.endorsement_no
        WHERE o.policy_no=#{policyNo}
        and   o.status=2
        and   o.amount!=0
        <if test="channel!=null and channel!='' ">
            and o.channel=#{channel}
        </if>
        <if test="invoiceStatus!=null and invoiceStatus==0 ">
            and r.invoice_id is null
        </if>
        <if test="invoiceStatus!=null and invoiceStatus==1 ">
            and r.invoice_id is not null
        </if>
        ORDER BY createTime DESC
        limit 0,200
    </select>

    <select id="queryApplicant" resultType="com.cfpamf.ms.insur.weixin.pojo.vo.apply.common.GroupApplicant">
      SELECT *
      FROM sm_order_applicant
      where fhOrderId=#{orderId}
    </select>
    <select id="queryEndorInfo"
            parameterType="com.cfpamf.ms.insur.weixin.pojo.query.order.OrderDetailQuery"
            resultType="com.cfpamf.ms.insur.weixin.pojo.dto.policy.EndorDTO">
        SELECT e.*,
               o.product_name as productName,
               a.personName as applicantName
        FROM sm_order_endor e
        LEFT JOIN sm_order_product o on e.raw_order_id=o.order_id
        left join sm_order_applicant a on e.raw_order_id=a.fhOrderId
        where e.order_id=#{id}
        <if test='userId != null'>
            AND e.operaor=#{userId}
        </if>
    </select>

    <select id="checkPermission" parameterType="com.cfpamf.ms.insur.weixin.pojo.query.order.OrderDetailQuery" resultType="java.lang.Integer">
        select count(1)
        from sm_order
        where fhOrderId=#{id}
        <if test='userId != null'>
              AND customerAdminId=#{userId}
        </if>
        <if test='agentId != null'>
              AND agentId=#{agentId}
        </if>
    </select>
</mapper>
