<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cfpamf.ms.insur.commission.mapper.CommissionUploadMonthMapper">

    <insert id="insertListOrCount">
        insert into commission_upload_month (`commission_month`, state, count, last_upload_time, updater_name,
                                             updater_job_number)
        VALUES (#{item.commissionMonth}, #{item.state}, #{item.count}, #{item.lastUploadTime}, #{item.updaterName},
                #{item.updaterJobNumber}) on duplicate key
        update count = count + 1,
            last_upload_time=values(last_upload_time),
            updater_name=values(updater_name),
            updater_job_number=values (updater_job_number),
            state =1
    </insert>
</mapper>
