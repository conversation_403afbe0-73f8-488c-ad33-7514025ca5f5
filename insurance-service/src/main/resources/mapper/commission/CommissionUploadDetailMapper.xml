<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cfpamf.ms.insur.commission.mapper.CommissionUploadDetailMapper">
    <insert id="insertListOrUpdateItem">
        insert into commission_upload_detail(upload_id,commission_month, region_name, org_name, job_number, user_name,
        commission_item,commission_data_type)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.uploadId},#{item.commissionMonth}, #{item.regionName}, #{item.orgName}, #{item.jobNumber}, #{item.userName},
            #{item.commissionItem},#{item.commissionDataType})
        </foreach>
        on duplicate key update
        upload_id=values(upload_id), commission_item = values(commission_item),user_name=values(user_name)
        ,region_name = values(region_name), org_name = values(org_name),enabled_flag = 0
        ,commission_data_type=values(commission_data_type)
    </insert>
    <update id="updateFlagByUploadId">
        update commission_upload_detail set enabled_flag = #{flag}   where upload_id = #{fileId}
    </update>
</mapper>
