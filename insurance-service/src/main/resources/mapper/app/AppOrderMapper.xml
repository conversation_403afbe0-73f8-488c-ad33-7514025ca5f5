<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.app.dao.AppOrderMapper">


    <select id="listAppPolicyListAll" resultType="com.cfpamf.ms.insur.app.pojo.dto.AppPolicyVo">
        SELECT t3.id AS policyId,
        t1.fhOrderId,
        t1.unitPrice*t1.qty AS totalAmount,
        t1.payStatus,
        t3.appStatus,
        t2.personName AS applicantPersonName,
        t3.personName AS insuredPersonName,
        t3.policyNo AS policyNo,
        t4.productName,
        t5.planName,
        t3.appStatus,
        t3.downloadURL,
        t6.amount,
        DATE_FORMAT(t1.startTime ,'%Y-%m-%d') AS startTime,
        DATE_FORMAT(t1.endTime ,'%Y-%m-%d') AS endTime,
        DATE_FORMAT(t1.endTime ,'%Y-%m-%d %H:%i:%s') AS fullEndTime,
        DATE_FORMAT(t1.create_time ,'%Y-%m-%d %T') AS createTime,
        t4.productType as productType,
        t7.name as productTypeName
        FROM sm_order t1
        LEFT JOIN sm_order_applicant t2 ON t1.fhOrderId=t2.fhOrderId
        LEFT JOIN sm_order_insured t3 ON t1.fhOrderId=t3.fhOrderId
        LEFT JOIN sm_product t4 ON t4.id=t1.productId
        LEFT JOIN sm_plan t5 ON t5.id=t1.planId
        left join sm_order_policy t6 on t6.policy_no = t3.policyNo
        and t6.fh_order_id = t1.fhOrderId and t3.appStatus = t6.policy_state
        left join dictionary t7 on t7.code = t4.productType and t7.type ='productGroup'
        WHERE
        <if test="isInsured == false ">
            t2.idNumber = #{idNumber}
        </if>
        <if test="isInsured == true ">
            t3.idNumber = #{idNumber}
        </if>AND t1.payStatus = '2'
        ORDER BY t1.create_time DESC
    </select>
    <select id="listAppPolicyList" resultType="com.cfpamf.ms.insur.app.pojo.dto.AppPolicyVo">
        SELECT t3.id AS policyId,
        t1.fhOrderId,
        t1.unitPrice*t1.qty AS totalAmount,
        t1.payStatus,
        t3.appStatus,
        t2.personName AS applicantPersonName,
        t3.personName AS insuredPersonName,
        t3.policyNo AS policyNo,
        t4.productName,
        t5.planName,
        t3.appStatus,
        t3.downloadURL,
        t6.amount,
        DATE_FORMAT(t1.startTime ,'%Y-%m-%d') AS startTime,
        DATE_FORMAT(t1.endTime ,'%Y-%m-%d') AS endTime,
        DATE_FORMAT(t1.endTime ,'%Y-%m-%d %H:%i:%s') AS fullEndTime,
        DATE_FORMAT(t1.create_time ,'%Y-%m-%d %T') AS createTime
        FROM sm_order t1
        LEFT JOIN sm_order_applicant t2 ON t1.fhOrderId=t2.fhOrderId
        LEFT JOIN sm_order_insured t3 ON t1.fhOrderId=t3.fhOrderId
        LEFT JOIN sm_product t4 ON t4.id=t1.productId
        LEFT JOIN sm_plan t5 ON t5.id=t1.planId
        left join sm_order_policy t6 on t6.policy_no = t3.policyNo
        and t6.fh_order_id = t1.fhOrderId and t3.appStatus = t6.policy_state
        WHERE
        <if test="isInsured == false ">
            t2.idNumber = #{idNumber}
        </if>
        <if test="isInsured == true ">
            t3.idNumber = #{idNumber}
        </if>
        <if test="queryHistory==null">
            <![CDATA[ AND t1.endTime >= CURRENT_TIMESTAMP() ]]>
            AND (
            t1.payStatus = '1'
            OR
            (
            t1.payStatus = '2' AND t3.appStatus in ('1','2','-2','-1','0')
            )
            )
        </if>
        <if test="queryHistory!=null and queryHistory">
            AND t1.payStatus = '2' AND ( t3.appStatus != '1' OR <![CDATA[ t1.endTime < CURRENT_TIMESTAMP() ]]>)
        </if>
        ORDER BY t1.create_time DESC
    </select>
</mapper>
