<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cfpamf.ms.insur.base.mapper.ActivitiMapper">
    <update id="updateHisActTaskEndTime">
        update act_hi_actinst
        set act_hi_actinst.END_TIME_ = now()
        where PROC_INST_ID_ = #{proInsId}
          and TASK_ID_ = #{taskId}
    </update>
    <update id="updateHisActEndTime">
        update act_hi_actinst
        set act_hi_actinst.END_TIME_ = #{endTime}
        where PROC_INST_ID_ = #{processInstanceId}
          and TASK_ID_ = #{taskId}
    </update>
    <update id="updateHisTaskEndTime">
        update act_hi_taskinst
        set END_TIME_ = #{endTime}
        where PROC_INST_ID_ = #{processInstanceId}
          and ID_ = #{taskId}
    </update>
    <select id="listByProInstId" resultType="com.cfpamf.ms.insur.base.pojo.vo.ActivitiActVO">
        select aha.PROC_INST_ID_,
               ACT_ID_,
               ACT_TYPE_,
               ACT_NAME_,
               ahc.MESSAGE_,
               ASSIGNEE_,
               au.userName assignee_name,
               aha.START_TIME_,
               aha.END_TIME_,
               aha.DURATION_
        from act_hi_actinst aha
                 left join act_hi_comment ahc on aha.PROC_INST_ID_ = ahc.PROC_INST_ID_
            and aha.TASK_ID_ = ahc.TASK_ID_ and ahc.TYPE_ = 'comment'
                 left join auth_user au on ASSIGNEE_ = au.userId
        where aha.PROC_INST_ID_ = #{proInstId}
        order by aha.START_TIME_
    </select>

    <select id="getLastEndUserTask" resultType="com.cfpamf.ms.insur.base.pojo.vo.ActivitiActVO">

        <!--
                select aha.PROC_INST_ID_,
                       ACT_ID_,
                       ACT_TYPE_,
                       ACT_NAME_,
                       ahc.MESSAGE_,
                       ASSIGNEE_,
                       au.userName assignee_name,
                       aha.START_TIME_,
                       aha.END_TIME_,
                       aha.DURATION_
                from act_hi_actinst aha
                         left join act_hi_comment ahc on aha.PROC_INST_ID_ = ahc.PROC_INST_ID_
                    and aha.TASK_ID_ = ahc.TASK_ID_ and ahc.TYPE_ = 'comment'
                         left join auth_user au on ASSIGNEE_ = au.userId
                where aha.PROC_INST_ID_ = #{proInstId}
                  and ACT_TYPE_ = 'userTask'
                   and END_TIME_ is not null
                order by aha.START_TIME_ desc
                limit 1
         -->
        select aha.PROC_INST_ID_,
        TASK_DEF_KEY_ act_id,
        TYPE_ act_type,
        NAME_ act_name,
        ahc.MESSAGE_,
        ASSIGNEE_,
        au.userName assignee_name,
        aha.START_TIME_,
        aha.END_TIME_,
        aha.DURATION_
        from act_hi_taskinst aha
        left join act_hi_comment ahc on aha.PROC_INST_ID_ = ahc.PROC_INST_ID_
        and aha.ID_ = ahc.TASK_ID_ and ahc.TYPE_ = 'comment'
        left join auth_user au on ASSIGNEE_ = au.userId
        where aha.PROC_INST_ID_ = #{proInstId}
        -- and ACT_TYPE_ = 'userTask'
        -- and END_TIME_ is not null
        order by aha.START_TIME_ desc limit 1

    </select>
    <select id="listTaskEndTime" resultType="com.cfpamf.ms.insur.base.pojo.vo.ActTaskEndTime">

        select PROC_INST_ID_, TASK_DEF_KEY_, max(END_TIME_) end_time
        from act_hi_taskinst
        where
        <if test="proInstIds!=null and proInstIds.size() >0">
            PROC_INST_ID_ in
            <foreach collection="proInstIds" item="proInstId" open="(" separator="," close=")">
                #{proInstId}
            </foreach>
            and
        </if>
        END_TIME_ is not null
        group by PROC_INST_ID_, TASK_DEF_KEY_

    </select>
</mapper>
