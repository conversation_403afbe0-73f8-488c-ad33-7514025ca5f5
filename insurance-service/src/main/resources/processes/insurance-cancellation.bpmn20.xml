<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:activiti="http://activiti.org/bpmn" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:omgdc="http://www.omg.org/spec/DD/20100524/DC" xmlns:omgdi="http://www.omg.org/spec/DD/20100524/DI" typeLanguage="http://www.w3.org/2001/XMLSchema" expressionLanguage="http://www.w3.org/1999/XPath" targetNamespace="http://www.activiti.org/processdef">
  <process id="insurance-cancellation" name="insurance-cancellation" isExecutable="true">
    <documentation>退保流程</documentation>
    <extensionElements>
      <activiti:eventListener events="ACTIVITY_STARTED" class="com.cfpamf.ms.insur.admin.event.activiti.ActivitiCreatedListener"></activiti:eventListener>
      <activiti:eventListener events="ACTIVITY_STARTED" class="com.cfpamf.ms.insur.admin.event.activiti.CancelTaskListener"></activiti:eventListener>
    </extensionElements>
    <startEvent id="cancel_start" name="开始" activiti:initiator="${creator}"></startEvent>
    <userTask id="ut_apply" name="提供退保申请资料">
      <extensionElements>
        <modeler:initiator-can-complete xmlns:modeler="http://activiti.com/modeler"><![CDATA[false]]></modeler:initiator-can-complete>
      </extensionElements>
    </userTask>
    <sequenceFlow id="sid-739BDF05-1343-49BC-AB65-F727BDBD7710" sourceRef="cancel_start" targetRef="ut_apply"></sequenceFlow>
    <exclusiveGateway id="ex_gw_user_type" default="sid-1CA9BA32-C90E-40EE-B321-E842CE67BCC5"></exclusiveGateway>
    <userTask id="ut_center_audit" name="总部审核"></userTask>
    <userTask id="ut_org_audit" name="机构审核" >
      <extensionElements>
        <activiti:taskListener event="create" expression="${orgPicCandidateTaskListener}"></activiti:taskListener>
        <modeler:initiator-can-complete xmlns:modeler="http://activiti.com/modeler"><![CDATA[false]]></modeler:initiator-can-complete>
      </extensionElements>
    </userTask>
    <exclusiveGateway id="exp_gate_safe_audit" default="sid-28165B18-34D2-40F4-B2BB-67894D8568FA"></exclusiveGateway>
    <exclusiveGateway id="exp_org_audit" default="org_audit_pass"></exclusiveGateway>
    <serviceTask id="st_send_file" name="提交到保险公司" default="sid-7C062F32-3F7F-44ED-A629-333BE486C789" activiti:expression="${smCancelService.commit2Company(execution)}"></serviceTask>
    <exclusiveGateway id="exp_ref_state" default="sid-5E3D3166-B6C9-4615-ADBA-0EAC2859CB4C"></exclusiveGateway>
    <endEvent id="cancel_end" name="结束流程"></endEvent>
    <sequenceFlow id="sid-156ABE2A-45E7-4204-811D-669CE64CF2E4" sourceRef="ut_center_audit" targetRef="exp_gate_safe_audit"></sequenceFlow>
    <sequenceFlow id="sid-4BA5DD60-3376-4A46-84E0-64EC8537B83A" sourceRef="ut_org_audit" targetRef="exp_org_audit"></sequenceFlow>
    <sequenceFlow id="sid-28165B18-34D2-40F4-B2BB-67894D8568FA" sourceRef="exp_gate_safe_audit" targetRef="st_send_file"></sequenceFlow>
    <sequenceFlow id="org_audit_pass" sourceRef="exp_org_audit" targetRef="ut_center_audit"></sequenceFlow>
    <sequenceFlow id="sid-1CA9BA32-C90E-40EE-B321-E842CE67BCC5" name="客户经理" sourceRef="ex_gw_user_type" targetRef="ut_org_audit"></sequenceFlow>
    <sequenceFlow id="sid-D048194F-78D1-4122-8B75-5C9E08CD2805" name="取消退保" sourceRef="exp_gate_safe_audit" targetRef="cancel_end">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${auditResult=='cancel'}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-5E3D3166-B6C9-4615-ADBA-0EAC2859CB4C" name="状态更新成功" sourceRef="exp_ref_state" targetRef="cancel_end"></sequenceFlow>
    <sequenceFlow id="sid-4F8191EB-4FF2-454B-A7EF-C4096819E14F" name="机构驳回" sourceRef="exp_org_audit" targetRef="ut_apply">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${auditResult=='reject'}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-428C162E-9762-455A-8C55-D212DE55E294" name="驳回" sourceRef="exp_gate_safe_audit" targetRef="ut_apply">
      <extensionElements>
        <activiti:executionListener event="end" expression="${smCancelService.safeCenterReject(execution)}"></activiti:executionListener>
      </extensionElements>
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${auditResult=='reject'}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-21DF778A-7F80-4D71-A523-4FD56B3FFA3D" sourceRef="ut_apply" targetRef="ex_gw_user_type"></sequenceFlow>
    <sequenceFlow id="is_head" name="总部员工" sourceRef="ex_gw_user_type" targetRef="ut_center_audit">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${creatorIsHead || centerRejectCount>0}]]></conditionExpression>
    </sequenceFlow>
    <userTask id="ut_company_do" name="保险公司处理中" default="sid-6A8452E8-7772-4717-85DF-A8F0DD83F382"></userTask>
    <sequenceFlow id="sid-7C062F32-3F7F-44ED-A629-333BE486C789" sourceRef="st_send_file" targetRef="ut_company_do"></sequenceFlow>
    <serviceTask id="st_cancel_cancel" name="取消退保" activiti:expression="${smCancel.actCancelCancel(execution)}"></serviceTask>
    <sequenceFlow id="sid-F258997A-A7A6-4225-BB5D-457B4CCDDD35" sourceRef="st_cancel_cancel" targetRef="cancel_end"></sequenceFlow>
    <sequenceFlow id="sid-F99B6E26-D13F-4034-88C4-3CDC51504CFB" sourceRef="exp_ref_state" targetRef="ut_company_do">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${failCount==1 && cancelState=='fail'}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-6A8452E8-7772-4717-85DF-A8F0DD83F382" sourceRef="ut_company_do" targetRef="exp_ref_state"></sequenceFlow>
  </process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_insurance-cancellation">
    <bpmndi:BPMNPlane bpmnElement="insurance-cancellation" id="BPMNPlane_insurance-cancellation">
      <bpmndi:BPMNShape bpmnElement="cancel_start" id="BPMNShape_cancel_start">
        <omgdc:Bounds height="30.0" width="30.0" x="90.0" y="265.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="ut_apply" id="BPMNShape_ut_apply">
        <omgdc:Bounds height="80.0" width="100.0" x="165.0" y="240.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="ex_gw_user_type" id="BPMNShape_ex_gw_user_type">
        <omgdc:Bounds height="40.0" width="40.0" x="320.0" y="255.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="ut_center_audit" id="BPMNShape_ut_center_audit">
        <omgdc:Bounds height="80.0" width="100.0" x="540.0" y="235.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="ut_org_audit" id="BPMNShape_ut_org_audit">
        <omgdc:Bounds height="80.0" width="100.0" x="435.0" y="120.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="exp_gate_safe_audit" id="BPMNShape_exp_gate_safe_audit">
        <omgdc:Bounds height="40.0" width="40.0" x="735.0" y="255.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="exp_org_audit" id="BPMNShape_exp_org_audit">
        <omgdc:Bounds height="40.0" width="40.0" x="570.0" y="135.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="st_send_file" id="BPMNShape_st_send_file">
        <omgdc:Bounds height="80.0" width="100.0" x="840.0" y="235.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="exp_ref_state" id="BPMNShape_exp_ref_state">
        <omgdc:Bounds height="40.0" width="40.0" x="705.0" y="420.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="cancel_end" id="BPMNShape_cancel_end">
        <omgdc:Bounds height="28.0" width="28.0" x="555.0" y="426.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="ut_company_do" id="BPMNShape_ut_company_do">
        <omgdc:Bounds height="80.0" width="100.0" x="840.0" y="360.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="st_cancel_cancel" id="BPMNShape_st_cancel_cancel">
        <omgdc:Bounds height="80.0" width="100.0" x="285.0" y="450.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge bpmnElement="is_head" id="BPMNEdge_is_head">
        <omgdi:waypoint x="359.5381526104418" y="275.4618473895582"></omgdi:waypoint>
        <omgdi:waypoint x="540.0" y="275.1002004008016"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-D048194F-78D1-4122-8B75-5C9E08CD2805" id="BPMNEdge_sid-D048194F-78D1-4122-8B75-5C9E08CD2805">
        <omgdi:waypoint x="744.8732193732194" y="284.8732193732194"></omgdi:waypoint>
        <omgdi:waypoint x="579.4993727157629" y="430.73915918636465"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-F99B6E26-D13F-4034-88C4-3CDC51504CFB" id="BPMNEdge_sid-F99B6E26-D13F-4034-88C4-3CDC51504CFB">
        <omgdi:waypoint x="740.0713378956432" y="435.0713378956432"></omgdi:waypoint>
        <omgdi:waypoint x="840.0" y="397.8421592855448"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-6A8452E8-7772-4717-85DF-A8F0DD83F382" id="BPMNEdge_sid-6A8452E8-7772-4717-85DF-A8F0DD83F382">
        <omgdi:waypoint x="897.7840699573302" y="440.0"></omgdi:waypoint>
        <omgdi:waypoint x="893.0" y="569.0"></omgdi:waypoint>
        <omgdi:waypoint x="736.2516891891892" y="448.74831081081084"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-739BDF05-1343-49BC-AB65-F727BDBD7710" id="BPMNEdge_sid-739BDF05-1343-49BC-AB65-F727BDBD7710">
        <omgdi:waypoint x="120.0" y="280.0"></omgdi:waypoint>
        <omgdi:waypoint x="165.0" y="280.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-7C062F32-3F7F-44ED-A629-333BE486C789" id="BPMNEdge_sid-7C062F32-3F7F-44ED-A629-333BE486C789">
        <omgdi:waypoint x="890.0" y="315.0"></omgdi:waypoint>
        <omgdi:waypoint x="890.0" y="360.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-5E3D3166-B6C9-4615-ADBA-0EAC2859CB4C" id="BPMNEdge_sid-5E3D3166-B6C9-4615-ADBA-0EAC2859CB4C">
        <omgdi:waypoint x="705.4358974358975" y="440.43589743589746"></omgdi:waypoint>
        <omgdi:waypoint x="582.9999285493736" y="440.044728206228"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="org_audit_pass" id="BPMNEdge_org_audit_pass">
        <omgdi:waypoint x="590.4201680672269" y="174.57983193277312"></omgdi:waypoint>
        <omgdi:waypoint x="590.1673640167364" y="235.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-156ABE2A-45E7-4204-811D-669CE64CF2E4" id="BPMNEdge_sid-156ABE2A-45E7-4204-811D-669CE64CF2E4">
        <omgdi:waypoint x="640.0" y="275.0"></omgdi:waypoint>
        <omgdi:waypoint x="735.0" y="275.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-F258997A-A7A6-4225-BB5D-457B4CCDDD35" id="BPMNEdge_sid-F258997A-A7A6-4225-BB5D-457B4CCDDD35">
        <omgdi:waypoint x="385.0" y="479.3162393162393"></omgdi:waypoint>
        <omgdi:waypoint x="555.3090560972225" y="442.92541536384135"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-428C162E-9762-455A-8C55-D212DE55E294" id="BPMNEdge_sid-428C162E-9762-455A-8C55-D212DE55E294">
        <omgdi:waypoint x="755.5" y="255.5"></omgdi:waypoint>
        <omgdi:waypoint x="755.5" y="62.0"></omgdi:waypoint>
        <omgdi:waypoint x="215.0" y="62.0"></omgdi:waypoint>
        <omgdi:waypoint x="215.0" y="240.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-4BA5DD60-3376-4A46-84E0-64EC8537B83A" id="BPMNEdge_sid-4BA5DD60-3376-4A46-84E0-64EC8537B83A">
        <omgdi:waypoint x="535.0" y="157.86729857819904"></omgdi:waypoint>
        <omgdi:waypoint x="571.3181818181819" y="156.3181818181818"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-28165B18-34D2-40F4-B2BB-67894D8568FA" id="BPMNEdge_sid-28165B18-34D2-40F4-B2BB-67894D8568FA">
        <omgdi:waypoint x="774.570895522388" y="275.42910447761193"></omgdi:waypoint>
        <omgdi:waypoint x="840.0" y="275.18587360594796"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-1CA9BA32-C90E-40EE-B321-E842CE67BCC5" id="BPMNEdge_sid-1CA9BA32-C90E-40EE-B321-E842CE67BCC5">
        <omgdi:waypoint x="340.5" y="255.5"></omgdi:waypoint>
        <omgdi:waypoint x="340.5" y="160.0"></omgdi:waypoint>
        <omgdi:waypoint x="435.0" y="160.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-4F8191EB-4FF2-454B-A7EF-C4096819E14F" id="BPMNEdge_sid-4F8191EB-4FF2-454B-A7EF-C4096819E14F">
        <omgdi:waypoint x="590.5" y="135.5"></omgdi:waypoint>
        <omgdi:waypoint x="590.5" y="96.0"></omgdi:waypoint>
        <omgdi:waypoint x="215.0" y="96.0"></omgdi:waypoint>
        <omgdi:waypoint x="215.0" y="240.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-21DF778A-7F80-4D71-A523-4FD56B3FFA3D" id="BPMNEdge_sid-21DF778A-7F80-4D71-A523-4FD56B3FFA3D">
        <omgdi:waypoint x="265.0" y="278.20717131474106"></omgdi:waypoint>
        <omgdi:waypoint x="321.1923076923077" y="276.1923076923077"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</definitions>
